/*Started by AICoder, pid:c7f19bd547da487cb2edb0e09c6b90db*/
package com.zte.step.impl;

import com.zte.application.step.impl.SetpImesServiceImpl;
import com.zte.domain.model.step.SetpImesRepository;
import com.zte.interfaces.step.dto.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
public class SetpImesServiceImplTest {
    @InjectMocks
    private SetpImesServiceImpl setpImesService;

    @Mock
    private SetpImesRepository setpImesRepository;

    @Test(timeout = 8000)
    public void testGetStockInfo_Empty() throws Exception{
        BaItemQueryDto dto = new BaItemQueryDto();
        setpImesService.getStockInfo(dto);
        Mockito.verify(setpImesRepository).getStockInfo(dto);

        BaItemDto dto1=new BaItemDto();
        dto1.setStockNo("32543434");
        BaItemQueryDto dto2=new BaItemQueryDto();
        dto2.setStockNo("32543434");
        Whitebox.invokeMethod(setpImesService, "getStockInfo",dto2);
        Assert.assertTrue(Objects.nonNull(dto1));
    }

    @Test
    public void getProjectInfo() throws Exception {
        List<ProjectDTO> list=new ArrayList<>();
        ProjectDTO dto=new ProjectDTO();
        dto.setProjectId(12323);
        Whitebox.invokeMethod(setpImesService, "getProjectInfo");
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void getStUtilityInfo() throws Exception {
        List<StUtilityDTO> list=new ArrayList<>();
        StUtilityDTO dto=new StUtilityDTO();
        dto.setUtilityNo("12323");
        Whitebox.invokeMethod(setpImesService, "getStUtilityInfo");
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void getInforBillNo() throws Exception{
        String billNo="12323";
        Whitebox.invokeMethod(setpImesService, "getInforBillNo");
        Assert.assertTrue(Objects.nonNull(12323));
    }

    @Test
    public void getIsErpPlan() throws Exception{
        OpOrderplanHeadDto dto=new OpOrderplanHeadDto();
        dto.setIsErpPlan(1);
        Whitebox.invokeMethod(setpImesService, "getIsErpPlan","234343");
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getEntrustBeforeInfo() throws Exception{
        BaBomHeadDto dto=new BaBomHeadDto();
        dto.setNewBomNo("32343434");
        Whitebox.invokeMethod(setpImesService, "getEntrustBeforeInfo","234343");
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getPlangroupInfo() throws Exception{
        DbProdclassPlangroupDto dto=new DbProdclassPlangroupDto();
        dto.setProjectId(345);
        Whitebox.invokeMethod(setpImesService, "getPlangroupInfo",dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }
}
/*Ended by AICoder, pid:c7f19bd547da487cb2edb0e09c6b90db*/
