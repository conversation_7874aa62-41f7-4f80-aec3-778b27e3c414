/* Started by AICoder, pid:j5e58kc0a0s4964144370a37b079dd03f1b10fd5 */
package com.zte.common.utils;

import com.zte.interfaces.infor.dto.IscpResultDTO;
import com.zte.interfaces.infor.dto.QaExInspectionHead;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/16 11:10
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class})
public class IscpRemoteServiceDataUtilTest {

    @InjectMocks
    private IscpRemoteServiceDataUtil iscpRemoteServiceDataUtil;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
    }
    @Test
    public void inspectionReq() throws Exception{
        List<QaExInspectionHead> dto = new ArrayList<>();
        QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
        qaExInspectionHead.setSupplierNo("1");
        dto.add(qaExInspectionHead);
        ServiceData<List<IscpResultDTO>> listServiceData = iscpRemoteServiceDataUtil.inspectionReq(dto);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.any(),Mockito.any(),
                Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("111");
        iscpRemoteServiceDataUtil.inspectionReq(dto);
        Assert.assertTrue(Objects.nonNull(listServiceData));
    }
}
/* Ended by AICoder, pid:j5e58kc0a0s4964144370a37b079dd03f1b10fd5 */