package com.zte.common.utils;
/* Started by AICoder, pid:ia45dxcadc1b52a14f2e09c7e0e95a2ee0e6283b */
import com.zte.interfaces.step.dto.BulkTaskDetailDTO;
import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, HttpClientUtil.class})
public class ImesCenterfactoryRemoteServiceTest {

    @InjectMocks
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class, HttpClientUtil.class);
    }
    @Test
    public void pushDataToB2B() throws Exception {

        List<CustomerDataLogDTO> dataList = Tools.newArrayList();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("242");
        dataList.add(customerDataLogDTO);
        String empNo = "12345";
        String responseStr = null;

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        responseStr = "{\"code\": {\"code\": \"0005\",\"msgId\": \"success\",\"msg\": \"请求不成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2B", dataList, empNo);

        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2B", dataList, empNo);

        Assert.assertNotNull(dataList);
    }

    @Test
    public void pushDataToB2BKafKa() throws Exception {

        List<CustomerDataLogDTO> dataList = Tools.newArrayList();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("242");
        dataList.add(customerDataLogDTO);
        String empNo = "12345";
        String responseStr = null;

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        responseStr = "{\"code\": {\"code\": \"0005\",\"msgId\": \"success\",\"msg\": \"请求不成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2BKafKa", dataList, empNo);

        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2BKafKa", dataList, empNo);

        Assert.assertNotNull(dataList);
    }

    @Test
    public void getCategory() throws Exception {
        BulkTaskDetailDTO bulkTaskDetailDTO = new BulkTaskDetailDTO();
        bulkTaskDetailDTO.setCustomerPartType("11111");
        List<String> taskNo = Tools.newArrayList();
        taskNo.add("628805421300220117");
        String empNo = "12345";
        String responseStr = null;

        responseStr = "{\"code\": {\"code\": \"0005\",\"msgId\": \"success\",\"msg\": \"请求不成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "getCategory", taskNo, empNo);

        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "getCategory", taskNo, empNo);
        Assert.assertNotNull(bulkTaskDetailDTO);
    }

}
/* Ended by AICoder, pid:ia45dxcadc1b52a14f2e09c7e0e95a2ee0e6283b */