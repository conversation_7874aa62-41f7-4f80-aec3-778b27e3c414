package com.zte.common.utils;
import com.zte.interfaces.infor.dto.B2bDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(PowerMockRunner.class)
public class InteractiveB2BTest {
    @InjectMocks
    private InteractiveB2B interactiveB2B;
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        interactiveB2B.applicationSecret = "secret";
        interactiveB2B.url = "http://example.com";
        interactiveB2B.appId = "testAppId";
    }

    /* Started by AICoder, pid:k8634nd0ba7e0df145930847d0e73d107db44c6f */
    @Test
    public void testGetHeadParams() {
        try {
            Map<String, String> headerParams = interactiveB2B.getHeadParams();

            assertTrue(headerParams.containsKey("app_id"));
            assertTrue(headerParams.containsKey("Authorization"));
            assertEquals("testAppId", headerParams.get("app_id"));
            assertTrue(headerParams.get("Authorization").startsWith("Sign "));
        } catch (Exception e) {
            System.err.println("Exception occurred: " + e.getMessage());
            throw new RuntimeException("Test failed due to exception", e);
        }
    }

    /* Ended by AICoder, pid:k8634nd0ba7e0df145930847d0e73d107db44c6f */
    /* Started by AICoder, pid:3b00327e49e7bba141420b57205033107e51719a */
    @Test
    public void testGetRequestBody() {
        Object inputData = new Object();
        Object requestBody = interactiveB2B.getRequestBody(inputData);

        assertTrue(requestBody instanceof B2bDTO);
        B2bDTO b2bRequest = (B2bDTO) requestBody;

        assertNotNull(b2bRequest.getUuid());
        assertEquals(b2bRequest.getUuid(), b2bRequest.getKeywords());
    }

    /* Ended by AICoder, pid:3b00327e49e7bba141420b57205033107e51719a */

}