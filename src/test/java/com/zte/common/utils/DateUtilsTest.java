package com.zte.common.utils;

import com.zte.Application;
import com.zte.action.iscpedi.WriteBackIscpStartHandler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Objects;

import static com.zte.common.utils.DateUtil.getTodayStartTime;

@RunWith(SpringJUnit4ClassRunner.class)
public class DateUtilsTest {

    @Test
    public void run() {
        try {
            String day = getTodayStartTime();
            Assert.assertTrue(Objects.nonNull(day));
        } catch (Exception e) {
            return;
        }
    }
}
