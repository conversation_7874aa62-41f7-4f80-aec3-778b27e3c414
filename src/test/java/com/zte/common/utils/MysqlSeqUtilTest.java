/* Started by AICoder, pid:0d237h08561a8e1146a80a040090310bc571e3c8 */
package com.zte.common.utils;

import com.zte.domain.model.material.MysqlSeqRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/16 11:20
 */
@RunWith(PowerMockRunner.class)
public class MysqlSeqUtilTest {
    @InjectMocks
    private MysqlSeqUtil mysqlSeqUtil;

    @Mock
    private MysqlSeqRepository mysqlSeqRepository;


    @Test
    public void getSeqCurval() throws Exception{
        String name = "1";
        mysqlSeqUtil.getSeqCurval(name);
        List<HashMap<String, Object>> list = new ArrayList<>();
        HashMap<String, Object> map = new HashMap<>();
        map.put("1","2");
        list.add(map);
        PowerMockito.when(mysqlSeqRepository.getSeqCurval(Mockito.anyString())).thenReturn(list);
        long seqCurval = mysqlSeqUtil.getSeqCurval(name);
        Assert.assertTrue(Objects.nonNull(seqCurval));
    }

    @Test
    public void getSeqNextval() throws Exception{
        String name = "1";
        mysqlSeqUtil.getSeqNextval(name);
        List<HashMap<String, Object>> list = new ArrayList<>();
        HashMap<String, Object> map = new HashMap<>();
        map.put("1","2");
        list.add(map);
        PowerMockito.when(mysqlSeqRepository.getSeqCurval(Mockito.anyString())).thenReturn(list);
        long seqCurval = mysqlSeqUtil.getSeqNextval(name);
        Assert.assertTrue(Objects.nonNull(seqCurval));
    }

}
/* Ended by AICoder, pid:0d237h08561a8e1146a80a040090310bc571e3c8 */