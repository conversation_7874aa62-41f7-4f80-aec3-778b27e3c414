package com.zte.common.utils;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertTrue;

@RunWith(PowerMockRunner.class)
public class ThreadUtilTest {
    private ThreadUtil.NameTreadFactory nameTreadFactory;

    @Before
    public void setUp() {
        nameTreadFactory = new ThreadUtil.NameTreadFactory();
    }

    @Test
    public void testNewThread() {
        Runnable r = () -> System.out.println("Running in new thread");
        Thread t = nameTreadFactory.newThread(r);
        assertTrue(t.getName().startsWith("my-thread-"));
    }
}
