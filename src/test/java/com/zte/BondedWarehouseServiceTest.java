package com.zte;

import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.application.infor.IBondedWarehouseService;
import com.zte.application.infor.impl.BondedWarehouseServiceImpl;
import com.zte.domain.model.infor.BondedWarehouseInventoryInfo;
import com.zte.domain.model.infor.EdiAsnqcSRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.utils.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;


/**
 * @Deacription 保税仓单元测试类
 * <AUTHOR>
 * @Date 2020/8/19 14:29
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class})
public class BondedWarehouseServiceTest {

    @InjectMocks
    private BondedWarehouseServiceImpl bondedWarehouseService;
    @Mock
    private EdiAsnqcSRepository ediAsnqcSRepository;
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class);
    }
    @Test
    public void queryBondedWarehouseInventoryTest() {
        BondedWarehouseInventoryInfo inventoryInfo = new BondedWarehouseInventoryInfo();
        inventoryInfo.setItemBarcode("220017713523");
        inventoryInfo.setStockNo("WMWHSE25");

        BondedWarehouseInventoryInfo inventoryInfo1 = new BondedWarehouseInventoryInfo();
        inventoryInfo1.setItemBarcode("220017688622");
        inventoryInfo1.setStockNo("WMWHSE25");

        BondedWarehouseInventoryInfo inventoryInfo2 = new BondedWarehouseInventoryInfo();
        inventoryInfo2.setItemBarcode("220015664483");
        inventoryInfo2.setStockNo("WMWHSE31");

        BondedWarehouseInventoryInfo inventoryInfo3 = new BondedWarehouseInventoryInfo();
        inventoryInfo3.setItemBarcode("220015625790");
        inventoryInfo3.setStockNo("WMWHSE25");

        BondedWarehouseInventoryInfo inventoryInfo4 = new BondedWarehouseInventoryInfo();
        inventoryInfo4.setItemBarcode("220015627079");
        inventoryInfo4.setStockNo("WMWHSE25");

        BondedWarehouseInventoryInfo inventoryInfo5 = new BondedWarehouseInventoryInfo();
        inventoryInfo5.setItemBarcode("220015625465");
        inventoryInfo5.setStockNo("WMWHSE25");

        List<BondedWarehouseInventoryInfo> inventoryInfoList = new ArrayList<>();
        inventoryInfoList.add(inventoryInfo);
        inventoryInfoList.add(inventoryInfo1);
        inventoryInfoList.add(inventoryInfo2);
        inventoryInfoList.add(inventoryInfo3);
        inventoryInfoList.add(inventoryInfo4);
        inventoryInfoList.add(inventoryInfo5);
        ServiceData<List<BondedWarehouseInventoryInfo>> listServiceData = bondedWarehouseService.queryBondedWarehouseInventory(inventoryInfoList);
        Assert.assertNotNull(inventoryInfo);
    }

}