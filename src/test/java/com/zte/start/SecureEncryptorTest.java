/**
 * Project Name:International
 * File Name:ApplicationTest
 * Package Name:com.zte
 * 日期:2021/1/11 17:06
 * 版权所有：版权所有(C) 2018,中兴通讯
 */
package com.zte.start;

import com.zte.itp.msa.util.constant.ResourceConst;
import com.zte.itp.msa.util.encrypt.secure.RootKeyInitializer;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.itp.msa.util.encrypt.secure.SecureRootKeyUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 描述: 数据库连接信息加密类，工具类方式
 * 日期: 2023/10/30 17:06
 * <AUTHOR>
 * @version V1.0
 */

public class SecureEncryptorTest extends SecureRootKeyUtils {


    public static class GenerateInitializer extends RootKeyInitializer {

        public void initialize(String factor1, String factor2,
                               String serviceName, String salt) {
            if (StringUtils.isNotBlank(serviceName)) {
                SecureRootKeyUtils.SALT_VALUE = getSaltStrByApplication(salt, serviceName);
                SecureRootKeyUtils.ROOT_CONTENT = getElementsStrByApplication(factor1, factor2, serviceName);
                SecureRootKeyUtils.generateEncryptedRootKey();
            }
        }

        private String getElementsStrByApplication(String factor1, String factor2,
                                                   String serviceName) {
            if (StringUtils.isBlank(factor1)) {
                return null;
            }
            if(StringUtils.isBlank(factor2)) {
                factor2 = serviceName;
            }
            return ResourceConst.ENC_START + factor1 + factor2 + SecureRootKeyUtils.class.getName()
                     + serviceName + ResourceConst.ENC_END;
        }

        protected String getSaltStrByApplication(String salt, String serviceName) {
            if(StringUtils.isBlank(salt)) {
                salt = serviceName;
            }
            return SecureRootKeyUtils.toHex(salt);
        }
    }

   public static void main(String[] args) {
        // 要素值 可通过 SecureRandomString.generate() 来生成

        // 要素1，msa.rootkey.factor1，必需配置，放配置中心（或本地调试放对应的profile，如loc中）
        String factor1 = RandomStringUtils.randomAlphanumeric(20);
        //String factor1 = "gdaNeZqceysHjknlRXPo";
        System.out.println("msa.rootkey.factor1:" + factor1);

        // 要素2，msa.rootkey.factor2，配置到common.properties中，不配默认取服务名
        String factor2 = "";
        System.out.println("msa.rootkey.factor2:" + factor2);

        // 服务名(spring.application.name) 正常启动时需要获取到参与加密
        String serviceName = "zte-mes-resourcewarehouse-datawb";

        // 盐值 msa.rootkey.salt，可不配置
        String salt = RandomStringUtils.randomAlphanumeric(20);
        //String salt = "gdaNeZqceysHjknlRXPo";
        System.out.println("msa.rootkey.salt:" + salt);

        // 用户自定义, 必须是32位（key的解释：用来加密用户名和密码的秘钥）
        String key = "keyztemesresourcewarehousesysmes";

        // 生成rootkey
        new GenerateInitializer().initialize(factor1, factor2, serviceName, salt);

        // 数据库用户名
        String username = "test";
        // 数据库密码
        String password = "test";

        encryptUserNameAndPwdTest(username, password, key);
        encryptStr(key);
   }

    public static void encryptStr(String key) {
        // 数据库用户名
        String usernameSct = "+v9XhqJSmuYTYSu0MzZv1SL2jCr9enXZZ57Irs881j6E64pCfx8\\=";
        // 数据库密码
        String passwordSct = "+YSL/56yi9E3atKyz4ZDBZgeMrwVPxLXnhlXLHVYnF2SPGssgTznWDdIMQk\\=";
        // 对key加密
        String encryptedKey = SecureEncryptorUtils.encryptKey(key);
        String decryptedUsername = SecureEncryptorUtils.decrypt(usernameSct, encryptedKey);
        System.out.println("==用户名解密结果是:" + decryptedUsername);
        String decryptedPwd = SecureEncryptorUtils.decrypt(passwordSct, encryptedKey);
        System.out.println("==密码 解密结果是:" + decryptedPwd);
        System.out.println("===================================================");

        //加密
        String encryptedUsername = SecureEncryptorUtils.encryptPwd(decryptedUsername, key);
        System.out.println("用户名加密结果是:" + encryptedUsername);

        String encryptedPwd = SecureEncryptorUtils.encryptPwd(decryptedPwd, key);
        System.out.println("密码 加密结果是:" + encryptedPwd);
        System.out.println("===================================================");
    }

    /**
     * 数据库用户名和密码加解密测试
     */
    public static void encryptUserNameAndPwdTest(String username, String password, String key){

        System.out.println("key length :" + key.length());
        System.out.println("===================================================");

        //加密
        String encryptedUsername = SecureEncryptorUtils.encryptPwd(username, key);
        System.out.println("用户名加密结果是:" + encryptedUsername);

        String encryptedPwd = SecureEncryptorUtils.encryptPwd(password, key);
        System.out.println("密码 加密结果是:" + encryptedPwd);

        // 对key加密
        String encryptedKey = SecureEncryptorUtils.encryptKey(key);

        // 对应的是msa.encrypt.rootkey
        System.out.println("根密钥 加密结果是:" + encryptedKey);
        System.out.println("===================================================");

        //解密测试
        String decryptedUsername = SecureEncryptorUtils.decrypt(encryptedUsername, encryptedKey);
        System.out.println("用户名解密结果是:" + decryptedUsername);
        String decryptedPwd = SecureEncryptorUtils.decrypt(encryptedPwd, encryptedKey);
        System.out.println("密码 解密结果是:" + decryptedPwd);
        System.out.println("===================================================");

    }

}
