/* Started by AICoder, pid:jb01cxc108590a51456c0947f07d350ab4c1bcf3 */
package com.zte;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/16 14:43
 */
@RunWith(PowerMockRunner.class)
public class Swagger3Test {
    @InjectMocks
    private Swagger3 swagger3;

    @Test
    public void createRestApi() {
        Docket restApi = swagger3.createRestApi();
        Assert.assertNotNull(restApi);
    }
}
/* Ended by AICoder, pid:jb01cxc108590a51456c0947f07d350ab4c1bcf3 */