package com.zte.application.tally;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.tally.TallyPackingServiceImpl;
import com.zte.domain.model.infor.SkuRepository;
import com.zte.domain.model.material.StDeliveryRepository;
import com.zte.domain.model.material.StTallyPackingDetailRepository;
import com.zte.domain.model.material.StTallyPackingHeadRepository;
import com.zte.domain.model.material.StWarehouseRepository;
import com.zte.interfaces.tally.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.redis.RedisHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({StringUtils.class, RedisSerialNoUtil.class, RedisHelper.class, RemoteServiceDataUtil.class,
        BusiAssertException.class})
public class TallyPackingServiceImplTest {
    @InjectMocks
    private TallyPackingServiceImpl tallyPackingService;

    @Mock
    private StTallyPackingDetailRepository stTallyPackingDetailRepository;

    @Mock
    private StTallyPackingHeadRepository stTallyPackingHeadRepository;

    @Mock
    private StDeliveryRepository stDeliveryRepository;

    @Mock
    private StWarehouseRepository stWarehouseRepository;

    @Mock
    private SkuRepository skuRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(StringUtils.class, RedisSerialNoUtil.class, RedisHelper.class,
                BusiAssertException.class);
    }

    @Test
    public void getQty() throws Exception {
        String sourceNo = "XB20041400014";
        String sourceResp = "11";
        List<TallyDetailDTO> tallyDetailDTOs = new ArrayList<>();
        TallyDetailDTO dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("其他");
        tallyDetailDTOs.add(dto);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        tallyDetailDTOs = new ArrayList<>();
        dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("WMS-HDH");
        tallyDetailDTOs.add(dto);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        tallyDetailDTOs = new ArrayList<>();
        dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("OMG-G");
        tallyDetailDTOs.add(dto);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        tallyDetailDTOs = new ArrayList<>();
        dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("INFOR-LPN");
        tallyDetailDTOs.add(dto);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        tallyDetailDTOs = new ArrayList<>();
        dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("INFOR-LFID");
        tallyDetailDTOs.add(dto);
        StockDTO stockDTO = new StockDTO();
        stockDTO.setStockName("4324");
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        PowerMockito.when(stWarehouseRepository.selectStWarehouse(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        PowerMockito.when(stWarehouseRepository.selectStWarehouse(Mockito.any())).thenReturn(stockDTO);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        tallyDetailDTOs = new ArrayList<>();
        dto = new TallyDetailDTO();
        dto.setContainerNo("232");
        dto.setStartNo("NFOR-LPN11");
        tallyDetailDTOs.add(dto);
        List<InforBillDTO> inforBillDTOs = new ArrayList<>();
        InforBillDTO inforBillDTO = new InforBillDTO();
        inforBillDTO.setExternalorderkey2("2");
        inforBillDTOs.add(inforBillDTO);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        PowerMockito.when(skuRepository.selectEdiSoAll(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        PowerMockito.when(skuRepository.selectEdiSoAll(Mockito.any())).thenReturn(inforBillDTOs);
        Whitebox.invokeMethod(tallyPackingService, "getQty", sourceNo, sourceResp);
        Assert.assertTrue(Objects.nonNull(sourceNo));
    }

    @Test
    public void getTransferType() throws Exception {
        String name = "00326";
        String language = "zh_CN";
        String dictionaryTypeCode = "100";
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SERVERERROR_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(retData);
        Whitebox.invokeMethod(tallyPackingService, "getTransferType", name, language, dictionaryTypeCode);
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsObject = new JSONObject();
        jsObject.put("code", "");
        retData = new ServiceData();
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(retData);
        Whitebox.invokeMethod(tallyPackingService, "getTransferType", name, language, dictionaryTypeCode);
        list = new ArrayList<>();
        jsObject = new JSONObject();
        jsObject.put("code", "");
        list.add(jsObject);
        retData = new ServiceData();
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(retData);
        Whitebox.invokeMethod(tallyPackingService, "getTransferType", name, language, dictionaryTypeCode);
        jsObject = new JSONObject();
        jsObject.put("code", "111");
        list = new ArrayList<>();
        list.add(jsObject);
        retData = new ServiceData();
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(retData);
        Whitebox.invokeMethod(tallyPackingService, "getTransferType", name, language, dictionaryTypeCode);
        Assert.assertTrue(Objects.nonNull(name));
    }

    @Test
    public void getInterfaceResponse() throws Exception {
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        String sourceNo = "XB20041400014";
        List<TallyHeadDTO> tallyHeadDTOs = new ArrayList<>();
        TallyHeadDTO dto = new TallyHeadDTO();
        dto.setBoxNum(1);
        dto.setSourceRepository("1");
        tallyHeadDTOs.add(dto);
        PowerMockito.when(stTallyPackingHeadRepository.selectStTallyPackingHeadList(Mockito.any())).thenReturn(null);
      //  Whitebox.invokeMethod(tallyPackingService, "getInterfaceResponse", sourceNo);
        PowerMockito.when(stTallyPackingHeadRepository.selectStTallyPackingHeadList(Mockito.any())).thenReturn(tallyHeadDTOs);
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsObject = new JSONObject();
        jsObject.put("code", "111");
        list.add(jsObject);
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(retData);
        List<TallyDetailDTO> tallyDetailDTOs = new ArrayList<>();
        TallyDetailDTO tallyDetailDTO = new TallyDetailDTO();
        tallyDetailDTO.setContainerNo("232");
        tallyDetailDTO.setStartNo("WMS-HDH");
        tallyDetailDTOs.add(tallyDetailDTO);
        List<BoxDetailDTO> boxDetailDTOs=new ArrayList<>();
        BoxDetailDTO boxDetailDTO = new BoxDetailDTO();
        boxDetailDTO.setContainerNo("323");
        boxDetailDTOs.add(boxDetailDTO);
        PowerMockito.when(stTallyPackingDetailRepository.selectStTallyPackingDetailList(Mockito.any())).thenReturn(tallyDetailDTOs);
        PowerMockito.when(stDeliveryRepository.selectStDeliveryList(Mockito.any())).thenReturn(boxDetailDTOs);
        InterfaceResponseDTO res = tallyPackingService.getInterfaceResponse(sourceNo);
        Assert.assertTrue(Objects.nonNull(sourceNo));
    }
}