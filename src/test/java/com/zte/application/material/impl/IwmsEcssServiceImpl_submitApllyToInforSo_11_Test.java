/*Started by AICoder, pid:295f827e66qcf00141b1088be1017a7c5ce9db14*/
package com.zte.application.material.impl;
import com.zte.application.onlinefallback.impl.OnlineFallBackServiceImpl;
import com.zte.application.step.ZteAlibabaService;
import com.zte.application.step.ZteStockInfoUploadService;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.material.IwmsEcssRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.ZteAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.EcssBillDTO;
import com.zte.interfaces.step.dto.ItemNoMpnDTO;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RemoteServiceDataUtil.class})
public class IwmsEcssServiceImpl_submitApllyToInforSo_11_Test {

    @InjectMocks
    private IwmsEcssServiceImpl iwmsEcssService;

    @Mock
    private OnlineFallBackServiceImpl onlineFallBackService;

    @Mock
    private IwmsEcssRepository iwmsEcssRepository;

    @Mock
    private ZteAlibabaRepository zteAlibabaRepository;

    @Mock
    private EmailUtil emailUtil;

    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Mock
    private ZteAlibabaService zteAlibabaService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
    }

    @Test
    public void testSubmitApllyToInforSo_NoRecordsFound() {
        EcssBillDTO dto = new EcssBillDTO();
        when(iwmsEcssRepository.getApplyBillToInfor(dto)).thenReturn(Collections.emptyList());

        iwmsEcssService.submitApllyToInforSo(dto);

        verify(iwmsEcssRepository, times(1)).getApplyBillToInfor(dto);
        verifyNoMoreInteractions(iwmsEcssRepository);
    }


    @Test
    public void testSubmitApllyToInforSo_ExceptionHandling() {
        EcssBillDTO dto = new EcssBillDTO();
        List<EcssBillDTO> list = Arrays.asList(
                new EcssBillDTO().setStatus("ECSSAUDITED").setReferenceNumber("LTH123").setMasterDataReferenceNumber("456"),
                new EcssBillDTO().setStatus("APPROVED").setReferenceNumber("STH456").setMasterDataReferenceNumber("123")
        );
        when(iwmsEcssRepository.getApplyBillToInfor(dto)).thenReturn(list);
        when(iwmsEcssRepository.selectItemNoByBillNo(anyList())).thenReturn(list);
        ItemNoMpnDTO itemNoMpnDTO = new ItemNoMpnDTO();
        itemNoMpnDTO.setItemNo("456");
        when(zteAlibabaRepository.getControlItemNo(anyList())).thenReturn(Arrays.asList(itemNoMpnDTO));
        doThrow(new RuntimeException("Test Exception")).when(iwmsEcssRepository).updateReturnApplyBill(any(EcssBillDTO.class));

        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(new SysLookupValuesDTO());
        PowerMockito.when(zteStockInfoUploadRepository.getStockUploadLog(any())).thenReturn(new ArrayList<>());
        iwmsEcssService.submitApllyToInforSo(dto);

        verify(iwmsEcssRepository, times(1)).getApplyBillToInfor(dto);
        verify(iwmsEcssRepository, times(1)).selectItemNoByBillNo(anyList());
        verify(zteAlibabaRepository, times(1)).getControlItemNo(anyList());
    }

    @Test
    public void testSendInforFailMail_NoRecordsFound() {
        EcssBillDTO dto = new EcssBillDTO().setSubmitTimes(4);
        when(iwmsEcssRepository.getApplyBillToInfor(dto)).thenReturn(Collections.emptyList());

        iwmsEcssService.sendInforFailMail();

        verify(iwmsEcssRepository, times(1)).getApplyBillToInfor(dto);
        verifyNoMoreInteractions(iwmsEcssRepository);
    }

    @Test
    public void testSendInforFailMail_WithRecordsFound() {
        EcssBillDTO dto = new EcssBillDTO().setSubmitTimes(4);
        List<EcssBillDTO> list = Arrays.asList(
                new EcssBillDTO().setReferenceNumber("LTH123"),
                new EcssBillDTO().setReferenceNumber("STH456")
        );
        when(iwmsEcssRepository.getApplyBillToInfor(dto)).thenReturn(list);
        when(onlineFallBackApplyBillRepository.getSysLookupValues(anyString())).thenReturn(Collections.emptyList());

        iwmsEcssService.sendInforFailMail();

        verify(iwmsEcssRepository, times(1)).getApplyBillToInfor(dto);
        verify(onlineFallBackApplyBillRepository, times(1)).getSysLookupValues(anyString());
    }
}
/*Ended by AICoder, pid:295f827e66qcf00141b1088be1017a7c5ce9db14*/