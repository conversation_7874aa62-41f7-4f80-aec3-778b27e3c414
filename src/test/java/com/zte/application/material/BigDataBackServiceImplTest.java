package com.zte.application.material;

import com.zte.application.material.impl.BigDataBackServiceImpl;
import com.zte.domain.model.material.*;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.INT_1000;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BigDataBackServiceImplTest {

    @InjectMocks
    private  BigDataBackServiceImpl bigDataBackService;

    @Mock
    private static final Logger logger = LoggerFactory.getLogger(BigDataBackServiceImpl.class);

    @Mock
    private StTallyPackingHeadRepository stTallyPackingHeadRepository;
    @Mock
    private StTallyPackingDetailBackRepository stTallyPackingDetailBackRepository;
    @Mock
    private StDeliveryBackRepository stDeliveryBackRepository;
    @Mock
    private StDeliveryRepository stDeliveryRepository;
    @Mock
    private StTallyPackingDetailRepository stTallyPackingDetailRepository;

    @Mock
    private
    StTallyPackingHeadBackRepository stTallyPackingHeadBackRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void backTallyPacking() throws Exception {
        StTallyPackingHead st=new StTallyPackingHead();
        st.setTallyPackingHeadGuid("ecc582f9-45b8-445f-9e3c-5b2043505486");
        List<StTallyPackingHead> stList=new ArrayList<>();
        stList.add(st);

        PowerMockito.when(stTallyPackingHeadRepository.getStTallyPackingHead()).thenReturn(stList);

        List<List<StTallyPackingHead>> lists =new ArrayList<>();
        lists.add(stList);

        PowerMockito.when(CommonUtils.splitList(stList,INT_1000)).thenReturn(lists);
        Whitebox.invokeMethod(bigDataBackService, "backTallyPacking");

        Exception e=new Exception();
        logger.error("backTallyPacking : Exception", e);
        Whitebox.invokeMethod(bigDataBackService, "backTallyPacking");
        Assert.assertTrue(Objects.nonNull(st));
    }
}
