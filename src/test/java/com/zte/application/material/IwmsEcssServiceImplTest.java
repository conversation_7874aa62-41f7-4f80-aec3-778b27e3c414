package com.zte.application.material;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.material.impl.IwmsEcssServiceImpl;
import com.zte.application.onlinefallback.impl.OnlineFallBackServiceImpl;
import com.zte.application.step.ZteAlibabaService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.material.IwmsEcssRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.ZteAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_16;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, RemoteServiceDataUtil.class})
public class IwmsEcssServiceImplTest {

    @InjectMocks
    IwmsEcssServiceImpl iwmsEcssServiceImpl;
    @Mock
    private IwmsEcssRepository iwmsEcssRepository;
    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;
    @Mock
    private OnlineFallBackServiceImpl onlineFallBackService;
    @Mock
    private EmailUtil emailUtil;
    @Value("${ecss.url}")
    private String ecssUrl;
    @Value("${ecss.auth.value}")
    private String ecssAuth;
    @Value("${ecss.feederSystem.token}")
    private String ecssToken;

    @Mock
    private ZteAlibabaService zteAlibabaService;


    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Mock
    private ZteAlibabaRepository zteAlibabaRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;


    @Before
    public void init() {
        PowerMockito.mockStatic(EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, RemoteServiceDataUtil.class);
    }

    @Test
    public void pushBillToEcss() throws Exception {

        EcssBillDTO dto = new EcssBillDTO();
        dto.setReferenceNumber("STH2007030004");
        List<EcssBillDTO> ecssBillList = new ArrayList<>();
        PowerMockito.when(iwmsEcssRepository.getAddingBillNo(dto)).thenReturn(ecssBillList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "pushBillToEcss", dto);

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("STH2007030004");
        ecssBillDTO.setFailNum(0);
        ecssBillList.add(ecssBillDTO);
        PowerMockito.when(iwmsEcssRepository.getAddingBillNo(dto)).thenReturn(ecssBillList);

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        ServiceData<?> res = new ServiceData<>();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        retCode.setMsg("2143124");
        res.setCode(retCode);

        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssUrl").set(iwmsEcssServiceImpl, "http://zecss.test.zte.com.cn");
        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssAuth").set(iwmsEcssServiceImpl, "x3W10I80hptuo3pm");
        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssToken").set(iwmsEcssServiceImpl, "phPm135F5pDTNGvS9hM6MZbUhlrubpaQjLZMCNbBzBnixb9YnbwT62z2tlZJI1MC");
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getFsDocumentDTO", ecssBillDTO.getReferenceNumber());
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "invokeBillToEcss", JSONObject.toJSONString(fsDocumentDTO), ECSS_BILL_ASYNC);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "dealEcssBill",ecssBillDTO, fsDocumentDTO, res, "23423");
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "pushBillToEcss", dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getFsDocumentDTO() throws Exception {

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        fsDocumentDTO.setReferenceNumber("LTH2004160001");
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getCode", fsDocumentDTO.getReferenceNumber());
        List<EcssBillDTO> materialInfoList = new ArrayList<>();
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getFsDocumentDTO", "LTH2004160001");
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("LTH2004160001");
        materialInfoList.add(ecssBillDTO);
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getFsDocumentDTO", "LTH2004160001");


        FsDocumentDTO fsDocumentDTO2 = new FsDocumentDTO();
        fsDocumentDTO2.setReferenceNumber("STH2007030004");
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getCode", fsDocumentDTO2.getReferenceNumber());
        List<EcssBillDTO> materialInfoList2 = new ArrayList<>();
        PowerMockito.when(iwmsEcssRepository.getFallbackApplyMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList2);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getFsDocumentDTO", "STH2007030004");
        EcssBillDTO ecssBillDTO2 = new EcssBillDTO();
        ecssBillDTO2.setReferenceNumber("STH2007030004");
        materialInfoList2.add(ecssBillDTO2);
        PowerMockito.when(iwmsEcssRepository.getFallbackApplyMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList2);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getFsDocumentDTO", "STH2007030004");
        Assert.assertTrue(Objects.nonNull(fsDocumentDTO));
    }

    @Test
    public void getCode() throws Exception {
        String test = "23141";
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "getCode", "STH2007030004");
        Assert.assertTrue(Objects.nonNull(test));
    }

    @Test
    public void invokeBillToEcss() throws Exception {

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssUrl").set(iwmsEcssServiceImpl, "http://zecss.test.zte.com.cn");
        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssAuth").set(iwmsEcssServiceImpl, "x3W10I80hptuo3pm");
        PowerMockito.field(IwmsEcssServiceImpl.class, "ecssToken").set(iwmsEcssServiceImpl, "phPm135F5pDTNGvS9hM6MZbUhlrubpaQjLZMCNbBzBnixb9YnbwT62z2tlZJI1MC");
        Map<String, String> header = new HashMap<>(INT_16);
        header.put(X_AUTH_VALUE, ecssAuth);
        header.put(X_FEEDER_SYSTEM_TOKEN, ecssToken);
        header.put(X_LANG_ID, X_LANG_ID_ZH);
        header.put(CONTENT_TYPE, APPLICATION_JSON);
        String url = ecssUrl + ECSS_BILL_ASYNC;
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(fsDocumentDTO), header)).thenReturn(result);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "invokeBillToEcss", JSONObject.toJSONString(fsDocumentDTO), "64574745");
        Assert.assertTrue(Objects.nonNull(result));
    }


    @Test
    public void dealEcssBill() throws Exception{

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        EcssBillDTO dto = new EcssBillDTO();
        dto.setEnabledFlag("N");
        dto.setFailNum(0);
        dto.setReferenceNumber("LTH2004160001");
        ServiceData<?> res = new ServiceData<>();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        retCode.setMsg("2143124");
        res.setCode(retCode);
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        PowerMockito.when(iwmsEcssRepository.insertOrUpdateEcssBill(ecssBillDTO)).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.updateReturnApplyBill(ecssBillDTO)).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "dealEcssBill", dto, fsDocumentDTO, res, "2134252");

        dto.setEnabledFlag("N");
        dto.setFailNum(3);
        dto.setReferenceNumber("STH2007030004");
        RetCode retCode2 = new RetCode();
        retCode2.setCode("0001");
        retCode2.setMsg("2143124");
        res.setCode(retCode2);
        PowerMockito.when(iwmsEcssRepository.insertOrUpdateEcssBill(ecssBillDTO)).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.updateFallbackApplyBill(ecssBillDTO)).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendFailMail", Mockito.anyString());
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "dealEcssBill", dto, fsDocumentDTO, res, "2134252");
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void sendFailMail() throws Exception{

        List<StSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendFailMail", Mockito.anyString());

        StSysLookupValuesDTO lookupValuesDTO = new StSysLookupValuesDTO();
        lookupValuesDTO.setLookupMeaning("21314");
        sysLookupValuesDTOList.add(lookupValuesDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendFailMail", Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(sysLookupValuesDTOList));
    }

    @Test
    public void callBackEcssToIwms() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class);

        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        BusiAssertException.isEmpty(dto, MessageId.CALL_BACK_DATA_EMPTY);
        BusiAssertException.isEmpty(dto.getRefNo(), MessageId.BILL_NO_EMPTY);
        dto.setRefNo("LTH2004160001");
        dto.setStatus("RELEASED");
        dto.setCStatus("S");

        List<EcssBillDTO> materialInfoList1 = new ArrayList<>();
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("LTH2004160001");
        ecssBillDTO.setStatus("ECSSAUDITED");
        ecssBillDTO.setEcssResult("通过");
        materialInfoList1.add(ecssBillDTO);
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);

        List<EcssBillDTO> materialInfoList5 = new ArrayList<>();
        EcssBillDTO ecssBillDTO5 = new EcssBillDTO();
        ecssBillDTO5.setReferenceNumber("LTH2004160001");
        ecssBillDTO5.setStatus("ECSSAUDITED");
        ecssBillDTO5.setEcssResult("不通过");
        materialInfoList5.add(ecssBillDTO5);
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList5);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);

        List<EcssBillDTO> materialInfoList2 = new ArrayList<>();
        EcssBillDTO ecssBillDTO2 = new EcssBillDTO();
        ecssBillDTO2.setReferenceNumber("LTH2004160001");
        ecssBillDTO2.setStatus("ECSSAUDITING");
        materialInfoList2.add(ecssBillDTO2);
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList2);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);

        List<EcssBillDTO> materialInfoList3 = new ArrayList<>();
        EcssBillDTO ecssBillDTO3 = new EcssBillDTO();
        ecssBillDTO3.setReferenceNumber("STH2007030004");
        ecssBillDTO3.setStatus("ECSSAUDITING");
        materialInfoList3.add(ecssBillDTO3);
        PowerMockito.when(iwmsEcssRepository.getFallbackApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList3);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);

        List<EcssBillDTO> materialInfoList6 = new ArrayList<>();
        EcssBillDTO ecssBillDTO6 = new EcssBillDTO();
        ecssBillDTO6.setReferenceNumber("STH2007030004");
        ecssBillDTO6.setStatus("ECSSAUDITED");
        ecssBillDTO6.setEcssResult("通过");
        materialInfoList6.add(ecssBillDTO6);
        PowerMockito.when(iwmsEcssRepository.getReturnApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList6);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);

        List<EcssBillDTO> materialInfoList4 = new ArrayList<>();
        EcssBillDTO ecssBillDTO4 = new EcssBillDTO();
        ecssBillDTO4.setReferenceNumber("STH2007030004");
        ecssBillDTO4.setStatus("ECSSAUDITED");
        ecssBillDTO4.setEcssResult("不通过");
        materialInfoList4.add(ecssBillDTO4);
        PowerMockito.when(iwmsEcssRepository.getFallbackApplyMaterialInfo(Mockito.any())).thenReturn(materialInfoList4);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "callBackEcssToIwms", dto);
        Assert.assertTrue(Objects.nonNull(dto));

    }

    @Test
    public void updateEcssDTO() throws Exception{

        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        dto.setRefNo("2342356");
        dto.setStatus("RELEASED");
        dto.setCStatus("S");

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber(dto.getRefNo());
        ecssBillDTO.setReturnParam(org.apache.commons.lang.StringUtils.substring(JSONObject.toJSONString(dto), NumConstant.INT_0, NumConstant.INT_4000));
        ecssBillDTO.setBillStatus(ECSS_BILL_ADDED);
        ecssBillDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        PowerMockito.when(iwmsEcssRepository.updateEcssBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto);
        PowerMockito.when(iwmsEcssRepository.failEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.insertEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.failEcssTextretruncollection(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);

        List<FsfReasonDTO> fsfReasonDTOS = new ArrayList<>();
        FsfReasonDTO fsfReasonDTO = new FsfReasonDTO();
        fsfReasonDTO.setFReason("234234");
        fsfReasonDTOS.add(fsfReasonDTO);
        dto.setFsFReasonDTOs(fsfReasonDTOS);
        PowerMockito.when(iwmsEcssRepository.updateEcssBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto);
        PowerMockito.when(iwmsEcssRepository.failEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.insertEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.failEcssTextretruncollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.insertEcssTextretruncollection(Mockito.anyList())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);

        List<String> list = new ArrayList<>();
        list.add("23424");
        dto.setFMessages(list);
        PowerMockito.when(iwmsEcssRepository.updateEcssBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto);
        PowerMockito.when(iwmsEcssRepository.failEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.insertEcssOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.failEcssTextretruncollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.insertEcssTextretruncollection(Mockito.anyList())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateEcssDTO", dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void updateApplyBill() throws Exception{

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("LTH2004160001");
        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        dto.setStatus("RELEASED");
        dto.setCStatus("E");
        PowerMockito.when(iwmsEcssRepository.updateReturnApplyBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto);

        DocumentCallbackResultDTO dto2 = new DocumentCallbackResultDTO();
        dto2.setStatus("RELEASED");
        dto2.setCStatus("S");
        PowerMockito.when(iwmsEcssRepository.updateReturnApplyBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto2);

        ecssBillDTO.setReferenceNumber("STH2007030004");
        PowerMockito.when(iwmsEcssRepository.updateFallbackApplyBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto);

        PowerMockito.when(iwmsEcssRepository.updateFallbackApplyBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "updateApplyBill", ecssBillDTO, dto2);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void submitApllyToInforSo() throws Exception{

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("LTH2004160001");
        List<EcssBillDTO> list = new ArrayList<>();
        PowerMockito.when(iwmsEcssRepository.getApplyBillToInfor(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "submitApllyToInforSo", ecssBillDTO);

        list.add(ecssBillDTO);
        EcssBillDTO ecssBillDTO2 = new EcssBillDTO();
        ecssBillDTO2.setReferenceNumber("STH2007030004");
        list.add(ecssBillDTO2);
        PowerMockito.when(iwmsEcssRepository.getApplyBillToInfor(Mockito.any())).thenReturn(list);
        ServiceData serviceData = new ServiceData();
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                Mockito.any())).thenReturn(serviceData);
        Whitebox.invokeMethod(onlineFallBackService, "submitApllyToInforSo", ecssBillDTO.getReferenceNumber());
        PowerMockito.when(iwmsEcssRepository.updateReturnApplyBill(Mockito.any())).thenReturn(1);
        PowerMockito.when(iwmsEcssRepository.updateFallbackApplyBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendInforFailMail");
//        Whitebox.invokeMethod(iwmsEcssServiceImpl, "submitApllyToInforSo", ecssBillDTO);
        Assert.assertTrue(Objects.nonNull(ecssBillDTO));
    }

    @Test
    public void sendInforFailMail() throws Exception{

        List<EcssBillDTO> list = new ArrayList<>();
        PowerMockito.when(iwmsEcssRepository.getApplyBillToInfor(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendInforFailMail");

        List<StSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendInforFailMail");

        EcssBillDTO dto = new EcssBillDTO();
        dto.setReferenceNumber("STH2007030004");
        list.add(dto);
        PowerMockito.when(iwmsEcssRepository.getApplyBillToInfor(Mockito.any())).thenReturn(list);

        StSysLookupValuesDTO lookupValuesDTO = new StSysLookupValuesDTO();
        lookupValuesDTO.setLookupMeaning("21314");
        sysLookupValuesDTOList.add(lookupValuesDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(iwmsEcssServiceImpl, "sendInforFailMail");
        Assert.assertTrue(Objects.nonNull(sysLookupValuesDTOList));
    }

    @Test
    public void sendEsccRedDotTask() throws Exception {
        DocumentCallbackResultDTO documentCallbackResultDTO = new DocumentCallbackResultDTO();
        EcssBillDTO dto = new EcssBillDTO();
        String billType = "LTH";
        iwmsEcssServiceImpl.sendEsccRedDotTask(documentCallbackResultDTO, billType);
        billType = "STH";
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("111");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupMeaning("222");
        sysLookupValuesDTOList.add(sysLookupValuesDTO1);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(new SysLookupValuesDTO().setLookupType("1000072"))).thenReturn(sysLookupValuesDTOList);
        iwmsEcssServiceImpl.sendEsccRedDotTask(documentCallbackResultDTO, billType);
        List<EcssBillDTO> ecssBillDTOS = new ArrayList<>();
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("11111");
        ecssBillDTO.setCreatedBy("1010");
        ecssBillDTO.setItemNo("001");
        ecssBillDTOS.add(ecssBillDTO);
        PowerMockito.when(iwmsEcssRepository.queryFallbackApplyInfo(dto)).thenReturn(ecssBillDTOS);
        iwmsEcssServiceImpl.sendEsccRedDotTask(documentCallbackResultDTO, billType);
        ecssBillDTO.setWarehouseNo("11");
        PowerMockito.when(iwmsEcssRepository.queryFallbackApplyInfo(dto)).thenReturn(ecssBillDTOS);
        iwmsEcssServiceImpl.sendEsccRedDotTask(documentCallbackResultDTO, billType);
        try {
            PowerMockito.when(iwmsEcssRepository.queryFallbackApplyInfo(dto)).thenThrow(new NullPointerException());
            iwmsEcssServiceImpl.sendEsccRedDotTask(documentCallbackResultDTO, billType);
        }catch (Exception ex) {
            ex.printStackTrace();
        }
        Assert.assertTrue(Objects.nonNull(ecssBillDTOS));
    }

}
