package com.zte.application.ems;

import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.ems.impl.EmsServiceImpl;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.vo.JobLogHisVO;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, AlarmHelper.class})
public class EmsServiceImplTest {

    @InjectMocks
    private EmsServiceImpl emsService;
    @Mock
    private StepIscpRepository stepIscpRepository;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;

    @Before
    public void init(){
        PowerMockito.mockStatic(BusiAssertException.class, AlarmHelper.class);
    }

    @Test
    public void doMrpMinitor() throws Exception{
        PowerMockito.mockStatic(AlarmHelper.class);

        List<String> jobList = new ArrayList<>();
        jobList.add("1111");
        List<JobLogHisVO> list = new ArrayList<>();
        JobLogHisVO jobLogHisVO = new JobLogHisVO();
        jobLogHisVO.setBeginDate("2023-04-10");
        jobLogHisVO.setInsId(21314L);
        jobLogHisVO.setJobName("12312341");
        StringBuilder error = new StringBuilder();
        for (int i=0; i<600; i++) {
            error.append("1");
        }
        jobLogHisVO.setErrors(error.toString());
        list.add(jobLogHisVO);
        JobLogHisVO jobLogHisVO1 = new JobLogHisVO();
        jobLogHisVO1.setBeginDate("2023-04-10");
        jobLogHisVO1.setInsId(21314L);
        jobLogHisVO1.setJobName("12312341");
        jobLogHisVO1.setErrors("");
        list.add(jobLogHisVO1);
        PowerMockito.when(inforIwmsIscpRepository.getDicList(MRP_DICS)).thenReturn(jobList);
        PowerMockito.when(stepIscpRepository.getMonitorListMrp(jobList)).thenReturn(list);

        PowerMockito.when(stepIscpRepository.getCountJobUnknown(21314L)).thenReturn(0);
        Whitebox.invokeMethod(emsService,"doMrpMinitor");

        PowerMockito.when(stepIscpRepository.getCountJobUnknown(21314L)).thenReturn(1);
        Whitebox.invokeMethod(emsService,"doMrpMinitor");
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void concelAlarm() throws Exception{
        PowerMockito.mockStatic(AlarmHelper.class);

        String jobName = "2342352";
        Whitebox.invokeMethod(emsService,"concelAlarm", jobName);
        Assert.assertTrue(Objects.nonNull(jobName));
    }
}
