/*Started by AICoder, pid:2425fk33f0e971314ec208ac903d7a9172933992*/
package com.zte.application.step;

import com.zte.application.infor.impl.InventoryDiffQueryServiceImpl;
import com.zte.application.step.impl.ZteAlibabaStockInfoUploadServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.EdiPoSRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.utils.CommonUtils;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, UUID.class, HttpClientUtil.class})
public class ZteAlibabaStockInfoUploadServiceImplTest {

    @InjectMocks
    private ZteAlibabaStockInfoUploadServiceImpl zteAlibabaStockInfoUploadService;

    private List<CustomerInventoryLinesDTO> customerInventoryLinesDTO;

    private List<CustomerDataLogDTO> customerDataLogDTOList;

    @Mock
    private CustomerInventoryLinesDTO mockInventoryItem;

    private List<CustomerInventoryLinesDTO> inventoryList;

    @Mock
    private ZteAlibabStockInfoUploadRepository zteAlibabaStockInfoUploadRepository;

    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;

    private List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList;

    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private EdiPoSRepository ediPoSRepository;

    @Mock
    private InventoryDiffQueryServiceImpl inventoryDiffQueryService;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
    }

    @SneakyThrows
    @Test
    public void testBuildZmsAliStockInfo_FullParams() {
        // 构造输入数据
        List<CustomerInventoryLinesDTO> tempList = new ArrayList<>();
        CustomerInventoryLinesDTO dto1 = new CustomerInventoryLinesDTO();
        dto1.setInventoryType("1");
        dto1.setConfigModel("model1");
        dto1.setMpn("mpn1");
        dto1.setInventoryBatch("batch1");
        dto1.setVendorInventoryQuantity(new java.math.BigDecimal("10"));
        dto1.setDeliveringQuantity(new java.math.BigDecimal("2"));
        dto1.setDeliveredQuantity(new java.math.BigDecimal("8"));
        dto1.setItemType(1);
        tempList.add(dto1);
        CustomerInventoryLinesDTO dto2 = new CustomerInventoryLinesDTO();
        dto2.setInventoryType("0");
        dto2.setConfigModel("model2");
        dto2.setMpn("mpn2");
        dto2.setInventoryBatch("batch2");
        dto2.setVendorInventoryQuantity(new java.math.BigDecimal("20"));
        dto2.setDeliveringQuantity(new java.math.BigDecimal("3"));
        dto2.setDeliveredQuantity(new java.math.BigDecimal("17"));
        dto2.setItemType(2);
        tempList.add(dto2);
        // 反射设置messageType字段
        Whitebox.setInternalState(zteAlibabaStockInfoUploadService, "messageType", "MSG_TYPE");
        // 调用方法
        ZmsALiStockInfoDTO result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "buildZmsAliStockInfo", tempList);
        // 断言
        assertNotNull(result);
        assertEquals("1", result.getInventoryType());
        assertEquals("ZTE101", result.getFactoryCode());
        assertEquals("ZTE", result.getMachineBrand()); // inventoryType为1时
        assertEquals(2, result.getCustomerInventoryList().size());
        // 校验customerInventoryList内容
        CustomerInventoryUpdateDTO update1 = result.getCustomerInventoryList().get(0);
        assertEquals("model1", update1.getConfigModel());
        assertEquals("mpn1", update1.getMpn());
        assertEquals("batch1", update1.getInventoryBatch());
        assertEquals(new java.math.BigDecimal("10"), update1.getVendorInventoryQuantity());
        assertEquals(new java.math.BigDecimal("2"), update1.getDeliveringQuantity());
        assertEquals(new java.math.BigDecimal("8"), update1.getDeliveredQuantity());
        assertEquals(Integer.valueOf(1), update1.getItemType());
        CustomerInventoryUpdateDTO update2 = result.getCustomerInventoryList().get(1);
        assertEquals("model2", update2.getConfigModel());
        assertEquals("mpn2", update2.getMpn());
        assertEquals("batch2", update2.getInventoryBatch());
        assertEquals(new java.math.BigDecimal("20"), update2.getVendorInventoryQuantity());
        assertEquals(new java.math.BigDecimal("3"), update2.getDeliveringQuantity());
        assertEquals(new java.math.BigDecimal("17"), update2.getDeliveredQuantity());
        assertEquals(Integer.valueOf(2), update2.getItemType());
    }

    @SneakyThrows
    @Test
    public void testCreateZmsAliStockInfo_FullParams() {
        // 构造输入数据
        List<CustomerInventoryLinesDTO> tempList = new ArrayList<>();
        CustomerInventoryLinesDTO dto1 = new CustomerInventoryLinesDTO();
        dto1.setInventoryType("1");
        dto1.setConfigModel("model1");
        dto1.setMpn("mpn1");
        dto1.setInventoryBatch("batch1");
        dto1.setVendorInventoryQuantity(new java.math.BigDecimal("10"));
        dto1.setDeliveringQuantity(new java.math.BigDecimal("2"));
        dto1.setDeliveredQuantity(new java.math.BigDecimal("8"));
        dto1.setItemType(1);
        tempList.add(dto1);
        CustomerInventoryLinesDTO dto2 = new CustomerInventoryLinesDTO();
        dto2.setInventoryType("0");
        dto2.setConfigModel("model2");
        dto2.setMpn("mpn2");
        dto2.setInventoryBatch("batch2");
        dto2.setVendorInventoryQuantity(new java.math.BigDecimal("20"));
        dto2.setDeliveringQuantity(new java.math.BigDecimal("3"));
        dto2.setDeliveredQuantity(new java.math.BigDecimal("17"));
        dto2.setItemType(2);
        tempList.add(dto2);
        String uuId = "test-uuid";
        String inventoryDirective = "ZTE-20240101-1234567890";
        String timestamp = "1700000000000";
        int customerInventoryLineNum = 1;
        // 反射设置messageType字段
        Whitebox.setInternalState(zteAlibabaStockInfoUploadService, "messageType", "MSG_TYPE");
        // 调用方法
        ZmsALiStockInfoDTO result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "createZmsAliStockInfo", tempList, uuId, inventoryDirective, timestamp, customerInventoryLineNum);
        // 断言
        assertNotNull(result);
        assertEquals("1", result.getInventoryType());
        assertEquals(inventoryDirective, result.getInventoryDirective());
        assertEquals("ZTE101", result.getFactoryCode());
        assertEquals(timestamp, result.getVersionSeqNo());
        assertEquals("ZTE", result.getMachineBrand()); // inventoryType为1时
        assertNotNull(result.getCustomerInventoryList());
        assertEquals(2, result.getCustomerInventoryList().size());
        // 校验customerInventoryList内容
        CustomerInventoryUpdateDTO update1 = result.getCustomerInventoryList().get(0);
        assertEquals("1", update1.getCustomerInventoryLineNumber());
        assertEquals("model1", update1.getConfigModel());
        assertEquals("mpn1", update1.getMpn());
        assertEquals("batch1", update1.getInventoryBatch());
        assertEquals(new java.math.BigDecimal("10"), update1.getVendorInventoryQuantity());
        assertEquals(new java.math.BigDecimal("2"), update1.getDeliveringQuantity());
        assertEquals(new java.math.BigDecimal("8"), update1.getDeliveredQuantity());
        assertEquals(Integer.valueOf(1), update1.getItemType());
        CustomerInventoryUpdateDTO update2 = result.getCustomerInventoryList().get(1);
        assertEquals("2", update2.getCustomerInventoryLineNumber());
        assertEquals("model2", update2.getConfigModel());
        assertEquals("mpn2", update2.getMpn());
        assertEquals("batch2", update2.getInventoryBatch());
        assertEquals(new java.math.BigDecimal("20"), update2.getVendorInventoryQuantity());
        assertEquals(new java.math.BigDecimal("3"), update2.getDeliveringQuantity());
        assertEquals(new java.math.BigDecimal("17"), update2.getDeliveredQuantity());
        assertEquals(Integer.valueOf(2), update2.getItemType());
    }

    /*Started by AICoder, pid:116f4sd7c6511bf14ce80bc211ce3d472d633b18*/
    @SneakyThrows
    @Test(expected = Exception.class)
    public void testPushDataAndUploadLog_NoSysLookupValues() {
        // Given
        String empNo = "12345";
        Boolean isRetry = false;

        when(inventoryholdRecordRepository.getLookupValue(anyString())).thenReturn(null);

        // When
        Whitebox.invokeMethod(zteAlibabaStockInfoUploadService, "pushDataAndUploadLog", customerInventoryLinesDTO, empNo, isRetry);
        // Then
        verify(zteStockInfoUploadRepository, never()).insertOrUpdateStockUploadLog(anyList());
        verify(imesCenterfactoryRemoteService, never()).pushDataToB2BKafKa(anyList(), anyString());
    }

    @SneakyThrows
    @Test
    public void testPushDataAndUploadLog() {
        // Given
        String empNo = "SYSTEM";
        Boolean isRetry = false;
        initMockInfo();

        when(zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(anyList())).thenReturn(1);
        doNothing().when(imesCenterfactoryRemoteService).pushDataToB2BKafKa(anyList(), anyString());

        // When
        Whitebox.invokeMethod(zteAlibabaStockInfoUploadService, "pushDataAndUploadLog", customerInventoryLinesDTO, isRetry);

        assertNotNull(customerInventoryLinesDTO);
    }

    @SneakyThrows
    @Test
    public void testPushDataAndUploadLog_Retry() {
        // Given
        String empNo = "SYSTEM";
        Boolean isRetry = true;
        initMockInfo();
        when(zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(anyList())).thenReturn(1);
        doNothing().when(imesCenterfactoryRemoteService).pushDataToB2BKafKa(anyList(), anyString());
        // When
        CustomerInventoryLinesDTO badInventory3 = new CustomerInventoryLinesDTO();
        badInventory3.setMessageId("messageId4");
        badInventory3.setKeywords("messageId4");
        customerInventoryLinesDTO.add(badInventory3);

        Whitebox.invokeMethod(zteAlibabaStockInfoUploadService, "pushDataAndUploadLog", customerInventoryLinesDTO, isRetry);
        when(ediPoSRepository.getTransCountLimit(Constant.LOOKUP_CODE_100008800006)).thenReturn(null);
        Whitebox.invokeMethod(zteAlibabaStockInfoUploadService, "pushDataAndUploadLog", customerInventoryLinesDTO, isRetry);

        assertNotNull(customerInventoryLinesDTO);
    }
    /*Ended by AICoder, pid:116f4sd7c6511bf14ce80bc211ce3d472d633b18*/

    private void initMockInfo() {
        // Setup code if needed
        customerInventoryLinesDTO = new ArrayList<>();
        CustomerInventoryLinesDTO goodInventory = new CustomerInventoryLinesDTO();
        goodInventory.setInventoryType("0");
        goodInventory.setMessageId("messageId1");
        goodInventory.setKeywords("messageId1");
        customerInventoryLinesDTO.add(goodInventory);

        CustomerInventoryLinesDTO badInventory = new CustomerInventoryLinesDTO();
        badInventory.setInventoryType("5");
        badInventory.setMessageId("messageId2");
        badInventory.setKeywords("messageId2");
        customerInventoryLinesDTO.add(badInventory);

        CustomerInventoryLinesDTO badInventory1 = new CustomerInventoryLinesDTO();
        badInventory1.setInventoryType("1");
        badInventory1.setMessageId("messageId3");
        badInventory1.setKeywords("messageId3");
        customerInventoryLinesDTO.add(badInventory1);

        inventoryList = new ArrayList<>();
        zteStockInfoUploadLogDTOList = new ArrayList<>();
        customerDataLogDTOList = new ArrayList<>();

        CustomerInventoryLinesDTO item1 = new CustomerInventoryLinesDTO();
        item1.setInventoryType("type1");
        item1.setInventoryDirective("directive1");
        item1.setVersionSeqNo("version1");

        CustomerInventoryLinesDTO item2 = new CustomerInventoryLinesDTO();
        item2.setInventoryType("type2");
        item2.setInventoryDirective("directive2");
        item2.setVersionSeqNo("version2");

        inventoryList.add(item1);
        inventoryList.add(item2);

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("testMessage");
        when(inventoryholdRecordRepository.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);

        List<ZteWarehouseInfoDTO> warehouseList = new ArrayList<>();
        ZteWarehouseInfoDTO warehouseInfoDTO = new ZteWarehouseInfoDTO();
        warehouseInfoDTO.setWarehouseId("wmwhse01");
        warehouseInfoDTO.setDbType(1);
        warehouseList.add(warehouseInfoDTO);
        when(zteAlibabaStockInfoUploadRepository.getInforWarehouseList()).thenReturn(warehouseList);
        when(ediPoSRepository.getTransCountLimit(anyString())).thenReturn(2);
    }
    /*Ended by AICoder, pid:351c26ac38w3153140f2093df1e5d107ee99a94e*/


    /*Started by AICoder, pid:a2a95yc4f45b3df145bd092af18f507aa592d36f*/
    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_WithExceptionInGetInventoryStatisticsDataNull() throws Exception {
        initMockInfo();
        String empNo = "testEmpNo";
        // Mocking the repository methods

        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);

        doThrow(new RuntimeException("Test Exception")).when(imesCenterfactoryRemoteService).pushDataToB2BKafKa(anyList(), anyString());

        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_WithExceptionInPushDataAndUploadLog() throws Exception {
        initMockInfo();
        String empNo = "testEmpNo";
        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        CustomerInventoryLinesDTO goodInventory = new CustomerInventoryLinesDTO();
        goodInventory.setInventoryType("0");
        goodInventory.setMessageId("messageId1");
        mergedData.add(goodInventory);

        CustomerInventoryLinesDTO badInventory = new CustomerInventoryLinesDTO();
        badInventory.setInventoryType("5");
        goodInventory.setMessageId("messageId2");
        mergedData.add(badInventory);
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);
        when(inventoryholdRecordRepository.getLookupValue(anyString())).thenReturn(null);

        // Execute the method under test and expect an exception
        //BusiAssertException.result(() -> service.aliStockInfoUpload(empNo), "上传数据异常: Test Exception");
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }
    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_Query_IS_NULL() throws Exception {
        initMockInfo();
        String empNo = "testEmpNo";
        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        CustomerInventoryLinesDTO goodInventory = new CustomerInventoryLinesDTO();
        goodInventory.setInventoryType("0");
        goodInventory.setMessageId("messageId1");
        mergedData.add(goodInventory);

        CustomerInventoryLinesDTO badInventory = new CustomerInventoryLinesDTO();
        badInventory.setInventoryType("5");
        goodInventory.setMessageId("messageId2");
        mergedData.add(badInventory);
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);

        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }
    @Test
    public void testAliStockInfoUpload_Sucessed() throws Exception {
        initMockInfo();
        String empNo = "testEmpNo";
        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        CustomerInventoryLinesDTO goodInventory = new CustomerInventoryLinesDTO();
        goodInventory.setInventoryType("0");
        goodInventory.setMessageId("messageId1");
        mergedData.add(goodInventory);

        CustomerInventoryLinesDTO badInventory = new CustomerInventoryLinesDTO();
        badInventory.setInventoryType("5");
        goodInventory.setMessageId("messageId2");
        mergedData.add(badInventory);
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);

        String responseStr =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":1,\"size\":200,\"total\":1,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);

        verify(zteAlibabaStockInfoUploadRepository, times(1)).addInventoryMergedData(anyList());
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        List<CustomerInventoryPickUpDTO> inforInventoryList = Arrays.asList(
                new CustomerInventoryPickUpDTO(), new CustomerInventoryPickUpDTO()
        );
        when(zteAlibabaStockInfoUploadRepository.getALiStockInfoStaticsList(any(ZteWarehouseInfoDTO.class))).thenReturn(inforInventoryList);

        List<CustomerInventoryPickUpDTO> productInventoryList = new ArrayList<>();
        when(inventoryDiffQueryService.getProductInventory(Mockito.any())).thenReturn(productInventoryList);

        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);

        // Execute the method under test
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_fetchInforInventory_exception() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        doThrow(new RuntimeException("Test Exception")).when(zteAlibabaStockInfoUploadRepository).getALiStockInfoStaticsList(any(ZteWarehouseInfoDTO.class));
        // Execute the method under test
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test
    public void testFetchInforInventory_ZteWarehouseInfoDTOIsNull() throws Exception {
        String empNo = "testEmpNo";

        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO = new ArrayList<>();
        // Execute the method under test
        List<CustomerInventoryPickUpDTO> result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "fetchInforInventory", zteWarehouseInfoDTO, empNo);
        assertNotNull(result);
        assertEquals(0, result.size());
    }


    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_warehouseIds_null() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        when(zteAlibabaStockInfoUploadRepository.getInforWarehouseList()).thenReturn(null);

        // Execute the method under test
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_WithExceptionInGetInventoryStatisticsData() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        List<CustomerInventoryPickUpDTO> inforInventoryList = new ArrayList<>();
        when(zteAlibabaStockInfoUploadRepository.getALiStockInfoStaticsList(any(ZteWarehouseInfoDTO.class))).thenReturn(inforInventoryList);

        String responseStr =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":1,\"size\":200,\"total\":1,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        doThrow(new RuntimeException("Test Exception")).when(zteAlibabaStockInfoUploadRepository).getInventoryStatisticsData();

        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_WithExceptionInAddInventoryMergedData() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        List<CustomerInventoryPickUpDTO> inforInventoryList = new ArrayList<>();
        when(zteAlibabaStockInfoUploadRepository.getALiStockInfoStaticsList(any(ZteWarehouseInfoDTO.class))).thenReturn(inforInventoryList);

        String responseStr =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":1,\"size\":200,\"total\":1,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        CustomerInventoryLinesDTO goodInventory = new CustomerInventoryLinesDTO();
        goodInventory.setInventoryType("0");
        goodInventory.setMessageId("messageId1");
        mergedData.add(goodInventory);
        when(zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData()).thenReturn(mergedData);

        doThrow(new RuntimeException("Test Exception")).when(zteAlibabaStockInfoUploadRepository).addInventoryMergedData(anyList());

        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }

    @Test(expected = Exception.class)
    public void testAliStockInfoUpload_WithExceptionInSaveInforInventoryToStatics() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();

        List<CustomerInventoryPickUpDTO> inforInventoryList = new ArrayList<>();
        when(zteAlibabaStockInfoUploadRepository.getALiStockInfoStaticsList(any(ZteWarehouseInfoDTO.class))).thenReturn(inforInventoryList);

        String responseStr =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":1,\"size\":200,\"total\":1,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        doThrow(new RuntimeException("Test Exception")).when(zteAlibabaStockInfoUploadRepository).addInventoryStaticsData(anyList());

        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(empNo);
    }
    /*Ended by AICoder, pid:a2a95yc4f45b3df145bd092af18f507aa592d36f*/


    /*Started by AICoder, pid:c2a9dq84fb0f32614a9f0a54a060f670ce24d2c0*/
    @Test
    public void testSaveSnapshotAndDisableOldData() {
        // Given
        initMockInfo();
        CustomerInventoryLinesDTO dto1 = new CustomerInventoryLinesDTO();
        CustomerInventoryLinesDTO dto2 = new CustomerInventoryLinesDTO();
        List<CustomerInventoryLinesDTO> mergedData = Arrays.asList(dto1, dto2);

        // Mocking repository methods
        when(zteAlibabaStockInfoUploadRepository.addInventoryMergedData(mergedData)).thenReturn(1);
        when(zteAlibabaStockInfoUploadRepository.disableForInventoryStaticsData()).thenReturn(1);

        // When
        zteAlibabaStockInfoUploadService.saveSnapshotAndDisableOldData(mergedData);

        // Then
        verify(zteAlibabaStockInfoUploadRepository, times(1)).addInventoryMergedData(mergedData);
        verify(zteAlibabaStockInfoUploadRepository, times(1)).disableForInventoryStaticsData();
    }
    /*Ended by AICoder, pid:c2a9dq84fb0f32614a9f0a54a060f670ce24d2c0*/


    /*Started by AICoder, pid:02fdae649bq161714a2609af708a9299ed547215*/
    @Test
    public void testSaveInforInventoryToStatics_SingleBatch() {
        List<CustomerInventoryPickUpDTO> inforInventoryList = Arrays.asList(
                new CustomerInventoryPickUpDTO(), new CustomerInventoryPickUpDTO()
        );
        when(zteAlibabaStockInfoUploadRepository.addInventoryStaticsData(anyList())).thenReturn(1);

        zteAlibabaStockInfoUploadService.saveInforInventoryToStatics(inforInventoryList);

        verify(zteAlibabaStockInfoUploadRepository, times(1)).addInventoryStaticsData(anyList());
    }

    @Test
    public void testSaveInforInventoryToStatics_EmptyList() {
        List<CustomerInventoryPickUpDTO> inforInventoryList = Arrays.asList();

        zteAlibabaStockInfoUploadService.saveInforInventoryToStatics(inforInventoryList);

        verify(zteAlibabaStockInfoUploadRepository, times(1)).addInventoryStaticsData(anyList());
    }

    @Test
    public void testAliStockInfoUploadManual_Succeed() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();
        CustomerInventoryLinesDTO dto = new CustomerInventoryLinesDTO();
        dto.setEmpNo(empNo);
        dto.setUploadDateBegin("2025-05-12 00:00:00");
        dto.setUploadDateEnd("2025-05-12 23:59:59");

        when(zteAlibabaStockInfoUploadRepository.getInventoryMergedData(any(CustomerInventoryLinesDTO.class))).thenReturn(customerInventoryLinesDTO);
        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUploadManual(dto);

        verify(zteStockInfoUploadRepository, times(1)).insertOrUpdateStockUploadLog(anyList());
    }

    @Test
    public void testAliStockInfoUploadManual_Data_Null() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();
        CustomerInventoryLinesDTO dto = new CustomerInventoryLinesDTO();
        dto.setEmpNo(empNo);

        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUploadManual(dto);
        verify(zteAlibabaStockInfoUploadRepository, times(1)).getInventoryMergedData(any());
    }

    @Test
    public void testAliStockInfoUploadManual_MergedDataIsNull() throws Exception {
        String empNo = "testEmpNo";
        initMockInfo();
        CustomerInventoryLinesDTO dto = new CustomerInventoryLinesDTO();
        dto.setEmpNo(empNo);
        dto.setUploadDateBegin("2025-05-12 00:00:00");
        dto.setUploadDateEnd("2025-05-12 23:59:59");

        when(zteAlibabaStockInfoUploadRepository.getInventoryMergedData(any(CustomerInventoryLinesDTO.class))).thenReturn(null);
        // Execute the method under test and expect an exception
        zteAlibabaStockInfoUploadService.aliStockInfoUploadManual(dto);

        verify(zteStockInfoUploadRepository, never()).insertOrUpdateStockUploadLog(anyList());
    }

    /*Ended by AICoder, pid:02fdae649bq161714a2609af708a9299ed547215*/
    /*Started by AICoder, pid:4e09cvecdavcfbf143ec081f519f8e8b71414dcf*/
    @Test
    public void testGetServiceData_Success() throws Exception {
        String url = "http://example.com";
        CustomerInventoryRepairQueryDTO repairParam = new CustomerInventoryRepairQueryDTO();
        Map<String, String> headers = new HashMap<>();
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"},\"bo\":{\"key\":\"value\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        CustomerInventoryRepairResultDTO result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "getServiceData", url, repairParam, headers);
        assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void testGetServiceData_EmptyResponse() throws Exception {
        String url = "http://example.com";
        CustomerInventoryRepairQueryDTO repairParam = new CustomerInventoryRepairQueryDTO();
        Map<String, String> headers = new HashMap<>();

        String responseStr = null;
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        CustomerInventoryRepairResultDTO result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "getServiceData", url, repairParam, headers);
    }

    @Test
    public void testGetServiceData_Success_BOIsNull() throws Exception {
        String url = "http://example.com";
        CustomerInventoryRepairQueryDTO repairParam = new CustomerInventoryRepairQueryDTO();
        Map<String, String> headers = new HashMap<>();
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        CustomerInventoryRepairResultDTO result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "getServiceData", url, repairParam, headers);
        assertNull(result);
    }

    /*Ended by AICoder, pid:4e09cvecdavcfbf143ec081f519f8e8b71414dcf*/
    /*Started by AICoder, pid:ad458bc40bo01d6147d408e9415b1333f5c9d1e0*/
    @Test
    public void testFetchRepairInventory_EmptyResult() throws Exception {
        String empNo = "testEmpNo";

        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        List<CustomerInventoryPickUpDTO> result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "fetchRepairInventory", empNo);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testFetchRepairInventory_EmptyRecords() throws Exception {
        String empNo = "testEmpNo";
        String responseStr1 =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":2,\"size\":1,\"total\":2,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        String responseStr2 =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":2,\"pages\":2,\"size\":1,\"total\":2,\"records\":null}}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any()))
                .thenReturn(responseStr1).thenReturn(responseStr2);

        List<CustomerInventoryPickUpDTO> result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "fetchRepairInventory", empNo);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void testFetchRepairInventory_Success_One() throws Exception {
        String empNo = "testEmpNo";

        String responseStr =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":1,\"size\":200,\"total\":1,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        List<CustomerInventoryPickUpDTO> result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "fetchRepairInventory", empNo);
        assertNotNull(result);
        assertEquals(1, result.size());
    }
    @Test
    public void testFetchRepairInventory_Success_Multi() throws Exception {
        String empNo = "testEmpNo";
        String responseStr1 =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":1,\"pages\":2,\"size\":1,\"total\":2,\"records\":[{\"whseid\":\"11111\",\"mpn\":\"mpn1\"}]}}";
        String responseStr2 =  "{\"code\":{\"code\":\"0000\",\"msgId\":\"success\",\"msg\":\"请求成功\"},"
                + "\"bo\":{\"current\":2,\"pages\":2,\"size\":1,\"total\":2,\"records\":[{\"whseid\":\"22222\",\"mpn\":\"mpn2\"}]}}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any()))
                .thenReturn(responseStr1).thenReturn(responseStr2);

        List<CustomerInventoryPickUpDTO> result = Whitebox.invokeMethod(zteAlibabaStockInfoUploadService,
                "fetchRepairInventory", empNo);
        assertNotNull(result);
        assertEquals(2, result.size());
    }
    /*Ended by AICoder, pid:ad458bc40bo01d6147d408e9415b1333f5c9d1e0*/
}
/*Ended by AICoder, pid:2425fk33f0e971314ec208ac903d7a9172933992*/