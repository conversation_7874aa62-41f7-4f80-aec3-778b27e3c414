package com.zte.application.step;

import com.zte.application.step.impl.StepWarnServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.StepWarnRepository;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.anyString;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, EmailUtil.class, WebServiceClient.class})
public class StepWarnServiceImplTest {
    @InjectMocks
    StepWarnServiceImpl stepWarnService;

    @Mock
    private StepWarnRepository stepWarnRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private EmailUtil emailUtil;

    @Mock
    private StepTransferRepository stepTransfer;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, EmailUtil.class, WebServiceClient.class);
    }

    @Test
    public void getLeakBill() throws Exception {
        List<String> getEmailList = new ArrayList<>();
        getEmailList.add("342352");
        List<String> getBillList = new ArrayList<>();
        getBillList.add("342352");
        getBillList.add("22");
        List<String> ecssControl = new ArrayList<>();
        ecssControl.add("1");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(null);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(null);
        Whitebox.invokeMethod(stepWarnService, "getLeakBill", ecssControl);
        ecssControl = new ArrayList<>();
        ecssControl.add("5");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(getEmailList);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(getEmailList);
        Whitebox.invokeMethod(stepWarnService, "getLeakBill", ecssControl);
        ecssControl = new ArrayList<>();
        ecssControl.add("3");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(getEmailList);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(getEmailList);
        Whitebox.invokeMethod(stepWarnService, "getLeakBill", ecssControl);
        ecssControl = new ArrayList<>();
        ecssControl.add("4");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(getEmailList);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(getEmailList);
        Whitebox.invokeMethod(stepWarnService, "getLeakBill", ecssControl);
        ecssControl = new ArrayList<>();
        ecssControl.add("2");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(getEmailList);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(getEmailList);
        List<String> res = stepWarnService.getLeakBill(ecssControl);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void warnGtsEcssMonitorStep() throws Exception {
        List<String> getEmailList = new ArrayList<>();
        getEmailList.add("342352");
        List<String> getBillList = new ArrayList<>();
        getBillList.add("342352");
        getBillList.add("22");
        List<String> ecssControl = new ArrayList<>();
        ecssControl.add("1");
        PowerMockito.when(stepTransfer.getEmailUser(LOOKUP_TYPE_1000020)).thenReturn(null);
        Whitebox.invokeMethod(stepWarnService, "warnGtsEcssMonitorStep");
        PowerMockito.when(stepTransfer.getEmailUser(LOOKUP_TYPE_1000020)).thenReturn(ecssControl);
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(null);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(null);
        Whitebox.invokeMethod(stepWarnService, "warnGtsEcssMonitorStep");
        PowerMockito.when(stepWarnRepository.queryAllGtsList()).thenReturn(getEmailList);
        PowerMockito.when(stepWarnRepository.queryAllEcssList()).thenReturn(getEmailList);
        Whitebox.invokeMethod(stepWarnService, "warnGtsEcssMonitorStep");
        try {
            PowerMockito.when(emailUtil.sendMail(anyString(), anyString(), anyString(), anyString(), anyString()))
                    .thenThrow(new NullPointerException());
            Whitebox.invokeMethod(stepWarnService, "warnGtsEcssMonitorStep");
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.NO_DATA_FOUND);
        }
        Assert.assertTrue(Objects.nonNull(getEmailList));
    }


}
