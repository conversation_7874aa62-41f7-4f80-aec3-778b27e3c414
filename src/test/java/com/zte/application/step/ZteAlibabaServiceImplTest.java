package com.zte.application.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.impl.InventoryDiffQueryServiceImpl;
import com.zte.application.step.impl.AliInventorySyncServiceImpl;
import com.zte.application.step.impl.ZteAlibabaServiceImpl;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.ZteAliApprovalRepository;
import com.zte.domain.model.step.ZteAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.AliOrderDeductionBillDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.dto.UpdateZtePkgIdBoundSnInfoDTO;
import com.zte.interfaces.infor.dto.ZteSnBoundPkgIdDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.STR_10;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class})
public class ZteAlibabaServiceImplTest {

    @InjectMocks
    private ZteAlibabaServiceImpl zteAlibabaService;
    @Mock
    private ZteAlibabaRepository zteAlibabaRepository;
    @Mock
    private ZteSnBoundPkgIdRepository zteSnBoundPkgIdRepository;
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Mock
    private ZteAliApprovalRepository zteAliApprovalRepository;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Mock
    private ZteAlibabStockInfoUploadRepository zteAlibabStockInfoUploadRepository;
    @Mock
    private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;
    @Mock
    private ZteAlibabaInventoryRepository zteAlibabaInventoryRepository;
    @Mock
    private TransferBoxRepository transferBoxRepository;
    @Mock
    private StockDiscrepancyFeedbackRepository stockDiscrepancyFeedbackRepository;
    @Mock
	private InventoryDiffQueryServiceImpl inventoryDiffQueryService;
    @Mock
    private InventoryTransRepository inventoryTransRepository;
    @Mock
    private EdiPoSRepository ediPosRepository;

    private List<IscpEdiLog> deliveryParams;
    private B2BCallBackDTO dto;
    private ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class);
    }
    @Test
    public void deductionPlan() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        List<ZteDeductionBillInfoDTO> zteDeductionBillInfoDTOList = new ArrayList<>();
        dto.setBillType("1");
        dto.setSourceSystem("1");
        dto.setBusinessType("1");
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setStatus("111");
        zteDeductionBillInfoDTO.setMasterDataReferenceNumber("111");
        zteDeductionBillInfoDTO.setIsSn(3);
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("11111");
        sysLookupValuesDTO.setAttribute1("1");
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        List<StepSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupCode("111");
        stepSysLookupValuesDTO.setLookupMeaning("11111");
        stepSysLookupValuesDTO.setAttribute1("1");
        sysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        when(zteAlibabaRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);

        dto.setSourceSystem("2");
        when(zteAliApprovalRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        List<ItemNoMpnDTO> controlItemNo = new ArrayList<>();
        ItemNoMpnDTO itemNoMpnDTO = new ItemNoMpnDTO();
        itemNoMpnDTO.setItemNo("111");
        itemNoMpnDTO.setMpn("1111");
        itemNoMpnDTO.setIsSn(3);
        controlItemNo.add(itemNoMpnDTO);
        when(zteAlibabaRepository.getControlItemNo(Mockito.any())).thenReturn(controlItemNo);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);

        dto.setSourceSystem("1");
        dto.setBusinessType("3");
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);

        zteDeductionBillInfoDTOList.clear();
        zteDeductionBillInfoDTO.setStatus("SUBMITED");
        zteDeductionBillInfoDTO.setCustomerControlType(2);
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);

        zteDeductionBillInfoDTOList.clear();
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillInfoDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillInfoDTOList);
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        dto.setBillNo("billNo");
        dto.setMessageId(null);
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(zteDeductionPlanDTO), dto,"");
        when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);

        dto.setBusinessType("2");
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillInfoDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillInfoDTOList);
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        PowerMockito.when(zteStockInfoUploadRepository.getStockUploadLog(any())).thenReturn(Collections.singletonList(new ZteStockInfoUploadLogDTO()));
        dto.setMessageId("id");
        dto.setBillNo(null);
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(zteDeductionPlanDTO), dto,"");
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);
        PowerMockito.when(zteStockInfoUploadRepository.getStockUploadLog(any())).thenReturn(Collections.emptyList());
        dto.setMessageId("id");
        dto.setBillNo(null);
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(zteDeductionPlanDTO), dto,"");

        Assert.assertNotNull(dto);
    }

    @Test
    public void checkBillInfo() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBusinessType("2");
        dto.setSourceSystem("2");
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setStatus("11");
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("1");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setCustomerControlType(2);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("1");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setStatus("SUBMITED");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("3");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setStatus("111");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("3");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setStatus("CHECKING");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("4");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setStatus("111");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("4");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        zteDeductionBillInfoDTO.setStatus("RECHECK");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("5");
        dto.setBusinessType("1");
        dto.setSourceSystem("2");
        zteDeductionBillInfoDTO.setStatus("111");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        dto.setBillType("5");
        dto.setBusinessType("1");
        dto.setSourceSystem("2");
        zteDeductionBillInfoDTO.setStatus("ECSSAUDITED");
        zteDeductionBillInfoDTO.setCustomerControlType(1);
        zteDeductionBillDTOList.clear();
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillDTOList);

        Assert.assertNotNull(dto);
    }


    @Test
    public void getDeductionPlanData() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBusinessType("1");
        dto.setSourceSystem("1");
        dto.setBillType("1");
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setBillId("11");
        zteDeductionBillInfoDTO.setBillNo("111");
        zteDeductionBillInfoDTO.setBillType("1");
        zteDeductionBillInfoDTO.setRemark("111");
        zteDeductionBillInfoDTO.setMpn("111");
        zteDeductionBillInfoDTO.setDetailId("111");
        zteDeductionBillInfoDTO.setQty(1);
        zteDeductionBillInfoDTO.setOpenQty(1);
        zteDeductionBillInfoDTO.setTransType("1");
        zteDeductionBillInfoDTO.setItemBarcode("111");
        zteDeductionBillInfoDTO.setIsSn(3);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDetailList = new ArrayList<>();
        List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList = new ArrayList<>();
        List<StepSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupCode("111");
        stepSysLookupValuesDTO.setLookupMeaning("11111");
        stepSysLookupValuesDTO.setAttribute1("1");
        sysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        when(zteAlibabaRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        when(zteSnBoundPkgIdRepository.getSnBoundPkgId(Mockito.any())).thenReturn(zteSnBoundPkgIdDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, "11", zteSnBoundPkgIdDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillDTOList);

        dto.setBillType("2");
        dto.setBusinessType("2");
        zteDeductionBillDTOList.clear();
        zteDeductionBillInfoDTO.setIsSn(0);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, "11");
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillDTOList);

        dto.setBillType("3");
        dto.setSourceSystem("2");
        dto.setBusinessType("3");
        zteDeductionBillDTOList.clear();
        zteDeductionBillInfoDTO.setCustomerPlanId("111");
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, "11");
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void checkBillSn() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setBillType("1");
        dto.setBusinessType("1");
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setBillId("11");
        zteDeductionBillInfoDTO.setBillNo("111");
        zteDeductionBillInfoDTO.setBillType("1");
        zteDeductionBillInfoDTO.setRemark("111");
        zteDeductionBillInfoDTO.setMpn("111");
        zteDeductionBillInfoDTO.setDetailId("111");
        zteDeductionBillInfoDTO.setQty(1);
        zteDeductionBillInfoDTO.setOpenQty(1);
        zteDeductionBillInfoDTO.setTransType("1");
        zteDeductionBillInfoDTO.setItemBarcode("111");
        zteDeductionBillInfoDTO.setIsSn(3);
        zteDeductionBillInfoDTO.setFallbackType("01");
        List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDetailList = new ArrayList<>();
        ZteSnBoundPkgIdDTO zteSnBoundPkgIdDTO = new ZteSnBoundPkgIdDTO();
        zteSnBoundPkgIdDTO.setSn("111");
        zteSnBoundPkgIdDetailList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        dto.setBusinessType("2");
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        dto.setBillType("2");
        String whseId = "";
        when(zteSnBoundPkgIdRepository.getWhseIdByBillNo(Mockito.any())).thenReturn(whseId);
        when(zteSnBoundPkgIdRepository.getOutQtyByBillNo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(2);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        dto.setBillType("4");
        whseId = "WMWHSE1";
        when(zteSnBoundPkgIdRepository.getWhseIdByBillNo(Mockito.any())).thenReturn(whseId);
        when(zteSnBoundPkgIdRepository.getOutQtyByBillNo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        dto.setBillType("6");
        when(zteSnBoundPkgIdRepository.getWhseIdByBillNo(Mockito.any())).thenReturn(whseId);
        when(zteSnBoundPkgIdRepository.getOutQtyByBillNo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        dto.setBillType("5");
        zteDeductionBillInfoDTO.setFallbackType("02");
        when(zteSnBoundPkgIdRepository.getWhseIdByBillNo(Mockito.any())).thenReturn(whseId);
        when(zteSnBoundPkgIdRepository.getOutQtyByBillNo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        dto.setBillType("5");
        zteDeductionBillInfoDTO.setFallbackType("01");
        when(zteSnBoundPkgIdRepository.getWhseIdByBillNo(Mockito.any())).thenReturn(whseId);
        when(zteSnBoundPkgIdRepository.getOutQtyByBillNo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "checkBillSn", dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDetailList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getOneCodeDeductionItemDTO() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setSourceSystem("1");
        dto.setBusinessType("1");
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
        zteDeductionBillInfoDTO.setApprovalType(null);
        zteDeductionBillInfoDTO.setTransType("111");
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        String planId = "1111";
        List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);

        dto.setBusinessType("2");
        zteDeductionBillDTOList.clear();
        zteDeductionBillInfoDTO.setApprovalType("222");
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);

        dto.setSourceSystem("2");
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);

        dto.setSourceSystem("1");
        dto.setBusinessType("3");
        List<InforScatterSnDTO> inforScatterSnDTOList = new ArrayList<>();
        when(zteAlibabaRepository.getInforScatterSnDTOList(Mockito.any())).thenReturn(inforScatterSnDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);

        dto.setSourceSystem("1");
        dto.setBusinessType("3");
        InforScatterSnDTO inforScatterSnDTO = new InforScatterSnDTO();
        inforScatterSnDTO.setOriginPlanId("111");
        inforScatterSnDTO.setOriginDetailId("222");
        inforScatterSnDTOList.add(inforScatterSnDTO);
        when(zteAlibabaRepository.getInforScatterSnDTOList(Mockito.any())).thenReturn(inforScatterSnDTOList);
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getDeductionDetailList() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBusinessType("1");
        dto.setBillType("1");
        dto.setSourceSystem("1");
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setDetailId("111");
        zteDeductionBillInfoDTO.setSourcekey("111");
        zteDeductionBillInfoDTO.setPkgId("111");
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setControlGranularity("11");
        ZteSnBoundPkgIdDTO zteSnBoundPkgIdDTO = new ZteSnBoundPkgIdDTO();
        zteSnBoundPkgIdDTO.setPkgId("222");
        zteSnBoundPkgIdDTO.setExternlineno("111");
        zteSnBoundPkgIdDTO.setPickdetailkey("111");
        zteSnBoundPkgIdDTO.setPkgType("0");
        zteSnBoundPkgIdDTO.setQty(1);
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        dto.setBillType("2");
        dto.setBusinessType("2");
        dto.setControlGranularity("111");
        zteSnBoundPkgIdDTOList.clear();
        zteSnBoundPkgIdDTO.setPkgId("111");
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        dto.setBillType("2");
        dto.setBusinessType("2");
        dto.setControlGranularity("SN");
        zteSnBoundPkgIdDTOList.clear();
        zteSnBoundPkgIdDTO.setPkgId("111");
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);


        dto.setBillType("2");
        dto.setBusinessType("4");
        dto.setControlGranularity("SN");
        zteSnBoundPkgIdDTOList.clear();
        zteSnBoundPkgIdDTO.setPkgId("111");
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        dto.setBillType("2");
        dto.setBusinessType("2");
        zteSnBoundPkgIdDTOList.clear();
        zteSnBoundPkgIdDTO.setPkgId("111");
        zteSnBoundPkgIdDTO.setPkgType("1");
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        dto.setBillType("2");
        dto.setBusinessType("4");
        zteSnBoundPkgIdDTOList.clear();
        zteSnBoundPkgIdDTO.setPkgId("111");
        zteSnBoundPkgIdDTO.setPkgType("1");
        zteSnBoundPkgIdDTOList.add(zteSnBoundPkgIdDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionDetailList", dto, zteDeductionBillInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getNotOneCodeDeductionItemDTO() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setSourceSystem("1");
        dto.setBusinessType("4");
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        ZteDeductionBillInfoDTO billInfoDTO = new ZteDeductionBillInfoDTO();
        billInfoDTO.setItemBarcode("111");
        billInfoDTO.setQty(1);
        billInfoDTO.setDetailId("111");
        zteDeductionBillDTOList.add(billInfoDTO);
        List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        String planId = "11";
        List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = new ArrayList<>();
        List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList = new ArrayList<>();
        when(zteAlibabaRepository.getInforApprovalAlibabaList(Mockito.any())).thenReturn(inforApprovalAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId);

        dto.setBusinessType("3");
        when(zteAlibabaRepository.getInforEdiSoAlibabaList(Mockito.any())).thenReturn(inforEdiSoAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId);

        dto.setBusinessType("1");
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, null, null);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTO", dto, zteDeductionBillDTOList, zteDeductionItemDTOList, planId);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getNotOneCodeDeductionItemDTOLoop() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setSourceSystem("1");
        dto.setBusinessType("2");
        ZteDeductionBillInfoDTO billInfoDTO = new ZteDeductionBillInfoDTO();
        billInfoDTO.setItemBarcode("111");
        billInfoDTO.setApprovalType(null);
        billInfoDTO.setTransType("111");
        String planId = "11";
        List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
        List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = new ArrayList<>();
        List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);

        dto.setSourceSystem("1");
        dto.setBusinessType("3");
        billInfoDTO.setApprovalType("1111");
        billInfoDTO.setQty(1);
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdApply", dto, billInfoDTO, zteDeductionItemDTO, inforEdiSoAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);

        dto.setSourceSystem("1");
        dto.setBusinessType("4");
        billInfoDTO.setApprovalType("1111");
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdDo", billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);

        dto.setSourceSystem("2");
        dto.setBusinessType("1");
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDeductionItemDTOLoop", dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getNotOneCodeDetailIdApply() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setSourceSystem("1");
        dto.setBusinessType("2");
        ZteDeductionBillInfoDTO billInfoDTO = new ZteDeductionBillInfoDTO();
        billInfoDTO.setItemBarcode("111");
        billInfoDTO.setApprovalType(null);
        billInfoDTO.setTransType("111");
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList = new ArrayList<>();
        billInfoDTO.setItemBarcode("111");
        billInfoDTO.setQty(5);
        InforEdiSoAlibabaDTO inforEdiSoAlibabaDTO = new InforEdiSoAlibabaDTO();
        inforEdiSoAlibabaDTO.setSerialkey("1");
        inforEdiSoAlibabaDTO.setCanUseQty(6);
        inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO);
        InforEdiSoAlibabaDTO inforEdiSoAlibabaDTO2 = new InforEdiSoAlibabaDTO();
        inforEdiSoAlibabaDTO2.setSerialkey("2");
        inforEdiSoAlibabaDTO2.setCanUseQty(4);
        inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO2);
        when(zteAlibabaRepository.insertInforApprovalAlibaba(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdApply", dto, billInfoDTO, zteDeductionItemDTO, inforEdiSoAlibabaDTOList);

        inforEdiSoAlibabaDTOList.clear();
        inforEdiSoAlibabaDTO.setCanUseQty(0);
        inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO);
        inforEdiSoAlibabaDTO2.setCanUseQty(4);
        inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO2);
        InforEdiSoAlibabaDTO inforEdiSoAlibabaDTO3 = new InforEdiSoAlibabaDTO();
        inforEdiSoAlibabaDTO3.setSerialkey("3");
        inforEdiSoAlibabaDTO3.setCanUseQty(2);
        inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO3);
        when(zteAlibabaRepository.insertInforApprovalAlibaba(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdApply", dto, billInfoDTO, zteDeductionItemDTO, inforEdiSoAlibabaDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getNotOneCodeDetailIdDo() throws Exception {
        ZteDeductionBillInfoDTO billInfoDTO = new ZteDeductionBillInfoDTO();
        billInfoDTO.setBillNo("111");
        billInfoDTO.setDetailId("1111");
        ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
        List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = new ArrayList<>();
        InforApprovalAlibabaDTO inforApprovalAlibabaDTO = new InforApprovalAlibabaDTO();
        inforApprovalAlibabaDTO.setBillNo("222");
        inforApprovalAlibabaDTO.setDetailId("2222");
        inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdDo", billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);

        inforApprovalAlibabaDTOList.clear();
        inforApprovalAlibabaDTO.setBillNo("111");
        inforApprovalAlibabaDTO.setDetailId("2222");
        inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdDo", billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);

        inforApprovalAlibabaDTOList.clear();
        inforApprovalAlibabaDTO.setBillNo("222");
        inforApprovalAlibabaDTO.setDetailId("1111");
        inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdDo", billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);

        inforApprovalAlibabaDTOList.clear();
        inforApprovalAlibabaDTO.setBillNo("111");
        inforApprovalAlibabaDTO.setDetailId("1111");
        inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
        Whitebox.invokeMethod(zteAlibabaService, "getNotOneCodeDetailIdDo", billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);

        Assert.assertNotNull(billInfoDTO);
    }

    @Test
    public void pushDataToB2B() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("11111");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        when(zteStockInfoUploadRepository.insertStockUploadLog(Mockito.any())).thenReturn(1);
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2BKafKa", customerDataLogDTOList, dto.getEmpNo());
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(zteDeductionPlanDTO), dto,"");

        Assert.assertNotNull(dto);
    }

    @Test
    public void excutedUploadJob() throws Exception {

        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(new SysLookupValuesDTO());
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("111");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedUploadJob", dto);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternkey("111");
        iscpEdiLog.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog.setOperateType("9");
        externkeyList.add(iscpEdiLog);
        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setExternkey("111");
        iscpEdiLog2.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog2.setOperateType("10");
        externkeyList.add(iscpEdiLog2);
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setExternkey("111");
        iscpEdiLog3.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog3.setOperateType("11");
        iscpEdiLog3.setWhseid("whseid11");
        externkeyList.add(iscpEdiLog3);
        IscpEdiLog iscpEdiLo4 = new IscpEdiLog();
        iscpEdiLo4.setExternkey("111");
        iscpEdiLo4.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLo4.setOperateType("12");
        externkeyList.add(iscpEdiLo4);
        IscpEdiLog iscpEdiLo5 = new IscpEdiLog();
        iscpEdiLo5.setExternkey("111");
        iscpEdiLo5.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLo5.setOperateType("13");
        externkeyList.add(iscpEdiLo5);
        when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeductionPlan", externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeliveryDoCustomer", externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDistributeMaterial", externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedTransferInventoryMove", externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "excutedUploadJob", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void excutedDeductionPlan() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("A1111");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternkey("A1111");
        externkeyList.add(iscpEdiLog);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("111");
        sysLookupValuesDTO.setAttribute1("1");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setApprovalType(null);
        zteDeductionBillInfoDTO.setBillNo("111");
        zteDeductionBillInfoDTO.setDetailId("111");
        zteDeductionBillInfoDTO.setQty(1);
        zteDeductionBillInfoDTO.setOpenQty(1);
        zteDeductionBillInfoDTO.setIsSn(3);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        List<ZteDeductionBillInfoDTO> poSoList = new ArrayList<>();
        poSoList.add(zteDeductionBillInfoDTO);
        when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        List<StepSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupCode("111");
        stepSysLookupValuesDTO.setLookupMeaning("11111");
        stepSysLookupValuesDTO.setAttribute1("1");
        sysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        when(zteAlibabaRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillInfoPending", dto, iscpEdiLog);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeductionPlan", externkeyList);

        externkeyList.clear();
        externkeyList.add(iscpEdiLog);
        zteDeductionBillDTOList.clear();
        zteDeductionBillInfoDTO.setApprovalType("111");
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        Whitebox.invokeMethod(zteAlibabaService, "getBillInfoPending", dto, iscpEdiLog);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeductionPlan", externkeyList);

        dto.setSourceSystem("2");
        when(zteAlibabaRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        when(zteAliApprovalRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        List<ItemNoMpnDTO> controlItemNo = new ArrayList<>();
        ItemNoMpnDTO itemNoMpnDTO = new ItemNoMpnDTO();
        itemNoMpnDTO.setMpn("1");
        itemNoMpnDTO.setIsSn(3);
        controlItemNo.add(itemNoMpnDTO);
        when(zteAlibabaRepository.getControlItemNo(Mockito.any())).thenReturn(controlItemNo);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlan", dto);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeductionPlan", externkeyList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getBillInfoPending() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setControlGranularity("1");
        IscpEdiLog iscpEdiLog = new IscpEdiLog();

        List<ZteDeductionBillInfoDTO> poSoList = new ArrayList<>();
        when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillInfoPending", dto, iscpEdiLog);

        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setDetailId("111");
        zteDeductionBillInfoDTO.setBillNo("1111");
        zteDeductionBillInfoDTO.setQty(1);
        zteDeductionBillInfoDTO.setOpenQty(1);
        poSoList.add(zteDeductionBillInfoDTO);
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO2 = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO2.setDetailId("222");
        zteDeductionBillInfoDTO2.setBillNo("2222");
        zteDeductionBillInfoDTO2.setQty(1);
        zteDeductionBillInfoDTO2.setOpenQty(1);
        poSoList.add(zteDeductionBillInfoDTO);
        when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillInfoPending", dto, iscpEdiLog);

        when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getBillInfoPending", dto, iscpEdiLog);

        Assert.assertNotNull(dto);
    }

    @Test
    public void deductionPlanPending() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        List<ZteDeductionBillInfoDTO> zteDeductionBillInfoDTOList = new ArrayList<>();
        dto.setBillType("1");
        dto.setSourceSystem("1");
        dto.setBusinessType("1");
        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setStatus("111");
        zteDeductionBillInfoDTO.setMasterDataReferenceNumber("111");
        zteDeductionBillInfoDTO.setIsSn(3);
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        Whitebox.invokeMethod(zteAlibabaService, "checkBillInfo", dto, zteDeductionBillInfoDTOList);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("111");
        sysLookupValuesDTO.setAttribute1("1");
        List<StepSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupCode("111");
        stepSysLookupValuesDTO.setLookupMeaning("11111");
        stepSysLookupValuesDTO.setAttribute1("1");
        sysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        when(zteAlibabaRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "getDeductionPlanData", dto, zteDeductionBillInfoDTOList);
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        dto.setBillNo("billNo");
        dto.setMessageId(null);
        ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(zteDeductionPlanDTO), dto,"");
        when(zteAlibabaRepository.updateBillDetailOpenQty(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "deductionPlanPending", dto, zteDeductionBillInfoDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void excutedDeliveryDoCustomer() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("111");
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("111");
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setExternkey("111");
        iscpEdiLog3.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog3.setOperateType("11");
        iscpEdiLog3.setWhseid("whseid11");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        externkeyList.add(iscpEdiLog3);

        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDeliveryDoCustomer", externkeyList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void excutedDistributeMaterial() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("111");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setExternkey("111");
        iscpEdiLog3.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog3.setOperateType("11");
        iscpEdiLog3.setWhseid("whseid11");
        externkeyList.add(iscpEdiLog3);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("111");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "excutedDistributeMaterial", externkeyList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void excutedTransferInventoryMove() throws Exception {
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("111");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setExternkey("111");
        iscpEdiLog3.setSourceTable(ALIBABA_PUBLIC_CLOUD);
        iscpEdiLog3.setOperateType("11");
        iscpEdiLog3.setWhseid("whseid11");
        externkeyList.add(iscpEdiLog3);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("111");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(zteAlibabaService, "excutedTransferInventoryMove", externkeyList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getBillType() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("A111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("B111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("M111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("P111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("STH111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        dto.setBillNo("LTH111");
        Whitebox.invokeMethod(zteAlibabaService, "getBillType", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void noticeApproveResult() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");
        List<ZteApproveResultDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);

        ZteApproveResultDTO zteApproveResultDTO = new ZteApproveResultDTO();
        zteApproveResultDTO.setDeductionItemLineNumber("1111");
        zteApproveResultDTO.setApproveAvailableQuantity(1L);
        zteApproveResultDTO.setApproveTotalQuantity(1L);
        zteApproveResultDTO.setApproveStatus(2);
        zteApproveResultDTO.setPlanId("1111");
        zteApproveResultDTO.setBillNo("111");
        zteApproveResultDTO.setBillType("1");
        list.add(zteApproveResultDTO);
        when(zteAlibabaRepository.insertZmsNoticeApproveResult(Mockito.any())).thenReturn(1);
        List<ZteDeductionBillInfoDTO> zteDeductionBillInfoDTOList = new ArrayList<>();
        when(zteAlibabaRepository.getBillHeadByPlanId(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);

        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setCustomerPlanId("1111");
        zteDeductionBillInfoDTO.setBillId("111");
        zteDeductionBillInfoDTO.setBillNo("111");
        zteDeductionBillInfoDTO.setBillType("1");
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillHeadByPlanId(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        when(zteAlibabaRepository.updateBillDetailApprovalQty(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.getBillDetailNotApproved(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);

        list.clear();
        zteApproveResultDTO.setApproveStatus(3);
        list.add(zteApproveResultDTO);
        zteDeductionBillInfoDTOList.clear();
        zteDeductionBillInfoDTO.setApprovalType("111");
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillHeadByPlanId(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        when(zteAlibabaRepository.updateBillDetailApprovalQty(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.getBillDetailNotApproved(Mockito.any())).thenReturn(0);
        when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.updateInforApprovalAlibaba(Mockito.any())).thenReturn(1);
        List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = new ArrayList<>();
        when(zteAlibabaRepository.getInforApprovalAlibabaList(Mockito.any())).thenReturn(inforApprovalAlibabaDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);

        InforApprovalAlibabaDTO inforApprovalAlibabaDTO = new InforApprovalAlibabaDTO();
        inforApprovalAlibabaDTO.setOriginSerialkey("111");
        inforApprovalAlibabaDTO.setApprovalQty(1);
        inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
        when(zteAlibabaRepository.getBillHeadByPlanId(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        when(zteAlibabaRepository.updateBillDetailApprovalQty(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.getBillDetailNotApproved(Mockito.any())).thenReturn(0);
        when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.updateInforApprovalAlibaba(Mockito.any())).thenReturn(1);
        when(zteAlibabaRepository.getInforApprovalAlibabaList(Mockito.any())).thenReturn(inforApprovalAlibabaDTOList);
        when(zteAlibabaRepository.updateInforEdiSoAlibaba(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);

        Assert.assertNotNull(dto);
        zteDeductionBillInfoDTOList.clear();
        zteDeductionBillInfoDTO.setBillType("6");
        zteDeductionBillInfoDTOList.add(zteDeductionBillInfoDTO);
        when(zteAlibabaRepository.getBillHeadByPlanId(Mockito.any())).thenReturn(zteDeductionBillInfoDTOList);
        list.clear();
        zteApproveResultDTO.setBillType("6");
        list.add(zteApproveResultDTO);
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);
    }

    @Test
    public void zteB2BReturnInfo() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");
        B2BCallBackDTO b2BCallBackDTO = new B2BCallBackDTO();
        b2BCallBackDTO.setMessageType("1111");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "zteB2BReturnInfo", b2BCallBackDTO);

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("1111");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        SysLookupValuesDTO sysLookupValuesDTO2 = new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupCode("111");
        sysLookupValuesDTO2.setLookupMeaning("2222");
        sysLookupValuesDTOList.add(sysLookupValuesDTO2);
        when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);
        Whitebox.invokeMethod(zteAlibabaService, "zteB2BReturnInfo", b2BCallBackDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void reformatJson() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");

        String json = "";
        Whitebox.invokeMethod(zteAlibabaService, "reformatJson", json);
        json = "\\{\"key\":\"value\"\\}";
        Whitebox.invokeMethod(zteAlibabaService, "reformatJson", json);
        json = "{\"key\":\"value\"}";
        Whitebox.invokeMethod(zteAlibabaService, "reformatJson", json);
        Assert.assertNotNull(dto);
    }

    @Test
    public void dealBillByMessage() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("11111");
        sysLookupValuesDTO.setLookupMeaning("1111");
        B2BCallBackDTO b2BCallBackDTO = new B2BCallBackDTO();
        b2BCallBackDTO.setSuccess(STR_FALSE);
        b2BCallBackDTO.setMessageType("1111");
        doNothing().when(zteStockInfoUploadRepository).updateStockUploadLogJsonData(any());
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100003");
        List<ZteApproveResultDTO> list = new ArrayList<>();
        ZteApproveResultDTO zteApproveResultDTO = new ZteApproveResultDTO();
        zteApproveResultDTO.setApproveDesc("1111");
        list.add(zteApproveResultDTO);
        b2BCallBackDTO.setData(JSONObject.toJSONString(list));
        Whitebox.invokeMethod(zteAlibabaService, "dealNoticeApproveResult", b2BCallBackDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        sysLookupValuesDTO.setLookupCode("100009100001");
        B2BCallBackDataDTO b2BCallBackDataDTO = new B2BCallBackDataDTO();
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        B2BCallBackResultDTO b2BCallBackResultDTO = new B2BCallBackResultDTO();
        b2BCallBackDataDTO.setRequestId("111");
        b2BCallBackDataDTO.setResult(b2BCallBackResultDTO);
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        when(zteStockInfoUploadRepository.getStockUploadLog(Mockito.any())).thenReturn(zteStockInfoUploadLogDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        zteStockInfoUploadLogDTO.setContractNo("111");
        zteStockInfoUploadLogDTO.setTaskNo("111");
        zteStockInfoUploadLogDTO.setItemNo("111");
        zteStockInfoUploadLogDTO.setSn("1111");
        zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
        when(zteStockInfoUploadRepository.getStockUploadLog(Mockito.any())).thenReturn(zteStockInfoUploadLogDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        Assert.assertNotNull(dto);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100005");
        b2BCallBackDTO.setData(JSONObject.toJSONString(new B2BCallBackDataDTO()));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100007");
        b2BCallBackDTO.setData(JSONObject.toJSONString(new B2BCallBackDataDTO()));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100013");
        b2BCallBackDTO.setData(JSONObject.toJSONString(new B2BCallBackDataDTO()));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100000000");
        B2BCallBackDataDTO b2BCallBackDataDTO1 = new B2BCallBackDataDTO();
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);
        sysLookupValuesDTO.setLookupCode("100009100006");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);
        sysLookupValuesDTO.setLookupCode("100009100012");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);
        sysLookupValuesDTO.setLookupCode("100009100004");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);


        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100002");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100009");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100014");
        b2BCallBackDTO.setData(JSONObject.toJSONString(new B2BCallBackDataDTO()));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        sysLookupValuesDTO.setLookupCode("100009100011");
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        sysLookupValuesDTO.setLookupCode("100009100010");
        b2BCallBackDTO.setData(JSONObject.toJSONString(b2BCallBackDataDTO1));
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);

        sysLookupValuesDTO.setLookupCode("100009100008");
        Whitebox.invokeMethod(zteAlibabaService, "dealBillByMessage", sysLookupValuesDTO, b2BCallBackDTO);
    }

    @Test
    public void dealNoticeApproveResult() throws Exception {
        B2BCallBackDTO b2BCallBackDTO = new B2BCallBackDTO();
        b2BCallBackDTO.setSuccess(STR_FALSE);

        Whitebox.invokeMethod(zteAlibabaService, "dealNoticeApproveResult", b2BCallBackDTO);

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");

        b2BCallBackDTO.setSuccess(STR_TRUE);
        List<ZteApproveResultDTO> list = new ArrayList<>();
        ZteApproveResultDTO zteApproveResultDTO = new ZteApproveResultDTO();
        zteApproveResultDTO.setApproveDesc("1111");
        list.add(zteApproveResultDTO);
        b2BCallBackDTO.setData(JSONObject.toJSONString(list));
        Whitebox.invokeMethod(zteAlibabaService, "noticeApproveResult", list);
        Whitebox.invokeMethod(zteAlibabaService, "dealNoticeApproveResult", b2BCallBackDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void dealBillDeductionPlan() throws Exception {
        B2BCallBackDTO b2BCallBackDTO = new B2BCallBackDTO();
        b2BCallBackDTO.setSuccess(STR_FALSE);

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setEmpNo("111");
        dto.setBillType("1");
        dto.setBusinessType("2");
        dto.setSourceSystem("1");
        dto.setBillNo("1111");

        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        zteStockInfoUploadLogDTO.setContractNo("111");
        zteStockInfoUploadLogDTO.setTaskNo("111");
        zteStockInfoUploadLogDTO.setItemNo("111");
        zteStockInfoUploadLogDTO.setSn("1111");
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO , zteStockInfoUploadLogDTO);

        b2BCallBackDTO.setSuccess(STR_TRUE);
        b2BCallBackDTO.setData("{\"request_id\":null}");
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO , zteStockInfoUploadLogDTO);

        B2BCallBackDataDTO b2BCallBackDataDTO = new B2BCallBackDataDTO();
        b2BCallBackDataDTO.setResult(null);
        b2BCallBackDataDTO.setRequestId("111");
        b2BCallBackDTO.setData(JacksonJsonConverUtil.beanToJson(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO , zteStockInfoUploadLogDTO);

        B2BCallBackResultDTO b2BCallBackResultDTO = new B2BCallBackResultDTO();
        b2BCallBackResultDTO.setIsSuccess(STR_TRUE);
        b2BCallBackDataDTO.setResult(b2BCallBackResultDTO);
        b2BCallBackDTO.setData(JacksonJsonConverUtil.beanToJson(b2BCallBackDataDTO));
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan",b2BCallBackDTO , zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("2");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("4");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        b2BCallBackResultDTO.setIsSuccess(STR_FALSE);
        b2BCallBackDataDTO.setResult(b2BCallBackResultDTO);
        b2BCallBackDTO.setData(JacksonJsonConverUtil.beanToJson(b2BCallBackDataDTO));
        zteStockInfoUploadLogDTO.setItemNo("1111");
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("1");
        zteStockInfoUploadLogDTO.setTaskNo("1");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("1");
        zteStockInfoUploadLogDTO.setTaskNo("3");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("1");
        zteStockInfoUploadLogDTO.setTaskNo("4");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("3");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(zteAlibabaRepository.updateBillHead(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteAlibabaRepository.deleteInforApprovalAlibaba(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("3");
        zteStockInfoUploadLogDTO.setSn("2");
        PowerMockito.when(zteAliApprovalRepository.updateBillHead(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("2");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("4");
        zteStockInfoUploadLogDTO.setSn("1");
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealBillDeductionPlan", b2BCallBackDTO, zteStockInfoUploadLogDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void rollBackOpenQty() throws Exception {

        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setSourcetable(ALIBABA_PUBLIC_CLOUD);
        dto.setOperatetype(STR_10);

        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        zteStockInfoUploadLogDTO.setSn("2");
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setSn("1");
        zteStockInfoUploadLogDTO.setItemNo("1");
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);

        zteStockInfoUploadLogDTO.setItemNo("2");
        zteStockInfoUploadLogDTO.setContractNo("1111");
        zteStockInfoUploadLogDTO.setRemark("1111");
        List<IscpEdiLog> externkeyList = new ArrayList<>();
        PowerMockito.when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setWhseid("111");
        iscpEdiLog.setExternkey("1111");
        iscpEdiLog.setSourcekey("1111");
        externkeyList.add(iscpEdiLog);
        PowerMockito.when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        List<ZteDeductionBillInfoDTO> poSoList = new ArrayList<>();
        PowerMockito.when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);

        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO.setDetailId("1111");
        zteDeductionBillInfoDTO.setBillNo("1111");
        zteDeductionBillInfoDTO.setQty(1);
        poSoList.add(zteDeductionBillInfoDTO);
        PowerMockito.when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        PowerMockito.when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = new ArrayList<>();
        PowerMockito.when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);

        ZteDeductionBillInfoDTO zteDeductionBillInfoDTO2 = new ZteDeductionBillInfoDTO();
        zteDeductionBillInfoDTO2.setDetailId("1111");
        zteDeductionBillInfoDTO2.setBillNo("1111");
        zteDeductionBillInfoDTO2.setQty(1);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO);
        zteDeductionBillDTOList.add(zteDeductionBillInfoDTO2);
        PowerMockito.when(zteSnBoundPkgIdRepository.getAlibabaExternkey(Mockito.any())).thenReturn(externkeyList);
        PowerMockito.when(transferBoxRepository.getPoSoBySourceKey(Mockito.any())).thenReturn(poSoList);
        PowerMockito.when(zteAlibabaRepository.getBillInfo(Mockito.any())).thenReturn(zteDeductionBillDTOList);
        PowerMockito.when(zteAlibabaRepository.updateBillDetailOpenQty(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "rollBackOpenQty", zteStockInfoUploadLogDTO);
        Assert.assertNotNull(dto);
    }

    @Test
    public void dealCreateMoveBillResult() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");

        dto.setData("{\"data\":null,\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"[Assertion failed] - this expression must be true\\\",\\\"trace_id\\\":\\\"0b7b4c8a17504716515343652d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"6001.eel.invoke_remote_failure\\\",\\\"data\\\":null,\\\"success\\\":false,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        PowerMockito.when(transferBoxRepository.updateSendStatus(Mockito.any(), Mockito.any())).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"[Assertion failed] - this expression must be true\\\",\\\"trace_id\\\":\\\"0b7b4c8a17504716515343652d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"6001.eel.invoke_remote_failure\\\",\\\"data\\\":null,\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(transferBoxRepository.updateSendStatus(Mockito.any(), Mockito.any())).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealCreateMoveBillResult", dto, zteStockInfoUploadLogDTO, "13");
    }

    @Test
    public void dealDeliveryDoResult() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":null,\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"success\\\":true,\\\"err_msg\\\":\\\"找不到发货申请单\\\",\\\"err_code\\\":\\\"6001.remote_call_failed\\\"}\",\"success\":false},\"request_id\":\"eg5akd9pbtye\"}");
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"success\\\":true,\\\"err_msg\\\":\\\"找不到发货申请单\\\",\\\"err_code\\\":\\\"6001.remote_call_failed\\\"}\",\"success\":true},\"request_id\":\"eg5akd9pbtye\"}");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogTimes(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealDeliveryDoResult", dto, zteStockInfoUploadLogDTO);
    }

    @Test
    public void dealInventoryStaticsData() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryStaticsData", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryStaticsData", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"result\":null,\"request_id\":\"eg5al5shjj7c\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryStaticsData", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"result\":{\"err_code\":\"00000000\",\"err_msg\":\"成功\",\"is_success\":false},\"request_id\":\"eg5al5shjj7c\"}");
        PowerMockito.when(zteAlibabStockInfoUploadRepository.updateInventoryMergedStatus(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryStaticsData", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"result\":{\"err_code\":\"00000000\",\"err_msg\":\"成功\",\"is_success\":true},\"request_id\":\"eg5al5shjj7c\"}");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteAlibabStockInfoUploadRepository.updateInventoryMergedStatus(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryStaticsData", dto, zteStockInfoUploadLogDTO);
    }

    @Test
    public void dealPkgIdBoundSnResult() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":null,\"request_id\":\"eg5ahly0i3an\"}");
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"success\\\":true,\\\"err_msg\\\":\\\"找不到发货申请单\\\",\\\"err_code\\\":\\\"6001.remote_call_failed\\\"}\",\"success\":false},\"request_id\":\"eg5akd9pbtye\"}");
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"success\\\":true,\\\"err_msg\\\":\\\"找不到发货申请单\\\",\\\"err_code\\\":\\\"6001.remote_call_failed\\\"}\",\"success\":true},\"request_id\":\"eg5akd9pbtye\"}");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealPkgIdBoundSnResult", dto, zteStockInfoUploadLogDTO);
    }

    @Test
    public void dealInventoryDiffFeedbackResult() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        PowerMockito.when(stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryDiffFeedbackResult", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryDiffFeedbackResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\": null,\"success\":false},\"request_id\":\"eg59ee3xa6a5\"}");
        PowerMockito.when(stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryDiffFeedbackResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"success\\\",\\\"trace_id\\\":\\\"0b7b4c8a17474704217797628d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"5001.arowana.voucher_order_order_not_exist\\\",\\\"data\\\":null,\\\"success\\\":false,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59ee3xa6a5\"}");
        PowerMockito.when(stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryDiffFeedbackResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"success\\\",\\\"trace_id\\\":\\\"0b7b4c8a17474704217797628d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"5001.arowana.voucher_order_order_not_exist\\\",\\\"data\\\":null,\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59ee3xa6a5\"}");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealInventoryDiffFeedbackResult", dto, zteStockInfoUploadLogDTO);
    }

    @Test
    public void dealSnInventoryData() throws Exception {
        B2BCallBackDTO dto = new B2BCallBackDTO();
        dto.setSuccess("false");
        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        Whitebox.invokeMethod(zteAlibabaService,"dealSnInventoryData", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        B2BCallBackDataDTO b2BCallBackDataDTO = new B2BCallBackDataDTO();
        b2BCallBackDataDTO.setRequestId("Request1");
        dto.setData(JSON.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService,"dealSnInventoryData", dto, zteStockInfoUploadLogDTO);

        AliCreateBillResResultDTO aliCreateBillResResultDto = new AliCreateBillResResultDTO();
        b2BCallBackDataDTO.setData(aliCreateBillResResultDto);
        dto.setData(JSON.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService,"dealSnInventoryData", dto, zteStockInfoUploadLogDTO);

        when(zteStockInfoUploadRepository.updateStockUploadLogStatus(any())).thenReturn(1);
        when(zteAlibabStockInfoUploadRepository.updateInventoryMergedStatusExt(any())).thenReturn(1);
        AliCreateBillResDataDTO aliCreateBillResDataDto = new AliCreateBillResDataDTO();
        aliCreateBillResDataDto.setSuccess(false);
        aliCreateBillResResultDto.setResult(aliCreateBillResDataDto);
        b2BCallBackDataDTO.setData(aliCreateBillResResultDto);
        dto.setData(JSON.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService,"dealSnInventoryData", dto, zteStockInfoUploadLogDTO);

        aliCreateBillResDataDto = new AliCreateBillResDataDTO();
        aliCreateBillResDataDto.setSuccess(true);
        aliCreateBillResResultDto.setResult(aliCreateBillResDataDto);
        b2BCallBackDataDTO.setData(aliCreateBillResResultDto);
        dto.setData(JSON.toJSONString(b2BCallBackDataDTO));
        Whitebox.invokeMethod(zteAlibabaService,"dealSnInventoryData", dto, zteStockInfoUploadLogDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void dealQueryDetailsResult() throws Exception {

        initB2BCallBack();
        dto.setSuccess("false");
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setSuccess("true");
        dto.setData("{}");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":null,\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg5ahly0i3an\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":null,\\\"success\\\":false,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg5ak4926hcx\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":null,\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg5ak4926hcx\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);

        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":[{\\\"tran_quantity\\\":2,\\\"inventory_category_name\\\":\\\"中兴部件库\\\",\\\"tran_date_str\\\":\\\"2025-06-19 19:12:05\\\",\\\"category_name\\\":\\\"SSD\\\",\\\"batch\\\":\\\"20241008\\\",\\\"brand_name\\\":\\\"Intel\\\",\\\"mpn\\\":\\\"SSDPF2KX038TZ\\\",\\\"item_name\\\":\\\"SSDPF2KX038TZ\\\",\\\"tran_order_no\\\":\\\"ZTEODMT1202506110014\\\",\\\"tran_category_name\\\":\\\"YMT-板载芯片板卡成品入库\\\",\\\"order_line_no\\\":\\\"118685\\\",\\\"quantity_type\\\":1,\\\"inventory_space\\\":\\\"0\\\",\\\"create_date_str\\\":\\\"2025-06-19 19:12:05\\\"}],\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg5ak4926hcx\"}");
        PowerMockito.when(inventoryTransRepository.batchInsert(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealQueryDetailsResult", dto, zteStockInfoUploadLogDTO);
    }

    @Test
    public void dealAlibabaInventoryResult() throws Exception {
        B2BCallBackDTO dto = new B2BCallBackDTO();
        ZteStockInfoUploadLogDTO zsDTO = new ZteStockInfoUploadLogDTO();
        zsDTO.setTaskNo("1");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("Server");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setSuccess(STR_TRUE);
        dto.setData("{\"data\":null,\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":[],\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59l2ajrq1j\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        zsDTO.setTaskNo("2");
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":[{\\\"available_quantity\\\":80,\\\"inventory_quantity\\\":80,\\\"inventory_category_name\\\":\\\"中兴部件库\\\",\\\"hold_duration\\\":296,\\\"category_name\\\":\\\"Server\\\",\\\"batch\\\":\\\"20240731\\\",\\\"item_name\\\":\\\"12000306\\\",\\\"brand_name\\\":\\\"Intel\\\",\\\"mpn\\\":\\\"8163\\\",\\\"freeze_quantity\\\":0}],\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59l2ajrq1j\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        zsDTO.setTaskNo("1");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        List<CustomerInventoryPickUpDTO> customerInventoryPickUpDTOList = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO1 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO1.setMpn("123");
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO2 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO2.setMpn("8163");
        customerInventoryPickUpDTO2.setSourceSystem("2");
        customerInventoryPickUpDTO2.setVendorInventoryQuantity(new BigDecimal(10));
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO3 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO3.setMpn("8163");
        customerInventoryPickUpDTO3.setSourceSystem("1");
        customerInventoryPickUpDTO3.setVendorInventoryQuantity(new BigDecimal(10));
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO1);
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO3);
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO2);
        when(zteAlibabStockInfoUploadRepository.getInventoryDataByCurrent()).thenReturn(customerInventoryPickUpDTOList);
        when(zteStockInfoUploadRepository.updateStockUploadLogStatus(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        List<B2BCallBackInventoryResultDTO> dataList = new ArrayList<>();
        dataList.add(new B2BCallBackInventoryResultDTO());
        // 8. 测试data中categoryName过滤逻辑
        SysLookupValuesDTO valuesDTO = new SysLookupValuesDTO();
        valuesDTO.setLookupType("1000097");
        valuesDTO.setLookupMeaning("Server");
        List<SysLookupValuesDTO> lookupValuesDTOS = new ArrayList<>();
        lookupValuesDTOS.add(valuesDTO);
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(null).thenReturn(lookupValuesDTOS);
        B2BCallBackInventoryResultDTO resultDTO = new B2BCallBackInventoryResultDTO();
        resultDTO.setInventoryCategoryName("Server");
        dataList.clear();
        dataList.add(resultDTO);
        zsDTO.setTaskNo("1");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto, zsDTO);

        // 9. 测试data中categoryName不在types中的分支
        resultDTO.setInventoryCategoryName("OtherType");
        dataList.clear();
        dataList.add(resultDTO);
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto, zsDTO);

        // 10. 测试data为空时的分支
        dataList.clear();
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto, zsDTO);

        // 断言无异常即可
        Assert.assertNotNull(dto);
    }

    @Test
    public void dealAlibabaInventoryResult_inventoryType0() throws Exception {
        B2BCallBackDTO dto = new B2BCallBackDTO();
        ZteStockInfoUploadLogDTO zsDTO = new ZteStockInfoUploadLogDTO();
        zsDTO.setTaskNo("1");
        doNothing().when(zteStockInfoUploadRepository).updateStockUploadLogJsonData(any());
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("Server");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setSuccess(STR_TRUE);
        dto.setData("{\"data\":null,\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":[],\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59l2ajrq1j\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        dto.setKeywords("2");
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"成功\\\",\\\"trace_id\\\":null,\\\"total\\\":0,\\\"code\\\":\\\"00000000\\\",\\\"data\\\":[{\\\"available_quantity\\\":80,\\\"inventory_quantity\\\":80,\\\"inventory_category_name\\\":\\\"中兴部件库\\\",\\\"hold_duration\\\":296,\\\"category_name\\\":\\\"CPU\\\",\\\"batch\\\":\\\"20240731\\\",\\\"item_name\\\":\\\"12000306\\\",\\\"brand_name\\\":\\\"Intel\\\",\\\"mpn\\\":\\\"8163\\\",\\\"freeze_quantity\\\":0}],\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59l2ajrq1j\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        zsDTO.setTaskNo("1");
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);
        List<CustomerInventoryPickUpDTO> customerInventoryPickUpDTOList = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO1 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO1.setMpn("123");
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO2 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO2.setMpn("8163");
        customerInventoryPickUpDTO2.setSourceSystem("2");
        customerInventoryPickUpDTO2.setVendorInventoryQuantity(new BigDecimal(10));
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO3 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO3.setMpn("8163");
        customerInventoryPickUpDTO3.setSourceSystem("1");
        customerInventoryPickUpDTO3.setVendorInventoryQuantity(new BigDecimal(10));
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO1);
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO3);
        customerInventoryPickUpDTOList.add(customerInventoryPickUpDTO2);
        when(zteAlibabStockInfoUploadRepository.getInventoryDataByCurrent()).thenReturn(customerInventoryPickUpDTOList);
        Whitebox.invokeMethod(zteAlibabaService, "dealAlibabaInventoryResult", dto,zsDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void executeQueryInventoryJob() throws Exception {
        ZteAlibabaInventoryParamDTO dto= new ZteAlibabaInventoryParamDTO();
        dto.setTaskType("1");
        dto.setEmpNo("dfgdfg");
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("test-lookup-meaning");
        when(inventoryholdRecordRepository.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);
        doNothing().when(inventoryDiffQueryService).dealProductInventoryData(anyString());
        when(zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(anyList())).thenReturn(3);
        doNothing().when(imesCenterfactoryRemoteService).pushDataToB2BKafKa(anyList(),anyString());
        zteAlibabaService.executeQueryInventoryJob(dto);
        dto.setTaskType("2");
        zteAlibabaService.executeQueryInventoryJob(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void dealOmsBillsResult() throws Exception {
        B2BCallBackDTO dto = new B2BCallBackDTO();
        ZteStockInfoUploadLogDTO logDto = new ZteStockInfoUploadLogDTO();
        dto.setSuccess(STR_FALSE);
        Whitebox.invokeMethod(zteAlibabaService, "dealOmsBillsResult", dto,logDto);
        dto.setSuccess(STR_TRUE);
        dto.setData("{\"data\":null,\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealOmsBillsResult", dto,logDto);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":null,\"success\":true},\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealOmsBillsResult", dto,logDto);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"业务租户ais业务分类PartTypeA,不存在编号为628805421300220117的生产工单,请厂商自查\\\",\\\"trace_id\\\":\\\"0b7b4c8a17478123029815806d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"5001.arowana.mo_not_exist\\\",\\\"data\\\":null,\\\"success\\\":false,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealOmsBillsResult", dto,logDto);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"业务租户ais业务分类PartTypeA,不存在编号为628805421300220117的生产工单,请厂商自查\\\",\\\"trace_id\\\":\\\"0b7b4c8a17478123029815806d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"5001.arowana.mo_not_exist\\\",\\\"data\\\":null,\\\"success\\\":true,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59iuxpno8d\"}");
        Whitebox.invokeMethod(zteAlibabaService, "dealOmsBillsResult", dto,logDto);
        Assert.assertNotNull(dto);
    }
    @Test
    public void executeSnInventoryUploadJobTest() throws Exception {
        ZteDeductionPlanParamDTO requestDto = new ZteDeductionPlanParamDTO();
        requestDto.setEmpNo("12345678");

        SysLookupValuesDTO sysLookupValuesDto = new SysLookupValuesDTO();
        sysLookupValuesDto.setLookupMeaning("ZTEiMES-Alibaba-SyncCartonSnInventory");
        when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(sysLookupValuesDto);

        List<ZteWarehouseInfoDTO> zteWarehouseList = new ArrayList<>();
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(zteWarehouseList);

        zteAlibabaService.executeSnInventoryUploadJob(requestDto);

        ZteWarehouseInfoDTO zteWarehouseInfoDto = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDto.setWarehouseId("WMWHSE23");
        zteWarehouseInfoDto.setDbType(1);
        zteWarehouseList.add(zteWarehouseInfoDto);

        ZteWarehouseInfoDTO zteWarehouseInfoDto2 = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDto2.setWarehouseId("WMWHSE34");
        zteWarehouseInfoDto2.setDbType(4);
        zteWarehouseList.add(zteWarehouseInfoDto2);
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(zteWarehouseList);
        when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt())).thenReturn("ZTE202505080000000001");
        zteAlibabaService.executeSnInventoryUploadJob(requestDto);

        List<ZteSnInventoryQueryDTO> allInventoryStockList = new ArrayList<>();
        ZteSnInventoryQueryDTO zteSnInventoryQueryDto = new ZteSnInventoryQueryDTO();
        zteSnInventoryQueryDto.setInventoryType("5");
        allInventoryStockList.add(zteSnInventoryQueryDto);

        ZteSnInventoryQueryDTO zteSnInventoryQueryDto2 = new ZteSnInventoryQueryDTO();
        zteSnInventoryQueryDto2.setInventoryType("0");
        allInventoryStockList.add(zteSnInventoryQueryDto2);
        when(zteSnBoundPkgIdRepository.getAllInventory(any())).thenReturn(allInventoryStockList);
        when(ediPosRepository.getTransCountLimit(any())).thenReturn(20);
        zteAlibabaService.executeSnInventoryUploadJob(requestDto);

        List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoOneBatchList = new ArrayList<>();
        CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
        customerInventoryLinesDto.setInventoryType("0");
        zmsInventoryMergedDtoOneBatchList.add(customerInventoryLinesDto);
        when(zteAlibabStockInfoUploadRepository.addInventoryMergedData(any())).thenReturn(1);
        Whitebox.invokeMethod(zteAlibabaService,"generateAndPushB2B",requestDto,zmsInventoryMergedDtoOneBatchList,0);
        Assert.assertNotNull(requestDto);
    }


    @Test
    public void executeSnInventoryUploadLastBatchJobTest() throws Exception{
        ZteDeductionPlanParamDTO requestDto = new ZteDeductionPlanParamDTO();
        requestDto.setEmpNo("12345678");

        SysLookupValuesDTO sysLookupValuesDto = new SysLookupValuesDTO();
        sysLookupValuesDto.setLookupMeaning("ZTEiMES-Alibaba-SyncCartonSnInventory");
        when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(sysLookupValuesDto);

        List<CustomerInventoryLinesDTO> customerInventoryLinesDtoList = new ArrayList<>();
        CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
        customerInventoryLinesDto.setFinishFlag(1);
        customerInventoryLinesDto.setInventoryType("5");
        customerInventoryLinesDto.setSendStatus("-1");
        customerInventoryLinesDtoList.add(customerInventoryLinesDto);
        when(zteAlibabStockInfoUploadRepository.getAllUnsuccessfulInventoryMergedData(any())).thenReturn(customerInventoryLinesDtoList);
        when(ediPosRepository.getTransCountLimit(any())).thenReturn(20);
        zteAlibabaService.executeSnInventoryUploadLastBatchJob(requestDto);

        CustomerInventoryLinesDTO customerInventoryLinesDto1 = new CustomerInventoryLinesDTO();
        customerInventoryLinesDto1.setFinishFlag(0);
        customerInventoryLinesDto1.setInventoryType("0");
        customerInventoryLinesDto1.setSendStatus("0");
        customerInventoryLinesDtoList.add(customerInventoryLinesDto1);
        when(zteAlibabStockInfoUploadRepository.getAllUnsuccessfulInventoryMergedData(any())).thenReturn(customerInventoryLinesDtoList);
        zteAlibabaService.executeSnInventoryUploadLastBatchJob(requestDto);

        List<CustomerInventoryLinesDTO> customerInventoryLinesLastBatchList = new ArrayList<>();
        Whitebox.invokeMethod(zteAlibabaService,"singleBatchPush",requestDto,customerInventoryLinesLastBatchList);

        Assert.assertNotNull(requestDto);
    }

    @Test
    public void doExcutedDistributeMaterial() throws Exception {
        when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(new SysLookupValuesDTO());

        when(zteStockMoveInfoUploadRepository.getDistributeMaterialInfo(any())).thenReturn(new ArrayList<>());
        zteAlibabaService.doExcutedDistributeMaterial(new ArrayList<IscpEdiLog>());
        verify(zteStockMoveInfoUploadRepository, never()).getDistributeMaterialInfo(any());


        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("meaning");
        when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(sysLookupValuesDTO);
        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        zteStockInfoUploadLogDTO.setContractNo("ZDM1812270007_1");
        zteStockInfoUploadLogDTO.setMessageType("meaning");
        List<ZteStockInfoUploadLogDTO> uploadLogList = Arrays.asList(
                zteStockInfoUploadLogDTO
        );
        when(zteStockInfoUploadRepository.getStockUploadLog(any())).thenReturn(uploadLogList);
        List<BulkTaskDetailDTO> bulkTaskDetailDTOList = new ArrayList<>();
        BulkTaskDetailDTO bulkTaskDetailDTO = new BulkTaskDetailDTO();
        bulkTaskDetailDTO.setCustomerPartType("11111");
        bulkTaskDetailDTO.setTaskNo("628805421300220117");
        bulkTaskDetailDTOList.add(bulkTaskDetailDTO);
        when(imesCenterfactoryRemoteService.getCategory(anyList(),any())).thenReturn(bulkTaskDetailDTOList);
        AliOrderDeductionBillDTO aliOrderDeductionBillDTO = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO.setMpn("MTA18ASF2G72HZ1");
        aliOrderDeductionBillDTO.setExternOrderNo("1111");
        aliOrderDeductionBillDTO.setQtyReceived(1);
        aliOrderDeductionBillDTO.setExternOrderkey("628805421300220117");
        aliOrderDeductionBillDTO.setBoxType("originalBox");
        aliOrderDeductionBillDTO.setOperateType(1);

        AliOrderDeductionBillDTO aliOrderDeductionBillDTO2 = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO2.setMpn("MTA18ASF2G72HZ1");
        aliOrderDeductionBillDTO2.setExternOrderNo("1111");
        aliOrderDeductionBillDTO2.setQtyReceived(1);
        aliOrderDeductionBillDTO2.setExternOrderkey("628805421300220117");
        aliOrderDeductionBillDTO2.setBoxType("nonControl");
        aliOrderDeductionBillDTO2.setOperateType(1);

        AliOrderDeductionBillDTO aliOrderDeductionBillDTO4 = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO4.setMpn("MTA18ASF2G72HZ1");
        aliOrderDeductionBillDTO4.setExternOrderNo("1111");
        aliOrderDeductionBillDTO4.setQtyReceived(1);
        aliOrderDeductionBillDTO4.setExternOrderkey("628805421300220117");
        aliOrderDeductionBillDTO4.setBoxType("mixBox");
        aliOrderDeductionBillDTO4.setOperateType(1);

        AliOrderDeductionBillDTO aliOrderDeductionBillDTO5 = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO5.setMpn("MTA18ASF2G72HZ1");
        aliOrderDeductionBillDTO5.setExternOrderNo("1111");
        aliOrderDeductionBillDTO5.setQtyReceived(1);
        aliOrderDeductionBillDTO5.setExternOrderkey("628805421300220117");
        aliOrderDeductionBillDTO5.setBoxType("originalBox");
        aliOrderDeductionBillDTO5.setOperateType(2);

        AliOrderDeductionBillDTO aliOrderDeductionBillDTO6 = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO6.setMpn("MTA18ASF2G72HZ1");
        aliOrderDeductionBillDTO6.setExternOrderNo("1111");
        aliOrderDeductionBillDTO6.setQtyReceived(1);
        aliOrderDeductionBillDTO6.setExternOrderkey("628805421300220117");
        aliOrderDeductionBillDTO6.setBoxType("mixBox");
        aliOrderDeductionBillDTO6.setOperateType(2);

        List<AliOrderDeductionBillDTO> aliOrderDeductionBillDTOList =  new ArrayList<>();
        aliOrderDeductionBillDTOList.add(aliOrderDeductionBillDTO);
        aliOrderDeductionBillDTOList.add(aliOrderDeductionBillDTO2);
        aliOrderDeductionBillDTOList.add(aliOrderDeductionBillDTO4);
        when(zteStockMoveInfoUploadRepository.getDistributeMaterialInfo(any())).thenReturn(aliOrderDeductionBillDTOList);
        List<IscpEdiLog> list = new ArrayList<>();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternkey("111");
        iscpEdiLog.setWhseid("22222");
        list.add(iscpEdiLog);
        zteAlibabaService.doExcutedDistributeMaterial(list);
        verify(zteStockMoveInfoUploadRepository, times(1)).getDistributeMaterialInfo(any());

        List<AliOrderDeductionBillDTO> orderDeductionInfoList = new ArrayList<>();
        when(zteStockMoveInfoUploadRepository.getDistributeMaterialInfo(any())).thenReturn(orderDeductionInfoList);
        zteAlibabaService.doExcutedDistributeMaterial(list);

        List<AliOrderDeductionBillDTO> orderDeductionInfoList2 = new ArrayList<>();
        AliOrderDeductionBillDTO aliOrderDeductionBillDTO3 = new AliOrderDeductionBillDTO();
        aliOrderDeductionBillDTO3.setBoxType("noBox");
        aliOrderDeductionBillDTO3.setExternOrderkey("628805421300220119");
        aliOrderDeductionBillDTO3.setMpn("MTA18ASF2G72HZ3");
        aliOrderDeductionBillDTO3.setExternOrderNo("3333");
        orderDeductionInfoList2.add(aliOrderDeductionBillDTO3);
        when(zteStockMoveInfoUploadRepository.getDistributeMaterialInfo(any())).thenReturn(orderDeductionInfoList2);
        doThrow(new Exception("Test Exception")).when(imesCenterfactoryRemoteService).getCategory(anyList(), anyString());
        zteAlibabaService.doExcutedDistributeMaterial(list);
        Assert.assertNotNull(orderDeductionInfoList2);
    }

    @Test
    public void testSelectPicklistMain() throws Exception {
        PowerMockito.when(zteAlibabaRepository.selectPicklistMain(any())).thenReturn(null);
        ZmsPicklistMainInDTO zmsPicklistMainInDTO = new ZmsPicklistMainInDTO();
        this.zteAlibabaService.selectPicklistMain(zmsPicklistMainInDTO);
        Assert.assertNotNull(zmsPicklistMainInDTO);
    }

    private void initDeliveryParams(){
        deliveryParams = new ArrayList<>();
        IscpEdiLog log1 = new IscpEdiLog();
        log1.setExternkey("billNo1");
        log1.setWhseid("whseId1");
        deliveryParams.add(log1);

        IscpEdiLog log2 = new IscpEdiLog();
        log2.setExternkey("billNo1");
        log2.setWhseid("whseId2");
        deliveryParams.add(log2);
    }
    @Test
    public void testDoExecutedDeliveryDoCustomer_EmptyResult() throws Exception {
        initDeliveryParams();
        when(zteAlibabStockInfoUploadRepository.getPartsReceiptList(any()))
                .thenReturn(Collections.emptyList());

        Whitebox.invokeMethod(zteAlibabaService, "doExecutedDeliveryDoCustomer",
                deliveryParams);
        verify(zteAlibabStockInfoUploadRepository, times(2)).getPartsReceiptList(any());
        verifyNoMoreInteractions(imesCenterfactoryRemoteService, inforIwmsIscpRepository);
    }

    @Test
    public void testDoExecutedDeliveryDoCustomer_NonEmptyResult() throws Exception {
        initDeliveryParams();
        List<AliDeliveryReceiptQueryDTO> receipts = new ArrayList<>();
        AliDeliveryReceiptQueryDTO receipt1 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber1")
                .mpn("mpn1")
                .brand("brand1")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("LC20101840001")
                .coo("china")
                .aliInvBatch("20050512")
                .tag("1")
                .specifications(BigDecimal.valueOf(20.00))
                .build();
        receipts.add(receipt1);
        AliDeliveryReceiptQueryDTO receipt2 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber1")
                .mpn("mpn1")
                .brand("brand1")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("LC20101840002")
                .coo("china")
                .aliInvBatch("20050512")
                .tag("1")
                .specifications(BigDecimal.valueOf(30.00))
                .build();
        receipts.add(receipt2);
        AliDeliveryReceiptQueryDTO receipt3 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber1")
                .mpn("mpn1")
                .brand("brand1")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("sn1")
                .coo("china")
                .aliInvBatch("20050512")
                .tag("2")
                .specifications(BigDecimal.valueOf(1))
                .build();
        receipts.add(receipt3);
        AliDeliveryReceiptQueryDTO receipt4 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber1")
                .mpn("mpn1")
                .brand("brand1")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("sn2")
                .coo("china")
                .aliInvBatch("20050512")
                .tag("2")
                .build();
        receipts.add(receipt4);
        AliDeliveryReceiptQueryDTO receipt5 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber2")
                .mpn("mpn2")
                .brand("brand2")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("sn3")
                .coo("eng")
                .aliInvBatch("20050512")
                .tag("2")
                .specifications(BigDecimal.valueOf(1))
                .build();
        receipts.add(receipt5);
        AliDeliveryReceiptQueryDTO receipt6 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber2")
                .mpn("mpn3")
                .brand("brand3")
                .actualTimert("actualTimert1")
                .asnLine("asnLine3")
                .tag("3")
                .specifications(BigDecimal.valueOf(10.00))
                .build();
        receipts.add(receipt6);
        AliDeliveryReceiptQueryDTO receipt7 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber2")
                .mpn("mpn3")
                .brand("brand3")
                .actualTimert("actualTimert1")
                .asnLine("asnLine3")
                .tag("3")
                .specifications(BigDecimal.valueOf(10.00))
                .build();
        receipts.add(receipt7);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType("111");
        sysLookupValuesDTO.setLookupMeaning("1111");
        when(inventoryholdRecordRepository.getLookupValue(any()))
                .thenReturn(sysLookupValuesDTO);
        when(zteAlibabStockInfoUploadRepository.getPartsReceiptList(any()))
                .thenReturn(receipts);
        when(zteStockInfoUploadRepository.insertStockUploadLog(anyList())).thenReturn(1);
        doNothing().when(imesCenterfactoryRemoteService).pushDataToB2BKafKa(anyList(), anyString());
        when(inforIwmsIscpRepository.updateIscpEdiLogTimes(any())).thenReturn(1);

        Whitebox.invokeMethod(zteAlibabaService, "doExecutedDeliveryDoCustomer", deliveryParams);
        verify(zteAlibabStockInfoUploadRepository, times(2)).getPartsReceiptList(any());
        verify(imesCenterfactoryRemoteService, times(2)).pushDataToB2BKafKa(anyList(), anyString());
        verify(inforIwmsIscpRepository, times(2)).updateIscpEdiLogTimes(any(IscpEdiLog.class));
    }

    @Test
    public void testDoExecutedDeliveryDoCustomer_ExceptionHandling() throws Exception {
        initDeliveryParams();
        when(zteAlibabStockInfoUploadRepository.getPartsReceiptList(any()))
                .thenThrow(new RuntimeException("Test Exception"));

        Whitebox.invokeMethod(zteAlibabaService, "doExecutedDeliveryDoCustomer",
                deliveryParams);

        verify(zteAlibabStockInfoUploadRepository, times(1)).getPartsReceiptList(any());
    }

    @Test(expected = Exception.class)
    public void testDoExecutedDeliveryDoCustomer_ExceptionByPushDoDataToB2B() throws Exception {
        List<AliDeliveryReceiptQueryDTO> receipts = new ArrayList<>();
        AliDeliveryReceiptQueryDTO receipt1 = AliDeliveryReceiptQueryDTO.builder()
                .actualDepartureNoteNumber("departureNoteNumber")
                .deliveryNote("deliveryNote")
                .customerPoNumber("poNumber")
                .customerPoLineNumber("PoLineNumber1")
                .mpn("mpn1")
                .brand("brand1")
                .actualTimert("actualTimert1")
                .asnLine("asnLine1")
                .skuId("LC20101840001")
                .coo("china")
                .aliInvBatch("20050512")
                .tag("1")
                .specifications(BigDecimal.valueOf(20.00))
                .build();
        receipts.add(receipt1);

        when(zteAlibabStockInfoUploadRepository.getPartsReceiptList(any()))
                .thenReturn(receipts);
        doThrow(new RuntimeException("Test Exception")).when(zteStockInfoUploadRepository).insertStockUploadLog(anyList());

        Whitebox.invokeMethod(zteAlibabaService, "doExecutedDeliveryDoCustomer",
                deliveryParams, "messageType");
    }

    public void initB2BCallBack() {
        dto = new B2BCallBackDTO();
        zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
    }



    @Test
    public void executeInventoryDiffFeedback() throws Exception {
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("11111");
        Assert.assertNotNull(sysLookupValuesDTO);
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);

        zteAlibabaService.executeInventoryDiffFeedback(new ArrayList<>(), new ArrayList<>(), "111");
    }

    @Test
    public void executedInventoryDiffFeedbackJob() throws Exception {
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupCode("111");
        sysLookupValuesDTO.setLookupMeaning("11111");
        Assert.assertNotNull(sysLookupValuesDTO);
        when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);

        InventoryDiffFeedbackJobDTO jobDTO = new InventoryDiffFeedbackJobDTO();
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);

        List<StockDiscrepancyFeedback> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedback() {{
                setMessageId("11");
            }});
            add(new StockDiscrepancyFeedback() {{
                setMessageId("22");
            }});
        }};
        when(stockDiscrepancyFeedbackRepository.getAllUnsuccessfulData()).thenReturn(new ArrayList<>());
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);

        when(stockDiscrepancyFeedbackRepository.getAllUnsuccessfulData()).thenReturn(infoList);
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);

        List<String> messageIds = new ArrayList<>();
        messageIds.add("33");
        jobDTO.setMessageIds(messageIds);
        when(stockDiscrepancyFeedbackRepository.getDataByMessageIds(Mockito.any())).thenReturn(new ArrayList<>());
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);

        when(stockDiscrepancyFeedbackRepository.getDataByMessageIds(Mockito.any())).thenReturn(infoList);
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);
    }
}
