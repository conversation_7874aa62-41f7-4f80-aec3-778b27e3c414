package com.zte.application.step;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.step.impl.OmsGtsServiceImpl;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.domain.model.step.OmsGtsRepository;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, CommonUtils.class, JacksonJsonConverUtil.class})
public class OmsGtsServiceImplTest {

    @InjectMocks
    OmsGtsServiceImpl omsGtsServiceImpl;
    @Mock
    private OmsGtsRepository omsGtsRepository;
    @Mock
    private OmsEcssRepository omsEcssRepository;
    @Mock
    private EmailUtil emailUtil;
    @Mock
    private ObjectMapper mapperInstance;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    @Before
    public void init() {
        PowerMockito.mockStatic(EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, CommonUtils.class, JacksonJsonConverUtil.class);
    }

    /* Started by AICoder, pid:dcedfb057a4542b78a3fa74dbbe97452 */
    @Test
    public void pushBillToGts() throws Exception {

        GtsBillDTO dto = new GtsBillDTO();
        dto.setLastUpdatedBy("23423");
        List<GtsBillDTO> gtsBillDTOList = new ArrayList<>();
        PowerMockito.when(omsGtsRepository.getAddingBillNo(dto)).thenReturn(gtsBillDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "pushBillToGts", dto);

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("C22080500002");
        gtsBillDTO.setSalesContractNo("111");
        gtsBillDTO.setCustomerNo("111");
        gtsBillDTO.setDetailId("1111");
        gtsBillDTO.setItemNo("111");
        gtsBillDTO.setSalePrice(new BigDecimal("0.001"));
        gtsBillDTO.setQty(new BigDecimal("1"));
        gtsBillDTO.setFailNum(0);
        gtsBillDTOList.add(gtsBillDTO);
        PowerMockito.when(omsGtsRepository.getAddingBillNo(dto)).thenReturn(gtsBillDTOList);

        GtsPushParamDTO gtsPushParamDTO = new GtsPushParamDTO();
        GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
        isMsgHeader.setDocNo("1111");
        isMsgHeader.setInstanceId("111");
        gtsPushParamDTO.setIsMsgHeader(isMsgHeader);
        GtsPushReturnDTO gtsPushReturnDTO = new GtsPushReturnDTO();
        gtsPushReturnDTO.setInstanceId("111");
        gtsPushReturnDTO.setProcessStatus("S");

        PowerMockito.field(OmsGtsServiceImpl.class, "inoneUrl").set(omsGtsServiceImpl, "https://icosg.test.zte.com.cn");
        PowerMockito.field(OmsGtsServiceImpl.class, "inoneAppcode").set(omsGtsServiceImpl, "ad8e86b3658f4dad8b2224a140e2db61");
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.anyString())).thenReturn(gtsBillDTOList);
        String result = "{\"SOURCE_SYSTEM_ID\":\"OMS\",\"INSTANCE_ID\":\"OMS_ND23052200001_20240906154354\",\"LOG_SYSTEM\":\"CNKX\",\"DOC_TYPE\":\"ZDO\",\"DOC_NO\":\"ND23052200001\",\"PROCESS_STATUS\":\"S\",\"PROCESS_MESSAGE\":\"The data has been successfully saved in GTS.The document check result will be sent back later.\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(result);
        Whitebox.invokeMethod(omsGtsServiceImpl, "dealGtsBill",gtsBillDTO, gtsPushParamDTO, gtsPushReturnDTO, Mockito.anyString());
        Whitebox.invokeMethod(omsGtsServiceImpl, "pushBillToGts", dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Ended by AICoder, pid:dcedfb057a4542b78a3fa74dbbe97452 */

    /* Started by AICoder, pid:******************************** */
    @Test
    public void getGtsPushParamDTO() throws Exception {

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("C22080500002");
        gtsBillDTO.setSalesContractNo("111");
        gtsBillDTO.setCustomerNo("111");
        gtsBillDTO.setDetailId("1111");
        gtsBillDTO.setItemNo("111");
        gtsBillDTO.setSalePrice(new BigDecimal("0.001"));
        gtsBillDTO.setQty(new BigDecimal("1"));
        List<GtsBillDTO> gtsBillDTOList = new ArrayList<>();
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.anyString())).thenReturn(gtsBillDTOList);
        gtsBillDTOList.add(gtsBillDTO);
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.anyString())).thenReturn(gtsBillDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getProdVal", gtsBillDTO);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsPushParamDTO", Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(gtsBillDTO));
    }
    /* Ended by AICoder, pid:******************************** */

    /* Started by AICoder, pid:30c448d23b6d409695973ee01f7e5e9b */
    @Test
    public void getProdVal() throws Exception {

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("C22080500002");
        gtsBillDTO.setSalesContractNo("111");
        gtsBillDTO.setCustomerNo("111");
        gtsBillDTO.setDetailId("1111");
        gtsBillDTO.setItemNo("111");
        gtsBillDTO.setSalesType(0);
        gtsBillDTO.setSalePrice(new BigDecimal("0.5"));
        gtsBillDTO.setQty(new BigDecimal("1"));
        Whitebox.invokeMethod(omsGtsServiceImpl, "getProdVal", gtsBillDTO);

        gtsBillDTO.setSalePrice(new BigDecimal("0.001"));
        Whitebox.invokeMethod(omsGtsServiceImpl, "getProdVal", gtsBillDTO);

        gtsBillDTO.setSalesType(1);
        gtsBillDTO.setSalePrice(new BigDecimal("0.001"));
        List<ErpItemCostDTO> erpItemCostDTOList = new ArrayList<>();
        String responseStr4 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":0.005,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr4);
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        ErpItemCostDTO erpItemCostDTO = new ErpItemCostDTO();
        erpItemCostDTO.setAvecost(new BigDecimal("0.01"));
        erpItemCostDTOList.add(erpItemCostDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(erpItemCostDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getProdVal", gtsBillDTO);

        gtsBillDTO.setSalePrice(new BigDecimal("0.5"));
        List<ErpItemCostDTO> erpItemCostDTOList2 = new ArrayList<>();
        String responseStr2 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":0.005,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr2);
        ObjectMapper objectMapper2 = new ObjectMapper();
        JsonNode json2 = objectMapper2.readTree(responseStr2);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json2);
        ErpItemCostDTO erpItemCostDTO2 = new ErpItemCostDTO();
        erpItemCostDTO2.setAvecost(new BigDecimal("0.01"));
        erpItemCostDTOList2.add(erpItemCostDTO2);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(erpItemCostDTOList2);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getProdVal", gtsBillDTO);
        Assert.assertTrue(Objects.nonNull(gtsBillDTO));
    }
    /* Ended by AICoder, pid:30c448d23b6d409695973ee01f7e5e9b */

    /* Started by AICoder, pid:085e473ce29d4584ab41a6c20302347d */
    @Test
    public void getErpPrice() throws Exception {

        PowerMockito.field(OmsGtsServiceImpl.class, "inoneUrl").set(omsGtsServiceImpl, "https://icosg.test.zte.com.cn");
        PowerMockito.field(OmsGtsServiceImpl.class, "inoneAppcode").set(omsGtsServiceImpl, "ad8e86b3658f4dad8b2224a140e2db61");

        Map<String, String> headerParamsMap = new HashMap<>();
        headerParamsMap.put(X_EMP_NO, STR_OMS);
        headerParamsMap.put(INONE_APPCODE, inoneAppcode);
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);

        ErpSearchInfoDTO erpSearchInfoDTO = new ErpSearchInfoDTO();
        erpSearchInfoDTO.setApiCode(ERP_API_CODE_GET_ITEM_COST);
        erpSearchInfoDTO.setInvokeKey(NumConstant.STR_13);
        erpSearchInfoDTO.setProductType(ERP_PRODUCT_TYPE_EPMS);
        erpSearchInfoDTO.setServiceCode(ERP_SERVICE_CODE_EPMS_MANAGE);
        Map<String, String> configParamMap = new HashMap<>();
        configParamMap.put(STR_ORGANIZATION_ID, NumConstant.STR_395);
        configParamMap.put(ITEM_CODE, "11111111");
        erpSearchInfoDTO.setConfigParamMap(configParamMap);

        String url = inoneUrl + ERP_INONE_PUBLIC_QUERY_DATA;
        List<ErpItemCostDTO> erpItemCostDTOList = new ArrayList<>();

        String responseStr = "{}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = null;
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getErpPrice", Mockito.anyString());

        String responseStr2 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":597.405925,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr2);
        ObjectMapper objectMapper2 = new ObjectMapper();
        JsonNode json2 = objectMapper2.readTree(responseStr2);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json2);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getErpPrice", Mockito.anyString());

        String responseStr3 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr3);
        ObjectMapper objectMapper3 = new ObjectMapper();
        JsonNode json3 = objectMapper3.readTree(responseStr3);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json3);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(erpItemCostDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getErpPrice", Mockito.anyString());

        String responseStr4 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":597.405925,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr4);
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        ErpItemCostDTO erpItemCostDTO = new ErpItemCostDTO();
        erpItemCostDTO.setAvecost(new BigDecimal("0.01"));
        erpItemCostDTOList.add(erpItemCostDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(erpItemCostDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getErpPrice", Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(headerParamsMap));
    }
    /* Ended by AICoder, pid:085e473ce29d4584ab41a6c20302347d */

    /* Started by AICoder, pid:060d4a5bf96b4425b1c56caf22430d9b */
    @Test
    public void invokeBillToGts() throws Exception {

        GtsPushParamDTO gtsPushParamDTO = new GtsPushParamDTO();
        PowerMockito.when(omsGtsServiceImpl.invokeBillToGts(gtsPushParamDTO)).thenThrow(new NullPointerException());

        GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
        isMsgHeader.setDocNo("1111");
        isMsgHeader.setInstanceId("111");
        gtsPushParamDTO.setIsMsgHeader(isMsgHeader);
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupMeaning("11111");
        stepSysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);

        String result = "{\"SOURCE_SYSTEM_ID\":\"OMS\",\"INSTANCE_ID\":\"OMS_ND23052200001_20240906154354\",\"LOG_SYSTEM\":\"CNKX\",\"DOC_TYPE\":\"ZDO\",\"DOC_NO\":\"ND23052200001\",\"PROCESS_STATUS\":\"S\",\"PROCESS_MESSAGE\":\"The data has been successfully saved in GTS.The document check result will be sent back later.\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(result);
        Whitebox.invokeMethod(omsGtsServiceImpl, "invokeBillToGts", gtsPushParamDTO);
        Assert.assertTrue(Objects.nonNull(gtsPushParamDTO));
    }
    /* Ended by AICoder, pid:060d4a5bf96b4425b1c56caf22430d9b */

    /* Started by AICoder, pid:64ff692585ba4ab9990084bd16901a64 */
    @Test
    public void dealGtsBill() throws Exception{

        GtsBillDTO dto = new GtsBillDTO();
        dto.setFailNum(1);
        dto.setReferenceNumber("C22080500002");
        GtsPushParamDTO gtsPushParamDTO = new GtsPushParamDTO();
        GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
        isMsgHeader.setDocNo("1111");
        isMsgHeader.setInstanceId("111");
        gtsPushParamDTO.setIsMsgHeader(isMsgHeader);
        GtsPushReturnDTO gtsPushReturnDTO = new GtsPushReturnDTO();
        Whitebox.invokeMethod(omsGtsServiceImpl, "dealGtsBill", dto, gtsPushParamDTO, gtsPushReturnDTO, Mockito.anyString());

        gtsPushReturnDTO.setInstanceId("111");
        gtsPushReturnDTO.setProcessStatus("N");
        PowerMockito.when(omsGtsRepository.insertOrUpdateGtsBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "dealGtsBill", dto, gtsPushParamDTO, gtsPushReturnDTO, Mockito.anyString());

        gtsPushReturnDTO.setInstanceId("111");
        gtsPushReturnDTO.setProcessStatus("S");
        PowerMockito.when(omsGtsRepository.insertOrUpdateGtsBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "dealGtsBill", dto, gtsPushParamDTO, gtsPushReturnDTO, Mockito.anyString());

        dto.setReferenceNumber("ND23052500003");
        dto.setFailNum(3);
        gtsPushReturnDTO.setProcessStatus("N");
        PowerMockito.when(omsGtsRepository.insertOrUpdateGtsBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "sendFailMail", Mockito.anyString());
        Whitebox.invokeMethod(omsGtsServiceImpl, "dealGtsBill", dto, gtsPushParamDTO, gtsPushReturnDTO, Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Ended by AICoder, pid:64ff692585ba4ab9990084bd16901a64 */

    /* Started by AICoder, pid:7e70233ee7c949feaf9bf35ed7a7da5c */
    @Test
    public void sendFailMail() throws Exception{

        StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build()
                .setLookupType(LOOKUP_TYPE_1000022).setDescription(GTS_BILL_FAILED);
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO)).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "sendFailMail", Mockito.anyString());

        StepSysLookupValuesDTO lookupValuesDTO = new StepSysLookupValuesDTO();
        lookupValuesDTO.setLookupMeaning("21314");
        stepSysLookupValuesDTOList.add(lookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO)).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "sendFailMail", Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(stepSysLookupValuesDTO));
    }
    /* Ended by AICoder, pid:7e70233ee7c949feaf9bf35ed7a7da5c */

    /* Started by AICoder, pid:07f1d301b1b1427d8b640c011858c8b2 */
    @Test
    public void callBackGtsToOms() throws Exception{

        GtsBackParamDTO dto = new GtsBackParamDTO();
        PowerMockito.when(omsGtsServiceImpl.callBackGtsToOms(dto)).thenThrow(new NullPointerException());
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        List<GtsItReturnDTO> itReturn = new ArrayList<>();
        GtsItReturnDTO gtsItReturnDTO = new GtsItReturnDTO();
        gtsItReturnDTO.setMessage("111");
        itReturn.add(gtsItReturnDTO);
        dto.setItReturn(itReturn);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
        isMsgHeader.setInstanceId("11111");
        dto.setIsMsgHeader(isMsgHeader);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        List<GtsItResultDTO> itResult = new ArrayList<>();
        GtsItResultDTO gtsItResultDTO = new GtsItResultDTO();
        gtsItResultDTO.setCheckInd("11");
        itResult.add(gtsItResultDTO);
        dto.setItResult(itResult);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        isMsgHeader.setDocNo("11111");
        dto.setIsMsgHeader(isMsgHeader);

        List<GtsBillDTO> gtsBillDTOList = new ArrayList<>();
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.any())).thenReturn(gtsBillDTOList);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("234234");
        gtsBillDTO.setStatus("AUDITING");
        gtsBillDTOList.add(gtsBillDTO);
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.any())).thenReturn(gtsBillDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);

        gtsBillDTOList.clear();
        GtsBillDTO gtsBillDTO2 = new GtsBillDTO();
        gtsBillDTO2.setReferenceNumber("234234");
        gtsBillDTO2.setStatus("AUDITING2");
        gtsBillDTOList.add(gtsBillDTO2);
        PowerMockito.when(omsGtsRepository.getOmsSalesInfo(Mockito.any())).thenReturn(gtsBillDTOList);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsDTO", dto, Mockito.anyString());
        Whitebox.invokeMethod(omsGtsServiceImpl, "callBackGtsToOms", dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Ended by AICoder, pid:07f1d301b1b1427d8b640c011858c8b2 */

    /* Started by AICoder, pid:ca186ec753dc4023be22f6313d069289 */
    @Test
    public void updateGtsDTO() throws Exception{

        GtsBackParamDTO dto = new GtsBackParamDTO();
        GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
        isMsgHeader.setInstanceId("11111");
        dto.setIsMsgHeader(isMsgHeader);
        List<GtsItResultDTO> itResult = new ArrayList<>();
        GtsItResultDTO gtsItResultDTO = new GtsItResultDTO();
        gtsItResultDTO.setCheckInd("11");
        itResult.add(gtsItResultDTO);
        dto.setItResult(itResult);
        List<GtsItReturnDTO> itReturn = new ArrayList<>();
        GtsItReturnDTO gtsItReturnDTO = new GtsItReturnDTO();
        gtsItReturnDTO.setMessage("111");
        itReturn.add(gtsItReturnDTO);
        dto.setItReturn(itReturn);

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsBillDTO", gtsBillDTO, itResult, "通过");
        PowerMockito.when(omsGtsRepository.updateGtsBill(Mockito.any())).thenReturn(1);
        PowerMockito.when(omsGtsRepository.updateInforOmSalesBill(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsOutputcollection", gtsBillDTO, itResult);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsTextretruncollection", gtsBillDTO, itReturn);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsDTO", dto, Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Started by AICoder, pid:ca186ec753dc4023be22f6313d069289 */

    /* Started by AICoder, pid:b910e166dc82416189da44087caaad23 */
    @Test
    public void updateGtsOutputcollection() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("1111");
        List<GtsItResultDTO> itResult = new ArrayList<>();
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsOutputcollection", gtsBillDTO, itResult);

        GtsItResultDTO gtsItResultDTO = new GtsItResultDTO();
        gtsItResultDTO.setCheckInd("11");
        itResult.add(gtsItResultDTO);
        PowerMockito.when(omsGtsRepository.failGtsOutputcollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(omsGtsRepository.insertGtsOutputcollection(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsOutputcollection", gtsBillDTO, itResult);
        Assert.assertTrue(Objects.nonNull(gtsBillDTO));
    }
    /* Ended by AICoder, pid:b910e166dc82416189da44087caaad23 */

    /* Started by AICoder, pid:a9b32d4df27a4e4ab4705534e0ac9d3a */
    @Test
    public void updateGtsTextretruncollection() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("1111");
        List<GtsItReturnDTO> itReturn = new ArrayList<>();
        PowerMockito.when(omsGtsRepository.failGtsTextretruncollection(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsTextretruncollection", gtsBillDTO, itReturn);

        GtsItReturnDTO gtsItReturnDTO = new GtsItReturnDTO();
        gtsItReturnDTO.setMessage("111");
        gtsItReturnDTO.setType("S");
        itReturn.add(gtsItReturnDTO);
        PowerMockito.when(omsGtsRepository.failGtsTextretruncollection(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsTextretruncollection", gtsBillDTO, itReturn);

        itReturn.clear();
        GtsItReturnDTO gtsItReturnDTO2 = new GtsItReturnDTO();
        gtsItReturnDTO2.setMessage("111");
        gtsItReturnDTO2.setType("E");
        itReturn.add(gtsItReturnDTO2);
        PowerMockito.when(omsGtsRepository.failGtsTextretruncollection(Mockito.any())).thenReturn(1);
        PowerMockito.when(omsGtsRepository.insertGtsTextretruncollection(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(omsGtsServiceImpl, "updateGtsTextretruncollection", gtsBillDTO, itReturn);
        Assert.assertTrue(Objects.nonNull(gtsBillDTO));
    }
    /* Ended by AICoder, pid:a9b32d4df27a4e4ab4705534e0ac9d3a */

    /* Started by AICoder, pid:249621fe6f20433da339b1a5bfb91fce */
    @Test
    public void getGtsBillDTO() throws Exception{

        GtsBillDTO gtsBillDTO = new GtsBillDTO();
        gtsBillDTO.setReferenceNumber("111");
        List<GtsItResultDTO> itResult = new ArrayList<>();
        GtsItResultDTO gtsItResultDTO = new GtsItResultDTO();
        gtsItResultDTO.setCheckInd("B");
        itResult.add(gtsItResultDTO);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsBillDTO", gtsBillDTO, itResult, "通过");
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsBillDTO", gtsBillDTO, itResult, "不通过");

        itResult.clear();
        GtsItResultDTO gtsItResultDTO2 = new GtsItResultDTO();
        gtsItResultDTO2.setCheckInd("A");
        itResult.add(gtsItResultDTO2);
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsBillDTO", gtsBillDTO, itResult, "通过");
        Whitebox.invokeMethod(omsGtsServiceImpl, "getGtsBillDTO", gtsBillDTO, itResult, "不通过");
        Assert.assertTrue(Objects.nonNull(gtsBillDTO));
    }
    /* Ended by AICoder, pid:249621fe6f20433da339b1a5bfb91fce */

}
