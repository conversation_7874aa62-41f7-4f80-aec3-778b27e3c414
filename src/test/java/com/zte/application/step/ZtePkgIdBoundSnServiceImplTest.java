package com.zte.application.step;

import com.alibaba.fastjson.JSON;
import com.zte.application.step.impl.ZtePkgIdBoundSnServiceImpl;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteSnBoundPkgIdRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.dto.UpdateZtePkgIdBoundSnInfoDTO;
import com.zte.interfaces.infor.dto.ZtePkgIdBoundSnInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class,CollectionUtils.class, JSON.class, UUID.class})
public class ZtePkgIdBoundSnServiceImplTest {

    @InjectMocks
    private ZtePkgIdBoundSnServiceImpl ztePkgIdBoundSnService;

    @Mock
    private ZteSnBoundPkgIdRepository zteSnBoundPkgIdRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class, CollectionUtils.class, JSON.class, UUID.class);
    }

    @Test
    public void excuteUploadPkgIdBoundSnJob() throws Exception {
        List<String> list = new ArrayList<>();
        PowerMockito.when(zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnForExternalkey("1")).thenReturn(list);
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "excuteUploadMixedBoxPkgIdBoundSnJob", "");

        list.add("123");
        list.add("");
        PowerMockito.when(zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnForExternalkey("1")).thenReturn(list);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "excuteUploadMixedBoxPkgIdBoundSnJob", "");

        sysLookupValuesDTO.setLookupCode("123");
        sysLookupValuesDTO.setLookupMeaning("123");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDTO);
        PowerMockito.when(zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnInfoList(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "excuteUploadMixedBoxPkgIdBoundSnJob", "");
        List<ZtePkgIdBoundSnInfoDTO> dto=new ArrayList<>();
        ZtePkgIdBoundSnInfoDTO item = new ZtePkgIdBoundSnInfoDTO();
        dto.add(item);
        PowerMockito.when(zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnInfoList(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "excuteUploadMixedBoxPkgIdBoundSnJob", "");
        item.setMessageId("123");
        dto.add(item);
        PowerMockito.when(zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnInfoList(Mockito.anyString(),Mockito.anyString())).thenReturn(dto);
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "excuteUploadMixedBoxPkgIdBoundSnJob", "");
        Assert.assertNotNull(dto);
    }

    @Test
    public void testExcuteModifyPkgIdBoundSnJob_EmptyBindingList() {
        // 模拟空列表情况
        PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyList())).thenReturn(true);
        PowerMockito.when(zteSnBoundPkgIdRepository.queryPkgSnBindingsByExternalNo()).thenReturn(new ArrayList<>());

        ztePkgIdBoundSnService.excuteModifyOriginBoxPkgIdBoundSnJob("EMP001");

        // 验证没有调用后续方法
        Mockito.verify(inventoryholdRecordRepository, Mockito.never()).getLookupValue(Mockito.any());

    }

    @Test
    public void testExcuteModifyPkgIdBoundSnJob_Success() {
        // 准备测试数据
        List<ZtePkgIdBoundSnInfoDTO> bindingList = new ArrayList<>();
        ZtePkgIdBoundSnInfoDTO item1 = new ZtePkgIdBoundSnInfoDTO();
        item1.setPkgId("PKG001");
        item1.setSnCode("SN001");
        item1.setOriginSn("ORIGIN001");
        item1.setMpn("MPN001");
        item1.setExternalNo("EXT001");
        bindingList.add(item1);

        // 添加足够多的数据以测试分批处理
        for (int i = 0; i < 600; i++) {
            ZtePkgIdBoundSnInfoDTO item = new ZtePkgIdBoundSnInfoDTO();
            item.setPkgId("PKG002");
            item.setSnCode("SN" + i);
            item.setOriginSn("ORIGIN" + i);
            item.setMpn("MPN002");
            item.setExternalNo("EXT002");
            bindingList.add(item);
        }

        PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyList())).thenReturn(false);
        PowerMockito.when(zteSnBoundPkgIdRepository.queryPkgSnBindingsByExternalNo()).thenReturn(bindingList);

        // 模拟lookup值查询
        SysLookupValuesDTO lookupValuesDTO = new SysLookupValuesDTO();
        lookupValuesDTO.setLookupMeaning("MESSAGE_TYPE_001");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(anyString())).thenReturn(lookupValuesDTO);

        // 模拟UUID生成
        PowerMockito.when(UUID.randomUUID()).thenReturn(new UUID(1, 1));

        // 执行测试
        ztePkgIdBoundSnService.excuteModifyOriginBoxPkgIdBoundSnJob("EMP001");

        // 验证分组和分批处理
        // 应该有两个箱号(PKG001和PKG002)
        // PKG001有1条记录，应该处理1批
        // PKG002有600条记录，应该处理2批(500+100)
        Mockito.verify(zteSnBoundPkgIdRepository, Mockito.times(3)).updateZtePkgIdBoundSnInfo(Mockito.any());
        Mockito.verify(imesCenterfactoryRemoteService, Mockito.times(3)).pushDataToB2BKafKa(Mockito.anyList(), Mockito.eq("EMP001"));
    }

    @Test
    public void testPushBatchDataToB2B() throws Exception {
        // 准备测试数据
        List<ZtePkgIdBoundSnInfoDTO> batch = new ArrayList<>();
        ZtePkgIdBoundSnInfoDTO item = new ZtePkgIdBoundSnInfoDTO();
        item.setSnCode("SN001");
        item.setOriginSn("ORIGIN001");
        item.setMpn("MPN001");
        item.setExternalNo("EXT001");
        batch.add(item);

        // 模拟UUID生成
        PowerMockito.when(UUID.randomUUID()).thenReturn(new UUID(1, 1));

        // 模拟JSON转换
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn("{}");

        // 使用反射调用私有方法
        Whitebox.invokeMethod(ztePkgIdBoundSnService, "pushOriginBoxBatchDataToB2B",
            "PKG001", batch, "MESSAGE_TYPE_001", "EMP001");

        // 验证方法调用
        Mockito.verify(zteSnBoundPkgIdRepository).updateZtePkgIdBoundSnInfo(Mockito.any());
        Mockito.verify(imesCenterfactoryRemoteService).pushDataToB2BKafKa(Mockito.anyList(), Mockito.eq("EMP001"));

        // 验证参数
        ArgumentCaptor<UpdateZtePkgIdBoundSnInfoDTO> updateCaptor = ArgumentCaptor.forClass(UpdateZtePkgIdBoundSnInfoDTO.class);
        Mockito.verify(zteSnBoundPkgIdRepository).updateZtePkgIdBoundSnInfo(updateCaptor.capture());
        assertEquals("EXT001", updateCaptor.getValue().getExternalkey());
        assertEquals("EMP001", updateCaptor.getValue().getEmpNo());
        assertEquals("MESSAGE_TYPE_001", updateCaptor.getValue().getMessageType());
    }
}
