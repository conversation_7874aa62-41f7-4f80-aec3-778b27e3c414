package com.zte.application.step;

import com.alibaba.fastjson.JSON;
import com.zte.application.step.impl.AliInventorySyncServiceImpl;
import com.zte.application.step.impl.ZteAlibabaServiceImpl;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.interfaces.infor.dto.AliInventoryRequest;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import static com.zte.common.utils.Constant.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class})
public class AliInventorySyncServiceImplTest {

    @InjectMocks
    private AliInventorySyncServiceImpl inventoryService;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private ZteAlibabaServiceImpl zteAlibabaService;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class);
    }


    @Test
    public void syncAliInventoryTransData() throws Exception {
        // 准备mock数据
        SysLookupValuesDTO lookupDTO1 = new SysLookupValuesDTO();
        lookupDTO1.setLookupCode(LOOKUP_CODE_100008800002);
        lookupDTO1.setLookupMeaning("2023-01-01 00:00:00");

        SysLookupValuesDTO lookupDTO2 = new SysLookupValuesDTO();
        lookupDTO2.setLookupCode(LOOKUP_CODE_100009100010);
        lookupDTO2.setLookupMeaning("INVENTORY_MSG_TYPE");

        AliInventoryRequest aliRequest = new AliInventoryRequest();
        aliRequest.setFactoryCode(ALIBABA_FACTORY_CODE);

        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(lookupDTO1);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(lookupDTO2);
        Whitebox.invokeMethod(inventoryService, "pushDataToB2B", aliRequest, "INVENTORY_MSG_TYPE");
        Whitebox.invokeMethod(inventoryService, "syncAliInventoryTransData", "SYSTEM");

        Assert.assertNotNull(lookupDTO1);
    }

    @Test
    public void pushDataToB2B() throws Exception {
        AliInventoryRequest aliRequest = new AliInventoryRequest();
        aliRequest.setFactoryCode(ALIBABA_FACTORY_CODE);
        String messageType = "INVENTORY_MSG_TYPE";
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setMessageType(messageType);
        dto.setEmpNo(SYSTEM);
        Whitebox.invokeMethod(zteAlibabaService, "pushDataToB2B", JSON.toJSONString(aliRequest), dto, ALI_INVENTORY_SYNC_ZH);
        PowerMockito.when(inventoryholdRecordRepository.updateLookupMeaning(Mockito.any(), Mockito.anyString())).thenReturn(1);
        Whitebox.invokeMethod(inventoryService, "pushDataToB2B", aliRequest, "INVENTORY_MSG_TYPE");

        Assert.assertNotNull(dto);
    }

}
