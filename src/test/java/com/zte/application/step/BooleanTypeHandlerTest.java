package com.zte.application.step;

import com.zte.common.utils.Constant;
import com.zte.handler.BooleanTypeHandler;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.sql.*;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @since 2023年9月8日17:15
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class BooleanTypeHandlerTest {

    @InjectMocks
    private BooleanTypeHandler booleanTypeHandler;

    @Mock
    private PreparedStatement preparedStatement;

    @Mock
    private ResultSet resultSet;

    @Mock
    private CallableStatement callableStatement;

    @Test
    public void validateBooleanTypeHandler() throws Exception {
        this.booleanTypeHandler.setParameter(preparedStatement, 0, null, null);
        this.booleanTypeHandler.setParameter(preparedStatement, 0, true, null);
        this.booleanTypeHandler.setParameter(preparedStatement, 0, false, null);
        this.booleanTypeHandler.getResult(resultSet, null);
        PowerMockito.when(resultSet.getString(anyString())).thenReturn(Constant.STR_TRUE);
        this.booleanTypeHandler.getResult(resultSet, Constant.STR_TRUE);

        this.booleanTypeHandler.getResult(resultSet, 0);
        PowerMockito.when(resultSet.getBoolean(anyInt())).thenReturn(true);
        this.booleanTypeHandler.getResult(resultSet, 0);
        PowerMockito.when(resultSet.getBoolean(anyInt())).thenReturn(false);
        this.booleanTypeHandler.getResult(resultSet, 0);

        this.booleanTypeHandler.getResult(callableStatement, 0);
        PowerMockito.when(callableStatement.getBoolean(anyInt())).thenReturn(true);
        this.booleanTypeHandler.getResult(callableStatement, 0);
        PowerMockito.when(callableStatement.getBoolean(anyInt())).thenReturn(false);
        Object test = this.booleanTypeHandler.getResult(callableStatement, 0);
        Assert.assertTrue(Objects.nonNull(test));
    }
}


