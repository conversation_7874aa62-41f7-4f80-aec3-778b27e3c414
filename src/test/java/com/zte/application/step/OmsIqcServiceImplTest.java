package com.zte.application.step;

import com.alibaba.fastjson.JSON;
import com.zte.application.infor.impl.TransferToInforServiceImpl;
import com.zte.application.step.impl.OmsIqcServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.IscpRemoteServiceDataUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.*;
import com.zte.interfaces.infor.dto.IscpResultDTO;
import com.zte.interfaces.infor.dto.QaExInspectionHead;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, IscpRemoteServiceDataUtil.class, CommonUtils.class, WebServiceClient.class})
public class OmsIqcServiceImplTest {

    @InjectMocks
    OmsIqcServiceImpl omsIqcServiceImpl;
    @Mock
    private OmsTransferRepository omsTransferRepository;
    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, IscpRemoteServiceDataUtil.class, CommonUtils.class, WebServiceClient.class);
        ReflectionTestUtils.setField(omsIqcServiceImpl, "inoneUrl", "https://icosg.test.zte.com.cn");
        ReflectionTestUtils.setField(omsIqcServiceImpl, "inoneAppcode", "ad8e86b3658f4dad8b2224a140e2db61");
    }

    /* Started by AICoder, pid:dcedfb057a4542b78a3fa74dbbe97452 */
    @Test
    public void pushTransferCheckInfoToIqc() throws Exception {
        // 准备测试数据
        TransferDetailDTO dto = TransferDetailDTO.builder()
                .billNo("TR001")
                .lastUpdatedBy("testUser")
                .build();
        List<TransferDetailDTO> transferDetailDTOList = new ArrayList<>();
        TransferDetailDTO transferDTO = TransferDetailDTO.builder()
                .billNo("TR001")
                .status(STR_01)
                .stockNo(STR_WMWHSE11)
                .supplierNo("SUP001")
                .supplierName("供应商1")
                .itemBarcode("ITEM001")
                .deliveryQty(new BigDecimal("10"))
                .barcodeControlType(STR_02)
                .build();
        transferDetailDTOList.add(transferDTO);
        PowerMockito.when(omsTransferRepository.getTransferBillIqcInfo(dto)).thenReturn(transferDetailDTOList);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        transferDetailDTOList.clear();
        transferDTO.setStatus(STR_02).setStockNo("WMWHSE1");
        transferDetailDTOList.add(transferDTO);
        PowerMockito.when(omsTransferRepository.getTransferBillIqcInfo(dto)).thenReturn(transferDetailDTOList);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        transferDetailDTOList.clear();
        transferDTO.setStockNo(STR_WMWHSE11);
        transferDetailDTOList.add(transferDTO);
        PowerMockito.when(omsTransferRepository.getTransferBillIqcInfo(dto)).thenReturn(transferDetailDTOList);

        Whitebox.invokeMethod(omsIqcServiceImpl, "getQaExInspectionHead", transferDTO);
        PowerMockito.when(omsTransferRepository.updateTransferDetailBatch(any())).thenReturn(1);
        // Mock ISCP服务调用
        ServiceData<List<IscpResultDTO>> serviceData = new ServiceData<>();
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        RetCode retCode = new RetCode();
        serviceData.setCode(retCode);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);


        retCode.setCode("0005");
        serviceData.setCode(retCode);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        retCode.setCode(SUCESS_CODE);
        serviceData.setCode(retCode);
        List<IscpResultDTO> iscpResultList = new ArrayList<>();
        serviceData.setBo(iscpResultList);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        IscpResultDTO iscpResultDTO = new IscpResultDTO();
        iscpResultDTO.setProcessStatus(E);
        iscpResultList.add(iscpResultDTO);
        serviceData.setBo(iscpResultList);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        iscpResultList.clear();
        iscpResultDTO.setProcessStatus(S);
        iscpResultList.add(iscpResultDTO);
        serviceData.setBo(iscpResultList);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(any())).thenReturn(serviceData);
        PowerMockito.when(omsTransferRepository.updateTransferDetailBatch(any())).thenReturn(1);
        PowerMockito.when(omsTransferRepository.updateTransferHead(any())).thenReturn(1);
        Whitebox.invokeMethod(omsIqcServiceImpl, "pushTransferCheckInfoToIqc", dto);

        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Ended by AICoder, pid:dcedfb057a4542b78a3fa74dbbe97452 */

    /* Started by AICoder, pid:060d4a5bf96b4425b1c56caf22430d9b */
    @Test
    public void getQaExInspectionHead() throws Exception {
        // 准备测试数据
        TransferDetailDTO transferDetailDTO = TransferDetailDTO.builder()
                .billNo("TR001")
                .supplierNo("SUP001")
                .supplierName("供应商1")
                .stockNo(STR_WMWHSE11)
                .itemBarcode("ITEM001")
                .deliveryQty(new BigDecimal("10"))
                .barcodeControlType(STR_02)
                .build();

        Whitebox.invokeMethod(omsIqcServiceImpl, "getQaExInspectionHead", transferDetailDTO);

        transferDetailDTO.setBarcodeControlType(STR_01);
        Whitebox.invokeMethod(omsIqcServiceImpl, "getQaExInspectionHead", transferDetailDTO);

        transferDetailDTO.setBarcodeControlType(STR_0);
        Whitebox.invokeMethod(omsIqcServiceImpl, "getQaExInspectionHead", transferDetailDTO);

        Assert.assertTrue(Objects.nonNull(transferDetailDTO));
    }
    /* Ended by AICoder, pid:060d4a5bf96b4425b1c56caf22430d9b */

    /* Started by AICoder, pid:64ff692585ba4ab9990084bd16901a64 */
    @Test
    public void transferCheckResultToOms() throws Exception {
        // 准备测试数据
        CheckResultHeadDTO checkResultHeadDTO = CheckResultHeadDTO.builder()
                .recheckNo("TR001")
                .lineId("LINE001")
                .dealer("testUser")
                .build();
        List<CheckResultDetailDTO> checkResultDetailDTOList = new ArrayList<>();
        CheckResultDetailDTO checkResultDetailDTO = new CheckResultDetailDTO();
        checkResultDetailDTO.setRightQty(new BigDecimal("8"));
        checkResultDetailDTOList.add(checkResultDetailDTO);
        checkResultHeadDTO.setCheckResultDetailDTOList(checkResultDetailDTOList);

        List<TransferDetailDTO> transferDetailDTOList = new ArrayList<>();
        TransferDetailDTO transferDTO = TransferDetailDTO.builder()
                .billNo("TR001")
                .lineId("LINE001")
                .status(STR_06) // 检验中
                .checkStatus(TESTING) // 检验中
                .build();
        transferDetailDTOList.add(transferDTO);
        PowerMockito.when(omsTransferRepository.getTransferBillInfo(any())).thenReturn(transferDetailDTOList);
        PowerMockito.when(omsTransferRepository.updateTransferDetail(any())).thenReturn(1);

        List<TransferDetailDTO> transferDetailDTOList2 = new ArrayList<>();
        TransferDetailDTO transferDTO2 = TransferDetailDTO.builder()
                .billNo("TR001")
                .lineId("LINE002")
                .status(STR_06) // 检验中
                .checkStatus(TESTING) // 检验中
                .build();
        transferDetailDTOList2.add(transferDTO2);
        PowerMockito.when(omsTransferRepository.getTransferBillInfo(any())).thenReturn(transferDetailDTOList2);
        Whitebox.invokeMethod(omsIqcServiceImpl, "transferCheckResultToOms", checkResultHeadDTO);

        transferDetailDTOList2.clear();
        transferDTO2.setRightQty(new BigDecimal("0"));
        transferDetailDTOList2.add(transferDTO2);
        PowerMockito.when(omsTransferRepository.getTransferBillInfo(any())).thenReturn(transferDetailDTOList2);
        PowerMockito.when(omsTransferRepository.updateTransferHead(any())).thenReturn(1);
        Whitebox.invokeMethod(omsIqcServiceImpl, "transferCheckResultToOms", checkResultHeadDTO);

        transferDetailDTOList2.clear();
        transferDTO2.setRightQty(new BigDecimal("8"));
        transferDetailDTOList2.add(transferDTO2);
        PowerMockito.when(omsTransferRepository.getTransferBillInfo(any())).thenReturn(transferDetailDTOList2);
        PowerMockito.when(omsTransferRepository.updateTransferHead(any())).thenReturn(1);
        ServiceData serviceData = new ServiceData<>();
        RetCode retCode = new RetCode("ERROR", "Failed");
        serviceData.setCode(retCode);
        PowerMockito.when(WebServiceClient.submitInfor(anyString(), anyString())).thenReturn(serviceData);
        PowerMockito.when(onlineFallBackApplyBillRepository.getWsdlUrl()).thenReturn("http://test.url");
        Whitebox.invokeMethod(omsIqcServiceImpl, "sendInfor", "111");
        Whitebox.invokeMethod(omsIqcServiceImpl, "transferCheckResultToOms", checkResultHeadDTO);

        retCode.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(retCode);
        PowerMockito.when(WebServiceClient.submitInfor(anyString(), anyString())).thenReturn(serviceData);
        PowerMockito.when(omsTransferRepository.insertTransferSoHead(any())).thenReturn(1);
        PowerMockito.when(omsTransferRepository.insertTransferSoDetail(any())).thenReturn(1);
        PowerMockito.when(omsTransferRepository.updateTransferHead(any())).thenReturn(1);
        Whitebox.invokeMethod(omsIqcServiceImpl, "transferCheckResultToOms", checkResultHeadDTO);

        Assert.assertTrue(Objects.nonNull(checkResultHeadDTO));
    }
    /* Ended by AICoder, pid:64ff692585ba4ab9990084bd16901a64 */

    /* Started by AICoder, pid:b910e166dc82416189da44087caaad23 */
    @Test
    public void sendInfor() throws Exception {

        String billNo = "TR001";
        PowerMockito.when(onlineFallBackApplyBillRepository.getWsdlUrl()).thenReturn("http://test.url");
        List<SoHeader> soHeaderList = new ArrayList<>();
        SoHeader soHeader = new SoHeader();
        soHeader.setExternalOrderKey2("TR001");
        soHeaderList.add(soHeader);
        PowerMockito.when(omsTransferRepository.getTransferSoHead(billNo)).thenReturn(soHeaderList);

        List<SoDetail> soDetailList = new ArrayList<>();
        SoDetail soDetail = new SoDetail();
        soDetail.setRef60("TR001");
        soDetailList.add(soDetail);
        PowerMockito.when(omsTransferRepository.getTransferSoDetail(billNo)).thenReturn(soDetailList);

        ServiceData serviceData = new ServiceData<>();
        RetCode retCode = new RetCode("ERROR", "Failed");
        serviceData.setCode(retCode);
        PowerMockito.when(WebServiceClient.submitInfor(anyString(), anyString())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "sendInfor", billNo);

        retCode.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(retCode);
        PowerMockito.when(WebServiceClient.submitInfor(anyString(), anyString())).thenReturn(serviceData);
        Whitebox.invokeMethod(omsIqcServiceImpl, "sendInfor", billNo);

        Assert.assertTrue(Objects.nonNull(billNo));
    }
    /* Ended by AICoder, pid:b910e166dc82416189da44087caaad23 */

    /* Started by AICoder, pid:249621fe6f20433da339b1a5bfb91fce */
    @Test
    public void getXmlMessage() throws Exception {
        String billNo = "TR001";

        List<SoHeader> soHeaderList = new ArrayList<>();
        SoHeader soHeader = new SoHeader();
        soHeader.setExternalOrderKey2("TR001");
        soHeaderList.add(soHeader);
        PowerMockito.when(omsTransferRepository.getTransferSoHead(billNo)).thenReturn(soHeaderList);

        List<SoDetail> soDetailList = new ArrayList<>();
        Whitebox.invokeMethod(omsIqcServiceImpl, "getXmlMessage", billNo);

        SoDetail soDetail = new SoDetail();
        soDetail.setRef60("TR001");
        soDetailList.add(soDetail);
        PowerMockito.when(omsTransferRepository.getTransferSoDetail(billNo)).thenReturn(soDetailList);
        Whitebox.invokeMethod(omsIqcServiceImpl, "getXmlMessage", billNo);

        Assert.assertTrue(Objects.nonNull(billNo));
    }
    /* Ended by AICoder, pid:249621fe6f20433da339b1a5bfb91fce */

} 