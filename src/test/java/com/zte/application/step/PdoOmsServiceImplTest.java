package com.zte.application.step;

import com.zte.application.step.impl.PdoOmsServiceImpl;
import com.zte.common.constants.PdoConstants;
import com.zte.domain.model.step.PdoOmsRepository;
import com.zte.interfaces.step.dto.ErrorDto;
import com.zte.interfaces.step.dto.PdoDetailInputDto;
import com.zte.interfaces.step.dto.PdoDetailOutputDto;
import com.zte.interfaces.step.dto.PdoDetailResultDto;
import com.zte.itp.msa.core.exception.BusiException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.dao.DuplicateKeyException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.zte.common.constants.PdoConstants.MSG_INPUT_LIMIT_EXCEEDED;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

/**
 * PdoOmsServiceImpl单元测试
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class PdoOmsServiceImplTest {

    @InjectMocks
    private PdoOmsServiceImpl pdoOmsService;

    @Mock
    private PdoOmsRepository pdoOmsRepository;

    @Before
    public void setUp() throws Exception {
        // 简化设置，不需要mock UUID
    }

    /**
     * 测试输入数据超过500条限制的情况
     */
    @Test
    public void testCreatePdoDetail_InputExceeds500Limit() {
        // 准备测试数据 - 超过500条
        List<PdoDetailInputDto> inputDtoList = new ArrayList<>();
        for (int i = 0; i < 501; i++) {
            PdoDetailInputDto dto = new PdoDetailInputDto();
            dto.setPdoBill("PDO" + i);
            dto.setItemNo("ITEM" + i);
            dto.setReqQty(new BigDecimal(100));
            inputDtoList.add(dto);
        }

        // 执行测试，期望抛出BusiException
        try {
            pdoOmsService.createPdoDetail(inputDtoList);
            Assert.fail("应该抛出BusiException异常");
        } catch (BusiException e) {
            // 验证异常信息
            Assert.assertTrue(e.getMessage().contains("params.limit"));
        }
    }

    /**
     * 测试批量插入成功的情况
     */
    @Test
    public void testCreatePdoDetail_BatchInsertSuccess() {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(3);

        // 模拟repository返回成功
        when(pdoOmsRepository.batchInsertPdoDetail(anyList())).thenReturn(3);

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getOutputCollection());
        Assert.assertEquals(3, result.getOutputCollection().size());
        
        // 验证输出集合中的每个结果
        for (int i = 0; i < result.getOutputCollection().size(); i++) {
            PdoDetailResultDto resultDto = result.getOutputCollection().get(i);
            Assert.assertEquals("PDO" + i, resultDto.getPdoBill());
            Assert.assertEquals("ITEM" + i, resultDto.getItemNo());
            Assert.assertEquals(new BigDecimal(100 * (i + 1)), resultDto.getReqQty());
            Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS, resultDto.getStatus());
            Assert.assertEquals(PdoConstants.MSG_PDO_DETAIL_SUCCESS, resultDto.getMessage());
        }
    }

    /**
     * 测试批量插入行数不匹配的情况
     */
    @Test
    public void testCreatePdoDetail_BatchInsertMismatch() {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(3);

        // 模拟repository返回不匹配的行数
        when(pdoOmsRepository.batchInsertPdoDetail(anyList())).thenReturn(2);

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getErrorCollection());
        Assert.assertEquals(1, result.getErrorCollection().size());
        Assert.assertEquals(PdoConstants.ERROR_CODE_SYSTEM_ERROR, result.getErrorCollection().get(0).getErrorCode());
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains("批量插入异常"));
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains("预期插入3条，实际插入2条"));
    }

    /**
     * 测试重复键异常的情况
     */
    @Test
    public void testCreatePdoDetail_DuplicateKeyException() {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(2);

        // 模拟repository抛出重复键异常
        when(pdoOmsRepository.batchInsertPdoDetail(anyList()))
                .thenThrow(new DuplicateKeyException("Duplicate key error"));

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getErrorCollection());
        Assert.assertEquals(1, result.getErrorCollection().size());
        Assert.assertEquals(PdoConstants.ERROR_CODE_DUPLICATE, result.getErrorCollection().get(0).getErrorCode());
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains(PdoConstants.ERROR_MSG_DUPLICATE));
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains(PdoConstants.MSG_DUPLICATE_ROLLBACK));
    }

    /**
     * 测试系统异常的情况
     */
    @Test
    public void testCreatePdoDetail_SystemException() {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(2);

        // 模拟repository抛出系统异常
        when(pdoOmsRepository.batchInsertPdoDetail(anyList()))
                .thenThrow(new RuntimeException("System error occurred"));

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getErrorCollection());
        Assert.assertEquals(1, result.getErrorCollection().size());
        Assert.assertEquals(PdoConstants.ERROR_CODE_SYSTEM_ERROR, result.getErrorCollection().get(0).getErrorCode());
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains(PdoConstants.MSG_SYSTEM_ERROR_PREFIX));
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains("System error occurred"));
        Assert.assertTrue(result.getErrorCollection().get(0).getErrorMessage().contains(PdoConstants.MSG_DUPLICATE_ROLLBACK));
    }

    /**
     * 测试边界情况 - 输入数据正好500条
     */
    @Test
    public void testCreatePdoDetail_ExactlyLimit() {
        // 准备测试数据 - 正好500条
        List<PdoDetailInputDto> inputDtoList = createTestInputData(500);

        // 模拟repository返回成功
        when(pdoOmsRepository.batchInsertPdoDetail(anyList())).thenReturn(500);

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getOutputCollection());
        Assert.assertEquals(500, result.getOutputCollection().size());
    }

    /**
     * 测试空列表的情况
     */
    @Test
    public void testCreatePdoDetail_EmptyList() {
        // 准备测试数据 - 空列表
        List<PdoDetailInputDto> inputDtoList = new ArrayList<>();

        // 模拟repository返回0
        when(pdoOmsRepository.batchInsertPdoDetail(anyList())).thenReturn(0);

        // 执行测试
        PdoDetailOutputDto result = pdoOmsService.createPdoDetail(inputDtoList);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getOutputCollection());
        Assert.assertEquals(0, result.getOutputCollection().size());
    }

    /**
     * 测试buildSuccessOutput私有方法
     */
    @Test
    public void testBuildSuccessOutput() throws Exception {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(2);
        PdoDetailOutputDto outputDto = new PdoDetailOutputDto();
        List<PdoDetailResultDto> resultList = new ArrayList<>();

        // 调用私有方法
        Whitebox.invokeMethod(pdoOmsService, "buildSuccessOutput", outputDto, resultList, inputDtoList);

        // 验证结果
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS, outputDto.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_SUCCESS_DESC, outputDto.getProcessStatusDesc());
        Assert.assertNotNull(outputDto.getOutputCollection());
        Assert.assertEquals(2, outputDto.getOutputCollection().size());
        Assert.assertEquals(2, resultList.size());
    }

    /**
     * 测试buildErrorOutput私有方法（三参数版本）
     */
    @Test
    public void testBuildErrorOutput_ThreeParams() throws Exception {
        // 准备测试数据
        PdoDetailOutputDto outputDto = new PdoDetailOutputDto();
        List<ErrorDto> errorList = new ArrayList<>();
        String errorCode = PdoConstants.ERROR_CODE_SYSTEM_ERROR;
        String errorMsg = "测试错误消息";
        String detail = "测试详情";

        // 调用私有方法
        Whitebox.invokeMethod(pdoOmsService, "buildErrorOutput", outputDto, errorList, errorCode, errorMsg, detail);

        // 验证结果
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR, outputDto.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR_DESC, outputDto.getProcessStatusDesc());
        Assert.assertNotNull(outputDto.getErrorCollection());
        Assert.assertEquals(1, outputDto.getErrorCollection().size());
        Assert.assertEquals(1, errorList.size());
        Assert.assertEquals(errorCode, errorList.get(0).getErrorCode());
        Assert.assertEquals(errorMsg, errorList.get(0).getErrorMessage());
        Assert.assertEquals(detail, errorList.get(0).getErrorField());
    }

    /**
     * 测试buildErrorOutput私有方法（两参数版本）
     */
    @Test
    public void testBuildErrorOutput_TwoParams() throws Exception {
        // 准备测试数据
        String errorCode = PdoConstants.ERROR_CODE_DUPLICATE;
        String errorMsg = "重复错误消息";

        // 调用私有方法
        PdoDetailOutputDto result = Whitebox.invokeMethod(pdoOmsService, "buildErrorOutput", errorCode, errorMsg);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR, result.getProcessStatus());
        Assert.assertEquals(PdoConstants.PROCESS_STATUS_ERROR_DESC, result.getProcessStatusDesc());
        Assert.assertNotNull(result.getErrorCollection());
        Assert.assertEquals(1, result.getErrorCollection().size());
        Assert.assertEquals(errorCode, result.getErrorCollection().get(0).getErrorCode());
        Assert.assertEquals(errorMsg, result.getErrorCollection().get(0).getErrorMessage());
        Assert.assertNull(result.getErrorCollection().get(0).getErrorField());
    }

    /**
     * 测试UUID生成功能
     */
    @Test
    public void testUuidGeneration() {
        // 准备测试数据
        List<PdoDetailInputDto> inputDtoList = createTestInputData(2);

        // 模拟repository返回成功
        when(pdoOmsRepository.batchInsertPdoDetail(anyList())).thenReturn(2);

        // 执行测试
        pdoOmsService.createPdoDetail(inputDtoList);

        // 验证UUID被设置（不验证具体值，只验证不为空）
        for (PdoDetailInputDto dto : inputDtoList) {
            Assert.assertNotNull(dto.getId());
            Assert.assertTrue(dto.getId().length() > 0);
        }
    }

    /**
     * 创建测试输入数据的辅助方法
     */
    private List<PdoDetailInputDto> createTestInputData(int count) {
        List<PdoDetailInputDto> inputDtoList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            PdoDetailInputDto dto = new PdoDetailInputDto();
            dto.setPdoBill("PDO" + i);
            dto.setItemNo("ITEM" + i);
            dto.setReqQty(new BigDecimal(100 * (i + 1)));
            inputDtoList.add(dto);
        }
        return inputDtoList;
    }
} 