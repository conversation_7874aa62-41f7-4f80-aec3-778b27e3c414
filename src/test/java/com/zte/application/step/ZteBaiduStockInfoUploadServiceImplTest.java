/*Started by AICoder, pid:y919fte614n456514c2e0ae641b65773dce47929*/
package com.zte.application.step;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.step.impl.ZteBaiduStockInfoUploadServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.InforIapsRepository;
import com.zte.domain.model.infor.SysWebserviceConfigRepository;
import com.zte.domain.model.infor.ZteBaiduStockInfoUploadRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.interfaces.step.dto.CivControlInfoDTO;
import com.zte.interfaces.step.dto.ErpStockResultDTO;
import com.zte.interfaces.step.dto.ZteBaiduInfoDTO;
import com.zte.interfaces.step.dto.ZteBaiduReturnDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.INT_6;
import static com.zte.common.utils.Constant.WEBSERVICE_LOOKUP_TYPE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, RedisSerialNoUtil.class, CommonUtils.class, JacksonJsonConverUtil.class})
public class ZteBaiduStockInfoUploadServiceImplTest {

    @InjectMocks
    private ZteBaiduStockInfoUploadServiceImpl zteBaiduStockInfoUploadService;

    @Mock
    private ZteBaiduStockInfoUploadRepository zteBaiduStockInfoUploadRepository;
    @Mock
    private ZteStockInfoUploadService zteStockInfoUploadService;
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    @Mock
    private SysWebserviceConfigRepository repository;
    @Mock
    private StepTransferRepository stepTransferRepository;
    @Mock
    private InforIapsRepository inforIapsRepository;
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Mock
    private EmailUtil emailUtil;
    @Mock
    private ObjectMapper mapperInstance;
    private ZteBaiduInfoDTO dto;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(EmailUtil.class, BusiAssertException.class, HttpClientUtil.class, CommonUtils.class, RedisSerialNoUtil.class, JacksonJsonConverUtil.class);

        dto = new ZteBaiduInfoDTO();
        dto.setEmpNo("testEmpNo");
        dto.setItemNo(new ArrayList<>());
    }

    @Test
    public void testBaiduStockInfoUpload_ItemNoExceeds500() throws Exception {
        for (int i = 0; i < 501; i++) {
            dto.getItemNo().add("item" + i);
        }
        zteBaiduStockInfoUploadService.baiduStockInfoUpload(dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void testBaiduStockInfoUpload_NoDataFromRepository() throws Exception {
        when(zteBaiduStockInfoUploadRepository.getBaiduStockInfoList(any())).thenReturn(Collections.emptyList());

        zteBaiduStockInfoUploadService.baiduStockInfoUpload(dto);

        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void testBaiduStockInfoUpload_WithDataFromRepository() throws Exception {
        List<ZteBaiduReturnDTO> returnDTOS = new ArrayList<>();
        ZteBaiduReturnDTO returnDTO = new ZteBaiduReturnDTO();
        returnDTO.setOemPartName("testItem");
        returnDTO.setTotalStockAmount(100);
        returnDTOS.add(returnDTO);
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        String responseStr4 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":0.005,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        PowerMockito.when(zteBaiduStockInfoUploadRepository.getBaiduStockInfoList(any())).thenReturn(returnDTOS);
        List<AllocateExceptionMonitorVo> barcodeAgeList = new ArrayList<>();
        barcodeAgeList.add(new AllocateExceptionMonitorVo() {{
            setItemNo("itemNo");
            setStoreAge("storeAge");
        }});
        barcodeAgeList.add(new AllocateExceptionMonitorVo() {{
            setItemNo("itemNo");
            setStoreAge("storeAge");
        }});
        PowerMockito.when(stepTransferRepository.getMaxStockAgeByBarcode(Mockito.anyList())).thenReturn(barcodeAgeList);
        List<String> list = new ArrayList<>();
        PowerMockito.when(inforIapsRepository.getCodeByCodelkup()).thenReturn(list);
        List<SysLookupValuesDTO> list1 = new ArrayList<>();
        PowerMockito.when(repository.getWebserviceInterface(Mockito.anyList())).thenReturn(list1);
        PowerMockito.when(zteStockInfoUploadService.getDataTransferBatchNo(Mockito.anyString())).thenReturn("IQC2024100900001");
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(Constant.ZTE1, INT_6)).thenReturn("IQC2024100900001");
        zteBaiduStockInfoUploadService.baiduStockInfoUpload(dto);
        Assert.assertTrue(Objects.nonNull(dto.toString()));

        list.add("ORG1");
        list.add("ORG2");

        zteBaiduStockInfoUploadService.baiduStockInfoUpload(dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void testQueryTotalQuantity() throws Exception {
        List<ZteBaiduReturnDTO> zteBaiduReturnDTOList = new ArrayList<>();
        zteBaiduReturnDTOList.add(new ZteBaiduReturnDTO() {{
            setOemPartName("itemNo1");
        }});
        List<SysLookupValuesDTO> list1 = new ArrayList<>();
        PowerMockito.when(repository.getWebserviceInterface(Mockito.anyList())).thenReturn(list1);
        List<String> list = new ArrayList<>();
        list.add("ORG1");
        list.add("ORG2");
        PowerMockito.when(inforIapsRepository.getCodeByCodelkup()).thenReturn(list);
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        String responseStr4 = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"Success\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"organizationId\": 4457,\n" +
                "      \"segment1\": \"040081600537\",\n" +
                "      \"quantity\": 170,\n" +
                "      \"locatorId\": 197597,\n" +
                "      \"subinventoryCode\": \"WMWHSE26\",\n" +
                "      \"primaryUomCode\": \"块\",\n" +
                "      \"revision\": null\n" +
                "    }\n" +
                "  ],\n" +
                "  \"other\": null,\n" +
                "  \"responseRule\": \"msa\"\n" +
                "}";
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        List<ErpStockResultDTO> dtoList = new ArrayList<>();
        dtoList.add(new ErpStockResultDTO(){{
            setSegment1("s1");
        }});
        dtoList.add(new ErpStockResultDTO(){{
            setSegment1("s1");
            setQuantity(1);
        }});
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(dtoList);
        Whitebox.invokeMethod(zteBaiduStockInfoUploadService, "queryTotalQuantity", zteBaiduReturnDTOList, "empNo");
        Assert.assertTrue((dto != null));
    }

    @Test
    public void testQueryCivControlInfoList() throws Exception {
        List<ZteBaiduReturnDTO> zteBaiduReturnDTOList = new ArrayList<>();
        zteBaiduReturnDTOList.add(new ZteBaiduReturnDTO() {{
            setOemPartName("itemNo1");
        }});
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        String responseStr4 = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"pageNum\": 1,\n" +
                "        \"pageSize\": 10,\n" +
                "        \"size\": 1,\n" +
                "        \"orderBy\": null,\n" +
                "        \"startRow\": 1,\n" +
                "        \"endRow\": 1,\n" +
                "        \"total\": 1,\n" +
                "        \"pages\": 1,\n" +
                "        \"list\": [\n" +
                "        ],\n" +
                "        \"firstPage\": 1,\n" +
                "        \"prePage\": 0,\n" +
                "        \"nextPage\": 0,\n" +
                "        \"lastPage\": 1,\n" +
                "        \"isFirstPage\": true,\n" +
                "        \"isLastPage\": true,\n" +
                "        \"hasPreviousPage\": false,\n" +
                "        \"hasNextPage\": false,\n" +
                "        \"navigatePages\": 8,\n" +
                "        \"navigatepageNums\": [\n" +
                "            1\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        List<CivControlInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(new CivControlInfoDTO(){{
            setItemNo("item1");
            setCustomerSupplyType("supply1");
        }});
        dtoList.add(new CivControlInfoDTO(){{
            setItemNo("item1");
            setCustomerSupplyType("supply1");
        }});
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(dtoList);
        Whitebox.invokeMethod(zteBaiduStockInfoUploadService, "queryCivControlInfoList", zteBaiduReturnDTOList, "empNo");
        Assert.assertTrue((dto != null));
    }

    @Test
    public void testPushDataToB2BAndUploadLog() throws Exception {
        ZteBaiduInfoDTO dto = new ZteBaiduInfoDTO();
        List<ZteBaiduReturnDTO> zteBaiduReturnDTOS = new ArrayList<>();
        Whitebox.invokeMethod(zteBaiduStockInfoUploadService, "pushDataToB2BAndUploadLog", zteBaiduReturnDTOS, dto);

        zteBaiduReturnDTOS.add(new ZteBaiduReturnDTO(){{
            setOemPartName("item1");
            setStockType("str");
            setMaxStockAge(-1);
        }});
        zteBaiduReturnDTOS.add(new ZteBaiduReturnDTO(){{
            setOemPartName("item2");
            setStockType("str");
        }});
        zteBaiduReturnDTOS.add(new ZteBaiduReturnDTO(){{
            setOemPartName("item2");
            setStockType("");
        }});
        Whitebox.invokeMethod(zteBaiduStockInfoUploadService, "pushDataToB2BAndUploadLog", zteBaiduReturnDTOS, dto);

        Assert.assertTrue((dto != null));
    }
}
/*Ended by AICoder, pid:y919fte614n456514c2e0ae641b65773dce47929*/