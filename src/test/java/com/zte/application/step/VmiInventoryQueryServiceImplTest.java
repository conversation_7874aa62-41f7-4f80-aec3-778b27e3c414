package com.zte.application.step;


import java.util.Arrays;
import java.util.List;

import com.zte.application.infor.impl.IqcTestRequisitionServiceImpl;
import com.zte.domain.model.step.VmiInventoryQueryRepository;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import com.zte.Application;
import com.zte.BaseTestCase;
import com.zte.application.step.impl.VmiInventoryQueryServiceImpl;
import com.zte.interfaces.step.dto.VmiDetailInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiSkuInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiUUIDInventoryQueryDTO;
import com.zte.itp.msa.core.model.ServiceData;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class})
public class VmiInventoryQueryServiceImplTest extends BaseTestCase {

	@InjectMocks
    private  VmiInventoryQueryServiceImpl  vmiInventoryQueryServiceImpl;

	@Mock
	private VmiInventoryQueryRepository vmiInventoryQueryRepository;

	@Before
	public void init() throws Exception {
		PowerMockito.mockStatic(BusiAssertException.class);
	}

	@Test
    public void vmiSkuInventoryQuery() throws Exception {
    	VmiSkuInventoryQueryDTO inDTO=new VmiSkuInventoryQueryDTO();
    	String [] itemNos= {"045020100204","045020100204","045020100204"};
    	List<String> itemNo=Arrays.asList(itemNos);
    	inDTO.setItemNo(itemNo);
    	inDTO.setPageNo(1);
    	inDTO.setPageSize(10);
		Whitebox.invokeMethod(vmiInventoryQueryServiceImpl, "vmiSkuInventoryQuery", inDTO);
    	ServiceData<?> delayTest=vmiInventoryQueryServiceImpl.vmiSkuInventoryQuery(inDTO);
        Assert.assertTrue((delayTest!=null));
    }
    
    @Test 
    public void vmiUUIDInventoryQuery() throws Exception {
    	VmiUUIDInventoryQueryDTO inDTO=new VmiUUIDInventoryQueryDTO();
    	String [] itemNos= {"045020100204","045020100204","045020100204"};
    	List<String> itemNo=Arrays.asList(itemNos);
    	inDTO.setItemNo(itemNo);
    	inDTO.setPageNo(1);
    	inDTO.setPageSize(10);
    	ServiceData<?> delayTest=vmiInventoryQueryServiceImpl.vmiUUIDInventoryQuery(inDTO);
        Assert.assertTrue((delayTest!=null));
    }
    
    @Test 
    public void vmiDetailInventoryQuery() throws Exception {
    	VmiDetailInventoryQueryDTO inDTO=new VmiDetailInventoryQueryDTO();
    	String [] itemNos= {"045020100204","045020100204","045020100204"};
    	List<String> itemNo=Arrays.asList(itemNos);
    	inDTO.setItemNo(itemNo);
    	inDTO.setPageNo(1);
    	inDTO.setPageSize(10);
    	ServiceData<?> delayTest=vmiInventoryQueryServiceImpl.vmiDetailInventoryQuery(inDTO);
        Assert.assertTrue((delayTest!=null));
    }
}
