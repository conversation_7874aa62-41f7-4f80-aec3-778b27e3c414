package com.zte.application.step;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.step.impl.OmsEcssServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.security.ZteSecurity;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_16;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({EmailUtil.class, BusiAssertException.class, HttpClientUtil.class})
public class OmsEcssServiceImplTest {

    @InjectMocks
    OmsEcssServiceImpl omsEcssServiceImpl;
    @Mock
    private OmsEcssRepository omsEcssRepository;
    @Mock
    private EmailUtil emailUtil;
    @Mock
    private ZteSecurity zteSecurity;
    @Value("${ecss.url}")
    private String ecssUrl;

    @Before
    public void init() {
        PowerMockito.mockStatic(EmailUtil.class, BusiAssertException.class, HttpClientUtil.class);
    }

    @Test
    public void pushBillToEcss() throws Exception {

        EcssBillDTO dto = new EcssBillDTO();
        dto.setLastUpdatedBy("23423");
        List<EcssBillDTO> ecssBillList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getAddingBillNo(dto)).thenReturn(ecssBillList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "pushBillToEcss", dto);

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("C22080500002");
        ecssBillDTO.setFailNum(0);
        ecssBillList.add(ecssBillDTO);
        PowerMockito.when(omsEcssRepository.getAddingBillNo(dto)).thenReturn(ecssBillList);

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        ServiceData<?> res = new ServiceData<>();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        retCode.setMsg("2143124");
        res.setCode(retCode);

        PowerMockito.field(OmsEcssServiceImpl.class, "ecssUrl").set(omsEcssServiceImpl, "http://zecss.test.zte.com.cn");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getFsDocumentDTO", ecssBillDTO.getReferenceNumber());
        Whitebox.invokeMethod(omsEcssServiceImpl, "invokeBillToEcss", fsDocumentDTO, dto.getLastUpdatedBy(), 395);
        Whitebox.invokeMethod(omsEcssServiceImpl, "dealEcssBill",ecssBillDTO, fsDocumentDTO, res, "23423");
        Whitebox.invokeMethod(omsEcssServiceImpl, "pushBillToEcss", dto);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getFsDocumentDTO() throws Exception {

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        fsDocumentDTO.setReferenceNumber("C22080500002");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getCode", fsDocumentDTO.getReferenceNumber());
        List<EcssBillDTO> materialInfoList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "getFsDocumentDTO", "C22080500002");
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("C22080500002");
        materialInfoList.add(ecssBillDTO);
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "getFsDocumentDTO", "C22080500002");


        FsDocumentDTO fsDocumentDTO2 = new FsDocumentDTO();
        fsDocumentDTO2.setReferenceNumber("ND22080500002");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getCode", fsDocumentDTO2.getReferenceNumber());
        List<EcssBillDTO> materialInfoList2 = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList2);
        Whitebox.invokeMethod(omsEcssServiceImpl, "getFsDocumentDTO", "ND22080500002");
        EcssBillDTO ecssBillDTO2 = new EcssBillDTO();
        ecssBillDTO2.setReferenceNumber("C22080500002");
        materialInfoList2.add(ecssBillDTO2);
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.anyString())).thenReturn(materialInfoList2);
        FsDocumentDTO test = omsEcssServiceImpl.getFsDocumentDTO("ND22080500002");
        Assert.assertTrue(Objects.nonNull(test));
    }


    @Test
    public void getCode() throws Exception {

        String test = "23141";
        Whitebox.invokeMethod(omsEcssServiceImpl, "getCode", "C22080500002");
        Assert.assertTrue(Objects.nonNull(test));
    }

    @Test
    public void invokeBillToEcss() throws Exception {

        Integer saleOrderOrg = 395;
        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        PowerMockito.field(OmsEcssServiceImpl.class, "ecssUrl").set(omsEcssServiceImpl, "http://zecss.test.zte.com.cn");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssHeaderMap", saleOrderOrg);
        Map<String, String> header = new HashMap<>(INT_16);
        header.put(X_EMP_NO, "2132141");
        header.put(X_LANG_ID, X_LANG_ID_ZH);
        header.put(CONTENT_TYPE, APPLICATION_JSON);
        String url = ecssUrl + ECSS_BILL_ASYNC;
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(fsDocumentDTO), header)).thenReturn(result);
        Whitebox.invokeMethod(omsEcssServiceImpl, "invokeBillToEcss", fsDocumentDTO, "34252", saleOrderOrg);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void getEcssHeaderMap() throws Exception{
        Integer saleOrderOrg = 395;
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssHeaderMap", saleOrderOrg);

        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setSortSeq(4437);
        stepSysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssHeaderMap", saleOrderOrg);

        StepSysLookupValuesDTO stepSysLookupValuesDTO2 = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO2.setSortSeq(395);
        stepSysLookupValuesDTOList.add(stepSysLookupValuesDTO2);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);
        PowerMockito.when(zteSecurity.decrypt(Mockito.any())).thenReturn("111");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssHeaderMap", saleOrderOrg);
        Assert.assertTrue(Objects.nonNull(saleOrderOrg));
    }


    @Test
    public void dealEcssBill() throws Exception{

        FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
        EcssBillDTO dto = new EcssBillDTO();
        dto.setEnabledFlag("N");
        dto.setFailNum(0);
        ServiceData<?> res = new ServiceData<>();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        retCode.setMsg("2143124");
        res.setCode(retCode);
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        PowerMockito.when(omsEcssRepository.insertOrUpdateEcssBill(ecssBillDTO)).thenReturn(1);
        Whitebox.invokeMethod(omsEcssServiceImpl, "dealEcssBill", dto, fsDocumentDTO, res, "2134252");

        dto.setEnabledFlag("N");
        dto.setFailNum(3);
        dto.setReferenceNumber("C22080500002");
        RetCode retCode2 = new RetCode();
        retCode2.setCode("0001");
        retCode2.setMsg("2143124");
        res.setCode(retCode2);
        Whitebox.invokeMethod(omsEcssServiceImpl, "sendFailMail", Mockito.anyString());
        Whitebox.invokeMethod(omsEcssServiceImpl, "dealEcssBill", dto, fsDocumentDTO, res, "2134252");
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void sendFailMail() throws Exception{

        StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build()
                .setLookupType(LOOKUP_TYPE_1000022).setDescription(ECSS_BILL_FAILED);
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO)).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "sendFailMail", Mockito.anyString());

        StepSysLookupValuesDTO lookupValuesDTO = new StepSysLookupValuesDTO();
        lookupValuesDTO.setLookupMeaning("21314");
        stepSysLookupValuesDTOList.add(lookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO)).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "sendFailMail", Mockito.anyString());
        Assert.assertTrue(Objects.nonNull(stepSysLookupValuesDTO));
    }

    @Test
    public void callBackEcssToOms() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class);

        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        BusiAssertException.isEmpty(dto, MessageId.CALL_BACK_DATA_EMPTY);
        BusiAssertException.isEmpty(dto.getRefNo(), MessageId.BILL_NO_EMPTY);
        dto.setRefNo("2342356");
        dto.setStatus("RELEASED");
        dto.setCStatus("S");

        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);
        BusiAssertException.isEmpty(stepSysLookupValuesDTOList, MessageId.OMS_SALE_ECSS_LOOKUP_NOT_EXISTS);

        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupMeaning("2");
        stepSysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, null, 2);
        Whitebox.invokeMethod(omsEcssServiceImpl, "callBackEcssToOms", dto);

        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList1 = new ArrayList<>();
        StepSysLookupValuesDTO stepSysLookupValuesDTO1 = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO1.setLookupMeaning("3");
        stepSysLookupValuesDTOList1.add(stepSysLookupValuesDTO1);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(Mockito.any())).thenReturn(stepSysLookupValuesDTOList1);

        List<EcssBillDTO> materialInfoList = new ArrayList<>();
        PowerMockito.when(omsEcssRepository.getMaterialInfo(dto.getRefNo())).thenReturn(materialInfoList);
        BusiAssertException.isEmpty(materialInfoList, MessageId.OMS_SALE_BILL_NOT_EXISTS);

        List<EcssBillDTO> materialInfoList1 = new ArrayList<>();
        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        ecssBillDTO.setReferenceNumber("234234");
        ecssBillDTO.setStatus("AUDITING");
        materialInfoList1.add(ecssBillDTO);
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.any())).thenReturn(materialInfoList1);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, "通过", 3);
        Whitebox.invokeMethod(omsEcssServiceImpl, "callBackEcssToOms", dto);

        List<EcssBillDTO> materialInfoList2 = new ArrayList<>();
        EcssBillDTO ecssBillDTO2 = new EcssBillDTO();
        ecssBillDTO2.setReferenceNumber("234234");
        ecssBillDTO2.setStatus("AUDITING2");
        materialInfoList2.add(ecssBillDTO2);
        PowerMockito.when(omsEcssRepository.getMaterialInfo(Mockito.any())).thenReturn(materialInfoList2);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, "通过", 4);
        Whitebox.invokeMethod(omsEcssServiceImpl, "callBackEcssToOms", dto);
        Assert.assertTrue(Objects.nonNull(dto));

    }

    @Test
    public void updateEcssDTO() throws Exception{

        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        dto.setRefNo("2342356");
        dto.setStatus("RELEASED");
        dto.setCStatus("S");

        EcssBillDTO ecssBillDTO3 = new EcssBillDTO();
        ecssBillDTO3.setReferenceNumber(dto.getRefNo());
        ecssBillDTO3.setReturnParam(org.apache.commons.lang.StringUtils.substring(JSONObject.toJSONString(dto), NumConstant.INT_0, NumConstant.INT_4000));
        ecssBillDTO3.setBillStatus(ECSS_BILL_ADDED);
        ecssBillDTO3.setLastUpdatedBy(dto.getLastUpdatedBy());
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO3, dto, "通过", 3);
        PowerMockito.when(omsEcssRepository.updateEcssBill(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(omsEcssRepository.updateInforOmSalesBill(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(omsEcssRepository.failEcssOutputcollection(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(omsEcssRepository.insertEcssOutputcollection(Mockito.anyObject())).thenReturn(1);
        PowerMockito.when(omsEcssRepository.failEcssTextretruncollection(Mockito.anyObject())).thenReturn(1);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, "通过", 3);

        List<FsfReasonDTO> fsfReasonDTOS = new ArrayList<>();
        FsfReasonDTO fsfReasonDTO = new FsfReasonDTO();
        fsfReasonDTO.setFReason("234234");
        fsfReasonDTOS.add(fsfReasonDTO);
        dto.setFsFReasonDTOs(fsfReasonDTOS);
        PowerMockito.when(omsEcssRepository.insertEcssTextretruncollection(Mockito.anyList())).thenReturn(1);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, "通过", 3);

        List<String> list = new ArrayList<>();
        list.add("23424");
        dto.setFMessages(list);
        PowerMockito.when(omsEcssRepository.insertEcssTextretruncollection(Mockito.anyList())).thenReturn(1);
        Whitebox.invokeMethod(omsEcssServiceImpl, "updateEcssDTO", dto, "通过", 3);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getEcssBillDTO() throws Exception{

        EcssBillDTO ecssBillDTO = new EcssBillDTO();
        DocumentCallbackResultDTO dto = new DocumentCallbackResultDTO();
        dto.setStatus("RELEASED");
        dto.setCStatus("E");
        String gtsResult = RELEASED_ZH;
        String gtsResult2 = NOT_RELEASED_ZH;
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO, dto, gtsResult, 1);

        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO, dto, gtsResult2, 3);

        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO, dto, gtsResult, 4);

        DocumentCallbackResultDTO dto2 = new DocumentCallbackResultDTO();
        dto2.setStatus("RELEASED");
        dto2.setCStatus("S");
        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO, dto2, gtsResult, 3);

        Whitebox.invokeMethod(omsEcssServiceImpl, "getEcssBillDTO", ecssBillDTO, dto2, gtsResult2, 4);
        Assert.assertTrue(Objects.nonNull(dto));
    }

}
