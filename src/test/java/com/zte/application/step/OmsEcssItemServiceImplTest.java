package com.zte.application.step;

import com.zte.application.infor.EccnStockRemoteService;
import com.zte.application.step.impl.OmsEcssItemServiceImpl;
import com.zte.application.step.impl.OmsEcssServiceImpl;
import com.zte.domain.model.step.OmsEcssItemRepository;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.interfaces.infor.dto.EccnStockDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @since 2023年9月1日16:50
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class, HttpClientUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class OmsEcssItemServiceImplTest {

    @InjectMocks
    private OmsEcssItemServiceImpl omsEcssItemService;

    @Mock
    private OmsEcssItemRepository omsEcssItemRepository;

    @Mock
    private EccnStockRemoteService eccnStockRemoteService;

    @Mock
    private OmsEcssRepository omsEcssRepository;

    @Mock
    private OmsEcssServiceImpl omsEcssServiceImpl;

    @Mock
    private EmailUtil emailUtil;

    @Test
    public void validateOmsEcssItem() throws Exception {
        List<EcssItemDTO> ecssItemDTOList = new ArrayList<>();
        EcssItemDTO ecssItemDTO1 = EcssItemDTO.builder()
                .batchId("WMS_M_20230816161757000006").itemCode("040011001553").inGuid("C22072900006")
                .recordId(207L).itemName("存储服务器2插槽Riser卡B").orgCode("ZTE_Corp").codeType("MT001_TDWL_CGJ")
                .codeCategory("MC001_Material").enabledFlag("Y").itemVersion("1").indFunc(false).failNum(1).build();
        ecssItemDTOList.add(ecssItemDTO1);
        EcssItemDTO ecssItemDTO2 = EcssItemDTO.builder()
                .batchId("WMS_M_20230816161757000006").itemCode("042032900014").inGuid("C22072900006")
                .recordId(208L).itemName("4x6 92Ohm Whiisper 插头").orgCode("ZTE_Corp").codeType("MT001_TDWL_CGJ")
                .codeCategory("MC001_Material").enabledFlag("Y").itemVersion("1").indFunc(true).failNum(2).build();
        ecssItemDTOList.add(ecssItemDTO2);
        EcssItemDTO ecssItemDTO3 = EcssItemDTO.builder()
                .batchId("WMS_M_20230816161757000006").itemCode("056461000245").inGuid("C22072900006")
                .recordId(209L).itemName("十字槽小盘头组合螺钉").orgCode("ZTE_Corp").codeType("MT001_TDWL_CGJ")
                .codeCategory("MC001_Material").enabledFlag("Y").itemVersion("1").indFunc(null).failNum(3).build();
        ecssItemDTOList.add(ecssItemDTO3);
        PowerMockito.when(omsEcssItemRepository.getUnregisteredItemList(anyString())).thenReturn(null);
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(emailUtil.sendMail(anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(true);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(any())).thenReturn(stepSysLookupValuesDTOList);
        try {
            this.omsEcssItemService.registerStart();
        } catch (Exception e) {

        }
        List<EccnStockDTO> eccnStockDTOList = new ArrayList<>();
        EccnStockDTO eccnStockDTO = new EccnStockDTO();
        Map<String, Object> map = new HashMap<>();
        map.put("list", eccnStockDTOList);
        PowerMockito.when(omsEcssItemRepository.getUnregisteredItemList(anyString())).thenReturn(ecssItemDTOList);
        PowerMockito.when(eccnStockRemoteService.getEccnStockInfo(anyString())).thenReturn(map);
        this.omsEcssItemService.registerStart();

        eccnStockDTO.setItemNo("040011001553");
        eccnStockDTO.setVersion("1");
        eccnStockDTO.setPurchasePartFlg(STR_YES);
        eccnStockDTOList.add(eccnStockDTO);
        map.clear();
        map.put("list", eccnStockDTOList);
        PowerMockito.when(eccnStockRemoteService.getEccnStockInfo(anyString())).thenReturn(map);
        this.omsEcssItemService.registerStart();

        List<EccnItemDTO> eccnItemDTOList = new ArrayList<>();
        this.omsEcssItemService.setEccnParams(1, "", "", eccnItemDTOList);
        this.omsEcssItemService.eccnFail(eccnItemDTOList, "DADADA", ecssItemDTOList);

        List<EcssItemDTO> ecssItemList = new ArrayList<>();
        this.omsEcssItemService.sendMailCommon(ecssItemList);

        List<String> itemCodeListAll = new ArrayList<>();
        itemCodeListAll.add("111");
        itemCodeListAll.add("222");
        this.omsEcssItemService.updateFailItems(itemCodeListAll, "failReason", ecssItemDTOList);
        this.omsEcssItemService.sendMailCommon(ecssItemDTOList);

        StepSysLookupValuesDTO stepSysLookupValuesDTO = new StepSysLookupValuesDTO();
        stepSysLookupValuesDTO.setLookupMeaning("111");
        stepSysLookupValuesDTO.setLookupCode("222");
        stepSysLookupValuesDTOList.add(stepSysLookupValuesDTO);
        PowerMockito.when(omsEcssRepository.getEcssLookupInfo(any())).thenReturn(stepSysLookupValuesDTOList);
        this.omsEcssItemService.sendMailCommon(ecssItemDTOList);
        this.omsEcssItemService.buildEcssParam(ecssItemDTOList);

        List<FsBatchMaterialDTO> fsBatchMaterialDTOList = new ArrayList<>();
        FsBatchMaterialDTO fsBatchMaterialDTO = new FsBatchMaterialDTO();
        this.omsEcssItemService.buildFSBatchMaterialDTOList(fsBatchMaterialDTOList, fsBatchMaterialDTO);
        List<FsMaterialDTO> fsMaterialDTOsList = new ArrayList<>();
        FsCorresDTO fsCorresDTO1 = FsCorresDTO.builder().orgCode(ZTE_CORP).refNo("042032900014").build();
        FsGeneInfoDTO fsGeneInfoDTO1 = FsGeneInfoDTO.builder()
                .code("042032900014").indFunc(true).fsCorresDTO(fsCorresDTO1).build();
        FsMaterialDTO fsMaterialDTO1 = FsMaterialDTO.builder()
                .failNum(1).recordId(207L).inGuid("C22072900006").refNo("042032900014").fsGeneInfoDTO(fsGeneInfoDTO1).build();
        FsCorresDTO fsCorresDTO2 = FsCorresDTO.builder().orgCode(ZTE_CORP).refNo("042032900015").build();
        FsGeneInfoDTO fsGeneInfoDTO2 = FsGeneInfoDTO.builder()
                .code("042032900015").indFunc(true).fsCorresDTO(fsCorresDTO2).build();
        FsMaterialDTO fsMaterialDTO2 = FsMaterialDTO.builder()
                .failNum(3).recordId(208L).inGuid("C22072900007").refNo("042032900015").fsGeneInfoDTO(fsGeneInfoDTO2).build();
        fsMaterialDTOsList.add(fsMaterialDTO1);
        fsMaterialDTOsList.add(fsMaterialDTO2);
        fsBatchMaterialDTO.setFsMaterialDTOs(fsMaterialDTOsList);
        this.omsEcssItemService.buildFSBatchMaterialDTOList(fsBatchMaterialDTOList, fsBatchMaterialDTO);

        fsBatchMaterialDTO = new FsBatchMaterialDTO();
        fsBatchMaterialDTO.setEccnFail(false);
        this.omsEcssItemService.postEcssAsyncApi(fsBatchMaterialDTO);
        FsPageDTO fsPageDTO = FsPageDTO.builder()
                .currentPage(1)
                .pageSize(5000)
                .totalPage(1)
                .batchId("WMS_M_20230816161757000006")
                .fCount(2)
                .instanceId("111")
                .sCount(50)
                .submitDate("2023-9-4 09:46:11")
                .totalRecord(52).build();
        fsBatchMaterialDTO.setFsMaterialDTOs(fsMaterialDTOsList);
        fsBatchMaterialDTO.setFsPageDTO(fsPageDTO);
        fsBatchMaterialDTO.setSaleOrderOrg(395);
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        fsBatchMaterialDTO.setEccnFail(true);
        this.omsEcssItemService.postEcssAsyncApi(fsBatchMaterialDTO);
        fsBatchMaterialDTO.setEccnFail(false);
        this.omsEcssItemService.postEcssAsyncApi(fsBatchMaterialDTO);
        responseStr = "{\"code\": {\"code\": \"0004\",\"msgId\": \"failed\",\"msg\": \"参数效验不通过\"\t},\t\"bo\": [\"fsMaterialDTOs[0].refNo: 仅支持输入字符、数字，'-'，'_'， '/' 和 '.' 。\",\"fsPageDTO.pageSize: 输入值必须是大于0的整数。\",\"fsPageDTO.totalRecord: 输入值必须是大于0的整数。\",\"fsPageDTO.currentPage: 输入值必须是大于0的整数。\",\"fsPageDTO.totalPage: 输入值必须是大于0的整数。\",\"fsMaterialDTOs[0].fsGeneInfoDTO.code: 仅支持输入字符、数字，'-'，'_'， '/' 和 '.' 。\"]}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        this.omsEcssItemService.postEcssAsyncApi(fsBatchMaterialDTO);
        this.omsEcssItemService.ecssFail(fsBatchMaterialDTO, "123");
        this.omsEcssItemService.ecssSuccess(fsBatchMaterialDTO);
        this.omsEcssItemService.updateSuccessList(itemCodeListAll, STR_TRUE, "instanceId");

        FsAsyncBatchDTO fsAsyncBatchDTO = new FsAsyncBatchDTO();
        FsAsyncBatchProInfoListDTO proInfos = new FsAsyncBatchProInfoListDTO();
        List<FsAsyncBatchProInfoDTO> failedList = new ArrayList<>();
        FsAsyncBatchProInfoDTO fsAsyncBatchProInfoDTO1 = FsAsyncBatchProInfoDTO.builder()
                .refNo("040011001553").status("E").details(itemCodeListAll).build();
        FsAsyncBatchProInfoDTO fsAsyncBatchProInfoDTO2 = FsAsyncBatchProInfoDTO.builder()
                .refNo("042032900014ABB").status("E").details(itemCodeListAll).build();
        failedList.add(fsAsyncBatchProInfoDTO1);
        failedList.add(fsAsyncBatchProInfoDTO2);
        proInfos.setFailedList(failedList);
        fsAsyncBatchDTO.setFsPageDTO(fsPageDTO);
        fsAsyncBatchDTO.setStatus("E");
        fsAsyncBatchDTO.setProInfos(proInfos);
        PowerMockito.when(omsEcssItemRepository.getItemList(any())).thenReturn(ecssItemDTOList);
        this.omsEcssItemService.registerBack(fsAsyncBatchDTO);
        fsAsyncBatchDTO.setStatus("S");
        this.omsEcssItemService.registerBack(fsAsyncBatchDTO);
        fsPageDTO.setSCount(0);
        fsAsyncBatchDTO.setFsPageDTO(fsPageDTO);
        this.omsEcssItemService.registerBack(fsAsyncBatchDTO);

        this.omsEcssItemService.cutFailReason("111");
        StringBuilder failReason = new StringBuilder();
        for (int i = 0; i < 300; i++) {
            failReason.append("ABCDEFGHIJKLMNOPQ");
        }
        this.omsEcssItemService.cutFailReason(failReason.toString());
        Assert.assertTrue(Objects.nonNull(failReason.toString()));
    }
}
