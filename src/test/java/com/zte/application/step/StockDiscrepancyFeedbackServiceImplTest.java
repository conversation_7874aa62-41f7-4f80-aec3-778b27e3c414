package com.zte.application.step;

import com.zte.application.step.impl.StockDiscrepancyFeedbackServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.StockDiscrepancyFeedback;
import com.zte.domain.model.infor.StockDiscrepancyFeedbackRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.StockDiscrepancyFeedbackExportVO;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackAddDTO;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2025/5/23 14:35
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ExcelUtil.class,CommonUtils.class})
public class StockDiscrepancyFeedbackServiceImplTest {

    @InjectMocks
    private StockDiscrepancyFeedbackServiceImpl stockDiscrepancyFeedbackServiceImpl;
    @Mock
    private ZteAlibabaService zteAlibabaService;
    @Mock
    private StockDiscrepancyFeedbackRepository  stockDiscrepancyFeedbackRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class,CommonUtils.class);
    }


    @Test
    public void query() throws Exception {
        StockDiscrepancyFeedbackDTO queryDTO = new StockDiscrepancyFeedbackDTO();
        queryDTO.setPageIndex(1);
        queryDTO.setPageSize(10);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getTotalCount(Mockito.any())).thenReturn(10);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(stockDiscrepancyFeedbackServiceImpl.query(queryDTO));
        List<SysLookupValuesDTO> sysList = new ArrayList() {{
            add(new SysLookupValuesDTO() {{
                setAttribute2("Test");
            }});
            add(new SysLookupValuesDTO() {{
                setAttribute2("Test1");
            }});
        }};
        List<StockDiscrepancyFeedback> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedback() {{
                setSecondDiffCategory("Test");
            }});
            add(new StockDiscrepancyFeedback() {{
                setSecondDiffCategory("Test2");
            }});
        }};
        PowerMockito.when(stockDiscrepancyFeedbackRepository.selectByCondition(Mockito.any())).thenReturn(infoList);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysList);
        Assert.assertNotNull(stockDiscrepancyFeedbackServiceImpl.query(queryDTO));
    }

    @Test
    public void add() throws Exception {
        StockDiscrepancyFeedbackAddDTO addDTO = new StockDiscrepancyFeedbackAddDTO();
        addDTO.setMpn("Test");
        PowerMockito.when(stockDiscrepancyFeedbackRepository.mpnIsSaved(Mockito.any())).thenReturn(10);
        stockDiscrepancyFeedbackServiceImpl.add(addDTO);
        Assert.assertNotNull(addDTO);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.mpnIsSaved(Mockito.any())).thenReturn(0);
        stockDiscrepancyFeedbackServiceImpl.add(addDTO);
    }

    @Test
    public void getSysList() throws Exception {
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(stockDiscrepancyFeedbackServiceImpl.getSysList(new SysLookupValuesDTO()));
    }

    @Test
    public void deleteByKey() throws Exception {
        List<Long> keys = new ArrayList<>();
        stockDiscrepancyFeedbackServiceImpl.deleteByKey(keys);
        keys.add(1L);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(Mockito.any())).thenReturn(1L);
        try {
            stockDiscrepancyFeedbackServiceImpl.deleteByKey(keys);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_DELETE, e.getMessage());
        }
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(Mockito.any())).thenReturn(0L);
        stockDiscrepancyFeedbackServiceImpl.deleteByKey(keys);
    }

    @Test
    public void export() throws Exception {
        StockDiscrepancyFeedbackDTO dto = new StockDiscrepancyFeedbackDTO();
        dto.setEmpNo("00000000");
        Assert.assertNotNull(dto);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getTotalCount(Mockito.any())).thenReturn(10);
        List<StockDiscrepancyFeedbackExportVO> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SAVED);
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
            }});
        }};
        PowerMockito.when(stockDiscrepancyFeedbackRepository.exportByCondition(Mockito.any())).thenReturn(infoList);
        stockDiscrepancyFeedbackServiceImpl.export(dto);
    }

    @Test
    public void transferStatus() throws Exception {
        List<StockDiscrepancyFeedbackExportVO> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SAVED);
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
            }});
        }};
        Assert.assertNotNull(infoList);
        Whitebox.invokeMethod(stockDiscrepancyFeedbackServiceImpl, "transferStatus",infoList);
    }

    @Test
    public void transferTypeAndCategory() throws Exception {
        List<StockDiscrepancyFeedbackExportVO> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedbackExportVO() {{
                setSecondDiffCategory("Test");
                setInventoryType(0);
                setStatus(Constant.SAVED);
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
                setInventoryType(1);
                setSecondDiffCategory("Test2");
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
                setInventoryType(4);
                setSecondDiffCategory("Test2");
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
                setInventoryType(8);
                setSecondDiffCategory("Test2");
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
                setInventoryType(9);
                setSecondDiffCategory("Test2");
            }});
            add(new StockDiscrepancyFeedbackExportVO() {{
                setStatus(Constant.SUBMITED);
                setInventoryType(40);
                setSecondDiffCategory("Test2");
            }});
        }};
        List<SysLookupValuesDTO> sysList = new ArrayList() {{
            add(new SysLookupValuesDTO() {{
                setAttribute2("Test");
            }});
            add(new SysLookupValuesDTO() {{
                setAttribute2("Test1");
            }});
        }};
        Assert.assertNotNull(infoList);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysList);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.exportByCondition(Mockito.any())).thenReturn(infoList);
        Whitebox.invokeMethod(stockDiscrepancyFeedbackServiceImpl, "transferTypeAndCategory",infoList);
    }

    @Test
    public void submit() throws Exception {
        stockDiscrepancyFeedbackServiceImpl.submit(new StockDiscrepancyFeedbackDTO());

        StockDiscrepancyFeedbackDTO feedbackDTO = new StockDiscrepancyFeedbackDTO();
        feedbackDTO.setSerialkeyList(new ArrayList() {{
            add(10100L);
        }});
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(Mockito.any())).thenReturn(1L);
        try {
            stockDiscrepancyFeedbackServiceImpl.submit(feedbackDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_SUBMIT, e.getMessage());
        }

        PowerMockito.when(stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(Mockito.any())).thenReturn(0L);
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getInfoByKeys(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            stockDiscrepancyFeedbackServiceImpl.submit(feedbackDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DETAILS_IS_NULL, e.getMessage());
        }

        List<StockDiscrepancyFeedback> infoList = new ArrayList() {{
            add(new StockDiscrepancyFeedback() {{
                setMessageId("123456");
                setSerialkey(1L);
                setMpn("123");
            }});
        }};
        PowerMockito.when(stockDiscrepancyFeedbackRepository.getInfoByKeys(Mockito.any())).thenReturn(infoList);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn(MessageId.HAVE_SUBMITTED);
        try {
            stockDiscrepancyFeedbackServiceImpl.submit(feedbackDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HAVE_SUBMITTED, e.getMessage());
        }

        infoList.add(new StockDiscrepancyFeedback() {{
            setMessageId("");
            setSerialkey(2L);
        }});
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(new SysLookupValuesDTO());
        try {
            stockDiscrepancyFeedbackServiceImpl.submit(feedbackDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS, e.getMessage());
        }
    }
}
