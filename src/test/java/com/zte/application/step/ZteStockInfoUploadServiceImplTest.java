package com.zte.application.step;

/* Started by AICoder, pid:w2efd36320p81b714b710b64b0fb6777fd68f33b */
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.step.impl.ZteStockInfoUploadServiceImpl;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.ZteStockMoveInfoUploadRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.interfaces.step.dto.ZteStockInfoDTO;
import com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class})
public class ZteStockInfoUploadServiceImplTest {

    @InjectMocks
    private ZteStockInfoUploadServiceImpl zteStockInfoUploadService;
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    @Mock
    private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Mock
    private StepTransferRepository stepTransferRepository;
    @Mock
    private EmailUtil emailUtil;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class);
    }
    @Test
    public void stockInfoUpload() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        List<String> itemNoList = new ArrayList<>();
        for (int i=0; i<501; i++) {
            itemNoList.add(i + "1");
        }
        dto.setItemNoList(itemNoList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockInfoUpload", dto);

        itemNoList.clear();
        List<ZteStockInfoDTO> zteStockInfoDTOList = new ArrayList<>();
        PowerMockito.when(zteStockInfoUploadRepository.getStockInfo(Mockito.any())).thenReturn(zteStockInfoDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockInfoUpload", dto);

        itemNoList.add("23423");
        PowerMockito.when(zteStockInfoUploadRepository.getStockInfo(Mockito.any())).thenReturn(zteStockInfoDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockInfoUpload", dto);

        ZteStockInfoDTO zteStockInfoDTO = new ZteStockInfoDTO();
        zteStockInfoDTO.setDataTransferBatchNo("234252");
        zteStockInfoDTO.setBuCode("2342");
        zteStockInfoDTO.setStockStatus("23423");
        zteStockInfoDTO.setOdmPlantCode("ZTE1");
        zteStockInfoDTOList.add(zteStockInfoDTO);
        PowerMockito.when(zteStockInfoUploadRepository.getStockInfo(Mockito.any())).thenReturn(zteStockInfoDTOList);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        PowerMockito.when(zteStockInfoUploadRepository.insertStockUploadSnapshot(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteStockInfoUploadRepository.insertStockUploadLog(Mockito.any())).thenReturn(1);
        List<CustomerDataLogDTO> dataList = Tools.newArrayList();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("242");
        dataList.add(customerDataLogDTO);
        String empNo = "12345";
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2B", dataList, empNo);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockInfoUpload", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getDataTransferBatchNo() throws Exception {

        String batchNoType = "ZTE1";
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(batchNoType, INT_6)).thenReturn("ZTE120240621000001");
        Whitebox.invokeMethod(zteStockInfoUploadService, "getDataTransferBatchNo", batchNoType);

        Assert.assertNotNull(batchNoType);
    }

    @Test
    public void stockMoveInfoUpload() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        List<String> itemNoList = new ArrayList<>();
        for (int i=0; i<501; i++) {
            itemNoList.add(i + "1");
        }
        dto.setItemNoList(itemNoList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockMoveInfoUpload", dto);

        itemNoList.clear();
        List<ZteStockMoveInfoUploadDTO> zteStockMoveInfoUploadDTOList = new ArrayList<>();
        PowerMockito.when(zteStockMoveInfoUploadRepository.getStockMoveInfo(Mockito.any())).thenReturn(zteStockMoveInfoUploadDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockMoveInfoUpload", dto);

        itemNoList.add("23423");
        PowerMockito.when(zteStockMoveInfoUploadRepository.getStockMoveInfo(Mockito.any())).thenReturn(zteStockMoveInfoUploadDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockMoveInfoUpload", dto);

        for (int i=0; i<501; i++) {
        ZteStockMoveInfoUploadDTO zteStockMoveInfoUploadDTO = new ZteStockMoveInfoUploadDTO();
        zteStockMoveInfoUploadDTO.setDataTransferBatchNo("234252"+i);
        zteStockMoveInfoUploadDTO.setBdPlantCode("2342");
        zteStockMoveInfoUploadDTO.setMaterialCode("23423");
        zteStockMoveInfoUploadDTO.setOdmPlantCode("ZTE1");
        zteStockMoveInfoUploadDTOList.add(zteStockMoveInfoUploadDTO);
        }
        PowerMockito.when(zteStockMoveInfoUploadRepository.getStockMoveInfo(Mockito.any())).thenReturn(zteStockMoveInfoUploadDTOList);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        PowerMockito.when(zteStockInfoUploadRepository.insertStockUploadLog(Mockito.any())).thenReturn(1);
        List<CustomerDataLogDTO> dataList = Tools.newArrayList();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("242");
        dataList.add(customerDataLogDTO);
        String empNo = "12345";
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2B", dataList, empNo);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockMoveInfoUpload", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void updateStockUploadLog() throws Exception {

        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setId("23423523");
        PowerMockito.when(zteStockInfoUploadRepository.updateStockUploadLog(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteStockInfoUploadService, "updateStockUploadLog", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void stockUploadLogMonitor() throws Exception {

        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        PowerMockito.when(zteStockInfoUploadRepository.stockUploadLogMonitor()).thenReturn(zteStockInfoUploadLogDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockUploadLogMonitor");

        ZteStockInfoUploadLogDTO dto = new ZteStockInfoUploadLogDTO();
        dto.setMessageType("1");
        dto.setQty(1);
        zteStockInfoUploadLogDTOList.add(dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockUploadLogMonitor");

        ZteStockInfoUploadLogDTO dto1 = new ZteStockInfoUploadLogDTO();
        dto1.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY);
        dto1.setQty(1);
        zteStockInfoUploadLogDTOList.add(dto1);
        ZteStockInfoUploadLogDTO dto2 = new ZteStockInfoUploadLogDTO();
        dto2.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
        dto2.setQty(1);
        zteStockInfoUploadLogDTOList.add(dto2);
        ZteStockInfoUploadLogDTO dto3 = new ZteStockInfoUploadLogDTO();
        dto3.setMessageType(ZTE_IMES_BYTEDANCE_GOODS_RECEIPT_OF_PURCHASE_ORDER);
        dto3.setQty(1);
        zteStockInfoUploadLogDTOList.add(dto3);
        List<String> users = new ArrayList<>();
        users.add("234252");
        PowerMockito.when(stepTransferRepository.getEmailUser(Mockito.any())).thenReturn(users);
        Whitebox.invokeMethod(zteStockInfoUploadService, "stockUploadLogMonitor");

        Assert.assertNotNull(dto);
    }

    @Test
    public void purchaseOrderUpload() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        List<String> billNoList = new ArrayList<>();
        for (int i=0; i<501; i++) {
            billNoList.add(i + "1");
        }
        dto.setBillNoList(billNoList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        billNoList.clear();
        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        PowerMockito.when(zteStockMoveInfoUploadRepository.getPurchaseOrder(Mockito.any())).thenReturn(ztePurchaseOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        billNoList.add("11131");
        PowerMockito.when(zteStockMoveInfoUploadRepository.getPurchaseOrder(Mockito.any())).thenReturn(ztePurchaseOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);

        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        PowerMockito.when(zteStockMoveInfoUploadRepository.getPurchaseOrder(Mockito.any())).thenReturn(ztePurchaseOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO2.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        PowerMockito.when(zteStockMoveInfoUploadRepository.getPurchaseOrder(Mockito.any())).thenReturn(ztePurchaseOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        ztePurchaseOrderDTOList.clear();
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        PowerMockito.when(zteStockMoveInfoUploadRepository.getPurchaseOrder(Mockito.any())).thenReturn(ztePurchaseOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "purchaseOrderUpload", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getIsrmReceiveOrder() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO2.setMovementCategory("H");
        /* Started by AICoder, pid:ob23836de3qf8e5148bd0982800a6b06a5a236a2 */
        ztePurchaseOrderDTO2.setPurchaseOrderNo("1");
        ztePurchaseOrderDTO2.setOriginalPurchaseOrderNo("11");
        /* Ended by AICoder, pid:ob23836de3qf8e5148bd0982800a6b06a5a236a2 */
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO.setMaterialDocLine("1");
        ztePurchaseOrderDTO.setItemNo("1111");
        ztePurchaseOrderDTO.setMovementCategory("S");
        /* Started by AICoder, pid:29405m8c80z2f1414d200a5a7049330d4b44012d */
        ztePurchaseOrderDTO.setOriginalPOLineNo("");
        ztePurchaseOrderDTO.setOriginalPurchaseOrderNo("");
        ztePurchaseOrderDTO.setPurchaseNetPrice("111");
        ztePurchaseOrderDTO.setCurrencyCode("1");
        /* Ended by AICoder, pid:29405m8c80z2f1414d200a5a7049330d4b44012d */
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);

        String responseStr = null;
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);

        responseStr = "{\"code\": {\"code\": \"0005\",\"msgId\": \"success\",\"msg\": \"请求不成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"total\":2,\"list\":[{\"deliNo\":\"F2207SC00000005\",\"purOrgId\":27,\"receiveOrgId\":308,\"purOrigOrgId\":395,\"receiveOrigOrgId\":395,\"supplierName\":\"深南电路股份有限公司\",\"supplierNo\":\"40000100\",\"deliRowNo\":1,\"dn\":null,\"invoiceNum\":null,\"invoiceDate\":null,\"itemNo\":\"040010200616\",\"itemName\":\"上行低噪声放大器板\",\"poNo\":\"2202POSC00000038\",\"poId\":1199916303,\"poType\":\"SC\",\"custSupType\":null,\"billDate\":\"2022-02-09 13:55:10\",\"publishDate\":\"2022-02-22 14:01:52\",\"effectDate\":\"2022-02-28 19:19:22\",\"latest\":1,\"poRowNo\":2,\"poDetailId\":2042751339,\"orderQty\":38.777,\"saleCtNo\":null,\"custSaleCtNo\":null}],\"pageNum\":1,\"pageSize\":500,\"size\":2,\"startRow\":1,\"endRow\":2,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"other\":{\"opResult\":\"Success\",\"event_id\":\"com.zte.iscp.interfaces.gateway.GatewayWebController@basicRequest\",\"costTime\":\"222ms\",\"tag\":\"GatewayWebController@basicRequest\",\"serviceName\":\"zte-scm-iscp-bff-service\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<IsrmReceiveOrderDTO> isrmReceiveOrderDTOList = new ArrayList<>();
        IsrmReceiveOrderDTO isrmReceiveOrderDTO = new IsrmReceiveOrderDTO();
        isrmReceiveOrderDTO.setDeliNo("F2207SC00000005");
        isrmReceiveOrderDTO.setDeliRowNo(1);
        isrmReceiveOrderDTO.setItemNo("1111");
        isrmReceiveOrderDTO.setPoRowNo(1);
        isrmReceiveOrderDTOList.add(isrmReceiveOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReceiveOrder", ztePurchaseOrderDTOList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getIsrmReturnOrder() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO2.setMaterialDocLine("1");
        ztePurchaseOrderDTO2.setItemNo("1111");
        ztePurchaseOrderDTO2.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);

        String responseStr = null;
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);

        responseStr = "{\"code\": {\"code\": \"0005\",\"msgId\": \"success\",\"msg\": \"请求不成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"total\":2,\"list\":[{\"deliNo\":\"F2207SC00000005\",\"purOrgId\":27,\"receiveOrgId\":308,\"purOrigOrgId\":395,\"receiveOrigOrgId\":395,\"supplierName\":\"深南电路股份有限公司\",\"supplierNo\":\"40000100\",\"deliRowNo\":1,\"dn\":null,\"invoiceNum\":null,\"invoiceDate\":null,\"itemNo\":\"040010200616\",\"itemName\":\"上行低噪声放大器板\",\"poNo\":\"2202POSC00000038\",\"poId\":1199916303,\"poType\":\"SC\",\"custSupType\":null,\"billDate\":\"2022-02-09 13:55:10\",\"publishDate\":\"2022-02-22 14:01:52\",\"effectDate\":\"2022-02-28 19:19:22\",\"latest\":1,\"poRowNo\":2,\"poDetailId\":2042751339,\"orderQty\":38.777,\"saleCtNo\":null,\"custSaleCtNo\":null}],\"pageNum\":1,\"pageSize\":500,\"size\":2,\"startRow\":1,\"endRow\":2,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"other\":{\"opResult\":\"Success\",\"event_id\":\"com.zte.iscp.interfaces.gateway.GatewayWebController@basicRequest\",\"costTime\":\"222ms\",\"tag\":\"GatewayWebController@basicRequest\",\"serviceName\":\"zte-scm-iscp-bff-service\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<IsrmReturnOrderDTO> isrmReturnOrderDTOList = new ArrayList<>();
        IsrmReturnOrderDTO isrmReturnOrderDTO = new IsrmReturnOrderDTO();
        isrmReturnOrderDTO.setBillNo("STH24052900039");
        isrmReturnOrderDTO.setBillRowNo("1");
        isrmReturnOrderDTO.setItemNo("1111");
        isrmReturnOrderDTO.setPoNo("1");
        isrmReturnOrderDTO.setPoRowNo("11");
        isrmReturnOrderDTO.setRealOutNumber(new BigDecimal(1));
        isrmReturnOrderDTOList.add(isrmReturnOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getIsrmReturnOrder", ztePurchaseOrderDTOList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getReceivePurchaseOrderData() throws Exception {

        List<IsrmReceiveOrderDTO> isrmReceiveOrderDTOList = new ArrayList<>();
        IsrmReceiveOrderDTO isrmReceiveOrderDTO = new IsrmReceiveOrderDTO();
        isrmReceiveOrderDTO.setDeliNo("F2207SC00000005");
        isrmReceiveOrderDTO.setDeliRowNo(1);
        isrmReceiveOrderDTO.setItemNo("1111");
        isrmReceiveOrderDTO.setPoRowNo(1);
        isrmReceiveOrderDTO.setOrigPrice("1");
        isrmReceiveOrderDTOList.add(isrmReceiveOrderDTO);
        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);

        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO2.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO.setMaterialDocLine("1");
        ztePurchaseOrderDTO.setItemNo("2222");
        ztePurchaseOrderDTO.setHref11("100");
        ztePurchaseOrderDTO.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO3 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO3.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO3.setMaterialDocLine("1");
        ztePurchaseOrderDTO3.setItemNo("1111");
        ztePurchaseOrderDTO3.setHref11("100");
        ztePurchaseOrderDTO3.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO3);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO4 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO4.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO4.setMaterialDocLine("1");
        ztePurchaseOrderDTO4.setItemNo("1111");
        ztePurchaseOrderDTO4.setHref11("461");
        ztePurchaseOrderDTO4.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO4);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);


        isrmReceiveOrderDTOList.clear();
        isrmReceiveOrderDTO.setPublishDate(new Date());
        isrmReceiveOrderDTOList.add(isrmReceiveOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReceivePurchaseOrderData", ztePurchaseOrderDTOList, isrmReceiveOrderDTOList);

        Assert.assertNotNull(isrmReceiveOrderDTOList);
    }

    @Test
    public void getReturnPurchaseOrderData() throws Exception {

        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        List<IsrmReturnOrderDTO> isrmReturnOrderDTOList = new ArrayList<>();
        IsrmReturnOrderDTO isrmReturnOrderDTO = new IsrmReturnOrderDTO();
        isrmReturnOrderDTO.setBillNo("STH24052900039");
        isrmReturnOrderDTO.setBillRowNo("1");
        isrmReturnOrderDTO.setItemNo("1111");
        isrmReturnOrderDTO.setPoNo("1");
        isrmReturnOrderDTO.setPoRowNo("11");
        isrmReturnOrderDTO.setRealOutNumber(new BigDecimal(1));
        isrmReturnOrderDTOList.add(isrmReturnOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);

        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("F2207SC00000005");
        ztePurchaseOrderDTO2.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO.setMaterialDocLine("1");
        ztePurchaseOrderDTO.setItemNo("2222");
        ztePurchaseOrderDTO.setHref11("351");
        ztePurchaseOrderDTO.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO3 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO3.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO3.setMaterialDocLine("1");
        ztePurchaseOrderDTO3.setItemNo("1111");
        ztePurchaseOrderDTO3.setHref11("351");
        ztePurchaseOrderDTO3.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO3);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO4 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO4.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO4.setMaterialDocLine("1");
        ztePurchaseOrderDTO4.setItemNo("1111");
        ztePurchaseOrderDTO4.setHref11("471");
        ztePurchaseOrderDTO4.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO4);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);


        isrmReturnOrderDTOList.clear();
        isrmReturnOrderDTO.setPublishDate(new Date());
        isrmReturnOrderDTOList.add(isrmReturnOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getReturnPurchaseOrderData", ztePurchaseOrderDTOList, isrmReturnOrderDTOList);

        Assert.assertNotNull(isrmReturnOrderDTOList);
    }

    @Test
    public void getPurchaseOrderData() throws Exception {

        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        dto.setEmpNo("2222");
        Whitebox.invokeMethod(zteStockInfoUploadService, "updatePoSoEndTime", ztePurchaseOrderDTOList);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);

        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setEnableFlag("N");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setMaterialDocNo("STH24052900039");
        ztePurchaseOrderDTO2.setMaterialDocLine("1");
        ztePurchaseOrderDTO2.setItemNo("2222");
        ztePurchaseOrderDTO2.setMovementCategory("H");
        ztePurchaseOrderDTO2.setEnableFlag("Y");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO2);

        List<ZtePurchaseOrderUploadHeadDTO> ztePurchaseOrderUploadHeadDTOList = new ArrayList<>();
        ZtePurchaseOrderUploadHeadDTO ztePurchaseOrderUploadHeadDTO = new ZtePurchaseOrderUploadHeadDTO();
        ztePurchaseOrderUploadHeadDTO.setMaterialDocNo("1111");
        ztePurchaseOrderUploadHeadDTOList.add(ztePurchaseOrderUploadHeadDTO);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId("ZTE1", 6)).thenReturn("ZTE120240621000001");
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushPurchaseOrderData", ztePurchaseOrderUploadHeadDTOList, dto);
        Whitebox.invokeMethod(zteStockInfoUploadService, "getPurchaseOrderData", ztePurchaseOrderDTOList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void updatePoSoEndTime() throws Exception {

        List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = new ArrayList<>();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO.setEnableFlag("Y");
        ztePurchaseOrderDTO.setSerialkey("111");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO);
        Whitebox.invokeMethod(zteStockInfoUploadService, "updatePoSoEndTime", ztePurchaseOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO2 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO2.setEnableFlag("Y");
        ztePurchaseOrderDTO2.setSerialkey("111");
        ztePurchaseOrderDTO2.setMovementCategory("A");

        Whitebox.invokeMethod(zteStockInfoUploadService, "updatePoSoEndTime", ztePurchaseOrderDTOList);

        ztePurchaseOrderDTOList.clear();
        ZtePurchaseOrderDTO ztePurchaseOrderDTO3 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO3.setEnableFlag("N");
        ztePurchaseOrderDTO3.setSerialkey("111");
        ztePurchaseOrderDTO3.setMaterialDocNo("11111");
        ztePurchaseOrderDTO3.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO3);
        ZtePurchaseOrderDTO ztePurchaseOrderDTO5 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO5.setEnableFlag("Y");
        ztePurchaseOrderDTO5.setSerialkey("111");
        ztePurchaseOrderDTO5.setMaterialDocNo("22222");
        ztePurchaseOrderDTO5.setMovementCategory("H");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO5);
        ZtePurchaseOrderDTO ztePurchaseOrderDTO4 = new ZtePurchaseOrderDTO();
        ztePurchaseOrderDTO4.setEnableFlag("N");
        ztePurchaseOrderDTO4.setSerialkey("111");
        ztePurchaseOrderDTO4.setMovementCategory("S");
        ztePurchaseOrderDTOList.add(ztePurchaseOrderDTO4);
        PowerMockito.when(zteStockMoveInfoUploadRepository.updateEdiPoSBySerialKey(Mockito.any())).thenReturn(1);
        PowerMockito.when(zteStockMoveInfoUploadRepository.updateEdiSoSBySerialKey(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zteStockInfoUploadService, "updatePoSoEndTime", ztePurchaseOrderDTOList);

        Assert.assertNotNull(ztePurchaseOrderDTOList);
    }

    @Test
    public void pushPurchaseOrderData() throws Exception {

        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        dto.setEmpNo("2222");
        List<ZtePurchaseOrderUploadHeadDTO> ztePurchaseOrderUploadHeadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushPurchaseOrderData", ztePurchaseOrderUploadHeadDTOList, dto);

        ZtePurchaseOrderUploadHeadDTO ztePurchaseOrderUploadHeadDTO = new ZtePurchaseOrderUploadHeadDTO();
        ztePurchaseOrderUploadHeadDTO.setDataTransferBatchNo("ZTE120240716200001");
        PowerMockito.when(zteStockInfoUploadRepository.insertStockUploadLog(Mockito.any())).thenReturn(1);
        List<CustomerDataLogDTO> dataList = Tools.newArrayList();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("242");
        dataList.add(customerDataLogDTO);
        String empNo = "12345";
        Whitebox.invokeMethod(imesCenterfactoryRemoteService, "pushDataToB2B", dataList, empNo);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushPurchaseOrderData", ztePurchaseOrderUploadHeadDTOList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void pushTransferSkuToIsrm() throws Exception {
        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        dto.setEmpNo("2222");
        String xEmpNo = "123456";
        List<IscpEdiLog> iscpEdiLogList = new ArrayList<>();
        PowerMockito.when(zteStockMoveInfoUploadRepository.getTransferNo()).thenReturn(iscpEdiLogList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransferSkuToIsrm", xEmpNo);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setSourceTable("1111");
        iscpEdiLog.setExternkey("111");
        iscpEdiLogList.add(iscpEdiLog);
        PowerMockito.when(zteStockMoveInfoUploadRepository.getTransferNo()).thenReturn(iscpEdiLogList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransferSkuToIsrm", xEmpNo);

        iscpEdiLogList.clear();
        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setSourceTable("EDI_ZMD_PO_S");
        iscpEdiLog2.setExternkey("222");
        iscpEdiLogList.add(iscpEdiLog2);
        PowerMockito.when(zteStockMoveInfoUploadRepository.getTransferNo()).thenReturn(iscpEdiLogList);
        List<InforIsrmTransferDTO> transferInList = new ArrayList<>();
        InforIsrmTransferDTO inforIsrmTransferDTO = new InforIsrmTransferDTO();
        inforIsrmTransferDTO.setStockTransferNo("111");
        transferInList.add(inforIsrmTransferDTO);
        PowerMockito.when(zteStockMoveInfoUploadRepository.getEdiPoSList(Mockito.any())).thenReturn(transferInList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferInList, xEmpNo, "EDI_ZMD_PO_S");
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransferSkuToIsrm", xEmpNo);

        iscpEdiLogList.clear();
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setSourceTable("EDI_ZMD_SO_S");
        iscpEdiLog3.setExternkey("333");
        iscpEdiLogList.add(iscpEdiLog3);
        PowerMockito.when(zteStockMoveInfoUploadRepository.getTransferNo()).thenReturn(iscpEdiLogList);
        List<InforIsrmTransferDTO> transferOutList = new ArrayList<>();
        transferOutList.add(inforIsrmTransferDTO);
        PowerMockito.when(zteStockMoveInfoUploadRepository.getEdiPoSList(Mockito.any())).thenReturn(transferOutList);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferOutList, xEmpNo, "EDI_ZMD_SO_S");
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransferSkuToIsrm", xEmpNo);

        Assert.assertNotNull(dto);
    }

    @Test
    public void pushTransfer() throws Exception {
        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        dto.setEmpNo("2222");
        String xEmpNo = "123456";
        List<InforIsrmTransferDTO> transferList = new ArrayList<>();
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferList, xEmpNo, "EDI_ZMD_SO_S");

        InforIsrmTransferDTO inforIsrmTransferDTO = new InforIsrmTransferDTO();
        inforIsrmTransferDTO.setStockTransferNo("111");
        transferList.add(inforIsrmTransferDTO);

        String responseStr = null;
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferList, xEmpNo, "EDI_ZMD_SO_S");

        responseStr = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.BusinessError\",\"msg\":\"业务异常\"},\"bo\":\"An error occurs when receiving virtual orders. error: 客户专用代码033040400092的字节物料编码/物料名称未维护，请检查；\",\"other\":{\"opResult\":\"Success\",\"event_id\":null,\"costTime\":\"250ms\",\"tag\":null,\"serviceName\":\"zte-scm-iscp-delicoordination-service\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<String> transferNoList = new ArrayList<>();
        transferNoList.add("111");
        PowerMockito.when(zteStockMoveInfoUploadRepository.updateIscpEdiLog(transferNoList, "EDI_ZMD_SO_S", -1, "111")).thenReturn(1);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferList, xEmpNo, "EDI_ZMD_SO_S");

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"total\":2,\"list\":[{\"deliNo\":\"F2207SC00000005\",\"purOrgId\":27,\"receiveOrgId\":308,\"purOrigOrgId\":395,\"receiveOrigOrgId\":395,\"supplierName\":\"深南电路股份有限公司\",\"supplierNo\":\"40000100\",\"deliRowNo\":1,\"dn\":null,\"invoiceNum\":null,\"invoiceDate\":null,\"itemNo\":\"040010200616\",\"itemName\":\"上行低噪声放大器板\",\"poNo\":\"2202POSC00000038\",\"poId\":1199916303,\"poType\":\"SC\",\"custSupType\":null,\"billDate\":\"2022-02-09 13:55:10\",\"publishDate\":\"2022-02-22 14:01:52\",\"effectDate\":\"2022-02-28 19:19:22\",\"latest\":1,\"poRowNo\":2,\"poDetailId\":2042751339,\"orderQty\":38.777,\"saleCtNo\":null,\"custSaleCtNo\":null}],\"pageNum\":1,\"pageSize\":500,\"size\":2,\"startRow\":1,\"endRow\":2,\"pages\":1,\"prePage\":0,\"nextPage\":0,\"isFirstPage\":true,\"isLastPage\":true,\"hasPreviousPage\":false,\"hasNextPage\":false,\"navigatePages\":8,\"navigatepageNums\":[1],\"navigateFirstPage\":1,\"navigateLastPage\":1},\"other\":{\"opResult\":\"Success\",\"event_id\":\"com.zte.iscp.interfaces.gateway.GatewayWebController@basicRequest\",\"costTime\":\"222ms\",\"tag\":\"GatewayWebController@basicRequest\",\"serviceName\":\"zte-scm-iscp-bff-service\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        PowerMockito.when(zteStockMoveInfoUploadRepository.updateIscpEdiLog(transferNoList, "EDI_ZMD_SO_S", 0, null)).thenReturn(1);
        Whitebox.invokeMethod(zteStockInfoUploadService, "pushTransfer", transferList, xEmpNo, "EDI_ZMD_SO_S");

        Assert.assertNotNull(dto);
    }

}
/* Ended by AICoder, pid:w2efd36320p81b714b710b64b0fb6777fd68f33b */