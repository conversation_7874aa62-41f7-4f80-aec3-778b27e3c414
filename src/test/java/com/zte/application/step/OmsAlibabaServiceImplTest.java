package com.zte.application.step;

import com.zte.application.step.impl.OmsAlibabaServiceImpl;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.step.OmsAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class})
public class OmsAlibabaServiceImplTest {

    @InjectMocks
    private OmsAlibabaServiceImpl omsAlibabaService;
    @Mock
    private OmsAlibabaRepository omsAlibabaRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class);
    }

    @Test
    public void pushRequisitionBillToAlibaba() throws Exception {
        OmsAlibabaBillDTO dto = new OmsAlibabaBillDTO();
        dto.setEmpNo("111");
        List<OmsAlibabaBillDTO> omsAlibabaBillDTOList = new ArrayList<>();
        when(omsAlibabaRepository.getRequisitionBills(dto)).thenReturn(omsAlibabaBillDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushRequisitionBillToAlibaba", dto);
        OmsAlibabaBillDTO d1 = new OmsAlibabaBillDTO();
        omsAlibabaBillDTOList.add(d1);
        when(omsAlibabaRepository.getRequisitionBills(dto)).thenReturn(omsAlibabaBillDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushRequisitionBillToAlibaba", dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void pushReturnBillToAlibaba() throws Exception {
        OmsAlibabaBillDTO dto = new OmsAlibabaBillDTO();
        dto.setEmpNo("111");
        List<OmsAlibabaBillDTO> omsAlibabaBillDTOList = new ArrayList<>();
        when(omsAlibabaRepository.getReturnBills(dto)).thenReturn(omsAlibabaBillDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushReturnBillToAlibaba", dto);
        OmsAlibabaBillDTO d1 = new OmsAlibabaBillDTO();
        omsAlibabaBillDTOList.add(d1);
        when(omsAlibabaRepository.getReturnBills(dto)).thenReturn(omsAlibabaBillDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushReturnBillToAlibaba", dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void pushBillsToAlibabaTest() throws Exception {
        OmsAlibabaBillDTO dto = new OmsAlibabaBillDTO();
        dto.setEmpNo("111");
        List<OmsAlibabaBillDTO> omsAlibabaBillDTOList = new ArrayList<>();
        OmsAlibabaBillDTO d1 = new OmsAlibabaBillDTO();
        d1.setEntityName("111");
        d1.setBillNo("123");
        omsAlibabaBillDTOList.add(d1);
        OmsAlibabaBillDTO d2 = new OmsAlibabaBillDTO();
        d2.setEntityName("999");
        d2.setBillNo("999");
        omsAlibabaBillDTOList.add(d2);
        List<BulkTaskDetailDTO> bulkTaskDetailDTOList = new ArrayList<>();
        when(imesCenterfactoryRemoteService.getCategory(Mockito.any(), Mockito.anyString())).thenReturn(bulkTaskDetailDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("123");
        when(inventoryholdRecordRepository.getLookupValue(Mockito.anyString())).thenReturn(sysLookupValuesDTO);
        BulkTaskDetailDTO bulkTaskDetailDTO1 = new BulkTaskDetailDTO();
        bulkTaskDetailDTO1.setFixBomId("123");
        bulkTaskDetailDTO1.setTaskNo("111");
        bulkTaskDetailDTOList.add(bulkTaskDetailDTO1);
        BulkTaskDetailDTO bulkTaskDetailDTO2 = new BulkTaskDetailDTO();
        bulkTaskDetailDTO2.setFixBomId("123");
        bulkTaskDetailDTO2.setTaskNo("321");
        bulkTaskDetailDTOList.add(bulkTaskDetailDTO2);
        when(imesCenterfactoryRemoteService.getCategory(Mockito.any(), Mockito.anyString())).thenReturn(bulkTaskDetailDTOList);
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": [],\n" +
                "    \"other\": {\n" +
                "        \"msg\": \"操作成功\",\n" +
                "        \"opResult\": \"Success\",\n" +
                "        \"event_id\": \"com.zte.interfaces.cbom.FixBomCommonController@queryFixBomDetailByFixBomId\",\n" +
                "        \"code\": \"0000\",\n" +
                "        \"costTime\": \"12ms\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"clickTime\": \"Thu May 22 10:12:11 CST 2025\",\n" +
                "        \"tag\": \"通过fixBomId查询明细\",\n" +
                "        \"hostAddress\": \"*************\",\n" +
                "        \"serviceName\": \"zte-mes-manufactureshare-centerfactory\",\n" +
                "        \"userId\": \"1\"\n" +
                "    },\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}");
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"Success\"\n" +
                "    },\n" +
                "    \"bo\": null,\n" +
                "    \"other\": null,\n" +
                "    \"responseRule\": \"msa\",\n" +
                "    \"requestId\": \"a7bd0c65b8d34156\"\n" +
                "}");
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": [\n" +
                "        {\n" +
                "            \"zteCode\": \"123\",\n" +
                "            \"customerCode\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"other\": {\n" +
                "        \"msg\": \"操作成功\",\n" +
                "        \"opResult\": \"Success\",\n" +
                "        \"event_id\": \"com.zte.interfaces.cbom.FixBomCommonController@queryFixBomDetailByFixBomId\",\n" +
                "        \"code\": \"0000\",\n" +
                "        \"costTime\": \"12ms\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"clickTime\": \"Thu May 22 10:12:40 CST 2025\",\n" +
                "        \"tag\": \"通过fixBomId查询明细\",\n" +
                "        \"hostAddress\": \"*************\",\n" +
                "        \"serviceName\": \"zte-mes-manufactureshare-centerfactory\",\n" +
                "        \"userId\": \"1\"\n" +
                "    },\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}");
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        d1.setItemNo("234");
        omsAlibabaBillDTOList.add(d1);
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        d1.setItemNo("123");
        omsAlibabaBillDTOList.add(d1);
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": [\n" +
                "        {\n" +
                "            \"zteCode\": \"123\",\n" +
                "            \"customerCode\": \"123\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"other\": {\n" +
                "        \"msg\": \"操作成功\",\n" +
                "        \"opResult\": \"Success\",\n" +
                "        \"event_id\": \"com.zte.interfaces.cbom.FixBomCommonController@queryFixBomDetailByFixBomId\",\n" +
                "        \"code\": \"0000\",\n" +
                "        \"costTime\": \"12ms\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"clickTime\": \"Thu May 22 10:12:40 CST 2025\",\n" +
                "        \"tag\": \"通过fixBomId查询明细\",\n" +
                "        \"hostAddress\": \"*************\",\n" +
                "        \"serviceName\": \"zte-mes-manufactureshare-centerfactory\",\n" +
                "        \"userId\": \"1\"\n" +
                "    },\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}");
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_REQUISITION_BILL_ZH, omsAlibabaBillDTOList,dto);
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn("");
        Whitebox.invokeMethod(omsAlibabaService, "pushBillsToAlibaba",OMS_RETURN_BILL_ZH, omsAlibabaBillDTOList,dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void pushDataToB2BTest() throws Exception {
        OmsAlibabaBillDTO dto = new OmsAlibabaBillDTO();
        dto.setEmpNo("111");
        Map<String, Object> map =new HashMap<>();
        Whitebox.invokeMethod(omsAlibabaService, "pushDataToB2B", "","","",dto,map);
        List<ZteStockInfoUploadLogDTO> list =new ArrayList<>();
        ZteStockInfoUploadLogDTO log =new ZteStockInfoUploadLogDTO();
        log.setId("11");
        list.add(log);
        when(zteStockInfoUploadRepository.getStockUploadLog(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(omsAlibabaService, "pushDataToB2B", "","","",dto,map);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getCustomerItemsInfoTest() throws Exception{
        when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.any(),Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": [\n" +
                "        {\n" +
                "            \"zteCode\": \"123\",\n" +
                "            \"customerCode\": \"123\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"other\": {\n" +
                "        \"msg\": \"操作成功\",\n" +
                "        \"opResult\": \"Success\",\n" +
                "        \"event_id\": \"com.zte.interfaces.cbom.FixBomCommonController@queryFixBomDetailByFixBomId\",\n" +
                "        \"code\": \"0000\",\n" +
                "        \"costTime\": \"12ms\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"clickTime\": \"Thu May 22 10:12:40 CST 2025\",\n" +
                "        \"tag\": \"通过fixBomId查询明细\",\n" +
                "        \"hostAddress\": \"*************\",\n" +
                "        \"serviceName\": \"zte-mes-manufactureshare-centerfactory\",\n" +
                "        \"userId\": \"1\"\n" +
                "    },\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}");
        List<String> list = new ArrayList<>();
        Whitebox.invokeMethod(omsAlibabaService, "getCustomerItemsInfo", list,"","");
        Whitebox.invokeMethod(omsAlibabaService, "getCustomerItemsInfo", list,"1","");
        list.add("11");
        Whitebox.invokeMethod(omsAlibabaService, "getCustomerItemsInfo", list,"","");
        Whitebox.invokeMethod(omsAlibabaService, "getCustomerItemsInfo", list,"1","");
        Assert.assertNotNull(list);
    }

    @Test
    public void setSnAndPkgIdHeadDataTest() throws Exception {
        List<CustomerItemsDTO> dto =new ArrayList<>();
        List<OmsAlibabaBillDTO> list = new ArrayList<>();
        OmsAlibabaBillDTO d1 = new OmsAlibabaBillDTO();
        d1.setBoxId("111");
        d1.setBillNo("123");
        d1.setItemNo("123");
        list.add(d1);
        Whitebox.invokeMethod(omsAlibabaService, "setSnAndPkgIdHeadData","", dto,list);
        List<String> snList = new ArrayList<>();
        snList.add("123");
        snList.add("234");
        when(omsAlibabaRepository.getReturnBillSnList(Mockito.anyString(),Mockito.anyString())).thenReturn(snList);
        Whitebox.invokeMethod(omsAlibabaService, "setSnAndPkgIdHeadData", "",dto,list);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("123");
        dto.add(customerItemsDTO);
        Whitebox.invokeMethod(omsAlibabaService, "setSnAndPkgIdHeadData", "",dto,list);
        dto=new ArrayList<>();
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("123");
        customerItemsDTO.setCustomerCode("123");
        dto.add(customerItemsDTO);
        Whitebox.invokeMethod(omsAlibabaService, "setSnAndPkgIdHeadData", "",dto,list);

        Assert.assertNotNull(dto);
    }
}
