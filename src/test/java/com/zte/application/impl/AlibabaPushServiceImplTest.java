package com.zte.application.impl;

/* Started by AICoder, pid:811b0j0677r39db149d60a92b230a206b926e748 */

import com.zte.application.CustomerItemsService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.WmsRemoteService;
import com.zte.interfaces.dto.AlibabaOnHandQtyVO;
import com.zte.interfaces.dto.CustomerInventoryListVO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.erp.GetOnhandQtyDTO;
import com.zte.interfaces.dto.erp.GetOnhandQtyResultLineDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyDTO;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyResultDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.NoticeCenterUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@PrepareForTest({SpringContextUtil.class})
public class AlibabaPushServiceImplTest extends BaseTestCase {

    @InjectMocks
    private AlibabaPushServiceImpl alibabaPushService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private WmsRemoteService wmsRemoteService;

    @Mock
    private TradeDataLogService tradeDataLogService;

    @Mock
    private CustomerItemsService customerItemsService;

    @Mock
    private CpqdRemoteService cpqdRemoteService;

    @Mock
    private LocaleMessageSourceBean lmb;

    @Mock
    private NoticeCenterUtils noticeCenterUtils;

    @Mock
    private ErpRemoteService erpRemoteService;

    @Before
    public void setUp() {
        // Setup any necessary data before each test
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES)).thenReturn(lmb);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("10349620");
        sysLookupValues.setLookupMeaning("10349620");
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599003)).thenReturn(null).thenReturn(new SysLookupValues()).thenReturn(sysLookupValues);
    }

    @Test
    public void onHandQtyPushTest() throws Exception {
        // Mocking the dependencies
        SysLookupValues lookupValue = new SysLookupValues();
        lookupValue.setLookupCode(BigDecimal.ONE);
        lookupValue.setAttribute2("warehouse1,warehouse2");
        lookupValue.setLookupMeaning("10349620");

        when(sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_7504001))
                .thenReturn(lookupValue);

        List<GetOnhandQtyResultLineDTO> onhandQtyResults = new ArrayList<>();
        GetOnhandQtyResultLineDTO onhandQtyResult = new GetOnhandQtyResultLineDTO();
        onhandQtyResult.setQuantity(10);
        onhandQtyResult.setSegment1("zteCode1");
        onhandQtyResults.add(onhandQtyResult);

        when(erpRemoteService.getOnHandQty(any(GetOnhandQtyDTO.class)))
                .thenReturn(onhandQtyResults);

        List<WmsQueryItemQtyResultDTO> wmsResults = new ArrayList<>();
        WmsQueryItemQtyResultDTO wmsResult = new WmsQueryItemQtyResultDTO();
        wmsResult.setDeliveryQty(5);
        wmsResults.add(wmsResult);

        when(wmsRemoteService.selectWarehouseToWms(any(WmsQueryItemQtyDTO.class)))
                .thenReturn(wmsResults);

        SysLookupValues lookupCode = new SysLookupValues();
        lookupCode.setLookupMeaning("Factory1");
        when(sysLookupValuesService.findByLookupCode(Integer.valueOf(Constant.LOOKUP_CODE_7505001)))
                .thenReturn(null).thenReturn(lookupCode);

//        assertThrows(MessageId.ERPSTOCK_NULL, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush());

        Page<CustomerItemsDTO> value = new Page<>();
        ArrayList<CustomerItemsDTO> rows = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("zteCode1");
        value.setRows(rows);
        when(customerItemsService.pageCustomerItemsInfo(any())).thenReturn(new Page<>()).thenReturn(value);

        ArrayList<CpqdGbomDTO> value1 = new ArrayList<>();
        CpqdGbomDTO cpqdGbomDTO = new CpqdGbomDTO();
        cpqdGbomDTO.setCbomCode("fixbom1");
        cpqdGbomDTO.setInstanceNo("zteCode1");
        value1.add(cpqdGbomDTO);
        when(cpqdRemoteService.queryGbomList(any())).thenReturn(value1);

        // Call the method to test
        Assert.assertThrows(MessageId.ITEM_NO_NULL, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush("Y"));
        Assert.assertThrows(MessageId.ITEM_NO_NULL, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush("Y"));
        rows.add(customerItemsDTO);
        Assert.assertThrows(MessageId.GET_LOOKUP_VALUE_ERROR, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush("Y"));
        customerItemsDTO.setZteCode(null);
//        Assert.assertThrows(MessageId.SYS_LOOK_NOT_CONFIG, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush());
//        Assert.assertThrows(MessageId.SYS_LOOK_NOT_CONFIG, MesBusinessException.class, () -> alibabaPushService.onHandQtyPush());
        alibabaPushService.onHandQtyPush("N");
        customerItemsDTO.setZteCode("zteCode1");
        cpqdGbomDTO.setInstanceNo(null);
        alibabaPushService.onHandQtyPush("N");
        cpqdGbomDTO.setInstanceNo("zteCode1");
        cpqdGbomDTO.setCbomCode(null);
        alibabaPushService.onHandQtyPush("N");
        cpqdGbomDTO.setCbomCode("fixbom1");
        cpqdGbomDTO.setCustomerItemName("123");
        alibabaPushService.onHandQtyPush("N");
        ArrayList<AlibabaOnHandQtyVO> result = alibabaPushService.onHandQtyPush("Y");

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.size());
        AlibabaOnHandQtyVO vo = result.get(0);
        assertEquals(10349620, vo.getInventoryType().intValue());
        assertNotNull(vo.getInventoryDirective());
        assertEquals("Factory1", vo.getFactoryCode());
        assertNotNull(vo.getCustomerInventoryList());
        assertEquals(1, vo.getCustomerInventoryList().size());
        CustomerInventoryListVO customerInventory = vo.getCustomerInventoryList().get(0);
        assertEquals("fixbom1", customerInventory.getMpn());
    }

    @Test(expected = MesBusinessException.class)
    public void onHandQtyPush_NoWarehouseLookupTest() throws Exception {
        when(sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_7504001))
                .thenReturn(null);

        alibabaPushService.onHandQtyPush("Y");
    }

    @Test(expected = MesBusinessException.class)
    public void onHandQtyPush_EmptyWarehouseLookupTest() throws Exception {
        when(sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_7504001))
                .thenReturn(new SysLookupValues());

        alibabaPushService.onHandQtyPush("Y");
    }

    @Test(expected = MesBusinessException.class)
    public void onHandQtyPush_EmptyFixBomDetailsTest() throws Exception {
        SysLookupValues lookupValue = new SysLookupValues();
        lookupValue.setLookupCode(BigDecimal.ONE);
        lookupValue.setAttribute2(null);
        lookupValue.setLookupMeaning("10349620");

        when(sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_7504001))
                .thenReturn(lookupValue);

        alibabaPushService.onHandQtyPush("Y");
    }
}

/* Ended by AICoder, pid:811b0j0677r39db149d60a92b230a206b926e748 */