/* Started by AICoder, pid:a033399d5fifbe21412e0b80f031a7021fa2ff8d */
package com.zte.application.kafka;

import com.zte.action.iscpedi.GlobalVariable;
import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.action.iscpedi.model.EdiIscpData;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.step.StepIscpRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/17 14:01
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({GlobalVariable.class})
public class WriteBackIscpHandlerTest {

    @InjectMocks
    private WriteBackIscpHandler writeBackIscpHandler;

    @Mock
    private IKafkaMessageProducer kafkaMessageProducer;
    @Mock
    private WriteBackIscpResultListener writeBackIscpResultListener;
    @Mock
    private ReSendKafkaToIscpListener reSendKafkaToIscpListener;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Mock
    private StepIscpRepository stepIscpRepository;

    public static int queueSize = 10000;

    public static BlockingQueue<List<EdiIscpData>> ediQueue = new LinkedBlockingQueue<>(queueSize);
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(GlobalVariable.class);
        GlobalVariable.ediQueue = new LinkedBlockingQueue<>();
        this.ediQueue = GlobalVariable.ediQueue;

    }

    @Test
    public void WriteBackIscpHandler() {
        new WriteBackIscpHandler(kafkaMessageProducer, writeBackIscpResultListener, inforIwmsIscpRepository, stepIscpRepository);
        WriteBackIscpHandler writeBackIscpHandler2 = new WriteBackIscpHandler(kafkaMessageProducer, reSendKafkaToIscpListener, inforIwmsIscpRepository, stepIscpRepository);
        Assert.assertTrue(Objects.nonNull(writeBackIscpHandler2));

    }

    @Test
    public void doBiz() throws Exception {
        List<EdiIscpData> list = new ArrayList<>();
        EdiIscpData ediIscpData = new EdiIscpData();
        list.add(ediIscpData);
        ediIscpData.setSourceTable("ST_RECEIVECHECK");
        writeBackIscpHandler.doBiz(list);
        writeBackIscpHandler.doBiz(list,31,1);
        ediIscpData.setSourceTable("ST_RECEIVECHECK_S");
        writeBackIscpHandler.doBiz(list,1,1);
        ediIscpData.setSourceTable("11");
        writeBackIscpHandler.doBiz(list,1,1);
        Assert.assertTrue(Objects.nonNull(list));
    }
}
/* Ended by AICoder, pid:a033399d5fifbe21412e0b80f031a7021fa2ff8d */