package com.zte.application.kafka;

import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.action.iscpedi.WriteBackIscpStartHandler;
import com.zte.action.iscpedi.model.EdiIscpData;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,WriteBackIscpHandler.class,IKafkaMessageProducer.class})
public class WriteBackIscpStartHandlerTest {

    @InjectMocks
    private WriteBackIscpStartHandler writeBackIscpStartHandler;
    @Mock
    WriteBackIscpHandler writeBackIscpHandler;
    @Mock
    IKafkaMessageProducer kafkaMessageProducer;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class,WriteBackIscpHandler.class,IKafkaMessageProducer.class);
    }
    @Test
    public void run() {
        try {
            List<EdiIscpData> ediIscpData = Tools.newArrayList();
            EdiIscpData ediIscpData1 = new EdiIscpData();
            ediIscpData1.setSerialKey(8541222);
            ediIscpData1.setBatchNo("233213");
            ediIscpData.add(ediIscpData1);
            writeBackIscpStartHandler.runBiz(ediIscpData);
            writeBackIscpStartHandler.runBizNew(ediIscpData);
            Assert.assertTrue(Objects.nonNull(ediIscpData));
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void start() {
        String msg = "msg";
        writeBackIscpStartHandler.start();
        Assert.assertTrue(Objects.nonNull(msg));
    }

    @Test
    public void setArgs() {
        List<String> list = Arrays.asList("");
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiEntryArgs()).thenReturn(list);
        writeBackIscpStartHandler.setArgs();
        list = Arrays.asList("1|");
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiEntryArgs()).thenReturn(list);
        writeBackIscpStartHandler.setArgs();
        list = Arrays.asList("1|2");
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiEntryArgs()).thenReturn(list);
        writeBackIscpStartHandler.setArgs();
        Assert.assertTrue(Objects.nonNull(list));
    }
}
