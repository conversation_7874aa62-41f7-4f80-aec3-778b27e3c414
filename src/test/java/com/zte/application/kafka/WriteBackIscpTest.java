package com.zte.application.kafka;

import com.zte.Application;
import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.application.infor.InforIwmsIscpService;
import com.zte.application.kafka.IKafkaMessageProducer;
import com.zte.application.kafka.WriteBackIscpResultListener;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Objects;

/**
 * @Deacription 出库入库回写ISCP单元测试
 * <AUTHOR>
 * @Date 2020/9/3 14:16
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,IKafkaMessageProducer.class})
public class WriteBackIscpTest {

    @Mock
    private InforIwmsIscpService inforIwmsIscpService;
    @Mock
    private IKafkaMessageProducer kafkaMessageProducer;
    @InjectMocks
    private WriteBackIscpResultListener writeBackIscpResultListener;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Mock
    private StepIscpRepository stepIscpRepository;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class,IKafkaMessageProducer.class);
    }

    @Test
    public void sendMessageTest() throws InterruptedException {
        boolean test =kafkaMessageProducer.sendMessage("zte-scm-basic", "DeliCallback", "[{\"barCodeList\":[{\"actualQty\":\"1\",\"anticipatedQty\":\"1\",\"barCode\":\"220008724880\",\"batchNo\":\"\",\"boxNo\":\" \",\"operateTime\":\"2019-08-19 17:45:07\",\"serialkey\":3214171}],\"deliBillNo\":\"T19081900004\",\"operateType\":\"08\",\"rowNo\":\"2193326\"}]", writeBackIscpResultListener);
        System.out.println(test);
        Assert.assertTrue(Objects.nonNull(test));
    }

    @Test
    public void writeBackTest() {
        String runNormal = "Y";
        inforIwmsIscpService.iscpEdiEntry(null, null);
        Assert.assertTrue(Objects.nonNull(runNormal));
    }

    @Test
    public void resultListenerTest() {
        ProducerRecord producerRecord = new ProducerRecord("topic", "key", "[{\"barCodeList\":[{\"actualQty\":\"1\",\"anticipatedQty\":\"1\",\"barCode\":\"220008724880\",\"batchNo\":\"\",\"boxNo\":\" \",\"operateTime\":\"2019-08-19 17:45:07\",\"serialkey\":3214171}],\"deliBillNo\":\"T19081900004\",\"operateType\":\"08\",\"rowNo\":\"2193326\"}]");
        writeBackIscpResultListener.onSuccess(producerRecord, null);
        Assert.assertTrue(Objects.nonNull(producerRecord));
    }
    
    @Test
    public void cancelListenerTest() {
        ProducerRecord producerRecord = new ProducerRecord("topic", "key", "[{\"barCodeList\":[{\"actualQty\":\"1\",\"anticipatedQty\":\"1\",\"barCode\":\"220008724880\",\"batchNo\":\"\",\"boxNo\":\" \",\"operateTime\":\"2019-08-19 17:45:07\",\"serialkey\":3214171}],\"deliBillNo\":\"T19081900004\",\"operateType\":\"08\",\"rowNo\":\"2193326\"}]");
        writeBackIscpResultListener.syncMessageState(producerRecord, 0);
        Assert.assertTrue(Objects.nonNull(producerRecord));
    }
}
