package com.zte.application.kafka;

import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.interfaces.transfer.ManualTransferController;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.kafka.consumer.KafkaConsumerService;
import com.zte.resourcewarehouse.common.orm.core.IOrmTemplate;
import com.zte.resourcewarehouse.common.springbootframe.datasource.DatabaseType;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Objects;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, WriteBackIscpHandler.class})
public class KafkaConsumerServiceTest {

    @InjectMocks
    private KafkaConsumerService kafkaConsumerService;
    @Mock
    private IOrmTemplate ormTemplate;


    @Mock
    private ManualTransferController manualTransferController;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class,WriteBackIscpHandler.class);
    }
    @Test
    public void dealTransfer(){
        DatabaseContextHolder.setDatabaseType(DatabaseType.DB_1);

        try {
            kafkaConsumerService.dealTransfer("");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            kafkaConsumerService.dealTransfer("103");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            boolean res = kafkaConsumerService.dealTransfer("102");
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }

        DatabaseContextHolder.setDatabaseType(DatabaseType.DB_1);
    }




    @Test
    public void doBiz(){
        String runNormal = "Y";
        manualTransferController.autoTransferDeal();
        Assert.assertTrue(Objects.nonNull(runNormal));
    }
}
