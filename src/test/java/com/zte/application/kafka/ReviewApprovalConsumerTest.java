package com.zte.application.kafka;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.ApprovalProcessInfoService;
import com.zte.application.kafka.consumer.ReviewApprovalConsumer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JSONObject.class})
public class ReviewApprovalConsumerTest {
    @InjectMocks
    private ReviewApprovalConsumer reviewApprovalConsumer;
    @Mock
    private ApprovalProcessInfoService approvalProcessInfoService;

    @Test
    public void kafkaMsgReceive() throws Exception{
        String responseStr = "{\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}";
        reviewApprovalConsumer.kafkaMsgReceive(responseStr);

        responseStr = "{\"messageKey\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}";
        PowerMockito.field(ReviewApprovalConsumer.class, "reviewAppId").set(reviewApprovalConsumer, "504249875830");
        reviewApprovalConsumer.kafkaMsgReceive(responseStr);

        responseStr = "{\"messageKey\": \"5042498758300000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}";
        PowerMockito.field(ReviewApprovalConsumer.class, "reviewAppId").set(reviewApprovalConsumer, "504249875830");
        reviewApprovalConsumer.kafkaMsgReceive(responseStr);
        Assert.assertTrue(true);
    }
}
