/* Started by AICoder, pid:wf7d3v8bc67102c1426c087f0054ce0b96b15cd3 */
package com.zte.application.kafka;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.ApprovalProcessInfoService;
import com.zte.application.kafka.consumer.ApprovalConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/16 10:05
 */
@RunWith(PowerMockRunner.class)
public class ApprovalConsumerTest {
    @InjectMocks
    private ApprovalConsumer approvalConsumer;
    @Mock
    private ApprovalProcessInfoService approvalProcessInfoService;

    @Test
    public void kafkaMsgReceive() throws Exception{
        ConsumerRecord<String, String> record = null;
        approvalConsumer.kafkaMsgReceive(record);
        PowerMockito.field(ApprovalConsumer.class, "appCode").set(approvalConsumer, "wrongKey");
        record = new ConsumerRecord<>("topic", 0, 1L, "", "{}");
        approvalConsumer.kafkaMsgReceive(record);
        record = new ConsumerRecord<>("topic", 0, 1L, "wrongKey", "{\"field\":\"value\"}");
        approvalConsumer.kafkaMsgReceive(record);
        Assert.assertTrue(Objects.nonNull(record));
    }


}
/* Ended by AICoder, pid:wf7d3v8bc67102c1426c087f0054ce0b96b15cd3 */