package com.zte.application.onlinefallback;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.material.impl.IwmsEcssServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.IscpEsbLogRepository;
import com.zte.domain.model.infor.OnlineFallbackInforRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.material.StFallbackApplybillHead;
import com.zte.domain.model.step.OnlineFallBackRepository;
import com.zte.interfaces.onlinefallback.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.zte.application.onlinefallback.impl.OnlineFallBackServiceImpl;
import com.zte.domain.model.material.StFallbackApplybillDetail;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;

import static com.zte.common.utils.Constant.ECSS_BILL_TERMINATE;
import static com.zte.resourcewarehouse.common.springbootframe.datasource.DatabaseType.DB_1;
import static com.zte.resourcewarehouse.common.springbootframe.datasource.DatabaseType.DB_STEP;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class, RemoteServiceDataUtil.class, RedisSerialNoUtil.class, JsonUtil.class, WebServiceClient.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class OnlineFallBackServiceImplTest {

    @InjectMocks
    private OnlineFallBackServiceImpl onlineFallBackServiceImpl;

    @Mock
    private OnlineFallBackRepository onlineFallBackRepository;

    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Mock
    private OnlineFallbackInforRepository onlineFallbackInforRepository;

    @Mock
    private IscpEsbLogRepository iscpEsbLogRepository;

    @Mock
    private IwmsEcssServiceImpl iwmsEcssService;

    @Test
    public void scheduleDealFallbackNo() {
        String fallbackNo = "test123456";
        ServiceData<?> serviceData = new ServiceData<>();
        try {
            serviceData = onlineFallBackServiceImpl.scheduleDealFallbackNo(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        serviceData = new ServiceData<>();
        Assert.assertTrue(RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode()));
    }

    @Test
    public void submitApplyBillToISRM() {
        String fallbackNo = "B13081600108";
        try {
            onlineFallBackServiceImpl.submitApplyBillToISRM(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        ServiceData<?> serviceData = new ServiceData<>();
        List<ApplyBillDTO> listH = new ArrayList<>();
        List<ApplyBillItemDTO> listD = new ArrayList<>();
        ApplyBillDTO applyBillDTO = new ApplyBillDTO();
        applyBillDTO.setBillNo("11");
        applyBillDTO.setReelid("1");
        listH.add(applyBillDTO);
        ApplyBillItemDTO applyBillItemDTO = new ApplyBillItemDTO();
        applyBillItemDTO.setItemNo("22");
        applyBillItemDTO.setBarCode("33");
        listD.add(applyBillItemDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryFallbackApplybillHead(anyString())).thenReturn(listH);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryFallbackApplybillDetail(anyString())).thenReturn(listD);
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        try {
            onlineFallBackServiceImpl.submitApplyBillToISRM(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        serviceData.getCode().setCode(RetCode.SUCCESS_CODE);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        List<String> waitPushList = new ArrayList<>();
        waitPushList.add("111");
        waitPushList.add("222");
        PowerMockito.when(onlineFallBackApplyBillRepository.queryWaitPushList()).thenReturn(waitPushList);
        try {
            onlineFallBackServiceImpl.autoSubmitApplyBillToISRM();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            onlineFallBackServiceImpl.submitApplyBillToISRM(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        try {
            ServiceData<?> test = onlineFallBackServiceImpl.submitApplyBillToISRM(fallbackNo);
            Assert.assertTrue(Objects.nonNull(test));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void autoSubmitApplyBillToISRM() {
        List<String> waitPushList = new ArrayList<>();
        PowerMockito.when(onlineFallBackApplyBillRepository.queryWaitPushList()).thenReturn(waitPushList);
        try {
            onlineFallBackServiceImpl.autoSubmitApplyBillToISRM();
        } catch (Exception e) {
            e.printStackTrace();
        }

        waitPushList.add("111");
        waitPushList.add("222");
        PowerMockito.when(onlineFallBackApplyBillRepository.queryWaitPushList()).thenReturn(waitPushList);
        try {
            ServiceData<?> test =  onlineFallBackServiceImpl.autoSubmitApplyBillToISRM();
            Assert.assertTrue(Objects.nonNull(test));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getStFallbackApplybillHead() {
        String fallbackNo = "ZTH2007290002";
        ServiceData<?> serviceData = new ServiceData<>();
        DatabaseContextHolder.setDatabaseType(DB_1);
        try {
            onlineFallBackServiceImpl.getStFallbackApplybillHead(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertTrue(RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode()));
        fallbackNo = "12345342";
        try {
            onlineFallBackServiceImpl.getStFallbackApplybillHead(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void scheduleDealWaitFeedApplyNoTest() {
        String applybillNo = "STH2006150017";
        List<ApplyBillDTO> list = new ArrayList<>();
        ApplyBillDTO applyBillDTO=new ApplyBillDTO();
        applyBillDTO.setBillNo("1");
        applyBillDTO.setTargetLocation("WMWHSE30");
        list.add(applyBillDTO);
        ApplyBillDTO applyBillDTO1=new ApplyBillDTO();
        applyBillDTO1.setBillNo("2");
        applyBillDTO1.setTargetLocation("WMWHSE34");
        list.add(applyBillDTO1);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryWaitFeedApplyNo(anyString())).thenReturn(list);
        List<String> externalOrderKeyList = new ArrayList<>();
        PowerMockito.when(onlineFallbackInforRepository.queryDealWarehouseSo(any())).thenReturn(externalOrderKeyList);
        try {
            onlineFallBackServiceImpl.scheduleDealWaitFeedApplyNo(applybillNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<String> stringList=new ArrayList<>();
        stringList.add("2");
        stringList.add("1");
        PowerMockito.when(onlineFallbackInforRepository.queryDealWarehouseSo(any())).thenReturn(stringList);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryStFallbackApplybillHeadHandlingOptions(any())).thenReturn(Constant.STR_AGREE);
        try {
            onlineFallBackServiceImpl.scheduleDealWaitFeedApplyNo(applybillNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.when(onlineFallBackApplyBillRepository.queryStFallbackApplybillHeadHandlingOptions(any())).thenReturn(Constant.STR_SCRAP);
        try {
            onlineFallBackServiceImpl.scheduleDealWaitFeedApplyNo(applybillNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.when(onlineFallBackApplyBillRepository.queryStFallbackApplybillHeadHandlingOptions(any())).thenReturn(Constant.STR_BOX);
        try {
            onlineFallBackServiceImpl.scheduleDealWaitFeedApplyNo(applybillNo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.when(onlineFallBackApplyBillRepository.queryStFallbackApplybillHeadHandlingOptions(any())).thenThrow(new NullPointerException());
        try {
            onlineFallBackServiceImpl.scheduleDealWaitFeedApplyNo(applybillNo);
            Assert.assertTrue(Objects.nonNull(applybillNo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void submitApllyToInforSo() {
        try {
            onlineFallBackServiceImpl.submitApllyToInforSo("B13081600108");
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.mockStatic(WebServiceClient.class);
        PowerMockito.when(WebServiceClient.submitInfor(anyString(), anyString())).thenReturn(new ServiceData<>());
        try {
            ServiceData<?> test = onlineFallBackServiceImpl.submitApllyToInforSo("B13081600108");
            Assert.assertTrue(Objects.nonNull(test));
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.when(onlineFallBackApplyBillRepository.getWsdlUrl()).thenThrow(new NullPointerException());
        try {
            ServiceData<?> test = onlineFallBackServiceImpl.submitApllyToInforSo("B13081600108");
            Assert.assertTrue(Objects.nonNull(test));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void feedbackApplyBillSoResult() {
        List<ApplyBillSoResultDTO> listH = new ArrayList<>();
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(anyString())).thenReturn(listH);
        List<ApplyBillSoResultItemDTO> listD = new ArrayList<>();
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultDetail(anyString())).thenReturn(listD);
        List<Map<String, String>> listR = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("rowNo", "111");
        map1.put("reelSn", "222");
        listR.add(map1);
        Map<String, String> map2 = new HashMap<>();
        map2.put("rowNo", "");
        map2.put("reelSn", "");
        listR.add(map2);
        Map<String, String> map3 = new HashMap<>();
        map3.put("rowNo", "111");
        map3.put("reelSn", "333");
        listR.add(map3);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultReelSn(anyString())).thenReturn(listR);
        PowerMockito.when(onlineFallbackInforRepository.queryRealOutNumber(anyString(), anyInt())).thenReturn(1.23);
        try {
            onlineFallBackServiceImpl.feedbackApplyBillSoResult("STH2007290001");
        } catch (Exception e) {
            e.printStackTrace();
        }

        ApplyBillSoResultDTO applyBillSoResultDTO = new ApplyBillSoResultDTO();
        applyBillSoResultDTO.setHandlingOpinions("111");
        applyBillSoResultDTO.setFallbackNo("222");
        listH.add(applyBillSoResultDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(anyString())).thenReturn(listH);
        try {
            onlineFallBackServiceImpl.feedbackApplyBillSoResult("STH2007290001");
        } catch (Exception e) {
            e.printStackTrace();
        }

        ServiceData<?> serviceData = new ServiceData<>();
        serviceData.getCode().setCode(RetCode.BUSINESSERROR_CODE);
        ApplyBillSoResultItemDTO applyBillSoResultItemDTO1 = new ApplyBillSoResultItemDTO();
        applyBillSoResultItemDTO1.setBarCode("11");
        applyBillSoResultItemDTO1.setLpn("111");
        listD.add(applyBillSoResultItemDTO1);
        ApplyBillSoResultItemDTO applyBillSoResultItemDTO2 = new ApplyBillSoResultItemDTO();
        applyBillSoResultItemDTO2.setBarCode("22");
        applyBillSoResultItemDTO2.setLpn("222");
        listD.add(applyBillSoResultItemDTO2);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultDetail(anyString())).thenReturn(listD);
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        try {
            onlineFallBackServiceImpl.feedbackApplyBillSoResult("STH2007290001");
        } catch (Exception e) {
            e.printStackTrace();
        }

        serviceData.getCode().setCode(RetCode.SUCCESS_CODE);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        try {
            ServiceData<?> res = onlineFallBackServiceImpl.feedbackApplyBillSoResult("STH2007290001");
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }

        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(anyString())).thenThrow(new NullPointerException());
        try {
            onlineFallBackServiceImpl.feedbackApplyBillSoResult("STH2007290001");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void acceptApplyBillStatus() {
        ApplyBillStatusDTO applyBillStatusDTO = new ApplyBillStatusDTO();
        List<ApplyBillSoResultDTO> applyBillSoResultDTOList = new ArrayList<>();
        ApplyBillSoResultDTO applyBillSoResultDTO = new ApplyBillSoResultDTO();
        applyBillSoResultDTO.setHandlingOpinions("111");
        applyBillSoResultDTO.setFallbackNo("222");
        applyBillSoResultDTOList.add(applyBillSoResultDTO);
        List<StSysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        StSysLookupValuesDTO stSysLookupValuesDTO = new StSysLookupValuesDTO();
        stSysLookupValuesDTO.setLookupCode(null);
        stSysLookupValuesDTO.setLookupMeaning("111");
        sysLookupValuesDTOList.add(stSysLookupValuesDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(anyString())).thenReturn(applyBillSoResultDTOList);
        iwmsEcssService.invokeBillToEcss(JSONObject.toJSONString(Arrays.asList("234215415")), ECSS_BILL_TERMINATE);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(anyString())).thenReturn(sysLookupValuesDTOList);
        try {
            onlineFallBackServiceImpl.acceptApplyBillStatus(applyBillStatusDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        applyBillStatusDTO.setBillNo("1235462342");
        try {
            onlineFallBackServiceImpl.acceptApplyBillStatus(applyBillStatusDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        applyBillStatusDTO.setBillStatus("CLOSED");
        try {
            onlineFallBackServiceImpl.acceptApplyBillStatus(applyBillStatusDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        applyBillStatusDTO.setModifyTime("123");
        stSysLookupValuesDTO.setLookupMeaning("222");
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTOList.add(stSysLookupValuesDTO);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSysLookupValues(anyString())).thenReturn(sysLookupValuesDTOList);
        try {
            onlineFallBackServiceImpl.acceptApplyBillStatus(applyBillStatusDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<StFallbackApplybillHead> listHead = new ArrayList<>();
        StFallbackApplybillHead stFallbackApplybillHead = new StFallbackApplybillHead();
        stFallbackApplybillHead.setApplybillNo("11");
        listHead.add(stFallbackApplybillHead);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryFallbackHead(anyString())).thenReturn(listHead);
        List<StFallbackApplybillDetail> listDetail = new ArrayList<>();
        StFallbackApplybillDetail stFallbackApplybillDetail = new StFallbackApplybillDetail();
        stFallbackApplybillDetail.setItemBarcode("123");
        listDetail.add(stFallbackApplybillDetail);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryFallbackDetail(anyString())).thenReturn(listDetail);
        List<ReceiptDetailDTO> receiptDetailDTOs = new ArrayList<>();
        ReceiptDetailDTO receiptDetailDTO = new ReceiptDetailDTO();
        receiptDetailDTO.setToid("12");
        receiptDetailDTO.setQtyreceived(null);
        receiptDetailDTO.setLottable06("22");
        receiptDetailDTOs.add(receiptDetailDTO);
        PowerMockito.when(onlineFallbackInforRepository.queryDetailInforReceived(any())).thenReturn(receiptDetailDTOs);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseIdPattern(anyString(), anyInt(), anyString())).thenReturn("2234");
        onlineFallBackServiceImpl.reSaveOnlineFallBackApplyBill(applyBillStatusDTO);
        Assert.assertTrue(Objects.nonNull(applyBillStatusDTO));
    }

    @Test
    public void acceptApplyBillSupplyResult() {
        SupplyResultDTO supplyResultDTO = new SupplyResultDTO();
        supplyResultDTO.setOrigId(-1);
        try {
            onlineFallBackServiceImpl.acceptApplyBillSupplyResult(supplyResultDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        supplyResultDTO.setOrigId(1);
        supplyResultDTO.setBillNo("123211");
        supplyResultDTO.setCode("213123");
        supplyResultDTO.setReturnComments("2132132");
        try {
            onlineFallBackServiceImpl.acceptApplyBillSupplyResult(supplyResultDTO);
            Assert.assertTrue(Objects.nonNull(supplyResultDTO));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void failReelIdBarcodeAndFeedbackResultTest() {
        String applybillNo = "TEST001111";
        onlineFallBackServiceImpl.getXmlMessage(applybillNo);
        String xEmpNo = "10233138";
        List<String> applybillNoList = new ArrayList<>();
        applybillNoList.add("111");
        applybillNoList.add("222");
        PowerMockito.when(iscpEsbLogRepository.queryListApplybillNo(anyString())).thenReturn(applybillNoList);
        String xAuthValue = "8wU4BcMjWW1wQdNW-2iIBZci3vVKwiedrFBfvwcCnLO.9NTNzkTO1QDM2EjOiAHelJCLiIGOhFDZmBDZzcDNhZmMwMWMykTMhFjZiNTZjNzNkZWMiojI1Jye.9JiN1IzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye";
        ServiceData<?> serviceData = onlineFallBackServiceImpl.failReelIdBarcodeAndFeedbackResult(applybillNo, xEmpNo, xAuthValue);
        Assert.assertTrue(RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode()));
        applybillNo = "B13081600108";
        try {
            onlineFallBackServiceImpl.getXmlMessage(applybillNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            onlineFallBackServiceImpl.failReelIdBarcodeAndFeedbackResult(applybillNo, xEmpNo, xAuthValue);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<SoHeader> listSoHeader = new ArrayList<>();
        SoHeader soHeader = new SoHeader();
        soHeader.setConsigneeKey("11");
        listSoHeader.add(soHeader);
        List<SoDetail> listSoDetail = new ArrayList<>();
        SoDetail soDetail = new SoDetail();
        soDetail.setLottable06("222");
        listSoDetail.add(soDetail);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSoHeader(anyString())).thenReturn(listSoHeader);
        PowerMockito.when(onlineFallBackApplyBillRepository.getSoDetail(anyString())).thenReturn(listSoDetail);
        try {
            onlineFallBackServiceImpl.getXmlMessage(applybillNo);
            Assert.assertTrue(Objects.nonNull(applybillNo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void encapsulatingDataandInvokingtheBarcodeCenterTest() {
        String xEmpNo = null;
        String xAuthValue = null;
        List<ApplyBillDTO> barcodeList = new ArrayList<>();
        ApplyBillDTO applyBillDTO1 = new ApplyBillDTO();
        applyBillDTO1.setReelid("111");
        applyBillDTO1.setReelidFlag(BigDecimal.ONE);
        applyBillDTO1.setBillNo("aaa");
        barcodeList.add(applyBillDTO1);
        ApplyBillDTO applyBillDTO2 = new ApplyBillDTO();
        applyBillDTO2.setReelid("222");
        applyBillDTO2.setReelidFlag(NumConstant.BIG_2);
        applyBillDTO2.setBillNo("bbb");
        barcodeList.add(applyBillDTO2);
        ApplyBillDTO applyBillDTO3 = new ApplyBillDTO();
        applyBillDTO3.setReelid("333");
        applyBillDTO3.setReelidFlag(NumConstant.BIG_1);
        applyBillDTO3.setBillNo("aaa");
        barcodeList.add(applyBillDTO3);
        ServiceData<?> serviceData = new ServiceData<>();
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), anyString(), anyString(), anyMap())).thenReturn(serviceData);
        onlineFallBackServiceImpl.encapsulatingDataandInvokingtheBarcodeCenter(barcodeList, xEmpNo, xAuthValue);
        Assert.assertTrue(Objects.nonNull(applyBillDTO3));
    }

    @Test
    // public void updateIscpEsbLog(ApplyBillDTO abd, ServiceData<?> res)
    public void updateIscpEsbLog() {
        String runNormal = "Y";
        ApplyBillDTO abd = new ApplyBillDTO();
        ServiceData<?> res = new ServiceData<>();
        onlineFallBackServiceImpl.updateIscpEsbLog(abd, res);
        Assert.assertTrue(Objects.nonNull(runNormal));
    }

    @Test
    public void invokeBarcodeInterfaceTest() {
        String runNormal = "Y";
        List<Map<String, String>> list = new ArrayList<>();
        String xEmpNo = null;
        String xAuthValue = null;
        try {
            onlineFallBackServiceImpl.invokeBarcodeInterface(list, xEmpNo, xAuthValue);
            Assert.assertTrue(Objects.nonNull(runNormal));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void getLpnFromInfor() throws CloneNotSupportedException {
        List<StFallbackApplybillDetail> listDetail = new ArrayList<>();
        List<ReceiptDetailDTO> receiptDetailDTOs = new ArrayList<>();
        ReceiptDetailDTO receiptDetailDTO = new ReceiptDetailDTO();
        receiptDetailDTO.setToid("1");
        receiptDetailDTO.setQtyreceived(null);
        receiptDetailDTO.setLottable06("222");
        receiptDetailDTOs.add(receiptDetailDTO);
        StFallbackApplybillDetail sDetail = new StFallbackApplybillDetail();
        String teString = "ZTH2101260004";
        sDetail.setFallbackDetailguid(teString);
        sDetail.setToId(teString);
        sDetail.setItemBarcode("************");
        listDetail.add(sDetail);
        listDetail.add(sDetail);
        PowerMockito.when(onlineFallbackInforRepository.queryDetailInforReceived(any())).thenReturn(receiptDetailDTOs);
        StFallbackApplybillHead dto=new StFallbackApplybillHead();
        dto.setFallbackNo("32443");
        dto.setTargetLocation("WMWHSE30");
        List<StFallbackApplybillDetail> test = onlineFallBackServiceImpl.getLpnFromInfor(listDetail, teString,dto);
        Assert.assertTrue(Objects.nonNull(test));
    }

    @Test
    public void produceApplyBillToDB() {
        ServiceData<?> res = new ServiceData<>();
        try {
            res = onlineFallBackServiceImpl.produceApplyBillToDB("");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            res = onlineFallBackServiceImpl.produceApplyBillToDB("ZTH2007290002");
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            res = onlineFallBackServiceImpl.produceApplyBillToDB("ZHTYYYY11222");
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getNonconformingBillTest() {
        List<NonconformingBillOutputDTO> list = new ArrayList<>();
        NonconformingBillOutputDTO nonconformingBillOutputDTO = new NonconformingBillOutputDTO();
        nonconformingBillOutputDTO.setType(NumConstant.STR_01);
        nonconformingBillOutputDTO.setStatus(NumConstant.STR_01);
        nonconformingBillOutputDTO.setItemBarcode("111");
        PowerMockito.when(onlineFallBackRepository.queryProcessedBySkuManager(any())).thenReturn(list);
        PowerMockito.when(onlineFallBackRepository.queryNonconformingBill(any())).thenReturn(list);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        NonconformingBillInputDTO input = new NonconformingBillInputDTO();
        input.setNonconformingProductNo("QTY202106162241");
        DatabaseContextHolder.setDatabaseType(DB_STEP);
        try {
            onlineFallBackServiceImpl.getNonconformingBill(input);
        } catch (Exception e) {
            e.printStackTrace();
        }

        list.add(nonconformingBillOutputDTO);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        try {
            onlineFallBackServiceImpl.getNonconformingBill(input);
        } catch (Exception e) {
            e.printStackTrace();
        }

        nonconformingBillOutputDTO.setType(NumConstant.STR_02);
        nonconformingBillOutputDTO.setStatus(NumConstant.STR_10001);
        list.clear();
        list.add(nonconformingBillOutputDTO);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        try {
            onlineFallBackServiceImpl.getNonconformingBill(input);
        } catch (Exception e) {
            e.printStackTrace();
        }

        nonconformingBillOutputDTO.setType(NumConstant.STR_01);
        nonconformingBillOutputDTO.setStatus(NumConstant.STR_10);
        list.clear();
        list.add(nonconformingBillOutputDTO);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        try {
            onlineFallBackServiceImpl.getNonconformingBill(input);
        } catch (Exception e) {
            e.printStackTrace();
        }

        nonconformingBillOutputDTO.setType(NumConstant.STR_03);
        nonconformingBillOutputDTO.setStatus(NumConstant.STR_10001);
        list.clear();
        list.add(nonconformingBillOutputDTO);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        try {
            onlineFallBackServiceImpl.getNonconformingBill(input);
        } catch (Exception e) {
            e.printStackTrace();
        }

        NonconformingBillOutputDTO nonconformingBillOutputDTO1 = new NonconformingBillOutputDTO();
        nonconformingBillOutputDTO1.setType(NumConstant.STR_01);
        nonconformingBillOutputDTO1.setStatus(NumConstant.STR_10);
        nonconformingBillOutputDTO1.setItemBarcode("222");
        nonconformingBillOutputDTO.setType(NumConstant.STR_02);
        nonconformingBillOutputDTO.setStatus(NumConstant.STR_01);
        list.clear();
        list.add(nonconformingBillOutputDTO);
        list.add(nonconformingBillOutputDTO1);
        PowerMockito.when(onlineFallBackRepository.getNonconformingBill(any())).thenReturn(list);
        PowerMockito.when(onlineFallBackRepository.queryNonconformingBill(any())).thenReturn(list);
        try {
            ServiceData<?> res = onlineFallBackServiceImpl.getNonconformingBill(input);
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void scheduleUpdateFallbackSrcSoStatus() {
        String fallbackNo = "ZTH2101250004";
        List<FallbackNoInfo> list = new ArrayList<>();
        FallbackNoInfo fallbackNoInfo1 = new FallbackNoInfo();
        fallbackNoInfo1.setSourcelocation("1");
        fallbackNoInfo1.setFallbackNo("1");
        FallbackNoInfo fallbackNoInfo2 = new FallbackNoInfo();
        fallbackNoInfo2.setFallbackNo("2");
        list.add(fallbackNoInfo1);
        list.add(fallbackNoInfo2);
        PowerMockito.when(onlineFallBackApplyBillRepository.queryWaitFallback(any())).thenReturn(list);
        PowerMockito.when(onlineFallbackInforRepository.queryInforSoStatus(anyString(), any())).thenReturn(list);
        onlineFallBackServiceImpl.scheduleUpdateFallbackSrcSoStatus("");
        PowerMockito.when(onlineFallbackInforRepository.queryInforSoStatus(anyString(), any())).thenReturn(null);
        ServiceData<?> res = onlineFallBackServiceImpl.scheduleUpdateFallbackSrcSoStatus(fallbackNo);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void scheduleDealFallbackNoNext() {
        String fallbackNo = "ZTH2007010010";
        try {
            ServiceData<?> res = onlineFallBackServiceImpl.scheduleDealFallbackNo(fallbackNo);
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
        fallbackNo = "ZTH2011180001";
        try {
            onlineFallBackServiceImpl.scheduleDealFallbackNo(fallbackNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void updateStatusAndQty() {
        String applybillNo = "123";
        List<ApplyBillSoResultItemDTO> listD = new ArrayList<>();
        ApplyBillSoResultItemDTO abs = new ApplyBillSoResultItemDTO();
        abs.setRealOutNumber(123);
        abs.setRowNo(123);
        listD.add(abs);
        StFallbackApplybillHead tmp = new StFallbackApplybillHead();
        try {
            onlineFallBackServiceImpl.updateStatusAndQty(applybillNo, listD, tmp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertTrue(Objects.nonNull(applybillNo));
    }

}
