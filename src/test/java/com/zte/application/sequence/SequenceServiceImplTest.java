/* Started by AICoder, pid:j4fce23162o40ef14d8a0885b0503b005b31c1a1 */
package com.zte.application.sequence;

import com.zte.application.sequence.impl.SequenceServiceImpl;
import com.zte.domain.model.material.Sequence;
import com.zte.domain.model.material.SequenceRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/1/16 14:21
 */
@RunWith(PowerMockRunner.class)
public class SequenceServiceImplTest {
    @InjectMocks
    private SequenceServiceImpl sequenceService;

    @Mock
    private SequenceRepository sequenceRepository;


    @Test
    public void getSequenceWithDate() throws Exception {
        sequenceService.getSequenceWithDate("111");
        Sequence sequence = new Sequence();
        sequence.setYear(2024);
        sequence.setMonth(12);
        sequence.setDay(1);
        sequence.setSequencePrefix("Seq");
        sequence.setSequenceSuffix("Fix");
        sequence.setSequenceCurrNo(1);
        sequence.setSequenceStep(2);
        sequence.setSequenceLength(10);
        PowerMockito.when(sequenceRepository.selectByPrimaryKey(Mockito.anyString())).thenReturn(sequence);
        sequenceService.getSequenceWithDate("111");
        sequence.setYear(2025);
        sequence.setMonth(12);
        sequence.setDay(1);
        sequence.setSequencePrefix("");
        sequence.setSequenceSuffix("");
        PowerMockito.when(sequenceRepository.updateByPrimaryKeyAndVersion(Mockito.any())).thenReturn(1);
        String sequenceWithDate = sequenceService.getSequenceWithDate("111");
        Assert.assertTrue(Objects.nonNull(sequenceWithDate));
    }



    @Test
    public void getSequenceWithOutDate() throws Exception {
        sequenceService.getSequenceWithOutDate("111");
        Sequence sequence = new Sequence();
        sequence.setYear(2024);
        sequence.setMonth(12);
        sequence.setDay(1);
        sequence.setSequencePrefix("Seq");
        sequence.setSequenceSuffix("Fix");
        sequence.setSequenceCurrNo(1);
        sequence.setSequenceStep(2);
        sequence.setSequenceLength(10);
        PowerMockito.when(sequenceRepository.selectByPrimaryKey(Mockito.anyString())).thenReturn(sequence);
        sequenceService.getSequenceWithOutDate("111");
        sequence.setYear(2025);
        sequence.setMonth(12);
        sequence.setDay(1);
        sequence.setSequencePrefix("");
        sequence.setSequenceSuffix("");
        sequence.setSequenceStep(2147483646);
        PowerMockito.when(sequenceRepository.updateByPrimaryKeyAndVersion(Mockito.any())).thenReturn(1);
        String sequenceWithDate = sequenceService.getSequenceWithOutDate("111");
        Assert.assertTrue(Objects.nonNull(sequenceWithDate));
    }
}
/* Ended by AICoder, pid:j4fce23162o40ef14d8a0885b0503b005b31c1a1 */