package com.zte.application.infor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.CarrierGpsServiceImpl;
import com.zte.common.utils.InteractiveB2B;
import com.zte.interfaces.infor.dto.CarrierGpsDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, HttpClientUtil.class})
public class CarrierGpsServiceImplTest {

    @InjectMocks
    private CarrierGpsServiceImpl carrierGpsServiceImpl;
    @Mock
    private InteractiveB2B pushDataHandler;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, HttpClientUtil.class);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(pushDataHandler.getRequestBody(anyString())).thenReturn("EXPECTED_RETURN_VALUE");
        ReflectionTestUtils.setField(carrierGpsServiceImpl, "pushDataHandler", pushDataHandler);
        ReflectionTestUtils.setField(carrierGpsServiceImpl, "b2bUrl", "https://b2b.test.zte.com.cn/gateway/common/1.0/api/receive");
    }

    @Test
    public void testCarrierGpsB2B() {
        int initialCapacity = 16;
        String waybillNo = "SF7000301138071";
        Map<String, Object> dataMap = new HashMap<>(initialCapacity);
        dataMap.put("waybillNo", waybillNo);

        Map<String, Object> jsonMsgContentMap = new HashMap<>(initialCapacity);
        jsonMsgContentMap.put("method", "faceToCustomerTrajectoryQuery");
        jsonMsgContentMap.put("data", dataMap);

        Mockito.when(pushDataHandler.getRequestBody(Mockito.any())).thenReturn(jsonMsgContentMap);

        try {
            List<CarrierGpsDTO> carrierGpsDTOS = carrierGpsServiceImpl.carrierGpsB2B(waybillNo);
            assertNull(carrierGpsDTOS);
        } catch (Exception e) {
            throw new RuntimeException("Test failed due to an exception: " + e.getMessage(), e);
        }
    }

    @Test
    public void testCarrierGpsB2B_Success() {
        String waybillNo = "SF7000301138071";
        Map<String, Object> jsonMsgContentMap = new HashMap<>();
        jsonMsgContentMap.put("method", "faceToCustomerTrajectoryQuery");
        jsonMsgContentMap.put("data", new HashMap<String, Object>() {{
            put("waybillNo", waybillNo);
        }});

        // 模拟成功返回数据
        String jsonResponse = "{\"success\":true,\"data\":[{\"location\":\"some location\"}]}";
        Mockito.when(pushDataHandler.getRequestBody(Mockito.any())).thenReturn(jsonMsgContentMap);
        Mockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(jsonResponse);

        String jsonResponseEmpty = "{}";
        Mockito.when(pushDataHandler.getRequestBody(Mockito.any())).thenReturn(jsonMsgContentMap);
        Mockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(jsonResponseEmpty);

        String jsonResponseInvalid = "{\"success\":true,\"data\":[{}]}";
        Mockito.when(pushDataHandler.getRequestBody(Mockito.any())).thenReturn(jsonMsgContentMap);
        Mockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(jsonResponseInvalid);
        try {
            List<CarrierGpsDTO> carrierGpsDTOS = carrierGpsServiceImpl.carrierGpsB2B(waybillNo);
            assertFalse(carrierGpsDTOS.isEmpty());
        } catch (Exception e) {

        }
    }
    @Test
    public void testRealTimeInteractiveB2B() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.field(CarrierGpsServiceImpl.class, "b2bUrl").set(carrierGpsServiceImpl, "https://b2b.test.zte.com.cn/gateway/common/1.0/api/receive");
        String responseStr = "";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(responseStr);
        Assert.assertNull(carrierGpsServiceImpl.carrierGpsB2B(Mockito.any()));
        responseStr = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"6c58a230-2cfb-4b83-b8a5-371ce74abf7d_6c58a230-2cfb-4b83-b8a5-371ce74abf7d\",\n" +
                "        \"msg\": \"ok\"\n" +
                "    },\n" +
                "    \"bo\": \"{}\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(responseStr);
        Assert.assertNull(carrierGpsServiceImpl.carrierGpsB2B(Mockito.any()));
        responseStr = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"6c58a230-2cfb-4b83-b8a5-371ce74abf7d_6c58a230-2cfb-4b83-b8a5-371ce74abf7d\",\n" +
                "        \"msg\": \"ok\"\n" +
                "    },\n" +
                "    \"bo\": \"{\\\"requestId\\\":null,\\\"success\\\":true,\\\"business\\\":null,\\\"errorCode\\\":null,\\\"errorMessage\\\":null,\\\"params\\\":null,\\\"date\\\":null,\\\"version\\\":null,\\\"obj\\\":{}}\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(responseStr);
        Assert.assertNull(carrierGpsServiceImpl.carrierGpsB2B(Mockito.any()));
        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"6c58a230-2cfb-4b83-b8a5-371ce74abf7d_6c58a230-2cfb-4b83-b8a5-371ce74abf7d\",\"msg\":\"ok\"}," +
                "\"bo\":\"{\\\"requestId\\\":null,\\\"success\\\":true,\\\"business\\\":null,\\\"errorCode\\\":null,\\\"errorMessage\\\":null,\\\"params\\\":null,\\\"date\\\":null,\\\"version\\\":null," +
                "\\\"obj\\\":{\\\"realCoordinates\\\":[{\\\"devicecode\\\":\\\"804AB0CA-219F-4214-AC2C-5E96267D2192\\\",\\\"gpslongitude\\\":112.92431,\\\"gpslatitude\\\":28.904463,\\\"gpsvelocity\\\":0,\\\"maplongitude\\\":null,\\\"maplatitude\\\":null,\\\"altitude\\\":null,\\\"vehiclecode\\\":\\\"粤CJ1126\\\",\\\"direction\\\":null,\\\"gpstime\\\":1724814026536},{\\\"devicecode\\\":\\\"804AB0CA-219F-4214-AC2C-5E96267D2192\\\",\\\"gpslongitude\\\":112.92431,\\\"gpslatitude\\\":28.804462,\\\"gpsvelocity\\\":0,\\\"maplongitude\\\":null,\\\"maplatitude\\\":null,\\\"altitude\\\":null,\\\"vehiclecode\\\":\\\"粤CJ1126\\\",\\\"direction\\\":null,\\\"gpstime\\\":1724814056995}],\\\"waybillNo\\\":\\\"SF7000301138071\\\",\\\"channelOrderId\\\":null}}\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(responseStr);
        Assert.assertNotNull(carrierGpsServiceImpl.carrierGpsB2B(Mockito.any()));
    }
    @Test
    public void testRealTimeInteractiveB2B_readTreeThrowsException()  {
        String responseStr = "{\"bo\": \"valid_bo\"}";
        try {
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance().readTree(responseStr)).thenThrow(new RuntimeException("Simulated exception"));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(responseStr);
        List<CarrierGpsDTO> result = null;
        try {
            result = carrierGpsServiceImpl.realTimeInteractiveB2B(new HashMap<>());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Assert.assertNull(result);
    }
}