/*Started by AICoder, pid:4ce5556701f3450fbb4038d93166381c*/
package com.zte.application.infor;
/* Started by AICoder, pid:449aa2e81761418ebd839dc717acd448 */
// serviceImpl实现类

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.OverdueMaterialsServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.OverdueMaterialsRepository;
import com.zte.interfaces.infor.dto.OverdueMaterialsDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, Executor.class, ExcelUtil.class, EmailUtil.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, Tools.class, HttpClientUtil.class, JacksonJsonConverUtil.class})
public class OverdueMaterialsServiceImplTest {
    @InjectMocks
    private OverdueMaterialsServiceImpl overdueMaterialsServiceImpl;
    @Mock
    private OverdueMaterialsRepository overdueMaterialsRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private EmailUtil emailUtil;

    @Mock
    private ObjectMapper mapperInstance;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, RemoteServiceDataUtil.class, EmailUtil.class, FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, HttpClientUtil.class);
    }

    @Test
    public void query() throws Exception {
        // Given
        OverdueMaterialsDTO overdueMaterialsDTO = new OverdueMaterialsDTO();
        overdueMaterialsDTO.setPageIndex(1);
        overdueMaterialsDTO.setPageSize(10);

        List<OverdueMaterialsDTO> overdueMaterialsList = Arrays.asList(new OverdueMaterialsDTO());
        when(overdueMaterialsRepository.selectOverdueMaterialsListVOTotal(overdueMaterialsDTO)).thenReturn(1);
        when(overdueMaterialsRepository.selectOverdueMaterialsList(overdueMaterialsDTO)).thenReturn(overdueMaterialsList);

        // When
        OverDueMaterialsListVO result = overdueMaterialsServiceImpl.query(overdueMaterialsDTO);
        // Then
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void exportOverdueMaterialsRecord() throws Exception {
        OverdueMaterialsDTO dto = new OverdueMaterialsDTO();
        dto.setItemNo("1111");
        when(overdueMaterialsRepository.selectOverdueMaterialsListVOTotal(any())).thenReturn(10);
        when(overdueMaterialsRepository.selectOverdueMaterialsList(any())).thenReturn(new ArrayList<>());

        // When
        overdueMaterialsServiceImpl.exportOverdueMaterialsRecord(dto);
        Assert.assertTrue(Objects.nonNull(dto));


    }

    @Test
    public void getOverdueSysList() throws Exception {
        SysLookupValuesDTO dto = new SysLookupValuesDTO();
        dto.setLookupType("1000043");
        List<SysLookupValuesDTO> list = new ArrayList<>();
        list.add(dto);
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(list);
        // When
        List<SysLookupValuesDTO> result = overdueMaterialsServiceImpl.getOverdueSysList(dto);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void importOverdueMaterials() throws Exception {
        List<OverdueMaterialsDTO> list = new ArrayList<>();
        overdueMaterialsServiceImpl.importOverdueMaterials(list, "00222");
        OverdueMaterialsDTO dto4 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11").setQty(BigDecimal.valueOf(1));
        OverdueMaterialsDTO dto5 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11").setQty(BigDecimal.valueOf(1));
        list.add(dto4);
        list.add(dto5);
        overdueMaterialsServiceImpl.importOverdueMaterials(list, "00222");
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void checkOverdueData() throws Exception {
        List<OverdueMaterialsDTO> list = new ArrayList<>();
        OverdueMaterialsDTO dto1 = OverdueMaterialsDTO.builder().build().setFactory("").setItemNo("11").setQty(BigDecimal.valueOf(1));
        OverdueMaterialsDTO dto2 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("").setQty(BigDecimal.valueOf(1));
        OverdueMaterialsDTO dto3 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11");
        OverdueMaterialsDTO dto4 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11").setQty(BigDecimal.valueOf(1));
        OverdueMaterialsDTO dto5 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11").setQty(BigDecimal.valueOf(1));
        List<OverdueMaterialsDTO> list1 = new ArrayList<>();
        list1.add(dto1);
        overdueMaterialsServiceImpl.checkOverdueData(list1);

        List<OverdueMaterialsDTO> list2 = new ArrayList<>();
        list2.add(dto2);
        overdueMaterialsServiceImpl.checkOverdueData(list2);

        List<OverdueMaterialsDTO> list3 = new ArrayList<>();
        list3.add(dto3);
        overdueMaterialsServiceImpl.checkOverdueData(list3);

        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        list.add(dto4);
        list.add(dto5);
        overdueMaterialsServiceImpl.checkOverdueData(list);
        list = new ArrayList<>();
        OverdueMaterialsDTO dto6 = OverdueMaterialsDTO.builder().build().setFactory("11111").setItemNo("222222222222").setQty(BigDecimal.valueOf(1));
        list.add(dto6);
        overdueMaterialsServiceImpl.checkOverdueData(list);
        list = new ArrayList<>();
        dto4 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("11").setQty(BigDecimal.valueOf(1));
        dto5 = OverdueMaterialsDTO.builder().build().setFactory("11").setItemNo("12").setQty(BigDecimal.valueOf(1));
        list.add(dto4);
        list.add(dto5);
        overdueMaterialsServiceImpl.checkOverdueData(list);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void overdueMaterialCalc() throws Exception {
        List<OverDueMaterialsEmailListVO> overList = new ArrayList<>();
        OverDueMaterialsEmailListVO vo = OverDueMaterialsEmailListVO.builder().build().setEmailType("1");
        overList.add(vo);
        when(overdueMaterialsRepository.selectOverdueMaterialItemList(20000)).thenReturn(null);
        overdueMaterialsServiceImpl.overdueMaterialCalc("00222");
        when(overdueMaterialsRepository.selectOverdueMaterialItemList(20000)).thenReturn(overList);
        overdueMaterialsServiceImpl.overdueMaterialCalc("00222");

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_OVERDUE_MATERIAL_MONITOR);
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO valuesDTO=new SysLookupValuesDTO();
        valuesDTO.setLookupType(Constant.LOOKUP_TYPE_1000059);
        valuesDTO.setLookupCode(Constant.LOOKUP_CODE_100005900002);
        valuesDTO.setLookupMeaning("30000");
        sysLookupValuesDTOList.add(valuesDTO);
        when(inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO)).thenReturn(sysLookupValuesDTOList);
        when(overdueMaterialsRepository.selectOverdueMaterialItemList(20000)).thenReturn(overList);
        overdueMaterialsServiceImpl.overdueMaterialCalc("00222");

        List<SysLookupValuesDTO> sysLookupValuesDTOList1=new ArrayList<>();
        SysLookupValuesDTO valuesDTO1=new SysLookupValuesDTO();
        valuesDTO1.setLookupType(Constant.LOOKUP_TYPE_1000059);
        valuesDTO1.setLookupCode(Constant.LOOKUP_CODE_100005900001);
        valuesDTO1.setLookupMeaning("30000");
        sysLookupValuesDTOList1.add(valuesDTO1);
        when(inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO)).thenReturn(sysLookupValuesDTOList1);
        when(overdueMaterialsRepository.selectOverdueMaterialItemList(20000)).thenReturn(overList);
        overdueMaterialsServiceImpl.overdueMaterialCalc("00222");
        Assert.assertTrue(Objects.nonNull(vo));
    }

    @Test
    public void dealPlanData() throws Exception {
        PowerMockito.mockStatic(Tools.class);
        List<OverDueMaterialsEmailListVO> overList=new ArrayList<>();
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("深圳");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        overList.add(dto);
        String xEmpNo="00222";
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        String isEnableLead="1";
        String responseStr = "{\"code\":{\"code\":\"0400\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        PowerMockito.when(Tools.isNotEmpty(Mockito.anyString())).thenReturn(false);
        overdueMaterialsServiceImpl.dealPlanData(overList,xEmpNo,isProdplan,isEnableLead);
        Assert.assertTrue(true);
    }

    @Test
    public void overdueRequireCalc() throws Exception {
        List<String> itemNoList=new ArrayList<>();
        List<OverDueMaterialsEmailListVO> overRequireList=new ArrayList<>();
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,null,null);

        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        itemNoList.add("00222");
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("深圳");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        overRequireList.add(dto);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(null);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        OverDueMaterialsEmailListVO dto10=new OverDueMaterialsEmailListVO();
        dto10.setItemNo("00223");
        dto10.setWhseId("深圳");
        dto10.setLottable08("10");
        dto10.setEmailType("1");
        dto10.setItemBarcode("002220000001");
        dto10.setFefo("0");
        dto10.setQty(new BigDecimal(10));
        List<OverDueMaterialsEmailListVO> overRequireList10=new ArrayList<>();
        overRequireList10.add(dto10);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList10);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList10,null);

        OverDueMaterialsEmailListVO dto11=new OverDueMaterialsEmailListVO();
        dto11.setItemNo("00222");
        dto11.setWhseId("深圳");
        dto11.setLottable08("10");
        dto11.setEmailType("1");
        dto11.setItemBarcode("002220000001");
        dto11.setFefo("0");
        dto11.setQty(new BigDecimal(10));
        List<OverDueMaterialsEmailListVO> overRequireList11=new ArrayList<>();
        overRequireList11.add(dto11);
        OverDueMaterialsEmailListVO dto12=new OverDueMaterialsEmailListVO();
        dto12.setItemNo("00222");
        dto12.setWhseId("长沙");
        dto12.setLottable08("10");
        dto12.setEmailType("1");
        dto12.setItemBarcode("002220000001");
        dto12.setFefo("0");
        dto12.setQty(new BigDecimal(10));
        List<OverDueMaterialsEmailListVO> overRequireList12=new ArrayList<>();
        overRequireList12.add(dto12);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList11);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList12,null);

        List<OverDueMaterialsEmailListVO> overRequireList1=new ArrayList<>();
        OverDueMaterialsEmailListVO dto1=new OverDueMaterialsEmailListVO();
        dto1.setItemNo("0022223");
        dto1.setWhseId("深圳2");
        dto1.setLottable08("102");
        dto1.setEmailType("1");
        dto1.setItemBarcode("00222000000132");
        dto1.setFefo("0");
        dto1.setQty(new BigDecimal(1000));
        overRequireList1.add(dto1);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList1);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        List<OverDueMaterialsEmailListVO> overRequireList2=new ArrayList<>();
        OverDueMaterialsEmailListVO dto2=new OverDueMaterialsEmailListVO();
        dto2.setItemNo("00222");
        dto2.setWhseId("");
        dto2.setLottable08("10");
        dto2.setEmailType("1");
        dto2.setItemBarcode("002220000001");
        dto2.setFefo("0");
        dto2.setQty(new BigDecimal(10));
        overRequireList2.add(dto2);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList2);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList2,null);

        List<OverDueMaterialsEmailListVO> overRequireList3=new ArrayList<>();
        OverDueMaterialsEmailListVO dto3=new OverDueMaterialsEmailListVO();
        dto3.setItemNo("00222");
        dto3.setWhseId("深圳");
        dto3.setLottable08("10");
        dto3.setEmailType("1");
        dto3.setItemBarcode("002220000001");
        dto3.setFefo("0");
        dto3.setQty(new BigDecimal(1000));
        overRequireList3.add(dto3);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList3);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        List<OverDueMaterialsEmailListVO> overRequireList4=new ArrayList<>();
        OverDueMaterialsEmailListVO dto4=new OverDueMaterialsEmailListVO();
        dto4.setItemNo("00222");
        dto4.setWhseId("深圳12323");
        dto4.setLottable08("10");
        dto4.setEmailType("1");
        dto4.setItemBarcode("002220000001");
        dto4.setFefo("0");
        dto4.setQty(new BigDecimal(10));
        overRequireList4.add(dto4);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList4);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList,null);

        List<OverDueMaterialsEmailListVO> overRequireList5=new ArrayList<>();
        OverDueMaterialsEmailListVO dto5=new OverDueMaterialsEmailListVO();
        dto5.setItemNo("00222");
        dto5.setWhseId("深圳");
        dto5.setLottable08("10");
        dto5.setEmailType("1");
        dto5.setItemBarcode("002220000001");
        dto5.setFefo("1");
        dto5.setQty(new BigDecimal(1));
        dto5.setLottable12(new Date());
        overRequireList5.add(dto5);
        OverDueMaterialsEmailListVO dto6=new OverDueMaterialsEmailListVO();
        dto6.setItemNo("00222");
        dto6.setWhseId("深圳");
        dto6.setLottable08("10");
        dto6.setEmailType("1");
        dto6.setItemBarcode("002220000002");
        dto6.setFefo("1");
        dto6.setQty(new BigDecimal(1));
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.MINUTE, -1);
        Date newDate = calendar.getTime();
        dto6.setLottable12(newDate);
        overRequireList5.add(dto6);
        List<OverDueMaterialsEmailListVO> recheckList=new ArrayList<>();
        OverDueMaterialsEmailListVO emailListVO=new OverDueMaterialsEmailListVO();
        emailListVO.setWhseId("深圳");
        emailListVO.setItemBarcode("002220000001");
        emailListVO.setItemNo("00222");
        emailListVO.setQcStatus("复检中");
        emailListVO.setQty(new BigDecimal(12));
        recheckList.add(emailListVO);
        when(overdueMaterialsRepository.selectOverdueRequireList()).thenReturn(overRequireList5);
        overdueMaterialsServiceImpl.overdueRequireCalc(itemNoList,overRequireList5,recheckList);
        Assert.assertTrue(Objects.nonNull(overRequireList));
    }

    @Test
    public void getRecheckMaterialItemList(){
        List<List<String>> splitList=new ArrayList<>();
        List<String> list=new ArrayList<>();
        list.add("00222");
        splitList.add(list);
        List<OverDueMaterialsEmailListVO> allRecheckList=new ArrayList<>();
        when(overdueMaterialsRepository.selectRecheckMaterialItemList(anyList())).thenReturn(null);
        overdueMaterialsServiceImpl.getRecheckMaterialItemList(splitList,allRecheckList);

        OverDueMaterialsEmailListVO vo=new OverDueMaterialsEmailListVO();
        vo.setQty(new BigDecimal(12));
        allRecheckList.add(vo);
        when(overdueMaterialsRepository.selectRecheckMaterialItemList(anyList())).thenReturn(allRecheckList);
        overdueMaterialsServiceImpl.getRecheckMaterialItemList(splitList,allRecheckList);
        Assert.assertTrue(true);
    }

    @Test
    public void overdueMaterialMatchBarcode() throws Exception {
        List<PlanKitItemListVo> planList=new ArrayList<>();
        PlanKitItemListVo vo=new PlanKitItemListVo();
        vo.setItemNo("00222");
        vo.setPlanOrgName("南京");
        vo.setIsLead("10");
        vo.setReqQty(10);
        planList.add(vo);
        List<OverDueMaterialsEmailListVO> overRequireList=new ArrayList<>();
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("WMWHSE26");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        overRequireList.add(dto);
        List<String> list = new ArrayList<>();
        list.add("WMWHSE26");
        when(overdueMaterialsRepository.selectOverdueMaterialWhseId(planList.get(INT_0).getItemNo())).thenReturn(null);
        overdueMaterialsServiceImpl.overdueMaterialMatchBarcode(overRequireList,"0",planList,null);

        List<PlanKitItemListVo> planList1=new ArrayList<>();
        PlanKitItemListVo vo1=new PlanKitItemListVo();
        vo1.setItemNo("00222");
        vo1.setPlanOrgName("深圳");
        vo1.setIsLead("");
        vo1.setReqQty(10);
        planList1.add(vo1);
        when(overdueMaterialsRepository.selectOverdueMaterialWhseId(planList1.get(INT_0).getItemNo())).thenReturn(list);
        overdueMaterialsServiceImpl.overdueMaterialMatchBarcode(overRequireList,"0",planList1,null);

        List<PlanKitItemListVo> planList2=new ArrayList<>();
        PlanKitItemListVo vo2=new PlanKitItemListVo();
        vo2.setItemNo("00222");
        vo2.setPlanOrgName("深圳");
        vo2.setIsLead("");
        vo2.setReqQty(10);
        planList2.add(vo2);
        when(overdueMaterialsRepository.selectOverdueMaterialWhseId(planList2.get(INT_0).getItemNo())).thenReturn(list);
        List<OverDueMaterialsLeadVO> isLeadList=new ArrayList<>();
        OverDueMaterialsLeadVO vo3=new OverDueMaterialsLeadVO();
        vo3.setDescription("10");
        isLeadList.add(vo3);
        when(overdueMaterialsRepository.selectOverdueMaterialIsLead(any())).thenReturn(isLeadList);
        overdueMaterialsServiceImpl.overdueMaterialMatchBarcode(overRequireList,"0",planList2,null);
        Assert.assertTrue(Objects.nonNull(overRequireList));
    }

    @Test
    public void overdueMaterialCalcBarcode() throws Exception {
        List<PlanKitItemListVo> planList=new ArrayList<>();
        PlanKitItemListVo vo=new PlanKitItemListVo();
        vo.setItemNo("00222");
        vo.setWhseId("WMWHSE26");
        vo.setIsLead("20");
        vo.setReqQty(10);
        planList.add(vo);
        PlanKitItemListVo vo1=new PlanKitItemListVo();
        vo1.setItemNo("00222");
        vo1.setWhseId("WMWHSE26");
        vo1.setIsLead("10");
        vo1.setReqQty(5);
        planList.add(vo1);
        List<OverDueMaterialsEmailListVO> overRequireList=new ArrayList<>();
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("WMWHSE26");
        dto.setLottable08("20");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(25));
        overRequireList.add(dto);
        OverDueMaterialsEmailListVO dto1=new OverDueMaterialsEmailListVO();
        dto1.setItemNo("00222");
        dto1.setWhseId("WMWHSE26");
        dto1.setLottable08("10");
        dto1.setEmailType("1");
        dto1.setItemBarcode("002220000002");
        dto1.setFefo("0");
        dto1.setQty(new BigDecimal(5));
        overRequireList.add(dto1);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList,"1",planList,null);

        List<OverDueMaterialsEmailListVO> overRequireList2=new ArrayList<>();
        OverDueMaterialsEmailListVO dto4=new OverDueMaterialsEmailListVO();
        dto4.setItemNo("00222");
        dto4.setWhseId("WMWHSE26");
        dto4.setLottable08("20");
        dto4.setEmailType("2");
        dto4.setItemBarcode("002220000001");
        dto4.setFefo("0");
        dto4.setQty(new BigDecimal(25));
        overRequireList2.add(dto4);
        OverDueMaterialsEmailListVO dto5=new OverDueMaterialsEmailListVO();
        dto5.setItemNo("00222");
        dto5.setWhseId("WMWHSE26");
        dto5.setLottable08("10");
        dto5.setEmailType("2");
        dto5.setItemBarcode("002220000002");
        dto5.setFefo("0");
        dto5.setQty(new BigDecimal(5));
        overRequireList2.add(dto5);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList2,"1",planList,null);

        List<OverDueMaterialsEmailListVO> overRequireList3=new ArrayList<>();
        OverDueMaterialsEmailListVO dto6=new OverDueMaterialsEmailListVO();
        dto6.setItemNo("00222");
        dto6.setWhseId("WMWHSE26");
        dto6.setLottable08("20");
        dto6.setEmailType("1");
        dto6.setItemBarcode("002220000001");
        dto6.setFefo("0");
        dto6.setQty(new BigDecimal(2));
        overRequireList3.add(dto6);
        OverDueMaterialsEmailListVO dto7=new OverDueMaterialsEmailListVO();
        dto7.setItemNo("00222");
        dto7.setWhseId("WMWHSE26");
        dto7.setLottable08("10");
        dto7.setEmailType("1");
        dto7.setItemBarcode("002220000001");
        dto7.setFefo("0");
        dto7.setQty(new BigDecimal(5));
        overRequireList3.add(dto7);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList3,"1",planList,null);

        List<PlanKitItemListVo> planList1=new ArrayList<>();
        PlanKitItemListVo vo2=new PlanKitItemListVo();
        vo2.setItemNo("00222");
        vo2.setWhseId("WMWHSE26");
        vo2.setIsLead("20");
        vo2.setReqQty(10);
        vo2.setProdPlanNo("A");
        planList1.add(vo2);
        PlanKitItemListVo vo3=new PlanKitItemListVo();
        vo3.setItemNo("00222");
        vo3.setWhseId("WMWHSE26");
        vo3.setIsLead("10");
        vo3.setReqQty(5);
        vo3.setProdPlanNo("B");
        planList1.add(vo3);
        PlanKitItemListVo vo4=new PlanKitItemListVo();
        vo4.setItemNo("00222");
        vo4.setWhseId("WMWHSE26");
        vo4.setIsLead("10");
        vo4.setReqQty(5);
        vo4.setProdPlanNo("C");
        planList1.add(vo4);
        List<OverDueMaterialsEmailListVO> overRequireList1=new ArrayList<>();
        OverDueMaterialsEmailListVO dto2=new OverDueMaterialsEmailListVO();
        dto2.setItemNo("00222");
        dto2.setWhseId("WMWHSE26");
        dto2.setLottable08("20");
        dto2.setEmailType("1");
        dto2.setItemBarcode("002220000001");
        dto2.setLottable12(new Date());
        dto2.setFefo("1");
        dto2.setQty(new BigDecimal(25));
        overRequireList1.add(dto2);
        OverDueMaterialsEmailListVO dto3=new OverDueMaterialsEmailListVO();
        dto3.setItemNo("00222");
        dto3.setWhseId("WMWHSE26");
        dto3.setLottable08("10");
        dto3.setEmailType("1");
        dto3.setItemBarcode("002220000002");
        dto3.setLottable12(new Date());
        dto3.setFefo("1");
        dto3.setQty(new BigDecimal(5));
        overRequireList1.add(dto3);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList1,"0",planList1,null);

        List<OverDueMaterialsEmailListVO> overRequireList4=new ArrayList<>();
        OverDueMaterialsEmailListVO dto10=new OverDueMaterialsEmailListVO();
        dto10.setItemNo("0022246");
        dto10.setWhseId("WMWHSE26");
        dto10.setLottable08("20");
        dto10.setEmailType("1");
        dto10.setItemBarcode("002220000001");
        dto10.setFefo("0");
        dto10.setQty(new BigDecimal(2));
        overRequireList4.add(dto10);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList4,"1",planList,null);

        List<OverDueMaterialsEmailListVO> overRequireList5=new ArrayList<>();
        OverDueMaterialsEmailListVO dto11=new OverDueMaterialsEmailListVO();
        dto11.setItemNo("00222");
        dto11.setWhseId("WMWHSE27");
        dto11.setLottable08("10");
        dto11.setEmailType("1");
        dto11.setItemBarcode("002220000001");
        dto11.setFefo("0");
        dto11.setQty(new BigDecimal(5));
        overRequireList5.add(dto11);
        List<OverDueMaterialsEmailListVO> recheckList=new ArrayList<>();
        OverDueMaterialsEmailListVO emailListVO=new OverDueMaterialsEmailListVO();
        emailListVO.setWhseId("WMWHSE26");
        emailListVO.setItemBarcode("002220000001");
        emailListVO.setItemNo("00222");
        emailListVO.setLottable08("20");
        emailListVO.setQcStatus("复检中");
        emailListVO.setQty(new BigDecimal(12));
        recheckList.add(emailListVO);
        overdueMaterialsServiceImpl.overdueMaterialCalcBarcode(overRequireList5,"1",planList,recheckList);
        Assert.assertTrue(Objects.nonNull(overRequireList1));
    }

    @Test
    public void recheckOverdueRequireCalc() throws Exception{
        Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo=new HashMap<>();
        OverDueMaterialsEmailListVO currReqQty=new OverDueMaterialsEmailListVO();
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList=new ArrayList<>();
        overdueMaterialsServiceImpl.recheckOverdueRequireCalc(recheckListByItemNo,currReqQty,currOverDueMaterialsEmailList);

        currReqQty.setItemNo("00222");
        List<OverDueMaterialsEmailListVO> list=new ArrayList<>();
        OverDueMaterialsEmailListVO vo=new OverDueMaterialsEmailListVO();
        vo.setItemNo("00222");
        vo.setWhseId("WMWHSE26");
        vo.setItemBarcode("0022200001");
        vo.setQty(new BigDecimal(12));
        list.add(vo);
        recheckListByItemNo.put("00222",list);
        currOverDueMaterialsEmailList.add(vo);
        overdueMaterialsServiceImpl.recheckOverdueRequireCalc(recheckListByItemNo,currReqQty,currOverDueMaterialsEmailList);

        currReqQty.setWhseId("WMWHSE27");
        overdueMaterialsServiceImpl.recheckOverdueRequireCalc(recheckListByItemNo,currReqQty,currOverDueMaterialsEmailList);
        Assert.assertTrue(Objects.nonNull(vo.getQty()));
    }

    @Test
    public void recheckMaterialCalc() throws Exception{
        String isEnableLead ="0";
        PlanKitItemListVo item=new PlanKitItemListVo();
        List<OverDueMaterialsEmailListVO> recheckList=new ArrayList<>();
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList=new ArrayList<>();
        overdueMaterialsServiceImpl.recheckMaterialCalc(isEnableLead,item,recheckList,currOverDueMaterialsEmailList);

        item.setItemNo("00222");
        item.setWhseId("WMWHSE27");
        item.setIsLead("10");
        List<OverDueMaterialsEmailListVO> list=new ArrayList<>();
        OverDueMaterialsEmailListVO vo=new OverDueMaterialsEmailListVO();
        vo.setItemNo("00222");
        vo.setWhseId("WMWHSE26");
        vo.setItemBarcode("0022200001");
        vo.setQty(new BigDecimal(12));
        vo.setLottable08("10");
        list.add(vo);
        currOverDueMaterialsEmailList.add(vo);
        recheckList.add(vo);
        overdueMaterialsServiceImpl.recheckMaterialCalc(isEnableLead,item,recheckList,currOverDueMaterialsEmailList);

        item.setWhseId("WMWHSE26");
        item.setReqQty(10);
        OverDueMaterialsEmailListVO vo1=new OverDueMaterialsEmailListVO();
        vo1.setItemNo("00222");
        vo1.setWhseId("WMWHSE26");
        vo1.setItemBarcode("0022200001");
        vo1.setQty(new BigDecimal(12));
        vo1.setLottable08("10");
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList1=new ArrayList<>();
        currOverDueMaterialsEmailList1.add(vo1);
        overdueMaterialsServiceImpl.recheckMaterialCalc(isEnableLead,item,recheckList,currOverDueMaterialsEmailList1);
        Assert.assertTrue(Objects.nonNull(vo.getQty()));
    }

    @Test
    public void shouldUpdateEmailType() throws Exception {
        OverDueMaterialsEmailListVO t=new OverDueMaterialsEmailListVO();
        t.setLottable08("10");
        t.setEmailType("2");
        boolean isEnableLeadZero=false;
        String itemIsLead="10";
        String whseId="WMWHSE26";
        boolean hasWarehouseFilter=false;
        overdueMaterialsServiceImpl.shouldUpdateEmailType(t,isEnableLeadZero,itemIsLead,whseId,hasWarehouseFilter);

        t.setEmailType("1");
        isEnableLeadZero=true;
        isEnableLeadZero=true;
        overdueMaterialsServiceImpl.shouldUpdateEmailType(t,isEnableLeadZero,itemIsLead,whseId,hasWarehouseFilter);
        Assert.assertTrue(true);
    }

    public void shouldCountInTotal() {
        OverDueMaterialsEmailListVO t=new OverDueMaterialsEmailListVO();
        t.setLottable08("10");
        boolean isEnableLeadZero=false;
        String itemIsLead="10";
        String whseId="WMWHSE26";
        boolean hasWarehouseFilter=false;
        overdueMaterialsServiceImpl.shouldCountInTotal(t,isEnableLeadZero,itemIsLead,whseId,hasWarehouseFilter);
        Assert.assertTrue(true);

        isEnableLeadZero=true;
        isEnableLeadZero=true;
        overdueMaterialsServiceImpl.shouldUpdateEmailType(t,isEnableLeadZero,itemIsLead,whseId,hasWarehouseFilter);
        Assert.assertTrue(true);
    }

    @Test
    public void getOverDueMaterialsEmailListVOS() throws Exception {
        PlanKitItemListVo item =new PlanKitItemListVo();
        item.setItemNo("00222");
        item.setWhseId("WMWHSE26");
        item.setIsLead("10");
        item.setReqQty(10);
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList=new ArrayList<>();
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("WMWHSE26");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        currOverDueMaterialsEmailList.add(dto);
        overdueMaterialsServiceImpl.getOverDueMaterialsEmailListVOS("0",item,currOverDueMaterialsEmailList);

       List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList1=new ArrayList<>();
        OverDueMaterialsEmailListVO dto1=new OverDueMaterialsEmailListVO();
        dto1.setItemNo("00222");
        dto1.setWhseId("WMWHSE26");
        dto1.setLottable08("10");
        dto1.setEmailType("2");
        dto1.setItemBarcode("002220000001");
        dto1.setFefo("0");
        dto1.setQty(new BigDecimal(10));
        currOverDueMaterialsEmailList1.add(dto1);
        overdueMaterialsServiceImpl.getOverDueMaterialsEmailListVOS("0",item,currOverDueMaterialsEmailList1);

        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList2=new ArrayList<>();
        OverDueMaterialsEmailListVO dto2=new OverDueMaterialsEmailListVO();
        dto2.setItemNo("00222");
        dto2.setWhseId("WMWHSE27");
        dto2.setLottable08("10");
        dto2.setEmailType("1");
        dto2.setItemBarcode("002220000001");
        dto2.setFefo("0");
        dto2.setQty(new BigDecimal(10));
        currOverDueMaterialsEmailList2.add(dto2);
        overdueMaterialsServiceImpl.getOverDueMaterialsEmailListVOS("0",item,currOverDueMaterialsEmailList2);

        PlanKitItemListVo item1 =new PlanKitItemListVo();
        item1.setItemNo("00222");
        item1.setWhseId("");
        item1.setIsLead("10");
        item1.setReqQty(10);
        overdueMaterialsServiceImpl.getOverDueMaterialsEmailListVOS("0",item1,currOverDueMaterialsEmailList2);

        item1.setWhseId("深圳");
        List<OverDueMaterialsEmailListVO> overRequireList5=new ArrayList<>();
        OverDueMaterialsEmailListVO dto5=new OverDueMaterialsEmailListVO();
        dto5.setItemNo("00222");
        dto5.setWhseId("深圳");
        dto5.setLottable08("10");
        dto5.setEmailType("1");
        dto5.setItemBarcode("002220000001");
        dto5.setFefo("1");
        dto5.setQty(new BigDecimal(1));
        dto5.setLottable12(new Date());
        overRequireList5.add(dto5);
        OverDueMaterialsEmailListVO dto6=new OverDueMaterialsEmailListVO();
        dto6.setItemNo("00222");
        dto6.setWhseId("深圳");
        dto6.setLottable08("10");
        dto6.setEmailType("1");
        dto6.setItemBarcode("002220000002");
        dto6.setFefo("1");
        dto6.setQty(new BigDecimal(1));
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.MINUTE, -1);
        Date newDate = calendar.getTime();
        dto6.setLottable12(newDate);
        overRequireList5.add(dto6);
        overdueMaterialsServiceImpl.getOverDueMaterialsEmailListVOS("0",item1,overRequireList5);
        Assert.assertTrue(Objects.nonNull(currOverDueMaterialsEmailList));
    }

    @Test
    public void sortLogic() throws Exception{
        OverDueMaterialsEmailListVO dto2=new OverDueMaterialsEmailListVO();
        dto2.setItemNo("00222");
        dto2.setWhseId("WMWHSE26");
        dto2.setLottable08("20");
        dto2.setEmailType("1");
        dto2.setItemBarcode("002220000001");
        dto2.setLottable12(new Date());
        dto2.setFefo("0");
        dto2.setQty(new BigDecimal(25));

        OverDueMaterialsEmailListVO dto3=new OverDueMaterialsEmailListVO();
        dto3.setItemNo("00222");
        dto3.setWhseId("WMWHSE26");
        dto3.setLottable08("10");
        dto3.setEmailType("1");
        dto3.setItemBarcode("002220000002");
        dto3.setLottable12(new Date());
        dto3.setFefo("0");
        dto3.setQty(new BigDecimal(5));
        overdueMaterialsServiceImpl.sortLogic(dto2,dto3);

        dto2.setFefo("1");
        dto3.setFefo("1");
        overdueMaterialsServiceImpl.sortLogic(dto2,dto3);

        Date a=new Date();
        dto2.setLottable12(a);
        dto3.setLottable12(a);
        overdueMaterialsServiceImpl.sortLogic(dto2,dto3);
        Assert.assertTrue(true);
    }

    @Test
    public void filterAndSortByLead() throws Exception{
        PlanKitItemListVo vo2=new PlanKitItemListVo();
        vo2.setItemNo("00222");
        vo2.setWhseId("WMWHSE26");
        vo2.setIsLead("20");
        vo2.setReqQty(10);
        List<OverDueMaterialsEmailListVO> overRequireList1=new ArrayList<>();
        OverDueMaterialsEmailListVO dto2=new OverDueMaterialsEmailListVO();
        dto2.setItemNo("00222");
        dto2.setWhseId("WMWHSE26");
        dto2.setLottable08("20");
        dto2.setEmailType("1");
        dto2.setItemBarcode("002220000001");
        dto2.setLottable12(new Date());
        dto2.setFefo("1");
        dto2.setQty(new BigDecimal(25));
        overRequireList1.add(dto2);
        OverDueMaterialsEmailListVO dto3=new OverDueMaterialsEmailListVO();
        dto3.setItemNo("00222");
        dto3.setWhseId("WMWHSE26");
        dto3.setLottable08("10");
        dto3.setEmailType("1");
        dto3.setItemBarcode("002220000002");
        dto3.setLottable12(new Date());
        dto3.setFefo("1");
        dto3.setQty(new BigDecimal(5));
        overRequireList1.add(dto3);
        overdueMaterialsServiceImpl.filterAndSortByLead(overRequireList1,vo2);

        PlanKitItemListVo vo3=new PlanKitItemListVo();
        vo3.setItemNo("00222");
        vo3.setWhseId("WMWHSE26");
        vo3.setIsLead("50");
        vo3.setReqQty(10);
        overdueMaterialsServiceImpl.filterAndSortByLead(overRequireList1,vo3);

        PlanKitItemListVo vo4=new PlanKitItemListVo();
        vo4.setItemNo("00222");
        vo4.setWhseId("WMWHSE27");
        vo4.setIsLead("50");
        vo4.setReqQty(10);
        overdueMaterialsServiceImpl.filterAndSortByLead(overRequireList1,vo4);

        List<OverDueMaterialsEmailListVO> overRequireList2=new ArrayList<>();
        OverDueMaterialsEmailListVO dto5=new OverDueMaterialsEmailListVO();
        dto5.setItemNo("00222");
        dto5.setWhseId("WMWHSE26");
        dto5.setLottable08("20");
        dto5.setEmailType("2");
        dto5.setItemBarcode("002220000001");
        dto5.setLottable12(new Date());
        dto5.setFefo("1");
        dto5.setQty(new BigDecimal(25));
        overRequireList2.add(dto5);
        overdueMaterialsServiceImpl.filterAndSortByLead(overRequireList2,vo4);

        List<OverDueMaterialsEmailListVO> overRequireList5=new ArrayList<>();
        OverDueMaterialsEmailListVO dto7=new OverDueMaterialsEmailListVO();
        dto7.setItemNo("00222");
        dto7.setWhseId("WMWHSE27");
        dto7.setLottable08("50");
        dto7.setEmailType("1");
        dto7.setItemBarcode("002220000001");
        dto7.setFefo("1");
        dto7.setQty(new BigDecimal(1));
        dto7.setLottable12(new Date());
        overRequireList5.add(dto7);
        OverDueMaterialsEmailListVO dto6=new OverDueMaterialsEmailListVO();
        dto6.setItemNo("00222");
        dto6.setWhseId("WMWHSE27");
        dto6.setLottable08("50");
        dto6.setEmailType("1");
        dto6.setItemBarcode("002220000002");
        dto6.setFefo("1");
        dto6.setQty(new BigDecimal(1));
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.MINUTE, -1);
        Date newDate = calendar.getTime();
        dto6.setLottable12(newDate);
        overRequireList5.add(dto6);
        overdueMaterialsServiceImpl.filterAndSortByLead(overRequireList5,vo4);
        Assert.assertTrue(Objects.nonNull(overRequireList1));
    }


    @Test
    public void postPlanAsyncApi() throws Exception {
        PowerMockito.mockStatic(Tools.class);
        List<String> itemNoList=new ArrayList<>();
        itemNoList.add("00222");
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        List<OverDueMaterialsEmailListVO> overList=new ArrayList<>();
        String isEnableLead="1";
        String responseStr = "{\"code\":{\"code\":\"0400\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        Map<String, String> planMap = new HashMap<>();
        planMap.put(X_EMP_NO,"00222");
        planMap.put(IS_ENABLE_LEAD,isEnableLead);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<OverDueMaterialsEmailListVO> list = overdueMaterialsServiceImpl.postPlanAsyncApi(itemNoList,isProdplan,overList,planMap,null);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void overdueMaterialWarnEmail() throws Exception {
        String falg = "Y";
        PowerMockito.when(overdueMaterialsRepository.selectOverdueEmailListCount()).thenReturn(0);
        overdueMaterialsServiceImpl.overdueMaterialWarnEmail("00222");
        PowerMockito.when(overdueMaterialsRepository.selectOverdueEmailListCount()).thenReturn(1);
        overdueMaterialsServiceImpl.overdueMaterialWarnEmail("00222");
        Assert.assertTrue(Objects.nonNull(falg));
    }

    @Test
    public void overDueSendEmail() throws Exception {
        String receipts = "00326075";
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO dto = SysLookupValuesDTO.builder().build().setLookupMeaning("00326075");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(null);
        overdueMaterialsServiceImpl.overDueSendEmail(1, "12121");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        overdueMaterialsServiceImpl.overDueSendEmail(1, "12121");

        PowerMockito.mockStatic(SpringContextUtil.class, ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        String expectedFileKey = "fileKey";
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        overdueMaterialsServiceImpl.overDueSendEmail(1, "12121");

        overdueMaterialsServiceImpl.overDueSendEmail(0, "12121");
        Assert.assertTrue(Objects.nonNull(receipts));
    }

    @Test
    public void clodDiskSendEmail() throws Exception {
        String receipts = "00326075";
        String expectedFileKey = "fileKey";
        List<OverDueMaterialsEmailListVO> listException = new ArrayList<>();
        OverDueMaterialsEmailListVO vo = OverDueMaterialsEmailListVO.builder().build().setWhseId("WMWHSE1").setItemBarcode("220")
                .setPageIndex(1).setPageSize(100);
        listException.add(vo);
        PowerMockito.mockStatic(SpringContextUtil.class, ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        overdueMaterialsServiceImpl.clodDiskSendEmail(receipts, "121221");
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.isEmpty(Mockito.anyString())).thenReturn(false);
        overdueMaterialsServiceImpl.clodDiskSendEmail(receipts, "121221");
        Assert.assertTrue(Objects.nonNull(receipts));
    }

    @Test
    public void postPlanAsyncPage() throws Exception {
        List<String> itemNoList=new ArrayList<>();
        itemNoList.add("11");
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(X_EMP_NO, "xEmpNo");
        headerParamsMap.put(INONE_APPCODE, "inoneAppcode");
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, null,1);

        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        String responseStr = "{\"code\":{\"code\":\"0400\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, isProdplan,1);

        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, isProdplan,1);
        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 3,\"rows\": []},\"other\": null,\"responseRule\": \"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, isProdplan,1);
        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 3,\"rows\": [{\"planOrgName\": \"深圳\"}]},\"other\": null,\"responseRule\": \"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        PlanKitItemListPageVo result = overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, isProdplan,1);

        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        // 模拟HTTP响应
        responseStr = "{}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = null;
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        overdueMaterialsServiceImpl.postPlanAsyncPage(headerParamsMap,itemNoList, isProdplan,1);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void createHeaderParamsMap() throws Exception {
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(X_EMP_NO, "xEmpNo");
        headerParamsMap.put(INONE_APPCODE, "inoneAppcode");
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        overdueMaterialsServiceImpl.createHeaderParamsMap("23");
        Assert.assertTrue(Objects.nonNull(headerParamsMap));
    }

    @Test
    public void fetchAllPlanList() throws Exception {
        PowerMockito.mockStatic(Tools.class);
        List<String> itemNoList=new ArrayList<>();
        itemNoList.add("11");
        List<List<String>> splitList =new ArrayList<>();
        splitList.add(itemNoList);
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(X_EMP_NO, "xEmpNo");
        headerParamsMap.put(INONE_APPCODE, "inoneAppcode");
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        String responseStr = "{\"code\":{\"code\":\"0400\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        PowerMockito.when(Tools.isNotEmpty(Mockito.anyString())).thenReturn(false);
        List<PlanKitItemListVo> result = overdueMaterialsServiceImpl.fetchAllPlanList(splitList,headerParamsMap, isProdplan);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void fetchAllPlanListTest() throws Exception {
        List<String> itemNoList=new ArrayList<>();
        itemNoList.add("11");
        List<List<String>> splitList =new ArrayList<>();
        splitList.add(itemNoList);
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(X_EMP_NO, "xEmpNo");
        headerParamsMap.put(INONE_APPCODE, "inoneAppcode");
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 100003,\"rows\": [{\"planOrgName\": \"深圳\"}]},\"other\": null,\"responseRule\": \"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<PlanKitItemListVo> result = overdueMaterialsServiceImpl.fetchAllPlanList(splitList,headerParamsMap, isProdplan);
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void postPlanAsyncApiTest() throws Exception {
        List<String> itemNoList=new ArrayList<>();
        itemNoList.add("00222");
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        List<OverDueMaterialsEmailListVO> overList=new ArrayList<>();
        String isEnableLead="1";
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 3,\"rows\": [{\"planOrgName\": \"深圳\",\"itemNo\": \"00222\",\"isLead\": \"10\",\"reqQty\": \"10\"}]},\"other\": null,\"responseRule\": \"msa\"}";
        Map<String, String> planMap = new HashMap<>();
        planMap.put(X_EMP_NO,"00222");
        planMap.put(IS_ENABLE_LEAD,isEnableLead);

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        List<OverDueMaterialsEmailListVO> list = overdueMaterialsServiceImpl.postPlanAsyncApi(itemNoList, isProdplan,overList,planMap,null);

        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("深圳");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        overList.add(dto);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.postPlanAsyncApi(itemNoList, isProdplan,overList,planMap,null);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void dealPlanDataTest() throws Exception {
        List<OverDueMaterialsEmailListVO> overList=new ArrayList<>();
        OverDueMaterialsEmailListVO dto=new OverDueMaterialsEmailListVO();
        dto.setItemNo("00222");
        dto.setWhseId("深圳");
        dto.setLottable08("10");
        dto.setEmailType("1");
        dto.setItemBarcode("002220000001");
        dto.setFefo("0");
        dto.setQty(new BigDecimal(10));
        overList.add(dto);
        String xEmpNo="00222";
        List<String> isProdplan=new ArrayList<>();
        isProdplan.add("1");
        isProdplan.add("2");
        isProdplan.add("-1");
        String isEnableLead="1";
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 3,\"rows\": [{\"planOrgName\": \"深圳\",\"itemNo\": \"00222\",\"isLead\": \"10\",\"reqQty\": \"10\"}]},\"other\": null,\"responseRule\": \"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.dealPlanData(overList,xEmpNo,isProdplan,isEnableLead);

        List<OverDueMaterialsEmailListVO> overList1=new ArrayList<>();
        dto.setItemNo("0023232422");
        overList1.add(dto);
        responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"RetCode.Success\",\"msg\": \"Success\"},\"bo\": {\"current\": 1,\"total\": 3,\"rows\": [{\"planOrgName\": \"深圳\",\"itemNo\": \"00222\",\"isLead\": \"10\",\"reqQty\": \"10\"}]},\"other\": null,\"responseRule\": \"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        overdueMaterialsServiceImpl.dealPlanData(overList1,xEmpNo,isProdplan,isEnableLead);
        Assert.assertTrue(true);
    }
}
/*Ended by AICoder, pid:449aa2e81761418ebd839dc717acd448*/