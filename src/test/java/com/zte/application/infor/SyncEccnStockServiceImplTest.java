package com.zte.application.infor;
import com.zte.BaseTestCase;
import com.zte.application.infor.impl.SyncEccnStockDataServiceImpl;
import com.zte.domain.model.infor.EccnStockRepository;
import com.zte.interfaces.infor.dto.EccnStock;
import com.zte.interfaces.infor.dto.EccnStockDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 */
public class SyncEccnStockServiceImplTest extends BaseTestCase {
    @InjectMocks
    private SyncEccnStockDataServiceImpl eccnStockServiceImpl;
    @Mock
    private EccnStockRepository eccnStockRepository;;

    @Test
    public void syncEccnStockData(){
        EccnStock dto = null;
        String runNormal = "Y";
        try {
            eccnStockServiceImpl.syncEccnStockData();
            Assert.assertTrue(Objects.nonNull(runNormal));
        }
        catch (Exception e)
        {
            return ;
        }
    }

    @Test
    public void dealSynEccnDataTest(){
        String endDate = "2019-01-23 20:03:17";
        String startDate = "2020-01-23 20:03:17";
        long total = 1;
        Map<String, Object> totalMap = new HashMap<>();
        totalMap.put("total",1);
        try {
            eccnStockServiceImpl.dealSynEccnData(endDate,startDate,total,totalMap);
            Assert.assertTrue(Objects.nonNull(endDate));
        }
        catch (Exception e)
        {
            return ;
        }
    }

    @Test
    public void setEccnParamsTest(){
        long currentPage = 1;
        String startDate = "2019-01-23 20:03:17";
        String endDate = "2020-01-23 20:03:17";
        try {
            String test = eccnStockServiceImpl.setEccnParams(currentPage,endDate,startDate);
            Assert.assertTrue(Objects.nonNull(test));
        }
        catch (Exception e)
        {
            return ;
        }
    }

    @Test
    public void writeEccnRunErrorLogTest(){
        Exception ex = new Exception();
        String runNormal = "Y";
        try {
            eccnStockServiceImpl.writeEccnRunErrorLog(ex);
            Assert.assertTrue(Objects.nonNull(runNormal));
        }
        catch (Exception e)
        {
            return ;
        }
    }


    @Test
    public void eccnSuccessLogsTest(){
        Exception ex = new Exception();
        String endDate = "2019-01-23 20:03:17";
        String startDate = "2020-01-23 20:03:17";
        try {
            eccnStockServiceImpl.eccnSuccessLogs(startDate,endDate);
            Assert.assertTrue(Objects.nonNull(endDate));
        }
        catch (Exception e)
        {
            return ;
        }
    }

    @Test
    public void saveEccnStock(){
        List<EccnStockDTO> eccnStockDTOlist = new LinkedList<>();
        EccnStockDTO dto = new EccnStockDTO();
        dto.setItemNo("180000107211");
        dto.setOrgId("0000");
        dto.setComplianceFlg("YES");
        dto.setEarFlgDM25("YES");
        dto.setDeminimisDM25("0.00%");
        dto.setEarFlgDM10("YES");
        dto.setDeminimisDM10("100.00%");
        dto.setCalculateTime("2019-01-23 20:03:17");
        dto.setPurchasePartFlg("YES");
        dto.setComptime("2021-03-31 10:00:33");
        eccnStockDTOlist.add(dto);
        String lastUpdatedBy = "123";
        try {
            eccnStockServiceImpl.saveEccnStock(eccnStockDTOlist);
            Assert.assertTrue(Objects.nonNull(eccnStockDTOlist));
        }
        catch (Exception e)
        {
            return ;
        }
    }

    @Test
    public void updateEccnStocksTest(){
        List<EccnStock> eccnList = new ArrayList<>();
        EccnStock es = new EccnStock();
        es.setVersion("123");
        eccnList.add(es);
        try {
            eccnStockServiceImpl.updateEccnStocks(eccnList);
            Assert.assertTrue(Objects.nonNull(eccnList));
        }
        catch (Exception e)
        {
            return ;
        }
    }


    @Test
    public void insertEccnStocksTest(){
        List<EccnStock> eccnList = new ArrayList<>();
        EccnStock es = new EccnStock();
        es.setVersion("123");
        eccnList.add(es);
        try {
            eccnStockServiceImpl.insertEccnStocks(eccnList);
            Assert.assertTrue(Objects.nonNull(eccnList));
        }
        catch (Exception e)
        {
            return ;
        }
    }
}
