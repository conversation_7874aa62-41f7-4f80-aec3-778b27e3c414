/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.InforBarcodeCenterServiceImpl;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.interfaces.infor.dto.RuleReqDTO;
import com.zte.interfaces.infor.dto.SkuDTO;
import com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.anyString;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年06月15日 <br>
 * @see com.zte.application.infor <br>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RemoteServiceDataUtil.class,StringUtils.class, HttpClientUtil.class, JacksonJsonConverUtil.class, Tools.class, CommonUtils.class})
public class InforBarcodeCenterServiceImplTest {
    @InjectMocks
    InforBarcodeCenterServiceImpl inforBarcodeCenterService;

    @Mock
    private InforBarcodeCenterRepository inforBarcodeCenterRepository;

     @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        // 初始化静态mock
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.mockStatic(CommonUtils.class);

        // 设置服务属性
        Whitebox.setInternalState(inforBarcodeCenterService, "inoneUrl", "http://mock-url");
        Whitebox.setInternalState(inforBarcodeCenterService, "inoneAppcode", "TEST_APPCODE");
    }

    @Test
    public void testGetShortSnCode_WhenRegisterBarcodeIsEmpty_ShouldReturnValidationError() {
        // 准备测试数据
        RuleReqDTO dto = new RuleReqDTO();
        dto.setRegisterBarcode("");
        dto.setContainerBarcode("CONTAINER123");

        // 模拟StringUtils行为
        PowerMockito.when(StringUtils.isEmpty(Mockito.anyString())).thenReturn(true);

        // 执行测试
        ServiceData<?> result = inforBarcodeCenterService.getShortSnCode(dto);

        // 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetShortSnCode_WhenBothContainerAndBatchBarcodeAreEmpty_ShouldReturnValidationError() {
        // 准备测试数据
        RuleReqDTO dto = new RuleReqDTO();
        dto.setRegisterBarcode("REG123");
        dto.setContainerBarcode("");
        dto.setBatchBarcode("");

        // 模拟StringUtils行为
        PowerMockito.when(StringUtils.isEmpty("REG123")).thenReturn(false);
        PowerMockito.when(StringUtils.isEmpty("")).thenReturn(true);

        // 执行测试
        ServiceData<?> result = inforBarcodeCenterService.getShortSnCode(dto);

        // 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetShortSnCode_WhenSuccessResponse_ShouldReturnSuccessData() throws Exception {
        // 准备测试数据
        RuleReqDTO dto = new RuleReqDTO();
        dto.setRegisterBarcode("REG123");
        dto.setContainerBarcode("CONT123");

        // 模拟StringUtils行为
        PowerMockito.when(StringUtils.isEmpty(Mockito.anyString())).thenReturn(false);

        // 模拟HTTP响应
        String successResponse = "{\"code\":{\"code\":\"0000\",\"msg\":\"成功\"},\"bo\":{\"shortSn\":\"SHORT123\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                   .thenReturn(successResponse);

        // 模拟JSON解析
        ObjectMapper mapper = new ObjectMapper();
        JsonNode mockJsonNode = mapper.readTree(successResponse);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapper);
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(Mockito.any())).thenReturn("{}");

        // 模拟Tools.equals
        PowerMockito.when(Tools.equals(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        // 执行测试
        ServiceData<?> result = inforBarcodeCenterService.getShortSnCode(dto);

        // 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetShortSnCode_WhenErrorResponse_ShouldReturnBusinessError() throws Exception {
        // 准备测试数据
        RuleReqDTO dto = new RuleReqDTO();
        dto.setRegisterBarcode("REG123");
        dto.setContainerBarcode("CONT123");

        // 模拟StringUtils行为
        PowerMockito.when(StringUtils.isEmpty(Mockito.anyString())).thenReturn(false);

        // 模拟HTTP响应
        String errorResponse = "{\"code\":{\"code\":\"9999\",\"msg\":\"业务错误\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                   .thenReturn(errorResponse);

        // 模拟JSON解析
        ObjectMapper mapper = new ObjectMapper();
        JsonNode mockJsonNode = mapper.readTree(errorResponse);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapper);
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(Mockito.any())).thenReturn("{}");

        // 模拟Tools.equals
        PowerMockito.when(Tools.equals(Mockito.anyString(), Mockito.anyString())).thenReturn(false);

        // 执行测试
        ServiceData<?> result = inforBarcodeCenterService.getShortSnCode(dto);

        // 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetShortSnCode_WhenHttpException_ShouldReturnValidationError() throws Exception {
        // 准备测试数据
        RuleReqDTO dto = new RuleReqDTO();
        dto.setRegisterBarcode("REG123");
        dto.setContainerBarcode("CONT123");

        // 模拟StringUtils行为
        PowerMockito.when(StringUtils.isEmpty(Mockito.anyString())).thenReturn(false);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("测试");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("测试");
        // 模拟HTTP异常
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                   .thenThrow(new RuntimeException("HTTP调用异常"));

        // 执行测试
        ServiceData<?> result = inforBarcodeCenterService.getShortSnCode(dto);

        // 验证结果
        Assert.assertNotNull(result);
    }


    @Test
    public void pushTransferReelidToBarcodeCenter() throws Exception {
        //PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);

        String xEmpNo = "A0549158885371727872";
        String xAuthValue = "A0549158885371727872";
        TransferReelidRelationshipDTO dto = new TransferReelidRelationshipDTO();
        dto.setExternreceiptkey("HZ141000018");
        dto.setNewSku("21421541");
        dto.setOldSku("235262");
        List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList = new ArrayList<>();
        PowerMockito.when(inforBarcodeCenterRepository.getTransferReelidRelationship(Mockito.any())).thenReturn(transferReelidRelationshipDTOList);
        Whitebox.invokeMethod(inforBarcodeCenterService, "pushTransferReelidToBarcodeCenter", xEmpNo, xAuthValue, dto);

        List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList2 = new ArrayList<>();
        transferReelidRelationshipDTOList2.add(dto);
        PowerMockito.when(inforBarcodeCenterRepository.getTransferReelidRelationship(Mockito.any())).thenReturn(transferReelidRelationshipDTOList2);
        Whitebox.invokeMethod(inforBarcodeCenterService, "getSkuNameMap", transferReelidRelationshipDTOList2);
        List<Map<String, String>> list1 = Tools.newArrayList();
        Whitebox.invokeMethod(inforBarcodeCenterService, "invokeTransferReelid", list1, xEmpNo, xAuthValue);
        ServiceData<?> res = new ServiceData<>();
        Whitebox.invokeMethod(inforBarcodeCenterService, "updateTransferReelids", transferReelidRelationshipDTOList2, res, xEmpNo);

        Whitebox.invokeMethod(inforBarcodeCenterService, "pushTransferReelidToBarcodeCenter", xEmpNo, xAuthValue, dto);
        ServiceData<?> rest = inforBarcodeCenterService.pushTransferReelidToBarcodeCenter(xEmpNo, xAuthValue, dto);
        Assert.assertTrue(Objects.nonNull(rest));
    }

    @Test
    public void getSkuNameMap() throws Exception {

        List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList = new ArrayList<>();
        Whitebox.invokeMethod(inforBarcodeCenterService, "getSkuNameMap", transferReelidRelationshipDTOList);

        List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList2 = new ArrayList<>();
        TransferReelidRelationshipDTO dto = new TransferReelidRelationshipDTO();
        dto.setExternreceiptkey("HZ141000018");
        dto.setNewSku("21421541");
        dto.setOldSku("235262");
        transferReelidRelationshipDTOList2.add(dto);
        List<SkuDTO> skuDTOList = new ArrayList<>();
        SkuDTO sku = new SkuDTO();
        sku.setSku("2352623");
        sku.setDescr("2363262");
        skuDTOList.add(sku);
        PowerMockito.when(inforBarcodeCenterRepository.getSkuNameList(Mockito.any())).thenReturn(skuDTOList);

        Whitebox.invokeMethod(inforBarcodeCenterService, "getSkuNameMap", transferReelidRelationshipDTOList2);
        Map<String, String> res = inforBarcodeCenterService.getSkuNameMap(transferReelidRelationshipDTOList2);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void updateTransferReelids() throws Exception {

        String xEmpNo = "A0549158885371727872";
        ServiceData<?> res = new ServiceData<>();
        List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList = new ArrayList<>();
        TransferReelidRelationshipDTO dto = new TransferReelidRelationshipDTO();
        dto.setExternreceiptkey("HZ141000018");
        dto.setNewSku("21421541");
        dto.setOldSku("235262");
        transferReelidRelationshipDTOList.add(dto);
        PowerMockito.when(inforBarcodeCenterRepository.updateTransferReelidRelationship(transferReelidRelationshipDTOList)).thenReturn(1);

        Whitebox.invokeMethod(inforBarcodeCenterService, "updateTransferReelids", transferReelidRelationshipDTOList, res, xEmpNo);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void invokeTransferReelid() throws Exception {
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);

        String xEmpNo = "A0549158885371727872";
        String xAuthValue = "A0549158885371727872";
        List<Map<String, String>> list1 = Tools.newArrayList();
        Whitebox.invokeMethod(inforBarcodeCenterService, "invokeTransferReelid", list1, xEmpNo, xAuthValue);
        ServiceData<?> res = inforBarcodeCenterService.invokeTransferReelid(list1, xEmpNo, xAuthValue);
        Assert.assertTrue(Objects.isNull(res));
    }

}
