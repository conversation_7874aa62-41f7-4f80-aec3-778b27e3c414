/* Started by AICoder, pid:k4e73q85e3z9e6c141590b0d1004570c5d65df38 */
package com.zte.application.infor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.DeliveryNoReceivesServiceImpl;
import com.zte.domain.model.infor.*;
import com.zte.interfaces.infor.dto.DeliverySignDTO;
import com.zte.interfaces.infor.dto.PcbOverdueDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.DeliverySignListVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, HttpClientUtil.class, JacksonJsonConverUtil.class})
public class DeliveryNoReceivesServiceImplTest {
    @InjectMocks
    private DeliveryNoReceivesServiceImpl deliveryNoReceivesService;
    @Mock
    private DeliveryNoReceivesRepository deliveryNoReceivesRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, HttpClientUtil.class, JacksonJsonConverUtil.class);
    }
    @Test
    public void insertSignInfo() throws Exception {
        List<String> whseList = new ArrayList<>();
        String a1 = "MWHSE1";
        whseList.add(a1);
        DeliverySignDTO dto = DeliverySignDTO.builder().build().setPageIndex(1).setPageSize(20).setCreatedBy("100").setProductBase("sz").setWhseidList(whseList);
        DeliverySignDTO dto1 = DeliverySignDTO.builder().build().setPageIndex(1).setPageSize(20).setCreatedBy("100").setWhseidList(whseList);
        List<DeliverySignDetailDTO> list = new ArrayList<>();
        DeliverySignDetailDTO detailDTO = DeliverySignDetailDTO.builder().build().setAsnStatus("0,6");
        list.add(detailDTO);
        DeliverySignDetailDTO detailDTO1 = DeliverySignDetailDTO.builder().build().setAsnStatus("0,0");
        DeliverySignDetailDTO detailDTO2 = DeliverySignDetailDTO.builder().build().setAsnStatus("5,12");
        DeliverySignDetailDTO detailDTO3 = DeliverySignDetailDTO.builder().build().setAsnStatus("11");
        PowerMockito.when(deliveryNoReceivesRepository.queryExistsByDelivery(Mockito.any())).thenReturn(1);
        PowerMockito.when(deliveryNoReceivesRepository.queryDeliveryInfo(Mockito.any())).thenReturn(list);
        PowerMockito.when(deliveryNoReceivesRepository.queryDeliverySignInfo(Mockito.any())).thenReturn(detailDTO);
        deliveryNoReceivesService.insertSignInfo(dto1);
        PowerMockito.when(deliveryNoReceivesRepository.queryDeliverySignInfo(Mockito.any())).thenReturn(detailDTO1);
        deliveryNoReceivesService.insertSignInfo(dto);
        PowerMockito.when(deliveryNoReceivesRepository.queryDeliverySignInfo(Mockito.any())).thenReturn(detailDTO2);
        deliveryNoReceivesService.insertSignInfo(dto);
        PowerMockito.when(deliveryNoReceivesRepository.queryExistsByDelivery(Mockito.any())).thenReturn(0);
        PowerMockito.when(deliveryNoReceivesRepository.queryDeliverySignInfo(Mockito.any())).thenReturn(detailDTO3);
        DeliverySignDetailDTO result = deliveryNoReceivesService.insertSignInfo(dto);
        Assert.assertNotNull(result);
    }

    /* Started by AICoder, pid:6ef57f9efe0e474ca711fd363a6ea404 */
    @Test
    public void getDeliverySignInfo() throws Exception {
        List<String> whseList = new ArrayList<>();
        String a1 = "MWHSE1";
        whseList.add(a1);
        DeliverySignDTO dto = DeliverySignDTO.builder().build().setPageIndex(1).setPageSize(20).setCreatedBy("100").setProductBase("sz").setWhseidList(whseList);
        DeliverySignDTO dto1 = DeliverySignDTO.builder().build().setPageIndex(1).setPageSize(20).setCreatedBy("100").setWhseidList(whseList);
        DeliverySignDTO dto2 = DeliverySignDTO.builder().build().setPageIndex(1).setPageSize(20).setCreatedBy("100").setWhseId("WMWHSE1");

        // 创建一个包含详细信息的列表
        List<DeliverySignDetailDTO> list = new ArrayList<>();

        // 创建一个详细信息对象并设置状态
        DeliverySignDetailDTO detailDTO = DeliverySignDetailDTO.builder()
                .asnStatus("新")
                .build();

        // 将详细信息添加到列表中
        list.add(detailDTO);

        // 使用 PowerMockito 模拟仓库方法的行为
        PowerMockito.when(deliveryNoReceivesRepository.getDeliverySignDetailTotal(Mockito.any()))
                .thenReturn(1);

        PowerMockito.when(deliveryNoReceivesRepository.getDeliverySignDetailVo(Mockito.any()))
                .thenReturn(list);
        deliveryNoReceivesService.getDeliverySignInfo(dto2);
        deliveryNoReceivesService.getDeliverySignInfo(dto1);
        // 调用服务方法获取结果
        DeliverySignListVo result = deliveryNoReceivesService.getDeliverySignInfo(dto);

        // 断言结果不为空
        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:6ef57f9efe0e474ca711fd363a6ea404 */

    /* Started by AICoder, pid:7c2891f1d0a348d8944995fddbc3caf7 */
    @Test
    public void getEmptyLocByWhseId() throws Exception {
        // 创建 DTO 对象
        DeliverySignDTO dto = DeliverySignDTO.builder().build();

        // 创建一个包含详细信息的列表
        List<DeliverySignDetailDTO> list = new ArrayList<>();

        // 创建一个详细信息对象并设置状态
        DeliverySignDetailDTO detailDTO = DeliverySignDetailDTO.builder()
                .asnStatus("新")
                .build();

        // 将详细信息添加到列表中
        list.add(detailDTO);

        // 使用 PowerMockito 模拟仓库方法的行为
        PowerMockito.when(deliveryNoReceivesRepository.getEmptyLocByWhseId(Mockito.any()))
                .thenReturn(list);

        // 调用服务方法获取结果
        List<DeliverySignDetailDTO> result = deliveryNoReceivesService.getEmptyLocByWhseId(dto);

        // 断言结果不为空
        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:7c2891f1d0a348d8944995fddbc3caf7 */

    @Test
    public void getQualityByWhseId() {
        DeliverySignDTO dto = new DeliverySignDTO();
        dto.setAction("QCREQ");
        dto.setWhseId("111");
        dto.setToIdList(Arrays.asList("1"));
        deliveryNoReceivesService.getQualityByWhseId(dto);
        List<DeliveryDetailDTO> notReceived = new ArrayList<>();
        DeliveryDetailDTO deliveryDetailDTO = new DeliveryDetailDTO();
        deliveryDetailDTO.setStateType("未收货");
        deliveryDetailDTO.setToId("2");
        notReceived.add(deliveryDetailDTO);
        PowerMockito.when(deliveryNoReceivesRepository.getNotReceivedInfo(Mockito.any())).thenReturn(notReceived);
        deliveryNoReceivesService.getQualityByWhseId(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getPcbOverdueInfo() throws Exception{
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        String date = "2025-01-01";
        PcbOverdueDTO dto = new PcbOverdueDTO();
        dto.setItemBarcode("1");
        dto.setItemNo("1");
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        PcbOverdueInfoDTO pcbOverdueInfoDTO = new PcbOverdueInfoDTO();
        pcbOverdueInfoDTO.setItemNo("1");
        PowerMockito.when(deliveryNoReceivesRepository.getPcbOverdueInfo(Mockito.any())).thenReturn(pcbOverdueInfoDTO);
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        pcbOverdueInfoDTO.setProductDate(sim.parse(date));
        pcbOverdueInfoDTO.setEffectiveDate(sim.parse(date));
        PowerMockito.when(deliveryNoReceivesRepository.getPcbOverdueInfo(Mockito.any())).thenReturn(pcbOverdueInfoDTO);
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        String result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DESC\": \"没有查询到数据！\",\n" +
                "        \"RESULT\": \"T\"\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\",\n" +
                "                        \"CODEVALUE\": {\n" +
                "                            \"SPECIALITY\": [\n" +
                "                                {\n" +
                "                                    \"PROPERTY\": [\n" +
                "                                        {\n" +
                "                                            \"PROPERTYCODE\": \"005304\",\n" +
                "                                            \"PROPERTYNAME\": \"Surface treatment\",\n" +
                "                                            \"PROPERTYVALUE\": \"OSP\",\n" +
                "                                            \"SUFFIXVALUE\": \":\",\n" +
                "                                            \"SYMBOLVALUE\": \";\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"SPECIALITYCODE\": \"004000\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"DESC\": \"成功查询到1条数据！\",\n" +
                "        \"RESULT\": \"S\"\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = objectMapper.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        List<SysLookupValuesDTO> sysLookupValuesDTOS = new ArrayList<>();
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("0");
            setDescription("1");
            setAttribute1("0");
            setAttribute2("1");
        }});
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("1");
            setDescription("1");
            setAttribute1("0");
            setAttribute2("2");
        }});
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOS);
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\",\n" +
                "                        \"CODEVALUE\": {\n" +
                "                            \"SPECIALITY\": [\n" +
                "                                {\n" +
                "                                    \"PROPERTY\": [\n" +
                "                                        {\n" +
                "                                            \"PROPERTYCODE\": \"005304\",\n" +
                "                                            \"PROPERTYNAME\": \"Surface treatment\",\n" +
                "                                            \"PROPERTYVALUE\": \"ENIP\",\n" +
                "                                            \"SUFFIXVALUE\": \":\",\n" +
                "                                            \"SYMBOLVALUE\": \";\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"SPECIALITYCODE\": \"004000\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"DESC\": \"成功查询到1条数据！\",\n" +
                "        \"RESULT\": \"S\"\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper1 = new ObjectMapper();
        JsonNode json1 = objectMapper1.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json1);
        try {
            deliveryNoReceivesService.getPcbOverdueInfo(dto);
        }catch (Exception ex) {
        }
        Assert.assertNotNull(dto);
    }

    @Test
    public void getResultInfo() {
        List<SysLookupValuesDTO> sysLookupValuesDTOS = new ArrayList<>();
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("0");
            setDescription("1");
            setAttribute1("0");
            setAttribute2("1");
        }});
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("1,2");
            setDescription("1");
            setAttribute1("0");
        }});
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("2,3");
            setDescription("1");
            setAttribute1("0");
        }});
        sysLookupValuesDTOS.add(new SysLookupValuesDTO(){{
            setLookupMeaning("10");
            setDescription("1");
            setAttribute1("0");
            setAttribute2("2");
        }});
        deliveryNoReceivesService.getResultInfo("0", -1, sysLookupValuesDTOS);
        deliveryNoReceivesService.getResultInfo("0", 1, sysLookupValuesDTOS);
        deliveryNoReceivesService.getResultInfo("0", 2, sysLookupValuesDTOS);
        deliveryNoReceivesService.getResultInfo("0", 10, sysLookupValuesDTOS);
        deliveryNoReceivesService.getResultInfo("0", 5, sysLookupValuesDTOS);
        deliveryNoReceivesService.getResultInfo("0", 10, new ArrayList<>());
        Assert.assertNotNull(sysLookupValuesDTOS);
    }

    @Test
    public void findPropertyByCode() throws Exception{
        String result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\",\n" +
                "                        \"CODEVALUE\": {\n" +
                "                            \"SPECIALITY\": [\n" +
                "                                {\n" +
                "                                    \"PROPERTY\": [\n" +
                "                                        {\n" +
                "                                            \"PROPERTYCODE\": \"005301\",\n" +
                "                                            \"PROPERTYNAME\": \"Surface treatment\",\n" +
                "                                            \"PROPERTYVALUE\": \"ENIG\",\n" +
                "                                            \"SUFFIXVALUE\": \":\",\n" +
                "                                            \"SYMBOLVALUE\": \";\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"SPECIALITYCODE\": \"004000\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        \"DESC\": \"成功查询到1条数据！\",\n" +
                "        \"RESULT\": \"S\"\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper1 = new ObjectMapper();
        JsonNode json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\",\n" +
                "                        \"CODEVALUE\": {\n" +
                "                            \"SPECIALITY\": [\n" +
                "                                {\n" +
                "                                    \"PROPERTY\": [],\n" +
                "                                    \"SPECIALITYCODE\": \"004000\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"PROPERTY\": [],\n" +
                "                                    \"SPECIALITYCODE\": \"0040010\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\",\n" +
                "                        \"CODEVALUE\": {\n" +
                "                            \"SPECIALITY\": []\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CATEGORYCODE\": \"401816\",\n" +
                "                        \"CATEGORYNAME\": \"十六层板\",\n" +
                "                        \"CODE\": \"040181600002\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": []\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        result = "{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {}\n" +
                "    }\n" +
                "}";
        json1 = objectMapper1.readTree(result);
        deliveryNoReceivesService.findPropertyByCode(json1, "005304", "004000");
        Assert.assertNotNull(result);
    }
}
/* Ended by AICoder, pid:k4e73q85e3z9e6c141590b0d1004570c5d65df38 */