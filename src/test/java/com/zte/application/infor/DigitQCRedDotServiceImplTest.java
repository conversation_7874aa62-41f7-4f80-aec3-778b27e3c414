/* Started by AICoder, pid:yec2dy1133l84e1146180b7160992f06cee6aac9 */
package com.zte.application.infor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.DigitQCRedDotServiceImpl;
import com.zte.domain.model.infor.DigitQCRedDotDetailDTO;
import com.zte.domain.model.infor.DigitQCRedDotRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.ErpStockResultDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/4/14 16:48
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class})
public class DigitQCRedDotServiceImplTest {
    @InjectMocks
    private DigitQCRedDotServiceImpl digitQCRedDotService;

    @Mock
    private DigitQCRedDotRepository digitQCRedDotRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private String authToken;

    @Mock
    private String redDotUrl;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class);
    }


    @Test
    public void pullInspectionInfoJob() throws Exception{
        String result = "{\n" +
                "\t\"code\": {\n" +
                "\t\t\"code\": \"0001\",\n" +
                "\t\t\"msgId\": \"RetCode.Success\",\n" +
                "\t\t\"msg\": \"Success\"\n" +
                "\t},\n" +
                "\t\"bo\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"textareaField_uu6w7151\": \"测试单\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"other\": null,\n" +
                "\t\"responseRule\": \"msa\"\n" +
                "}";
        DigitQCRedDotManageDTO digitQCRedDotManageDTO = new DigitQCRedDotManageDTO();
        digitQCRedDotManageDTO.setCreatedBy("1");
        digitQCRedDotManageDTO.setCreationDate("-1");
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCRedDotService.pullInspectionInfoJob(digitQCRedDotManageDTO);
        String result1 = "{\n" +
                "\t\"code\": {\n" +
                "\t\t\"code\": \"0000\",\n" +
                "\t\t\"msgId\": \"RetCode.Success\",\n" +
                "\t\t\"msg\": \"Success\"\n" +
                "\t},\n" +
                "\t\"bo\": [{\n" +
                "        \"departmentSelectField_29f7llku\":[],\n" +
                "        \"employeeField_du4vdnzf\":[],\n" +
                "        \"employeeField_y66mk6nl\":[],\n" +
                "        \"attachmentField_wzaaw6gl\":[]\n" +
                "    }],\n" +
                "\t\"other\": null,\n" +
                "\t\"responseRule\": \"msa\"\n" +
                "}";
        List<String> ids = Arrays.asList("1");
        PowerMockito.when(digitQCRedDotRepository.selectIdaInfo()).thenReturn(ids);
        List<DigitQCIdaInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(new DigitQCIdaInfoDTO(){{
            setId("s1");
            setAttachmentField_wzaaw6gl(new ArrayList<>());
            setEmployeeField_y66mk6nl(new ArrayList<>());
            setEmployeeField_du4vdnzf(new ArrayList<>());
            setDepartmentSelectField_29f7llku(new ArrayList<>());
        }});
        List<AttacheInfoDTO> attacheInfoDTOS = new ArrayList<>();
        AttacheInfoDTO attacheInfoDTO = new AttacheInfoDTO();
        attacheInfoDTO.setDownloadUrl("1");
        attacheInfoDTOS.add(attacheInfoDTO);
        List<DigitQCIdaEmpInfoDTO> digitQCIdaEmpInfoDTOS = new ArrayList<>();
        DigitQCIdaEmpInfoDTO dto = new DigitQCIdaEmpInfoDTO();
        dto.setEmpUIID("11");
        dto.setEmpName("22");
        digitQCIdaEmpInfoDTOS.add(dto);
        dtoList.add(new DigitQCIdaInfoDTO(){{
            setId("s1");
            setAttachmentField_wzaaw6gl(attacheInfoDTOS);
            setEmployeeField_y66mk6nl(digitQCIdaEmpInfoDTOS);
            setEmployeeField_du4vdnzf(digitQCIdaEmpInfoDTOS);
            setDepartmentSelectField_29f7llku(Arrays.asList("1"));
            setRadioField_71klyzoo("整改完成");
        }});
        JsonNode json = objectMapper4.readTree(result1);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(dtoList);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000083");
            setLookupMeaning("1");
            setDescription("1");
            setAttribute1("bm");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000083");
            setLookupMeaning("2");
            setDescription("2");
            setAttribute1("1");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000083");
            setLookupMeaning("3");
            setDescription("3");
            setAttribute1("2");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000080");
            setLookupMeaning("4");
            setDescription("4");
            setAttribute1("ks");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000082");
            setLookupMeaning("5");
            setDescription("5");
            setAttribute1("bm");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000082");
            setLookupMeaning("6");
            setDescription("6");
            setAttribute1("2");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000082");
            setLookupMeaning("7");
            setDescription("7");
            setAttribute1("3");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000080");
            setLookupMeaning("8");
            setDescription("8");
            setAttribute1("3");
        }});
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        digitQCRedDotService.pullInspectionInfoJob(digitQCRedDotManageDTO);
        Assert.assertNotNull(digitQCRedDotManageDTO);
    }

    @Test
    public void digitQCRedDotImport() throws Exception{
        List<DigitQCRedDotManageDTO> list = new ArrayList<>();
        DigitQCRedDotManageDTO dto = new DigitQCRedDotManageDTO();
        dto.setLiability("111");
        dto.setProblemDesc("1");
        dto.setCreatedBy("2");
        dto.setDepartment("配送");
        dto.setIsWarning("Y");
        digitQCRedDotService.digitQCRedDotImport(list, "1");
        list.add(dto);
        digitQCRedDotService.digitQCRedDotImport(list, "1");
        List<DigitQCRedDotDetailDTO> digitQCRedDotDetailDTOS = new ArrayList<>();
        DigitQCRedDotDetailDTO digitQCRedDotDetailDTO = new DigitQCRedDotDetailDTO();
        digitQCRedDotDetailDTO.setProductBase("南京基地");
        digitQCRedDotDetailDTO.setInforStock("WH26");
        digitQCRedDotDetailDTO.setQcProject("库存查询");
        digitQCRedDotDetailDTOS.add(digitQCRedDotDetailDTO);
        PowerMockito.when(digitQCRedDotRepository.queryQCRedDotInfo(Mockito.any())).thenReturn(digitQCRedDotDetailDTOS);
        List<SysLookupValuesDTO> sysLookupValuesDTOS = new ArrayList<>();
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupType("1000072");
        sysLookupValuesDTO.setLookupCode("100007200001");
        sysLookupValuesDTO.setLookupMeaning("11111");
        sysLookupValuesDTOS.add(sysLookupValuesDTO);
        SysLookupValuesDTO sysLookupValuesDTO1 = new SysLookupValuesDTO();
        sysLookupValuesDTO1.setLookupType("1000073");
        sysLookupValuesDTO1.setLookupMeaning("sz");
        sysLookupValuesDTO1.setDescription("深圳基地");
        sysLookupValuesDTOS.add(sysLookupValuesDTO1);
        SysLookupValuesDTO sysLookupValuesDTO2 = new SysLookupValuesDTO();
        sysLookupValuesDTO2.setLookupType("1000082");
        sysLookupValuesDTO2.setLookupMeaning("1");
        sysLookupValuesDTO2.setDescription("库存抽查");
        sysLookupValuesDTOS.add(sysLookupValuesDTO2);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOS);
        digitQCRedDotService.digitQCRedDotImport(list, "1");
        Assert.assertNotNull(list);
    }

    @Test
    public void sendRedDotInfo() {
        String empNo = "1";
        List<DigitQCRedDotDetailDTO> list = new ArrayList<>();
        DigitQCRedDotDetailDTO digitQCRedDotDetailDTO = new DigitQCRedDotDetailDTO();
        digitQCRedDotDetailDTO.setDepartment("部门");
        digitQCRedDotDetailDTO.setProductBase("sz");
        digitQCRedDotDetailDTO.setInforStock("WH26");
        digitQCRedDotDetailDTO.setQcProject("1");
        digitQCRedDotDetailDTO.setLiability("石涛");
        digitQCRedDotDetailDTO.setProblemDesc("缺料");
        digitQCRedDotDetailDTO.setIsWarning("Y");
        list.add(digitQCRedDotDetailDTO);
        Map<String, String> baseMap = new HashMap<>();
        baseMap.put("sz", "深圳基地");
        baseMap.put("1", "库存查询");
        String result = "{\n" +
                "\t\"code\": {\n" +
                "\t\t\"code\": \"0000\",\n" +
                "\t\t\"msgId\": \"RetCode.Success\",\n" +
                "\t\t\"msg\": \"操作成功\"\n" +
                "\t},\n" +
                "\t\"bo\": \"1111\",\n" +
                "\t\"other\": null,\n" +
                "\t\"responseRule\": \"msa\",\n" +
                "\t\"requestId\": \"4da7401e74ed4535\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCRedDotService.sendRedDotInfo(digitQCRedDotDetailDTO, empNo, baseMap);
        digitQCRedDotService.isSendRedDotInfo(list, empNo, baseMap);
        digitQCRedDotDetailDTO.setSection("科室");
        digitQCRedDotDetailDTO.setGroups("班组");
        result = "{\n" +
                "\t\"code\": {\n" +
                "\t\t\"code\": \"0001\",\n" +
                "\t\t\"msgId\": \"RetCode.Success\",\n" +
                "\t\t\"msg\": \"操作失败\"\n" +
                "\t},\n" +
                "\t\"bo\": \"1111\",\n" +
                "\t\"other\": null,\n" +
                "\t\"responseRule\": \"msa\",\n" +
                "\t\"requestId\": \"4da7401e74ed4535\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCRedDotService.sendRedDotInfo(digitQCRedDotDetailDTO, empNo, baseMap);
        Assert.assertNotNull(list);
    }

    @Test
    public void digitQCRedDotQuery() {
        DigitQCRedDotManageDTO dto = new DigitQCRedDotManageDTO();
        dto.setProductBase("sz");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        digitQCRedDotService.digitQCRedDotQuery(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void digitQCRedDotLose() {
        List<String> serialKeys = Arrays.asList("11");
        DigitQCRedDotManageDTO dto = new DigitQCRedDotManageDTO();
        digitQCRedDotService.digitQCRedDotLose(dto);
        dto.setSerialKeys(serialKeys);
        digitQCRedDotService.digitQCRedDotLose(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void digitQCRedDotCallback() {
        DigitQCRedDotManageDTO dto = new DigitQCRedDotManageDTO();
        try {
            digitQCRedDotService.digitQCRedDotCallback(dto);
        }catch (Exception ex){
        }
        dto.setTaskOrderNo("1");
        digitQCRedDotService.digitQCRedDotCallback(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void digitQCRedDotExport() {
        DigitQCRedDotManageDTO dto = new DigitQCRedDotManageDTO();
        String empNo = "";
        dto.setProductBase("sz");
        PowerMockito.when(digitQCRedDotRepository.getDigitQCRedDotDetailTotal(Mockito.any())).thenReturn(1);
        List<DigitQCRedDotDetailDTO> list = new ArrayList<>();
        DigitQCRedDotDetailDTO digitQCRedDotDetailDTO = new DigitQCRedDotDetailDTO();
        digitQCRedDotDetailDTO.setProductBase("sz");
        digitQCRedDotDetailDTO.setSourceType("IWMS");
        digitQCRedDotDetailDTO.setLiability("1");
        digitQCRedDotDetailDTO.setState("进行中");
        list.add(digitQCRedDotDetailDTO);
        PowerMockito.when(digitQCRedDotRepository.getDigitQCRedDotDetailVo(Mockito.any())).thenReturn(list);
        digitQCRedDotService.digitQCRedDotExport(dto, empNo);
        Assert.assertNotNull(dto);
    }

}
/* Ended by AICoder, pid:yec2dy1133l84e1146180b7160992f06cee6aac9 */