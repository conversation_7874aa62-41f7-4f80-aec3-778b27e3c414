package com.zte.application.infor;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.impl.RedDotExecuteServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.RedDotExecuteInfo;
import com.zte.domain.model.infor.RedDotExecuteRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.RedDotExecuteDetilInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoVo;
import com.zte.interfaces.material.dto.OverTimeBarcodeDTO;
import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2025-03-11 17:39
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class,HttpClientUtil.class,ExcelUtil.class, Tools.class})
public class RedDotExecuteServiceImplTest {
    @InjectMocks
    private RedDotExecuteServiceImpl redDotExecuteService;

    @Mock
    private RedDotExecuteRepository redDotExecuteRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private StepTransferRepository stepIscpRepository;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class,BusiAssertException.class);
    }

    @Test
    public void orderRedDotTriggerJob() throws Exception{
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("1000077");
        sysLookupValuesDTOList.add(dto);
        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupType("1000076");
        dto1.setLookupCode("100007600001");
        sysLookupValuesDTOList.add(dto1);
        SysLookupValuesDTO dto2=new SysLookupValuesDTO();
        dto2.setLookupType("1000076");
        dto2.setLookupCode("100007600004");
        sysLookupValuesDTOList.add(dto2);
        SysLookupValuesDTO dto3=new SysLookupValuesDTO();
        dto3.setLookupType("1000076");
        dto3.setLookupCode("100007600002");
        sysLookupValuesDTOList.add(dto3);
        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType("1000059");
        dto4.setLookupCode("100005900004");
        sysLookupValuesDTOList.add(dto4);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redList.add(redDotExecuteInfo);
        PowerMockito.when(redDotExecuteRepository.getRedDotExecuteInfo(Mockito.any())).thenReturn(redList);
        redDotExecuteService.orderRedDotTriggerJob();
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getOrderData() throws Exception{
        List<PlDbDTO> whsetList=new ArrayList<>();
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setWarehouseId("WWWWW1");
        whsetList.add(plDbDTO);
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("1000071");
        dto.setLookupMeaning("WWWWW1");
        sysLookupValuesDTOList.add(dto);
        //获取超期复检白名单
        List<OrdersDto> delayWhit=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setOrderKey("23434");
        ordersDto.setIsTimeout(1);
        ordersDto.setIsKitting(0);
        ordersDto.setOrderKey("90001");
        ordersDto.setOrderLineNumber("001");
        delayWhit.add(ordersDto);
        PowerMockito.when(redDotExecuteRepository.getDelayWhiteLists(null)).thenReturn(delayWhit);
        PowerMockito.when(redDotExecuteRepository.getOrderData(Mockito.any())).thenReturn(null);
        redDotExecuteService.getOrderData(whsetList,redList,strMap,sysLookupValuesDTOList);

        PowerMockito.when(redDotExecuteRepository.getOrderData(Mockito.any())).thenReturn(delayWhit);
        redDotExecuteService.getOrderData(whsetList,redList,strMap,sysLookupValuesDTOList);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void batchUpdateRedDotInfo() throws Exception{
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(1);
        redDotExecuteInfo.setRedDotSubclass(0);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redDotExecuteInfo.setIsAdd(1);
        redList.add(redDotExecuteInfo);

        RedDotExecuteInfo redDotExecuteInfo1=new RedDotExecuteInfo();
        redDotExecuteInfo1.setRedDotType(1);
        redDotExecuteInfo1.setOverTimeCheckRed(0);
        redDotExecuteInfo1.setRedDotSubclass(1);
        redDotExecuteInfo1.setWhseId("WWWWW1");
        redDotExecuteInfo1.setExternreceiptkey("90001");
        redDotExecuteInfo1.setExternreceiptnumber("001");
        redDotExecuteInfo1.setIsAdd(1);
        redList.add(redDotExecuteInfo1);
        redDotExecuteService.batchUpdateRedDotInfo(redList);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void calculationRedDot() throws Exception{
        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("23434");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(0);
        ordersDto1.setOrderLineNumber("001");
        list.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(WARNING_DAYS,"87");
        List<OrdersDto> delayWhit=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setWhseId("WWWWW1");
        ordersDto.setOrderKey("23434");
        ordersDto.setLottable02("22000001");
        ordersDto.setIsTimeout(1);
        ordersDto.setIsKitting(0);
        ordersDto.setOrderLineNumber("001");
        delayWhit.add(ordersDto);
        redDotExecuteService.calculationRedDot(list,redList,strMap,plDbDTO,delayWhit);

        PowerMockito.when(redDotExecuteRepository.getOrderLot02(Mockito.any())).thenReturn(delayWhit);
        List<OrdersDto> list1=new ArrayList<>();
        ordersDto1.setIsTimeout(0);
        list1.add(ordersDto1);
        List<OrdersDto> delayWhit1=new ArrayList<>();
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setWhseId("WWWWW1");
        ordersDto2.setOrderKey("23435");
        ordersDto2.setLottable02("22000001");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(0);
        ordersDto2.setOrderLineNumber("001");
        delayWhit1.add(ordersDto2);
        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();
        OverTimeBarcodeDTO barcodeDTO=new OverTimeBarcodeDTO();
        barcodeDTO.setItemBarcodes("22000001");
        overTimeInDTOList.add(barcodeDTO);
        PowerMockito.when(stepIscpRepository.getValidateBarcode(Mockito.any())).thenReturn(overTimeInDTOList);
        List<String> overTimeList = new ArrayList<>();
        overTimeList.add("22000002");
        PowerMockito.when(redDotExecuteRepository.getDelayCheckInfo(Mockito.any())).thenReturn(overTimeList);
        redDotExecuteService.calculationRedDot(list1,redList,strMap,plDbDTO,delayWhit1);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void allocationNotTimeoutRedDot() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("23434");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(0);
        ordersDto1.setOrderLineNumber("001");
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        List<OrdersDto> delayWhit=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setWhseId("WWWWW1");
        ordersDto.setOrderKey("23434");
        ordersDto.setLottable02("22000001");
        ordersDto.setIsTimeout(1);
        ordersDto.setIsKitting(0);
        ordersDto.setOrderLineNumber("001");
        delayWhit.add(ordersDto);
        redDotExecuteService.allocationNotTimeoutRedDot(redList,ordersDtos,strMap,plDbDTO,delayWhit);

        List<OrdersDto> ordersDtos1=new ArrayList<>();
        ordersDto1.setIsTimeout(0);
        ordersDto1.setIsKitting(1);
        ordersDtos1.add(ordersDto1);
        List<OrdersDto> delayWhit1=new ArrayList<>();
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setWhseId("WWWWW1");
        ordersDto2.setOrderKey("23435");
        ordersDto2.setLottable02("22000001");
        ordersDto2.setIsTimeout(0);
        ordersDto2.setIsKitting(1);
        ordersDto2.setOrderLineNumber("001");
        delayWhit1.add(ordersDto2);
        PowerMockito.when(redDotExecuteRepository.getOrderAllocationTimeout(Mockito.any())).thenReturn(ordersDtos1);
        redDotExecuteService.allocationNotTimeoutRedDot(redList,ordersDtos1,strMap,plDbDTO,delayWhit1);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void orderKittingRedDot() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("23434");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.orderKittingRedDot(redList,ordersDtos,strMap,plDbDTO);

        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("23434");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(0);
        ordersDto2.setAvailableQty(2);
        ordersDto2.setQty(2);
        ordersDto2.setOrderLineNumber("001");
        ordersDtos.add(ordersDto2);
        PowerMockito.when(redDotExecuteRepository.getOrderAllocationTimeout(Mockito.any())).thenReturn(ordersDtos);
        PowerMockito.when(redDotExecuteRepository.getOrderQty(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.orderKittingRedDot(redList,ordersDtos,strMap,plDbDTO);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getOrderQty() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("23434");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getOrderQty(redList,ordersDtos,plDbDTO,strMap);

        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("23434");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(0);
        ordersDto2.setAvailableQty(2);
        ordersDto2.setQty(2);
        ordersDto2.setOrderLineNumber("001");
        ordersDtos.add(ordersDto2);
        PowerMockito.when(redDotExecuteRepository.getOrderQty(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getOrderQty(redList,ordersDtos,plDbDTO,strMap);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getNotKittingRed() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("23434");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setAvailableQty(0);
        ordersDto1.setOrderLineNumber("001");
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getNotKittingRed(ordersDtos,redList,plDbDTO,strMap);

        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("23434");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(0);
        ordersDto2.setAvailableQty(2);
        ordersDto2.setQty(2);
        ordersDto2.setOrderLineNumber("002");
        ordersDtos.add(ordersDto2);
        OrdersDto ordersDto3=new OrdersDto();
        ordersDto3.setOrderKey("23434");
        ordersDto3.setIsTimeout(1);
        ordersDto3.setIsKitting(0);
        ordersDto3.setAvailableQty(0);
        ordersDto3.setQty(0);
        ordersDto3.setRef11("513");
        ordersDto3.setOrderLineNumber("003");
        ordersDtos.add(ordersDto3);
        redDotExecuteService.getNotKittingRed(ordersDtos,redList,plDbDTO,strMap);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getLackMaterialsAndFreezeRed() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setSku("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(3);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getLackMaterialsAndFreezeRed(ordersDtos,redList,plDbDTO,strMap);

        List<RedDotExecuteInfo> redList1=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo1=new RedDotExecuteInfo();
        redDotExecuteInfo1.setRedDotType(1);
        redDotExecuteInfo1.setOverTimeCheckRed(0);
        redDotExecuteInfo1.setRedDotSubclass(1);
        redDotExecuteInfo1.setWhseId("WWWWW1");
        redDotExecuteInfo1.setExternreceiptkey("90001");
        redDotExecuteInfo1.setExternreceiptnumber("001");
        redList1.add(redDotExecuteInfo1);
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("23434");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(0);
        ordersDto2.setAvailableQty(2);
        ordersDto2.setQty(0);
        ordersDto2.setSku("123");
        ordersDto2.setRef11("430");
        ordersDto2.setOrderLineNumber("001");
        ordersDto2.setLottable02("2200001");
        ordersDto2.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto2);
        OrdersDto ordersDto3=new OrdersDto();
        ordersDto3.setOrderKey("23434");
        ordersDto3.setIsTimeout(1);
        ordersDto3.setIsKitting(0);
        ordersDto3.setAvailableQty(-15);
        ordersDto3.setQty(15);
        ordersDto3.setSku("123");
        ordersDto3.setRef11("513");
        ordersDto3.setOrderLineNumber("003");
        ordersDto3.setHoldDesc("测试");
        ordersDto3.setLottable02("2200001");
        ordersDto3.setIsSupplierDelivery(1);
        ordersDto3.setHoldCode("QCFAILED");
        ordersDtos.add(ordersDto3);
        redDotExecuteService.getLackMaterialsAndFreezeRed(ordersDtos,redList1,plDbDTO,strMap);

        OrdersDto ordersDto4=new OrdersDto();
        ordersDto4.setOrderKey("23434");
        ordersDto4.setIsTimeout(1);
        ordersDto4.setIsKitting(0);
        ordersDto4.setAvailableQty(2);
        ordersDto4.setQty(0);
        ordersDto4.setSku("123");
        ordersDto4.setRef11("513");
        ordersDto4.setOrderLineNumber("001");
        ordersDto4.setLottable02("2200001");
        ordersDto4.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto4);
        PowerMockito.when(redDotExecuteRepository.getReceiptBarcode(Mockito.any())).thenReturn(ordersDtos);
        PowerMockito.when(redDotExecuteRepository.getOrderHoldInfo(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getLackMaterialsAndFreezeRed(ordersDtos,redList1,plDbDTO,strMap);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getHoldRedList() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        PlDbDTO plDbDTO=new PlDbDTO();
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        Map<String, String> strMap=new HashMap<>();
        redDotExecuteService.getHoldRedList(redList,null,plDbDTO,strMap);

        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setSku("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(5);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getHoldRedList(redList,ordersDtos,plDbDTO,strMap);

        List<RedDotExecuteInfo> redList1=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo1=new RedDotExecuteInfo();
        redDotExecuteInfo1.setRedDotType(1);
        redDotExecuteInfo1.setOverTimeCheckRed(0);
        redDotExecuteInfo1.setRedDotSubclass(1);
        redDotExecuteInfo1.setWhseId("WWWWW1");
        redDotExecuteInfo1.setExternreceiptkey("90001");
        redDotExecuteInfo1.setExternreceiptnumber("001");
        redList1.add(redDotExecuteInfo1);
        PowerMockito.when(redDotExecuteRepository.getOrderHoldInfo(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getHoldRedList(redList1,ordersDtos,plDbDTO,strMap);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getHoldDescInfo() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setSku("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        List<OrdersDto> holdLotList=new ArrayList<>();
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setSku("123");
        ordersDto2.setHoldCode("12");
        ordersDto2.setHoldDesc("12");
        holdLotList.add(ordersDto2);
        redDotExecuteService.getHoldDescInfo(ordersDtos,holdLotList,plDbDTO,strMap);

        List<OrdersDto> holdLotList1=new ArrayList<>();
        OrdersDto ordersDto3=new OrdersDto();
        ordersDto3.setSku("123");
        ordersDto3.setHoldCode("QCFAILED");
        ordersDto3.setHoldDesc("12");
        holdLotList1.add(ordersDto3);
        redDotExecuteService.getHoldDescInfo(ordersDtos,holdLotList1,plDbDTO,strMap);
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        RedDotExecuteInfo redDto=new RedDotExecuteInfo();
        redDto.setRedDotType(1);
        allRedList.add(redDto);
        Assert.assertTrue(Objects.nonNull(allRedList));
    }

    @Test
    public void getHoldOrderInfo() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        List<String> holdLotList=new ArrayList<>();
        holdLotList.add("123");
        redDotExecuteService.getHoldOrderInfo(ordersDtos,holdLotList,plDbDTO,strMap);

        OrdersDto ordersDto3=new OrdersDto();
        ordersDto3.setLot("124");
        ordersDto3.setHoldCode("QCFAILED");
        ordersDto3.setHoldDesc("12");
        ordersDtos.add(ordersDto3);
        redDotExecuteService.getHoldOrderInfo(ordersDtos,holdLotList,plDbDTO,strMap);
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        RedDotExecuteInfo redDto=new RedDotExecuteInfo();
        redDto.setRedDotType(1);
        allRedList.add(redDto);
        Assert.assertTrue(Objects.nonNull(allRedList));
    }

    @Test
    public void getIsSupplierDeliveryRed() throws Exception{
        redDotExecuteService.getIsSupplierDeliveryRed(null,null,null);

        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        List<String> holdLotList=new ArrayList<>();
        holdLotList.add("123");
        PowerMockito.when(redDotExecuteRepository.getReceiptBarcode(Mockito.any())).thenReturn(null);
        redDotExecuteService.getIsSupplierDeliveryRed(ordersDtos,plDbDTO,strMap);

        PowerMockito.when(redDotExecuteRepository.getReceiptBarcode(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getIsSupplierDeliveryRed(ordersDtos,plDbDTO,strMap);
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        RedDotExecuteInfo redDto=new RedDotExecuteInfo();
        redDto.setRedDotType(1);
        allRedList.add(redDto);
        Assert.assertTrue(Objects.nonNull(allRedList));
    }

    @Test
    public void getReceiptRedList() throws Exception{
        List<OrdersDto> lackMaterialsList=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        lackMaterialsList.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getReceiptRedList(lackMaterialsList,null,plDbDTO,strMap);

        List<OrdersDto> allOrderList=new ArrayList<>();
        allOrderList.add(ordersDto1);
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("90001");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(1);
        ordersDto2.setQty(2);
        ordersDto2.setLot("123");
        ordersDto2.setAvailableQty(2);
        ordersDto2.setOrderLineNumber("001");
        ordersDto2.setLottable02("2200002");
        ordersDto2.setIsSupplierDelivery(0);
        lackMaterialsList.add(ordersDto2);
        redDotExecuteService.getReceiptRedList(lackMaterialsList,allOrderList,plDbDTO,strMap);
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        RedDotExecuteInfo redDto=new RedDotExecuteInfo();
        redDto.setRedDotType(1);
        allRedList.add(redDto);
        Assert.assertTrue(Objects.nonNull(allRedList));
    }

    @Test
    public void getAllotRedList() throws Exception{
        List<OrdersDto> lackMaterialsList=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        lackMaterialsList.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        List<String> allBarcodeList=new ArrayList<>();
        allBarcodeList.add("2200001");
        List<OrdersDto> allOrderList=new ArrayList<>();
        allOrderList.add(ordersDto1);
        redDotExecuteService.getAllotRedList(lackMaterialsList,allOrderList,allBarcodeList,plDbDTO,strMap);

        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setOrderKey("90001");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(1);
        ordersDto2.setQty(2);
        ordersDto2.setLot("123");
        ordersDto2.setAvailableQty(2);
        ordersDto2.setOrderLineNumber("001");
        ordersDto2.setLottable02("2200002");
        ordersDto2.setIsSupplierDelivery(0);
        allOrderList.add(ordersDto2);
        lackMaterialsList.add(ordersDto2);
        redDotExecuteService.getAllotRedList(lackMaterialsList,allOrderList,allBarcodeList,plDbDTO,strMap);
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        RedDotExecuteInfo redDto=new RedDotExecuteInfo();
        redDto.setRedDotType(1);
        allRedList.add(redDto);
        Assert.assertTrue(Objects.nonNull(allRedList));
    }

    @Test
    public void getOrderAllocationTimeout() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        redDotExecuteService.getOrderAllocationTimeout(redList,ordersDtos,strMap,plDbDTO);

        List<RedDotExecuteInfo> redList1=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo1=new RedDotExecuteInfo();
        redDotExecuteInfo1.setRedDotType(1);
        redDotExecuteInfo1.setOverTimeCheckRed(0);
        redDotExecuteInfo1.setRedDotSubclass(1);
        redDotExecuteInfo1.setWhseId("WWWWW1");
        redDotExecuteInfo1.setExternreceiptkey("90001");
        redDotExecuteInfo1.setExternreceiptnumber("001");
        redList1.add(redDotExecuteInfo1);
        PowerMockito.when(redDotExecuteRepository.getOrderAllocationTimeout(Mockito.any())).thenReturn(null);
        redDotExecuteService.getLackMaterialsAndFreezeRed(ordersDtos,redList1,plDbDTO,strMap);

        PowerMockito.when(redDotExecuteRepository.getOrderAllocationTimeout(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getLackMaterialsAndFreezeRed(ordersDtos,redList1,plDbDTO,strMap);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getOrderLot02ByWhiteList() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setWhseId("WWWWW1");
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        List<OrdersDto> delayWhit=new ArrayList<>();
        delayWhit.add(ordersDto1);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        PowerMockito.when(redDotExecuteRepository.getOrderLot02(Mockito.any())).thenReturn(null);
        redDotExecuteService.getOrderLot02ByWhiteList(ordersDtos,plDbDTO,delayWhit,strMap);

        PowerMockito.when(redDotExecuteRepository.getOrderLot02(Mockito.any())).thenReturn(ordersDtos);
        redDotExecuteService.getOrderLot02ByWhiteList(ordersDtos,plDbDTO,delayWhit,strMap);

        List<OrdersDto> ordersDtos1=new ArrayList<>();
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setWhseId("WWWWW1");
        ordersDto2.setOrderKey("90002");
        ordersDto2.setIsTimeout(1);
        ordersDto2.setIsKitting(1);
        ordersDto2.setQty(2);
        ordersDto2.setLot("123");
        ordersDto2.setAvailableQty(2);
        ordersDto2.setOrderLineNumber("001");
        ordersDto2.setLottable02("2200002");
        ordersDto2.setIsSupplierDelivery(1);
        ordersDtos1.add(ordersDto2);
        PowerMockito.when(redDotExecuteRepository.getOrderLot02(Mockito.any())).thenReturn(ordersDtos1);
        redDotExecuteService.getOrderLot02ByWhiteList(ordersDtos,plDbDTO,delayWhit,strMap);
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void checkOverTime() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setWhseId("WWWWW1");
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(TIMEOUT,"24");
        strMap.put(WARNING_DAYS,"87");
        redDotExecuteService.checkOverTime(ordersDtos,plDbDTO,strMap);

        List<OverTimeBarcodeDTO> overTimeBarcodeDTOS=new ArrayList<>();
        OverTimeBarcodeDTO overTimeBarcodeDTO=new OverTimeBarcodeDTO();
        overTimeBarcodeDTO.setItemBarcodes("2200001");
        overTimeBarcodeDTOS.add(overTimeBarcodeDTO);
        PowerMockito.when(stepIscpRepository.getValidateBarcode(Mockito.any())).thenReturn(overTimeBarcodeDTOS);
        PowerMockito.when(redDotExecuteRepository.getDelayCheckInfo(Mockito.any())).thenReturn(null);
        redDotExecuteService.checkOverTime(ordersDtos,plDbDTO,strMap);

        List<String> list=new ArrayList<>();
        list.add("2200001");
        PowerMockito.when(redDotExecuteRepository.getDelayCheckInfo(Mockito.any())).thenReturn(list);
        redDotExecuteService.checkOverTime(ordersDtos,plDbDTO,strMap);
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getRedDotSet() throws Exception{
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setWhseId("WWWWW1");
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(2);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        redDotExecuteService.getRedDotSet(redList,ordersDtos);
        Assert.assertTrue(Objects.nonNull(ordersDtos));
    }

    @Test
    public void getLocationName() throws Exception{
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWarehouseId("WWWWW1");
        OrdersDto dto=new OrdersDto();
        redDotExecuteService.getLocationName(plDbDTO,dto);

        plDbDTO.setDbType(1);
        dto.setClassgroup("12");
        redDotExecuteService.getLocationName(plDbDTO,dto);
        Assert.assertTrue(Objects.nonNull(plDbDTO.getWarehouseId()));
    }

    @Test
    public void getRedDotDesc() throws Exception{
        Map<String, String> descMap = new HashMap<>();
        descMap.put(DESC,  "23");
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWmwhseName("1仓");
        plDbDTO.setWarehouseId("WWWWW1");
        OrdersDto dto=new OrdersDto();
        dto.setClassgroup("12");
        dto.setOrderKey("12");
        dto.setOrderLineNumber("12");
        redDotExecuteService.getRedDotDesc(dto,0,plDbDTO);

        redDotExecuteService.getRedDotDesc(dto,1,plDbDTO);
        redDotExecuteService.getRedDotDesc(dto,2,plDbDTO);
        redDotExecuteService.getRedDotDesc(dto,4,plDbDTO);

        dto.setHoldDesc("12");
        dto.setIsHoldCheck(0);
        plDbDTO.setRedDotThreeClass(1);
        redDotExecuteService.getRedDotDesc(dto,3,plDbDTO);
        Assert.assertTrue(Objects.nonNull(descMap));
    }

    @Test
    public void getLackMaterialsAndFreezeRedDesc() throws Exception{
        Map<String, String> descMap = new HashMap<>();
        descMap.put(DESC,  "23");
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWmwhseName("1仓");
        plDbDTO.setWarehouseId("WWWWW1");
        plDbDTO.setRedDotThreeClass(1);
        OrdersDto dto=new OrdersDto();
        dto.setClassgroup("12");
        dto.setOrderKey("12");
        dto.setOrderLineNumber("12");
        redDotExecuteService.getLackMaterialsAndFreezeRedDesc(dto,3,plDbDTO);

        plDbDTO.setRedDotThreeClass(2);
        redDotExecuteService.getLackMaterialsAndFreezeRedDesc(dto,3,plDbDTO);

        plDbDTO.setRedDotThreeClass(3);
        redDotExecuteService.getLackMaterialsAndFreezeRedDesc(dto,3,plDbDTO);

        dto.setHoldDesc("12");
        dto.setIsHoldCheck(0);
        redDotExecuteService.getLackMaterialsAndFreezeRedDesc(dto,5,plDbDTO);
        Assert.assertTrue(Objects.nonNull(descMap));
    }

    @Test
    public void getFreezeRedDesc() throws Exception{
        Map<String, String> descMap = new HashMap<>();
        descMap.put(DESC,  "23");
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWmwhseName("1仓");
        plDbDTO.setWarehouseId("WWWWW1");
        plDbDTO.setRedDotThreeClass(1);
        OrdersDto dto=new OrdersDto();
        dto.setClassgroup("12");
        dto.setOrderKey("12");
        dto.setOrderLineNumber("12");
        dto.setIsHoldCheck(1);
        redDotExecuteService.getFreezeRedDesc(dto,plDbDTO);

        dto.setIsHoldCheck(0);
        redDotExecuteService.getFreezeRedDesc(dto,plDbDTO);
        Assert.assertTrue(Objects.nonNull(descMap));
    }

    @Test
    public void sendRedDotTask() throws Exception{
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setDbType(2);
        plDbDTO.setWmwhseName("1仓");
        plDbDTO.setWarehouseId("WWWWW1");
        plDbDTO.setRedDotThreeClass(1);
        plDbDTO.setXFactoryId("58");
        List<OrdersDto> ordersDtos=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setOrderKey("90001");
        ordersDto1.setIsTimeout(1);
        ordersDto1.setIsKitting(1);
        ordersDto1.setQty(2);
        ordersDto1.setLot("123");
        ordersDto1.setAvailableQty(2);
        ordersDto1.setOrderLineNumber("001");
        ordersDto1.setLottable02("2200001");
        ordersDto1.setIsSupplierDelivery(1);
        ordersDtos.add(ordersDto1);
        Map<String, String> strMap=new HashMap<>();
        strMap.put(ALLOCATION_TIMEOUT,"2");
        strMap.put(X_EMP_NO,"10346719");
        List<RedDotExecuteInfo> redList=new ArrayList<>();
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(1);
        redDotExecuteInfo.setOverTimeCheckRed(0);
        redDotExecuteInfo.setRedDotSubclass(3);
        redDotExecuteInfo.setWhseId("WWWWW1");
        redDotExecuteInfo.setExternreceiptkey("90001");
        redDotExecuteInfo.setExternreceiptnumber("001");
        redList.add(redDotExecuteInfo);
        redDotExecuteService.sendRedDotTask(ordersDtos,strMap,plDbDTO,1);

        redDotExecuteService.sendRedDotTask(ordersDtos,strMap,plDbDTO,0);
        Assert.assertTrue(Objects.nonNull(redList));
    }

    @Test
    public void getLookupCode() throws Exception{
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupCode("10007");
        sysLookupValuesDTOList.add(dto);
        redDotExecuteService.getLookupCode("10007",sysLookupValuesDTOList);
        Assert.assertTrue(Objects.nonNull(dto));
    }

    @Test
    public void getRedDotSysList() throws Exception{
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupCode("10007");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        redDotExecuteService.getRedDotSysList(dto);
        Assert.assertTrue(Objects.nonNull(sysLookupValuesDTOList));
    }

    @Test
    public void queryRedDotInfo() throws Exception{
        RedDotExecuteInfoListVo listVo=new RedDotExecuteInfoListVo();
        RedDotExecuteInfo dto =new RedDotExecuteInfo();
        dto.setPageIndex(INT_1);
        dto.setPageSize(INT_1);

        redDotExecuteService.queryRedDotInfo(dto);

        dto.setBillType("rwck");
        redDotExecuteService.queryRedDotInfo(dto);

        dto.setBillType("qt");
        redDotExecuteService.queryRedDotInfo(dto);
        listVo.setTotal(INT_1);
        Assert.assertTrue(Objects.nonNull(listVo));
    }

    @Test
    public void getPropertiesMap(){
        List<SysLookupValuesDTO> list=new ArrayList<>();
        Map<String, SysLookupValuesDTO> productBaseMap=new HashMap<>();
        Map<String, String> billTypeMap=new HashMap<>();
        Map<String, String> bussTypeMap=new HashMap<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("");
        dto.setLookupMeaning("2343243");
        list.add(dto);
        redDotExecuteService.getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);

        dto.setLookupType("1000076");
        list.add(dto);
        redDotExecuteService.getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);
        dto.setLookupType(LOOKUP_TYPE_1000075);
        list.add(dto);
        redDotExecuteService.getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);

        dto.setLookupType(LOOKUP_TYPE_1000074);
        list.add(dto);
        redDotExecuteService.getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);

        dto.setLookupType(LOOKUP_TYPE_1000071);
        list.add(dto);
        redDotExecuteService.getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);
        Assert.assertTrue(true);
    }

    @Test
    public void coverRowsProperties() {
        // 1 生产基地、仓库名称map
        Map<String, SysLookupValuesDTO> productBaseMap = new HashMap<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("1000071");
        dto.setLookupMeaning("WH1");
        dto.setDescription("部件深圳");
        dto.setAttribute1("深圳工厂");
        dto.setAttribute2("sz");
        dto.setAttribute3("58");
        productBaseMap.put("WH1",dto);

        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        sysLookupValuesDTOList.add(dto);
        SysLookupValuesDTO dt1=new SysLookupValuesDTO();
        dt1.setLookupType("1000074");
        dt1.setLookupMeaning("110");
        dt1.setAttribute1("rwck");
        sysLookupValuesDTOList.add(dt1);
        SysLookupValuesDTO dto2=new SysLookupValuesDTO();
        dto2.setLookupType("1000075");
        dto2.setLookupMeaning("rwck");
        dto2.setDescription("任务出库");
        sysLookupValuesDTOList.add(dto2);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        List<RedDotExecuteInfoVo> rows=new ArrayList<>();
        RedDotExecuteInfoVo vo=new RedDotExecuteInfoVo();
        vo.setBillType("110");
        vo.setWhseId("WH1");
        rows.add(vo);
        redDotExecuteService.coverRowsProperties(rows);

        List<RedDotExecuteInfoVo> rows1=new ArrayList<>();
        RedDotExecuteInfoVo vo1=new RedDotExecuteInfoVo();
        vo1.setBillType("555");
        vo1.setWhseId("WH1");
        rows1.add(vo1);
        redDotExecuteService.coverRowsProperties(rows1);

        // 1 生产基地、仓库名称map
        Map<String, SysLookupValuesDTO> productBaseMap1 = new HashMap<>();
        productBaseMap1.put("WH1",dto);
        List<SysLookupValuesDTO> sysLookupValuesDTOList1=new ArrayList<>();
        sysLookupValuesDTOList1.add(dto);
        sysLookupValuesDTOList1.add(dt1);
        dto2.setLookupMeaning("123");
        sysLookupValuesDTOList1.add(dto2);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList1);
        redDotExecuteService.coverRowsProperties(rows);
        Assert.assertTrue(true);
    }

    @Test
    public void exportExcel() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class);
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setCreatedBy("123");
        dto.setPageIndex(1);
        dto.setPageSize(1);
        dto.setStartRow(1);
        dto.setEndRow(1);
        PowerMockito.when(redDotExecuteRepository.queryRedDotInfoCount(Mockito.any())).thenReturn(1);
        List<RedDotExecuteInfoVo> list = new ArrayList<>();
        RedDotExecuteInfoVo vo=new RedDotExecuteInfoVo();
        vo.setProductBase("深圳工厂");
        list.add(vo);
        PowerMockito.when(redDotExecuteRepository.queryRedDotInfo(Mockito.any())).thenReturn(list);
        redDotExecuteService.exportExcel(dto);

        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO dt1=new SysLookupValuesDTO();
        dt1.setLookupType("1000074");
        dt1.setLookupMeaning("110");
        dt1.setAttribute1("rwck");
        sysLookupValuesDTOList.add(dt1);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(redDotExecuteRepository.queryRedDotInfoCount(Mockito.any())).thenReturn(1);
        PowerMockito.when(redDotExecuteRepository.queryRedDotInfo(Mockito.any())).thenReturn(list);
        dto.setBillType("110");
        redDotExecuteService.exportExcel(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void updateRedDotInfo() throws Exception {
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setWhseId("3245324");
        redDotExecuteService.updateRedDotInfo(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void queryRedDotDetilInfo() throws Exception {
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setDetilType(OVER_TIME_CHECK);
        dto.setWhseId("WH1");
        dto.setExternreceiptkey("9001410");
        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setLottable02("23434");
        list.add(ordersDto);
        PowerMockito.when(redDotExecuteRepository.queryRedDotDetilLot02(Mockito.any())).thenReturn(list);
        PowerMockito.when(redDotExecuteRepository.getDelayWhiteLists(Mockito.any())).thenReturn(list);
        redDotExecuteService.queryRedDotDetilInfo(dto);

        dto.setDetilType(IS_TIME_OUT);
        PowerMockito.when(redDotExecuteRepository.queryRedDotTimeOutDetilInfo(Mockito.any())).thenReturn(null);
        redDotExecuteService.queryRedDotDetilInfo(dto);

        PowerMockito.when(redDotExecuteRepository.queryRedDotTimeOutDetilInfo(Mockito.any())).thenReturn(list);
        redDotExecuteService.queryRedDotDetilInfo(dto);

        dto.setDetilType("Allo");
        PowerMockito.when(redDotExecuteRepository.queryRedDotDetilInfo(Mockito.any())).thenReturn(list);
        redDotExecuteService.queryRedDotDetilInfo(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void queryRedDotDetilLot02() throws Exception {
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setDetilType(OVER_TIME_CHECK);
        dto.setWhseId("WH1");
        dto.setExternreceiptkey("9001410");
        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setLottable02("23434");
        list.add(ordersDto);
        PowerMockito.when(redDotExecuteRepository.queryRedDotDetilLot02(Mockito.any())).thenReturn(list);
        PowerMockito.when(redDotExecuteRepository.getDelayWhiteLists(Mockito.any())).thenReturn(list);
        redDotExecuteService.queryRedDotDetilLot02(dto);

        PowerMockito.when(redDotExecuteRepository.queryRedDotDetilLot02(Mockito.any())).thenReturn(list);
        List<OrdersDto> list1=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setLottable02("1");
        list1.add(ordersDto1);
        PowerMockito.when(redDotExecuteRepository.getDelayWhiteLists(Mockito.any())).thenReturn(list1);
        redDotExecuteService.queryRedDotDetilLot02(dto);

        PowerMockito.when(redDotExecuteRepository.queryRedDotDetilLot02(Mockito.any())).thenReturn(list);
        PowerMockito.when(redDotExecuteRepository.getDelayWhiteLists(Mockito.any())).thenReturn(list1);
        List<OverTimeBarcodeDTO> overTimeInDTOList =new ArrayList<>();
        OverTimeBarcodeDTO overTimeInDTO=new OverTimeBarcodeDTO();
        overTimeInDTO.setItemBarcodes("21");
        overTimeInDTOList.add(overTimeInDTO);
        //获取数据字典
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType("1000059");
        dto4.setLookupCode("100005900004");
        dto4.setLookupMeaning("87");
        sysLookupValuesDTOList.add(dto4);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(stepIscpRepository.getValidateBarcode(Mockito.any())).thenReturn(overTimeInDTOList);
        redDotExecuteService.queryRedDotDetilLot02(dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void executeRedDot() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setRedDotSubclass(0);
        dto.setWhseId("WH1");
        dto.setExternreceiptkey("90001");
        dto.setExecuteOverMin(9);
        dto.setExecuteMin(9);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO valuesDTO=new SysLookupValuesDTO();
        valuesDTO.setLookupType("1000076");
        valuesDTO.setLookupCode("100007600003");
        valuesDTO.setLookupMeaning("10");
        sysLookupValuesDTOList.add(valuesDTO);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(redDotExecuteRepository.getRedDotExecuteInfo(Mockito.any())).thenReturn(null);
        try {
            redDotExecuteService.executeRedDot(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<RedDotExecuteInfo> redList=new ArrayList<>();
        redList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(redDotExecuteRepository.getRedDotExecuteInfo(Mockito.any())).thenReturn(redList);
        try {
            redDotExecuteService.executeRedDot(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }

        dto.setExecuteOverMin(100);
        dto.setExecuteMin(100);
        List<RedDotExecuteInfo> redList1=new ArrayList<>();
        redList1.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(redDotExecuteRepository.getRedDotExecuteInfo(Mockito.any())).thenReturn(redList1);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("测试");
        redDotExecuteService.executeRedDot(dto);

        dto.setRedDotSubclass(1);
        dto.setExternreceiptnumber("0001");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(redDotExecuteRepository.getRedDotExecuteInfo(Mockito.any())).thenReturn(redList1);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("测试");
        redDotExecuteService.executeRedDot(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void executeNotOverTimeCheckRedDot() throws Exception {
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setRedDotSubclass(2);
        redDotExecuteService.executeNotOverTimeCheckRedDot(dto);

        dto.setRedDotSubclass(1);
        redDotExecuteService.executeNotOverTimeCheckRedDot(dto);

        dto.setRedDotSubclass(3);
        redDotExecuteService.executeNotOverTimeCheckRedDot(dto);

        dto.setRedDotSubclass(4);
        redDotExecuteService.executeNotOverTimeCheckRedDot(dto);

        dto.setRedDotSubclass(5);
        redDotExecuteService.executeNotOverTimeCheckRedDot(dto);

        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setRedDotThreeClass(2);
        list.add(ordersDto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void executeHoldRedDot() throws Exception {
        redDotExecuteService.executeHoldRedDot(null,null);

        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setHoldDesc("343");
        ordersDto.setSku("32");
        list.add(ordersDto);
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setWhseId("WH1");
        redDotExecuteService.executeHoldRedDot(list,dto);

        PowerMockito.when(redDotExecuteRepository.getOrderHoldInfo(Mockito.any())).thenReturn(list);
        redDotExecuteService.executeHoldRedDot(list,dto);

        List<OrdersDto> list1=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setHoldDesc("343");
        ordersDto1.setSku("32");
        ordersDto1.setHoldCode("QCFAILED");
        list1.add(ordersDto1);
        PowerMockito.when(redDotExecuteRepository.getOrderHoldInfo(Mockito.any())).thenReturn(list1);
        redDotExecuteService.executeHoldRedDot(list1,dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void executeLackMateRedDot() throws Exception {
        redDotExecuteService.executeLackMateRedDot(null,null);

        List<OrdersDto> list=new ArrayList<>();
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setRef11("343");
        list.add(ordersDto);
        redDotExecuteService.executeLackMateRedDot(list,null);

        List<OrdersDto> list1=new ArrayList<>();
        OrdersDto ordersDto1=new OrdersDto();
        ordersDto1.setRef11("110");
        ordersDto1.setSku("43120");
        ordersDto1.setIsSupplierDelivery(0);
        list1.add(ordersDto1);
        RedDotExecuteInfo dto=new RedDotExecuteInfo();
        dto.setWhseId("WH1");
        redDotExecuteService.executeLackMateRedDot(list1,dto);

        PowerMockito.when(redDotExecuteRepository.getReceiptBarcode(Mockito.any())).thenReturn(list1);
        redDotExecuteService.executeLackMateRedDot(list1,dto);

        List<OrdersDto> list2=new ArrayList<>();
        OrdersDto ordersDto2=new OrdersDto();
        ordersDto2.setRef11("110");
        ordersDto2.setSku("43120");
        ordersDto2.setIsSupplierDelivery(1);
        list2.add(ordersDto2);
        PowerMockito.when(redDotExecuteRepository.getReceiptBarcode(Mockito.any())).thenReturn(list2);
        redDotExecuteService.executeLackMateRedDot(list2,dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void executeRedDotUpdateStatus() throws Exception {
        RedDotExecuteInfo dto =new RedDotExecuteInfo();
        dto.setRedDotSubclass(INT_0);
        dto.setWhseId("WH1");
        List<OrdersDto> ordersDtoList=new ArrayList<>();
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);

        dto.setRedDotSubclass(INT_1);
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);

        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setRef11("343");
        ordersDtoList.add(ordersDto);

        SysLookupValuesDTO dt1=new SysLookupValuesDTO();
        dt1.setLookupType("1000074");
        dt1.setLookupMeaning("110");
        dt1.setAttribute1("rwck");
        sysLookupValuesDTOList.add(dt1);

        SysLookupValuesDTO dt2=new SysLookupValuesDTO();
        dt2.setLookupType("1000076");
        dt2.setLookupCode(LOOKUP_CODE_100007600004);
        dt2.setLookupMeaning("110");
        dt2.setAttribute1("rwck");
        sysLookupValuesDTOList.add(dt2);
        List<PlDbDTO> whsetList1=new ArrayList<>();
        PlDbDTO plDbDTO1=new PlDbDTO();
        plDbDTO1.setWarehouseId("WH22");
        whsetList1.add(plDbDTO1);
        PowerMockito.when(redDotExecuteRepository.getInforWarehouseList(Mockito.any())).thenReturn(whsetList1);
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);

        List<PlDbDTO> whsetList=new ArrayList<>();
        PlDbDTO plDbDTO=new PlDbDTO();
        plDbDTO.setWarehouseId("WH1");
        whsetList.add(plDbDTO);
        PowerMockito.when(redDotExecuteRepository.getInforWarehouseList(Mockito.any())).thenReturn(whsetList);
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);

        SysLookupValuesDTO dt3=new SysLookupValuesDTO();
        dt3.setLookupType("1000071");
        dt3.setLookupMeaning("WH1");
        dt3.setAttribute3("rwck");
        dt3.setDescription("rwck");
        sysLookupValuesDTOList.add(dt3);
        PowerMockito.when(redDotExecuteRepository.getInforWarehouseList(Mockito.any())).thenReturn(whsetList);
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);

        dto.setRedDotSubclass(INT_3);
        PowerMockito.when(redDotExecuteRepository.getInforWarehouseList(Mockito.any())).thenReturn(whsetList);
        redDotExecuteService.executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);
        Assert.assertTrue(Objects.nonNull(ordersDtoList));
    }

    @Test
    public void getRedDotCodeBySend() throws Exception {
        List<SysLookupValuesDTO> sysLookupValuesDTOS=new ArrayList<>();
        String whseId="WH14";
        RedDotTaskDTO redDotTaskDTO=new RedDotTaskDTO();
        Map<String, String> mapHeader=new HashMap<>();
        redDotExecuteService.getRedDotCodeBySend(sysLookupValuesDTOS,whseId,redDotTaskDTO,mapHeader);

        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupMeaning("WH15");
        sysLookupValuesDTOS.add(dto);
        redDotExecuteService.getRedDotCodeBySend(sysLookupValuesDTOS,whseId,redDotTaskDTO,mapHeader);

        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupMeaning("WH14");
        dto1.setDescription("DIS10");
        sysLookupValuesDTOS.add(dto1);
        redDotExecuteService.getRedDotCodeBySend(sysLookupValuesDTOS,whseId,redDotTaskDTO,mapHeader);
        Assert.assertTrue(true);
    }
}
