/*Started by AICoder, pid:038ac5544df54408b08f56d32a49b3c9*/
package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.zte.application.infor.impl.InforImesServiceImpl;
import com.zte.application.infor.impl.InventoryholdRecordServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.InforImesRepository;
import com.zte.domain.model.step.TechItemStoreRepository;
import com.zte.interfaces.infor.dto.EdiSoSImesDTO;
import com.zte.interfaces.infor.dto.InforImesQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto;
import com.zte.interfaces.infor.vo.LocVo;
import com.zte.interfaces.infor.vo.ZteinboundserialVo;
import com.zte.interfaces.step.dto.TechItemStoreDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;
import java.util.concurrent.Executor;

import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @date 2021-12-08 15:39
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class})
public class InforImesServiceImplTest {
    @InjectMocks
    private InforImesServiceImpl inforImesServiceImpl;

    @Mock
    private InforImesRepository inforImesRepository;

    @Mock
    private TechItemStoreRepository techItemStoreRepository;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class);
    }

    @Test
    public void queryEdiSoSByKey2() throws Exception{

        String externalorderkey2 = null;
        Whitebox.invokeMethod(inforImesServiceImpl,"queryEdiSoSByKey2", externalorderkey2);

        externalorderkey2 = "23523562";
        List<EdiSoSImesDTO> ediSoSImesList = new ArrayList<>();
        PowerMockito.when(inforImesRepository.queryEdiSoSByKey2(externalorderkey2)).thenReturn(ediSoSImesList);
        Whitebox.invokeMethod(inforImesServiceImpl,"queryEdiSoSByKey2", externalorderkey2);

        EdiSoSImesDTO ediSoSImesDTO = new EdiSoSImesDTO();
        ediSoSImesDTO.setWhseid("2352362");
        ediSoSImesList.add(ediSoSImesDTO);
        PowerMockito.when(inforImesRepository.queryEdiSoSByKey2(externalorderkey2)).thenReturn(ediSoSImesList);
        List<String> whseidList = new ArrayList<>();
        PowerMockito.when(inforImesRepository.queryWhseidByKey2(externalorderkey2)).thenReturn(whseidList);
        Whitebox.invokeMethod(inforImesServiceImpl,"queryEdiSoSByKey2", externalorderkey2);

        whseidList.add("3253463643");
        PowerMockito.when(inforImesRepository.queryWhseidByKey2(externalorderkey2)).thenReturn(whseidList);
        Integer integer = 1;
        PowerMockito.when(inforImesRepository.queryAllStatus(whseidList, externalorderkey2)).thenReturn(integer);
        Whitebox.invokeMethod(inforImesServiceImpl,"queryEdiSoSByKey2", externalorderkey2);
        List<EdiSoSImesDTO> res = inforImesServiceImpl.queryEdiSoSByKey2(externalorderkey2);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void queryVeneerInfo() throws Exception{

        String externalorderkey2 = null;
        String serialnumber = null;
        String billType = "1111";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryVeneerInfo", externalorderkey2, serialnumber, billType);

        externalorderkey2 = "23523623";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryVeneerInfo", externalorderkey2, serialnumber, billType);

        serialnumber = "3523623";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryVeneerInfo", externalorderkey2, serialnumber, billType);

        billType = "20";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryVeneerInfo", externalorderkey2, serialnumber, billType);
        EdiSoSImesDTO dto = new EdiSoSImesDTO();
        dto.setExternalorderkey2("434");
        PowerMockito.when(inforImesRepository.queryVeneerInfo(Mockito.any(),Mockito.any())).thenReturn(dto);
        EdiSoSImesDTO res = inforImesServiceImpl.queryVeneerInfo(externalorderkey2, serialnumber, billType);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void queryProcessedInfo() throws Exception{
        EdiSoSImesDTO dto = new EdiSoSImesDTO();
        dto.setExternalorderkey2("434");
        String externalorderkey2 = null;
        String serialnumber = null;
        Whitebox.invokeMethod(inforImesServiceImpl,"queryProcessedInfo", externalorderkey2, serialnumber);

        externalorderkey2 = "3523362";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryProcessedInfo", externalorderkey2, serialnumber);
        serialnumber = "2352362362";
        Whitebox.invokeMethod(inforImesServiceImpl,"queryProcessedInfo", externalorderkey2, serialnumber);

        List<String> whseidList = new ArrayList<>();
        PowerMockito.when(inforImesRepository.queryWhseidByKey2(externalorderkey2)).thenReturn(whseidList);
        Whitebox.invokeMethod(inforImesServiceImpl,"queryProcessedInfo", externalorderkey2, serialnumber);
        whseidList.add("2352362");
        inforImesRepository.queryProcessedInfo(externalorderkey2, serialnumber, whseidList);
        PowerMockito.when(inforImesRepository.queryProcessedInfo(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dto);
        EdiSoSImesDTO  res = inforImesServiceImpl.queryProcessedInfo(externalorderkey2, serialnumber);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void getIsReturnReceived() throws Exception{
        InforImesReturnWarehouseDto dto = null;
        try {
            inforImesServiceImpl.getIsReturnReceived(dto);
        } catch (Exception e) {
            assertTrue(e instanceof NullPointerException);
        }

        Integer count=0;
        InforImesReturnWarehouseDto dto1=new InforImesReturnWarehouseDto();
        dto1.setProdplanNo("324442");

        List<String> list=new ArrayList<>();
        PowerMockito.when(inforImesRepository.getReturnWarehouse(dto1)).thenReturn(list);
        Whitebox.invokeMethod(inforImesServiceImpl,"getIsReturnReceived", dto1);

        List<String> list1=new ArrayList<>();
        list1.add("214324");
        PowerMockito.when(inforImesRepository.getReturnWarehouse(dto1)).thenReturn(list1);
        Whitebox.invokeMethod(inforImesServiceImpl,"getIsReturnReceived", dto1);
        Assert.assertNotNull(count);
    }

    @Test
    public void getReturnQty() throws Exception{
        List<InforImesReturnQtyDto> list=new ArrayList<>();
        InforImesReturnQtyDto qtyDto=new InforImesReturnQtyDto();
        qtyDto.setReturnQty(1);
        list.add(qtyDto);
        InforImesQtyDto dto=new InforImesQtyDto();
        dto.setProdplanNo("324442");
        Whitebox.invokeMethod(inforImesServiceImpl,"getReturnQty", dto);
        Assert.assertNotNull(list);
    }

    @Test
    public void getIssuedQty() throws Exception{
        List<InforImesReturnQtyDto> list=new ArrayList<>();
        InforImesReturnQtyDto qtyDto=new InforImesReturnQtyDto();
        qtyDto.setIssuedQty(1);
        list.add(qtyDto);
        InforImesQtyDto dto=new InforImesQtyDto();
        dto.setProdplanNo("324442");
        Whitebox.invokeMethod(inforImesServiceImpl,"getIssuedQty", dto);
        Assert.assertNotNull(list);
    }

    @Test
    public void getReqQty() throws Exception{
        List<InforImesReturnQtyDto> list=new ArrayList<>();
        InforImesReturnQtyDto qtyDto=new InforImesReturnQtyDto();
        qtyDto.setReqQty(1);
        list.add(qtyDto);
        InforImesQtyDto dto=new InforImesQtyDto();
        dto.setProdplanNo("324442");
        Whitebox.invokeMethod(inforImesServiceImpl,"getReqQty", dto);
        Assert.assertNotNull(list);
    }

    @Test
    public void getSerialnumber() throws Exception{
        ZteinboundserialVo dto=new ZteinboundserialVo();
        dto.setSerialnumber("1");
        BusiAssertException.isEmpty(dto.getExternreceiptkey(), MessageId.NO_PARAMS);
        Whitebox.invokeMethod(inforImesServiceImpl,"getSerialnumber", dto);

        dto.setExternreceiptkey("1");
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.NO_PARAMS);
        Whitebox.invokeMethod(inforImesServiceImpl,"getSerialnumber", dto);

        dto.setWhseid("1");
        Whitebox.invokeMethod(inforImesServiceImpl,"getSerialnumber", dto);
        List<String> list=new ArrayList<>();
        list.add("1");
        Assert.assertNotNull(list);
    }

    @Test
    public void checkInforLoc() throws Exception{
        LocVo dto=new LocVo();
        dto.setWhseid("1");
        BusiAssertException.isEmpty(dto.getLoc(), MessageId.NO_PARAMS);
        Whitebox.invokeMethod(inforImesServiceImpl,"checkInforLoc", dto);

        dto.setWhseid("");
        dto.setLoc("1");
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.NO_PARAMS);
        Whitebox.invokeMethod(inforImesServiceImpl,"checkInforLoc", dto);

        dto.setWhseid("1");
        PowerMockito.when(inforImesRepository.checkInforLoc(dto)).thenReturn(0);
        BusiAssertException.isTrue(true, MessageId.CHECK_INFORLOC);
        Whitebox.invokeMethod(inforImesServiceImpl,"checkInforLoc", dto);

        dto.setWhseid("1");
        PowerMockito.when(inforImesRepository.checkInforLoc(dto)).thenReturn(1);
        Whitebox.invokeMethod(inforImesServiceImpl,"checkInforLoc", dto);

        Assert.assertNotNull(ServiceDataUtil.getSuccess());
    }

    @Test
    public void getTechItemStore() throws Exception{
        TechItemStoreDTO dto=new TechItemStoreDTO();
        dto.setBomId("123");
        PowerMockito.when(techItemStoreRepository.getTechItemStore("223")).thenReturn(dto);
        Whitebox.invokeMethod(inforImesServiceImpl,"getTechItemStore", "223");
        Assert.assertNotNull(dto);
    }

    @Test
    public void updateTechItemStore() throws Exception{
        TechItemStoreDTO dto=new TechItemStoreDTO();
        dto.setBomId("123");
        PowerMockito.when(techItemStoreRepository.checkTechItemStore(Mockito.any())).thenReturn(0);
        techItemStoreRepository.insertTechItemStore(dto);
        Whitebox.invokeMethod(inforImesServiceImpl,"updateTechItemStore", dto);

        PowerMockito.when(techItemStoreRepository.checkTechItemStore(Mockito.any())).thenReturn(1);
        techItemStoreRepository.updateTechItemStore(dto);
        Whitebox.invokeMethod(inforImesServiceImpl,"updateTechItemStore", dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void queryEdiSoSByKey2AndSku() throws Exception{
        EdiSoSImesDTO ediSoSImesDTO = new EdiSoSImesDTO();
        ediSoSImesDTO.setExternalorderkey2("123");
        ediSoSImesDTO.setSku("1");
        List<String> whseidList = Arrays.asList();
        PowerMockito.when(inforImesRepository.queryEdiSoSByKey2AndSku(Mockito.any())).thenReturn(whseidList);
        inforImesServiceImpl.queryEdiSoSByKey2AndSku(ediSoSImesDTO);
        whseidList = Arrays.asList("WMWHSE1");
        PowerMockito.when(inforImesRepository.queryEdiSoSByKey2AndSku(Mockito.any())).thenReturn(whseidList);
        List<String> teamGroup = Arrays.asList();
        PowerMockito.when(inforImesRepository.queryEdiSoSByWhseidAndSku(Mockito.any(),Mockito.any())).thenReturn(teamGroup);
        inforImesServiceImpl.queryEdiSoSByKey2AndSku(ediSoSImesDTO);
        teamGroup = Arrays.asList("贴片阻容库");
        PowerMockito.when(inforImesRepository.queryEdiSoSByWhseidAndSku(Mockito.any(),Mockito.any())).thenReturn(teamGroup);
        inforImesServiceImpl.queryEdiSoSByKey2AndSku(ediSoSImesDTO);
        Assert.assertNotNull(ediSoSImesDTO);
    }
}
/*Ended by AICoder, pid:038ac5544df54408b08f56d32a49b3c9*/