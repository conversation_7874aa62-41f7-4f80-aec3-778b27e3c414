/* Started by AICoder, pid:bf750h0e77bd14614486084530a1c60c14a1661c */
package com.zte.application.infor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.DigitQCDataBaseServiceImpl;
import com.zte.domain.model.infor.DigitObjectValueDTO;
import com.zte.domain.model.infor.DigitQCDataBaseHeadDTO;
import com.zte.domain.model.infor.DigitQCDataBaseRepository;
import com.zte.interfaces.infor.dto.DigitQCDataBaseDTO;
import com.zte.interfaces.infor.dto.ItemSplitRespDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.eq;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/4/14 16:48
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class})
public class DigitQCDataBaseServiceImplTest {
    @InjectMocks
    private DigitQCDataBaseServiceImpl digitQCDataBaseService;

    @Mock
    private DigitQCDataBaseRepository digitQCDataBaseRepository;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private String authToken;

    @Mock
    private String redDotUrl;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class);
    }

    @Test
    public void pullInforItemNoJob() throws Exception{
        digitQCDataBaseService.pullInforItemNoJob();
        PowerMockito.when(digitQCDataBaseRepository.selectWmwhseId()).thenReturn(Arrays.asList("1"));
        digitQCDataBaseService.pullInforItemNoJob();
        PowerMockito.when(digitQCDataBaseRepository.queryItemNum(Mockito.any())).thenReturn(10);
        digitQCDataBaseService.pullInforItemNoJob();
        List<String> items = Arrays.asList("2","1");
        PowerMockito.when(digitQCDataBaseRepository.queryItemsByPage(anyObject(),eq(0),eq(500))).thenReturn(items);
        digitQCDataBaseService.pullInforItemNoJob();
        String result = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"total\": 17,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"itemNo\": \"002010100005\",\n" +
                "                \"itemName\": \"高压高电流运放\",\n" +
                "                \"supplierName\": \"ZTE (H.K.) LIMITED\",\n" +
                "                \"supplierNo\": \"90001000\",\n" +
                "\t\t\t\t\"packType\": \"20\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper1 = new ObjectMapper();
        JsonNode json1 = objectMapper1.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json1);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        List<ItemSplitRespDTO> itemSplitRespDTOS = new ArrayList<>();
        itemSplitRespDTOS.add(new ItemSplitRespDTO(){{
            setSupplierNo("1");
        }});
        itemSplitRespDTOS.add(new ItemSplitRespDTO(){{
            setSupplierNo("NO1");
        }});
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.any())).thenReturn(itemSplitRespDTOS);
        digitQCDataBaseService.pullInforItemNoJob();
        PowerMockito.when(digitQCDataBaseRepository.selectQCDataBase()).thenReturn(1);
        digitQCDataBaseService.pullInforItemNoJob();
        PowerMockito.when(digitQCDataBaseRepository.queryAddItemInfo(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList("1"));
        digitQCDataBaseService.pullInforItemNoJob();
        PowerMockito.when(digitQCDataBaseRepository.queryItemNoIsExist(Mockito.any())).thenReturn(1);
        digitQCDataBaseService.pullInforItemNoJob();
        Assert.assertNotNull(itemSplitRespDTOS);
    }

    @Test
    public void getQCDataBaseList() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        digitQCDataBaseService.getQCDataBaseList(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void exportQCDataBaseList() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        String xEmpNo = "1";
        PowerMockito.when(digitQCDataBaseRepository.getDigitQCDataBaseTotal(Mockito.any())).thenReturn(1);
        List<DigitQCDataBaseHeadDTO> list = new ArrayList<>();
        list.add(new DigitQCDataBaseHeadDTO(){{
            setCustom1("1");
        }});
        PowerMockito.when(digitQCDataBaseRepository.getDigitQCDataBaseVo(Mockito.any())).thenReturn(list);
        digitQCDataBaseService.exportQCDataBaseList(dto, xEmpNo);
        Assert.assertNotNull(dto);
    }

    @Test
    public void loseQCDataBase() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        digitQCDataBaseService.loseQCDataBase(dto);
        dto.setSerialKeys(Arrays.asList("1"));
        digitQCDataBaseService.loseQCDataBase(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void effectQCDataBase() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        digitQCDataBaseService.effectQCDataBase(dto);
        dto.setSerialKeys(Arrays.asList("1"));
        digitQCDataBaseService.effectQCDataBase(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void updateQCDataBase() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        dto.setPackType("1");
        digitQCDataBaseService.updateQCDataBase(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void importQCDataBase() throws Exception{
        List<DigitQCDataBaseDTO> list = new ArrayList<>();
        digitQCDataBaseService.importQCDataBase(list);
        list.add(new DigitQCDataBaseDTO(){{
            setPackType("1");
            setItemNo("1");
            setSupplierNo("NO1");
        }});
        try {
            digitQCDataBaseService.importQCDataBase(list);
        }catch (Exception ex) {
        }
        String result = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0001\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"total\": 1,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"itemNo\": \"002010100005\",\n" +
                "                \"itemName\": \"高压高电流运放\",\n" +
                "                \"supplierName\": \"WINTECH MICROELECTRONICS （HK）LTD\",\n" +
                "                \"supplierNo\": \"12107600\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = objectMapper.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCDataBaseService.importQCDataBase(list);
        result = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"total\": 1,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"itemNo\": \"002010100005\",\n" +
                "                \"itemName\": \"高压高电流运放\",\n" +
                "                \"supplierName\": \"WINTECH MICROELECTRONICS （HK）LTD\",\n" +
                "                \"supplierNo\": \"12107600\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper1 = new ObjectMapper();
        JsonNode json1 = objectMapper1.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json1);
        List<ItemSplitRespDTO> itemSplitRespDTOS = new ArrayList<>();
        itemSplitRespDTOS.add(new ItemSplitRespDTO(){{
            setSupplierNo("1");
        }});
        itemSplitRespDTOS.add(new ItemSplitRespDTO(){{
            setSupplierNo("NO1");
        }});
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.any())).thenReturn(itemSplitRespDTOS);
        digitQCDataBaseService.importQCDataBase(list);
        PowerMockito.when(digitQCDataBaseRepository.queryIsExist(Mockito.any())).thenReturn(1);
        digitQCDataBaseService.importQCDataBase(list);

        Assert.assertNotNull(list);
    }

    @Test
    public void importObjectInfo() {
        List<DigitQCDataBaseDTO> list = new ArrayList<>();
        digitQCDataBaseService.importObjectInfo(list);
        list.add(new DigitQCDataBaseDTO(){{
            setItemNo("1");
        }});
        digitQCDataBaseService.importObjectInfo(list);
        PowerMockito.when(digitQCDataBaseRepository.queryObjectExist(Mockito.any())).thenReturn(1);
        digitQCDataBaseService.importObjectInfo(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void getObjectInfo() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        dto.setItemNo("1");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        digitQCDataBaseService.getObjectInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void deleteObjectInfo() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        digitQCDataBaseService.deleteObjectInfo(dto);
        dto.setSerialKeys(Arrays.asList("1"));
        digitQCDataBaseService.deleteObjectInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void exportObjectInfo() {
        DigitQCDataBaseDTO dto = new DigitQCDataBaseDTO();
        String xEmpNo = "1";
        PowerMockito.when(digitQCDataBaseRepository.getObjectValueTotal(Mockito.any())).thenReturn(1);
        List<DigitObjectValueDTO> list = new ArrayList<>();
        list.add(new DigitObjectValueDTO(){{
            setType("1");
        }});
        PowerMockito.when(digitQCDataBaseRepository.getObjectValueVo(Mockito.any())).thenReturn(list);
        digitQCDataBaseService.exportObjectInfo(dto, xEmpNo);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getItemMasterInfo() throws Exception{
        String itemNo = "111";
        String result = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0001\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"total\": 1,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"itemNo\": \"002010100005\",\n" +
                "                \"itemName\": \"高压高电流运放\",\n" +
                "                \"supplierName\": \"WINTECH MICROELECTRONICS （HK）LTD\",\n" +
                "                \"supplierNo\": \"12107600\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = objectMapper.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCDataBaseService.getItemMasterInfo(itemNo);
        result = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"total\": 1,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"itemNo\": \"002010100005\",\n" +
                "                \"itemName\": \"高压高电流运放\",\n" +
                "                \"supplierName\": \"WINTECH MICROELECTRONICS （HK）LTD\",\n" +
                "                \"supplierNo\": \"12107600\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper1 = new ObjectMapper();
        JsonNode json1 = objectMapper1.readTree(result);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json1);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(result);
        digitQCDataBaseService.getItemMasterInfo(itemNo);
        Assert.assertNotNull(itemNo);
    }

}
/* Ended by AICoder, pid:bf750h0e77bd14614486084530a1c60c14a1661c */