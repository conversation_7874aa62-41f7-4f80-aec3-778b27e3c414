package com.zte.application.infor;

import com.zte.application.infor.impl.InforLottableDealServiceImpl;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.infor.LotattributeDealLog;
import com.zte.domain.model.infor.LotattributeDealLogRepository;
import com.zte.domain.model.material.InforLottableDealRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.interfaces.infor.dto.InforLottablesDTO;
import com.zte.interfaces.infor.dto.LotattributeDealLogDTO;
import com.zte.interfaces.infor.vo.LotattributeDealLogListVO;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.resourcewarehouse.common.redis.RedisHelper;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Qualifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.INT_7;
import static com.zte.common.utils.NumConstant.INT_1;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({StringUtils.class, RedisSerialNoUtil.class, RedisHelper.class,
        BusiAssertException.class})
public class InforLottableDealServiceImplTest {

    @InjectMocks
    InforLottableDealServiceImpl inforLottableDealServiceImpl;
    @Mock
    private InforLottableDealRepository inforLottableDealRepository;
    @Mock
    private InforBarcodeCenterRepository barcodeCenterRepository;
    @Mock
    private OnlineFallBackApplyBillRepository applyBillRepository;
    @Mock
    private LotattributeDealLogRepository lotattributeDealLogRepository;
    @Mock
    @Qualifier("thplExecutor")
    private Executor thplExecutor;

    @Before
    public void init() {
        PowerMockito.mockStatic(StringUtils.class, RedisSerialNoUtil.class, RedisHelper.class,
                BusiAssertException.class);
    }

    @Test
    public void splitBarcodeList() throws Exception {

        InforLottablesDTO dto = new InforLottablesDTO();
        List<String> list = new ArrayList<>();
        dto.setItemBarcodeList(list);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "splitBarcodeList", dto);

        for (int i=0; i<501; i++) {
            list.add(i + "");
        }
        dto.setItemBarcodeList(list);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "splitBarcodeList", dto);

        list.clear();
        list.add("************");
        dto.setItemBarcodeList(list);
        dto.setSrcLottable09("30");
        dto.setDstLottable09("10");
        dto.setItemNo("21342154");
        dto.setItemUuid("234252");
        dto.setUpdatedBy("srm");
        dto.setUpdateReason("SRM更新拆分状态");
        PowerMockito.when(inforLottableDealRepository.insertInforLottableDeal(Mockito.anyObject())).thenReturn(1);
        List<String> wms = new ArrayList<>();
        PowerMockito.when(barcodeCenterRepository.getEnvWmwhids()).thenReturn(wms);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "splitBarcodeList", dto);

        wms.add("WMWHSE1");
        PowerMockito.when(barcodeCenterRepository.getEnvWmwhids()).thenReturn(wms);
        List<LotattributeDealLog> lotattributeDealLogList = new ArrayList<>();
        LotattributeDealLog lotattributeDealLog = new LotattributeDealLog();
        lotattributeDealLog.setLottable02("23425");
        lotattributeDealLogList.add(lotattributeDealLog);
        PowerMockito.when(lotattributeDealLogRepository.insertLotattributeDealLog(lotattributeDealLogList)).thenReturn(1);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "getSerialNo");
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "splitBarcodeList", dto);
        Assert.assertTrue(Objects.nonNull(wms));

    }

    @Test
    public void getSerialNo() throws Exception {
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseIdWithDate(LOT, IWMS_DATAWB, DEAL_LOTTABLES, INT_1, INT_7)).thenReturn("111");
        String res = inforLottableDealServiceImpl.getSerialNo();
        Assert.assertTrue(Objects.nonNull(res));
    }


    @Test
    public void getInforLottableLogList() throws Exception {
        LotattributeDealLogDTO dto = new LotattributeDealLogDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        LotattributeDealLogListVO listVo = new LotattributeDealLogListVO();
        PowerMockito.when(lotattributeDealLogRepository.getLotattributeDealLogListVOTotal(dto)).thenReturn(1);
        List<LotattributeDealLogDTO> lotattributeDealLogDTOList = new ArrayList<>();
        PowerMockito.when(lotattributeDealLogRepository.getLotattributeDealLogList(dto)).thenReturn(lotattributeDealLogDTOList);
        LotattributeDealLogListVO res = inforLottableDealServiceImpl.getInforLottableLogList(dto);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void updateInforLottableLog() throws Exception {
        String runNormal = "Y";
        LotattributeDealLogDTO dto = new LotattributeDealLogDTO();
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "updateInforLottableLog", dto);
        Assert.assertTrue(Objects.nonNull(runNormal));
    }


    @Test
    public void dataArchiving() throws Exception{

        List<StSysLookupValuesDTO> values = new ArrayList<>();
        PowerMockito.when(applyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(values);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "dataArchiving");

        List<StSysLookupValuesDTO> values2 = new ArrayList<>();
        StSysLookupValuesDTO stSysLookupValuesDTO = new StSysLookupValuesDTO();
        stSysLookupValuesDTO.setLookupMeaning("0");
        values2.add(stSysLookupValuesDTO);
        PowerMockito.when(applyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(values2);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "dataArchiving");

        List<StSysLookupValuesDTO> values3 = new ArrayList<>();
        StSysLookupValuesDTO stSysLookupValuesDTO1 = new StSysLookupValuesDTO();
        stSysLookupValuesDTO1.setLookupMeaning("100");
        values3.add(stSysLookupValuesDTO1);
        PowerMockito.when(applyBillRepository.getSysLookupValues(Mockito.anyString())).thenReturn(values3);
        List<String> serialNoList = new ArrayList<>();
        PowerMockito.when(inforLottableDealRepository.selectInforLottableDeal(Mockito.anyInt())).thenReturn(serialNoList);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "dataArchiving");

        List<String> serialNoList2 = new ArrayList<>();
        for (int i=0; i<600; i++) {
            serialNoList2.add(i + "");
        }
        PowerMockito.when(inforLottableDealRepository.selectInforLottableDeal(Mockito.anyInt())).thenReturn(serialNoList2);
        PowerMockito.when(inforLottableDealRepository.dataArchiving(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(inforLottableDealRepository.deleteInforLottableDeal(Mockito.anyList())).thenReturn(1);
        Whitebox.invokeMethod(inforLottableDealServiceImpl, "dataArchiving");
        Assert.assertTrue(Objects.nonNull(serialNoList2));
    }


}
