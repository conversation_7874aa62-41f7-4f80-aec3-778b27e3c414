package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.infor.impl.WarehouseReplenishmentPathServiceImpl;
import com.zte.domain.model.infor.InforWmsRepublishRepository;
import com.zte.domain.model.infor.WarehouseRoadWorkRepository;
import com.zte.interfaces.infor.dto.RepublishRouteDTO;
import com.zte.interfaces.infor.dto.WarehouseReplenishmentPathDTO;
import com.zte.interfaces.infor.vo.WarehouseRepPathWorkListVO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.concurrent.Executor;

import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, Executor.class, ExcelUtil.class, EmailUtil.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, JSON.class, Tools.class, ApprovalFlowClient.class})
public class WarehouseReplenishmentPathServiceImplTest {

    @Mock
    private WarehouseRoadWorkRepository mockWarehouseRoadWorkRepository;
    @Mock
    private InforWmsRepublishRepository mockInforWmsRepublishRepository;

    @InjectMocks
    private WarehouseReplenishmentPathServiceImpl warehouseReplenishmentPathServiceImplUnderTest;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RemoteServiceDataUtil.class, EmailUtil.class, FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class, org.apache.commons.lang3.StringUtils.class, ApprovalFlowClient.class);
    }

    @Test
    public void testGetReplenishmentPathList() {
        // Setup
        final WarehouseReplenishmentPathDTO dto = new WarehouseReplenishmentPathDTO();
        dto.setWarehouse("house");
        dto.setWhseid("whseid");
        dto.setRepType("1");
        dto.setWarehouseArea("area");
        dto.setPageSize(10);
        dto.setPageIndex(1);
        when(mockWarehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(0);

        Assert.assertThrows(Exception.class,()-> warehouseReplenishmentPathServiceImplUnderTest.getReplenishmentPathList(dto));
        String json = "[{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"LTC001A\",\"id\":\"02VHS42503064311\",\"loc\":\"LTC001A\",\"locType\":\"位置码\",\"lotTable02\":\"220025892170\",\"qty\":703,\"repType\":\"拣货库位补货\",\"sku\":\"045021600016\",\"start\":false,\"toLoc\":\"13AD21C\",\"turningPoint1X\":-38,\"turningPoint1Y\":6,\"turningPoint2X\":-24.6,\"turningPoint2Y\":6,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-30.6,\"ycoord\":6},{\"end\":false,\"fromLoc\":\"13AE28G\",\"loc\":\"13AE28G\",\"locType\":\"库房拣选起点\",\"start\":true,\"turningPoint1X\":-38,\"turningPoint1Y\":8,\"turningPoint2X\":-24.6,\"turningPoint2Y\":8,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-26,\"ycoord\":8},{\"end\":true,\"fromLoc\":\"13AE37G\",\"loc\":\"13AE37G\",\"locType\":\"库房拣选终点\",\"start\":false,\"turningPoint1X\":-38,\"turningPoint1Y\":8,\"turningPoint2X\":-24.6,\"turningPoint2Y\":8,\"warehouseAreaDesc\":\"南京阻容库动态拣选区\",\"warehouseDesc\":\"南京阻容库\",\"whName\":\"部件南京\",\"whseid\":\"WMWHSE26\",\"xcoord\":-33,\"ycoord\":8}]";

        when(mockInforWmsRepublishRepository.getInforWmsRepublishList(Mockito.any())).thenReturn(JSON.parseArray(json,RepublishRouteDTO.class));
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
        retData.setCode(retCode);
retData.setBo("{\"replenishRouteDTOList\":[{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"},{\"fromLoc\":\"LTC001A\",\"repType\":\"拣货库位补货\",\"lotTable02\":\"220025892170\",\"turningPoint2X\":-24.6,\"turningPoint1Y\":6,\"turningPoint1X\":-38,\"turningPoint2Y\":6,\"xcoord\":-30.6,\"toLoc\":\"13AD21C\",\"qty\":703,\"id\":\"02VHS42503064311\",\"sku\":\"045021600016\",\"locType\":\"位置码\",\"ycoord\":6,\"whseid\":\"WMWHSE26\"}],\"replenishResultDTO\":{\"objectivePathLength\":23.8,\"optimizeRatio\":-1.4927368398321434E-16,\"originPathLength\":23.799999999999997,\"optimizeLength\":-3.552713678800501E-15}}");
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(retData);

        when(mockWarehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(1);
        warehouseReplenishmentPathServiceImplUnderTest.getReplenishmentPathList(dto);

        RetCode retCode2 = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode2);
        // Run the test
        WarehouseRepPathWorkListVO result = warehouseReplenishmentPathServiceImplUnderTest.getReplenishmentPathList(dto);

        // Verify the results
        Assert.assertNotNull(result);
    }

    /* Started by AICoder, pid:i01e97c335uff5d14e1a088a0076ad1d04d928d4 */
    @Test
    public void testExportReplenishmentPath() {
        WarehouseReplenishmentPathDTO dto = new WarehouseReplenishmentPathDTO();
        dto.setWhseid("WMWHSE1");
        dto.setPageIndex(1);
        dto.setPageSize(10);

        warehouseReplenishmentPathServiceImplUnderTest.exportReplenishmentPath(dto);

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:i01e97c335uff5d14e1a088a0076ad1d04d928d4 */
    /* Started by AICoder, pid:57f964f9ebfed6e14b070a785080eb1051f2805f */
    @Test
    public void testNullList() {
        List<String> result = warehouseReplenishmentPathServiceImplUnderTest.pageList(null, 1, 10);
        Assert.assertNull(result);
        List<String> list = Lists.newArrayList();
        List<String> result1 = warehouseReplenishmentPathServiceImplUnderTest.pageList(list, 1, 10);
        Assert.assertTrue(result1.isEmpty());
        List<String> result2 = warehouseReplenishmentPathServiceImplUnderTest.pageList(Lists.newArrayList("a", "b", "c"), 3, 2);
        Assert.assertTrue(result2.isEmpty());
    }
    /* Ended by AICoder, pid:57f964f9ebfed6e14b070a785080eb1051f2805f */
}
