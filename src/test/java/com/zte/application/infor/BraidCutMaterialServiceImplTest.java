package com.zte.application.infor;

import com.zte.application.infor.impl.BraidCutMaterialServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.BraidCutMaterialRepository;
import com.zte.interfaces.infor.dto.OutPickingTaskDto;
import com.zte.interfaces.infor.dto.PickingTaskDto;
import com.zte.interfaces.infor.vo.BraidPickingInfoListVo;
import com.zte.interfaces.infor.vo.BraidPickingInfoVo;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, ExcelUtil.class,CollectionUtils.class})
public class BraidCutMaterialServiceImplTest {
    @InjectMocks
    private BraidCutMaterialServiceImpl braidCutMaterialService;
    @Mock
    private BraidCutMaterialRepository braidCutMaterialRepository;
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class,CollectionUtils.class);
    }
    @Test
    public void queryPickingTask() throws Exception{
        try {
            braidCutMaterialService.queryPickingTask(null);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.INPUT_DATA_CAN_NOT_NULL);
        }
        List<PickingTaskDto> list=new ArrayList<>();
        PickingTaskDto dto=new PickingTaskDto();
        dto.setWhseid("234324");
        list.add(dto);
        try {
            braidCutMaterialService.queryPickingTask(list);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.INPUT_DATA_CAN_NOT_NULL);
        }

        List<PickingTaskDto> list1=new ArrayList<>();
        PickingTaskDto dto1=new PickingTaskDto();
        dto1.setOrderkey("234324");
        list1.add(dto1);
        try {
            braidCutMaterialService.queryPickingTask(list1);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.INPUT_DATA_CAN_NOT_NULL);
        }

        List<PickingTaskDto> list2=new ArrayList<>();
        PickingTaskDto dto2=new PickingTaskDto();
        dto2.setOrderkey("234324");
        dto2.setWhseid("234324");
        list2.add(dto2);
        PowerMockito.when(braidCutMaterialRepository.queryPickingTask(Mockito.any())).thenReturn(null);
        braidCutMaterialService.queryPickingTask(list2);

        List<OutPickingTaskDto> outPickingTaskDtos=new ArrayList<>();
        OutPickingTaskDto outPickingTaskDto=new OutPickingTaskDto();
        outPickingTaskDto.setConsigneeKey("234324");
        outPickingTaskDtos.add(outPickingTaskDto);
        PowerMockito.when(braidCutMaterialRepository.queryPickingTask(Mockito.any())).thenReturn(outPickingTaskDtos);
        PowerMockito.when(CollectionUtils.isNotEmpty(Mockito.any())).thenReturn(true);
        braidCutMaterialService.queryPickingTask(list2);
        Assert.assertTrue(Objects.nonNull(outPickingTaskDto));
    }

    @Test
    public void queryPickingInfo() throws Exception{
        PickingTaskDto dto=new PickingTaskDto();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        braidCutMaterialService.queryPickingInfo(dto);

        BraidPickingInfoVo vo=new BraidPickingInfoVo();
        vo.setWaveKey("43243");
        List<BraidPickingInfoVo> list = new ArrayList<>();
        list.add(vo);
        BraidPickingInfoListVo braidPickingInfoListVo=new  BraidPickingInfoListVo();
        braidPickingInfoListVo.setPickingInfoVoList(list);
        Assert.assertTrue(Objects.nonNull(braidPickingInfoListVo));
    }

    @Test
    public void exportPickingInfo() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class);
        PickingTaskDto dto=new PickingTaskDto();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(braidCutMaterialRepository.queryPickingInfoTotal(Mockito.any())).thenReturn(1);
        braidCutMaterialService.exportPickingInfo(dto);
        Assert.assertTrue(true);
    }
}
