package com.zte.application.infor;


import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.zte.common.model.MessageId;
import com.zte.common.utils.IscpRemoteServiceDataUtil;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.infor.DelayBillRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.ItemUUIDInforVo;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import com.zte.application.infor.impl.EdiAsnqcsServiceImpl;
import com.zte.itp.msa.core.model.ServiceData;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, ServiceData.class, IscpRemoteServiceDataUtil.class, JSONObject.class,
        CommonUtils.class})
public class EdiAsnqcsServiceImplTest {

    @InjectMocks
    private EdiAsnqcsServiceImpl ediAsnqcsService;

    @Mock
    private EdiAsnqcSRepository ediAsnqcSRepository;
    @Mock
    private IscpEsbLogRepository iscpEsbLogRepository;
    @Mock
    private DelayBillRepository delayBillRepository;
    @Mock
    private StepIscpRepository stepIscpRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, ServiceData.class, IscpRemoteServiceDataUtil.class, JSONObject.class,
                CommonUtils.class);
    }

    @Test
    public void selectEdiAsnqcSAll() throws Exception {
        List<EdiAsnqcS> list = new ArrayList<>();
        EdiAsnqcS dto = new EdiAsnqcS();
        dto.setArea("AA");
        list.add(dto);
        PowerMockito.when(ediAsnqcSRepository.selectEdiAsnqcSAll()).thenReturn(list);
        List<EdiAsnqcS> res = ediAsnqcsService.selectEdiAsnqcSAll();
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void selectAsnQcData() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        int i = 1;
        AsnqcTransDTO asnDto = new AsnqcTransDTO();
        asnDto.setRn(BigDecimal.valueOf(i));

        List<AsnqcTransDTO> transDTOs = new ArrayList<>();
        AsnqcTransDTO transDTO = new AsnqcTransDTO();
        transDTO.setReceiptKey("213424");
        transDTO.setWhseId("WMWHSE1");
        transDTO.setRn(new BigDecimal(1));
        transDTOs.add(transDTO);
        PowerMockito.when(ediAsnqcSRepository.selectAsnQcData(Mockito.any())).thenReturn(transDTOs);
        Whitebox.invokeMethod(ediAsnqcsService, "selectQualityCheck", "213424", "WMWHSE1");
        Whitebox.invokeMethod(ediAsnqcsService, "selectAsnQcData", i);
        ServiceData<?> res = ediAsnqcsService.selectAsnQcData(i);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void selectQualityCheck() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);
        PowerMockito.mockStatic(IscpRemoteServiceDataUtil.class);

        String receiptKey = "213424";
        String wmwhseId = "WMWHSE1";
        String sendingPerson = "246y3473";
        EdiAsnqcS eAsnqcS = new EdiAsnqcS();
        eAsnqcS.setReceiptkey(receiptKey);
        eAsnqcS.setWhseid(wmwhseId);

        List<AsnQcCheckDTO> list = new ArrayList<>();
        AsnQcCheckDTO asnQcCheckDTO1 = new AsnQcCheckDTO();
        asnQcCheckDTO1.setBillType("2342352");
        asnQcCheckDTO1.setDeliveryAddress("2342352");
        asnQcCheckDTO1.setReceiptKey("2342352");
        asnQcCheckDTO1.setStock("2342352");
        asnQcCheckDTO1.setReceiveNo("2342352");
        asnQcCheckDTO1.setSendingPerson("23423352");
        asnQcCheckDTO1.setReceiveTime("235235623");
        list.add(asnQcCheckDTO1);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheck(Mockito.any())).thenReturn(list);

        List<QaExInspectionHead> dto = new ArrayList<>();
        Whitebox.invokeMethod(ediAsnqcsService, "selectQualityCheck", receiptKey, wmwhseId);


        ServiceData<List<IscpResultDTO>> ret1 = new ServiceData<List<IscpResultDTO>>();
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(Mockito.any())).thenReturn(ret1);

        List<IscpResultDTO> list1 = new ArrayList<>();
        IscpResultDTO iscpResultDTO = new IscpResultDTO();
        iscpResultDTO.setReceiptKey("35326");
        list1.add(iscpResultDTO);
        ret1.setBo(list1);
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(Mockito.any())).thenReturn(ret1);

        Whitebox.invokeMethod(ediAsnqcsService, "selectQualityCheck", receiptKey, wmwhseId);
        ServiceData<?> res = ediAsnqcsService.selectQualityCheck(receiptKey, wmwhseId);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void selectQualityCheckDetail() throws Exception {
        EdiAsnqcS record = new EdiAsnqcS();
        String sendingPerson = "246y3473";
        String receiveTime = "23452362";
        String wmwhseId = "WMWHSE1";

        List<AsnQcCheckDetailDTO> list = new ArrayList<>();
        AsnQcCheckDetailDTO asnQcCheckDetailDTO = new AsnQcCheckDetailDTO();
        asnQcCheckDetailDTO.setReceiptKey("98574");
        asnQcCheckDetailDTO.setItemNo("23425323");
        asnQcCheckDetailDTO.setUuid("123");
        asnQcCheckDetailDTO.setItemName("5423632");
        asnQcCheckDetailDTO.setBrandName("2352362");
        asnQcCheckDetailDTO.setBrandNo("23526326");
        asnQcCheckDetailDTO.setBrandStyle("35262");
        asnQcCheckDetailDTO.setPackageRequire("352362");
        asnQcCheckDetailDTO.setLineId("2352562");
        asnQcCheckDetailDTO.setDeliveryQty(new BigDecimal(23));
        asnQcCheckDetailDTO.setTransferBillNo("578");
        asnQcCheckDetailDTO.setTestMode("23546262");
        asnQcCheckDetailDTO.setReceiveNo("35262");
        asnQcCheckDetailDTO.setReceiptlinenumber("100001");
        asnQcCheckDetailDTO.setStock("11AA01A");
        asnQcCheckDetailDTO.setIsNeedSupReport(BigDecimal.valueOf(1000));
        asnQcCheckDetailDTO.setBarcodeControlType("1");
        list.add(asnQcCheckDetailDTO);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheckDetail(record)).thenReturn(list);
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();

        LocationModel locationModel1 = new LocationModel();
        locationModel1.setLocationcategory("2352652");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel1);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());
        locationModel1.setLocationcategory("NJLTC");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel1);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());

        EdiAsnqcS record1 = new EdiAsnqcS();
        record1.setReceiptkey(asnQcCheckDetailDTO.getReceiptKey());
        record1.setReceiptlinenumber(asnQcCheckDetailDTO.getReceiptlinenumber());
        record1.setWhseid(wmwhseId);
        record1.setExternreceiptkey(asnQcCheckDetailDTO.getReceiveNo());
        List<AsnQcCheckBoxDTO> asnQcCheckBoxDTOArrayList = new ArrayList<>();
        AsnQcCheckBoxDTO asnQcCheckBoxDTO = new AsnQcCheckBoxDTO();
        asnQcCheckBoxDTO.setBatchNo("235262");
        asnQcCheckBoxDTO.setReceiveNo("35235");
        asnQcCheckBoxDTOArrayList.add(asnQcCheckBoxDTO);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheckDetailBox(record1)).thenReturn(asnQcCheckBoxDTOArrayList);
        Whitebox.invokeMethod(ediAsnqcsService, "dealList", list, sendingPerson, receiveTime, wmwhseId);


        List<AsnQcCheckDetailDTO> list2 = new ArrayList<>();
        AsnQcCheckDetailDTO asnQcCheckDetailDTO2 = new AsnQcCheckDetailDTO();
        asnQcCheckDetailDTO2.setReceiptKey("98574");
        asnQcCheckDetailDTO2.setItemNo("23425323");
        asnQcCheckDetailDTO2.setUuid("123");
        asnQcCheckDetailDTO2.setItemName("5423632");
        asnQcCheckDetailDTO2.setBrandName("2352362");
        asnQcCheckDetailDTO2.setBrandNo("23526326");
        asnQcCheckDetailDTO2.setBrandStyle("35262");
        asnQcCheckDetailDTO2.setPackageRequire("352362");
        asnQcCheckDetailDTO2.setLineId("2352562");
        asnQcCheckDetailDTO2.setDeliveryQty(new BigDecimal(23));
        asnQcCheckDetailDTO2.setTransferBillNo("578");
        asnQcCheckDetailDTO2.setTestMode("23546262");
        asnQcCheckDetailDTO2.setReceiveNo("35262");
        asnQcCheckDetailDTO2.setReceiptlinenumber("100001");
        asnQcCheckDetailDTO2.setStock("11AA01A");
        asnQcCheckDetailDTO2.setIsNeedSupReport(BigDecimal.valueOf(1000));
        asnQcCheckDetailDTO2.setBarcodeControlType("2");
        list2.add(asnQcCheckDetailDTO2);

        LocationModel locationModel2 = new LocationModel();
        locationModel2.setLocationcategory("2352652");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel2);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());
        locationModel2.setLocationcategory("NJLTC");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel2);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());

        EdiAsnqcS record2 = new EdiAsnqcS();
        record2.setReceiptkey(asnQcCheckDetailDTO.getReceiptKey());
        record2.setReceiptlinenumber(asnQcCheckDetailDTO.getReceiptlinenumber());
        record2.setWhseid(wmwhseId);
        record2.setExternreceiptkey(asnQcCheckDetailDTO.getReceiveNo());
        List<AsnQcCheckBoxDTO> asnQcCheckBoxDTOArrayList2 = new ArrayList<>();
        AsnQcCheckBoxDTO asnQcCheckBoxDTO2 = new AsnQcCheckBoxDTO();
        asnQcCheckBoxDTO2.setBatchNo("235262");
        asnQcCheckBoxDTO2.setReceiveNo("35235");
        asnQcCheckBoxDTOArrayList2.add(asnQcCheckBoxDTO2);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheckDetailBox(record2)).thenReturn(asnQcCheckBoxDTOArrayList2);
        Whitebox.invokeMethod(ediAsnqcsService, "dealList", list, sendingPerson, receiveTime, wmwhseId);


        Whitebox.invokeMethod(ediAsnqcsService, "selectQualityCheckDetail", record, sendingPerson, receiveTime, wmwhseId);
        List<QaExInspectionDetail> res = ediAsnqcsService.selectQualityCheckDetail(record, sendingPerson, receiveTime, wmwhseId);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void dealList() throws Exception {
        String sendingPerson = "246y3473";
        String receiveTime = "23452362";
        String wmwhseId = "WMWHSE1";

        List<AsnQcCheckDetailDTO> list = new ArrayList<>();
        AsnQcCheckDetailDTO asnQcCheckDetailDTO = new AsnQcCheckDetailDTO();
        asnQcCheckDetailDTO.setReceiptKey("98574");
        asnQcCheckDetailDTO.setItemNo("23425323");
        asnQcCheckDetailDTO.setUuid("123");
        asnQcCheckDetailDTO.setItemName("5423632");
        asnQcCheckDetailDTO.setBrandName("2352362");
        asnQcCheckDetailDTO.setBrandNo("23526326");
        asnQcCheckDetailDTO.setBrandStyle("35262");
        asnQcCheckDetailDTO.setPackageRequire("352362");
        asnQcCheckDetailDTO.setLineId("2352562");
        asnQcCheckDetailDTO.setDeliveryQty(new BigDecimal(23));
        asnQcCheckDetailDTO.setTransferBillNo("578");
        asnQcCheckDetailDTO.setTestMode("23546262");
        asnQcCheckDetailDTO.setReceiveNo("35262");
        asnQcCheckDetailDTO.setReceiptlinenumber("100001");
        asnQcCheckDetailDTO.setStock("11AA01A");
        asnQcCheckDetailDTO.setIsNeedSupReport(BigDecimal.valueOf(1000));
        asnQcCheckDetailDTO.setBarcodeControlType("1");
        list.add(asnQcCheckDetailDTO);
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();

        LocationModel locationModel1 = new LocationModel();
        locationModel1.setLocationcategory("2352652");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel1);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());
        locationModel1.setLocationcategory("NJLTC");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel1);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());

        EdiAsnqcS record1 = new EdiAsnqcS();
        record1.setReceiptkey(asnQcCheckDetailDTO.getReceiptKey());
        record1.setReceiptlinenumber(asnQcCheckDetailDTO.getReceiptlinenumber());
        record1.setWhseid(wmwhseId);
        record1.setExternreceiptkey(asnQcCheckDetailDTO.getReceiveNo());
        List<AsnQcCheckBoxDTO> asnQcCheckBoxDTOArrayList = new ArrayList<>();
        AsnQcCheckBoxDTO asnQcCheckBoxDTO = new AsnQcCheckBoxDTO();
        asnQcCheckBoxDTO.setBatchNo("235262");
        asnQcCheckBoxDTO.setReceiveNo("35235");
        asnQcCheckBoxDTO.setUuid("2315");
        asnQcCheckBoxDTOArrayList.add(asnQcCheckBoxDTO);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheckDetailBox(Mockito.any())).thenReturn(asnQcCheckBoxDTOArrayList);
        Whitebox.invokeMethod(ediAsnqcsService, "dealList", list, sendingPerson, receiveTime, wmwhseId);


        List<AsnQcCheckDetailDTO> list2 = new ArrayList<>();
        AsnQcCheckDetailDTO asnQcCheckDetailDTO2 = new AsnQcCheckDetailDTO();
        asnQcCheckDetailDTO2.setReceiptKey("98574");
        asnQcCheckDetailDTO2.setItemNo("23425323");
        asnQcCheckDetailDTO2.setUuid("123");
        asnQcCheckDetailDTO2.setItemName("5423632");
        asnQcCheckDetailDTO2.setBrandName("2352362");
        asnQcCheckDetailDTO2.setBrandNo("23526326");
        asnQcCheckDetailDTO2.setBrandStyle("35262");
        asnQcCheckDetailDTO2.setPackageRequire("352362");
        asnQcCheckDetailDTO2.setLineId("2352562");
        asnQcCheckDetailDTO2.setDeliveryQty(new BigDecimal(23));
        asnQcCheckDetailDTO2.setTransferBillNo("578");
        asnQcCheckDetailDTO2.setTestMode("23546262");
        asnQcCheckDetailDTO2.setReceiveNo("35262");
        asnQcCheckDetailDTO2.setReceiptlinenumber("100001");
        asnQcCheckDetailDTO2.setStock("11AA01A");
        asnQcCheckDetailDTO2.setIsNeedSupReport(BigDecimal.valueOf(1000));
        asnQcCheckDetailDTO2.setBarcodeControlType("2");
        list2.add(asnQcCheckDetailDTO2);

        LocationModel locationModel2 = new LocationModel();
        locationModel2.setLocationcategory("2352652");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel2);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());
        locationModel2.setLocationcategory("NJLTC");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(wmwhseId, asnQcCheckDetailDTO.getStock())).thenReturn(locationModel2);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());

        EdiAsnqcS record2 = new EdiAsnqcS();
        record2.setReceiptkey(asnQcCheckDetailDTO.getReceiptKey());
        record2.setReceiptlinenumber(asnQcCheckDetailDTO.getReceiptlinenumber());
        record2.setWhseid(wmwhseId);
        record2.setExternreceiptkey(asnQcCheckDetailDTO.getReceiveNo());
        List<AsnQcCheckBoxDTO> asnQcCheckBoxDTOArrayList2 = new ArrayList<>();
        AsnQcCheckBoxDTO asnQcCheckBoxDTO2 = new AsnQcCheckBoxDTO();
        asnQcCheckBoxDTO2.setBatchNo("235262");
        asnQcCheckBoxDTO2.setReceiveNo("35235");
        asnQcCheckBoxDTO2.setUuid("2315");
        asnQcCheckBoxDTOArrayList2.add(asnQcCheckBoxDTO2);
        PowerMockito.when(ediAsnqcSRepository.selectQualityCheckDetailBox(Mockito.any())).thenReturn(asnQcCheckBoxDTOArrayList2);
        Whitebox.invokeMethod(ediAsnqcsService, "dealList", list2, sendingPerson, receiveTime, wmwhseId);
        List<QaExInspectionDetail> res = ediAsnqcsService.dealList(list2, sendingPerson, receiveTime, wmwhseId);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void updateIscpResult() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        ServiceData<List<IscpResultDTO>> ret1 = new ServiceData<List<IscpResultDTO>>();
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpResult", ret1);

        List<IscpResultDTO> list = new ArrayList<>();
        IscpResultDTO iscpResultDTO = new IscpResultDTO();
        iscpResultDTO.setReceiptKey("342532");
        iscpResultDTO.setProcessStatus("325262");
        list.add(iscpResultDTO);
        ret1.setBo(list);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpResult", ret1);

        RetCode code = new RetCode("0000", "RetCode.Success");
        ret1.setCode(code);
        Whitebox.invokeMethod(ediAsnqcsService, "addIscpStatus", iscpResultDTO);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpResult", ret1);
        Assert.assertTrue(Objects.nonNull(ret1));
    }

    @Test
    public void addIscpStatus() throws Exception {

        IscpResultDTO iscpResultDTO = new IscpResultDTO();
        iscpResultDTO.setReceiptKey("342532");
        iscpResultDTO.setProcessStatus("S");

        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpLog", iscpResultDTO);
        EdiAsnqcS record = new EdiAsnqcS();
        record.setReceiptkey("21415151");
        record.setExternreceiptkey("23523623632");

        Whitebox.invokeMethod(ediAsnqcsService, "addIscpStatus", iscpResultDTO);
        Assert.assertTrue(Objects.nonNull(iscpResultDTO));
    }

    @Test
    public void updateIscpLog() throws Exception {
        //PowerMockito.mockStatic(JSONObject.class);

        IscpResultDTO iscpResultDTO = new IscpResultDTO();
        iscpResultDTO.setReceiptKey("235252");
        iscpResultDTO.setSerialId("323526");
        IscpEsbLog iscpEsbLog = new IscpEsbLog();
        List<IscpEsbLog> listmodel = new ArrayList<>();
        PowerMockito.when(iscpEsbLogRepository.selectIscpEsbLog(Mockito.any())).thenReturn(listmodel);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpLog", iscpResultDTO);

        IscpResultDTO iscpResultDTO2 = new IscpResultDTO();
        iscpResultDTO2.setReceiptKey("235252");
        iscpResultDTO2.setSerialId("323526");
        IscpDetailResultDTO iscpDetailResultDTO2 = new IscpDetailResultDTO();
        StringBuilder sb2 = new StringBuilder();
        for (int i = 0; i < 2500; i++) {
            sb2.append("d");
        }
        iscpDetailResultDTO2.setSerialId(sb2.toString());
        iscpResultDTO2.setDetails(iscpDetailResultDTO2);
        IscpEsbLog iscpEsbLog2 = new IscpEsbLog();
        List<IscpEsbLog> listmodel2 = new ArrayList<>();
        PowerMockito.when(iscpEsbLogRepository.selectIscpEsbLog(Mockito.any())).thenReturn(listmodel2);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpLog", iscpResultDTO2);

        IscpResultDTO iscpResultDT3 = new IscpResultDTO();
        iscpResultDT3.setReceiptKey("235252");
        iscpResultDT3.setSerialId("323526");
        IscpEsbLog iscpEsbLog3 = new IscpEsbLog();
        iscpEsbLog3.setExternreceiptkey("2352362");
        List<IscpEsbLog> listmodel3 = new ArrayList<>();
        listmodel3.add(iscpEsbLog3);
        PowerMockito.when(iscpEsbLogRepository.selectIscpEsbLog(Mockito.any())).thenReturn(listmodel3);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpLog", iscpResultDT3);

        IscpResultDTO iscpResultDT4 = new IscpResultDTO();
        iscpResultDT4.setReceiptKey("235252");
        iscpResultDT4.setSerialId("323526");
        IscpDetailResultDTO iscpDetailResultDTO4 = new IscpDetailResultDTO();
        StringBuilder sb4 = new StringBuilder();
        for (int i = 0; i < 2500; i++) {
            sb4.append("d");
        }
        iscpDetailResultDTO4.setSerialId(sb4.toString());
        iscpResultDT4.setDetails(iscpDetailResultDTO4);
        IscpEsbLog iscpEsbLog4 = new IscpEsbLog();
        iscpEsbLog4.setExternreceiptkey("2352362");
        List<IscpEsbLog> listmodel4 = new ArrayList<>();
        listmodel4.add(iscpEsbLog4);
        PowerMockito.when(iscpEsbLogRepository.selectIscpEsbLog(Mockito.any())).thenReturn(listmodel4);
        Whitebox.invokeMethod(ediAsnqcsService, "updateIscpLog", iscpResultDT4);
        Assert.assertTrue(Objects.nonNull(iscpResultDT4));
    }

    @Test
    public void updateAsnFlag() throws Exception {
        String runNormal = "Y";
        EdiAsnqcS record = new EdiAsnqcS();
        Whitebox.invokeMethod(ediAsnqcsService, "updateAsnFlag", record);
        Assert.assertTrue(Objects.nonNull(runNormal));
    }

    @Test
    public void insertEdiAsnqcS() throws Exception {
        String runNormal = "Y";
        EdiAsnqcS record = new EdiAsnqcS();
        Whitebox.invokeMethod(ediAsnqcsService, "insertEdiAsnqcS", record);
        Assert.assertTrue(Objects.nonNull(runNormal));
    }

    @Test
    public void getVmiStoreByWmwhse() throws Exception {

        VmiStoreParamDTO vmiStoreParamDTO = new VmiStoreParamDTO();
        vmiStoreParamDTO.setBrandNo("235434463");
        List<VmiStoreDTO> vmiStorelist = new ArrayList<>();
        PowerMockito.when(ediAsnqcSRepository.findVmiStoreByWmwhse(vmiStoreParamDTO)).thenReturn(vmiStorelist);
        Whitebox.invokeMethod(ediAsnqcsService, "getVmiStoreByWmwhse", vmiStoreParamDTO);

        VmiStoreDTO vmiStoreDTO = new VmiStoreDTO();
        vmiStoreDTO.setBrandNo("3254363");
        vmiStorelist.add(vmiStoreDTO);
        PowerMockito.when(ediAsnqcSRepository.findVmiStoreByWmwhse(vmiStoreParamDTO)).thenReturn(vmiStorelist);
        Whitebox.invokeMethod(ediAsnqcsService, "getVmiStoreDto", vmiStorelist, vmiStoreParamDTO);
        Whitebox.invokeMethod(ediAsnqcsService, "getVmiStoreByWmwhse", vmiStoreParamDTO);
        List<VmiStoreDTO> res = ediAsnqcsService.getVmiStoreByWmwhse(vmiStoreParamDTO);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void getVmiStoreDto() throws Exception {

        VmiStoreParamDTO vmiStoreParamDTO = new VmiStoreParamDTO();
        vmiStoreParamDTO.setBrandNo("235434463");
        List<VmiStoreDTO> vmiStorelist = new ArrayList<>();
        VmiStoreDTO vmiStoreDTO = new VmiStoreDTO();
        vmiStoreDTO.setBrandNo("3254363");
        vmiStoreDTO.setSerialKey("11111111");
        vmiStorelist.add(vmiStoreDTO);

        List<String> serialKeyList = new ArrayList<>();
        Map<String, Object> vmiQtyReelIdMap = new HashMap<>(16);
        vmiQtyReelIdMap.put("whseid", "3453463463");
        vmiQtyReelIdMap.put("serialKeyList", serialKeyList);
        List<VmiQtyReelIdMode> qtyReelIdList = new ArrayList<>();
        VmiQtyReelIdMode vmiQtyReelIdMode = new VmiQtyReelIdMode();
        vmiQtyReelIdMode.setQty(new BigDecimal(2));
        vmiQtyReelIdMode.setReelId("23542362");
        vmiQtyReelIdMode.setSerialKey("11111111");
        qtyReelIdList.add(vmiQtyReelIdMode);
        PowerMockito.when(ediAsnqcSRepository.findQtyReelId(Mockito.any())).thenReturn(qtyReelIdList);
        Whitebox.invokeMethod(ediAsnqcsService, "getVmiStoreDto", vmiStorelist, vmiStoreParamDTO);
        List<VmiStoreDTO> res = ediAsnqcsService.getVmiStoreDto(vmiStorelist, vmiStoreParamDTO);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void selectAsnQcDataDelay() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        AsnqcTransDTO asnDto = new AsnqcTransDTO();
        List<AsnqcTransDTO> transDTOs = new ArrayList<>();
        AsnqcTransDTO asnqcTransDTO = new AsnqcTransDTO();
        asnqcTransDTO.setRn(new BigDecimal(3252356));
        asnqcTransDTO.setReceiptKey("235262");
        asnqcTransDTO.setWhseId("WMWHSE1");
        transDTOs.add(asnqcTransDTO);
        PowerMockito.when(ediAsnqcSRepository.selectAsnQcDataDelay(Mockito.any())).thenReturn(transDTOs);
        Whitebox.invokeMethod(ediAsnqcsService, "selectQualityCheck", "23523526", "325262");
        Whitebox.invokeMethod(ediAsnqcsService, "selectAsnQcDataDelay");
        ServiceData<?> res = ediAsnqcsService.selectAsnQcDataDelay();
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void setLocation() throws Exception {
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();
        String whseid = "WMWHSE1";
        String loc = "234252";

        LocationModel locationModel = new LocationModel();
        locationModel.setLocationcategory("2352652");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(whseid, loc)).thenReturn(locationModel);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, whseid, loc);

        locationModel.setLocationcategory("NJLTC");
        PowerMockito.when(ediAsnqcSRepository.selectLocationDetail(whseid, loc)).thenReturn(locationModel);
        Whitebox.invokeMethod(ediAsnqcsService, "setLocation", qaExInspectionDetail, whseid, loc);
        Assert.assertTrue(Objects.nonNull(locationModel));
    }

    @Test
    public void sendDelayRecheckToCheck() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        String recheckNo = null;
        Whitebox.invokeMethod(ediAsnqcsService, "sendDelayRecheckToCheck", recheckNo);

        recheckNo = "2352362";
        Integer recheckNoNum = 0;
        PowerMockito.when(delayBillRepository.getRecheckNoNum(recheckNo)).thenReturn(recheckNoNum);

        Whitebox.invokeMethod(ediAsnqcsService, "sendDelayRecheckToCheck", recheckNo);

        recheckNoNum = 2;
        PowerMockito.when(delayBillRepository.getRecheckNoNum(recheckNo)).thenReturn(recheckNoNum);
        QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
        qaExInspectionHead.setItemBarcode("235262");
        qaExInspectionHead.setSupplierNo("23523626");
        qaExInspectionHead.setStock("23523636");
        PowerMockito.when(delayBillRepository.getQaExInspectionHead(recheckNo)).thenReturn(qaExInspectionHead);

        List<ItemUUIDInforVo> codeList = new ArrayList<>();
        ItemUUIDInforVo itemUUIDInforVo = new ItemUUIDInforVo();
        itemUUIDInforVo.setSupplierNo("2352362");
        codeList.add(itemUUIDInforVo);
        PowerMockito.when(stepIscpRepository.getitemUuidAndEnv(qaExInspectionHead.getItemBarcode())).thenReturn(codeList);

        ItemUUIDInforVo uuidInforVo = codeList.get(0);
        qaExInspectionHead.setSupplierNo(uuidInforVo.getSupplierNo());
        List<QaExInspectionDetail> details = new ArrayList<>();
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();
        qaExInspectionDetail.setBrandName("2352362");
        qaExInspectionDetail.setLocation("111111");
        details.add(qaExInspectionDetail);
        PowerMockito.when(delayBillRepository.getQaExInspectionDetail(recheckNo, qaExInspectionHead.getStock())).thenReturn(details);

        Map<String, String> locationCategoryMap = new HashMap<>();
        locationCategoryMap.put("111111", "NJLTC");
        Whitebox.invokeMethod(ediAsnqcsService, "getLocationCategoryMap", qaExInspectionHead, details);
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);

        qaExInspectionDetail.setPackNo("23423525");
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);

        List<QaExInspectionHead> dto = new ArrayList<>();
        dto.add(qaExInspectionHead);
        ServiceData<List<IscpResultDTO>> ret1 = null;
        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(dto)).thenReturn(ret1);
        Whitebox.invokeMethod(ediAsnqcsService, "sendBillToCheck", recheckNo);
        Whitebox.invokeMethod(ediAsnqcsService, "sendDelayRecheckToCheck", recheckNo);
        ServiceData<?> res = ediAsnqcsService.sendDelayRecheckToCheck(recheckNo);
        Assert.assertTrue(Objects.isNull(res));
    }

    @Test
    public void sendBillToCheck() throws Exception {
        PowerMockito.mockStatic(JSONObject.class);
        PowerMockito.mockStatic(IscpRemoteServiceDataUtil.class);

        String recheckNo = "2352362";

        QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
        qaExInspectionHead.setItemBarcode("235262");
        qaExInspectionHead.setSupplierNo("23523626");
        qaExInspectionHead.setStock("23523636");
        PowerMockito.when(delayBillRepository.getQaExInspectionHead(recheckNo)).thenReturn(qaExInspectionHead);

        List<ItemUUIDInforVo> codeList = new ArrayList<>();
        ItemUUIDInforVo itemUUIDInforVo = new ItemUUIDInforVo();
        itemUUIDInforVo.setSupplierNo("2352362");
        codeList.add(itemUUIDInforVo);
        PowerMockito.when(stepIscpRepository.getitemUuidAndEnv(qaExInspectionHead.getItemBarcode())).thenReturn(codeList);

        ItemUUIDInforVo uuidInforVo = codeList.get(0);
        qaExInspectionHead.setSupplierNo(uuidInforVo.getSupplierNo());
        List<QaExInspectionDetail> details = new ArrayList<>();
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();
        qaExInspectionDetail.setBrandName("2352362");
        qaExInspectionDetail.setLocation("111111");
        details.add(qaExInspectionDetail);
        PowerMockito.when(delayBillRepository.getQaExInspectionDetail(recheckNo, qaExInspectionHead.getStock())).thenReturn(details);

        Map<String, String> locationCategoryMap = new HashMap<>();
        locationCategoryMap.put("111111", "NJLTC");
        Whitebox.invokeMethod(ediAsnqcsService, "getLocationCategoryMap", qaExInspectionHead, details);
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);

        qaExInspectionDetail.setPackNo("23423525");
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);

        List<QaExInspectionHead> dto = new ArrayList<>();
        dto.add(qaExInspectionHead);
        ServiceData<List<IscpResultDTO>> ret1 = new ServiceData<>();

        PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(Mockito.any())).thenReturn(ret1);
        Whitebox.invokeMethod(ediAsnqcsService, "sendBillToCheck", recheckNo);
        ServiceData<?> res = ediAsnqcsService.sendBillToCheck(recheckNo);
        Assert.assertTrue(Objects.nonNull(res));
        try {
            PowerMockito.when(IscpRemoteServiceDataUtil.inspectionReq(Mockito.any())).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOUND));
            Whitebox.invokeMethod(ediAsnqcsService, "sendBillToCheck", recheckNo);
        } catch (Exception e) {
            ServiceDataUtil.getBusinessError(e.getMessage());
        }

    }

    @Test
    public void combineParam() throws Exception {
        String recheckNo = "HFJ2022062366592";

        QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
        qaExInspectionHead.setItemBarcode("235262");
        qaExInspectionHead.setSupplierNo("23523626");
        qaExInspectionHead.setStock("23523636");
        PowerMockito.when(delayBillRepository.getQaExInspectionHead(recheckNo)).thenReturn(qaExInspectionHead);

        List<ItemUUIDInforVo> codeList = new ArrayList<>();
        ItemUUIDInforVo itemUUIDInforVo = new ItemUUIDInforVo();
        itemUUIDInforVo.setSupplierNo("2352362");
        codeList.add(itemUUIDInforVo);
        PowerMockito.when(stepIscpRepository.getitemUuidAndEnv(qaExInspectionHead.getItemBarcode())).thenReturn(codeList);

        ItemUUIDInforVo uuidInforVo = codeList.get(0);
        qaExInspectionHead.setSupplierNo(uuidInforVo.getSupplierNo());
        List<QaExInspectionDetail> details = new ArrayList<>();
        QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();
        qaExInspectionDetail.setBrandName("2352362");
        qaExInspectionDetail.setLocation("11AA01A");
        details.add(qaExInspectionDetail);
        PowerMockito.when(delayBillRepository.getQaExInspectionDetail(recheckNo, qaExInspectionHead.getStock())).thenReturn(details);

        Map<String, String> locationCategoryMap = new HashMap<>();
        locationCategoryMap.put("11AA01A", "NJLTC");
        Whitebox.invokeMethod(ediAsnqcsService, "getLocationCategoryMap", qaExInspectionHead, details);
        List<QaExInspectionPack> packs = new ArrayList<QaExInspectionPack>();
        PowerMockito.when(iscpEsbLogRepository.getQaExInspectionPack(qaExInspectionDetail)).thenReturn(packs);
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);

        qaExInspectionDetail.setPackNo("23423525");
        QaExInspectionPack qaExInspectionPack = new QaExInspectionPack();
        qaExInspectionPack.setBatchNo("3423523");
        packs.add(qaExInspectionPack);
        PowerMockito.when(delayBillRepository.getQaExInspectionPackWithPackNo(qaExInspectionDetail)).thenReturn(packs);
        Whitebox.invokeMethod(ediAsnqcsService, "combineParam", recheckNo);
        QaExInspectionHead res = ediAsnqcsService.combineParam(recheckNo);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void getLocationCategoryMap() throws Exception {

        QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
        qaExInspectionHead.setStock("WMWHSE17");
        List<QaExInspectionDetail> details = new ArrayList<>();
        List<String> locationGroup = new ArrayList<>();
        List<QaExInspectionDetail> qaExInspectionDetailList = new ArrayList<>();
        PowerMockito.when(iscpEsbLogRepository.getWmwhseXLocationMapList(Mockito.any(), Mockito.any())).thenReturn(qaExInspectionDetailList);
        Whitebox.invokeMethod(ediAsnqcsService, "getLocationCategoryMap", qaExInspectionHead, details);

        List<QaExInspectionDetail> qaExInspectionDetailList2 = new ArrayList<>();
        QaExInspectionDetail q = new QaExInspectionDetail();
        q.setLocation("11AA01A");
        q.setLocationcategory("3252363");
        qaExInspectionDetailList2.add(q);
        List<QaExInspectionDetail> details2 = new ArrayList<>();
        details2.add(q);
        PowerMockito.when(iscpEsbLogRepository.getWmwhseXLocationMapList(Mockito.any(), Mockito.any())).thenReturn(qaExInspectionDetailList2);
        Whitebox.invokeMethod(ediAsnqcsService, "getLocationCategoryMap", qaExInspectionHead, details2);
        Map<String, String> res = ediAsnqcsService.getLocationCategoryMap(qaExInspectionHead, details2);
        Assert.assertTrue(Objects.nonNull(res));
    }
}
