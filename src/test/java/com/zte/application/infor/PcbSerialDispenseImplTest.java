package com.zte.application.infor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.zte.application.infor.impl.PcbSerialDispenseImpl;
import com.zte.domain.model.infor.EdiPcbserial;
import com.zte.domain.model.infor.EdiPcbserialRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.redis.RedisHelper;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.zte.Application;


@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisSerialNoUtil.class, RedisHelper.class, CommonUtils.class,
        BusiAssertException.class})
public class PcbSerialDispenseImplTest {

    @InjectMocks
    private PcbSerialDispenseImpl pcbSerialDispenseImpl;
    @Mock
    private EdiPcbserialRepository ediPcbserialRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(RedisSerialNoUtil.class, RedisHelper.class, CommonUtils.class,
                BusiAssertException.class);
    }

    @Test
    public void addPcbSerial() throws Exception {
        pcbSerialDispenseImpl.addPcbSerial(null);
        pcbSerialDispenseImpl.addPcbSerial(new ArrayList<>());
        List<PcbserialDTO> list = new ArrayList<>();
        PcbserialDTO pcbserialDTO = new PcbserialDTO();
        pcbserialDTO.setStorerKey("ZTE");
        pcbserialDTO.setExternReceiptkey("F2302SC00000201");
        pcbserialDTO.setExternLineno("1");
        pcbserialDTO.setSku("040010201198");
        pcbserialDTO.setLottable02("220015677347");
        pcbserialDTO.setToid("PPP2258741362");
        pcbserialDTO.setSerialNumber("199E654123987");
        pcbserialDTO.setQty(BigDecimal.ONE);
        pcbserialDTO.setHref11("100");
        pcbserialDTO.setAsnType(new BigDecimal(2));
        pcbserialDTO.setGoodDie(BigDecimal.ONE);
        pcbserialDTO.setWhseid("");
        list.add(pcbserialDTO);

        EdiPcbserial query = new EdiPcbserial();
        query.setWhseid("WMWHSE8");
        query.setExternreceiptkey("F2302SC00000201");
        pcbSerialDispenseImpl.addPcbSerial(list);

        list = new ArrayList<>();
        pcbserialDTO = new PcbserialDTO();
        pcbserialDTO.setStorerKey("ZTE");
        pcbserialDTO.setExternReceiptkey("F2302SC00000201");
        pcbserialDTO.setExternLineno("1");
        pcbserialDTO.setSku("040010201198");
        pcbserialDTO.setLottable02("220015677347");
        pcbserialDTO.setToid("PPP2258741362");
        pcbserialDTO.setSerialNumber("199E654123987");
        pcbserialDTO.setQty(BigDecimal.ONE);
        pcbserialDTO.setHref11("100");
        pcbserialDTO.setAsnType(new BigDecimal(2));
        pcbserialDTO.setGoodDie(BigDecimal.ONE);
        pcbserialDTO.setWhseid("WMWHSE1");
        list.add(pcbserialDTO);
        PowerMockito.when(ediPcbserialRepository.selectPldb(Mockito.any())).thenReturn(0);
        pcbSerialDispenseImpl.addPcbSerial(list);
        PowerMockito.when(ediPcbserialRepository.selectPldb(Mockito.any())).thenReturn(1);
        PowerMockito.when(ediPcbserialRepository.selectReceiptCount(Mockito.any())).thenReturn(2);
        pcbSerialDispenseImpl.addPcbSerial(list);
        PowerMockito.when(ediPcbserialRepository.selectReceiptCount(Mockito.any())).thenReturn(0);
        PowerMockito.when(ediPcbserialRepository.selectPcbSerialCount(Mockito.any())).thenReturn(2);
        PowerMockito.when(ediPcbserialRepository.selectZteInboundSerial(Mockito.any())).thenReturn(2);
        PowerMockito.when(ediPcbserialRepository.selectZteInboundSerialToId(Mockito.any())).thenReturn(2);
        pcbSerialDispenseImpl.addPcbSerial(list);
        PowerMockito.when(ediPcbserialRepository.selectZteInboundSerial(Mockito.any())).thenReturn(0);
        PowerMockito.when(ediPcbserialRepository.selectZteInboundSerialToId(Mockito.any())).thenReturn(0);
        ServiceData<?> res = pcbSerialDispenseImpl.addPcbSerial(list);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void checkSerialList() {
        String preWhse = "WMWHSE";
        int existsFlag = -1;
        int zero = 0;
        int line = 1;

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ZERO);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ZERO);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ZERO);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ZERO);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(null);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ONE);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ZERO);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ONE);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey(null);
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ONE);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ONE);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ONE);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ONE);
            list.add(pcbserialDTO);
            pcbSerialDispenseImpl.checkSerialList(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ONE);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ONE);
            list.add(pcbserialDTO);
            String res = pcbSerialDispenseImpl.checkSerialList(list);
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void changePcbserialDto() {

        try {
            List<PcbserialDTO> list = new ArrayList<>();
            PcbserialDTO pcbserialDTO = new PcbserialDTO();
            pcbserialDTO.setWhseid("WMWHSE8");
            pcbserialDTO.setStorerKey("ZTE");
            pcbserialDTO.setExternReceiptkey("F2302SC00000201");
            pcbserialDTO.setExternLineno("1");
            pcbserialDTO.setSku("040010201198");
            pcbserialDTO.setLottable02("220015677347");
            pcbserialDTO.setToid("PPP2258741362");
            pcbserialDTO.setSerialNumber("199E654123987");
            pcbserialDTO.setQty(BigDecimal.ZERO);
            pcbserialDTO.setHref11("100");
            pcbserialDTO.setAsnType(new BigDecimal(2));
            pcbserialDTO.setGoodDie(BigDecimal.ZERO);
            list.add(pcbserialDTO);
            List<EdiPcbserial> res = pcbSerialDispenseImpl.changePcbserialDto(list);
            Assert.assertTrue(Objects.nonNull(res));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
