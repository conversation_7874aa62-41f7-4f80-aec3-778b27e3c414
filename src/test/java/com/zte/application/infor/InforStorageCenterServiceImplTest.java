/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.impl.InforStorageCenterServiceImpl;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年11月19日 <br>
 * @see com.zte.application.infor <br>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Tools.class, HttpClientUtil.class, RemoteServiceDataUtil.class})
public class InforStorageCenterServiceImplTest {
    @InjectMocks
    private InforStorageCenterServiceImpl inforStorageCenterServiceImpl;
    @Mock
    private InforStorageCenterRepository inforStorageCenterRepository;
    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Mock
    private EdiPoSRepository ediPoSRepository;
    @Before
    public void init() {
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
    }
    @Test
    public void getSoByBillTypeHzTest() {
        List<HZBillHead> list = new ArrayList<>();
        HZBillHead dto = new HZBillHead();
        dto.setSrcBillNo("123");
        list.add(dto);
        PowerMockito.when(inforStorageCenterRepository.getAllHZBill(Mockito.any())).thenReturn(null);
        inforStorageCenterServiceImpl.getSoByBillTypeHz("", "");
        String externalOrderkey2 = "HZ141000018";
        String orderKey = "9000001838";
        PowerMockito.when(inforStorageCenterRepository.getAllHZBill(Mockito.any())).thenReturn(list);
        PowerMockito.when(inforStorageCenterRepository.checkIsSo(Mockito.any(),Mockito.any())).thenReturn(1);
        inforStorageCenterServiceImpl.getSoByBillTypeHz(externalOrderkey2, orderKey);
        PowerMockito.when(inforStorageCenterRepository.checkIsSo(Mockito.any(),Mockito.any())).thenReturn(0);
        inforStorageCenterServiceImpl.getSoByBillTypeHz(externalOrderkey2, orderKey);
        Assert.assertTrue(Objects.nonNull(list));
    }
    @Test
    public void pushHzBillToStorageCenterTest() {
        inforStorageCenterServiceImpl.pushHzBillToStorageCenter("", "", "", "");
        String externalOrderkey2 = "HZ141000018";
        String orderKey = "9000001838";
        inforStorageCenterServiceImpl.pushHzBillToStorageCenter("10263048", "7V4nDoytYqGGPEPIhQP3tIsjRXp72Da0", externalOrderkey2, orderKey);
        PowerMockito.when(inforStorageCenterRepository.getUnPushedData(Mockito.any())).thenReturn(null);
        inforStorageCenterServiceImpl.pushHzBillToStorageCenter("10263048", "7V4nDoytYqGGPEPIhQP3tIsjRXp72Da0", externalOrderkey2, orderKey);

        List<HZBillHead> list = new ArrayList<>();
        HZBillHead dto = new HZBillHead();
        dto.setSrcBillNo("123");
        list.add(dto);
        PowerMockito.when(inforStorageCenterRepository.getUnPushedData(Mockito.any())).thenReturn(list);
        List<HZBillHead> hzBillHeadList = new ArrayList<>();
        List<HZBillDetail> hzBillDetailList = new ArrayList<>();
        List<EdiCacheList> ediCacheList = new ArrayList<>();

        HZBillHead hzBillHead = new HZBillHead();
        hzBillHead.setRelatedBillNo("HZ170700841");
        hzBillHead.setSrcBillNo("9000296069");
        hzBillHeadList.add(hzBillHead);

        HZBillDetail hzBillDetail = new HZBillDetail();
        hzBillDetail.setRelatedBillNo("HZ170700841");
        hzBillDetail.setSrcBillNo("9000296069");
        hzBillDetail.setReelid("704427200002");
        hzBillDetail.setType("02");
        hzBillDetailList.add(hzBillDetail);

        EdiCacheList ediCacheList1 = new EdiCacheList();
        ediCacheList1.setRelatedBillNo("HZ170700841");
        ediCacheList1.setSrcBillNo("9000296069");
        ediCacheList1.setReelid("704427200002");
        ediCacheList.add(ediCacheList1);
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(retData);
        PowerMockito.when(inforStorageCenterRepository.getHZBillHead(Mockito.any())).thenReturn(hzBillHeadList);
        PowerMockito.when(inforStorageCenterRepository.getHZBillDetail(Mockito.any())).thenReturn(hzBillDetailList);
        PowerMockito.when(inforStorageCenterRepository.getEdiCacheList(Mockito.any())).thenReturn(ediCacheList);
        inforStorageCenterServiceImpl.pushHzBillToStorageCenter("10263048", "7V4nDoytYqGGPEPIhQP3tIsjRXp72Da0", externalOrderkey2, orderKey);
        Assert.assertTrue(Objects.nonNull(orderKey));
    }

    @Test
    public void pushDataToStorageCenterTest() {
        String xEmpNo = "10263048";
        String xAuthValue = "7V4nDoytYqGGPEPIhQP3tIsjRXp72Da0";
        List<HZBillDetail> hzBillDetailList = new ArrayList<>();
        HZBillDetail hzBillDetail = new HZBillDetail();
        hzBillDetail.setRelatedBillNo("HZ170700841");
        hzBillDetail.setSrcBillNo("9000296069");
        hzBillDetail.setReelid("704427200002");
        hzBillDetail.setType("02");
        hzBillDetailList.add(hzBillDetail);
        HZBillHead hzBillHead = new HZBillHead();
        hzBillHead.setRelatedBillNo("HZ170700841");
        hzBillHead.setSrcBillNo("9000296069");
        List<HZBillDTO> hzBillDTOList = new ArrayList<HZBillDTO>();
        HZBillDTO hzBillDTO = new HZBillDTO();
        hzBillDTO.setBillHead(hzBillHead);
        hzBillDTO.setBillDetail(hzBillDetailList);
        hzBillDTOList.add(hzBillDTO);
        List<JSONObject> list = new ArrayList<>();
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(retData);
        inforStorageCenterServiceImpl.pushDataToStorageCenter(hzBillDTOList, xEmpNo, xAuthValue);
        Assert.assertTrue(Objects.nonNull(hzBillDTOList));
    }

    @Test
    public void getAndCombineParamTest() {
        HZBillHead hzBillHead1 = new HZBillHead();
        hzBillHead1.setRelatedBillNo("HZ141000018");
        hzBillHead1.setSrcBillNo("123");
        PowerMockito.when(inforStorageCenterRepository.getUnPushedData(Mockito.any())).thenReturn(null);
        inforStorageCenterServiceImpl.getAndCombineParam(hzBillHead1);
        List<HZBillHead> list = new ArrayList<>();
        HZBillHead dto = new HZBillHead();
        dto.setSrcBillNo("123");
        list.add(dto);
        PowerMockito.when(inforStorageCenterRepository.getUnPushedData(Mockito.any())).thenReturn(list);
        List<HZBillHead> hzBillHeadList = new ArrayList<>();
        List<HZBillDetail> hzBillDetailList = new ArrayList<>();
        List<EdiCacheList> ediCacheList = new ArrayList<>();

        HZBillHead hzBillHead = new HZBillHead();
        hzBillHead.setRelatedBillNo("HZ170700841");
        hzBillHead.setSrcBillNo("9000296069");
        hzBillHeadList.add(hzBillHead);

        HZBillDetail hzBillDetail = new HZBillDetail();
        hzBillDetail.setRelatedBillNo("HZ170700841");
        hzBillDetail.setSrcBillNo("9000296069");
        hzBillDetail.setReelid("704427200002");
        hzBillDetail.setType("02");
        hzBillDetailList.add(hzBillDetail);

        EdiCacheList ediCacheList1 = new EdiCacheList();
        ediCacheList1.setRelatedBillNo("HZ170700841");
        ediCacheList1.setSrcBillNo("9000296069");
        ediCacheList1.setReelid("704427200002");
        ediCacheList.add(ediCacheList1);
        PowerMockito.when(inforStorageCenterRepository.getHZBillHead(Mockito.any())).thenReturn(hzBillHeadList);
        PowerMockito.when(inforStorageCenterRepository.getHZBillDetail(Mockito.any())).thenReturn(hzBillDetailList);
        PowerMockito.when(inforStorageCenterRepository.getEdiCacheList(Mockito.any())).thenReturn(ediCacheList);
        List<HZBillDTO> test = inforStorageCenterServiceImpl.getAndCombineParam(hzBillHead);
        Assert.assertTrue(Objects.nonNull(test));
    }
    @Test
    public void combineDataTest() {
        List<HZBillHead> hzBillHeadList = new ArrayList<>();
        List<HZBillDetail> hzBillDetailList = new ArrayList<>();
        List<EdiCacheList> ediCacheList = new ArrayList<>();

        HZBillHead hzBillHead = new HZBillHead();
        hzBillHead.setRelatedBillNo("HZ170700841");
        hzBillHead.setSrcBillNo("9000296069");
        hzBillHeadList.add(hzBillHead);

        HZBillDetail hzBillDetail = new HZBillDetail();
        hzBillDetail.setRelatedBillNo("HZ170700841");
        hzBillDetail.setSrcBillNo("9000296069");
        hzBillDetail.setReelid("704427200002");
        hzBillDetail.setType("02");
        hzBillDetailList.add(hzBillDetail);

        EdiCacheList ediCacheList1 = new EdiCacheList();
        ediCacheList1.setRelatedBillNo("HZ170700841");
        ediCacheList1.setSrcBillNo("9000296069");
        ediCacheList1.setReelid("704427200002");
        ediCacheList.add(ediCacheList1);
        List<HZBillDTO> list = inforStorageCenterServiceImpl.combineData(hzBillHeadList, hzBillDetailList, ediCacheList);
        Assert.assertTrue(!list.isEmpty());
    }


    @Test
    public void selectSoOutBoundInfo() {
        SoOutBoundDTO boundDTO = new SoOutBoundDTO();
        boundDTO.setBeginDate("2020-12-31 00:00:00");
        boundDTO.setEndDate("2020-10-22 00:00:00");
        ServiceData<?> ret = inforStorageCenterServiceImpl.selectSoOutBoundInfo(boundDTO);
        Assert.assertTrue(ret != null);
    }

    @Test
    public void selectEdiSosInfo() {
        SoOutBoundDTO boundDTO = new SoOutBoundDTO();
        boundDTO.setBillNo("B20110400005");
        ServiceData<?> ret = inforStorageCenterServiceImpl.selectEdiSosInfo(boundDTO);
        Assert.assertTrue(ret != null);
    }

    @Test
    public void getSoByReverseScrap() {
        String externalOrderkey2 = "B13081300132";
        String orderKey = "9000113995";
        inforStorageCenterServiceImpl.getSoByReverseScrap(externalOrderkey2, orderKey);
        Assert.assertTrue(Objects.nonNull(externalOrderkey2));
    }

    @Test
    public void pushReverseScrapBillToStorageCenter() {
        String xEmpNo = "10263048";
        String externalOrderkey2 = "HZ141000018";
        String orderKey = "9000001838";
        inforStorageCenterServiceImpl.pushReverseScrapBillToStorageCenter(xEmpNo, externalOrderkey2, orderKey);
        Assert.assertTrue(Objects.nonNull(externalOrderkey2));
    }

    @Test
    public void getReqHeadAndDetailReqParams() {
        ReqHeadReqInstanceDTO dto = ReqHeadReqInstanceDTO.builder().build().setOrderkey("9000001838").setExternalorderkey2("HZ141000018");
        List<ReqHeadAndDetailReqParamsDTO> list = inforStorageCenterServiceImpl.getReqHeadAndDetailReqParams(dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void pushReverseScrapDataToStorageCenter() {
        String xEmpNo = "10263048";
        List<ReqHeadAndDetailReqParamsDTO> list = new ArrayList<ReqHeadAndDetailReqParamsDTO>();
        inforStorageCenterServiceImpl.pushReverseScrapDataToStorageCenter(list, xEmpNo);
        Assert.assertTrue(Objects.nonNull(xEmpNo));
    }

    /*Started by AICoder, pid:c510b9ccedh311f14cc309a831dafe34c4d7dfb0*/
    @Test
    public void testSelectEdiPosInfo_validInput() throws Exception {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1", "item2"));
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("2023-01-31 23:59:59");

        PowerMockito.when(ediPoSRepository.getTransCountLimit(anyString())).thenReturn(500);
        List<PoInBoundInfoDTO> expectedList = Arrays.asList(new PoInBoundInfoDTO(), new PoInBoundInfoDTO());
        PowerMockito.when(ediPoSRepository.getPoInboundInfo(any(PoInBoundDTO.class))).thenReturn(expectedList);

        List<PoInBoundInfoDTO> result = inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);

        assertNotNull(result);
        assertEquals(expectedList, result);
    }

    @Test
    public void testSelectEdiPosInfo_invalidBeginDate() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1", "item2"));
        inboundDTO.setInstoreBegindate("invalid-date");
        inboundDTO.setInstoreEnddate("2023-01-31 23:59:59");

        assertThrows(BusiException.class, ()->{
            inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
        });
    }

    @Test
    public void testSelectEdiPosInfo_invalidEndDate() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1", "item2"));
        inboundDTO.setInstoreBegindate("2023-01-31 23:59:59");
        inboundDTO.setInstoreEnddate("invalid-date");

        assertThrows(BusiException.class, ()->{
            inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
        });
    }

    /*Started by AICoder, pid:o6415p15a8nc44614272086c41a0816adf81ac6c*/
    @Test(expected = Exception.class)
    public void testSelectEdiPosInfo_BeginDateEmpty() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1"));
        inboundDTO.setInstoreBegindate(null);
        inboundDTO.setInstoreEnddate("2023-01-31");

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }

    @Test(expected = Exception.class)
    public void testSelectEdiPosInfo_EndDateEmpty() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1"));
        inboundDTO.setInstoreBegindate("2023-01-01");
        inboundDTO.setInstoreEnddate("");

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }
    /*Ended by AICoder, pid:o6415p15a8nc44614272086c41a0816adf81ac6c*/

    @Test(expected = Exception.class)
    public void testSelectEdiPosInfo_itemNoEmpty() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Collections.emptyList());
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("2023-01-31 23:59:59");

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }

    @Test(expected = BusiException.class)
    public void testSelectEdiPosInfo_noEndDate() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1", "item2"));
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("");

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }

    @Test(expected = BusiException.class)
    public void testSelectEdiPosInfo_ItemNoIsEmpty() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("", ""));
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("");

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }

    @Test(expected = Exception.class)
    public void testSelectEdiPosInfo_itemNoCountExceedsLimit() {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        List<String> itemList = new ArrayList<>();
        for (int i = 0; i < 600; i++) {
            itemList.add("item" + i);
        }
        inboundDTO.setItemNo(itemList);
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("2023-01-31 23:59:59");

        PowerMockito.when(ediPoSRepository.getTransCountLimit(anyString())).thenReturn(500);
        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }

    @Test(expected = Exception.class)
    public void testSelectEdiPosInfo_noDataFound() throws Exception {
        PoInBoundDTO inboundDTO = new PoInBoundDTO();
        inboundDTO.setItemNo(Arrays.asList("item1", "item2"));
        inboundDTO.setInstoreBegindate("2023-01-01 00:00:00");
        inboundDTO.setInstoreEnddate("2023-01-31 23:59:59");
        PowerMockito.when(ediPoSRepository.getTransCountLimit(anyString())).thenReturn(500);
        PowerMockito.when(ediPoSRepository.getPoInboundInfo(any(PoInBoundDTO.class))).thenReturn(Collections.emptyList());

        inforStorageCenterServiceImpl.selectEdiPosInfo(inboundDTO);
    }
    /*Ended by AICoder, pid:c510b9ccedh311f14cc309a831dafe34c4d7dfb0*/
    /*Started by AICoder, pid:dd48884bb43b9da145e00ba87065d5803b0286c3*/
    @Test
    public void testHasNonEmptyItem_withNonEmptyList() throws Exception {
        // Given
        List<String> itemNo = Arrays.asList("item1", "", null);

        // When
        boolean result = Whitebox.invokeMethod(inforStorageCenterServiceImpl, "hasNonEmptyItem", itemNo);

        // Then
        assertTrue(result);
    }

    @Test
    public void testHasNonEmptyItem_withEmptyList() throws Exception {
        // Given
        List<String> itemNo = Collections.emptyList();

        // When
        boolean result = Whitebox.invokeMethod(inforStorageCenterServiceImpl, "hasNonEmptyItem", itemNo);

        // Then
        assertFalse(result);
    }

    @Test
    public void testHasNonEmptyItem_withAllNullsAndEmptyStrings() throws Exception {
        // Given
        List<String> itemNo = Arrays.asList(null, "", null);

        // When
        boolean result = Whitebox.invokeMethod(inforStorageCenterServiceImpl, "hasNonEmptyItem", itemNo);

        // Then
        assertFalse(result);
    }
    /*Ended by AICoder, pid:dd48884bb43b9da145e00ba87065d5803b0286c3*/

}
