package com.zte.application.infor;

import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Value;


/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.crypto.*")
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,
        SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class,})
public class EccnStockRemoteServiceTest {

    @InjectMocks
    private EccnStockRemoteService eccnStockRemoteService;
    @Value("${eccn.token}")
    private String eccnToken;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,
                SpringContextUtil.class, RedisSerialNoUtil.class, HttpClientUtil.class);
    }
    @Test
    public void getEccnStockInfo() throws Exception {

        String paramsMap = "";
        String responseStr = null;
        PowerMockito.field(EccnStockRemoteService.class, "eccnToken").set(eccnStockRemoteService, "x3W10I80hptuo3pm");
        PowerMockito.field(EccnStockRemoteService.class, "inoneUrl").set(eccnStockRemoteService, "https://icosg.test.zte.com.cn");
        PowerMockito.field(EccnStockRemoteService.class, "inoneAppcode").set(eccnStockRemoteService, "phPm135F5pDTNGvS9hM6MZbUhlrubpaQjLZMCNbBzBnixb9YnbwT62z2tlZJI1MC");

        responseStr = "{\"code\":{\"code\":\"0005\",\"msgId\":\"tokenCheckFailed\",\"msg\":\"token校验不通过.The token check failed.\"},\"bo\":null,\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(responseStr);
        Whitebox.invokeMethod(eccnStockRemoteService, "getEccnStockInfo", paramsMap);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":{\"current\":1,\"total\":1,\"rows\":[{\"itemNo\":\"125047031000\",\"version\":\"AB\",\"orgId\":\"0000\",\"complianceFlg\":1,\"errMsg\":-1,\"earFlgDM25\":\"N\",\"deminimisDM25\":\"0.00%\",\"usECCN\":\"5A002\",\"license\":null,\"licenseException\":\"ENC\",\"applicableExcClause\":\"740.17(a)(3)&(b)(2)\",\"ccats\":\"G178076\",\"earFlgDM10\":\"Y\",\"deminimisDM10\":\"44.83%\",\"calculateTime\":\"2019-01-25 13:31:59\",\"remarksZH\":null,\"remarksEN\":null,\"purchasePartFlg\":\"Y\",\"lastUpdatedTime\":\"2025-03-20 08:03:54\",\"comptime\":\"2019-07-16 20:09:20\",\"convECCN\":\"5A002\",\"partLink\":\"https://eccn.test.zte.com.cn/zte-grc-eccn-ui/eccn-detail?encryptItemNumber=RUKxm5is9ibrfmBFldKz3aizVXaIkJBg&encryptOrgId=Ul7KQt9h92xt928M4S9Z6Q==&encryptVersion=TaXElPgJuK9dt1LZM2ze5A==\",\"classified\":\"Y\",\"preAuthorization\":null,\"countryRegionGroup\":null}]},\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(responseStr);
        Whitebox.invokeMethod(eccnStockRemoteService, "getEccnStockInfo", paramsMap);

        Assert.assertNotNull(paramsMap);
    }

}
