package com.zte.application.infor;

import com.zte.application.infor.impl.StoreIssueSituationServiceImpl;
import com.zte.domain.model.infor.StoreIssueSituationRepository;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.LocalCacheUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.zte.common.utils.Constant.INT_1;
import static com.zte.common.utils.Constant.INT_1000;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, Executor.class, LocalCacheUtil.class, HttpClientUtil.class})
public class StoreIssueSituationServiceImplTest {
    @InjectMocks
    private StoreIssueSituationServiceImpl issueSituationService;
    @Mock
    private StoreIssueSituationRepository situationRepository;
    @Mock
    private RestTemplate restTemplate;
    private ThreadPoolExecutor thplExecutor = new ThreadPoolExecutor(10, 10,
            10, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, LocalCacheUtil.class, HttpClientUtil.class);
        PowerMockito.field(StoreIssueSituationServiceImpl.class, "thplExecutor")
                .set(issueSituationService, thplExecutor);
    }

    @Test
    public void updateProdRecDate() {
        String runNormal = "Y";
        List<String> externList = new ArrayList<>();
        List<String> externList2 = new ArrayList<>();
        externList2.add(runNormal);
        PowerMockito.when(situationRepository.getStoreIssueList(INT_1, INT_1000, runNormal)).thenReturn(externList2);
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"failed\",\"msg\": \"参数效验不通过\"\t},\t\"bo\": [\"rows: 仅支持输入字符、数字，'-'，'_'， '/' 和 '.' 。\",\"fsPageDTO.pageSize: 输入值必须是大于0的整数。\",\"fsPageDTO.totalRecord: 输入值必须是大于0的整数。\",\"fsPageDTO.currentPage: 输入值必须是大于0的整数。\",\"fsPageDTO.totalPage: 输入值必须是大于0的整数。\",\"fsMaterialDTOs[0].fsGeneInfoDTO.code: 仅支持输入字符、数字，'-'，'_'， '/' 和 '.' 。\"]}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        issueSituationService.updateProdRecDate("129571751003AOB", "111");
        PowerMockito.when(situationRepository.getStoreIssueList(INT_1, INT_1000, runNormal)).thenReturn(externList);
        issueSituationService.updateProdRecDate("129571751003AOB", "111");
        PowerMockito.when(situationRepository.getStoreIssueList(INT_1, INT_1000, runNormal)).thenThrow(new NullPointerException());
        issueSituationService.updateProdRecDate("129571751003AOB", "111");
        Assert.assertTrue(Objects.nonNull(runNormal));
    }

    @Test
    public void getRemoteList() {
        String runNormal = "Y";
        String responseStr = "{\"code\": {\"code\": \"0000\",\"msgId\": \"success\",\"msg\": \"请求操作成功\"}}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        issueSituationService.getRemoteList(Tools.newArrayList("129571751003AOB"), "1111");
        Assert.assertTrue(Objects.nonNull(runNormal));
    }
}
