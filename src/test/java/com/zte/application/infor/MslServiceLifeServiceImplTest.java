package com.zte.application.infor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.MslServiceLifeServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.MslServiceLifeInfo;
import com.zte.domain.model.infor.MslServiceLifeRepository;
import com.zte.interfaces.infor.dto.StockFlowOutDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.MslServiceLifeInfoVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2025-04-03 16:01
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class, HttpClientUtil.class, ExcelUtil.class, Tools.class,JacksonJsonConverUtil.class})
public class MslServiceLifeServiceImplTest {
    @InjectMocks
    private MslServiceLifeServiceImpl mslServiceLifeService;
    @Mock
    private MslServiceLifeRepository mslServiceLifeRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private ObjectMapper mapperInstance;
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class,HttpClientUtil.class);
    }
    @Test
    public void insertMslServiceLifeJob() throws Exception{
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("1000071");
        dto.setLookupMeaning("WH1");
        dto.setAttribute5(new BigDecimal(1));
        sysLookupValuesDTOList.add(dto);
        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupType("1000078");
        dto1.setLookupMeaning("2334");
        sysLookupValuesDTOList.add(dto1);
        SysLookupValuesDTO dto2=new SysLookupValuesDTO();
        dto2.setLookupType("1000079");
        dto2.setLookupCode("100007600001");
        dto2.setLookupMeaning("2334");
        sysLookupValuesDTOList.add(dto2);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        mslServiceLifeService.insertMslServiceLifeJob();

        List<SysLookupValuesDTO> sysLookupValuesDTOList1 =new ArrayList<>();
        SysLookupValuesDTO dto3=new SysLookupValuesDTO();
        dto3.setLookupType("1000071");
        dto3.setLookupMeaning("WH1");
        dto3.setAttribute5(new BigDecimal(2));
        sysLookupValuesDTOList1.add(dto3);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList1);
        mslServiceLifeService.insertMslServiceLifeJob();
        Assert.assertTrue(true);
    }

    @Test
    public void getMslServiceLifeData() throws Exception{
        MslServiceLifeInfo dto=new MslServiceLifeInfo();
        mslServiceLifeService.getMslServiceLifeData(dto);

        List<MslServiceLifeInfo> list=new ArrayList<>();
        dto.setWhseid("WH1");
        list.add(dto);
        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeList(Mockito.any())).thenReturn(list);
        mslServiceLifeService.getMslServiceLifeData(dto);

        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeList(Mockito.any())).thenReturn(list);
        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeByOrderList(Mockito.any())).thenReturn(list);
        mslServiceLifeService.getMslServiceLifeData(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void getDeliveryDateByMslServiceLife() throws Exception{
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        MslServiceLifeInfo dto=new MslServiceLifeInfo();
        dto.setWhseid("WH1");
        dto.setSerialnumber("ZTE0001");
        List<MslServiceLifeInfo> list=new ArrayList<>();
        list.add(dto);
        String responseStr = "{}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        JsonNode json = null;
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        mslServiceLifeService.getDeliveryDateByMslServiceLife(list);

        responseStr = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json2 = objectMapper.readTree(responseStr);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json2);
        mslServiceLifeService.getDeliveryDateByMslServiceLife(list);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        ObjectMapper objectMapper2 = new ObjectMapper();
        JsonNode json3 = objectMapper2.readTree(responseStr);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json3);
        mslServiceLifeService.getDeliveryDateByMslServiceLife(list);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\n" +
                "            \"bill_no\": \"XBZKCN20201126000245\",\n" +
                "            \"id\": \"1331788645660274690\",\n" +
                "            \"journal_date\": \"2020-11-26T02:35:31.000Z\",\n" +
                "            \"reelid\": \"ZTE00200812000028\",\n" +
                "            \"src_bill_no\": \"XBZKZH20201126000243\"\n" +
                "        }],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        ObjectMapper objectMapper3 = new ObjectMapper();
        JsonNode json4 = objectMapper3.readTree(responseStr);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);
        mslServiceLifeService.getDeliveryDateByMslServiceLife(list);

        responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\n" +
                "            \"bill_no\": \"XBZKCN20201126000245\",\n" +
                "            \"id\": \"1331788645660274690\",\n" +
                "            \"journal_date\": \"234\",\n" +
                "            \"reelid\": \"ZTE00200812000028\",\n" +
                "            \"src_bill_no\": \"XBZKZH20201126000243\"\n" +
                "        }],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);
        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json5 = objectMapper4.readTree(responseStr);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json5);
        mslServiceLifeService.getDeliveryDateByMslServiceLife(list);
        Assert.assertTrue(true);
    }

    @Test
    public void formattedDateTime() throws Exception{
        String str = "2020-11-26T02:35:31.000Z";
        mslServiceLifeService.formattedDateTime(str);
        String str1 = "2020-11-26 10:35:31";

        mslServiceLifeService.formattedDateTime(str1);
        Assert.assertTrue(true);
    }

    @Test
    public void getStockFlowList() throws Exception{
        List<StockFlowOutDTO> list=new ArrayList<>();
        StockFlowOutDTO dto=new StockFlowOutDTO();
        dto.setSrcBillNo("XBZKCN20201126000245");
        dto.setReelid("ZTE00200812000028");
        dto.setJournalDate("2020-11-26T02:35:31.000Z");
        list.add(dto);
        StockFlowOutDTO dto1=new StockFlowOutDTO();
        dto1.setSrcBillNo("XBZKCN20201126000245");
        dto1.setReelid("ZTE00200812000028");
        dto1.setJournalDate("2020-11-26T02:35:31.000Z");
        list.add(dto1);
        List<MslServiceLifeInfo> mslServiceLifeInfoList=new ArrayList<>();
        MslServiceLifeInfo mslServiceLifeInfo=new MslServiceLifeInfo();
        mslServiceLifeInfo.setSerialnumber("ZTE00200812000028");
        mslServiceLifeInfo.setExternalorderkey2("XBZKCN20201126000246");
        mslServiceLifeInfoList.add(mslServiceLifeInfo);
        mslServiceLifeService.getStockFlowList(mslServiceLifeInfoList,list);

        List<StockFlowOutDTO> list1=new ArrayList<>();
        StockFlowOutDTO dto2=new StockFlowOutDTO();
        dto2.setSrcBillNo("XBZKCN20201126000245");
        dto2.setReelid("ZTE00200812000028");
        dto2.setJournalDate("");
        list1.add(dto2);
        List<MslServiceLifeInfo> mslServiceLifeInfoList1=new ArrayList<>();
        MslServiceLifeInfo mslServiceLifeInfo1=new MslServiceLifeInfo();
        mslServiceLifeInfo1.setSerialnumber("ZTE00200812000028");
        mslServiceLifeInfo1.setExternalorderkey2("XBZKCN20201126000245");
        mslServiceLifeInfoList1.add(mslServiceLifeInfo1);
        mslServiceLifeService.getStockFlowList(mslServiceLifeInfoList1,list1);

        List<StockFlowOutDTO> list2=new ArrayList<>();
        StockFlowOutDTO dto3=new StockFlowOutDTO();
        dto3.setSrcBillNo("XBZKCN20201126000245");
        dto3.setReelid("ZTE00200812000028");
        dto3.setJournalDate("2020-11-26T02:35:31.000Z");
        list2.add(dto3);
        mslServiceLifeService.getStockFlowList(mslServiceLifeInfoList1,list2);
        Assert.assertTrue(true);
    }

    @Test
    public void getLookupValues() throws Exception{
        List<SysLookupValuesDTO> list=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        list.add(dto);
        mslServiceLifeService.getLookupValues(dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void getStockFlowByRidJob() throws Exception{
        mslServiceLifeService.getStockFlowByRidJob();
        Assert.assertTrue(true);
    }

    @Test
    public void mslServiceLifeRedDotJob() throws Exception{
        mslServiceLifeService.mslServiceLifeRedDotJob();

        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupMeaning("23423");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(mslServiceLifeRepository.selectWhseId()).thenReturn(sysLookupValuesDTOList);
        mslServiceLifeService.mslServiceLifeRedDotJob();

        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeRedDot(any())).thenReturn(null);
        mslServiceLifeService.mslServiceLifeRedDotJob();

        List<MslServiceLifeInfo> list = new ArrayList<>();
        MslServiceLifeInfo mslServiceLifeInfo=new MslServiceLifeInfo();
        mslServiceLifeInfo.setSerialkey(1);
        list.add(mslServiceLifeInfo);
        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeRedDot(any())).thenReturn(list);
        mslServiceLifeService.mslServiceLifeRedDotJob();
        Assert.assertTrue(true);
    }

    @Test
    public void sendRedDotTask() throws Exception{
        List<MslServiceLifeInfo> list = new ArrayList<>();
        MslServiceLifeInfo mslServiceLifeInfo=new MslServiceLifeInfo();
        mslServiceLifeInfo.setSerialkey(1);
        mslServiceLifeInfo.setClassgroup("234234");
        mslServiceLifeInfo.setSku("234234");
        mslServiceLifeInfo.setLottable02("234234");
        mslServiceLifeInfo.setSerialnumber("234234");
        list.add(mslServiceLifeInfo);
        String empNo = "10346719";
        SysLookupValuesDTO dto =new SysLookupValuesDTO();
        dto.setDescription("32");
        dto.setDbType(2);
        mslServiceLifeService.sendRedDotTask(list,empNo,dto);
        Assert.assertTrue(true);
    }

    @Test
    public void getRedDotDesc() throws Exception{
        MslServiceLifeInfo mslServiceLifeInfo=new MslServiceLifeInfo();
        mslServiceLifeInfo.setSerialkey(1);
        mslServiceLifeInfo.setClassgroup("");
        mslServiceLifeInfo.setSku("234234");
        mslServiceLifeInfo.setLottable02("234234");
        mslServiceLifeInfo.setSerialnumber("234234");
        SysLookupValuesDTO dto =new SysLookupValuesDTO();
        dto.setDescription("32");
        dto.setDbType(2);
        mslServiceLifeService.getRedDotDesc(dto,mslServiceLifeInfo);

        dto.setDbType(1);
        mslServiceLifeInfo.setClassgroup("234234");
        mslServiceLifeService.getRedDotDesc(dto,mslServiceLifeInfo);
        Assert.assertTrue(true);
    }

    @Test
    public void queryMslServiceLife() throws Exception{
        MslServiceLifeInfo mslServiceLifeInfo=new MslServiceLifeInfo();
        mslServiceLifeInfo.setSerialkey(1);
        mslServiceLifeInfo.setClassgroup("234234");
        mslServiceLifeInfo.setSku("234234");
        mslServiceLifeInfo.setLottable02("234234");
        mslServiceLifeInfo.setSerialnumber("234234");
        mslServiceLifeInfo.setPageIndex(1);
        mslServiceLifeInfo.setPageSize(10);
        mslServiceLifeService.queryMslServiceLife(mslServiceLifeInfo);

        List<MslServiceLifeInfo> list = new ArrayList<>();
        list.add(mslServiceLifeInfo);
        MslServiceLifeInfoVo dto=new MslServiceLifeInfoVo();
        dto.setMslServiceLifeInfoList(list);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void selectWhseId(){
        mslServiceLifeService.selectWhseId();
        List<SysLookupValuesDTO> list=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupCode("324343");
        list.add(dto);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void exportExcel() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class);
        MslServiceLifeInfo dto=new MslServiceLifeInfo();
        dto.setCreatedBy("123");
        dto.setPageIndex(1);
        dto.setPageSize(1);
        dto.setStartRow(1);
        dto.setEndRow(1);
        PowerMockito.when(mslServiceLifeRepository.getMslServiceLifeAndOrderCount(Mockito.any())).thenReturn(1);
        mslServiceLifeService.exportExcel(dto);
        Assert.assertTrue(true);
    }

    @Test
    public void mslServiceLifeQuery() throws Exception{
        MslServiceLifeInfo dto=new MslServiceLifeInfo();
        dto.setSerialkey(12);
        try {
            mslServiceLifeService.mslServiceLifeQuery(dto);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.MSL_SERIALNUMBER_IS_NOT_EMPTY);
        }

        dto.setSerialnumber("12");
        try {
            mslServiceLifeService.mslServiceLifeQuery(dto);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.MSL_SKU_IS_NOT_EMPTY);
        }

        dto.setSku("12");
        try {
            mslServiceLifeService.mslServiceLifeQuery(dto);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.MSL_LOTTABLE02_IS_NOT_EMPTY);
        }

        dto.setLottable02("12");
        try {
            mslServiceLifeService.mslServiceLifeQuery(dto);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.MSL_WMWHSE_IS_NOT_EMPTY);
        }

        dto.setWhseid("12");
        mslServiceLifeService.mslServiceLifeQuery(dto);
        List<MslServiceLifeInfo> list=new ArrayList<>();
        list.add(dto);
        Assert.assertTrue(Objects.nonNull(list));
    }
}
