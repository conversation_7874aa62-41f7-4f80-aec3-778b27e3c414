package com.zte.application.infor;

import com.zte.application.infor.impl.TransferBoxServiceImpl;
import com.zte.application.step.ZteAlibabaService;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.TransferBoxRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SnBoundDetail;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.dto.TransferReceiptDetailDTO;
import com.zte.interfaces.infor.dto.ZmsTransferBox;
import com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

@PrepareForTest({BusiAssertException.class, RedisSerialNoUtil.class})
@RunWith(PowerMockRunner.class)
public class TransferBoxServiceImplTest {

    @Mock
    private ZteAlibabStockInfoUploadRepository zteAlibabStockInfoUploadRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepositor;

    @Mock
    private ZteAlibabaService zteAlibabaService;

    @Mock
    private TransferBoxRepository transferBoxRepository;

    @InjectMocks
    private TransferBoxServiceImpl transferBoxService;

    @Test
    public void testSyncTransferBoxDataJob_NonEmptyWarehouseList_EmptySyncData() {
        ZteWarehouseInfoDTO warehouseId = new ZteWarehouseInfoDTO();
        warehouseId.setWarehouseId("warehouseId");
        List<ZteWarehouseInfoDTO> warehouseList = Collections.singletonList(warehouseId);
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(warehouseList);
        when(transferBoxRepository.selectSyncData("warehouseId")).thenReturn(Collections.emptyList());

        transferBoxService.syncTransferBoxDataJob();

        verify(transferBoxRepository).selectSyncData("warehouseId");
        verify(transferBoxRepository, never()).batchInsertTransferBox(anyList());
    }

    @Test
    public void testSyncTransferBoxDataJob_NonEmptyWarehouseList_NonEmptySyncData() {
        ZteWarehouseInfoDTO warehouseId = new ZteWarehouseInfoDTO();
        warehouseId.setWarehouseId("warehouseId");
        List<ZteWarehouseInfoDTO> warehouseList = Collections.singletonList(warehouseId);
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(warehouseList);

        List<TransferReceiptDetailDTO> syncData = new ArrayList<>();
        TransferReceiptDetailDTO detail = new TransferReceiptDetailDTO();
        detail.setDatereceived(new Date());
        detail.setFromId("123");
        detail.setLottable02("123");
        detail.setQty(new BigDecimal(5));
        detail.setSourceKey("key");
        detail.setExternalKey("key");
        detail.setSku("123");
        detail.setToLot("lot");
        detail.setToLoc("loc");
        detail.setWhseid("warehouseId");
        detail.setCustomerItemNo("ali");
        detail.setUnPack(0);
        syncData.add(detail);
        TransferReceiptDetailDTO detail2 = new TransferReceiptDetailDTO();
        detail2.setDatereceived(new Date());
        detail2.setFromId("123");
        detail2.setLottable02("123");
        detail2.setQty(new BigDecimal(5));
        detail2.setSourceKey("key");
        detail2.setExternalKey("key");
        detail2.setSku("123");
        detail2.setToLot("lot");
        detail2.setToLoc("loc");
        detail2.setWhseid("warehouseId");
        detail2.setCustomerItemNo("ali");
        detail2.setUnPack(1);
        syncData.add(detail2);

        when(transferBoxRepository.selectSyncData("warehouseId")).thenReturn(syncData);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("meaning");
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);

        transferBoxService.syncTransferBoxDataJob();

        verify(transferBoxRepository).selectSyncData("warehouseId");
        verify(transferBoxRepository).batchInsertTransferBox(anyList());
    }

    @Test
    public void testSyncTransferBoxDataJob_EmptyWarehouseList() {
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(Collections.emptyList());

        transferBoxService.syncTransferBoxDataJob();

        verify(transferBoxRepository, never()).selectSyncData(anyString());
        verify(transferBoxRepository, never()).batchInsertTransferBox(anyList());
    }

    @Test
    public void testSyncTransferBoxDataJob_SysLookupValuesNotExists() {
        ZteWarehouseInfoDTO warehouseId = new ZteWarehouseInfoDTO();
        warehouseId.setWarehouseId("warehouseId");
        List<ZteWarehouseInfoDTO> warehouseList = Collections.singletonList(warehouseId);
        when(zteAlibabStockInfoUploadRepository.getInforWarehouseList()).thenReturn(warehouseList);

        List<TransferReceiptDetailDTO> syncData = new ArrayList<>();
        TransferReceiptDetailDTO detail = new TransferReceiptDetailDTO();
        detail.setDatereceived(new Date());
        detail.setFromId("123");
        detail.setLottable02("123");
        detail.setQty(new BigDecimal(5));
        detail.setSourceKey("key");
        detail.setExternalKey("key");
        detail.setSku("123");
        detail.setToLot("lot");
        detail.setToLoc("loc");
        detail.setWhseid("warehouseId");
        detail.setCustomerItemNo("ali");
        detail.setUnPack(0);
        syncData.add(detail);
        TransferReceiptDetailDTO detail2 = new TransferReceiptDetailDTO();
        detail2.setDatereceived(new Date());
        detail2.setFromId("123");
        detail2.setLottable02("123");
        detail2.setQty(new BigDecimal(5));
        detail2.setSourceKey("key");
        detail2.setExternalKey("key");
        detail2.setSku("123");
        detail2.setToLot("lot");
        detail2.setToLoc("loc");
        detail2.setWhseid("warehouseId");
        detail2.setCustomerItemNo("ali");
        detail2.setUnPack(1);
        syncData.add(detail2);

        when(transferBoxRepository.selectSyncData("warehouseId")).thenReturn(syncData);
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(new SysLookupValuesDTO());

        transferBoxService.syncTransferBoxDataJob();
        verify(transferBoxRepository, times(0)).selectPendingTransfer();
    }

    /*Started by AICoder, pid:z054150b44la15014560086991fc4370e9c6c6d4*/
    @Test
    public void testProcessTransfersDataJob_NoPendingData() {
        when(transferBoxRepository.selectPendingTransfer()).thenReturn(Collections.emptyList());

        transferBoxService.processTransfersDataJob();

        verify(transferBoxRepository, times(1)).selectPendingTransfer();
        verifyNoMoreInteractions(transferBoxRepository);
    }

    @Test
    public void testProcessTransfersDataJob_SomeBoxesHaveStatusZero() {
        ZmsTransferBox box1 = new ZmsTransferBox();
        box1.setStatus("0");
        box1.setExternalKey("key");

        List<ZmsTransferBox> pendingList = Arrays.asList(box1);
        when(transferBoxRepository.selectPendingTransfer()).thenReturn(pendingList);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);

        transferBoxService.processTransfersDataJob();

        verify(transferBoxRepository, times(1)).selectPendingTransfer();
        verify(inventoryholdRecordRepositor, times(1)).getLookupValue(anyString());
        verifyNoMoreInteractions(transferBoxRepository, zteAlibabaService);
    }

    @Test
    public void testProcessTransfersDataJob_LookupValueNotFound() {
        List<ZmsTransferBox> pendingList = Arrays.asList(new ZmsTransferBox());
        when(transferBoxRepository.selectPendingTransfer()).thenReturn(pendingList);
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(null);

        try {
            transferBoxService.processTransfersDataJob();
            fail("Expected BusiAssertException");
        } catch (Exception e) {
        }
        verify(transferBoxRepository, times(1)).selectPendingTransfer();
    }

    @Test
    public void testProcessTransfersDataJob_FailedProcessing() {
        ZmsTransferBox box1 = new ZmsTransferBox();
        box1.setExternalKey("key1");
        box1.setOldPkgId("pkgId");
        box1.setMpn("mpn1");
        box1.setQty(new BigDecimal(10));
        box1.setMessageId("msg1");
        box1.setSerialKey("serial1");

        List<ZmsTransferBox> pendingList = Arrays.asList(box1);
        when(transferBoxRepository.selectPendingTransfer()).thenReturn(pendingList);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);

        SnBoundDetail snDetail = new SnBoundDetail();
        snDetail.setPkgId("pkgId");
        snDetail.setSn("sn");
        when(transferBoxRepository.selectSnByExternalKeyAndPkgs(anyString(), anyList())).thenReturn(Arrays.asList(snDetail));

        when(zteAlibabaService.pushDataToB2B(anyString(), any(ZteDeductionPlanParamDTO.class), anyString())).thenReturn(false);

        transferBoxService.processTransfersDataJob();

        verify(transferBoxRepository, times(1)).selectPendingTransfer();
        verify(inventoryholdRecordRepositor, times(1)).getLookupValue(anyString());
        verify(transferBoxRepository, times(1)).selectSnByExternalKeyAndPkgs(anyString(), anyList());
        verify(zteAlibabaService, times(1)).pushDataToB2B(anyString(), any(ZteDeductionPlanParamDTO.class), anyString());
    }

    @Test
    public void testProcessTransfersDataJob_SuccessfulProcessing() {
        ZmsTransferBox box1 = new ZmsTransferBox();
        box1.setExternalKey("key1");
        box1.setOldPkgId("pkg1");
        box1.setMpn("mpn1");
        box1.setQty(new BigDecimal(10));
        box1.setMessageId("msg1");
        box1.setSerialKey("serial1");

        List<ZmsTransferBox> pendingList = Arrays.asList(box1);
        when(transferBoxRepository.selectPendingTransfer()).thenReturn(pendingList);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        when(inventoryholdRecordRepositor.getLookupValue(anyString())).thenReturn(sysLookupValuesDTO);

        SnBoundDetail snDetail = new SnBoundDetail();
        snDetail.setPkgId("pkgId");
        snDetail.setSn("sn");
        when(transferBoxRepository.selectSnByExternalKeyAndPkgs(anyString(), anyList())).thenReturn(Arrays.asList(snDetail));

        when(zteAlibabaService.pushDataToB2B(anyString(), any(ZteDeductionPlanParamDTO.class), anyString())).thenReturn(true);

        transferBoxService.processTransfersDataJob();

        verify(transferBoxRepository, times(1)).selectPendingTransfer();
        verify(inventoryholdRecordRepositor, times(1)).getLookupValue(anyString());
        verify(transferBoxRepository, times(1)).selectSnByExternalKeyAndPkgs(anyString(), anyList());
        verify(zteAlibabaService, times(1)).pushDataToB2B(anyString(), any(ZteDeductionPlanParamDTO.class), anyString());
    }

    @Before
    public void setUp() {
        PowerMockito.mockStatic(BusiAssertException.class);
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        PowerMockito.when(inventoryholdRecordRepositor.getLookupValue(any())).thenReturn(new SysLookupValuesDTO());
        // Setup code if needed
            // Setup code if needed
    }
    /*Ended by AICoder, pid:z054150b44la15014560086991fc4370e9c6c6d4*/
}
