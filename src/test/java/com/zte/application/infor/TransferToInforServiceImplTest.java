package com.zte.application.infor;

import com.zte.application.infor.impl.TransferToInforServiceImpl;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.StepTransferBill;
import com.zte.domain.model.step.StepTransferDetail;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,EmailUtil.class, WebServiceClient.class})
public class TransferToInforServiceImplTest {
    @InjectMocks
    TransferToInforServiceImpl transferToInforService;
    @Mock
    StepTransferRepository stepTransfer;
    @Mock
    OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;
    @Mock
    private EmailUtil emailUtil;

    @Before
    public void init(){
        PowerMockito.mockStatic(BusiAssertException.class,EmailUtil.class, WebServiceClient.class);
    }

    @Test
    public void transferToInfor() throws Exception{
        PowerMockito.when(stepTransfer.getCountJobUnknown(JOB_AUTO_TRANSFER_BILL)).thenReturn(1);
        Whitebox.invokeMethod(transferToInforService,"transferToInfor");
        List<StepTransferBill> transferBillList1 = new ArrayList<>();
        List<StepTransferBill> transferBillList = new ArrayList<>();
        StepTransferBill step = new StepTransferBill();
        step.setBillNo("22236");
        transferBillList.add(step);
        PowerMockito.when(stepTransfer.getCountJobUnknown(JOB_AUTO_TRANSFER_BILL)).thenReturn(0);
        PowerMockito.when(stepTransfer.getJobId()).thenReturn(121212);
        doNothing().when(stepTransfer).insertJobHistory(JOB_AUTO_TRANSFER_BILL, 121212);
        PowerMockito.when(stepTransfer.getTransferBilToInfor()).thenReturn(transferBillList1);
        Whitebox.invokeMethod(transferToInforService,"transferToInfor");

        //返回true
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        List<SoHeader> listSoHeader = new ArrayList<>();
        SoHeader so = new SoHeader();
        so.setExternalOrderKey2("333");
        listSoHeader.add(so);
        List<SoDetail>  soDetails = new ArrayList<>();
        SoDetail de = new SoDetail();
        de.setRef60("333");
        soDetails.add(de);
        List<String> users = new ArrayList<>();
        users.add("555");
        PowerMockito.when(stepTransfer.getTransferBilToInfor()).thenReturn(transferBillList);
        PowerMockito.when(onlineFallBackApplyBillRepository.getWsdlUrl()).thenReturn("123456");
        PowerMockito.when(WebServiceClient.submitInfor(Mockito.any(), Mockito.any())).thenReturn(retData);
        PowerMockito.when(stepTransfer.getTransferSoHead("111")).thenReturn(listSoHeader);
        PowerMockito.when(stepTransfer.getTransferSoDe("111")).thenReturn(soDetails);
        doNothing().when(stepTransfer).insertTransferSoHead(listSoHeader);
        doNothing().when(stepTransfer).insertTransferSoDetail(soDetails);
        doNothing().when(stepTransfer).updateJobHistory(12, TRANSFER_SUCCESS, "");
        PowerMockito.when(stepTransfer.getEmailUser(TRANSFER_EMAIL_USER)).thenReturn(users);
        Whitebox.invokeMethod(transferToInforService,"transferToInfor");

        //返回false
        ServiceData retData1 = new ServiceData();
        RetCode retCode1 = new RetCode(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
        retData1.setCode(retCode1);
        List<StepTransferDetail> transferDetailsList = new ArrayList<>();
        StepTransferDetail td = new StepTransferDetail();
        td.setDeliveryId("555");
        transferDetailsList.add(td);
        PowerMockito.when(WebServiceClient.submitInfor(Mockito.any(), Mockito.any())).thenReturn(retData1);
        PowerMockito.when(stepTransfer.getTransferDetail("56")).thenReturn(transferDetailsList);
        doNothing().when(stepTransfer).updateDeliveryTransfer(transferDetailsList);
        doNothing().when(stepTransfer).updateTransferStatus("555");
        Whitebox.invokeMethod(transferToInforService,"transferToInfor");
        Assert.assertTrue(Objects.nonNull(transferDetailsList));
    }

    @Test
    public void rollbackBill() throws Exception {
        List<StepTransferDetail> transferDetailsList = new ArrayList<>();
        StepTransferDetail tds = new StepTransferDetail();
        tds.setDeliveryId("555");
        tds.setPlanQty(new BigDecimal("42"));
        transferDetailsList.add(tds);
        PowerMockito.when(stepTransfer.getTransferDetail(anyString())).thenReturn(transferDetailsList);
        doNothing().when(stepTransfer).updateDeliveryTransfer(transferDetailsList);
        doNothing().when(stepTransfer).updateTransferStatus("555");
        Whitebox.invokeMethod(transferToInforService,"rollbackBill","555");
        Assert.assertTrue(Objects.nonNull(transferDetailsList));
    }

    @Test
    public void getXmlMessage() throws Exception {
        List<SoHeader> listSoHeader = new ArrayList<>();
        SoHeader so = new SoHeader();
        so.setExternalOrderKey2("333");
        listSoHeader.add(so);
        List<SoDetail>  soDetails = new ArrayList<>();
        SoDetail de = new SoDetail();
        de.setRef60("333");
        soDetails.add(de);
        List<SoDetail>  soDetails1 = new ArrayList<>();
        PowerMockito.when(stepTransfer.getTransferSoHead(anyString())).thenReturn(listSoHeader);
        PowerMockito.when(stepTransfer.getTransferSoDe(anyString())).thenReturn(soDetails1);
        Whitebox.invokeMethod(transferToInforService,"getXmlMessage",anyString());

        PowerMockito.when(stepTransfer.getTransferSoDe(anyString())).thenReturn(soDetails);
        Whitebox.invokeMethod(transferToInforService,"getXmlMessage",anyString());
        String res = transferToInforService.getXmlMessage(anyString());
        Assert.assertTrue(Objects.nonNull(res));
    }

}
