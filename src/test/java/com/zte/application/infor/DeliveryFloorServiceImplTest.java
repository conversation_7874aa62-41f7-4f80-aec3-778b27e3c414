/* Started by AICoder, pid:ld63fk8e56id17d148b30952d0f8ed08ca821c4b */
package com.zte.application.infor;

import com.zte.application.infor.impl.DeliveryFloorServiceImpl;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.step.StepBaUserRepository;
import com.zte.interfaces.infor.dto.DeliveryFloorDTO;
import com.zte.interfaces.infor.dto.DeliveryPrintInfoDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/4/14 16:48
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class})
public class DeliveryFloorServiceImplTest {
    @InjectMocks
    private DeliveryFloorServiceImpl deliveryFloorService;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private DeliveryFloorRepository deliveryFloorRepository;
    @Mock
    private DeliveryNoReceivesRepository deliveryNoReceivesRepository;
    @Mock
    private StepBaUserRepository stepBaUserRepository;
    @Mock
    private String authToken;

    @Mock
    private String redDotUrl;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BeanUtils.class, HttpClientUtil.class, ExcelUtil.class, SpringContextUtil.class, JacksonJsonConverUtil.class);
    }


    @Test
    public void importFloorInfo() throws Exception{
        List<DeliveryFloorDTO> list = new ArrayList<>();
        String xEmpNo = "1";
        deliveryFloorService.importFloorInfo(list, xEmpNo);
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        dto.setProductBase("cs");
        dto.setLiability("123");
        dto.setDirector("工号123");
        list.add(dto);
        try {
            deliveryFloorService.importFloorInfo(list, xEmpNo);
        }catch (Exception ex) {
        }
        dto.setLiability("工号123");
        deliveryFloorService.importFloorInfo(list, xEmpNo);
        PowerMockito.when(deliveryFloorRepository.queryFloorInfoExist(Mockito.any())).thenReturn(1);
        deliveryFloorService.importFloorInfo(list, xEmpNo);
        Assert.assertNotNull(list);
    }

    @Test
    public void getFloorInfo() throws Exception {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        deliveryFloorService.getFloorInfo(dto);
        dto.setProductBase("cs");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupMeaning("cs");
            setDescription("1");
        }});
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        deliveryFloorService.getFloorInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void loseFloorInfo() {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        String xEmpNo = "1";
        deliveryFloorService.loseFloorInfo(dto, xEmpNo);
        dto.setSerialKeys(Arrays.asList("1"));
        deliveryFloorService.loseFloorInfo(dto, xEmpNo);
        Assert.assertNotNull(dto);
    }

    @Test
    public void effectFloorInfo() {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        String xEmpNo = "1";
        deliveryFloorService.effectFloorInfo(dto, xEmpNo);
        dto.setSerialKeys(Arrays.asList("1"));
        deliveryFloorService.effectFloorInfo(dto, xEmpNo);
        Assert.assertNotNull(dto);
    }

    @Test
    public void sendRedDotInfo() {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        dto.setProductBase("cs");
        dto.setFloor("长沙10栋1F");
        dto.setCallType("Y");
        dto.setHelpType("异地交货确认");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000073");
            setLookupMeaning("cs");
            setDescription("长沙基地");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000073");
            setLookupMeaning("nj");
            setDescription("南京基地");
        }});
        sysLookupValuesDTOList.add(new SysLookupValuesDTO(){{
            setLookupType("1000072");
            setLookupMeaning("cs");
            setLookupCode("100007200001");
        }});
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        deliveryFloorService.sendRedDotInfo(dto);
        List<DeliveryFloorDetailDTO> list = new ArrayList<>();
        list.add(new DeliveryFloorDetailDTO(){{
            setFloor("1");
        }});
        PowerMockito.when(deliveryFloorRepository.getDeliveryFloorDetailVo(Mockito.any())).thenReturn(list);
        dto.setProductBase("cs");
        deliveryFloorService.sendRedDotInfo(dto);
        dto.setCallType("N");
        dto.setProductBase("cs");
        deliveryFloorService.sendRedDotInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getPrintInfo() {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        dto.setProductBase("cs");
        try {
            deliveryFloorService.getPrintInfo(dto);
        }catch (Exception ex) {
        }
        PowerMockito.when(deliveryNoReceivesRepository.queryWhseByBase(Mockito.any(), Mockito.anyString())).thenReturn(Arrays.asList("11"));
        try {
            deliveryFloorService.getPrintInfo(dto);
        }catch (Exception ex) {
        }
        List<DeliveryPrintInfoDTO> deliveryPrintInfoDTOS = new ArrayList<>();
        deliveryPrintInfoDTOS.add(new DeliveryPrintInfoDTO(){{
            setCompany("1");
        }});
        List<DeliverySignDetailDTO> deliverySignDetailDTOS = new ArrayList<>();
        deliverySignDetailDTOS.add(new DeliverySignDetailDTO(){{
            setAsnStatus("6");
        }});
        PowerMockito.when(deliveryFloorRepository.queryDeliveryInfo(Mockito.any())).thenReturn(deliverySignDetailDTOS);
        try {
            deliveryFloorService.getPrintInfo(dto);
        }catch (Exception ex) {
        }
        deliverySignDetailDTOS = new ArrayList<>();
        deliverySignDetailDTOS.add(new DeliverySignDetailDTO(){{
            setAsnStatus("10");
        }});
        PowerMockito.when(deliveryFloorRepository.queryDeliveryInfo(Mockito.any())).thenReturn(deliverySignDetailDTOS);
        PowerMockito.when(deliveryFloorRepository.getDeliveryPrintInfo(Mockito.any())).thenReturn(deliveryPrintInfoDTOS);
        dto.setProductBase("");
        PowerMockito.when(stepBaUserRepository.getFullName(Mockito.any())).thenReturn("11");
        deliveryFloorService.getPrintInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void deleteFloorInfo() {
        DeliveryFloorDTO dto = new DeliveryFloorDTO();
        String xEmpNo = "1";
        deliveryFloorService.deleteFloorInfo(dto, xEmpNo);
        dto.setSerialKeys(Arrays.asList("1"));
        deliveryFloorService.deleteFloorInfo(dto, xEmpNo);
        Assert.assertNotNull(dto);
    }

}
/* Ended by AICoder, pid:ld63fk8e56id17d148b30952d0f8ed08ca821c4b */