package com.zte.application.infor;

import com.zte.application.infor.impl.InforIapsServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.InforIapsRepository;
import com.zte.domain.model.step.InforStepErpStock;
import com.zte.domain.model.step.InforStepErpStockRepository;
import com.zte.interfaces.infor.dto.IapsInforStock;
import com.zte.interfaces.infor.dto.IapsInforStockDto;
import com.zte.interfaces.infor.dto.IapsStockDto;
import com.zte.interfaces.infor.dto.XcItemInfoDTO;
import com.zte.interfaces.infor.vo.IapsInforStockVo;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, Executor.class})
public class InforIapsServiceImplTest {
    @InjectMocks
    private InforIapsServiceImpl inforIapsService;

    @Mock
    private InforIapsRepository inforIapsRepository;

    @Mock
    private InforStepErpStockRepository inforStepErpStockRepository;

    private ThreadPoolExecutor thplExecutor = new ThreadPoolExecutor(10,10,
            10, TimeUnit.SECONDS,new LinkedBlockingQueue<>());
    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class);
        PowerMockito.field(InforIapsServiceImpl.class, "thplExecutor")
                .set(inforIapsService, thplExecutor);
    }

    @Test
    public void synXcItemInfo() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);

        List<XcItemInfoDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        for (int i=0; i<5001; i++) {
            XcItemInfoDTO xcItemInfoDTO = XcItemInfoDTO.builder().build().setSku("234235235")
                    .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
            list.add(xcItemInfoDTO);
        }
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO1 = XcItemInfoDTO.builder().build().setSku("")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO1);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO2 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO2);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO3 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO3);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO4 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("").setUpdateBy("234235235");
        list.add(xcItemInfoDTO4);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO5 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("");
        list.add(xcItemInfoDTO5);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO6 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO6);
        XcItemInfoDTO xcItemInfoDTO7 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO7);
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        list.clear();
        XcItemInfoDTO xcItemInfoDTO8 = XcItemInfoDTO.builder().build().setSku("234235235")
                .setDomesticFirst("234235235").setInternalFirst("234235235").setEnableFlag("234235235").setUpdateBy("234235235");
        list.add(xcItemInfoDTO8);
        PowerMockito.when(CommonUtils.splitList(Mockito.any())).thenReturn(null);
        PowerMockito.when(inforIapsRepository.saveXcItemInfo(Mockito.any())).thenReturn(1);
        thplExecutor.execute(()-> inforIapsRepository.saveXcItemInfo(list));
        Whitebox.invokeMethod(inforIapsService,"synXcItemInfo", list);

        Assert.assertNotNull(xcItemInfoDTO8);
    }

    @Test
    public void getInforStockInfo() throws Exception{
        IapsStockDto dto=new IapsStockDto();
        List<IapsInforStock> stockNoList=new ArrayList<>();
        dto.setStockItemList(stockNoList);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        dto.setPageSize(0);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        dto.setPageIndex(0);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        dto.setPageSize(1);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        dto.setPageIndex(1);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        for (int i=0;i<502;i++){
            IapsInforStock stockNo=new IapsInforStock();
            stockNo.setStockNo(String.valueOf(i));
            stockNoList.add(stockNo);
        }
        dto.setStockItemList(stockNoList);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        List<IapsInforStock> stockNoList1=new ArrayList<>();
        IapsInforStock stockNo=new IapsInforStock();
        stockNo.setStockNo("HUB_KXSUB");
        stockNoList1.add(stockNo);
        dto.setStockItemList(stockNoList1);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        List<IapsInforStock> stockNoList2=new ArrayList<>();
        IapsInforStock stockNo1=new IapsInforStock();
        stockNo1.setStockNo("HUB_KXSUB");
        stockNo1.setItemBarcode("220010436437");
        stockNoList2.add(stockNo1);
        dto.setStockItemList(stockNoList2);
        List<InforStepErpStock> stockList=new ArrayList<>();
        PowerMockito.when(inforStepErpStockRepository.getInforStepErpStock(Mockito.any())).thenReturn(stockList);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        InforStepErpStock infoStockNo1=new InforStepErpStock();
        infoStockNo1.setInforStock("wmwhse1");
        infoStockNo1.setStepStock("HUB_KXSUB");
        infoStockNo1.setOrganizationId("15");
        infoStockNo1.setGoodsType("16");
        stockList.add(infoStockNo1);
        PowerMockito.when(inforStepErpStockRepository.getInforStepErpStock(Mockito.any())).thenReturn(stockList);

        List<IapsInforStockDto> list=new ArrayList<>();
        IapsInforStockDto iapsInforStockDto=new IapsInforStockDto();
        iapsInforStockDto.setWhseId("wmwhse1");
        iapsInforStockDto.setStockNo("HUB_KXSUB");
        list.add(iapsInforStockDto);

        List<String> codelKupVOS=new ArrayList<>();
        codelKupVOS.add("13");
        codelKupVOS.add("14");
        PowerMockito.when(inforIapsRepository.getCodelkup()).thenReturn(codelKupVOS);
        PowerMockito.when(inforIapsRepository.selectInforStockPage(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        PowerMockito.when(inforStepErpStockRepository.getInforStepErpStock(Mockito.any())).thenReturn(stockList);
        PowerMockito.when(inforIapsRepository.getCodelkup()).thenReturn(codelKupVOS);
        PowerMockito.when(inforIapsRepository.selectInforStockCount(dto)).thenReturn(1);
        PowerMockito.when(inforIapsRepository.selectInforStockPage(dto)).thenReturn(list);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        PowerMockito.when(inforStepErpStockRepository.getInforStepErpStock(Mockito.any())).thenReturn(stockList);
        IapsInforStockDto iapsInforStockDto1=new IapsInforStockDto();
        iapsInforStockDto1.setWhseId("wmwhse");
        list.add(iapsInforStockDto1);
        PowerMockito.when(inforIapsRepository.getCodelkup()).thenReturn(codelKupVOS);
        PowerMockito.when(inforIapsRepository.selectInforStockCount(dto)).thenReturn(1);
        PowerMockito.when(inforIapsRepository.selectInforStockPage(dto)).thenReturn(list);
        Whitebox.invokeMethod(inforIapsService,"getInforStockInfo", dto);

        IapsInforStockVo stockVo=new IapsInforStockVo();
        stockVo.setTotal(1);
        stockVo.setInforStockList(list);
        Assert.assertNotNull(stockVo);
    }

}
