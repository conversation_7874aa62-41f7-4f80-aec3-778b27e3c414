package com.zte.application.infor;

import com.zte.application.infor.impl.BondedWarehouseServiceImpl;
import com.zte.domain.model.infor.BondedWarehouseInventoryInfo;
import com.zte.domain.model.infor.EdiAsnqcSRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, ServiceData.class})
public class BondedWarehouseServiceImplTest {

    @InjectMocks
    private BondedWarehouseServiceImpl bondedWarehouseService;
    @Mock
    private EdiAsnqcSRepository ediAsnqcSRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, ServiceData.class);
    }

    @Test
    public void queryBondedWarehouseInventory() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        List<BondedWarehouseInventoryInfo> inventoryInfoList = new ArrayList<>();
        ServiceData<List<BondedWarehouseInventoryInfo>> serviceData = new ServiceData<>();
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList);

        BondedWarehouseInventoryInfo inventoryInfo = new BondedWarehouseInventoryInfo();
        inventoryInfo.setItemCode("342362");
        inventoryInfo.setBoxNo("2342352");
        inventoryInfo.setStoreKey("ZTE");
        inventoryInfoList.add(inventoryInfo);
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList);

        List<BondedWarehouseInventoryInfo> inventoryInfoList1 = new ArrayList<>();
        BondedWarehouseInventoryInfo inventoryInfo1 = new BondedWarehouseInventoryInfo();
        inventoryInfo1.setItemCode("342362");
        inventoryInfo1.setBoxNo("2342352");
        inventoryInfo1.setStoreKey("ZTE");
        inventoryInfo1.setItemBarcode("3534636");
        inventoryInfoList1.add(inventoryInfo1);
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList1);

        List<BondedWarehouseInventoryInfo> inventoryInfoList2 = new ArrayList<>();
        BondedWarehouseInventoryInfo inventoryInfo2 = new BondedWarehouseInventoryInfo();
        inventoryInfo2.setItemCode("342362");
        inventoryInfo2.setBoxNo("2342352");
        inventoryInfo2.setStoreKey("ZTE");
        inventoryInfo2.setStockNo("WMWHSE1");
        inventoryInfoList2.add(inventoryInfo2);
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList2);

        List<BondedWarehouseInventoryInfo> inventoryInfoList3 = new ArrayList<>();
        BondedWarehouseInventoryInfo inventoryInfo3 = new BondedWarehouseInventoryInfo();
        inventoryInfo3.setItemCode("342362");
        inventoryInfo3.setBoxNo("2342352");
        inventoryInfo3.setStoreKey("ZTE");
        inventoryInfo3.setStockNo("WMWHSE1");
        inventoryInfo3.setItemBarcode("3534636");
        inventoryInfo3.setQty(new BigDecimal(1));
        inventoryInfoList3.add(inventoryInfo3);
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList3);

        PowerMockito.when(ediAsnqcSRepository.queryBondedWarehouseInventory(inventoryInfoList3)).thenReturn(inventoryInfoList3);
        Whitebox.invokeMethod(bondedWarehouseService, "queryBondedWarehouseInventory", inventoryInfoList3);
        ServiceData<List<BondedWarehouseInventoryInfo>> res = bondedWarehouseService.queryBondedWarehouseInventory(inventoryInfoList3);
        Assert.assertTrue(Objects.nonNull(res));
    }
}