package com.zte.application.infor;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.InventoryDiffQueryServiceImpl;
import com.zte.application.step.impl.ZteAlibabaServiceImpl;
import com.zte.application.step.impl.ZteAlibabaStockInfoUploadServiceImpl;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.AlibabaOnHandQtyVO;
import com.zte.interfaces.infor.vo.UnBindPkgSnJobVo;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.Constant.STR_OUT_STORE;
import static com.zte.common.utils.Constant.STR_STORE;
import static org.mockito.Mockito.*;

/* Started by AICoder, pid:f5e6d709efe48b9140f1087f022cc39959910321 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({ExcelUtil.class, HttpClientUtil.class,CommonUtils.class})
public class InventoryDiffQueryServiceImplTest {
    @InjectMocks
    private InventoryDiffQueryServiceImpl inventoryDiffQueryService;
    @Mock
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;
    @Mock
    private ZteAlibabaStockInfoUploadServiceImpl zteAlibabaStockInfoUploadService;
    @Mock
    private ZteAlibabaServiceImpl zteAlibabaService;
    @Mock
    private ZteAlibabStockInfoUploadRepository zteAlibabaStockInfoUploadRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(ExcelUtil.class, HttpClientUtil.class);
    }
    @Test
    public void getInventoryDiffData()  {
        InventoryDiffDTO dto = new InventoryDiffDTO();
        dto.setPageIndex(1);
        dto.setPageSize(0);
        dto.setCompareMode(true);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffData(Mockito.any())).thenReturn(null);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffDataCount(Mockito.any())).thenReturn(0);
        inventoryDiffQueryService.getInventoryDiffData(dto);

        dto.setCompareMode(false);
        dto.setPageSize(50);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffTempData(Mockito.any())).thenReturn(null);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffDataTempCount(Mockito.any())).thenReturn(0);
        inventoryDiffQueryService.getInventoryDiffData(dto);
        verify(inventoryDiffQueryRepository, times(1)).getInventoryDiffDataTempCount(Mockito.any());
    }
    @Test
    public void exportInventoryDiffData()  {
        InventoryDiffDTO dto = new InventoryDiffDTO();
        dto.setPageIndex(1);
        dto.setCompareMode(true);
        dto.setEmpNo("gdfg");
        PowerMockito.mockStatic(ExcelUtil.class);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffData(Mockito.any())).thenReturn(null);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffDataCount(Mockito.any())).thenReturn(0);
        inventoryDiffQueryService.exportInventoryDiffData(dto);

        dto.setCompareMode(false);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffTempData(Mockito.any())).thenReturn(null);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryDiffDataTempCount(Mockito.any())).thenReturn(0);
        inventoryDiffQueryService.exportInventoryDiffData(dto);
        verify(inventoryDiffQueryRepository, times(1)).getInventoryDiffDataTempCount(Mockito.any());
    }

    @Test
    public void synchronizeInventoryDiffData()  {
        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryAlibabaTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryDiffTemp();
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTOList = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDTO = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDTOList.add(zteWarehouseInfoDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseInfoDTOList);
        PowerMockito.when(inventoryDiffQueryRepository.getALiStockInfoList(Mockito.any())).thenReturn(new ArrayList<>());
        List<CustomerInventoryPickUpDTO> inventoryStatics = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO.setMpn("gdg");
        customerInventoryPickUpDTO.setCustomerControlType("1");
        customerInventoryPickUpDTO.setInventoryType("0");
        inventoryStatics.add(customerInventoryPickUpDTO);
        PowerMockito.when(zteAlibabaStockInfoUploadService.fetchRepairInventory(Mockito.anyString())).thenReturn(inventoryStatics);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).addInventoryTempData(Mockito.anyList());
        inventoryDiffQueryService.synchronizeInventoryDiffData("gg");
        verify(inventoryDiffQueryRepository, times(1)).addInventoryTempData(Mockito.anyList());
    }
    @Test
    public void synchronizeInventoryDiffData1()  {
        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryAlibabaTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryDiffTemp();
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTOList = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDTO = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDTOList.add(zteWarehouseInfoDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseInfoDTOList);
        PowerMockito.when(inventoryDiffQueryRepository.getALiStockInfoList(Mockito.any())).thenReturn(new ArrayList<>());
        List<CustomerInventoryPickUpDTO> inventoryStatics = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO.setMpn("gdg");
        customerInventoryPickUpDTO.setCustomerControlType("2");
        customerInventoryPickUpDTO.setInventoryType("0");
        inventoryStatics.add(customerInventoryPickUpDTO);
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO1 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO1.setMpn("gdg");
        customerInventoryPickUpDTO1.setCustomerControlType("2");
        customerInventoryPickUpDTO1.setInventoryType("1");
        inventoryStatics.add(customerInventoryPickUpDTO1);
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO2 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO2.setMpn("gdg");
        customerInventoryPickUpDTO2.setCustomerControlType("1");
        customerInventoryPickUpDTO2.setInventoryType("0");
        inventoryStatics.add(customerInventoryPickUpDTO2);
        PowerMockito.when(zteAlibabaStockInfoUploadService.fetchRepairInventory(Mockito.anyString())).thenReturn(inventoryStatics);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).addInventoryTempData(Mockito.anyList());
        inventoryDiffQueryService.synchronizeInventoryDiffData("gg");
        verify(inventoryDiffQueryRepository, times(1)).addInventoryTempData(Mockito.anyList());
    }
    @Test
    public void fetchInforInventory() throws Exception {
        Method addMethod = InventoryDiffQueryServiceImpl.class.getDeclaredMethod("fetchInforInventory", List.class, String.class);
        addMethod.setAccessible(true);
        List<CustomerInventoryPickUpDTO> result = (List) addMethod.invoke(inventoryDiffQueryService, new ArrayList<>(), "en");
        Assert.assertEquals(0,result.size());
    }
    @Test
    public void synchronizeInventoryDiffData_save()  {
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryAlibabaTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryTemp();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).deleteInventoryDiffTemp();
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTOList = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDTO = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDTOList.add(zteWarehouseInfoDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseInfoDTOList);
        List<CustomerInventoryPickUpDTO> inventoryStatics = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO.setMpn("gdg");
        inventoryStatics.add(customerInventoryPickUpDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getALiStockInfoList(Mockito.any())).thenReturn(inventoryStatics);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).addInventoryTempData(Mockito.anyList());
        PowerMockito.doNothing().when(zteAlibabaService).executeQueryInventoryJob(Mockito.any());

        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);

        inventoryDiffQueryService.synchronizeInventoryDiffData("gg");
        verify(inventoryDiffQueryRepository, times(1)).addInventoryTempData(Mockito.anyList());
    }
    @Test
    public void dealAliInventoryTempData_withEmpty()throws Exception  {
        String tt="{\"code\":\"0000\",\"data\":\"{\\\"data\\\":{\\\"code\\\":\\\"200\\\",\\\"result\\\":\\\"{\\\\\\\"msg\\\\\\\":\\\\\\\"成功\\\\\\\",\\\\\\\"trace_id\\\\\\\":null,\\\\\\\"total\\\\\\\":0,\\\\\\\"code\\\\\\\":\\\\\\\"00000000\\\\\\\",\\\\\\\"data\\\\\\\":[{\\\\\\\"available_quantity\\\\\\\":80,\\\\\\\"inventory_quantity\\\\\\\":80,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":32,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250421\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999996,\\\\\\\"inventory_quantity\\\\\\\":999996,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000126\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8357C-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999708,\\\\\\\"inventory_quantity\\\\\\\":999708,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":144,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241230\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":100,\\\\\\\"inventory_quantity\\\\\\\":100,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":201,\\\\\\\"inventory_quantity\\\\\\\":201,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":7,\\\\\\\"inventory_quantity\\\\\\\":7,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":0,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250523\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbomMDSMK1000195799_1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":2,\\\\\\\"inventory_quantity\\\\\\\":2,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"F86M4T1C32.C4.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_F86M4T1C32_250423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_002\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":9998,\\\\\\\"inventory_quantity\\\\\\\":9998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":17,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":3},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":2,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250521\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999976,\\\\\\\"inventory_quantity\\\\\\\":999976,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12003292\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Hynix\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"HMAA8GR7AJR4N-XN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":997664,\\\\\\\"inventory_quantity\\\\\\\":997664,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6103-000052\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Samsung\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"M393A4K40DB3-CWE-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":10,\\\\\\\"inventory_quantity\\\\\\\":10,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999998,\\\\\\\"inventory_quantity\\\\\\\":999998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"新部件\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"J45.34.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":null,\\\\\\\"mpn\\\\\\\":\\\\\\\"R1290-F9012-17G05D\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999866,\\\\\\\"inventory_quantity\\\\\\\":999866,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":1000000,\\\\\\\"inventory_quantity\\\\\\\":1000000,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0}],\\\\\\\"success\\\\\\\":true,\\\\\\\"pending\\\\\\\":false}\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59l5ox8bkb\\\"}\",\"keywords\":\"2\",\"messageId\":\"66f1cde0-d8c4-46d5-b8c8-32d3ba402862\",\"messageType\":\"ZTEiMES-Alibaba-QueryInventories\",\"requestId\":\"eg59l5ox8bkb\",\"source\":\"Alibaba\",\"success\":true}";
        ObjectMapper objectMapper = new ObjectMapper();
        B2BCallBackDTO dto = objectMapper.readValue(tt, B2BCallBackDTO.class);
        String result = "{\"data\":{\"code\":\"200\",\"result\":{\"msg\":\"成功\",\"trace_id\":null,\"total\":0,\"code\":\"00000000\",\"data\":[{\"available_quantity\":80,\"inventory_quantity\":80,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"CPU\",\"batch\":\"20240731\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":32,\"category_name\":\"CPU\",\"batch\":\"20250421\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":999996,\"inventory_quantity\":999996,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"CPU\",\"batch\":\"20250423\",\"item_name\":\"6102-000126\",\"brand_name\":\"Intel\",\"mpn\":\"8357C-WC1\",\"freeze_quantity\":0},{\"available_quantity\":999708,\"inventory_quantity\":999708,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"CPU\",\"batch\":\"20250422\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":144,\"category_name\":\"CPU\",\"batch\":\"20241230\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":100,\"inventory_quantity\":100,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":201,\"inventory_quantity\":201,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":7,\"inventory_quantity\":7,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":0,\"category_name\":\"Server\",\"batch\":\"20250523\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbomMDSMK1000195799_1\",\"freeze_quantity\":0},{\"available_quantity\":2,\"inventory_quantity\":2,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"F86M4T1C32.C4.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_F86M4T1C32_250423_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_002\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":9998,\"inventory_quantity\":9998,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":17,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":3},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":2,\"category_name\":\"Server\",\"batch\":\"20250521\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":0},{\"available_quantity\":999976,\"inventory_quantity\":999976,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"MEM\",\"batch\":\"20250423\",\"item_name\":\"12003292\",\"brand_name\":\"Hynix\",\"mpn\":\"HMAA8GR7AJR4N-XN\",\"freeze_quantity\":0},{\"available_quantity\":997664,\"inventory_quantity\":997664,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"MEM\",\"batch\":\"20250422\",\"item_name\":\"6103-000052\",\"brand_name\":\"Samsung\",\"mpn\":\"M393A4K40DB3-CWE-WC1\",\"freeze_quantity\":0},{\"available_quantity\":10,\"inventory_quantity\":10,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"MEM\",\"batch\":\"20240731\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"MEM\",\"batch\":\"20241008\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":999998,\"inventory_quantity\":999998,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"新部件\",\"batch\":\"20250423\",\"item_name\":\"J45.34.C0V1P0U1\",\"brand_name\":null,\"mpn\":\"R1290-F9012-17G05D\",\"freeze_quantity\":0},{\"available_quantity\":999866,\"inventory_quantity\":999866,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"SSD\",\"batch\":\"20250422\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0},{\"available_quantity\":1000000,\"inventory_quantity\":1000000,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"SSD\",\"batch\":\"20250423\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0}],\"success\":true,\"pending\":false},\"success\":true},\"request_id\":\"eg59l5ox8bkb\"}";
        B2BCallBackDataDTO b2BCallBackDataDTO = JacksonJsonConverUtil.jsonToBean(result, new TypeReference<B2BCallBackDataDTO>(){});
        AliCreateBillResListDataDTO aliCreateBillResListDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2BCallBackDataDTO.getData().getResult()), new TypeReference<AliCreateBillResListDataDTO>() {});
        List<B2BCallBackInventoryResultDTO> data = aliCreateBillResListDataDTO.getData();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertInventoryDiffTempData(Mockito.anyList());
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertAliInventoryTempData(Mockito.anyList());
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryTempData()).thenReturn(Mockito.anyList());
        inventoryDiffQueryService.dealAliInventoryTempData(dto,data);
        verify(inventoryDiffQueryRepository, times(1)).insertInventoryDiffTempData(Mockito.anyList());
    }

    @Test
    public void testDealAliInventoryTempData_allBranches() {
        // 构造输入参数
        B2BCallBackDTO dto = new B2BCallBackDTO();
        List<B2BCallBackInventoryResultDTO> data = new ArrayList<>();
        B2BCallBackInventoryResultDTO item1 = new B2BCallBackInventoryResultDTO();
        item1.setMpn("mpn1");
        item1.setInventoryCategoryName("cat1");
        item1.setAvailableQuantity(BigDecimal.ONE);
        item1.setFreezeQuantity(BigDecimal.ZERO);
        item1.setInventoryQuantity(BigDecimal.TEN);
        item1.setCategoryName("catName1");
        data.add(item1);

        // 1. lookupValues 为空
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(null);

        // 2. insertAliInventoryTempData分组
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(anyList(), anyInt())).thenAnswer(invocation -> {
            List list = invocation.getArgument(0);
            List<List> result = new ArrayList<>();
            result.add(list);
            return result;
        });

        doNothing().when(inventoryDiffQueryRepository).insertAliInventoryTempData(anyList());

        // 3. inventoryTempDataList 为空
        when(inventoryDiffQueryRepository.getInventoryTempData()).thenReturn(new ArrayList<>());

        // 4. insertInventoryDiffTempData分组
        doNothing().when(inventoryDiffQueryRepository).insertInventoryDiffTempData(anyList());

        // 5. setInventoryDiffTempData分支
        // 由于setInventoryDiffTempData是public，可以直接mock或spy
        InventoryDiffQueryServiceImpl spyService = PowerMockito.spy(inventoryDiffQueryService);
        PowerMockito.doReturn(new ArrayList<>()).when(spyService).setInventoryDiffTempData(anyMap(), anyMap());

        // 执行
        spyService.dealAliInventoryTempData(dto, data);

        // 6. lookupValues 不为空，data过滤后为空
        List<SysLookupValuesDTO> lookupValues = new ArrayList<>();
        SysLookupValuesDTO lookup = new SysLookupValuesDTO();
        lookup.setLookupMeaning("cat1");
        lookupValues.add(lookup);
        when(inventoryholdRecordRepository.getLookupValues(any())).thenReturn(lookupValues);

        // 7. inventoryTempDataList 不为空
        List<CustomerInventoryPickUpDTO> inventoryTempDataList = new ArrayList<>();
        CustomerInventoryPickUpDTO pickUpDTO = new CustomerInventoryPickUpDTO();
        pickUpDTO.setMpn("mpn1");
        inventoryTempDataList.add(pickUpDTO);
        when(inventoryDiffQueryRepository.getInventoryTempData()).thenReturn(inventoryTempDataList);

        // 8. setInventoryDiffTempData返回非空
        List<InventoryDiffTempDTO> diffList = new ArrayList<>();
        InventoryDiffTempDTO diff = new InventoryDiffTempDTO();
        diff.setMpn("mpn1");
        diffList.add(diff);
        PowerMockito.doReturn(diffList).when(spyService).setInventoryDiffTempData(anyMap(), anyMap());

        // 再执行一次
        spyService.dealAliInventoryTempData(dto, data);

        // 验证调用
        verify(inventoryDiffQueryRepository, atLeastOnce()).insertAliInventoryTempData(anyList());
        verify(inventoryDiffQueryRepository, atLeastOnce()).insertInventoryDiffTempData(anyList());
    }
    @Test
    public void dealAliInventoryTempData()throws Exception  {
        String tt="{\"code\":\"0000\",\"data\":\"{\\\"data\\\":{\\\"code\\\":\\\"200\\\",\\\"result\\\":\\\"{\\\\\\\"msg\\\\\\\":\\\\\\\"成功\\\\\\\",\\\\\\\"trace_id\\\\\\\":null,\\\\\\\"total\\\\\\\":0,\\\\\\\"code\\\\\\\":\\\\\\\"00000000\\\\\\\",\\\\\\\"data\\\\\\\":[{\\\\\\\"available_quantity\\\\\\\":80,\\\\\\\"inventory_quantity\\\\\\\":80,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":32,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250421\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999996,\\\\\\\"inventory_quantity\\\\\\\":999996,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000126\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8357C-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999708,\\\\\\\"inventory_quantity\\\\\\\":999708,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":144,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241230\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":100,\\\\\\\"inventory_quantity\\\\\\\":100,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":201,\\\\\\\"inventory_quantity\\\\\\\":201,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":7,\\\\\\\"inventory_quantity\\\\\\\":7,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":0,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250523\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbomMDSMK1000195799_1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":2,\\\\\\\"inventory_quantity\\\\\\\":2,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"F86M4T1C32.C4.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_F86M4T1C32_250423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_002\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":9998,\\\\\\\"inventory_quantity\\\\\\\":9998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":17,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":3},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":2,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250521\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999976,\\\\\\\"inventory_quantity\\\\\\\":999976,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12003292\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Hynix\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"HMAA8GR7AJR4N-XN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":997664,\\\\\\\"inventory_quantity\\\\\\\":997664,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6103-000052\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Samsung\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"M393A4K40DB3-CWE-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":10,\\\\\\\"inventory_quantity\\\\\\\":10,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999998,\\\\\\\"inventory_quantity\\\\\\\":999998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"新部件\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"J45.34.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":null,\\\\\\\"mpn\\\\\\\":\\\\\\\"R1290-F9012-17G05D\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999866,\\\\\\\"inventory_quantity\\\\\\\":999866,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":1000000,\\\\\\\"inventory_quantity\\\\\\\":1000000,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0}],\\\\\\\"success\\\\\\\":true,\\\\\\\"pending\\\\\\\":false}\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59l5ox8bkb\\\"}\",\"keywords\":\"2\",\"messageId\":\"66f1cde0-d8c4-46d5-b8c8-32d3ba402862\",\"messageType\":\"ZTEiMES-Alibaba-QueryInventories\",\"requestId\":\"eg59l5ox8bkb\",\"source\":\"Alibaba\",\"success\":true}";
        ObjectMapper objectMapper = new ObjectMapper();
        B2BCallBackDTO dto = objectMapper.readValue(tt, B2BCallBackDTO.class);
        String result = "{\"data\":{\"code\":\"200\",\"result\":{\"msg\":\"成功\",\"trace_id\":null,\"total\":0,\"code\":\"00000000\",\"data\":[{\"available_quantity\":80,\"inventory_quantity\":80,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"CPU\",\"batch\":\"20240731\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":32,\"category_name\":\"CPU\",\"batch\":\"20250421\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":999996,\"inventory_quantity\":999996,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"CPU\",\"batch\":\"20250423\",\"item_name\":\"6102-000126\",\"brand_name\":\"Intel\",\"mpn\":\"8357C-WC1\",\"freeze_quantity\":0},{\"available_quantity\":999708,\"inventory_quantity\":999708,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"CPU\",\"batch\":\"20250422\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":144,\"category_name\":\"CPU\",\"batch\":\"20241230\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":100,\"inventory_quantity\":100,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":201,\"inventory_quantity\":201,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":7,\"inventory_quantity\":7,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":0,\"category_name\":\"Server\",\"batch\":\"20250523\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbomMDSMK1000195799_1\",\"freeze_quantity\":0},{\"available_quantity\":2,\"inventory_quantity\":2,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"F86M4T1C32.C4.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_F86M4T1C32_250423_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_002\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":9998,\"inventory_quantity\":9998,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":17,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":3},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":2,\"category_name\":\"Server\",\"batch\":\"20250521\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":0},{\"available_quantity\":999976,\"inventory_quantity\":999976,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"MEM\",\"batch\":\"20250423\",\"item_name\":\"12003292\",\"brand_name\":\"Hynix\",\"mpn\":\"HMAA8GR7AJR4N-XN\",\"freeze_quantity\":0},{\"available_quantity\":997664,\"inventory_quantity\":997664,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"MEM\",\"batch\":\"20250422\",\"item_name\":\"6103-000052\",\"brand_name\":\"Samsung\",\"mpn\":\"M393A4K40DB3-CWE-WC1\",\"freeze_quantity\":0},{\"available_quantity\":10,\"inventory_quantity\":10,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"MEM\",\"batch\":\"20240731\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"MEM\",\"batch\":\"20241008\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":999998,\"inventory_quantity\":999998,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"新部件\",\"batch\":\"20250423\",\"item_name\":\"J45.34.C0V1P0U1\",\"brand_name\":null,\"mpn\":\"R1290-F9012-17G05D\",\"freeze_quantity\":0},{\"available_quantity\":999866,\"inventory_quantity\":999866,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"SSD\",\"batch\":\"20250422\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0},{\"available_quantity\":1000000,\"inventory_quantity\":1000000,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"SSD\",\"batch\":\"20250423\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0}],\"success\":true,\"pending\":false},\"success\":true},\"request_id\":\"eg59l5ox8bkb\"}";
        B2BCallBackDataDTO b2BCallBackDataDTO = JacksonJsonConverUtil.jsonToBean(result, new TypeReference<B2BCallBackDataDTO>(){});
        AliCreateBillResListDataDTO aliCreateBillResListDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2BCallBackDataDTO.getData().getResult()), new TypeReference<AliCreateBillResListDataDTO>() {});
        List<B2BCallBackInventoryResultDTO> data = aliCreateBillResListDataDTO.getData();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertAliInventoryTempData(Mockito.anyList());
        List<CustomerInventoryPickUpDTO> inventoryTempDataList = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO.setMpn("CPU01-MPN");
        customerInventoryPickUpDTO.setSourceSystem("1");
        customerInventoryPickUpDTO.setVendorInventoryQuantity(NumConstant.BIG_0);
        inventoryTempDataList.add(customerInventoryPickUpDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryTempData()).thenReturn(inventoryTempDataList);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertInventoryDiffTempData(Mockito.anyList());
        inventoryDiffQueryService.dealAliInventoryTempData(dto,data);
        verify(inventoryDiffQueryRepository, times(1)).insertInventoryDiffTempData(Mockito.anyList());
    }

    @Test
    public void unBindSnRelationAlarm() throws Exception {
        SysLookupValuesDTO sysLookupValuesDto = new SysLookupValuesDTO();
        sysLookupValuesDto.setLookupMeaning("1234546");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(any())).thenReturn(sysLookupValuesDto);

        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn("OK");

        PowerMockito.when(inventoryDiffQueryRepository.getUnBindSnRelationData()).thenReturn(null);
        inventoryDiffQueryService.unBindSnRelationAlarm();

        //分支：入库
        List<UnBindPkgSnJobVo> data = new ArrayList<>();
        UnBindPkgSnJobVo vo = new UnBindPkgSnJobVo();
        vo.setExternalkey("test1");
        vo.setSourcekey("test1");
        vo.setWhseid("teset1");
        vo.setTranType(STR_STORE);
        data.add(vo);
        PowerMockito.when(inventoryDiffQueryRepository.getUnBindSnRelationData()).thenReturn(data);
        inventoryDiffQueryService.unBindSnRelationAlarm();

        //分支：出库
        data.clear();
        vo.setExternalkey("test1");
        vo.setSourcekey("test1");
        vo.setWhseid("teset1");
        vo.setTranType(STR_OUT_STORE);
        data.add(vo);
        PowerMockito.when(inventoryDiffQueryRepository.getUnBindSnRelationData()).thenReturn(data);
        inventoryDiffQueryService.unBindSnRelationAlarm();

        //其它分支场景
        data.clear();
        vo.setExternalkey("test1");
        vo.setSourcekey("test1");
        vo.setWhseid("teset1");
        vo.setTranType("");
        data.add(vo);
        PowerMockito.when(inventoryDiffQueryRepository.getUnBindSnRelationData()).thenReturn(data);
        inventoryDiffQueryService.unBindSnRelationAlarm();

        Assert.assertNotNull(data);
    }


    @Test
    public void originAndMixedBoxMaterialConsistencyWarningTest() throws Exception {
        List<ZteWarehouseInfoDTO> zteWarehouseList = new ArrayList<>();
        PowerMockito.when(zteAlibabaStockInfoUploadRepository.getInforWarehouseList()).thenReturn(zteWarehouseList);
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();

        ZteWarehouseInfoDTO zteWarehouseInfoDto = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDto.setWarehouseId("WMWHSE34");
        zteWarehouseInfoDto.setDbType(4);
        zteWarehouseList.add(zteWarehouseInfoDto);
        PowerMockito.when(zteAlibabaStockInfoUploadRepository.getInforWarehouseList()).thenReturn(zteWarehouseList);
        SysLookupValuesDTO sysLookupValuesDto = new SysLookupValuesDTO();
        sysLookupValuesDto.setLookupMeaning("1234546");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDto);

        List<OriginMixedBoxInventoryDTO> originMixedBoxInventoryDtoList = new ArrayList<>();
        PowerMockito.when(inventoryDiffQueryRepository.getOriginAndMixedBoxInventory(Mockito.any())).thenReturn(originMixedBoxInventoryDtoList);
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();

        OriginMixedBoxInventoryDTO originMixedBoxInventoryDto = new OriginMixedBoxInventoryDTO();
        originMixedBoxInventoryDto.setWhseid("WMWHSE34");
        originMixedBoxInventoryDtoList.add(originMixedBoxInventoryDto);
        PowerMockito.when(inventoryDiffQueryRepository.getOriginAndMixedBoxInventory(Mockito.any())).thenReturn(originMixedBoxInventoryDtoList);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn("OK");
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();

        List<MixedBoxInventoryDTO> mixedBoxInventoryDtoList = new ArrayList<>();
        PowerMockito.when(inventoryDiffQueryRepository.getMixedBoxAndSnInventory(Mockito.any())).thenReturn(mixedBoxInventoryDtoList);
        Whitebox.invokeMethod(inventoryDiffQueryService,"processInventoryConsistency", mixedBoxInventoryDtoList,null,"","");
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();

        MixedBoxInventoryDTO mixedBoxInventoryDto = new MixedBoxInventoryDTO();
        mixedBoxInventoryDto.setWhseid("WMWHSE34");
        mixedBoxInventoryDtoList.add(mixedBoxInventoryDto);
        PowerMockito.when(inventoryDiffQueryRepository.getMixedBoxAndSnInventory(Mockito.any())).thenReturn(mixedBoxInventoryDtoList);
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();
        Assert.assertNotNull(zteWarehouseList);
    }

    @Test
    public void aliInventoryConsistencyWarningTest() throws Exception {
        SysLookupValuesDTO sysLookupValuesDto = new SysLookupValuesDTO();
        sysLookupValuesDto.setLookupMeaning("1234546");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValue(Mockito.any())).thenReturn(sysLookupValuesDto);
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDtoList = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDto = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDto.setWarehouseId("warehouse1");
        zteWarehouseInfoDto.setDbType(4);
        zteWarehouseInfoDtoList.add(zteWarehouseInfoDto);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseInfoDtoList);
        List<CustomerInventoryPickUpDTO> inventoryStatics = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDto = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDto.setInventoryType("0");
        inventoryStatics.add(customerInventoryPickUpDto);
        PowerMockito.when(inventoryDiffQueryRepository.getALiStockInfoList(any())).thenReturn(inventoryStatics);
        List<CustomerInventoryPickUpDTO> repairInventoryList = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDto1 = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDto1.setInventoryType("0");
        customerInventoryPickUpDto1.setCustomerControlType("1");
        customerInventoryPickUpDto1.setMpn("mpn1");
        repairInventoryList.add(customerInventoryPickUpDto1);
        PowerMockito.when(zteAlibabaStockInfoUploadService.fetchRepairInventory(Mockito.any())).thenReturn(repairInventoryList);
        //PowerMockito.stub(PowerMockito.method(InventoryDiffQueryServiceImpl.class, "synchronizeInventoryDiffData", String.class)).toReturn(true);

        List<ZteAliInventoryDTO> zteAliInventoryDtoList = new ArrayList<>();
        ZteAliInventoryDTO zteAliInventoryDto = new ZteAliInventoryDTO();
        zteAliInventoryDto.setMpn("mpn1");

        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        doNothing().when(inventoryDiffQueryRepository).addInventoryTempData(anyList());

        inventoryDiffQueryService.aliInventoryConsistencyWarning();
        Assert.assertNotNull(sysLookupValuesDto);
    }
    @Test
    public void getProductInventoryData() throws Exception {
        String res = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        List<AlibabaOnHandQtyVO> alibabaOnHandQtyVOList = inventoryDiffQueryService.getProductInventoryData("dff");
        Assert.assertEquals(0,alibabaOnHandQtyVOList.size());
    }

    @Test
    public void getProductInventory() throws Exception {
        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":12,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        List<CustomerInventoryPickUpDTO> customerInventoryPickUpDTOList = inventoryDiffQueryService.getProductInventory("dff");

        String res1 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list1\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res1);
        customerInventoryPickUpDTOList = inventoryDiffQueryService.getProductInventory("dff");

        Assert.assertEquals(0,customerInventoryPickUpDTOList.size());
    }
    @Test
    public void dealProductInventoryData() throws Exception {
        String res = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"inventory_type\":1,\"inventory_directive\":\"ZTE202505301808173775357034362\",\"machine_brand\":\"ZTE\",\"factory_code\":\"ZTE101\",\"version_seq_no\":1,\"customer_inventory_list\":[{\"customer_inventory_line_number\":1,\"config_model\":\"G83G4.J2.C0V1P0U1\",\"mpn\":\"ZTE_G83G4.J2.C0V1P0U1_9002\",\"vendor_inventory_quantity\":0,\"delivered_quantity\":0,\"item_type\":1}]}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.AlibabaPushController@onHandQtyPush\",\"code\":\"0000\",\"costTime\":\"776ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Fri May 30 18:08:17 CST 2025\",\"tag\":\"阿里巴巴库存上传\",\"hostAddress\":\"*************\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10158982\"},\"responseRule\":\"msa\"}";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(),any(),any())).thenReturn(res);
        when(inventoryDiffQueryRepository.saveProductInventoryStaticsData(anyList())).thenReturn(4);
        inventoryDiffQueryService.dealProductInventoryData("dff");
        verify(inventoryDiffQueryRepository, times(1)).saveProductInventoryStaticsData(Mockito.anyList());
    }
    @Test
    public void dealAliInventoryTempData_inventoryType1()throws Exception  {
        String tt="{\"code\":\"0000\",\"data\":\"{\\\"data\\\":{\\\"code\\\":\\\"200\\\",\\\"result\\\":\\\"{\\\\\\\"msg\\\\\\\":\\\\\\\"成功\\\\\\\",\\\\\\\"trace_id\\\\\\\":null,\\\\\\\"total\\\\\\\":0,\\\\\\\"code\\\\\\\":\\\\\\\"00000000\\\\\\\",\\\\\\\"data\\\\\\\":[{\\\\\\\"available_quantity\\\\\\\":80,\\\\\\\"inventory_quantity\\\\\\\":80,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":32,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250421\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12000306\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8163\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999996,\\\\\\\"inventory_quantity\\\\\\\":999996,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000126\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8357C-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999708,\\\\\\\"inventory_quantity\\\\\\\":999708,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":144,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241230\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12078218\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"8369B\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":100,\\\\\\\"inventory_quantity\\\\\\\":100,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":201,\\\\\\\"inventory_quantity\\\\\\\":201,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"CPU\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6102-000094\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"H3C\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"CPU01-MPN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":7,\\\\\\\"inventory_quantity\\\\\\\":7,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":0,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250523\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbomMDSMK1000195799_1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":2,\\\\\\\"inventory_quantity\\\\\\\":2,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"F86M4T1C32.C4.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_F86M4T1C32_250423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_002\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":9998,\\\\\\\"inventory_quantity\\\\\\\":9998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P3U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4422_003\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":17,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":3},{\\\\\\\"available_quantity\\\\\\\":40,\\\\\\\"inventory_quantity\\\\\\\":40,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴整机库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":2,\\\\\\\"category_name\\\\\\\":\\\\\\\"Server\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250521\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"G83G4.J2.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"ZTE\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"fixbom_G83G4423_001\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999976,\\\\\\\"inventory_quantity\\\\\\\":999976,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12003292\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Hynix\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"HMAA8GR7AJR4N-XN\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":997664,\\\\\\\"inventory_quantity\\\\\\\":997664,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"6103-000052\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Samsung\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"M393A4K40DB3-CWE-WC1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":10,\\\\\\\"inventory_quantity\\\\\\\":10,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":296,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20240731\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":20,\\\\\\\"inventory_quantity\\\\\\\":20,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴在途库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":227,\\\\\\\"category_name\\\\\\\":\\\\\\\"MEM\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20241008\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12077927\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Micron\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"MTA18ASF2G72HZ-2G6E1\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999998,\\\\\\\"inventory_quantity\\\\\\\":999998,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"新部件\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"J45.34.C0V1P0U1\\\\\\\",\\\\\\\"brand_name\\\\\\\":null,\\\\\\\"mpn\\\\\\\":\\\\\\\"R1290-F9012-17G05D\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":999866,\\\\\\\"inventory_quantity\\\\\\\":999866,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":31,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250422\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0},{\\\\\\\"available_quantity\\\\\\\":1000000,\\\\\\\"inventory_quantity\\\\\\\":1000000,\\\\\\\"inventory_category_name\\\\\\\":\\\\\\\"中兴部件库\\\\\\\",\\\\\\\"hold_duration\\\\\\\":30,\\\\\\\"category_name\\\\\\\":\\\\\\\"SSD\\\\\\\",\\\\\\\"batch\\\\\\\":\\\\\\\"20250423\\\\\\\",\\\\\\\"item_name\\\\\\\":\\\\\\\"12002231\\\\\\\",\\\\\\\"brand_name\\\\\\\":\\\\\\\"Intel\\\\\\\",\\\\\\\"mpn\\\\\\\":\\\\\\\"SSDSCKKB240G8_PRODUCT\\\\\\\",\\\\\\\"freeze_quantity\\\\\\\":0}],\\\\\\\"success\\\\\\\":true,\\\\\\\"pending\\\\\\\":false}\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59l5ox8bkb\\\"}\",\"keywords\":\"2\",\"messageId\":\"66f1cde0-d8c4-46d5-b8c8-32d3ba402862\",\"messageType\":\"ZTEiMES-Alibaba-QueryInventories\",\"requestId\":\"eg59l5ox8bkb\",\"source\":\"Alibaba\",\"success\":true}";
        ObjectMapper objectMapper = new ObjectMapper();
        B2BCallBackDTO dto = objectMapper.readValue(tt, B2BCallBackDTO.class);
        String result = "{\"data\":{\"code\":\"200\",\"result\":{\"msg\":\"成功\",\"trace_id\":null,\"total\":0,\"code\":\"00000000\",\"data\":[{\"available_quantity\":80,\"inventory_quantity\":80,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"CPU\",\"batch\":\"20240731\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":32,\"category_name\":\"CPU\",\"batch\":\"20250421\",\"item_name\":\"12000306\",\"brand_name\":\"Intel\",\"mpn\":\"8163\",\"freeze_quantity\":0},{\"available_quantity\":999996,\"inventory_quantity\":999996,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"CPU\",\"batch\":\"20250423\",\"item_name\":\"6102-000126\",\"brand_name\":\"Intel\",\"mpn\":\"8357C-WC1\",\"freeze_quantity\":0},{\"available_quantity\":999708,\"inventory_quantity\":999708,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"CPU\",\"batch\":\"20250422\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":144,\"category_name\":\"CPU\",\"batch\":\"20241230\",\"item_name\":\"12078218\",\"brand_name\":\"Intel\",\"mpn\":\"8369B\",\"freeze_quantity\":0},{\"available_quantity\":100,\"inventory_quantity\":100,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":201,\"inventory_quantity\":201,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"CPU\",\"batch\":\"20241008\",\"item_name\":\"6102-000094\",\"brand_name\":\"H3C\",\"mpn\":\"CPU01-MPN\",\"freeze_quantity\":0},{\"available_quantity\":7,\"inventory_quantity\":7,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":0,\"category_name\":\"Server\",\"batch\":\"20250523\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbomMDSMK1000195799_1\",\"freeze_quantity\":0},{\"available_quantity\":2,\"inventory_quantity\":2,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"F86M4T1C32.C4.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_F86M4T1C32_250423_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_001\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_002\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":9998,\"inventory_quantity\":9998,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":31,\"category_name\":\"Server\",\"batch\":\"20250422\",\"item_name\":\"G83G4.J2.C0V1P3U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4422_003\",\"freeze_quantity\":0},{\"available_quantity\":17,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":30,\"category_name\":\"Server\",\"batch\":\"20250423\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":3},{\"available_quantity\":40,\"inventory_quantity\":40,\"inventory_category_name\":\"中兴整机库\",\"hold_duration\":2,\"category_name\":\"Server\",\"batch\":\"20250521\",\"item_name\":\"G83G4.J2.C0V1P0U1\",\"brand_name\":\"ZTE\",\"mpn\":\"fixbom_G83G4423_001\",\"freeze_quantity\":0},{\"available_quantity\":999976,\"inventory_quantity\":999976,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"MEM\",\"batch\":\"20250423\",\"item_name\":\"12003292\",\"brand_name\":\"Hynix\",\"mpn\":\"HMAA8GR7AJR4N-XN\",\"freeze_quantity\":0},{\"available_quantity\":997664,\"inventory_quantity\":997664,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"MEM\",\"batch\":\"20250422\",\"item_name\":\"6103-000052\",\"brand_name\":\"Samsung\",\"mpn\":\"M393A4K40DB3-CWE-WC1\",\"freeze_quantity\":0},{\"available_quantity\":10,\"inventory_quantity\":10,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":296,\"category_name\":\"MEM\",\"batch\":\"20240731\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":20,\"inventory_quantity\":20,\"inventory_category_name\":\"中兴在途库\",\"hold_duration\":227,\"category_name\":\"MEM\",\"batch\":\"20241008\",\"item_name\":\"12077927\",\"brand_name\":\"Micron\",\"mpn\":\"MTA18ASF2G72HZ-2G6E1\",\"freeze_quantity\":0},{\"available_quantity\":999998,\"inventory_quantity\":999998,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"新部件\",\"batch\":\"20250423\",\"item_name\":\"J45.34.C0V1P0U1\",\"brand_name\":null,\"mpn\":\"R1290-F9012-17G05D\",\"freeze_quantity\":0},{\"available_quantity\":999866,\"inventory_quantity\":999866,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":31,\"category_name\":\"SSD\",\"batch\":\"20250422\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0},{\"available_quantity\":1000000,\"inventory_quantity\":1000000,\"inventory_category_name\":\"中兴部件库\",\"hold_duration\":30,\"category_name\":\"SSD\",\"batch\":\"20250423\",\"item_name\":\"12002231\",\"brand_name\":\"Intel\",\"mpn\":\"SSDSCKKB240G8_PRODUCT\",\"freeze_quantity\":0}],\"success\":true,\"pending\":false},\"success\":true},\"request_id\":\"eg59l5ox8bkb\"}";
        B2BCallBackDataDTO b2BCallBackDataDTO = JacksonJsonConverUtil.jsonToBean(result, new TypeReference<B2BCallBackDataDTO>(){});
        AliCreateBillResListDataDTO aliCreateBillResListDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2BCallBackDataDTO.getData().getResult()), new TypeReference<AliCreateBillResListDataDTO>() {});
        List<B2BCallBackInventoryResultDTO> data = aliCreateBillResListDataDTO.getData();
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertAliInventoryTempData(Mockito.anyList());
        List<CustomerInventoryPickUpDTO> inventoryTempDataList = new ArrayList<>();
        CustomerInventoryPickUpDTO customerInventoryPickUpDTO = new CustomerInventoryPickUpDTO();
        customerInventoryPickUpDTO.setMpn("CPU01-MPN");
        customerInventoryPickUpDTO.setSourceSystem("1");
        customerInventoryPickUpDTO.setVendorInventoryQuantity(NumConstant.BIG_0);
        inventoryTempDataList.add(customerInventoryPickUpDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInventoryTempData()).thenReturn(inventoryTempDataList);
        PowerMockito.doNothing().when(inventoryDiffQueryRepository).insertInventoryDiffTempData(Mockito.anyList());
        List<String> categoryNameList = new ArrayList<>();
        categoryNameList.add("CPU");
        PowerMockito.when(zteAlibabaService.getCategoryName()).thenReturn(categoryNameList);
        inventoryDiffQueryService.dealAliInventoryTempData(dto,data);
        verify(inventoryDiffQueryRepository, times(1)).insertInventoryDiffTempData(Mockito.anyList());
    }
    /* Started by AICoder, pid:279049e227pf0a1144bc0907a06122442e98c445 */
    @Test
    public void saveProductInventoryToStatics() throws Exception {
        inventoryDiffQueryService.saveProductInventoryToStatics(new ArrayList<>());
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:279049e227pf0a1144bc0907a06122442e98c445 */
}

/* Ended by AICoder, pid:f5e6d709efe48b9140f1087f022cc39959910321 */
