package com.zte.application.infor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.IqcTestRequisitionServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.IqcTestRequisitionBillDTO;
import com.zte.domain.model.infor.IqcTestRequisitionDetailDTO;
import com.zte.domain.model.infor.IqcTestRequisitionRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.IqcTestRequisitionListVO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.zte.common.utils.Constant.CLOUD_DISK;
import static com.zte.common.utils.NumConstant.INT_5;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, CommonUtils.class, RedisSerialNoUtil.class, RemoteServiceDataUtil.class, HttpClientUtil.class,
        WebServiceClient.class,ExcelUtil.class,CloudDiskHelper.class,SpringContextUtil.class, Tools.class, JacksonJsonConverUtil.class})
public class IqcTestRequisitionServiceImpllTest {

    @InjectMocks
    private IqcTestRequisitionServiceImpl iqcTestRequisitionService;
    @Mock
    private IqcTestRequisitionRepository iqcTestRequisitionRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private EmailUtil emailUtil;

    @Mock
    private StepTransferRepository stransferRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    private ThreadPoolExecutor thplExecutor = new ThreadPoolExecutor(10, 10,
            10, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, CommonUtils.class, RedisSerialNoUtil.class, RemoteServiceDataUtil.class,
                HttpClientUtil.class, WebServiceClient.class,CloudDiskHelper.class,SpringContextUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.field(IqcTestRequisitionServiceImpl.class, "thplExecutor")
                .set(iqcTestRequisitionService, thplExecutor);
    }

    @Test
    /* Started by AICoder, pid:d975000ef46b43b093ebae674c8d3a70 */
    public void getInventoryholdRecordList() throws Exception {
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", null);
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build().setDeliveryNo("4324");
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        IqcTestRequisitionHeadDTO headDTO2 = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");

        List<IqcTestRequisitionPkgDTO> pkgDTOList2 = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO2 = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgPickQty(BigDecimal.valueOf(1));
        pkgDTOList2.add(pkgDTO2);
        headDTO2.setPackList(pkgDTOList2);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO2);
        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        IqcTestRequisitionPkgDTO pkgDTO1 = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO1);
        headDTO.setPackList(pkgDTOList);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(Constant.QCL, INT_5)).thenReturn("IQC2024100900001");
        PowerMockito.when(iqcTestRequisitionRepository.getItemReelControl(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        List<IqcTestRequisitionPkgDTO> pkgDTOList1 = new ArrayList<>();
        pkgDTOList1.add(pkgDTO1);
        PowerMockito.when(iqcTestRequisitionRepository.getItemReelControl(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList1);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcProductClass(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList);
        PowerMockito.when(stransferRepository.getIqcPlanningGroups(Mockito.any())).thenReturn(pkgDTOList);
        ServiceData<?> response = iqcTestRequisitionService.insertIqcTestRequisitionInfo(headDTO);
        Assert.assertNotNull(response.getCode());
    }
    /* Ended by AICoder, pid:d975000ef46b43b093ebae674c8d3a70 */

    /* Started by AICoder, pid:6748bcbb549b4cfdb7e2f32b4997d8f3 */
    @Test
    public void checkIqcHeadDtoList() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setStockNo("WMWHSE1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcHeadDtoList", headDTO);

        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        String result = iqcTestRequisitionService.checkIqcHeadDtoList(headDTO);
        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:6748bcbb549b4cfdb7e2f32b4997d8f3 */

    /* Started by AICoder, pid:dfd81412cfa64ec7a9cb92350ab063db */
    @Test
    public void checkIqcPkgDtoList() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");

        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);

        PowerMockito.when(iqcTestRequisitionRepository.isRepeatByBarcode(Mockito.any(), Mockito.any())).thenReturn(2);

        String result = iqcTestRequisitionService.checkIqcPkgDtoList(headDTO);
        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:dfd81412cfa64ec7a9cb92350ab063db */

    @Test
    public void checkIqcDetailDtoList() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(0))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        List<IqcTestRequisitionReelDTO> reelDtoList = new ArrayList<>();
        IqcTestRequisitionReelDTO reelDto = IqcTestRequisitionReelDTO.builder().build().setPkgId("1").setReelId("1").setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1)).setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcDetailDtoList", headDTO);
        String result = iqcTestRequisitionService.checkIqcDetailDtoList(headDTO);
        Assert.assertNotNull(result);
    }

    /* Started by AICoder, pid:d7895321469e41fdb153c1453d0372ac */
    @Test
    public void checkIqcReelData() throws Exception {
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");

        List<IqcTestRequisitionReelDTO> reelDtoList = new ArrayList<>();
        IqcTestRequisitionReelDTO reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(0));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "checkIqcReelData", pkgDTO, new StringBuilder());

        String result = iqcTestRequisitionService.checkIqcReelData(pkgDTO, new StringBuilder());
        Assert.assertNotNull(result);
    }

    /* Ended by AICoder, pid:d7895321469e41fdb153c1453d0372ac */
    @Test
    public void insertIqcTestRequisiton() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        List<IqcTestRequisitionReelDTO> reelDtoList = new ArrayList<>();
        IqcTestRequisitionReelDTO reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("N")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("N")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(1)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);
        Assert.assertNotNull(headDTO);
    }

    @Test
    /* Started by AICoder, pid:6740c8ec045b4965a747557dba5e67de */
    public void getIqcTestStatusToIscp() throws Exception {
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .deliveryNo("1")
                .billNo("1")
                .itemNo("1")
                .itemBarcode("2")
                .beginDate("2021-01-01")
                .endDate("2024-01-01")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto);

        IqcTestRequisitionQueryDTO dto0 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto0);

        dto0 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .beginDate("2021-01-01")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto0);

        dto0 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .endDate("2021-01-01")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto0);

        dto0 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .beginDate("2021-01-01 00:00:00")
                .endDate("2022-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto0);

        dto0 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto0);

        IqcTestRequisitionQueryDTO dto2 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .billNo("1")
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto2);

        IqcTestRequisitionQueryDTO dto3 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .billNo("1")
                .deliveryNo("1")
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto3);

        IqcTestRequisitionQueryDTO dto4 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .deliveryNo("1")
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto4);

        IqcTestRequisitionQueryDTO dto5 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .itemNo("1")
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto5);

        IqcTestRequisitionQueryDTO dto6 = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .itemBarcode("1")
                .beginDate("2021-01-01 00:00:00")
                .endDate("2024-01-01 00:00:00")
                .build();
        iqcTestRequisitionService.getIqcTestStatusToIscp(dto6);

        IqcTestRequisitionListVO list = new IqcTestRequisitionListVO();
        List<IqcTestRequisitionQueryDTO> listDto = new ArrayList<>();
        listDto.add(dto);
        list.setIqcTestRequisitionDTOList(listDto);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestStatusToIscListVo(Mockito.any())).thenReturn(listDto);
        IqcTestRequisitionListVO vo = iqcTestRequisitionService.getIqcTestStatusToIscp(dto);
        Assert.assertNotNull(vo);
    }
    /* Ended by AICoder, pid:6740c8ec045b4965a747557dba5e67de */

    /* Started by AICoder, pid:687d9d09d9d745f4a7b61c674ff0e9d1 */
    @Test
    public void getIbarcodeHeaderMap() throws Exception {
        List<SysLookupValuesDTO> sysLookupValuesDTOList1 = new ArrayList<>();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();

        SysLookupValuesDTO sysDto = SysLookupValuesDTO.builder()
                .lookupMeaning("GELI")
                .attribute1("1")
                .build();

        sysLookupValuesDTOList.add(sysDto);

        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList1);
        iqcTestRequisitionService.getIbarcodeHeaderMap();

        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Map<String, String> result = iqcTestRequisitionService.getIbarcodeHeaderMap();

        Assert.assertNotNull(result);
    }
    /* Ended by AICoder, pid:687d9d09d9d745f4a7b61c674ff0e9d1 */

    /* Started by AICoder, pid:459b19dfaee34df3bb97b352588cf6db */
    @Test
    public void splitIqcDetailsJob() throws Exception {
        List<IqcTestRequisitionDetailDTO> detailDTOS = new ArrayList<>();
        List<IqcTestRequisitionDetailDTO> detailDTOS1 = new ArrayList<>();
        List<IqcTestRequisitionDetailDTO> detailDTOS2 = new ArrayList<>();
        List<IqcTestRequisitionDetailDTO> detailDTOS3 = new ArrayList<>();

        IqcTestRequisitionDetailDTO dto1 = IqcTestRequisitionDetailDTO.builder()
                .reelId("ZTE001")
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        IqcTestRequisitionDetailDTO dto3 = IqcTestRequisitionDetailDTO.builder()
                .pkgId("ZTEPKG001")
                .pkgGoodQty(BigDecimal.valueOf(10))
                .pkgPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        IqcTestRequisitionDetailDTO dto5 = IqcTestRequisitionDetailDTO.builder()
                .reelId("ZTE001")
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        IqcTestRequisitionDetailDTO dto2 = IqcTestRequisitionDetailDTO.builder()
                .reelId("ZTE001")
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(15))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        IqcTestRequisitionDetailDTO dto4 = IqcTestRequisitionDetailDTO.builder()
                .pkgId("ZTEPKG001")
                .pkgGoodQty(BigDecimal.valueOf(10))
                .pkgPickQty(BigDecimal.valueOf(15))
                .inforSplitStatus(0)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();
        IqcTestRequisitionDetailDTO dto6 = IqcTestRequisitionDetailDTO.builder()
                .reelId("ZTE001")
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .newReelid("ZTE457")
                .build();

        detailDTOS.add(dto1);
        detailDTOS.add(dto3);
        detailDTOS.add(dto5);
        detailDTOS3.add(dto6);

        detailDTOS2.add(dto2);
        detailDTOS2.add(dto4);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS1);
        iqcTestRequisitionService.splitIqcDetailsJob();

        ServiceData<?> result = new ServiceData<>();
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(result);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        List<JSONObject> list1 = new ArrayList<>();
        JSONObject jsObject1 = new JSONObject();
        jsObject1.put("barcode1", "");
        list1.add(jsObject1);

        ServiceData retData2 = new ServiceData();
        RetCode retCode2 = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData2.setCode(retCode2);
        retData2.setBo(list1);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData2);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        /* Started by AICoder, pid:0b5eeeb105e246bd82106680866017ce */
        // 使用StringBuilder构建一个包含3000个"a"的字符串
        StringBuilder dynamicStringBuilder = new StringBuilder();
        for (int i = 0; i < 3000; i++) {
            dynamicStringBuilder.append("a");
        }
        // 将StringBuilder转换为String
        String dynamicString = dynamicStringBuilder.toString();
        /* Ended by AICoder, pid:0b5eeeb105e246bd82106680866017ce */

        ServiceData retData11 = new ServiceData();
        RetCode retCode11 = new RetCode(RetCode.AUTHFAILED_CODE, dynamicString);
        retData11.setCode(retCode11);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData11);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        ServiceData retData1 = new ServiceData();
        RetCode retCode1 = new RetCode(RetCode.AUTHFAILED_CODE, RetCode.BUSINESSERROR_MSG);
        retData1.setCode(retCode1);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData1);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsObject = new JSONObject();
        jsObject.put("barcode", "111");
        list.add(jsObject);

        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData);

        String responseStr = "{\"code\":{\"code\":\"0400\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"},\"bo\":null,\"other\":null,\"requestId\":\"0557816d6e6f4604\"}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        responseStr = "{\"code\":\"200\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseStr);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        iqcTestRequisitionService.splitIqcDetailsJob();

        List<JSONObject> list3 = new ArrayList<>();
        JSONObject jsObject3 = new JSONObject();
        jsObject3.put("barcode1", "");
        list3.add(jsObject1);

        ServiceData retData3 = new ServiceData();
        RetCode retCode3 = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData3.setCode(retCode3);
        retData3.setBo(list3);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData3);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS2);
        iqcTestRequisitionService.splitIqcDetailsJob();

        ServiceData retData4 = new ServiceData();
        RetCode retCode4 = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData4.setCode(retCode4);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData4);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS2);
        iqcTestRequisitionService.splitIqcDetailsJob();

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitIqcDetailsJob");


        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS3);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitIqcDetailsJob");

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails()).thenReturn(detailDTOS2);

        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenThrow(new NullPointerException());
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitIqcDetailsJob");

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                Mockito.any())).thenThrow(new NullPointerException());
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitIqcDetailsJob");

        Assert.assertNotNull(dto1);
    }
    /* Ended by AICoder, pid:459b19dfaee34df3bb97b352588cf6db */


    /* Started by AICoder, pid:2a05741da1c34fc7b17495eea7b9a4f6 */
    @Test
    public void pushIqcDetailToInfor() throws Exception {
        // 初始化测试数据
        List<IqcTestRequisitionBillDTO> list = new ArrayList<>();
        List<IqcTestRequisitionBillDTO> list1 = new ArrayList<>();
        IqcTestRequisitionBillDTO dto = IqcTestRequisitionBillDTO.builder()
                .deliveryNo("1")
                .billNo("1")
                .billFailTimes(1)
                .build();
        list1.add(dto);

        // 模拟数据库查询结果
        PowerMockito.when(iqcTestRequisitionRepository.getIqcBillToInfor()).thenReturn(list);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        // 模拟成功提交到infor的情况
        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcBillToInfor()).thenReturn(list1);
        PowerMockito.when(WebServiceClient.submitInfor(Mockito.any(), Mockito.any())).thenReturn(retData);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        // 模拟失败提交到infor的情况
        ServiceData retData1 = new ServiceData();
        RetCode retCode1 = new RetCode(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
        retData1.setCode(retCode1);
        PowerMockito.when(WebServiceClient.submitInfor(Mockito.any(), Mockito.any())).thenReturn(retData1);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        // 模拟获取soHeader和soDetail的情况
        List<SoHeader> listSoHeader1 = new ArrayList<>();
        List<SoHeader> listSoHeader = new ArrayList<>();
        SoHeader so = new SoHeader();
        so.setExternalOrderKey2("333");
        so.setHref04("43");
        so.setHref33("43");
        so.setHref01("43");
        so.setHref04("43");
        so.setHref05("43");
        so.setConsigneeKey("21");
        listSoHeader.add(so);
        List<SoDetail> soDetails1 = new ArrayList<>();
        List<SoDetail> soDetails = new ArrayList<>();
        SoDetail de = new SoDetail();
        de.setRef60("333");
        soDetails.add(de);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoHeader(Mockito.any())).thenReturn(listSoHeader1);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoDetail(Mockito.any())).thenReturn(soDetails);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoHeader(Mockito.any())).thenReturn(listSoHeader);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoDetail(Mockito.any())).thenReturn(soDetails1);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoHeader(Mockito.any())).thenReturn(listSoHeader);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoDetail(Mockito.any())).thenReturn(soDetails);
        PowerMockito.when(stransferRepository.getUtilityInfo(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(stransferRepository.getUtilityInfo(Mockito.any())).thenReturn(so);
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(stransferRepository.getMaterialName(Mockito.any())).thenReturn("");
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(stransferRepository.getMaterialName(Mockito.any())).thenReturn("32");
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(stransferRepository.getPlaceName(Mockito.any())).thenReturn("");
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(stransferRepository.getPlaceName(Mockito.any())).thenReturn("32");
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        PowerMockito.when(iqcTestRequisitionRepository.getIqcSoHeader(Mockito.any())).thenThrow(new NullPointerException());
        Whitebox.invokeMethod(iqcTestRequisitionService, "pushIqcDetailToInfor");

        // 断言
        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:2a05741da1c34fc7b17495eea7b9a4f6 */

    /* Started by AICoder, pid:e6a6e64992f94f369b7932a252a16440 */
    @Test
    public void getIqcTestRequistionList() throws Exception {
        // 初始化测试数据
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .deliveryNo("1")
                .billNo("1")
                .itemNo("1")
                .itemBarcode("2")
                .beginDate("2021-01-01")
                .endDate("2024-01-01")
                .build();

        List<IqcTestRequisitionQueryDTO> listDto = new ArrayList<>();
        listDto.add(dto);

        List<List<IqcTestRequisitionQueryDTO>> lists = new ArrayList<>();
        lists.add(listDto);

        PowerMockito.when(CommonUtils.splitList(listDto, NumConstant.INT_500)).thenReturn(lists);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequistionListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequistionListVo(Mockito.any())).thenReturn(listDto);

        iqcTestRequisitionService.getIqcTestRequistionList(dto);

        // 更新测试数据
        dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .deliveryNo("1")
                .billNo("1")
                .itemNo("1")
                .itemBarcode("2")
                .beginDate("2021-01-01")
                .endDate("2024-01-01")
                .productClass("1")
                .planningGroup("1")
                .build();

        listDto.add(dto);
        lists = new ArrayList<>();
        lists.add(listDto);

        List<IqcTestRequisitionPkgDTO> pkgList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDto = IqcTestRequisitionPkgDTO.builder()
                .productClass("1")
                .productClassDesc("1")
                .planningGroup("1")
                .planningGroupDesc("2")
                .build();
        IqcTestRequisitionPkgDTO pkgDto1 = IqcTestRequisitionPkgDTO.builder()
                .productClass("2")
                .productClassDesc("1")
                .planningGroup("1")
                .planningGroupDesc("2")
                .build();

        pkgList.add(pkgDto);
        pkgList.add(pkgDto1);

        PowerMockito.when(CommonUtils.splitList(listDto, NumConstant.INT_500)).thenReturn(lists);
        PowerMockito.when(stransferRepository.getIqcPlanningGroups(Mockito.any())).thenReturn(pkgList);

        IqcTestRequisitionListVO listVo = iqcTestRequisitionService.getIqcTestRequistionList(dto);

        Assert.assertNotNull(listVo);
    }
    /* Ended by AICoder, pid:e6a6e64992f94f369b7932a252a16440 */


    /* Started by AICoder, pid:a43ff25ab74540e2bf7805c089cb33ae */
    @Test
    public void batchUpdateIqcBill() throws Exception {
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .build();
        iqcTestRequisitionService.batchUpdateIqcBill(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void batchUpdateIqcDetail() throws Exception {
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .build();
        iqcTestRequisitionService.batchUpdateIqcDetail(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void exportIqcTestRequistion() throws Exception {
        IqcTestRequisitionQueryDTO dto = new IqcTestRequisitionQueryDTO();
        dto.setItemNo("1111");

        when(iqcTestRequisitionRepository.getIqcTestRequistionListVOTotal(any())).thenReturn(10);
        when(iqcTestRequisitionRepository.getIqcTestRequistionListVo(any())).thenReturn(new ArrayList<>());

        iqcTestRequisitionService.exportIqcTestRequistion(dto);

        Assert.assertTrue(Objects.nonNull(dto));
    }
    /* Ended by AICoder, pid:a43ff25ab74540e2bf7805c089cb33ae */

    /* Started by AICoder, pid:a74a0add06924a6aae501d0274467793 */
    @Test
    public void getIqcTestRequisitionBill() throws Exception {
        // 初始化测试数据
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .pageIndex(1)
                .pageSize(1)
                .deliveryNo("1")
                .billNo("1")
                .itemNo("1")
                .itemBarcode("2")
                .beginDate("2021-01-01")
                .endDate("2024-01-01")
                .build();

        List<IqcTestRequisitionQueryDTO> listDto = new ArrayList<>();
        listDto.add(dto);

        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequistionBillVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcTestRequistionBillVo(Mockito.any())).thenReturn(listDto);

        IqcTestRequisitionListVO listVo = iqcTestRequisitionService.getIqcTestRequisitionBill(dto);

        Assert.assertNotNull(listVo);
    }
    /* Ended by AICoder, pid:a74a0add06924a6aae501d0274467793 */

    @Test
    public void iqcFailSendMail() throws Exception {
        PowerMockito.when(iqcTestRequisitionRepository.selectIqcEmailListCount()).thenReturn(0);
        iqcTestRequisitionService.iqcFailSendMail("43");
        List<SysLookupValuesDTO> sysLookupValuesDTOList1 = new ArrayList<>();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysDto = SysLookupValuesDTO.builder()
                .lookupMeaning("GELI")
                .attribute1("1")
                .build();
        sysLookupValuesDTOList.add(sysDto);
        String expectedFileKey = "fileKey";
        PowerMockito.when(iqcTestRequisitionRepository.selectIqcEmailListCount()).thenReturn(5);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList1);
        iqcTestRequisitionService.iqcFailSendMail("43");
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.mockStatic(SpringContextUtil.class, ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        iqcTestRequisitionService.iqcFailSendMail("43");
        PowerMockito.mockStatic(Tools.class);
        PowerMockito.when(Tools.isEmpty(Mockito.anyString())).thenReturn(false);
        iqcTestRequisitionService.iqcFailSendMail("43");
        PowerMockito.when(iqcTestRequisitionRepository.selectIqcEmailListVo(Mockito.any())).thenThrow(new NullPointerException());
        iqcTestRequisitionService.iqcFailSendMail("43");
        Assert.assertNotNull(sysDto);
    }
    /* Started by AICoder, pid:417bb5e76fd341c8a59bc1f0e6a7de97 */
    @Test
    public void insertIqcData() throws Exception {
        // 初始化测试数据
        List<IqcTestRequisitionDetailDTO> detailDTOS = new ArrayList<>();

        IqcTestRequisitionDetailDTO dto1 = IqcTestRequisitionDetailDTO.builder()
                .isLtc("Y")
                .checkResult(0)
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .detailStatus(0)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        IqcTestRequisitionDetailDTO dto2 = IqcTestRequisitionDetailDTO.builder()
                .isLtc("N")
                .checkResult(0)
                .pkgGoodQty(BigDecimal.valueOf(10))
                .pkgPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .detailStatus(0)
                .stockNo("WMWHSE26")
                .build();

        detailDTOS.add(dto1);
        detailDTOS.add(dto2);

        IqcTestRequisitionBillDTO dto = IqcTestRequisitionBillDTO.builder()
                .deliveryNo("1")
                .billNo("1")
                .billFailTimes(1)
                .build();

        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcData", detailDTOS, dto);

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:417bb5e76fd341c8a59bc1f0e6a7de97 */

    /* Started by AICoder, pid:355e541a87db4930831a15f23ecbe3ba */
    @Test
    public void splitInforPkg() throws Exception {
        // 初始化测试数据
        IqcTestRequisitionDetailDTO dto = IqcTestRequisitionDetailDTO.builder()
                .pkgId("ZTEPKG001")
                .pkgGoodQty(BigDecimal.valueOf(10))
                .pkgPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();

        // 模拟HTTP响应
        String responseStr = "{}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = null;
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforPkg", dto, "4343");

        // 更新模拟HTTP响应
        String responseStr2 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":597.405925,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr2);

        ObjectMapper objectMapper2 = new ObjectMapper();
        JsonNode json2 = objectMapper2.readTree(responseStr2);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json2);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforPkg", dto, "4343");

        // 更新模拟HTTP响应
        String responseStr3 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr3);

        ObjectMapper objectMapper3 = new ObjectMapper();
        JsonNode json3 = objectMapper3.readTree(responseStr3);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json3);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforPkg", dto, "4343");

        // 更新模拟HTTP响应
        String responseStr4 = "{\"code\":\"200\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr4);

        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforPkg", dto, "4343");

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:355e541a87db4930831a15f23ecbe3ba */
    /* Started by AICoder, pid:3c42081cc0d542fb8c077373ab51be29 */
    @Test
    public void splitInforReel() throws Exception {
        // 初始化测试数据
        IqcTestRequisitionDetailDTO dto = IqcTestRequisitionDetailDTO.builder()
                .reelId("ZTE001")
                .reelGoodQty(BigDecimal.valueOf(10))
                .reelPickQty(BigDecimal.valueOf(5))
                .inforSplitStatus(1)
                .splitFailTimes(0)
                .stockNo("WMWHSE26")
                .build();
        ServiceData retData4 = new ServiceData();
        RetCode retCode4 = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData4.setCode(retCode4);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData4);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        ServiceData retData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.AUTHFAILED_CODE, RetCode.BUSINESSERROR_MSG);
        retData.setCode(retCode);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsObject = new JSONObject();
        jsObject.put("barcode", "1");
        list.add(jsObject);

        retData = new ServiceData();
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo("");

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");


        retData = new ServiceData();
        retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);
        retData.setBo(list);

        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(retData);
        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        // 模拟HTTP响应
        String responseStr = "{}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = null;
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        // 更新模拟HTTP响应
        String responseStr2 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"message\":[{\"organizationId\":395,\"inventoryItemId\":22979037,\"creationdate\":\"2018-08-02T05:16:00.000+00:00\",\"lastupdatedate\":\"2020-07-27T07:38:19.000+00:00\",\"itemcode\":\"130000174940\",\"lastupdatedby\":\"袁,国智10154954\",\"avecost\":597.405925,\"rn\":1,\"currencycode\":\"CNY\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr2);

        ObjectMapper objectMapper2 = new ObjectMapper();
        JsonNode json2 = objectMapper2.readTree(responseStr2);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json2);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        /* Started by AICoder, pid:0b5eeeb105e246bd82106680866017ce */
        // 使用StringBuilder构建一个包含3000个"a"的字符串
        StringBuilder dynamicStringBuilder = new StringBuilder();
        for (int i = 0; i < 3000; i++) {
            dynamicStringBuilder.append("a");
        }

        // 将StringBuilder转换为String
        String dynamicString = dynamicStringBuilder.toString();

        // 创建一个JSON对象并添加键值对
        JSONObject jsonItems = new JSONObject();
        jsonItems.put("message", dynamicString);
        jsonItems.put("code", "0000");

        // 将JSON对象转换为字符串
        String updatedJsonString = jsonItems.toString();
        /* Ended by AICoder, pid:0b5eeeb105e246bd82106680866017ce */
        // 更新模拟HTTP响应
       PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(updatedJsonString);

        ObjectMapper objectMapper3 = new ObjectMapper();
        JsonNode json3 = objectMapper3.readTree(updatedJsonString);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json3);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        // 更新模拟HTTP响应
        String responseStr4 = "{\"code\":\"200\",\"msgId\":\"RetCode.ServerError\",\"msg\":\"服务器错误\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseStr4);

        ObjectMapper objectMapper4 = new ObjectMapper();
        JsonNode json4 = objectMapper4.readTree(responseStr4);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json4);

        Whitebox.invokeMethod(iqcTestRequisitionService, "splitInforReel", dto, "4343");

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:3c42081cc0d542fb8c077373ab51be29 */

    @Test
    public void getInventoryholdRecordList1() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build().setDeliveryNo("4324");
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        IqcTestRequisitionHeadDTO headDTO2 = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");

        List<IqcTestRequisitionPkgDTO> pkgDTOList2 = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO2 = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgPickQty(BigDecimal.valueOf(1));
        pkgDTOList2.add(pkgDTO2);
        headDTO2.setPackList(pkgDTOList2);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO2);
        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO);
        IqcTestRequisitionPkgDTO pkgDTO1 = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        pkgDTOList.add(pkgDTO1);
        headDTO.setPackList(pkgDTOList);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(Constant.QCL, INT_5)).thenReturn("IQC2024100900001");
        PowerMockito.when(iqcTestRequisitionRepository.getItemReelControl(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisitionInfo", headDTO);
        List<IqcTestRequisitionPkgDTO> pkgDTOList1 = new ArrayList<>();
        pkgDTOList1.add(pkgDTO1);
        List<IqcTestRequisitionPkgDTO> pkgDTOList8 = new ArrayList<>();
        PowerMockito.when(iqcTestRequisitionRepository.getItemReelControl(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList1);
        PowerMockito.when(iqcTestRequisitionRepository.getIqcProductClass(Mockito.any(), Mockito.any())).thenReturn(pkgDTOList8);
        PowerMockito.when(stransferRepository.getIqcPlanningGroups(Mockito.any())).thenReturn(pkgDTOList);
        ServiceData<?> response = iqcTestRequisitionService.insertIqcTestRequisitionInfo(headDTO);
        Assert.assertNotNull(response.getCode());
    }

    @Test
    public void insertIqcTestRequisiton1() throws Exception {
        IqcTestRequisitionHeadDTO headDTO = IqcTestRequisitionHeadDTO.builder().build()
                .setDeliveryNo("4324")
                .setStockNo("WMWHSE1")
                .setMaterialPicker("1")
                .setMaterialPickDept("1")
                .setReceiveOrg("1")
                .setUsageCode("1")
                .setUsageName("1");
        List<IqcTestRequisitionPkgDTO> pkgDTOList = new ArrayList<>();
        IqcTestRequisitionPkgDTO pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        List<IqcTestRequisitionReelDTO> reelDtoList = new ArrayList<>();
        IqcTestRequisitionReelDTO reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("Y")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("N")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);

        pkgDTOList = new ArrayList<>();
        pkgDTO = IqcTestRequisitionPkgDTO.builder().build()
                .setItemBarcode("11")
                .setItemNo("1221")
                .setIsLtc("N")
                .setPkgId("132")
                .setChecktype("1")
                .setPkgCheckResult(1)
                .setReelidProcess(0)
                .setPkgGoodQty(BigDecimal.valueOf(1))
                .setPkgPickQty(BigDecimal.valueOf(1))
                .setProductClass("AA")
                .setPlanningGroup("PG009");
        reelDtoList = new ArrayList<>();
        reelDto = IqcTestRequisitionReelDTO.builder().build()
                .setPkgId("1")
                .setReelId("1")
                .setReelIdCheckResult(1)
                .setReelGoodQty(BigDecimal.valueOf(1))
                .setReelPickQty(BigDecimal.valueOf(1));
        reelDtoList.add(reelDto);
        pkgDTO.setReelList(reelDtoList);
        pkgDTOList.add(pkgDTO);
        headDTO.setPackList(pkgDTOList);
        Whitebox.invokeMethod(iqcTestRequisitionService, "insertIqcTestRequisiton", headDTO);
        Assert.assertNotNull(headDTO);
    }
    /* Started by AICoder, pid:9aad66cfdbcc4e4b82bbbccb8540eb62 */
    @Test
    public void getProductName() throws Exception {
        // 初始化测试数据
        IqcTestRequisitionQueryDTO dto = IqcTestRequisitionQueryDTO.builder()
                .productClass("")
                .planningGroup("1")
                .build();

        IqcTestRequisitionQueryDTO dto1 = IqcTestRequisitionQueryDTO.builder()
                .productClass("1")
                .planningGroup("1")
                .build();

        IqcTestRequisitionQueryDTO dto2 = IqcTestRequisitionQueryDTO.builder()
                .productClass("1")
                .planningGroup("")
                .build();

        List<IqcTestRequisitionQueryDTO> listDto = new ArrayList<>();
        listDto.add(dto);
        listDto.add(dto1);
        listDto.add(dto2);

        List<List<IqcTestRequisitionQueryDTO>> lists = new ArrayList<>();
        lists.add(listDto);

        List<IqcTestRequisitionPkgDTO> pkgList = new ArrayList<>();

        IqcTestRequisitionPkgDTO pkgDto = IqcTestRequisitionPkgDTO.builder()
                .productClass("1")
                .productClassDesc("1")
                .planningGroup("1")
                .planningGroupDesc("2")
                .build();

        IqcTestRequisitionPkgDTO pkgDto1 = IqcTestRequisitionPkgDTO.builder()
                .productClass("2")
                .productClassDesc("1")
                .planningGroup("1")
                .planningGroupDesc("2")
                .build();

        IqcTestRequisitionPkgDTO pkgDto2 = IqcTestRequisitionPkgDTO.builder()
                .productClass("2")
                .productClassDesc("1")
                .planningGroup("2")
                .planningGroupDesc("2")
                .build();

        IqcTestRequisitionPkgDTO pkgDto3 = IqcTestRequisitionPkgDTO.builder()
                .productClass("1")
                .productClassDesc("1")
                .planningGroup("2")
                .planningGroupDesc("2")
                .build();

        pkgList.add(pkgDto);
        pkgList.add(pkgDto1);
        pkgList.add(pkgDto2);
        pkgList.add(pkgDto3);

        PowerMockito.when(CommonUtils.splitList(listDto, NumConstant.INT_500)).thenReturn(lists);
        PowerMockito.when(stransferRepository.getIqcPlanningGroups(Mockito.any())).thenReturn(pkgList);

        List<IqcTestRequisitionQueryDTO> listVo = iqcTestRequisitionService.getProductName(listDto);

        Assert.assertNotNull(listVo);
    }
    /* Ended by AICoder, pid:9aad66cfdbcc4e4b82bbbccb8540eb62 */
}
