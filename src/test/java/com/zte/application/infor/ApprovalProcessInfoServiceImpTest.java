package com.zte.application.infor;


import cn.hutool.core.io.FileUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.impl.ApprovalProcessInfoServiceImpl;
import com.zte.application.infor.impl.BondedWarehouseServiceImpl;
import com.zte.application.step.impl.OmsGtsServiceImpl;
import com.zte.common.utils.IscpRemoteServiceDataUtil;
import com.zte.domain.model.infor.ApprovalProcessInforRepository;
import com.zte.domain.model.infor.EdiAsnqcSRepository;
import com.zte.domain.model.step.ApprovalProcessInfoRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, ServiceData.class, IscpRemoteServiceDataUtil.class, JSONObject.class, CommonUtils.class, StringUtils.class, RemoteServiceDataUtil.class})
public class ApprovalProcessInfoServiceImpTest {

    @InjectMocks
    private ApprovalProcessInfoServiceImpl approvalProcessInfoServiceImpl;
    @Mock
    ApprovalProcessInfoRepository approvalProcessInfoRepository;
    @Mock
    ApprovalProcessInforRepository inforRepository;

    @Value("${review.flow.temp.code}")
    private String revieflowTempCode;

    @Value("${review.flow.temp.code.detail}")
    private String revieflowTempCodeDetail;

    @Value("${review.appId}")
    private String reviewAppId;


    @Test
    /* Started by AICoder, pid:54bf00579db64616bb262265d74d5947 */
    public void dealScatterMachine() throws Exception {
        JSONObject data = new JSONObject();
        data.put("result", "");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        String status ="";
        try{
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3000;i++){
                sb.append("d");
            }
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
        } catch (Exception e) {
        }
        data = new JSONObject();
        data.put("result", "33");
        data.put("flowCode", "");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try{
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3200;i++){
                sb.append("d");
            }
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SAVE");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "90");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "");
        data.put("approver","10277404");
        try{
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3200;i++){
                sb.append("d");
            }
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","");
        try{
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3200;i++){
                sb.append("d");
            }
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try{
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3200;i++){
                sb.append("d");
            }
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("1");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "", sb.toString());
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "Y");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            StringBuilder sb = new StringBuilder();
            for (int i =0 ;i<3200;i++){
                sb.append("d");
            }
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn("")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","3234");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "Y");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","333");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineBoardIn("")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","3344");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-MATERIAL-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","33443");
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn(Mockito.anyObject())).thenReturn("1");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","33443");
        } catch (Exception e) {
        }

        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-MATERIAL-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn("88")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","35");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "Y");
        data.put("flowCode", "OMS-SCATTER-MACHINE-MATERIAL-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn(Mockito.anyObject())).thenReturn("SUBMITTED");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","345");
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn(Mockito.anyObject())).thenReturn("11");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","345");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "Y");
        data.put("flowCode", "OMS-SCATTER-MACHINE-MATERIAL-IN");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineMaterialIn("")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","45");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "Y");
        data.put("flowCode", "OMS-SCATTER-MACHINE-OUT");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut(Mockito.anyObject())).thenReturn("SUBMITTED");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","445");
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut(Mockito.anyObject())).thenReturn("1");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","445");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-OUT");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut("")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","4545");
        } catch (Exception e) {
        }

        data = new JSONObject();
        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-OUT");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut(Mockito.anyObject())).thenReturn("SUBMITTED");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","4545");
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut(Mockito.anyObject())).thenReturn("11");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","4545");
        } catch (Exception e) {
        }
        approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","");

        data.put("result", "N");
        data.put("flowCode", "OMS-SCATTER-MACHINE-OUT");
        data.put("businessId", "444");
        data.put("approver","10277404");
        try {
            PowerMockito.when(approvalProcessInfoRepository.queryInforMachineOut("")).thenReturn("");
            approvalProcessInfoServiceImpl.dealScatterMachine("", data, "","4554");
            Assert.assertTrue(Objects.nonNull(data));
        } catch (Exception e) {
        }
        data.put("result", "N");
        data.put("flowCode", "IWMS-INVENTORY-HOLD");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS-TASK", "4554");
        data.put("result", "Y");
        data.put("flowCode", "OMS-REMOVE-HOLD-QUALITY");
        data.put("nodeName", "STQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS-TASK", "4554");
        data.put("result", "Y");
        data.put("flowCode", "IWMS-INVENTORY-HOLD");
        data.put("nodeName", "STQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "Y");
        data.put("flowCode", "IWMS-INVENTORY-HOLD");
        data.put("nodeName", "SSTQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "NA");
        data.put("flowCode", "IWMS-INVENTORY-HOLD");
        data.put("nodeName", "STQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "NA");
        data.put("flowCode", "OMS-REMOVE-HOLD-QUALITY");
        data.put("nodeName", "STQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "Y");
        data.put("flowCode", "OMS-REMOVE-HOLD-PLAN");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "N");
        data.put("flowCode", "OMS-REMOVE-HOLD-PLAN");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
        data.put("result", "Y");
        data.put("flowCode", "OMS-REMOVE-HOLD-QUALITYT");
        data.put("nodeName", "STQE");
        approvalProcessInfoServiceImpl.dealScatterMachine("OMS-taskCompleted", data, "OMS", "4554");
    }
    /* Ended by AICoder, pid:54bf00579db64616bb262265d74d5947 */

    /* Started by AICoder, pid:01a110bee0c84decaf87cccd912645d4 */
    @Test
    public void ReviewDealScatterMachine() throws Exception {
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);
        JSONObject data = new JSONObject();
        data.put("flowCode", "OMS-SCATTER-MACHINE-BOARD-IN");
        data.put("businessId", "444");
        try {
            approvalProcessInfoServiceImpl.ReviewDealScatterMachine("", data,"");
        }catch (Exception e){}

        data = new JSONObject();
        data.put("flowCode", "RP241118003");
        data.put("businessId", "444");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 3000; i++) {
            sb.append("d");
        }
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "revieflowTempCode").set(approvalProcessInfoServiceImpl, "RP241118003");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "reviewAppId").set(approvalProcessInfoServiceImpl, "504249875830");
        approvalProcessInfoServiceImpl.ReviewDealScatterMachine("", data, sb.toString());

        data = new JSONObject();
        data.put("flowCode", "RP241211005");
        data.put("businessId", "444");
        StringBuilder sb1 = new StringBuilder();
        for (int i = 0; i < 3200; i++) {
            sb1.append("d");
        }
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "revieflowTempCodeDetail").set(approvalProcessInfoServiceImpl, "RP241211005");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "reviewAppId").set(approvalProcessInfoServiceImpl, "504249875830");
        approvalProcessInfoServiceImpl.ReviewDealScatterMachine("504249875830-review-ended", data, sb1.toString());

        data = new JSONObject();
        data.put("flowCode", "RP241118003");
        data.put("businessId", "444");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "revieflowTempCode").set(approvalProcessInfoServiceImpl, "RP241118003");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "reviewAppId").set(approvalProcessInfoServiceImpl, "504249875830");
        approvalProcessInfoServiceImpl.ReviewDealScatterMachine("504249875830-review-ended", data, "3434");

        data = new JSONObject();
        data.put("flowCode", "RP241211005");
        data.put("businessId", "444");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "revieflowTempCodeDetail").set(approvalProcessInfoServiceImpl, "RP241211005");
        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "reviewAppId").set(approvalProcessInfoServiceImpl, "504249875830");
        approvalProcessInfoServiceImpl.ReviewDealScatterMachine("504249875830-review-turn-down", data, "");
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:01a110bee0c84decaf87cccd912645d4 */
}
