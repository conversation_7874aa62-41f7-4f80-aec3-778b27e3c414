package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.zte.action.iscpedi.GlobalVariable;
import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.action.iscpedi.model.EdiIscpData;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.impl.InforIwmsIscpServiceImpl;
import com.zte.application.infor.impl.InforWarnServiceImpl;
import com.zte.application.infor.impl.InventoryholdRecordServiceImpl;
import com.zte.application.kafka.IKafkaMessageProducer;
import com.zte.application.kafka.ReSendKafkaToIscpListener;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.zte.common.model.MessageId.DATE_FORMAT;
import static com.zte.common.utils.Constant.CLOUD_DISK;
import static com.zte.common.utils.Constant.INT_2;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, RemoteServiceDataUtil.class, Executor.class, EmailUtil.class,ExcelUtil.class,Tools.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class})
public class InforWarnServiceImplTest {

    @InjectMocks
    private InforWarnServiceImpl inforWarnService;
    @Mock
    private InforWarnRepository inforWarnRepository;
    @Mock
    private StepTransferRepository stransferRepository;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private EmailUtil emailUtil;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    private static final Logger LOGGER = LoggerFactory.getLogger(InforIwmsIscpServiceImpl.class);
    private ThreadPoolExecutor thplExecutor = new ThreadPoolExecutor(10, 10,
            10, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, RemoteServiceDataUtil.class, EmailUtil.class,FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class);
        PowerMockito.field(InforWarnServiceImpl.class, "thplExecutor")
                .set(inforWarnService, thplExecutor);
    }

    @Test
    public void warnAllocateMonitorInfor() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class,ExcelUtil.class);
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220").setStoreAge("55");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO dto = SysLookupValuesDTO.builder().build().setLookupMeaning("00326075");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(null);
        inforWarnService.warnAllocateMonitorInfor(vo);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(1)).thenReturn(1);
        inforWarnService.warnAllocateMonitorInfor(vo);
        Assert.assertTrue(Objects.nonNull(vo));
    }
    @Test
    public void warnAllocateMonitorInfor1() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class,ExcelUtil.class);
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220").setStoreAge("55");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO dto = SysLookupValuesDTO.builder().build().setLookupMeaning("00326075");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(null);
        inforWarnService.warnAllocateMonitorInfor(vo);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(1)).thenReturn(0);
        inforWarnService.warnAllocateMonitorInfor(vo);
        Assert.assertTrue(Objects.nonNull(vo));
    }
    @Test
    public void fefoEmail() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class, ExcelUtil.class);
        String receipts = "00326";
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220").setStoreAge("55");
        PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(INT_2)).thenReturn(0);
        Whitebox.invokeMethod(inforWarnService,"fefoEmail",vo,receipts);
        PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(INT_2)).thenReturn(1);
        Whitebox.invokeMethod(inforWarnService,"fefoEmail",vo,receipts);
        Assert.assertTrue(Objects.nonNull(vo));
        try {
            PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(INT_2))
                    .thenThrow(new NullPointerException());
            Whitebox.invokeMethod(inforWarnService,"fefoEmail",vo,receipts);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.NO_DATA_FOUND);
        }
    }
    @Test
    public void warnAllocateMonitorInfor2() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class,ExcelUtil.class);
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220").setStoreAge("55");
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO dto = SysLookupValuesDTO.builder().build().setLookupMeaning("00326075");
        sysLookupValuesDTOList.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(null);
        inforWarnService.warnAllocateMonitorInfor(vo);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.when(inforWarnRepository.getAllocateExceptionMonitorTotal(1)).thenThrow(new NullPointerException());
        inforWarnService.warnAllocateMonitorInfor(vo);
        Assert.assertTrue(Objects.nonNull(vo));
    }
    @Test
    public void getStockAge() throws Exception {
        List<AllocateExceptionMonitorVo> listException = new ArrayList<>();
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220").setStoreAge("55");
        listException.add(vo);
        PowerMockito.when(stransferRepository.getStockAgeByBarcode(Mockito.any())).thenReturn(listException);
        List<AllocateExceptionMonitorVo> res = inforWarnService.getStockAge(listException);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void AllocateSendEmail() throws Exception {
        String receipts = "00326075";
        int count = 1996;
        List<AllocateExceptionMonitorVo> listException = new ArrayList<>();
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220")
                .setStoreAge("55").setPageIndex(1).setPageSize(100);
        listException.add(vo);
        PowerMockito.when(stransferRepository.getStockAgeByBarcode(Mockito.any())).thenReturn(listException);
        PowerMockito.mockStatic(SpringContextUtil.class,ExcelUtil.class);
        inforWarnService.getAllocateSendEmail(receipts, 1, vo);
        inforWarnService.getAllocateSendEmail(receipts, 2, vo);
        Assert.assertTrue(Objects.nonNull(receipts));
    }
    @Test
    public void AllocateSendEmail2() throws Exception {
        String receipts = "00326075";
        int count = 1996;
        String expectedFileKey = "fileKey";
        List<AllocateExceptionMonitorVo> listException = new ArrayList<>();
        AllocateExceptionMonitorVo vo = AllocateExceptionMonitorVo.builder().build().setWhseId("WMWHSE1").setItemBarcode("220")
                .setStoreAge("55").setPageIndex(1).setPageSize(100);
        listException.add(vo);
        PowerMockito.when(stransferRepository.getStockAgeByBarcode(Mockito.any())).thenReturn(listException);
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        PowerMockito.when(Tools.isEmpty(Mockito.anyString())).thenReturn(false);
        inforWarnService.getAllocateSendEmail(receipts, 1, vo);
        inforWarnService.getAllocateSendEmail(receipts, 2, vo);
        Assert.assertTrue(Objects.nonNull(receipts));
    }
}
