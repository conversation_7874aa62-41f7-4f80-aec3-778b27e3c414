package com.zte.application.infor;

import com.zte.application.infor.impl.SysWebserviceConfigServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ThreadUtilTest;
import com.zte.domain.model.infor.SysWebserviceConfigRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.utils.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.zte.common.utils.Constant.UTC;
import static com.zte.common.utils.Constant.WEBSERVICE_LOOKUP_TYPE;
import static org.junit.Assert.assertTrue;

@RunWith(PowerMockRunner.class)
@PrepareForTest({EmailUtil.class,WebServiceClient.class,CommonUtils.class, ThreadUtilTest.class,ZoneId.class})
public class SysWebserviceConfigServiceImplTest {
    @InjectMocks
    SysWebserviceConfigServiceImpl configService;

    @Mock
    private EmailUtil emailUtil;
    @Mock
    private SysWebserviceConfigRepository repository;
    @Before
    public void init(){
        PowerMockito.mockStatic(EmailUtil.class, WebServiceClient.class,CommonUtils.class, ThreadUtilTest.class,ZoneId.class);
    }

    @Test
    public void WebserviceInterfaceMonitoring() throws Exception{
        List<String> lookupValue = Arrays.asList(WEBSERVICE_LOOKUP_TYPE.split(Constant.COMMA));
        List<SysLookupValuesDTO> list1=new ArrayList<>();
        PowerMockito.when(repository.getWebserviceInterface(lookupValue)).thenReturn(list1);
        Whitebox.invokeMethod(configService,"webServiceInterfaceMonitoring");
        assertTrue(Objects.nonNull(list1));

        SysLookupValuesDTO dto2=new SysLookupValuesDTO();
        dto2.setLookupType("1000041");
        dto2.setLookupMeaning("http://mes.zte.com.cn/mes_service/Commonservice.asmx");
        dto2.setDescription("wqwewwe");
        dto2.setAttribute1("100");
        list1.add(dto2);
        PowerMockito.when(repository.getWebserviceInterface(lookupValue)).thenReturn(list1);
        PowerMockito.when(WebServiceClient.inforStore(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Whitebox.invokeMethod(configService,"webServiceInterfaceMonitoring");
        assertTrue(Objects.nonNull(list1));

        List<SysLookupValuesDTO> list=new ArrayList<>();
        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType("1000041");
        dto.setLookupMeaning("http://mes.zte.com.cn/mes_service/Commonservice.asmx");
        dto.setDescription("wqwewwe");
        dto.setAttribute1("-1");
        list.add(dto);
        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupType("1000042");
        dto1.setLookupMeaning("li.zhibiao1");
        dto1.setAttribute1("-1");
        list.add(dto1);
        PowerMockito.when(repository.getWebserviceInterface(lookupValue)).thenReturn(list);
        PowerMockito.when(WebServiceClient.inforStore(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Whitebox.invokeMethod(configService,"webServiceInterfaceMonitoring");
        assertTrue(Objects.nonNull(list));

        PowerMockito.when(repository.getWebserviceInterface(lookupValue)).thenReturn(list);
        try {
            PowerMockito.when(WebServiceClient.inforStore(Mockito.anyString(), Mockito.anyString())).thenThrow(new NullPointerException());
            configService.webServiceInterfaceMonitoring();
        }catch (Exception e) {
            configService.interfaceSendMail(list,"434", LocalDateTime.now(),LocalDateTime.now(),-1);
            assertTrue(true);
        }
    }

    @Test
    public void interfaceSendMail() throws Exception{
        List<SysLookupValuesDTO> list=new ArrayList<>();
        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupType("1000042");
        dto1.setLookupMeaning("li.zhibiao1");
        list.add(dto1);
        Whitebox.invokeMethod(configService,"interfaceSendMail",list,"434", LocalDateTime.now(),LocalDateTime.now(),-1);
        assertTrue(Objects.nonNull(list));
    }
}