package com.zte.application.infor;

import com.zte.action.iscpedi.GlobalVariable;
import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.action.iscpedi.model.EdiIscpData;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.impl.InforIwmsIscpServiceImpl;
import com.zte.application.kafka.IKafkaMessageProducer;
import com.zte.application.kafka.ReSendKafkaToIscpListener;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.EdiSoS;
import com.zte.domain.model.infor.EdiSoSRepository;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zte.common.model.MessageId.DATE_FORMAT;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ServiceDataUtil.class, GlobalVariable.class, CommonUtils.class,
        BusiAssertException.class, WriteBackIscpHandler.class, RetCode.class})
public class InforIwmsIscpServiceImplTest {

    @InjectMocks
    private InforIwmsIscpServiceImpl inforIwmsIscpServiceImpl;
    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Mock
    private OnlineFallBackApplyBillRepository applyBillRepository;
    @Mock
    private StepIscpRepository stepIscpRepository;
    @Mock
    private EdiSoSRepository ediSoSRepository;
    @Mock
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;
    @Mock
    private IKafkaMessageProducer kafkaMessageProducer;
    @Mock
    private EmailUtil emailUtil;
    @Mock
    private WriteBackIscpHandler writeBackIscpHandler;
    @Mock
    private ReSendKafkaToIscpListener reSendKafkaToIscpListener;
    private static final Logger LOGGER = LoggerFactory.getLogger(InforIwmsIscpServiceImpl.class);

    @Before
    public void init() {
        PowerMockito.mockStatic(ServiceDataUtil.class, GlobalVariable.class, CommonUtils.class,
                BusiAssertException.class, WriteBackIscpHandler.class, RetCode.class);
    }

    @Test
    public void updateFallBackSoPrice() throws Exception {

        List<OnlineFallBackPriceHeadDTO> oList = new ArrayList<>();
        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO.setFallBackId("319912");
        onlineFallBackPriceHeadDTO.setFallBackNo("STH23050500170");
        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal("1.6046"));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal("1.0"));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal("1.42"));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal("0.13"));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal("1.6046"));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal("1.42"));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        onlineFallBackPriceHeadDTO.setDetails(details);
        oList.add(onlineFallBackPriceHeadDTO);

        OnlineFallBackPriceResultDTO resultDto = new OnlineFallBackPriceResultDTO();
        resultDto.setBillNo("STH23050500170");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updatePriceDone", resultDto, onlineFallBackPriceHeadDTO);
        PowerMockito.when(ServiceDataUtil.getSuccess(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateFallBackSoPrice", oList);
        ServiceData<?> res = inforIwmsIscpServiceImpl.updateFallBackSoPrice(oList);
        Assert.assertTrue(Objects.isNull(res));
        try {
            PowerMockito.when(ServiceDataUtil.getSuccess(Mockito.anyObject())).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOUND));
            Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateFallBackSoPrice", oList);
        } catch (Exception e) {
            LOGGER.info(e.toString());
        }

    }

    @Test
    public void updatePriceDone() throws Exception {

        List<OnlineFallBackPriceHeadDTO> oList = new ArrayList<>();
        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO.setFallBackId("319912");
        onlineFallBackPriceHeadDTO.setFallBackNo("STH23050500170");
        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        onlineFallBackPriceHeadDTO.setDetails(details);
        oList.add(onlineFallBackPriceHeadDTO);

        OnlineFallBackPriceResultDTO resultDto = new OnlineFallBackPriceResultDTO();
        resultDto.setBillNo("STH23050500170");

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkOnlineDto", onlineFallBackPriceHeadDTO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updatePriceDone", resultDto, onlineFallBackPriceHeadDTO);

        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setExternalorderkey2("STH23050500170");
        List<EdiSoS> ediSoSs = new ArrayList<>();
        PowerMockito.when(ediSoSRepository.selectEdiSoSAll(ediSoS)).thenReturn(ediSoSs);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updatePriceDone", resultDto, onlineFallBackPriceHeadDTO);

        EdiSoS ediSoS1 = new EdiSoS();
        ediSoS1.setSerialkey(new BigDecimal(*********));
        ediSoS1.setExternlineno("1");
        ediSoS1.setLottable02("220022984098");
        ediSoS1.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS1);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildSosLineNoData", ediSoSs);
        List<EdiSoSParamDTO> ediSoSParamDTOs = new ArrayList<>();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackData", ediSoSParamDTOs, onlineFallBackPriceHeadDTO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updatePriceDone", resultDto, onlineFallBackPriceHeadDTO);

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertEdiSo", ediSoSs, ediSoSParamDTOs, details);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updatePriceDone", resultDto, onlineFallBackPriceHeadDTO);
        Assert.assertTrue(Objects.nonNull(ediSoSs));
    }

    @Test
    public void add() throws Exception {
        double a = 1;
        double b = 2;
        double res = inforIwmsIscpServiceImpl.add(a, b);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void buildSosLineNoData() throws Exception {
        List<EdiSoS> ediSoSs = new ArrayList<>();
        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setSerialkey(new BigDecimal(*********));
        ediSoS.setExternlineno("1");
        ediSoS.setLottable02("220022984098");
        ediSoS.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS);
        EdiSoS ediSoS1 = new EdiSoS();
        ediSoS1.setSerialkey(new BigDecimal(*********));
        ediSoS1.setExternlineno("1");
        ediSoS1.setLottable02("220022984098");
        ediSoS1.setShippedqty(new BigDecimal(790));
        ediSoSs.add(ediSoS1);
        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(184861661));
        ediSoS2.setExternlineno("1");
        ediSoS2.setLottable02("220022984098");
        ediSoS2.setShippedqty(new BigDecimal(355));
        ediSoSs.add(ediSoS2);
        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setSerialkey(new BigDecimal(184861655));
        ediSoS3.setExternlineno("2");
        ediSoS3.setLottable02("220022984098");
        ediSoS3.setShippedqty(new BigDecimal(1260));
        ediSoSs.add(ediSoS3);

        double a = 1;
        double b = 2;
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "add", a, b);
        List<EdiSoSParamDTO> res = inforIwmsIscpServiceImpl.buildSosLineNoData(ediSoSs);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void checkOnlineDto() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO2 = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO2.setFallBackId("319912");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkOnlineDto", onlineFallBackPriceHeadDTO2);

        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO.setFallBackNo("STH23050500170");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkOnlineDto", onlineFallBackPriceHeadDTO);

        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO1 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO1.setSerailKey("*********");
        details.add(onlineFallBackPriceDetailDTO1);
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO2 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO2.setSerailKey("*********");
        onlineFallBackPriceDetailDTO2.setExlineNo("1");
        details.add(onlineFallBackPriceDetailDTO2);
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO3 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO3.setSerailKey("*********");
        onlineFallBackPriceDetailDTO3.setExlineNo("1");
        onlineFallBackPriceDetailDTO3.setItemBarcode("220022984098");
        details.add(onlineFallBackPriceDetailDTO3);
        onlineFallBackPriceHeadDTO.setDetails(details);

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkItems", items);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkOnlineDto", onlineFallBackPriceHeadDTO);
        Assert.assertTrue(Objects.nonNull(onlineFallBackPriceHeadDTO));

    }

    @Test
    public void checkItems() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        OnlineFallBackPriceDTO onlineFallBackPriceDTO1 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO1.setCurrencyType("CNY");
        onlineFallBackPriceDTO1.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO1.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO1.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO1.setQty(null);
        onlineFallBackPriceDTO1.setReceiptLineNo("1");
        onlineFallBackPriceDTO1.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO1.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO1.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO1);
        OnlineFallBackPriceDTO onlineFallBackPriceDTO2 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO2.setCurrencyType("CNY");
        onlineFallBackPriceDTO2.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO2.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO2.setQty(new BigDecimal(0));
        onlineFallBackPriceDTO2.setReceiptLineNo("1");
        onlineFallBackPriceDTO2.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO2.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO2);
        OnlineFallBackPriceDTO onlineFallBackPriceDTO3 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO3.setCurrencyType("CNY");
        onlineFallBackPriceDTO3.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO3.setPriceNoTax(null);
        onlineFallBackPriceDTO3.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO3.setReceiptLineNo("1");
        onlineFallBackPriceDTO3.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO3.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO3);
        OnlineFallBackPriceDTO onlineFallBackPriceDTO4 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO4.setCurrencyType("CNY");
        onlineFallBackPriceDTO4.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO4.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO4.setPriceNoTax(new BigDecimal(0));
        onlineFallBackPriceDTO4.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO4.setReceiptLineNo("1");
        onlineFallBackPriceDTO4.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO4.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO4.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO4);

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkItems", items);
        Assert.assertTrue(Objects.nonNull(items));
    }

    @Test
    public void checkEdiSoData() throws Exception {

        List<EdiSoS> ediSoSs = new ArrayList<>();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkEdiSoData", ediSoSs);

        EdiSoS ediSoS1 = new EdiSoS();
        ediSoS1.setSerialkey(new BigDecimal(*********));
        ediSoS1.setExternlineno("1");
        ediSoS1.setLottable02("220022984098");
        ediSoS1.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS1);
        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(*********));
        ediSoS2.setExternlineno(1);
        ediSoS2.setLottable02("220022984098");
        ediSoS2.setShippedqty(new BigDecimal(80));
        ediSoS2.setRef14(new BigDecimal(1));
        ediSoSs.add(ediSoS2);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkEdiSoData", ediSoSs);
        Assert.assertTrue(Objects.nonNull(ediSoSs));
    }

    @Test
    public void checkFallBackData() throws Exception {

        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO.setFallBackId("319912");
        onlineFallBackPriceHeadDTO.setFallBackNo("STH23050500170");
        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("2");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        onlineFallBackPriceHeadDTO.setDetails(details);

        List<EdiSoSParamDTO> ediSoSParamDTOs = new ArrayList<>();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackData", ediSoSParamDTOs, onlineFallBackPriceHeadDTO);

        EdiSoSParamDTO ediSoSParamDTO = new EdiSoSParamDTO();
        ediSoSParamDTO.setExLineNo("1");
        ediSoSParamDTO.setLottable02("220022984098");
        ediSoSParamDTO.setQty(1225);
        ediSoSParamDTOs.add(ediSoSParamDTO);

        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO2 = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO2.setFallBackId("319912");
        onlineFallBackPriceHeadDTO2.setFallBackNo("STH23050500170");
        List<OnlineFallBackPriceDetailDTO> details2 = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO2 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO2.setExlineNo("1");
        onlineFallBackPriceDetailDTO2.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO2.setSerailKey("463349");
        details2.add(onlineFallBackPriceDetailDTO2);
        onlineFallBackPriceHeadDTO2.setDetails(details2);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackData", ediSoSParamDTOs, onlineFallBackPriceHeadDTO2);

        OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO3 = new OnlineFallBackPriceHeadDTO();
        onlineFallBackPriceHeadDTO3.setFallBackId("319912");
        onlineFallBackPriceHeadDTO3.setFallBackNo("STH23050500170");
        List<OnlineFallBackPriceDetailDTO> details3 = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO3 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO3.setExlineNo("1");
        onlineFallBackPriceDetailDTO3.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO3.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items3 = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO3 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO3.setCurrencyType("CNY");
        onlineFallBackPriceDTO3.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO3.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO3.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO3.setReceiptLineNo("1");
        onlineFallBackPriceDTO3.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO3.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setUniPriceNoTax(new BigDecimal(1.42));
        items3.add(onlineFallBackPriceDTO3);
        onlineFallBackPriceDetailDTO3.setItems(items3);
        details3.add(onlineFallBackPriceDetailDTO3);
        onlineFallBackPriceHeadDTO3.setDetails(details3);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackDataSize", "STH23050500170", "1", details3, ediSoSParamDTO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackData", ediSoSParamDTOs, onlineFallBackPriceHeadDTO3);
        Assert.assertTrue(Objects.nonNull(ediSoSParamDTOs));
    }

    @Test
    public void checkFallBackDataSize() throws Exception {

        List<OnlineFallBackPriceDetailDTO> details3 = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO3 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO3.setExlineNo("1");
        onlineFallBackPriceDetailDTO3.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO3.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items3 = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO3 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO3.setCurrencyType("CNY");
        onlineFallBackPriceDTO3.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO3.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO3.setQty(new BigDecimal(0));
        onlineFallBackPriceDTO3.setReceiptLineNo("1");
        onlineFallBackPriceDTO3.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO3.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO3.setUniPriceNoTax(new BigDecimal(1.42));
        items3.add(onlineFallBackPriceDTO3);
        onlineFallBackPriceDetailDTO3.setItems(items3);
        details3.add(onlineFallBackPriceDetailDTO3);

        EdiSoSParamDTO ediSoSParamDTO = new EdiSoSParamDTO();
        ediSoSParamDTO.setExLineNo("1");
        ediSoSParamDTO.setLottable02("220022984098");
        ediSoSParamDTO.setQty(1225.4);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackDataSize", "STH23050500170", "1", details3, ediSoSParamDTO);

        List<OnlineFallBackPriceDetailDTO> details2 = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO2 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO2.setExlineNo("1");
        onlineFallBackPriceDetailDTO2.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO2.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items2 = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO2 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO2.setCurrencyType("CNY");
        onlineFallBackPriceDTO2.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO2.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO2.setQty(new BigDecimal(100));
        onlineFallBackPriceDTO2.setReceiptLineNo("1");
        onlineFallBackPriceDTO2.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO2.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setUniPriceNoTax(new BigDecimal(1.42));
        items2.add(onlineFallBackPriceDTO2);
        onlineFallBackPriceDetailDTO2.setItems(items2);
        details2.add(onlineFallBackPriceDetailDTO2);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackDataSize","STH23050500170", "1", details2, ediSoSParamDTO);

        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ediSoSRepository.insertOnlineFallBackPriceLog(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkFallBackDataSize","STH23050500170", "1", details, ediSoSParamDTO);
        String res =inforIwmsIscpServiceImpl.checkFallBackDataSize("STH23050500170","1", details, ediSoSParamDTO);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void insertEdiSo() throws Exception {

        List<EdiSoS> ediSoSs = new ArrayList<>();
        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setSerialkey(new BigDecimal(*********));
        ediSoS.setExternlineno("1");
        ediSoS.setLottable02("220022984098");
        ediSoS.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS);

        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO2 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO2.setExlineNo("1");
        onlineFallBackPriceDetailDTO2.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO2.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items2 = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO2 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO2.setCurrencyType("CNY");
        onlineFallBackPriceDTO2.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO2.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO2.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO2.setReceiptLineNo("1");
        onlineFallBackPriceDTO2.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO2.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setUniPriceNoTax(new BigDecimal(1.42));
        items2.add(onlineFallBackPriceDTO2);
        onlineFallBackPriceDetailDTO2.setItems(items2);
        details.add(onlineFallBackPriceDetailDTO2);

        List<EdiSoSParamDTO> ediSoSParamDTOs = new ArrayList<>();
        EdiSoSParamDTO ediSoSParamDTO = new EdiSoSParamDTO();
        ediSoSParamDTO.setExLineNo("1");
        ediSoSParamDTO.setLottable02("220022984098");
        ediSoSParamDTO.setQty(1225);
        ediSoSParamDTOs.add(ediSoSParamDTO);

        List<List<EdiSoS>> pageAddEdiSo = new ArrayList<>();
        List<EdiSoS> addList = new ArrayList<>();
        EdiSoS ediSoS1 = new EdiSoS();
        ediSoS1.setSerialkey(new BigDecimal(0));
        addList.add(ediSoS1);
        pageAddEdiSo.add(addList);
        List<List<EdiSoS>> pageUpdateEdiSo = new ArrayList<>();
        List<EdiSoS> updateList = new ArrayList<>();
        EdiSoS ediSoS4 = new EdiSoS();
        ediSoS4.setSerialkey(new BigDecimal(463349));
        updateList.add(ediSoS4);
        pageUpdateEdiSo.add(updateList);
        List<List<EdiSoS>> pageDeleteEdiSo = new ArrayList<>();
        List<EdiSoS> deleteList = new ArrayList<>();
        EdiSoS ediSoS5 = new EdiSoS();
        ediSoS5.setSerialkey(new BigDecimal(463349));
        ediSoS5.setOrderkey("-1");
        deleteList.add(ediSoS5);
        pageDeleteEdiSo.add(deleteList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSosData", ediSoSs, ediSoSParamDTOs, details);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", addList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", updateList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", deleteList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertOrUpdateEdiSo", pageAddEdiSo, pageUpdateEdiSo, pageDeleteEdiSo);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertEdiSo", ediSoSs, ediSoSParamDTOs, details);

        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(*********));
        ediSoS2.setExternlineno("1");
        ediSoS2.setLottable02("220022984098");
        ediSoS2.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS2);
        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setSerialkey(new BigDecimal(*********));
        ediSoS3.setExternlineno("1");
        ediSoS3.setLottable02("220022984098");
        ediSoS3.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS3);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSosData", ediSoSs, ediSoSParamDTOs, details);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", addList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", updateList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", deleteList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertOrUpdateEdiSo", pageAddEdiSo, pageUpdateEdiSo, pageDeleteEdiSo);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertEdiSo", ediSoSs, ediSoSParamDTOs, details);
        Assert.assertTrue(Objects.nonNull(ediSoSs));
    }

    @Test
    public void buildPageEdiSo() throws Exception {

        List<EdiSoS> list = new ArrayList<>();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", list);

        for (int i = 0; i < 999; i++) {
            EdiSoS ediSoS = new EdiSoS();
            ediSoS.setSerialkey(new BigDecimal(*********));
            ediSoS.setExternlineno("1");
            ediSoS.setLottable02("220022984098");
            ediSoS.setShippedqty(new BigDecimal(80));
            list.add(ediSoS);
        }
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", list);

        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setSerialkey(new BigDecimal(*********));
        ediSoS.setExternlineno("1");
        ediSoS.setLottable02("220022984098");
        ediSoS.setShippedqty(new BigDecimal(80));
        list.add(ediSoS);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildPageEdiSo", list);
        Assert.assertTrue(Objects.nonNull(list));

    }

    @Test
    public void insertOrUpdateEdiSo() throws Exception {

        List<List<EdiSoS>> pageAddEdiSo = new ArrayList<>();
        List<EdiSoS> addList = new ArrayList<>();
        EdiSoS ediSoS1 = new EdiSoS();
        ediSoS1.setSerialkey(new BigDecimal(0));
        addList.add(ediSoS1);
        pageAddEdiSo.add(addList);
        List<List<EdiSoS>> pageUpdateEdiSo = new ArrayList<>();
        List<EdiSoS> updateList = new ArrayList<>();
        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(463349));
        updateList.add(ediSoS2);
        pageUpdateEdiSo.add(updateList);
        List<List<EdiSoS>> pageDeleteEdiSo = new ArrayList<>();
        List<EdiSoS> deleteList = new ArrayList<>();
        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setSerialkey(new BigDecimal(463349));
        ediSoS3.setOrderkey("-1");
        deleteList.add(ediSoS3);
        pageDeleteEdiSo.add(deleteList);

        PowerMockito.when(ediSoSRepository.insertEdiSosBatch(addList)).thenReturn(1);
        PowerMockito.when(ediSoSRepository.deleteEdiSosBatch(updateList)).thenReturn(1);
        PowerMockito.when(ediSoSRepository.updateEdiSosBatch(deleteList)).thenReturn(1);

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "insertOrUpdateEdiSo", pageAddEdiSo, pageUpdateEdiSo, pageDeleteEdiSo);
        Assert.assertTrue(Objects.nonNull(pageAddEdiSo));
    }

    @Test
    public void buildEdiSosData() throws Exception {

        List<EdiSoS> allSoData = new ArrayList<>();
        List<EdiSoS> ediSoSs = new ArrayList<>();
        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setSerialkey(new BigDecimal(*********));
        ediSoS.setExternlineno("1");
        ediSoS.setLottable02("220022984098");
        ediSoS.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS);

        List<OnlineFallBackPriceDetailDTO> details = new ArrayList<>();
        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO.setExlineNo("1");
        onlineFallBackPriceDetailDTO.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        items.add(onlineFallBackPriceDTO);
        onlineFallBackPriceDetailDTO.setItems(items);
        details.add(onlineFallBackPriceDetailDTO);

        List<EdiSoSParamDTO> ediSoSParamDTOs = new ArrayList<>();
        EdiSoSParamDTO ediSoSParamDTO = new EdiSoSParamDTO();
        ediSoSParamDTO.setExLineNo("1");
        ediSoSParamDTO.setLottable02("220022984098");
        ediSoSParamDTO.setQty(1225);
        ediSoSParamDTOs.add(ediSoSParamDTO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos", allSoData, ediSoSs, items);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSosData", ediSoSs, ediSoSParamDTOs, details);

        OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO2 = new OnlineFallBackPriceDetailDTO();
        onlineFallBackPriceDetailDTO2.setExlineNo("1");
        onlineFallBackPriceDetailDTO2.setItemBarcode("220022984098");
        onlineFallBackPriceDetailDTO2.setSerailKey("463349");
        List<OnlineFallBackPriceDTO> items2 = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO2 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO2.setCurrencyType("CNY");
        onlineFallBackPriceDTO2.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO2.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO2.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO2.setReceiptLineNo("1");
        onlineFallBackPriceDTO2.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO2.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setUniPriceNoTax(new BigDecimal(1.42));
        items2.add(onlineFallBackPriceDTO2);
        onlineFallBackPriceDetailDTO2.setItems(items2);
        details.add(onlineFallBackPriceDetailDTO2);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos", allSoData, ediSoSs, items);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSosData", ediSoSs, ediSoSParamDTOs, details);

        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(*********));
        ediSoS2.setExternlineno("1");
        ediSoS2.setLottable02("220022984098");
        ediSoS2.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS2);

        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setSerialkey(new BigDecimal(*********));
        ediSoS3.setExternlineno("1");
        ediSoS3.setLottable02("220022984098");
        ediSoS3.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS3);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos", allSoData, ediSoSs, items);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSosData", ediSoSs, ediSoSParamDTOs, details);
        List<EdiSoS> res = inforIwmsIscpServiceImpl.buildEdiSosData(ediSoSs, ediSoSParamDTOs, details);
        Assert.assertTrue(Objects.nonNull(res));
    }

    @Test
    public void buildEdiSos() throws Exception {

        List<EdiSoS> allSoData = new ArrayList<>();

        List<EdiSoS> ediSoSs = new ArrayList<>();
        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setSerialkey(new BigDecimal(*********));
        ediSoS.setExternlineno("1");
        ediSoS.setLottable02("220022984098");
        ediSoS.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS);

        List<OnlineFallBackPriceDTO> onlineFallBackPriceDTOs = new ArrayList<>();
        OnlineFallBackPriceDTO onlineFallBackPriceDTO = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO.setCurrencyType("CNY");
        onlineFallBackPriceDTO.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO.setReceiptLineNo("1");
        onlineFallBackPriceDTO.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO.setUniPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTOs.add(onlineFallBackPriceDTO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos", allSoData, ediSoSs, onlineFallBackPriceDTOs);

        OnlineFallBackPriceDTO onlineFallBackPriceDTO2 = new OnlineFallBackPriceDTO();
        onlineFallBackPriceDTO2.setCurrencyType("CNY");
        onlineFallBackPriceDTO2.setPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setExchangeRate(new BigDecimal(1.0));
        onlineFallBackPriceDTO2.setPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTO2.setQty(new BigDecimal(1225));
        onlineFallBackPriceDTO2.setReceiptLineNo("1");
        onlineFallBackPriceDTO2.setTaxRate(new BigDecimal(0.13));
        onlineFallBackPriceDTO2.setUniPrice(new BigDecimal(1.6046));
        onlineFallBackPriceDTO2.setUniPriceNoTax(new BigDecimal(1.42));
        onlineFallBackPriceDTOs.add(onlineFallBackPriceDTO2);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos",allSoData, ediSoSs, onlineFallBackPriceDTOs);

        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setSerialkey(new BigDecimal(*********));
        ediSoS2.setExternlineno("1");
        ediSoS2.setLottable02("220022984098");
        ediSoS2.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS2);

        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setSerialkey(new BigDecimal(*********));
        ediSoS3.setExternlineno("1");
        ediSoS3.setLottable02("220022984098");
        ediSoS3.setShippedqty(new BigDecimal(80));
        ediSoSs.add(ediSoS3);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "buildEdiSos", allSoData, ediSoSs, onlineFallBackPriceDTOs);
        Assert.assertTrue(Objects.nonNull(ediSoSs));
    }

    @Test
    public void updateVmiSoPrice() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        InforIwmsIscpServiceImpl service = Mockito.spy(new InforIwmsIscpServiceImpl());

        List<VmiSoPriceDTO> oList = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
        vmiSoPriceDTO.setBillNo("21423523");
        vmiSoPriceDTO.setItemBarcode("13141");
        vmiSoPriceDTO.setSerailKey("23423542");
        vmiSoPriceDTO.setUniPriceNoTax(new BigDecimal(100));
        vmiSoPriceDTO.setUniPriceNoTax(new BigDecimal(1));
        oList.add(vmiSoPriceDTO);

        ServiceData<?> checkMustError = new ServiceData<>();
        RetCode code = new RetCode();
        code.setCode("0005");
        code.setMsgId("RetCode.BusinessError");
        checkMustError.setCode(code);
        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(checkMustError);
        //PowerMockito.when(inforIwmsIscpServiceImpl.checkMust(oList)).thenReturn(checkMustError);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateVmiSoPrice", oList);

        RetCode code2 = new RetCode();
        code2.setCode("0000");
        code2.setMsgId("RetCode.Success");
        checkMustError.setCode(code2);
        List<EdiSoS> ediSoSs = new ArrayList<>();
        PowerMockito.when(ediSoSRepository.selectEdiSoSAllInfor(Mockito.anyList())).thenReturn(ediSoSs);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.any())).thenReturn(checkMustError);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateVmiSoPrice", oList);

        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setExternalorderkey2("12131");
        ediSoS.setLottable02("2142");
        ediSoSs.add(ediSoS);
        EdiSoS ediSoS2 = new EdiSoS();
        ediSoS2.setExternalorderkey2("12131");
        ediSoS2.setLottable02("2142");
        ediSoSs.add(ediSoS2);
        EdiSoS ediSoS3 = new EdiSoS();
        ediSoS3.setExternalorderkey2("121313525");
        ediSoS3.setLottable02("21422522");
        ediSoSs.add(ediSoS3);
        PowerMockito.when(ediSoSRepository.selectEdiSoSAllInfor(oList)).thenReturn(ediSoSs);

        ServiceData checkIsExistedAndUpdatedError = new ServiceData<>();
        RetCode code3 = new RetCode();
        code3.setCode("0005");
        code3.setMsgId("RetCode.BusinessError");
        checkIsExistedAndUpdatedError.setCode(code3);
        Map<String, EdiSoS> mapBillNoAndItemBarcode = new HashMap<>();
        mapBillNoAndItemBarcode.put("*********", ediSoS);
        PowerMockito.when(service.checkIsExistedAndUpdated(oList, mapBillNoAndItemBarcode)).thenReturn(checkIsExistedAndUpdatedError);

        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(checkIsExistedAndUpdatedError);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateVmiSoPrice", oList);

        RetCode code4 = new RetCode();
        code4.setCode("0000");
        code4.setMsgId("RetCode.Success");
        checkIsExistedAndUpdatedError.setCode(code4);
        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(checkIsExistedAndUpdatedError);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateBatch", oList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateVmiSoPrice", oList);
        ServiceData<?> res = inforIwmsIscpServiceImpl.updateVmiSoPrice(oList);
        Assert.assertTrue(Objects.isNull(res));
    }

    @Test
    public void updateBatch() throws Exception {

        List<VmiSoPriceDTO> oList = new ArrayList<>();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateBatch", oList);

        for (int i = 0; i < 99; i++) {
            VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
            vmiSoPriceDTO.setBillNo("21423523");
            vmiSoPriceDTO.setItemBarcode(String.valueOf(i));
            vmiSoPriceDTO.setUniPriceNoTax(new BigDecimal(1));
            oList.add(vmiSoPriceDTO);
        }
        try {
            List<VmiSoPriceDTO> list = new ArrayList<>();
            PowerMockito.when(ediSoSRepository.updateVmiSoPrice(list)).thenReturn(1);
            PowerMockito.when(ServiceDataUtil.getSuccess(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        } catch (Exception e) {
            PowerMockito.when(ServiceDataUtil.getBusinessError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        }
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateBatch", oList);

        VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
        vmiSoPriceDTO.setBillNo("21423523");
        vmiSoPriceDTO.setItemBarcode("100");
        vmiSoPriceDTO.setUniPriceNoTax(new BigDecimal(1));
        oList.add(vmiSoPriceDTO);
        try {
            List<VmiSoPriceDTO> list = new ArrayList<>();
            PowerMockito.when(ediSoSRepository.updateVmiSoPrice(list)).thenReturn(1);
            PowerMockito.when(ServiceDataUtil.getSuccess(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        } catch (Exception e) {
            PowerMockito.when(ServiceDataUtil.getBusinessError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        }
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateBatch", oList);
        Assert.assertTrue(Objects.nonNull(oList));
        try {
            PowerMockito.when(ediSoSRepository.updateVmiSoPrice(Mockito.any())).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOUND));
            Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "updateBatch", oList);
        } catch (Exception e) {
            ServiceDataUtil.getBusinessError(e.getMessage());
        }

    }

    @Test
    public void checkIsExistedAndUpdated() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        List<VmiSoPriceDTO> oList = new ArrayList<>();
        Map<String, EdiSoS> mapBillNoAndItemBarcode = new HashMap<>();
        EdiSoS ediSoS = new EdiSoS();
        mapBillNoAndItemBarcode.put("21423523", ediSoS);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkIsExistedAndUpdated", oList, mapBillNoAndItemBarcode);

        VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
        vmiSoPriceDTO.setBillNo("21423523");
        vmiSoPriceDTO.setItemBarcode("1");
        vmiSoPriceDTO.setUniPriceNoTax(new BigDecimal(1));
        oList.add(vmiSoPriceDTO);

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkIsExistedAndUpdated", oList, mapBillNoAndItemBarcode);
        ServiceData<?> res = inforIwmsIscpServiceImpl.checkIsExistedAndUpdated(oList, mapBillNoAndItemBarcode);
        Assert.assertTrue(Objects.isNull(res));
        mapBillNoAndItemBarcode.put("*********", ediSoS);
        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(null);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkIsExistedAndUpdated", oList, mapBillNoAndItemBarcode);

    }

    @Test
    public void checkMust() throws Exception {

        List<VmiSoPriceDTO> oList = new ArrayList<>();
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList);


        VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
        oList.add(vmiSoPriceDTO);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList);

        List<VmiSoPriceDTO> oList2 = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO2 = new VmiSoPriceDTO();
        vmiSoPriceDTO2.setBillNo("23423542");
        oList2.add(vmiSoPriceDTO2);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList2);

        List<VmiSoPriceDTO> oList3 = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO3 = new VmiSoPriceDTO();
        vmiSoPriceDTO3.setBillNo("23423542");
        vmiSoPriceDTO3.setSerailKey("23423542");
        oList3.add(vmiSoPriceDTO3);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList3);

        List<VmiSoPriceDTO> oList4 = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO4 = new VmiSoPriceDTO();
        vmiSoPriceDTO4.setBillNo("23423542");
        vmiSoPriceDTO4.setSerailKey("23423542");
        vmiSoPriceDTO4.setItemBarcode("23423542");
        oList4.add(vmiSoPriceDTO4);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList4);

        List<VmiSoPriceDTO> oList5 = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO5 = new VmiSoPriceDTO();
        vmiSoPriceDTO5.setBillNo("23423542");
        vmiSoPriceDTO5.setSerailKey("23423542");
        vmiSoPriceDTO5.setItemBarcode("23423542");
        vmiSoPriceDTO5.setUniPriceNoTax(new BigDecimal(0));
        oList5.add(vmiSoPriceDTO5);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList5);

        List<VmiSoPriceDTO> oList6 = new ArrayList<>();
        VmiSoPriceDTO vmiSoPriceDTO6 = new VmiSoPriceDTO();
        vmiSoPriceDTO6.setBillNo("23423542");
        vmiSoPriceDTO6.setSerailKey("23423542");
        vmiSoPriceDTO6.setItemBarcode("23423542");
        vmiSoPriceDTO6.setUniPriceNoTax(new BigDecimal(100));
        oList6.add(vmiSoPriceDTO6);
        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(null);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkMust", oList6);
        ServiceData<?> res = inforIwmsIscpServiceImpl.checkMust(oList6);
        Assert.assertTrue(Objects.isNull(res));
    }

    @Test
    public void iscpEdiEntry() throws Exception {
        PowerMockito.mockStatic(GlobalVariable.class);

        String externkey = "123214";
        String sourceTable = "WMWHSE1";
        PowerMockito.when(inforIwmsIscpRepository.updateExceptionIscpEdiLog()).thenReturn(1);
        PowerMockito.when(stepIscpRepository.updateExceptionIscpEdiLog()).thenReturn(1);
        int avaiableSize = 2;
        PowerMockito.when(ServiceDataUtil.getBusinessError(anyString())).thenReturn(null);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpEdiEntry", externkey, sourceTable);

        try {
            List<IscpEdiLog> list = new ArrayList<>();
            IscpEdiLog iscpEdiLog = new IscpEdiLog();
            iscpEdiLog.setSourceTable("EDI_PO_R");
            list.add(iscpEdiLog);
            List<IscpEdiLog> listPr = new ArrayList<>();
            listPr.add(iscpEdiLog);
            IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
            iscpEdiLog2.setSourceTable("EDI_PO_S");
            list.add(iscpEdiLog2);
            List<IscpEdiLog> listPs = new ArrayList<>();
            listPs.add(iscpEdiLog2);
            IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
            iscpEdiLog3.setSourceTable("EDI_SO_S");
            list.add(iscpEdiLog3);
            List<IscpEdiLog> listSs = new ArrayList<>();
            listSs.add(iscpEdiLog3);
            IscpEdiLog iscpEdiLog4 = new IscpEdiLog();
            iscpEdiLog4.setSourceTable("EDI_SO_S_TRANSFER");
            list.add(iscpEdiLog4);
            List<IscpEdiLog> listSstransfer = new ArrayList<>();
            listSstransfer.add(iscpEdiLog4);
            PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLog(externkey, sourceTable, 1)).thenReturn(list);

            List<IscpEdiLog> listStep = new ArrayList<>();
            IscpEdiLog iscpEdiLog5 = new IscpEdiLog();
            iscpEdiLog5.setSourceTable("ST_RECEIVECHECK");
            listStep.add(iscpEdiLog5);
            List<IscpEdiLog> listStepR = new ArrayList<>();
            listStepR.add(iscpEdiLog5);
            IscpEdiLog iscpEdiLog6 = new IscpEdiLog();
            iscpEdiLog6.setSourceTable("ST_RECEIVECHECK_S");
            listStep.add(iscpEdiLog6);
            List<IscpEdiLog> listStepS = new ArrayList<>();
            listStepS.add(iscpEdiLog6);
            PowerMockito.when(stepIscpRepository.getIscpEdiLog(externkey, sourceTable, 1)).thenReturn(listStep);
            Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getWaitSendDataToQueue", list, listStepR, listStepS);
        } catch (Exception e) {
            PowerMockito.when(ServiceDataUtil.getBusinessError(e)).thenReturn(null);
        }
        PowerMockito.when(ServiceDataUtil.getSuccess()).thenReturn(null);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpEdiEntry", externkey, sourceTable);
        ServiceData<?> res = inforIwmsIscpServiceImpl.iscpEdiEntry(externkey, sourceTable);
        Assert.assertTrue(Objects.isNull(res));
    }

    @Test
    public void getWaitSendDataToQueue() throws Exception {

        List<EdiIscpData> list = new ArrayList<>();
        EdiIscpData ediIscpData = new EdiIscpData();
        ediIscpData.setLottable02("11111");
        ediIscpData.setOperateType("11111");
        ediIscpData.setDeliBillNo("11111");
        ediIscpData.setRowNo("1");
        list.add(ediIscpData);
        EdiIscpData ediIscpData2 = new EdiIscpData();
        ediIscpData2.setLottable02("11111");
        ediIscpData2.setOperateType("11111");
        ediIscpData2.setDeliBillNo("11111");
        ediIscpData2.setRowNo("1");
        list.add(ediIscpData2);
        EdiIscpData ediIscpData3 = new EdiIscpData();
        ediIscpData3.setLottable02("22222");
        ediIscpData3.setOperateType("22222");
        ediIscpData3.setDeliBillNo("22222");
        ediIscpData3.setRowNo("2");
        list.add(ediIscpData3);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setSourceTable("EDI_PO_R");
        List<IscpEdiLog> listPr = new ArrayList<>();
        listPr.add(iscpEdiLog);
        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setSourceTable("EDI_PO_S");
        List<IscpEdiLog> listPs = new ArrayList<>();
        listPs.add(iscpEdiLog2);
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setSourceTable("EDI_SO_S");
        List<IscpEdiLog> listSs = new ArrayList<>();
        listSs.add(iscpEdiLog3);
        IscpEdiLog iscpEdiLog4 = new IscpEdiLog();
        iscpEdiLog4.setSourceTable("EDI_SO_S_TRANSFER");
        List<IscpEdiLog> listSstransfer = new ArrayList<>();
        listSstransfer.add(iscpEdiLog4);
        IscpEdiLog iscpEdiLog5 = new IscpEdiLog();
        iscpEdiLog5.setSourceTable("ST_RECEIVECHECK");
        List<IscpEdiLog> listStepR = new ArrayList<>();
        listStepR.add(iscpEdiLog5);
        IscpEdiLog iscpEdiLog6 = new IscpEdiLog();
        iscpEdiLog6.setSourceTable("ST_RECEIVECHECK_S");
        List<IscpEdiLog> listStepS = new ArrayList<>();
        listStepS.add(iscpEdiLog6);
        PowerMockito.when(inforIwmsIscpRepository.updateListSendState(listPr)).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.selectEdiPosData(listPr)).thenReturn(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineData", list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getWaitSendDataToQueue", listPr, listStepR, listStepS);
        Assert.assertTrue(Objects.nonNull(listPr));
    }

    @Test
    public void directSendDataToKafka() throws Exception {

        List<EdiIscpData> list = new ArrayList<>();
        EdiIscpData ediIscpData = new EdiIscpData();
        ediIscpData.setLottable02("11111");
        ediIscpData.setOperateType("11111");
        ediIscpData.setDeliBillNo("11111");
        ediIscpData.setRowNo("1");
        list.add(ediIscpData);
        EdiIscpData ediIscpData2 = new EdiIscpData();
        ediIscpData2.setLottable02("11111");
        ediIscpData2.setOperateType("11111");
        ediIscpData2.setDeliBillNo("11111");
        ediIscpData2.setRowNo("1");
        list.add(ediIscpData2);
        EdiIscpData ediIscpData3 = new EdiIscpData();
        ediIscpData3.setLottable02("22222");
        ediIscpData3.setOperateType("22222");
        ediIscpData3.setDeliBillNo("22222");
        ediIscpData3.setRowNo("2");
        list.add(ediIscpData3);

        List<IscpEdiLog> listPs = new ArrayList<>();
        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setSourceTable("EDI_PO_S");
        listPs.add(iscpEdiLog2);
        List<IscpEdiLog> listSs = new ArrayList<>();
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setSourceTable("EDI_SO_S");
        listSs.add(iscpEdiLog3);

        PowerMockito.when(inforIwmsIscpRepository.updateListSendState(listPs)).thenReturn(1);
        PowerMockito.when(inforIwmsIscpRepository.selectEdiPosData(listPs)).thenReturn(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineDataNew", list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "directSendDataToKafka", listPs, listSs);
        Assert.assertTrue(Objects.nonNull(listPs));
    }

    @Test
    public void combineData() throws Exception {

        List<EdiIscpData> list = new ArrayList<>();
        EdiIscpData ediIscpData = new EdiIscpData();
        ediIscpData.setLottable02("11111");
        ediIscpData.setOperateType("11111");
        ediIscpData.setDeliBillNo("11111");
        ediIscpData.setRowNo("1");
        list.add(ediIscpData);
        EdiIscpData ediIscpData2 = new EdiIscpData();
        ediIscpData2.setLottable02("11111");
        ediIscpData2.setOperateType("11111");
        ediIscpData2.setDeliBillNo("11111");
        ediIscpData2.setRowNo("1");
        list.add(ediIscpData2);
        EdiIscpData ediIscpData3 = new EdiIscpData();
        ediIscpData3.setLottable02("22222");
        ediIscpData3.setOperateType("22222");
        ediIscpData3.setDeliBillNo("22222");
        ediIscpData3.setRowNo("2");
        list.add(ediIscpData3);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineData", list, false);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineData", list);
        Assert.assertTrue(Objects.nonNull(list));
        try {
            PowerMockito.when(inforIwmsIscpServiceImpl.getWriteBackIscpHandler()).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOUND));
            Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineData", list);
        } catch (Exception e) {
            LOGGER.error("direct to kafka InterruptedException :{}", e.getMessage());
        }

    }

    @Test
    public void getWriteBackIscpHandler() throws Exception {

        writeBackIscpHandler = new WriteBackIscpHandler(kafkaMessageProducer, reSendKafkaToIscpListener, inforIwmsIscpRepository, stepIscpRepository);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getWriteBackIscpHandler");
        Assert.assertTrue(Objects.nonNull(writeBackIscpHandler));
    }

    @Test
    public void combineDataNew() throws Exception {

        List<EdiIscpData> list = new ArrayList<>();
        EdiIscpData ediIscpData = new EdiIscpData();
        ediIscpData.setLottable02("11111");
        ediIscpData.setOperateType("11111");
        ediIscpData.setDeliBillNo("11111");
        ediIscpData.setRowNo("1");
        list.add(ediIscpData);
        EdiIscpData ediIscpData2 = new EdiIscpData();
        ediIscpData2.setLottable02("11111");
        ediIscpData2.setOperateType("11111");
        ediIscpData2.setDeliBillNo("11111");
        ediIscpData2.setRowNo("1");
        list.add(ediIscpData2);
        EdiIscpData ediIscpData3 = new EdiIscpData();
        ediIscpData3.setLottable02("22222");
        ediIscpData3.setOperateType("22222");
        ediIscpData3.setDeliBillNo("22222");
        ediIscpData3.setRowNo("2");
        list.add(ediIscpData3);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineData", list, true);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "combineDataNew", list);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void getReelidInventory() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ReelidInventoryInputDTO reelidInventoryInputDTO = new ReelidInventoryInputDTO();
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getReelidInventory", reelidInventoryInputDTO);

        reelidInventoryInputDTO.setBeginTime("2023-06-14 00:00:00");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getReelidInventory", reelidInventoryInputDTO);

        reelidInventoryInputDTO.setEndTime("2023-06-14 23:59:59");

        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDTO1 = new ZteWarehouseInfoDTO();
        zteWarehouseInfoDTO1.setWarehouseId("WMWHSE1");
        zteWarehouseInfoDTO.add(zteWarehouseInfoDTO1);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseInfoDTO);
        List<ReelidInventoryOutDTO> reelidInventoryOutList = new ArrayList<>();
        PowerMockito.when(inforIwmsIscpRepository.selectReelIDInventoryList(reelidInventoryInputDTO)).thenReturn(reelidInventoryOutList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getReelidInventory", reelidInventoryInputDTO);
        List<ReelidInventoryOutDTO> res = inforIwmsIscpServiceImpl.getReelidInventory(reelidInventoryInputDTO);
        Assert.assertTrue(Objects.nonNull(res));
        try {
            BusiAssertException.result(DATE_FORMAT);
            Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getReelidInventory", reelidInventoryInputDTO);
        } catch (Exception e) {
            BusiAssertException.result(DATE_FORMAT);
        }
    }

    @Test
    public void checkObjAllFieldsIsNull() throws Exception {

        ReelidInventoryInputDTO reelidInventoryInputDTO1 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO1.setSku("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO1);

        ReelidInventoryInputDTO reelidInventoryInputDTO2 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO2.setLottable02("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO2);

        ReelidInventoryInputDTO reelidInventoryInputDTO3 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO3.setWhseId("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO3);

        ReelidInventoryInputDTO reelidInventoryInputDTO4 = new ReelidInventoryInputDTO();
        List<String> list = new ArrayList<>();
        list.add("342352");
        reelidInventoryInputDTO4.setLot(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO4);

        ReelidInventoryInputDTO reelidInventoryInputDTO5 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO5.setId("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO5);

        ReelidInventoryInputDTO reelidInventoryInputDTO6 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO6.setLoc("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO6);

        ReelidInventoryInputDTO reelidInventoryInputDTO7 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO7.setSerialNumber("132131");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO7);

        ReelidInventoryInputDTO reelidInventoryInputDTO8 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO8.setBeginTime("2023-06-14 00:00:00");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO8);

        ReelidInventoryInputDTO reelidInventoryInputDTO9 = new ReelidInventoryInputDTO();
        reelidInventoryInputDTO9.setEndTime("2023-06-14 23:59:59");
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "checkObjAllFieldsIsNull", reelidInventoryInputDTO9);
        Assert.assertTrue(Objects.nonNull(reelidInventoryInputDTO9));
    }

    @Test
    public void getBoxLabelInfor() throws Exception {

        BoxLabelDTO boxLabelDTO = new BoxLabelDTO();
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("");
        PowerMockito.when(ServiceDataUtil.getValidAttionError(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getBoxLabelInfor", boxLabelDTO);

        boxLabelDTO.setDropId("2342352");
        List<BoxLabelDTO> list = new ArrayList<>();
        PowerMockito.when(inforIwmsIscpRepository.getBoxLabelInfor(boxLabelDTO)).thenReturn(list);
        PowerMockito.when(ServiceDataUtil.getSuccess(Mockito.anyObject())).thenReturn(Mockito.anyObject());
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "getBoxLabelInfor", boxLabelDTO);
        ServiceData<?> res = inforIwmsIscpServiceImpl.getBoxLabelInfor(boxLabelDTO);
        Assert.assertTrue(Objects.isNull(res));
    }

    @Test
    public void iscpReturnInfor() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class);

        ISCPReturnDTO dto = new ISCPReturnDTO();
        List<String> allSerialKeyList = new ArrayList<>();
        List<String> errorSerialKeyList = new ArrayList<>();
        dto.setAllSerialKeyList(allSerialKeyList);
        dto.setErrorSerialKeyList(errorSerialKeyList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);


        allSerialKeyList.add("2342352");
        dto.setAllSerialKeyList(allSerialKeyList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);

        errorSerialKeyList.add("342362");
        dto.setErrorSerialKeyList(errorSerialKeyList);

        List<Long> allSerialKeys = new ArrayList<>();
        allSerialKeys.add(1L);
        List<Long> errorSerialKeys = new ArrayList<>();
        errorSerialKeys.add(1L);
        List<IscpEdiLog> allList = new ArrayList<>();
        List<IscpEdiLog> errorList = new ArrayList<>();
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(allSerialKeys, "", "EDI_PO_S")).thenReturn(allList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternlineno("1");
        allList.add(iscpEdiLog);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(allSerialKeys, "", "110")).thenReturn(allList);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(errorSerialKeys, "", "110")).thenReturn(errorList);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);

        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setExternlineno("1");
        errorList.add(iscpEdiLog2);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(allSerialKeys, "", "110")).thenReturn(allList);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(errorSerialKeys, "", "110")).thenReturn(errorList);
        dto.setBillType("EDI_PO_R");
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogBySerialKey(allSerialKeys, "EDI_PO_R", 3)).thenReturn(1);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);

        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(allSerialKeys, "", "110")).thenReturn(allList);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(errorList);
        dto.setBillType("EDI_PO_S");
        List<IscpEdiLog> pList = new ArrayList<>();
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiPorsLog(iscpEdiLog2)).thenReturn(pList);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogBySerialKey(allSerialKeys, "EDI_PO_S", 3)).thenReturn(1);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);

        dto.setBillType("EDI_PO_S");
        List<IscpEdiLog> pList2 = new ArrayList<>();
        IscpEdiLog iscpEdiLog3 = new IscpEdiLog();
        iscpEdiLog3.setExternlineno("1");
        pList2.add(iscpEdiLog3);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiPorsLog(iscpEdiLog2)).thenReturn(pList2);
        PowerMockito.when(inforIwmsIscpRepository.updateIscpEdiLogBySerialKey(allSerialKeys, "EDI_PO_S", 3)).thenReturn(1);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "iscpReturnInfor", dto);
        Assert.assertTrue(Objects.nonNull(pList2));
    }

    @Test
    public void dealIscpReturnData() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class);

        String serialKey = "";
        String sourceTable = "";
        List<IscpEdiLog> list = new ArrayList<>();
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(null, "ISSEND", "")).thenReturn(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "dealIscpReturnData", serialKey, sourceTable);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternlineno("1");
        iscpEdiLog.setSourceTable("EDI_PO_S");
        iscpEdiLog.setIsSend(31);
        list.add(iscpEdiLog);
        IscpEdiLog iscpEdiLog2 = new IscpEdiLog();
        iscpEdiLog2.setExternlineno("1");
        iscpEdiLog2.setSourceTable("EDI_SO_S");
        iscpEdiLog2.setIsSend(31);
        list.add(iscpEdiLog2);
        List<IscpEdiLog> listPO = new ArrayList<>();
        listPO.add(iscpEdiLog);
        List<IscpEdiLog> listSO = new ArrayList<>();
        listSO.add(iscpEdiLog2);
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(null, "ISSEND", "")).thenReturn(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "directSendDataToKafka", listPO, listSO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "dealIscpReturnData", serialKey, sourceTable);

        serialKey = "234243";
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "dealIscpReturnData", serialKey, sourceTable);

        sourceTable = "EDI_PO_S";
        PowerMockito.when(inforIwmsIscpRepository.getIscpEdiLogBySerialKey(Tools.newArrayList(Long.parseLong(serialKey)), "ISSEND", "EDI_PO_S")).thenReturn(list);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "directSendDataToKafka", listPO, listSO);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "dealIscpReturnData", serialKey, sourceTable);
        Assert.assertTrue(Objects.nonNull(list));
    }

    @Test
    public void warnInfor() throws Exception {

        int iscpCount = 0;
        PowerMockito.when(inforIwmsIscpRepository.getBillToWarn("1")).thenReturn(iscpCount);
        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "warnInfor");

        iscpCount = 1;
        PowerMockito.when(inforIwmsIscpRepository.getBillToWarn("1")).thenReturn(iscpCount);
        List<StSysLookupValuesDTO> list = new ArrayList<>();
        StSysLookupValuesDTO stSysLookupValuesDTO = new StSysLookupValuesDTO();
        stSysLookupValuesDTO.setLookupMeaning("234252356");
        list.add(stSysLookupValuesDTO);
        PowerMockito.when(applyBillRepository.getSysLookupValues(anyString())).thenReturn(list);
        Whitebox.invokeMethod(emailUtil, "sendMail", anyString(), anyString(), anyString(), anyString(), anyString());

        Whitebox.invokeMethod(inforIwmsIscpServiceImpl, "warnInfor");
        Assert.assertTrue(Objects.nonNull(list));
    }
}
