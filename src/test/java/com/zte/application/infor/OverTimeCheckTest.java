package com.zte.application.infor;

import com.zte.application.material.impl.OverTimeCheckServiceImpl;
import com.zte.domain.model.infor.DelayBillRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.material.dto.OverTimeBarcodeDTO;
import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.interfaces.material.dto.OverTimeOutDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, ServiceData.class,Tools.class})
public class OverTimeCheckTest {

    @InjectMocks
    private OverTimeCheckServiceImpl overTimeCheckService;
    @Mock
    private StepTransferRepository stepTransferRepository;
    @Mock
    private DelayBillRepository delayBillRepository;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(BusiAssertException.class, ServiceData.class);
    }

    @Test
    public void queryInventory() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        OverTimeInDTO overTimeInDTO = new OverTimeInDTO();
        List<String> list = new ArrayList<>();
        list.add("123");
        overTimeInDTO.setWhseid("12");
        overTimeInDTO.setItemBarcode(list);

        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();
        PowerMockito.when(stepTransferRepository.getValidateBarcode(overTimeInDTO)).thenReturn(overTimeInDTOList);
        ServiceData<List<OverTimeOutDTO>> resp = overTimeCheckService.queryInventory(overTimeInDTO);
        Assert.assertTrue(Objects.nonNull(resp.getCode()));
    }


    @Test
    public void queryInventory2() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);
        OverTimeInDTO overTimeInDTO = new OverTimeInDTO();
        List<String> list = new ArrayList<>();
        list.add("123");
        overTimeInDTO.setWhseid("12");
        overTimeInDTO.setItemBarcode(list);

        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();

        OverTimeBarcodeDTO overTimeBarcodeDTO = new OverTimeBarcodeDTO();
        overTimeBarcodeDTO.setItemBarcodes("123");
        overTimeInDTOList.add(overTimeBarcodeDTO);
        PowerMockito.when(stepTransferRepository.getValidateBarcode(overTimeInDTO)).thenReturn(overTimeInDTOList);
        //获取数据字典
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType("1000059");
        dto4.setLookupCode("100005900004");
        dto4.setLookupMeaning("87");
        sysLookupValuesDTOList.add(dto4);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        List<OverTimeOutDTO> listOverTimeOutDTO = new ArrayList<>();
        PowerMockito.when(delayBillRepository.queryInventory(overTimeInDTO)).thenReturn(listOverTimeOutDTO);
        ServiceData<List<OverTimeOutDTO>> resp = overTimeCheckService.queryInventory(overTimeInDTO);
        Assert.assertTrue(Objects.nonNull(resp.getCode()));
    }

    @Test
    public void queryInventory24() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);
        PowerMockito.mockStatic(Tools.class);
        OverTimeInDTO overTimeInDTO = new OverTimeInDTO();
        List<String> list = new ArrayList<>();
        list.add("123");
        overTimeInDTO.setWhseid("12");
        overTimeInDTO.setItemBarcode(list);

        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();

        OverTimeBarcodeDTO overTimeBarcodeDTO = new OverTimeBarcodeDTO();
        overTimeBarcodeDTO.setItemBarcodes("123");
        overTimeInDTOList.add(overTimeBarcodeDTO);
        PowerMockito.when(stepTransferRepository.getValidateBarcode(overTimeInDTO)).thenReturn(overTimeInDTOList);
        //获取数据字典
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType("1000060");
        dto4.setLookupCode("100006000004");
        dto4.setLookupMeaning("87");
        sysLookupValuesDTOList.add(dto4);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        List<OverTimeOutDTO> listOverTimeOutDTO = new ArrayList<>();
        PowerMockito.when(delayBillRepository.queryInventory(overTimeInDTO)).thenReturn(listOverTimeOutDTO);
        ServiceData<List<OverTimeOutDTO>> resp = overTimeCheckService.queryInventory(overTimeInDTO);
        Assert.assertTrue(Objects.nonNull(resp.getCode()));

    }

    @Test
    public void queryInventory23() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        OverTimeInDTO overTimeInDTO = new OverTimeInDTO();
        List<String> list = new ArrayList<>();
        list.add("123");
        overTimeInDTO.setWhseid("12");
        overTimeInDTO.setItemBarcode(list);

        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();

        OverTimeBarcodeDTO overTimeBarcodeDTO = new OverTimeBarcodeDTO();
        overTimeBarcodeDTO.setItemBarcodes("123");
        overTimeInDTOList.add(overTimeBarcodeDTO);
        PowerMockito.when(stepTransferRepository.getValidateBarcode(overTimeInDTO)).thenReturn(overTimeInDTOList);
        //获取数据字典
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType("1000059");
        dto4.setLookupCode("100005900004");
        dto4.setLookupMeaning("87");
        sysLookupValuesDTOList.add(dto4);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);

        List<OverTimeOutDTO> listOverTimeOutDTO = new ArrayList<>();
        OverTimeOutDTO overTimeOutDTO = new OverTimeOutDTO();
        overTimeOutDTO.setIsOk("Y");
        overTimeOutDTO.setItemBarcode("123");
        listOverTimeOutDTO.add(overTimeOutDTO);
        PowerMockito.when(delayBillRepository.queryInventory(overTimeInDTO)).thenReturn(listOverTimeOutDTO);
        ServiceData<List<OverTimeOutDTO>> resp = overTimeCheckService.queryInventory(overTimeInDTO);
        Assert.assertTrue(Objects.nonNull(resp.getCode()));

    }


    @Test
    public void queryInventory3() throws Exception {
        PowerMockito.mockStatic(ServiceData.class);

        OverTimeInDTO overTimeInDTO = new OverTimeInDTO();
        List<String> list = new ArrayList<>();
        list.add("220015593448");
        list.add("0000970388");
        overTimeInDTO.setWhseid("WMWHSE1");
        overTimeInDTO.setItemBarcode(list);

        List<OverTimeBarcodeDTO> overTimeInDTOList = new ArrayList<>();

        OverTimeBarcodeDTO overTimeBarcodeDTO = new OverTimeBarcodeDTO();
        overTimeBarcodeDTO.setItemBarcodes("220015593448");
        overTimeInDTOList.add(overTimeBarcodeDTO);
        PowerMockito.when(stepTransferRepository.getValidateBarcode(overTimeInDTO)).thenReturn(overTimeInDTOList);

        List<OverTimeOutDTO> listout = new ArrayList<>();
        OverTimeOutDTO overTimeOutDTO = new OverTimeOutDTO();
        overTimeOutDTO.setIsOk("Y");
        overTimeOutDTO.setItemBarcode("220015593448");
        listout.add(overTimeOutDTO);
        OverTimeOutDTO overTimeOutDTO2 = new OverTimeOutDTO();
        overTimeOutDTO2.setIsOk("Y");
        overTimeOutDTO2.setItemBarcode("0000970388");
        listout.add(overTimeOutDTO2);
        PowerMockito.when(delayBillRepository.queryInventory(Mockito.any())).thenReturn(listout);
        try {
            ServiceData<List<OverTimeOutDTO>> resp = overTimeCheckService.queryInventory(overTimeInDTO);
            Assert.assertTrue(Objects.nonNull(resp.getCode()));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


}
