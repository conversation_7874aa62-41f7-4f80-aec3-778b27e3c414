package com.zte.application.infor;

import com.zte.application.infor.impl.UpdateInforStageServiceImpl;
import com.zte.domain.model.infor.UpdateInforStageRepository;
import com.zte.interfaces.infor.dto.CodelKup;
import com.zte.interfaces.infor.dto.Orders;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class})
public class UpdateInforStageServiceImplTest {
    @InjectMocks
    UpdateInforStageServiceImpl updateInforStageService;
    @Mock
    private UpdateInforStageRepository updateInforStage;

    @Before
    public void init(){
        PowerMockito.mockStatic(BusiAssertException.class);
    }

    @Test
    public void updateInforStage() throws Exception{
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String tim = df.format(new Date());

        List<String> lin = Tools.newArrayList();
        lin.add("WMWHSE1");
        PowerMockito.when(updateInforStage.getAllWhse()).thenReturn(lin);
        CodelKup codelKupsn = CodelKup.builder().build().setActive("1").setUdf2("599").setUdf1(tim).setWhseId("WMWHSE14");
        PowerMockito.when(updateInforStage.getCodelKup(lin.get(0))).thenReturn(null);
        Whitebox.invokeMethod(updateInforStageService,"updateInforStage");

        List<String> li = Tools.newArrayList();
        li.add("WMWHSE1");
        PowerMockito.when(updateInforStage.getAllWhse()).thenReturn(li);
        CodelKup codelKup = CodelKup.builder().build().setActive("1").setUdf2("599").setUdf1(tim).setWhseId("WMWHSE1");
        List<Orders> listOrder = Tools.newArrayList();
        Orders orders = Orders.builder().build().setOrderKey("0057814").setStage("").setWhseId("WMWHSE1");
        listOrder.add(orders);
        PowerMockito.when(updateInforStage.getCodelKup(li.get(0))).thenReturn(codelKup);
        PowerMockito.when(updateInforStage.getOrdersList(li.get(0))).thenReturn(listOrder);
        Whitebox.invokeMethod(updateInforStageService,"updateInforStage");

        List<String> lis = Tools.newArrayList();
        lis.add("WMWHSE2");
        CodelKup codelKups = CodelKup.builder().build().setActive("1").setUdf2("113").setUdf1("2023-02-11").setWhseId("WMWHSE2");
        List<Orders> listOrders = Tools.newArrayList();
        Orders orders2 = Orders.builder().build().setOrderKey("0057814").setStage("").setWhseId("WMWHSE2");
        listOrders.add(orders2);
        PowerMockito.when(updateInforStage.getAllWhse()).thenReturn(lis);
        PowerMockito.when(updateInforStage.getCodelKup(lis.get(0))).thenReturn(codelKups);
        PowerMockito.when(updateInforStage.getOrdersList(lis.get(0))).thenReturn(listOrders);
        Whitebox.invokeMethod(updateInforStageService,"updateInforStage");
        Assert.assertTrue(Objects.nonNull(listOrders));
    }

}
