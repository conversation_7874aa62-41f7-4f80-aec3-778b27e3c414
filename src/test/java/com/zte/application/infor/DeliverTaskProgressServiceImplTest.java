package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.zte.application.infor.impl.DeliveryTaskProgressServiceImpl;
import com.zte.application.infor.impl.InforIwmsIscpServiceImpl;
import com.zte.application.infor.impl.InforWarnServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.DeliveryTaskProgressRepository;
import com.zte.domain.model.infor.InforWarnRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.DeliverTaskProgressDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.zte.common.utils.Constant.CLOUD_DISK;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, EmailUtil.class, WebServiceClient.class})
public class DeliverTaskProgressServiceImplTest {

    @InjectMocks
    private DeliveryTaskProgressServiceImpl deliveryTaskProgressService;
    @Mock
    DeliveryTaskProgressRepository deliveryTaskProgressRepository;

    Logger logger = LoggerFactory.getLogger(this.getClass());


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(BusiAssertException.class, EmailUtil.class, WebServiceClient.class);
    }
    @Test
    public void getOrderStatusByWh() throws Exception {
        String exterNo = "order1";
        List<DeliverTaskProgressDTO> exterNoList =new ArrayList<>();
        DeliverTaskProgressDTO exterDto1 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE1");
        DeliverTaskProgressDTO exterDto2 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("02").setWhseId("WMWHSE2");
        DeliverTaskProgressDTO exterDto3 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("92").setWhseId("WMWHSE3");
        DeliverTaskProgressDTO exterDto4 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE7");
        DeliverTaskProgressDTO exterDto5 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE14");
        DeliverTaskProgressDTO exterDto6 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE19");
        DeliverTaskProgressDTO exterDto7 =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE26");
        exterNoList.add(exterDto1);
        exterNoList.add(exterDto2);
        exterNoList.add(exterDto3);
        exterNoList.add(exterDto4);
        exterNoList.add(exterDto5);
        exterNoList.add(exterDto6);
        exterNoList.add(exterDto7);
        PowerMockito.when(deliveryTaskProgressRepository.queryShortCount(Mockito.any(),Mockito.any())).thenReturn(0);
        Whitebox.invokeMethod(deliveryTaskProgressService, "getOrderStatusByWh", exterNo,exterNoList,exterNoList);
        PowerMockito.when(deliveryTaskProgressRepository.queryShortCount(Mockito.any(),Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(deliveryTaskProgressService, "getOrderStatusByWh", exterNo,exterNoList,exterNoList);
        Assert.assertTrue(Objects.nonNull(exterNoList));

    }

    @Test
    public void deliveryTaskQuery() throws Exception {
        List<String> externoList = new ArrayList<>();
        externoList.add("11");
        externoList.add("22");
        DeliverTaskProgressDTO deliveryTaskProgressDTO = DeliverTaskProgressDTO.builder().build().setExternoList(externoList);
        List<DeliverTaskProgressDTO> orderList =new ArrayList<>();
        DeliverTaskProgressDTO dto =DeliverTaskProgressDTO.builder().build()
                .setActualShipDate(new Date()).setProdplanNo("11").setStatus("95").setWhseId("WMWHSE1");
        orderList.add(dto);
        PowerMockito.when(deliveryTaskProgressRepository.queryOrderList(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(deliveryTaskProgressService, "deliveryTaskQuery", deliveryTaskProgressDTO);
        PowerMockito.when(deliveryTaskProgressRepository.queryOrderList(Mockito.any())).thenReturn(orderList);

        PowerMockito.when(deliveryTaskProgressRepository.queryShortCount(Mockito.any(),Mockito.any())).thenReturn(0);
        Whitebox.invokeMethod(deliveryTaskProgressService, "deliveryTaskQuery", deliveryTaskProgressDTO);
        orderList =new ArrayList<>();
         dto =DeliverTaskProgressDTO.builder().build()
                .setProdplanNo("11").setStatus("95").setWhseId("WMWHSE1");
        orderList.add(dto);
        PowerMockito.when(deliveryTaskProgressRepository.queryOrderList(Mockito.any())).thenReturn(orderList);
        Whitebox.invokeMethod(deliveryTaskProgressService, "deliveryTaskQuery", deliveryTaskProgressDTO);

        orderList =new ArrayList<>();
        dto =DeliverTaskProgressDTO.builder().build()
                .setProdplanNo("11").setStatus("92").setWhseId("WMWHSE1");
        orderList.add(dto);
        PowerMockito.when(deliveryTaskProgressRepository.queryOrderList(Mockito.any())).thenReturn(orderList);
        Whitebox.invokeMethod(deliveryTaskProgressService, "deliveryTaskQuery", deliveryTaskProgressDTO);
        try {
            PowerMockito.when(deliveryTaskProgressRepository.queryShortCount(Mockito.any(),Mockito.any()))
                    .thenThrow(new NullPointerException());
            Whitebox.invokeMethod(deliveryTaskProgressService, "deliveryTaskQuery", deliveryTaskProgressDTO);
        } catch (Exception e) {
            Assert.assertNotEquals(e.getMessage(), MessageId.NO_DATA_FOUND);
        }
        Assert.assertTrue(Objects.nonNull(deliveryTaskProgressDTO));
    }

}
