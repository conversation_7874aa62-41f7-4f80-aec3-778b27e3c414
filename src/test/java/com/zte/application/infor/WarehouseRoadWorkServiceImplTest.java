package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.infor.impl.WarehouseRoadWorkServiceImpl;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.WarehouseRoadWorkRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,Executor.class,ExcelUtil.class,EmailUtil.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class,JSON.class,Tools.class, ApprovalFlowClient.class})
public class WarehouseRoadWorkServiceImplTest {

    @InjectMocks
    private WarehouseRoadWorkServiceImpl warehouseRoadWorkService;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private WarehouseRoadWorkRepository warehouseRoadWorkRepository;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, ApprovalFlowClient.class);
    }
    @Test
    public void getCoordinateTypeList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000062);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "getCoordinateTypeList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getLocMarkList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000063);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "getLocMarkList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getWarehouseList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000064);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "getWarehouseList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getWarehouseAreaList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000065);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "getWarehouseAreaList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getWarehouseRoadWorkList() throws Exception {

        WarehouseRoadWorkDTO dto = new WarehouseRoadWorkDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "getWarehouseRoadWorkList", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void saveWarehouseRoadWork() throws Exception {

        WarehouseRoadWorkDTO dto = new WarehouseRoadWorkDTO();
        dto.setWhseid("WMWHSE1");
        dto.setLoc("11AA01A");
        PowerMockito.when(warehouseRoadWorkRepository.getLocTotal(Mockito.any())).thenReturn(0);
        Whitebox.invokeMethod(warehouseRoadWorkService, "saveWarehouseRoadWork", dto);

        PowerMockito.when(warehouseRoadWorkRepository.getLocTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "saveWarehouseRoadWork", dto);

        PowerMockito.when(warehouseRoadWorkRepository.getLocTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(0);
        PowerMockito.when(warehouseRoadWorkRepository.saveWarehouseRoadWork(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "saveWarehouseRoadWork", dto);

        dto.setSerialkey(BigDecimal.ONE);
        PowerMockito.when(warehouseRoadWorkRepository.saveWarehouseRoadWork(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "saveWarehouseRoadWork", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void updateWarehouseRoadWork() throws Exception {

        WarehouseRoadWorkDTO dto = new WarehouseRoadWorkDTO();
        dto.setSerialkey(BigDecimal.ONE);
        PowerMockito.when(warehouseRoadWorkRepository.updateWarehouseRoadWork(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "updateWarehouseRoadWork", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void importWarehouseRoadWork() throws Exception {

        WarehouseRoadWorkDTO dto = new WarehouseRoadWorkDTO();
        dto.setWhseid("WMWHSE1");
        dto.setLoc("11AA01A");

        List<WarehouseRoadWorkDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(warehouseRoadWorkService, "importWarehouseRoadWork", list);

        list.add(dto);
        PowerMockito.when(warehouseRoadWorkRepository.getLocTotal(Mockito.any())).thenReturn(0);
        Whitebox.invokeMethod(warehouseRoadWorkService, "importWarehouseRoadWork", list);


        PowerMockito.when(warehouseRoadWorkRepository.getLocTotal(Mockito.any())).thenReturn(1);
        WarehouseRoadWorkDTO dto2 = new WarehouseRoadWorkDTO();
        dto2.setWhseid("WMWHSE1");
        dto2.setLoc("11AA01A");
        list.add(dto2);
        Whitebox.invokeMethod(warehouseRoadWorkService, "importWarehouseRoadWork", list);

        list.clear();
        list.add(dto);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkCount(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "importWarehouseRoadWork", list);

        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkCount(Mockito.any())).thenReturn(0);
        PowerMockito.when(warehouseRoadWorkRepository.saveWarehouseRoadWorkList(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseRoadWorkService, "importWarehouseRoadWork", list);

        Assert.assertNotNull(dto);
    }

    @Test
    public void exportWarehouseRoadWork() throws Exception {

        WarehouseRoadWorkDTO dto = new WarehouseRoadWorkDTO();
        dto.setWhseid("WMWHSE1");
        dto.setLoc("11AA01A");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseRoadWorkRepository.getWarehouseRoadWorkList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseRoadWorkService, "exportWarehouseRoadWork", dto);

        Assert.assertNotNull(dto);
    }

}
