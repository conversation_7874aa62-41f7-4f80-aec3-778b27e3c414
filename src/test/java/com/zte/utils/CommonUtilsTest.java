/*Started by AICoder, pid:4f8622f28fl836e140fa0bee20115e5b65c1fede*/
package com.zte.utils;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class CommonUtilsTest {

    @Mock
    private ZoneId mockZoneId;

    @InjectMocks
    private CommonUtils commonUtils;

    @Mock
    private ThreadLocalRandom threadLocalRandom;

    @Test
    public void testGenerateTimestamp() {
        // Arrange
        String expectedPattern = "yyyyMMddHHmmssSSS";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(expectedPattern).withZone(mockZoneId);

        LocalDateTime now = LocalDateTime.now();
        String expectedTimestamp = now.format(formatter);

        // Act
        String actualTimestamp = CommonUtils.generateTimestamp();

        // Assert
        assertEquals(expectedTimestamp.substring(0, 10), CommonUtils.generateTimestamp().substring(0, 10));
    }

    /*Started by AICoder, pid:ic927a317f7741814a9809c790ac9654e4d11205*/
    @Test
    public void testGenerate10DigitNumber() {
        // Given
        when(threadLocalRandom.nextInt(eq(10))).thenReturn(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);

        // When
        String result = CommonUtils.generate10DigitNumber();

        // Then
        assertNotNull(result);
        assertEquals(10, result.length());
        assertTrue(result.matches("[0-9]{10}"));
    }

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }
    /*Ended by AICoder, pid:ic927a317f7741814a9809c790ac9654e4d11205*/
}
/*Ended by AICoder, pid:4f8622f28fl836e140fa0bee20115e5b65c1fede*/