package com.zte.interfaces.cbom;

import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.ThreadUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * packageName com.zte.interfaces.cbom
 *
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/4/30
 */
@RestController
@Validated
@RequestMapping(value = "fixBomCommonController")
public class FixBomCommonController {
    @Autowired
    private FixBomCommonService fixBomCommonService;

    @ApiOperation("定时计算FixBom")
    @TransmittableHeader
    @GetMapping(value = "refreshFixBomId")
    public ServiceData<?> refreshFixBomId(@RequestParam(required = false, value = "async") String async) {
        if (Constant.FLAG_Y.equals(async)) {
            ThreadUtil.EXECUTOR.submit(() -> fixBomCommonService.refreshFixBomId());
        } else {
            fixBomCommonService.refreshFixBomId();
        }
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("删除FixBom头+FirBom详情")
    @DeleteMapping(value = "deleteFixBomHeadAndDetailById")
    public ServiceData<?> deleteFixBomHeadAndDetailById(@Size(max = 50, message = "数组最大50") @RequestBody List<String> idList) {
        fixBomCommonService.deleteFixBomHeadAndDetailById(idList);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("通过fixBomId查询明细")
    @GetMapping(value = "queryFixBomDetailByFixBomId")
    @OpenApi(consumer = {"mes"}, describe = "通过fixBomId查询明细", name = "queryFixBomDetailByFixBomId")
    public ServiceData<?> queryFixBomDetailByFixBomId(@RequestParam(name = "fixBomId") String fixBomId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryFixBomDetailByFixBomId(fixBomId));
    }

    @ApiOperation("通过headId查询明细")
    @GetMapping(value = "queryFixBomDetailByHeadId")
    @OpenApi(consumer = {"mes"}, describe = "通过headId查询明细", name = "queryFixBomDetailByHeadId")
    public ServiceData<?> queryFixBomDetailByHeadId(@RequestParam(name = "headId") String headId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryFixBomDetailByHeadId(headId));
    }

    @ApiOperation("根据任务号重新生成fixBom数据")
    @GetMapping(value = "reCreateFixBomByTaskNo")
    public ServiceData<?> reCreateFixBomByTaskNo(@RequestParam(name = "taskNo") String taskNo) {
        fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("根据任务号生成fixBom数据")
    @GetMapping(value = "createFixBomByTaskNo")
    public ServiceData<?> createFixBomByTaskNo(@RequestParam(name = "taskNo") String taskNo) {
        fixBomCommonService.createFixBomByTaskNo(taskNo);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("获取mbom树状结构")
    @GetMapping(value = "queryTreeNodeByFixBomId")
    public ServiceData<?> queryTreeNodeByFixBomId(@RequestParam(name = "fixBomId") String fixBomId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryTreeNodeByFixBomId(fixBomId));
    }

    @ApiOperation("获取mbom树状结构")
    @GetMapping(value = "queryTreeNodeByHeadId")
    public ServiceData<?> queryTreeNodeByHeadId(@RequestParam(name = "headId") String headId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryTreeNodeByHeadId(headId));
    }

    @ApiOperation("获取mbom树状结构-修正用量")
    @GetMapping(value = "adjustItemNumberByTaskNo")
    public ServiceData<?> adjustItemNumberByTaskNo(@RequestParam(name = "taskNo") String taskNo) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.adjustItemNumberByTaskNo(taskNo));
    }

    @ApiOperation("根据任务号获取fixBom详情")
    @GetMapping(value = "getFixBomByTaskNo")
    public ServiceData<?> getFixBomByTaskNo(@RequestParam(name = "taskNo") String taskNo) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.getFixBomByTaskNo(taskNo));
    }

    @ApiOperation("根据任务号或fixbomid获取fixBom")
    @GetMapping("queryFixBomByTaskNoOrFixBomId")
    public ServiceData<List<FixBomDetailDTO>> queryFixBomByTaskNoOrFixBomId(@RequestParam(name = "taskNo", required = false) String taskNo, @RequestParam(name = "fixBomId", required = false) String fixBomId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryFixBomByTaskNoOrFixBomId(taskNo, fixBomId));
    }

    @ApiOperation("根据任务号或headid获取fixBom")
    @GetMapping("queryFixBomByTaskNoOrHeadId")
    public ServiceData<List<FixBomDetailDTO>> queryFixBomByTaskNoOrHeadId(@RequestParam(name = "taskNo", required = false) String taskNo, @RequestParam(name = "headId", required = false) String headId) {
        return ServiceDataBuilderUtil.success(fixBomCommonService.queryFixBomByTaskNoOrHeadId(taskNo, headId));
    }

}
