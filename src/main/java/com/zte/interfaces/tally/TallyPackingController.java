package com.zte.interfaces.tally;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.tally.TallyPackingService;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import com.zte.interfaces.tally.dto.InterfaceResponseDTO;
import com.zte.interfaces.tally.dto.TallyDTO;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.Api;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/datawb/tally")
@Api(tags = "理货打包查询", description="TallyPackingController")
public class TallyPackingController {
	
	@Autowired
	private TallyPackingService tallyPackingService;
	/**
     * 货运查询理货信息
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @RequestMapping(value = "/query",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData query(HttpServletRequest request, @RequestBody TallyDTO dto) {
    	InterfaceResponseDTO resultDto=tallyPackingService.getInterfaceResponse(dto.getSourceNo());
        return ServiceDataUtil.getSuccess(resultDto);
    }
}
