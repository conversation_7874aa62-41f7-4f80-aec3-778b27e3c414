package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * 指令编号
     */
    @ApiModelProperty(value = "指令编号")
    private String orderNo;

    /**
     * 提单号/快递单号
     */
    @ApiModelProperty("提单号/快递单号")
    private String ladingBillNo;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 文本合同号
     */
    @ApiModelProperty(value = "文本合同号")
    private String contractNoText;

    /**
     * 指令类型
     */
    @ApiModelProperty(value = "指令类型")
    private String orderType;

    /**
     * 产品大类
     */
    @ApiModelProperty(value = "产品大类")
    private String productTypeLevelOne;

    /**
     * 产品小类
     */
    @ApiModelProperty(value = "产品小类")
    private String productTypeLevelTwo;

    /**
     * 业务区域
     */
    @ApiModelProperty(value = "业务区域")
    private String businessRegion;

    /**
     * 业务场景大类
     */
    @ApiModelProperty(value = "业务场景大类")
    private String businessType;

    /**
     * 业务场景小类
     */
    @ApiModelProperty(value = "业务场景小类")
    private String businessTypeLevelTwo;

    /**
     * 指令状态
     */
    @ApiModelProperty(value = "指令状态")
    private String orderStatus;

    /**
     * 指令状态文字
     */
    @ApiModelProperty(value = "指令状态文字")
    private String orderStatusName;

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceNo;

    /**
     * 费用归属公司
     */
    @ApiModelProperty(value = "费用归属公司")
    private String feeBelongCompany;

    /**
     * 费用归属部门
     */
    @ApiModelProperty(value = "费用归属部门")
    private String feeBelongDepartment;

    /**
     * 要求运输方式大类
     */
    @ApiModelProperty(value = "要求运输方式大类")
    private String requireTransportTypeLevelOne;

    /**
     * 要求运输方式小类
     */
    @ApiModelProperty(value = "要求运输方式小类")
    private String requireTransportTypeLevelTwo;

    /**
     * 要求发货日期
     */
    @ApiModelProperty(value = "要求发货日期")
    private Date requireDeliveryDate;

    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    private Date requireArrivalDate;

    /**
     * 发货金额
     */
    @ApiModelProperty(value = "发货金额")
    private BigDecimal deliveryAmount;

    /**
     * 发货币种
     */
    @ApiModelProperty(value = "发货币种")
    private String deliveryCurrency;

    /**
     * 托盘数
     */
    @ApiModelProperty(value = "托盘数")
    private Integer palletNum;

    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private Integer boxNum;

    /**
     * 件数
     */
    @ApiModelProperty(value = "件数")
    private Integer pieceNum;

    /**
     * 是否紧急发货,0否，1是
     */
    @ApiModelProperty(value = "是否紧急发货,0否，1是")
    private String urgentDispatchFlag;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 体积
     */
    @ApiModelProperty(value = "体积")
    private BigDecimal volume;

    /**
     * 是否为危险品,0否，1是
     */
    @ApiModelProperty(value = "是否为危险品,0否，1是")
    private String dangerousFlag;

    /**
     * 始发地编号
     */
    @ApiModelProperty(value = "始发地编号")
    private String shippingFromPlaceNo;

    /**
     * 目的地编号
     */
    @ApiModelProperty(value = "目的地编号")
    private String shippingToPlaceNo;

    /**
     * 发货人名称
     */
    @ApiModelProperty(value = "发货人名称")
    private String consignorName;

    /**
     * 发货人联系方式
     */
    @ApiModelProperty(value = "发货人联系方式")
    private String consignorContact;

    /**
     * 收货人名称
     */
    @ApiModelProperty(value = "收货人名称")
    private String consigneeName;

    /**
     * 收货人单位
     */
    @ApiModelProperty(value = "收货人单位")
    private String consigneeCompany;

    /**
     * 收货人联系方式
     */
    @ApiModelProperty(value = "收货人联系方式")
    private String consigneeContact;

    /**
     * 制单人
     */
    @ApiModelProperty(value = "制单人")
    private String createdOrderBy;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 交单方式
     */
    @ApiModelProperty(value = "交单方式")
    private String sumbitOrderType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 特殊备注
     */
    @ApiModelProperty(value = "特殊备注")
    private String specialRequirement;

    /**
     * 指令创建方式
     */
    @ApiModelProperty(value = "指令创建方式")
    private String orderCreateType;

    /**
     * 指令接口来源
     */
    @ApiModelProperty(value = "指令接口来源")
    private String orderInterfaceSource;

    /**
     * 退回原因
     */
    @ApiModelProperty(value = "退回原因")
    private String returnReason;
    
    /**
     * 是否商检，0否，1是
     */
    @ApiModelProperty("是否商检，0否，1是")
    private String businessCheckFlag;

    /**
     * 商检机构
     */
    @ApiModelProperty(value = "商检机构")
    private String businessCheckOrg;

    /**
     * 商检预约号
     */
    @ApiModelProperty(value = "商检预约号")
    private String businessCheckBookNo;

    /**
     * 所属组织id
     */
    @ApiModelProperty(value = "所属组织id")
    private Long orgId;

    /**
     * 制单人姓名
     */
    @ApiModelProperty(value = "制单人姓名")
    private String createdOrderByName;

    /**
     * 制单日期
     */
    @ApiModelProperty(value = "制单日期")
    private String createdOrderDate;

    /**
     * 运输服务等级
     */
    @ApiModelProperty("运输服务等级")
    private String shippingServiceLevel;

    /**
     * 追踪单号
     */
    @ApiModelProperty("追踪单号")
    private String trackNo;

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getLadingBillNo() {
		return ladingBillNo;
	}

	public void setLadingBillNo(String ladingBillNo) {
		this.ladingBillNo = ladingBillNo;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getContractNoText() {
		return contractNoText;
	}

	public void setContractNoText(String contractNoText) {
		this.contractNoText = contractNoText;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getProductTypeLevelOne() {
		return productTypeLevelOne;
	}

	public void setProductTypeLevelOne(String productTypeLevelOne) {
		this.productTypeLevelOne = productTypeLevelOne;
	}

	public String getProductTypeLevelTwo() {
		return productTypeLevelTwo;
	}

	public void setProductTypeLevelTwo(String productTypeLevelTwo) {
		this.productTypeLevelTwo = productTypeLevelTwo;
	}

	public String getBusinessRegion() {
		return businessRegion;
	}

	public void setBusinessRegion(String businessRegion) {
		this.businessRegion = businessRegion;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getBusinessTypeLevelTwo() {
		return businessTypeLevelTwo;
	}

	public void setBusinessTypeLevelTwo(String businessTypeLevelTwo) {
		this.businessTypeLevelTwo = businessTypeLevelTwo;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getOrderStatusName() {
		return orderStatusName;
	}

	public void setOrderStatusName(String orderStatusName) {
		this.orderStatusName = orderStatusName;
	}

	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	public String getFeeBelongCompany() {
		return feeBelongCompany;
	}

	public void setFeeBelongCompany(String feeBelongCompany) {
		this.feeBelongCompany = feeBelongCompany;
	}

	public String getFeeBelongDepartment() {
		return feeBelongDepartment;
	}

	public void setFeeBelongDepartment(String feeBelongDepartment) {
		this.feeBelongDepartment = feeBelongDepartment;
	}

	public String getRequireTransportTypeLevelOne() {
		return requireTransportTypeLevelOne;
	}

	public void setRequireTransportTypeLevelOne(String requireTransportTypeLevelOne) {
		this.requireTransportTypeLevelOne = requireTransportTypeLevelOne;
	}

	public String getRequireTransportTypeLevelTwo() {
		return requireTransportTypeLevelTwo;
	}

	public void setRequireTransportTypeLevelTwo(String requireTransportTypeLevelTwo) {
		this.requireTransportTypeLevelTwo = requireTransportTypeLevelTwo;
	}

	public Date getRequireDeliveryDate() {
		return requireDeliveryDate;
	}

	public void setRequireDeliveryDate(Date requireDeliveryDate) {
		this.requireDeliveryDate = requireDeliveryDate;
	}

	public Date getRequireArrivalDate() {
		return requireArrivalDate;
	}

	public void setRequireArrivalDate(Date requireArrivalDate) {
		this.requireArrivalDate = requireArrivalDate;
	}

	public BigDecimal getDeliveryAmount() {
		return deliveryAmount;
	}

	public void setDeliveryAmount(BigDecimal deliveryAmount) {
		this.deliveryAmount = deliveryAmount;
	}

	public String getDeliveryCurrency() {
		return deliveryCurrency;
	}

	public void setDeliveryCurrency(String deliveryCurrency) {
		this.deliveryCurrency = deliveryCurrency;
	}

	public Integer getPalletNum() {
		return palletNum;
	}

	public void setPalletNum(Integer palletNum) {
		this.palletNum = palletNum;
	}

	public Integer getBoxNum() {
		return boxNum;
	}

	public void setBoxNum(Integer boxNum) {
		this.boxNum = boxNum;
	}

	public Integer getPieceNum() {
		return pieceNum;
	}

	public void setPieceNum(Integer pieceNum) {
		this.pieceNum = pieceNum;
	}

	public String getUrgentDispatchFlag() {
		return urgentDispatchFlag;
	}

	public void setUrgentDispatchFlag(String urgentDispatchFlag) {
		this.urgentDispatchFlag = urgentDispatchFlag;
	}

	public BigDecimal getWeight() {
		return weight;
	}

	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public String getDangerousFlag() {
		return dangerousFlag;
	}

	public void setDangerousFlag(String dangerousFlag) {
		this.dangerousFlag = dangerousFlag;
	}

	public String getShippingFromPlaceNo() {
		return shippingFromPlaceNo;
	}

	public void setShippingFromPlaceNo(String shippingFromPlaceNo) {
		this.shippingFromPlaceNo = shippingFromPlaceNo;
	}

	public String getShippingToPlaceNo() {
		return shippingToPlaceNo;
	}

	public void setShippingToPlaceNo(String shippingToPlaceNo) {
		this.shippingToPlaceNo = shippingToPlaceNo;
	}

	public String getConsignorName() {
		return consignorName;
	}

	public void setConsignorName(String consignorName) {
		this.consignorName = consignorName;
	}

	public String getConsignorContact() {
		return consignorContact;
	}

	public void setConsignorContact(String consignorContact) {
		this.consignorContact = consignorContact;
	}

	public String getConsigneeName() {
		return consigneeName;
	}

	public void setConsigneeName(String consigneeName) {
		this.consigneeName = consigneeName;
	}

	public String getConsigneeCompany() {
		return consigneeCompany;
	}

	public void setConsigneeCompany(String consigneeCompany) {
		this.consigneeCompany = consigneeCompany;
	}

	public String getConsigneeContact() {
		return consigneeContact;
	}

	public void setConsigneeContact(String consigneeContact) {
		this.consigneeContact = consigneeContact;
	}

	public String getCreatedOrderBy() {
		return createdOrderBy;
	}

	public void setCreatedOrderBy(String createdOrderBy) {
		this.createdOrderBy = createdOrderBy;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getSumbitOrderType() {
		return sumbitOrderType;
	}

	public void setSumbitOrderType(String sumbitOrderType) {
		this.sumbitOrderType = sumbitOrderType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSpecialRequirement() {
		return specialRequirement;
	}

	public void setSpecialRequirement(String specialRequirement) {
		this.specialRequirement = specialRequirement;
	}

	public String getOrderCreateType() {
		return orderCreateType;
	}

	public void setOrderCreateType(String orderCreateType) {
		this.orderCreateType = orderCreateType;
	}

	public String getOrderInterfaceSource() {
		return orderInterfaceSource;
	}

	public void setOrderInterfaceSource(String orderInterfaceSource) {
		this.orderInterfaceSource = orderInterfaceSource;
	}

	public String getReturnReason() {
		return returnReason;
	}

	public void setReturnReason(String returnReason) {
		this.returnReason = returnReason;
	}

	public String getBusinessCheckFlag() {
		return businessCheckFlag;
	}

	public void setBusinessCheckFlag(String businessCheckFlag) {
		this.businessCheckFlag = businessCheckFlag;
	}

	public String getBusinessCheckOrg() {
		return businessCheckOrg;
	}

	public void setBusinessCheckOrg(String businessCheckOrg) {
		this.businessCheckOrg = businessCheckOrg;
	}

	public String getBusinessCheckBookNo() {
		return businessCheckBookNo;
	}

	public void setBusinessCheckBookNo(String businessCheckBookNo) {
		this.businessCheckBookNo = businessCheckBookNo;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getCreatedOrderByName() {
		return createdOrderByName;
	}

	public void setCreatedOrderByName(String createdOrderByName) {
		this.createdOrderByName = createdOrderByName;
	}

	public String getCreatedOrderDate() {
		return createdOrderDate;
	}

	public void setCreatedOrderDate(String createdOrderDate) {
		this.createdOrderDate = createdOrderDate;
	}

	public String getShippingServiceLevel() {
		return shippingServiceLevel;
	}

	public void setShippingServiceLevel(String shippingServiceLevel) {
		this.shippingServiceLevel = shippingServiceLevel;
	}

	public String getTrackNo() {
		return trackNo;
	}

	public void setTrackNo(String trackNo) {
		this.trackNo = trackNo;
	}
    
    
}
