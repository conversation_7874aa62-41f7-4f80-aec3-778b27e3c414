package com.zte.interfaces.tally.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令-条款信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderClauseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long clauseId;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * D条款送货地编号
     */
    @ApiModelProperty(value = "D条款送货地编号")
    private String dDeliveryPlaceNo;

    /**
     * F条款代理人联系姓名
     */
    @ApiModelProperty(value = "F条款代理人联系姓名")
    private String fClauseAgentName;

    /**
     * F条款代理人联系方式
     */
    @ApiModelProperty(value = "F条款代理人联系方式")
    private String fClauseAgentContact;

    /**
     * 通知人名称
     */
    @ApiModelProperty(value = "通知人名称")
    private String notifierName;

    /**
     * 通知人联系方式
     */
    @ApiModelProperty(value = "通知人联系方式")
    private String notifierContact;

    /**
     * 清关文件寄送地编码
     */
    @ApiModelProperty(value = "清关文件寄送地编码")
    private String sendPlaceNo;
}
