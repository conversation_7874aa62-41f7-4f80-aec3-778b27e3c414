package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class TallyDetailDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String sourceNo;
	
	private BigDecimal bulkWeight;
	
	private BigDecimal bulkVolume;
	
	private String containerNo;
	
	private String parentContainerNo;
	
	private String startNo;

	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	public BigDecimal getBulkWeight() {
		return bulkWeight;
	}

	public void setBulkWeight(BigDecimal bulkWeight) {
		this.bulkWeight = bulkWeight;
	}

	public BigDecimal getBulkVolume() {
		return bulkVolume;
	}

	public void setBulkVolume(BigDecimal bulkVolume) {
		this.bulkVolume = bulkVolume;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public String getParentContainerNo() {
		return parentContainerNo;
	}

	public void setParentContainerNo(String parentContainerNo) {
		this.parentContainerNo = parentContainerNo;
	}

	public String getStartNo() {
		return startNo;
	}

	public void setStartNo(String startNo) {
		this.startNo = startNo;
	}
	
	
	 

}
