package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by 6051000080 on 2019/12/18.
 */
@Data
public class InterfaceOrderResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("返回结果")
    private Boolean retFlag;

    @ApiModelProperty("指令ID")
    private Long orderId;

    @ApiModelProperty("指令编号")
    private String orderNo;

    @ApiModelProperty("托盘id列表")
    private List<Long> palletIdList;

    @ApiModelProperty("箱id列表")
    private List<Long> boxIdList;

}
