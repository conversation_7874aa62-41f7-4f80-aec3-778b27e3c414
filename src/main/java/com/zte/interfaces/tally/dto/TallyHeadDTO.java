package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class TallyHeadDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String sourceNo;
	
	private String feeBelongDepartment;
	
	private Integer boxNum;
	
	private int pieceNum;
	
	private BigDecimal weight;
	
	private BigDecimal volume;
	
	/**
	 * 源仓
	 * */
	private String shippingFromPlaceNo;
	
	/**
	 * 目的仓
	 * */
	private String shippingToPlaceNo;
	
	private String createdOrderBy;
	
	private String createdOrderByName;
	
	private String createdOrderDate;
	
	/**
	 * 箱袋
	 * */
	private String packageTypeName;
	
	private String shippingToPlaceText;
	
	private String consignorName;
	
	private String consignorContact;
	
	private String consigneeName;
	
	private String consigneeCompany;
	
	private String consigneeContact;
	
	private String shippingType;
	
	private String sourceRepository;

	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	public Integer getBoxNum() {
		return boxNum;
	}

	public void setBoxNum(Integer boxNum) {
		this.boxNum = boxNum;
	}

	public int getPieceNum() {
		return pieceNum;
	}

	public void setPieceNum(int pieceNum) {
		this.pieceNum = pieceNum;
	}

	public BigDecimal getWeight() {
		return weight;
	}

	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public String getShippingFromPlaceNo() {
		return shippingFromPlaceNo;
	}

	public void setShippingFromPlaceNo(String shippingFromPlaceNo) {
		this.shippingFromPlaceNo = shippingFromPlaceNo;
	}

	public String getShippingToPlaceNo() {
		return shippingToPlaceNo;
	}

	public void setShippingToPlaceNo(String shippingToPlaceNo) {
		this.shippingToPlaceNo = shippingToPlaceNo;
	}

	public String getCreatedOrderBy() {
		return createdOrderBy;
	}

	public void setCreatedOrderBy(String createdOrderBy) {
		this.createdOrderBy = createdOrderBy;
	}

	public String getCreatedOrderByName() {
		return createdOrderByName;
	}

	public void setCreatedOrderByName(String createdOrderByName) {
		this.createdOrderByName = createdOrderByName;
	}

	public String getCreatedOrderDate() {
		return createdOrderDate;
	}

	public void setCreatedOrderDate(String createdOrderDate) {
		this.createdOrderDate = createdOrderDate;
	}

	public String getPackageTypeName() {
		return packageTypeName;
	}

	public void setPackageTypeName(String packageTypeName) {
		this.packageTypeName = packageTypeName;
	}

	public String getFeeBelongDepartment() {
		return feeBelongDepartment;
	}

	public void setFeeBelongDepartment(String feeBelongDepartment) {
		this.feeBelongDepartment = feeBelongDepartment;
	}

	public String getShippingToPlaceText() {
		return shippingToPlaceText;
	}

	public void setShippingToPlaceText(String shippingToPlaceText) {
		this.shippingToPlaceText = shippingToPlaceText;
	}

	public String getConsignorName() {
		return consignorName;
	}

	public void setConsignorName(String consignorName) {
		this.consignorName = consignorName;
	}

	public String getConsignorContact() {
		return consignorContact;
	}

	public void setConsignorContact(String consignorContact) {
		this.consignorContact = consignorContact;
	}

	public String getConsigneeName() {
		return consigneeName;
	}

	public void setConsigneeName(String consigneeName) {
		this.consigneeName = consigneeName;
	}

	public String getConsigneeCompany() {
		return consigneeCompany;
	}

	public void setConsigneeCompany(String consigneeCompany) {
		this.consigneeCompany = consigneeCompany;
	}

	public String getConsigneeContact() {
		return consigneeContact;
	}

	public void setConsigneeContact(String consigneeContact) {
		this.consigneeContact = consigneeContact;
	}

	public String getShippingType() {
		return shippingType;
	}

	public void setShippingType(String shippingType) {
		this.shippingType = shippingType;
	}

	public String getSourceRepository() {
		return sourceRepository;
	}

	public void setSourceRepository(String sourceRepository) {
		this.sourceRepository = sourceRepository;
	}  

}
