package com.zte.interfaces.tally.dto;

import java.util.List;

public class BoxQtyDTO {
	
	private String startNo;
	
    private int boxNum;
    
    private int pieceNum;
    
    private List<TallyDetailDTO> tallyDetailDTOs;
    
    private List<BoxDetailDTO> boxDetailDTOs; 

	public String getStartNo() {
		return startNo;
	}

	public void setStartNo(String startNo) {
		this.startNo = startNo;
	}

	public int getBoxNum() {
		return boxNum;
	}

	public void setBoxNum(int boxNum) {
		this.boxNum = boxNum;
	}

	public int getPieceNum() {
		return pieceNum;
	}

	public void setPieceNum(int pieceNum) {
		this.pieceNum = pieceNum;
	}

	public List<TallyDetailDTO> getTallyDetailDTOs() {
		return tallyDetailDTOs;
	}

	public void setTallyDetailDTOs(List<TallyDetailDTO> tallyDetailDTOs) {
		this.tallyDetailDTOs = tallyDetailDTOs;
	}

	public List<BoxDetailDTO> getBoxDetailDTOs() {
		return boxDetailDTOs;
	}

	public void setBoxDetailDTOs(List<BoxDetailDTO> boxDetailDTOs) {
		this.boxDetailDTOs = boxDetailDTOs;
	}
	
	
    
    
}
