package com.zte.interfaces.tally.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令-容器明细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@Data
public class InterfaceFreightOrderContainerItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long itemId;

    /**
     * 容器id
     */
    @ApiModelProperty(value = "容器id")
    private Long containerId;

    /**
     * 容器号
     */
    @ApiModelProperty(value = "容器号")
    private String containerNo;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private String materialId;

    /**
     * 物料代码
     */
    @ApiModelProperty(value = "物料代码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 件数
     */
    @ApiModelProperty(value = "件数")
    private Integer piece;

    /**
     * 是否战略物资，0否，1是
     */
    @ApiModelProperty(value = "是否战略物资，0否，1是")
    private String strategicMaterialFlag;

    /**
     * 是否危险品，0否，1是
     */
    @ApiModelProperty(value = "是否危险品，0否，1是")
    private String dangerousFlag;

    /**
     * 危险品类别
     */
    @ApiModelProperty(value = "危险品类别")
    private String dangerousCategory;

    /**
     * 危险品代码
     */
    @ApiModelProperty(value = "危险品代码")
    private String dangerousCode;

    /**
     * 电池标签类别
     */
    @ApiModelProperty(value = "电池标签类别")
    private String batteryTagType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * 产品区分标识
     */
    @ApiModelProperty(value = "产品区分标识")
    private String productIdentify;

    /**
     * UUID(物料拆分号)
     */
    @ApiModelProperty(value = "UUID(物料拆分号)")
    private String materialSplitNo;

    /**
     * 机型代码
     */
    @ApiModelProperty(value = "机型代码")
    private String modelCode;

    /**
     * 是否享惠
     */
    @ApiModelProperty(value = "是否享惠")
    private String dicountFlag;

    /**
     * EKY产品描述
     */
    @ApiModelProperty(value = "EKY产品描述")
    private String productDesc;

    /**
     * 申报颗粒度
     */
    @ApiModelProperty(value = "申报颗粒度")
    private String applyGranularity;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String specificationsCode;

    /**
     * 原产国
     */
    @ApiModelProperty(value = "原产国")
    private String countryOfOrigin;

    /**
     * IMEI号，逗号隔开
     */
    @ApiModelProperty(value = "IMEI号，逗号隔开")
    private String barCodeStr;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public Long getContainerId() {
		return containerId;
	}

	public void setContainerId(Long containerId) {
		this.containerId = containerId;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public String getMaterialId() {
		return materialId;
	}

	public void setMaterialId(String materialId) {
		this.materialId = materialId;
	}

	public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public String getMaterialName() {
		return materialName;
	}

	public void setMaterialName(String materialName) {
		this.materialName = materialName;
	}

	public Integer getPiece() {
		return piece;
	}

	public void setPiece(Integer piece) {
		this.piece = piece;
	}

	public String getStrategicMaterialFlag() {
		return strategicMaterialFlag;
	}

	public void setStrategicMaterialFlag(String strategicMaterialFlag) {
		this.strategicMaterialFlag = strategicMaterialFlag;
	}

	public String getDangerousFlag() {
		return dangerousFlag;
	}

	public void setDangerousFlag(String dangerousFlag) {
		this.dangerousFlag = dangerousFlag;
	}

	public String getDangerousCategory() {
		return dangerousCategory;
	}

	public void setDangerousCategory(String dangerousCategory) {
		this.dangerousCategory = dangerousCategory;
	}

	public String getDangerousCode() {
		return dangerousCode;
	}

	public void setDangerousCode(String dangerousCode) {
		this.dangerousCode = dangerousCode;
	}

	public String getBatteryTagType() {
		return batteryTagType;
	}

	public void setBatteryTagType(String batteryTagType) {
		this.batteryTagType = batteryTagType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getProductIdentify() {
		return productIdentify;
	}

	public void setProductIdentify(String productIdentify) {
		this.productIdentify = productIdentify;
	}

	public String getMaterialSplitNo() {
		return materialSplitNo;
	}

	public void setMaterialSplitNo(String materialSplitNo) {
		this.materialSplitNo = materialSplitNo;
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public String getDicountFlag() {
		return dicountFlag;
	}

	public void setDicountFlag(String dicountFlag) {
		this.dicountFlag = dicountFlag;
	}

	public String getProductDesc() {
		return productDesc;
	}

	public void setProductDesc(String productDesc) {
		this.productDesc = productDesc;
	}

	public String getApplyGranularity() {
		return applyGranularity;
	}

	public void setApplyGranularity(String applyGranularity) {
		this.applyGranularity = applyGranularity;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getSpecificationsCode() {
		return specificationsCode;
	}

	public void setSpecificationsCode(String specificationsCode) {
		this.specificationsCode = specificationsCode;
	}

	public String getCountryOfOrigin() {
		return countryOfOrigin;
	}

	public void setCountryOfOrigin(String countryOfOrigin) {
		this.countryOfOrigin = countryOfOrigin;
	}

	public String getBarCodeStr() {
		return barCodeStr;
	}

	public void setBarCodeStr(String barCodeStr) {
		this.barCodeStr = barCodeStr;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

}
