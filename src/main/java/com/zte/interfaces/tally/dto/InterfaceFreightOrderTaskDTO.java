package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令-任务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderTaskDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 来源单号
     */
    @ApiModelProperty("来源单号")
    private String sourceNo;

    /**
     * 主键id
     */
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 任务编号
     */
    @ApiModelProperty("任务编号")
    private String taskNo;

    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 指令id
     */
    @ApiModelProperty("指令id")
    private Long orderId;

    /**
     * 任务状态
     */
    @ApiModelProperty("任务状态")
    private String taskStatus;

    /**
     * 任务描述
     */
    @ApiModelProperty("任务描述")
    private String taskDescription;

    /**
     * 预估重量
     */
    @ApiModelProperty("预估重量")
    private BigDecimal evaluateWeight;

    /**
     * 预估体积
     */
    @ApiModelProperty("预估体积")
    private BigDecimal evaluateVolume;

    /**
     * 实际重量
     */
    @ApiModelProperty("实际重量")
    private BigDecimal actualWeight;

    /**
     * 实际体积
     */
    @ApiModelProperty("实际体积")
    private BigDecimal actualVolume;

    /**
     * 托盘数
     */
    @ApiModelProperty("托盘数")
    private Integer palletNum;

    /**
     * 箱数
     */
    @ApiModelProperty("箱数")
    private Integer boxNum;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer piece;

    /**
     * 未发货箱数
     */
    @ApiModelProperty("未发货箱数")
    private Integer unShipBoxNum;

    /**
     * 已发货箱数
     */
    @ApiModelProperty("已发货箱数")
    private Integer shippedBoxNum;

    /**
     * 生产单位
     */
    @ApiModelProperty("生产单位")
    private String productionUnit;

    /**
     * 预计入库时间
     */
    @ApiModelProperty("预计入库时间")
    private Date evaluateLoadInTime;


}
