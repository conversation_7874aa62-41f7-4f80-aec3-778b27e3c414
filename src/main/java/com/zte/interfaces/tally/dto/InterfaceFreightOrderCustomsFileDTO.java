package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令关务文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderCustomsFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 来源单号
     */
    @ApiModelProperty("来源单号")
    private String sourceNo;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long fileId;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件数量
     */
    @ApiModelProperty(value = "文件数量")
    private Integer fileNum;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
}
