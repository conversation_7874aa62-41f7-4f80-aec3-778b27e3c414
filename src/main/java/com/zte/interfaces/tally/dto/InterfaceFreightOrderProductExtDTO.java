package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 前台-成品指令扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderProductExtDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty("扩展id")
    private Long extId;

    /**
     * 指令id
     */
    @ApiModelProperty("指令id")
    private Long orderId;

    /**
     * 预计入库日期
     */
    @ApiModelProperty("预计入库日期")
    private Date expectAcceptDate;

    /**
     * 是否集货，0否，1是
     */
    @ApiModelProperty("是否集货，0否，1是")
    private String collectionFlag;

    /**
     * 是否完成集货，0否，1是
     */
    @ApiModelProperty("是否完成集货，0否，1是")
    private String collectionCompleteFlag;

    /**
     * 完成集货时间
     */
    @ApiModelProperty("完成集货时间")
    private Date collectionCompleteTime;

    /**
     * 集货指令号
     */
    @ApiModelProperty("集货指令号")
    private String collectionOrderNo;

    /**
     * 集货区域
     */
    @ApiModelProperty("集货区域")
    private String collectionArea;


}
