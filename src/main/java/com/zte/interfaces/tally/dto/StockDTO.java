package com.zte.interfaces.tally.dto;

import java.io.Serializable;

public class StockDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String stockName;
	
	private String areaName;
	
	private String address;
	
	private String receiptBy;
	
	private String telephone;
	
	private String receiptClass;
	
	private String mainReceiptBy;
	
	private String enableFlag;

	public String getStockName() {
		return stockName;
	}

	public void setStockName(String stockName) {
		this.stockName = stockName;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getReceiptBy() {
		return receiptBy;
	}

	public void setReceiptBy(String receiptBy) {
		this.receiptBy = receiptBy;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getReceiptClass() {
		return receiptClass;
	}

	public void setReceiptClass(String receiptClass) {
		this.receiptClass = receiptClass;
	}

	public String getMainReceiptBy() {
		return mainReceiptBy;
	}

	public void setMainReceiptBy(String mainReceiptBy) {
		this.mainReceiptBy = mainReceiptBy;
	}

	public String getEnableFlag() {
		return enableFlag;
	}

	public void setEnableFlag(String enableFlag) {
		this.enableFlag = enableFlag;
	}  

}
