package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令-容器信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderContainerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long containerId;

    /**
     * 上游系统容器id
     */
    @ApiModelProperty(value = "上游系统容器id")
    private String sourceContainerId;

    public Long getContainerId() {
		return containerId;
	}

	public void setContainerId(Long containerId) {
		this.containerId = containerId;
	}

	public String getSourceContainerId() {
		return sourceContainerId;
	}

	public void setSourceContainerId(String sourceContainerId) {
		this.sourceContainerId = sourceContainerId;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public Long getParentContainerId() {
		return parentContainerId;
	}

	public void setParentContainerId(Long parentContainerId) {
		this.parentContainerId = parentContainerId;
	}

	public String getParentContainerNo() {
		return parentContainerNo;
	}

	public void setParentContainerNo(String parentContainerNo) {
		this.parentContainerNo = parentContainerNo;
	}

	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getTaskNo() {
		return taskNo;
	}

	public void setTaskNo(String taskNo) {
		this.taskNo = taskNo;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getPalletFlag() {
		return palletFlag;
	}

	public void setPalletFlag(String palletFlag) {
		this.palletFlag = palletFlag;
	}

	public String getMainContainerFlag() {
		return mainContainerFlag;
	}

	public void setMainContainerFlag(String mainContainerFlag) {
		this.mainContainerFlag = mainContainerFlag;
	}

	public BigDecimal getLength() {
		return length;
	}

	public void setLength(BigDecimal length) {
		this.length = length;
	}

	public BigDecimal getWidth() {
		return width;
	}

	public void setWidth(BigDecimal width) {
		this.width = width;
	}

	public BigDecimal getHeight() {
		return height;
	}

	public void setHeight(BigDecimal height) {
		this.height = height;
	}

	public BigDecimal getBulkVolume() {
		return bulkVolume;
	}

	public void setBulkVolume(BigDecimal bulkVolume) {
		this.bulkVolume = bulkVolume;
	}

	public BigDecimal getRealVolume() {
		return realVolume;
	}

	public void setRealVolume(BigDecimal realVolume) {
		this.realVolume = realVolume;
	}

	public BigDecimal getBulkWeight() {
		return bulkWeight;
	}

	public void setBulkWeight(BigDecimal bulkWeight) {
		this.bulkWeight = bulkWeight;
	}

	public BigDecimal getRealWeight() {
		return realWeight;
	}

	public void setRealWeight(BigDecimal realWeight) {
		this.realWeight = realWeight;
	}

	public Integer getBoxNum() {
		return boxNum;
	}

	public void setBoxNum(Integer boxNum) {
		this.boxNum = boxNum;
	}

	public Integer getPieceNum() {
		return pieceNum;
	}

	public void setPieceNum(Integer pieceNum) {
		this.pieceNum = pieceNum;
	}

	public String getPackageType() {
		return packageType;
	}

	public void setPackageType(String packageType) {
		this.packageType = packageType;
	}

	public String getPackageTypeName() {
		return packageTypeName;
	}

	public void setPackageTypeName(String packageTypeName) {
		this.packageTypeName = packageTypeName;
	}

	public String getShakeTagFlag() {
		return shakeTagFlag;
	}

	public void setShakeTagFlag(String shakeTagFlag) {
		this.shakeTagFlag = shakeTagFlag;
	}

	public String getObliqueTagFlag() {
		return obliqueTagFlag;
	}

	public void setObliqueTagFlag(String obliqueTagFlag) {
		this.obliqueTagFlag = obliqueTagFlag;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getLocator() {
		return locator;
	}

	public void setLocator(String locator) {
		this.locator = locator;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
     * 容器号
     */
    @ApiModelProperty(value = "容器号")
    private String containerNo;

    /**
     * 父级容器id
     */
    @ApiModelProperty(value = "父级容器id")
    private Long parentContainerId;

    /**
     * 父级容器号
     */
    @ApiModelProperty(value = "父级容器号")
    private String parentContainerNo;

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceNo;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;

    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * 是否托盘，0否，1是
     */
    @ApiModelProperty(value = "是否托盘，0否，1是")
    private String palletFlag;

    /**
     * 是否主容器，0否，1是
     */
    @ApiModelProperty(value = "是否主容器，0否，1是")
    private String mainContainerFlag;

    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    private BigDecimal length;

    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    private BigDecimal width;

    /**
     * 高
     */
    @ApiModelProperty(value = "高")
    private BigDecimal height;

    /**
     * 毛体积
     */
    @ApiModelProperty(value = "毛体积")
    private BigDecimal bulkVolume;

    /**
     * 净体积
     */
    @ApiModelProperty(value = "净体积")
    private BigDecimal realVolume;

    /**
     * 毛重量
     */
    @ApiModelProperty(value = "毛重量")
    private BigDecimal bulkWeight;

    /**
     * 净重量
     */
    @ApiModelProperty(value = "净重量")
    private BigDecimal realWeight;

    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private Integer boxNum;

    /**
     * 件数
     */
    @ApiModelProperty(value = "件数")
    private Integer pieceNum;

    /**
     * 包装类型（编码）
     */
    @ApiModelProperty("包装类型（编码）")
    private String packageType;

    /**
     * 包装类型（名称）
     */
    @ApiModelProperty("包装类型（名称）")
    private String packageTypeName;

    /**
     * 是否含震动标签，0否，1是
     */
    @ApiModelProperty(value = "是否含震动标签，0否，1是")
    private String shakeTagFlag;

    /**
     * 是否含倾斜标签，0否，1是
     */
    @ApiModelProperty(value = "是否含倾斜标签，0否，1是")
    private String obliqueTagFlag;

    /**
     * 状态，0未入库，1已入库
     */
    @ApiModelProperty(value = "状态，0未入库，1已入库")
    private String status;

    /**
     * 库位
     */
    @ApiModelProperty(value = "库位")
    private String locator;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
