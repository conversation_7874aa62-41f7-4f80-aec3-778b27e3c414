package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 货运指令-关务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Data
public class InterfaceFreightOrderCustomsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long customsId;

    /**
     * 指令id
     */
    @ApiModelProperty(value = "指令id")
    private Long orderId;

    /**
     * 进出口方式
     */
    @ApiModelProperty(value = "进出口方式")
    private String tradeType;

    /**
     * 贸易术语
     */
    @ApiModelProperty(value = "贸易术语")
    private String tradeTerms;

    /**
     * 报关金额
     */
    @ApiModelProperty(value = "报关金额")
    private BigDecimal declareAtCustomsAmount;

    /**
     * 清关金额
     */
    @ApiModelProperty(value = "清关金额")
    private BigDecimal customsClearanceAmount;

    /**
     * 是否战略物资管制，0否，1是
     */
    @ApiModelProperty(value = "是否战略物资管制，0否，1是")
    private String strategicMaterialControlFlag;

    /**
     * 战略物资证号
     */
    @ApiModelProperty(value = "战略物资证号")
    private String strategicMaterialNo;

    /**
     * 信用证号
     */
    @ApiModelProperty(value = "信用证号")
    private String creditNo;

    /**
     * 信用证文件
     */
    @ApiModelProperty("信用证文件")
    private String creditFileUrl;

    /**
     * 是否开具信用证号
     */
    @ApiModelProperty("是否开具信用证号")
    private String issueCreditFlag;

    /**
     * 目的港编码
     */
    @ApiModelProperty("目的港编码")
    private String shippingToPortNo;

    /**
     *报关币种
     */
    @ApiModelProperty("报关币种")
    private String declareAtCustomsCurrency;

    /**
     * 清关币种
     */
    @ApiModelProperty("清关币种")
    private String customsClearanceCurrency;

    /**
     *交付地编码
     */
    @ApiModelProperty("交付地编码")
    private String deliverPlaceNo;

    /**
     * 是否双优项目，0否，1是
     */
    @ApiModelProperty("是否双优项目，0否，1是")
    private String excellentProjectFlag;

}
