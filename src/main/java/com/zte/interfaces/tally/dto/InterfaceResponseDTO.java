package com.zte.interfaces.tally.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 外部系统调用返回dto
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-12
 */
@Data
public class InterfaceResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 指令基础DTO
     */
    @ApiModelProperty(value = "指令基础DTO")
    private InterfaceFreightOrderDTO orderBase;

    /**
     * 托盘数据集DTO List
     */
    @ApiModelProperty(value = "托盘数据集DTO List")
    private List<InterfaceFreightOrderContainerDTO> palletList;

    /**
     * 箱数据集DTO List
     */
    @ApiModelProperty(value = "箱数据集DTO List")
    private List<InterfaceFreightOrderContainerDTO> boxList;

    /**
     * 箱明细数据集DTO List
     */
    @ApiModelProperty(value = "箱明细数据集DTO List")
    private List<InterfaceFreightOrderContainerItemDTO> boxItemList;

    /**
     * 任务数据集DTO List
     */
    @ApiModelProperty(value = "任务数据集DTO List")
    private List<InterfaceFreightOrderTaskDTO> taskList;

    /**
     * 关务DTO
     */
    @ApiModelProperty(value = "关务DTO")
    private InterfaceFreightOrderCustomsDTO customs;

    /**
     * 扩展DTO
     */
    @ApiModelProperty(value = "扩展DTO")
    private InterfaceFreightOrderProductExtDTO ext;

    /**
     * 条款DTO
     */
    @ApiModelProperty(value = "条款DTO")
    private InterfaceFreightOrderClauseDTO clause;

    /**
     * 文件数据集DTO List
     */
    @ApiModelProperty(value = "文件数据集DTO List")
    private List<InterfaceFreightOrderCustomsFileDTO> fileList;

    /**
     * 外部系统code
     */
    @ApiModelProperty(value = "外部系统code")
    private String systemCode;

    /**
     * 送货单号
     */
    @ApiModelProperty(value = "送货单号")
    private String sourceNo;

	public InterfaceFreightOrderDTO getOrderBase() {
		return orderBase;
	}

	public void setOrderBase(InterfaceFreightOrderDTO orderBase) {
		this.orderBase = orderBase;
	}

	public List<InterfaceFreightOrderContainerDTO> getPalletList() {
		return palletList;
	}

	public void setPalletList(List<InterfaceFreightOrderContainerDTO> palletList) {
		this.palletList = palletList;
	}

	public List<InterfaceFreightOrderContainerDTO> getBoxList() {
		return boxList;
	}

	public void setBoxList(List<InterfaceFreightOrderContainerDTO> boxList) {
		this.boxList = boxList;
	}

	public List<InterfaceFreightOrderContainerItemDTO> getBoxItemList() {
		return boxItemList;
	}

	public void setBoxItemList(List<InterfaceFreightOrderContainerItemDTO> boxItemList) {
		this.boxItemList = boxItemList;
	}

	public List<InterfaceFreightOrderTaskDTO> getTaskList() {
		return taskList;
	}

	public void setTaskList(List<InterfaceFreightOrderTaskDTO> taskList) {
		this.taskList = taskList;
	}

	public InterfaceFreightOrderCustomsDTO getCustoms() {
		return customs;
	}

	public void setCustoms(InterfaceFreightOrderCustomsDTO customs) {
		this.customs = customs;
	}

	public InterfaceFreightOrderProductExtDTO getExt() {
		return ext;
	}

	public void setExt(InterfaceFreightOrderProductExtDTO ext) {
		this.ext = ext;
	}

	public InterfaceFreightOrderClauseDTO getClause() {
		return clause;
	}

	public void setClause(InterfaceFreightOrderClauseDTO clause) {
		this.clause = clause;
	}

	public List<InterfaceFreightOrderCustomsFileDTO> getFileList() {
		return fileList;
	}

	public void setFileList(List<InterfaceFreightOrderCustomsFileDTO> fileList) {
		this.fileList = fileList;
	}

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}
    
    

}
