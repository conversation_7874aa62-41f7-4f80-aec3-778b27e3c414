package com.zte.interfaces.VO;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/9 下午2:47
 */
/* Started by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ColumnWidth(17)
@ExcelIgnoreUnannotated
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class PsDeliveryFeedbackVO implements Serializable {
    /**
     * 主键ID
     */
    @ExcelIgnore
    private String id;

    /**
     * 单据类型(1指令、2工单)
     */
    @ExcelIgnore
    private String orderType;
    /**
     * 单据类型名称(1指令、2工单)
     */
    @ExcelProperty(value = "单据类型",order = 15)
    private String orderTypeName;

    /**
     * 工单类型
     */
    private String businessScene;
    /**
     * 工单类型名称
     */
    @ExcelProperty(value = "工单类型",order = 10)
    private String businessSceneName;

    /**
     * 单据编号(指令编号/工单编号)
     */
    @ExcelProperty(value = "任务号",order = 5)
    private String orderNo;

    /**
     * 业务分类
     */
    @ExcelProperty(value = "业务分类",order = 25)
    private String category;

    /**
     * 反馈数量
     */
    @ExcelProperty(value = "反馈数量",order = 40)
    private BigDecimal quantity;

    /**
     * 预计完工日期
     */
    @ExcelProperty(value = "预计完工日期",order = 45)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateEstimatedCompletion;

    /**
     * 期望完工日期
     */
    @ExcelProperty(value = "期望完工日期",order = 50)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateExpectedCompletion;

    /**
     * 厂商自供料预计齐套日期
     */
    @ExcelProperty(value = "厂商自供料预计齐套日期",order = 55)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateMaterialEstimatedPrepared;

    /**
     * 全部物料预计齐套日期
     */
    @ExcelProperty(value = "全部物料预计齐套日期",order = 60)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialEstimatedPrepared;

    /**
     * 厂商自供料实际齐套日期
     */
    @ExcelProperty(value = "厂商自供料实际齐套日期",order = 65)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeMaterialPrepared;

    /**
     * 全部物料实际齐套日期
     */
    @ExcelProperty(value = "全部物料实际齐套日期",order = 70)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialPrepared;

    /**
     * 预计投产日期
     */
    @ExcelProperty(value = "预计投产日期",order = 75)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateScheduledProduction;

    /**
     * 实际投产日期
     */
    @ExcelProperty(value = "实际投产日期",order = 80)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeProduction;

    /**
     * 首次领料日期
     */
    @ExcelProperty(value = "首次领料日期", order = 125)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstPickingDate;

    /**
     * 责任（1阿里责任，2厂商责任）
     */
    @ExcelIgnore
    private String liability;
    /**
     * 责任名称
     */
    @ExcelProperty(value = "责任",order = 85)
    private String liabilityName;

    /**
     * 一级原因分类
     */
    private String abnormalCategoryFirst;

    /**
     * 二级原因分类
     */
    private String abnormalCategorySecond;

    /**
     * 三级原因分类
     */
    private String abnormalCategoryThird;

    /**
     * 一级原因分类名称
     */
    @ExcelProperty(value = "一级原因分类",order = 90)
    private String abnormalCategoryFirstName;

    /**
     * 二级原因分类名称
     */
    @ExcelProperty(value = "二级原因分类",order = 95)
    private String abnormalCategorySecondName;

    /**
     * 三级原因分类名称
     */
    @ExcelProperty(value = "三级原因分类",order = 100)
    private String abnormalCategoryThirdName;

    /**
     * 原因分类（级联模式）
     */
    private List<String> abnormalCategory;

    /**
     * 原因
     */
    @ExcelProperty(value = "原因",order = 105)
    private String remark;

    /**
     * 延期编号(不能重复)
     */
    @ExcelProperty(value = "延期编号",order = 110)
    private String abnormalNo;

    /**
     * 操作类型，1-人工，2-定时任务
     */
    @ExcelIgnore
    private String operateType;

    /**
     * 推送状态
     */
    private String pushStatus;
    /**
     * 推送状态名称
     */
    @ExcelProperty(value = "推送状态",order = 111)
    private String pushStatusName;

    /**
     * 推送报错消息
     */
    @ExcelProperty(value = "推送报错消息",order = 112)
    private String pushErrorMsg;

    /**
     * 创建人
     */
    @ExcelProperty(value = "反馈人",order = 30)
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "反馈人时间",order = 35)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 最后更新人
     */
    @ExcelProperty(value = "最后更新人",order = 115)
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    @ExcelProperty(value = "最后更新时间",order = 120)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    /**
     * 有效标识，Y-有效，N-无效
     */
    @ExcelIgnore
    private String enabledFlag;

    /**
     * 定时任务调度表示，Y-已调度，N-未调度
     */
    @ExcelIgnore
    private String scheduleFlag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注",order = 20)
    private String notes;
}

/* Ended by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */