package com.zte.interfaces.VO;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/9 下午2:47
 */
/* Started by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.common.utils.Constant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class PsDeliveryFeedbackDownImportVO {
    /**
     * 主键ID,修改时有值
     */
    @ExcelIgnore
    private String id;
    /**
     * 单据编号(指令编号/工单编号)
     */
    @NotBlank(message = "单据编号不能为空")
    @ExcelProperty(value = "任务号",order = 5)
    private String orderNo;

    /**
     * 预计完工日期
     */
    @ExcelProperty(value = "预计完工日期",order = 10)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateEstimatedCompletion;

    /**
     * 厂商自供料预计齐套日期
     */
    @ExcelProperty(value = "厂商自供料预计齐套日期",order = 15)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateMaterialEstimatedPrepared;

    /**
     * 全部物料预计齐套日期
     */
    @ExcelProperty(value = "全部物料预计齐套日期",order = 20)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialEstimatedPrepared;

    /**
     * 厂商自供料实际齐套日期
     */
    @ExcelProperty(value = "厂商自供料实际齐套日期",order = 25)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeMaterialPrepared;

    /**
     * 全部物料实际齐套日期
     */
    @ExcelProperty(value = "全部物料实际齐套日期",order = 30)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialPrepared;

    /**
     * 预计投产日期
     */
    @ExcelProperty(value = "预计投产日期",order = 35)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateScheduledProduction;

    /**
     * 实际投产日期
     */
    @ExcelProperty(value = "实际投产日期",order = 40)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeProduction;

    /**
     * 首次领料日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstPickingDate;

    /**
     * 责任（1阿里责任，2厂商责任）
     */
    @Pattern(regexp = "^$|^阿里责任$|^厂商责任$", message = "责任,值只能填阿里责任或厂商责任")
    @ExcelProperty(value = "责任",order = 45)
    private String liability;
    /**
     * 责任名称
     */
    @ExcelIgnore
    private String liabilityName;

    /**
     * 一级原因分类
     */
    @Size(max = 64,message = "一级原因分类不能超过64字符")
    @ExcelProperty(value = "一级原因分类",order = 50)
    private String abnormalCategoryFirst;

    /**
     * 二级原因分类
     */
    @Size(max = 64,message = "二级原因分类不能超过64字符")
    @ExcelProperty(value = "二级原因分类",order = 55)
    private String abnormalCategorySecond;

    /**
     * 三级原因分类
     */
    @Size(max = 64,message = "三级原因分类不能超过64字符")
    @ExcelProperty(value = "三级原因分类",order = 60)
    private String abnormalCategoryThird;

    /**
     * 原因
     */
    @Size(max = 2000,message = "延期原因不能超过2000字符")
    @ExcelProperty(value = "原因",order = 65)
    private String remark;

    /**
     * 延期编号(不能重复)
     */
    @Size(max = 64,message = "延期编号不能超过64字符")
    @ExcelProperty(value = "延期编号",order = 70)
    private String abnormalNo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注",order = 75)
    private String notes;

    /**
     * excel校验结果
     */
    @ExcelIgnore
    private String validateMsg = Constant.OK_STATUS;
}

/* Ended by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */