package com.zte.interfaces.VO;

import com.zte.domain.model.PsDeliveryFeedbackDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/12 下午4:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderTaskInfoVO extends PsDeliveryFeedbackDO {
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 客户编码
     */
    private String customerNo;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 阿里客户标识
     */
    private String aliFlag;
    /**
     * 发送失败标识
     */
    private String sendEmailFail;
    /**
     * 推送消息失败标识
     */
    private String pushMessageFail;
}
