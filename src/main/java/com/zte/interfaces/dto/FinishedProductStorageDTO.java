package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FinishedProductStorageDTO {

    /**
     * 序列号
     */
    @NotBlank(message = "seq_no is blank")
    @JsonProperty("seq_no")
    private String seqNo;

    /**
     * 工单编号
     */
    @NotBlank(message = "manufacture_order_no is blank")
    @JsonProperty("manufacture_order_no")
    private String manufactureOrderNo;

    /**
     * 客户部件类型
     */
    @NotBlank(message = "category is blank")
    @JsonProperty("category")
    private String category;

    /** 物料质量(1良品，2不良品) */
    @NotNull(message = "material_quality is null")
    @JsonProperty("material_quality")
    private Integer materialQuality;

    /** 完⼯数量(增量) */
    @NotNull(message = "quantity_completed is null")
    @JsonProperty("quantity_completed")
    private Integer quantityCompleted;

    /** 完⼯批次 */
    @JsonProperty("product_batch")
    private String productBatch;

    /** 物料清单 */
    @JsonProperty("material_bill_list")
    private List<FinishedProductStorageItemDTO> materialBillList;
}
