package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FinishedProductStorageItemDTO {

    /**
     * 序列号
     */
    @NotBlank(message = "material_category is blank")
    @JsonProperty("material_category")
    private String materialCategory;

    /**
     * 工单编号
     */
    @NotBlank(message = "material_sign is blank")
    @JsonProperty("material_sign")
    private String materialSign;

    /**
     * 客户部件类型
     */
    @NotBlank(message = "material_name is blank")
    @JsonProperty("material_name")
    private String materialName;

    /** 物料质量(1良品，2不良品) */
    @NotBlank(message = "material_bom is blank")
    @JsonProperty("material_bom")
    private String materialBom;

    /** SN编码 */
    @NotBlank(message = "sn_no is blank")
    @JsonProperty("sn_no")
    private String snNo;

    private String originalBarcode;

    /** 物料清单 */
    @JsonProperty("material_bill_list")
    private List<FinishedProductStorageItemDTO> materialBillList;
}
