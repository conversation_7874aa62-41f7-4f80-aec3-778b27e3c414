package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ManufactureOrderDTO {
    public interface FormalScedule {}
    public interface ReworkWithoutMd {}
    public interface BufferSceduleWithoutMd {}
    public interface ConfirmationScedule {}

    /**
     * 工单编号
     */
    @NotBlank(message = "manufacture_order_no(工单编号) is blank", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("manufacture_order_no")
    private String manufactureOrderNo;

    /**
     * 场景编码
     */
    @NotBlank(message = "scene_code(场景编码) is blank", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class})
    @JsonProperty("scene_code")
    private String sceneCode;

    /**
     * 指令编号
     */
    @NotBlank(message = "manufacture_directive_no(指令编号) is blank", groups = {FormalScedule.class})
    @JsonProperty("manufacture_directive_no")
    private String manufactureDirectiveNo;

    /**
     * 客户部件类型
     */
    @NotBlank(message = "category(客户部件类型) is blank", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("category")
    private String category;

    /**
     * 任务类型
     */
    @NotNull(message = "manufacture_order_type(任务类型) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("manufacture_order_type")
    private Integer manufactureOrderType;

    /**
     * fixBom
     */
    @NotBlank(message = "fix_bom(fixBom) is blank", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("fix_bom")
    private String fixBom;

    /**
     * 排产数量
     */
    @NotNull(message = "quantity_produce(排产数量) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("quantity_produce")
    private Integer quantityProduce;

    /**
     * 预计完工日期
     */
    @NotNull(message = "date_estimated_completion(预计完工日期) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("date_estimated_completion")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dateEstimatedCompletion;

    /**
     * 厂商自供料预计齐套日期
     */
    @NotNull(message = "date_material_estimated_prepared(厂商自供料预计齐套日期) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("date_material_estimated_prepared")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dateMaterialEstimatedPrepared;

    /**
     * 全部物料预计齐套日期
     */
    @NotNull(message = "date_all_material_estimated_prepared(全部物料预计齐套日期) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class, ConfirmationScedule.class})
    @JsonProperty("date_all_material_estimated_prepared")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dateAllMaterialEstimatedPrepared;

    /**
     * 预计投产日期
     */
    @JsonProperty("date_scheduled_production")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dateScheduledProduction;

    /**
     * 物料清单
     */
    @JsonProperty("material_bill_list")
    private List<MaterialBillDTO> materialBillList;

    /**
     * 工厂编码
     */
    @JsonProperty("factory_code")
    private String factoryCode;

    @JsonProperty("origin_fixbom")
    private String originFixbom;

    /**
     * 成品物料名称
     */
    @JsonProperty("material_name")
    private String materialName;

    /**
     * 库存来源
     */
    @JsonProperty("inventory_source")
    private Integer inventorySource;

    /**
     * 业务场景
     */
    @NotNull(message = "business_scene(业务场景) is null", groups = {FormalScedule.class, ReworkWithoutMd.class, BufferSceduleWithoutMd.class})
    @JsonProperty("business_scene")
    private Integer businessScene;

    /**
     * 关联工单号
     */
    @JsonProperty("rel_mo")
    private String relMo;

    @JsonProperty("SNList")
    private List<String> snList;
}
