package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceDetailDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "主键")
    private String distributionId;

    @ApiModelProperty(value = "申请id")
    private String applyId;

    @ApiModelProperty(value = "资源id")
    private String resourceId;

    @ApiModelProperty(value = "资源开始")
    private String resourceStart;

    @ApiModelProperty(value = "资源步距")
    private Short resourceStep;

    @ApiModelProperty(value = "资源号")
    private String resourceSn;

    @ApiModelProperty(value = "申请状态(初始化/锁定中/已使用)")
    private String resourceStatus;

    private String notResourceStatus;

    @ApiModelProperty(value = "用户")
    private String wlanUsername;

    @ApiModelProperty(value = "密码")
    private String wlanPwd;

    @ApiModelProperty(value = "默认终端配置地址")
    private String lanIp;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "2.4G-1无线网络名称")
    private String wirelessName2g1;

    @ApiModelProperty(value = "2.4G-1无线网络密钥")
    private String wirelessPass2g1;

    @ApiModelProperty(value = "额定电压")
    private String ratedVoltage;

    @ApiModelProperty(value = "额定电流")
    private String ratedCurrent;

    @ApiModelProperty(value = "设备标识")
    private String deviceSerialNumberPrint;

    @ApiModelProperty(value = "是否生效")
    private String enableFlag;

    @ApiModelProperty(value = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "申请人")
    private String createUser;

    @ApiModelProperty(value = "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty(value = "更新人")
    private String updateUser;

    private List<String> applyIdList;
}
