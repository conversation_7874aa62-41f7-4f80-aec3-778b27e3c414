package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 标模任务条码推送测试记录表PageQuery
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-15 08:53:49
 */
@ApiModel
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushModelSnTestRecordPageQueryDTO extends PageDTO {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String requestId;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String type;
    /**
     * 生产指令
     */
    @ApiModelProperty(value = "生产指令")
    private String directiveNumber;
    /**
     * 中兴任务号
     */
    @ApiModelProperty(value = "中兴任务号")
    private String workorderId;
    /**
     * 整机条码
     */
    @ApiModelProperty(value = "整机条码")
    private String chassisSn;
    /**
     * 整机条码
     */
    @ApiModelProperty(value = "整机条码")
    private String nodeSn;
    /**
     * 制造品牌名
     */
    @ApiModelProperty(value = "制造品牌名")
    private String brand;
    /**
     * 主板条码，存在多个时，使用,分隔
     */
    @ApiModelProperty(value = "主板条码，存在多个时，使用,分隔")
    private String boardSn;
    /**
     * 任务附加属性表-三段机型
     */
    @ApiModelProperty(value = "任务附加属性表-三段机型")
    private String model;
    /**
     * 站位名称
     */
    @ApiModelProperty(value = "站位名称")
    private String stationName;
    /**
     * 检测的开始时间
     */
    @ApiModelProperty(value = "检测的开始时间")
    private Date startedTime;
    /**
     * 检测的结束时间
     */
    @ApiModelProperty(value = "检测的结束时间")
    private Date finishedTime;
    /**
     * 检测结果,取值范围：Pass/Fail
     */
    @ApiModelProperty(value = "检测结果,取值范围：Pass/Fail")
    private String result;
    /**
     * 工序站位检查结果失败时的原因说明
     */
    @ApiModelProperty(value = "工序站位检查结果失败时的原因说明")
    private String message;
    /**
     * oss测试文件
     */
    @ApiModelProperty(value = "oss测试文件")
    private String ossFileKey;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String manufacturerName;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String enabledFlag;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Date createDate;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Date lastUpdatedDate;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String createBy;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "冗余天数")
    private Integer preDays;

    @ApiModelProperty(value = "冗余天数")
    private List<String> snList;

    @ApiModelProperty(value = "站位上传状态")
    private String stationUploadStatus;
    @ApiModelProperty(value = "oss文件上传状态")
    private String fileUploadStatus;

    private String lastId;

}

