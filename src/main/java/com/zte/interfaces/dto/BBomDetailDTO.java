/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;

/**
 * BOM明细实体
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class BBomDetailDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = 9195155193045427550L;

    @ApiModelProperty(value = "BOM明细主键", hidden = true)
    private String bBomDetailId;

    @ApiModelProperty(value = "BOMId")
    private String bomHeaderId;

    @ApiModelProperty(value = "零件料号")

    @NotNull(message = "零件料号不能为空")
    private String itemCode;

    @ApiModelProperty(value = "条目编码")
    @NotNull(message = "条目编码不能为空")
    private String levelNo;

    @ApiModelProperty(value = "条目等级编号")
    @NotNull(message = "条目等级不能为空")
    private String gradeNo;

    @ApiModelProperty(value = "用量")
    @NotNull(message = "用量不能为空")
    private BigDecimal usageCount;

    @ApiModelProperty(value = "工序编号")
    @NotNull(message = "工序编号不能为空")
    private String processCode;

    @ApiModelProperty(value = "组装顺序")
    @NotNull(message = "组装顺序不能为空")
    private BigDecimal asyIndex;

    @ApiModelProperty(value = "关键零件")
    @NotNull(message = "关键零件不能为空")
    private String keyComp;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createBy;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "最后修改人", hidden = true)
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后修改时间", hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "有效标志", hidden = true, allowableValues = "Y,N")
    private String enabledFlag;

    @ApiModelProperty(value = "组织ID")
    private BigDecimal orgId;

    @ApiModelProperty(value = "工厂ID")
    private BigDecimal factoryId;

    @ApiModelProperty(value = "实体ID")
    private BigDecimal entityId;

    @ApiModelProperty(value = "物料民称")
    private String chiDesc;

    @ApiModelProperty(value = "坐标")
    private String locationCode;

    @ApiModelProperty(value = "工艺段")
    private String craftSection;
    
    private String positionExt;
    
    private String placeNoExt1;
    
    private String placeNoExt2;
    
    private String placeNoExt3;
    
    private String placeNoExt4;
    
    private String placeNoExt5;

    private String placeNoExt6;

    private String placeNoExt7;

    //是否贴片
    private String isSmt;

    //规格
    private String style;

    //单位
    private String unit;

    //bs_item_info
    private String sourceItemId;

    //是否无铅
    private String codeDesc;

    //物料编码属性
    private String itemCodeAttr;

    //技术参数
    private String param;

    //封装
    private String encapsulation;

    //是否有AVL指定
    private String isAvl;
    // 物料类型
    private String itemType;
    
    // 排除掉的物料清单
    private List<String> excludeItemCodeList;
    
    private List<String> productCodeList;

    private List<String> headerIds;

    private String mainProductCode;

    private String itemName;

    //Y则必须是维护了可绑定物料的
    private String filterFlag;
	private String mainCraftSection;

	private String prodplanId;

    private List<String> productCodes;

    private String originalItemCode;

    public String getOriginalItemCode() {
        return originalItemCode;
    }

    public void setOriginalItemCode(String originalItemCode) {
        this.originalItemCode = originalItemCode;
    }

    public List<String> getProductCodes() {
        return productCodes;
    }

    public void setProductCodes(List<String> productCodes) {
        this.productCodes = productCodes;
    }

    public String getProdplanId() {
        return prodplanId;
    }

    public void setProdplanId(String prodplanId) {
        this.prodplanId = prodplanId;
    }

    public String getFilterFlag() {
        return filterFlag;
    }

    public void setFilterFlag(String filterFlag) {
        this.filterFlag = filterFlag;
    }

    public List<String> getHeaderIds() {
        return headerIds;
    }

    public void setHeaderIds(List<String> headerIds) {
        this.headerIds = headerIds;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMainProductCode() {
        return mainProductCode;
    }

    public void setMainProductCode(String mainProductCode) {
        this.mainProductCode = mainProductCode;
    }

    public String getSourceItemId() {
        return sourceItemId;
    }

    public void setSourceItemId(String sourceItemId) {
        this.sourceItemId = sourceItemId;
    }

    public String getCodeDesc() {
        return codeDesc;
    }

    public void setCodeDesc(String codeDesc) {
        this.codeDesc = codeDesc;
    }

    public String getItemCodeAttr() {
        return itemCodeAttr;
    }

    public void setItemCodeAttr(String itemCodeAttr) {
        this.itemCodeAttr = itemCodeAttr;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getEncapsulation() {
        return encapsulation;
    }

    public void setEncapsulation(String encapsulation) {
        this.encapsulation = encapsulation;
    }

    public String getIsAvl() {
        return isAvl;
    }

    public void setIsAvl(String isAvl) {
        this.isAvl = isAvl;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getIsSmt() {
        return isSmt;
    }

    public void setIsSmt(String isSmt) {
        this.isSmt = isSmt;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public void setbBomDetailId(String bBomDetailId) {
        this.bBomDetailId = bBomDetailId;
    }

    public String getbBomDetailId() {
        return bBomDetailId;
    }

    public void setBomHeaderId(String bomHeaderId) {
        this.bomHeaderId = bomHeaderId;
    }

    public String getBomHeaderId() {
        return bomHeaderId;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setLevelNo (String levelNo) {
        this.levelNo = levelNo;
    }

    public String getLevelNo() {
        return levelNo;
    }

    public void setGradeNo(String gradeNo) {
        this.gradeNo = gradeNo;
    }

    public String getGradeNo() {
        return gradeNo;
    }

    public void setUsageCount(BigDecimal usageCount) {
        this.usageCount = usageCount;
    }

    public BigDecimal getUsageCount() {
        return usageCount;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setAsyIndex(BigDecimal asyIndex) {
        this.asyIndex = asyIndex;
    }

    public BigDecimal getAsyIndex() {
        return asyIndex;
    }

    public void setKeyComp(String keyComp) {
        this.keyComp = keyComp;
    }

    public String getKeyComp() {
        return keyComp;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

	public String getChiDesc() {
		return chiDesc;
	}

	public void setChiDesc(String chiDesc) {
		this.chiDesc = chiDesc;
	}

	public String getLocationCode() {
		return locationCode;
	}

	public void setLocationCode(String locationCode) {
		this.locationCode = locationCode;
	}

	public String getCraftSection() {
		return craftSection;
	}

	public void setCraftSection(String craftSection) {
		this.craftSection = craftSection;
	}
    

    private String productCode;

    public String getProductCode() {

        return productCode;
    }

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getPositionExt() {
		return positionExt;
	}

	public void setPositionExt(String positionExt) {
		this.positionExt = positionExt;
	}

	public String getPlaceNoExt1() {
		return placeNoExt1;
	}

	public void setPlaceNoExt1(String placeNoExt1) {
		this.placeNoExt1 = placeNoExt1;
	}

	public String getPlaceNoExt2() {
		return placeNoExt2;
	}

	public void setPlaceNoExt2(String placeNoExt2) {
		this.placeNoExt2 = placeNoExt2;
	}

	public String getPlaceNoExt3() {
		return placeNoExt3;
	}

	public void setPlaceNoExt3(String placeNoExt3) {
		this.placeNoExt3 = placeNoExt3;
	}

	public String getPlaceNoExt4() {
		return placeNoExt4;
	}

	public void setPlaceNoExt4(String placeNoExt4) {
		this.placeNoExt4 = placeNoExt4;
	}

	public String getPlaceNoExt5() {
		return placeNoExt5;
	}

	public void setPlaceNoExt5(String placeNoExt5) {
		this.placeNoExt5 = placeNoExt5;
	}

    public String getPlaceNoExt6() {
        return placeNoExt6;
    }

    public void setPlaceNoExt6(String placeNoExt6) {
        this.placeNoExt6 = placeNoExt6;
    }

    public String getPlaceNoExt7() {
        return placeNoExt7;
    }

    public void setPlaceNoExt7(String placeNoExt7) {
        this.placeNoExt7 = placeNoExt7;
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

	public List<String> getExcludeItemCodeList() {
		return excludeItemCodeList;
	}

	public void setExcludeItemCodeList(List<String> excludeItemCodeList) {
		this.excludeItemCodeList = excludeItemCodeList;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getMainCraftSection() {
		return mainCraftSection;
	}

	public void setMainCraftSection(String mainCraftSection) {
		this.mainCraftSection = mainCraftSection;
	}
}

