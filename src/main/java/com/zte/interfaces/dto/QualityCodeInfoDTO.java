package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
/* Started by AICoder, pid:za3df80673r630e1488d08e05024fe3db7a52d20 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualityCodeInfoDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务号
     */
    private String taskNo;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 条码
     */
    private String sn;

    /**
     * 质量码
     */
    private String qualityCode;
    /**
     * 节点条码
     */
    private String nodeSn;

    private String createBy;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
}

/* Ended by AICoder, pid:za3df80673r630e1488d08e05024fe3db7a52d20 */