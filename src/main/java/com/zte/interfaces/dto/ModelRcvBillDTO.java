package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ModelRcvBillDTO {

    @ApiModelProperty(value = "领料单号")
    private String billNo;

    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "客户名称或客户编码")
    private String customerName;

    @ApiModelProperty(value = "推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常")
    private Integer pushStatus;

    @ApiModelProperty(value = "当前进程数据推送时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date pushDate;

    @ApiModelProperty(value = "异常信息")
    private String errorMsg;

    @ApiModelProperty(value = "推送失败次数")
    private Integer pushFailCount;

    @ApiModelProperty(value = "凭证单号")
    private String rcvNo;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "是否有效标志 Y:有效 N:无效")
    private String enabledFlag;

    @ApiModelProperty(value = "创建开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date startCreateDate;

    @ApiModelProperty(value = "创建结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date endCreateDate;
}
