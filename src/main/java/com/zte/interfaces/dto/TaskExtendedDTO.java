package com.zte.interfaces.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 告警表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-29 20:29:24
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskExtendedDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    //工单类型
    @ApiModelProperty(value = "工单类型")
    private String entityClass;

    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "客户物料名称（三段码），新增此字段排产回写时保存此字段")
    private String customerItemName;

    @ApiModelProperty(value = "客户部件类型：需要新增此字段，在排产回写时保存此字段")
    private String customerPartType;

    @ApiModelProperty(value = "客户编码")
    private String customerNo;

    @ApiModelProperty(value = "fix_bom_id")
    private String fixBomId;

    @ApiModelProperty(value = "整机料单代码")
    private String itemNo;

    @ApiModelProperty(value = "指令编号")
    private String billNo;

    @ApiModelProperty(value = "任务类型")
    private String taskType;
    @ApiModelProperty(value = "任务数量")
    private BigDecimal taskQty;
    /**
     * 转正状态（1：待转正，2：转正中，3：已转正，4：已取消）
     */
    private String confirmationStatus;

    /**
     * 物料管控类型
     */
    @ApiModelProperty(value = "物料管控类型")
    private String materialControl;

    /**
     * 云类型
     */
    @ApiModelProperty(value = "云类型")
    private String cloudType;

}

