package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>标模推送信息头扩展DTO</p>
 *
 * <AUTHOR>
 * @since 2025/5/9 15:41
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushStdModelDataExtDTO {

    private String taskNo;

    private String billNo;

    private String customerPartType;

    private String customerNo;

    private String taskStatus;

    private String entityClass;

    /**
     * fix_bom_head表id
     */
    private String fixBomHeadId;

    /**
     * 云类型 PC:公有云 HC:混合云
     */
    private String cloudType;

    /**
     * 物料管控类型 1:扣料模式 2:整机模式 3:部分扣料模式
     */
    private String materialControl;

    private Date lastUpdatedDate;
}
