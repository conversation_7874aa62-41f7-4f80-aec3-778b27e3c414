package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/16 18:43
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApsCustInstructBomDTO {

    private Long id;
    private Long customerBomId;
    private String customerNo;
    private String billNo;
    private String customerItemName;
    private String customerMaterialName;
    private Long parentId;
    private String materialNo;
    private String materialBom;
    private String materialCategory;
    private String materialSign;
    private String materialBillType;
    private BigDecimal unitQty;
    private BigDecimal rootUnitQty;
    private Integer bomLevel;
    private String leafFlag;
    private String locationDesc;
    private String enabledFlag;
    private Integer tenantId;

    private List<ApsCustInstructBomDTO> bomList;

}
