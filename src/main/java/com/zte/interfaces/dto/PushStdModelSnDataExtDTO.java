package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <p>标模推送信息SN明细表</p>
 *
 * <AUTHOR>
 * @since 2025/5/12 9:38
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushStdModelSnDataExtDTO extends PushStdModelSnDataDTO {

    private String stockName;
    /** 库位组织 从ps_task获取 */
    private Integer stockOrgId;
    /**
     * 需要处理等待推进 状态3 的当前进程
     */
    private String pushNextProcess;

    private String taskFixBomId;

    private String taskBillNo;

    private String taskCustomerPartType;

    private String taskCustomerNo;

    /** 任务三段机型 */
    private String taskCustomerItemName;

    /** 任务类别 */
    private String taskEntityClass;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * fixbom&箱单配置表
     */
    private List<PackingListConfigDTO> packList;

    /**
     * 质量码白名单
     */
    private List<String> whiteTaskNoList;

    /**
     * fix_bom_head表id
     */
    private String fixBomHeadId;

    /**
     * 云类型 PC:公有云 HC:混合云
     */
    private String cloudType;

    /**
     * 物料管控类型 1:扣料模式 2:整机模式 3:部分扣料模式
     */
    private String materialControl;
}
