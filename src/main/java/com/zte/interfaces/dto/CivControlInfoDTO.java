package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: panXu
 * @Date: 2020/6/3 15:34
 * @Description:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CivControlInfoDTO implements java.io.Serializable {
    private String itemNo;
    private String itemLicenseexception;
    private String exportcontrolmethod;
    private String exportcontrolmethodName;
    // 选型属性编码
    private String propertyNo;
    // 选型属性名称
    private String propertyName;

    @JsonProperty("isControl")
    private Integer control;

    //条码管控类型
    private Integer barcodeControlType;

    public Integer getBarcodeControlType() {
        return barcodeControlType;
    }

    public void setBarcodeControlType(Integer barcodeControlType) {
        this.barcodeControlType = barcodeControlType;
    }

    public Integer getControl() {
        return control;
    }

    public void setControl(Integer control) {
        this.control = control;
    }

    public String getPropertyNo() {
        return propertyNo;
    }

    public void setPropertyNo(String propertyNo) {
        this.propertyNo = propertyNo;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemLicenseexception() {
        return itemLicenseexception;
    }

    public void setItemLicenseexception(String itemLicenseexception) {
        this.itemLicenseexception = itemLicenseexception;
    }

    public String getExportcontrolmethod() {
        return exportcontrolmethod;
    }

    public void setExportcontrolmethod(String exportcontrolmethod) {
        this.exportcontrolmethod = exportcontrolmethod;
    }

    public String getExportcontrolmethodName() {
        return exportcontrolmethodName;
    }

    public void setExportcontrolmethodName(String exportcontrolmethodName) {
        this.exportcontrolmethodName = exportcontrolmethodName;
    }
}
