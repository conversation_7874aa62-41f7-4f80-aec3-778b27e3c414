package com.zte.interfaces.dto.mbom;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * packageName com.zte.interfaces.dto.mbom
 *
 * <AUTHOR>
 * @version JDK 8
 * @className CpqdGbomDto (此处以class为例)
 * @date 2025/4/29
 * @description TODO
 */
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CpqdGbomDTO {
    // 生产指令编号
    private String billNo;
    // FixBOM编码
    private String cbomCode;
    // 客户整机三段码名称
    private String customerItemName;
    // 产品实例编码
    private String instanceNo;
    // 销售模型ID
    private String sbomModelId;
    // 销售模型版本
    private Integer version;
    // 物料标识
    private String materialSign;
    // 物料标识名称
    private String materialSignName;
    /**
     * 云类型 PC 公有云 HC 混合云
     */
    private String cloudType;
}
