package com.zte.interfaces.dto.mbom;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * packageName com.zte.interfaces.dto.mbom
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/4/28
 */
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FixBomHeadDTO {
    private String id;
    private String fixBomId;
    // 客户名称
    private String custName;
    // 客户代码
    private String custNo;
    /** 名称 */
    private String productName;
    // 版本
    private String mbomVersion;
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date creationDate;
    private String lastUpdatedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdateDate;
    private String enabledFlag;
    /**
     * 任务号
     */
    private String taskNo;
}
