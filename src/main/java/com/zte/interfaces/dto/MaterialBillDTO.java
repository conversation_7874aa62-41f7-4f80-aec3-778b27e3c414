package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaterialBillDTO {
    /**
     * 物料分类
     */
    @NotBlank(message = "material_category can not be blank")
    @Size(min = 1, max = 32, message = "material_category length can not exceed 32")
    @JsonProperty("material_category")
    private String materialCategory;

    /**
     * 物料名称，最上层物料传三段码，其余叶子节点传mpn，即供应商料号或客户物料代码
     */
    @NotBlank(message = "material_name can not be blank")
    @Size(min = 1, max = 128, message = "material_name length can not exceed 128")
    @JsonProperty("material_name")
    private String materialName;

    @JsonProperty("material_bom")
    private String materialBom;

    /**
     * 指令编码
     */
    @JsonProperty("instruction_bom_number")
    private String instructionBomNumber;

    /**
     * 物料列表（递归）
     */
    @JsonProperty("material_bill_list")
    private List<MaterialBillDTO> materialBillList;

    /**
     * 单位用量
     */
    @NotNull(message = "quantity_unit can not be null")
    @JsonProperty("quantity_unit")
    private Integer quantityUnit;

    @JsonProperty("locationDesc")
    private String locationDesc;

    @JsonProperty("supply_distinct")
    private String supplyDistinct;

    @JsonProperty("supply_source")
    private String supplySource;

    @JsonProperty("control_level")
    private Integer controlLevel;

    @JsonProperty("sample")
    private String sample;

    /**
     * 物料层级
     */
    private String itemLevel;
    /**
     * 物料排序
     */
    private String itemSeq;

    /**
     * 物料类型
     */
    private String itemType;
}
