package com.zte.interfaces.dto.spare;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.EmSmtStencil;
import com.zte.interfaces.dto.FixtureInfoDetailDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-05 9:23
 */

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SparePartAllocationHeadDTO implements Serializable {
    private static final long serialVersionUID = 4201247084899559908L;
    @NotBlank(message = "单据号不能为空")
    private String billNo;
    private String status;
    private String statusDes;
    /**
     * 备注
     */
    private String remark;
    /**
     * 调拨原因
     */
    private String transferReason;
    /**
     * 辅料类型
     */
    @NotBlank(message = "辅料类型不能为空")
    private String partType;
    private String strPartType;

	private String partName;
    private String partTypeDesc;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;
    private String lastUpdatedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    private String emailCc;
    @NotBlank(message = "调出工厂不能为空")
    private String transferFactoryId;
    private String transferFactoryName;
    @NotBlank(message = "接收工厂不能为空")
    private String receivingFactoryId;
    private String receivingFactoryName;
    @Valid
    @NotNull
    @Size(min = 1, max = 1000, message = "调拨明细只能是1-1000")
    private List<SparePartAllocationDetailDTO> detailList;
    @Valid
    @NotNull
    @Size(min = 1, max = 1000, message = "审批明细只能是1-1000个")
    private List<ApprovalProcessInfoEntityDTO> approveList;
    private String factoryId;
    private String docId;
    private String fileName;
    private String fileSize;

	private String partCode;
	private String receiveLocation;
	private String locationCode;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date receiveDate;
	private boolean transOutbound = true;
	private EmSmtStencil emSmtStencil;
	private String itemNo;
	private String itemDetail;
	private String detailId;
	private Integer quantity;
	private FixtureInfoDetailDTO fixtureInfoDetailDTO;

    // 区分PDA，pda前端不显示超过20条，因为卡顿。
    private String sourceSys;
}
