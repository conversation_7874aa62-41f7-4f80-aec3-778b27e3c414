package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资源申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-01 16:31:48
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceApplicationEntityDTO extends PageDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	private String applyId;
	/**
	 * 单据号
	 */
	private String applyBillNo;
	/**
	 * 父单据号
	 */
	private String parentApplyBillNo;
	/**
	 * 资源类型
	 */
	@NotBlank
	private String resourceType;
	/**
	 * 用户类型
	 */
	@NotBlank
	private String userType;
	/**
	 * 资源id
	 */
	@NotBlank
	private String resourceId;
	/**
	 * 资源id
	 */
	@NotBlank
	private String resourceNo;
	/**
	 * 资源步距
	 */
	private Integer resourceStep;
	/**
	 * 资源开始
	 */
	private String resourceStart;
	/**
	 * 资源结束
	 */
	private String resourceEnd;
	/**
	 * 申请总数
	 */
	private BigDecimal applyAmount;
	/**
	 * 拆分分配总数
	 */
	private Long allocatedQty;
	/**
	 * 可分配数量
	 */
	private Integer distributableQty;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 来源系统
	 */
	private String sourceSystem;
	/**
	 * 申请状态（初始化/锁定/作废）
	 */
	private String resourceStatus;
	/**
	 * 订单/任务
	 */
	private String applyTask;
	/**
	 * 物料代码
	 */
	private String itemNo;
	/**
	 * 生产单位
	 */
	private String usageScope;
	/**
	 * 定制信息
	 */
	private String custInfo;
	/**
	 * 申请数量
	 */
	@NotNull
	private BigDecimal applyQty;
	/**
	 * 单个用量()步距
	 */
	@NotNull
	private BigDecimal standardQty;
	/**
	 * 归属地
	 */
	private String attributedArea;
	/**
	 * 是否导出(Y/N)
	 */
	private String isExported;
	/**
	 * 是否分配(Y/N)
	 */
	private String isDistributed;
	/**
	 * 是否生效
	 */
	private String enableFlag;
	/**
	 * 申请日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date createDate;
	/**
	 * 申请人
	 */
	private String createUser;
	/**
	 * 更新日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date updateDate;
	/**
	 * 更新人
	 */
	private String updateUser;

	/**
	 * 采购批次编号
	 */
	private String purchaseBatchCode;

	/**
	 * 收到开始时间
	 */
	private Date receiveStartTime;

	/**
	 * 收到技术时间
	 */
	private Date receiveEndTime;

	@ApiModelProperty(value = "标签名称")
	private String tagName;

	private String productionCompany;
	/**
	 * 资源号规则
	 */
	private String resourceNoRule;
	/**
	 * 资源申请明细结束
	 */
	private String resourceApplyEnd;

	private Long availableQuantity;

	// 实际消耗数量
	private Long consumptionQty;
	/**
	 * 继续生成剩余数量
	 */
	private BigDecimal remainQty;

	private String isLastNo;

	private String separator;
	/**
	 * gpon-sn厂商编码
	 */
	private String mCode;
	/**
	 * 产品大类
	 */
	private String productBigClass;
}
