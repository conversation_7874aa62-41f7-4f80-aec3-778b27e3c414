package com.zte.interfaces.dto.bytedance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 整机：数据库表
 *
 * <AUTHOR>
 * @date 2025-01-21 19:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PowerSourceInfoDTO extends CustomerParamsParentDTO {
    private static final long serialVersionUID = 1894268753937816511L;
    @JsonProperty(value = "电源功率")
    private String powerSupply;
    @JsonProperty(value = "最大输入电流")
    private String maxInputCurrent;
}
