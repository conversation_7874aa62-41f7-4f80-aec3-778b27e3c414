package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 个参生成表PageQuery
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-15 17:12:33
 */
@ApiModel
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpSpecialityParamPageQueryDTO extends PageDTO {

        /**
     * 个参ID
     */
    @ApiModelProperty(value = "个参ID")
    private String specialityParamId;
        /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
    private String templateId;
        /**
     * 物料代码
     */
    @ApiModelProperty(value = "物料代码")
    private String itemCode;
        /**
     * 物料版本
     */
    @ApiModelProperty(value = "物料版本")
    private String itemVersion;
        /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskId;
        /**
     * 生产单位
     */
    @ApiModelProperty(value = "生产单位")
    private String usageScope;
        /**
     * 运营商
     */
    @ApiModelProperty(value = "运营商")
    private String operator;
        /**
     * 发往地区
     */
    @ApiModelProperty(value = "发往地区")
    private String destinedArea;
        /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String applyTask;
        /**
     * 动态参数
     */
    @ApiModelProperty(value = "动态参数")
    private String fixedStr;
        /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Integer progress;
        /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
        /**
     * 创建日期 默认为当前时间
     */
    @ApiModelProperty(value = "创建日期 默认为当前时间")
    private Date createDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

        /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;
        /**
     * 更新日期 默认为当前时间
     */
    @ApiModelProperty(value = "更新日期 默认为当前时间")
    private Date lastUpdatedDate;
        /**
     * 是否有效 选项：Y=正常 N=已删除
     */
    @ApiModelProperty(value = "是否有效 选项：Y=正常 N=已删除")
    private String enabledFlag;

    /**
     * 是否同步MDS 选项：Y=已同步， N=未同步
     */
    @ApiModelProperty(value = "同步MDS状态")
    private String syncMdsStatus;
    
}

