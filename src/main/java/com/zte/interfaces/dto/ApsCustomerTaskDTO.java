package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/16 18:43
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApsCustomerTaskDTO extends TaskExtendedDTO {

    private String businessScene;

    private List<ApsCustInstructBomDTO> bomList;
}