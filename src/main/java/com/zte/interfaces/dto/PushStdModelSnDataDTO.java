package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;


/**
 * 标模任务条码推送信息表DTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-14 09:58:57
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushStdModelSnDataDTO extends PageDTO {

    /**
     * 
     */
    private String id;
    /**
     * 条码
     */
    @ApiModelProperty(value = "条码")
    private String sn;
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskNo;
    /**
     * 工厂id
     */
    @ApiModelProperty(value = "工厂id")
    private Integer factoryId;
    /**
     * 当前推送进程 25:整机测试数据上报 30:产品SN上报 40:成品入库上报
     */
    @ApiModelProperty(value = "当前推送进程 25:整机测试数据上报 30:产品SN上报 40:成品入库上报")
    private String currProcess;
    /**
     * 当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常
     */
    @ApiModelProperty(value = "当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常")
    private Integer pushStatus;
    /**
     * 当前进程数据推送时间
     */
    @ApiModelProperty(value = "当前进程数据推送时间")
    private Date pushDate;
    /**
     * 异常信息
     */
    @ApiModelProperty(value = "异常信息")
    private String errorMsg;
    /**
     * 当前进程推送失败次数
     */
    @ApiModelProperty(value = "当前进程推送失败次数")
    private Integer pushFailCount;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String createBy;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Date createDate;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String lastUpdatedBy;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Date lastUpdatedDate;
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String enabledFlag;
    @ApiModelProperty(value = "生产指令")
    private String billNo;
    @ApiModelProperty(value = "三段码")
    private String customerItemName;
    @ApiModelProperty(value = "物料代码")
    private String itemNo;
    @ApiModelProperty(value = "虚拟moc条码")
    private String virtualSn;
    @ApiModelProperty(value = "虚拟moc条码原始条码")
    private String originalSn;


    private List<String> snList;
    private String lastId;
    private Integer preDays;
    private Integer limit;


}

