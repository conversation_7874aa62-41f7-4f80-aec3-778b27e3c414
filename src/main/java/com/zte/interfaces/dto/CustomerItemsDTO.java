package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/4 11:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerItemsDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = 2700987257471052325L;
    /**
     * 参数列表
     */
    private Object paramsDetail;
    /**
     * 参数描述
     */
    private String paramsDetailStr;
    /**
     * PN码
     */
    private String pnCode;
    /**
     * 属性id
     */
    private String additionalInfoId;
    /**
     * 主键id
     **/
    private String id;
    /**
     * 客户名称
     **/
    private String customerName;
    /**
     * 客户id
     **/
    private String customerId;
    /**
     * 项目名称
     **/
    private String projectName;
    /**
     * 项目阶段
     **/
    private String projectPhase;
    /**
     * 项目类型
     * 项目类型0:自研主板 1:自研背板 2:自研子卡 3:整机 4:其他物料 5:整机-主板
     **/
    private String projectType;

    private List<String> projectTypeList;

    private String strProjectType;
    /**
     * 合作模式
     **/
    private String cooperationMode;
    /**
     * ZTE代码名称
     **/
    private String zteCodeName;
    /**
     * ZTE代码
     **/
    private String zteCode;

    private String sourceZteCode;
    /**
     * 客户物料型号
     **/
    private String customerMaterialType;
    /**
     * 客户部件类型
     **/
    private String customerComponentType;
    /**
     * 客户代码
     **/
    private String customerCode;

    /**
     * 完整mpn
     */
    private String wholeCustomerCode;

    private List<String> customerCodeList = Lists.newArrayList();

    /**
     * 客户代码
     **/
    private String originalCustomerCode;
    /**
     * 客户编码
     **/
    private String customerNumber;

    /**
     * 制造商物料名称
     **/
    private String customerItemName;
    /**
     * ZTE供应商
     **/
    private String zteSupplier;

    private String sourceZteSupplier;
    /**
     * 客户供应商
     **/
    private String customerSupplier;
    /**
     * ZTE规格型号
     **/
    private String zteBrandStyle;

    private String sourceZteBrandStyle;
    /**
     * 客户物料名称
     **/
    private String customerSpecification;
    /**
     * 客户型号
     **/
    private String customerModel;
    /**
     * 状态
     **/
    private String status;
    /**
     * 板码类型  0:单卡成品  1:单卡半成品 2:多模组成品  3:多模组半成品
     **/
    private String boardType;


    private String strStatus;
    /**
     * 备注
     **/
    private String remark;
    /**
     * 创建人
     **/
    private String createBy;
    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;
    /**
     * 更新人
     **/
    private String lastUpdatedBy;
    /**
     * 更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    /**
     * 是否有效Y/N
     **/
    private String enabledFlag;

    // 开始创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startCreateDate;

    // 结束创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date endCreateDate;

    // 开始更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startUpdateDate;

    // 结束更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date endUpdateDate;

    private String emailUrl;

    private String customerSubName;

    private List<String> itemNoList;
    private List<String> customerNameList;

    private String strCustomerComponentType;

    private String empNo;

    private String isZjFlag;

    private String isSelfFlag;

    private String strBoardType;

    /**
     * 项目类型
     */
    private String projType;
    private String strProjType;
    private Map<String, String> projTypeMap;

    /**
     * 原厂制造商名称
     */
    private String originalManufacturerName;

    /**
     * 功率规格
     */
    private String powerSpecification;
}
