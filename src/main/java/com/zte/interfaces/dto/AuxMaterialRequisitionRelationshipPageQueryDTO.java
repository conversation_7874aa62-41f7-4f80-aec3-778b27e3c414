package com.zte.interfaces.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.domain.model.AuxMaterialRequisitionRelationship;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 辅料领用关系表PageQuery
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-28 16:30:54
 */
@ApiModel
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)

@ColumnWidth(15)
@FieldNameConstants
@ExcelIgnoreUnannotated
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,wrapped = BooleanEnum.TRUE)
public class AuxMaterialRequisitionRelationshipPageQueryDTO extends PageDTO {

    private static final long serialVersionUID = 4294123697193783393L;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 变更单号
     */
    @ApiModelProperty(value = "变更单号")
    private String changeOrderNo;
    /**
     * 关系维度0:料单级 1:产品大类级
     */
    @ApiModelProperty(value = "")
    private int auxMaterialType;
    @ExcelProperty(value = "辅料类型", order = 0)
    private String auxMaterialTypeName;
    /**
     * 
     */
    @ApiModelProperty(value = "关系维度0:料单级 1:产品大类级")
    private int relationshipDimension;
    @ExcelProperty(value = "关系维度", order = 1)
    private String relationshipDimensionName;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private int status;
    /**
     * 料单代码
     */
    @ApiModelProperty(value = "料单代码")
    @ExcelProperty(value = "料单代码", order = 2)
    private String itemNo;
    /**
     * 料单名称
     */
    @ApiModelProperty(value = "料单名称")
    @ExcelProperty(value = "料单名称", order = 3)
    private String itemName;
    /**
     * PCB版本
     */
    @ApiModelProperty(value = "PCB版本")
    private String pcbVersion;
    /**
     * 产品大类
     */
    @ApiModelProperty(value = "产品大类")
    @ExcelProperty(value = "产品大类", order = 4)
    private String productCategory;
    /**
     * 辅料代码
     */
    @ApiModelProperty(value = "辅料代码")
    @ExcelProperty(value = "辅料代码*", order = 4)
    private String auxMaterialCode;
    @ExcelProperty(value = "单位", order = 6)
    private String unit;
    @ExcelProperty(value = "单位用量", order = 7)
    private String unitUsage;
    /**
     * 当前处理人
     */
    @ApiModelProperty(value = "当前处理人")
    private String currentProcessor;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间 默认为当前时间
     */
    @ApiModelProperty(value = "创建时间 默认为当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createStartDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createEndDate;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;
    /**
     * 更新日期 默认为当前时间
     */
    @ApiModelProperty(value = "更新日期 默认为当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedStartDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedEndDate;
    /**
     * 是否有效 选项：Y=正常 N=已删除
     */
    @ApiModelProperty(value = "是否有效 选项：Y=正常 N=已删除")
    private String enabledFlag;
    @ApiModelProperty(value = "变更类型：0新增 1变更 2删除 3撤销审批")
    private String changeType;
    private String handledByMe;
    private List<AuxMaterialRequisitionRelationship> approveList;
}

