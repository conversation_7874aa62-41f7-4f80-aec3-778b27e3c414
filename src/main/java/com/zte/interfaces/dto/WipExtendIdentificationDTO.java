package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/8 10:29
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class WipExtendIdentificationDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * // TODO remarks
     **/
    private String identiId;

    /**
     * // TODO remarks
     **/
    private String sn;

    /**
     * // TODO remarks
     **/
    private String formSn;

    /**
     * // TODO remarks
     **/
    private String itemNo;

    /**
     * // TODO remarks
     **/
    private String formType;

    private String formTypeName;

    /**
     * // TODO remarks
     **/
    private String processCode;

    //子工序名称
    private String processName;

    /**
     * // TODO remarks
     **/
    private BigDecimal formQty;

    /**
     * // TODO remarks
     **/
    private String isIdentifier;
    private String complete;

    //物料版本
    private String bomVersion;

    /**
     * 中试对接标志
     **/
    private String toZsFlag;


    /**
     * // TODO remarks
     **/
    private String remark;

    /**
     * // TODO remarks
     **/
    private String createBy;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedDate;


    private String lastUpdatedDateStr;
    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private String attribute1;

    /**
     * // TODO remarks
     **/
    private String attribute2;

    /**
     * // TODO remarks
     **/
    private String attribute3;

    /**
     * // TODO remarks
     **/
    private String attribute4;

    /**
     * // TODO remarks
     **/
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date attribute5;

    private String prodPlanId;

    private String taskNo;

    /**
     * 整机条码物料代码
     */
    private String formItemNo;

    //整机条码物料代码名称
    private String formItemName;
    //整机条码子工序
    private String formProcessCode;
    //整机条码子工序名称
    private String formProcessName;
    /**
     * 整机条码环保属性
     */
    private String formHbCode;

    /**
     * 环保属性
     */
    private String hbCode;

    //是否中试
    private String isZsIdentification;
    //中试扫描顺序
    private String zsScanOrder;

    private BigDecimal qty;

    private String replaceItemNo;

    /**
     * 主卡料单
     */
    private String mainProductCode;

    private String day;

    private String batch;

    private String itemName;

    private List<String> formSnList;

    private FixBomDetailDTO fixBomDetailDTO;

    private String virtualSn;
    private String itemSupplierNo;
    private String customerComponentType;

    /**
     * 客户物料型号(对应mpn)
     */
    private String customerItemStyle;
}
