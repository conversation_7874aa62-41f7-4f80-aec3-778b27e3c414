package com.zte.interfaces.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;

/**
 * BOM信息(头+明细)实体
 * 
 * <AUTHOR>
 * 
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class BBomInfoDTO implements Serializable {

    private static final long serialVersionUID = -6968850645918781019L;

    @ApiModelProperty(value = "BOM头主键", hidden = true)
    private String bomHeaderId;

    @ApiModelProperty(value = "零件料号(物料代码)")
    private String productCode;

    @ApiModelProperty(value = "零件类型")
    private String productType;

    @ApiModelProperty(value = "英文描述")
    private String engDesc;

    @ApiModelProperty(value = "中文描述")
    private String chiDesc;

    @ApiModelProperty(value = "版本")
    private String verNo;

    @ApiModelProperty(value = "系统来源")
    private String sourceSystem;

    @ApiModelProperty(value = "系统来源id")
    private String sourceItemId;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createBy;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "最后修改人", hidden = true)
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后修改时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "组织ID")
    private BigDecimal orgId;

    @ApiModelProperty(value = "工厂ID")
    private BigDecimal factoryId;

    @ApiModelProperty(value = "实体ID")
    private BigDecimal entityId;

    @ApiModelProperty(value = "明细列表")
    private java.util.List<BBomDetailDTO> listDetail;

    @ApiModelProperty(value = "是否获取明细")
    private Boolean getDetail = true;

    @ApiModelProperty(value = "排序字段")
    private java.lang.String sort;

    @ApiModelProperty(value = "排序方式(默认升序,设为desc时降序)")
    private java.lang.String order;

    @ApiModelProperty(value = "请求的页码", hidden = true)
    private java.lang.String page;

    @ApiModelProperty(value = "每页条数", hidden = true)
    private java.lang.String rows;

    public void setBomHeaderId(String bomHeaderId) {

        this.bomHeaderId = bomHeaderId;
    }

    public String getBomHeaderId() {

        return bomHeaderId;
    }

    public void setProductCode(String productCode) {

        this.productCode = productCode;
    }

    public String getProductCode() {

        return productCode;
    }

    public void setProductType(String productType) {

        this.productType = productType;
    }

    public String getProductType() {

        return productType;
    }

    public void setEngDesc(String engDesc) {

        this.engDesc = engDesc;
    }

    public String getEngDesc() {

        return engDesc;
    }

    public void setChiDesc(String chiDesc) {

        this.chiDesc = chiDesc;
    }

    public String getChiDesc() {

        return chiDesc;
    }

    public void setVerNo(String verNo) {

        this.verNo = verNo;
    }

    public String getVerNo() {

        return verNo;
    }

    public void setSourceSystem(String sourceSystem) {

        this.sourceSystem = sourceSystem;
    }

    public String getSourceSystem() {

        return sourceSystem;
    }

    public void setSourceItemId(String sourceItemId) {

        this.sourceItemId = sourceItemId;
    }

    public String getSourceItemId() {

        return sourceItemId;
    }

    public void setCreateBy(String createBy) {

        this.createBy = createBy;
    }

    public String getCreateBy() {

        return createBy;
    }

    public void setCreateDate(Date createDate) {

        this.createDate = createDate;
    }

    public Date getCreateDate() {

        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {

        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {

        return lastUpdatedDate;
    }

    public void setOrgId(BigDecimal orgId) {

        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {

        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {

        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {

        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {

        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {

        return entityId;
    }

    public java.util.List<BBomDetailDTO> getListDetail() {

        return listDetail;
    }

    public void setListDetail(java.util.List<BBomDetailDTO> listDetail) {

        this.listDetail = listDetail;
    }

    public java.lang.String getSort() {

        return sort;
    }

    public void setSort(java.lang.String sort) {

        this.sort = sort;
    }

    public java.lang.String getOrder() {

        return order;
    }

    public void setOrder(java.lang.String order) {

        this.order = order;
    }

    public java.lang.String getPage() {

        return page;
    }

    public void setPage(java.lang.String page) {

        this.page = page;
    }

    public java.lang.String getRows() {

        return rows;
    }

    public void setRows(java.lang.String rows) {

        this.rows = rows;
    }

    @ApiModelProperty("0不包含，1包含")
    private Integer includePcbFpc;

    public Integer getIncludePcbFpc() {
        return includePcbFpc;
    }

    public void setIncludePcbFpc(Integer includePcbFpc) {
        this.includePcbFpc = includePcbFpc;
    }

    public Boolean isGetDetail() {
        return getDetail;
    }

    public void setGetDetail(Boolean getDetail) {
        this.getDetail = getDetail;
    }
}
