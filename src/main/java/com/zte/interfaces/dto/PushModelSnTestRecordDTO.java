package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


import java.util.Date;


/**
 * 标模任务条码推送测试记录表DTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-15 08:53:49
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushModelSnTestRecordDTO {

    /**
     *
     */
    @ApiModelProperty(value = "requestId")
    private String requestId;
    @ApiModelProperty(value = "stationId")
    private String stationId;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String type;
    /**
     * 生产指令
     */
    @ApiModelProperty(value = "生产指令")
    private String directiveNumber;
    /**
     * 中兴任务号
     */
    @ApiModelProperty(value = "中兴任务号")
    private String workorderId;
    /**
     * 整机条码
     */
    @ApiModelProperty(value = "整机条码")
    private String sn;
    @ApiModelProperty(value = "设备条码")
    private String nodeSn;
    /**
     * 制造品牌名
     */
    @ApiModelProperty(value = "制造品牌名")
    private String brand;
    /**
     * 主板条码，存在多个时，使用,分隔
     */
    @ApiModelProperty(value = "主板条码，存在多个时，使用,分隔")
    private String boardSn;
    /**
     * 任务附加属性表-三段机型
     */
    @ApiModelProperty(value = "任务附加属性表-三段机型")
    private String model;
    /**
     * 站位名称
     */
    @ApiModelProperty(value = "站位名称")
    private String stationName;
    /**
     * 检测的开始时间
     */
    @ApiModelProperty(value = "检测的开始时间")
    private String startedTime;
    /**
     * 检测的结束时间
     */
    @ApiModelProperty(value = "检测的结束时间")
    private String finishedTime;
    /**
     * 检测结果,取值范围：Pass/Fail
     */
    @ApiModelProperty(value = "检测结果,取值范围：Pass/Fail")
    private String result;
    /**
     * 工序站位检查结果失败时的原因说明
     */
    @ApiModelProperty(value = "工序站位检查结果失败时的原因说明")
    private String message;
    /**
     * oss测试文件
     */
    @ApiModelProperty(value = "oss测试文件")
    private String ossFileKey;
    @ApiModelProperty(value = "oss测试文件地址")
    private String ossFileUrl;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String manufacturerName;
    @ApiModelProperty(value = "维修类型")
    private String actionCode;
    @ApiModelProperty(value = "维修详情")
    private String actionMsg;
    @ApiModelProperty(value = "维修时间")
    private String finishReworkTime;
    @ApiModelProperty(value = "送修时间")
    private String reworkTime;
    /**
     *
     */
    @ApiModelProperty(value = "enabledFlag")
    private String enabledFlag;
    /**
     *
     */
    @ApiModelProperty(value = "createDate")
    private Date createDate;
    /**
     *
     */
    @ApiModelProperty(value = "lastUpdatedDate")
    private Date lastUpdatedDate;
    /**
     *
     */
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /**
     *
     */
    @ApiModelProperty(value = "lastUpdatedBy")
    private String lastUpdatedBy;
    @ApiModelProperty(value = "状态 0 待推送 1 整机测试数据推送成功  2 整机测试数据推送失败")
    private String uploadStatus;
    @ApiModelProperty(value = "整机测试数据推送错误信息")
    private String uploadMsg;
    @ApiModelProperty(value = "状态 0 待推送 1 站位分析结果推送成功  2 站位分析结果推送失败")
    private String stationUploadStatus;
    @ApiModelProperty(value = "站位分析结果推送结果")
    private String stationUploadMsg;
    @ApiModelProperty(value = "状态 0 待推送 1 oss日志文件推送成功  2 oss日志文件推送失败")
    private String fileUploadStatus;
    @ApiModelProperty(value = "oss日志文件推送结果")
    private String fileUploadMsg;
    private Integer factoryId;
}

