package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <p>标模推送信息SN明细表</p>
 *
 * <AUTHOR>
 * @since 2025/5/12 9:38
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushStdModelSnDataHandleDTO {

    /** id主键 */
    private String id;

    private String sn;

    private String taskNo;

    private Integer factoryId;
    /**
     * 仓库
     */
    private String stockName;

    private String currProcess;

    /** 装配关系 */
    private List<WipExtendIdentificationDTO> wipExtendIdentifications;
}
