/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;

/**
 * BOM头实体
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class BBomHeaderDTO extends PageDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 文档云ID
     */
    private String cloudDiskId;

    /**
     * 文档云文件名
     */
    private String fileName;

    @ApiModelProperty(value = "BOM头主键", hidden = true)
    private String bomHeaderId;

    @ApiModelProperty(value = "零件料号")
    private String productCode;

    @ApiModelProperty(value = "零件类型")
    private String productType;

    @ApiModelProperty(value = "英文描述")
    private String engDesc;

    @ApiModelProperty(value = "中文描述")
    private String chiDesc;

    @ApiModelProperty(value = "版本")
    private String verNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "系统来源")
    private String sourceSystem;

    /**
     * // TODO remarks
     **/
    private String sourceItemId;

	private String standardCost;

	public String getStandardCost() {
		return standardCost;
	}

	public void setStandardCost(String standardCost) {
		this.standardCost = standardCost;
	}

	@ApiModelProperty(value = "创建人", hidden = true)
    private String createBy;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "最后修改人", hidden = true)
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后修改时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "有效标志", hidden = true, allowableValues = "Y,N")
    private String enabledFlag;

    @ApiModelProperty(value = "组织ID")
    private BigDecimal orgId;

    @ApiModelProperty(value = "工厂ID")
    private BigDecimal factoryId;

    @ApiModelProperty(value = "实体ID")
    private BigDecimal entityId;

    @ApiModelProperty(value = "物料代码")
    private String itemCode;

    @ApiModelProperty(value = "是否整机子卡")
    private String zjSubcardFlag;

	@ApiModelProperty(value = "料单代码串")
	private List<String> inProductCodes;

	@ApiModelProperty(value = "价格")
	private String lastPrice;

	public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public void setBomHeaderId(String bomHeaderId) {

        this.bomHeaderId = bomHeaderId;
    }

    public String getBomHeaderId() {

        return bomHeaderId;
    }

    public void setProductCode(String productCode) {

        this.productCode = productCode;
    }

    public String getProductCode() {

        return productCode;
    }

    public void setProductType(String productType) {

        this.productType = productType;
    }

    public String getProductType() {

        return productType;
    }

    public String getCloudDiskId() {
        return cloudDiskId;
    }

    public void setCloudDiskId(String cloudDiskId) {
        this.cloudDiskId = cloudDiskId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void setEngDesc(String engDesc) {

        this.engDesc = engDesc;
    }

    public String getEngDesc() {

        return engDesc;
    }

    public void setChiDesc(String chiDesc) {

        this.chiDesc = chiDesc;
    }

    public String getChiDesc() {

        return chiDesc;
    }

    public void setVerNo(String verNo) {

        this.verNo = verNo;
    }

    public String getVerNo() {

        return verNo;
    }

    public void setRemark(String remark) {

        this.remark = remark;
    }

    public String getRemark() {

        return remark;
    }

    public void setSourceSystem(String sourceSystem) {

        this.sourceSystem = sourceSystem;
    }

    public String getSourceSystem() {

        return sourceSystem;
    }

    public void setSourceItemId(String sourceItemId) {

        this.sourceItemId = sourceItemId;
    }

    public String getSourceItemId() {

        return sourceItemId;
    }

    public void setCreateBy(String createBy) {

        this.createBy = createBy;
    }

    public String getCreateBy() {

        return createBy;
    }

    public void setCreateDate(Date createDate) {

        this.createDate = createDate;
    }

    public Date getCreateDate() {

        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {

        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {

        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {

        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {

        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {

        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {

        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {

        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {

        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {

        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {

        return entityId;
    }

    public String getZjSubcardFlag() {
        return zjSubcardFlag;
    }

    public void setZjSubcardFlag(String zjSubcardFlag) {
        this.zjSubcardFlag = zjSubcardFlag;
    }

	public List<String> getInProductCodes() {
		return inProductCodes;
	}

	public void setInProductCodes(List<String> inProductCodes) {
		this.inProductCodes = inProductCodes;
	}

	public String getLastPrice() {
		return lastPrice;
	}

	public void setLastPrice(String lastPrice) {
		this.lastPrice = lastPrice;
	}
}
