/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-02-13
 * 修改历史 :
 *   1. [2019-02-13] 创建文件 by 6396000647
 **/
package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class EdiSoSDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal registerQty;
	private String binder;

	@ApiModelProperty(value = "任务号")
	private String taskNo;

	private BigDecimal serialkey;

	@ApiModelProperty(value = "凭证单号")
	private String externalorderkey2;

	@ApiModelProperty(value = "领料单号")
	private String orderNo;

	@ApiModelProperty(value = "物料代码")
	private String sku;

	@ApiModelProperty(value = "箱号")
	private String fromId;

	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "领料单号List")
	private List<String> externalorderkey2List;

	@ApiModelProperty(value = "仓库号List")
	private String warehouseIds;

	@ApiModelProperty(value = "客户部件类型")
	private String customerComponentType;

	@ApiModelProperty(value = "客户代码")
	private String customerCode;

	@ApiModelProperty(value = "发料仓")
	private String whseId;

	private String hsusr1;

	@ApiModelProperty(value = "要料单号")
	private String orderId;

	@ApiModelProperty(value = "物料代码")
	private String itemId;

	@ApiModelProperty(value = "物料名称")
	private String itemName;

	@ApiModelProperty(value = "X-VALUE")
	private String xValue;

	@ApiModelProperty(value = "WMS接口地址")
	private String wmsAddress;

	public String getOrderNo() {
		return orderNo;
	}

	public EdiSoSDTO setOrderNo(String orderNo) {
		this.orderNo = orderNo;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTaskNo() {
		return taskNo;
	}

	public void setTaskNo(String taskNo) {
		this.taskNo = taskNo;
	}

	public String getWhseId() {
		return whseId;
	}

	public void setWhseId(String whseId) {
		this.whseId = whseId;
	}

	public String getWarehouseIds() {
		return warehouseIds;
	}

	public void setWarehouseIds(String warehouseIds) {
		this.warehouseIds = warehouseIds;
	}

	public List<String> getExternalorderkey2List() {
		return externalorderkey2List;
	}

	public void setExternalorderkey2List(List<String> externalorderkey2List) {
		this.externalorderkey2List = externalorderkey2List;
	}

	public String getFromId() {
		return fromId;
	}

	public void setFromId(String fromId) {
		this.fromId = fromId;
	}

	public String getxValue() {
		return xValue;
	}

	public void setxValue(String xValue) {
		this.xValue = xValue;
	}

	public String getWmsAddress() {
		return wmsAddress;
	}

	public void setWmsAddress(String wmsAddress) {
		this.wmsAddress = wmsAddress;
	}

	/*
	 * 物料条码 材料取物料条码,单板取序列号 lottable02
	 * */
	private String barcodeSeries;

	/**
	 * 15位料单代码，将原数据的代码和版本连接合并
	 * HSUSR2+HSUSR4
	 */
	@ApiModelProperty(value = "单板代码")
	private String pcbCode;

	private BigDecimal qty;

	private String supplierNo;

	/*
	 *
	 * */
	private String supplierName;

	@ApiModelProperty(value = "物料UUID")
	private String itemUuid;

	/*
	 *
	 * */
	private String brandStyle;

	/*
	 *
	 * */
	private String brandName;

	/*
	 *
	 * */
	private String vertion;

	private String ref45;


	@ApiModelProperty(value = "子项目录值")
	private String lookupMeaning;

	@ApiModelProperty(value = "子项代码")
	private String lookupCode;
	@ApiModelProperty(value = "目录代码")
	private String lookupType;
	@ApiModelProperty(value = "开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;
	@ApiModelProperty(value = "结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;
	@ApiModelProperty(value = "REELID条码")
	private String serialnumber;
	@ApiModelProperty(value = "REELID数量")
	private BigDecimal reelidQty;
	@ApiModelProperty(value = "单据号")
	private String billNo;
	@ApiModelProperty(value = "行号")
	private BigDecimal reqLineIndex;
	@ApiModelProperty(value = "同步WMS INFOR数据明细")
	private ToWmsItemDTO taskDetail;
	@ApiModelProperty(value = "调拨单类型")
	private String taskTypeCode;

	public String getBillNo() {
		return billNo;
	}

	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}

	public BigDecimal getReqLineIndex() {
		return reqLineIndex;
	}

	public void setReqLineIndex(BigDecimal reqLineIndex) {
		this.reqLineIndex = reqLineIndex;
	}

	public ToWmsItemDTO getTaskDetail() {
		return taskDetail;
	}

	public void setTaskDetail(ToWmsItemDTO taskDetail) {
		this.taskDetail = taskDetail;
	}

	public String getTaskTypeCode() {
		return taskTypeCode;
	}

	public void setTaskTypeCode(String taskTypeCode) {
		this.taskTypeCode = taskTypeCode;
	}

	public String getSerialnumber() {
		return serialnumber;
	}

	public void setSerialnumber(String serialnumber) {
		this.serialnumber = serialnumber;
	}

	public BigDecimal getReelidQty() {
		return reelidQty;
	}

	public void setReelidQty(BigDecimal reelidQty) {
		this.reelidQty = reelidQty;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getLookupMeaning() {
		return lookupMeaning;
	}

	public void setLookupMeaning(String lookupMeaning) {
		this.lookupMeaning = lookupMeaning;
	}

	public String getLookupCode() {
		return lookupCode;
	}

	public void setLookupCode(String lookupCode) {
		this.lookupCode = lookupCode;
	}

	public String getLookupType() {
		return lookupType;
	}

	public void setLookupType(String lookupType) {
		this.lookupType = lookupType;
	}

	@ApiModelProperty(value = "生产批次")
	private String lotNo;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date addDate;

	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date editDate;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	@ApiModelProperty(value = "开始行数")
	private long startRow;

	@ApiModelProperty(value = "结束行数")
	private long endRow;

	@ApiModelProperty(value = "请求的页码")
	private Integer page = 0;

	@ApiModelProperty(value = "每页条数")
	private Integer rows = 0;

	@ApiModelProperty(value = "开始时间")
	private String startTimeStr;

	@ApiModelProperty(value = "结束时间")
	private String endTimeStr;

	@ApiModelProperty(value = "单行号")
	private BigDecimal externLineNo;

	public BigDecimal getExternLineNo() {
		return externLineNo;
	}

	public void setExternLineNo(BigDecimal externLineNo) {
		this.externLineNo = externLineNo;
	}

	@ApiModelProperty(value = "单行号")
	private BigDecimal externlineno;

	public BigDecimal getExternlineno() {
		return externlineno;
	}

	public void setExternlineno(BigDecimal externlineno) {
		this.externlineno = externlineno;
	}

	@ApiModelProperty(value = "行号")
	private BigDecimal orderlinenumber;

	public BigDecimal getOrderlinenumber() {
		return orderlinenumber;
	}

	public void setOrderlinenumber(BigDecimal orderlinenumber) {
		this.orderlinenumber = orderlinenumber;
	}



	@ApiModelProperty(value = "发货数量")
	private String shippedQty;

	@ApiModelProperty(value = "UUID")
	private String lottable07;

	@ApiModelProperty(value = "铅属性/环保属性代码")
	private String leadCode;

	@ApiModelProperty(value = "配送去向")
	private String ref02;

	@ApiModelProperty(value = "计划数量")
	private String ref01;

	@ApiModelProperty(value = "累计发货")
	private BigDecimal qtyShipedTotal;

	@ApiModelProperty(value = "计划跟踪单")
	private String externOrderKey;

	@ApiModelProperty(value = "需求时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date requestEdshipDate;

	@ApiModelProperty(value = "item id")
	private String susr2;


	@ApiModelProperty(value = "单据ID")
	private String billId;


	@ApiModelProperty(value = "计划数量")
	private String planQty;

	@ApiModelProperty(value = "发货组织")
	private String sourceOrgId;


	@ApiModelProperty(value = "发货库")
	private String sourceWarehouse;

	@ApiModelProperty(value = "目标组织")
	private String targetOrgId;


	@ApiModelProperty(value = "目标库")
	private String targetWarehouse;


	@ApiModelProperty(value = "用途")
	private String purpose;

	@ApiModelProperty(value = "单据类型")
	private String billType;


	@ApiModelProperty(value = "配送去向")
	private String position;

	@ApiModelProperty(value = "提交时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date requestedDate;

	@ApiModelProperty(value = "提交人")
	private String requestor;

	@ApiModelProperty(value = "线体")
	private String lineCode;

	@ApiModelProperty(value = "单板版本")
	private String bomVersion;

	@ApiModelProperty(value = "物料条码")
	private String itemSn;

	@ApiModelProperty(value = "单板名称")
	private String pcbName;

	/**
	 *
	 */
	private Date ediAdddate;


	/**
	 *
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date ediEditdate;


	/**
	 *
	 */
	private BigDecimal symbol;


	/**
	 *
	 */
	private BigDecimal invsymbol;


	/**
	 *
	 */
	private BigDecimal invfaildsymbol;


	/**
	 *
	 */
	private BigDecimal feedbackflag;


	/**
	 *
	 */
	private Date feedbackdate;


	/**
	 *
	 */
	private String orderkey;


	/**
	 *
	 */
	private String externorderkey;


	/**
	 *
	 */
	private String type;


	/**
	 *
	 */
	private String storerkey;



	/**
	 *
	 */
	private String ordersid;


	/**
	 *
	 */
	private String hsusr2;


	/**
	 *
	 */
	private String hsusr3;


	/**
	 *
	 */
	private String hsusr4;



	/**
	 *
	 */
	private String href03;



	/**
	 *
	 */
	private String href05;


	/**
	 *
	 */
	private String href06;



	/**
	 *
	 */
	private String href09;


	/**
	 *
	 */
	private String href10;



	/**
	 *
	 */
	private String href12;


	/**
	 *
	 */
	private String href13;


	/**
	 *
	 */
	private String href14;


	/**
	 *
	 */
	private String href15;


	/**
	 *
	 */
	private String href16;


	/**
	 *
	 */
	private String href17;


	/**
	 *
	 */
	private String href18;


	/**
	 *
	 */
	private String href19;


	/**
	 *
	 */
	private String href20;


	/**
	 *
	 */
	private String href21;


	/**
	 *
	 */
	private String href22;


	/**
	 *
	 */
	private String href23;


	/**
	 *
	 */
	private String href24;


	/**
	 *
	 */
	private String href25;


	/**
	 *
	 */
	private Date href26;


	/**
	 *
	 */
	private Date href27;


	/**
	 *
	 */
	private Date href28;


	/**
	 *
	 */
	private Date href29;


	/**
	 *
	 */
	private String href33;


	/**
	 *
	 */
	private String href34;


	/**
	 *
	 */
	private String href35;


	/**
	 *
	 */
	private String href36;


	/**
	 *
	 */
	private String href37;


	/**
	 *
	 */
	private String href38;


	/**
	 *
	 */
	private String href39;


	/**
	 *
	 */
	private String href40;


	/**
	 *
	 */
	private String href41;


	/**
	 *
	 */
	private String href42;


	/**
	 *
	 */
	private String href43;


	/**
	 *
	 */
	private String href44;


	/**
	 *
	 */
	private String href45;


	/**
	 *
	 */
	private String href46;


	/**
	 *
	 */
	private String href47;


	/**
	 *
	 */
	private String href48;


	/**
	 *
	 */
	private String href49;


	/**
	 *
	 */
	private String href50;


	/**
	 *
	 */
	private String href51;


	/**
	 *
	 */
	private String href52;


	/**
	 *
	 */
	private String href53;


	/**
	 *
	 */
	private String href54;


	/**
	 *
	 */
	private String href55;


	/**
	 *
	 */
	private String href56;


	/**
	 *
	 */
	private String href57;


	/**
	 *
	 */
	private String href58;


	/**
	 *
	 */
	private String href59;


	/**
	 *
	 */
	private String href60;









	/**
	 *
	 */
	private String packkey;


	/**
	 *
	 */
	private BigDecimal originalqty;


	/**
	 *
	 */
	private BigDecimal qtyshipedtotal;


	/**
	 *
	 */
	private BigDecimal shippedqty;


	/**
	 *
	 */
	private String uom;


	/**
	 *
	 */
	private BigDecimal allowoverpick;


	/**
	 *
	 */
	private String preallocatestrategykey;


	/**
	 *
	 */
	private String allocatestrategykey;


	/**
	 *
	 */
	private String allocatestrategytype;


	/**
	 *
	 */
	private BigDecimal shelflife;


	/**
	 *
	 */
	private String rotation;


	/**
	 *
	 */
	private String skurotation;


	/**
	 *
	 */
	private String lot;


	/**
	 *
	 */
	private String lottable01;


	/**
	 *
	 */
	private String lottable02;


	/**
	 *
	 */
	private String lottable03;


	/**
	 *
	 */
	private Date lottable04;


	/**
	 *
	 */
	private Date lottable05;


	/**
	 *
	 */
	private String lottable06;

	private String lottable08;

	public String getLottable08() {
		return lottable08;
	}

	public void setLottable08(String lottable08) {
		this.lottable08 = lottable08;
	}

	/**
	 *
	 */
	private String lottable09;


	/**
	 *
	 */
	private String lottable10;


	/**
	 *
	 */
	private Date lottable11;


	/**
	 *
	 */
	private Date lottable12;


	/**
	 *
	 */
	private String susr1;


	/**
	 *
	 */
	private String susr3;


	/**
	 *
	 */
	private String susr4;


	/**
	 *
	 */
	private String susr5;


	/**
	 *
	 */
	private String notes2;


	/**
	 *
	 */
	private String notes;


	/**
	 *
	 */
	private String ref03;


	/**
	 *
	 */
	private String ref04;


	/**
	 *
	 */
	private String ref05;


	/**
	 *
	 */
	private String ref06;


	/**
	 *
	 */
	private String ref07;


	/**
	 *
	 */
	private String ref08;


	/**
	 *
	 */
	private String ref09;


	/**
	 *
	 */
	private String ref10;


	/**
	 *
	 */
	private String ref11;


	/**
	 *
	 */
	private String ref12;


	/**
	 *
	 */
	private String ref13;


	/**
	 *
	 */
	private String ref14;


	/**
	 *
	 */
	private String ref15;


	/**
	 *
	 */
	private String ref16;


	/**
	 *
	 */
	private String ref17;


	/**
	 *
	 */
	private String ref18;


	/**
	 *
	 */
	private String ref19;


	/**
	 *
	 */
	private String ref20;


	/**
	 *
	 */
	private String ref21;


	/**
	 *
	 */
	private String ref22;


	/**
	 *
	 */
	private String ref23;


	/**
	 *
	 */
	private String ref24;


	/**
	 *
	 */
	private String ref25;


	/**
	 *
	 */
	private Date ref26;


	/**
	 *
	 */
	private Date ref27;


	/**
	 *
	 */
	private Date ref28;


	/**
	 *
	 */
	private Date ref29;


	/**
	 *
	 */
	private Date ref30;


	/**
	 *
	 */
	private String ref31;


	/**
	 *
	 */
	private String ref32;


	/**
	 *
	 */
	private String ref33;


	/**
	 *
	 */
	private String ref34;


	/**
	 *
	 */
	private String ref35;


	/**
	 *
	 */
	private String ref36;


	/**
	 *
	 */
	private String ref37;


	/**
	 *
	 */
	private String ref38;


	/**
	 *
	 */
	private String ref39;


	/**
	 *
	 */
	private String ref40;


	/**
	 *
	 */
	private String ref41;


	/**
	 *
	 */
	private String ref42;


	/**
	 *
	 */
	private String ref43;


	/**
	 *
	 */
	private String ref44;



	/**
	 *
	 */
	private String ref46;


	/**
	 *
	 */
	private String ref47;


	/**
	 *
	 */
	private String ref48;


	/**
	 *
	 */
	private String ref49;


	/**
	 *
	 */
	private String ref50;


	/**
	 *
	 */
	private String ref51;


	/**
	 *
	 */
	private String ref52;


	/**
	 *
	 */
	private String ref53;


	/**
	 *
	 */
	private String ref54;


	/**
	 *
	 */
	private String ref55;


	/**
	 *
	 */
	private String ref56;


	/**
	 *
	 */
	private String ref57;


	/**
	 *
	 */
	private String ref58;


	/**
	 *
	 */
	private String ref59;


	/**
	 *
	 */
	private String ref60;


	/**
	 *
	 */
	private Date adddate;


	/**
	 *
	 */
	private Date editdate;


	/**
	 *
	 */
	private String editwho;


	/**
	 *
	 */
	private String whseid;


	/**
	 *
	 */
	private String id;


	/**
	 *
	 */
	private BigDecimal smallamountflag;


	/**
	 *
	 */
	private String remark;


	/**
	 *
	 */
	private Date endtime;


	/**
	 *
	 */
	private String href61;


	/**
	 *
	 */
	private String href62;


	/**
	 *
	 */
	private String href63;


	/**
	 *
	 */
	private String href64;

	private String href11;

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getCustomerComponentType() {
		return customerComponentType;
	}

	public void setCustomerComponentType(String customerComponentType) {
		this.customerComponentType = customerComponentType;
	}

	public String getHref11() {
		return href11;
	}

	public void setHref11(String href11) {
		this.href11 = href11;
	}

	public BigDecimal getSerialkey() {
		return serialkey;
	}

	public void setSerialkey(BigDecimal serialkey) {
		this.serialkey = serialkey;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getBarcodeSeries() {
		return barcodeSeries;
	}

	public void setBarcodeSeries(String barcodeSeries) {
		this.barcodeSeries = barcodeSeries;
	}

	public String getPcbCode() {
		return pcbCode;
	}

	public void setPcbCode(String pcbCode) {
		this.pcbCode = pcbCode;
	}

	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	public String getSupplierNo() {
		return supplierNo;
	}

	public void setSupplierNo(String supplierNo) {
		this.supplierNo = supplierNo;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public String getItemUuid() {
		return itemUuid;
	}

	public void setItemUuid(String itemUuid) {
		this.itemUuid = itemUuid;
	}

	public String getBrandStyle() {
		return brandStyle;
	}

	public void setBrandStyle(String brandStyle) {
		this.brandStyle = brandStyle;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getVertion() {
		return vertion;
	}

	public void setVertion(String vertion) {
		this.vertion = vertion;
	}

	public String getRef45() {
		return ref45;
	}

	public void setRef45(String ref45) {
		this.ref45 = ref45;
	}


	public String getLotNo() {
		return lotNo;
	}

	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}

	public Date getAddDate() {
		return addDate;
	}

	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}

	public Date getEditDate() {
		return editDate;
	}

	public void setEditDate(Date editDate) {
		this.editDate = editDate;
	}

	public long getStartRow() {
		return startRow;
	}

	public void setStartRow(long startRow) {
		this.startRow = startRow;
	}

	public long getEndRow() {
		return endRow;
	}

	public void setEndRow(long endRow) {
		this.endRow = endRow;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getRows() {
		return rows;
	}

	public void setRows(Integer rows) {
		this.rows = rows;
	}

	public String getStartTimeStr() {
		return startTimeStr;
	}

	public void setStartTimeStr(String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}

	public String getEndTimeStr() {
		return endTimeStr;
	}

	public void setEndTimeStr(String endTimeStr) {
		this.endTimeStr = endTimeStr;
	}



	public String getShippedQty() {
		return shippedQty;
	}

	public void setShippedQty(String shippedQty) {
		this.shippedQty = shippedQty;
	}

	public String getLottable07() {
		return lottable07;
	}

	public void setLottable07(String lottable07) {
		this.lottable07 = lottable07;
	}

	public String getLeadCode() {
		return leadCode;
	}

	public void setLeadCode(String leadCode) {
		this.leadCode = leadCode;
	}

	public String getRef02() {
		return ref02;
	}

	public void setRef02(String ref02) {
		this.ref02 = ref02;
	}

	public String getRef01() {
		return ref01;
	}

	public void setRef01(String ref01) {
		this.ref01 = ref01;
	}

	public BigDecimal getQtyShipedTotal() {
		return qtyShipedTotal;
	}

	public void setQtyShipedTotal(BigDecimal qtyShipedTotal) {
		this.qtyShipedTotal = qtyShipedTotal;
	}

	public String getExternOrderKey() {
		return externOrderKey;
	}

	public void setExternOrderKey(String externOrderKey) {
		this.externOrderKey = externOrderKey;
	}

	public Date getRequestEdshipDate() {
		return requestEdshipDate;
	}

	public void setRequestEdshipDate(Date requestEdshipDate) {
		this.requestEdshipDate = requestEdshipDate;
	}

	public String getSusr2() {
		return susr2;
	}

	public void setSusr2(String susr2) {
		this.susr2 = susr2;
	}

	public String getBillId() {
		return billId;
	}

	public void setBillId(String billId) {
		this.billId = billId;
	}

	public String getPlanQty() {
		return planQty;
	}

	public void setPlanQty(String planQty) {
		this.planQty = planQty;
	}

	public String getSourceOrgId() {
		return sourceOrgId;
	}

	public void setSourceOrgId(String sourceOrgId) {
		this.sourceOrgId = sourceOrgId;
	}

	public String getSourceWarehouse() {
		return sourceWarehouse;
	}

	public void setSourceWarehouse(String sourceWarehouse) {
		this.sourceWarehouse = sourceWarehouse;
	}

	public String getTargetOrgId() {
		return targetOrgId;
	}

	public void setTargetOrgId(String targetOrgId) {
		this.targetOrgId = targetOrgId;
	}

	public String getTargetWarehouse() {
		return targetWarehouse;
	}

	public void setTargetWarehouse(String targetWarehouse) {
		this.targetWarehouse = targetWarehouse;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Date getRequestedDate() {
		return requestedDate;
	}

	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}

	public String getRequestor() {
		return requestor;
	}

	public void setRequestor(String requestor) {
		this.requestor = requestor;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getBomVersion() {
		return bomVersion;
	}

	public void setBomVersion(String bomVersion) {
		this.bomVersion = bomVersion;
	}

	public String getItemSn() {
		return itemSn;
	}

	public void setItemSn(String itemSn) {
		this.itemSn = itemSn;
	}

	public Date getEdiAdddate() {
		return ediAdddate;
	}

	public void setEdiAdddate(Date ediAdddate) {
		this.ediAdddate = ediAdddate;
	}

	public Date getEdiEditdate() {
		return ediEditdate;
	}

	public void setEdiEditdate(Date ediEditdate) {
		this.ediEditdate = ediEditdate;
	}

	public BigDecimal getSymbol() {
		return symbol;
	}

	public void setSymbol(BigDecimal symbol) {
		this.symbol = symbol;
	}

	public BigDecimal getInvsymbol() {
		return invsymbol;
	}

	public void setInvsymbol(BigDecimal invsymbol) {
		this.invsymbol = invsymbol;
	}

	public BigDecimal getInvfaildsymbol() {
		return invfaildsymbol;
	}

	public void setInvfaildsymbol(BigDecimal invfaildsymbol) {
		this.invfaildsymbol = invfaildsymbol;
	}

	public BigDecimal getFeedbackflag() {
		return feedbackflag;
	}

	public void setFeedbackflag(BigDecimal feedbackflag) {
		this.feedbackflag = feedbackflag;
	}

	public Date getFeedbackdate() {
		return feedbackdate;
	}

	public void setFeedbackdate(Date feedbackdate) {
		this.feedbackdate = feedbackdate;
	}

	public String getOrderkey() {
		return orderkey;
	}

	public void setOrderkey(String orderkey) {
		this.orderkey = orderkey;
	}

	public String getExternorderkey() {
		return externorderkey;
	}

	public void setExternorderkey(String externorderkey) {
		this.externorderkey = externorderkey;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getStorerkey() {
		return storerkey;
	}

	public void setStorerkey(String storerkey) {
		this.storerkey = storerkey;
	}

	public String getOrdersid() {
		return ordersid;
	}

	public void setOrdersid(String ordersid) {
		this.ordersid = ordersid;
	}

	public String getHsusr2() {
		return hsusr2;
	}

	public void setHsusr2(String hsusr2) {
		this.hsusr2 = hsusr2;
	}

	public String getHsusr3() {
		return hsusr3;
	}

	public void setHsusr3(String hsusr3) {
		this.hsusr3 = hsusr3;
	}

	public String getHsusr4() {
		return hsusr4;
	}

	public void setHsusr4(String hsusr4) {
		this.hsusr4 = hsusr4;
	}

	public String getHref03() {
		return href03;
	}

	public void setHref03(String href03) {
		this.href03 = href03;
	}

	public String getHref05() {
		return href05;
	}

	public void setHref05(String href05) {
		this.href05 = href05;
	}

	public String getHref06() {
		return href06;
	}

	public void setHref06(String href06) {
		this.href06 = href06;
	}

	public String getHref09() {
		return href09;
	}

	public void setHref09(String href09) {
		this.href09 = href09;
	}

	public String getHref10() {
		return href10;
	}

	public void setHref10(String href10) {
		this.href10 = href10;
	}

	public String getHref12() {
		return href12;
	}

	public void setHref12(String href12) {
		this.href12 = href12;
	}

	public String getHref13() {
		return href13;
	}

	public void setHref13(String href13) {
		this.href13 = href13;
	}

	public String getHref14() {
		return href14;
	}

	public void setHref14(String href14) {
		this.href14 = href14;
	}

	public String getHref15() {
		return href15;
	}

	public void setHref15(String href15) {
		this.href15 = href15;
	}

	public String getHref16() {
		return href16;
	}

	public void setHref16(String href16) {
		this.href16 = href16;
	}

	public String getHref17() {
		return href17;
	}

	public void setHref17(String href17) {
		this.href17 = href17;
	}

	public String getHref18() {
		return href18;
	}

	public void setHref18(String href18) {
		this.href18 = href18;
	}

	public String getHref19() {
		return href19;
	}

	public void setHref19(String href19) {
		this.href19 = href19;
	}

	public String getHref20() {
		return href20;
	}

	public void setHref20(String href20) {
		this.href20 = href20;
	}

	public String getHref21() {
		return href21;
	}

	public void setHref21(String href21) {
		this.href21 = href21;
	}

	public String getHref22() {
		return href22;
	}

	public void setHref22(String href22) {
		this.href22 = href22;
	}

	public String getHref23() {
		return href23;
	}

	public void setHref23(String href23) {
		this.href23 = href23;
	}

	public String getHref24() {
		return href24;
	}

	public void setHref24(String href24) {
		this.href24 = href24;
	}

	public String getHref25() {
		return href25;
	}

	public void setHref25(String href25) {
		this.href25 = href25;
	}

	public Date getHref26() {
		return href26;
	}

	public void setHref26(Date href26) {
		this.href26 = href26;
	}

	public Date getHref27() {
		return href27;
	}

	public void setHref27(Date href27) {
		this.href27 = href27;
	}

	public Date getHref28() {
		return href28;
	}

	public void setHref28(Date href28) {
		this.href28 = href28;
	}

	public Date getHref29() {
		return href29;
	}

	public void setHref29(Date href29) {
		this.href29 = href29;
	}

	public String getHref33() {
		return href33;
	}

	public void setHref33(String href33) {
		this.href33 = href33;
	}

	public String getHref34() {
		return href34;
	}

	public void setHref34(String href34) {
		this.href34 = href34;
	}

	public String getHref35() {
		return href35;
	}

	public void setHref35(String href35) {
		this.href35 = href35;
	}

	public String getHref36() {
		return href36;
	}

	public void setHref36(String href36) {
		this.href36 = href36;
	}

	public String getHref37() {
		return href37;
	}

	public void setHref37(String href37) {
		this.href37 = href37;
	}

	public String getHref38() {
		return href38;
	}

	public void setHref38(String href38) {
		this.href38 = href38;
	}

	public String getHref39() {
		return href39;
	}

	public void setHref39(String href39) {
		this.href39 = href39;
	}

	public String getHref40() {
		return href40;
	}

	public void setHref40(String href40) {
		this.href40 = href40;
	}

	public String getHref41() {
		return href41;
	}

	public void setHref41(String href41) {
		this.href41 = href41;
	}

	public String getHref42() {
		return href42;
	}

	public void setHref42(String href42) {
		this.href42 = href42;
	}

	public String getHref43() {
		return href43;
	}

	public void setHref43(String href43) {
		this.href43 = href43;
	}

	public String getHref44() {
		return href44;
	}

	public void setHref44(String href44) {
		this.href44 = href44;
	}

	public String getHref45() {
		return href45;
	}

	public void setHref45(String href45) {
		this.href45 = href45;
	}

	public String getHref46() {
		return href46;
	}

	public void setHref46(String href46) {
		this.href46 = href46;
	}

	public String getHref47() {
		return href47;
	}

	public void setHref47(String href47) {
		this.href47 = href47;
	}

	public String getHref48() {
		return href48;
	}

	public void setHref48(String href48) {
		this.href48 = href48;
	}

	public String getHref49() {
		return href49;
	}

	public void setHref49(String href49) {
		this.href49 = href49;
	}

	public String getHref50() {
		return href50;
	}

	public void setHref50(String href50) {
		this.href50 = href50;
	}

	public String getHref51() {
		return href51;
	}

	public void setHref51(String href51) {
		this.href51 = href51;
	}

	public String getHref52() {
		return href52;
	}

	public void setHref52(String href52) {
		this.href52 = href52;
	}

	public String getHref53() {
		return href53;
	}

	public void setHref53(String href53) {
		this.href53 = href53;
	}

	public String getHref54() {
		return href54;
	}

	public void setHref54(String href54) {
		this.href54 = href54;
	}

	public String getHref55() {
		return href55;
	}

	public void setHref55(String href55) {
		this.href55 = href55;
	}

	public String getHref56() {
		return href56;
	}

	public void setHref56(String href56) {
		this.href56 = href56;
	}

	public String getHref57() {
		return href57;
	}

	public void setHref57(String href57) {
		this.href57 = href57;
	}

	public String getHref58() {
		return href58;
	}

	public void setHref58(String href58) {
		this.href58 = href58;
	}

	public String getHref59() {
		return href59;
	}

	public void setHref59(String href59) {
		this.href59 = href59;
	}

	public String getHref60() {
		return href60;
	}

	public void setHref60(String href60) {
		this.href60 = href60;
	}





	public String getPackkey() {
		return packkey;
	}

	public void setPackkey(String packkey) {
		this.packkey = packkey;
	}

	public BigDecimal getOriginalqty() {
		return originalqty;
	}

	public void setOriginalqty(BigDecimal originalqty) {
		this.originalqty = originalqty;
	}

	public BigDecimal getQtyshipedtotal() {
		return qtyshipedtotal;
	}

	public void setQtyshipedtotal(BigDecimal qtyshipedtotal) {
		this.qtyshipedtotal = qtyshipedtotal;
	}

	public BigDecimal getShippedqty() {
		return shippedqty;
	}

	public void setShippedqty(BigDecimal shippedqty) {
		this.shippedqty = shippedqty;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public BigDecimal getAllowoverpick() {
		return allowoverpick;
	}

	public void setAllowoverpick(BigDecimal allowoverpick) {
		this.allowoverpick = allowoverpick;
	}

	public String getPreallocatestrategykey() {
		return preallocatestrategykey;
	}

	public void setPreallocatestrategykey(String preallocatestrategykey) {
		this.preallocatestrategykey = preallocatestrategykey;
	}

	public String getAllocatestrategykey() {
		return allocatestrategykey;
	}

	public void setAllocatestrategykey(String allocatestrategykey) {
		this.allocatestrategykey = allocatestrategykey;
	}

	public String getAllocatestrategytype() {
		return allocatestrategytype;
	}

	public void setAllocatestrategytype(String allocatestrategytype) {
		this.allocatestrategytype = allocatestrategytype;
	}

	public BigDecimal getShelflife() {
		return shelflife;
	}

	public void setShelflife(BigDecimal shelflife) {
		this.shelflife = shelflife;
	}

	public String getRotation() {
		return rotation;
	}

	public void setRotation(String rotation) {
		this.rotation = rotation;
	}

	public String getSkurotation() {
		return skurotation;
	}

	public void setSkurotation(String skurotation) {
		this.skurotation = skurotation;
	}

	public String getLot() {
		return lot;
	}

	public void setLot(String lot) {
		this.lot = lot;
	}

	public String getLottable01() {
		return lottable01;
	}

	public void setLottable01(String lottable01) {
		this.lottable01 = lottable01;
	}

	public String getLottable02() {
		return lottable02;
	}

	public void setLottable02(String lottable02) {
		this.lottable02 = lottable02;
	}

	public String getLottable03() {
		return lottable03;
	}

	public void setLottable03(String lottable03) {
		this.lottable03 = lottable03;
	}

	public Date getLottable04() {
		return lottable04;
	}

	public void setLottable04(Date lottable04) {
		this.lottable04 = lottable04;
	}

	public Date getLottable05() {
		return lottable05;
	}

	public void setLottable05(Date lottable05) {
		this.lottable05 = lottable05;
	}

	public String getLottable06() {
		return lottable06;
	}

	public void setLottable06(String lottable06) {
		this.lottable06 = lottable06;
	}

	public String getLottable09() {
		return lottable09;
	}

	public void setLottable09(String lottable09) {
		this.lottable09 = lottable09;
	}

	public String getLottable10() {
		return lottable10;
	}

	public void setLottable10(String lottable10) {
		this.lottable10 = lottable10;
	}

	public Date getLottable11() {
		return lottable11;
	}

	public void setLottable11(Date lottable11) {
		this.lottable11 = lottable11;
	}

	public Date getLottable12() {
		return lottable12;
	}

	public void setLottable12(Date lottable12) {
		this.lottable12 = lottable12;
	}

	public String getSusr1() {
		return susr1;
	}

	public void setSusr1(String susr1) {
		this.susr1 = susr1;
	}

	public String getSusr3() {
		return susr3;
	}

	public void setSusr3(String susr3) {
		this.susr3 = susr3;
	}

	public String getSusr4() {
		return susr4;
	}

	public void setSusr4(String susr4) {
		this.susr4 = susr4;
	}

	public String getSusr5() {
		return susr5;
	}

	public void setSusr5(String susr5) {
		this.susr5 = susr5;
	}

	public String getNotes2() {
		return notes2;
	}

	public void setNotes2(String notes2) {
		this.notes2 = notes2;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getRef03() {
		return ref03;
	}

	public void setRef03(String ref03) {
		this.ref03 = ref03;
	}

	public String getRef04() {
		return ref04;
	}

	public void setRef04(String ref04) {
		this.ref04 = ref04;
	}

	public String getRef05() {
		return ref05;
	}

	public void setRef05(String ref05) {
		this.ref05 = ref05;
	}

	public String getRef06() {
		return ref06;
	}

	public void setRef06(String ref06) {
		this.ref06 = ref06;
	}

	public String getRef07() {
		return ref07;
	}

	public void setRef07(String ref07) {
		this.ref07 = ref07;
	}

	public String getRef08() {
		return ref08;
	}

	public void setRef08(String ref08) {
		this.ref08 = ref08;
	}

	public String getRef09() {
		return ref09;
	}

	public void setRef09(String ref09) {
		this.ref09 = ref09;
	}

	public String getRef10() {
		return ref10;
	}

	public void setRef10(String ref10) {
		this.ref10 = ref10;
	}

	public String getRef11() {
		return ref11;
	}

	public void setRef11(String ref11) {
		this.ref11 = ref11;
	}

	public String getRef12() {
		return ref12;
	}

	public void setRef12(String ref12) {
		this.ref12 = ref12;
	}

	public String getRef13() {
		return ref13;
	}

	public void setRef13(String ref13) {
		this.ref13 = ref13;
	}

	public String getRef14() {
		return ref14;
	}

	public void setRef14(String ref14) {
		this.ref14 = ref14;
	}

	public String getRef15() {
		return ref15;
	}

	public void setRef15(String ref15) {
		this.ref15 = ref15;
	}

	public String getRef16() {
		return ref16;
	}

	public void setRef16(String ref16) {
		this.ref16 = ref16;
	}

	public String getRef17() {
		return ref17;
	}

	public void setRef17(String ref17) {
		this.ref17 = ref17;
	}

	public String getRef18() {
		return ref18;
	}

	public void setRef18(String ref18) {
		this.ref18 = ref18;
	}

	public String getRef19() {
		return ref19;
	}

	public void setRef19(String ref19) {
		this.ref19 = ref19;
	}

	public String getRef20() {
		return ref20;
	}

	public void setRef20(String ref20) {
		this.ref20 = ref20;
	}

	public String getRef21() {
		return ref21;
	}

	public void setRef21(String ref21) {
		this.ref21 = ref21;
	}

	public String getRef22() {
		return ref22;
	}

	public void setRef22(String ref22) {
		this.ref22 = ref22;
	}

	public String getRef23() {
		return ref23;
	}

	public void setRef23(String ref23) {
		this.ref23 = ref23;
	}

	public String getRef24() {
		return ref24;
	}

	public void setRef24(String ref24) {
		this.ref24 = ref24;
	}

	public String getRef25() {
		return ref25;
	}

	public void setRef25(String ref25) {
		this.ref25 = ref25;
	}

	public Date getRef26() {
		return ref26;
	}

	public void setRef26(Date ref26) {
		this.ref26 = ref26;
	}

	public Date getRef27() {
		return ref27;
	}

	public void setRef27(Date ref27) {
		this.ref27 = ref27;
	}

	public Date getRef28() {
		return ref28;
	}

	public void setRef28(Date ref28) {
		this.ref28 = ref28;
	}

	public Date getRef29() {
		return ref29;
	}

	public void setRef29(Date ref29) {
		this.ref29 = ref29;
	}

	public Date getRef30() {
		return ref30;
	}

	public void setRef30(Date ref30) {
		this.ref30 = ref30;
	}

	public String getRef31() {
		return ref31;
	}

	public void setRef31(String ref31) {
		this.ref31 = ref31;
	}

	public String getRef32() {
		return ref32;
	}

	public void setRef32(String ref32) {
		this.ref32 = ref32;
	}

	public String getRef33() {
		return ref33;
	}

	public void setRef33(String ref33) {
		this.ref33 = ref33;
	}

	public String getRef34() {
		return ref34;
	}

	public void setRef34(String ref34) {
		this.ref34 = ref34;
	}

	public String getRef35() {
		return ref35;
	}

	public void setRef35(String ref35) {
		this.ref35 = ref35;
	}

	public String getRef36() {
		return ref36;
	}

	public void setRef36(String ref36) {
		this.ref36 = ref36;
	}

	public String getRef37() {
		return ref37;
	}

	public void setRef37(String ref37) {
		this.ref37 = ref37;
	}

	public String getRef38() {
		return ref38;
	}

	public void setRef38(String ref38) {
		this.ref38 = ref38;
	}

	public String getRef39() {
		return ref39;
	}

	public void setRef39(String ref39) {
		this.ref39 = ref39;
	}

	public String getRef40() {
		return ref40;
	}

	public void setRef40(String ref40) {
		this.ref40 = ref40;
	}

	public String getRef41() {
		return ref41;
	}

	public void setRef41(String ref41) {
		this.ref41 = ref41;
	}

	public String getRef42() {
		return ref42;
	}

	public void setRef42(String ref42) {
		this.ref42 = ref42;
	}

	public String getRef43() {
		return ref43;
	}

	public void setRef43(String ref43) {
		this.ref43 = ref43;
	}

	public String getRef44() {
		return ref44;
	}

	public void setRef44(String ref44) {
		this.ref44 = ref44;
	}

	public String getRef46() {
		return ref46;
	}

	public void setRef46(String ref46) {
		this.ref46 = ref46;
	}

	public String getRef47() {
		return ref47;
	}

	public void setRef47(String ref47) {
		this.ref47 = ref47;
	}

	public String getRef48() {
		return ref48;
	}

	public void setRef48(String ref48) {
		this.ref48 = ref48;
	}

	public String getRef49() {
		return ref49;
	}

	public void setRef49(String ref49) {
		this.ref49 = ref49;
	}

	public String getRef50() {
		return ref50;
	}

	public void setRef50(String ref50) {
		this.ref50 = ref50;
	}

	public String getRef51() {
		return ref51;
	}

	public void setRef51(String ref51) {
		this.ref51 = ref51;
	}

	public String getRef52() {
		return ref52;
	}

	public void setRef52(String ref52) {
		this.ref52 = ref52;
	}

	public String getRef53() {
		return ref53;
	}

	public void setRef53(String ref53) {
		this.ref53 = ref53;
	}

	public String getRef54() {
		return ref54;
	}

	public void setRef54(String ref54) {
		this.ref54 = ref54;
	}

	public String getRef55() {
		return ref55;
	}

	public void setRef55(String ref55) {
		this.ref55 = ref55;
	}

	public String getRef56() {
		return ref56;
	}

	public void setRef56(String ref56) {
		this.ref56 = ref56;
	}

	public String getRef57() {
		return ref57;
	}

	public void setRef57(String ref57) {
		this.ref57 = ref57;
	}

	public String getRef58() {
		return ref58;
	}

	public void setRef58(String ref58) {
		this.ref58 = ref58;
	}

	public String getRef59() {
		return ref59;
	}

	public void setRef59(String ref59) {
		this.ref59 = ref59;
	}

	public String getRef60() {
		return ref60;
	}

	public void setRef60(String ref60) {
		this.ref60 = ref60;
	}

	public Date getAdddate() {
		return adddate;
	}

	public void setAdddate(Date adddate) {
		this.adddate = adddate;
	}

	public Date getEditdate() {
		return editdate;
	}

	public void setEditdate(Date editdate) {
		this.editdate = editdate;
	}

	public String getEditwho() {
		return editwho;
	}

	public void setEditwho(String editwho) {
		this.editwho = editwho;
	}

	public String getWhseid() {
		return whseid;
	}

	public void setWhseid(String whseid) {
		this.whseid = whseid;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public BigDecimal getSmallamountflag() {
		return smallamountflag;
	}

	public void setSmallamountflag(BigDecimal smallamountflag) {
		this.smallamountflag = smallamountflag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getEndtime() {
		return endtime;
	}

	public void setEndtime(Date endtime) {
		this.endtime = endtime;
	}

	public String getHref61() {
		return href61;
	}

	public void setHref61(String href61) {
		this.href61 = href61;
	}

	public String getHref62() {
		return href62;
	}

	public void setHref62(String href62) {
		this.href62 = href62;
	}

	public String getHref63() {
		return href63;
	}

	public void setHref63(String href63) {
		this.href63 = href63;
	}

	public String getHref64() {
		return href64;
	}

	public void setHref64(String href64) {
		this.href64 = href64;
	}

	public String getExternalorderkey2() {
		return externalorderkey2;
	}

	public void setExternalorderkey2(String externalorderkey2) {
		this.externalorderkey2 = externalorderkey2;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getHsusr1() {
		return hsusr1;
	}

	public void setHsusr1(String hsusr1) {
		this.hsusr1 = hsusr1;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPcbName() {
		return pcbName;
	}

	public void setPcbName(String pcbName) {
		this.pcbName = pcbName;
	}

	public BigDecimal getRegisterQty() {
		return registerQty;
	}

	public void setRegisterQty(BigDecimal registerQty) {
		this.registerQty = registerQty;
	}

	public String getBinder() {
		return binder;
	}

	public void setBinder(String binder) {
		this.binder = binder;
	}
}
