package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/16 下午3:10
 */
@Accessors(chain = true)
@Data
public class PsDeliveryFeedbackInfoDTO {
    /**
     * 反馈数量
     */
    private BigDecimal quantity;
    /**
     * 预计完工日期
     */
    @JsonProperty("date_estimated_completion")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long dateEstimatedCompletion;

    /**
     * 厂商自供料预计齐套日期
     */
    @JsonProperty("date_material_estimated_prepared")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long dateMaterialEstimatedPrepared;

    /**
     * 全部物料预计齐套日期
     */
    @JsonProperty("date_all_material_estimated_prepared")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long dateAllMaterialEstimatedPrepared;

    /**
     * 预计投产日期
     */
    @JsonProperty("date_scheduled_production")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long dateScheduledProduction;
    /**
     * 厂商自供料实际齐套日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long timeMaterialPrepared;

    /**
     * 全部物料实际齐套日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long dateAllMaterialPrepared;

    /**
     * 实际投产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private long timeProduction;

    /**
     * 一级原因分类
     */
    @JsonProperty("abnormal_category_first")
    private String abnormalCategoryFirst;

    /**
     * 二级原因分类
     */
    @JsonProperty("abnormal_category_second")
    private String abnormalCategorySecond;

    /**
     * 三级原因分类
     */
    @JsonProperty("abnormal_category_third")
    private String abnormalCategoryThird;

    /**
     * 延期编号(不能重复)
     */
    @JsonProperty("abnormal_no")
    private String abnormalNo;

    /**
     * 责任（1阿里责任，2厂商责任）
     */
    private Integer liability;

    /**
     * 延期原因
     */
    private String remark;
}
