package com.zte.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 6396001283
 * @description 物料存储属性
 * @date 2024/1/23
 **/
@Data
public class MaterialStorageAttributesDTO {
    /**
     * id
     */
    private String id;

    /**
     * 维护类型
     */
    private String maintenanceType;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 物料代码
     */
    private String itemCode;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 存储方式
     */
    private String storageMethod;

    //最长解冻时长小时
    private Integer thawingDuration;
    //最短解冻时长小时
    private Integer minThawingDuration;
    //最长使用时长小时
    private Integer usageDuration;
    // 回收后最长存储时间
    private Integer maxRecoverStorageDate;


    /**
     * 有效期（天）
     */
    private Integer expirationDuration;

    /**
     * create_by
     */
    private String createBy;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * last_updated_by
     */
    private String lastUpdatedBy;

    /**
     * last_updated_date
     */
    private Date lastUpdatedDate;

    /**
     * enabled_flag
     */
    private String enabledFlag;

    private List<String> barcodeList;
    private List<String> itemCodeList;
    private List<String> supplierList;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("每页条数")
    private Integer rows = 10;
}
