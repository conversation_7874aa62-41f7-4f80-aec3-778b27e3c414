package com.zte.interfaces.dto.datawb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PickListQueryDTO implements Serializable {
    @Size(max = 100, message = "任务号列表最多支持100个")
    private List<@Size(max = 32, message = "生产任务号长度不能超过32") String> taskNos;

    @NotEmpty(message = "开始日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式必须为yyyy-MM-dd")
    private String startDate;

    @NotEmpty(message = "结束日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式必须为yyyy-MM-dd")
    private String endDate;

    private String orderNo;

    private String creator;

    private String sku;


}
