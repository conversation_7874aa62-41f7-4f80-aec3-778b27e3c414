package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标模推送信息头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-02 14:43:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PushStdModelDataDTO extends PushStdModelDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 任务号
	 */
	private String taskNo;
	/**
	 * 领料单号
	 */
	private String billNo;
	/**
	 * 凭证单号
	 */
	private String rcvNo;
	/**
	 * 箱号
	 */
	private String cartonNo;
	/**
	 * 料单代码
	 */
	private String itemNo;
	/**
	 * 任务数量
	 */
	private Integer taskQty;
	/**
	 * 工厂id
	 */
	private Integer factoryId;
	/**
	 * 客户名称
	 */
	private String customerName;
	/**
	 * 当前推送进程 10: 排产信息推送
	 */
	private String currProcess;
	/**
	 * 当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 7:数据异常 8:推送异常 9:回调结果异常
	 */
	private Integer pushStatus;
	/**
	 * 当前进程数据推送时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date pushDate;
	/**
	 * 异常信息
	 */
	private String errorMsg;
	/**
	 * $column.comments
	 */
	private Integer pushFailCount;
	/**
	 * $column.comments
	 */
	private String createBy;
	/**
	 * $column.comments
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createDate;
	/**
	 * $column.comments
	 */
	private String lastUpdatedBy;
	/**
	 * $column.comments
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date lastUpdatedDate;
	/**
	 * $column.comments
	 */
	private String enabledFlag;
	private BigDecimal orgId;
}
