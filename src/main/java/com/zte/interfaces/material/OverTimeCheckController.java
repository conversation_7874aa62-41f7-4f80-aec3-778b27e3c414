package com.zte.interfaces.material;


import com.zte.application.material.OverTimeCheckService;
import com.zte.common.utils.Constant;

import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.interfaces.material.dto.OverTimeOutDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Deacription 三次超期复检物料可用信息同步(河源三期)
 * <AUTHOR>
 * @Date 2023/7/6 15:33
 **/
@RestController
@RequestMapping("/material")
@Api(tags = "原材料管理-入库管理-超期复检", description = "OverTimeCheckController")
public class OverTimeCheckController {
    @Autowired
    private OverTimeCheckService overTimeCheckService;

    @ApiOperation("三次超期复检物料可用信息同步查询接口")
    @PostMapping(value = "/overTimeCheckController", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<List<OverTimeOutDTO>> overTimeCheck(@RequestBody OverTimeInDTO overTimeInDTO) {
        ServiceData<List<OverTimeOutDTO>> serviceData = new ServiceData<>();


        if (overTimeInDTO == null || CollectionUtils.isEmpty(overTimeInDTO.getItemBarcode()) ||
                CommonUtils.isEmpty(overTimeInDTO.getWhseid())) {
            serviceData.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, Constant.PARAM_MUST_INPUT));
            return serviceData;
        }

        if (overTimeInDTO.getItemBarcode().size() > Constant.INT_2 * Constant.INT_100) {
            serviceData.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, Constant.NO_OVER_BARCODE));
            return serviceData;
        }

        ServiceData<List<OverTimeOutDTO>> listServiceData = overTimeCheckService.queryInventory(overTimeInDTO);
        return listServiceData;
    }

}
