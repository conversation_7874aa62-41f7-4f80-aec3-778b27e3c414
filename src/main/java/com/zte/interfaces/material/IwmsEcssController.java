package com.zte.interfaces.material;

import com.zte.application.material.IwmsEcssService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.step.dto.DocumentCallbackResultDTO;
import com.zte.interfaces.step.dto.EcssBillDTO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 *
 */
@ZTERestController("/iwmsEcss")
@Api(tags = "iWMS与ECSS对接")
public class IwmsEcssController {

    @Autowired
    private IwmsEcssService iwmsEcssService;

    @ApiOperation("推送单据信息到ECSS")
    @PostMapping("/pushBillToEcss")
    public void pushBillToEcss(HttpServletRequest request, @RequestBody EcssBillDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setLastUpdatedBy(xEmpNo);
        iwmsEcssService.pushBillToEcss(dto);
    }

    @ApiOperation("ECSS回传单据扫描结果")
    @PostMapping("/callBackEcssToIwms")
    public void callBackEcssToIWms(HttpServletRequest request, @RequestBody DocumentCallbackResultDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setCreatedBy(xEmpNo);
        dto.setLastUpdatedBy(xEmpNo);
        iwmsEcssService.callBackEcssToIwms(dto);
    }

    @ApiOperation("提交退货申请单到INFOR(JOB)")
    @PostMapping("/submitApllyToInforSo")
    public void submitApllyToInforSo(@RequestBody EcssBillDTO dto){
        iwmsEcssService.submitApllyToInforSo(dto);
    }

}