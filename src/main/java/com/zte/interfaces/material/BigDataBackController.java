package com.zte.interfaces.material;

import com.zte.application.material.BigDataBackService;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * @Deacription 原材料大数据备份
 * <AUTHOR>
 * @Date 2023/11/9
 **/
@ZTERestController("/BigDataBack")
@Api(tags = "原材料管理-大数据量表归档", description = "BigDataBackController")
public class BigDataBackController {
    @Autowired
    private BigDataBackService bigDataBackService;

    @ApiOperation("理货打包备份接口")
    @PostMapping("/backTallyPacking")
    public void backTallyPacking() {
        bigDataBackService.backTallyPacking();
    }
}
