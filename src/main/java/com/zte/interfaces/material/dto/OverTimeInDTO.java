package com.zte.interfaces.material.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date
 **/
@Setter
@Getter
public class OverTimeInDTO {
    @ApiModelProperty(value = "查询条件，要支持批量查询")
    private List<String> itemBarcode;
    private String whseid;
    private String orderKey;
    private Integer warningDays;

}
