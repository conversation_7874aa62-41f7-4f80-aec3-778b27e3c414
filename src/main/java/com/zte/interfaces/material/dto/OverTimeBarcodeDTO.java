package com.zte.interfaces.material.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date
 **/
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OverTimeBarcodeDTO implements Serializable {
    @ApiModelProperty(value = "itemBarcodes")
    private String itemBarcodes;


}
