/**
 * 项目名称 : SnCaBinding
 * 创建日期 : 2019-07-11
 * 修改历史 :
 *   1. [2019-07-11] 创建文件 by 10243397
 **/
package com.zte.interfaces.sncabind.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.common.model.PageDTO;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.constant.SnCaConstant;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpecifiedPsTaskDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "批次号")
    private List<String> prodplanIdList;

    @ApiModelProperty(value = "料单代码")
    private List<String> itemNoList;

    @ApiModelProperty(value = "条码")
    private String sn;

    @ApiModelProperty(value = "指令状态")
    private String taskStatus;

    // 任务号List
    private List<String> taskNoList;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createStartDate;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createEndDate;

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public List<String> getTaskNoList() {
        return taskNoList;
    }

    public void setTaskNoList(List<String> taskNoList) {
        this.taskNoList = taskNoList;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public List<String> getProdplanIdList() {
        return prodplanIdList;
    }

    public void setProdplanIdList(List<String> prodplanIdList) {
        this.prodplanIdList = prodplanIdList;
    }

    public List<String> getItemNoList() {
        return itemNoList;
    }

    public void setItemNoList(List<String> itemNoList) {
        this.itemNoList = itemNoList;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }
}