package com.zte.interfaces.infor;

import com.zte.application.infor.InforWarnService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
@ZTERestController("/inforWarn")
@Api(tags = "infor邮件预警", description = "InforWarnController")
public class InforWarnController {
    @Autowired
    private InforWarnService inforWarnService;

    @ApiOperation("先进先出邮件预警")
    @PostMapping("/warnAllocateMonitorInfor")
    public ServiceData warnAllocateMonitorInfor(HttpServletRequest request) {
        ServiceData ret = new ServiceData();
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        AllocateExceptionMonitorVo dto = AllocateExceptionMonitorVo.builder().build().setEmpNo(xEmpNo);
        inforWarnService.warnAllocateMonitorInfor(dto);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
}
