package com.zte.interfaces.infor;

import com.zte.application.infor.WarehouseAlgorithmService;
import com.zte.application.infor.WarehouseReplenishmentPathService;
import com.zte.application.infor.WarehouseRoadWorkService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.*;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZTERestController("/infor")
@Api(tags = "原材料仓储AI助手", description = "WarehouseAiAssistantController")
public class WarehouseAiAssistantController {

    @Autowired
    private WarehouseRoadWorkService warehouseRoadWorkService;
    @Autowired
    private WarehouseAlgorithmService warehouseAlgorithmService;

    @Autowired
    private WarehouseReplenishmentPathService warehouseReplenishmentPathService;

    @ApiOperation("查询坐标类型列表")
    @PostMapping("/getCoordinateTypeList")
    public List<SysLookupValuesDTO> getCoordinateTypeList(){
       return warehouseRoadWorkService.getCoordinateTypeList();
    }

    @ApiOperation("查询库位标记列表")
    @PostMapping("/getLocMarkList")
    public List<SysLookupValuesDTO> getLocMarkList(){
        return warehouseRoadWorkService.getLocMarkList();
    }

    @ApiOperation("查询库房列表")
    @PostMapping("/getWarehouseList")
    public List<SysLookupValuesDTO> getWarehouseList(){
        return warehouseRoadWorkService.getWarehouseList();
    }

    @ApiOperation("查询库区列表")
    @PostMapping("/getWarehouseAreaList")
    public List<SysLookupValuesDTO> getWarehouseAreaList(){
        return warehouseRoadWorkService.getWarehouseAreaList();
    }

    @ApiOperation("查询路网数据")
    @PostMapping("/getWarehouseRoadWorkList")
    public WarehouseRoadWorkListVO getWarehouseRoadWorkList(@RequestBody WarehouseRoadWorkDTO dto){
        return warehouseRoadWorkService.getWarehouseRoadWorkList(dto);
    }

    @ApiOperation("保存路网数据")
    @PostMapping("/saveWarehouseRoadWork")
    public void saveWarehouseRoadWork(@RequestBody WarehouseRoadWorkDTO dto){
        warehouseRoadWorkService.saveWarehouseRoadWork(dto);
    }

    @ApiOperation("更新路网数据")
    @PostMapping("/updateWarehouseRoadWork")
    public void updateWarehouseRoadWork(@RequestBody WarehouseRoadWorkDTO dto){
        warehouseRoadWorkService.updateWarehouseRoadWork(dto);
    }

    @ApiOperation("导入路网数据")
    @PostMapping("/importWarehouseRoadWork")
    public void importWarehouseRoadWork(@RequestBody List<WarehouseRoadWorkDTO> list){
        warehouseRoadWorkService.importWarehouseRoadWork(list);
    }

    @ApiOperation("导出路网数据")
    @PostMapping("/exportWarehouseRoadWork")
    public void exportWarehouseRoadWork(HttpServletRequest request, @RequestBody WarehouseRoadWorkDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        warehouseRoadWorkService.exportWarehouseRoadWork(dto);
    }

    @ApiOperation("查询方案类型列表")
    @PostMapping("/getSchemeTypeList")
    public List<SysLookupValuesDTO> getSchemeTypeList(){
        return warehouseAlgorithmService.getSchemeTypeList();
    }

    @ApiOperation("查询整库策略列表")
    @PostMapping("/getWholeWarehouseStrategyList")
    public List<SysLookupValuesDTO> getWholeWarehouseStrategyList(){
        return warehouseAlgorithmService.getWholeWarehouseStrategyList();
    }

    @ApiOperation("查询算法方案")
    @PostMapping("/getWarehouseAlgorithmList")
    public WarehouseAlgorithmListVO getWarehouseAlgorithmList(@RequestBody WarehouseAlgorithmDTO dto){
        return warehouseAlgorithmService.getWarehouseAlgorithmList(dto);
    }

    @ApiOperation("保存算法方案")
    @PostMapping("/saveWarehouseAlgorithm")
    public void saveWarehouseAlgorithm(@RequestBody WarehouseAlgorithmDTO dto){
        warehouseAlgorithmService.saveWarehouseAlgorithm(dto);
    }

    @ApiOperation("更新算法方案")
    @PostMapping("/updateWarehouseAlgorithm")
    public void updateWarehouseAlgorithm(@RequestBody WarehouseAlgorithmDTO dto){
        warehouseAlgorithmService.updateWarehouseAlgorithm(dto);
    }

    @ApiOperation("导入算法方案")
    @PostMapping("/importWarehouseAlgorithm")
    public void importWarehouseAlgorithm(@RequestBody List<WarehouseAlgorithmDTO> list){
        warehouseAlgorithmService.importWarehouseAlgorithm(list);
    }

    @ApiOperation("导出算法方案")
    @PostMapping("/exportWarehouseAlgorithm")
    public void exportWarehouseAlgorithm(HttpServletRequest request, @RequestBody WarehouseAlgorithmDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        warehouseAlgorithmService.exportWarehouseAlgorithm(dto);
    }

    @ApiOperation("查询算法计算结果")
    @PostMapping("/getAlgorithmResultHeadList")
    public AlgorithmResultHeadListVO getAlgorithmResultHeadList(@RequestBody AlgorithmResultDetailDTO dto){
        return warehouseAlgorithmService.getAlgorithmResultHeadList(dto);
    }

    @ApiOperation("查询算法计算结果明细")
    @PostMapping("/getAlgorithmResultDetailList")
    public AlgorithmResultDetailListVO getAlgorithmResultDetailList(@RequestBody AlgorithmResultDetailDTO dto){
        return warehouseAlgorithmService.getAlgorithmResultDetailList(dto);
    }

    @ApiOperation("导出算法计算结果")
    @PostMapping("/exportAlgorithmResult")
    public void exportAlgorithmResult(HttpServletRequest request, @RequestBody AlgorithmResultDetailDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        warehouseAlgorithmService.exportAlgorithmResult(dto);
    }

    @ApiOperation("查询算法执行日志")
    @PostMapping("/getAlgorithmExecuteLogList")
    public AlgorithmExecuteLogListVO getAlgorithmExecuteLogList(@RequestBody AlgorithmExecuteLogDTO dto){
        return warehouseAlgorithmService.getAlgorithmExecuteLogList(dto);
    }

    @ApiOperation("查询补货路径优化")
    @PostMapping("/getReplenishmentPathList")
    public WarehouseRepPathWorkListVO getReplenishmentPathList(@RequestBody WarehouseReplenishmentPathDTO dto){
        return warehouseReplenishmentPathService.getReplenishmentPathList(dto);
    }

    @ApiOperation("导出补货路径优化")
    @PostMapping("/exportReplenishmentPath")
    public void exportReplenishmentPath(HttpServletRequest request, @RequestBody WarehouseReplenishmentPathDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        warehouseReplenishmentPathService.exportReplenishmentPath(dto);
    }

}
