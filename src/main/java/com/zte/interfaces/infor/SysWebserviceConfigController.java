package com.zte.interfaces.infor;

import com.zte.application.infor.SysWebserviceConfigService;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;

@ZTERestController("/webserviceMonitoring")
@Api(tags = "WebServiceMonitoring", description = "SysWebserviceConfigController")
public class SysWebserviceConfigController {

    @Autowired
    private SysWebserviceConfigService configService;


    @ApiOperation("WebService查询接口超时监控")
    @PostMapping("/webServiceInterfaceMonitoring")
    public ServiceData webServiceInterfaceMonitoring(){
        ServiceData ret = new ServiceData();
        configService.webServiceInterfaceMonitoring();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
}