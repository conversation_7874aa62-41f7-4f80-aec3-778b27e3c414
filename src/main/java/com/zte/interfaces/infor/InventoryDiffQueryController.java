package com.zte.interfaces.infor;

import com.zte.application.infor.InventoryDiffQueryService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.InventoryDiffDTO;
import com.zte.interfaces.infor.vo.InventoryDiffVO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

/* Started by AICoder, pid:q46e23d79bi020214c1509b1c0609766205796d7 */
@ZTERestController("/inventoryDiff")
@Api(tags = "库存对账查询", description = "InventoryDiffQueryController")
public class InventoryDiffQueryController {
    @Autowired
    private InventoryDiffQueryService inventoryDiffQueryService;

    @ApiOperation("获取库存差异数据")
    @PostMapping("/getInventoryDiffData")
    public InventoryDiffVO getInventoryDiffData(@RequestBody InventoryDiffDTO dto){
        return inventoryDiffQueryService.getInventoryDiffData(dto);
    }

    @ApiOperation("导出库存差异数据")
    @PostMapping("/exportInventoryDiffData")
    public void exportInventoryDiffData(HttpServletRequest request, @RequestBody InventoryDiffDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        inventoryDiffQueryService.exportInventoryDiffData(dto);
    }
    @ApiOperation("同步阿里库存")
    @PostMapping("/synchronizeInventoryDiffData")
    public void synchronizeInventoryDiffData(HttpServletRequest request){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        inventoryDiffQueryService.synchronizeInventoryDiffData(xEmpNo);
    }

    @ApiOperation("箱与sn未及时绑定监控")
    @PostMapping("/unBindSnRelationAlarm")
    public void unBindSnRelationAlarm(HttpServletRequest request) {
        inventoryDiffQueryService.unBindSnRelationAlarm();
    }

    @ApiOperation("原箱区物料和混箱区物料一致性监控")
    @PostMapping("/originAndMixedBoxMaterialConsistencyWarning")
    public void originAndMixedBoxMaterialConsistencyWarning(HttpServletRequest request) {
        inventoryDiffQueryService.originAndMixedBoxMaterialConsistencyWarning();
    }

    @ApiOperation("ZTE&阿里库存一致性监控")
    @PostMapping("/aliInventoryConsistencyWarning")
    public void aliInventoryConsistencyWarning(HttpServletRequest request) {
        inventoryDiffQueryService.aliInventoryConsistencyWarning();
    }
}

/* Ended by AICoder, pid:q46e23d79bi020214c1509b1c0609766205796d7 */