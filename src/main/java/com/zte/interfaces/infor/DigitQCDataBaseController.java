package com.zte.interfaces.infor;

import com.zte.application.infor.DigitQCDataBaseService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.DigitQCDataBaseHeadDTO;
import com.zte.interfaces.infor.dto.DigitQCDataBaseDTO;
import com.zte.interfaces.infor.vo.DigitObjectValueListVo;
import com.zte.interfaces.infor.vo.DigitQCDataBaseListVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Description: 数字QC/内控/安全红点管理
 *
 * <AUTHOR>
 * date: 2025/4/2 9:57
 */

@ZTERestController("/digitQCDataBase")
@Api(tags = "数智QC基础数据", description = "DigitQCDataBaseController")
public class DigitQCDataBaseController {

    @Autowired
    private DigitQCDataBaseService digitQCDataBaseService;

    @ApiOperation("拉取infor有库存的物料代码")
    @PostMapping("/pullInforItemNoJob")
    public void pullInforItemNoJob(HttpServletRequest request) throws Exception{
        digitQCDataBaseService.pullInforItemNoJob();
    }

    @ApiOperation("查询智能QC基础数据")
    @PostMapping("/getQCDataBaseList")
    public DigitQCDataBaseListVo getQCDataBaseList(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        return digitQCDataBaseService.getQCDataBaseList(dto);
    }

    @ApiOperation("导出智能QC基础数据")
    @PostMapping("/exportQCDataBaseList")
    public void exportQCDataBaseList(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.exportQCDataBaseList(dto, xEmpNo);
    }

    @ApiOperation("智能QC基础数据失效")
    @PostMapping("/loseQCDataBase")
    public void loseQCDataBase(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.loseQCDataBase(dto);
    }

    @ApiOperation("智能QC基础数据生效")
    @PostMapping("/effectQCDataBase")
    public void effectQCDataBase(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.effectQCDataBase(dto);
    }

    @ApiOperation("智能QC基础数据维护")
    @PostMapping("/updateQCDataBase")
    public void updateQCDataBase(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.updateQCDataBase(dto);
    }

    @ApiOperation("智能QC基础数据导入")
    @PostMapping("/importQCDataBase")
    public void importQCDataBase(HttpServletRequest request, @RequestBody List<DigitQCDataBaseDTO> list) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.importQCDataBase(list);
    }

    @ApiOperation("智能QC对象值数据导入")
    @PostMapping("/importObjectInfo")
    public void importObjectInfo(HttpServletRequest request, @RequestBody List<DigitQCDataBaseDTO> list) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.importObjectInfo(list);
    }

    @ApiOperation("智能QC对象值数据查询")
    @PostMapping("/getObjectInfo")
    public DigitObjectValueListVo getObjectInfo(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        return digitQCDataBaseService.getObjectInfo(dto);
    }

    @ApiOperation("智能QC对象值数据删除")
    @PostMapping("/deleteObjectInfo")
    public void deleteObjectInfo(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.deleteObjectInfo(dto);
    }


    @ApiOperation("智能QC对象值数据导出")
    @PostMapping("/exportObjectInfo")
    public void exportObjectInfo(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCDataBaseService.exportObjectInfo(dto, xEmpNo);
    }

    @ApiOperation("数智QC基础数据查询")
    @PostMapping("/queryDataBaseInfoList")
    public List<DigitQCDataBaseHeadDTO> queryDataBaseInfoList(HttpServletRequest request, @RequestBody DigitQCDataBaseDTO dto) throws Exception{
        return digitQCDataBaseService.queryDataBaseInfoList(dto);
    }
}
