package com.zte.interfaces.infor;

import com.zte.application.infor.UpdateInforStageService;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * <AUTHOR>
 */
@ZTERestController("/infor")
@Api(tags = "自动波&待运库位改造", description = "UpdateInforStageController")
public class UpdateInforStageController {
    @Autowired
    private UpdateInforStageService updateInforStageService;

    @ApiOperation("自动波更新待运库位")
    @PostMapping("/updateInforStage")
    public void updateInforStage(){
        updateInforStageService.updateInforStage();
    }
}
