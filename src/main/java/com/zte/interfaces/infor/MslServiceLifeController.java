package com.zte.interfaces.infor;

import com.zte.application.infor.MslServiceLifeService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.MslServiceLifeInfo;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.MslServiceLifeInfoVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@ZTERestController("/mslServiceLife")
@Api(tags = "潮敏物料车间寿命", description = "MslServiceLifeController")
public class MslServiceLifeController {
    @Autowired
    private MslServiceLifeService mslServiceLifeService;

    @ApiOperation("获取潮敏物料车间寿命信息JOB")
    @PostMapping("/insertMslServiceLifeJob")
    public void insertMslServiceLifeJob(HttpServletRequest request) {
        mslServiceLifeService.insertMslServiceLifeJob();
    }

    @ApiOperation("获取仓储中心库存交易时间JOB")
    @PostMapping("/getStockFlowByRidJob")
    public void getStockFlowByRidJob(HttpServletRequest request) {
        mslServiceLifeService.getStockFlowByRidJob();
    }

    @ApiOperation("潮敏物料车间寿命触发红点JOB")
    @PostMapping("/mslServiceLifeRedDotJob")
    public void mslServiceLifeRedDotJob(HttpServletRequest request) {
        mslServiceLifeService.mslServiceLifeRedDotJob();
    }

    @ApiOperation("查询潮敏物料车间寿命信息")
    @PostMapping("/queryMslServiceLife")
    public MslServiceLifeInfoVo queryMslServiceLife(HttpServletRequest request,@RequestBody MslServiceLifeInfo dto) {
        return mslServiceLifeService.queryMslServiceLife(dto);
    }

    @ApiOperation("潮敏物料车间寿命查询接口")
    @PostMapping("/mslServiceLifeQuery")
    public List<MslServiceLifeInfo> mslServiceLifeQuery(HttpServletRequest request,@RequestBody MslServiceLifeInfo dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        return mslServiceLifeService.mslServiceLifeQuery(dto);
    }

    @ApiOperation("查询潮敏物料仓库信息")
    @PostMapping("/selectWhseId")
    public List<SysLookupValuesDTO> selectWhseId(HttpServletRequest request) {
        return mslServiceLifeService.selectWhseId();
    }

    @ApiOperation("潮敏物料车间寿命-导出")
    @PostMapping("/exportExcel")
    public void exportExcel(HttpServletRequest request, @RequestBody MslServiceLifeInfo dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        mslServiceLifeService.exportExcel(dto);
    }
}
