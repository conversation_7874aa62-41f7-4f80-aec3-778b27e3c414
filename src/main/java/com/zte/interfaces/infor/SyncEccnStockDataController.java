package com.zte.interfaces.infor;

import com.google.common.hash.Hashing;
import com.zte.application.infor.SyncEccnStockDataService;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 */
@ZTERestController("/syncEccnData")
@Api(tags = "定时执行同步Eccn数据", description = "SyncEccnStockDataController")
public class SyncEccnStockDataController {
    @Autowired
    private SyncEccnStockDataService syncEccnStockDataService;

    @ApiOperation("定时执行同步Eccn数据")
    @GetMapping("/stockStastics")
    public void syncEccnStockData(){
        syncEccnStockDataService.syncEccnStockData();
    }

}
