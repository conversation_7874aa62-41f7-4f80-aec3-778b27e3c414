package com.zte.interfaces.infor;

import com.zte.application.infor.InventoryholdRecordService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.InventoryholdDTO;
import com.zte.interfaces.infor.dto.InventoryholdRecordDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.InventoryholdRecordListVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZTERestController("/infor")
@Api(tags = "试样超采", description = "InforInventoryholdRecordController")
public class InventoryholdRecordController {

    @Autowired
    private InventoryholdRecordService inforInventoryholdRecordService;

    @ApiOperation("iSRM同步试样超采/库存抽检库存冻结记录到infor")
    @PostMapping("/synInforInventoryholdRecord")
    public void synInforInventoryholdRecord(@RequestBody List<InventoryholdDTO> list){
        inforInventoryholdRecordService.synInforInventoryholdRecord(list);
    }

    @ApiOperation("iSRM同步库存抽检库存解冻记录到infor")
    @PostMapping("/unfreezeInventoryhold")
    public void unfreezeInventoryhold(@RequestBody InventoryholdDTO dto){
        inforInventoryholdRecordService.unfreezeInventoryhold(dto);
    }

    @ApiOperation("iSRM回滚试样超采库存冻结")
    @PostMapping("/deleteInventoryholdRecord")
    public void deleteInventoryholdRecord(@RequestBody List<InventoryholdDTO> list){
        inforInventoryholdRecordService.deleteInventoryholdRecord(list);
    }

    @ApiOperation("查询冻结原因列表")
    @PostMapping("/getInventoryholdCodeList")
    public List<SysLookupValuesDTO> getInventoryholdCodeList(@RequestBody SysLookupValuesDTO dto){
       return inforInventoryholdRecordService.getInventoryholdCodeList(dto);
    }

    @ApiOperation("查询库存冻结记录")
    @PostMapping("/getInventoryholdRecordList")
    public InventoryholdRecordListVO getInventoryholdRecordList(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        return inforInventoryholdRecordService.getInventoryholdRecordList(dto);
    }

    @ApiOperation("获取代码信息")
    @PostMapping("/getItemNoByItemBarcode")
    public InventoryholdRecordDTO getItemNoByItemBarcode(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        return inforInventoryholdRecordService.getItemNoByItemBarcode(dto);
    }

    @ApiOperation("维护库存冻结记录")
    @PostMapping("/saveInventoryholdRecord")
    public void saveInventoryholdRecord(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        inforInventoryholdRecordService.saveInventoryholdRecord(dto);
    }

    @ApiOperation("导入库存冻结记录")
    @PostMapping("/importInventoryHold")
    public void importInventoryHold(HttpServletRequest request, @RequestBody List<InventoryholdRecordDTO> list){
        inforInventoryholdRecordService.importInventoryHold(list);
    }

    @ApiOperation("生效库存冻结记录")
    @PostMapping("/effectiveInventoryholdRecord")
    public void effectiveInventoryholdRecord(HttpServletRequest request, @RequestBody List<InventoryholdRecordDTO> list){
        inforInventoryholdRecordService.effectiveInventoryholdRecord(list);
    }

    @ApiOperation("失效库存冻结记录")
    @PostMapping("/expireInventoryholdRecord")
    public void expireInventoryholdRecord(HttpServletRequest request, @RequestBody List<InventoryholdRecordDTO> list){
        inforInventoryholdRecordService.expireInventoryholdRecord(list);
    }

    @ApiOperation("导出库存冻结记录")
    @PostMapping("/exportInventoryholdRecord")
    public void exportInventoryholdRecord(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        inforInventoryholdRecordService.exportInventoryholdRecord(dto);
    }

    @ApiOperation("导出库存冻结异常记录")
    @PostMapping("/exportInventoryholdException")
    public void exportInventoryholdException(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        inforInventoryholdRecordService.exportInventoryholdException(dto);
    }

    @ApiOperation("批量更新库存冻结记录")
    @PostMapping("/batchUpdateInventoryholdRecord")
    public void batchUpdateInventoryholdRecord(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        inforInventoryholdRecordService.batchUpdateInventoryholdRecord(dto);
    }

    @ApiOperation("库存冻结记录定时冻结JOB")
    @PostMapping("/inventoryholdRecordJob")
    public void inventoryholdRecordJob(HttpServletRequest request, @RequestBody InventoryholdRecordDTO dto){
        inforInventoryholdRecordService.inventoryholdRecordJob(dto);
    }

    @ApiOperation("库存冻结邮件通知及提醒")
    @PostMapping("/inventoryholdRecordSendMail")
    public void inventoryholdRecordSendMail(HttpServletRequest request) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        inforInventoryholdRecordService.inventoryholdRecordSendMail(xEmpNo);
    }

}
