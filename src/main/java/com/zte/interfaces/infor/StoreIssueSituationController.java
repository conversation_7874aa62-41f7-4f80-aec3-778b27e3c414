package com.zte.interfaces.infor;

import com.zte.application.infor.StoreIssueSituationService;
import com.zte.common.utils.Constant;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

@ZTERestController
public class StoreIssueSituationController {

    @Autowired
    private StoreIssueSituationService situationService;
    @GetMapping("/updateProdRecDate")
    public void updateProdRecDate(HttpServletRequest request, String externKey){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        situationService.updateProdRecDate(externKey, xEmpNo);
    }
}
