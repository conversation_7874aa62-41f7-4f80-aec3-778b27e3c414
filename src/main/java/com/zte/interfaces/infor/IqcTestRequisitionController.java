/* Started by AICoder, pid:5e80ff3a3cff4259a07e3b3733fb7fc0 */
package com.zte.interfaces.infor;

import com.zte.application.infor.IqcTestRequisitionService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.IqcTestRequisitionHeadDTO;
import com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO;
import com.zte.interfaces.infor.vo.IqcTestRequisitionListVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@ZTERestController("/iqcTestRequisition")
@Api(tags = "iqc检验领料接口", description = "IqcTestRequisitionController")
public class IqcTestRequisitionController {

    @Autowired
    private IqcTestRequisitionService iqcTestRequisitionService;

    @ApiOperation("新增检验领料接口数据")
    @PostMapping("/insertIqcTestRequisitionInfo")
    public ServiceData<?> insertIqcTestRequisitionInfo(@RequestBody IqcTestRequisitionHeadDTO oList) {
        return iqcTestRequisitionService.insertIqcTestRequisitionInfo(oList);
    }

    @ApiOperation("定时拆分领料明细JOB")
    @PostMapping("/splitIqcDetailsJob")
    public void splitIqcDetailsJob(HttpServletRequest request) {
        iqcTestRequisitionService.splitIqcDetailsJob();
    }

    @ApiOperation("定时推送领料明细到inforJOB")
    @PostMapping("/pushIqcDetailToInfor")
    public void pushIqcDetailToInfor(HttpServletRequest request) {
        iqcTestRequisitionService.pushIqcDetailToInfor();
    }

    @ApiOperation("给ISCP提供IQC领料结果")
    @PostMapping("/getIqcTestStatusToIscp")
    public IqcTestRequisitionListVO getIqcTestStatusToIscp(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        return iqcTestRequisitionService.getIqcTestStatusToIscp(dto);
    }

    @ApiOperation("查询IQC领料明细")
    @PostMapping("/getIqcTestRequisitionList")
    public IqcTestRequisitionListVO getIqcTestRequistionList(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        return iqcTestRequisitionService.getIqcTestRequistionList(dto);
    }

    @ApiOperation("批量更新领料单头")
    @PostMapping("/batchUpdateIqcBill")
    public void batchUpdateIqcBill(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        iqcTestRequisitionService.batchUpdateIqcBill(dto);
    }

    @ApiOperation("批量更新领料单明细")
    @PostMapping("/batchUpdateIqcDetail")
    public void batchUpdateIqcDetail(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        iqcTestRequisitionService.batchUpdateIqcDetail(dto);
    }

    @ApiOperation("导出领料单明细")
    @PostMapping("/exportIqcTestRequistion")
    public void exportIqcTestRequistion(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        iqcTestRequisitionService.exportIqcTestRequistion(dto);
    }

    @ApiOperation("查询IQC领料单头")
    @PostMapping("/getIqcTestRequisitionBill")
    public IqcTestRequisitionListVO getIqcTestRequisitionBill(HttpServletRequest request, @RequestBody IqcTestRequisitionQueryDTO dto) {
        return iqcTestRequisitionService.getIqcTestRequisitionBill(dto);
    }

    @ApiOperation("IQC领料拆分和提交失败邮件通知及提醒")
    @PostMapping("/iqcFailSendMail")
    public void iqcFailSendMail(HttpServletRequest request) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        iqcTestRequisitionService.iqcFailSendMail(xEmpNo);
    }
}
/* Ended by AICoder, pid:5e80ff3a3cff4259a07e3b3733fb7fc0 */