package com.zte.interfaces.infor;

import com.zte.application.infor.InforImesService;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.EdiSoSImesDTO;
import com.zte.interfaces.infor.dto.InforImesQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto;
import com.zte.interfaces.infor.vo.LocVo;
import com.zte.interfaces.infor.vo.ZteinboundserialVo;
import com.zte.interfaces.step.dto.TechItemStoreDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021-12-08 12:30
 */
@RestController
@RequestMapping("imes")
@Api(tags = "imes")
public class InforImesController {
    @Autowired
    private InforImesService inforImesService;

    /**
     * 通过单据号2 查询 infor 发料信息
     *
     * @param externalorderkey2 单据号2
     * @return 单据汇总信息
     */
    @ApiOperation("通过单据号2 查询 infor 发料信息")
    @GetMapping("queryEdiSoSByKey2")
    public ServiceData queryEdiSoSByKey2(@RequestParam String externalorderkey2) {
        return ServiceDataUtil.getSuccess(inforImesService.queryEdiSoSByKey2(externalorderkey2));
    }

    /**
     * 查询条码信息
     *
     * @param externalorderkey2 外部单单号2
     * @param serialnumber      单板条码
     * @param billType      单据类型 10 单板 20 袁材料
     * @return 单板条码
     */
    @ApiOperation("查询单板信息")
    @GetMapping("queryVeneerInfo")
    public ServiceData queryVeneerInfo(@RequestParam String externalorderkey2, @RequestParam String serialnumber,
                                       @RequestParam String billType) {
        return ServiceDataUtil.getSuccess(inforImesService.queryVeneerInfo(externalorderkey2, serialnumber, billType));
    }

    @ApiOperation("查询是否存在已退单未收料")
    @RequestMapping(value = "/getIsReturnReceived", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData getIsReturnReceived(@RequestBody InforImesReturnWarehouseDto entity) {
        return ServiceDataUtil.getSuccess(inforImesService.getIsReturnReceived(entity));
    }

    @ApiOperation("查询已退、任务退数量")
    @RequestMapping(value = "/getReturnQty", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getReturnQty(@RequestBody InforImesQtyDto entity) {
        return ServiceDataUtil.getSuccess(inforImesService.getReturnQty(entity));
    }

    @ApiOperation("查询已发数量")
    @RequestMapping(value = "/getIssuedQty", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getIssuedQty(@RequestBody InforImesQtyDto entity) {
        return ServiceDataUtil.getSuccess(inforImesService.getIssuedQty(entity));
    }

    @ApiOperation("查询需求数量")
    @RequestMapping(value = "/getReqQty", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getReqQty(@RequestBody InforImesQtyDto entity) {
        return ServiceDataUtil.getSuccess(inforImesService.getReqQty(entity));
    }

    @ApiOperation("根据单据、仓库或条码查询已完成收货的条码")
    @RequestMapping(value = "/getSerialnumber", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getSerialnumber(@RequestBody ZteinboundserialVo entity) {
        return ServiceDataUtil.getSuccess(inforImesService.getSerialnumber(entity));
    }

    @ApiOperation("infor库位码正确性校验")
    @RequestMapping(value = "/checkInforLoc", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> checkInforLoc(@RequestBody LocVo entity) {
        return inforImesService.checkInforLoc(entity);
    }

    @ApiOperation("获取单板重量信息")
    @GetMapping(value = "/getTechItemStore", produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> getTechItemStore(@RequestParam(value = "prodplanId", required = true) String prodplanId) {
        return ServiceDataUtil.getSuccess(inforImesService.getTechItemStore(prodplanId));
    }

    @ApiOperation("更新单板重量信息")
    @RequestMapping(value = "/updateTechItemStore", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData<?> updateTechItemStore(@RequestBody TechItemStoreDTO dto) {
        inforImesService.updateTechItemStore(dto);
        return ServiceDataUtil.getSuccess(RetCode.SUCCESS_CODE);
    }

    @ApiOperation("通过单据号2和物料代码查询infor发料信息")
    @PostMapping("queryEdiSoSByKey2andsku")
    public ServiceData queryEdiSoSByKey2andsku(@RequestBody EdiSoSImesDTO ediSoSImesDTO) {
        return ServiceDataUtil.getSuccess(inforImesService.queryEdiSoSByKey2AndSku(ediSoSImesDTO));
    }
}
