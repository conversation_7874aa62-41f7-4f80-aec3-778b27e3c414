package com.zte.interfaces.infor;

import com.zte.application.infor.InforLottableDealService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.InforLottablesDTO;
import com.zte.interfaces.infor.dto.LotattributeDealLogDTO;
import com.zte.interfaces.infor.vo.LotattributeDealLogListVO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.redis.RedisHelper;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;

import static com.zte.common.model.MessageId.*;

/**
 * <AUTHOR>
 */
@ZTERestController("/infor")
@Api(tags = "批属性变更", description = "UpdateInforLottablesController")
public class InforLottableDealController {

    @Autowired
    private InforLottableDealService inforLottableDealService;

    @ApiOperation("批属性变更")
    @PostMapping("/updateInforLottables")
    public void updateInforLottables(@RequestBody InforLottablesDTO dto){
        inforLottableDealService.splitBarcodeList(dto);
    }

    @ApiOperation("查询INFOR批属性变更日志表")
    @PostMapping("/getInforLottableLogList")
    public LotattributeDealLogListVO getInforLottableLogList(@RequestBody LotattributeDealLogDTO dto){
       return inforLottableDealService.getInforLottableLogList(dto);
    }

    @ApiOperation("更新INFOR批属性变更日志表")
    @PostMapping("/updateInforLottableLog")
    public void updateInforLottableLog(HttpServletRequest request, @RequestBody LotattributeDealLogDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEditwho(xEmpNo);
        inforLottableDealService.updateInforLottableLog(dto);
    }

    @ApiOperation("小批量数据归档")
    @PostMapping("/dataArchiving")
    public void dataArchiving(){
        inforLottableDealService.dataArchiving();
    }

    @ApiOperation("redis删除key")
    @GetMapping("/deleteRedisKey")
    public void deleteRedisKey(String key){
        try {
            RedisHelper.delete(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation("redis获取key")
    @GetMapping("/getByRedisKey")
    public String getByRedisKey(String key){
        BusiAssertException.isEmpty(key,PLEASE_INPUT_KEY_VALUE);
        BusiAssertException.isEmpty(RedisHelper.get(key.getBytes(StandardCharsets.UTF_8)),CAN_NOT_GET_KEY_VALUE);
        return new String(RedisHelper.get(key.getBytes(StandardCharsets.UTF_8)),StandardCharsets.UTF_8);
    }
}
