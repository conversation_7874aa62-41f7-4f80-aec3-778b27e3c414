package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 5.4.1 ⼚商新增或取消箱包与SN绑定关系DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class SyncCartonSnRelationOperateDTO {
    private Integer operateType; // 0:新增 1:取消
    private String source;      // ZTE101
    private List<CartonOperation> operateData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    @Accessors(chain = true)
    public static class CartonOperation {
        private String cartonId;   // 箱号
        private String mpn;       // MPN号
        private List<SnOperation> operateDetailList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    @Accessors(chain = true)
    public static class SnOperation {
        private String sn;       // SN码
        private String originSn; // 原始SN(可选)
    }
}

