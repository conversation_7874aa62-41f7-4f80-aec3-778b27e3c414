package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 采购订单收货DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class ZtePurchaseOrderUploadHeadDTO {

    private String dataTransferBatchNo;
    private String uuid;
    private String materialDocNo;
    private String materialDocYear;
    private String dataDate;
    private String receivingDate;
    private String vendorName;
    private List<ZtePurchaseOrderUploadDetailDTO> purchaseOrderDeliveryItemParams;

}

