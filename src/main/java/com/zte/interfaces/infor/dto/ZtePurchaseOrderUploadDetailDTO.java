package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 采购订单收货DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class ZtePurchaseOrderUploadDetailDTO {

    private String materialDocNo;
    private String materialDocLine;
    private String odmPlantCode;
    private String purchaseOrderNo;
    private String poLineNo;
    private String materialCode;
    private String materialDesc;
    private BigDecimal receivingQuantity;
    private String unit;
    private String odmStorageLoc;
    private String movementType;
    private String movementCategory;
    private String originalPurchaseOrderNo;
    private String originalPOLineNo;
    private String purchaseOrderType;
    private String purchaseNetPrice;
    private String currencyCode;
    private String orderDate;

}

