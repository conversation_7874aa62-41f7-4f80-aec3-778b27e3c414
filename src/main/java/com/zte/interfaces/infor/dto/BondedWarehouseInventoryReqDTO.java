package com.zte.interfaces.infor.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Deacription 保税仓库存查询DTO
 * <AUTHOR>
 * @Date 2020/8/18 15:40
 **/
public class BondedWarehouseInventoryReqDTO {

    @ApiModelProperty(value = "查询条件，要支持批量查询")
    private List<BondedWarehouseInventoryDTO> queryList;

    public List<BondedWarehouseInventoryDTO> getQueryList() {
        return queryList;
    }

    public void setQueryList(List<BondedWarehouseInventoryDTO> queryList) {
        this.queryList = queryList;
    }

    @Override
    public String toString() {
        return "BondedWarehouseInventoryReqDTO{" +
                "queryList=" + queryList +
                '}';
    }
}