package com.zte.interfaces.infor.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @Deacription 保税仓库存查询返回DTO
 * <AUTHOR>
 * @Date 2020/8/18 15:59
 **/
public class BondedWarehouseInventoryInfoDTO {

    @ApiModelProperty(value = "22条码")
    private String itemBarcode;

    @ApiModelProperty(value = "仓库编码")
    private String stockNo;

    @ApiModelProperty(value = "物料代码")
    private String itemCode;

    @ApiModelProperty(value = "箱号")
    private String boxNo;

    @ApiModelProperty(value = "货主分类")
    private String storeKey;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getStockNo() {
        return stockNo;
    }

    public void setStockNo(String stockNo) {
        this.stockNo = stockNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(String boxNo) {
        this.boxNo = boxNo;
    }

    public String getStoreKey() {
        return storeKey;
    }

    public void setStoreKey(String storeKey) {
        this.storeKey = storeKey;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Override
    public String toString() {
        return "BondedWarehouseInventoryInfoDTO{" +
                "itemBarcode='" + itemBarcode + '\'' +
                ", stockNo='" + stockNo + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", boxNo='" + boxNo + '\'' +
                ", storeKey='" + storeKey + '\'' +
                ", qty=" + qty +
                '}';
    }
}