package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * SN绑定DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class UpdateZtePkgIdBoundSnInfoDTO {
    private String externalkey;
    private String externalNo;  // 外部单号
    private String messageType;
    private String messageId;
    private String empNo;
    private String sendStatus;

    private List<Integer> serialkeys;

    private List<String> snCodes;
}

