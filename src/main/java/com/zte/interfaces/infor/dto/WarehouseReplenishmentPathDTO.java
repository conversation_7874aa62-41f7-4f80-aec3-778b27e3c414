package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class WarehouseReplenishmentPathDTO {
    /**
     * 补货类型
     */
    private String repType;
    /**
     * INFOR库位
     */
    private String whseid;
    /**
     * 库房名称
     */
    private String warehouse;

    /**
     * 位置码
     */
    private List<String> locList;
    /**
     * 动态库区
     */
    private String warehouseArea;
    /**
     * 库存库区
     */
    private String inventoryStorageArea;
    /**
     * 当前页
     */
    private Integer pageIndex;
    /**
     * 页码
     */
    private Integer pageSize;
    private Integer startRow;
    private Integer endRow;

    private String empNo;
}
