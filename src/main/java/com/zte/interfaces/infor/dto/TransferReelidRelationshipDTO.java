package com.zte.interfaces.infor.dto;

import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class TransferReelidRelationshipDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiParam("数据字典明细编号")
	private BigDecimal serialkey;

	@ApiParam("转代码单号")
	private String externreceiptkey;

	@ApiParam("仓库")
	private String whseid;

	@ApiParam("原物料代码")
	private String oldSku;

	@ApiParam("原物料条码")
	private String oldLottable02;

	@ApiParam("原REELID")
	private String oldSerialnumber;

	@ApiParam("新物料代码")
	private String newSku;

	@ApiParam("新物料条码")
	private String newLottable02;

	@ApiParam("新REELID")
	private String newSerialnumber;

	@ApiParam("创建日期")
	private Date adddate;

	@ApiParam("创建人")
	private String addwho;

	@ApiParam("更新日期")
	private Date editdate;

	@ApiParam("更新人")
	private String editwho;

	@ApiParam("是否推送")
	private BigDecimal issend;

	@ApiParam("推送次数")
	private BigDecimal sendtimes;

	@ApiParam("消息")
	private String requestparam;

	public BigDecimal getSerialkey() {
		return serialkey;
	}

	public void setSerialkey(BigDecimal serialkey) {
		this.serialkey = serialkey;
	}

	public String getExternreceiptkey() {
		return externreceiptkey;
	}

	public void setExternreceiptkey(String externreceiptkey) {
		this.externreceiptkey = externreceiptkey;
	}

	public String getWhseid() {
		return whseid;
	}

	public void setWhseid(String whseid) {
		this.whseid = whseid;
	}

	public String getOldSku() {
		return oldSku;
	}

	public void setOldSku(String oldSku) {
		this.oldSku = oldSku;
	}

	public String getOldLottable02() {
		return oldLottable02;
	}

	public void setOldLottable02(String oldLottable02) {
		this.oldLottable02 = oldLottable02;
	}

	public String getOldSerialnumber() {
		return oldSerialnumber;
	}

	public void setOldSerialnumber(String oldSerialnumber) {
		this.oldSerialnumber = oldSerialnumber;
	}

	public String getNewSku() {
		return newSku;
	}

	public void setNewSku(String newSku) {
		this.newSku = newSku;
	}

	public String getNewLottable02() {
		return newLottable02;
	}

	public void setNewLottable02(String newLottable02) {
		this.newLottable02 = newLottable02;
	}

	public String getNewSerialnumber() {
		return newSerialnumber;
	}

	public void setNewSerialnumber(String newSerialnumber) {
		this.newSerialnumber = newSerialnumber;
	}

	public Date getAdddate() {
		return adddate;
	}

	public void setAdddate(Date adddate) {
		this.adddate = adddate;
	}

	public String getAddwho() {
		return addwho;
	}

	public void setAddwho(String addwho) {
		this.addwho = addwho;
	}

	public Date getEditdate() {
		return editdate;
	}

	public void setEditdate(Date editdate) {
		this.editdate = editdate;
	}

	public String getEditwho() {
		return editwho;
	}

	public void setEditwho(String editwho) {
		this.editwho = editwho;
	}

	public BigDecimal getIssend() {
		return issend;
	}

	public void setIssend(BigDecimal issend) {
		this.issend = issend;
	}

	public BigDecimal getSendtimes() {
		return sendtimes;
	}

	public void setSendtimes(BigDecimal sendtimes) {
		this.sendtimes = sendtimes;
	}

	public String getRequestparam() {
		return requestparam;
	}

	public void setRequestparam(String requestparam) {
		this.requestparam = requestparam;
	}
}
