package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class InventoryTransOperateDetailDTO {
    private String tranOrderNo;
    private String orderLineNo;
    private String mpn;
    private String itemName;
    private Integer quantity;
    private Byte operationType;
    private String tranDate;
}
