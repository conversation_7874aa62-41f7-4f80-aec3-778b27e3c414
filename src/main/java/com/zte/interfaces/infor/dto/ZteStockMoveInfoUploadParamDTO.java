package com.zte.interfaces.infor.dto;
/* Started by AICoder, pid:6419c55053c547a2b6999307eadcc794 */
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 库存推送DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class ZteStockMoveInfoUploadParamDTO {

    private String requestTime;
    private String requester;
    private String dataTransferBatchNo;
    private String requestId;
    private Integer pageNo;
    private String isLastPage;
    private Integer pageSize;
    private List<ZteStockMoveInfoUploadDTO> body;

}
/* Ended by AICoder, pid:6419c55053c547a2b6999307eadcc794 */
