package com.zte.interfaces.infor;

import com.zte.application.infor.InforIapsService;
import com.zte.interfaces.infor.dto.IapsStockDto;
import com.zte.interfaces.infor.dto.XcItemInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@ZTERestController("/InforIaps")
@Api(tags = "infor与计划对接", description = "InforIapsController")
public class InforIapsController {

    @Autowired
    private InforIapsService inforIapsService;

    @ApiOperation("计划同步XC物料后进先出信息到infor")
    @PostMapping("/synXcItemInfo")
    public void synXcItemInfo(@RequestBody List<XcItemInfoDTO> list){
        inforIapsService.synXcItemInfo(list);
    }

    @ApiOperation("根据物料信息和STEP库位查询INFOR库存信息")
    @PostMapping("/getInforStockInfo")
    public ServiceData getInforStockInfo(@RequestBody IapsStockDto dto) throws Exception{
        return inforIapsService.getInforStockInfo(dto);
    }

}
