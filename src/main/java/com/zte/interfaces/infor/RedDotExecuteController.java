package com.zte.interfaces.infor;

import com.zte.application.infor.RedDotExecuteService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.RedDotExecuteInfo;
import com.zte.interfaces.infor.vo.RedDotExecuteDetilInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoListVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@ZTERestController("/redDotExecute")
@Api(tags = "红点执行", description = "RedDotExecuteController")
public class RedDotExecuteController {
    @Autowired
    private RedDotExecuteService redDotExecuteService;

    @ApiOperation("订单异常触发红点JOB")
    @PostMapping("/orderRedDotTriggerJob")
    public void orderRedDotTriggerJob(HttpServletRequest request) {
        redDotExecuteService.orderRedDotTriggerJob();
    }

    @ApiOperation("计调任务红点-查询")
    @PostMapping("/queryRedDotInfo")
    public RedDotExecuteInfoListVo queryRedDotInfo(@RequestBody RedDotExecuteInfo dto) {
        return redDotExecuteService.queryRedDotInfo(dto);
    }

    @ApiOperation("计调任务红点-导出")
    @PostMapping("/exportExcel")
    public void exportExcel(HttpServletRequest request, @RequestBody RedDotExecuteInfo dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        redDotExecuteService.exportExcel(dto);
    }

    @ApiOperation("计调任务红点-状态修改")
    @PostMapping("/updateRedDotInfo")
    public void updateRedDotInfo(RedDotExecuteInfo dto){
        redDotExecuteService.updateRedDotInfo(dto);
    }

    @ApiOperation("计调任务红点-查询明细")
    @PostMapping("/queryRedDotDetilInfo")
    public RedDotExecuteDetilInfoListVo queryRedDotDetilInfo(RedDotExecuteInfo dto){
        return redDotExecuteService.queryRedDotDetilInfo(dto);
    }

    @ApiOperation("计调任务红点-执行红点")
    @PostMapping("/executeRedDot")
    public void executeRedDot(RedDotExecuteInfo dto){
        redDotExecuteService.executeRedDot(dto);
    }

}
