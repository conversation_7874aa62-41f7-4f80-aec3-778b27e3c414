package com.zte.interfaces.infor;

import com.zte.application.infor.DeliveryFloorService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.DeliveryFloorDTO;
import com.zte.interfaces.infor.dto.DeliveryPrintInfoDTO;
import com.zte.interfaces.infor.vo.DeliveryFloorListVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 */
@ZTERestController("/deliveryFloor")
@Api(tags = "无人接收楼栋配置接口", description = "DeliveryFloorController")
public class DeliveryFloorController {
    @Autowired
    private DeliveryFloorService deliveryFloorService;

    @ApiOperation("无人接收楼栋配置导入接口")
    @PostMapping("/importFloorInfo")
    public void importFloorInfo(HttpServletRequest request, @RequestBody List<DeliveryFloorDTO> list) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        deliveryFloorService.importFloorInfo(list, xEmpNo);

    }

    @ApiOperation("无人接收楼栋配置查询接口")
    @PostMapping("/getFloorInfo")
    public DeliveryFloorListVo getFloorInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        return deliveryFloorService.getFloorInfo(dto);
    }

    @ApiOperation("无人接收楼栋配置失效接口")
    @PostMapping("/loseFloorInfo")
    public void loseFloorInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        deliveryFloorService.loseFloorInfo(dto, xEmpNo);
    }

    @ApiOperation("无人接收楼栋配置生效接口")
    @PostMapping("/effectFloorInfo")
    public void effectFloorInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        deliveryFloorService.effectFloorInfo(dto, xEmpNo);
    }
    @ApiOperation("无人接收呼叫帮助触发红点")
    @PostMapping("/sendRedDotInfo")
    public void sendRedDotInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        deliveryFloorService.sendRedDotInfo(dto);
    }

    @ApiOperation("无人接收呼叫帮助触发红点")
    @PostMapping("/getPrintInfo")
    public List<DeliveryPrintInfoDTO> getPrintInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        return deliveryFloorService.getPrintInfo(dto);
    }

    @ApiOperation("无人接收楼栋配置删除接口")
    @PostMapping("/deleteFloorInfo")
    public void deleteFloorInfo(HttpServletRequest request, @RequestBody DeliveryFloorDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        deliveryFloorService.deleteFloorInfo(dto, xEmpNo);
    }

}
