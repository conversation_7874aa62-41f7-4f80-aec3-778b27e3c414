package com.zte.interfaces.infor;

import com.zte.application.infor.BraidCutMaterialService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.OutPickingTaskDto;
import com.zte.interfaces.infor.dto.PickingTaskDto;
import com.zte.interfaces.infor.vo.BraidPickingInfoListVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 */
@ZTERestController("/braidCutMaterial")
@Api(tags = "编带剪料机对接", description = "BraidCutMaterialController")
public class BraidCutMaterialController {
    @Autowired
    private BraidCutMaterialService braidCutMaterialService;

    @ApiOperation("INFOR_拆零拣货任务接口")
    @PostMapping("/queryPickingTask")
    public List<OutPickingTaskDto> queryPickingTask(HttpServletRequest request, @RequestBody List<PickingTaskDto> list) {
        return braidCutMaterialService.queryPickingTask(list);
    }

    @ApiOperation("查询拆零拣货清单")
    @PostMapping("/queryPickingInfo")
    public BraidPickingInfoListVo queryPickingInfo(@RequestBody PickingTaskDto queryParams){
        return braidCutMaterialService.queryPickingInfo(queryParams);
    }

    @ApiOperation("导出拆零拣货清单")
    @PostMapping("/exportPickingInfo")
    public void exportPickingInfo(HttpServletRequest request,@RequestBody PickingTaskDto queryParams) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        queryParams.setEmpNo(xEmpNo);
        braidCutMaterialService.exportPickingInfo(queryParams);
    }
}
