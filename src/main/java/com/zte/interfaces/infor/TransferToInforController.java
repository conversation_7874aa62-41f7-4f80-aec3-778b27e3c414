package com.zte.interfaces.infor;

import com.zte.application.infor.TransferToInforService;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 */
@ZTERestController("/transferToInfor")
@Api(tags = "调拨单自动提交到infor", description = "TransferToInforController")
public class TransferToInforController {

    @Autowired
    private TransferToInforService transferToInforService;

    @ApiOperation("调拨单自动提交到infor")
    @PostMapping("/transferToInfor")
    public ServiceData transferToInfor() throws Exception{
        ServiceData ret = new ServiceData();
        transferToInforService.transferToInfor();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
}
