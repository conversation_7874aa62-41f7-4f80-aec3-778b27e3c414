/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-08-30
 * 修改历史 :
 *   1. [2019-08-30] 创建文件 by 6396000647
 **/
package com.zte.interfaces.infor;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.infor.EdiAsnqcsService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.infor.dto.VmiStoreDTO;
import com.zte.interfaces.infor.dto.VmiStoreParamDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/infor")
@Api(tags = "INFOR接口", description="EdiAsnqcsController")
public class EdiAsnqcsController {

    @Autowired
    private EdiAsnqcsService ediAsnqcSService;  
    /**
     * 服务调用成功返回值
     * */
    private String serviceSuccessResult="0000";
    
    private String iscpServiceSuccessResult="S";

    /**
     * 添加方法功能描述
     * @param request
     * @param Id
     * @return ServiceData
     * @throws Exception 
     **/ 
    @ApiOperation("检验接口定时调用,优化")
    @RequestMapping(value = "/asnQcTrans",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData asnQcTrans(HttpServletRequest request,int rn) throws Exception { 
        return ediAsnqcSService.selectAsnQcData(rn); 
        
    }
    /**
     * 添加方法功能描述
     * @param request
     * @param Id
     * @return ServiceData
     * @throws Exception 
     **/ 
    @ApiOperation("检验接口定时调用,优化，延时处理已经超过4次的数据")
    @RequestMapping(value = "/asnQcDelayTrans",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData asnQcTrans(HttpServletRequest request) throws Exception { 
        return ediAsnqcSService.selectAsnQcDataDelay(); 
        
    }
    /**
     * 添加方法功能描述
     * @param request
     * @param Id
     * @return ServiceData
     * @throws Exception 
     **/ 
    @ApiOperation("检验接口定时调用")
    @RequestMapping(value = "/qualityCheckService",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData qualityCheckService(HttpServletRequest request,String receiptKey,String wmwhseId) throws Exception {
        
        return ediAsnqcSService.selectQualityCheck(receiptKey,wmwhseId); 
        
    } 
    
    /**
    * 添加方法功能描述
    * @param request
    * @param Id
    * @return ServiceData
    * @throws Exception 
    **/ 
   @ApiOperation("INFOR系统VMI现存量接口")
   @RequestMapping(value = "/stInforVmiStore",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
   public ServiceData stInforVmiStore(HttpServletRequest request,VmiStoreParamDTO vmiStoreParamDTO) throws Exception{
   	ServiceData ret = new ServiceData();
   	ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
   	List<String> whseidList=new ArrayList();
   	if(!StringUtils.isEmpty(vmiStoreParamDTO.getWhseid())
   			&&vmiStoreParamDTO.getWhseid().length()>0){
   		whseidList.add(vmiStoreParamDTO.getWhseid());
   	}
   	else{ 
   		ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE,RetCode.VALIDATIONERROR_MSGID));
   		ret.setBo(CommonUtils.getLmbMessage(MessageId.WHSEID_CAN_NOT_BE_EMPTY));
   		return ret;
   	}
   	if(whseidList.size()>0){
   		
   		List<VmiStoreDTO> list=ediAsnqcSService.getVmiStoreByWmwhse(vmiStoreParamDTO);
   	   	ret.setBo(list);
   	}
   	return ret;
   }
   
   @ApiOperation("超期复检单送检接口 ")
   @RequestMapping(value = "/sendDelayRecheckToCheck",method = RequestMethod.GET)
   public ServiceData sendDelayRecheckToCheck(HttpServletRequest request,String recheckNo) throws Exception {
       return ediAsnqcSService.sendDelayRecheckToCheck(recheckNo); 
   } 
}