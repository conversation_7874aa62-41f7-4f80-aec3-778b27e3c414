package com.zte.interfaces.infor;

import com.zte.application.infor.IBondedWarehouseService;
import com.zte.domain.model.infor.BondedWarehouseInventoryInfo;
import com.zte.interfaces.infor.dto.BondedWarehouseInventoryReqDTO;
import com.zte.interfaces.infor.dto.BondedWarehouseInventoryInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Deacription 整箱出库控制器
 * <AUTHOR>
 * @Date 2020/8/18 15:33
 **/
@RestController
@RequestMapping("/infor")
@Api(tags = "保税仓-整箱出库",description = "BondedWarehouseController")
public class BondedWarehouseController {

    @Autowired
    private IBondedWarehouseService bondedWarehouseService;

    @ApiOperation("保税仓库存查询接口")
    @PostMapping(value = "/bondedWarehouseInventory", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<List<BondedWarehouseInventoryInfoDTO>> queryBondedWarehouseInventory(@RequestBody BondedWarehouseInventoryReqDTO bondedWarehouseInventoryReqDTO) {
        ServiceData<List<BondedWarehouseInventoryInfoDTO>> serviceData = new ServiceData<>();
        if (CommonUtils.isEmpty(bondedWarehouseInventoryReqDTO.getQueryList())) {
            serviceData.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
            return serviceData;
        }
        List<BondedWarehouseInventoryInfo> inventoryInfoList = bondedWarehouseInventoryReqDTO.getQueryList().stream().map(
                t -> {
                    BondedWarehouseInventoryInfo inventoryInfo = new BondedWarehouseInventoryInfo();
                    BeanUtils.copyProperties(t, inventoryInfo);
                    return inventoryInfo;
                }
        ).collect(Collectors.toList());
        ServiceData<List<BondedWarehouseInventoryInfo>> listServiceData = bondedWarehouseService.queryBondedWarehouseInventory(inventoryInfoList);
        if (CommonUtils.isNotEmpty(listServiceData.getBo())) {
            List<BondedWarehouseInventoryInfoDTO> inventoryInfoDTOList = listServiceData.getBo().stream().map(
                    t -> {
                        BondedWarehouseInventoryInfoDTO bondedWarehouseInventoryInfoDTO = new BondedWarehouseInventoryInfoDTO();
                        BeanUtils.copyProperties(t, bondedWarehouseInventoryInfoDTO);
                        return bondedWarehouseInventoryInfoDTO;
                    }).collect(Collectors.toList());
            serviceData.setBo(inventoryInfoDTOList);
        }
        serviceData.setCode(listServiceData.getCode());
        return serviceData;
    }
}