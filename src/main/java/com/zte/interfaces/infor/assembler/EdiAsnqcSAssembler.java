/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-08-30
 * 修改历史 :
 *   1. [2019-08-30] 创建文件 by 6396000647
 **/
package com.zte.interfaces.infor.assembler;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.zte.domain.model.infor.EdiAsnqcS;
import com.zte.interfaces.infor.dto.EdiAsnqcSDTO;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class EdiAsnqcSAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return EdiAsnqcSDTO
     **/
    public static EdiAsnqcSDTO toDTO(EdiAsnqcS entity) {
        EdiAsnqcSDTO dto = new EdiAsnqcSDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return EdiAsnqcS
     **/
    public static EdiAsnqcS toEntity(EdiAsnqcSDTO dto) {
        EdiAsnqcS entity = new EdiAsnqcS();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<EdiAsnqcSDTO>
     **/
    public static java.util.List<EdiAsnqcSDTO> toEdiAsnqcSDTOList(java.util.List<EdiAsnqcS> entityList) {
        List<EdiAsnqcSDTO> dtoList = new ArrayList<EdiAsnqcSDTO>();
        for (EdiAsnqcS entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<EdiAsnqcS>
     **/
    public static java.util.List<EdiAsnqcS> toEdiAsnqcSList(java.util.List<EdiAsnqcSDTO> dtoList) {
        List<EdiAsnqcS> entityList = new ArrayList<EdiAsnqcS>();
        for (EdiAsnqcSDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}