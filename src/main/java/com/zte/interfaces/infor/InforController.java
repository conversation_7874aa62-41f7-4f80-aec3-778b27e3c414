package com.zte.interfaces.infor;

import java.util.List;


import com.zte.interfaces.infor.dto.*;
import com.zte.resourcewarehouse.common.annotation.ServiceDataResult;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.zte.application.infor.InforIwmsIscpService;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.constraints.NotNull;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/

@ZTERestController("/inforInquery")
@Api(tags = "infor查询接口", description = "InforController")
public class InforController {

    public Logger logger = LoggerFactory.getLogger(InforController.class);

    @Autowired
    private InforIwmsIscpService inforIwmsIscpService;

    @ApiOperation("价格信息更新")
    @PostMapping("/price/updateOnlineFallBackBatch")
    public ServiceData<?> updateOnlineFallBackPriceBatch(@RequestBody List<OnlineFallBackPriceHeadDTO> oList) {
        return inforIwmsIscpService.updateFallBackSoPrice(oList);
    }

    @ApiOperation("VMI出库价格信息更新")
    @PostMapping("/price/updateVmiSoPrice")
    public ServiceData<?> updateVmiSoPrice(@RequestBody List<VmiSoPriceDTO> oList) throws CloneNotSupportedException {
        return inforIwmsIscpService.updateVmiSoPrice(oList);
    }

    @ApiOperation("v1.1 签收/入库/出库回写ISCP")
    @PostMapping("/iscpEdiEntry")
    public ServiceData<?> iscpEdiEntry(String externkey, String sourceTable) {
        return inforIwmsIscpService.iscpEdiEntry(externkey, sourceTable);
    }
    @ApiOperation("推送iscp返回的数据到kafka")
    @PostMapping("/dealIscpReturnData")
    @ServiceDataResult
    public void dealIscpReturnData(String serialKey, String sourceTable) {
         inforIwmsIscpService.dealIscpReturnData(serialKey,sourceTable);
    }
    @ApiOperation("ISCP返回不能处理kafka消息给iwms")
    @PostMapping("/iscpReturnInfor")
    @ServiceDataResult
    public void iscpReturnInfor(@RequestBody ISCPReturnDTO dto) {
        inforIwmsIscpService.iscpReturnInfor(dto);
    }
    @ApiOperation("ISCP出入库反馈告警")
    @GetMapping("/warnInfor")
    @ServiceDataResult
    public void warnInfor() {
        inforIwmsIscpService.warnInfor();
    }

    @ApiOperation("REELID库存查询")
    @PostMapping("/getReelidInventory")
    public List<ReelidInventoryOutDTO> getReelidInventory(@RequestBody ReelidInventoryInputDTO reelidInventoryInputDTO) {
        return inforIwmsIscpService.getReelidInventory(reelidInventoryInputDTO);
    }
    
    @ApiOperation("箱标签信息查询")
    @PostMapping("/getBoxLabelInfor")
    public ServiceData<?> getBoxLabelInfor(@RequestBody BoxLabelDTO boxLabelDTO) {
        return inforIwmsIscpService.getBoxLabelInfor(boxLabelDTO);
    }

}