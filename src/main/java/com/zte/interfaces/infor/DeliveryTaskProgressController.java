package com.zte.interfaces.infor;

import com.zte.application.infor.DeliveryTaskProgressService;
import com.zte.interfaces.infor.dto.DeliverTaskProgressDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 */
@ZTERestController("/deliveryTaskProgress")
@Api(tags = "配送任务进展INFOR数据接口", description = "DeliveryTaskProgressController")
public class DeliveryTaskProgressController {
    @Autowired
    private DeliveryTaskProgressService deliveryTaskProgres;

    @ApiOperation("查询配送任务进展INFOR数据")
    @PostMapping("/deliveryTaskQuery")
    public ServiceData<?> deliveryTaskQuery(HttpServletRequest request, @RequestBody DeliverTaskProgressDTO dto) {
        List<DeliverTaskProgressDTO> taskProgressDTOList = deliveryTaskProgres.deliveryTaskQuery(dto);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(taskProgressDTOList);
        return ret;
    }
}
