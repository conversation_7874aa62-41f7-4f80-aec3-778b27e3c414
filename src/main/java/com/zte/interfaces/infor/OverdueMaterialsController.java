package com.zte.interfaces.infor;
/* Started by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */
// controller类

import com.zte.application.infor.OverdueMaterialsService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.OverdueMaterialsDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.OverDueMaterialsListVO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@ZTERestController("/overdueMaterials")
@Api(tags = "超期物料需求", description = "OverdueMaterialsController")
public class OverdueMaterialsController {

    @Autowired
    private OverdueMaterialsService overdueMaterialsService;

    @ApiOperation("查询物料维护记录")
    @PostMapping("/query")
    public OverDueMaterialsListVO query(HttpServletRequest request, @RequestBody OverdueMaterialsDTO overdueMaterialsDTO) {
        return overdueMaterialsService.query(overdueMaterialsDTO);
    }

    /* Ended by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */
    // 其他接口方法...
    @ApiOperation("导出超期发料需求")
    @PostMapping("/exportOverdueMaterialsRecord")
    public void exportOverdueMaterialsRecord(HttpServletRequest request, @RequestBody OverdueMaterialsDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setEmpNo(xEmpNo);
        overdueMaterialsService.exportOverdueMaterialsRecord(dto);
    }

    @ApiOperation("查询数据字典信息")
    @PostMapping("/getOverdueSysList")
    public List<SysLookupValuesDTO> getOverdueSysList(@RequestBody SysLookupValuesDTO dto) {
        return overdueMaterialsService.getOverdueSysList(dto);
    }

    @ApiOperation("导入超期发料需求")
    @PostMapping("/importOverdueMaterials")
    public void importOverdueMaterials(HttpServletRequest request, @RequestBody List<OverdueMaterialsDTO> dtos) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        overdueMaterialsService.importOverdueMaterials(dtos,xEmpNo);
    }
    @ApiOperation("三次超期邮件监控预警")
    @PostMapping("/overdueMaterialWarnEmail")
    public void overdueMaterialWarnEmail(HttpServletRequest request) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        overdueMaterialsService.overdueMaterialWarnEmail(xEmpNo);
    }
    @ApiOperation("三次超期物料計算")
    @PostMapping("/overdueMaterialCalc")
    public void overdueMaterialCalc(HttpServletRequest request) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        overdueMaterialsService.overdueMaterialCalc(xEmpNo);
    }
}