/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.interfaces.infor;

import com.zte.application.infor.InforBarcodeCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.RuleReqDTO;
import com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/** 
 * [描述] <br> 
 *  
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年06月15日 <br>
 * @see com.zte.interfaces.infor <br>
 */

@RestController
@RequestMapping("/inforBarcodeCenter")
@Api(tags = "infor与条码中心对接", description = "InforBarcodeCenterController")
public class InforBarcodeCenterController {

    public Logger log = LoggerFactory.getLogger(InforBarcodeCenterController.class);
	
	@Autowired
    public InforBarcodeCenterService inforBarcodeCenterService;
	
	@ApiOperation("定时推送REELID转代码注册的数据推送给条码中心")
	@RequestMapping(value = "/pushTransferReelidToBarcodeCenter",method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public ServiceData<?> pushTransferReelidToBarcodeCenter(HttpServletRequest request, @RequestBody TransferReelidRelationshipDTO dto) {

		String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        String xAuthValue = request.getHeader(Constant.X_AUTH_VALUE);
        if(StringUtils.isEmpty(xEmpNo) || StringUtils.isEmpty(xAuthValue)){
        	return ServiceDataUtil.getValidAttionError(CommonUtils.getLmbMessage(MessageId.PUSH_HZBILL_TO_STORAGECENTER_001));
        }
		return inforBarcodeCenterService.pushTransferReelidToBarcodeCenter(xEmpNo, xAuthValue, dto);
    }

    @ApiOperation("获取SN短序列号")
    @PostMapping("/getShortSnCode")
    public ServiceData<?> getShortSnCode(@RequestBody RuleReqDTO dto) {
        try {
            // 调用服务获取短SN序列
            return inforBarcodeCenterService.getShortSnCode(dto);
        } catch (Exception e) {
            log.error("获取SN短序列号业务异常: {}", e.getMessage(), e);
            return ServiceDataUtil.getBusinessError("获取SN短序列号失败，请稍后重试");
        }
    }

}
