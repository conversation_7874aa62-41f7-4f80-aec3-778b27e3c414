/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.interfaces.infor;

import javax.servlet.http.HttpServletRequest;

import com.zte.interfaces.infor.dto.PoInBoundDTO;
import com.zte.interfaces.infor.dto.PoInBoundInfoDTO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.zte.application.infor.InforStorageCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.SoOutBoundDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;


@ZTERestController("/inforStorageCenter")
@Api(description = "infor与仓储中心对接")
public class InforStorageCenterController {
	
	@Autowired
    public InforStorageCenterService inforStorageCenterService;

	@ApiOperation("定时捞取INFOR已经出库完成的终端备件单据并写到日志表")
	@GetMapping("/getSoByBillTypeHz")
    public void getSoByBillTypeHz(String externalOrderkey2,String orderKey) {
	    inforStorageCenterService.getSoByBillTypeHz(externalOrderkey2, orderKey);
    }
	
	@ApiOperation("定时捞取终端备件的数据推送给仓储中心")
	@GetMapping("/pushHzBillToStorageCenter")
    public void pushHzBillToStorageCenter(HttpServletRequest request, String externalOrderkey2, String orderKey) {
		String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        String xAuthValue = request.getHeader(Constant.X_AUTH_VALUE);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        BusiAssertException.isEmpty(xAuthValue, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);

        inforStorageCenterService.pushHzBillToStorageCenter(xEmpNo, xAuthValue, externalOrderkey2, orderKey);
    }

	@ApiOperation("INFOR出库信息查询")
    @PostMapping("/so/outDetail")
    public ServiceData<?> getReelidOutDetail(@RequestBody SoOutBoundDTO boundDTO) {
        return inforStorageCenterService.selectSoOutBoundInfo(boundDTO);
    }

    @ApiOperation("出库信息查询")
    @PostMapping("/so/ediSoSInfo")
    public ServiceData<?> getEdiSosInfo(@RequestBody SoOutBoundDTO boundDTO) {
        return inforStorageCenterService.selectEdiSosInfo(boundDTO);
    }

    @ApiOperation("定时捞取INFOR已经出库完成的逆向报废单据并写到日志表")
    @PostMapping("/getSoByReverseScrap")
    public void getSoByReverseScrap(String externalOrderkey2,String orderkey) {
	    inforStorageCenterService.getSoByReverseScrap(externalOrderkey2, orderkey);
    }

    @ApiOperation("定时捞取逆向报废的数据推送给仓储中心")
    @PostMapping("/pushReverseScrapBillToStorageCenter")
    public void pushReverseScrapBillToStorageCenter(HttpServletRequest request, String externalOrderkey2, String orderKey) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);

	    inforStorageCenterService.pushReverseScrapBillToStorageCenter(xEmpNo, externalOrderkey2, orderKey);
    }

    @ApiOperation("入库信息查询")
    @PostMapping("/po/ediPoSInfo")
    public List<PoInBoundInfoDTO> getEdiPosInfo(@RequestBody PoInBoundDTO inboundDTO) {
        return inforStorageCenterService.selectEdiPosInfo(inboundDTO);
    }
}
