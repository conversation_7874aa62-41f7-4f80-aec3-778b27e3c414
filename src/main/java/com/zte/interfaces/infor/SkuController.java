package com.zte.interfaces.infor;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.infor.SkuService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.infor.dto.SkuQueryDTO;
import com.zte.interfaces.infor.dto.SkuReelIdInputDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/infor/sku")
@Api(tags="SKU接口", description="SkuController")
public class SkuController {

	@Autowired
	private SkuService skuService;
	/**
	 * 添加方法功能描述
	 * @param request
	 * @param Id
	 * @return ServiceData
	 * @throws Exception
	 **/
	@ApiOperation("仓库级代码默认仓库查询")
	@RequestMapping(value = "/query",method = RequestMethod.GET,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ServiceData query(HttpServletRequest request,SkuQueryDTO record) throws Exception{
		return ServiceDataUtil.getSuccess(skuService.selectSkuById(record));
	}

	@ApiOperation("物料代码盘码管控")
	@RequestMapping(value = "/selectReelId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ServiceData<?> selectReelId(HttpServletRequest request,@RequestBody SkuReelIdInputDTO dto) {
		int int1000=1000;
		int int1=1;
		int int50=50;
		String whseId=dto.getWhseid();
		if(StringUtils.isEmpty(whseId)){
			//"仓库不能为空"
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_WMWHSE_IS_NOT_EMPTY));
		}
		whseId=whseId.toUpperCase();
		String whsePre="WMWHSE";
		if(whseId.indexOf(whsePre)<0){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_WMWHSE_IS_NOT_VAL1));
		}
		String whseLast=whseId.replace(whsePre, "");
		if(!CommonUtils.isInteger(whseLast)){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_WMWHSE_IS_NOT_VAL1));
		}
		int wmseNum=Integer.parseInt(whseLast);
		if(wmseNum<int1||wmseNum>int50){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_WMWHSE_IS_NOT_VAL2));
		}
		if(null==dto.getSkuList()){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_SKU_IS_NOT_EMPTY));
		}
		List<String> skuList=dto.getSkuList();
		if(Tools.isEmpty(skuList)){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_SKU_IS_NOT_EMPTY));
		}

		if(skuList.size()>int1000){
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.SKU_REELID_SKU_IS_NOT_VAL1)+int1000);
		}
		dto.setWhseid(whseId);
		return ServiceDataUtil.getSuccess(skuService.selectReelId(dto));
	}

}
