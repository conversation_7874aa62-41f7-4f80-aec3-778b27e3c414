package com.zte.interfaces.infor;

import com.zte.application.infor.CarrierGpsService;
import com.zte.interfaces.infor.dto.CarrierGpsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 走b2b调用承运商接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gps")
public class CarrierGpsController {
    @Autowired
    private CarrierGpsService carrierGPSService;

    @RequestMapping(value = "/realTimeInteractiveB2B",method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<CarrierGpsDTO> realTimeInteractiveB2B(String waybillNo) throws Exception {
        return carrierGPSService.carrierGpsB2B(waybillNo);
    }

}
