package com.zte.interfaces.infor;

import com.zte.application.infor.DigitQCRedDotService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.infor.dto.DigitQCRedDotManageDTO;
import com.zte.interfaces.infor.vo.DigitQCRedDotListVo;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Description: 数字QC/内控/安全红点管理
 *
 * <AUTHOR>
 * date: 2025/4/2 9:57
 */

@ZTERestController("/digitQCRedDotManage")
@Api(tags = "红点执行", description = "DigitQCRedDotController")
public class DigitQCRedDotController {

    @Autowired
    private DigitQCRedDotService digitQCRedDotService;

    @ApiOperation("拉取IDA巡检录入信息JOB")
    @PostMapping("/pullInspectionInfoJob")
    public void pullInspectionInfoJob(HttpServletRequest request, @RequestBody DigitQCRedDotManageDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        dto.setCreatedBy(xEmpNo);
        digitQCRedDotService.pullInspectionInfoJob(dto);
    }

    @ApiOperation("QC红点信息导入")
    @PostMapping("/digitQCRedDotImport")
    public void digitQCRedDotImport(HttpServletRequest request, @RequestBody List<DigitQCRedDotManageDTO> list) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCRedDotService.digitQCRedDotImport(list, xEmpNo);
    }

    @ApiOperation("QC红点信息查询")
    @PostMapping("/digitQCRedDotQuery")
    public DigitQCRedDotListVo digitQCRedDotQuery(HttpServletRequest request, @RequestBody DigitQCRedDotManageDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        return digitQCRedDotService.digitQCRedDotQuery(dto);
    }


    @ApiOperation("QC红点信息失效")
    @PostMapping("/digitQCRedDotLose")
    public void digitQCRedDotLose(HttpServletRequest request, @RequestBody DigitQCRedDotManageDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCRedDotService.digitQCRedDotLose(dto);
    }

    @ApiOperation("QC红点信息任务完成回调")
    @PostMapping("/digitQCRedDotCallback")
    public void digitQCRedDotCallback(@RequestBody DigitQCRedDotManageDTO dto) throws Exception{
        digitQCRedDotService.digitQCRedDotCallback(dto);
    }


    @ApiOperation("QC红点信息任务信息导出")
    @PostMapping("/digitQCRedDotExport")
    public void digitQCRedDotExport(HttpServletRequest request, @RequestBody DigitQCRedDotManageDTO dto) throws Exception{
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PLEASE_LOGIN_FIRST);
        digitQCRedDotService.digitQCRedDotExport(dto, xEmpNo);
    }

}
