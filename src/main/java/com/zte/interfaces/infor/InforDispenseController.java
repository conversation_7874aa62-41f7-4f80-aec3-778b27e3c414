package com.zte.interfaces.infor;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.infor.PcbSerialDispense;
import com.zte.interfaces.infor.dto.PcbserialDTO;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.ApiOperation;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/dispense")
@Api(tags = "dispense",description = "InforDispenseController")
public class InforDispenseController {
	
	@Autowired
	PcbSerialDispense pcbSerialDispense;
	
	/**
     * 添加方法功能描述
     * @param request
     * @param Id
     * @return ServiceData
     * @throws Exception 
     **/ 
    @ApiOperation("reelid数据插入")
    @RequestMapping(value = "/addPcbSerial",method = RequestMethod.POST,consumes=MediaType.APPLICATION_JSON_UTF8_VALUE,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData addPcbSerial(HttpServletRequest request,@RequestBody List<PcbserialDTO> list) throws Exception {
        return pcbSerialDispense.addPcbSerial(list); 
        
    }

}
