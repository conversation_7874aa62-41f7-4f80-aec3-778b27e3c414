package com.zte.interfaces.infor;

import com.zte.application.infor.DeliveryNoReceivesService;
import com.zte.domain.model.infor.DeliveryDetailDTO;
import com.zte.domain.model.infor.DeliverySignDetailDTO;
import com.zte.domain.model.infor.PcbOverdueInfoDTO;
import com.zte.interfaces.infor.dto.DeliverySignDTO;
import com.zte.interfaces.infor.dto.PcbOverdueDTO;
import com.zte.interfaces.infor.vo.DeliverySignListVo;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 */
@ZTERestController("/deliveryNoReceives")
@Api(tags = "送货无人接收数据接口", description = "DeliveryNoReceivesController")
public class DeliveryNoReceivesController {
    @Autowired
    private DeliveryNoReceivesService deliveryNoReceivesService;

    /* Started by AICoder, pid:865d723c137f453783f49247677d501c */
    @ApiOperation("录入送货单接口")
    @PostMapping("/insertSignInfo")
    public ServiceData<?> insertSignInfo(HttpServletRequest request, @RequestBody DeliverySignDTO dto) {
        DeliverySignDetailDTO detail = deliveryNoReceivesService.insertSignInfo(dto);
        ServiceData ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(detail);
        return ret;
    }

    @ApiOperation("查询送货签到明细")
    @PostMapping("/getDeliverySignInfo")
    public DeliverySignListVo getDeliverySignInfo(HttpServletRequest request, @RequestBody DeliverySignDTO dto) {
        return deliveryNoReceivesService.getDeliverySignInfo(dto);
    }

    @ApiOperation("查询空库位接口")
    @PostMapping("/getEmptyLocByWhseId")
    public ServiceData<?> getEmptyLocByWhseId(HttpServletRequest request, @RequestBody DeliverySignDTO dto) {
        List<DeliverySignDetailDTO> list = deliveryNoReceivesService.getEmptyLocByWhseId(dto);
        ServiceData ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @ApiOperation("质检物料不合格查询接口")
    @PostMapping("/getQualityByWhseId")
    public ServiceData<?> getQualityByWhseId(HttpServletRequest request, @RequestBody DeliverySignDTO dto) {
        List<DeliveryDetailDTO> list = deliveryNoReceivesService.getQualityByWhseId(dto);
        ServiceData ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(list);
        return ret;
    }

    @ApiOperation("PCB超期情况查询接口")
    @PostMapping("/getPcbOverdueInfo")
    public ServiceData<?> getPcbOverdueInfo(HttpServletRequest request, @RequestBody PcbOverdueDTO dto) throws Exception{
        PcbOverdueInfoDTO pcbOverdueInfo = deliveryNoReceivesService.getPcbOverdueInfo(dto);
        ServiceData ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(pcbOverdueInfo);
        return ret;
    }
    /* Ended by AICoder, pid:865d723c137f453783f49247677d501c */
}
