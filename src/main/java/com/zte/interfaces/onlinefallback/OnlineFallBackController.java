package com.zte.interfaces.onlinefallback;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.onlinefallback.OnlineFallBackService;
import com.zte.common.utils.Constant;
import com.zte.interfaces.onlinefallback.dto.ApplyBillStatusDTO;
import com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO;
import com.zte.interfaces.onlinefallback.dto.SupplyResultDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "在线退ISRM对接接口", description = "OnlineFallBackController")
@RequestMapping("/onlineFallBack")
public class OnlineFallBackController {

    @Autowired
    public OnlineFallBackService onlineFallBackService;

    /**
     * [方法描述] 從STEP获取不合格單<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/getNonconformingBill", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
        produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> getNonconformingBill(@RequestBody NonconformingBillInputDTO input) {
        return onlineFallBackService.getNonconformingBill(input);
    }

    /**
     * [方法描述] 5分钟定时捞取已提交退库单调拨单更新出库状态<br>
     * 修改变短
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/scheduleUpFbSrcSoStatus", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> scheduleUpdateFallbackSrcSoStatus(String fallbackNo) {
        return onlineFallBackService.scheduleUpdateFallbackSrcSoStatus(fallbackNo);
    }

    /**
     * [方法描述] 5分钟定时捞取已提交退库单入库完成生成申请单<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/scheduleDealFallbackNo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> scheduleDealFallbackNo(String fallbackNo) {
        return onlineFallBackService.scheduleDealFallbackNo(fallbackNo);
    }

    /**
     * [方法描述] 根据退库单号生成在线退货申请单至DB<br>
     * 
     * <AUTHOR>
     * @return <br>
     */

    @RequestMapping(value = "/produceApplyBillToDB", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> produceApplyBillToDB(String fallbackNo) {
        return onlineFallBackService.produceApplyBillToDB(fallbackNo);
    }

    /**
     * [方法描述] 根据申请单号提交在线退货申请单至ISRM<br>
     * 
     * <AUTHOR>
     * @return <br>
     */

    @RequestMapping(value = "/submitApplyBillToISRM", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> submitApplyBillToISRM(String applybillNo) {
        return onlineFallBackService.submitApplyBillToISRM(applybillNo);
    }
    
    /**
     * [方法描述] 定时重推状态为新建的退货申请单给ISRM<br>
     * 
     * <AUTHOR>
     * @return <br>
     */

    @RequestMapping(value = "/autoSubmitApplyBillToISRM", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> autoSubmitApplyBillToISRM() {
        return onlineFallBackService.autoSubmitApplyBillToISRM();
    }

    /**
     * [方法描述] 接收ISRM申请单状态变更<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/acceptApplyBillStatus", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
        produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> acceptApplyBillStatus(@RequestBody ApplyBillStatusDTO billStatusDTO) {
        if (StringUtils.isBlank(billStatusDTO.getUpdateUserId())) {
            billStatusDTO.setUpdateUserId("ISRM");
        }
        return onlineFallBackService.acceptApplyBillStatus(billStatusDTO);
    }

    /**
     * [方法描述] 批量接收ISRM申请单状态变更<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/acceptApplyBillBatchStatus", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
        produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> acceptApplyBillBatchStatus(@RequestBody List<ApplyBillStatusDTO> list) {
        if (list == null || list.isEmpty()) {
            return ServiceDataUtil.getBusinessError("Apply Bill status is empty !");
        }
        list.forEach(ret -> {
            if (StringUtils.isBlank(ret.getUpdateUserId())) {
                ret.setUpdateUserId("ISRM");
            }
            ServiceData<?> tmp = onlineFallBackService.acceptApplyBillStatus(ret);
            ret.setCode(tmp.getCode().getCode());
            ret.setMsg(tmp.getCode().getMsg());
        });
        return ServiceDataUtil.getSuccess(list);
    }

    @RequestMapping(value = "/submitApllyToInforSo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> submitApllyToInforSo(String applybillNo) {
        return onlineFallBackService.submitApllyToInforSo(applybillNo);
    }

    /**
     * [方法描述] 接收供方处理结果<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/acceptApplyBillSupplyResult", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> acceptApplyBillSupplyResult(@RequestBody SupplyResultDTO supplyResultDTO) {
        return onlineFallBackService.acceptApplyBillSupplyResult(supplyResultDTO);
    }

    @RequestMapping(value = "/acceptApplyBillSupplyBatchResult", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> acceptApplyBillSupplyBatchResult(@RequestBody List<SupplyResultDTO> list) {
        list.forEach(ret -> {
            ServiceData<?> tmp = onlineFallBackService.acceptApplyBillSupplyResult(ret);
            ret.setCode(tmp.getCode().getCode());
            ret.setMsg(tmp.getCode().getMsg());
        });
        return ServiceDataUtil.getSuccess(list);
    }

    /**
     * [方法描述] 5分钟定时捞取待处理库已经出库完成的申请单进行反馈<br>
     * 把url改短
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/scheduleWaitFeedApplyNo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> scheduleDealWaitFeedApplyNo(String applybillNo) {
        return onlineFallBackService.scheduleDealWaitFeedApplyNo(applybillNo);
    }

    /**
     * [方法描述] 出库定时反馈结果至ISRM<br>
     * 
     * <AUTHOR>
     * @return <br>
     */
    @RequestMapping(value = "/feedbackApplyBillSoResult", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData<?> feedbackApplyBillSoResult(String applybillNo) {
        return onlineFallBackService.feedbackApplyBillSoResult(applybillNo);
    }

    @RequestMapping(value = "/failReelIdBarcodeAndFeedbackResult",method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "在线退ReelID/SN失效")
    public ServiceData<?> failReelIdBarcodeAndFeedbackResult(HttpServletRequest request, String applybillNo){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        String xAuthValue = request.getHeader(Constant.X_AUTH_VALUE);
        return onlineFallBackService.failReelIdBarcodeAndFeedbackResult(applybillNo, xEmpNo, xAuthValue);
    }
}
