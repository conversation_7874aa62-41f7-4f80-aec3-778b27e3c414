package com.zte.interfaces.onlinefallback.dto;

public class ApplyBillStatusDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = -412532854451291067L;

    private int origId;

    private String billNo;

    private String billStatus;

    private String modifyTime;

    private String updateUserId;

    private String updateUserName;

    private String code;

    private String msg;

    private String closeRemark;

    private String iwmsStatus;

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public int getOrigId() {
        return origId;
    }

    public void setOrigId(int origId) {
        this.origId = origId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCloseRemark() {
        return closeRemark;
    }
    public void setCloseRemark(String closeRemark) {
        this.closeRemark = closeRemark;
    }

    public String getIwmsStatus() {
        return iwmsStatus;
    }
    public void setIwmsStatus(String iwmsStatus) {
        this.iwmsStatus = iwmsStatus;
    }

}
