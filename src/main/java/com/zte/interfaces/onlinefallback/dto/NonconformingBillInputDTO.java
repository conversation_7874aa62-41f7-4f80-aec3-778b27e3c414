package com.zte.interfaces.onlinefallback.dto;

import io.swagger.annotations.ApiParam;

public class NonconformingBillInputDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = 1808026995171429087L;

    @ApiParam("Y 不合格单号")
    private String nonconformingProductNo;

    private String itemBarcode;

    private String itemNo;

    private String status;

    private String supplierNo;

    public String getNonconformingProductNo() {
        return nonconformingProductNo;
    }

    public void setNonconformingProductNo(String nonconformingProductNo) {
        this.nonconformingProductNo = nonconformingProductNo;
    }

    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

}
