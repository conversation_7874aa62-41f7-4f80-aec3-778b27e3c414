package com.zte.interfaces.onlinefallback.dto;

public class NonconformingInIsrmQtyDTO {
    private String barCode;

    public NonconformingInIsrmQtyDTO() {

    }

    public NonconformingInIsrmQtyDTO(String barCode) {
        this.barCode = barCode;
    }

    public String getBarCode() {

        return barCode;
    }

    public void setBarCode(String barCode) {

        this.barCode = barCode;
    }

}
