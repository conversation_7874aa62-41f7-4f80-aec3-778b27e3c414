package com.zte.interfaces.onlinefallback.dto;

import io.swagger.annotations.ApiParam;

public class NonconformingBillOutputDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = 3666050070735378313L;

    @ApiParam("不合格单号")
    private String nonconformingProductNo;

    private String itemNo;
    private String itemName;

    private String itemBarcode;

    private String supplierNo;

    private String supplierName;
    
    private String remark;

    private double qty;

    private String status;

    private String type;
    @ApiParam("生产批次")
    private String prodplanId;

    @ApiParam("进口批次")
    private String importBatchId;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNonconformingProductNo() {
        return nonconformingProductNo;
    }

    public void setNonconformingProductNo(String nonconformingProductNo) {
        this.nonconformingProductNo = nonconformingProductNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public double getQty() {
        return qty;
    }

    public void setQty(double qty) {
        this.qty = qty;
    }

    public String getProdplanId() {
        return prodplanId;
    }

    public void setProdplanId(String prodplanId) {
        this.prodplanId = prodplanId;
    }

    public String getImportBatchId() {
        return importBatchId;
    }

    public void setImportBatchId(String importBatchId) {
        this.importBatchId = importBatchId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
