package com.zte.interfaces.onlinefallback.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class ReceiptDetailDTO  implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String externreceiptkey;
	
	private String lottable02;
	
	private String toid;
	
	private BigDecimal qtyreceived;
	
	private String receiptLineNumber;

	private String lottable06;

	private String lottable03;

	private String targetLocation;

	public String getExternreceiptkey() {
		return externreceiptkey;
	}

	public void setExternreceiptkey(String externreceiptkey) {
		this.externreceiptkey = externreceiptkey;
	}

	public String getToid() {
		return toid;
	}

	public void setToid(String toid) {
		this.toid = toid;
	}

	public BigDecimal getQtyreceived() {
		return qtyreceived;
	}

	public void setQtyreceived(BigDecimal qtyreceived) {
		this.qtyreceived = qtyreceived;
	}

	public String getLottable02() {
		return lottable02;
	}

	public void setLottable02(String lottable02) {
		this.lottable02 = lottable02;
	}

	public String getReceiptLineNumber() {
		return receiptLineNumber;
	}

	public void setReceiptLineNumber(String receiptLineNumber) {
		this.receiptLineNumber = receiptLineNumber;
	}

	public String getLottable06() {
		return lottable06;
	}

	public void setLottable06(String lottable06) {
		this.lottable06 = lottable06;
	}

	public String getLottable03() {
		return lottable03;
	}

	public void setLottable03(String lottable03) {
		this.lottable03 = lottable03;
	}

	public String getTargetLocation() {
		return targetLocation;
	}

	public void setTargetLocation(String targetLocation) {
		this.targetLocation = targetLocation;
	}
}
