package com.zte.interfaces.onlinefallback.dto;

import io.swagger.annotations.ApiParam;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.Date;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年6月7日 <br>
 * @see com.zte.interfaces.onlinefallback.dto <br>
 */
public class StSysLookupValuesDTO implements java.io.Serializable {

    @ApiParam("数据字典明细编号")
    private BigDecimal lookupCode;

    @ApiParam("数据字典明细值")
    private String lookupMeaning;

    @ApiParam("中文描述")
    private String descriptionChin;

    @ApiParam("英文描述")
    private String descriptionEng;

    @ApiParam("数据字典编号")
    private BigDecimal lookupType;

    @ApiParam("能否编辑")
    private String editableFlag;

    @ApiParam("序号")
    private BigDecimal sortSeq;

    @ApiParam("备注")
    private String remark;

    @ApiParam("创建人")
    private String createBy;

    @ApiParam("创建时间")
    private Date createDate;

    @ApiParam("最后更新人")
    private String lastUpdatedBy;

    @ApiParam("最后更新时间")
    private Date lastUpdatedDate;

    @ApiParam("是否有效")
    private String enabledFlag;

    @ApiParam("组织id")
    private BigDecimal orgId;

    @ApiParam("工厂id")
    private BigDecimal factoryId;

    @ApiParam("实体id")
    private BigDecimal entityId;

    public BigDecimal getLookupCode() {
        return lookupCode;
    }

    public void setLookupCode(BigDecimal lookupCode) {
        this.lookupCode = lookupCode;
    }

    public String getLookupMeaning() {
        return lookupMeaning;
    }

    public void setLookupMeaning(String lookupMeaning) {
        this.lookupMeaning = lookupMeaning;
    }

    public String getDescriptionChin() {
        return descriptionChin;
    }

    public void setDescriptionChin(String descriptionChin) {
        this.descriptionChin = descriptionChin;
    }

    public String getDescriptionEng() {
        return descriptionEng;
    }

    public void setDescriptionEng(String descriptionEng) {
        this.descriptionEng = descriptionEng;
    }

    public BigDecimal getLookupType() {
        return lookupType;
    }

    public void setLookupType(BigDecimal lookupType) {
        this.lookupType = lookupType;
    }

    public String getEditableFlag() {
        return editableFlag;
    }

    public void setEditableFlag(String editableFlag) {
        this.editableFlag = editableFlag;
    }

    public BigDecimal getSortSeq() {
        return sortSeq;
    }

    public void setSortSeq(BigDecimal sortSeq) {
        this.sortSeq = sortSeq;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }
}
