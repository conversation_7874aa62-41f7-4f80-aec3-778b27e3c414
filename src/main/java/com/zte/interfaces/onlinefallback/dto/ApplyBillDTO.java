package com.zte.interfaces.onlinefallback.dto;

import io.swagger.annotations.ApiParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ApplyBillDTO implements java.io.Serializable {

    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = 6255714692545460295L;

    @ApiParam("仓退申请单ID")
    private int origId;

    @ApiParam("仓退申请单号")
    private String billNo;

    @ApiParam("申请单类型")
    private String returnType;

    @ApiParam("供应商编码")
    private String supCode;

    @ApiParam("供应商名称")
    private String supName;

    @ApiParam("到货单号")
    private String deliNo;

    @ApiParam("不合格单号")
    private String nonconformityNo;

    @ApiParam("退货原因")
    private String returnReason;

    @ApiParam("申请人")
    private String createUser;

    @ApiParam("取货地点")
    private String takeGoodsSite;

    @ApiParam("取货联系人")
    private String takeGoodsUser;

    @ApiParam("取货联系电话")
    private String takeGoodsPhone;

    @ApiParam("行信息")
    private List<ApplyBillItemDTO> items = new ArrayList<ApplyBillItemDTO>();

    @ApiParam("更新时间 yyyy-MM-dd HH:mm:ss")
    private String createTime;

    private String reelid;

    private BigDecimal reelidFlag;

    @ApiParam("货主分类")
    private String cargoOwner;

    private String targetLocation;

    public ApplyBillDTO(){}

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getOrigId() {
        return origId;
    }

    public void setOrigId(int origId) {
        this.origId = origId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getReturnType() {
        return returnType;
    }

    public void setReturnType(String returnType) {
        this.returnType = returnType;
    }

    public String getSupCode() {
        return supCode;
    }

    public void setSupCode(String supCode) {
        this.supCode = supCode;
    }

    public String getSupName() {
        return supName;
    }

    public void setSupName(String supName) {
        this.supName = supName;
    }

    public String getDeliNo() {
        return deliNo;
    }

    public void setDeliNo(String deliNo) {
        this.deliNo = deliNo;
    }

    public String getNonconformityNo() {
        return nonconformityNo;
    }

    public void setNonconformityNo(String nonconformityNo) {
        this.nonconformityNo = nonconformityNo;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getTakeGoodsSite() {
        return takeGoodsSite;
    }

    public void setTakeGoodsSite(String takeGoodsSite) {
        this.takeGoodsSite = takeGoodsSite;
    }

    public String getTakeGoodsUser() {
        return takeGoodsUser;
    }

    public void setTakeGoodsUser(String takeGoodsUser) {
        this.takeGoodsUser = takeGoodsUser;
    }

    public String getTakeGoodsPhone() {
        return takeGoodsPhone;
    }

    public void setTakeGoodsPhone(String takeGoodsPhone) {
        this.takeGoodsPhone = takeGoodsPhone;
    }

    public List<ApplyBillItemDTO> getItems() {
        return items;
    }

    public void setItems(List<ApplyBillItemDTO> items) {
        this.items = items;
    }

    public String getReelid() {
        return reelid;
    }

    public void setReelid(String reelid) {
        this.reelid = reelid;
    }

    public BigDecimal getReelidFlag() {
        return reelidFlag;
    }

    public void setReelidFlag(BigDecimal reelidFlag) {
        this.reelidFlag = reelidFlag;
    }

    public String getCargoOwner() {
        return cargoOwner;
    }

    public void setCargoOwner(String cargoOwner) {
        this.cargoOwner = cargoOwner;
    }

    public String getTargetLocation() {
        return targetLocation;
    }

    public void setTargetLocation(String targetLocation) {
        this.targetLocation = targetLocation;
    }
}
