package com.zte.interfaces.onlinefallback.dto;

/**
 * [描述] <br>
 * 
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年6月4日 <br>
 * @see com.zte.interfaces.onlinefallback.dto <br>
 */
public class SupplyResultDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = 601772102515663383L;

    private int origId;

    private String billNo;

    private String returnComments;

    private String returnModel;

    private String handlingOpinions;

    private String scrappingReason;

    private String remark;

    private String pickStyle;

    private String receiver;

    private String receiverContactNo;

    private String receiverAddress;

    private String carrier;

    private String returnAddress;

    private String pickAddress;

    private String modifyTime;

    private String code;

    private String msg;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getOrigId() {
        return origId;
    }

    public void setOrigId(int origId) {
        this.origId = origId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getReturnComments() {
        return returnComments;
    }

    public void setReturnComments(String returnComments) {
        this.returnComments = returnComments;
    }

    public String getReturnModel() {
        return returnModel;
    }

    public void setReturnModel(String returnModel) {
        this.returnModel = returnModel;
    }

    public String getHandlingOpinions() {
        return handlingOpinions;
    }

    public void setHandlingOpinions(String handlingOpinions) {
        this.handlingOpinions = handlingOpinions;
    }

    public String getScrappingReason() {
        return scrappingReason;
    }

    public void setScrappingReason(String scrappingReason) {
        this.scrappingReason = scrappingReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPickStyle() {
        return pickStyle;
    }

    public void setPickStyle(String pickStyle) {
        this.pickStyle = pickStyle;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getReceiverContactNo() {
        return receiverContactNo;
    }

    public void setReceiverContactNo(String receiverContactNo) {
        this.receiverContactNo = receiverContactNo;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getReturnAddress() {
        return returnAddress;
    }

    public void setReturnAddress(String returnAddress) {
        this.returnAddress = returnAddress;
    }

    public String getPickAddress() {
        return pickAddress;
    }

    public void setPickAddress(String pickAddress) {
        this.pickAddress = pickAddress;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

}
