package com.zte.interfaces.onlinefallback.dto;

import io.swagger.annotations.ApiParam;

public class ApplyBillItemDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = -2117383560573723214L;

    @ApiParam("取货行号")
    private int rowNo;

    @ApiParam("物料代码")
    private String itemNo;

    @ApiParam("退货数量")
    private double returnQty;

    @ApiParam("22条码")
    private String barCode;

    @ApiParam("到货单行号")
    private String deliRowNo;

    @ApiParam("生产批次")
    private String produceBatchNo;

    @ApiParam("进口批次")
    private String importBatchNo;

    @ApiParam("出库单单号")
    private String outNo;

    @ApiParam("出库单行号")
    private int outRowNo;

    public int getRowNo() {
        return rowNo;
    }

    public void setRowNo(int rowNo) {
        this.rowNo = rowNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public double getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(double returnQty) {
        this.returnQty = returnQty;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getDeliRowNo() {
        return deliRowNo;
    }

    public void setDeliRowNo(String deliRowNo) {
        this.deliRowNo = deliRowNo;
    }

    public String getProduceBatchNo() {
        return produceBatchNo;
    }

    public void setProduceBatchNo(String produceBatchNo) {
        this.produceBatchNo = produceBatchNo;
    }

    public String getImportBatchNo() {
        return importBatchNo;
    }

    public void setImportBatchNo(String importBatchNo) {
        this.importBatchNo = importBatchNo;
    }

    public String getOutNo() {
        return outNo;
    }

    public void setOutNo(String outNo) {
        this.outNo = outNo;
    }

    public int getOutRowNo() {
        return outRowNo;
    }

    public void setOutRowNo(int outRowNo) {
        this.outRowNo = outRowNo;
    }

}
