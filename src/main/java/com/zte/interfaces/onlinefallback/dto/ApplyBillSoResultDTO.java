package com.zte.interfaces.onlinefallback.dto;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApplyBillSoResultDTO implements java.io.Serializable {
    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = -1088487654426394015L;

    private int origId;

    private String billNo;

    private String returnType;

    private String modifyTime;

    private String fallbackNo;

    private String handlingOpinions;

    private List<ApplyBillSoResultItemDTO> items = new ArrayList<ApplyBillSoResultItemDTO>();

    public ApplyBillSoResultDTO() {
        this.modifyTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public int getOrigId() {
        return origId;
    }

    public void setOrigId(int origId) {
        this.origId = origId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getReturnType() {
        return returnType;
    }

    public void setReturnType(String returnType) {
        this.returnType = returnType;
    }

    public List<ApplyBillSoResultItemDTO> getItems() {
        return items;
    }

    public void setItems(List<ApplyBillSoResultItemDTO> items) {
        this.items = items;
    }

    public String getFallbackNo() {
        return fallbackNo;
    }

    public void setFallbackNo(String fallbackNo) {
        this.fallbackNo = fallbackNo;
    }

    public String getHandlingOpinions() {
        return handlingOpinions;
    }

    public void setHandlingOpinions(String handlingOpinions) {
        this.handlingOpinions = handlingOpinions;
    }
}
