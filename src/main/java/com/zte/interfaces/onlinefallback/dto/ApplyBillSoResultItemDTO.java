package com.zte.interfaces.onlinefallback.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiParam;

public class ApplyBillSoResultItemDTO implements java.io.Serializable {

    /**
     * serialVersionUID <br>
     */
    private static final long serialVersionUID = 3464506713655531358L;

    private int rowNo;

    private String barCode;

    @ApiParam("箱码LPN")
    private String lpn;

    @ApiParam("原箱码 无法提供，不传值")
    private String exParkNumber;

    @ApiParam("实际出库数量")
    private double realOutNumber;

    private String length;

    private String width;

    private String high;

    private String grossWeight;

    @ApiParam("Reel/SN 序列号SN无法提供，不传值")
    private List<ApplyBillSoResultItemReelSnDTO> sns = new ArrayList<ApplyBillSoResultItemReelSnDTO>();

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHigh() {
        return high;
    }

    public void setHigh(String high) {
        this.high = high;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public int getRowNo() {
        return rowNo;
    }

    public void setRowNo(int rowNo) {
        this.rowNo = rowNo;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getLpn() {
        return lpn;
    }

    public void setLpn(String lpn) {
        this.lpn = lpn;
    }

    public String getExParkNumber() {
        return exParkNumber;
    }

    public void setExParkNumber(String exParkNumber) {
        this.exParkNumber = exParkNumber;
    }

    public double getRealOutNumber() {
        return realOutNumber;
    }

    public void setRealOutNumber(double realOutNumber) {
        this.realOutNumber = realOutNumber;
    }

    public List<ApplyBillSoResultItemReelSnDTO> getSns() {
        return sns;
    }

    public void setSns(List<ApplyBillSoResultItemReelSnDTO> sns) {
        this.sns = sns;
    }

}
