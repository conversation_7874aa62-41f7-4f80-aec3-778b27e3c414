package com.zte.interfaces.onlinefallback.dto;

/**
 * [描述] <br>
 * 
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年6月30日 <br>
 * @see com.zte.interfaces.onlinefallback.dto <br>
 */
public class FallbackNoInfo {
    private String fallbackNo;

    private String fallbackType;

    private String fallStatus;

    private String lastUpdatedBy;

    private String lastUpdatedDate;

    private String sourcelocation;

    private String targetLocation;

    private String index;

    public FallbackNoInfo() {
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getFallbackType() {
        return fallbackType;
    }

    public void setFallbackType(String fallbackType) {
        this.fallbackType = fallbackType;
    }

    public String getFallbackNo() {
        return fallbackNo;
    }

    public void setFallbackNo(String fallbackNo) {
        this.fallbackNo = fallbackNo;
    }

    public String getFallStatus() {
        return fallStatus;
    }

    public void setFallStatus(String fallStatus) {
        this.fallStatus = fallStatus;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(String lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getSourcelocation() {
        return sourcelocation;
    }

    public void setSourcelocation(String sourcelocation) {
        this.sourcelocation = sourcelocation;
    }

    public String getTargetLocation() {
        return targetLocation;
    }

    public void setTargetLocation(String targetLocation) {
        this.targetLocation = targetLocation;
    }

}
