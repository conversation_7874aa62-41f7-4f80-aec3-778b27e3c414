package com.zte.interfaces.transfer;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.material.OrgTransferLogRepository;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.kafka.consumer.KafkaConsumerService;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.orm.core.IOrmTemplate;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.INT_500;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_2;

/**
 * 手动处理
 */
@ZTERestController("/manual/transfer")
public class ManualTransferController {
    @Autowired
    private IOrmTemplate ormTemplate;
    @Autowired
    private OrgTransferLogRepository logRepository;
    @Autowired
    private KafkaConsumerService kafkaConsumerService;
    @Autowired
    @Qualifier("ioThreadPool")
    private Executor ioThreadPool;
    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;
    @Autowired
    private OnlineFallBackApplyBillRepository applyBillRepository;
    @GetMapping("/maualTransfer")
    public void maualTransfer(String transferId){
        kafkaConsumerService.dealTransfer(transferId);
    }

    @GetMapping("/autoTransferDeal")
    public void autoTransferDeal(){
        List<StSysLookupValuesDTO> values = applyBillRepository.getSysLookupValues(TASK_NUM_CODE);
        int tasknum = Tools.isEmpty(values)?INT_500:Integer.parseInt(values.get(0).getLookupMeaning());
        if(tasknum==INT_0){
            return;
        }
        List<String> transferIdList = logRepository.getTransferIdList(DEALING);
        if(Tools.isEmpty(transferIdList)){
            return;
        }

        List<List<String>> splitIdsList = CommonUtils.splitList(transferIdList,tasknum);
        //获取处理器数目
        int cpuNum = Runtime.getRuntime().availableProcessors();
        //获取核心线程数
        int corePoolNumber =INT_2 * cpuNum;

        ThreadPoolExecutor ioPoolExecutor = new ThreadPoolExecutor(corePoolNumber, corePoolNumber, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactory() {
            private AtomicInteger poolNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r,POOL + poolNumber.getAndIncrement() + THREAD);
            }
        },new ThreadPoolExecutor.CallerRunsPolicy());
        Executor ttlExecutor = TtlExecutors.getTtlExecutor(ioPoolExecutor);
        thplExecutor.execute(()->splitIdsList.get(INT_0).forEach(t -> ttlExecutor.execute(()->kafkaConsumerService.dealTransfer(t))));
    }
}
