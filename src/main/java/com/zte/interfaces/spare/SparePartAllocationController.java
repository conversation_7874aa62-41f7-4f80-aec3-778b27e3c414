package com.zte.interfaces.spare;

import com.zte.application.parts.SparePartAllocationService;
import com.zte.interfaces.dto.EmSmtStencil;
import com.zte.interfaces.dto.FixtureInfoDetailDTO;
import com.zte.interfaces.dto.SolderInfoDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationItemDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023-06-05 8:49
 */
@RestController
@RequestMapping("SparePartAllocationCrl")
public class SparePartAllocationController {
    @Autowired
    private SparePartAllocationService sparePartAllocationService;

    @ApiOperation("查询辅料调拨明细根据单据号")
    @PostMapping(value = "queryPartHeadAndDetails")
    public ServiceData<?> queryPartHeadAndDetails(@RequestBody SparePartAllocationHeadDTO headDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.querySpareHeadAndDetails(headDTO));
    }


    @ApiOperation("保存辅料单据信息")
    @RedisDistributedLockAnnotation(lockFailMsgZh = "当前单据正在操作请稍后再试", redisPrefix = "insertSpareHeadAndDetails", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "headDTO", propertiesString = "billNo")}, lockFailMsgEn = "The bill No is being operated. Please try again later.")
    @PostMapping(value = "savePartHeadAndDetails")
    public ServiceData<?> savePartHeadAndDetails(@Validated @RequestBody SparePartAllocationHeadDTO headDTO) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        headDTO.setLastUpdatedBy(pair.getSecond());
        headDTO.setFactoryId(pair.getFirst());
        sparePartAllocationService.savePartHeadAndDetails(headDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("更新辅料调拨信息")
    @PostMapping(value = "updateSpareHeadAndDetails")
    @RedisDistributedLockAnnotation(lockFailMsgZh = "当前单据正在操作请稍后再试", redisPrefix = "insertSpareHeadAndDetails", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "headDTO", propertiesString = "billNo")}, lockFailMsgEn = "The bill No is being operated. Please try again later.")
    public ServiceData<?> updateSpareHeadAndDetails(@Validated @RequestBody SparePartAllocationHeadDTO headDTO) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        headDTO.setLastUpdatedBy(pair.getSecond());
        headDTO.setFactoryId(pair.getFirst());
        sparePartAllocationService.updateSpareHeadAndDetails(headDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("生产配送回调更新单据状态触发审批")
    @PutMapping("updateBillHeadStatus")
    public ServiceData<?> updateBillHeadStatus(@RequestBody SparePartAllocationHeadDTO headDTO) throws Exception {
        sparePartAllocationService.updateBillHeadStatus(headDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("下载导入模板")
    @GetMapping(value = "downLoadExcelTemplate")
    public void downLoadExcelTemplate(HttpServletResponse response) throws Exception {
        sparePartAllocationService.downLoadExcelTemplate(response);
    }


    @ApiOperation("导入解析数据")
    @PostMapping(value = "uploadExcel")
    public ServiceData<?> uploadExcel(MultipartFile file) throws Exception {
        return ServiceDataBuilderUtil.success(sparePartAllocationService.uploadExcel(file));
    }

    @ApiOperation("获取对应工厂审批完成及调拨中的单据")
    @GetMapping(value = "getSpareBillNoList")
    public ServiceData<?> getSpareBillNoList(SparePartAllocationHeadDTO headDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.getSpareBillNoList(headDTO));
    }

    @ApiOperation("获取对应状态单据号列表")
    @PostMapping(value = "getBillNosByStatus")
    public ServiceData<?> getBillNosByStatus(HttpServletRequest request, @RequestBody SparePartAllocationHeadDTO headDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(sparePartAllocationService.getBillNos(headDTO));
    }

    @ApiOperation("根据单据号获取详细信息")
    @PostMapping(value = "getInfoByBillNo")
    public ServiceData<?> getInfoByBillNo(HttpServletRequest request, @RequestBody SparePartAllocationHeadDTO headDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(sparePartAllocationService.getInfoByBillNo(headDTO));
    }

    @ApiOperation("确认编码是否在调拨单中")
    @PostMapping(value = "checkItemExist")
    public ServiceData<?> checkItemExist(HttpServletRequest request, @RequestBody SparePartAllocationHeadDTO headDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(sparePartAllocationService.checkItemExist(headDTO));
    }

    @ApiOperation("获取调拨单详情")
    @GetMapping(value = "getSpareOutBoundList")
    public ServiceData<?> getSpareOutBoundList(SparePartAllocationHeadDTO headDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.getSpareOutBoundList(headDTO));
    }

    @ApiOperation("获取调拨单详情")
    @PostMapping(value = "newSparePartItemDetail")
    public ServiceData<?> newSparePartItemDetail(@RequestBody SparePartAllocationItemDetailDTO detailDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.newSparePartItemDetail(detailDTO));
    }

    @ApiOperation("辅料调拨入库--关闭单据")
    @PostMapping(value = "updateHeadStatus")
    public ServiceData<?> updateHeadStatus(HttpServletRequest request, @RequestBody SparePartAllocationHeadDTO headDTO) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        headDTO.setLastUpdatedBy(pair.getSecond());
        sparePartAllocationService.updateHeadStatus(headDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("辅料调拨入库--更新单据状态")
    @PostMapping(value = "updateDetail")
    public ServiceData<?> updateDetail(HttpServletRequest request, @RequestBody SparePartAllocationHeadDTO headDTO) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        headDTO.setLastUpdatedBy(pair.getSecond());
        sparePartAllocationService.updateDetail(headDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("查询界面-查询辅料调拨单信息")
    @PostMapping(value = "querySpareHeadInfo")
    public ServiceData<?> querySpareHeadInfo(@RequestBody SparePartAllocationQueryDTO queryDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.querySpareHeadInfo(queryDTO));
    }

    @ApiOperation("查询界面-查询辅料调拨单详情信息")
    @PostMapping(value = "queryBillDetailByBillNo")
    public ServiceData<?> queryBillDetailByBillNo(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.queryBillDetailByBillNo(queryDTO));
    }

    @ApiOperation("查询界面-查询辅料调拨详情信息")
    @PostMapping(value = "queryItemDetailByBillNo")
    public ServiceData<?> queryItemDetailByBillNo(@RequestBody SparePartAllocationQueryDTO queryDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.queryItemDetailByBillNo(queryDTO));
    }

    @ApiOperation("关闭操作")
    @PostMapping(value = "closeSparePartAllocation")
    public ServiceData<?> closeSparePartAllocation(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        String empNo = pair.getSecond();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.closeSparePartAllocation(queryDTO.getBillNo(), empNo));
    }

    @ApiOperation("删除操作")
    @PostMapping(value = "deleteSparePartAllocation")
    public ServiceData<?> deleteSparePartAllocation(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        String empNo = pair.getSecond();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.deleteSparePartAllocation(queryDTO, empNo));
    }


    @ApiOperation("回退操作")
    @PostMapping(value = "rollbackSparePartAllocation")
    public ServiceData<?> rollbackSparePartAllocation(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        String empNo = pair.getSecond();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.rollbackSparePartAllocation(queryDTO, empNo));
    }

    @ApiOperation("查询界面-获取调拨单审批详情")
    @PostMapping(value = "getApprovalDetailByBillNo")
    public ServiceData<?> getApprovalDetailByBillNo(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.getApprovalDetailByBillNo(queryDTO.getBillNo()));
    }

    @ApiOperation("查询界面-获取导出数据数量")
    @PostMapping(value = "getCountExportTotal")
    public ServiceData<?> getCountExportTotal(@RequestBody SparePartAllocationQueryDTO queryDTO) {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.countExportTotal(queryDTO));
    }

    @ApiOperation("备件调拨入库-删除调出工厂锡膏基础信息")
    @PostMapping(value = "/deleteSolderOriginalInfo")
    public ServiceData deleteSolderOriginalInfo(HttpServletRequest request, @RequestBody SolderInfoDTO solderInfoDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        sparePartAllocationService.deleteSolderOriginalInfo(solderInfoDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("备件调拨入库-删除调出工厂工装基础信息")
    @PostMapping(value = "/deleteFixtureOriginalInfo")
    public ServiceData deleteFixtureOriginalInfo(HttpServletRequest request, @RequestBody FixtureInfoDetailDTO fixtureInfoDetailDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        sparePartAllocationService.deleteFixtureOriginalInfo(fixtureInfoDetailDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("备件调拨入库-删除调出工厂钢网基础信息")
    @PostMapping(value = "/deleteStencilsOriginalInfo")
    public ServiceData deleteStencilsOriginalInfo(HttpServletRequest request, @RequestBody EmSmtStencil emSmtStencil) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        sparePartAllocationService.deleteStencilsOriginalInfo(emSmtStencil);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("备件调拨入库-获取调出工厂钢网与单板对应关系信息")
    @PostMapping(value = "/queryRelPcbInfo")
    public ServiceData queryRelPcbInfo(HttpServletRequest request, @RequestBody EmSmtStencil emSmtStencil) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(sparePartAllocationService.queryRelPcbInfo(emSmtStencil));
    }

    @ApiOperation("查询辅料调拨单信息")
    @PostMapping(value = "querySpareInfoByPartCode")
    public ServiceData querySpareInfoByPartCode(HttpServletRequest request,  @RequestBody SparePartAllocationQueryDTO dto) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        return ServiceDataBuilderUtil.success(sparePartAllocationService.querySpareInfoByPartCode(dto.getPartCode()));
    }
}
