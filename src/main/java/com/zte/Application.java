package com.zte;

import com.zte.aiop.dtems.annotation.EnableDTEMS;
import com.zte.iss.approval.sdk.config.ApprovalSdkAutoConfiguration;
import com.zte.itp.msa.netflixclient.EnableHttpClient;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;

import com.alibaba.fastjson.parser.ParserConfig;
import com.zte.itp.msa.mybatis.MsaMybatisAutoConfiguration;
import com.zte.resourcewarehouse.common.springbootframe.datasource.MyMsaDatasourceConfig;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2019年11月29日 <br>
 * @see com.zte <br>
 */
@MapperScan("com.zte.domain.model")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,MsaMybatisAutoConfiguration.class,ApprovalSdkAutoConfiguration.class})
@Import(MyMsaDatasourceConfig.class)
@EnableAsync
@EnableTransactionManagement
@EnableHttpClient
@EnableDTEMS
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
    }
}