package com.zte;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.schema.Example;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Configuration
@EnableOpenApi
public class Swagger3 {
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.zte.interfaces"))
                .paths(PathSelectors.any())
                .build().globalRequestParameters(globalRequestParameters());
    }
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("仓储-原材料外部服务接口")
                .description("仓储-原材料外部服务接口")
                .version("1.0")
                .build();
    }
    private List<RequestParameter> globalRequestParameters() {
        // 所有接口默认请求头。框架默认都是非必须，应根据实际情况设置每个头的 required 属性来指明是否必须项。
        RequestParameter authValue = this.headerParameter("X-Auth-Value", "token值", false, null);
        RequestParameter empNo = this.headerParameter("X-Emp-No", "员工短工号", false, null);
        RequestParameter langId = this.headerParameter("X-Lang-Id", "语言标准编码", true, "zh_CN");
        RequestParameter orgId = this.headerParameter("X-Org-Id", "组织ID", false, null);
        RequestParameter tenantId = this.headerParameter("X-Tenant-Id", "租户编号", false, null);
        RequestParameter serviceName = this.headerParameter("X-Origin-ServiceName", "调用方微服务名", false, null);
        RequestParameter itpValue = this.headerParameter("X-Itp-Value", "客户端设备的相关信息", false, null);
        return Arrays.asList(authValue, empNo, langId, orgId, tenantId, serviceName, itpValue);
    }

    private RequestParameter headerParameter(String name, String description, boolean required, Object defaultValue) {
        RequestParameterBuilder builder = new RequestParameterBuilder()
                .name(name)
                .description(description)
                .in(ParameterType.HEADER)
                .required(required);
        if (Objects.nonNull(defaultValue)) {
            Example value = new Example(UUID.randomUUID().toString(), defaultValue.toString(), null, defaultValue, null, null);
            builder.examples(Arrays.asList(value)).example(value);
        }
        return builder.build();
    }
}