package com.zte.common.utils;

import java.math.BigInteger;

import static java.math.BigInteger.valueOf;

/**
 * 存放数字常量
 *
 * <AUTHOR>
 */
public class NumConstant {

    public static final String STRING_ZERO = "0";
    public static final String STRING_ONE = "1";
    public static final String STRING_TWO = "2";
    public static final String STRING_THREE = "3";
    public static final String STRING_FOUR = "4";
    public static final String STRING_FIVE = "5";
    public static final String STRING_SIX = "6";
    public static final String STRING_SEVEN = "7";
    public static final String STRING_EIGHT = "8";
    public static final String STRING_NINE = "9";
    public static final String STRING_TEN = "10";
    public static final String STRING_99 = "99";
    public static final String STR_1004041001 = "1004041001";

    public static final String STRING_A = "A";


    public static final String STRING_9999 = "9999";
    public static final String TICKET_CLOSED= "50";

    public static final int NUM_MINUS_ONE = -1;
    public static final int NUM_ZERO = 0;
    public static final int NUM_ONE = 1;
    public static final int NUM_TWO = 2;
    public static final int NUM_THREE = 3;
    public static final int NUM_FOUR = 4;
    public static final int NUM_FIVE = 5;
    public static final int NUM_SIX = 6;
    public static final int NUM_SEVEN = 7;
    public static final int NUM_EIGHT = 8;
    public static final int NUM_NINE = 9;
    public static final int NUM_TEN = 10;
    public static final int NUM_ELEVEN = 11;
    public static final int NUM_TWELVE = 12;
    public static final int NUM_FIFTEEN = 15;
    public static final int NUM_SIXTEEN = 16;
    public static final int NUM_TWENTY = 20;

    public static final int NUM_24 = 24;
    public static final int NUM_THIRTEEN = 13;

    public static final int NUM_999 = 999;
    public static final int NUM_48 = 48;
    public static final int NUM_60 = 60;
    public static final int NUM_64 = 64;
    public static final int NUM_100 = 100;
    public static final int NUM_200 = 200;
    public static final int NUM_1000 = 1000;
    public static final int NUM_1500 = 1500;
    public static final int NUM_1024 = 1024;
    public static final int NUM_1200 = 1200;
    public static final int NUM_2000 = 2000;
    public static final int NUM_3000 = 3000;
    public static final int NUM_4000 = 4000;
    public static final int NUM_3600 = 3600;
    public static final int NUM_5000 = 5000;
    public static final int NUM_100000 = 100000;
    public static final int NUM_500000 = 500000;

    public static final Long LONG_300000 = 300000L;

    public static final Long LONG_1000000000 = 1000000000L;
    public static final int NUM_50000 = 50000;
    public static final int NUM_10000 = 10000;
    public static final int NUM_500 = 500;
    public static final int NUM_600 = 600;
    public static final int NUM_1350 = 1350;
    public static final int NUM_180 = 180;
    public static final int NUM_150 = 150;
    public static final int NUM_80 = 80;
    public static final int NUM_120 = 120;
    public static final int NUM_230 = 230;
    public static final int NUM_HUNDRED = 100;
    public static final int NUM_THIRTY = 30;
    public static final int NUM_FORTY = 40;
    public static final int NUM_FIFTY = 50;
    public static final int NUM_TWENTY_FIVE = 25;
    public static final int NUM_MINUS_TWNTY = -20;

    public static final int NUM_MINUS_TEN = -60*2;

    public static final long MILLIS_NUM_OF_DAY = 1000*24*60*60;
    public static final int NUM_SECOND_WEEK = 60 * 60 * 24 * 7;
    public static final int NUM_SECOND_DAY = 60 * 60 * 24;
    public static final int NUM_SECOND_TWELVE_HOURS = 60 * 60 * 12;

    public static final long LONG_THIRTY_ONE = 31;

    public static final long LONG_SEVENTY_ONE = 71;
    public static final long LONG_SEVENTY_TWO = 72;
    public static final long LONG_SEVENTY_THREE = 73;
    public static final long LONG_SEVENTY_FOUR = 74;
    public static final long LONG_SEVENTY_FIVE = 75;
    public static final long LONG_SEVENTY_SIX = 76;
    public static final long LONG_SEVENTY_SEVEN = 77;
    public static final long LONG_SEVENTY_EIGHT = 78;
    public static final long LONG_SEVENTY_NINE = 79;
    public static final long LONG_EIGHTY = 80;
    public static final long LONG_EIGHTY_ONE = 81;
    public static final long LONG_EIGHTY_TWO = 82;
    public static final long LONG_EIGHTY_THREE = 83;
    public static final long LONG_EIGHTY_FOUR = 84;
    public static final long LONG_EIGHTY_FIVE = 85;
    public static final long LONG_EIGHTY_SIX = 86;
    public static final long LONG_EIGHTY_SEVEN = 87;
    public static final long LONG_500 = 500;

    public static final long LONG_5000 = 5000;

    public static final long LONG_ZERO = 0L;

    public static final double DOUBLE_EIGHTY = 80.0;
    public static final int LV_6103001 = 6103001;
	public static final BigInteger TEN_MILLION = valueOf(10000000);

    public static final String STRING_ZERO_ZERO_ZERO_ONE ="0001";
	public static final BigInteger MILLION = valueOf(1000000);

    public static final int NUM_360 = 360;
    public static final int NUM_5400= 5400;
}
