package com.zte.common.utils;

public interface KafkaConstant {

    String SYSTEM = "zte-mes-manufactureshare-systemsys";

    String BASICSETTING = "zte-mes-manufactureshare-basicsettingsys";

    String CENTERFACTORY = "zte-mes-manufactureshare-centerfactory";

    String CRAFTTECH = "zte-mes-manufactureshare-crafttechsys";

    String PLANSCHEDULE = "zte-mes-manufactureshare-planschedulesys";

    String PRODUCTIONMGMT = "zte-mes-manufactureshare-productionmgmtsys";
    String PRODUCTIONDELIVERYSYS = "zte-mes-manufactureshare-productiondeliverysys";

    String EQPMGMT = "zte-mes-manufactureshare-eqpmgmtsys";

    String DATAWB = "zte-mes-manufactureshare-datawbsys";

    String PLANSCHEDULESYS = "zte-mes-manufactureshare-planschedulesys";

    String LOG = "zte-mes-manufactureshare-log";

    String CUSTOMER_ITEM_MSG_TOPIC = "zte-scm-basic";

    String CUSTOMER_ITEM_MSG_KEY = "customerItemInfoKey";

    String TASK_CONFIRMATION_RESULT_KEY = "taskConfirmationResultKey";

    String ALARM_LOG = "alarmLog";
    String SERVICE_LOG = "serviceLog";

    String FLOWCODE = "flowCode";

    String CRAFT_INFO_SYNCHRONIZATION = "craftInfoSynchronization";

    String CRAFT_INFO_ABOLISH = "craftInfoAbolish";

    String BUSINESS_ID = "businessId";

    String APPROVER = "approver";

    String STATUS = "status";

    String RESULT = "result";

    String OPINION = "opinion";

    String APPROVAL_RESULT = "approvalResult";

    String HANDLER = "handler";

    String NODE_NAME = "nodeName";

    String APPROVAL_NODE_NAME = "approveNodeName";

    String EXTENDED_CODE = "extendedCode";

    String TASK_RECEIVER = "taskReceiver";

    //ACTIVE为进行中，COMPLETED为已完成)
    String NODE_STATUS = "nodeStatus";
    String COMPLETE = "COMPLETE";
    String SNAPSHOT_LOG = "snapshotLog";

    /**
     * 审批任务完成  key: '{appCode}'-taskCompleted
     * 审批任务转交  key: '{appCode}'-taskReassign
     * 流程实例撤销 key: '{appCode}'-approvalRevoke
     * 审批节点、会签节点完成消息通知  key: '{appCode}-nodeCompleted'
     */
    interface NodeStatus {
        String TASK_COMPLETED = "taskCompleted";
        String TASK_REASSIGN = "taskReassign";
        String APPROVAL_REVOKE = "approvalRevoke";
        String NODE_COMPLETED = "nodeCompleted";
    }

    String PDM_TECHNICAL_CHANGE_INFO_DEAL = "PdmTechnicalChangeInfoDeal";

    /**
     * aps 批次信息
     */
    interface ApsKafkaConstant {
        String PRODUCER_TOPIC = "zte-aps-scpa";
        String PRODUCER_TOPIC_KEY = "aps2ImesProdplan";
        String APS2_IMES_MODIFY_PROD_PLAN = "aps2ImesModifyProdplan";
        // 新增批次回写APS Key
        String PROD_PLAN_ID_PRODUCER = "prodPlanIdProducer";
        // 修改批次回写aps key
        String PROD_PLAN_MODIFY_PRODUCER = "prodPlanModifyProducer";

        //衍生码
        String APS2_IMES_PROD_PLAN_MODIFY = "aps2ImesProdplanModify";
        String ENTITY_BIND_KEY = "zte-aps-scpa_entity_bind";
        String ENTITY_MODIFY_KEY = "iaps_to_imes_entity_modify";
    }

    String SYNC_LMS_BAR_SUBMIT_BILL_STATUS = "SyncLmsBarsubmitBillStatus";

    String SYNC_LMS_BOARD_ONLINE_SN_IMU = "SyncLmsBoardOnlineSnImu";
    String SENT_BACK_BOARD_TO_BYTE_DANCE = "sentBackBoardToByteDance";
    /**
     * 辅料调拨key
     */
    String SPARE_PART_ALLOCATION = "sparePartAllocation";

    String COMMON_TRADE_MESSAGE = "common_trade_message";

    /**
     * 箱包变化topic
     */
    String CARTON_NO_CHANGE_TOPIC = "zte-iss-barcodecenter-barcode";
    /**
     * 箱包变化key
     */
    String CARTON_NO_CHANGE_KEY = "containerBarcodeChange";

}
