/**
 * 吉一民 6055000034
 * 3DES加密、解密算法
 * 2018.03.25
 */
package com.zte.common.utils;

import java.nio.charset.Charset;
import java.security.SecureRandom;
import java.util.Random;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.SecretKeySpec;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

@SuppressWarnings({ "restriction" })
public class TripleDESUtils {
    /**
     * 3DES秘钥生成
     * @param length
     * @return
     */


    public static String decryptThreeDESECB(final String src, final String key) throws Exception {
        String str = Constant.DE_STR.DE+Constant.DE_STR.SEDE;
        str +=Constant.DE_STR.EC+Constant.DE_STR.PKCS;
        str +=Constant.DE_STR.PAD+Constant.DE_STR.DING;
        // --通过base64,将字符串转成byte数组
        final BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] bytesrc = decoder.decodeBuffer(src);
            final DESedeKeySpec dks = new DESedeKeySpec(key.getBytes(MpConstant.BYTE_UTF_8));
            final SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(Constant.DE_STR.DE+Constant.DE_STR.SEDE);
            final SecretKey securekey = keyFactory.generateSecret(dks);
            final Cipher cipher = Cipher.getInstance(str);
            cipher.init(Cipher.DECRYPT_MODE, securekey);
            String retByte = new String(cipher.doFinal(bytesrc),Charset.forName(MpConstant.BYTE_UTF_8));
            return retByte;
        } catch (Exception e) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.DECRYPTION_EXCEPTION));
        }
    }

}
