package com.zte.common.utils;

import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.SpTemplateItemDTO;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * +,-,*,/四则运算的表达式逆波兰解析计算类,精确计算,应用BigDecimal类处理
 * 支持负数,但规范除整个表达式第一个数为负数时可以不出现在'('后，其它表达式中间任何位置的
 * 负数必须出现在'('后,即：用括号括起来。比如：-3+(-2+1)*10或-3+((-2)+1)*10或(-3)+(-2+1)*10或(-3)+((-2)+1)*10
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/11 14:19
 * @Description
 */
public class TemplateFunctionExpress {

    /**
     * 二元操作符
     */
    private static final String[] TWO_OPERATOR = {
            SpecialityParamConstant.TWO_OPERATOR_PLUS,
            SpecialityParamConstant.TWO_OPERATOR_MINUS,
            SpecialityParamConstant.TWO_OPERATOR_MULTIPLY,
            SpecialityParamConstant.TWO_OPERATOR_DIVIDE,
            SpecialityParamConstant.TWO_OPERATOR_LINK
    };

    /**
     * 函数，格式：<名称，参数个数>
     */
    public static final Map<String, Integer> FUNCTIONS;

    static {
        Map<String, Integer> tempFunction = new HashMap<>();
        tempFunction.put(SpecialityParamConstant.FUNCTION_BIT_AND, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_BIT_OR, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_BIT_XOR, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_MOD, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_TRUNC, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_ROUND, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_LOWER, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_UPPER, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_POWER, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_TO_NUMBER, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_TO_CHAR, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_SUBSTR, 3);
        tempFunction.put(SpecialityParamConstant.FUNCTION_RANDSTR, 2);
        tempFunction.put(SpecialityParamConstant.FUNCTION_REPLACE, 3);
        tempFunction.put(SpecialityParamConstant.FUNCTION_LENGTH, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_ASCII, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_CHR, 1);
        tempFunction.put(SpecialityParamConstant.FUNCTION_FIXEDSTR, 4);
        tempFunction.put(SpecialityParamConstant.FUNCTION_ROWNUMBER, 0);
        tempFunction.put(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE, 0);
        FUNCTIONS = Collections.unmodifiableMap(tempFunction);
    }

    /**
     * 最原始的表达式
     */
    private String initExpression;

    /**
     * 精度
     */
    private int precision = 20;

    /**
     * 取舍模式
     */
    private RoundingMode roundingMode = RoundingMode.HALF_UP;

    /**
     * 精度上下文
     */
    private MathContext mc;

    Pattern numPattern = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");
    /**
     *十进制
     */
    static Pattern decimalism = Pattern.compile("^FM9+|FM9+\\.?9+|9+|9+\\.?9+$");

    /**
     * 十六进制
     */
    static Pattern hexadecimal = Pattern.compile("^FM[X,x]+|[X,x]+$");


    public TemplateFunctionExpress(String initExpression) {
        init(initExpression, this.precision, this.roundingMode);
    }

    public TemplateFunctionExpress(String initExpression, int precision, RoundingMode roundingMode) {
        init(initExpression, precision, roundingMode);
    }

    public void init(String expBase, int precision, RoundingMode roundingMode) {
        this.initExpression = expBase;
        this.precision = precision;
        this.roundingMode = roundingMode;
        this.mc = new MathContext(precision, roundingMode);
    }

    public static void checkExpression(String expression) {
        if (!checkParenthesisMark(expression)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_BRACKETS, new String[]{expression});
        }
        if (!checkTwoOperator(expression)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_TWO_OPERATOR, new String[]{expression});
        }
        if (!checkFunction(expression)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_FUNCTION, new String[]{expression});
        }
    }

    /**
     * 各参数类型下，校验参数合法性
     *
     */
    public static void checkTemplateItem(SpTemplateItemDTO item) {
        if (!item.isWhetherAdd()) {
            return;
        }
        if (StringUtils.equals(item.getParamType(),Constant.ParamType.MAC)) {
            String separator = "";
            if (StringUtils.equals(item.getGenerationMethod(),Constant.GenerationMethod.MAC_START)) {
                separator = item.getParamValue();
            } else if (StringUtils.equals(item.getGenerationMethod(),Constant.GenerationMethod.CUSTOMIZED_MAC_START)) {
                separator = item.getParamValue().substring(0, item.getParamValue().indexOf(','));
            }
            if (StringUtils.isBlank(separator)) {
                return;
            }
            // 分割符不为 ":" 或 "-"
            if (!StringUtils.equals(separator,Constant.HORIZON) && !StringUtils.equals(separator,Constant.SEMICOLON)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_RULE_ERROR);
            }
        }
    }

    /**
     * 校验括号成对
     *
     * @param paramRule
     */
    private static boolean checkParenthesisMark(String paramRule) {
        int intIndex = Constant.INT_B1;
        int intLeft = Constant.INT_B1;
        int intRight = Constant.INT_B1;

        do {
            intIndex = paramRule.indexOf(SpecialityParamConstant.PARENTHESES_START, intIndex + Constant.INT_1);
            intLeft++;
        } while (intIndex >= Constant.INT_0);

        intIndex = Constant.INT_B1;
        do {
            intIndex = paramRule.indexOf(SpecialityParamConstant.PARENTHESES_END, intIndex + Constant.INT_1);
            intRight++;
        } while (intIndex >= Constant.INT_0);

        return intLeft == intRight;
    }

    /**
     * 校验二元运算符后的参数
     */

    private static boolean checkTwoOperator(String paramRule) {
        //循环校验
        for (String operator : TWO_OPERATOR) {
            int operatorPost = paramRule.indexOf(operator, Constant.INT_0);
            while (operatorPost >= Constant.INT_0) {
                if ((paramRule.length() == operatorPost + operator.length()) || (Constant.INT_0 == operatorPost)) {
                    //最后一位或者第一位，前面后面无参数了
                    return false;
                }

                //
                if (checkTwoOperatorBehind(paramRule, operatorPost + operator.length())) {
                    return false;
                }

                //查找下一个位置
                operatorPost = paramRule.indexOf(operator, operatorPost + Constant.INT_1);
            }
        }
        return true;
    }

    /**
     * 二元运算符后面不能是二元运算符
     *
     * @param paramRule
     * @param operatorEndLength
     * @return 如果存在则返回true
     */
    private static boolean checkTwoOperatorBehind(String paramRule, int operatorEndLength) {
        for (String operator : TWO_OPERATOR) {
            //先要校验二元运算符后面的位数是否够要查找的二元运算符的长度
            if ((paramRule.length() - operatorEndLength) < operator.length()) {
                //后面的位数小于检测的二元运算符，则不可能是此二元运算符，继续检测下一个
                continue;
            }
            if (operator.equals(paramRule.substring(operatorEndLength, operatorEndLength + operator.length()))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验函数的规则
     *
     * @param paramRule
     * @return
     */
    private static boolean checkFunction(String paramRule) {
        for (String functionName : TemplateFunctionExpress.FUNCTIONS.keySet()) {
            int intIndex = -1;
            intIndex = paramRule.indexOf(functionName + SpecialityParamConstant.PARENTHESES_START, intIndex + 1);
            while (intIndex >= 0) {
                if (paramRule.length() == intIndex + functionName.length() &&
                        !paramRule.substring(intIndex + functionName.length()).startsWith(SpecialityParamConstant.PARENTHESES_START)) {
                    //1、最后一位，后面无参数了,认为不是函数，无需后面的校验
                    //2、不是“（”的认为是普通的字符串，不是函数，无需后面的校验 (
                    //                //根据位置先判断后面是不是SpecialityParamConstant.PARENTHESES_START)
                    intIndex = paramRule.indexOf(functionName, intIndex + 1);
                    continue;
                }

                //返回参数的个数，根据函数的名字判断参数是否正确
                int intParamNumber = checkFunParamNumber(paramRule.substring((intIndex + functionName.length())));

                /*
                 * 根据具体的函数判断参数个数是否正确
                 * 第intFunIndex个函数参数的个数为arrFunParamNum[intFunIndex]个
                 */
                if (intParamNumber != TemplateFunctionExpress.FUNCTIONS.get(functionName)) {
                    return false;
                }

                //查找下一个位置
                intIndex = paramRule.indexOf(functionName, intIndex + 1);
            }
        }
        return true;
    }

    /**
     * 计算函数的参数的个数，有异常返回－1
     *
     * @param strFunParam
     * @return
     */
    private static int checkFunParamNumber(String strFunParam) {
        //根据传入的字符串先获取函数后面的参数以括号为标志
        int intIndex;
        for (intIndex = NumConstant.NUM_ZERO; intIndex < strFunParam.length(); intIndex++) {
            if (checkParenthesisMark(strFunParam.substring(NumConstant.NUM_ZERO, NumConstant.NUM_ONE + intIndex))) {
                //括号成对，此时index的位置是函数的‘）’
                break;
            }
        }

        if (strFunParam.length() == intIndex) {
            //没有找到匹配的括号
            return NumConstant.NUM_MINUS_ONE;
        }

        //去除括号后函数的参数
        String strFun = strFunParam.substring(NumConstant.NUM_ONE, intIndex);
        if (strFun.length() == NumConstant.NUM_ZERO) {
            //括号内为空，则参数个数是0
            return NumConstant.NUM_ZERO;
        }

        //根据查找','判断参数的个数
        //参数的个数
        int intParamNumber = NumConstant.NUM_ZERO;
        //上个','的位置
        int intLastPos = NumConstant.NUM_MINUS_ONE;

        intIndex = strFun.indexOf(Constant.COMMA, NumConstant.NUM_ZERO);

        while (intIndex >= NumConstant.NUM_ZERO) {
            //找到后，先判断','之前的括号是不是成对，这是为了区别参数中有括号的情况
            if (checkParenthesisMark(strFun.substring(NumConstant.NUM_ZERO, intIndex + NumConstant.NUM_ONE))) {
                //括号成对，则说明是分割此函数的','
                if (intLastPos + NumConstant.NUM_ONE == intIndex) {
                    //逗号与前一个只差一位，说明参数为空
                    return NumConstant.NUM_MINUS_ONE;
                }
                intParamNumber++;
                intLastPos = intIndex;
            }

            //查找下一个位置
            intIndex = strFun.indexOf(Constant.COMMA, intIndex + NumConstant.NUM_ONE);
        }

        //查找结束后，判断最后一个逗号后面的参数是否为空
        if (intLastPos == strFun.length() - NumConstant.NUM_ONE) {
            //逗号在最后一位
            return NumConstant.NUM_MINUS_ONE;
        } else {
            intParamNumber++;
            return intParamNumber;
        }
    }

    /**
     * 去除空白字符和在负号'-'前加'0',便于后面的StringTokenizer
     *
     * @param exp
     * @return
     */
    private static String initExpress(String exp) {
        String reStr = null;
        reStr = exp.replaceAll("\\s", Constant.STRING_EMPTY);
        if (reStr.startsWith(Constant.HORIZON)) {
            reStr = NumConstant.STRING_ZERO + reStr;
        }
        reStr = reStr.replaceAll("\\(\\-", "(0-");
        return reStr;
    }

    /**
     * 是否是整数或是浮点数,但默认-05.15这种也认为是正确的格式
     *
     * @param str
     * @return
     */
    private boolean isNumber(String str) {
        Matcher m = numPattern.matcher(str);
        return m.matches();
    }

    /**
     * 设置优先级顺序()设置与否无所谓
     *
     * @param str
     * @return
     */
    private int precedence(String str) {
        char sign = str.charAt(NumConstant.NUM_ZERO);
        switch (sign) {
            case '+':
            case '-':
                return NumConstant.NUM_ONE;
            case '*':
            case '/':
                return NumConstant.NUM_TWO;
            case '^':
            case '%':
                return NumConstant.NUM_THREE;
            case '(':
            case ')':
            default:
                return NumConstant.NUM_ZERO;
        }
    }

    /**
     * 转变为逆波兰表达式
     *
     * @param strList
     * @return
     */
    public List<String> initRPN(List<String> strList) {
        List<String> returnList = new ArrayList<>();
        //用来存放操作符的栈
        Stack stack = new Stack();
        int length = strList.size();
        for (int i = NumConstant.NUM_ZERO; i < length; i++) {
            String str = strList.get(i);
            if (isNumber(str)) {
                returnList.add(str);
            } else {
                extracted(returnList, stack, str);
            }
        }
        //如果栈不为空，则将栈中所有元素出栈放到逆波兰链表的最后
        while (!stack.isEmpty()) {
            returnList.add(stack.pop());
        }
        return returnList;
    }

    private void extracted(List<String> returnList, Stack stack, String str) {
        if (str.equals(SpecialityParamConstant.PARENTHESES_START)) {
            //'('直接入栈
            stack.push(str);
        } else if (str.equals(SpecialityParamConstant.PARENTHESES_END)) {
            //')'
            //进行出栈操作，直到栈为空或者遇到第一个左括号
            while (!stack.isEmpty()) {
                //将栈顶字符串做出栈操作
                String tempC = stack.pop();
                if (!tempC.equals(SpecialityParamConstant.PARENTHESES_START)) {
                    //如果不是左括号，则将字符串直接放到逆波兰链表的最后
                    returnList.add(tempC);
                } else {
                    //如果是左括号，退出循环操作
                    break;
                }
            }
        } else if (stack.isEmpty()) {
            //如果栈内为空
            //将当前字符串直接压栈
            stack.push(str);
        } else {
            // 栈不空,比较运算符优先级顺序 如果栈顶元素优先级大于当前元素优先级则
            while (!stack.isEmpty() && precedence(stack.top()) >= precedence(str)) {
                returnList.add(stack.pop());
            }
            stack.push(str);
        }
    }

    /**
     * 计算逆波兰表达式
     *
     * @param rpnList
     * @return
     */
    public String caculate(List<String> rpnList) {
        Stack numberStack = new Stack();
        int length = rpnList.size();
        for (int i = NumConstant.NUM_ZERO; i < length; i++) {
            String temp = rpnList.get(i);
            if (isNumber(temp)) {
                numberStack.push(temp);
            } else {
                if (!Pattern.matches("^([+]|[-]|[*]|[/]){1}$", temp)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_TWO_OPERATOR);
                }
                BigDecimal tempNumber1 = new BigDecimal(numberStack.pop(), this.mc);
                BigDecimal tempNumber2 = new BigDecimal(numberStack.pop(), this.mc);
                BigDecimal tempNumber = new BigDecimal(Constant.STR_0, this.mc);

                if (temp.equals(SpecialityParamConstant.TWO_OPERATOR_PLUS)) {
                    tempNumber = tempNumber2.add(tempNumber1);
                } else if (temp.equals(SpecialityParamConstant.TWO_OPERATOR_MINUS)) {
                    tempNumber = tempNumber2.subtract(tempNumber1);
                } else if (temp.equals(SpecialityParamConstant.TWO_OPERATOR_MULTIPLY)) {
                    tempNumber = tempNumber2.multiply(tempNumber1);
                } else if (temp.equals(SpecialityParamConstant.TWO_OPERATOR_DIVIDE)) {
                    tempNumber = tempNumber2.divide(tempNumber1, precision, roundingMode);
                }
                numberStack.push(tempNumber.toPlainString());
            }
        }
        return numberStack.pop();
    }

    /**
     * 计算二元表达式
     *
     * @param expression
     * @return
     */
    public String caculate(String expression) {
        List<String> tempExpressionArray = this.splitString(expression);
        StringBuilder stringBuilder = new StringBuilder();
        for (String exp : tempExpressionArray) {
            if (exp.startsWith(Constant.SINGLE_QUOTE) && exp.endsWith(Constant.SINGLE_QUOTE)) {
                // 字符串常量
                stringBuilder.append(exp, NumConstant.NUM_ONE, exp.length() - NumConstant.NUM_ONE);
                continue;
            }
            List<String> expList = new ArrayList<>();
            String expInited = initExpress(exp);
            StringTokenizer st = new StringTokenizer(expInited, "+-*/^%()", true);
            while (st.hasMoreElements()) {
                expList.add(st.nextElement().toString().replaceAll(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).trim());
            }
            stringBuilder.append(caculate(initRPN(expList)));
        }
        return stringBuilder.toString();
    }

    private List<String> splitString(String expression) {
        if (!expression.contains(Constant.SINGLE_QUOTE) || !expression.contains(Constant.OR)) {
            return Collections.singletonList(expression);
        }
        List<String> list = new ArrayList<>();
        int nextQuotePos = expression.indexOf(Constant.SINGLE_QUOTE);
        if (nextQuotePos > 0) {
            // 前半段
            String temp = expression.substring(0, nextQuotePos).trim();
            if (temp.startsWith(Constant.OR)) {
                temp = temp.substring(2).trim();
            }
            if (temp.endsWith(Constant.OR)) {
                temp = temp.substring(0, temp.length() - 2).trim();
            }
            if (StringUtils.isNotEmpty(temp)) {
                list.add(temp);
            }
            //后半段
            String last = expression.substring(nextQuotePos).trim();
            list.addAll(this.splitString(last));
        } else {
            nextQuotePos = expression.indexOf(Constant.SINGLE_QUOTE, 1);
            list.add(expression.substring(0, nextQuotePos + NumConstant.NUM_ONE));
            //后半段
            String last = expression.substring(nextQuotePos + NumConstant.NUM_ONE).trim();
            if (StringUtils.isNotEmpty(last)) {
                list.addAll(this.splitString(last));
            }
        }
        return list;
    }

    /**
     * 按照类的缺省参数进行计算
     *
     * @return
     */
    public String caculate() {
        return this.caculate(functionHander(this.initExpression));
    }

    /**
     * 函数处理
     *
     * @param expression
     * @return
     */
    private String functionHander(String expression) {
        for (String functionName : FUNCTIONS.keySet()) {
            while (expression.contains(functionName + SpecialityParamConstant.PARENTHESES_START)) {
                // 存在此函数
                String params = getFunctionParam(functionName, expression);
                while (isContainFunction(params)) {
                    String tempValue = this.functionHander(params);
                    expression = expression.replace(params, tempValue);
                    params = tempValue;
                }
                StringTokenizer st = new StringTokenizer(params, "(,)", false);
                List<String> paramList = new ArrayList<>();
                while (st.hasMoreElements()) {
                    String param = st.nextElement().toString().trim();
                    if (Pattern.matches(".*([']|[+]|[-]|[*]|[/]|[|]){1}.*", param)) {
                        param = this.caculate(param);
                    }
                    paramList.add(param);
                }
                expression = expression.replace(functionName + params, excuteFunction(functionName, paramList));
            }
        }
        return expression;
    }

    private static String getFunctionParam(String functionName, String functionExpression) {
        int nameIndex = functionExpression.indexOf(functionName + SpecialityParamConstant.PARENTHESES_START);
        String tail = functionExpression.substring(nameIndex + functionName.length());
        int endIndex = NumConstant.NUM_MINUS_ONE;
        String param;
        do {
            endIndex = tail.indexOf(SpecialityParamConstant.PARENTHESES_END, endIndex + NumConstant.NUM_ONE);
            param = tail.substring(NumConstant.NUM_ZERO, endIndex + NumConstant.NUM_ONE);
        } while (!checkParenthesisMark(param));
        return param;
    }

    private String excuteFunction(String functionName, List<String> paramList) {
        String paramValue = Constant.STRING_EMPTY;
        switch (functionName) {
            case SpecialityParamConstant.FUNCTION_BIT_AND:
                paramValue = Constant.SINGLE_QUOTE + bitAnd(paramList.get(NumConstant.NUM_ZERO), paramList.get(NumConstant.NUM_ONE)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_BIT_OR:
                paramValue = Constant.SINGLE_QUOTE + bitOr(paramList.get(NumConstant.NUM_ZERO), paramList.get(NumConstant.NUM_ONE)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_BIT_XOR:
                paramValue = Constant.SINGLE_QUOTE + bitXor(paramList.get(NumConstant.NUM_ZERO), paramList.get(NumConstant.NUM_ONE)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_MOD:
                paramValue = mod(paramList.get(NumConstant.NUM_ZERO), paramList.get(NumConstant.NUM_ONE));
                break;
            case SpecialityParamConstant.FUNCTION_TRUNC:
                paramValue = trunc(Float.valueOf(paramList.get(NumConstant.NUM_ZERO)));
                break;
            case SpecialityParamConstant.FUNCTION_ROUND:
                paramValue = round(Float.valueOf(paramList.get(NumConstant.NUM_ZERO)));
                break;
            case SpecialityParamConstant.FUNCTION_POWER:
                paramValue = power(Float.valueOf(paramList.get(NumConstant.NUM_ZERO)), Float.valueOf(paramList.get(NumConstant.NUM_ONE)));
                break;
            case SpecialityParamConstant.FUNCTION_LOWER:
                paramValue = Constant.SINGLE_QUOTE + paramList.get(NumConstant.NUM_ZERO).toLowerCase() + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_UPPER:
                paramValue = Constant.SINGLE_QUOTE + paramList.get(NumConstant.NUM_ZERO).toUpperCase() + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_TO_NUMBER:
                paramValue = toNumber(paramList.get(NumConstant.NUM_ZERO), paramList.get(NumConstant.NUM_ONE));
                break;
            case SpecialityParamConstant.FUNCTION_TO_CHAR:
                paramValue = Constant.SINGLE_QUOTE + toChar(new BigDecimal(paramList.get(NumConstant.NUM_ZERO)), paramList.get(NumConstant.NUM_ONE)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_SUBSTR:
                paramValue = Constant.SINGLE_QUOTE + subStr(paramList.get(NumConstant.NUM_ZERO), Integer.parseInt(paramList.get(NumConstant.NUM_ONE)), Integer.parseInt(paramList.get(NumConstant.NUM_TWO))) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_RANDSTR:
                paramValue = Constant.SINGLE_QUOTE + randomStr(paramList.get(NumConstant.NUM_ZERO), Integer.parseInt(paramList.get(NumConstant.NUM_ONE))) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_REPLACE:
                paramValue = Constant.SINGLE_QUOTE + paramList.get(NumConstant.NUM_ZERO).replace(paramList.get(NumConstant.NUM_ONE), paramList.get(NumConstant.NUM_TWO)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_LENGTH:
                paramValue = String.valueOf(paramList.get(Constant.NUM_ZERO).length());
                break;
            case SpecialityParamConstant.FUNCTION_ASCII:
                paramValue = Integer.toString(paramList.get(Constant.NUM_ZERO).charAt(NumConstant.NUM_ZERO));
                break;
            case SpecialityParamConstant.FUNCTION_CHR:
                paramValue = Constant.SINGLE_QUOTE + (char) Integer.parseInt(paramList.get(Constant.NUM_ZERO)) + Constant.SINGLE_QUOTE;
                break;
            case SpecialityParamConstant.FUNCTION_FIXEDSTR:
                paramValue = fixedstr(paramList.get(NumConstant.NUM_ONE));
                break;
            default:
                break;
        }
        return paramValue;
    }

    /**
     * 判断是否为函数
     *
     * @param expression
     * @return
     */
    private boolean isContainFunction(String expression) {
        for (String functionName : FUNCTIONS.keySet()) {
            if (expression.contains(functionName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @return the expBase
     */
    public String getInitExpression() {
        return initExpression;
    }

    /**
     * @param initExpression the expBase to set
     */
    public void setInitExpression(String initExpression) {
        this.initExpression = initExpression;
    }

    /**
     * @return the precision
     */
    public int getPrecision() {
        return precision;
    }

    /**
     * @param precision the precision to set
     */
    public void setPrecision(int precision) {
        this.precision = precision;
    }

    /**
     * @return the roundingMode
     */
    public RoundingMode getRoundingMode() {
        return roundingMode;
    }

    /**
     * @param roundingMode the roundingMode to set
     */
    public void setRoundingMode(RoundingMode roundingMode) {
        this.roundingMode = roundingMode;
    }

    /**
     * @return the mc
     */
    public MathContext getMc() {
        return mc;
    }

    /**
     * @param mc the mc to set
     */
    public void setMc(MathContext mc) {
        this.mc = mc;
    }

    /**
     * 栈
     */
    private class Stack {

        LinkedList<String> stackList = new LinkedList<>();

        /**
         * 入栈
         *
         * @param expression
         */
        public void push(String expression) {
            stackList.addLast(expression);
        }

        /**
         * 出栈
         *
         * @return
         */
        public String pop() {
            if (stackList.size() <= NumConstant.NUM_ZERO) {
                return "";
            }
            return stackList.removeLast();
        }

        /**
         * 栈顶元素
         *
         * @return
         */
        public String top() {
            return stackList.getLast();
        }

        /**
         * 栈是否为空
         *
         * @return
         */
        public boolean isEmpty() {
            return stackList.isEmpty();
        }
    }


    public static String bitAnd(String hex1, String hex2) {
        return bitCommon(hex1, hex2, NumConstant.NUM_ZERO);
    }

    public static String bitOr(String hex1, String hex2) {
        return bitCommon(hex1, hex2, NumConstant.NUM_ONE);
    }


    /**
     * 1. hex1,hex2 不是偶数在前面补0
     * 2.取hex1,hex2 长度长的一个，刚长度短的一个在后面补0，直到两个数长度一样
     * 3.转成十进制进行 异或 运算
     * 4.刚运算结束转成十六进制，并且结果不是偶数在前面补0
     */
    public static String bitXor(String hex1, String hex2) {
        return bitCommon(hex1, hex2, NumConstant.NUM_TWO);
    }

    private static String bitCommon(String hex1, String hex2, int type) {
        if (hex1.length() % NumConstant.NUM_TWO == NumConstant.NUM_ONE) {
            hex1 = NumConstant.STRING_ZERO + hex1;
        }
        if (hex2.length() % NumConstant.NUM_TWO == NumConstant.NUM_ONE) {
            hex2 = NumConstant.STRING_ZERO + hex2;
        }
        int charCout;
        if (hex1.length() > hex2.length()) {
            charCout = hex1.length() - hex2.length();
            for (int i = NumConstant.NUM_ZERO; i < charCout; i++) {
                hex2 += NumConstant.STRING_ZERO;
            }
        } else if (hex1.length() < hex2.length()) {
            charCout = hex2.length() - hex1.length();
            for (int i = NumConstant.NUM_ZERO; i < charCout; i++) {
                hex1 += NumConstant.STRING_ZERO;
            }
        }

        int dem;
        switch (type) {
            case NumConstant.NUM_ZERO:
                dem = Integer.parseInt(hex1, NumConstant.NUM_SIXTEEN) & Integer.parseInt(hex2, NumConstant.NUM_SIXTEEN);
                break;
            case NumConstant.NUM_ONE:
                dem = Integer.parseInt(hex1, NumConstant.NUM_SIXTEEN) | Integer.parseInt(hex2, NumConstant.NUM_SIXTEEN);
                break;
            case NumConstant.NUM_TWO:
            default:
                dem = Integer.parseInt(hex1, NumConstant.NUM_SIXTEEN) ^ Integer.parseInt(hex2, NumConstant.NUM_SIXTEEN);
        }
        String result = Integer.toHexString(dem);
        if (result.length() % NumConstant.NUM_TWO == NumConstant.NUM_ONE) {
            result = NumConstant.STRING_ZERO + result;
        }
        return result.toUpperCase();
    }

    public static String mod(String n1, String n2) {
        Long d1 = Long.parseLong(n1);
        Long d2 = Long.parseLong(n2);
        if (d2 == NumConstant.NUM_ZERO) {
            return n1;
        }
        return String.valueOf(d1 % d2);
    }

    public static String trunc(Float num) {
        return String.valueOf(num.intValue());
    }

    public static String round(Float num) {
        return String.valueOf(Math.round(num));
    }

    public static String power(Float num1, Float num2) {
        return String.valueOf(Math.pow(num1, num2));
    }

    private String fixedstr(String str) {
        if (isNumber(str)) {
            return str;
        }
        return Constant.SINGLE_QUOTE + str + Constant.SINGLE_QUOTE;
    }

    public static String subStr(String source, int start, int len) {
        if (start < NumConstant.NUM_ZERO) {
            start = start + source.length();
        } else if (start > NumConstant.NUM_ZERO) {
            start -= NumConstant.NUM_ONE;
        }
        if ((start + len) > source.length()) {
            return source.substring(start);
        } else {
            return source.substring(start, start + len);
        }
    }

    public static String toNumber(String str, String format) {
        format = format.substring(NumConstant.NUM_TWO);
        if (decimalism.matcher(format.toUpperCase().trim()).matches()) {
            str = trimZero(str);
            if (format.contains(Constants.POINT) && str.contains(Constants.POINT)) {
                format = format.substring(format.indexOf(Constants.POINT));
                DecimalFormat decimalFormat = new DecimalFormat(Constants.EL_PREFIX + format.replace(NumConstant.STRING_NINE, NumConstant.STRING_ZERO));
                decimalFormat.setRoundingMode(RoundingMode.DOWN);
                return decimalFormat.format(new BigDecimal(str));
            } else {
                return str.substring(NumConstant.NUM_ZERO, Math.min(str.length(), format.length()));
            }
        } else if (hexadecimal.matcher(format.toUpperCase().trim()).matches()) {
            if (str.trim().length() > NumConstant.NUM_TWELVE) {
                str = str.trim().substring(str.trim().length() - NumConstant.NUM_TWELVE);
            }
            return String.valueOf(Long.parseLong(str, NumConstant.NUM_SIXTEEN));
        }
        return str;
    }

    public static String trimZero(String val) {
        char[] value = val.toCharArray();
        int len = value.length;
        int st = 0;
        while ((st < len) && (value[st] <= SpecialityParamConstant.ZERO_CHAR)) {
            st++;
        }
        while ((st < len) && (value[len - 1] <= SpecialityParamConstant.ZERO_CHAR)) {
            len--;
        }
        return ((st > 0) || (len < value.length)) ? val.substring(st, len) : val;
    }


    /**
     * '99'表示转换为10进制数字符串,9的个数代表位数,其中'.'是在指定位置上返回一个小数点;
     * 'XX'表示转换为16进制数字符串,X的个数代表位数,位数不能少于字符串的位数
     *
     * @param num
     * @param format
     * @return
     */
    public static String toChar(BigDecimal num, String format) {
        format = format.substring(NumConstant.NUM_TWO);
        if (decimalism.matcher(format.toUpperCase().trim()).matches()) {
            int numberSize = format.length();
            if (format.contains(Constants.POINT)) {
                format = format.substring(format.indexOf(Constants.POINT));
            }
            numberSize = numberSize - format.length();
            String value = new DecimalFormat(Constants.EL_PREFIX + format.replace(NumConstant.STRING_NINE, NumConstant.STRING_ZERO)).format(num).toUpperCase();
            if (value.length() > numberSize + format.length()) {
                value = value.substring(value.length() - numberSize - format.length());
            }
            return value;
        } else if (hexadecimal.matcher(format.toUpperCase().trim()).matches()) {
            return Long.toHexString(num.longValue()).toUpperCase();
        }
        return num.toString().toUpperCase();
    }


    private static Random random = new Random();

    public static String randomStr(String baseString, int length) {
        if (length <= 0) {
            return Constant.STRING_EMPTY;
        } else if (baseString.length() < NumConstant.NUM_TEN) {
            StringBuilder sb = new StringBuilder();
            for (int i = NumConstant.NUM_ZERO; i < length; i++) {
                sb.append(baseString.charAt(random.nextInt(baseString.length())));
            }
            return sb.toString();
        } else {
            do {
                baseString += baseString;
            } while (baseString.length() < NumConstant.NUM_64);
            baseString = baseString.substring(NumConstant.NUM_ZERO, NumConstant.NUM_64);
            int index = 0;
            char[] temp = new char[length];
            int num = random.nextInt();
            for (int i = 0; i < length % Constant.INT_5; i++) {
                temp[index++] = baseString.charAt(num & 63);//取后面六位，记得对应的二进制是以补码形式存在的。
                num >>= 6;//63的二进制为:111111
                // 为什么要右移6位？因为数组里面一共有64个有效字符。为什么要除5取余？因为一个int型要用4个字节表示，也就是32位。
            }
            for (int i = 0; i < length / Constant.INT_5; i++) {
                num = random.nextInt();
                for (int j = 0; j < Constant.INT_5; j++) {
                    temp[index++] = baseString.charAt(num & 63);
                    num >>= 6;
                }
            }
            return new String(temp, 0, length);
        }
    }
}
