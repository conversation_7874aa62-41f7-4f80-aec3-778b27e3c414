package com.zte.common.utils;

import org.apache.commons.codec.digest.DigestUtils;
import java.util.zip.CRC32;

/**
 * RFID 电子签名CRC工具类
 * <AUTHOR>
 */
public class CRCUtil {

    public static long encode(byte[] data, byte[] salt) {

        byte[] cb = new byte[data.length + salt.length];
        System.arraycopy(data, 0, cb, 0, data.length);
        System.arraycopy(salt, 0, cb, data.length, salt.length);
        CRC32 crc32 = new CRC32();
        crc32.update(data);
        return crc32.getValue();
    }
}
