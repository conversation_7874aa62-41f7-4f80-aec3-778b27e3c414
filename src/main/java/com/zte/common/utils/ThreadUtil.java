package com.zte.common.utils;

import com.alibaba.ttl.threadpool.TtlExecutors;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 专用线程池工具类
 *
 * 通用线程池使用common包的工具类
 * @see com.zte.springbootframe.util.ThreadUtil;
 *
 * <AUTHOR>
 */
public class ThreadUtil extends com.zte.springbootframe.util.ThreadUtil {

    private static final Integer MAXIMUM_POOL_SIZE = 20 ;
    private static final Long KEEP_ALIVE_TIME = 60L ;
    private static final Integer ONE = 1 ;
    private static final String THREAD_NAME_PREFIX = "my-thread-" ;

    public static final ExecutorService BOM_SPLIT_THREAD_POOL_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.INT_10,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));

    public static final ExecutorService BOARD_CODE_QUERY_POOL_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.INT_10,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));

    public static final ExecutorService TASK_INFO_CHANGE_ABLE_QUERY_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));

    public static final ExecutorService SYNCHRONIZE_SPM_DATE_SCHEDULE_TASK_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));

    public static final ExecutorService SYNCHRONIZE_LASTDAY_FINISH_TECH_SCHEDULE_TASK_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));

    public static final ExecutorService SYNCHRONIZE_FINISH_TECH_DATA_SCHEDULE_TASK_EXECUTOR =
            TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(Constant.CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new SynchronousQueue<Runnable>(),new NameTreadFactory()));


    static class NameTreadFactory implements ThreadFactory {
        private final AtomicInteger mThreadNum = new AtomicInteger(ONE);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, THREAD_NAME_PREFIX + mThreadNum.getAndIncrement());
            return t;
        }
    }

}
