package com.zte.common.utils.constant;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/26 9:40
 * @Description
 */
public class RedisKeyConstant {

    public static final String PREFIX = "cache:";
    public static final String LOCK = "lock:";

    public static final String MENU_USER = PREFIX + "menu:%s";

    public static final String SPECIALITY_PARAM = LOCK + "speciality_param:%s";
    public static final String SPECIALITY_PARAM_GENERATE = PREFIX + "speciality_param:%s";
    public static final String IMPORT_RECOVER = LOCK + "import-recover:%s";
    public static final String SPECIALITY_PARAM_EXPORT = LOCK + "speciality_param:export:%s";
    public static final String SPECIALITY_PARAM_EXPORT2MDS = LOCK + "speciality_param:syncmds:%s";
    public static final String SPECIALITY_PARAM_RECOVERY_EXPORT = LOCK + "speciality_param_recovery:export:%s";

    public static final String BOM_CRAFT_ATTRIBUTE_EXPORT = "bom_craft_attribute_export";

    public static  final String IMES_PREFIX="imes:";

    public static  final String CENTERFACTORY_PREFIX="zte-mes-manufactureshare-centerfactory:";

    //技改单定时任务key
    public static final String PDM_TECHNICAL_CHANGE_KEY = IMES_PREFIX + CENTERFACTORY_PREFIX + "pdm_technical_change_deal_technical_bill";

    //技改单回写MES定时任务key
    public static final String TECHNICAL_CHANGE_SFC_KEY =  "LOCK:TECHNICAL_CHANGE_SFC_KEY:";
    //同步lms单据状态定时任务key
    public static final String SYNC_LMS_BILL_STATUS_KEY = "sync_lms_bill_status_key";
    //同步lms条码imu定时任务key
    public static final String SYNC_LMS_SN_IMU_KEY = "sync_lms_sn_imu_key";

    //imes扫描启用前_入库数据统计接口
    public static final String STOCK_STATICS_CENTER_LOCK = "STOCK_STATICS_CENTER_LOCK";

    public static final String SEND_SEMI_WIP_EXT_BY_SPM_LOCK = "send_semi_wip_ext_by_spm_lock";

    public static final String SEND_NULL_SEMI_WIP_EXT_BY_SPM_LOCK = "send_null_semi_wip_ext_by_spm_lock";

    public static final String SEND_MATERIAL_WIP_EXT_BY_SPM_LOCK = "send_material_wip_ext_by_spm_lock";
    public static final String SAVE_PROCESS_PATH = IMES_PREFIX + CENTERFACTORY_PREFIX +"save_process_path_";
    public static final String WIP_DAILY_STATISTIC_REPORT_EXPORT = IMES_PREFIX + CENTERFACTORY_PREFIX +"wip_daily_statistic_report_export_";

    public static final String CUSTOMER_ITEMS_LOCK = "customer_items_lock_";

    //技改单错误邮件发送key
    public static final String TECHNICAL_TRANSFORMATION_FORM_WRONG_EMAIL_SENDING_KEY= "technical_transformation_form_wrong_email_sending_key_";

    public static final String SET_BENCHMARK_QUANTITY_KEY= "set_benchmark_quantity_";
    public static final String UPDATE_MODEL_ENCODING= "update_Model_Encoding";
    public static final String AUTOMATIC_APPLICATION_OF_IDENTIFICATION_NUMBER_KEY= "automatic_application_of_identification_number";

    public static final String AUTOMATICALLY_DOWNLOAD_IDENTIFICATION_NUMBER= "automatically_download_identification_number";
    public static final String HOME_END_INDIVIDUAL_PARAMETER_BINDING= "home_end_individual_parameter_binding";

    public static final String DELIVER_KANBAN_INFO_EMAIL_SENDING_KEY = "deliver.kanban.info.email.sending.key";

    public static final String USE_INFO_IMPORT_KEY = "USE_INFO_IMPORT_KEY";
}
