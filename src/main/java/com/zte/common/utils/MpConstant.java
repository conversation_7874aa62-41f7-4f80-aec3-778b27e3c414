package com.zte.common.utils;

/**
 * 魔法值常量
 *
 * <AUTHOR>
 */
public interface MpConstant {


    /**
    * 任务信息导出
    *@Author: 10307315陈俊熙
    *@date 2022/9/6 上午10:26
    */
    interface TASK_INFO_QUERY_EXPORT {
        String TASK_INFO_QUERY_XLSX = "任务信息列表.xlsx";
        String TASK_INFO_QUERY_SHEET = "任务信息";
        String[] TITLE = new String[]{
                "任务号", "计划跟踪单号", "部件计划单号", "批次", "订单", "任务状态", "ERP状态", "备料状态", "制造料单代码", "物料名称","PCB版本",
                "工艺路径","工艺员", "产品大类", "机型", "计划组", "任务数", "计划日期", "计调员", "释放日", "要求完成时间",
                "生产单位ID", "环保属性", "生产单位", "预计发放日","发放人", "发放日期", "是否触发拉料", "工序指令按任务条码校验", "是否锁定任务条码入库",
                "要求装配完工时间", "要求测试完工时间", "要求包装完工时间", "计调备注", "装配备注", "料单代码", "物料标记"
        };
        String[] PROPS = new String[]{
                "taskNo", "prodplanNo", "partsPlanno", "prodplanId","planId", "taskStatus", "erpStatus", "inforExe", "mBom", "itemName","verNo",
                "routeDetail","technologist", "externalType", "internalType", "attribute1","taskQty", "plannedFinishDate", "plannerName","releaseDate", "requireCompleteDate",
                "factoryId", "isLead", "factoryName", "toGrantDate","grantByName", "grantTime","attribute11", "attribute7", "attribute8",
                "attribute5", "attribute6", "attribute10", "attribute3", "assemblyRemark", "itemNo", "materialSignName"
        };
    }


    String FILE_FORMAT_ERROR = "文件格式错误，请使用excel导入！";

    String WORKBOOK_INIT_FAIL = "Workbook初始化失败";
    //家端接口最大数据量
    int MAXIMUM_DATA_OF_HOME_INTERFACE = 1153001;
    // 是否启用条码中心
    int USE_BARCODE_FLAG = 1292001;
    int SELECT_BARCODE_USE_BARCODE_FLAG = 1292002;
    int REELID_WRITE_LOCAL = 1292006;
    String BARCODE_TYPE_SN_CODE = "SN_CODE";
    String BARCODE_TYPE_REEL_MP_CODE = "REEL_MP_CODE";
    String BARCODE_TYPE_CONTAINER_CODE = "CONTAINER_CODE";
    String BARCODE_X_TENANT_ID = "10001";


    //文件加密等级：1、2、5、99 -> 受限只读10天、只读、可编辑、不加密
    String FILE_ENCRYPTION_LEVEL_99 = "99";

    String ENABLED_FLAG_Y = "Y";
    String ENABLED_FLAG_N = "N";

    String BLOCK_BOOO2 = "B0002";
    String BLOCK_BOOO1 = "B0001";
    String BLOCK_G0001 = "G0001";
    //请求数据格式
    String CONTENT_TYPE_JSON = "application/json";

    String ERROR = "ERROR";

    //是否
    int INT_ONE = 1;
    int INT_ZERO = 0;

    //邮件收件人类型
    String LOOKUP_TYPE_EMAIL_RECEIVER = "1213";
    //日期格式
    String DATE_FORMAT_YYYYMM = "yyyy-MM";
    String DATE_FORMAT_YYYYMM_ALL = "yyyyMMddHHmmss";
    String DATE_FORMAT_YYYYMMDD = "yyyy-MM-dd";
    String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    String DATE_FORMAT_YYYYMMDDHHMMSS_O = "yyyy/MM/dd HH:mm:ss";
	String DATE_FORMAT_YYYYMMDD_O = "yyyy/MM/dd";
    String DATE_FORMAT_YYMDD = "yy[M]dd";
    String DATE_FORMAT_DDDAY = "dd日";
    String DATE_FORMAT_YYYYW = "yyyyw";
    String DATE_FORMAT_YYYYMMDD000000 = "yyyy-MM-dd 00:00:00";
    String DATE_FORMAT_YYYYMM01000000 = "yyyy-MM-01 00:00:00";
    String DATE_SEVENNUMBER = "07 00:00:00";
    String DATE_FORMAT_YDDHHMMSS = "dd HH:mm:ss";
    String DATE_FORMAT_YYYYMM01 = "yyyy-MM-01";

    String DATE_FIRST_DAY = "-01";
    String RF = "RF";
    String RP = "RP";
    String LW = "-w";
    String LT = "-t";
    String SPROWER = "模块电源";
    String BM = "M";
    String JSON_BO = "bo";
    String JSON_CODE = "code";
    String JSON_MSG = "msg";

    String INREMARK_STRS = "'N','E'";

    int BYTES_10M = 10 * 1024 * 1024;

    String USR_PATH = "/usr/local/tomcat/logs/";

    String FLAG_SUCCESS = "success";

    //产品分类
    String CLASSIFYCATEGORY = "ClassifyCategory";
    String SYSUSERSERVICEIMPL_URL = "https://uac.zte.com.cn/uactoken/auth/token/verify.serv";

    String SNCABINDINGACTUAL_DATA_EMPTY_INSERTINFO = "传输数据内容为空";
    String SNCABINDINGACTUAL_RESULT_ERROR_INSERTINFO = "CA接口返回异常提示";
    //加解密算法
    String B64 = "B64";
    String SHA256 = "SHA256";

    String ZIP = "ZIP";
    String DN = "DN";
    String CHECKALGORITHM = "CHECKALGORITHM";
    String CACHECK = "CACHECK";
    String CAPACKAGE = "CAPACKAGE";

    //最后更新日期
    String LAST_UPDATED_DATE = "lastUpdatedDate";

    String X_EMP_NO = "10243397";
    String EXCELBIGDATAEXPORT_X_EMP_NO = "10065130";
    String CREATEDATE = "createDate";
    String ASC = "asc";
    String SYS_JOB = "SYS_JOB";
    String SCM_MES = "imes";
    //isUnhandled; // 未处理标识(1-未处理;0/空-已处理)
    int IS_UNHANDLED = 1;

    String BYTE_UTF_8 = "UTF-8";

    String MANUAL_INPUT = "人工输入";

    String UNKNOWN = "unknown";

    String STRING_PREFIX = "/";

    String STRING_MINUS = "-";

    /**
     * BOM 分阶导出
     */
    interface PRE_EXCLE_EXPORT {
        String FILENAME = "-%s-前加工信息.xls";
        String[] TITLE = new String[]{
                "料单代码", "物料代码", "同代码多前加工排序", "是否前加工", "位号", "前加工类型", "前加工去向", "配送工序", "物料名称",
                "ABC分类", "物料规格", "维护人", "维护时间", "更新人", "更新时间", "备注"
        };
        String[] PROPS = new String[]{
                "bomCode", "itemNo", "sortSeq", "isPreManu", "tagNum", "typeName", "traceName", "deliveryProcess", "itemName", "abcType", "style",
                "createBy", "createDate", "lastUpdatedBy", "lastUpdatedDate", "remark"
        };
    }

    String MES_DB = "MES-DB";
    String MATERIALLEADINFO = "MaterialLeadInfo";

    String ITEMPROPERTY = "ItemProperty";

    String LINE_FEED = "\n";
    String SMT_DELIVERY = "SMT配送";
    String SMT = "SMT";

    /**
     * 异步表常量
     */
    interface AsyncConstant {
        // 已提交
        String SUBMITED = "1";
        // 处理中
        String PRECESSING = "2";
        // 成功
        String SUCCESS = "3";
        // 失败
        String FAILED = "4";
        // 等待用户确认
        String WAITING = "5";
        // 用户取消操作
        String CANCEL = "6";
        // CAD 导入 任务名称
        String CAD_ASYNC_TYPE = "CAD 导入异步处理结果";
        // 央仓调拨跑BOM 分阶
        String ALLOC_BOM = "央仓调拨异步调拨BOM 分阶任务";
    }

    //资源类型
    String GPON_SN = "GPON-SN";
    String MAC = "MAC";
    String CTEI = "CTEI";
    String CMEI = "CMEI";
    String CUEI = "CUEI";
    String IMEI = "IMEI";
    String D_SN = "D-SN";
    String NET_LICENSE = "入网许可";
    String NAL = "NAL";
    String SN = "SN";
    String STB_SN = "STB SN";
    String SO_NET_SN = "So-net-SN";

    String GPON_SN_PATTERN = "^ZTEG[\\da-fA-F]{8}";
    String MAC_PATTERN = "^[0-9A-F]{2}(?:[\\:\\-][0-9A-F]{2}){5}$";
    String CTEI_PATTERN = "^[0-9]{15}$";
    String CUEI_PATTERN = "^[0-9]{15}$";
    String CMEI_PATTERN = "^[0-9]{15}$";
    String IMEI_PATTERN = "^[0-9]{15}$";
    String D_SN_PATTERN = "^[0-9]{13}$";
    String SN_PATTERN = "^(?:[0-9]{15}|[0-9]{25})$";
    String STB_SN_PATTERN = "^[0-9]{15}$";
    String SO_NET_SN_PATTERN = "^[0-9]{12}$";
    String NAL_PATTERN = "\\d{2}-\\d{4}-\\d{6}";

    String ZTEG = "ZTEG";
    String IMES = "iMES";
    String ASMS = "ASMS";

    String ZT = "ZT";

    String RESOURCE_KEY_VALUE_PREFIX_NO = IMES + "_resourceGenerateNo_";

    String ABNORMAL_PREFIX_NO = IMES + "_abnormalMaterialGenerateNo_";
    String KEY_VALUE_AUX_REQUISITON_BILL = IMES + "_auxMaterialRequisitionRel_";

    //异常物料录入明细转EXCEL
    interface AbnormalMaterialDetailExcal {
        String[] PROPS = new String[]{"sn", "qty", "sourceBatchCode", "itemCode", "itemName", "brandName", "externalType", "style", "finderEmp", "abnormalType", "abnormalDescription"};
        String[] HEADER_LIST = new String[]{"条码", "数量", "批次码", "物料代码", "物料名称", "品牌名称", "产品大类", "机型", "发现人", "异常类型", "异常描述"};
        String EMAIL_TITLE_ZH = "异常物料代码详情";
        String FILE_NAME = "异常物料代码详情.xlsx";
        String FUNCTION_NAME = "异常物料代码";
        String SHEETNAME = "异常物料代码";
    }

    interface AuxMaterialRequisitionExcel {
        String[] PROPS = new String[]{"auxMaterialTypeName", "relationshipDimensionName", "changeTypeName", "itemNo", "itemName", "productCategory", "auxMaterialCode", "auxMaterialCodeAfter"};
        String[] HEADER_LIST = new String[]{"辅料类型", "关系维度", "变更类型", "料单代码", "料单名称", "产品大类", "变更前辅料代码", "变更后辅料代码"};
        String EMAIL_TITLE_ZH = "辅料领用关系详情";
        String FILE_NAME = "辅料领用关系详情.xlsx";
        String FUNCTION_NAME = "辅料领用关系详情";
        String SHEETNAME = "辅料领用关系详情";
    }

    interface LookupKey {
        String LOOKUP_CODE = "lookupCode";
        String LOOK_KEY_1004053008 = "1004053008";
    }

    String PUSH_RESULT_S = "S";
    String PUSH_RESULT_F = "F";

    //条码中心接口地址
    String LOOKUP_6679="6679";
    //根据目录名称获取模板列表
    String LOOKUP_6679001="6679001";
    //条码中心适配程序下载地址获取接口
    String LOOKUP_6679002="6679002";
    //条码中心打印接口
    String LOOKUP_6679003="6679003";
    //根据模板名称获取模板信息
    String LOOKUP_6679004="6679004";
    //条码扩展信息查询
    String LOOKUP_6679005="6679005";

    //SSP物料标签打印
    //打印模板目录
    int LOOKUP_6693001 = 6693001;

    //入网证打印模板目录
    int LOOKUP_2007001 = 2007001;
    //入网证空白模板名称
    int LOOKUP_2007002 = 2007002;
    //入网证打印机配置
    int LOOKUP_2008 = 2008;
    String LOOKUP_2010 = "2010";
    String LOOKUP_2011 = "2011";

    //资源明细状态
    int LOOKUP_7007 = 7007;


    //人事相关
    int LOOKUP_6694 = 6694;
    String LOOKUP_6694001 = "6694001";
    String LOOKUP_6694002 = "6694002";

    /**
     * T0001：股份12位电脑号
     *
     * T0002：股份8位工号 或 子公司/合作方公司 10位工号
     *
     * T0003：股份14位工号
     */
    String ID_TYPE_T0002 = "T0002";
    /**
     * B0000:ID信息
     *
     * B0001:个人信息
     *
     * B0002:人事基本信息
     *
     * B0003:组织信息
     *
     * B0004:岗位信息
     *
     * B0005:国籍信息
     *
     * B0010：标签数据 （是否脱敏、是否加密）
     *
     * B0012：证件信息
     *
     * B0013：联系方式
     */
    String BLOCK_TYPE_B0001 = "B0001";
    String CONTENT_TYPE="Content-Type";

    //状态 0:待审批,1:已审批,2:已拒绝
    String APPROVER_STATUS_ZERO="待审批";
    String APPROVER_STATUS_ONE="已审批";
    String APPROVER_STATUS_TWO="已拒绝";

    enum BoardScrapBillNode {
        /**
         * 材料技术质量工程师
         */
        PRODUCTION_QUALITY_ENGINEER("qualitier", "材料技术质量工程师"),
        /**
         * 供应链财经部财务
         */
        SUPPLY_CHAIN_FINANCE_DEPARTMENT_FINANCE("financer", "供应链财经部财务"),
        /**
         * 生产部部长
         */
        MINISTER_OF_PRODUCTION_QUALITY("qualitiyMinister", "生产部部长"),
        /**
         * 制造总经理
         */
        DEPUTY_GENERAL_MANAGER_OF_PRODUCTION("productManager", "制造总经理"),
        /**
         * 生产部部长
         */
        HEAD_OF_PARTS_PRODUCTION_DEPARTMENT("productMinister", "生产部部长"),
        /**
         * 车间主任
         */
        WORKSHOP_DIRECTOR("workshoper", "车间主任");

        /**
         * 节点编码
         */
        private  String code;
        /**
         * 节点名称
         */
        private  String name;

        private BoardScrapBillNode(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }

    enum ProductionFeedNode {
        /**
         * 审批人
         */
        PRODUCTION_QUALITY_ENGINEER("approver", "审批人")
        ;

        /**
         * 节点编码
         */
        private  String code;
        /**
         * 节点名称
         */
        private  String name;

        private ProductionFeedNode(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }

    String APPROVAL_LOG_NAME = "审批中心回调处理异常";

    String MAILBOX_SUFFIX = "@zte.com.cn";

    String SEND_EMAIL_NAME = "技改单发送邮件失败";

	String COLON = ";";

    String[] CERT_NAME=new String[]{"进网试用","进网许可"};
    interface RESOURCE_WARNING_CONFIG {
        String FILENAME = "资源预警规则维护.xls";
        String[] TITLE = new String[]{
                "预警维度", "资源编号", "设备型号", "水位值", "有效期预警期限(天)", "创建人", "创建时间", "最后更新人","最后更新时间"
        };
        String[] PROPS = new String[]{
                "warningType", "resourceNo", "deviceType", "lowWaterLevel", "expiryDateBefore", "createBy","createDate","lastUpdatedBy","lastUpdatedDate"
        };
    }
    String[] SN_TYPE = {"SN", "MAC"};
    String RESOURCE_USE_INFO_TEMPLATE_NAME = "资源使用导入模板.xlsx";
    String[] RESOURCE_USE_INFO_TEMPLATE_HEAD = {"序号类型", "条码", "资源号"};

    String NAL_EMAIL_TITLE_ZH = "入网数据导出 邮件通知";

    String DEFAULT_EXPORT_CONTROL_METHOD = "许可";
}
