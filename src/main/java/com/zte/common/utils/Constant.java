package com.zte.common.utils;


import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 常量
 *
 * <AUTHOR>
 */
public interface Constant {
    String TASK_STATUS = "待发放";
    String LOOK_UP_TYPE_ALIBABA = "1004115";
    String ISCP_ERROR = "采购接口报错： ";
    String LOOK_UP_TYPE = "lookupType";
    String ECCN_ERROR = "ECCN接口报错： ";
    String PRODPLAN_ID_PREFIX = "ZW深圳CPE";
    String CONCAT_PRODUCT_TASK_STR = "17206382026";
    String DELIVERY_ADDR = "SMT配送";
    String WET_LEVEL = "一 级";
    String WARM_REMINDER = "温馨提醒：本邮件为系统自动发送，请不要回复本邮件。";
    String WRONG_ANSWER = "很抱歉，暂时没有找到您咨询的相关知识";
    String MORE_SEE = "更多信息,";
    String MORE_SEE_CLICK = "点此链接";
    String X_TIMESTAMP = "x-timestamp";
    String SIGN_BLANK = "Sign ";
    String STR_SUCCESS = "SUCCESS";
    long LONG_1000 = 1000L;
    String SHA_512 = "SHA-512";
    String UTF_8 = "UTF-8";

    String AOI = "AOI";
    String AOI_REPAIR = "AOI_REPAIR";

    String SCHEDULE_INFO = "SCHEDULE_INFO";
    String CONFIRMATION_INFO = "CONFIRMATION_INFO";
    String ALIBABA = "alibaba";

    String ALIBABA_NAME = "阿里巴巴（中国）有限公司";
    String PARTCODE_TYPE_ZB = "主板";
    String OSS_FILE_UPLOAD = "OSS_FILE_UPLOAD";
    String UPLOAD_COMPLETE_MACHINE_TESTING_DATA = "UPLOAD_TESTING_DATA";
    String STATION_ANALYSIS_RESULTS = "STATION_ANALYSIS_RESULTS";
    String TICKET_CLOSED = "50";
    String ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE = "ZTEiMES-ByteDance-ServerQualityCode";
    String ZTEIMES_MEI_TUAN_QUALITYCODE = "ZTEiMES-Meituan-qualityCode";
    String ZTE_IMES_TENCENT_FORWARD_QUERY = "ZTEiMes-Tencent-ForwardQuery";
    String ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA = "ZTEiMes-Tencent-QueryMPTTestData";

    String ZTEIMES_ALIBABA_QUALITYCODE = "ZTEiMES-Alibaba-QualityGenerateQualitycode";
    String ZTEIMES_ALIBABA_TYPE = "MCT";
    String ZTEIMES_CHINATELECOM_GETMADEIN = "ZTEiMES-MIIT-GetMadeIn";
    /**
     * 新型进网许可标志对应关系上传
     */
    String ZTEIMES_CHINATELECOM_UPLODAPRODUCTINFO = "ZTEiMES-MIIT-UplodaProductInfo";
    /**
     * 查询新型进网许可标志登记结果
     */
    String ZTEIMES_MIIT_SELECTPRODUCTINFO = "ZTEiMES-MIIT-SelectProductInfo";
    String ZTEIMES_CHINATELECOM_SELECTPRODUCTINFO = "ZTEiMES-ChinaTelecom-SelectProductInfo";

    //新型进网许可标志订制申请接口
    String ZTEIMES_MIIT_APPLICATION = "ZTEiMES-MIIT-Application";
    //下载新型进网许可标志扰码数据
    String ZTEIMES_MIIT_DOWNLOADDATA = "ZTEiMES-MIIT-DownloadData";
    String ZTEIMES_MIIT_GETLICENCENO = "ZTEiMES-MIIT-GetLicenceNO";

    // AOI数据推送B2B对应messageType
    String MESSAGE_TYPE_AOI_DATA_PUSH = "ZTEiMES-Alibaba-BoardBoardproductinfo";
    // AOI对应维修数据推送B2B对应messageType
    String MESSAGE_TYPE_AOI_REPAIR_DATA_PUSH = "ZTEiMES-Alibaba-CaptureErrorProcess";
    // 排产信息推送
    String MESSAGE_TYPE_MANUFACTURE_ORDER_SCHEDULE = "ZTEiMES-Alibaba-ManufactureOrderSchedule";
    String MESSAGE_TYPE_SYNC_CUSTOMER_INVENTORY_POST = "ZTEiMES-Alibaba-SyncCustomerInventoryPost";
    String ZT_EI_MES_ALIBABA_CLOSE_MANUFACTURE_ORDER = "ZTEiMES-Alibaba-CloseManufactureOrder";

    String MESSAGE_TYPE_PRODUCT_SN_REPORT = "ZTEiMES-Alibaba-ProductSnReport";
    String MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE = "ZTEiMES-Alibaba-FinishedProductStorage";


    //整机测试数据
    String MESSAGE_TYPE_TEST_DATA_OF_THE_WHOLE_MACHINE = "ZTEiMES-Alibaba-MctServerproductinfoPublic";
    //站位分析结果
    String MESSAGE_TYPE_STATION_ANALYSIS_RESULTS = "ZTEiMES-Alibaba-CaptureErrorProcess";
    //OSS测试日志文件
    String MESSAGE_TYPE_OSS_TEST_LOG_FILE = "ZTEiMES-Alibaba-QualitycodeFilePubilc";

    String SERVER_SNS = "serverSns";
    String SN = "sn";
    String TYPE = "type";
    String SERVER_LICENCE = "Licence";
    String BIZ_CODE_UPPER = "BizCode";
    String E100 = "E100";
    CharSequence FORMAT = "\"[{";
    String REDIS_THIS_PART_IS_OPERATING = "当前辅料正在操作";
    String JSON_CODE = "code";
    String JSON_BO = "bo";

    String JSON_LIST = "list";

    String JSON_ROWS = "rows";
    String STR_TITLE = "title";
    String STR_CONTENT = "content";
    String LOOK_UP_CODE_7580001 = "7580001";
    String YEAR_MONTH_DAY = "yyyy年MM月dd日";
    String B2B_ALARM_TITLE = "B2B回调异常";
    String ABNORMAL_HANDLING_OF_TECHNICAL_RENOVATION_ORDERS = "以下技改单同步处理异常,请关注";
    String MAIL_ADDRESS = "<EMAIL>";
    String RESOURCE_RECYCLE_EXPORT = "资源回收导出";
    String RECYCLE_FILE_NAME = "回收列表_%s.xlsx";
    String FUNCTION_NAME = "个参数据导出";
    String ENVIRONMENT = "环境:";
    String ALARM = "告警:";
    String CONTENT = "内容:";
    String ALARM_TITLE_TYPE_ONE = "单板报废审批回调异常监控";
    String ALARM_TITLE_TYPE_TWO = "生产补料单审批回调异常监控";
    String ALARM_TITLE_TYPE_THREE = "工装条码报废审批回调异常监控";
    String ALARM_TITLE_TYPE_FOUR = "辅料调拨审批回调异常监控";
    String[] WRC_EXPORT_EX_FIELD = new String[]{"processNode", "processCapacity", "processCycle"};

    String WRC_EXPORT_FILE = "车间资源能力导出";
    String WRC_IMPORT_TEMPLATE = "车间资源能力导入模板";
    String APPLICATION_EXCEL = "application/vnd.ms-excel";
    String CONTENT_DISPOSITION = "Content-Disposition";
    String ATTACHMENT_FILENAME = "attachment;filename=";
    String WRC_IMPORT_RESULT = "车间资源能力导入校验结果";
    String DISPATCH_MANUFACTURE_ORDER_TITLE = "工单发放阿里回调异常告警";
    String ORDER_SCHEDULE_WARN_TITLE = "排产信息阿里回调异常告警";
    String STD_MODEL_PUSH_ERROR_TITLE = "标模任务推送失败告警";
    String STD_MODEL_RCV_BILL_ERROR_TITLE = "箱包&SN关系上传告警";
    String MAIN_EQUIPMENT = "主设备";
    String LINE_CN = "线";
    int TYPE_8880 = 8880;
    int TYPE_8881 = 8881;
    int TYPE_8883 = 8883;
    int TYPE_8884 = 8884;

    int BATCH_SIZE_499 = 499;

    String DOLLAR_FLAG = "$";
    String LAST_NUMBER_REGEX = "\\d+$";
    String ENTER = "\\n";
    String PIPELINE = "\\|";
    String GENERAL_FIELD = "generalField";
    String REGEX_PIPELINE = "^\\||\\|$";
    int BATCH_SIZE_10000 = 10000;
    String RESOURCE_SCRAP_NUMBER = "报废数量";
    String CONTENT_TYPE = "text/plain";
    String FILE = "file";
    int SCRAP_EXPIRE_TIME = 60;
    int INT_15 = 15;
    String MDS_PROGRAM_IN = "MDS_PROGRAM_IN";
    String MDS_PROGRAM_IN_ERROR = "MDS触发iMES前加工数据更新错误";
    String XP1_CODE = "XP1";
    String XP1_NAME = "写片室";
    String XP_NAME = "写片";
    String LOCATION_NO_ITEM_CODE = "料单%s下写片位号%s不存在或没找到对应物料代码,请人工处理";
    String LOCATION_DUPLICATE_ITEM_CODE = "料单%s下写片位号%s存在多个物料代码,请人工处理";
    String LOCATION_CRAFT_SECTION_NOT_SMT = "料单%s下写片位号%s不属于SMT物料,请人工处理";
    String MDS_PROGRAM_XPING = "当前料单存在正在执行的MDS触发iMES更新前加工任务，请稍等重试";
    String LOCATION_SAME_CRAFT_ITME_CODE_NOT_SAME = "料单%s写片位号%s在MDS是同代码同程序,在料单中是不同程序,请人工处理";
    String LOCATION_REPEAT = "料单%s下写片位号%s:IMES维护写片数据与MDS启用数据不一致，请人工处理";
    String DECIMAL_FORMAT_DOUBLE = "0.00%";
    String WORKSHOP = "WORKSHOP";
    String LINE_EN = "LINE";
    String LOOKUP_TYPE_7504 = "7504";
    Integer LOOKUP_CODE_7504001 = 7504001;
    Integer LOOKUP_CODE_2870001 = 2870001;
    Integer LOOKUP_CODE_2870002 = 2870002;
    String INONE_ERROR = "INONE接口报错：";

    interface WrcImportError {
        String COLUMN_NOT_FILLED = "带*号的列未填";
        String POSITIVE_NUMBER_ERR = "四列产能字段必须大于0";
        String NATURAL_NUMBER_ERR = "工序周期、偏置时间不能小于0";
        String MODEL_ERR = "机型不存在";
        String PLAN_GROUP_ERR = "计划组不存在";
        String EXIST_RECORD = "已存在相同生产单位工单类型计划组机型记录";
        String PROCESS_OVER = "相同生产单位工单类型计划组机型的工艺节点最多10组";
        String NUMBER_ERR = "数值列存在错误";
        String UNIT_ERR = "生产单位不存在";
        String TYPE_ERR = "工单类别不存在";
        String PROCESS_ERR = "工序不存在";
        String KEY_PROCESS_ERR = "关键工序不存在";
        String SUMMARY_HAS_ERROR = "汇总机型、汇总机型额定产能，必须同时为空或同时填写";
    }

    String INIT = "初始化";
    String PRODUCTION_UNIT = "生产单位";
    String WORK_ORDER_CATEGORY = "工单类别";
    String MODEL = "机型";

    String CATEGORY_EXPORT = "产能日历导出";

    String[] CATEGORY_EXPORT_HEAD = new String[]{
            "生产单位", "工单类别", "计划组", "汇总机型",
            "机型", "汇总机型线体", "机型线体", "汇总机型额定产能",
            "机型额定产能", "制造周期(天)", "动态合计"
    };
    String[] CATEGORY_EXPORT_FIELD = new String[]{
            "production_unit", "work_order_category", "plan_group_name", "summary_model",
            "model_name", "summary_model_line", "model_line", "summary_model_line_capacity",
            "model_rated_capacity", "manufacturing_cycle", "total_actual_capacity"
    };

    interface CalendarHeaderField {
        String PRODUCTION_UNIT = "production_unit";
        String WORK_ORDER_CATEGORY = "work_order_category";
        String PLAN_GROUP_NAME = "plan_group_name";
        String PLAN_GROUP_ID = "plan_group_id";
        String MODEL_NAME = "model_name";
        String MODEL_NO = "model_no";
        String MODEL_LINE = "model_line";
        String MODEL_RATED_CAPACITY = "model_rated_capacity";
        String SUMMARY_MODEL = "summary_model";
        String SUMMARY_MODEL_LINE = "summary_model_line";
        String SUMMARY_MODEL_LINE_CAPACITY = "summary_model_line_capacity";
        String MANUFACTURING_CYCLE = "manufacturing_cycle";
        String TOTAL_ACTUAL_CAPACITY = "total_actual_capacity";
    }

    interface PUSH_B2B_STATUS {
        String PN = "PN";
        String PY = "PY";
        String CN = "CN";
        String CY = "CY";
        String CE = "CE";
    }

    interface  PUSH_TYPE {
        String API = "API";
        String KAFKA = "KAFKA";
    }

    interface PUSH_STATUS {
        // 待推送
        int NOT_PUSHED = 0;
        // 已推送未回调
        int PUSHED_NOT_CALLBACK = 1;
        // 已推送回调成功
        int PUSHED_AND_CALLBACK = 2;
        // 等待上工序推进，中间状态
        int PUSHED_AND_NEXT = 3;

        /**
         * 数据校验失败或推送异常
         */
        int DATA_CHECK_OR_PUSH_FAIL = 8;
        // 回调结果异常
        int CALLBACK_ERROR = 9;
    }

    String LOOKUP_CODE_1003011002 = "1003011002";
    int INT_52 = 52;
    Integer INTEGER_15 = 15;
    Long SECOND_30 = 30L * 1000L;
    String SYNCHRONIZE_SPM_DATA_EMAIL_TITLE = "同步SPM技改数据邮件提示";
    String SYNCHRONIZE_FINISH_TECH_DATA_EMAIL_TITLE = "同步SPM技改完成数据邮件提示";
    String SYNCHRONIZE_LAST_DAY_FINISH_TECH_EMAIL_TITLE = "同步最近一天完成技改的SPM数据，邮件提示";
    String SYNCHRONIZE_SPM_SN_DATA_EMAIL_TITLE = "同步SPM技改条码数据邮件提示";
    String SYNCHRONIZE_HEAD_TECH_DATA_EMAIL_TITLE = "同步SPM特殊技改数据邮件提示";
    String SYNCHRONIZE_SPECIAL_SN_TECH_DATA_EMAIL_TITLE = "同步SPM特殊技改条码数据邮件提示";
    String TECH_SYNC_CHG_REG_NO = "技改单号：";
    String TECH_SYNC_PROD_ID = "，批次号：";
    String TECH_SYNC_SN = "，条码：";
    String TECH_SYNC_FAIL = "，失败原因：";
    String TASK_HAS_TRANSFER_ORDER = "已产生套料单或调拨单";
    String STR_WRAP = "\n";
    String DEFAULT_SYNCHRONIZE_TIME = "1900-01-01 00:00:00";
    String LOOKUP_CODE_1003011001 = "1003011001";

    String X_EMP_NO_SMALL = "x-emp-no";
    int INT_3600 = 3600;
    int INT_1800 = 1800;

    /**
     * 下划线，值_
     */
    // 1-已产生调拨单，2-已产生套料单,3-技改单
    String INFOR_EXE_STATUS_1 = "1";
    String INFOR_EXE_STATUS_1_MEANING = "已产生调拨单";
    String INFOR_EXE_STATUS_2 = "2";
    String INFOR_EXE_STATUS_2_MEANING = "已产生套料单";
    String INFOR_EXE_STATUS_3 = "3";
    String INFOR_EXE_STATUS_3_MEANING = "技改单";

    int INT_50 = 50;

    String BOARD_SENIOR_DAILY_REPORT_LOCK_KEY = "PS:getBoardSeniorDailyReportInfo";

    String SENIOR_DAILY_LOOKUP_TYPE = "1112";

    String LOOKUP_TYPE_7006 = "7006";

    String LOOKUP_TYPE_6669 = "6669";

    String SENIOR_DAILY_SPM_LOOKUP_CODE = "1112003";

    String FIELD_LOOKUP_TYPES = "lookupTypes";

    String SZ_FACTORY = "53";

    String HY_FACTORY = "55";

    String BO = "bo";

    int MAX_ROWS_NUM = 5001;

    int FIXED_VALUE = 3;
    String X_EMP_NO = "x-emp-no";

    String ZH_CN = "zh_CN";

    String DATA_IS_EMPTY = "传入数据为空";

    String DATA_ONE_EMPTY = "InCode||InFactoryName||ContractNumber";

    String UNDER_LINE = "_";
    String BIZ_CODE = "E101";
    String LINE = "-";

    String STR_V = "-V";

    String FLAG_Y = "Y";

    String FLAG_N = "N";

    String CODE = "code";
    String MSG = "msg";

    int ONE_YEAR = -12;
    int TWO_YEAR = -24;

    String STR_ONE = "1";
    String STR_TWO = "2";
    String STR_SEVEN = "7";
    String STR_95 = "95";

    String STRING_FOUR = "4";
    String STRING_FIVE = "5";

    String STR_THREE = "3";
    // 外协工厂编码
    String FACTORY_CODE = "factoryCode";

    String OUT_SOURCE = "外协";

    String COMMA = ",";

    String DON = "、";

    //位号拆分用，按逗号，原点，顿号拆分
    String LOC_SPLIT_SYMBOL = "\\.|\\,|\\、";

    String CHINESE_COMMA = "，";

    String ENCODE_UTF_8 = "utf-8";
    String SINGLE_QUOTE = "'";
    String DOUBLE_QUOTE = "''";
    String OR = "||";

    int FOUR_ZERO_ZERO = 400;
    int NUMBER_900 = 900;

    int NUMBER_100 = 100;

    String KEY_TAB = " ";

    String NUMBERONE = "#1";

    String IMES = "iMes";

    String AS = "AS";

    String ZH = "zh";

    String EN = "en";
    String PIPE = "|";
    String SHA_256 = "SHA-256";

    String EQUALSIGN = "=";

    String SIP = "sip:";

    String NUMBER_33 = "33";

    String chatBotId = "sip:<EMAIL>";

    String TERMINAL_TYPE = "Web@Desktop_999_ID1004";
    String AUTHKEY = "iMes";

    String senduri = "{senduri}";
    int NUMBER_200 = 200;
    int TWO_ZERO_ONE = 201;
    String TO = "?to=";
    String MSGID = "&msgid=";
    String MSGTYPE = "&msgtype=";

    String TEXT = "text";
    String SESSIONTYPE = "&sessionType=";
    String TO_SENDER = "&to_sender=";

    int FOUR_ZERO_FOUR = 404;
    String LOOKUP_TYPE_2035 = "2035";
    String A_TYPE = "A";
    String B_TYPE = "B";

    String STR_G = "G";
    String SOURCE_SYS = "3";

    String NUMBER_TWO = "2";

    String NUMBER_ONE = "1";

    String LEVEL_CODE = "0";

    String WE_STATUS = "已提交";

    String NUM_REGULAR = "[^0-9]";


    char CHAR_Z = 'Z';
    String[] ABC = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    String STATUS_FICTION = "拟制中";

    String SUCCESS = "0000";

    String BUSINESSERROR = "0005";


    String SYSTEM = "system";
    String SYSTEM_040 = "system040";

    String TASK_STATUS_TOGRANT = "待发放";

    String TASK_STATUS_GRANTED = "已发放";

    String YCBB = "YCBB";

    /**
     * 资源申请
     */
    String SPECIAL_USER = "specialUser";
    String GENERAL_USER = "generalUser";
    String APPLY_BILL_NO_PREFIX = "BQCS";
    String DATE_FORMATE_DAY = "yyyyMMdd";
    String APPLY_BILL_NO_REDIS_KEY = "applyApplication:billNo:%s";
    String APPLY_APPLICATION_LOCK_KEY = "applyApplication:lock:%s";
    String SYNC_TECH_LOCK_KEY = "LOCK:SYNC_TECH:%s";

    String SYNC_DELIVER_LOCK_KEY = "LOCK:SYNC_DELIVER:%s";
    String HEX_STRING_PREFIX_08 = "%08x";
    String HEX_STRING_PREFIX_12 = "%012x";
    String GPON_SN_PREFIX = "ZTEG";

    String LOCK_SUFFIX = "_lock";

    /**
     * 长沙工厂ID
     */
    String FACTORY_ID_CS = "52";

    /**
     * 深圳工厂ID
     */
    String FACTORY_ID_SZ = "53";
    /**
     * 河源工厂ID
     */
    String FACTORY_ID_HY = "55";
    /**
     * 南京工厂ID
     */
    String FACTORY_ID_NJ = "58";
    /**
     * 西安工厂ID
     */
    String FACTORY_ID_XA = "56";

    /**
     * 中心工厂ID
     */
    String FACTORY_ID_CENTER = "51";

    /**
     * 长沙工厂ReelId前缀
     */
    String CS_PKCODE_PREFIX = "PKCS";

    /**
     * PKCode长度
     */
    int PK_CODE_LENGTH = 16;
    String LOOKUP_CODE_6668 = "6668";
    String LOOKUP_CODE_7007 = "7007";

    String LOOKUP_CODE_200701 = "200701";
    String LOOKUP_CODE_7002 = "7002";
    String LOOK_UP_CODE_1245 = "1245";

    String LOOK_UP_CODE_6719 = "6719";

    String LOOK_UP_CODE_7307 = "7307";

    String LOOK_UP_CODE_6817 = "6817";

    String LOOK_UP_CODE_10000 = "10000";
    String LOOK_UP_CODE_1004105 = "1004105";
    String LOOK_UP_CODE_1004106 = "1004106";
    String LOOK_UP_CODE_1004107 = "1004107";
    String LOOK_UP_CODE_1004108 = "1004108";
    String LOOK_UP_CODE_1004109 = "1004109";
    // 同步任务定时时间点
    String LOOK_UP_CODE_1004113 = "1004113";

    String LOOK_UP_CODE_1004115 = "1004115";
    String LOOK_UP_CODE_1004116 = "1004116";
    String LOOK_UP_CODE_1004117 = "1004117";
    String LOOK_UP_CODE_1004118 = "1004118";

    String LOOK_UP_CODE_1004119 = "1004119";
    String LOOK_UP_CODE_1004116001 = "1004116001";

    String LOOK_UP_CODE_2025060301 = "2025060301";
    String LOOK_UP_CODE_2025060302 = "2025060302";
    String LOOK_UP_CODE_2025060303 = "2025060303";
    String LOOK_UP_CODE_2025060311 = "2025060311";
    String LOOK_UP_CODE_2025060312 = "2025060312";
    String LOOK_UP_CODE_2025060313 = "2025060313";

    String LOOK_UP_CODE_6672 = "6672";

    String LOOK_UP_CODE_6672005 = "6672005";

    String LOOK_UP_CODE_1269 = "1269";

    Integer LOOK_UP_CODE_6921 = 6921;


    String TECH_FILE_TYPE_SET = ".xlsx.xls.pdf.doc.docx.png.jpeg.jpg.bmp";

    String LOOK_UP_CODE_4001001 = "4001001";

    String LOOK_UP_TYPE_6908 = "6908";
    String LOOK_UP_TYPE_6909 = "6909";
    String LOOK_UP_TYPE_6912 = "6912";
    /**
     * 任务设备类型和设备大类对应关系数据字典
     */
    String LOOKUP_TYPE_1291 = "1291";
    /**
     * 组织id数据字典
     */
    String LOOKUP_TYPE_2222 = "2222";

    /**
     * 组织id数据字典
     */
    String LOOKUP_TYPE_1245 = "1245";

    /**
     * 组织id数据字典(2222中存在工厂id一样，但组织id不一样的值)
     */
    String LOOKUP_TYPE_2221 = "2221";
    /**
     * 主工序和bimu对应关系数字典
     */
    String LOOKUP_TYPE_2341 = "2341";

    /**
     * 仓库及货位(阿里)数据字典
     */
    String LOOKUP_TYPE_7502 = "7502";
    String STR_DEFECTIVE_PRODUCT_WAREHOUSE = "不良品库";
    String LOOKUP_CODE_BAD_WAREHOUSE = "7502003";
    // 成品入库需要上传阿里的物料标记配置
    String LOOK_UP_TYPE_7508 = "7508";
    /**
     * 客户名称
     */
    String STR_7300 = "7300";
    Integer INT_7306 = 7306;

    /**
     * 创建合同任务表征数据redis_key
     */
    String REDIS_KEY_CONTRACT_CHARA_INFO_CREATE = "redis_key_contract_chara_info_create";
    // 单板绑定设置保存
    String REDIS_KEY_PROD_BIND_SETTING = "REDIS_KEY_PROD_BIND_SETTING";
    /**
     * 箱码生成redis_key
     */
    String CREATE_LPN_LOCK = "CREATE_LPN_LOCK";

    /**
     * 入网许可证redis_key
     */
    String NETWORK_LICENSE_SIGN_LOCK = "NETWORK_LICENSE_SIGN_LOCK:";

    /**
     * 入网许可证导入redis_key，全局唯一锁
     */
    String NETWORK_LICENSE_SIGN_IMPORT_LOCK = "NETWORK_LICENSE_SIGN_IMPORT_LOCK";

    /**
     * 入网许使用记录导出redis_key
     */
    String NETWORK_LICENSE_USE_EXPORT_LOCK = "NETWORK_LICENSE_USE_EXPORT_LOCK:";
    String REDIS_KEY_UPLOAD_PROGRAM_FILE = "redis_key_upload_program_file";

    String CHARSET_UTF_8 = "text/plain;charset=utf-8";
    /**
     * 创建单板指令周期信息数据redis_key
     */
    String REDIS_KEY_BOARD_INSTRUCTION_CYCLE_INFO_CREATE_SPM = "redis_key_board_instruction_cycle_info_create_spm";

    String REDIS_KEY_BOARD_CODE_CYCLE_INFO_CREATE_SPM = "redis_key_board_code_cycle_info_create_spm";

    String AND = "&";
    String SPM = "SPM";

    String STRING_EMPTY = "";
    String STRING_ZERO = "0";
    String STRING_ONE = "1";
    String STRING_TWO = "2";
    String STRING_THREE = "3";
    String STRING_EIGHT = "8";
    String STRING_NINE = "9";
    String STRING_TWELVE = "12";
    String STRING_30 = "30";

    String REEL_ID_HEAD_01 = "ZTE01";
    String REEL_ID_HEAD_02 = "ZTE02";

    int MAX_PRODUCT_MAC_NUM = 2000000;

    int NUM_ZERO = 0;
    long LONG_ZERO = 0L;

    int MAX_IMPORT_ROWS = 5000;

    int BATCH_SIZE = 100;

    String ZTE_MAILBOX_DOMAIN_NAME = "@zte.com.cn";
    String ZTE_EMAIL_REGEX = "^[A-Za-z\\d]+([-_.][A-Za-z\\d]+)*@zte.com.cn$";
    String MAC_REGEX = "^[0-9A-F]{2}(?:[\\:\\-][0-9A-F]{2}){5}$";
    String URL_REGEX = "^[a-zA-Z0-9.\\-/]+$";

    // 物料代码前缀 长度
    int ITEM_PREFIX_LENGTH = 12;
    // 产品类型 长度
    int PROD_TYPE_LENGTH = 8;

    int BATCH_SIZE_100 = 100;

    int BATCH_SIZE_200 = 200;

    int BATCH_SIZE_500 = 500;

    int BATCH_SIZE_400 = 400;

    int BATCH_SIZE_1000 = 1000;

    String TEST_USER = "testUser";

    String XLS = "xls";

    String XLSX = "xlsx";

    String TXT = "txt";

    String CSV = "csv";

    String BUS_START_FLAG = "S";

    String BUS_ARRIVE_FLAG = "A";

    String BUS_END_FLAG = "E";

    String BUS_HAND_INPUT_FLAG = "I";

    String A_STATUS = "A";

    String C_STATUS = "C";

    String Y_STATUS = "Y";

    String N_STATUS = "N";

    String U_STATUS = "U";

    String OK_STATUS = "OK";

    String E_STATUS = "E";

    String R_STATUS = "R";

    String LIST_BILL = "listBill";

    String LIST_MAC = "listMac";

    String LIST_DSN = "listDsn";

    String BRACKET = "[]";

    String HORIZON = "-";

    String ADD = "+";
    String ADD_STR = "add";

    String VIRGULE = "/";

    String GET_PAGE = "getPage";

    String START_ROW = "startRow";

    String END_ROW = "endRow";

    String LIST_BAR = "listBar";

    String CODE_LIST = "codelist";

    String MAC1 = "mac1";

    String ID_NAME = "id";

    String TRUE = "true";

    String FALSE = "false";

    String HORIZON_DATE_FORMAT = "yyyy-MM-dd";
    String DATE_FORMAT = "yyyyMMdd";

    String VIRGULE_DATE_FORMAT = "yyyy/MM/dd";

    String SIMPLE_DATE_FORMAT = "yyyyMMddHHmmss";

    String STRING_TYPE = "String";

    String INT_TYPR = "int";

    String INTEGER_TYPE = "Integer";

    String LONG_TYPE = "long";

    String OBJECT_LONG_TYPE = "Long";

    String BOOLEAN_TYPE = "bealean";

    String LIST_STRING = "list";

    String OBJECT_BOOLEAN_TYPE = "Bealean";

    String DATE_TYPE = "Date";

    String BYTE_TYPE = "byte[]";

    String FIREFOX = "Firefox";

    String CHROME = "Chrome";

    String RUN_START_TIME = "RUN_START_TIME";

    String RUN_END_TIME = "RUN_END_TIME";

    String ARRIVAL_START_TIME = "ARRIVAL_START_TIME";

    String ARRIVAL_END_TIME = "ARRIVAL_END_TIME";

    String BUS_RUN_DATE = "BUS_RUN_DATE";

    /**
     * 过期时间1小时
     */
    long EXPIRE_TIME_HOUR = 60 * 60L;

    String DATA_ACCESS = "DATA_ACCESS";

    String ID_TYPE = "T0002";

    char[] HEXADECIMAL = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D',
            'E', 'F'};

    String NULL = "null";

    int NUM_TWO = 2;

    char CHAR_ZERO = '0';

    int ONE_HUNDRED_AND_TWENTY = 120;

    String COMSUP_PRODUCE = "comSupProduce";
    String ENSUP_PRODUCE = "enSupProduce";
    String SNRID_SUPPRODUCE = "snRidSupProduce";
    String IQCSUP_PRODUCE = "iqcSupProduce";
    String SALSUP_PRODUCE = "salSupProduce";
    String SALCSUP_PRODUCE = "salCSupProduce";


    String STR_UNDEFINED = "undefined";

    int CAD_FILE_LISTSIZE = 11;
    String YSE_FLAG = "YES";
    String SMT_A_FLAG = "SMT-A";
    String SMT_B_FLAG = "SMT-B";
    String SMT_A_B = "SMT-A,SMT-B";
    String SMT_FLAG = "SMT";
    String DIP_FLAG = "DIP";

    int MINUS_ONE = -1;
    Integer ONE_MINUTES = 60;
    String STYLE = "style";
    String SUPPLIER_NAME = "supplierName";
    String PRODUCT_DATE = "productDate";
    String BG_BRAND_NO = "bgBrandNo";
    char CHAR_COMMA = ',';
    String STRING_COMMA = "','";
    String STR_BO = "bo";
    String STR_CODE = "code";
    String STR_MSG = "msg";

    String PRODUCT = "product";
    String BEFORE = "Before";

    String OPERATE_GRANT = "grant";
    String OPERATE_CANCEL = "cancel";

    int INT_58 = 58;
    int INT_59 = 59;

    String BOM_LEVEL = "料单级";
    String ITEM_LEVEL = "物料级";
    String SMT_LEVEL = "是否贴片";
    String SMT_DELIVERY = "SMT配送";
    String DIP_DELIVERY = "装焊配送";
    String VERSION_STR = "V1.0.";
    String SPACE_STR = " ";
    String DEL_STR = ".";

    String[] EXCEL_TITLE_SUPPLIER_PRODUCE_INFO_QUERY = new String[]{
            "外协工厂名称", "ZTE Reel ID", "外协Reel ID", "ZTE物料条码", "物料数量", "物料代码", "物料名称", "物料周期", "整机SN/EN", "单板SN",
            "MAC", "GPON SN", "D-SN", "设备标识", "子部件代码", "产品型号", "合同号", "任务号", "用户地址", "箱号", "发货时间", "单板工单号", "整机工单号", "线体",
            "上料时间", "SN扫描时间", "上料数量"};

    String[] EXCEL_PROPS_SUPPLIER_PRODUCE_INFO_QUERY = new String[]{
            "inFactoryName", "zteReelId", "outReelId",
            "itemCode", "reelIdQty", "itemNo", "itemName", "itemCycle", "completeSn",
            "bomSn", "mac", "gponSn", "dSn", "equipmrntName", "prodItem",
            "prodType", "contractNumber", "submachineNumber", "userAddress", "billNumber", "actionDate", "dbBillNo", "zjBillNo", "lineName", "feedTime", "scanTime", "feedQty"};
    String ID_FLAG = "Id";
    String FLAG_T = "T";
    String FLAG_Z = "Z";
    /**
     * 电子签名设置key
     */
    String LOOKUP_TYPE_KEY_SIGN = "2000";

    /**
     * 电子签名设置加密盐值key
     */
    String LOOKUP_TYPE_VALUE_KEY_SIGN_SALT = "2000001";

    /**
     * 电子签名设置证书 Id值key
     */
    String LOOKUP_TYPE_VALUE_KEY_SIGN_CERT_ID = "2000002";

    /**
     * 电子签名设置证书Id获取地址值key
     */
    String LOOKUP_TYPE_VALUE_KEY_SIGN_GEY_CERT_ID_ADDR = "2000003";

    /**
     * RFID签名设置信息
     */
    String RFID_SIGN_SETTING_INFO = "RFID_SIGN_SETTING_INFO";

    String GLUE_ITEM = "物料代码";
    String GLUE_ITEM_SUP = "物料代码+供应商";
    String GLUE_BARCODE = "条码";

    String GLUE_NORMAL = "常温";
    String GLUE_FREEZING = "冷冻";

    long DAY_TIME = 1000 * 60 * 60 * 24;

    int INT_45 = 45;
    int INT_40 = 40;
    int INT_23 = 23;
    int INT_24 = 24;
    int INT_30 = 30;
    int INT_31 = 31;
    int INT_0 = 0;
    int INT_1 = 1;
    int INT_20 = 20;
    long LONG_1 = 1;
    int INT_2 = 2;
    int INT_3 = 3;
    int INT_4 = 4;
    int INT_5 = 5;
    int INT_6 = 6;
    int INT_7 = 7;
    int INT_8 = 8;
    int INT_12 = 12;
    int INT_16 = 16;
    int INT_17 = 17;
    int INT_18 = 18;
    int INT_10 = 10;
    int INT_90 = 90;
    int INT_100 = 100;
    int INT_240 = 240;
    int INT_500 = 500;
    int INT_800 = 800;
    int INT_1000 = 1000;
    int INT_2000 = 2000;
    int INT_100000 = 100000;
    long INT_1001 = 1001;
    int INT_5000 = 5000;
    long INT_50000 = 50000;
    int INTEGER_50000 = 50000;

    long LONG_ONE_HOUR_MILE_SECOND = 3600000L;

    String STR_0 = "0";

    String STR_20= "20";
    String STR_25= "25";

    double DOUBLE_21 = 21;

    int INT_B1 = -1;
    String STR_B01 = "-01";

    String STR_COMMA = ",|，";

    String DATE_MIN_FORMATE_SHORT = "yyyyMMddHHmm";
    String DATE_TIME_FORMATE_FULL = "yyyy-MM-dd HH:mm:ss";
    String DATE_TIME_FORMATE_FULL_WITHOUT_SPACE = "yyyyMMddHHmmssSSS";

    String PARTY_NOT_EXIST_IN_SYSTEM = "接入方无效，系统中没有此接入方信息。";
    String ABNORMAL_STREAM_SHUT_DOWN = " 关闭流异常   :";
    String ABNORMAL_STREAM = "  流异常    ::";
    String LINE_STR = "行。";
    String ROUTE_ERROR = "路线ERROR";
    String BEGIN_MAC_SAVE_EMPTY = "生产单位*值不能为空";
    String END_MAC_SAVE_EMPTY = "产品*值不能为空";
    String PRODUCE_TYPE_EMPTY = "起始MAC地址*值不能为空";
    String WORK_UNIT_EMPTY = "结束MAC地址*值不能为空";
    String ASSIGN_DATE_EMPTY = "分配时间*值格式不正确或值为空";
    String NOT_EXECUTE = "未执行";
    String EXECUTED = "执行完成";
    String EXECUTED_ERROR = "执行异常";
    String QUIT = "取消";
    String EXECUTING = "执行中";
    String COMMON_MAPPER_PAGE_SQL = "CASE U.STATUS WHEN 'I' THEN '人工输入'  WHEN 'S' THEN  '班车出发'  " +
            "WHEN 'A' THEN   '班车进站' WHEN 'E' THEN '班车到达' END STATUS,CASE U.IS_LATE " +
            "WHEN 'Y' THEN '晚点' WHEN 'N' THEN '准时' WHEN 'U' THEN '未到达' END IS_LATE FROM  ";
    String COMMON_MAPPER_COUNT_SQL = "CASE U.STATUS WHEN 'I' THEN '人工输入'  WHEN 'S' THEN  '班车出发'  " +
            "WHEN 'A' THEN   '班车进站' WHEN 'E' THEN '班车到达' END STATUS,CASE U.IS_LATE WHEN 'Y' " +
            "THEN '晚点' WHEN 'N' THEN '准时' WHEN 'U' THEN '未到达' END IS_LATE,ROWNUM RN FROM  ";
    String TEXT_COMPANY_NAME_ERROR = "班车公司名称已存在，请检查";
    String TEXT_BUS_NAME_ERROR = "班车信息已存在，请检查";
    String EMP_TYPE = "正式员工";
    String TEXT_EMP_CARD_EMPTY_ERROR = "您未填写卡号！";
    String TEXT_CARD_EMPTY_ERROR = "该卡号不存在！";
    String TEXT_INFO_ERROR = "该员工已有此路线关系！";
    String FILE_NAME_ERROR = "([^<>/\\\\\\|:\"\"\\*\\?]+)\\.\\w+$";
    String NAMESPACE_URL = "http://tempuri.org/";

    String TYPE1_STATUS_NAME = "排产,待投,装配,调试,检验,待包,包装,包装完成,入库,暂停,待退库,退库";
    String TYPE2_STATUS_NAME = "发货";

    String ITEM_NOT_RECEIVE = "物料未接收";

    /**
     * 线程池配置
     */
    Integer CORE_POOL_SIZE = 10;
    Integer MAX_POOL_SIZE = 20;
    Integer QUEUE_CAPACITY = 200;
    Integer KEEP_ALIVE_SECONDS = 10;
    Integer AWAIT_TERMINATION_SECONDS = 60;
    String THREAD_NAME_PREFIX = "taskExecutor--";

    /**
     * 编排器接口地址数据字典
     */
    int CHOREOGRAPHER_URL_TYPE = 6000;

    String CHOREOGRAPHER_URL_VUESTR = "/webPage/vuestr";

    String PRODPLAN_ID = "prodplanId";

    String YES = "是";
    String NO = "否";

    String STATUS_STR = "status";
    String E_STR = "E";

    String REELID = "ReelId";


    String SMT_STR = "SMT";
    String DIP_STR = "装焊";
    String FT_STR = "测试";

    /**
     * 物料维护excle 模板下载
     */
    String[] EQUIPMENT_TITLE = {"物料编码", "ABC分类"};
    String EQUIPMENTXINSERT = "物料代码批量维护ABC分类.xls";

    String[] RESOLVE_EXCEL_TITLE = {"itemNo", "abcType", "validResp"};
    String SUCCESS_CODE = "0";
    String SUCCESS_CODE_0000 = "0000";

    String PACKAGE = "PACKAGE";
    String SMTA = "SMT-A";
    String SMTB = "SMT-B";
    String DIP = "DIP";


    /**
     * JD对接优化需求：添加导入功能--模版下载
     */
    public String[] COMMON_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码"
    };

    public String[] CPU_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "CPU频率", "CPU核数", "CPU线程数",
            "CPU类型", "CPU微码版本"
    };

    public String[] MEMORY_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "容量", "内存频率", "内存代数", "内存等级"
    };

    public String[] NETWORK_CARD_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "网卡接口", "网卡端口数量", "网卡端口类型",
            "网卡芯片品牌", "网卡芯片型号", "网卡端口速率"
    };

    public String[] RAID_CARD_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "RAID卡芯片品牌", "RAID卡芯片型号",
            "RAID卡接口", "RAID卡缓存", "RAID卡电池"
    };

    public String[] HARD_DISK_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "硬盘介质", "硬盘尺寸", "硬盘转速",
            "硬盘传输速率", "容量", "硬盘接口"
    };

    public String[] GPU_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "GPU卡显存", "GPU卡平台", "GPU卡接口",
            "容量"
    };

    public String[] MOTHERBOARD_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "主板版本","PCB版本","VR芯片代码","VR芯片功率","EPLD芯片代码"
    };

    public String[] MACHINE_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "服务器高度"
    };

    public String[] POWER_SOURCE_INFO_TITLES = {
            "客户名称", "项目名称", "合作模式", "项目阶段", "项目类型", "类型",
            "内部代码", "内部代码名称", "客户物料型号", "客户部件类型", "客户型号",
            "板码类型", "PN码", "电源功率"
    };

    // 表头英文
    public String[] COMMON_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "checkResult"
    };

    public String[] CPU_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "cpuFrequency", "cpuCores", "cpuThreads",
            "cpuType", "cpuMicrocodeVersion", "checkResult"
    };

    public String[] MEMORY_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "capacity", "frequency", "generation", "grade", "checkResult"
    };

    public String[] NETWORK_CARD_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "networkCardInterface", "portCount", "portType",
            "chipBrand", "chipModel", "portSpeed", "checkResult"
    };

    public String[] RAID_CARD_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "raidCardBrand", "raidCardModel",
            "raidCardInterface", "cacheSize", "battery", "checkResult"
    };

    public String[] HARD_DISK_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "hardDiskMedia", "hardDiskSize", "rotationalSpeed",
            "transferRate", "capacity", "hardDiskInterface", "checkResult"
    };

    public String[] GPU_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "memory", "platform", "gpuInterface",
            "capacity", "checkResult"
    };

    public String[] MOTHERBOARD_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "motherBoardVersion", "pcbVersion","vrChipCode","vrChipPower","epldChipCode","checkResult"
    };

    public String[] MACHINE_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "serverHeight", "checkResult"
    };

    public String[] POWER_SOURCE_INFO_PROPNAMES = {
            "customerName", "projectName", "cooperationMode", "projectPhase", "projType", "projectType",
            "zteCode", "zteCodeName", "customerItemName", "customerComponentType", "customerModel",
            "boardType", "pnCode", "powerSupply", "checkResult"
    };

    String MATERICAL_CODE_MAINTENANCE = "客户物料代码维护";
    String POINT_XLSX = ".xlsx";

    /**
     * 客户物料代码维护 批量导入 校验结果
     */
    String CUSTOMER_NAME_CANNOT_BE_EMPTY = "客户名称不能为空; ";
    String CUSTOMER_NAME_MUST_BE = "客户名称只能为：";
    String COOPERATION_MODE_MUST_BE = "合作模式只能为：";
    String PROJECT_PHASE_MUST_BE = "项目阶段只能为：";
    String PROJECT_TYPE_CANNOT_BE_EMPTY = "项目类型不能为空; ";
    String PROJECT_TYPE_INPUT_ERROR = "项目类型输入错误; ";
    String TYPE_CANNOT_BE_EMPTY = "类型不能为空; ";
    String TYPE_INPUT_ERROR = "类型输入错误; ";
    String BOARD_TYPE_INPUT_ERROR = "板码类型输入错误; ";
    String PLEASE_REMOVE_BOARD_TYPE = "请删除板码类型，类型为自研主板，自研子卡，自研背板以外的不需要输入板码类型; ";
    String CUSTOMER_MODEL_CANNOT_BE_EMPTY = "类型为整机的时候，客户型号不能为空; ";
    String ZTE_CODE_CANNOT_BE_EMPTY = "ZTE代码不能为空; ";
    String CUSTOMER_COMPONENT_TYPE_CANNOT_BE_EMPTY = "客户部件类型不能为空; ";
    String CUSTOMER_COMPONENT_TYPE_NOT_CONSISTENT = "客户部件类型与所选部件类型不一致; ";
    String ZTE_CODE_NOT_EXIST = "ZTE代码不存在; ";
    String ZTE_CODE_OR_NAME_INPUT_ERROR = "ZTE代码或ZTE代码名称输入错误; ";
    String CUSTOMER_ITEMS_EXIST = "当前物料已存在，请检查ZTE代码; ";
    String DUPLICATE_DATA_WARNING = "数据重复，请检查唯一性; ";

    /**
     * 标模任务批量导入模板下载
     */
    String[] STANDARD_MODEL_TASK_TITLE = {"标模任务(*)", "标模批次(*)", "装配备注", "计调备注", "要求装配完工时间(yyyy-MM-dd HH:mm:ss)", "要求测试完工时间(yyyy-MM-dd HH:mm:ss)", "要求包装完工时间(yyyy-MM-dd HH:mm:ss)", "预计发放日(yyyy-MM-dd HH:mm:ss)", "工序指令按任务条码校验（Y/N）", "是否锁定任务条码入库(Y/N)", "是否触发拉料(Y/N)"};
    String STANDARD_MODEL_TASK = "标模任务";
    String STANDARD_MODEL_TASK_XLS = "标模任务批量导入模板.xlsx";
    String[] STANDARD_MODEL_TASK_PROPNAMES = {"taskNo", "prodplanId", "assemblyRemark", "attribute3", "attribute5", "attribute6", "attribute10", "toGrantDate", "attribute7", "attribute8", "attribute11", "validResp"};

    String LOOKUP_TYPE_1004041 = " 1004041";
    String LOOKUP_TYPE_1004041002 = " 1004041002";
    String FIELD_LOOKUP_TYPE = "lookupType";

    String CALL_BARCODE_FAILURE = "调用条码中心异常";
    String CALL_BARCODE_JSON_FAILURE = "调用条码中心,结果转换为json异常";
    String CALL_BARCODE_CODE_FAILURE = "调用条码中心,获取code异常";

    //烧录验证单据号前缀
    String PROGRAM_BILL_NO_PREFIX = "SL";
    String PROGRAM_KEY_VALUE_PREFIX_BILL = "programGenerateApplyNo";
    String GENERATE_TASK_NO_PREFIX = "programGenerateTaskNo";
    String FOUR_ZORE = "0000";
    int EXPIRE_TIME_DAY = 60 * 60 * 24;
    int FOUR = 4;

    //烧录审批结论
    String PASS = "通过";
    String NO_PASS = "不通过";
    String ABNORMAL_SHUTDOWN = "异常关闭";

    String SAVE = "保存";
    String SUBMIT = "提交";

    //烧录验证单邮件
    String SUBJECT_CN = "烧录验证任务进展";

    String CONTENT_CN_PREFIX = "有待您处理的烧录验证任务，请您尽快处理。谢谢！（";
    String CONTENT_CN_SUFFIX = "）";

    String CONTENT_CN_PREFIX_CC = "烧录验证任务进展：";
    String CONTENT_CN_MIDDLECC = " 处理中，请知悉，谢谢！（";
    String VENEER_NAME = "单板名称";
    String VENEER = "单板版本";
    //分隔符
    String COLON = ";";
    String SEMICOLON = ":";

    String OPERATOR = "操作人";

    String OPERAT_TIME = "操作时间";

    String CURRENT_POINT = "节点";

    String RESULT = "结果";

    String REMARK = "备注";

    String STRING_EMPTY_ONE = " ";

    String ESCAPE_S = "\\s+";

    String MAILBOX_SUFFIX = "@zte.com.cn";

    String EMAIL_PREFIX = "<p style=\"font-family:arial;font-size:30px;\">";
    String EMAIL_PREFIX_A = "<a style=\"color:blue;font-size:25px;font-weight:bold;\" href=";
    String EMAIL_SUFFIX = "</a></p>";
    String EMAIL_COLO = ">";
    String EMAIL_SPLIT = "_";
    String CLICK_DOWN = "请点击下载";

    String SLASH_TWO = "//";

    /**
     * bom详情编辑权限
     */
    String LOOKUP_TYPE_1121 = "1121";

    String LOOKUP_TYPE_1121001 = "1121001";

    String LATE_REEL_ID_BIND = "LateReelIdBind";
    int EXPIRE_TIME_WEEK = 60 * 60 * 24 * 7;
    String HTTP = "http://";
    String HTTPS = "https://";
    String BOM_CRAFT_ATTRIBUTE_EXPORT_FILE_NAME = "料单工艺参数.xlsx";
    String BOM_CRAFT_ATTRIBUTE_EXPORT_SHEET_NAME = "料单工艺参数";
    String SYNCHRONIZE_HEAD_TECH_SUCCESS = "同步SPM技改特殊数据成功";
    String SYNCHRONIZE_SPECIAL_SN_TECH_SUCCESS = "同步SPM技改特殊条码数据成功";
    String STATUS_SCRAPPED = "已废止";
    long TIME_STAMP_365_X_2_DAY = 2L * 365L * 24L * 3600L * 1000L;
    String LOOK_UP_CODE_1003017001 = "1003017001";

    String LOOK_UP_CODE_6819001 = "6819001";

    String MATERIAL_ID_CARD_GENERATION = "物料标识卡生成";
    String RD_OWN_MATERIALS_REGISTER = "研发自带料注册";
    String REEL_ID_SPLIT = "ReelId拆分";

    //字典含义字段名
    String FIELD_LOOKUP_MEANING = "lookupMeaning";
    //字典  字典代码字段名
    String FIELD_LOOKUP_CODE = "lookupCode";
    //时分秒分割符号:f
    String SYMBOL_COLON = ":";
    //字节编码utf-8
    String BYTE_CODE_UTF_8 = "UTF-8";
    BigDecimal TIME_HALF_YEAR = (new BigDecimal("184"));
    String POINT = ".";
    String YCWL = "YCWL";

    String PUSHED_KEY = "manufacture_order_pushed_fixbomid_%s";
    String PUSHING_KEY = "manufacture_order_pushing_fixbomid_%s";

    String ABNORMAL_MATERIAL_ENTRY_TITLE = "iMES异常物料录入申请单%s";
    String ABNORMAL_MATERIAL_ENTRY_DETAIL_INFO = "iMES异常物料录入单据详情";
    String ABNORMAL_MATERIAL_ENTRY_EMAIL_TITLE = "%s异常录入物料单据，提单人：%s 提单时间：%s，基地：%s，部门：%s，需要您处理。";
    String ABNORMAL_MATERIAL_ENTRY_REFUSE_EMAIL_TITLE = "您提交的%s异常录入物料单据已拒绝，请注意。";
    String ABNORMAL_MATERIAL_ENTRY_AGREE_EMAIL_TITLE = "您提交的%s异常录入物料单据已审批通过，请注意。";

    String ABNORMAL_MATERIAL_ENTRY_NOTICE = "单据待审批:";
    String ABNORMAL_MATERIAL_ENTRY_EMAIL_NOTICE_TITLE = "%s异常录入物料单据待审批，请注意。";

    interface AbnormalMaterialDetail {
        String SN = "条码";
        String QTY = "数量";
        String SOURCE_BATCH_CODE = "批次码";
        String ITEMCODE = "物料代码";
        String ITEMNAME = "物料名称";
        String BRANDNAME = "品牌名称";
        String EXTERNAL_TYPE = "产品大类";
        String STYLE = "机型";
        String FINDER_EMP = "发现人";
        String ABNORMAL_TYPE = "异常类型";
        String ABNORMAL_DESCRIPTION = "异常描述";
    }

    String APPROVER = "approver";
    String BILL_NO = "billNo";
    String EMP_NO = "empNo";
    String APPENDIX = "appendix";
    String APPROVER_ID = "approverId";
    String TITLE = "title";
    String APPROVE_LIST = "approveList";
    String INFORM_PERSON = "informPerson";
    //@符号
    String AYE = "@";
    String AT = "在";
    String ALARM_TITLE_TYPE_SIX = "辅料领用关系审批回调异常监控";

    String AUX_BILL_NO_PREFIX = "FMOD";
    String CREATE = "新建";
    String CHANGE = "变更";
    String DELETE = "删除";
    String LOOK_UP_CODE_7580014 = "7580014";
    String LOOK_UP_CODE_7580015 = "7580015";
    String AUX_MATERIAL_REQUISITION_ENTRY_EMAIL_TITLE = "%s辅料领用关系单据，提单人：%s 提单时间：%s，需要您处理。";
    String AUX_MATERIAL_REQUISITION_ENTRY_REFUSE_EMAIL_TITLE = "您提交的%s辅料领用关系单据已拒绝，请注意。";
    String AUX_MATERIAL_REQUISITION_ENTRY_AGREE_EMAIL_TITLE = "您提交的%s辅料领用关系单据已审批通过，请注意。";

    String AUX_MATERIAL_REQUISITION_ENTRY_INFO = "iMES辅料领用关系单据详情";
    String AUX_MATERIAL_REQUISITION_ENTRY_TITLE = "iMES辅料领用关系%s审批单%s";
    String AUX_MATERIAL_REQUISITION_MODEL_NAME = "辅料领用关系模板.xlsx";
    String RESOURCE_INFO_MODEL_NAME = "入网证导入模板.xlsx";
    String CUSTOMER_DELIVERY_FEEDBACK_DELAY = "客户生产交期任务延期情况";


    interface AuxMaterialRequisitionDetail {
        String CODE_REQUIRED = "-辅料代码必填-";
        String CATEGORY_REQUIRED = "-关系维度是产品大类时,对应产品大类输入框不能为空-";
        String ITEM_NO_NOT_REQUIRED = "-关系维度是产品大类时,料单代码不允许输入-";
        String ITEM_NO_REQUIRED = "-关系维度是料单代码时,对应料单代码输入框不能为空-";
        String CATEGORY_NOT_REQUIRED = "-关系维度是料单代码时,产品大类不允许输入-";
        String ITEM_NOT_EXIST = "-料单代码不存在-";
        String ITEM_NO_LENGTH = "-料单代码长度必须是12位或者15位-";
        String AUX_MATERIAL_RELATIONSHIP_EXIST = "该关系维度下已存在该辅料代码与对应料单/产品大类绑定关系，请确认";
        String AUX_MATERIAL_TYPE = "辅料类型";
        String RELATIONSHIP_DIMENSION = "关系维度";
        String ITEM_NO = "料单代码";
        String ITEM_NAME = "料单名称";
        String PRODUCT_CATEGORY = "产品大类";
        String AUX_MATERIAL_CODE = "辅料代码";
        String AUX_MATERIAL_CODE_AFTER = "变更后辅料代码";
    }

    interface DeliveryFeedbackDelayDetail {
        String orderNo = "任务号";
        String dateEstimatedCompletion = "预计完工日期";
        String dateExpectedCompletion = "期望完工日期";
        String remark = "延期原因";
    }

    String LAST_UPDATED_DATE = "lastUpdatedDate";
    String DESC = "desc";

    //0个体 1机构
    String SUBJECT_TYPE_ONE = "1";

    // 条码中心地址
    int LOOKUP_TYPE_1004052 = 1004052;
    int BARCODE_BASIC_URL = 1004052001;
    int BARCODE_TENANT_ID = 1004052002;
    int BARCODE_UPDATE_URI = 1004052005;
    int BARCODE_QUERY_URI = 1004052013;
    int BARCODE_EXPAND_QUERY_URI = 1004052006;
    int BARCODE_BATCH_QUERY_URI = 1004052014;
    int BARCODE_GENERATE_UIR = 1004052009;
    int BARCODE_BLANK_GENERATE_URI = 1004052010;
    int BARCODE_LOOKUPCODE = 1004052012;
    int BARCODE_APPID = 1004052011;

    String X_AUTH_ACCESSKEY = "X-Auth-AccessKey";

    /**
     * 位号拆分产品代码redis key
     */
    String REDIS_KEY_BPCB_LOCATION_BOM = "redis_key_bpcb_location_bom_productCode_";
    String RESOURCE_IMPORT = "RESOURCE_IMPORT";
    String SPECIALITY_PARAM_RECOVER = "SpecialityParamRecover";
    String RESOURCE_EXPORT = "RESOURCE_EXPORT:";
    String RESOURCE_EXPORT_NAME = "resourceSn";
    String RESOURCE_EXPORT_XLSX = ".xlsx";
    String RESOURCE_EXPORT_XLS = ".xls";
    String RESOURCE_EXPORT_TXT = ".txt";
    String RESOURCE_EXPORT_CSV = ".csv";
    String[] RESOURCE_EXPORT_TITLE = {"资源号"};
    String[] RESOURCE_EXPORT_FIELD = {"resourceSn"};
    byte[] BYTES = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
    String FEED = "\r\n";
    String RESOURCE_SERVICE_NAME = "localeResolverMes";
    // 正在执行BOM 分阶的料单集合
    String BOM_RUN_LIST = "factory:bom:run:%s";

    interface LookKey {
        // 装配子工序
        String LOOK_1299 = "1299";
        String LOOK_1004083 = "1004083";
        // aps 域名地址
        String LOOK_1005052001 = "1005052001";
        String LOOK_1005052 = "1005052";
        // 配送地组织id 对应关系
        String LOOK_2225 = "2225";
        // IMES 运维人员邮箱通知
        String LOOK_1004095 = "1004095";
    }

    String LOOK_UP_1129 = "1129";
    String LOOK_UP_6724 = "6724";
    String SYS_TYPE_STEP = "STEP";

    String SYS_TYPE_WMES = "WMES";

    String SOURCE_SYSTEM_MES = "MES";
    String HOST = "Host";

    String ORIGIN = "origin";
    String UAT55 = "uat55";

    String ZTESSP_SNID = "ZTESSP_SNID";

    int LOOKUP_TYPE_6680 = 6680;

    String PRINT_CATEGORY_6 = "6";
    String PRINT_TYPE_SSP = "SSP物料标签打印";

    // 环保属性字典
    String LOOK_UP_CODE_1036 = "1036";
    String LOOK_UP_CODE_28650003 = "28650003";
    Integer LOOK_UP_TYPE_1036 = 1036;
    String ITEM_CODE_040 = "040";
    String TYPE_PCB_NAME = "PCB贴标签";
    String TRACE_NAME = "综合配送组";
    String X_AUTH_VALUE = "X-Auth-Value";
    String X_FACTORY_ID = "x-factory-id";
    String X_ORIGIN_SERVICENAME = "X-Origin-ServiceName";

    /**
     * 物料绑定类型
     * 维护状态 0 所有需要绑定物料, 1 可绑定物料, 2 不能绑定子卡代码
     */
    interface MaintenanceStatus {
        /**
         * 0 所有需要绑定物料
         */
        String NEED_BINDING_ITEM = "0";
        /**
         * 1 可绑定物料
         */
        String CAN_BINDING_ITEM = "1";
        /**
         * 2 不能绑定子卡代码
         */
        String FORBID_BINDING_ITEM = "2";

    }

    interface RedisKey {
        // 物料代码
        String PROD_SETTING = "prodSetting:%s";
        String LOCKS = "lock:%s";
        String PROD_PLAN_SYNC = "prodPlanSync";
        String ANALYSE_BINDING = "analyseBinding:";
        String PULL_TASK_FROM_APS = "pullTaskFromAps:%s";
        String RETRY_TASK_KEY = "retryTaskKey:%s";
        String SYNCHRONIZE_SPM_DATE_SCHEDULE_TASK = "synchronizeSpmDateScheduleTask";
        String SYNCHRONIZE_SPM_DATE_SN_SCHEDULE_TASK = "synchronizeSpmDateSnScheduleTask";
        String SYNCHRONIZE_LASTDAY_FINISH_TECH_SCHEDULE_TASK = "synchronizeLastDayFinishTechScheduleTask";
        String SYNCHRONIZE_FINISH_TECH_DATA_SCHEDULE_TASK = "synchronizeFinishTechDataScheduleTask";
    }

    BigDecimal LOOKUP_TYPE_6705 = new BigDecimal(6705);
    BigDecimal LOOKUP_TYPE_6705001 = new BigDecimal(6705001);
    BigDecimal LOOKUP_TYPE_6705002 = new BigDecimal(6705002);

    BigDecimal LOOKUP_CODE_7000016 = new BigDecimal(7000016);
    BigDecimal LOOKUP_CODE_7000017 = new BigDecimal(7000017);

    Integer LOOKUP_TYPE_6717 = 6717;

    //技改上次执行时间
    BigDecimal LOOKUP_TYPE_6717001 = new BigDecimal(6717001);
    //错误单据处理最大次数
    BigDecimal LOOKUP_TYPE_6717002 = new BigDecimal(6717002);

    String SPACE_ID = "spaceId";
    String CONTENT_ID = "contentId";
    String PAGE = "page";
    String SIZE = "size";
    String TAGS = "tags";

    String ACTIVE = "ACTIVE";

    interface ApprovalStatus {
        String AGREE = "同意";
        String REFUSE = "拒绝";
        String APPROVAL_COMPLETED = "审批完成";
        String TRANSFER = "转交";
    }

    String AGREE = "同意";
    String REFUSE = "拒绝";
    String PENDING = "待审批";

    String CRAFT_ID = "craftId";
    String CRAFT_NO = "craftNo";
    String CRAFTSECTION = "craftSection";
    String FACTORY = "facotry";
    String CRAFTVERSION = "craftVersion";
    String CRAFTSTATUS = "craftStatus";
    String CREATE_BY = "createBy";
    String ITEMNO = "itemNo";
    String SOURCESYS = "sourceSys";
    String ITEM_OR_TASK = "itemOrTask";
    String ROUTE_ID = "routeId";
    String CRAFT_EN_NAME = "craftName";
    String FACTORY_ID = "factoryId";

    // 子工序新建同步code
    String BS_PROCESS_CODE = "kafKaBsProcess";
    String NEXT_PROCESS = "nextProcess";
    String YYMMDD = "yyMMdd";
    String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd hh:mm:ss";
    String CF = "CF";
    String PROCESSSEQ = "processSeq";
    String AUTO_TEST = "自动测试";
    int IN_MAX_SIZE = 900;
    String STR_SPLIT = ",";
    String SPLIT_IN = "','";
    String CRAFT_STATUS_SUMBIT = "已提交";
    String STR_USESCOPE = "useScope";
    String STR_STARTROW = "startRow";
    String STR_ENDROW = "endRow";
    String VERSION_SUCCESS = "版本升级成功";
    String VERSION_ZZ = "ZZ";
    String VERSION_ZZZ = "ZZZ";
    String VERSION_Z = "Z";
    int VERSION_LENGTH = 2;
    String AA_TYPE = "AA";
    String AAA = "AAA";
    String AAAA = "AAAA";
    String CRAFT_STATUS = "craftStatus";
    String FACTORY_NAME = "factoryName";
    String PROCESSTYPE = "processType";
    String PRODUCTTYPE = "productType";
    String ENTITYID = "entityId";
    String ORGID = "orgId";
    String ROUTETYPE = "routeType";
    String BOARD_ASSEMBER = "单板装配"; // 单板装配 工艺段
    String BOARD_TEST = "单板测试"; // 单板测试 工艺段
    String CRAFT_SECTION = "','跨工艺段'";
    String INCRAFTSECTION = "inCraftSection";
    String SOURCE_SYS_THREE = "3";

    String ZERO6 = "000000";
    String EMAIL = "email";

    /**
     * APS 计划状态
     */
    interface ProdStatus {
        String PROD_STATUS_01 = "研发";
        String PROD_STATUS_02 = "试产";
        String PROD_STATUS_03 = "量产";
        String PROD_STATUS_05 = "消耗";
        String PROD_STATUS_07 = "失效";
    }

    String APS_PROD_PLAN_MESSAGE = "批次序列预警提示";
    String ZERO_TIME = "00:00:00";

    //成功 失败 无需处理
    String S = "S";
    String F = "F";
    String O = "O";
    String T_SIGN = "-t";

    String PDM_TECHNICAL_DEAL_RESULT_FOR_PRODPLANID = "未找到批次对应任务信息";

    //PDM技改单状态
    interface PdmTechnicalChangeStatus {
        // 未发放
        String UNISSUED = "未发放";
        // 已发放
        String ISSUED = "已发放";
        // 已删除
        String DELETED = "已删除";
        // 已作废
        String VOIDED = "已作废";
    }

    interface NetworkUploadRspCode {
        // 成功
        String SUCCESS_CODE = "0000";
        // 失败
        String FAILED_CODE = "0001";
        // Session过期
        String EXPIRE_CODE = "0002";
        // 请求过于频繁
        String TOO_FREQUENTLT_CODE = "0003";

        String[] RESULT_CODE = {"0000", "0001", "0002", "0003"};
    }

    String INDEX = " 序号";
    String PRODPLANID = "批次";
    String ERROR_INFO = "错误信息: ";
    String PROCESSING_FAILED_BATCHES = "处理失败批次";

    String AOI_PUSH_ERROR_SN = "AOI数据推送失败条码";

    String WORK_ORDER_CALL_BACK_ERROR_TASK_NO = "工单发放信息回写回调失败任务号";
    String PUSH_ERROR_SN = "条码";
    String PUSH_ERROR_TASK_NO = "任务号";
    String CARTONNO = "箱号";
    String BARCODELIST = "SN";
    String PUSH_ERROR_MSG = "错误信息";
    String PUSH_ERROR_TIMES = "失败次数";
    String PRODPLAN_QTY_OVER = "技改单在线批次数量超过阈值";
    String TASK_COMPLETED = "已完工";
    String TECH_SYNC_CONTENT = "技改同步内容：";
    String CHG_REQ_NO = "技改单号";
    String TASK_QTY = "任务数量";
    // 记录同步最后执行时间数据字典
    String SYS_LOOK_7000 = "7000";
    // 最后一次同步lms单据状态至imes的时间
    String SYS_LOOK_7000006 = "7000006";
    // 最后一次同步lms条码IMU至imes的时间
    String SYS_LOOK_7000007 = "7000007";
    // 需要同步lms条码状态的imu值配置
    String SYS_LOOK_2211 = "2211";
    Integer HOUR_SECOND = 3600 * 1000;
    String STR_5000 = "5000";
    String MAIN_BOARD = "主板";
    String SUB_BOARD = "子板";
    String CRAFT_VERSION_A = "V.A";
    String CT_ROUTE_SYNC = "ctRouteSync";
    String SYS_LOOK_7000010 = "7000010";
    String SYS_LOOK_7000011 = "7000011";
    Long ONE_MONTH_TIME = 2595000000L;
    int INT_300 = 300;
    String REGEX = "[^0-9]";
    String STR_15 = "15";
    String LOOKUP_VALUE_7000012 = "7000012";
    String LOOKUP_VALUE_7000013 = "7000013";
    ;
    String LOOKUP_VALUE_7000020 = "7000020";
    String BATCH_1_END_DATE = "2010-12-31 23:59:59";
    String BATCH_2_END_DATE = "2016-12-31 23:59:59";
    String BATCH_3_END_DATE = "2023-12-31 23:59:59";
    String LOOKUP_VALUE_7000021 = "7000021";
    String STRING_7 = "7";
    String SENT_WIP_EXT_TO_IMES_FAILED = "装配关系回写imes未获取到任务信息批次报警";
    BigDecimal FACTORY_ID_51 = new BigDecimal(51);
    BigDecimal ORG_ID_NJ = new BigDecimal(4437);
    BigDecimal ORG_ID_HY = new BigDecimal(855);
    BigDecimal ORG_ID_CS = new BigDecimal(2437);
    BigDecimal ORG_ID_XA1 = new BigDecimal(3417);
    BigDecimal ORG_ID_XA2 = new BigDecimal(3577);
    String IN_STOCK = "入库";
    String BOM_TEMP_REDIS_KEY = "redis_key_bom_temperature_";
    String IS_LEAD = "有铅";
    String LEAD_FREE = "无铅";
    String XP_CODE = "XP";
    String CX_CODE = "CX";
    String HK_CODE = "HK";

    String THERE_IS_NO_BATCH_TASK_INFORMATION = "SPM锁定信息同步:不存在批次任务信息的";
    String BATCH_IS_NOT_A_LOCAL_FACTORY = "SPM锁定信息同步:批次不是本地工厂并且不是中心工厂+已完工的";

    String MORE_THAN_THRESHOLD_ONLINE_BATCHES = "技改单: %s在线批次超过%d个,请重新提单！";
    String FAILED_TO_GET_BATCH_TASK_INFORMATION = "获取批次任务信息失败(中心工厂)";
    String THE_TECHNICAL_MODIFICATION_SHEET_HAS_BEEN_DELETED = "当前技改单已删除";
    String THE_TECHNICAL_MODIFICATION_SHEET_PROCESSING_FAILED = "当前技改单: %s处理失败,请确认！";
    String THE_CURRENT_FACTORY_DOES_NOT_NEED_TO_BE_PROCESSED = "当前工厂: %s不需要处理,请查看数据字典6724";

    interface AdvancedDailyReportExport {
        String FILE_NAME = "-单板高级日报信息.xlsx";
        String EMAIL_PREFIX = "<p style=\"font-family:arial;font-size:30px;\">";
        String EMAIL_PREFIX_A = "<a style=\"color:blue;font-size:25px;font-weight:bold;\" href=";
        String EMAIL_SUFFIX = "</a></p>";
        String EMAIL_COLO = ">";
        String CLICK_DOWN = "请点击下载";
        String LEFT_BRACKET = "(";
        String RIGHT_BRACKET = ")";
        String VALID_FOR_SEVEN_DAYS = "下载有效期七天";
        String EMAIL_TITLE_ZH = "高级日报导出Excel 邮件通知";
        String EMAIL_TITLE_ZH_ERROR = "高级日报导出Excel异常";
        String ERROR_INFO = "高级日报导出日志";

    }

    String WIP_DAILY_SYS_TYPE = "6001";
    String X_REAL_EMP_NO = "X-Real-Emp-No";
    String SYSTEM_PRODUCTS = "系统产品";
    String MOBILE_PHONE = "手机";
    String CRAFT_POWER = "电源模块";

    String SYS_LOOKUP_TYPE_FACTORY_PRODUCT_TYPE = "1190";
    String CRAFT_EDIT_EMAIL_TITLE = "【工艺路径维护】%s料单%s产品 工艺路径维护信息，请查阅！";
    String ITEM_NAME = "料单名称";
    String ITEM_NO = "料单代码";
    String CRAFT_VERSION = "版本";
    String MAID_CRAFT_ROUTE = "主卡工艺路径";
    String SUB_CRAFT_ROUTE = "子卡工艺路径";
    String MAINTAIN = "维护人";
    String DELIVERY_FEEDBACK_EMAIL_TITLE = "%s客户已经存在%d个任务延期情况，请关注并及时反馈！！！";

    interface ApprovalType {
        String SCRAPPING_TOOLING_BARCODE = "scrappingOfToolingBarcode";
        String TOOLING_APPLICATION = "toolingApplication";
        String TOOLING_VERIFY = "toolingVerify";
    }
    String ALARM_TITLE_TYPE_SEVEN = "工装验证审批回调异常监控";

    BigDecimal LOOKUP_TYPE_7303 = new BigDecimal(7303);
    BigDecimal LOOKUP_TYPE_7306 = new BigDecimal(7306);
    BigDecimal LOOKUP_TYPE_7307 = new BigDecimal(7307);
    BigDecimal LOOKUP_TYPE_7300 = new BigDecimal(7300);
    BigDecimal LOOKUP_TYPE_7301 = new BigDecimal(7301);
    BigDecimal LOOKUP_TYPE_7302 = new BigDecimal(7302);
    BigDecimal LOOKUP_TYPE_6817 = new BigDecimal(6817);
    BigDecimal LOOKUP_TYPE_1004115 = new BigDecimal(1004115);
    BigDecimal LOOKUP_TYPE_2025041701 = new BigDecimal(2025041701);
    String STR_UPDATE = "修改";
    String STR_ADD = "新增";

    interface CustomerItems {
        String TITLE = "客户物料代码导出.xlsx";
        String EMAIL_TITLE_ZH = "imes客户物料详情邮件导出";
        String EMAIL_TITLE_ZH_ERROR = "客户物料代码详情导出Excel异常";
        String[] HEADER = {"客户名称", "项目名称", "合作模式", "项目阶段", "内部代码",
                "内部代码名称", "类型", "项目类型", "客户物料型号", "客户部件类型", "板码类型", "PN码", "客户代码", "制造商物料名称", "内部供应商",
                "客户供应商", "内部规格型号", "客户物料名称", "客户型号", "原厂制造商名称","功率规格",
                "是否已退市", "参数列表", "创建人", "创建时间", "最后更新人", "最后更新时间"};
        String[] PROPS = {"customerName", "projectName", "cooperationMode", "projectPhase", "zteCode",
                "zteCodeName", "strProjectType", "strProjType", "customerMaterialType", "strCustomerComponentType",
                "strBoardType", "pnCode",
                "customerCode", "customerItemName", "zteSupplier", "customerSupplier", "zteBrandStyle", "customerSpecification", "customerModel",
                "originalManufacturerName", "powerSpecification", "strStatus", "paramsDetailStr", "createBy", "createDate", "lastUpdatedBy", "lastUpdatedDate"};
    }

    interface CustomerParamsTable {
        String CPU_INFO = "cpu_info";
        // 内存： 数据库表memory_info
        String MEMORY_INFO = "memory_info";
        String NETWORK_CARD_INFO = "network_card_info";
        String RAID_CARD_INFO = "raid_card_info";
        // 数据库表hard_drive_info (注意itemType 三个都匹配硬盘详情表)  NVME也可以是SSD或SATA
        String HARD_DISK_INFO = "hard_disk_info";
        String GPU_INFO = "gpu_info";
        String MOTHERBOARD_INFO = "motherboard_info";
        String MACHINE_INFO = "machine_info";
        String POWER_SOURCE = "power_source_info";
    }

    interface ExcelName {
        String CPU_INFO = "_CPU";
        // 内存： 数据库表memory_info
        String MEMORY_INFO = "_MEMORY";
        String NETWORK_CARD_INFO = "_NETWORK_CARD";
        String RAID_CARD_INFO = "_RAID_CARD";
        // 数据库表hard_drive_info (注意itemType 三个都匹配硬盘详情表)  NVME也可以是SSD或SATA
        String HARD_DISK_INFO = "_HARD_DISK";
        String GPU_INFO = "_GPU";
        String MOTHERBOARD_INFO = "_MOTHERBOARD";
        String MACHINE_INFO = "_MACHINE";
        String POWER_SOURCE = "_POWER";
    }

    String ZTE_CODE = "内部代码";
    String CUSTOMER_NAME = "客户名称";
    String ZTE_BRAND_STYLE = "ZTE规格型号";
    BigDecimal VALUE_1004067001 = new BigDecimal("1004067001");

    BigDecimal BIG_DECIMAL_90DAYS = (new BigDecimal("90"));
    BigDecimal TIME_INTERVAL = (new BigDecimal("180"));
    BigDecimal TIME_YEAR = (new BigDecimal("366"));
    BigDecimal TWO_YEAR_BIG = (new BigDecimal("730"));
    String LOOK_UP_CODE_3410 = "3410";
    String LOOK_UP_CODE_250503 = "250503";
    String HAND_PASTE = "手贴";
    String SOURCE_SYSTEM_IMES = "iMES";
    String SOURCE_SYSTEM_ASMS = "ASMS";
    String STR_5 = "5";
    String STR_6 = "6";
    String STR_8 = "8";
    String STR_9 = "9";

    interface MessageType {
        String RMA_BOARD = "ZTEiMES-ByteDance-RmaBoard";
        String RMA_STATION = "ZTEiMES-ByteDance-RmaBoardStation";
        String RMA_LOG = "ZTEiMES-ByteDance-RmaBoardStationLog";

        String TEST_FEEDBACK = "ZTEiMES-ByteDance-BoardStationLog";
    }

    BigDecimal LOOKUP_TYPE_1004106 = new BigDecimal(1004106);
    BigDecimal LOOKUP_TYPE_1004107 = new BigDecimal(1004107);

    String TABLE_TD = "</td>";

    interface Spare {
        String EMAIL_TITLE = "%s辅料调拨单:%s,调出工厂:%s,接收工厂:%s 单据状态:%s 单据明细详情请登录系统查看!";
        String APPROVE_TITLE = "%s辅料调拨单:%s,调出工厂:%s,接收工厂:%s 已提交审批流程,请关注看审批进展";
        String DETAIL = "辅料调拨单明细：";
        String PART_NAME = "名称";
        String QUANTITY = "数量";
        String TRANSFER_REASON = " 调拨原因: ";
        String APPROVE = "审批明细：";
        String APPROVE_LEVEL = "审批层级";
        String APPROVER = "审批人";
        String JOB = "岗位";
        String MODEL_NAME = "调拨单据明细模板.xlsx";
        String MODEL_NAME_APPROVE = "调拨单据明细.xlsx";

        String RECEIVE_FACTORY_NAME = " 接收工厂: ";
        String TRANSFER_FACTORY_NAME = "调出工厂：";

        String[] PROPS = new String[]{"partName", "quantity"};
        String[] HEADER = new String[]{"名称", "数量"};
    }


    String BOARD_REPAIR_EMAIL_TITLE = "定时推送返修中心数据报错,类型：";
    String TECHNICAL_STATUS = "待更改";

    String LOOKUP_TYPE_6736 = "6736";

    //正整数
    String REG_POSITIVE_INTEGER = "^[1-9]\\d*$";

    String MADE_IN = "MadeIn";

    String LOSE_EFFICACY = "LOSE_EFFICACY";
    String STR_USING = "USING";
    String STR_INIT = "INIT";
    Integer INT_300000 = 300000;

    String REDIS_KEY_NETWORK_ALLOCATION = "redis_key_network_allocation_";
    String REDIS_KEY_RESOURCE_SCRAP = "redis_key_resource_scrap_";
    String RESOURCE_INFO_SERVICE = "resourceInfoService";
    String RESOURCE_INFO_DETAIL_SERVICE = "resourceInfoDetailService";
    String RESOURCE_APPLY_RECORD_SERVICE = "resourceApplyRecordService";


    String FIRST = "first";


    String RESOURCE_USE_INFO_ERROR_XLSX = "资源使用记录导错误文件.xlsx";

    // 工信部登记结果返回
    interface NetWorkUploadResult {
        String JOB_ID = "JobID";
        String BIZCODE = "BizCode";
        String BIZCODE_E102 = "E102";
        String LOOKUP_TYPE_286522 = "286522";
        String LOOKUP_CODE_286522001 = "286522001";
        String[] RESULT_VALID = {"3", "9"};
        String REGISTER_SUCCESS_MSG = "登记成功";
        String REGISTER_SUCCESS_STATUS = "1";
        String REGISTER_ERROR_STATUS = "2";
        String B2B_CALLED_FAILED = "入网许可工信部登记结果B2B接口调用失败";
        String B2B_RESP_ERROR_MSG = "B2B接口调用异常，返回结果为空";
        String B2B_RETURN_NO_FILEID = "未通过B2B接口得到工信部登记结果文档云ID";
        String DOWNLOAD_CLOUD_FILE_FAILED = "通过文档云下载登记结果文件失败,文档云下载链接:";
        String PARSING_FILE_FAILED = "入网许可登记结果文件解析失败！";
        String EMAIL_TITLE = "获取入网证工信部登记结果过程中的异常任务,请查收！";
        String EMAIL_LINE_MSG_PRE = "错误信息";
        String[] RESULT_CODE = {"0000", "0001", "0002"};
    }

    Integer LOOK_UP_CODE_6824001 = 6824001;
    String LOOK_UP_CODE_6825 = "6825";
    Integer LOOK_UP_CODE_6846001 = 6846001;

    /**
     * 0000  请求操作成功
     * 0001  服务器发生异常
     * 0002  身份效验不通过
     * 0003  业务权限效验不通过
     * 0004  参数效验不通过
     * 0005  服务程序发生异常
     */
    interface B2BStatusCode {
        // 成功
        String SUCCESS_CODE = "0000";
        String[] FAILED_CODE = {"0001", "0002", "0003", "0004", "0005"};
    }

    String B107 = "B107";
    String E = "E";
    String ZTE = "ZTE";

    String B124 = "B124";

    String B126 = "B126";

    /**
     * 0:已申请;1:申请异常;2:已下载;3:下载超期;4:文件处理异常;
     */
    interface NetWorkResourceApplyStatus {
        String APPLIED = "0";
        String ABNORMAL_APPLICATION = "1";
        String DOWNLOADED = "2";
        String DOWNLOAD_OVERDUE = "3";
        String FILE_PROCESSING_EXCEPTION = "4";
    }

    String FAILED_TO_DOWNLOAD_IDENTIFICATION_NUMBER = "下载标识号失败:%s";

    String AUTOMATIC_DOWNLOAD_IDENTIFICATION_NUMBER_NETWORK = "入网自动下载标识号超期";

    String DOWNLOAD_OVERDUE_BUT_UNSUCCESSFUL = "如下订单号超%s小时未下载标识号成功:%s";

    String DOWNLOAD_OVERDUE_BUT_UNSUCCESSFUL_TWO = "如下订单号超%s天未下载标识号成功（后续不再尝试下载该文件）:%s";

    String IF_DOWNSTREAM_PROCESSING_FAILS = "如下行处理失败:%s";

    String APPLICATION_NETWORK_IDENTIFICATION_NUMBER_FAILED = "iMES入网标志号自动申请失败";

    String APPLICATION_NETWORK_IDENTIFICATION_NUMBER_FAILED_DETAIL = "资源编号%s申请失败，错误信息:%s";

    String SCORE = "score";

    String APS_WHITE_LIST = "任务可修改指定品牌信息接口服务(APS)-白名单";

    String[] PROJECT_PHASE_MES = {"WAREHOUSE", "PROTOTYPE"};

    String MESSAGE_TYPE_B2B_MEITUAN = "ZTEiMES-Meituan-ProduceDateUpload";

    String MEITUAN_TEST_FILE_BYTEDANCE = "ZTEiMES-Meituan-TestFileUpload";

    String ZTE_IMES_BYTEDANCE_MOCOMPLETION_FEEDBACK = "ZTEiMES-ByteDance-MOCompletionFeedback";
    String INFOR_WMS = "InforWMS";
    String MES = "MES";
    BigDecimal LOOKUP_CODE_8240063 = new BigDecimal("8240063");

    // 入网证总体回传比例计算,时间跨度配置
    Integer LOOKUP_CODE_652301001 = 652301001;
    // 入网证总体回传比例计算结果存储
    Integer LOOKUP_CODE_652301002 = 652301002;
    // 入网证批文维度回传比例计算,时间跨度配置
    Integer LOOKUP_CODE_652302001 = 652302001;
    // 入网证批文维度标准阈值设置
    Integer LOOKUP_CODE_652302002 = 652302002;
    // 入网证回传比例低于阈值的邮件推送人配置
    Integer LOOKUP_CODE_652302003 = 652302003;
    // 入网证批文维度回传比例计算,计算的资源号创建时间跨度配置
    Integer LOOKUP_CODE_652302004 = 652302004;

    String NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE = "存在回传比例低于";

    String NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE_END = "的批文，请注意";

    String THESE_RESOURCE_NO_BACK_PASS_RATIO_UNDER_STANDARD_VALUE = "以下批文回传比例低于设限值:";

    String NETWORK_IDENTIFICATION_BACK_PASS_RATIO_SEND_EMAIL_FAILED = "批文回传比例预警邮件推送失败";

    String STR_PERCENT = "%";

    String RESOURCE_NO = "批文";
    String DEVICE_TYPE="设备型号";

    String MODEL_NUMBER = "552116";

    String BASE_QTY = "基准数量";

    String AVAILABLE_QTY = "可用数量";

    String BACK_PASS_RATIO = "回传比例";

    String DELIVER_TASK_PROGRESS_EXPORT_FILE_NAME = "配送任务进展信息数据.xlsx";
    String DELIVER_TASK_PROGRESS_EXPORT_SHEET_NAME = "配送任务进展信息数据";

    // MDS写片信息同步版本类型数据字典
    Integer LOOK_UP_TYPE_652305 = 652305;

    // MDS写片信息同步接口地址
    Integer LOOK_UP_VALUE_652306001 = 652306001;

    // 中试AOI测试数据查询
    Integer LOOK_UP_VALUE_2020011 = 2020011;

    // SMT在线维修数据查询
    Integer LOOK_UP_VALUE_2020012 = 2020012;

    // 中试 SN任务整机信息
    Integer LOOK_UP_VALUE_2020013 = 2020013;
    Integer LOOK_UP_VALUE_2020014 = 2020014;

    Integer LOOK_UP_VALUE_7599001 = 7599001;
    Integer LOOK_UP_VALUE_7599002 = 7599002;
    Integer LOOK_UP_VALUE_7599003 = 7599003;
    Integer LOOK_UP_VALUE_7599004 = 7599004;
    Integer LOOK_UP_VALUE_7599005 = 7599005;

    String Z_ACCESS_TOKEN = "Z-ACCESS-TOKEN";

    String IMES_PREFIX = "imes_";

    String APP_CODE = "appcode";

    String CENTERFACTORY_PREFIX = "zte_mes_manufactureshare_centerfactory_";

    String MDS_OBTAINS_TOKEN_KEY = IMES_PREFIX + CENTERFACTORY_PREFIX + "mds_obtains_token_key_";
    String LOOKUP_TYPE_6732 = "6732";
    String LOOKUP_TYPE_6732001 = "6732001";
    String LOOKUP_TYPE_6732002 = "6732002";
    String LOOKUP_TYPE_6732003 = "6732003";
    String LOOKUP_TYPE_6732004 = "6732004";
    String LOOKUP_TYPE_6732005 = "6732005";
    String LOOKUP_TYPE_6732006 = "6732006";
    String USER_NAME = "username";
    String PASS_WORD = "password";
    String GRANT_TYPE = "grant_type";
    // MDS写片信息同步开关
    String LOOK_UP_TYPE_652307 = "652307";
    String LOOK_UP_TYPE_652307001 = "652307001";
    String CAD_UPLOAD_REMARK_THREE = "CAD导入时MDS写片信息同步结果";
    String DO_NOT_NEED_XP = "无需写片或料单未启用写片";
    String FAILED = "FAILED";
    String SYNC_SUCCESS = "CAD导入时与MDS同步写片信息成功,无新增写片信息";
    String SYNC_SUCCESS_WITH_NEW_PRE_INFO = "CAD导入时与MDS同步写片信息成功,存在新增写片信息,位号: ";

    // MDS写片信息邮件接收人
    String LOOK_UP_TYPE_652308 = "652308";

    String LOOK_UP_CODE_1003021001 = "1003021001";

    String LOOK_UP_TYPE_8888001 = "8888001";

    // mes物料同步时间数据字典
    String SYS_LOOK_7000024 = "7000024";

    String OBTAIN_MODEL_CODE = "获取工信部型号编码失败,params:%s,errorInfo:%s";

    String DEAL_HISDATA_BY_NO_FACTORY = "处理未发放到本地工厂的批次";

    String FIRING_TYPE = "先烧后贴";
    String ERP_WRITEBACK_REMIND = "erp回写失败告警";
    String ERP_WRITEBACK_RUN_FAIL = "ERP回写定时任务执行失败";
    String NO_ERP_WRITEBACK = "没有符合条件的批次需要ERP回写";


    interface ResourceType {
        String MAC = "MAC";
        String GPON_SN = "GPON-SN";
        String CTEI = "CTEI";
        String CUEI = "CUEI";
        String CMEI = "CMEI";
        String IMEI = "IMEI";
        String SN = "SN";
        String STB_SN = "STB SN";
        String SO_NET_SN = "So-net-SN";
        String DEVICE_SERIAL_NUMBER = "DeviceSerialNumber";
        String NETWORK_ACCESS = "NAL";
    }

    interface ParamType {
        String MAC = "MAC";
        String GPON_SN = "GPON-SN";
        String WHOLE_DEVICE_CODE = "WholeDeviceCode";
        String ASSIGNMENT = "赋值";
        String INTERVAL_VALUE = "区间取值";
        String RANDOM_TYPE = "随机";
        String DEVICE_SERIAL_NUMBER = "DeviceSerialNumber";
        String D_SN = "D-SN";
        String NASN = "NASN";
        String SCRAMBLING_CODE = "ScramblingCode";
        String NACC = "NACC";
        String CUSTOMIZE = "Customize";
        String STBID = "STBID";
    }

    interface GenerationMethod {
        String MAC_START = "MacStart";
        String CUSTOMIZED_MAC_START = "定制MacStart";
        String MAC_END = "MacEnd";
        String MAC_ADD = "Mac增运算";
        String GPON_SN = "GPON-SN";
        String CUSTOMIZED_GPON_SN = "定制GPON-SN";
        String TELMEX_GPON_SN = "墨西哥Telmex";
        String ASSIGNMENT_DB = "数据库";
        String ASSIGNMENT_TEXT = "文本";
        String ASSIGNMENT_VARIABLE = "变量";
        String ASSIGNMENT_TEXT_AND_RANGE = "文本&量区间";
        String INTERVAL_VALUE_PREFIX = "前缀+变量区间";
        String INTERVAL_VALUE_SUFFIX = "变量区间+后缀";
        String INTERVAL_VALUE_PREFIX_SUFFIX = "前缀+变量区间+后缀";
        String INTERVAL_VALUE_RANGE = "变量取值区间";

        String RANDOM_PREFIX = "前缀+随机";
        String RANDOM_SUFFIX = "随机+后缀";
        String RANDOM_PREFIX_SUFFIX = "前缀+随机+后缀";
        String RANDOM = "随机";
        String D_SN = "D-SN";
        String SYSTEM_ALLOCATION = "系统分配";
        String DEVICE_TYPE = "DeviceType";
        String DSN_CMCC = "移动";
        String DSN_CUCC = "联通";
        String DSN_CTCC = "电信智能";
        String DSN_CTCC_FTTO = "电信FTTO";
        String DSN_CTCC_SN_EMPTY = "电信+SN含空格";
        String CUSTOMIZE = "自定义编写";
        String ENCRYKEY_METHOD_1 = "16进制大小写";
        String ENCRYKEY_METHOD_2 = "ASCII码+#$%^&";

        String WIRELESS_NET_NAME_METHOD_1 = "固定值";
        String WIRELESS_NET_NAME_METHOD_2 = "前缀+随机+后缀";
        String WIRELESS_NET_NAME_METHOD_3 = "前缀+GPON SN/MAC+后缀";

        String WLAN_PASS_METHOD_1 = "ASCII码";
        String WLAN_PASS_METHOD_2 = "3选3 ASCII码+#%+首位无#%";
        String WLAN_PASS_METHOD_3 = "3选3 ASCII码+@#$";
        String WLAN_PASS_METHOD_4 = "定制ASCII码+大小写位数";
        String NASN = "NASN";
        String STBID_AUTO = "数字化平台获取";
        String STBID_MANUAL = "非数字化平台获取";

    }
    interface DataBaseParams {
        String CMEI = "CMEI";
        String CUEI = "CUEI";
        String CTEI = "CTEI";
        String IMEI = "IMEI";
        String SN = "SN";
        String STB_SN = "STB SN";
        String SO_NET_SN = "So-net-SN";
    }

    List NET_WORK_PARAMS_LIST = Arrays.asList("NASN","ScramblingCode","NACC");

    String SEPARATOR = "separator";

    String SPECIAL_ENDING = "特殊结尾";

    String MAC_START_FUNCTION = "MacStart(%s)";
    String MAC_START_CUSTOMIZED_FUNCTION = "MacStart(%s)";
    String MAC_END_FUNCTION = "MacEnd(%s)";
    String MAC_ADD_FUNCTION = "MacAdd(%s)";
    String GPON_SN_FUNCTION = "GponSn(%s)";
    String RANDOM_FUNCTION = "Random(%s)";
    String GPON_SN_CUSTOMIZED_FUNCTION = "CustomizedGponSn(%s)";
    String GPON_SN_TELMEX_FUNCTION = "GponSnTelmex()";
    String ASSIGNMENT_DB_FUNCTION = "DB(%s)";
    String ASSIGNMENT_TEXT_FUNCTION = "Text(%s)";
    String ASSIGNMENT_VARIABLE_FUNCTION = "Variable(%s)";
    String ASSIGNMENT_TEXT_AND_RANGE_FUNCTION = "TextAndRange(%s)";
    String INTERVAL_VALUE_FUNCTION = "IntervalValue(%s)";
    String INTERVAL_VALUE_PREFIX_FUNCTION = "IntervalPrefix(%s)";
    String INTERVAL_VALUE_SUFFIX_FUNCTION = "IntervalSuffix(%s)";
    String INTERVAL_VALUE_PREFIX_SUFFIX_FUNCTION = "IntervalPreSuf(%s)";
    String INTERVAL_VALUE_RANGE_FUNCTION = "IntervalRange(%s)";
    String WHOLE_DEVICE_CODE_FUNCTION = "WholeDeviceCode(%s)";

    String DEVICE_TYPE_FUNCTION = "DeviceType(%s)";
    String CMEI_FUNCTION = "CMEI(%s)";

    String ENCRY_KEY_HEX_FUNCTION = "EncryKeyHex(%s)";

    String ENCRY_KEY_ASCII_FUNCTION = "EncryKeyASCII(%s)";

    String WIRELESS_NET_NAME_FIXED_VALUE_FUNCTION = "WNN_FIXEDVALUE(%s)";
    String WIRELESS_NET_NAME_DIGIT_FUNCTION = "WNN_DIGIT(%s)";
    String WIRELESS_NET_NAME_SN_OR_MAC_FUNCTION = "WNN_SNORMAC(%s)";

    String WIRELESS_NET_NAME_5G_FIXED_VALUE_FUNCTION = "WNN5G_FIXEDVALUE(%s)";
    String WIRELESS_NET_NAME_5G_DIGIT_FUNCTION = "WNN5G_DIGIT(%s)";
    String WIRELESS_NET_NAME_5G_SN_OR_MAC_FUNCTION = "WNN5G_SNORMAC(%s)";

    String WLAN_PASS_FUNCTION = "WLANPASS(%s)";

    String WLAN_PASS_5G_FUNCTION = "WLANPASS5G(%s)";
    String XGSPON_REGISTER_ID_FUNCTION = "XGSPON_REGISTERID(%s)";

    String OUI_FUNCTION = "OUI(%s)";
    String SN_FUNCTION = "SN(%s)";
    String NASN_FUNCTION = "getNASN(%s)";

    String SCRAMBLING_CODE_FUNCTION = "getScramblingCode(%s)";

    String NACC_FUNCTION = "getNACC(%s)";
    String DSN_D_SN_FUNCTION = "DSN_DSN(%s)";

    String DSN_CMCC_FUNCTION = "DSN_CMCC(%s)";

    String DSN_CUCC_FUNCTION = "DSN_CUCC(%s)";

    String DSN_CTCC_FUNCTION = "DSN_CTCC(%s)";

    String DSN_FTTO_FUNCTION = "DSN_FTTO(%s)";

    String DSN_CTCC_SN_FUNCTION = "DSN_CTCC_SN(%s)";

    String D_SN_FUNCTION = "D_SN(%s)";
    String STBID_FUNCTION = "STBID(%s)";

    String MAC_ITEM_DATA = "{\"MAC\":\"%s\"}";

    String GPON_SN_ITEM_DATA = "{\"GPON-SN\":\"%s\"}";

    String GPON_SN_START = "^%s";

    String CUCC_PREFIX = "CUCC";

    String CUCC_PARAM_NAME = "%s:";

    List PARAM_TYPE_LIST = Arrays.asList("MAC", "GPON-SN", "WholeDeviceCode", "赋值", "D-SN", "NASN", "ScramblingCode", "NACC");
    List VARIABLE_PARAMS_LIST = Arrays.asList("Mac增运算", "变量", "文本","文本&量区间", "前缀+变量区间", "变量区间+后缀", "前缀+变量区间+后缀", "变量取值区间", "首MAC前六位", "电信+SN含空格");
    List POST_PREVIEW_LIST = Arrays.asList("自定义编写", "变量", "文本&量区间", "Mac增运算", "前缀+变量区间", "变量区间+后缀", "前缀+变量区间+后缀", "变量取值区间", "联通", "电信+SN含空格");

    String SYNC_REEL_ID_HISTORY_FORM_INFO = "syncReelIdHistoryFormInfo";

    List AOI_TYPE_LIST = Arrays.asList("SPC-A", "SPC-B");

    String BOARD_SIZE_X = "BoardSizeX";

    String BOARD_SIZE_Y = "BoardSizeY";

    String REF_NAME = "RefName";

    String STEP_CENTER_X = "StepCenterX";

    String STEP_CENTER_Y = "StepCenterY";

    String STEP_SIZE_X = "StepSizeX";

    String STEP_SIZE_Y = "StepSizeY";
    String BYTE_DANCE = "ByteDance";

    //调拨单或者套料单已产生或者正在产生状态
    List ALREADY_GENERATED_OR_CURRENTLY_BEING_GENERATED_LIST = Arrays.asList("1", "2","100");

    Integer ECCN_LOOK_UP = 6100;

    String LUP_6100004 = "6100004";
    String LUP_6100001 = "6100001";
    String LUP_6100005 = "6100005";

    int NUM_1000 = 1000;
    String RUN = "run";
    String SUPPLIER_QUERY_COMPARE_RESULT = "SupplierQueryCompareResult";
    String SERVER_SN_LIST = "serverSNList";
    String SVR_SN = "SvrSN";
    String STR_SUCCESS_LOW = "success";
    String STR_MESSAGE = "message";
    String STR_DATA = "data";
    String REGEX_DESC = "正则匹配";
    String ACCURATE_DESC = "精确匹配";
    String MBOM_EXCEL_FILE = "MBOM导出.xlsx";
    String MBOM_HEADER_SHEET = "MBOM头信息";
    String MBOM_DETAIL_SHEET = "MBOM详情信息";
    String PAGE_SIZE = "pageSize";
    String PAGE_NO = "pageNo";
    String MODULES = "modules";
    String EXTRAFIELDS = "extraFields";
    String FIELD_NAME = "fieldName";
    String FIELD_TYPE = "fieldType";
    String STR_IN = "IN";
    String FIELD_VALUE = "fieldValue";

    interface BomCraftAttribute {
        String FILE_NAME = "料单工艺参数.xlsx";
        String SHEETNAME = "料单工艺参数";
        String[] TITLE = new String[]{
                "工艺段", "料单代码", "产品名称", "PCB版本", "面别", "炉温名称", "喷码机模板",
                "喷码机后缀", "工厂", "环保属性","创建人", "创建时间","更新人","更新时间","批次","维度"
        };
        String[] PROPS = new String[]{
                "craftSection", "bomNo", "bomName", "verNo", "surface", "furnaceTempName", "inkJetPrinterTemplate",
                "inkJetPrinterSuffix","factoryName", "leadFlagMean","createByName",
                "createDate","lastUpdatedByName","lastUpdatedDate","prodplanId","dimensionName"
        };
    }

    // uac加密公钥缓存key
    String UAC_PUBLIC_KEY_REDIS_KEY = "uac:public:key:";

    String X_MSA_ENCRYPT_DATAKEY="X-MSA-Encrypt-DataKey";

    String X_MSA_ENCRYPT_PUBLICKEY="X-MSA-Encrypt-PublicKey";

    String X_ENCRYPT_KMS_FLAG="X-Encrypt-Kms-Flag";

    interface ConfirmationStatus{
        String CONFIRMED = "已转正";
        String CONFIRMING = "转正中";
    }
    String TIP_APS_CANCELLATION = "%s任务转正状态为已转正或正在转正中，不允许操作取消转正";
    String TIP_APS_NO_TASK = "没有查到相关任务";
    String OPERATION_CONFIRMATION = "转正";
    String OPERATION_CANCEL = "取消转正";
    String CALL_ICC_ERROR = "调用ICC接口错误: ";
    String CALL_CPQD_ERROR = "调用CPQD接口错误: ";
    String CALL_PDM_ERROR = "调用PDM接口错误: ";
    String REFRESH_FIX_BOM_ID = "refreshFixBomId";
    String FG_DISAS_2 = "FG_DISAS_2";
    String CUSTOMER_COMPONENT_TYPE_CONFIG_MODEL = "server.configmodel";

    int LOOKUP_VALUE_1004115001 = 1004115001;
    int LOOKUP_VALUE_1777002 = 1777002;
    int LOOKUP_VALUE_1004113003 = 1004113003;
    int LOOKUP_VALUE_1004113004 = 1004113004;
    int LOOKUP_VALUE_1004113005 = 1004113005;
    int LOOKUP_VALUE_1004113006 = 1004113006;
    int LOOKUP_VALUE_8886002 = 8886002;
    int LOOKUP_VALUE_8886003 = 8886003;
    int LOOKUP_VALUE_1004115 = 1004115;

    String MESSAGE_TYPE_DISPATCH_MANUFACTURE_ORDER = "ZTEiMES-Alibaba-DispatchManufactureOrder";
    String MESSAGE_TYPE_CREATE_CARTO_RELATION = "ZTEiMES-Alibaba-CreateCartonRelation";

    String ZTEIMES_ALIBABA_MCTSERVERPRODUCTINFO = "ZTEiMES-Alibaba-MctServerproductinfo";
    String MESSAGE_TYPE_SYNC_CARTO_RELATION = "ZTEiMES-Alibaba-SyncCartonSnRelation";
    String MESSAGE_TYPE_UPDATE_CARTO_RELATION = "ZTEiMES-Alibaba-UpdateCartonRelation";

    String DISPATCH_ORDER_PROJECT_PHASE = "dispatchOrder";

    String COOPERATIONMODE_BS = "B&S";
    String COOPERATIONMODE_AVAP = "AVAP";

    String RESPONSE_SUCCESS_CODE = "00000000";
    String ZTEG = "ZTEG";
    String MAC_START = "MacStart";
    String STRING_ZERO_ONE = "01";
    long LONG_65536 = 65536L;
    String DSN_CTCC_SN = "DSN_CTCC_SN";
    String COMPONENTS_OF_THE_MANUFACTURER = "厂商部件";
    String COMPONENTS_OF_FINISHED_MATERIAL = "成品料";

    String ITEM_TYPE_L6_PKG = "L6包";
    String ITEM_TYPE_EBOM = "EBOM";
    //任务分类数据字典类型
    String LOOKUP_VALUE_TASK_TYPE = "2025051511";
    String LOOKUP_CODE_7505001 = "7505001";
    /**
     * 【iWMS提供】根据物料代码查询已发货数量
     */
    String LOOKUP_CODE_7506001 = "7506001";
    String ALIBABA_PUBLIC_CLOUD = "阿里公有云";

    String MESSAGE_TYPE_DELIVERY_FEEDBACK_DATA_PUSH = "ZTEiMES-Alibaba-ManufactureFeedBack";
    String ZTEIMES_ALIBABA_QUALITYCODE_KAFKA = "ZTEiMES-Alibaba-QualityCodePublic";
    String GET_QUALITY_CODE = "getQualityCode";

    public static final String PARTCODELIST = "partcodeList";

    public static final String PARTCODE = "partcode";

    public static final String STATIONIDLIST = "station_id_list";

    String LOOKUP_VALUE_6746 = "6746";
    String LOOKUP_VALUE_6747 = "6747";

    String PASS_STR = "PASS";

    interface ALi {
        String PASS = "Pass";
        String FAIL = "Fail";
        String RUN_IN = "run-in";

    }
    String FILE_CLOUD_LINK_SWITCH = "file_cloud_link_switch";

    String ABNORMAL_GENERATION_OF_OSS_FILE = "生成oss文件异常";

    String 生成oss文件异常 = "生成oss文件异常,条码{0},stationId:{1},异常信息:{0}";

    String MCT_CAPTURE = "MCT_CAPTURE";

    String STR_0001 = "0001";

    String ZTE101 = "ZTE101";

    /**
     * 条码替换掉keywords 参数
     */
    String CALL_BACK_SN_REPLACEMENT= "#snReplace";

    // 子条码推送
    String CALL_BACK_DATA_SUB = "#dataSub";

    String SUB_DATA_PUSH_ERROR = "子条码低优先级推送异常,请及时关注处理!";

    Integer LOOKUP_CODE_7599006 = 7599006;


    String CN = "CN";

    String HAVE_CUCC_PARAMS = "haveCUCCParams";

    // stbid-spparam
    String SPPARAM_KEY_STBID_PRRFIX = "stbidPrefix";
    String SPPARAM_KEY_STBID_CHECKAA = "stbidCheckAA";
    String DELIMITER_COMMA = ",";
    String DELIMITER_SHORTBAR = "-";
    String DELIMITER_STAR = "#";
    String SPECIAL_FFFF = "####";
    String DEFAULT_AA = "00";
    int STBIDPREFIX_LENGTH = 5;
    int STBIDPREFIX_CPART_LENGTH = 6;
    int STBIDPREFIX_C3START_INDEX = 2;
    int STBIDPREFIX_C3END_INDEX = 3;
    int STBID_CFG_LENGTH = 3;
    String STBID_CHECKAA_ON = "Y";

    String MOC_SUFFIX = "-001";
    Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[!@#\\$%\\^&*()_+\\-\\=\\{\\}\\[:\";'\\,\\.\\?/\\\\\\|]");

    String FMTYPE_CPE_IMESCODE = "1";
    String FMTYPE_CPE_MDSNAME = "固网_CPE";
    String FMTYPE_DHOME_IMESCODE = "2";
    String FMTYPE_DHOME_MDSNAME = "固网_DHOME";
    String SYNC_MDS_SUCCESS = "Y";

    interface DE_STR {
        String DE = "DE";
        String SEDE = "Sede";
        String EC = "/EC";
        String PKCS = "B/PKCS";
        String PAD = "5Pad";
        String DING = "ding";

    }

    String ABNORMAL_UPLOADING_OF_TEST_DATA = "上传阿里整机测试数据异常";

    String USAGE_SCOPE = "UsageScope";
    String ORIGINAL_LPN = "原箱";
    String NO_SHIPED = "未发料";
    String BINDING = "绑定中";
    long DAY_OF_SECOND = 24L * 3600 * 1000;

    /**
     * 公有云
     */
    String CLOUD_TYPE_PC="PC";
    /**
     * 混合云
     */
    String CLOUD_TYPE_HC="HC";
}
