package com.zte.common.utils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/11 13:52
 * @Description
 */
public class SpecialityParamConstant {

    public static final String TWO_OPERATOR_PLUS = "+";
    public static final String TWO_OPERATOR_MINUS = "-";
    public static final String TWO_OPERATOR_MULTIPLY = "*";
    public static final String TWO_OPERATOR_DIVIDE = "/";
    public static final String TWO_OPERATOR_LINK = "||";

    public static final String PARENTHESES = "()";
    public static final String PARENTHESES_START = "(";
    public static final String PARENTHESES_END = ")";
    public static final String BRACKETS_START = "[";
    public static final String BRACKETS_END = "]";

    public static final String FUNCTION_BIT_AND = "UTL_RAW.BIT_AND";
    public static final String FUNCTION_BIT_OR = "UTL_RAW.BIT_OR";
    public static final String FUNCTION_BIT_XOR = "UTL_RAW.BIT_XOR";
    public static final String FUNCTION_MOD = "MOD";
    public static final String FUNCTION_TRUNC = "TRUNC";
    public static final String FUNCTION_ROUND = "ROUND";
    public static final String FUNCTION_LOWER = "LOWER";
    public static final String FUNCTION_UPPER = "UPPER";
    public static final String FUNCTION_POWER = "POWER";
    public static final String FUNCTION_TO_NUMBER = "TO_NUMBER";
    public static final String FUNCTION_TO_CHAR = "TO_CHAR";
    public static final String FUNCTION_SUBSTR = "SUBSTR";
    public static final String FUNCTION_RANDSTR = "RANDSTR";
    public static final String FUNCTION_REPLACE = "REPLACE";
    public static final String FUNCTION_LENGTH = "LENGTH";
    public static final String FUNCTION_ASCII = "ASCII";
    public static final String FUNCTION_CHR = "CHR";
    public static final String FUNCTION_FIXEDSTR = "FIXEDSTR";
    public static final String FUNCTION_ROWNUMBER = "ROWNUMBER";
    public static final String FUNCTION_GETWHOLEDEVICECODE = "GETWHOLEDEVICECODE";
    public static final String RESOURCE_POOL_GETSTARTMAC = "GETSTARTMAC";
    public static final String RESOURCE_POOL_GETENDMAC = "GETENDMAC";
    public static final String RESOURCE_POOL_GETSTARTGPONSN = "GETSTARTGPONSN";
    public static final String RESOURCE_POOL_GETENDGPONSN = "GETENDGPONSN";
    public static final String RESOURCE_POOL_GETIMEI = "GETIMEI";
    public static final String RESOURCE_POOL_GETCTEI = "GETCTEI";
    public static final String RESOURCE_POOL_GETCMEI = "GETCMEI";
    public static final String RESOURCE_POOL_GETDSN = "GETD-SN";
    public static final String RESOURCE_POOL_MACSTART = "MacStart";
    public static final String RESOURCE_POOL_MACEND = "MacEnd";
    public static final String RESOURCE_POOL_GPONSN = "GponSn";
    public static final String RESOURCE_POOL_WHOLEDEVICECODE = "WholeDeviceCode";
    public static final String RESOURCE_POOL_D_SN = "D_SN";

    public static final String RESOURCE_POOL_DEVICETYPE = "DeviceType";
    public static final String RESOURCE_POOL_CMEI = "CMEI";
    public static final String RESOURCE_POOL_DB = "DB";
    public static final String RESOURCE_POOL_DEVICE_SERIAL_NUMBER = "DeviceSerialNumber";

    public static final char ZERO_CHAR = '0';

    public static final String MAC_PREVIEW_VALUE = "0019C651000F";

    public static final String GPON_SN_PREVIEW_VALUE = "ZTEGD0000001";

    public static final String GPON_SN_FUNCTION_PREFIX = "GponSn";
    public static final String D_SN_FUNCTION_PREFIX = "D_SN";

    public static final String WHOLE_DEVICE_CODE_FUNCTION_PREFIX = "WholeDeviceCode";
    public static final String MAC_ADD_FUNCTION_PREFIX = "MacAdd";
    public static final String STBID_FUNCTION_PREFIX = "STBID";
    public static final String OUI_FUNCTION_PREFIX = "OUI";
    public static final String SN_FUNCTION_PREFIX = "SN";

    public static final String NASN_FUNCTION_PREFIX = "getNASN";
    public static final String ASSIGNMENT_TEXT_FUNCTION_PREFIX = "Text";
    public static final String ASSIGNMENT_VARIABLE_FUNCTION_PREFIX = "Variable";

    public static final String ASSIGNMENT_TEXT_AND_RANGE_FUNCTION_PREFIX = "TextAndRange";
    public static final String ASSIGNMENT_BD_FUNCTION_PREFIX = "DB";

    public static final String INTERVAL_VALUE_FUNCTION_PREFIX = "IntervalValue";
    public static final String RANDOM_FUNCTION_PREFIX = "Random";
    public static final String PARENTHESES_START_REGULAR = "\\(";
    public static final String GPON_SN_FIXED_VALUE =  "ZTEG";

    public static final String NACC_PREVIEW_VALUE =  "000000001";

    public static final String SCRAMBLING_CODE_PREVIEW_VALUE =  "TXX0X8AN9YYCPN1";

    public static final String DEVICE_TYPE=  "ZXR10 6800";

    public static final String DB_RESOURCE_PREVIEW =  "%s0000001";
    public static final String SN_NINE_DIGIT_PREVIEW =  "%s000000001";
    public static final String SN_SEVEN_DIGIT_PREVIEW =  "%s0000001";
    public static final String SO_NET_SN_PREVIEW =  "%s00001";

    public static final String WHOLE_DEVICE_CODE_PREVIEW =  "%s000000001";


}
