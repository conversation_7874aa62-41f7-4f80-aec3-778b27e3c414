package com.zte.common.enums;

/**
 * 业务场景枚举
 * <AUTHOR>
 */
public enum BusinessSceneEnum {
    /**
     * 制造
     */
    MANUFACTURE("MANUFACTUR","制造", 1),
    /**
     * 技改
     */
    MODIFIED("MODIFIED","改配", 2),
    /**
     * 返工
     */
    RETURN("RETURN","返工", 3),
    /**
     * 维修
     */
    REPAIR("REPAIR","维修", 4),
    /**
     * 拆解
     */
    DISASSEMBLY("DISASSEMBLY","拆解", 5),
    /**
     * BUFFER
     */
    BUFFER("BUFFER","buffer", 6);

    private String code;
    private String name;

    private Integer index;

    BusinessSceneEnum(String code, String name, Integer value){
        this.code = code ;
        this.name = name ;
        this.index = value ;
    }

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }

    public Integer getIndex() {
        return index;
    }

    public static BusinessSceneEnum getEnum(String code) {
        for (BusinessSceneEnum tempEnum : BusinessSceneEnum.values()) {
            if (tempEnum.getCode().equals(code)) {
                return tempEnum;
            }
        }
        return null;
    }
    /**
     * 通过编码获取名称
     *
     * @param code
     * @return
     */
    public static String getNameFromCode(String code) {
        for (BusinessSceneEnum statusEnum : BusinessSceneEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return code;
    }
}


