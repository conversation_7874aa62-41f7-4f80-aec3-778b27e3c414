package com.zte.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推送状态枚举,1-已推送消息未收到阿里回调通知，2-已推送消息已收到阿里回调通知
 */
@Getter
@AllArgsConstructor
public enum PushStatusEnum {
    /**
     * 待推送
     */
    NOT_PUSHED(0, "待推送"),

    /**
     * 已推送未回调
     */
    PUSHED_NOT_CALLBACK(1, "已推送未回调"),

    /**
     * 已推送回调成功
     */
    PUSHED_AND_CALLBACK(2, "已推送回调成功"),

    /**
     * 数据校验失败或推送异常
     */
    DATA_CHECK_OR_PUSH_FAIL(8, "数据校验失败或推送异常"),

    /**
     * 回调结果异常
     */
    CALLBACK_ERROR(9, "回调结果异常"),

    ;

    private Integer code;
    private String name;

    /**
     * 通过编码获取名称
     *
     * @param code
     * @return
     */
    public static String getNameFromCode(int code) {
        for (PushStatusEnum statusEnum : PushStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return String.valueOf(code);
    }
}
