package com.zte.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举,1-人工，2-定时任务
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    /**
     * 人工
     */
    MANUAL("1", "人工"),
    /**
     * 定时任务
     */
    SCHEDULED_TASK("2", "定时任务"),
    ;
    private String code;
    private String name;

    /**
     * 通过编码获取名称
     *
     * @param code
     * @return
     */
    public static String getNameFromCode(String code) {
        for (OperateTypeEnum statusEnum : OperateTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return code;
    }

    public boolean isMe(String code){
        return this.getCode().equals(code);
    }
}
