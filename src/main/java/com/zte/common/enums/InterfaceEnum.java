package com.zte.common.enums;

/**
 * 接口信息枚举
 *
 * <AUTHOR>
 */
public enum InterfaceEnum {
    /**
     * 按物料查询接口
     */
    centerFactoryItpFind("zte-mes-manufactureshare-centerfactory", "ipt/find", "GET", "按物料查询接口"),
    centerFactoryFindListByLookupType("zte-mes-manufactureshare-centerfactory", "BS/findListByLookupType", "GET", "根据lookupType查询数据字典项信息"),
    dataWbSysBasBarcodeInfoFind("zte-mes-manufactureshare-datawbsys", "basBarcodeInfo/find/", "GET", "根据任务号查询条码信息"),
    dataWbErpItemPageList("zte-mes-manufactureshare-datawbsys", "/erpItemList/pagelist", "GET", "根据任务号查询库存信息"),
    dataWbErpItemList("zte-mes-manufactureshare-datawbsys", "/erpItemList/getTaskNoList", "POST", "查询所有未完工的任务号"),
    wareHouseInfoList("zte-mes-manufactureshare-productiondeliverysys", "/linesideWarehouseInfoCtrl/linesideWarehouseInfo", "GET", "查询分工厂id及仓库名称"),
    getPsTaskInfoByProdplanId("zte-mes-manufactureshare-planschedulesys", "PS/getPsTaskInfoByProdplanId", "GET", "根据prodplanId获取psTask信息"),
    getPsTaskByProdplanIdList("zte-mes-manufactureshare-planschedulesys", "PS/getPsTaskByProdplanIdList", "POST", "根据prodplanId获取psTask信息"),
    getIsHasDirFlag("zte-mes-manufactureshare-datawbsys", "bis/getIsHasDirFlag", "POST", "从回写服务查询物料方向性信息"),
    getListByItemAndVersion("zte-mes-manufactureshare-datawbsys", "pdmSelectLogger/getListByItemAndVersion", "POST", "根据料单以及版本获取推送结果"),
    updateForCenter("zte-mes-manufactureshare-datawbsys", "pdmSelectLogger/updateForCenter", "POST", "更新物料代码、版本在MES推送结果中数据（推送状态改为“待推送”，是否有装配关系字段根据界面选择更新），同时将数据写入推送历史表"),
    updateOrInsertPkcodeInfo("zte-mes-manufactureshare-productionmgmtsys", "pkCodeInfoCtrl/rdOwnMaterialsRegisterToLocal", "POST", "研发自带料注册写入本地pkcodeinfo及历史"),

    uploadResourceInfoToMes("zte-mes-manufactureshare-datawbsys", "/completeMachineDataLog/uploadResourceInfoToMes", "POST", "回写资源信息"),

    getMacByResourceNumber("zte-mes-manufactureshare-datawbsys", "/completeMachineDataLog/getMacByResourceNumber", "POST", "根据资源号查MAC"),
    updateOrInsertForLateReelIdBind("zte-mes-manufactureshare-productionmgmtsys", "pkCodeInfoCtrl/lateReelIdBindToLocal", "POST", "技改注册，将数据写入本地并推送历史表"),
    splitReelId("zte-mes-manufactureshare-productionmgmtsys", "pkCodeInfoCtrl/splitReelId", "POST", "ReelId拆分写入本地工厂"),
    getWarehouseEntryInfoDTOListByTaskNos("zte-mes-manufactureshare-productionmgmtsys", "/warehouseEntryInfo/getWarehouseEntryInfoDTOListByTaskNos", "POST", "通过任务号查询入库单信息"),
    taskStockQuery("zte-mes-manufactureshare-productionmgmtsys", "/warehouseEntryInfo/taskStockQuery", "POST", "通过任务号查询入库数量"),
    getProdPlan("zte-mes-manufactureshare-datawbsys", "baProdPlan/getProdPlan", "GET", "获取SPM批次"),
    delegateToLocalFactoryValidate("zte-mes-manufactureshare-planschedulesys", "PS/validateApsUpdateTaskInfo", "POST", "将任务委派给所属地方工厂进行校验"),
    delegateToLocalFactoryUpdate("zte-mes-manufactureshare-planschedulesys", "PS/updateImesAndApsTaskInfo", "POST", "将任务委派给所属地方工厂进行更新"),
    updateQtyByTaskNo("zte-mes-manufactureshare-planschedulesys", "PS/updateQtyByTaskNo", "POST", "根据任务号更新任务数量或指令数量"),
    syncPsTaskToSys("zte-mes-manufactureshare-planschedulesys", "PS/syncPsTaskToSys", "POST", "同步中心工厂任务到本地"),
    insertTechnicalInfo("zte-mes-manufactureshare-datawbsys", "technicalChangeBarcodeCtrl/insertInfoToMes", "POST", "调用MES存储过程新增技改数据"),
    deleteTechnicalInfo("zte-mes-manufactureshare-datawbsys", "technicalChangeBarcodeCtrl/deleteTechnicalInfo", "POST", "按批次维度删除MES技改数据"),
    deleteTechnicalInfoBySn("zte-mes-manufactureshare-datawbsys", "technicalChangeBarcodeCtrl/deleteTechnicalInfoBySn", "POST", "按条码维度删除MES技改数据"),
    getBarSubmitBatch("zte-mes-manufactureshare-datawbsys", "/kxStepBarSubmitCtrl/selectBarSubmitWithParam", "POST", "根据同步起止时间等查询kxbariii.bar_submit"),
    synchronizeSpmDate("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/synchronizeSpmDate", "POST", "SPM同步技改数据到IMES"),
    synchronizeSnSpmData("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/synchronizeSnSpmData", "POST", "SPM同步技改条码数据到IMES"),
    synchronizeLastDayFinishTech("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/synchronizeLastDayFinishTech", "POST", "SPM同步最近一天技改完成数据到IMES"),
    synchronizeFinishTechSPMData("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/synchronizeFinishTechSPMData", "POST", "SPM同步技改完成数据数据到IMES"),
    pageSelectSPMDateForTechnicalChange("zte-mes-manufactureshare-datawbsys", "boardTechProcessMaintain/pageSelectSPMDateForTechnicalChange", "POST", "SPM同步技改数据-查询SPM技改信息"),
    pageSelectSPMDateSnForTechnicalChange("zte-mes-manufactureshare-datawbsys", "boardTechProcessMaintain/pageSelectSPMDateSnForTechnicalChange", "POST", "SPM同步技改数据-查询SPM技改条码信息"),
    getItemNoByProdplanId("zte-mes-manufactureshare-datawbsys", "/model/getItemNoByProdplanId", "POST", "入库数据统计获取bom_no"),
    getBomInfoByBomNoList("zte-mes-manufactureshare-datawbsys", "baBomHeadCtrl/getBomInfoByBomNoList", "POST", "获取BOM信息"),
    queryBomInfoByBomNoList("zte-mes-manufactureshare-datawbsys", "baBomHeadCtrl/queryBomInfoByBomNoList", "POST", "根据料单代码获取bom信息及料单类别"),
    selectProdPlanImesInfoByProdPlanIdList("zte-mes-manufactureshare-datawbsys", "/boardpredictout/selectProdPlanImesInfoByProdPlanIdList", "POST", "获取SPM批次信息"),
    pullPreBomSPMByPage("zte-mes-manufactureshare-datawbsys", "/ProdSmtWriteCtl/pullPreBomSPMByPage", "POST", "获取SPM料单级前加工数据"),
    insertPreBomBatch("zte-mes-manufactureshare-datawbsys", "/SpmProdSmtWriteCtl/insertPreBomBatch", "POST",
            "批量新增料单级前加工数据"),
    insertPreItemBatch("zte-mes-manufactureshare-datawbsys", "/SpmProdSmtWriteCtl/insertPreItemBatch", "POST",
            "批量新增物料级前加工数据"),
    deletePreItemByItemNoList("zte-mes-manufactureshare-datawbsys", "/SpmProdSmtWriteCtl/deletePreItemByItemNoList",
            "DELETE",
            "根据物料代码删除物料级前加工数据"),
    deletePreBomByBomList("zte-mes-manufactureshare-datawbsys", "/SpmProdSmtWriteCtl/deletePreBomByBomList",
            "DELETE",
            "根据料单代码删除前加工数据"),
    insertOrUpdateBomPre("zte-mes-manufactureshare-datawbsys", "/SpmProdSmtWriteCtl/insertOrUpdateBomPre",
            "POST",
            "维护料单前加工同步至MES"),
    pageSelectFinishTechSPMData("zte-mes-manufactureshare-datawbsys", "boardTechProcessMaintain" +
            "/pageSelectFinishTechSPMData", "POST", "SPM技改数据同步-已完成技改数据"),
    postBoardStoveMaintenanceSpm("zte-mes-manufactureshare-datawbsys", "BoardStoveMaintenanceCtl/pageList", "POST",
            "根据炉温关系信息"),
    boardScrapSelectDetailPage("zte-mes-manufactureshare-datawbsys", "BoardScrapApplicationCtl/selectDetailPage", "POST",
            "分页获取报废明细"),
    boardScrapSelectProdUllPage("zte-mes-manufactureshare-datawbsys", "BoardScrapApplicationCtl/selectProdUllPage", "POST",
            "分页获取报废损耗明细"),
    getTechnicalAndLockInfoByProdplanId("zte-mes-manufactureshare-openapi", "technicalAndLock/getTechnicalAndLockInfoByProdplanId", "GET",
            "根据批次获取技改以及锁定信息"),
    moveTechnicalAndLockInfoByProdplanId("zte-mes-manufactureshare-openapi", "technicalAndLock/moveTechnicalAndLockInfoByProdplanId", "POST",
            "迁移技改以及锁定单信息"),
    getSPMLockInformation("zte-mes-manufactureshare-datawbsys", "/qakxonlinebomlocked/pageList", "GET", "获取SPM锁定信息"),
    getBoardOnlineBatch("zte-mes-manufactureshare-datawbsys", "/kxStepBoardOnlineCtrl/selectBoardOnlineWithParam", "POST", "根据imuId、批次等参数查询kxbariii.board_online"),
    specialTechDataSynchronization("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/specialTechDataSynchronization", "POST", "SPM同步特殊技改数据数据到IMES"),
    pageSelectSpecialSPMDataSn("zte-mes-manufactureshare-datawbsys", "boardTechProcessMaintain/pageSelectSpecialSPMDataSn", "POST", "SPM特殊技改数据同步-条码-分页查询"),
    downLoadFileByKey("zte-mes-manufactureshare-externalserv", "file/download", "GET", "从文档云中心下载文件"),
    specialSnTechDataSynchronization("zte-mes-manufactureshare-productionmgmtsys", "techChgCtrl/specialSnTechDataSynchronization", "POST", "SPM同步特殊技改条码数据数据到IMES"),
    deleteSolderOriginalInfo("zte-mes-manufactureshare-openapi", "SparePartsStorage/deleteSolderOriginalInfo", "POST", "调拨入库删除原工厂锡膏信息"),
    deleteFixtureOriginalInfo("zte-mes-manufactureshare-openapi", "SparePartsStorage/deleteFixtureOriginalInfo", "POST", "调拨入库删除原工厂工装信息"),
    deleteStencilsOriginalInfo("zte-mes-manufactureshare-openapi", "SparePartsStorage/deleteStencilsOriginalInfo", "POST", "调拨入库删除调出工厂钢网信息"),
    queryRelPcbInfo("zte-mes-manufactureshare-openapi", "SparePartsStorage/queryRelPcbInfo", "POST", "调拨入库获取钢网与单板对应关系信息"),

    queryTaskInfoByTaskNoList("zte-mes-manufactureshare-openapi", "PS/queryTaskInfoByTaskNoList", "POST", "查询任务数量信息"),
    updateLocalFactoryQty("zte-mes-manufactureshare-openapi", "PS/updateLocalFactoryQty", "POST", "任务数量有变更时，更新本地工厂相关信息"),
    forwardingRequest("zte-mes-manufactureshare-openapi", "commonForwardCtl/forwardingRequest", "POST", "中心工厂访问分工厂调用openapi通用接口"),

    checkCACertificateStandardBarcode("zte-mes-manufactureshare-openapi", "CACertificate/checkCACertificateStandardBarcode", "POST", "调用OPENAPI服务，得到条码的校验结果"),
    updateCSRStatusFromMES("zte-mes-manufactureshare-datawbsys", "mesUploadData/updateCSRStatus", "POST", "更新上传B2B的MES数据的状态"),
    updateOverAllUnitMeiTuanFromMES("zte-mes-manufactureshare-datawbsys", "overallUnit/updateOverAllUnitMeiTuanFromMES", "POST", "更新MES系统中美团整机生产质量数据回传记录表的推送状态"),
    updateFileLogMeiTuanFromMES("zte-mes-manufactureshare-datawbsys", "stationLogUpload/updateFileLogMeiTuanFromMES", "POST", "更新MES系统中美团测试文件上传日志表的推送状态"),
    updateMesInfoUploadLog("zte-mes-manufactureshare-datawbsys", "zmsIndicatorUpload/updateMesInfoUploadLog", "POST", "更新MES信息上传日志表的推送状态"),
    updateMesInfoUploadFailedLog("zte-mes-manufactureshare-datawbsys", "zmsForward/updateMesInfoUploadFailedLog", "POST", "更新MES消息主表的推送状态"),

    getSysLookupValuesList("zte-mes-manufactureshare-datawbsys", "zmsDeviceInventoryUpload/getSysLookupValuesList", "POST", "查询MES数据字典"),

    updateStockUploadLog("zte-mes-resourcewarehouse-datawb", "stepdt/updateStockUploadLog", "POST", "更新INFOR系统中B2B回传日志表的推送状态"),
    selectBarAccSignForSchTask("zte-mes-manufactureshare-datawbsys", "/completeMachineDataLog/selectBarAccSignForSchTask", "POST", "定时任务-根据条件分页查询MES已使用入网许可证"),

    dealHisDataWithNoFactoryId("zte-mes-manufactureshare-planschedulesys", "PS/dealHisDataWithNoFactoryId", "POST", "处理未发放到本地工厂的批次"),
    statByTroubleSmallCode("zte-mes-manufactureshare-datawbsys", "/mtnRepairLines/statByTroubleSmallCode", "GET", "根据维修小类统计维修信息-MES"),
    statByTroubleSmallCodeAndSiteNo("zte-mes-manufactureshare-datawbsys", "/mtnRepairLines/statByTroubleSmallCodeAndSiteNo", "GET", "根据维修小类和位号统计维修信息-MES"),
    selectByPage("zte-mes-manufactureshare-datawbsys", "/vMesReelid/selectByPage", "POST", "分页查询inforreelId发料历史"),
    getIncrementalItem("zte-mes-manufactureshare-datawbsys", "/mtlSystemItems/getIncrementalItem", "POST", "定时任务-获取mes物料信息"),
    queryInfoTransferOrder("zte-mes-manufactureshare-openapi", "PS/queryInfoTransferOrder", "POST",
                                           "查询本地工厂是否产生调拨单"),

    getWipExtendInfoByFormSnList("zte-mes-manufactureshare-productionmgmtsys", "PM/getWipExtendInfoByFormSnList", "POST",
            "查询本地工厂本地工厂装配关系"),
    getSubmitStatusBatch("zte-mes-manufactureshare-datawbsys", "opProdPlanInfo/getSubmitStatusBatch", "POST",
            "获取已经产生套料单的批次"),
    queryMaterialOrderNoByTaskNo("zte-mes-manufactureshare-datawbsys", "ExternalInterfacesCtl" +
            "/queryMaterialOrderNoByTaskNo", "POST", "获取改配任务条码"),
    queryPickListByTaskNos("zte-mes-manufactureshare-datawbsys", "ExternalInterfacesCtl" +
            "/queryPickListByTaskNos", "POST", "根据任务号查询领料单"),
    queryPickListCondition("zte-mes-manufactureshare-datawbsys", "ExternalInterfacesCtl" +
            "/queryPickListCondition", "POST", "根据任务号查询领料单信息"),
    queryProcPickDetailBatch("zte-mes-manufactureshare-datawbsys", "ExternalInterfacesCtl" +
            "/queryProcPickDetailBatch", "POST", "根据领料单号批量查询领料明细"),
    getMainItemNo("zte-mes-manufactureshare-datawbsys", "/baItemCtrl/getMainItemNo", "POST", "筛选为主代码的物料代码"),
    getMaterialsWarehouses("zte-mes-manufactureshare-datawbsys", "inforInquery" +
            "/getMaterialsWarehouses", "POST", "根据领料单查询仓库"),
    queryStatusByCondition("zte-mes-manufactureshare-datawbsys", "ediOrdersStatusViewCtl" +
            "/queryStatusByCondition", "POST", "获取央仓发料状态"),
    getSendMaterials("zte-mes-manufactureshare-datawbsys", "inforInquery" +
            "/getSendMaterials", "POST", "根据领料单号查询发料记录"),
    queryTotalNotComplete("zte-mes-manufactureshare-datawbsys", "inforInquery" +
            "/queryTotalNotComplete", "POST", "根据领料单号查询发料未完成数量总数"),
    taskBomChangeableQueryDelegate("zte-mes-manufactureshare-openapi", "PS/taskBomChangeableQueryDelegate", "GET",
            "分发到本地工厂校验aps是否可以更改任务的bom信息"),
    queryWarehouseByCode("zte-mes-manufactureshare-productiondeliverysys", "/linesideWarehouseInfoCtrl/linesideWarehouseInfo/", "GET", "查询仓库信息"),
    dataWbSysGetTaskNoStatusByErp("zte-mes-manufactureshare-datawbsys", "erpdt/getTaskNoStatusByErp", "POST", "首备、低位、退料查ERP确认计划跟踪单状态"),
    getOnHandQty("zte-mes-manufactureshare-productionmgmtsys", "/pmorgtransferorder/getOnHandQty", "POST", "ERP库存现有量信息查询接口");
    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 地址
     */
    private String url;

    /**
     * 请求方式
     */
    private String reqType;

    /**
     * 描述
     */
    private String desc;

    InterfaceEnum(String serviceName, String url, String reqType, String desc) {
        this.serviceName = serviceName;
        this.url = url;
        this.reqType = reqType;
        this.desc = desc;
    }

    public String getServiceName() {

        return serviceName;
    }

    public String getUrl() {

        return url;
    }

    public String getReqType() {

        return reqType;
    }

    public String getDesc() {

        return desc;
    }

}
