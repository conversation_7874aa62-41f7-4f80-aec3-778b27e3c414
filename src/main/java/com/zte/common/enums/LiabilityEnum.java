package com.zte.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 责任枚举,1阿里责任，2厂商责任）
 */
@Getter
@AllArgsConstructor
public enum LiabilityEnum {

    /**
     * 阿里责任
     */
    COMMAND("1", "阿里责任"),
    /**
     * 厂商责任
     */
    WORK_ORDER("2", "厂商责任"),
    ;
    private String code;
    private String name;

    /**
     * 通过编码获取名称
     *
     * @param code
     * @return
     */
    public static String getNameFromCode(String code) {
        for (LiabilityEnum statusEnum : LiabilityEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return code;
    }

    /**
     * 通过名称获取编码
     *
     * @param name
     * @return
     */
    public static String getCodeFromName(String name) {
        for (LiabilityEnum statusEnum : LiabilityEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getCode();
            }
        }
        return name;
    }

    public boolean isMe(String code){
        return this.getCode().equals(code);
    }
}
