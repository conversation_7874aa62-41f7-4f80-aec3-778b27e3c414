package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SqlUtils;
import com.zte.common.utils.StringWithChineseUtils;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.BoardInstructionCycleInfo;
import com.zte.domain.model.BoardProdDaily;
import com.zte.domain.model.PkCodeHistory;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.domain.model.datawb.BarSubmitDTO;
import com.zte.domain.model.datawb.BoardOnline;
import com.zte.interfaces.dto.AssemblyPushByHandRecordEntityDTO;
import com.zte.interfaces.dto.AvlDTO;
import com.zte.interfaces.dto.BarcodeNetSignDTO;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.interfaces.dto.CompletebindSupplierProduceDTO;
import com.zte.interfaces.dto.CompletebindSupplierProduceSelectDTO;
import com.zte.interfaces.dto.ContractCharaInfoQueryDTO;
import com.zte.interfaces.dto.ContractCharacterizationInfoDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.DeliverEntityCycleDTO;
import com.zte.interfaces.dto.DeliverSetsCycleDTO;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.MtlSystemItemsDTO;
import com.zte.interfaces.dto.PageAndIdRegionDTO;
import com.zte.interfaces.dto.PartsbarScanInfoDTO;
import com.zte.interfaces.dto.PickListResultDTO;
import com.zte.interfaces.dto.PmRepairDetailDTO;
import com.zte.interfaces.dto.PmRepairInfoStatDTO;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.interfaces.dto.ScanPartstoboardDTO;
import com.zte.interfaces.dto.SpSpecialityNalDTO;
import com.zte.interfaces.dto.SynchronizeSpmDateDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO;
import com.zte.interfaces.dto.TechnicalChangeExecInfoEntityDTO;
import com.zte.interfaces.dto.TechnicalSummaryInfoDTO;
import com.zte.interfaces.dto.WipEntityInfoDTO;
import com.zte.interfaces.dto.datawb.ApsProdModelCountDTO;
import com.zte.interfaces.dto.datawb.BoardStoveMaintenanceDTO;
import com.zte.interfaces.dto.datawb.EdiOrderStatusViewDTO;
import com.zte.interfaces.dto.datawb.OpProdplanDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.interfaces.dto.datawb.ProcPicklistDetail;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.interfaces.dto.datawb.ProdPlanDetailDTO;
import com.zte.interfaces.dto.datawb.ProdSmtWriteDTO;
import com.zte.interfaces.dto.datawb.UnitBasicDataDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 点对点调用DATAWB服务接口类
 *
 * <AUTHOR>
 */
@Service
public class DatawbRemoteService {

    private static final Logger logger = LoggerFactory.getLogger(DatawbRemoteService.class);

    @Autowired
    private ConstantInterface constantInterface;

    /**
     * 点对点调用查询视图kxstepiii.V_ST_ITEM_BARCODE
     *
     * @param
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public static JsonNode getStItemBarcode(Map<String, Object> map) {
        String getUrl = "/stItemBarcode/postListByItemBarcode";
        try {
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.DATAWB,
                    MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, getUrl,
                    JacksonJsonConverUtil.beanToJson(map), headerParamsMap);
            return JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
        } catch (Exception e) {
            logger.error(" 点对点调用失败 ", e);
        }
        return null;
    }

    public static List<UnitBasicDataDTO> listUitBasicData() throws Exception {
        String getUrl = "/boardFirstwarehouse/listUnitBasicData";
        List<UnitBasicDataDTO> dtos;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, null);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOARD_FIRST_HOUSE_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            dtos = (List<UnitBasicDataDTO>)
                    JSONArray.parseArray(bo, UnitBasicDataDTO.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOARD_FIRST_HOUSE_SERVICE_ERROR);
        }
        return dtos;
    }


    public static OpProdplanDTO getProdplanDtoByNo(String prodplanNo) throws Exception {
        String getUrl = "/opProdplan/getCycleDtoByNo/" + prodplanNo;
        OpProdplanDTO plan;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, null);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            /*plan = (OpProdplanDTO) JacksonJsonConverUtil
                    .jsonToListBean(bo, new TypeReference<OpProdplanDTO>() { });*/
            plan = JSONObject.parseObject(bo, OpProdplanDTO.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_SERVICE_ERROR);
        }
        return plan;
    }

    public static List<OpProdplanDTO> getProdplanDtoByDate(String stDate) throws Exception {
        String getUrl = "/opProdplan/getCycleDtoByDate/" + stDate;
        List<OpProdplanDTO> plans;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, null);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            plans = (List<OpProdplanDTO>) JSONArray.parseArray(bo, OpProdplanDTO.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLAN_SERVICE_ERROR);
        }
        return plans;
    }


    public static CompletebindSupplierProduceDTO getReturnList(CompletebindSupplierProduceDTO dto)
            throws RouteException, JsonProcessingException, IOException {

        String contractNumber = dto.getContractNumber();
        String submachineNumber = dto.getSubmachineNumber();
        String billNumber = dto.getBillNumber();
        //String startDate = "";
        //String endDate = "";

        if (StringHelper.isEmpty(contractNumber) && StringHelper.isEmpty(submachineNumber)
                && StringHelper.isEmpty(billNumber)) {
            return dto;
        }
        // StringHelper.isNotEmpty(startDate) || StringHelper.isNotEmpty(endDate)
        CompletebindSupplierProduceSelectDTO selectdto = new CompletebindSupplierProduceSelectDTO();
        selectdto.setContractNumber(contractNumber);
        selectdto.setSubmachineNumber(submachineNumber);
        selectdto.setBillNumber(billNumber);
        //selectdto.setStartDate(startDate);
        //selectdto.setEndDate(endDate);

        String params = JacksonJsonConverUtil.beanToJson(selectdto);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        // 点对点调用服务
        String serviceName = MicroServiceNameEum.DATAWB;
        String version = MicroServiceNameEum.VERSION;
        String sendType = MicroServiceNameEum.SENDTYPEPOST;
        String getUrl = "/CSP/selectSubmacScanInfoList";

        String getresult = MicroServiceRestUtil.invokeService(serviceName, version, sendType, getUrl,
                params, headerParamsMap);

        List<String> completeSnBatch = null;
        if (null != getresult && StringHelper.isNotEmpty(getresult)) {
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
            if (null != json) {
                JsonNode boNode = json.get("bo");
                if (null != boNode) {
                    completeSnBatch = (List<String>) JacksonJsonConverUtil.jsonToListBean(boNode.toString(),
                            new TypeReference<ArrayList<String>>() {
                            });
                }
            }
        }
        if (null != completeSnBatch && completeSnBatch.size() > Constant.NUM_ZERO) {
            dto.setCompleteSnBatch(completeSnBatch);
        } else {
            dto.setExistdata(false);
        }
        return dto;
    }

    /**
     * 通过ERP入库日时间段从dataWb服务 获取任务周期数据
     *
     * @param startTime ERP入库日 开始时间
     * @param endTime   ERP入库日 结束时间
     * @return 列表数据
     * @throws Exception
     */
    public static List<DeliverEntityCycleDTO> getEntityByDate(String startTime, String endTime)
            throws Exception {
        String getUrl = "/deliverEntityCycleCtrl/listByDate";
        Map<String, Object> map = new HashMap<>(2);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        List<DeliverEntityCycleDTO> list;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, map);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_ENTITY_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<DeliverEntityCycleDTO>)
                    JSONArray.parseArray(bo, DeliverEntityCycleDTO.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_ENTITY_SERVICE_ERROR);
        }
        return list;
    }


    public static List<DeliverSetsCycleDTO> getSetsByDate(String date) throws Exception {
        String getUrl = "/deliverSetsCtrl/getDataByDate/";
        List<DeliverSetsCycleDTO> dtos;
        Map<String, Object> map = new HashMap<>();
        map.put("startDate", date);
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, map);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            dtos = (List<DeliverSetsCycleDTO>)
                    JSONArray.parseArray(bo, DeliverSetsCycleDTO.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
        }
        return dtos;
    }

    public static List<BoardProdDaily> getBoardProdDaily() throws Exception {
        String getUrl = "/boardDailyReport/listBoardProdDaily/";
        List<BoardProdDaily> dtos;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, null);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            dtos = (List<BoardProdDaily>)
                    JSONArray.parseArray(bo, BoardProdDaily.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
        }
        return dtos;
    }

    public static List<BoardProdDaily> getBoardProdDailyByDate(Map<String, Object> map) throws Exception {
        String getUrl = "/boardDailyReport/listBoardProdDailyByDate/";
        List<BoardProdDaily> dtos;
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, map);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            dtos = (List<BoardProdDaily>)
                    JSONArray.parseArray(bo, BoardProdDaily.class);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVER_SETS_SERVICE_ERROR);
        }
        return dtos;
    }

    /**
     * AVL--回写查询型号
     *
     * @param sn
     * @return
     */
    public static PmRepairDetailDTO getStyleInfoByPtp(String sn) throws MesBusinessException {
        String getUrl = "/stItemBarcode/getListByItemBarcode";
        PmRepairDetailDTO pmRepairDetailDTO = new PmRepairDetailDTO();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("itemBarcode", sn);
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEGET, getUrl, paramsMap);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_STYLE_AND_BRAND_ERROR);
            }
            JsonNode bo = jsonNode.get(Constant.STR_BO);
            if (null != bo) {
                if (null != bo.get(sn)) {
                    String boInfo = bo.get(sn).toString();
                    PmRepairDetailDTO reDto = JsonConvertUtil.jsonToBean(boInfo, PmRepairDetailDTO.class);
                    if (reDto == null) {
                        return pmRepairDetailDTO;
                    }
                    pmRepairDetailDTO.setBgBrandNo(reDto.getBgBrandNo());
                    pmRepairDetailDTO.setStyle(reDto.getStyle());
                    pmRepairDetailDTO.setSupplierName(reDto.getSupplierName());
                }
            } else {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_STYLE_AND_BRAND_ERROR);
            }
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_STYLE_AND_BRAND_ERROR);
        }
        return pmRepairDetailDTO;
    }

    public static Map<String, Object> getAvlInfo(AvlDTO dto) throws MesBusinessException {
        String getUrl = "/avlCtrl/getAvlInfo";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("itemNo", dto.getItemNo());
        paramsMap.put("bomNo", dto.getBomNo());
        paramsMap.put("brandStyle", dto.getBrandStyle());
        try {
            JsonNode jsonNode = HttpRemoteService
                    .pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                            MicroServiceNameEum.SENDTYPEPOST, getUrl, paramsMap);
            if (null == jsonNode) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AVL_QUERY_ERROR);
            }
            String bo = jsonNode.get(MpConstant.JSON_BO).toString();
            return CommonUtils.jsonStringToMap(bo);
        } catch (Exception ex) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AVL_QUERY_ERROR);
        }
    }

    /**
     * 根据rownum分页获取每页entity_id区间（用于优化后续查询数据效率）
     *
     * @param
     * @return
     * @throws Exception
     */
    public static List<PageAndIdRegionDTO> getMinAndMaxEntityIdWithPage(ContractCharaInfoQueryDTO dto) throws Exception {
        String getUrl = "/wmesContractCharacterizationInfo/getMinAndMaxEntityIdWithPage";
        List<PageAndIdRegionDTO> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<PageAndIdRegionDTO>) JSONArray.parseArray(bo, PageAndIdRegionDTO.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 查询合同任务表征数据
     *
     * @param
     * @return
     * @throws Exception
     */
    public static List<ContractCharacterizationInfoDTO> getContractCharaInfo(ContractCharaInfoQueryDTO dto) throws Exception {
        String getUrl = "/wmesContractCharacterizationInfo/getList";
        List<ContractCharacterizationInfoDTO> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<ContractCharacterizationInfoDTO>) JSONArray.parseArray(bo, ContractCharacterizationInfoDTO.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 查询合同任务表征数据ID列表
     *
     * @param
     * @return
     * @throws Exception
     */
    public static List<String> getEntityIdList(ContractCharaInfoQueryDTO dto) throws Exception {
        String getUrl = "/wmesContractCharacterizationInfo/getEntityIdList";
        List<String> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<String>) JSONArray.parseArray(bo, String.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 查询任务号查询整机名称和数量
     *
     * @param entityNames
     * @return
     * @throws Exception
     */
    public static List<ApsProdModelCountDTO> getApsProdModelCountByEntityNames(Collection<String> entityNames) throws Exception {

        List<ApsProdModelCountDTO> list = new ArrayList<>();
        String getUrl = "/apsProdModelCount/getList";
        if (CollectionUtils.isEmpty(entityNames)) {
            return list;
        }
        try {
            ApsProdModelCountDTO dto = new ApsProdModelCountDTO();
            dto.setInEntityNames(SqlUtils.convertStrCollectionToSqlType(entityNames));
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<ApsProdModelCountDTO>) JSONArray.parseArray(bo, ApsProdModelCountDTO.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 根据物料代码获取物料名称
     *
     * @param itemNo
     * @return
     * @throws MesBusinessException
     */
    public static String getItemNameByItemNo(String itemNo) throws MesBusinessException {
        String itemName = Constant.STRING_EMPTY;
        if (StringUtils.isBlank(itemNo)) {
            return itemName;
        }
        String getUrl = "/baItemCtrl/getItemName/" + itemNo;
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, null);
            if (null == jsonNode || jsonNode.get(Constant.STR_CODE) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText())) {
                String[] param = {jsonNode.get(Constant.STR_CODE).get(Constant.STR_MSG).asText()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            itemName = jsonNode.get(Constant.STR_BO).asText();
        } catch (Exception ex) {
            ex.printStackTrace();
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return itemName;
    }

    /**
     * 根据首件入库日期查询数量
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws MesBusinessException
     */
    public static long getCountByFirstwarehouseBj(String startTime, String endTime) throws MesBusinessException {
        long count = NumConstant.NUM_ZERO;

        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return count;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);

        String getUrl = "/boardFirstwarehouse/getCountByFirstwarehouseBj";
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, map);
            if (null == jsonNode || jsonNode.get(Constant.STR_CODE) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText())) {
                String[] param = {jsonNode.get(Constant.STR_CODE).get(Constant.STR_MSG).asText()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (jsonNode.get(Constant.STR_BO) == null) {
                return count;
            }
            count = jsonNode.get(Constant.STR_BO).asLong();
        } catch (Exception ex) {
            ex.printStackTrace();
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return count;
    }

    /**
     * 根据首件入库日期查询计划跟踪单号、批次、及首件入库日期
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws MesBusinessException
     */
    public static List<BoardInstructionCycleDataCreateDTO> getProdplanNoAndIdByFirstwarehouseBj(String startTime, String endTime, int startRow, int endRow) throws MesBusinessException {
        List<BoardInstructionCycleDataCreateDTO> list = new ArrayList<>();

        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return list;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("startRow", startRow);
        map.put("endRow", endRow);

        String getUrl = "/boardFirstwarehouse/getProdplanNoByFirstwarehouseBj";
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEGET, getUrl, map);
            if (null == jsonNode || jsonNode.get(Constant.STR_CODE) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText())) {
                String[] param = {jsonNode.get(Constant.STR_CODE).get(Constant.STR_MSG).asText()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (jsonNode.get(Constant.STR_BO) == null) {
                return list;
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<BoardInstructionCycleDataCreateDTO>) JSONArray.parseArray(bo, BoardInstructionCycleDataCreateDTO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 根据计划跟踪单号查询生产指令信息
     *
     * @param prodplanNoList
     * @return
     * @throws MesBusinessException
     */
    public static List<BoardInstructionCycleInfo> getBoardWorkorderInfoByProdplanNo(List<String> prodplanNoList) throws MesBusinessException {
        List<BoardInstructionCycleInfo> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodplanNoList)) {
            return list;
        }
        String getUrl = "/opProdplan/getBoardWorkorderInfoByProdplanNo";
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, prodplanNoList);
            if (null == jsonNode || jsonNode.get(Constant.STR_CODE) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText())) {
                String[] param = {jsonNode.get(Constant.STR_CODE).get(Constant.STR_MSG).asText()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (jsonNode.get(Constant.STR_BO) == null) {
                return list;
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<BoardInstructionCycleInfo>) JSONArray.parseArray(bo, BoardInstructionCycleInfo.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 根据bimu和批次查询指定工序首件日期
     *
     * @param prodplanIdList
     * @param bimuList
     * @return
     * @throws MesBusinessException
     */
    public static List<BoardInstructionCycleDataCreateDTO> getFirstDateOfBimuAndProdplanId(List<String> prodplanIdList, List<String> bimuList) throws MesBusinessException {
        List<BoardInstructionCycleDataCreateDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodplanIdList) || CollectionUtils.isEmpty(bimuList)) {
            return list;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("prodplanIdList", prodplanIdList);
        map.put("bimuList", bimuList);
        String getUrl = "/BoardOnlineStockDetail/getFirstDateOfBimuAndProdplanId";
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, map);
            if (null == jsonNode || jsonNode.get(Constant.STR_CODE) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(jsonNode.get(Constant.STR_CODE).get(Constant.STR_CODE).asText())) {
                String[] param = {jsonNode.get(Constant.STR_CODE).get(Constant.STR_MSG).asText()};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            if (jsonNode.get(Constant.STR_BO) == null) {
                return list;
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<BoardInstructionCycleDataCreateDTO>) JSONArray.parseArray(bo, BoardInstructionCycleDataCreateDTO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 根据料单代码获取bom信息
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public static List<BaBomHead> getBomInfoByBomNo(BaBomHead dto) throws Exception {
        String getUrl = "/baBomHeadCtrl/getBomInfoByBomNo";
        List<BaBomHead> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<BaBomHead>) JSONArray.parseArray(bo, BaBomHead.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    /**
     * 查根据itemId获取技术参数,封装,是否有AVL等信息
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public static List<BaBomHead> getParamByItemId(BaBomHead dto) throws Exception {
        String getUrl = "/baBomHeadCtrl/getParamByItemId";
        List<BaBomHead> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            list = (List<BaBomHead>) JSONArray.parseArray(bo, BaBomHead.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
        return list;
    }

    public static List<SynchronizeSpmDateDTO> pageSelectSPMDateForTechnicalChange(SynchronizeSpmDateDTO synchronizeSpmDateDTO,
                                                                                  int page, int row) throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        // 设置参数
        synchronizeSpmDateDTO.setPage(page);
        synchronizeSpmDateDTO.setRow(row);
        String params = JacksonJsonConverUtil.beanToJson(synchronizeSpmDateDTO);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.pageSelectSPMDateForTechnicalChange);
        String response = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    public static List<SynchronizeSpmDateDTO> pageSelectSPMDateSnForTechnicalChange(SynchronizeSpmDateDTO synchronizeSpmDateDTO,
                                                                                    int page, int row) throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        // 设置参数
        synchronizeSpmDateDTO.setPage(page);
        synchronizeSpmDateDTO.setRow(row);
        String params = JacksonJsonConverUtil.beanToJson(synchronizeSpmDateDTO);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.pageSelectSPMDateSnForTechnicalChange);
        String response = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    public static List<SynchronizeSpmDateDTO> pageSelectSpecialSPMDataSn(SynchronizeSpmDateDTO synchronizeSpmDateDTO,
                                                                                    int page, int row) throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        // 设置参数
        synchronizeSpmDateDTO.setPage(page);
        synchronizeSpmDateDTO.setRow(row);
        String params = JacksonJsonConverUtil.beanToJson(synchronizeSpmDateDTO);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.pageSelectSpecialSPMDataSn);
        String response = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    public static List<SynchronizeSpmDateDTO> selectLastDayFinishTechSPMData() throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        // 设置参数
        String getUrl = "/boardTechProcessMaintain/selectLastDayFinishTechSPMData";
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, getUrl, new HashMap<>());
        if (json == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, com.zte.domain.model.MessageId.CUSTOMIZE_MSG);
        }
        String jsonCode = json.get(Constant.JSON_CODE).get(Constant.JSON_CODE).asText();
        String msg = json.get(Constant.JSON_CODE).get(Constant.STR_MSG).asText();
        if (!Constant.SUCCESS_CODE.equals(jsonCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, com.zte.domain.model.MessageId.CUSTOMIZE_MSG,
                    new Object[]{msg});
        }
        String result = json.get(Constant.JSON_BO).toString();
        String bo = Constant.NULL.equals(result) ? null : result;
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    public static List<SynchronizeSpmDateDTO> pageSelectFinishTechSPMData(SynchronizeSpmDateDTO dto, int page, int row) throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        Map<String, String> headParam = MESHttpHelper.getHttpRequestHeader();
        // 设置参数
        dto.setPage(page);
        dto.setRow(row);
        String param = JacksonJsonConverUtil.beanToJson(dto);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.pageSelectFinishTechSPMData);
        String response = HttpClientUtil.httpPostWithJSON(url, param, headParam);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    public static List<SynchronizeSpmDateDTO> selectTechSPMDataOfHead() throws Exception {
        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        String getUrl = "/boardTechProcessMaintain/selectTechSPMDataOfHead";
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String responseStr = MicroServiceRestUtil.invokeService(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET,
                getUrl, JacksonJsonConverUtil.beanToJson(new HashMap<>()), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
        resultList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<SynchronizeSpmDateDTO>>() {
        });
        return resultList;
    }

    /**
     * 验证是否有提交套料单
     */
    public Boolean validateSubmitStatus(String prodPlanId) throws Exception {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String getUrl = "/opProdPlanInfo/validateSubmitStatus";
        Map<String, String> requestMap = new HashMap<>(Constant.INT_2);
        requestMap.put("prodPlanId", prodPlanId);
        JsonNode json = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, getUrl, requestMap);
        if (null == json || json.get(MpConstant.JSON_BO) == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_SPM_TLD_ERROR);
        }
        String strBo = json.get(MpConstant.JSON_BO).asText();
        if (org.apache.commons.lang.StringUtils.isBlank(strBo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_SPM_TLD_ERROR);
        }
        return Boolean.valueOf(strBo);
    }

    /**
     * 根据料单以及版本 查询各分工厂以及mes是否存在推送记录
     *
     * @throws Exception
     */
    public static AssemblyPushByHandRecordEntityDTO getListByItemAndVersion(String factoryName, String itemCode, String itemVersion) throws Exception {
        if (StringUtils.isEmpty(itemVersion) || StringUtils.isEmpty(itemCode)) {
            return null;
        }
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getListByItemAndVersion);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("bomRevision", itemVersion);
        paramsMap.put("itemCode", itemCode);
        String result = HttpRemoteService.remoteExe(InterfaceEnum.getListByItemAndVersion, paramsMap, headParams, url);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        return JacksonJsonConverUtil.jsonToBean(bo, AssemblyPushByHandRecordEntityDTO.class);
    }


    /**
     * 中心工厂更新物料代码、版本在MES推送结果中数据（推送状态改为“待推送”，是否有装配关系字段根据界面选择更新），同时将数据写入推送历史表
     *
     * @throws Exception
     */
    @RecordLogAnnotation("更新MES推送结果")
    public static Pair<Boolean, String> updateSiteResultByItemCodeAndVersion(AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO) throws Exception {
        String factoryName = assemblyPushByHandRecordEntityDTO.getFactoryName();
        ServiceData<List<AssemblyPushByHandRecordEntityDTO>> retData = new ServiceData<>();
        com.zte.itp.msa.core.model.RetCode retCode = new com.zte.itp.msa.core.model.RetCode(com.zte.itp.msa.core.model.RetCode.SUCCESS_CODE, com.zte.itp.msa.core.model.RetCode.SUCCESS_MSGID);
        retData.setCode(retCode);

        //校验参数
        if (StringUtils.isEmpty(assemblyPushByHandRecordEntityDTO.getItemCode())) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.ITEM_NO_NULL, assemblyPushByHandRecordEntityDTO.getItemCode()));
        }
        if (StringUtils.isEmpty(assemblyPushByHandRecordEntityDTO.getBomRevision())) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.ITEM_VERSION_NULL, assemblyPushByHandRecordEntityDTO.getItemVersion()));
        }
        if (StringUtils.isEmpty(assemblyPushByHandRecordEntityDTO.getCombineFlag())) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.PARAMS_ERROR, assemblyPushByHandRecordEntityDTO.getCombineFlag()));
        }
        String result = StringUtils.EMPTY;
        try {
            Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
            String url = ConstantInterface.getUrlStatic(InterfaceEnum.updateForCenter);
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("itemVersion", assemblyPushByHandRecordEntityDTO.getItemVersion());
            paramsMap.put("bomRevision", assemblyPushByHandRecordEntityDTO.getBomRevision());
            paramsMap.put("itemCode", assemblyPushByHandRecordEntityDTO.getItemCode());
            paramsMap.put("combineFlag", assemblyPushByHandRecordEntityDTO.getCombineFlag());
            result = HttpRemoteService.remoteExe(InterfaceEnum.updateForCenter, paramsMap, headParams, url);
        } catch (Exception e) {
            //报错时也将错误信息写入记录表
            String message = e.getMessage() == null ? "" : e.getMessage();
            List<String> strList = StringWithChineseUtils.getStrList(message, com.zte.common.utils.NumConstant.NUM_80);
            String msg = org.apache.commons.collections.CollectionUtils.isEmpty(strList) ? "" : strList.get(com.zte.common.utils.NumConstant.NUM_ZERO);
            return Pair.of(false, msg);
        }
        if (StringUtils.isEmpty(result)) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.FAILED_TO_UPDATE_RESULT, factoryName));
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        if (null == json) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.FAILED_TO_UPDATE_RESULT, factoryName));
        }
        String code = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!com.zte.itp.msa.core.model.RetCode.SUCCESS_CODE.equalsIgnoreCase(code)) {
            return Pair.of(false, factoryName + json.get(Constant.CODE).get(Constant.MSG).toString());
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        int count = Integer.parseInt(bo);
        //的呢工艺0 说明更新失败
        if (count == com.zte.common.model.NumConstant.NUM_ZERO) {
            return Pair.of(false, CommonUtils.getLmbMessage(MessageId.FAILED_TO_UPDATE_RESULT, factoryName + Constant.SEMICOLON + count));
        }
        return Pair.of(true, CommonUtils.getLmbMessage(MessageId.UPDATE_SUCCESSED, factoryName + Constant.SEMICOLON + count));
    }

    //从回写服务查询物料方向性数据
    public List<TaskMaterialIssueSeqEntityDTO> getIsHasDirFlag(List<PkCodeInfo> list) {
        // 点对点调用服务
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getIsHasDirFlag);
        String params = JacksonJsonConverUtil.beanToJson(list);
        String msg = HttpRemoteUtil.remoteExe(params, headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<TaskMaterialIssueSeqEntityDTO>>() {
        });
    }

    public void insertTechnicalInfo(TechnicalSummaryInfoDTO technicalSummaryInfoDTO){
        // 点对点调用服务
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.insertTechnicalInfo);
        String result = HttpRemoteUtil.remoteExe(JSON.toJSONString(technicalSummaryInfoDTO), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    public void deleteTechnicalInfo(TechnicalSummaryInfoDTO dto) {
        // 点对点调用服务
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.deleteTechnicalInfo);
        String result = HttpRemoteUtil.remoteExe(JSON.toJSONString(dto), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    public void deleteTechnicalInfoBySn(List<TechnicalChangeExecInfoEntityDTO> list){
        // 点对点调用服务
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.deleteTechnicalInfoBySn);
        String result = HttpRemoteUtil.remoteExe(JSON.toJSONString(list), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
    }

    /**
     * 根据同步起止时间等查询 kxbariii.bar_submit
     *
     * @return
     * @throws Exception
     */
    public List<BarSubmitDTO> getBarSubmitBatch(Map<String, Object> paramsMap) {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getBarSubmitBatch);
        String params = JacksonJsonConverUtil.beanToJson(paramsMap);
        String msg = HttpRemoteUtil.remoteExe(params, headParams, url, MicroServiceNameEum.SENDTYPEPOST);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BarSubmitDTO>>() {
        });
    }

    /**
     * 根据 imuIdList、同步起止时间等查询 kxbariii.board_online(分页参数必传)
     *
     * @return
     * @throws Exception
     */
    public Page<BoardOnline> getBoardOnlineBatch(BoardOnline paramDto) {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getBoardOnlineBatch);
        String params = JacksonJsonConverUtil.beanToJson(paramDto);
        String msgResult = HttpRemoteUtil.remoteExe(params, headParams, url, MicroServiceNameEum.SENDTYPEPOST);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<BoardOnline>>() {
        });
    }

    /**
     * 查询bom信息
     */
    public static List<BaBomHeadDTO> getBomInfoByBomNoList(List<String> bomNoList) {
        List<BaBomHeadDTO> baBomHeadDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bomNoList)) {
            return baBomHeadDTOList;
        }
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(bomNoList);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getBomInfoByBomNoList);
        String result = HttpClientUtil.httpPostWithJSON(url, params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        if (StringUtils.isBlank(bo)) {
            return baBomHeadDTOList;
        }
        List<BaBomHeadDTO> baBomHeadDTOTempList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<BaBomHeadDTO>>() {
        });
        if (!CollectionUtils.isEmpty(baBomHeadDTOTempList)) {
            baBomHeadDTOList.addAll(baBomHeadDTOTempList);
        }
        return baBomHeadDTOList;
    }

    /**
     * 根据批次获取料单代码
     *
     * @return
     * @throws Exception
     */
    public List<ProdPlanDetailDTO> getItemNoByProdplanId(Set<BigDecimal> prodSet) throws Exception {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getItemNoByProdplanId);
        String params = JacksonJsonConverUtil.beanToJson(prodSet);
        String msg = HttpRemoteUtil.remoteExe(params, headParams, url, MicroServiceNameEum.SENDTYPEPOST);
        if (StringUtils.isBlank(msg)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.QUERY_BOM_NO_NULL);
        }
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProdPlanDetailDTO>>() {
        });
    }

    //从spm获取半成品装配关系
    public static List<ScanPartstoboardDTO> getSemiWipExtFromSpm(ScanPartstoboardDTO dto, boolean isNull) throws Exception {
        // 点对点调用服务
        String url;
        if (isNull) {
            url = "/ScanPartstoboardCtrl/getNullSemiWipExtFromSpm";
        } else {
            url = "/ScanPartstoboardCtrl/getSemiWipExtFromSpm";
        }
        Map<String,Object> map = new HashMap<>();
        map.put("day", dto.getDay());
        map.put("strStartTime", dto.getStrStartTime());
        map.put("strEndTime", dto.getStrEndTime());
        map.put("rowId", dto.getRowId());
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null == json) {
            return null;
        }
        JsonNode codeObj = json.get(MpConstant.JSON_CODE);
        if (null != codeObj && RetCode.SUCCESS_CODE.equals(codeObj.get(MpConstant.JSON_CODE).asText())) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ScanPartstoboardDTO>>() {
            });
        }
        return null;
    }

    //从spm获取半成品装配关系
    public static List<PartsbarScanInfoDTO> getMaterialWipExtFromSpm(PartsbarScanInfoDTO dto) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("day", dto.getDay());
        map.put("rowId", dto.getRowId());
        map.put("strStartTime", dto.getStrStartTime());
        map.put("strEndTime", dto.getStrEndTime());
        // 点对点调用服务
        String url = "/PartsbarScanInfoCtrl/getMaterialWipExtFromSpm";
        JsonNode json = HttpRemoteService.pointToPointSelective(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEGET, url, map);
        if (null == json) {
            return null;
        }
        JsonNode codeObj = json.get(MpConstant.JSON_CODE);
        if (null != codeObj && RetCode.SUCCESS_CODE.equals(codeObj.get(MpConstant.JSON_CODE).asText())) {
            String bo = json.get(MpConstant.JSON_BO).toString();
            return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PartsbarScanInfoDTO>>() {
            });
        }
        return null;
    }

    /**
     * MES 新增物料级前加工数据
     *
     * @return 分页数据
     * @throws Exception 业务异常
     */
    public void insertPreItemBatch(List<ProdSmtWriteDTO> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.insertPreItemBatch);
        String params = JSON.toJSONString(list);
        ;
        String msgResult = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * MES 删除 物料级前加工物料 根据物料代码
     *
     * @param itemList 物料代码
     * @throws Exception 业务异常
     */
    public void deletePreItemByItemNoList(List<String> itemList) throws Exception {
        ProdSmtWriteDTO dto = new ProdSmtWriteDTO();
        dto.setItemList(itemList);
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.deletePreItemByItemNoList);
        String params = JSON.toJSONString(dto);
        String msgResult = HttpClientUtil.httpDeleteWithJson(url, params, headParams);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * MES 删除 料单级前加工物料 根据料单代码
     *
     * @param itemList 物料代码
     * @param bomList  料单代码
     * @throws Exception 业务异常
     */
    public void deletePreBomByBomList(List<String> bomList, List<String> itemList) throws Exception {
        ProdSmtWriteDTO dto = new ProdSmtWriteDTO();
        dto.setItemList(itemList);
        dto.setBomList(bomList);
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.deletePreBomByBomList);
        String params = JSON.toJSONString(dto);
        String msgResult = HttpClientUtil.httpDeleteWithJson(url, params, headParams);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 同步imes 料单前加工数据到MES
     *
     * @param prodSmtWriteDTO 料单对象
     * @throws Exception 业务异常
     */
    public void insertOrUpdateBomPre(ProdSmtWriteDTO prodSmtWriteDTO) throws Exception {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.insertOrUpdateBomPre);
        String params = JSON.toJSONString(prodSmtWriteDTO);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 通过炉温名称获取料单信息
     *
     * @param current 当前页
     * @return 对应关系
     * @throws Exception 业务异常
     */
    public static Page<BoardStoveMaintenanceDTO> postBoardStoveMaintenanceSpm(int current) throws Exception {
        Page<BoardStoveMaintenanceDTO> page = new Page<>();
        BoardStoveMaintenanceDTO queryDTO = new BoardStoveMaintenanceDTO();
        page.setPageSize(Constant.INT_2000);
        page.setSearchCount(false);
        page.setParams(queryDTO);
        page.setCurrent(current);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.postBoardStoveMaintenanceSpm);
        String msg = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(page), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<Page<BoardStoveMaintenanceDTO>>() {
        });
    }

    /**
     * 获取SPM锁定信息
     * @param page
     * @param rows
     * @return
     * @throws Exception
     */
    public List<QaKxonlineBomlockedEntityDTO> getSPMLockInformation(Integer page,Integer rows) {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getSPMLockInformation);
        Map<String,Integer> params = new HashMap<>();
        params.put("page",page);
        params.put("rows",rows);
        String msgResult = HttpRemoteUtil.remoteExe(JSON.toJSONString(params),headParams,url,MicroServiceNameEum.SENDTYPEGET);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        Page<QaKxonlineBomlockedEntityDTO>  bomlockedEntityDTOPage = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<Page<QaKxonlineBomlockedEntityDTO>>() {});
        return bomlockedEntityDTOPage.getRows();
    }

    /**
     * 调用MES数据对应推送接口
     *
     * <AUTHOR>
     */
    public void repushCustormerLogDataFromMES(List<CustomerDataLogDTO> groupList, String url) {
        List<String> taskNoList = groupList.stream().map(CustomerDataLogDTO::getTaskNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> snList = groupList.stream().map(CustomerDataLogDTO::getSn).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("autoFlag", Constant.N_STATUS);
        params.put("entityNameList", taskNoList);
        params.put("snList", snList);
        groupList.forEach(item->item.setJsonData(null));
        params.put("customerDataLogList", groupList);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        MicroServiceRestUtil.invokeService(MicroServiceNameEum.DATAWB,
                MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, url,
                JacksonJsonConverUtil.beanToJson(params), headerParamsMap);
    }

    public void uploadResourceInfoToMes(List<SpSpecialityNalDTO> spSpecialityNalDTOList,Map<String, String> headerParamsMap) {
        String url = constantInterface.getUrl(InterfaceEnum.uploadResourceInfoToMes);
        String msg = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(spSpecialityNalDTOList), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
    }

    public List<SpSpecialityNalDTO> getMacByResourceNumber(List<String> list) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getMacByResourceNumber);
        String msg = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(list), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<SpSpecialityNalDTO>>() {
        });
    }

    /**
     * CSR上传谷歌回调结果更新到MES库相关表的状态上
     * @param customerDataLogDTO
     */
    public void updateCSRStatusFromMES(CustomerDataLogDTO customerDataLogDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.updateCSRStatusFromMES);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(customerDataLogDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 更新MES系统中美团整机生产质量数据回传记录表的推送状态
     * @param customerDataLogDTO
     */
    public void updateOverAllUnitMeiTuanFromMES(CustomerDataLogDTO customerDataLogDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.updateOverAllUnitMeiTuanFromMES);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(customerDataLogDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 更新MES系统中美团测试文件上传日志表的推送状态
     * @param customerDataLogDTO
     */
    public void updateFileLogMeiTuanFromMES(CustomerDataLogDTO customerDataLogDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.updateFileLogMeiTuanFromMES);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(customerDataLogDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    public List<BarcodeNetSignDTO> selectBarAccSignForSchTask(BarcodeNetSignDTO barcodeNetSignDTO) {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.selectBarAccSignForSchTask);
        String params = JSON.toJSONString(barcodeNetSignDTO);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, params, headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<BarcodeNetSignDTO>>() {
        });
    }

    /**
     * 更新MES信息上传日志表的推送状态
     */
    public void updateMesInfoUploadLog(CustomerDataLogDTO customerDataLogDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.updateMesInfoUploadLog);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(customerDataLogDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 更新MES消息主表的推送状态为上传失败
     */
    public void updateMesInfoUploadFailedLog(CustomerDataLogDTO customerDataLogDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.updateMesInfoUploadFailedLog);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(customerDataLogDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
    }

    /**
     * 获取mes增量物料代码信息
     * @param dto
     */
    public static List<MtlSystemItemsDTO> getIncrementalItem(MtlSystemItemsDTO dto) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getIncrementalItem);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(dto), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<MtlSystemItemsDTO>>() {
        });
    }

    public static List<PmRepairInfoStatDTO> statByTroubleSmallCode(String itemCode, String userFaultDesc) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.statByTroubleSmallCode);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("itemCode", itemCode);
        paramsMap.put("userFaultDesc", userFaultDesc);
        String msgResult = HttpClientUtil.httpGet(url, paramsMap, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<PmRepairInfoStatDTO>>() {
        });
    }

    public static List<PmRepairInfoStatDTO> statByTroubleSmallCodeAndSiteNo(String itemCode, String userFaultDesc) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.statByTroubleSmallCodeAndSiteNo);
        Map<String, String> params = new HashMap<>();
        params.put("userFaultDesc", userFaultDesc);
        params.put("itemCode", itemCode);
        String msg = HttpClientUtil.httpGet(url, params, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<PmRepairInfoStatDTO>>() {
        });
    }

    /* Started by AICoder, pid:d9b9ab67e4ea46e8b7f93323bb824f9f */
    public List<SysLookupValuesDTO> getSysLookupValuesList(SysLookupValuesDTO dto) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = constantInterface.getUrl(InterfaceEnum.getSysLookupValuesList);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(dto), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<List<SysLookupValuesDTO>>() {
        });
    }
    /* Ended by AICoder, pid:d9b9ab67e4ea46e8b7f93323bb824f9f */

    /**
     * 分页查询infor reelId 发料历史
     *
     * @param page 分页参数
     * @return
     */
    public static Page<PkCodeHistory> selectByPageReelHis(Page<?> page) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.selectByPage);
        String msgResult = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(page), headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msgResult);
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<Page<PkCodeHistory>>() {
        });
    }

    /**
     * 获取已经产生套料单的批次
     *
     * @param prodPlanIdList 批次列表
     * @return 已经产产生套料单的批次
     * @throws Exception 业务异常
     */
    public static List<String> getSubmitStatusBatch(List<String> prodPlanIdList) throws Exception {
        String params = JacksonJsonConverUtil.beanToJson(prodPlanIdList);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getSubmitStatusBatch);
        String msg = HttpClientUtil.httpPostWithJSON(url, params, MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<String>>() {
        });
    }
    /**
     * 查询bom信息
     */
    public static List<BaBomHeadDTO> queryBomInfoByBomNoList(List<String> bomNoList) {
        List<BaBomHeadDTO> baBomHeadDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bomNoList)) {
            return baBomHeadDTOList;
        }
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String params = JacksonJsonConverUtil.beanToJson(bomNoList);
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.queryBomInfoByBomNoList);
        String result = HttpClientUtil.httpPostWithJSON(url, params, header);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<BaBomHeadDTO>>() {
        });
    }

    /**
     * 获取改配、拆解任务 条码清单
     *
     * @param taskNo 任务号
     * @return 条码清单
     */
    public static List<PickListResultDTO> queryMaterialOrderNoByTaskNo(String taskNo) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryMaterialOrderNoByTaskNo);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic , JSON.toJSONString(Collections.singletonList(taskNo)), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<PickListResultDTO>>() {
        });
    }

    /**
     * 根据料单代码集合获取bom信息
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public static List<BaBomHead> getBomInfoByBomNoListAndCodeType(BaBomHead dto) throws Exception {
        String getUrl = "/baBomHeadCtrl/getBomInfoByBomNoListAndCodeType";
        List<BaBomHead> list = new ArrayList<>();
        try {
            JsonNode jsonNode = HttpRemoteService.pointToPointSelectiveByObj(MicroServiceNameEum.DATAWB, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, getUrl, dto);
            if (null == jsonNode || jsonNode.get(Constant.STR_BO) == null) {
                String[] param = {getUrl};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
            }
            String bo = jsonNode.get(Constant.STR_BO).toString();
            return JSONArray.parseArray(bo, BaBomHead.class);
        } catch (Exception ex) {
            String[] param = {getUrl};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.INTERFACE_CALL_ERROR, param);
        }
    }

    /**
     * 首备、低位、退料查ERP确认计划跟踪单状态
     */
    public static List<WipEntityInfoDTO> getTaskNoStatusByErp(List<WipEntityInfoDTO> taskList) {
        Map<String, String> headParams = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.dataWbSysGetTaskNoStatusByErp);
        String msg = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(taskList), headParams);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo,
                new TypeReference<ArrayList<WipEntityInfoDTO>>() {
                });
    }

    /**
     * 根据时间范围查询领料单
     *
     * @param queryDTO 查询范围
     * @return 领料单
     */
    public static List<ProdPickListMainDTO> queryPickListByTaskNos(PickListQueryDTO queryDTO) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryPickListByTaskNos);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic , JSON.toJSONString(queryDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProdPickListMainDTO>>() {
        });
    }

    /**
     * 根据时间范围查询领料单
     *
     * @param queryDTO 查询范围
     * @return 领料单
     */
    public static List<ProdPickListMainDTO> queryPickListCondition(PickListQueryDTO queryDTO) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryPickListCondition);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic , JSON.toJSONString(queryDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProdPickListMainDTO>>() {
        });
    }

    /**
     * 根据领料单号批量获取 领料明细数据
     * @param billNumberList billNumberList
     * @return List<ProdPickListMainDTO>
     */
    public static List<ProcPicklistDetail> queryProcPickDetailBatch(List<String> billNumberList) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryProcPickDetailBatch);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic , JSON.toJSONString(billNumberList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<ProcPicklistDetail>>() {
        });
    }

    /**
     * 筛选为主代码的物料代码(区别于衍生码)
     * @param itemNoList
     * @return
     */
    public static List<String> getMainItemNo(List<String> itemNoList) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.getMainItemNo);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic , JSON.toJSONString(itemNoList), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<String>>() {
        });
    }

    /**
     * 根据领料单查询仓库
     *
     * @param billNos 领料单
     * @return 领料单及材料信息
     */
    public static List<EdiSoSDTO> getMaterialsWarehouses(List<String> billNos) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("billNos",billNos);
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.getMaterialsWarehouses);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic, JSON.toJSONString(map), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EdiSoSDTO>>() {
        });
    }

    /**
     * 获取央仓发料状态
     *
     * @param record 领料单
     * @return 领料单及材料信息
     */
    public static List<EdiOrderStatusViewDTO> queryStatusByCondition(EdiOrderStatusViewDTO record) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryStatusByCondition);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic, JSON.toJSONString(record), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EdiOrderStatusViewDTO>>() {
        });
    }

    /**
     * 获取央仓发料状态
     *
     * @param ediSoSDTO 领料单
     * @return 领料单及材料信息
     */
    public static List<EdiSoSDTO> getSendMaterials(EdiSoSDTO ediSoSDTO) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.getSendMaterials);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic, JSON.toJSONString(ediSoSDTO), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EdiSoSDTO>>() {
        });
    }

    /**
     * 查询发料未完成的物料代码总数
     *
     * @param params params
     * @return Integer
     */
    public static List<EdiSoSDTO> queryTotalNotComplete(EdiSoSDTO params) {
        String urlStatic = ConstantInterface.getUrlStatic(InterfaceEnum.queryTotalNotComplete);
        String msg = HttpClientUtil.httpPostWithJSON(urlStatic, JSON.toJSONString(params), MESHttpHelper.getHttpRequestHeader());
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<EdiSoSDTO>>() {});
    }
}
