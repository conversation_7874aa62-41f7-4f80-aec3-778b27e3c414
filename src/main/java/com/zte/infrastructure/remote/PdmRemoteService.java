package com.zte.infrastructure.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.constant.SnCaConstant;
import com.zte.infrastructure.feign.PdmInOneClient;
import com.zte.interfaces.dto.mbom.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 点对点调用DATAWB服务接口类
 * <AUTHOR>
 */
@Service
public class PdmRemoteService {

    private static final Logger logger = LoggerFactory.getLogger(PdmRemoteService.class);

    private static final String PLM_G_BOM_URL = "/gbom/v2/query";

    @Value("${pdm.x.system.code}")
    private String pdmSystemCode;
    @Value("${pdm.x.authentication.value}")
    private String pdmAuthValue;

    @Value("${inone.imes.app.code:}")
    private String appCode;
    @Value("${inone.base.url:}")
    private String inoneBaseUrl;
    @Resource
    private PdmInOneClient pdmInOneClient;

    /**
     * <AUTHOR>
     * 获取pdm物料生产属性
     * @param itemNo
     */
    @AlarmAnnotation(alarmName = "call_pdm_error", alarmKey = "9907", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    public List<Map<String, Object>> getItemProperty(String itemNo) {
        Map<String, Object> nosMap = new HashMap<>();
        nosMap.put("no", itemNo.substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE));
        nosMap.put("version", "");
        List<Map<String, Object>> nosList = new ArrayList<>();
        nosList.add(nosMap);
        Map<String, Object> blockMap = new HashMap<>();
        blockMap.put("block", MpConstant.BLOCK_G0001);
        blockMap.put("ver", MicroServiceNameEum.VERSION_TWO);
        List<Map<String, Object>> blockList = new ArrayList<>();
        blockList.add(blockMap);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("NOs", nosList);
        paramMap.put("infoBlocks", blockList);
        String getUrl = PLM_G_BOM_URL;

        JsonNode json = pointToPointPDM(SnCaConstant.PLM_IPRODUCT_GBOM, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, getUrl, paramMap);
        List<Map<String, Object>> product = new ArrayList<>();
        if(json != null && RetCode.SUCCESS_CODE
                .equals(json.get(SnCaConstant.RET_CODE).get(SnCaConstant.RET_CODE).textValue())){
            if(null!=json.get(SnCaConstant.RET_BO)) {
                JsonNode boJson = json.get(SnCaConstant.RET_BO).get(NumConstant.NUM_ZERO);
                if(null!=boJson && null!=boJson.get(Constant.PRODUCT)) {
                    product = JacksonJsonConverUtil.jsonToListBean(boJson.get(Constant.PRODUCT).toString(),
                            new TypeReference<List<Map<String, Object>>>() {
                            });
                }
            }
        } else{
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, SnCaConstant.GBOM_ERROR);
        }
        return product;
    }

    /**
     * 服务点对点调用
     */
    private JsonNode pointToPointPDM(String serviceName, String version, String sendType,
                                    String getUrl, Map<String, Object> map) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.put(SysConst.HTTP_HEADER_X_EMP_ID,MpConstant.X_EMP_NO);
        headerParamsMap.put("X-System-Code", pdmSystemCode);
        headerParamsMap.put("x-system-code", pdmSystemCode);
        headerParamsMap.put("X-Auth-Value", pdmAuthValue);
        try {
            String responseStr = MicroServiceRestUtil.invokeService(serviceName, version, sendType,
                    getUrl, JacksonJsonConverUtil.beanToJson(map), headerParamsMap);
            return JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
        } catch (Exception e) {
            logger.error("点对点调用异常", e);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, SnCaConstant.GBOM_ERROR);
        }
    }

    /**
     * 根据料单查询PDM产品实例
     * 会主动截取料单前12位
     * @param itemNo
     * @return
     */
    public ProdInstanceDTO queryProdInstance(String itemNo) {
        if (StringUtils.length(itemNo) < NumConstant.NUM_TWELVE) {
            return null;
        }
        // 截取前12位
        itemNo = StringUtils.substring(itemNo, NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE);
        String url = "/iproduct300/iproduct/gbom/instance/query";
        Map<String, String> httpRequestHeader = this.getHeader();

        Map<String, Object> params = new HashMap<>();
        params.put("instanceNo", itemNo);
        params.put("pageNum", NumConstant.NUM_ONE);
        params.put("pageSize", NumConstant.NUM_ONE);

        String msg = HttpClientUtil.httpPostWithJSON(inoneBaseUrl + url, JacksonJsonConverUtil.beanToJson(params), httpRequestHeader);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg, Constant.CALL_PDM_ERROR);
        PdmPage<ProdInstanceDTO> pdmPage = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<PdmPage<ProdInstanceDTO>>() {});
        if (Objects.isNull(pdmPage) || CollectionUtils.isEmpty(pdmPage.getRows())) {
            return null;
        }
        return pdmPage.getRows().get(NumConstant.NUM_ZERO);
    }

    /**
     * 根据料单代码查询PDM接口获取客户部件类型
     * @param itemNo
     * @return
     */
    public String getCategory(String itemNo) {
        ProdInstanceDTO prodInstanceDTO = this.queryProdInstance(itemNo);
        if (prodInstanceDTO == null || StringUtils.isBlank(prodInstanceDTO.getCustomPartType())) {
            return Constant.STRING_EMPTY;
        }
        return prodInstanceDTO.getCustomPartType();
    }

    private Map<String, String> getHeader() {
        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        httpRequestHeader.put("appCode", appCode);
        httpRequestHeader.put("X-System-Code", pdmSystemCode);
        httpRequestHeader.put("x-system-code", pdmSystemCode);
        httpRequestHeader.put("x-auth-value", pdmAuthValue);
        return httpRequestHeader;
    }


    /* Started by AICoder, pid:m0850wb864x0ca7143e50844c0128645400619c2 */
    @RecordLogAnnotation("GBOM下层或上层关系查询")
    @AlarmAnnotation(alarmName = "call_PDM_error", alarmKey = "9903", alarmTitle = "调用PDM接口异常或超时", alarmTime = 30000)
    @OpenApi(name = "queryMbomDetail", describe = "GBOM下层或上层关系查询", provider = {"PDM"})
    public List<GbomDetailDTO> queryGbomDetail(String itemNo) {
        if (StringUtils.isBlank(itemNo)) {
            return Collections.emptyList();
        }
        List<GbomDetailDTO> resultList = new LinkedList<>();
        GbomQueryDTO queryDTO = new GbomQueryDTO();
        queryDTO.setMsName(MicroServiceNameEum.CENTERFACTORY);
        queryDTO.setPageSize(Constant.INT_500);

        GbomQueryDTO.QueryCondition queryCondition = new GbomQueryDTO.QueryCondition();
        queryCondition.setQueryVer("v1");
        queryCondition.setQueryType("P0001");
        // 测试代码
//        itemNo = "125100740205";
        GbomQueryDTO.QueryParamDTO queryParamDTO = new GbomQueryDTO.QueryParamDTO();
        queryParamDTO.setObjectNo(itemNo);
        queryParamDTO.setVersion("latest");

        List<GbomQueryDTO.QueryParamDTO> listParam = new LinkedList<>();
        listParam.add(queryParamDTO);
        queryCondition.setProductNo(listParam);
        queryDTO.setQueryCondition(queryCondition);

        String url = "/iproduct300/iproduct/gbom/relation/queryInfo";
        Map<String, String> httpRequestHeader = this.getHeader();

        int current = 1;
        List<GbomDetailDTO> tempList = null;
        do {
            queryDTO.setPageNo(current);
            String msg = HttpClientUtil.httpPostWithJSON(inoneBaseUrl + url, JacksonJsonConverUtil.beanToJson(queryDTO), httpRequestHeader);
            String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg, Constant.CALL_PDM_ERROR);

            PdmPage<GbomDetailDTO> pdmPage = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<PdmPage<GbomDetailDTO>>() {});
            if (Objects.isNull(pdmPage) || CollectionUtils.isEmpty(pdmPage.getGbomItem())) {
                break;
            }

            tempList = pdmPage.getGbomItem();
            resultList.addAll(tempList);
            current++;
        } while (tempList.size() == queryDTO.getPageSize());
        return resultList;
    }
    /* Ended by AICoder, pid:m0850wb864x0ca7143e50844c0128645400619c2 */

    public Map<String, String> getItemNoToCustomerNumberMap(Collection<String> itemNos) {
        return CollUtil.split(itemNos, Constant.BATCH_SIZE).stream().flatMap(itemNoCollection -> {
            Page<PdmProdInstanceResDTO> pdmProdInstanceResDTOPdmPage = pdmInOneClient.queryProdInstance(new PdmProdInstanceQueryDTO().setInstanceNo(StrJoiner.of(StrUtil.COMMA).append(itemNoCollection).toString()).setPageNum(1).setPageSize(100));
            return StreamUtil.of(CollUtil.emptyIfNull(pdmProdInstanceResDTOPdmPage.getRows()));
        }).filter(item -> StrUtil.isNotBlank(item.getCustomNo())).collect(Collectors.toMap(PdmProdInstanceResDTO::getInstanceNo, PdmProdInstanceResDTO::getCustomNo, (o1, o2) -> o1));
    }

    public List<PdmProdInstanceResDTO> getPdmProdInstanceResDTOList(Collection<String> itemNos){
        return CollUtil.split(itemNos, Constant.BATCH_SIZE).stream().flatMap(itemNoCollection -> {
            Page<PdmProdInstanceResDTO> pdmProdInstanceResDTOPdmPage = pdmInOneClient.queryProdInstance(new PdmProdInstanceQueryDTO().setInstanceNo(StrJoiner.of(StrUtil.COMMA).append(itemNoCollection).toString()).setPageNum(1).setPageSize(100));
            return StreamUtil.of(CollUtil.emptyIfNull(pdmProdInstanceResDTOPdmPage.getRows()));
        }).collect(Collectors.toList());
    }
}
