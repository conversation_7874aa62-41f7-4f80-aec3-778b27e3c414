package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.UploadFileUtils;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.mds.MdsAccessTokenDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

@Service
@Slf4j
public class MdsRemoteService {

    @Value("${mds.token.cache.redundance.second:5}")
    private int tokenCacheRedundance;

    @Value("${model.feedback.production.station.test.data.url:''}")
    private String feedbackOfProductionStationTestUrl;
    @Value("${model.feedback.production.station.generateFile.url:''}")
    private String feedbackOfProductionStationTestingGenerateFileUrl;
    @Value("${model.feedback.production.meituan.queryRepairInfo:''}")
    private String queryRepairInfoUrl;
    @Value("${model.feedback.production.ali.querySspTaskInfo:''}")
    private String querySspTaskInfoForAliUrl;
    @Value("${model.feedback.production.fm.uploadFmParamMesFileInfoUrl:''}")
    private String uploadFmParamMesFileInfoUrl;
    @Value("${model.feedback.production.meituan.eMailer:,}")
    private String eMailer;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private EmailUtils emailUtils;
    private static final String STATION_TYPE_AOI = "AOI";

    private static String getAoiTestDataUrl = "";
    private static String getRepairInfoUrl = "";
    private static String querySspTaskInfoUrl = "";
    private static String querySspMainBoardInfoUrl = "";


    @RecordLogAnnotation("获取中试料单写片信息接口")
    @OpenApi(name = "获取中试料单写片信息接口", provider = {"MDS"})
    public List<PreManuMDSProgrammingDTO> getPreManuMDSProgrammingList(PreManuMDSProgrammingDTO preManuMDSProgrammingDTO) throws Exception {
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(LOOK_UP_VALUE_652306001);
        if(sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_652306001});
        }
        String url = sysLookupValues.getLookupMeaning();
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(Constant.Z_ACCESS_TOKEN, this.getAccessToken());
        List<PreManuMDSProgrammingDTO> reList = new ArrayList<>();
        String msg = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(preManuMDSProgrammingDTO),header,url, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(msg);
        if (null == json.get(BO) || null == json.get(BO).get(LIST_STRING)) {
            return reList;
        }
        String bo = json.get(BO).get(LIST_STRING).toString();
        if (StringUtils.isEmpty(bo)) {
            return reList;
        }
        reList = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<PreManuMDSProgrammingDTO>>(){});
        return reList;
    }

    public MdsSspSnMainBoardDTO getSspMainBoardBySn(String sn) {
        if (StringUtils.isBlank(querySspMainBoardInfoUrl)) {
            SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014);
            if (sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_2020014});
            }
            querySspMainBoardInfoUrl = sysLookupValues.getLookupMeaning();
        }
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(Constant.Z_ACCESS_TOKEN, this.getAccessToken());
        Map<String, Object> params = Maps.newHashMap();
        params.put("snList", Lists.newArrayList(sn));
        String msg = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(params),header, querySspMainBoardInfoUrl, MicroServiceNameEum.SENDTYPEPOST);
        log.info("调用中试接口查询整机SN主板信息数据:" + JSONObject.toJSONString(params) + "###" + msg);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        List<MdsSspSnMainBoardDTO> mdsSspSnMainBoards = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<MdsSspSnMainBoardDTO>>() {
        });
        return CollectionUtils.isEmpty(mdsSspSnMainBoards) ? null : mdsSspSnMainBoards.get(0);
    }

    /**
     * 根据条码查询中试整机信息
     *
     * @param sn sn
     * @return 整机信息
     */
    public MdsSspTaskInfoDTO querySspTaskInfoBySn(String sn) {
        if (StringUtils.isBlank(querySspTaskInfoUrl)) {
            SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020013);
            if(sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_2020013});
            }
            querySspTaskInfoUrl = sysLookupValues.getLookupMeaning();
        }
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(Constant.Z_ACCESS_TOKEN, this.getAccessToken());
        Map<String, String> params = new HashMap<>();
        params.put("sn", sn);
        String msg = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(params),header, querySspTaskInfoUrl, MicroServiceNameEum.SENDTYPEPOST);
        log.info("调用中试接口查询SN整机信息数据:" + JSONObject.toJSONString(params) + "###" + msg);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToBean(bo, MdsSspTaskInfoDTO.class);
    }

    /**
     * 根据条码查询中试AOI测试数据
     * @param sn
     * @return
     */
    public List<MdsAoiTestDataDTO> getAoiTestData(String sn) {
        if (StringUtils.isBlank(sn)) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(getAoiTestDataUrl)) {
            SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020011);
            if(sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_2020011});
            }
            getAoiTestDataUrl = sysLookupValues.getLookupMeaning();
        }
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(Constant.Z_ACCESS_TOKEN, this.getAccessToken());
        Map<String, String> params = new HashMap<>();
        params.put("board_sn", sn);
        params.put("station_type", STATION_TYPE_AOI);
        String msg = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(params),header,getAoiTestDataUrl, MicroServiceNameEum.SENDTYPEPOST);
        log.info("调用中试接口查询AOI测试数据:" + JSONObject.toJSONString(params) + "###" + msg);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<MdsAoiTestDataDTO>>() {
        });
    }

    /**
     * 根据条码查询中试SMT在线维修数据
     * @param sn
     * @return
     */
    public List<MdsRepairInfoResponseDTO> getRepairInfoBySn(String sn) {
        if (StringUtils.isBlank(sn)) {
            return new ArrayList<>();
        }

        if (StringUtils.isBlank(getRepairInfoUrl)) {
            SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020012);
            if(sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_2020012});
            }
            getRepairInfoUrl = sysLookupValues.getLookupMeaning();
        }

        Map<String, String> params = new HashMap<>();
        params.put("board_sn", sn);
        params.put("station_type", STATION_TYPE_AOI);
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        header.put(Constant.Z_ACCESS_TOKEN, this.getAccessToken());

        String msg = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(params),header,getRepairInfoUrl, MicroServiceNameEum.SENDTYPEPOST);
        log.info("调用中试接口查询AOI测试数据:" + JSONObject.toJSONString(params) + "###" + msg);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<MdsRepairInfoResponseDTO>>() {
        });
    }

    /**
     * 中试获取accessToken接口
     *
     * @throws MesBusinessException 业务异常报错
     */
    @RecordLogAnnotation("中试获取accessToken接口")
    @OpenApi(name = "中试获取accessToken接口", provider = {"MDS"})
    public String getAccessToken() {
        String accessToken = (String)redisTemplate.opsForValue().get(Constant.MDS_OBTAINS_TOKEN_KEY);
        if(StringUtils.isNotEmpty(accessToken)){
            return accessToken;
        }
        List<SysLookupValues> sysList = sysLookupValuesService.selectValuesByType(Integer.valueOf(Constant.LOOKUP_TYPE_6732));
        if(CollectionUtils.isEmpty(sysList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.LOOKUP_TYPE_6732});
        }
        //账号密码 url
        SysLookupValues accountSysLookupTypesDTO = sysList.stream().filter(e->new BigDecimal(Constant.LOOKUP_TYPE_6732003).compareTo(e.getLookupCode()) == NumConstant.NUM_ZERO).collect(Collectors.toList()).stream().findFirst().orElse(null);
        if(accountSysLookupTypesDTO == null || StringUtils.isEmpty(accountSysLookupTypesDTO.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.LOOKUP_TYPE_6732003});
        }
        SysLookupValues pwdSysLookupTypesDTO = sysList.stream().filter(e->new BigDecimal(Constant.LOOKUP_TYPE_6732004).compareTo(e.getLookupCode()) == NumConstant.NUM_ZERO).collect(Collectors.toList()).stream().findFirst().orElse(null);
        if(pwdSysLookupTypesDTO == null || StringUtils.isEmpty(pwdSysLookupTypesDTO.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.LOOKUP_TYPE_6732004});
        }
        SysLookupValues urlSysLookupTypesDTO = sysList.stream().filter(e->new BigDecimal(Constant.LOOKUP_TYPE_6732002).compareTo(e.getLookupCode()) == NumConstant.NUM_ZERO).collect(Collectors.toList()).stream().findFirst().orElse(null);
        if(urlSysLookupTypesDTO == null || StringUtils.isEmpty(urlSysLookupTypesDTO.getLookupMeaning())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.LOOKUP_TYPE_6732002});
        }
        Map<String,Object> params = new HashMap<>();
        params.put(Constant.USER_NAME,accountSysLookupTypesDTO.getLookupMeaning());
        params.put(Constant.PASS_WORD,pwdSysLookupTypesDTO.getLookupMeaning());
        params.put(Constant.GRANT_TYPE,Constant.PASS_WORD);
        Map<String, String>  header = MESHttpHelper.getHttpRequestHeader();
        String bo = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(params),header,urlSysLookupTypesDTO.getLookupMeaning(), MicroServiceNameEum.SENDTYPEPOST);

        if (StringUtils.isEmpty(bo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DQAS_ERROR, new Object[]{bo});
        }
        MdsAccessTokenDTO mdsAccessTokenDTO = JacksonJsonConverUtil.jsonToBean(bo, MdsAccessTokenDTO.class);
        if (Objects.isNull(mdsAccessTokenDTO) || StringUtils.isEmpty(mdsAccessTokenDTO.getAssessToken())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DQAS_ERROR, new Object[]{bo});
        }
        // 冗余配置的时长，计算传输该值的延迟防止临界时刻出现缓存未失效，实际中试token已失效导致的偶发错误
        long tokenTimeOut = getTokenTimeOut(mdsAccessTokenDTO.getExpiresIn()) - tokenCacheRedundance;
        redisTemplate.opsForValue().set(Constant.MDS_OBTAINS_TOKEN_KEY,mdsAccessTokenDTO.getAssessToken(), tokenTimeOut, TimeUnit.SECONDS);
        return mdsAccessTokenDTO.getAssessToken();
    }

    public long getTokenTimeOut(long expiresIn) {
        return expiresIn > NumConstant.NUM_5400 ? NumConstant.NUM_5400 : expiresIn;
    }

    @RecordLogAnnotation("中试美团接口-获取维修信息")
    @OpenApi(name = "中试美团接口-获取维修信息", provider = {"MDS"})
    public List<MdsRepairInformationDTO> getRepairInformation(List<String> barCodeList) throws MesBusinessException {
        // 早期返回，避免不必要的对象创建
        if (CollectionUtils.isEmpty(barCodeList)) {
            return Collections.emptyList();
        }
        List<MdsRepairInformationDTO> list = new ArrayList<>(barCodeList.size());
        for (List<String> tempSnList : Lists.partition(barCodeList, NumConstant.NUM_TEN)) {
            list.addAll(this.getRepairInfo(tempSnList));
        }
        return list;
    }

    private List<MdsRepairInformationDTO> getRepairInfo(List<String> barCodeList) {
        // 获取请求头并添加访问令牌
        Map<String, String> headers = MESHttpHelper.getHttpRequestHeader();
        headers.put(Constant.Z_ACCESS_TOKEN, getAccessToken());

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PARTCODELIST, barCodeList);

        // 发送远程请求
        String response = HttpRemoteUtil.remoteExeFoExternal(
                JSON.toJSONString(params),
                headers,
                queryRepairInfoUrl,
                MicroServiceNameEum.SENDTYPEPOST
        );
        // 检查响应并处理异常
        String body = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isEmpty(body)) {
            throw new MesBusinessException(
                    RetCode.BUSINESSERROR_CODE,
                    MessageId.FAILED_TO_CALL_MDS_INTERFACE,
                    new Object[]{response}
            );
        }

        // 解析响应体为DTO列表
        List<MdsRepairInformationDTO> result = JacksonJsonConverUtil.jsonToListBean(
                body,
                new TypeReference<List<MdsRepairInformationDTO>>(){}
        );

        // 返回结果列表
        return !CollectionUtils.isEmpty(result) ? result : Collections.emptyList();
    }

    @RecordLogAnnotation("整机条码节点信息")
    @OpenApi(name = "整机条码节点信息 ", provider = {"MDS"})
    public List<SspTaskInfoDTO> querySspTaskInfoForAli(List<String> barCodeList) throws MesBusinessException {
        // 早期返回，避免不必要的对象创建
        if (CollectionUtils.isEmpty(barCodeList)) {
            return Collections.emptyList();
        }
        List<SspTaskInfoDTO> sspTaskInfoDTOList = new ArrayList<>();
        for (String sn : barCodeList) {
            sspTaskInfoDTOList.addAll(this.querySspTaskInfo(sn));
        }
        return sspTaskInfoDTOList;
    }

    private List<SspTaskInfoDTO> querySspTaskInfo(String sn) {
        // 获取请求头并添加访问令牌
        Map<String, String> headers = MESHttpHelper.getHttpRequestHeader();
        headers.put(Constant.Z_ACCESS_TOKEN, getAccessToken());

        Map<String, Object> params = new HashMap<>();
        params.put(Constant.SN, sn);

        // 发送远程请求
        String response = HttpRemoteUtil.remoteExeFoExternal(
                JSON.toJSONString(params),
                headers,
                querySspTaskInfoForAliUrl,
                MicroServiceNameEum.SENDTYPEPOST
        );

        // 检查响应并处理异常
        String body = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isEmpty(body)) {
            throw new MesBusinessException(
                    RetCode.BUSINESSERROR_CODE,
                    MessageId.FAILED_TO_CALL_MDS_INTERFACE,
                    new Object[]{response}
            );
        }


        // 解析响应体为DTO列表
        ZsSspTaskInfoDTO result = JacksonJsonConverUtil.jsonToBean(
                body,
                ZsSspTaskInfoDTO.class
        );

        // 返回结果列表
        return result != null && !CollectionUtils.isEmpty(result.getSspList()) ? result.getSspList() : Collections.emptyList();
    }

    @RecordLogAnnotation("整机信息_生产站测试信息")
    @OpenApi(name = "整机信息_生产站测试信息 ", provider = {"MDS"})
    public List<MdsFeedbackProductionStationTestingInfoDTO> feedbackOfProductionStationTestingInformation(List<String> barCodeList) throws MesBusinessException {
        // 早期返回，避免不必要的对象创建
        if (CollectionUtils.isEmpty(barCodeList)) {
            return Collections.emptyList();
        }
        List<MdsFeedbackProductionStationTestingInfoDTO> returnList = new ArrayList<>(barCodeList.size());
        for (List<String> tempSnList : Lists.partition(barCodeList, NumConstant.NUM_TEN)) {
            returnList.addAll(this.getMdsFeedbackProductionStationTestingInfoDTOS(tempSnList));
        }
        return returnList;
    }

    private List<MdsFeedbackProductionStationTestingInfoDTO> getMdsFeedbackProductionStationTestingInfoDTOS(List<String> barCodeList) {
        // 获取请求头并添加访问令牌
        Map<String, String> headers = MESHttpHelper.getHttpRequestHeader();
        headers.put(Constant.Z_ACCESS_TOKEN, getAccessToken());

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.PARTCODELIST, barCodeList);

        // 发送远程请求
        String response = HttpRemoteUtil.remoteExeFoExternal(
                JSON.toJSONString(params),
                headers,
                feedbackOfProductionStationTestUrl,
                MicroServiceNameEum.SENDTYPEPOST
        );

        // 检查响应并处理异常
        String body = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
        if (StringUtils.isEmpty(body)) {
            throw new MesBusinessException(
                    RetCode.BUSINESSERROR_CODE,
                    MessageId.FAILED_TO_CALL_MDS_INTERFACE,
                    new Object[]{response}
            );
        }

        // 解析响应体为DTO列表
        List<MdsFeedbackProductionStationTestingInfoDTO> result = JacksonJsonConverUtil.jsonToListBean(
                body,
                new TypeReference<List<MdsFeedbackProductionStationTestingInfoDTO>>(){}
        );

        // 返回结果列表
        return !CollectionUtils.isEmpty(result) ? result : Collections.emptyList();
    }

    @RecordLogAnnotation("整机测试文件信息生成")
    @OpenApi(name = "整机测试文件信息生成 ", provider = {"MDS"})
    public List<MdsFeedbackProductionStationFileDTO> feedbackCompleteMachineTestingGenerateFile(String sn, List<String> stationIdList, boolean fileCloudLinkSwitch) throws MesBusinessException {
        // 早期返回，避免不必要的对象创建
        if (CollectionUtils.isEmpty(stationIdList) || StringUtils.isEmpty(sn)) {
            return Collections.emptyList();
        }
        List<MdsFeedbackProductionStationFileDTO> returnList = new ArrayList<>(stationIdList.size());
        for (List<String> tempStationIdList : Lists.partition(stationIdList, NumConstant.NUM_TEN)) {
            returnList.addAll(this.getMdsFeedbackProductionStationFileDTOList(sn,tempStationIdList,fileCloudLinkSwitch));
        }
        return returnList;
    }

    private List<MdsFeedbackProductionStationFileDTO> getMdsFeedbackProductionStationFileDTOList(String sn,List<String> stationIdList,boolean fileCloudLinkSwitch) {
        // 获取请求头并添加访问令牌
        Map<String, String> headers = MESHttpHelper.getHttpRequestHeader();
        headers.put(Constant.Z_ACCESS_TOKEN, getAccessToken());

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put(Constant.STATIONIDLIST, stationIdList);
        params.put(Constant.PARTCODE, sn);
        params.put(Constant.FILE_CLOUD_LINK_SWITCH, fileCloudLinkSwitch);
        try {
            // 发送远程请求
            String response = HttpRemoteUtil.remoteExeFoExternal(
                    JSON.toJSONString(params),
                    headers,
                    feedbackOfProductionStationTestingGenerateFileUrl,
                    MicroServiceNameEum.SENDTYPEPOST
            );

            // 检查响应并处理异常
            String body = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response);
            if (StringUtils.isEmpty(body)) {
                throw new MesBusinessException(
                        RetCode.BUSINESSERROR_CODE,
                        MessageId.FAILED_TO_CALL_MDS_INTERFACE,
                        new Object[]{response}
                );
            }

            // 解析响应体为DTO列表
            List<MdsFeedbackProductionStationFileDTO> result = JacksonJsonConverUtil.jsonToListBean(
                    body,
                    new TypeReference<List<MdsFeedbackProductionStationFileDTO>>() {
                    }
            );

            // 返回结果列表
            return !CollectionUtils.isEmpty(result) ? result : Collections.emptyList();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @RecordLogAnnotation("个参文件同步mds")
    @OpenApi(name = "个参文件同步mds ", provider = {"MDS"})
    public String uploadFmParamMesFileInfo(MultipartFile multipartFile, String fmType, String task) throws Exception {
        log.info("个参文件同步mds | 文件: [名称={}, 大小={}字节], 类型: {}, 任务号: {}",
                multipartFile.getOriginalFilename(),
                multipartFile.getSize(), fmType, task);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("fmType", fmType);
        paramMap.put("taskNo", task);
        try {
            String result = UploadFileUtils.uploadFile(uploadFmParamMesFileInfoUrl, "paramFile", multipartFile, paramMap);
            if (result == null) {
                return result;
            }
            String logResult = result.length() > 100 ? result.substring(0, 100) + "..." : result;
            log.info("文件上传成功 | 任务号: {}, 返回结果: {}", task, logResult);
            return result;
        } catch (Exception e) {
            log.error("文件上传异常 | 文件名: {}, 任务号: {}, 错误信息: {}",
                    multipartFile.getOriginalFilename(), task, e.getMessage(), e);
            throw e;
        }
    }

}
