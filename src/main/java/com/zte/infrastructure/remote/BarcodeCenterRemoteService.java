package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.BarcodeExpandResponse;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.BarcodeUpdateDTO;
import com.zte.interfaces.dto.GenBarcodeParamDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.BARCODE_EXPAND_QUERY_URI;
import static com.zte.common.utils.Constant.BARCODE_QUERY_URI;
import static com.zte.common.utils.Constant.BARCODE_UPDATE_URI;

/**
 * 点对点调用条码中心服务接口类
 * <AUTHOR>
 */
@Service
public class BarcodeCenterRemoteService {

    private static final Logger logger = LoggerFactory.getLogger(BarcodeCenterRemoteService.class);

    // 条码小类CODE 空条码生成
    private static final String BLANK_GENERATE = "ZTE00_REELID";

    // 箱码生成
    public static final String PKG_GENERATE = "ZTEPKG_PKGID";

    // 请求参数键名常量
    private static final String PARAMS_KEY = "params";
    private static final String BARCODE_KEY = "barcode";
    private static final String SOURCE_NO_KEY = "sourceNo";
    private static final String PAGE_NO_KEY = "pageNo";
    private static final String PAGE_SIZE_KEY = "pageSize";

    @Autowired
    private SysLookupValuesService sysLookupValuesService;

    /**
     * 空条码生成
     * @param count		数量
     * @param token		token
     * @param ruleCode	规则编码（对应条码中心条码生成规则）
     * @return
     * @throws Exception
     */
    @RecordLogAnnotation("条码中心-空条码生成")
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<String> blankGenerate(int count,String token,String ruleCode) throws Exception{
        GenBarcodeParamDTO genBarcodeParamDTO = new GenBarcodeParamDTO();
        genBarcodeParamDTO.setCount(count);
        genBarcodeParamDTO.setCategoryCode(StringUtils.isBlank(ruleCode) ? BLANK_GENERATE : ruleCode);

        String getresult = callBarcodeCenter(Constant.BARCODE_BLANK_GENERATE_URI, JacksonJsonConverUtil.beanToJson(genBarcodeParamDTO));
        if (StringUtils.isBlank(getresult)){
            throw new Exception(Constant.CALL_BARCODE_FAILURE);
        }

        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        if (!RetCode.SUCCESS_CODE.equals(json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText())) {
            String msg = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_MSG).asText();
            String[] param = { msg };
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BLANK_GENERATE_ERROR,param);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<String> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<String>>() {
        });
        return list;
    }

    /**
     * 获取条码中心条码
     * @param barcode
     * @return
     * @throws Exception
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> getBarcodeFromBarcodeCenter(String barcode) throws Exception {
        Map<String, Object> bodyParams = getBodyParams(barcode);
        return getBarcodeExpandVOS(bodyParams);
    }

    /**
     * 获取条码中心条码 重载方法
     * @param barcodeList
     * @return
     * @throws Exception
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> getBarcodeFromBarcodeCenterList(List<String> barcodeList) throws Exception {
        Map<String, Object> bodyParams = getBodyParams(barcodeList.toArray(new String[0]));
        return getBarcodeExpandVOS(bodyParams);
    }

    private List<BarcodeExpandVO> getBarcodeExpandVOS(Map<String, Object> bodyParams) throws Exception {
        List<BarcodeExpandVO> barcodeExpandVOS = new ArrayList<BarcodeExpandVO>();
        String msg = callBarcodeCenter(BARCODE_EXPAND_QUERY_URI, JSONObject.toJSONString(bodyParams));
        if(StringUtils.isBlank(msg)){
            throw new Exception(Constant.CALL_BARCODE_FAILURE);
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(Normalizer.normalize(msg, Normalizer.Form.NFKC));
        if (json == null) {
            throw new Exception(Constant.CALL_BARCODE_JSON_FAILURE);
        }
        JsonNode code = json.get(Constant.STR_CODE);
        if(code == null) {
            throw new Exception(Constant.CALL_BARCODE_CODE_FAILURE);
        }
        String codeStr = code.get(Constant.STR_CODE).asText();
        // 返回错误，记录失败日志
        if(!com.zte.springbootframe.common.model.RetCode.SUCCESS_CODE.equalsIgnoreCase(codeStr)){
            throw new Exception(code.get(Constant.STR_MSG) == null ? "" : code.get(Constant.STR_MSG).asText());
        }else{
            if (null != json) {
                JsonNode boNode = json.get("bo");
                if (null != boNode) {
                    String bo = boNode.toString();
                    barcodeExpandVOS = JacksonJsonConverUtil.jsonToListBean(bo,
                            new TypeReference<ArrayList<BarcodeExpandVO>>() {

                            });
                }
            }
            return barcodeExpandVOS;
        }
    }

    /**
     * 条码生成
     * @param genBarcodeParamDTO	条码生成参数
     * @return
     * @throws Exception
     */
    @RecordLogAnnotation("条码中心-条码生成")
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<String> barcodeGenerate(GenBarcodeParamDTO genBarcodeParamDTO) throws Exception{
        String getresult = callBarcodeCenter(Constant.BARCODE_GENERATE_UIR, JacksonJsonConverUtil.beanToJson(genBarcodeParamDTO));
        if (StringUtils.isBlank(getresult)){
            throw new Exception(Constant.CALL_BARCODE_FAILURE);
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getresult);
        if (!RetCode.SUCCESS_CODE.equals(json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText())) {
            String msg = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_MSG).asText();
            String[] param = { msg };
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_GENERATE_ERROR,param);
        }
        String bo = json.get(MpConstant.JSON_BO).toString();
        List<String> list = JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<String>>() {
        });
        return list;
    }

    /**
     * 条码中心更新接口
     *
     * @param pkCodeInfos
     */
    @RecordLogAnnotation("条码更新接口")
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public String barcodeUpdate(List<PkCodeInfo> pkCodeInfos) throws Exception {
        String params = JacksonJsonConverUtil.beanToJson(getBarcodeUpdateDTO(pkCodeInfos));
        String result = callBarcodeCenter(BARCODE_UPDATE_URI, params);
        if (StringUtils.isBlank(result)){
            throw new Exception(Constant.CALL_BARCODE_FAILURE);
        }

        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        if (!RetCode.SUCCESS_CODE.equals(json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_CODE).asText())) {
            String msg = json.get(MpConstant.JSON_CODE).get(MpConstant.JSON_MSG).asText();
            String[] param = { msg };
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UPDATE_BARCODE_FAIL, param);
        }
        return result;
    }

    private List<BarcodeUpdateDTO> getBarcodeUpdateDTO(List<PkCodeInfo> pkCodeInfos) {
        return pkCodeInfos.stream().map(pkCodeInfo -> {
            BarcodeUpdateDTO dto = new BarcodeUpdateDTO();
            dto.setBarcode(pkCodeInfo.getPkCode());
            dto.setItemCode(pkCodeInfo.getItemCode());
            dto.setItemName(pkCodeInfo.getItemName());
            dto.setItemVersion(pkCodeInfo.getItemVersion());
            dto.setQuantity(pkCodeInfo.getItemQty());
            dto.setSourceBatchNo(pkCodeInfo.getSourceBatchCode());
            dto.setLfid(pkCodeInfo.getLfid());
            dto.setSysLotCode(pkCodeInfo.getSysLotCode());
            dto.setSupplierCode(pkCodeInfo.getSupplerCode());
            return dto;
        }).collect(Collectors.toList());
    }

    //获取头信息
    public static Map<String, Object> getBodyParams(String... barcodes){
        Map<String, Object> header = Maps.newHashMap();
        header.put("barcodeList",barcodes);
        return header;
    }

    /**
     * 调用条码中心公共方法
     * @return
     */
    public String callBarcodeCenter(int uriCode, String params) throws MesBusinessException {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        List<SysLookupValues> lookupValues = sysLookupValuesService.selectValuesByType(Constant.LOOKUP_TYPE_1004052);
        if (CollectionUtils.isEmpty(lookupValues)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Integer [] {Constant.LOOKUP_TYPE_1004052});
        }
        String url = getUrlAndSetHead(uriCode, lookupValues, headerParamsMap);
        return HttpRemoteUtil.remoteExeFoExternal(params,headerParamsMap,url,MicroServiceNameEum.SENDTYPEPOST);
    }

    private String getUrlAndSetHead(int uriCode, List<SysLookupValues> lookupValues, Map<String, String> headerParamsMap) throws MesBusinessException {
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, headerParamsMap.get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        String baseUrl = null;
        String uri = null;
        for (SysLookupValues value : lookupValues) {
            if (value == null || StringUtils.isBlank(value.getLookupMeaning()) || value.getLookupCode() == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                        new Integer [] {Constant.LOOKUP_TYPE_1004052});
            }
            String lookupMeaning = value.getLookupMeaning();
            int lookupCode = value.getLookupCode().intValue();
            setHeader(lookupCode, lookupMeaning, headerParamsMap);
            if (Constant.BARCODE_BASIC_URL == lookupCode) {
                baseUrl = lookupMeaning;
            }
            if (uriCode == lookupCode) {
                uri = lookupMeaning;
            }
        }
        return baseUrl + uri;
    }

    private void setHeader(int lookupCode, String lookupMeaning, Map<String, String> header) {
        switch (lookupCode) {
            case Constant.BARCODE_TENANT_ID:
                header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, lookupMeaning);
                header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID.toLowerCase(), lookupMeaning);
                break;
            case Constant.BARCODE_LOOKUPCODE:
                header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE.toLowerCase(), lookupMeaning);
                header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, lookupMeaning);
                break;
            case Constant.BARCODE_APPID:
                header.put(Constant.X_AUTH_ACCESSKEY.toLowerCase(), lookupMeaning);
                header.put(Constant.X_AUTH_ACCESSKEY, lookupMeaning);
                break;
            default:
                break;
        }
    }

    /**
     * 获取适用于条码中心头信息
     * @return
     */
    public Map<String, String> getHeaderForBarcodeCenter() throws Exception {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, headerParamsMap.get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        List<SysLookupValues> lookupValues = sysLookupValuesService.selectValuesByType(Constant.LOOKUP_TYPE_1004052);
        if (CollectionUtils.isEmpty(lookupValues)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object [] {Constant.LOOKUP_TYPE_1004052});
        }
        for (SysLookupValues value : lookupValues) {
            if (value == null || StringUtils.isBlank(value.getLookupMeaning()) || value.getLookupCode() == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                        new Object [] {Constant.LOOKUP_TYPE_1004052});
            }
            int lookupCode = value.getLookupCode().intValue();
            String lookupMeaning = value.getLookupMeaning();
            setHeader(lookupCode, lookupMeaning, headerParamsMap);
        }
        return headerParamsMap;
    }


    /**
     * 根据目录名称获取模板列表
     */
    @RecordLogAnnotation("根据目录名称获取模板列表")
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<String> getTemplateNameList(String templateContnet) throws Exception {
        // 获取url
        SysLookupValues where = new SysLookupValues();
        where.setLookupCode(new BigDecimal(MpConstant.LOOKUP_6679001));
        SysLookupValues lookupValue = sysLookupValuesService.selectSysLookupValuesById(where);
        //若没有配置数据字典则报错
        if (lookupValue == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{MpConstant.LOOKUP_6679001});
        }
        String url=lookupValue.getLookupMeaning();
        Map<String, String> headerParamsMap = getHeaderForBarcodeCenter();

        String getResult = HttpRemoteUtil.remoteExeFoExternal(new HashMap<>(),headerParamsMap,url+"?directoryName="+templateContnet,MicroServiceNameEum.SENDTYPEPOST);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_GET_TEMPLATE_ERROR);
        }
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_GET_TEMPLATE_ERROR_MSG,new Object[]{json.get(Constant.CODE).get(Constant.MSG).asText()});
        }
        String bo = json.get(Constant.BO).toString();
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<ArrayList<String>>() {});
    }


    //获取配置url
    public String getUrl(String lookUpValue) throws Exception {
        // 获取url
        SysLookupValues where = new SysLookupValues();
        where.setLookupCode(new BigDecimal(lookUpValue));
        SysLookupValues lookupValue = sysLookupValuesService.selectSysLookupValuesById(where);
        //若没有配置数据字典则启用条码中心
        if (lookupValue == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        String url = lookupValue.getLookupMeaning();
        if (StringUtils.isEmpty(url)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_CENTER_URL);
        }
        return url;
    }



    /**
     * 条码中心标签打印接口
     *
     * @return
     * @throws Exception
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public void serverTemplatePrint(BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO) throws Exception {
        String url = getUrl(MpConstant.LOOKUP_6679003);
        Map<String, String> headerParamsMap = getHeaderForBarcodeCenter();
        setXEmpNo(barcodeCenterTemplatePrintDTO, headerParamsMap);
        String getResult = HttpRemoteUtil.remoteExeFoExternal(JacksonJsonConverUtil.beanToJson(barcodeCenterTemplatePrintDTO), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_BARCODE_CENTER_TO_PRINT_FALIED);
        }
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_BARCODE_CENTER_TO_PRINT_FALIED,new Object[]{json.get(Constant.CODE).get(Constant.MSG).asText()});
        }
    }
    /**
     * 设置x-emp_no
     * @param barcodeCenterTemplatePrintDTO
     * @param headerParamsMap
     */
    public void setXEmpNo(BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO, Map<String, String> headerParamsMap) {
        if(headerParamsMap == null){
            return;
        }
        if(barcodeCenterTemplatePrintDTO == null){
            return;
        }
        String empNo = headerParamsMap.get(Constant.X_EMP_NO);
        if(StringUtils.isEmpty(empNo)){
            empNo = barcodeCenterTemplatePrintDTO.getCreateBy();
            headerParamsMap.put(Constant.X_EMP_NO,empNo);
        }
        if(StringUtils.isEmpty(empNo)){
            headerParamsMap.put(Constant.X_EMP_NO,Constant.SYSTEM);
        }
        String uppercaseEmpNo = headerParamsMap.get(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if(StringUtils.isEmpty(uppercaseEmpNo)){
            uppercaseEmpNo = barcodeCenterTemplatePrintDTO.getCreateBy();
            headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO,uppercaseEmpNo);
        }
        if(StringUtils.isEmpty(uppercaseEmpNo)){
            headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO,Constant.SYSTEM);
        }
    }

    /**
     * 条码中心查询
     *
     * @return
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> barcodeQuery(String parentCategoryCode, List<String> barcodes) {
        String msg = callBarcodeCenter(BARCODE_EXPAND_QUERY_URI, JacksonJsonConverUtil.beanToJson(new HashMap() {{
            put("parentCategoryCode", parentCategoryCode);
            put("barcodeList", barcodes);
        }}));
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        List<BarcodeExpandVO> listVO = JacksonJsonConverUtil.jsonToListBean(bo
                , new TypeReference<List<BarcodeExpandVO>>() {
                });
        return listVO;
    }

    /**
     * 根据箱号调用条码中心接口获取SN
     *
     * @return
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> getSnByBarCode(String cartonNo, String orderNo) {
        Map<String, Map<String,Object>> map = new HashMap<>(3);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("barcode", cartonNo);
        hashMap.put("sourceNo", orderNo);
        hashMap.put("pageNo", 1);
        hashMap.put("pageSize", 2000);
        map.put("params",hashMap);
        String msg = callBarcodeCenter(BARCODE_QUERY_URI, JacksonJsonConverUtil.beanToJson(map));
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return Collections.emptyList();
        }
        BarcodeExpandResponse response = JacksonJsonConverUtil.jsonToBean(bo, BarcodeExpandResponse.class);
        return response.getRows();
    }

    /**
     * 根据箱号和领料单号调用条码中心接口获取SN
     *
     * @return
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> getSnByBarCodeAndRcvNo(String cartonNo, String rcvNo) {
        Map<String, Map<String,Object>> map = new HashMap<>(3);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put(BARCODE_KEY, cartonNo);
        hashMap.put(SOURCE_NO_KEY, rcvNo);
        hashMap.put(PAGE_NO_KEY, Constant.INT_1);
        hashMap.put(PAGE_SIZE_KEY, Constant.INT_2000);
        map.put(PARAMS_KEY, hashMap);
        String msg = callBarcodeCenter(BARCODE_QUERY_URI, JacksonJsonConverUtil.beanToJson(map));
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return Collections.emptyList();
        }
        BarcodeExpandResponse response = JacksonJsonConverUtil.jsonToBean(bo, BarcodeExpandResponse.class);
        return Optional.ofNullable(response.getRows()).orElse(Collections.emptyList());
    }

    /**
     * 条码中心已绑定数量
     * @param cartonNo cartonNo
     * @param rcvNo rcvNo
     * @return Long
     */
    public BarcodeExpandResponse queryRegisterNumber(String cartonNo, String rcvNo){
        Map<String, Map<String,Object>> map = new HashMap<>(4);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put(BARCODE_KEY, cartonNo);
        hashMap.put(SOURCE_NO_KEY, rcvNo);
        hashMap.put(PAGE_NO_KEY, Constant.INT_1);
        hashMap.put(PAGE_SIZE_KEY, Constant.INT_1);
        map.put(PARAMS_KEY, hashMap);
        String msg = callBarcodeCenter(BARCODE_QUERY_URI, JacksonJsonConverUtil.beanToJson(map));
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new BarcodeExpandResponse();
        }
        return JacksonJsonConverUtil.jsonToBean(bo, new TypeReference<BarcodeExpandResponse>() {
        });
    }

    /**
     * 根据条码批量查询
     * @param barcodes
     * @return
     */
    @AlarmAnnotation(alarmName = "call_barcode_center_error", alarmKey = "9908", alarmTitle = "调用条码中心接口异常或超时", alarmTime = 30000)
    public List<BarcodeExpandVO> barcodeQueryBatch(List<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return new ArrayList<>();
        }
        String msg = callBarcodeCenter(Constant.BARCODE_BATCH_QUERY_URI, JacksonJsonConverUtil.beanToJson(new HashMap() {{
            put("barcodeList", barcodes);
        }}));
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return new ArrayList<>();
        }
        return JacksonJsonConverUtil.jsonToListBean(bo, new TypeReference<List<BarcodeExpandVO>>() {
        });
    }

}
