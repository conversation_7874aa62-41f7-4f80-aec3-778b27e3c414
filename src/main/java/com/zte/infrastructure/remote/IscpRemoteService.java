package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.MpConstant.DEFAULT_EXPORT_CONTROL_METHOD;

/**
 * <AUTHOR>
 * @date 2023-10-11 15:42
 */
@Component
public class IscpRemoteService {

    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    //采购CIV管控接口地址对应的数据字典
    private static final Integer LOOK_UP_CODE_CIV_CONTROL = 1242001;

    private static final Integer LOOK_UP_CODE_ISCP_ITEM_SPLIT = 1242002;

    private static final Integer LOOK_UP_EXTERNAL_ITEM_URI = 1242003;

    private static final String ISCP_ITEM_SPLIT_URI = "/external/item/split";
    private static final String EXTERNAL_ITEM_URI = "/external/item";

    private static final String ECCN = "ECCN";
    private static final String PURCHASE = "PURCHASE";
    private static final String CONDITIONS = "conditions";
    private static final List<String> EXTRAFIELDS = Arrays.asList(
            "customerSupplyType",
            "customerCode",
            "customerName",
            "customerItemNo",
            "customerItemName",
            "customerItemStyle",
            "typeName",
            "customerComponentType",
            "isControl",
            "uniqueFlag"
    );

    /**
     * 调用采购CIV管控接口
     * 获取受CIV管控物料信息
     */

    @AlarmAnnotation(alarmName = "call_iscp_error", alarmKey = "9903", alarmTitle = "调用iscp接口异常或超时", alarmTime = 30000)
    @OpenApi(name = "getCivControlInfo", describe = "调用采购CIV管控接口", provider = {"ISCP"})
    public List<CivControlInfoDTO> getCivControlInfo(List<String> itemNos) throws Exception {
        List<CivControlInfoDTO> infoDTOListT = new ArrayList();
        if (CollectionUtils.isEmpty(itemNos)) {
            return infoDTOListT;
        }
        String requestUrl = getRequestUrl(LOOK_UP_CODE_CIV_CONTROL, ISCP_ITEM_SPLIT_URI);

        List<List<String>> items = CommonUtils.splitList(itemNos, NumConstant.NUM_200) ;

        // 分批查
        Map<String , Object> requestParam = new HashMap<>(3);
        requestParam.put("pageSize" , NumConstant.NUM_1000);
        requestParam.put("modules" , Arrays.asList(ECCN));
        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        for (List<String> itemp : items) {
            List<CivControlInfoDTO> controlInfoDTOS = this.getCivControlInfoDTOS(requestUrl, requestParam, itemp, httpRequestHeader);
            if(CollectionUtils.isEmpty(controlInfoDTOS)) {
                continue;
            }
            infoDTOListT.addAll(controlInfoDTOS);
        }
        return infoDTOListT;
    }

    private List<CivControlInfoDTO> getCivControlInfoDTOS(String requestUrl, Map<String, Object> requestParam,
                                                                List<String> itemp,
                                                                Map<String, String> httpRequestHeader)
            throws JsonProcessingException {
        requestParam.put("itemNo" , itemp.stream().collect(Collectors.joining(Constant.COMMA)));
        String result = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(requestParam),httpRequestHeader,requestUrl,MicroServiceNameEum.SENDTYPEPOST);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        JsonNode dataArray = jsonNode.get(Constant.JSON_BO).get(Constant.JSON_LIST);
        List<CivControlInfoDTO> infoDTOList = JacksonJsonConverUtil.jsonToListBean(dataArray.toString(),
                new TypeReference<ArrayList<CivControlInfoDTO>>(){});
        if (CollectionUtils.isEmpty(infoDTOList)) {
            return null;
        }
        List<CivControlInfoDTO> civItemList = infoDTOList.stream()
                .filter(entry -> this.checkCivItem(entry))
                .collect(Collectors.toList());
        return civItemList;
    }

    private boolean checkCivItem(CivControlInfoDTO entry) {
        return DEFAULT_EXPORT_CONTROL_METHOD.equals(entry.getExportcontrolmethod())
                || DEFAULT_EXPORT_CONTROL_METHOD.equals(entry.getExportcontrolmethodName());
    }


    /**
     * 调用采购CIV管控接口
     * 获取受CIV管控物料信息
     */
    @RecordLogAnnotation("根据物料代码调用采购接口查询")
    @AlarmAnnotation(alarmName = "call_iscp_error", alarmKey = "9903", alarmTitle = "调用iscp接口异常或超时", alarmTime = 30000)
    @OpenApi(name = "queryCivControlInfoList", describe = "根据物料代码调用采购接口查询", provider = {"ISCP"})
    public List<CivControlInfoDTO> queryCivControlInfoList(List<String> itemNos) throws Exception {
        List<CivControlInfoDTO> infoDTOListT = new ArrayList();
        if (CollectionUtils.isEmpty(itemNos)) {
            return infoDTOListT;
        }
        String requestUrl = getRequestUrl(LOOK_UP_EXTERNAL_ITEM_URI, EXTERNAL_ITEM_URI);

        List<List<String>> items = CommonUtils.splitList(itemNos, NumConstant.NUM_100) ;

        // 分批查
        Map<String , Object> requestParam = new HashMap<>(4);
        requestParam.put(Constant.PAGE_SIZE , NumConstant.NUM_1000);
        requestParam.put(Constant.MODULES , Arrays.asList(ECCN));

        Map<String , Object> conditionMap = new HashMap<>(4);
        conditionMap.put(Constant.FIELD_NAME,Constant.ITEMNO);
        conditionMap.put(Constant.FIELD_TYPE,Constant.STR_IN);


        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        for (List<String> itemp : items) {
            conditionMap.put(Constant.FIELD_VALUE , itemp.stream().collect(Collectors.joining(Constant.COMMA)));
            requestParam.put(CONDITIONS, Collections.singletonList(conditionMap));
            List<CivControlInfoDTO> controlInfoDTOS = this.getCivControlInfoDTOList(requestUrl, requestParam, httpRequestHeader);
            if(CollectionUtils.isEmpty(controlInfoDTOS)) {
                continue;
            }
            infoDTOListT.addAll(controlInfoDTOS);
        }
        return infoDTOListT;
    }

    @AlarmAnnotation(alarmName = "call_iscp_error", alarmKey = "9903", alarmTitle = "调用iscp接口异常或超时", alarmTime = 30000)
    @OpenApi(name = "getItemSplitInfoByUuid", describe = "获取采购物料拆分信息", provider = {"ISCP"})
    public List<ItemSplitInfoDTO> getItemSplitInfoByUuid(List<String> itemUuidList) throws JsonProcessingException {
        if (CollectionUtils.isEmpty(itemUuidList)) {
            return new ArrayList<>();
        }
        String requestUrl = getRequestUrl(LOOK_UP_CODE_ISCP_ITEM_SPLIT, ISCP_ITEM_SPLIT_URI);
        Map<String, Object> requestParam = new HashMap<>();
        Map<String, Object> subParam = new HashMap<>();
        subParam.put("fieldName", "itemUuid");
        subParam.put("fieldType", "IN");
        subParam.put("fieldValue", StringUtils.join(itemUuidList, Constant.CHAR_COMMA));
        requestParam.put("conditions", Arrays.asList(subParam));
        requestParam.put("modules", Arrays.asList("CUSTOMERITEM"));
        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        String result = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(requestParam), httpRequestHeader, requestUrl, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        JsonNode dataArray = jsonNode.get(Constant.JSON_BO).get(Constant.JSON_LIST);
        List<ItemSplitInfoDTO> infoDTOList = JacksonJsonConverUtil.jsonToListBean(dataArray.toString(),
                new TypeReference<ArrayList<ItemSplitInfoDTO>>() {
                });
        if (CollectionUtils.isEmpty(infoDTOList)) {
            return new ArrayList<>();
        }
        return infoDTOList;
    }

    /**
     * 调用采购CIV管控接口
     * 根据物料代码查询阿里物料
     */
    @RecordLogAnnotation("根据物料代码查询阿里物料")
    @AlarmAnnotation(alarmName = "call_iscp_error", alarmKey = "9903", alarmTitle = "调用iscp接口异常或超时", alarmTime = 30000)
    @OpenApi(name = "queryAliControlInfoList", describe = "根据物料代码查询阿里物料", provider = {"ISCP"})
    public List<CivControlInfoDTO> queryAliControlInfoList(List<String> itemNos) throws Exception {
        if (CollectionUtils.isEmpty(itemNos)) {
            return Collections.emptyList();
        }
        String requestUrl = getRequestUrl(LOOK_UP_EXTERNAL_ITEM_URI, EXTERNAL_ITEM_URI);
        // 分批查
        Map<String , Object> requestParam = new HashMap<>(8);
        requestParam.put(Constant.MODULES , Collections.singletonList(PURCHASE));
        requestParam.put(Constant.PAGE_NO , NumConstant.NUM_ONE);
        requestParam.put(Constant.PAGE_SIZE , NumConstant.NUM_1000);
        requestParam.put(Constant.EXTRAFIELDS , EXTRAFIELDS);

        Map<String , Object> conditionMap = new HashMap<>(4);
        conditionMap.put(Constant.FIELD_NAME,Constant.ITEMNO);
        conditionMap.put(Constant.FIELD_TYPE,Constant.STR_IN);

        List<CivControlInfoDTO> infoDTOListT = new ArrayList<>();
        Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
        for (List<String> tempList : CommonUtils.splitList(itemNos, NumConstant.NUM_100)) {
            conditionMap.put(Constant.FIELD_VALUE , String.join(Constant.COMMA, tempList));
            requestParam.put(CONDITIONS, Collections.singletonList(conditionMap));
            List<CivControlInfoDTO> controlInfoDTOS = this.getCivControlInfoDTOList(requestUrl, requestParam, httpRequestHeader);
            if(CollectionUtils.isEmpty(controlInfoDTOS)) {
                continue;
            }
            infoDTOListT.addAll(controlInfoDTOS);
        }
        return infoDTOListT;
    }

    private String getRequestUrl(Integer lookUpExternalItemUri, String externalItemUri) {
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(lookUpExternalItemUri);
        String attribute1 = sysLookupValues.getAttribute1();
        return sysLookupValues.getLookupMeaning() + (StringUtils.isBlank(attribute1) ?
                externalItemUri : attribute1);
    }

    private List<CivControlInfoDTO> getCivControlInfoDTOList(String requestUrl, Map<String, Object> requestParam,
                                                          Map<String, String> httpRequestHeader)
            throws JsonProcessingException {
        String result = HttpRemoteUtil.remoteExeFoExternal(JSON.toJSONString(requestParam), httpRequestHeader, requestUrl, MicroServiceNameEum.SENDTYPEPOST);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        JsonNode dataArray = jsonNode.get(Constant.JSON_BO).get(Constant.JSON_LIST);
        return JacksonJsonConverUtil.jsonToListBean(dataArray.toString(),
                new TypeReference<ArrayList<CivControlInfoDTO>>() {
                });
    }
}
