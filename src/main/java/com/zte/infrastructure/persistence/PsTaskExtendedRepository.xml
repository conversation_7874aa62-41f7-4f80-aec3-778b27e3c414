<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PsTaskExtendedRepository">

    <resultMap id="taskExtendedResultMap" type="com.zte.interfaces.dto.TaskExtendedDTO">
        <result property="billNo" column="bill_no" />
        <result property="customerNo" column="customer_no" />
        <result property="entityClass" column="entity_class" />
        <result property="customerPartType" column="customer_part_type" />
        <result property="customerItemName" column="customer_item_name" />
        <result property="taskNo" column="task_no" />
        <result property="fixBomId" column="fix_bom_id" />
    </resultMap>


    <sql id="Base_Column">
        id ,
        bill_no,
        self_supply_prepare_date,
        full_prepare_date,
        business_scene,
        bill_bom,
        factory_code,
        customer_no,
        rel_no,
        entity_class,
        task_type,
        customer_part_type,
        customer_item_name,
        task_no,
        fix_bom_id,
        enabled_flag,
        create_date,
        last_updated_date,
        create_by,
        last_updated_by,
        fix_bom_complete,
        expected_completed_date,
        prepare_product_date,
        prepare_completed_date,
        material_control,
        cloud_type,
        fix_bom_head_id
    </sql>

    <insert id="batchSave" parameterType="java.util.List">
    INSERT INTO ps_task_extended (
        id,
        bill_no,
        self_supply_prepare_date,
        full_prepare_date,
        business_scene,
        bill_bom,
        factory_code,
        customer_no,
        rel_no,
        entity_class,
        task_type,
        customer_part_type,
        customer_item_name,
        task_no,
        fix_bom_id,
        enabled_flag,
        create_date,
        last_updated_date,
        create_by,
        last_updated_by,
        fix_bom_complete,
        expected_completed_date,
        cbom_error_message,
        prepare_product_date,
        prepare_completed_date,
        material_control,
        cloud_type
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
        (
            #{item.id},
            #{item.billNo},
            #{item.selfSupplyPrepareDate},
            #{item.fullPrepareDate},
            #{item.businessScene},
            #{item.billBom},
            #{item.factoryCode},
            #{item.customerNo},
            #{item.relNo},
            #{item.entityClass},
            #{item.taskType},
            #{item.customerPartType},
            #{item.customerItemName},
            #{item.taskNo},
            #{item.fixBomId},
            'Y',
            sysdate,
            sysdate,
            #{item.createBy},
            #{item.lastUpdatedBy},
            #{item.fixBomComplete},
            #{item.expectedCompletedDate},
            #{item.cbomErrorMessage},
            #{item.prepareProductDate},
            #{item.prepareCompletedDate},
            #{item.materialControl},
            #{item.cloudType}
        )
    </foreach>
</insert>

    <select id="bulkQueriesByTaskNos" resultType="com.zte.interfaces.dto.TaskExtendedDTO">
        select
        te.task_no,
        te.task_type,
        te.entity_class,
        te.customer_item_name,
        te.customer_part_type,
        te.customer_no,
        te.fix_bom_id,
        te.material_control,
        te.cloud_type,
        pt.item_no,
        pt.task_qty,
        te.bill_no,
        pt.confirmation_status
        from ps_task_extended te
        left join ps_task pt on te.task_no = pt.task_no
        <where>
            te.ENABLED_FLAG = 'Y'
            AND pt.ENABLED_FLAG = 'Y'
            and te.task_no IN
                <foreach collection="taskNos" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>

        </where>
    </select>


    <select id="queryTaskExtendedList" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        SELECT PTE.TASK_NO,PTE.CUSTOMER_NO,PTE.ENTITY_CLASS,PTE.ID,PT.ITEM_NO
        FROM
        PS_TASK_EXTENDED PTE
        JOIN PS_TASK PT ON PT.TASK_NO = PTE.TASK_NO
        WHERE PTE.ENABLED_FLAG = 'Y'
        AND PT.ENABLED_FLAG = 'Y'
        AND COALESCE(PT.ERP_STATUS, '') != '已取消'
        AND NVL(PTE.FIX_BOM_COMPLETE,'N') = 'N'
        <!--时间往前推半年 180-->
        AND PTE.LAST_UPDATED_DATE > SYSDATE - #{refreshFixBomBeforeHour,jdbcType=INTEGER}
    </select>

    <select id="queryExtendedByTaskNo" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        SELECT PTE.TASK_NO,PTE.CUSTOMER_NO,PTE.ENTITY_CLASS,PTE.ID,PTE.FIX_BOM_ID,PT.ITEM_NO,PTE.FIX_BOM_HEAD_ID
        FROM PS_TASK_EXTENDED PTE  JOIN PS_TASK PT ON PT.TASK_NO = PTE.TASK_NO
        WHERE
        PTE.ENABLED_FLAG = 'Y'
        AND PT.ENABLED_FLAG = 'Y'
        and PTE.task_no = #{taskNo,jdbcType=VARCHAR}
    </select>

    <select id="queryByTaskNos" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select <include refid="Base_Column" />
        from ps_task_extended t
        where t.enabled_flag = 'Y'
        and t.task_no in
        <foreach collection="taskNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryAllFixBomId" resultType="java.lang.String">
        select FIX_BOM_ID
        FROM FIX_BOM_HEAD
        WHERE ENABLED_FLAG = 'Y'
    </select>

    <select id="filterListByCustomerNo" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select t.task_no, customer_no from ps_task_extended t
        where t.enabled_flag = 'Y'
        and t.customer_no in
        <foreach collection="customerNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.task_no in
        <foreach collection="taskNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updatePsExtendedBatch" parameterType="java.util.List">
        with T as(
        <foreach collection="list" item="item" index="index" separator="union">
            (
            select #{item.id,jdbcType=VARCHAR} ID, #{item.fixBomId,jdbcType=VARCHAR} FIX_BOM_ID,
            #{item.fixBomComplete,jdbcType=VARCHAR} FIX_BOM_COMPLETE,
            #{item.cbomErrorMessage,jdbcType=VARCHAR} CBOM_ERROR_MESSAGE,
            #{item.fixBomHeadId,jdbcType=VARCHAR} FIX_BOM_HEAD_ID
            )
        </foreach>
        )
        update PS_TASK_EXTENDED BI
        set BI.FIX_BOM_ID = T.FIX_BOM_ID,
        BI.FIX_BOM_COMPLETE = T.FIX_BOM_COMPLETE,
        BI.LAST_UPDATED_DATE = sysdate,
        BI.CBOM_ERROR_MESSAGE = T.CBOM_ERROR_MESSAGE,
        BI.FIX_BOM_HEAD_ID = T.FIX_BOM_HEAD_ID
        from T where BI.id = T.id
    </update>

    <update id="updateCustomPartType">
        update PS_TASK_EXTENDED t
        set t.customer_part_type = #{customPartType}
        where t.task_no = #{taskNo}
    </update>

    <update id="batchUpdateByTaskNo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE ps_task_extended
            <set>
                <if test="item.billNo != null">bill_no = #{item.billNo},</if>
                <if test="item.selfSupplyPrepareDate != null">self_supply_prepare_date = #{item.selfSupplyPrepareDate},</if>
                <if test="item.fullPrepareDate != null">full_prepare_date = #{item.fullPrepareDate},</if>
                <if test="item.businessScene != null">business_scene = #{item.businessScene},</if>
                <if test="item.billBom != null">bill_bom = #{item.billBom},</if>
                <if test="item.factoryCode != null">factory_code = #{item.factoryCode},</if>
                <if test="item.customerNo != null">customer_no = #{item.customerNo},</if>
                <if test="item.relNo != null">rel_no = #{item.relNo},</if>
                <if test="item.entityClass != null">entity_class = #{item.entityClass},</if>
                <if test="item.taskType != null">task_type = #{item.taskType},</if>
                <if test="item.customerPartType != null">customer_part_type = #{item.customerPartType},</if>
                <if test="item.customerItemName != null">customer_item_name = #{item.customerItemName},</if>
                <if test="item.taskNo != null">task_no = #{item.taskNo},</if>
                <if test="item.fixBomId != null">fix_bom_id = #{item.fixBomId},</if>
                <if test="item.enabledFlag != null">enabled_flag = #{item.enabledFlag},</if>
                <if test="item.fixBomComplete != null">fix_bom_complete = #{item.fixBomComplete},</if>
                <if test="item.expectedCompletedDate != null">expected_completed_date = #{item.expectedCompletedDate},</if>
                <if test="item.cbomErrorMessage != null">cbom_error_message = #{item.cbomErrorMessage},</if>
                <if test="item.prepareProductDate != null">prepare_product_date = #{item.prepareProductDate},</if>
                <if test="item.prepareCompletedDate != null">prepare_completed_date = #{item.prepareCompletedDate},</if>
                last_updated_date = now() -- 自动更新最后修改时间
            </set>
            WHERE task_no = #{item.taskNo}
        </foreach>
    </update>

    <select id="getReconfigurationOrSpecificFlag" resultType="java.lang.String">
        select 1
        from ps_task_extended
        where enabled_flag = 'Y'
        and task_no = #{taskNo, jdbcType=VARCHAR}
        and entity_class = #{entityClass, jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getSpecificTaskExtended" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select
        t.id, t.bill_no, t.self_supply_prepare_date, t.full_prepare_date, t.business_scene, t.bill_bom,
        t.factory_code, t.customer_no, t.rel_no, t.entity_class, t.task_type, t.customer_part_type,
        t.customer_item_name, t.task_no, t.fix_bom_id, t.enabled_flag, t.create_date, t.last_updated_date, t.create_by, t.last_updated_by,
        t.fix_bom_complete, t.expected_completed_date, t.cbom_error_message, t.prepare_product_date, t.prepare_completed_date,
        t.material_control, t.cloud_type,
        case when pt.confirmation_status is null or pt.confirmation_status = '' then '0' else pt.confirmation_status end as confirmation_status
        from ps_task_extended t
        left join ps_task pt
        on t.task_no = pt.task_no
        and pt.enabled_flag = 'Y'
        where t.enabled_flag = 'Y'
        <if test="taskNo != null and taskNo != ''">
            and t.task_no = #{taskNo, jdbcType=VARCHAR}
        </if>
        <if test="taskNoList != null and taskNoList.size() > 0">
            and t.task_no in
            <foreach collection="taskNoList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and t.customer_no in
        <foreach collection="customerNoList" item="item" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="(taskNo == null or taskNo == '') and (taskNoList == null or taskNoList.size() == 0)">
            and 1=2
        </if>
    </select>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select
        t.id, t.bill_no, t.self_supply_prepare_date, t.full_prepare_date, t.business_scene, t.bill_bom,
        t.factory_code, t.customer_no, t.rel_no, t.entity_class, t.task_type, t.customer_part_type,
        t.customer_item_name, t.task_no, t.fix_bom_id, t.enabled_flag, t.create_date, t.last_updated_date, t.create_by, t.last_updated_by,
        t.fix_bom_complete, t.expected_completed_date, t.cbom_error_message, t.prepare_product_date, t.prepare_completed_date,
        t.material_control, t.cloud_type,
        case when pt.confirmation_status is null or pt.confirmation_status = '' then '0' else pt.confirmation_status end as confirmation_status
        from ps_task_extended t
        left join ps_task pt
        on t.task_no = pt.task_no
        and pt.enabled_flag = 'Y'
        where t.enabled_flag = 'Y' and t.customer_no = #{customerNo, jdbcType=VARCHAR}
        <if test="taskNo != null and taskNo != ''">
            and t.task_no = #{taskNo, jdbcType=VARCHAR}
        </if>
        <if test="taskNoList != null and taskNoList.size() > 0">
            and t.task_no in
            <foreach collection="taskNoList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="listByTaskNos" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select <include refid="Base_Column"/>
        from ps_task_extended
        where enabled_flag = 'Y' and task_no IN
        <foreach item="taskNo" index="index" collection="collection"
                 open="(" separator="," close=")">
            #{taskNo}
        </foreach>
    </select>

    <select id="getTaskNoFromWmes" resultType="java.lang.String">
        select distinct t.task_no
        from ps_task_extended t
        left join ps_task pt
        on t.task_no = pt.task_no
        and pt.enabled_flag = 'Y'
        where t.enabled_flag = 'Y'
        and pt.source_sys='WMES'
        and t.customer_no =  #{customerNo}
        and t.task_no in
        <foreach collection="taskNoList" item="taskNo" open="(" separator="," close=")">
            #{taskNo}
        </foreach>
        <if test="entityClassList != null and entityClassList.size() > 0">
            and t.entity_class in
            <foreach collection="entityClassList" item="entityClass" open="(" separator="," close=")">
                #{entityClass}
            </foreach>
        </if>
    </select>
    <select id="filterSpecificTaskNo" resultType="java.lang.String">
        select task_no from ps_task_extended t
        where t.enabled_flag = 'Y'
        and t.task_no in
        <foreach collection="taskNoList" item="taskNo" open="(" separator="," close=")">
            #{taskNo}
        </foreach>
        and t.customer_no in
        <foreach collection="customerNoList" item="customerNo" open="(" separator="," close=")">
            #{customerNo}
        </foreach>
    </select>
    <select id="queryExtendedByTaskNoAndFixBomId" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select
        <include refid="Base_Column"/>
        from ps_task_extended
        where enabled_flag = 'Y'
        and task_no = #{taskNo, jdbcType=VARCHAR}
        <if test="fixBomId != null and fixBomId != ''">
            and fix_bom_id = #{fixBomId, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryExtendedByTaskNoAndFixBomHeadId" resultType="com.zte.interfaces.dto.PsTaskExtendedDTO">
        select
        <include refid="Base_Column"/>
        from ps_task_extended
        where enabled_flag = 'Y'
        and task_no = #{taskNo, jdbcType=VARCHAR}
        <if test="fixBomHeadId != null and fixBomHeadId != ''">
            and fix_bom_head_id = #{fixBomHeadId, jdbcType=VARCHAR}
        </if>
    </select>


</mapper>