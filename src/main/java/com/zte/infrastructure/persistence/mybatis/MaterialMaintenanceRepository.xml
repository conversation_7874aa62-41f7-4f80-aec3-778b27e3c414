<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.MaterialMaintenanceRepository">

    <resultMap id="queryMaterialResultMap" type="com.zte.interfaces.dto.MaterialMaintenanceDTO">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="maintenance_type" jdbcType="VARCHAR" property="maintenanceType" />
        <result column="barcode" jdbcType="VARCHAR" property="barcode" />
        <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
        <result column="item_name" jdbcType="VARCHAR" property="itemName" />
        <result column="supplier" jdbcType="VARCHAR" property="supplier" />
        <result column="storage_method" jdbcType="VARCHAR" property="storageMethod" />
        <result column="thawing_duration" jdbcType="INTEGER" property="thawingDuration" />
        <result column="min_thawing_duration" jdbcType="INTEGER" property="minThawingDuration" />
        <result column="max_recover_storage_date" jdbcType="INTEGER" property="maxRecoverStorageDate" />
        <result column="usage_duration" jdbcType="INTEGER" property="usageDuration" />
        <result column="expiration_duration" jdbcType="INTEGER" property="expirationDuration" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="last_updated_date" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    </resultMap>

    <select id="queryMaterial" resultMap="queryMaterialResultMap">
        select
        id,
        maintenance_type,
        barcode,
        item_code,
        item_name,
        supplier,
        storage_method,
        thawing_duration,
        usage_duration,
        min_thawing_duration,
        max_recover_storage_date,
        expiration_duration,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date
        from material_storage_attributes t
        where t.enabled_flag='Y'
        <if test="params.itemCode != null and params.itemCode != ''">and t.item_code = #{params.itemCode,jdbcType=VARCHAR}</if>
        <if test="params.barcode != null and params.barcode != ''">and t.barcode = #{params.barcode,jdbcType=VARCHAR}</if>
        <if test="params.createBy != null and params.createBy != ''"> and t.create_by = #{params.createBy,jdbcType=VARCHAR}</if>
        <if test="params.startDate != null and params.endDate != null ">and (t.create_date between #{params.startDate} and #{params.endDate})</if>
        <if test="((params.itemCode == null or params.itemCode == '')
          and (params.barcode == null or params.barcode == '')
          and (params.createBy == null or params.createBy == '')
          and (params.startDate == null or params.endDate == null))">
            and 1=2
        </if>
        order by last_updated_date desc, id desc
    </select>

    <select id="queryMaterialByGlue" resultType="java.lang.String">
        select distinct(item_name) from material_storage_attributes where enabled_flag = 'Y' and item_name != ''
        and item_name is not null
    </select>

    <select id="queryItemCodeByItemName" resultType="java.lang.String">
        select distinct(item_code) from material_storage_attributes where enabled_flag = 'Y' and item_code != ''
        and item_code is not null and item_name = #{itemName}
    </select>

    <select id="querySupplierByItemNo" resultType="java.lang.String">
        select distinct(supplier) from material_storage_attributes where enabled_flag = 'Y' and supplier != ''
        and supplier is not null and item_code = #{itemNo}
    </select>

    <insert id="addMaterial" parameterType="com.zte.interfaces.dto.MaterialMaintenanceDTO">
        INSERT INTO material_storage_attributes
        (id,maintenance_type , barcode, item_code, item_name,
         supplier, storage_method, thawing_duration, min_thawing_duration, usage_duration,
        expiration_duration, create_by, create_date, last_updated_by, last_updated_date,
        enabled_flag, max_recover_storage_date)
        VALUES
        (gen_random_uuid(), #{maintenanceType,jdbcType=VARCHAR},
        #{barcode,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
        #{supplier,jdbcType=VARCHAR}, #{storageMethod,jdbcType=VARCHAR},
        #{thawingDuration,jdbcType=INTEGER}, #{minThawingDuration,jdbcType=INTEGER}, #{usageDuration,jdbcType=INTEGER},
        #{expirationDuration,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR},sysdate, #{createBy,jdbcType=VARCHAR},
        sysdate, 'Y', #{maxRecoverStorageDate,jdbcType=INTEGER})
    </insert>

    <update id="updateMaterial" parameterType="com.zte.interfaces.dto.MaterialMaintenanceDTO">
        update material_storage_attributes set
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        barcode = #{barcode,jdbcType=VARCHAR},
        supplier = #{supplier,jdbcType=VARCHAR},
        <if test="storageMethod != null and storageMethod != ''">
            storage_method = #{storageMethod,jdbcType=VARCHAR},
        </if>
        <if test="thawingDuration != null">
            thawing_duration = #{thawingDuration,jdbcType=INTEGER},
        </if>
        <if test="minThawingDuration != null">
            min_thawing_duration = #{minThawingDuration,jdbcType=INTEGER},
        </if>
        <if test="maxRecoverStorageDate != null">
            max_recover_storage_date = #{maxRecoverStorageDate,jdbcType=INTEGER},
        </if>
        <if test="usageDuration != null ">
            usage_duration = #{usageDuration,jdbcType=INTEGER},
        </if>
        <if test="expirationDuration != null ">
            expiration_duration = #{expirationDuration,jdbcType=INTEGER},
        </if>
        <if test="enabledFlag != null and enabledFlag != ''">
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
        </if>
        last_updated_date = sysdate
        where id = #{id,jdbcType=VARCHAR}
        and maintenance_type = #{maintenanceType,jdbcType=VARCHAR}
    </update>

    <select id="checkExist" resultType="int">
        select count(1) from material_storage_attributes
        where enabled_flag = 'Y'
        and maintenance_type = #{maintenanceType,jdbcType=VARCHAR}
        <if test="itemCode != null and itemCode != ''">and item_code = #{itemCode,jdbcType=VARCHAR}</if>
        <if test="supplier != null and supplier != ''">and supplier = #{supplier,jdbcType=VARCHAR}</if>
        <if test="barcode != null and barcode != ''">and barcode = #{barcode,jdbcType=VARCHAR}</if>
        <if test="id != null and id != ''">and id != #{id,jdbcType=VARCHAR}</if>
    </select>

    <update id="delMaterial" parameterType="com.zte.interfaces.dto.MaterialMaintenanceDTO">
        update material_storage_attributes
        set last_updated_date = sysdate,
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        enabled_flag = 'N'
        where enabled_flag = 'Y'
        and id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="queryMaterialByItemNoOrBarcode" resultMap="queryMaterialResultMap">
        select * from material_storage_attributes
        where enabled_flag = 'Y'
        and storage_method = '冷冻'
        <if test="itemNo != null and itemNo != ''">
            and item_code = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="barcode != null and barcode != ''">
            and barcode = #{barcode,jdbcType=VARCHAR}
        </if>
        <if test="(itemNo == null or itemNo == '') and (barcode == null or barcode == '')">
            and 1=2
        </if>
    </select>

    <select id="queryMaterialsByBarcodes" resultMap="queryMaterialResultMap">
        SELECT *
        FROM material_storage_attributes
        WHERE enabled_flag = 'Y'
        AND storage_method = '冷冻'
        AND barcode IN
        <foreach collection="barcodes" item="barcode" open="(" separator="," close=")">
            #{barcode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryMaterialsByItemNos" resultMap="queryMaterialResultMap">
        SELECT *
        FROM material_storage_attributes
        WHERE enabled_flag = 'Y'
        AND storage_method = '冷冻'
        AND item_code IN
        <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
            #{itemNo,jdbcType=VARCHAR}
        </foreach>
    </select>



</mapper>