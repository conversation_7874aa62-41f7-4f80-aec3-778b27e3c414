<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.TradeDataLogRepository">

    <insert id="batchInsert">
        insert into trade_data_log (
        id, tid, keywords, origin, customer_name, project_name, project_phase, cooperation_mode, message_type, push_type,
        craft_section, contract_no, task_no, item_no, sn, json_data, err_msg, begin_time, end_time,
        factory_id, status, remark, create_by, create_date, last_updated_by, last_updated_date, enabled_flag
        )
        values
        <foreach collection="dtoList" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.tid}, #{item.keywords}, #{item.origin}, #{item.customerName}, #{item.projectName}, #{item.projectPhase},
            #{item.cooperationMode}, #{item.messageType}, #{item.pushType},
            #{item.craftSection}, #{item.contractNo}, #{item.taskNo}, #{item.itemNo}, #{item.sn}, #{item.jsonData},
            #{item.errMsg}, #{item.beginTime}, #{item.endTime},
            #{item.factoryId}, #{item.status}, #{item.remark}, #{item.createBy}, sysdate, #{item.lastUpdatedBy},
            sysdate, 'Y'
            )
        </foreach>
    </insert>

    <insert id="insert">
        insert into trade_data_log (
        id, tid, keywords, origin, customer_name, project_name, project_phase, cooperation_mode, message_type, push_type,
        craft_section, contract_no, task_no, item_no, sn, json_data, err_msg, begin_time, end_time,
        factory_id, status, remark, create_by, create_date, last_updated_by, last_updated_date, enabled_flag
        ) values (
        #{id}, #{tid}, #{keywords}, #{origin}, #{customerName}, #{projectName}, #{projectPhase},
        #{cooperationMode}, #{messageType}, #{pushType},
        #{craftSection}, #{contractNo}, #{taskNo}, #{itemNo}, #{sn}, #{jsonData},
        #{errMsg}, #{beginTime}, #{endTime},
        #{factoryId}, #{status}, #{remark}, #{createBy}, sysdate, #{lastUpdatedBy},
        sysdate, 'Y'
        )
    </insert>

    <update id="updateStatusAndErrMsgById">
        update trade_data_log t
        set t.err_msg = #{msg, jdbcType=VARCHAR},
        t.status = #{status, jdbcType=VARCHAR},
        t.last_updated_date = sysdate
        where t.id = #{id, jdbcType=VARCHAR}
    </update>

    <update id="updateParentStatusById">
        update trade_data_log t
        set t.status = #{status, jdbcType=VARCHAR},
        t.last_updated_date = sysdate
        where t.id in (select t1.tid from trade_data_log t1 where t1.id = #{id, jdbcType=VARCHAR})
    </update>
    <update id="updateStatusAndRemarkById">
        update trade_data_log t
        set t.remark = #{remark, jdbcType=VARCHAR},
        t.status = #{status, jdbcType=VARCHAR},
        t.last_updated_date = sysdate
        where t.id = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getPushErrorData" parameterType="com.zte.springbootframe.common.model.Page"
            resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        select
        t.id,t.tid,t.origin,t.customer_name,t.project_name,t.project_phase,
        t.cooperation_mode,t.message_type,t.sn,t.craft_section,t.json_data
        from trade_data_log t
        where t.enabled_flag = 'Y' and status in('PN','CN') and (tid is null or tid = '')
        and t.message_Type = #{params.messageType,jdbcType=VARCHAR}
        and t.customer_name = #{params.customerName,jdbcType=VARCHAR}
        and t.factory_id = #{params.factoryId,jdbcType=INTEGER}
        and t.last_updated_date BETWEEN #{params.startDate,jdbcType=TIMESTAMP} AND #{params.endDate,jdbcType=TIMESTAMP}
        <if test="params.id != null and params.id != ''">
            and t.id > #{params.id,jdbcType=VARCHAR}
        </if>
        order by t.id asc
    </select>
    <select id="getDataBySn" resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        select t.id, t.tid, t.project_phase, t.message_type, t.sn, t.status
        from trade_data_log t
        where t.enabled_flag = 'Y'
        <!-- 获取初始数据状态 -->
        and length(nvl(tid,'')) = 0
        and t.sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="projectPhaseList != null and projectPhaseList.size() > 0">
            and t.project_phase in
            <foreach collection="projectPhaseList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTradeDataLogById" resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        select
        t.id,t.tid,t.origin,t.customer_name,t.project_name,t.project_phase,t.task_no,t.sn,factory_id
        from trade_data_log t
        where t.enabled_flag = 'Y'
        and t.id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getPushData" resultType="java.lang.String">
        select
        t.json_data
        from trade_data_log t
        where t.enabled_flag = 'Y'
        and t.id = #{uuid,jdbcType=VARCHAR}
    </select>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        SELECT
        id, tid, origin, CUSTOMER_NAME, PROJECT_NAME, PROJECT_PHASE, COOPERATION_MODE, MESSAGE_TYPE, PUSH_TYPE, CONTRACT_NO,
        TASK_NO, ITEM_NO, CRAFT_SECTION, SN, JSON_DATA, STATUS, ERR_MSG, FACTORY_ID, CREATE_BY, CREATE_DATE,
        LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG,keywords
        FROM trade_data_log
        WHERE ENABLED_FLAG = 'Y'
        <if test="params.customerName != null and params.customerName != ''">
            AND CUSTOMER_NAME = #{params.customerName,jdbcType=VARCHAR}
        </if>
        <if test="params.messageType != null and params.messageType != ''">
            AND MESSAGE_TYPE = #{params.messageType,jdbcType=VARCHAR}
        </if>
        <if test="params.id != null and params.id != ''">
            AND id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.keywords != null and params.keywords != ''">
            AND keywords = #{params.keywords,jdbcType=VARCHAR}
        </if>
        <if test="params.contractNo != null and params.contractNo != ''">
            AND CONTRACT_NO = #{params.contractNo,jdbcType=VARCHAR}
        </if>
        <if test="params.taskNo != null and params.taskNo != ''">
            AND TASK_NO = #{params.taskNo,jdbcType=VARCHAR}
        </if>
        <if test="params.sn !=null and params.sn != ''">
            AND SN = #{params.sn,jdbcType=VARCHAR}
        </if>
        <if test="params.status != null and params.status != ''">
            AND STATUS = #{params.status,jdbcType=VARCHAR}
        </if>
        <if test="params.statusList != null and params.statusList.size() > 0">
            AND STATUS in
            <foreach collection="params.statusList" item="item" index="index" open="(" separator="," close=")">
                #{params.statusList[${index}],jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.startDate !=null and params.endDate != null">
            AND CREATE_DATE between #{params.startDate,jdbcType=TIMESTAMP} AND #{params.endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="(params.customerName == null or params.customerName == '') and (params.messageType == null or params.messageType == '')
        and (params.contractNo == null or params.contractNo == '') and (params.taskNo == null or params.taskNo == '')
        and (params.sn == null or params.sn == '') and (params.status == null or params.status == '')
        and (params.startDate == null or params.endDate == null) and (params.statusList == null or params.statusList.length = 0)">
            AND 1=2
        </if>
        ORDER BY CREATE_DATE DESC
    </select>

    <select id="selectFirstDateOfTaskNo" resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        SELECT task_no, last_updated_date
        FROM (
        SELECT b.task_no, a.last_updated_date,
        ROW_NUMBER() OVER (PARTITION BY b.task_no ORDER BY a.last_updated_date ASC) AS rn
        FROM trade_data_log a,
        (
            <foreach collection="params" item="item"  separator="UNION ALL" >
                SELECT
                #{item.taskNo,jdbcType=VARCHAR} task_no, #{item.contractNo,jdbcType=VARCHAR} contract_no
            </foreach>
        ) b
        WHERE a.enabled_flag = 'Y'
        and a.contract_no = b.contract_no
        and a.create_date > sysdate - 180
        and a.message_type = #{messageType,jdbcType=VARCHAR}
        and a.status = 'CY'
        ) t
        WHERE t.rn = 1
    </select>

    <select id="selectPushFailList" resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        select
        t.id,t.origin,t.customer_name,t.project_name,t.project_phase,t.factory_id,
        t.cooperation_mode,t.message_type,t.sn,t.craft_section,t.json_data,T.keywords
        from trade_data_log t
        where t.enabled_flag = 'Y' and status in('PN','CN') and (tid is null or tid = '')
        and t.id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="createDate != null">
            <![CDATA[AND CREATE_DATE>= #{createDate} ]]>
        </if>
    </select>
</mapper>