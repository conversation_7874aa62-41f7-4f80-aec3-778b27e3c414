<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.cbom.FixBomHeadRepository">

    <sql id="baseColumns">"
        ID,FIX_BOM_ID,ENABLED_FLAG,CREATED_BY,CREATION_DATE,
        LAST_UPDATED_BY,LAST_UPDATE_DATE,MBOM_VERSION,CUST_NAME,CUST_NO,TASK_NO
    </sql>

    <delete id="deleteByIdList" parameterType="java.util.List">
        DELETE FROM  FIX_BOM_HEAD
        WHERE ID IN
        <foreach collection="idList" open="(" separator="," item="item" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectOneByFixBomId" parameterType="java.lang.String" resultType="java.lang.String">
        select id
        from FIX_BOM_HEAD
        WHERE ENABLED_FLAG = 'Y'
        AND FIX_BOM_ID = #{fixBomId,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO FIX_BOM_HEAD
        (
        ID,FIX_BOM_ID,ENABLED_FLAG,CREATED_BY,CREATION_DATE,
        LAST_UPDATED_BY,LAST_UPDATE_DATE,MBOM_VERSION,CUST_NAME,CUST_NO,TASK_NO
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.fixBomId,jdbcType=VARCHAR},
            'Y',
            #{item.createdBy,jdbcType=VARCHAR},sysdate,
            #{item.lastUpdatedBy,jdbcType=VARCHAR},sysdate,
            #{item.mbomVersion,jdbcType=VARCHAR},#{item.custName,jdbcType=VARCHAR},#{item.custNo,jdbcType=VARCHAR},
            #{item.taskNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectIdByFixBomIdList" resultType="com.zte.interfaces.dto.mbom.FixBomHeadDTO">
        select
        <include refid="baseColumns"/>
        FROM FIX_BOM_HEAD
        where enabled_flag = 'Y'
        <if test="fixBomIdList != null and fixBomIdList.size >0">
            AND FIX_BOM_ID IN
            <foreach collection="fixBomIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


</mapper>