<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.SpSpecialityParamRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.domain.model.SpSpecialityParam" id="spSpecialityParamMap">
        <result property="specialityParamId" column="speciality_param_id"/>
        <result property="templateId" column="template_id"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemName" column="item_name"/>
        <result property="itemUnit" column="item_unit"/>
        <result property="itemVersion" column="item_version"/>
        <result property="taskId" column="task_id"/>
        <result property="usageScope" column="usage_scope"/>
        <result property="operator" column="operator"/>
        <result property="destinedArea" column="destined_area"/>
        <result property="applyTask" column="apply_task"/>
        <result property="fixedStr" column="fixed_str"/>
        <result property="progress" column="progress"/>
        <result property="barcode" column="barcode"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="isRecovery" column="is_recovery"/>
        <result property="itemNum" column="item_num"/>
        <result property="productionUnit" column="production_unit"/>
        <result property="productBigClass" column="product_category"/>
        <result property="productSmallClass" column="product_small_class"/>
        <result property="syncMdsStatus" column="sync_mds_status"/>
    </resultMap>

    <insert id="insert">
        insert into sp_speciality_param (speciality_param_id, template_id, template_name, item_code, item_name,item_unit, item_version, template_item,
        task_id, usage_scope, operator, destined_area, apply_task, fixed_str, progress, create_by, last_updated_by,item_num,production_unit,product_category,product_small_class)
        values (#{specialityParamId}, #{templateId}, #{templateName}, #{itemCode}, #{itemName}, #{itemUnit}, #{itemVersion}, #{templateItem},
        #{taskId}, #{usageScope}, #{operator}, #{destinedArea}, #{applyTask}, #{fixedStr}, #{progress}, #{createBy}, #{lastUpdatedBy},#{itemNum},#{productionUnit},#{productBigClass},#{productSmallClass})
    </insert>

    <update id="updateById">
        update sp_speciality_param
        <set>
            <!--  last_updated_date = SYSDATE,
                  last_updated_by = #{lastUpdatedBy},
             -->
            <if test="specialityParamId != null and specialityParamId != ''" >
                speciality_param_id = #{specialityParamId},
            </if>
            <if test="templateId != null and templateId != ''" >
                template_id = #{templateId},
            </if>
            <if test="itemCode != null and itemCode != ''" >
                item_code = #{itemCode},
            </if>
            <if test="itemVersion != null and itemVersion != ''" >
                item_version = #{itemVersion},
            </if>
            <if test="taskId != null and taskId != ''" >
                task_id = #{taskId},
            </if>
            <if test="usageScope != null and usageScope != ''" >
                usage_scope = #{usageScope},
            </if>
            <if test="operator != null and operator != ''" >
                operator = #{operator},
            </if>
            <if test="destinedArea != null and destinedArea != ''" >
                destined_area = #{destinedArea},
            </if>
            <if test="applyTask != null and applyTask != ''" >
                apply_task = #{applyTask},
            </if>
            <if test="fixedStr != null and fixedStr != ''" >
                fixed_str = #{fixedStr},
            </if>
            <if test="progress != null and progress != ''" >
                progress = #{progress},
            </if>
            <if test="barcode != null and barcode != ''" >
                barcode = #{barcode},
            </if>
            <if test="createBy != null and createBy != ''" >
                create_by = #{createBy},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''" >
                last_updated_by = #{lastUpdatedBy},
            </if>
            <if test="lastUpdatedDate != null" >
                last_updated_date = #{lastUpdatedDate},
            </if>
            <if test="enabledFlag != null and enabledFlag != ''" >
                enabled_flag = #{enabledFlag},
            </if>
            <if test="isRecovery != null and isRecovery != ''" >
                is_recovery = #{isRecovery},
            </if>
            <if test="itemNum != null and itemNum != ''" >
                item_num = item_num + #{itemNum},
            </if>
            <if test="syncMdsStatus != null and syncMdsStatus != ''" >
                sync_mds_status = #{syncMdsStatus},
            </if>
        </set>
        where speciality_param_id = #{specialityParamId}
    </update>

    <delete id="deleteByIds">
        delete from sp_speciality_param where speciality_param_id in
        <foreach collection="specialityParamIds" item="id" index="index"  open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="countPage" resultType="java.lang.Long">
        select count(1) from sp_speciality_param ssp
        <include refid="pageParam"/>
    </select>
    <select id="queryPage" resultMap="spSpecialityParamMap">
        select ssp.*,st.template_name templateName,ssp.progress||'%' as progressStr,
        case when ssp.is_recovery =1 then '已回收' when ssp.progress = 100 then '已完成' else '生成中' end as status
        from sp_speciality_param ssp
        left join sp_template st on ssp.template_id=st.template_id
        <include refid="pageParam"/>
        order by ssp.create_date desc
        limit  #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </select>
    <sql id="pageParam">
        <where>
            ssp.enabled_flag= 'Y'
            <if test="specialityParamId != null and specialityParamId != ''" >
                and ssp.speciality_param_id = #{specialityParamId}
            </if>
            <if test="templateId != null and templateId != ''" >
                and ssp.template_id = #{templateId}
            </if>
            <if test="itemCode != null and itemCode != ''" >
                and ssp.item_code = #{itemCode}
            </if>
            <if test="itemVersion != null and itemVersion != ''" >
                and ssp.item_version = #{itemVersion}
            </if>
            <if test="taskId != null and taskId != ''" >
                and ssp.task_id = #{taskId}
            </if>
            <if test="usageScope != null and usageScope != ''" >
                and ssp.usage_scope = #{usageScope}
            </if>
            <if test="operator != null and operator != ''" >
                and ssp.operator = #{operator}
            </if>
            <if test="destinedArea != null and destinedArea != ''" >
                and ssp.destined_area = #{destinedArea}
            </if>
            <if test="applyTask != null and applyTask != ''" >
                and ssp.apply_task = #{applyTask}
            </if>
            <if test="fixedStr != null and fixedStr != ''" >
                and ssp.fixed_str = #{fixedStr}
            </if>
            <if test="progress != null and progress != ''" >
                and ssp.progress = #{progress}
            </if>
            <if test="createBy != null and createBy != ''" >
                and ssp.create_by = #{createBy}
            </if>
            <if test="createDate != null and createDate != ''" >
                and ssp.create_date = #{createDate}
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''" >
                and ssp.last_updated_by = #{lastUpdatedBy}
            </if>
            <if test="lastUpdatedDate != null and lastUpdatedDate != ''" >
                and ssp.last_updated_date = #{lastUpdatedDate}
            </if>
            <if test="enabledFlag != null and enabledFlag != ''" >
                and ssp.enabled_flag = #{enabledFlag}
            </if>
            <if test="startTime != null and endTime != null">
                AND ssp.create_date between #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="syncMdsStatus != null and syncMdsStatus != ''" >
                and ssp.sync_mds_status = #{syncMdsStatus}
            </if>
        </where>
    </sql>

    <select id="selectById" resultMap="spSpecialityParamMap">
        select * from sp_speciality_param where speciality_param_id = #{specialityParamId}
    </select>
    <select id="selectList" resultMap="spSpecialityParamMap">
        select * from sp_speciality_param where enabled_flag= 'Y'
        <if test="specialityParamId != null and specialityParamId != ''" >
            and speciality_param_id = #{specialityParamId}
        </if>
        <if test="progress != null" >
            and progress <![CDATA[<]]> #{progress}
        </if>
<!--        and last_updated_date <![CDATA[<]]> now()::timestamp - INTERVAL '10 min'-->
    </select>

    <select id="selectIdByTask" resultMap="spSpecialityParamMap">
        select speciality_param_id,progress from sp_speciality_param where apply_task = #{task}
        and enabled_flag= 'Y'
    </select>

    <select id="getEigenValueIndex" resultType="java.lang.Long">
        select total from whole_device_code_info
        where eigen_value = #{eigenValue}
        and enabled_flag = 'Y'
    </select>

    <select id="countUndoneByCreator" resultType="java.lang.Long">
        select count(1) from sp_speciality_param
        where create_by = #{createBy}
        and enabled_flag = 'Y'
        and progress != 100
    </select>

    <insert id="insertEigenValue">
        insert into whole_device_code_info(eigen_value,total,enabled_flag,create_by,last_updated_by)
        values(#{wdcEigenValue},#{wdcTotal},'Y',#{lastUpdatedBy},#{lastUpdatedBy})
    </insert>


    <update id="updateEigenValueIndex">
        update whole_device_code_info
        set total = total + #{wdcTotal},
        last_updated_by = #{lastUpdatedBy},
        last_updated_date = sysdate
        where eigen_value = #{wdcEigenValue}
        and enabled_flag = 'Y'
    </update>

    <select id="getTelmexEigenValueIndex" resultType="java.lang.Long">
        select total from GPON_TELMEX_SN_INFO
        where eigen_value = #{eigenValue}
        and enabled_flag = 'Y'
    </select>

    <update id="updateTelmexEigenValueIndex">
        update GPON_TELMEX_SN_INFO
        set total = #{total},
        last_updated_by = #{lastUpdatedBy},
        last_updated_date = sysdate
        where eigen_value = #{eigenValue}
        and enabled_flag = 'Y'
    </update>

    <insert id="insertTelmexEigenValue">
        insert into GPON_TELMEX_SN_INFO(eigen_value,total,enabled_flag,create_by,last_updated_by)
        values(#{telmexEigenValue},#{telmexTotal},'Y',#{lastUpdatedBy},#{lastUpdatedBy})
    </insert>

</mapper>