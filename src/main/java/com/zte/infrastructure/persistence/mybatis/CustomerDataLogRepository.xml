<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.CustomerDataLogRepository">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.CustomerDataLogDTO" id="customerDataLogMap">
        <result property="id" column="id"/>
        <result property="tid" column="tid"/>
        <result property="origin" column="origin"/>
        <result property="customerName" column="customer_name"/>
        <result property="projectName" column="project_name"/>
        <result property="projectPhase" column="project_phase"/>
        <result property="cooperationMode" column="cooperation_mode"/>
        <result property="messageType" column="message_type"/>
        <result property="craftSection" column="craft_section"/>
        <result property="contractNo" column="contract_no"/>
        <result property="taskNo" column="task_no"/>
        <result property="itemNo" column="item_no"/>
        <result property="sn" column="sn"/>
        <result property="jsonData" column="json_data"/>
        <result property="errMsg" column="err_msg"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="factoryId" column="factory_id"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
    </resultMap>

    <select id="getPushErrorData" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="customerDataLogMap">
        select
        t.id,t.tid,t.origin,t.customer_name,t.project_name,t.project_phase,
        t.cooperation_mode,t.message_type,t.sn,t.craft_section,t.json_data
        from customer_data_log t
        where t.enabled_flag = 'Y' and status in( 'PN','CN') and (tid is null or tid = '')
        and t.message_Type  = #{params.messageType,jdbcType=VARCHAR}
        and t.customer_name  = #{params.customerName,jdbcType=VARCHAR}
        and t.factory_id  = #{params.factoryId,jdbcType=VARCHAR}
        and t.last_updated_date  BETWEEN #{params.startDate,jdbcType=TIMESTAMP}  AND #{params.endDate,jdbcType=TIMESTAMP}
        <if test="params.id != null and params.id != ''">
            and t.id > #{params.id,jdbcType=VARCHAR}
        </if>
        order by t.id asc
    </select>


    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="customerDataLogMap">
        SELECT
        id, tid, origin, CUSTOMER_NAME, PROJECT_NAME, PROJECT_PHASE, COOPERATION_MODE, MESSAGE_TYPE, CONTRACT_NO,
        TASK_NO, ITEM_NO, CRAFT_SECTION, SN,
        JSON_DATA, STATUS, ERR_MSG, FACTORY_ID, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG
        FROM CUSTOMER_DATA_LOG
        WHERE ENABLED_FLAG = 'Y'
        <if test="params.customerName != null and params.customerName != ''">
            AND CUSTOMER_NAME = #{params.customerName,jdbcType=VARCHAR}
        </if>
        <if test="params.messageType != null and params.messageType != ''">
            AND MESSAGE_TYPE = #{params.messageType,jdbcType=VARCHAR}
        </if>
        <if test="params.contractNo != null and params.contractNo != ''">
            AND CONTRACT_NO = #{params.contractNo,jdbcType=VARCHAR}
        </if>
        <if test="params.taskNo != null and params.taskNo != ''">
            AND TASK_NO = #{params.taskNo,jdbcType=VARCHAR}
        </if>
        <if test="params.sn !=null and params.sn != ''">
            AND SN = #{params.sn,jdbcType=VARCHAR}
        </if>
        <if test="params.status != null and params.status != ''">
            AND STATUS = #{params.status,jdbcType=VARCHAR}
        </if>
        <if test="params.statusList != null and params.statusList.size() > 0">
            AND STATUS in
            <foreach collection="params.statusList" item="item" index="index" open="(" separator="," close=")">
                #{params.statusList[${index}],jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.lastUpdatedStartDate !=null and params.lastUpdatedEndDate != null">
            AND LAST_UPDATED_DATE between #{params.lastUpdatedStartDate,jdbcType=TIMESTAMP}
            AND #{params.lastUpdatedEndDate,jdbcType=TIMESTAMP}
        </if>
        <if test="(params.customerName == null or params.customerName == '') and (params.messageType == null or params.messageType == '')
        and (params.contractNo == null or params.contractNo == '') and (params.taskNo == null or params.taskNo == '')
        and (params.sn == null or params.sn == '') and (params.status == null or params.status == '')
        and (params.lastUpdatedStartDate == null or params.lastUpdatedEndDate == null) and (params.statusList == null or params.statusList.length = 0)">
            AND 1=2
        </if>
        ORDER BY LAST_UPDATED_DATE DESC
    </select>

    <insert id="insertCustomerDataLogFromMES" parameterType="com.zte.interfaces.dto.CustomerDataLogDTO">
        INSERT INTO CUSTOMER_DATA_LOG
        (id, tid, origin, CUSTOMER_NAME, PROJECT_NAME, PROJECT_PHASE, COOPERATION_MODE, MESSAGE_TYPE, CRAFT_SECTION,
        CONTRACT_NO, TASK_NO, ITEM_NO, SN,
        JSON_DATA, ERR_MSG, BEGIN_TIME, END_TIME, FACTORY_ID, STATUS, REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY,
        LAST_UPDATED_DATE, ENABLED_FLAG)
        VALUES(#{id,jdbcType=VARCHAR}, #{tid,jdbcType=VARCHAR}, #{origin,jdbcType=VARCHAR},
        #{customerName,jdbcType=VARCHAR},
        #{projectName,jdbcType=VARCHAR}, #{projectPhase,jdbcType=VARCHAR}, #{cooperationMode,jdbcType=VARCHAR},
        #{messageType,jdbcType=VARCHAR},
        #{craftSection,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR}, #{taskNo,jdbcType=VARCHAR},
        #{itemNo,jdbcType=VARCHAR},
        #{sn,jdbcType=VARCHAR}, #{jsonData,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR},
        #{beginTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP}, #{factoryId,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR},
        #{lastUpdatedDate,jdbcType=TIMESTAMP}, 'Y')
    </insert>

    <insert id="insertEntity" parameterType="com.zte.interfaces.dto.CustomerDataLogDTO">
        insert into customer_data_log
        (
        id, tid, origin, customer_name, project_name, project_phase, cooperation_mode, message_type,
        craft_section, contract_no, task_no, item_no, sn, json_data, err_msg, begin_time, end_time,
        factory_id, status, remark, create_by, create_date, last_updated_by, last_updated_date, enabled_flag
        )
        values
        (
        #{id}, #{tid}, #{origin}, #{customerName}, #{projectName}, #{projectPhase}, #{cooperationMode}, #{messageType},
        #{craftSection}, #{contractNo}, #{taskNo}, #{itemNo}, #{sn}, #{jsonData}, #{errMsg}, #{beginTime}, #{endTime},
        #{factoryId}, #{status}, #{remark}, #{createBy}, sysdate, #{lastUpdatedBy}, sysdate, 'Y'
        )
    </insert>

    <insert id="batchInsert">
        insert into customer_data_log
        (
        id, tid, origin, customer_name, project_name, project_phase, cooperation_mode, message_type,
        craft_section, contract_no, task_no, item_no, sn, json_data, err_msg, begin_time, end_time,
        factory_id, status, remark, create_by, create_date, last_updated_by, last_updated_date, enabled_flag
        )
        values
        <foreach collection="dtoList" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.tid}, #{item.origin}, #{item.customerName}, #{item.projectName}, #{item.projectPhase},
            #{item.cooperationMode}, #{item.messageType},
            #{item.craftSection}, #{item.contractNo}, #{item.taskNo}, #{item.itemNo}, #{item.sn}, #{item.jsonData},
            #{item.errMsg}, #{item.beginTime}, #{item.endTime},
            #{item.factoryId}, #{item.status}, #{item.remark}, #{item.createBy}, sysdate, #{item.lastUpdatedBy},
            sysdate, 'Y'
            )
        </foreach>
    </insert>

    <update id="updateStatusAndErrMsgById">
        update customer_data_log t
        set t.err_msg = #{msg, jdbcType=VARCHAR},
        t.status = #{status, jdbcType=VARCHAR},
        t.last_updated_date = sysdate
        where t.id = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getPushedSnScanData" resultType="java.lang.String">
        select distinct sn||craft_section
        from customer_data_log
        where sn in
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
        and message_type = 'ZTEiMES-ByteDance-BoardStation'
        and (tid is null or tid = '')
        and status in ('CY', 'PY')
        and enabled_flag = 'Y'
    </select>

    <select id="getPushedTestProcessSnScanData" resultMap="customerDataLogMap">
        select sn, craft_section
        from customer_data_log
        where (sn, craft_section) in
        <foreach collection="customerDataLogDTOList" item="item" open="(" separator="," close=")">
            (#{item.sn, jdbcType=VARCHAR}, #{item.craftSection, jdbcType=VARCHAR})
        </foreach>
        and message_type = #{messageType, jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </select>

    <select id="selectByParamsPage" parameterType="com.zte.springbootframe.common.model.Page"
            resultType="com.zte.interfaces.dto.CustomerDataLogDTO">
        SELECT
        *
        FROM CUSTOMER_DATA_LOG
        WHERE ENABLED_FLAG = 'Y'
        and tid is null
        <if test="params.statusList != null and params.statusList.size()>0">
            and status in
            <foreach collection="params.statusList" open="(" separator="," index="index" close=")">
                #{params.statusList[${index}]}
            </foreach>
        </if>
        <if test="params.idList != null and params.idList.size()>0">
            and id in
            <foreach collection="params.idList" open="(" separator="," index="index" close=")">
                #{params.idList[${index}]}
            </foreach>
        </if>
        <if test="params.messageTypeList != null and params.messageTypeList.size()>0">
            and MESSAGE_TYPE in
            <foreach collection="params.messageTypeList" open="(" index="index" separator="," close=")">
                #{params.messageTypeList[${index}]}
            </foreach>
        </if>
        <if test="params.customerNameList != null and params.customerNameList.size()>0">
            and CUSTOMER_NAME in
            <foreach collection="params.customerNameList" open="(" index="index" separator="," close=")">
                #{params.customerNameList[${index}]}
            </foreach>
        </if>
        <if test="params.endTime != null">
            and END_TIME = #{params.endTime}
        </if>
        order by create_date asc,id asc
    </select>

    <update id="updateParentStatusById">
        update customer_data_log t
        set t.status = #{status, jdbcType=VARCHAR},
        t.last_updated_date = sysdate
        where t.id in
        (select t1.tid from customer_data_log t1 where t1.id = #{id, jdbcType=VARCHAR})
    </update>

    <select id="getPushedDataById" parameterType="java.lang.String" resultMap="customerDataLogMap">
        select
        t.id, t.tid, t.customer_name,t.project_name, t.project_phase, t.message_type, t.contract_no, t.status, t.sn,
        t.task_no,t.json_data,t.err_msg,
        t.origin
        from customer_data_log t
        where t.id = #{id, jdbcType=VARCHAR}
    </select>

    <select id="getPushedSnScanDataOfL6" resultType="java.lang.String">
        select distinct sn||craft_section
        from customer_data_log
        where sn in
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
        and message_type = 'ZTEiMes-Tencent-PushL6TestData'
        and (tid is null or tid = '')
        and status in ('CY', 'PY')
        and enabled_flag = 'Y'
    </select>
</mapper>