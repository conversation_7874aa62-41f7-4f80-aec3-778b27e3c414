<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.ModelRcvBillRepository">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.ModelRcvBillDTO" id="modeRcvBillMap">
        <result property="billNo" column="bill_no"/>
        <result property="taskNo" column="task_no"/>
        <result property="customerName" column="customer_name"/>
        <result property="pushStatus" column="push_status"/>
        <result property="pushDate" column="push_date"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="pushFailCount" column="push_fail_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
    </resultMap>

    <sql id="ModelRcvBill_Base_Column_List">
        bill_no,
        task_no,
        customer_name,
        push_status,
        push_date,
        error_msg,
        push_fail_count,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date,
        enabled_flag,
        rcv_no
    </sql>

    <insert id="batchInsertIgnoreExisted" parameterType="java.util.List">
        INSERT INTO push_std_model_rcv_bill (
        bill_no,
        task_no,
        customer_name,
        push_status,
        rcv_no,
        create_by,
        last_updated_by
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.billNo},
            #{item.taskNo},
            #{item.customerName},
            #{item.pushStatus},
            #{item.rcvNo},
            #{item.createBy},
            #{item.lastUpdatedBy}
            )
        </foreach>
        ON CONFLICT (rcv_no) DO NOTHING
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO push_std_model_rcv_bill_detail (
        id,
        order_no,
        material_mpn,
        storage_area,
        carton_no,
        sn_no,
        rcv_no,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date,
        enabled_flag
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.billNo},
            #{item.materialMpn},
            #{item.storageArea},
            #{item.cartonNo},
            #{item.snNo},
            #{item.orderNo},
            #{item.createBy},
            sysdate,
            #{item.lastUpdatedBy},
            sysdate,
            'Y'
            )
        </foreach>
    </insert>

    <select id="getByCondition" resultMap="modeRcvBillMap" parameterType="com.zte.interfaces.dto.ModelRcvBillDTO">
        SELECT
        <include refid="ModelRcvBill_Base_Column_List" />
        FROM mes_centerfactory.push_std_model_rcv_bill
        WHERE enabled_flag = 'Y'
        <if test="pushStatus != null">
            AND push_status = #{pushStatus}
        </if>
        <if test="startCreateDate != null">
            AND create_date &gt;= #{startCreateDate}
        </if>
        <if test="endCreateDate != null">
            AND create_date &lt;= #{endCreateDate}
        </if>
        <if test="billNo != null and billNo != ''">
            AND bill_no = #{billNo}
        </if>
        <if test="taskNo != null and taskNo != ''">
            AND task_no = #{taskNo}
        </if>
        ORDER BY create_date DESC
    </select>

    <update id="updateStatus">
        <foreach collection="list" item="item" separator=";">
            UPDATE push_std_model_rcv_bill
            SET
            push_status = #{item.pushStatus},
            <if test="item.pushDate != null">
                push_date = #{item.pushDate},
            </if>
            error_msg = #{item.errorMsg},
            last_updated_date = sysdate
            WHERE rcv_no = #{item.rcvNo}
            AND enabled_flag = 'Y'
        </foreach>
    </update>

    <select id="getChangeSNByCondition" resultType="com.zte.interfaces.dto.ModelChangedCartonDTO">
        SELECT
        id,
        carton_no AS cartonNo,
        push_status AS pushStatus,
        push_date AS pushDate,
        error_msg AS errorMsg,
        push_fail_count AS pushFailCount,
        create_by AS createBy,
        create_date AS createDate,
        last_updated_by AS lastUpdatedBy,
        last_updated_date AS lastUpdatedDate,
        enabled_flag AS enabledFlag
        FROM
        push_std_model_changed_carton
        WHERE
        enabled_flag = 'Y'
        <if test="startCreateDate != null">
            AND create_date &gt;= #{startCreateDate}
        </if>
        <if test="endCreateDate != null">
            AND create_date &lt;= #{endCreateDate}
        </if>
        <if test="pushStatus != null">
            AND push_status = #{pushStatus}
        </if>
        ORDER BY
        create_date DESC
        limit 1000
    </select>

    <select id="getModelRcvBillDetailByCartonNo" resultType="com.zte.interfaces.dto.ModelRcvBillDetailDTO">
        SELECT
        mrbd.id,
        mrbd.order_no AS orderNo,
        mrb.task_no AS taskNo,
        mrb.rcv_no AS rcvNo,
        mrbd.material_mpn AS materialMpn,
        mrbd.storage_area AS storageArea,
        mrbd.carton_no AS cartonNo,
        mrbd.sn_no AS snNo,
        mrbd.create_by AS createBy,
        mrbd.create_date AS createDate,
        mrbd.last_updated_by AS lastUpdatedBy,
        mrbd.last_updated_date AS lastUpdatedDate,
        mrbd.enabled_flag AS enabledFlag
        FROM
        push_std_model_rcv_bill_detail as mrbd
        left join push_std_model_rcv_bill as mrb on mrbd.order_no = mrb.bill_no
        WHERE
        mrbd.enabled_flag = 'Y' and mrb.enabled_flag = 'Y'
        and mrb.push_status = 2
        AND mrbd.carton_no IN
        <foreach item="cartonNo" collection="cartonNoList" open="(" separator="," close=")">
            #{cartonNo}
        </foreach>
        ORDER BY
        mrbd.last_updated_date DESC
    </select>

    <update id="updateStatusByCartonNo">
        <foreach collection="list" item="item" separator=";">
            UPDATE push_std_model_changed_carton
            SET
            push_status = #{item.pushStatus},
            <if test="item.pushDate != null">
                push_date = #{item.pushDate},
            </if>
            <if test="item.errorMsg != null and item.errorMsg != ''">
                error_msg = #{item.errorMsg},
            </if>
            last_updated_date = sysdate
            WHERE carton_no = #{item.cartonNo}
            AND enabled_flag = 'Y'
        </foreach>
    </update>

    <insert id="batchInsertChangeCartonNo" parameterType="java.util.List">
        INSERT INTO push_std_model_changed_carton (
        id,
        carton_no,
        push_status,
        create_by,
        create_date,
        enabled_flag
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.cartonNo},
            #{item.pushStatus},
            #{item.createBy},
            sysdate,
            'Y'
            )
        </foreach>
    </insert>

    <delete id="deleteExistedSn">
        delete from push_std_model_rcv_bill_detail
        <where>
            <if test="rcvNos != null and rcvNos.size() > 0">
                rcv_no IN (
                <foreach collection="rcvNos" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </delete>

    <update id="updateErrorMsg">
        <foreach collection="list" item="dto" separator=";">
            update push_std_model_rcv_bill
            SET
            error_msg = #{dto.errorMsg},
            last_updated_date = sysdate
            WHERE rcv_no = #{dto.rcvNo}
            AND enabled_flag = 'Y'
        </foreach>
    </update>

</mapper>
