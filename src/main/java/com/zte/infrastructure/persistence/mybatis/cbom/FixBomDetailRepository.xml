<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.cbom.FixBomDetailRepository">

    <sql id="baseColumns">"
        ID,FIX_BOM_ID,ITEM_LEVEL,ITEM_SEQ,ITEM_TYPE,ZTE_CODE,CUSTOMER_COMPONENT_TYPE,ITEM_NO,
        ITEM_SUPPLIER_NO,ITEM_NAME,
        ITEM_NUMBER,ITEM_GROUP,DELETE_FLAG,
        REQUIRE_MATERIAL_UPLOAD,IS_PRICED_MATERIAL,UPLOAD_BY_SN,
        FIX_BOM_REQUIRED,BOX_BOM_REQUIRED,BOX_PRIORITY,ITEM_VERSION,ITEM_MATERIAL_TYPE,
        ENA<PERSON>ED_FLAG,ZTE_CODE_NAME,CREATED_BY,LAST_UPDATED_BY,CREATION_DATE,LAST_UPDATE_DATE,HEAD_ID
    </sql>


    <delete id="deleteByHeadIdList">
        UPDATE FIX_BOM_DETAIL
        SET
        ENABLED_FLAG = 'N',
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{lastUpdatedBy}
        WHERE ENABLED_FLAG = 'Y'
        AND HEAD_ID in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>


    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO FIX_BOM_DETAIL
        (
            ID,FIX_BOM_ID,ITEM_LEVEL,ITEM_SEQ,
            ITEM_TYPE,ZTE_CODE,CUSTOMER_COMPONENT_TYPE,ITEM_NO,
            ITEM_SUPPLIER_NO,ITEM_NAME,ITEM_NUMBER,ITEM_GROUP,
            DELETE_FLAG, REQUIRE_MATERIAL_UPLOAD,IS_PRICED_MATERIAL,UPLOAD_BY_SN,
            FIX_BOM_REQUIRED,BOX_BOM_REQUIRED,BOX_PRIORITY,ENABLED_FLAG,
            ZTE_CODE_NAME, ITEM_VERSION,ITEM_MATERIAL_TYPE,
            CREATED_BY,LAST_UPDATED_BY,CREATION_DATE,LAST_UPDATE_DATE,HEAD_ID
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=VARCHAR},#{item.fixBomId,jdbcType=VARCHAR},#{item.itemLevel,jdbcType=VARCHAR},#{item.itemSeq,jdbcType=VARCHAR},
                #{item.itemType,jdbcType=VARCHAR},#{item.zteCode,jdbcType=VARCHAR},#{item.customerComponentType,jdbcType=VARCHAR},#{item.itemNo,jdbcType=VARCHAR},
                #{item.itemSupplierNo,jdbcType=VARCHAR},#{item.itemName,jdbcType=VARCHAR},
            #{item.itemNumber,jdbcType=VARCHAR},#{item.itemGroup,jdbcType=VARCHAR},
                #{item.deleteFlag,jdbcType=VARCHAR},#{item.requireMaterialUpload,jdbcType=VARCHAR},#{item.isPricedMaterial,jdbcType=VARCHAR},#{item.uploadBySn,jdbcType=VARCHAR},
                #{item.fixBomRequired,jdbcType=VARCHAR},#{item.boxBomRequired,jdbcType=VARCHAR},#{item.boxPriority,jdbcType=INTEGER},'Y',
                #{item.zteCodeName,jdbcType=VARCHAR},#{item.itemVersion,jdbcType=VARCHAR},#{item.itemMaterialType,jdbcType=VARCHAR},
                #{item.createdBy,jdbcType=VARCHAR},#{item.lastUpdatedBy,jdbcType=VARCHAR},#{item.creationDate,jdbcType=TIMESTAMP},#{item.lastUpdateDate,jdbcType=TIMESTAMP},
                #{item.headId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectIdByHeadId" resultType="com.zte.interfaces.dto.mbom.FixBomDetailDTO">
        SELECT
        fbd.id,
        fbd.ITEM_LEVEL,fbd.ITEM_SEQ,fbd.ITEM_TYPE,fbd.ZTE_CODE,fbd.CUSTOMER_COMPONENT_TYPE,fbd.ITEM_NO,
        fbd.ITEM_SUPPLIER_NO,fbd.ITEM_NAME,
        fbd.ITEM_NUMBER,fbd.ITEM_GROUP,fbd.DELETE_FLAG,
        fbd.REQUIRE_MATERIAL_UPLOAD,fbd.IS_PRICED_MATERIAL,fbd.UPLOAD_BY_SN,
        fbd.FIX_BOM_REQUIRED,fbd.BOX_BOM_REQUIRED,fbd.BOX_PRIORITY,FBD.ZTE_CODE_NAME,
        fbd.ITEM_MATERIAL_TYPE,fbd.HEAD_ID
        FROM   FIX_BOM_DETAIL fbd,FIX_BOM_HEAD fbh
        WHERE fbh.ENABLED_FLAG = 'Y' and fbd.ENABLED_FLAG = 'Y' AND fbd.HEAD_ID = fbh.ID
        AND fbh.ID = #{headId,jdbcType=VARCHAR}
    </select>

    <select id="selectIdByFixBomId" resultType="com.zte.interfaces.dto.mbom.FixBomDetailDTO">
        SELECT
        fbd.id,
        fbd.ITEM_LEVEL,fbd.ITEM_SEQ,fbd.ITEM_TYPE,fbd.ZTE_CODE,fbd.CUSTOMER_COMPONENT_TYPE,fbd.ITEM_NO,
        fbd.ITEM_SUPPLIER_NO,fbd.ITEM_NAME,
        fbd.ITEM_NUMBER,fbd.ITEM_GROUP,fbd.DELETE_FLAG,
        fbd.REQUIRE_MATERIAL_UPLOAD,fbd.IS_PRICED_MATERIAL,fbd.UPLOAD_BY_SN,
        fbd.FIX_BOM_REQUIRED,fbd.BOX_BOM_REQUIRED,fbd.BOX_PRIORITY,FBD.ZTE_CODE_NAME,
        fbd.ITEM_MATERIAL_TYPE,fbd.HEAD_ID
        FROM   FIX_BOM_DETAIL fbd,FIX_BOM_HEAD fbh
        WHERE fbh.ENABLED_FLAG = 'Y' and fbd.ENABLED_FLAG = 'Y' AND fbd.FIX_BOM_ID = fbh.FIX_BOM_ID
        AND fbh.FIX_BOM_ID = #{fixBomId,jdbcType=VARCHAR}
    </select>


    <select id="getFixBomByTaskNo" resultType="com.zte.interfaces.dto.mbom.FixBomDetailDTO">
        select fbd.ZTE_CODE, fbd.item_number, case when fbd.item_number is null or fbd.item_number = '' then '0' else fbd.item_number::numeric end as item_qty,
        fbd.require_material_upload, fbd.upload_by_sn, fix_bom_required,
        fbd.item_type,BOX_bom_required,item_supplier_no,FBD.customer_component_type
        from fix_bom_detail fbd,
        ps_task_extended pte
        where fbd.fix_bom_id = pte.fix_bom_id
        and fbd.enabled_flag = 'Y'
        and pte.enabled_flag = 'Y'
        and fbd.delete_flag = 'N'
        and pte.task_no = #{taskNo}
        and pte.fix_bom_id is not null
        and pte.fix_bom_id != ''
        and fbd.ZTE_CODE is not null
        and fbd.ZTE_CODE != ''
    </select>

    <select id="getFixBomDetailByTaskNo" resultType="com.zte.interfaces.dto.mbom.FixBomDetailDTO">
        select fbd.id, fbd.fix_bom_id ,fbd.zte_code, fbd.item_number, fbd.customer_component_type, fbd.box_bom_required
        fbd.box_bom_required, fbd.item_type, fbd.item_supplier_no, fbd.item_level, fbd.zte_code_name, fbd.item_material_type
        from fix_bom_detail fbd,
        ps_task_extended pte
        where fbd.fix_bom_id = pte.fix_bom_id
        and fbd.enabled_flag = 'Y'
        and pte.enabled_flag = 'Y'
        and fbd.delete_flag = 'N'
        and pte.task_no = #{taskNo}
    </select>

</mapper>