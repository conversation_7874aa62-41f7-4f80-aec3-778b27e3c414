<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PackingListConfigRepository">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.PackingListConfigDTO" id="packingListConfigMap">
        <result property="id" column="id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="customerComponentType" column="customer_component_type"/>
        <result property="requireMaterialUpload" column="require_material_upload"/>
        <result property="isPricedMaterial" column="is_priced_material"/>
        <result property="uploadBySn" column="upload_by_sn"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="priority" column="priority"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        customer_code,
        customer_component_type,
        require_material_upload,
        is_priced_material,
        upload_by_sn,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date,
        enabled_flag,
        priority
    </sql>

    <select id="selectPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="packingListConfigMap">
        select <include refid="Base_Column_List"/>
        from packing_list_config
        <where>
            enabled_flag = 'Y'
            <if test="params.customerCode != null and params.customerCode != ''">
                and customer_code = #{params.customerCode, jdbcType=VARCHAR}
            </if>
            <if test="params.customerComponentType != null and params.customerComponentType != ''">
                and customer_component_type = #{params.customerComponentType, jdbcType=VARCHAR}
            </if>
            <if test="params.requireMaterialUpload != null and params.requireMaterialUpload != ''">
                and require_material_upload = #{params.requireMaterialUpload, jdbcType=CHAR}
            </if>
            <if test="params.isPricedMaterial != null and params.isPricedMaterial != ''">
                and is_priced_material = #{params.isPricedMaterial, jdbcType=CHAR}
            </if>
            <if test="params.uploadBySn != null and params.uploadBySn != ''">
                and upload_by_sn = #{params.uploadBySn, jdbcType=CHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority, jdbcType=INTEGER}
            </if>
            <if test="params.startCreateDate != null and params.endCreateDate != null">
                and create_date &gt;= #{params.startCreateDate, jdbcType=TIMESTAMP}
                and create_date &lt;= #{params.endCreateDate, jdbcType=TIMESTAMP}
            </if>
            <if test="params.startUpdateDate != null and params.endUpdateDate != null">
                and last_updated_date &gt;= #{params.startUpdateDate, jdbcType=TIMESTAMP}
                and last_updated_date &lt;= #{params.endUpdateDate, jdbcType=TIMESTAMP}
            </if>
        </where>
        order by last_updated_date desc
    </select>

    <insert id="save" parameterType="com.zte.interfaces.dto.PackingListConfigDTO">
        INSERT INTO packing_list_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="customerCode != null and customerCode != ''">
                customer_code,
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                customer_component_type,
            </if>
            <if test="requireMaterialUpload != null and requireMaterialUpload != ''">
                require_material_upload,
            </if>
            <if test="isPricedMaterial != null and isPricedMaterial != ''">
                is_priced_material,
            </if>
            <if test="uploadBySn != null and uploadBySn != ''">
                upload_by_sn,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                last_updated_by,
            </if>
            <if test="lastUpdatedDate != null">
                last_updated_date,
            </if>
            <if test="enabledFlag != null and enabledFlag != ''">
                enabled_flag,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id, jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null and customerCode != ''">
                #{customerCode, jdbcType=VARCHAR},
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                #{customerComponentType, jdbcType=VARCHAR},
            </if>
            <if test="requireMaterialUpload != null and requireMaterialUpload != ''">
                #{requireMaterialUpload, jdbcType=CHAR},
            </if>
            <if test="isPricedMaterial != null and isPricedMaterial != ''">
                #{isPricedMaterial, jdbcType=CHAR},
            </if>
            <if test="uploadBySn != null and uploadBySn != ''">
                #{uploadBySn, jdbcType=CHAR},
            </if>
            <if test="priority != null">
                 #{priority, jdbcType=INTEGER},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate, jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                #{lastUpdatedBy, jdbcType=VARCHAR},
            </if>
            <if test="lastUpdatedDate != null">
                #{lastUpdatedDate, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.zte.interfaces.dto.PackingListConfigDTO">
        UPDATE packing_list_config
        <set>
            <if test="customerCode != null and customerCode != ''">
                customer_code = #{customerCode, jdbcType=VARCHAR},
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                customer_component_type = #{customerComponentType, jdbcType=VARCHAR},
            </if>
            <if test="requireMaterialUpload != null and requireMaterialUpload != ''">
                require_material_upload = #{requireMaterialUpload, jdbcType=CHAR},
            </if>
            <if test="isPricedMaterial != null and isPricedMaterial != ''">
                is_priced_material = #{isPricedMaterial, jdbcType=CHAR},
            </if>
            <if test="uploadBySn != null and uploadBySn != ''">
                upload_by_sn = #{uploadBySn, jdbcType=CHAR},
            </if>
            <if test="priority != null">
                priority = #{priority, jdbcType=INTEGER},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
            </if>
            last_updated_date = LOCALTIMESTAMP,
        </set>
        WHERE id = #{id, jdbcType=VARCHAR}
    </update>

    <delete id="deleteById" parameterType="java.lang.String">
        delete from packing_list_config
        WHERE id = #{id, jdbcType=VARCHAR}
    </delete>

    <select id="countExistPackListInfo" parameterType="com.zte.interfaces.dto.PackingListConfigDTO" resultMap="packingListConfigMap">
        SELECT <include refid="Base_Column_List"/>
        FROM packing_list_config
        <where>
            enabled_flag = 'Y'
            <if test="id != null and id != ''">
                AND id != #{id, jdbcType=VARCHAR}
            </if>
            <if test="customerCode != null and customerCode != ''">
                AND customer_code = #{customerCode, jdbcType=VARCHAR}
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                AND customer_component_type = #{customerComponentType, jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryPackListByCustomer" resultType="com.zte.interfaces.dto.PackingListConfigDTO">
        SELECT
            customer_code,
            customer_component_type,
            require_material_upload,
            is_priced_material,
            upload_by_sn,
            priority
        FROM packing_list_config
        where ENABLED_FLAG = 'Y'
        AND CUSTOMER_CODE = #{customerCode, jdbcType=VARCHAR}
        <if test="customerComponentTypeList != null and customerComponentTypeList.size()>0">
            AND customer_component_type IN
            <foreach collection="customerComponentTypeList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
