<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.part.SparePartHeadRepository">

    <resultMap id="headAndDetail" type="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="EMAIL_CC" property="emailCc" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="TRANSFER_FACTORY_ID" property="transferFactoryId" jdbcType="VARCHAR"/>
        <result column="RECEIVING_FACTORY_ID" property="receivingFactoryId" jdbcType="VARCHAR"/>
        <result column="PART_TYPE" property="partType" jdbcType="VARCHAR"/>
		<result column="PART_CODE" property="partCode" jdbcType="VARCHAR"/>
		<result column="RECEIVE_LOCATION" property="receiveLocation" jdbcType="VARCHAR"/>
		<result column="RECEIVE_DATE" property="receiveDate" jdbcType="TIMESTAMP"/>
		<result column="ITEM_DETAIL" property="itemDetail" jdbcType="VARCHAR"/>
		<result column="ITEM_NO" property="itemNo" jdbcType="VARCHAR"/>
        <result column="TRANSFER_REASON" property="transferReason" jdbcType="VARCHAR"/>
        <collection property="detailList" ofType="com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO">
            <result column="detail_id" property="detailId"/>
            <result column="quantity" property="quantity"/>
            <result column="item_quantity" property="itemQuantity"/>
            <result column="part_name" property="partName"/>
        </collection>
        <collection property="approveList" ofType="com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO">
            <result column="id" property="id"/>
            <result column="node_Code" property="nodeCode"/>
            <result column="node_Name" property="nodeName"/>
            <result column="APPROVER_ID" property="approverId"/>
            <result column="APPROVER_NAME" property="approverName"/>
            <result column="seq" property="seq"/>
        </collection>
    </resultMap>

	<resultMap id="headAndDetailMap" type="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
		<result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
		<result column="STATUS" property="status" jdbcType="VARCHAR"/>
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
		<result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="VARCHAR"/>
		<result column="LAST_UPDATED_DATE" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
		<result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
		<result column="EMAIL_CC" property="emailCc" jdbcType="VARCHAR"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="TRANSFER_FACTORY_ID" property="transferFactoryId" jdbcType="VARCHAR"/>
		<result column="RECEIVING_FACTORY_ID" property="receivingFactoryId" jdbcType="VARCHAR"/>
		<result column="PART_TYPE" property="partType" jdbcType="VARCHAR"/>
		<result column="PART_CODE" property="partCode" jdbcType="VARCHAR"/>
		<result column="RECEIVE_LOCATION" property="receiveLocation" jdbcType="VARCHAR"/>
		<result column="RECEIVE_DATE" property="receiveDate" jdbcType="TIMESTAMP"/>
		<result column="ITEM_DETAIL" property="itemDetail" jdbcType="VARCHAR"/>
		<result column="ITEM_NO" property="itemNo" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="part_name" property="partName"/>
        <result column="TRANSFER_REASON" property="transferReason" jdbcType="VARCHAR"/>
	</resultMap>

    <insert id="insertHead" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
        INSERT INTO CF_SPARE_PART_ALLOCATION_HEAD
        (
        BILL_NO,STATUS,CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,
        LAST_UPDATED_DATE,ENABLED_FLAG,EMAIL_CC,TRANSFER_FACTORY_ID,RECEIVING_FACTORY_ID,REMARK,PART_TYPE,
        TRANSFER_REASON
        )values(
        #{billNo,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{createBy,jdbcType=VARCHAR},sysdate,
        #{lastUpdatedBy,jdbcType=VARCHAR},
        sysdate,'Y',#{emailCc,jdbcType=VARCHAR},#{transferFactoryId,jdbcType=VARCHAR},
        #{receivingFactoryId,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR},#{partType,jdbcType=VARCHAR},
        #{transferReason,jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectHeadByObject" resultType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
        SELECT BILL_NO,STATUS,PART_TYPE FROM CF_SPARE_PART_ALLOCATION_HEAD WHERE ENABLED_FLAG = 'Y'
        AND BILL_NO = #{billNo,jdbcType=VARCHAR}
    </select>


    <select id="querySpareHeadAndDetails" resultMap="headAndDetail">
        select h.BILL_NO,h.STATUS,h.EMAIL_CC,h.TRANSFER_FACTORY_ID,h.RECEIVING_FACTORY_ID,h.PART_TYPE,H.REMARK,
        H.LAST_UPDATED_BY,h.transfer_reason,D.detail_id,D.quantity,D.part_name,d.create_date as de_create_date
        ,A.id,A.node_Code,A.node_Name,A.APPROVER_ID,A.APPROVER_NAME,A.seq
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL D ON H.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN APPROVAL_PROCESS_INFO A ON A.BILL_NO = H.BILL_NO AND A.ENABLED_FLAG='Y'
        where h.enabled_flag = 'Y'
        AND h.BILL_NO = #{billNo,jdbcType=VARCHAR}
        order by seq,APPROVER_ID,de_create_date asc
    </select>

    <update id="updateHead" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
        update CF_SPARE_PART_ALLOCATION_HEAD set status=#{status,jdbcType=VARCHAR},EMAIL_CC =
        #{emailCc,jdbcType=VARCHAR},
        TRANSFER_FACTORY_ID=#{transferFactoryId,jdbcType=VARCHAR},RECEIVING_FACTORY_ID =
        #{receivingFactoryId,jdbcType=VARCHAR}
        ,LAST_UPDATED_DATE=sysdate , last_updated_by=#{lastUpdatedBy,jdbcType=VARCHAR},
        transfer_reason=#{transferReason,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG = 'Y' AND BILL_NO=#{billNo,jdbcType=VARCHAR}
    </update>


    <update id="updateBillHeadStatus" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
        update CF_SPARE_PART_ALLOCATION_HEAD SET status=#{status,jdbcType=VARCHAR}
        ,LAST_UPDATED_DATE=sysdate
        ,REMARK=#{remark,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG = 'Y' AND BILL_NO=#{billNo,jdbcType=VARCHAR}
    </update>


    <select id="getSpareBillNoList" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO"
            resultType="java.lang.String">
        select distinct h.bill_no
        from cf_spare_part_allocation_head h
        where h.enabled_flag = 'Y'
        and h.status in ('4','5')
        and h.transfer_factory_id = #{transferFactoryId,jdbcType=VARCHAR}
    </select>

    <select id="getSpareOutBoundList" resultMap="headAndDetail">
        select h.BILL_NO, h.STATUS, h.transfer_factory_id,D.detail_id, h.receiving_factory_id, h.part_type,
        H.LAST_UPDATED_BY,h.transfer_reason, D.quantity, D.part_name, sum(nvl(id.quantity,0)) item_quantity
        from CF_SPARE_PART_ALLOCATION_HEAD h, CF_SPARE_PART_ALLOCATION_DETAIL D
        LEFT JOIN cf_spare_part_allocation_item id
            ON id.detail_id = D.detail_id
            AND id.ENABLED_FLAG='Y'
            and id.status != '9'
        where h.enabled_flag = 'Y'
            and H.BILL_NO = D.BILL_NO AND
            D.ENABLED_FLAG='Y'
            AND h.BILL_NO = #{billNo,jdbcType=VARCHAR}
        group by h.BILL_NO,h.part_type, h.transfer_factory_id,D.detail_id, h.receiving_factory_id
        order by h.CREATE_DATE
    </select>

	<!--带出调拨中或回退中状态的单据号-->
	<select id="getBillNos" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultMap="headAndDetail">
		select bill_no from cf_spare_part_allocation_head
		where ENABLED_FLAG = 'Y'
		<if test="(status == null or status == '') and (billNo == null or billNo == '')
            and (transferFactoryId == null or transferFactoryId == '') and (receivingFactoryId == null or receivingFactoryId == '')">AND 1=2</if>
		<if test="billNo != null and billNo != ''">AND bill_no = #{billNo}</if>
		<if test="status != null and status != ''"> AND STATUS = #{status}</if>
		<if test="transferFactoryId != null and transferFactoryId != ''">AND TRANSFER_FACTORY_ID = #{transferFactoryId}</if>
		<if test="receivingFactoryId != null and receivingFactoryId != ''"> AND RECEIVING_FACTORY_ID = #{receivingFactoryId}</if>
		limit 100
	</select>

	<!--根据单据号查找信息-->
	<select id="getInfoByBillNo" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultMap="headAndDetailMap">
		select h.transfer_factory_id, h.receiving_factory_id,h.part_type,i.part_code,i.part_name,i.status,i.receive_location,i.receive_date,i.item_detail,i.item_no
		from cf_spare_part_allocation_head h
		left join cf_spare_part_allocation_detail d on h.bill_no = d.bill_no
		left join cf_spare_part_allocation_item i on d.detail_id = i.detail_id
		where h.enabled_flag = 'Y' and d.enabled_flag = 'Y' and i.enabled_flag = 'Y'
		and h.bill_no = #{billNo}
		<if test="status != null and status != ''"> AND i.status = #{status}</if>
		<if test="partCode != null and partCode != ''"> AND i.part_code = #{partCode}</if>
        <if test="sourceSys != null and sourceSys == 'pda'">
            order by i.last_updated_date desc limit 20
        </if>
	</select>

	<!--查找单据编码在单据拣料明细中是否存在-->
	<select id="checkItemExist" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultType="java.lang.Integer">
		select count(1)
		from cf_spare_part_allocation_item
		where enabled_flag = 'Y'
		from cf_spare_part_allocation_head h
		left join cf_spare_part_allocation_detail d on h.bill_no = d.bill_no
		left join cf_spare_part_allocation_item i on d.detail_id = i.detail_id
		where h.enabled_flag = 'Y' and d.enabled_flag = 'Y' and i.enabled_flag = 'Y'
		and h.bill_no = #{billNo}
		and i.status = #{status}
		and i.part_code = #{partCode}
	</select>

	<select id="checkDetailId" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultType="java.lang.String">
		select i.detail_id
		from cf_spare_part_allocation_head h
		left join cf_spare_part_allocation_detail d on h.bill_no = d.bill_no
		left join cf_spare_part_allocation_item i on d.detail_id = i.detail_id
		where h.enabled_flag = 'Y' and d.enabled_flag = 'Y' and i.enabled_flag = 'Y'
		and h.bill_no = #{billNo}
		and i.part_code = #{partCode}
	</select>

	<!--更新调拨明细状态-->
	<update id="updateDetailStatus" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
		update cf_spare_part_allocation_item
		set status = #{status,jdbcType=VARCHAR}, last_updated_date=sysdate ,last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        receive_location = #{locationCode,jdbcType=VARCHAR},receive_by = #{lastUpdatedBy,jdbcType=VARCHAR},receive_date=sysdate
		where enabled_flag = 'Y'
		and part_code = #{partCode}
		and detail_id = #{detailId}
	</update>

	<!--关闭调拨单-->
	<update id="updateHeadStatus" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO">
		update CF_SPARE_PART_ALLOCATION_HEAD
		SET status=#{status,jdbcType=VARCHAR},LAST_UPDATED_DATE=sysdate ,last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR}
		WHERE ENABLED_FLAG = 'Y' AND BILL_NO=#{billNo,jdbcType=VARCHAR}
	</update>

	<!--查指定单据辅料调拨数量-->
	<select id="getCountByBillNo" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultMap="headAndDetailMap">
		select d.quantity,d.part_name
		from cf_spare_part_allocation_detail d
		where d.enabled_flag = 'Y' and d.bill_no = #{billNo}
	</select>

	<!--查指定单据辅料已接收/已回退数量-->
	<select id="getCountByBillNoAndStatus" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO" resultType="java.lang.Integer">
		select sum(i.quantity)
		from cf_spare_part_allocation_head h
		left join cf_spare_part_allocation_detail d on h.bill_no = d.bill_no
		left join CF_SPARE_PART_ALLOCATION_ITEM i ON d.DETAIL_ID = i.DETAIL_ID
		where h.enabled_flag = 'Y' and d.enabled_flag = 'Y' and i.enabled_flag = 'Y'
		and h.bill_no = #{billNo} and d.part_name = #{partName} and i.status = #{status}
		group by h.bill_no,d.part_name
	</select>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO">
        select h.BILL_NO,h.STATUS AS BILL_STATUS,h.EMAIL_CC,h.TRANSFER_FACTORY_ID,h.RECEIVING_FACTORY_ID,h.PART_TYPE,h.CREATE_BY,h.LAST_UPDATED_BY,
        h.REMARK,h.CREATE_DATE,h.LAST_UPDATED_DATE,h.transfer_reason
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON h.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN cf_spare_part_allocation_item i ON d.DETAIL_ID = i.DETAIL_ID
        where h.enabled_flag = 'Y'
        <if test="params.billNo != null and params.billNo != ''">and h.BILL_NO = #{params.billNo}</if>
        <if test="params.partType != null and params.partType != ''">and h.PART_TYPE = #{params.partType}</if>
        <if test="params.partName != null and params.partName != ''">and d.PART_NAME = #{params.partName}</if>
        <if test="params.partCode != null and params.partCode != ''">and i.PART_CODE = #{params.partCode}</if>
        <if test="params.billStatus != null and params.billStatus != ''">and h.STATUS = #{params.billStatus}</if>
        <if test="params.transferFactoryId != null and params.transferFactoryId != ''">and h.TRANSFER_FACTORY_ID = #{params.transferFactoryId}</if>
        <if test="params.receivingFactoryId != null and params.receivingFactoryId != ''">and h.RECEIVING_FACTORY_ID = #{params.receivingFactoryId}</if>
        <if test="params.receiveBy != null and params.receiveBy != ''">and i.RECEIVE_BY = #{params.receiveBy}</if>
        <if test="params.transferBy != null and params.transferBy != ''">and i.TRANSFER_BY = #{params.transferBy}</if>
        <if test="params.transferStartDate != null and params.transferStartDate != '' and params.transferEndDate != null and params.transferEndDate != ''">
            and i.TRANSFER_DATE between to_timestamp(#{params.transferStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.transferEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.receiveStartDate != null and params.receiveStartDate != '' and params.receiveEndDate != null and params.receiveEndDate != ''">
            and i.RECEIVE_DATE between to_timestamp(#{params.receiveStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.receiveEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.createStartDate != null and params.createStartDate != '' and params.createEndDate != null and params.createEndDate != ''">
            and h.CREATE_DATE between to_timestamp(#{params.createStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.createEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.lastUpdatedStartDate != null and params.lastUpdatedStartDate != '' and params.lastUpdatedEndDate != null and params.lastUpdatedEndDate != ''">
            and h.LAST_UPDATED_DATE between to_timestamp(#{params.lastUpdatedStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.lastUpdatedEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="(params.billNo == null or params.billNo == '') and (params.partType == null or params.partType == '') and (params.partName == null or params.partName == '')
            and (params.partCode == null or params.partCode == '') and (params.billStatus == null or params.billStatus == '') and (params.transferFactoryId == null or params.transferFactoryId == '')
            and (params.receivingFactoryId == null or params.receivingFactoryId == '') and (params.receiveBy == null or params.receiveBy == '') and (params.transferBy == null or params.transferBy == '')
            and (params.transferStartDate == null or params.transferStartDate == '' or params.transferEndDate == null or params.transferEndDate == '')
            and (params.receiveStartDate == null or params.receiveStartDate == '' or params.receiveEndDate == null or params.receiveEndDate == '')
            and (params.createStartDate == null or params.createStartDate == '' or params.createEndDate == null or params.createEndDate == '')
            and (params.lastUpdatedStartDate == null or params.lastUpdatedStartDate == '' or params.lastUpdatedEndDate == null or params.lastUpdatedEndDate == '')">
            and 1=2
        </if>
        group by h.bill_no
        order by h.LAST_UPDATED_DATE desc
    </select>

    <select id="countSparePartAllocationQuery" parameterType="com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO" resultType="java.lang.Integer">
        select count(1)
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON h.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN cf_spare_part_allocation_item i ON d.DETAIL_ID = i.DETAIL_ID
        where h.enabled_flag = 'Y'
        <if test="billNo != null and billNo != ''">and h.BILL_NO = #{billNo}</if>
        <if test="partType != null and partType != ''">and h.PART_TYPE = #{partType}</if>
        <if test="partName != null and partName != ''">and d.PART_NAME = #{partName}</if>
        <if test="partCode != null and partCode != ''">and i.PART_CODE = #{partCode}</if>
        <if test="billStatus != null and billStatus != ''">and h.STATUS = #{billStatus}</if>
        <if test="transferFactoryId != null and transferFactoryId != ''">and h.TRANSFER_FACTORY_ID = #{transferFactoryId}</if>
        <if test="receivingFactoryId != null and receivingFactoryId != ''">and h.RECEIVING_FACTORY_ID = #{receivingFactoryId}</if>
        <if test="receiveBy != null and receiveBy != ''">and i.RECEIVE_BY = #{receiveBy}</if>
        <if test="transferBy != null and transferBy != ''">and i.TRANSFER_BY = #{transferBy}</if>
        <if test="transferStartDate != null and transferStartDate != '' and transferEndDate != null and transferEndDate != ''">
            and i.TRANSFER_DATE between to_timestamp(#{transferStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{transferEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="receiveStartDate != null and receiveStartDate != '' and receiveEndDate != null and receiveEndDate != ''">
            and i.RECEIVE_DATE between to_timestamp(#{receiveStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{receiveEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="createStartDate != null and createStartDate != '' and createEndDate != null and createEndDate != ''">
            and h.CREATE_DATE between to_timestamp(#{createStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{createEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdatedStartDate != null and lastUpdatedStartDate != '' and lastUpdatedEndDate != null and lastUpdatedEndDate != ''">
            and h.LAST_UPDATED_DATE between to_timestamp(#{lastUpdatedStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{lastUpdatedEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by h.LAST_UPDATED_DATE desc
    </select>

    <select id="queryExprotSpareInfo" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO">
        select h.BILL_NO,h.STATUS AS BILL_STATUS,h.EMAIL_CC,h.TRANSFER_FACTORY_ID,h.RECEIVING_FACTORY_ID,h.PART_TYPE,h.CREATE_BY,h.LAST_UPDATED_BY,
        h.REMARK,h.CREATE_DATE,h.LAST_UPDATED_DATE,h.transfer_reason,
        d.DETAIL_ID,d.PART_NAME,d.QUANTITY,
        i.PART_CODE,i.RECEIVE_BY,i.TRANSFER_BY,i.TRANSFER_DATE,i.RECEIVE_DATE,i.ITEM_NO,i.TRANSFER_LOCATION,i.RECEIVE_LOCATION,i.STATUS AS ALLOCATION_STATUS
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON h.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN cf_spare_part_allocation_item i ON d.DETAIL_ID = i.DETAIL_ID
        where h.enabled_flag = 'Y'
        <if test="params.billNo != null and params.billNo != ''">and h.BILL_NO = #{params.billNo}</if>
        <if test="params.partType != null and params.partType != ''">and h.PART_TYPE = #{params.partType}</if>
        <if test="params.partName != null and params.partName != ''">and d.PART_NAME = #{params.partName}</if>
        <if test="params.partCode != null and params.partCode != ''">and i.PART_CODE = #{params.partCode}</if>
        <if test="params.billStatus != null and params.billStatus != ''">and h.STATUS = #{params.billStatus}</if>
        <if test="params.transferFactoryId != null and params.transferFactoryId != ''">and h.TRANSFER_FACTORY_ID = #{params.transferFactoryId}</if>
        <if test="params.receivingFactoryId != null and params.receivingFactoryId != ''">and h.RECEIVING_FACTORY_ID = #{params.receivingFactoryId}</if>
        <if test="params.receiveBy != null and params.receiveBy != ''">and i.RECEIVE_BY = #{params.receiveBy}</if>
        <if test="params.transferBy != null and params.transferBy != ''">and i.TRANSFER_BY = #{params.transferBy}</if>
        <if test="params.transferStartDate != null and params.transferStartDate != '' and params.transferEndDate != null and params.transferEndDate != ''">
            and i.TRANSFER_DATE between to_timestamp(#{params.transferStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.transferEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.receiveStartDate != null and params.receiveStartDate != '' and params.receiveEndDate != null and params.receiveEndDate != ''">
            and i.RECEIVE_DATE between to_timestamp(#{params.receiveStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.receiveEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.createStartDate != null and params.createStartDate != '' and params.createEndDate != null and params.createEndDate != ''">
            and h.CREATE_DATE between to_timestamp(#{params.createStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.createEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="params.lastUpdatedStartDate != null and params.lastUpdatedStartDate != '' and params.lastUpdatedEndDate != null and params.lastUpdatedEndDate != ''">
            and h.LAST_UPDATED_DATE between to_timestamp(#{params.lastUpdatedStartDate},'yyyy-mm-dd hh24:mi:ss') and to_timestamp(#{params.lastUpdatedEndDate},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="(params.billNo == null or params.billNo == '') and (params.partType == null or params.partType == '') and (params.partName == null or params.partName == '')
            and (params.partCode == null or params.partCode == '') and (params.billStatus == null or params.billStatus == '') and (params.transferFactoryId == null or params.transferFactoryId == '')
            and (params.receivingFactoryId == null or params.receivingFactoryId == '') and (params.receiveBy == null or params.receiveBy == '') and (params.transferBy == null or params.transferBy == '')
            and (params.transferStartDate == null or params.transferStartDate == '' or params.transferEndDate == null or params.transferEndDate == '')
            and (params.receiveStartDate == null or params.receiveStartDate == '' or params.receiveEndDate == null or params.receiveEndDate == '')
            and (params.createStartDate == null or params.createStartDate == '' or params.createEndDate == null or params.createEndDate == '')
            and (params.lastUpdatedStartDate == null or params.lastUpdatedStartDate == '' or params.lastUpdatedEndDate == null or params.lastUpdatedEndDate == '')">
            and 1=2
        </if>
        order by h.LAST_UPDATED_DATE desc
    </select>


    <select id="queryItemDetailByBillNo" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO">
        select i.detail_id,transfer_by,part_type,i.part_name,status as allocation_status,part_code,
        item_no,transfer_location,transfer_date,receive_location,receive_by,receive_date,item_detail_id
        from cf_spare_part_allocation_item i
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON i.detail_id = d.detail_id AND d.ENABLED_FLAG='Y'
        WHERE i.enabled_flag = 'Y'
        <if test="params.billNo != null and params.billNo != ''">and d.BILL_NO = #{params.billNo,jdbcType=VARCHAR}</if>
        <if test="params.detailId != null and params.detailId != ''">and i.detail_id = #{params.detailId,jdbcType=VARCHAR}</if>
        <if test="(params.billNo == null or params.billNo == '') and (params.detailId == null or params.detailId == '')">
            and 1=2
        </if>
        order by i.LAST_UPDATED_DATE desc
    </select>

    <update id="closeSparePartAllocation" parameterType="java.lang.String">
        update CF_SPARE_PART_ALLOCATION_HEAD d
        set d.status= '10',LAST_UPDATED_DATE=sysdate, LAST_UPDATED_BY=#{empNo,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG = 'Y' AND BILL_NO=#{billNo,jdbcType=VARCHAR} AND STATUS = '5'
    </update>

    <update id="deleteSparePartAllocation" parameterType="java.lang.String">
        update CF_SPARE_PART_ALLOCATION_HEAD d
        set enabled_flag = 'N',LAST_UPDATED_DATE=sysdate, LAST_UPDATED_BY=#{empNo,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG = 'Y' AND BILL_NO=#{billNo,jdbcType=VARCHAR} AND STATUS IN ('2','7')
    </update>

    <update id="rollbackSparePartAllocation" parameterType="java.lang.String">
        update cf_spare_part_allocation_item
        set status = '11',LAST_UPDATED_DATE=sysdate, LAST_UPDATED_BY=#{empNo,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG = 'Y' AND part_code=#{partCode,jdbcType=VARCHAR} AND STATUS = '5'
    </update>

    <select id="checkAllocationItemDetailClose" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON h.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN cf_spare_part_allocation_item i ON d.DETAIL_ID = i.DETAIL_ID
        where h.enabled_flag = 'Y'
        and h.BILL_NO = #{billNo,jdbcType=VARCHAR}
        and i.STATUS NOT IN ('8','9')
        order by h.LAST_UPDATED_DATE desc
    </select>

    <select id="querySpareInfoByPartCode" resultType="com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO">
        select h.BILL_NO,h.STATUS AS BILL_STATUS,h.EMAIL_CC,h.TRANSFER_FACTORY_ID,h.RECEIVING_FACTORY_ID,h.PART_TYPE,h.CREATE_BY,h.LAST_UPDATED_BY,
        h.REMARK,h.CREATE_DATE,h.LAST_UPDATED_DATE,h.transfer_reason,
        d.DETAIL_ID,d.PART_NAME,d.QUANTITY,
        i.PART_CODE,i.RECEIVE_BY,i.TRANSFER_BY,i.TRANSFER_DATE,i.RECEIVE_DATE,i.ITEM_NO,i.TRANSFER_LOCATION,i.RECEIVE_LOCATION,i.STATUS AS ALLOCATION_STATUS
        from CF_SPARE_PART_ALLOCATION_HEAD h
        LEFT JOIN CF_SPARE_PART_ALLOCATION_DETAIL d ON h.BILL_NO = D.BILL_NO AND D.ENABLED_FLAG='Y'
        LEFT JOIN cf_spare_part_allocation_item i ON d.DETAIL_ID = i.DETAIL_ID
        where h.enabled_flag = 'Y' AND i.PART_CODE = #{partCode,jdbcType=VARCHAR}
    </select>
</mapper>