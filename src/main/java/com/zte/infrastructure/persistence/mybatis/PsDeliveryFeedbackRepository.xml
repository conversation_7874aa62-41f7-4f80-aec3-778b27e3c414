<?xml version="1.0" encoding="UTF-8" ?>
<!-- Started by AICoder, pid:h446f8d852w995514e0109d3029dbe59c594923c -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.PsDeliveryFeedbackRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.PsDeliveryFeedbackDO">
        <id property="id" column="id"/>
        <result property="orderType" column="order_type"/>
        <result property="orderNo" column="order_no"/>
        <result property="category" column="category"/>
        <result property="quantity" column="quantity"/>
        <result property="dateEstimatedCompletion" column="date_estimated_completion"/>
        <result property="dateExpectedCompletion" column="date_expected_completion"/>
        <result property="dateMaterialEstimatedPrepared" column="date_material_estimated_prepared"/>
        <result property="dateAllMaterialEstimatedPrepared" column="date_all_material_estimated_prepared"/>
        <result property="dateScheduledProduction" column="date_scheduled_production"/>
        <result property="timeMaterialPrepared" column="time_material_prepared"/>
        <result property="dateAllMaterialPrepared" column="date_all_material_prepared"/>
        <result property="timeProduction" column="time_production"/>
        <result property="liability" column="liability"/>
        <result property="abnormalCategoryFirst" column="abnormal_category_first"/>
        <result property="abnormalCategorySecond" column="abnormal_category_second"/>
        <result property="abnormalCategoryThird" column="abnormal_category_third"/>
        <result property="remark" column="remark"/>
        <result property="abnormalNo" column="abnormal_no"/>
        <result property="operateType" column="operate_type"/>
        <result property="pushStatus" column="push_status"/>
        <result property="pushErrorMsg" column="push_error_msg"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="scheduleFlag" column="schedule_flag"/>
        <result property="notes" column="notes"/>
        <result property="firstPickingDate" column="first_picking_date"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, order_type,business_scene, order_no, category, quantity, date_estimated_completion, date_expected_completion,
        date_material_estimated_prepared, date_all_material_estimated_prepared, date_scheduled_production,
        time_material_prepared, date_all_material_prepared, time_production, first_picking_date, liability, abnormal_category_first,
        abnormal_category_second, abnormal_category_third, remark, abnormal_no, operate_type, push_status,
        push_error_msg, create_by, create_date, last_updated_by, last_updated_date, enabled_flag, schedule_flag, notes
    </sql>

    <select id="listByPage" resultType="com.zte.interfaces.VO.PsDeliveryFeedbackVO" parameterType="com.zte.springbootframe.common.model.Page">
        select
        <include refid="Base_Column_List"/>
        from ps_delivery_feedback t
        where enabled_flag= 'Y'
        <if test="params.taskNos != null and params.taskNos.size() > 0">
            and order_no in
            <foreach collection="params.taskNos" index="index" open="(" separator="," close=")">
                #{params.taskNos[${index}]}
            </foreach>
        </if>
        <if test="params != null and params.businessScene != null and params.businessScene != ''">
            and business_scene = #{params.businessScene}
        </if>
        <if test="params != null and params.createBy != null and params.createBy != ''">
            and create_by = #{params.createBy}
        </if>
        <if test="params != null and params.createStartDate != null">
            and create_date &gt;= #{params.createStartDate}
        </if>
        <if test="params != null and params.createEndDate != null">
            and create_date &lt;= #{params.createEndDate}
        </if>
        order by last_updated_date desc
    </select>

    <select id="listByTask" resultType="com.zte.interfaces.VO.PsDeliveryFeedbackVO">
        select
        <include refid="Base_Column_List" />
        from ps_delivery_feedback
        <where>
            enabled_flag='Y'
            <if test="list != null and list.size() > 0">
                and order_no in
                <foreach collection="list" open="(" close=")" item="taskNo" separator=",">
                    #{taskNo}
                </foreach>
            </if>
        </where>
    </select>
    <select id="countSparePartAllocationQuery" resultType="java.lang.Integer">
        select
        count(1)
        from ps_delivery_feedback t
        where enabled_flag= 'Y'
        <if test="taskNos != null and taskNos.size() > 0">
            and order_no in
            <foreach collection="taskNos" index="index" open="(" separator="," close=")">
                #{taskNos[${index}]}
            </foreach>
        </if>
        <if test="businessScene != null and businessScene != ''">
            and business_scene = #{businessScene}
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}
        </if>
        <if test="createStartDate != null">
            and create_date &gt;= #{createStartDate}
        </if>
        <if test="createEndDate != null">
            and create_date &lt;= #{createEndDate}
        </if>
        order by last_updated_date desc
    </select>
    <select id="listTaskNosOfScheduleExclude" resultType="java.lang.String">
        select order_no from ps_delivery_feedback
        where enabled_flag ='Y' and (operate_type = '1' or schedule_flag = 'Y')
        and order_no in
        <foreach collection="list" open="(" close=")" item="taskNo" separator=",">
            #{taskNo}
        </foreach>
    </select>
    <select id="listByAbnormalNo" resultType="java.lang.String">
        select abnormal_no
        from ps_delivery_feedback
        where abnormal_no = #{abnormalNo}
        <if test="id!=null and id!=''">
            and id != #{id}
        </if>
        <if test="orderNo!=null and orderNo!=''">
            and order_no != #{orderNo}
        </if>
    </select>

    <insert id="batchInsert">
        INSERT INTO ps_delivery_feedback (
        id, order_type, order_no, category, quantity, date_estimated_completion, date_expected_completion,
        date_material_estimated_prepared, date_all_material_estimated_prepared, date_scheduled_production,
        time_material_prepared, date_all_material_prepared, time_production, first_picking_date, liability, abnormal_category_first,
        abnormal_category_second, abnormal_category_third, remark, abnormal_no, operate_type, push_status,
        push_error_msg, create_by, create_date, last_updated_by, last_updated_date, enabled_flag, schedule_flag, notes,business_scene
        ) VALUES
        <foreach collection="list" item="e" separator=",">
            (#{e.id}, #{e.orderType}, #{e.orderNo}, #{e.category}, #{e.quantity}, #{e.dateEstimatedCompletion}, #{e.dateExpectedCompletion},
            #{e.dateMaterialEstimatedPrepared}, #{e.dateAllMaterialEstimatedPrepared}, #{e.dateScheduledProduction},
            #{e.timeMaterialPrepared}, #{e.dateAllMaterialPrepared}, #{e.timeProduction}, #{e.firstPickingDate}, #{e.liability}, #{e.abnormalCategoryFirst},
            #{e.abnormalCategorySecond}, #{e.abnormalCategoryThird}, #{e.remark}, #{e.abnormalNo}, #{e.operateType}, #{e.pushStatus},
            #{e.pushErrorMsg}, #{e.createBy}, #{e.createDate}, #{e.lastUpdatedBy}, #{e.lastUpdatedDate}, #{e.enabledFlag}, #{e.scheduleFlag}, #{e.notes}, #{e.businessScene})
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.zte.domain.model.PsDeliveryFeedbackDO">
        update ps_delivery_feedback
        <set>
            <if test="orderType != null and orderType != ''">
                order_type = #{orderType},
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="dateEstimatedCompletion != null">
                date_estimated_completion = #{dateEstimatedCompletion},
            </if>
            <if test="dateExpectedCompletion != null">
                date_expected_completion = #{dateExpectedCompletion},
            </if>
            <if test="dateMaterialEstimatedPrepared != null">
                date_material_estimated_prepared = #{dateMaterialEstimatedPrepared},
            </if>
            <if test="dateAllMaterialEstimatedPrepared != null">
                date_all_material_estimated_prepared = #{dateAllMaterialEstimatedPrepared},
            </if>
            <if test="dateScheduledProduction != null">
                date_scheduled_production = #{dateScheduledProduction},
            </if>
            <if test="timeMaterialPrepared != null">
                time_material_prepared = #{timeMaterialPrepared},
            </if>
            <if test="dateAllMaterialPrepared != null">
                date_all_material_prepared = #{dateAllMaterialPrepared},
            </if>
            <if test="timeProduction != null">
                time_production = #{timeProduction},
            </if>
            <if test="liability != null and liability != ''">
                liability = #{liability},
            </if>
            <if test="abnormalCategoryFirst != null and abnormalCategoryFirst != ''">
                abnormal_category_first = #{abnormalCategoryFirst},
            </if>
            <if test="abnormalCategorySecond != null and abnormalCategorySecond != ''">
                abnormal_category_second = #{abnormalCategorySecond},
            </if>
            <if test="abnormalCategoryThird != null and abnormalCategoryThird != ''">
                abnormal_category_third = #{abnormalCategoryThird},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="abnormalNo != null and abnormalNo != ''">
                abnormal_no = #{abnormalNo},
            </if>
            <if test="operateType != null and operateType != ''">
                operate_type = #{operateType},
            </if>
            <if test="pushStatus != null and pushStatus != ''">
                push_status = #{pushStatus},
            </if>
            <if test="pushErrorMsg != null and pushErrorMsg != ''">
                push_error_msg = #{pushErrorMsg},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                last_updated_by = #{lastUpdatedBy},
            </if>
            <if test="lastUpdatedDate != null">
                last_updated_date = #{lastUpdatedDate},
            </if>
            <if test="enabledFlag != null and enabledFlag != ''">
                enabled_flag = #{enabledFlag},
            </if>
            <if test="scheduleFlag != null and scheduleFlag != ''">
                schedule_flag = #{scheduleFlag},
            </if>
            <if test="notes != null and notes != ''">
                notes = #{notes},
            </if>
            <if test="firstPickingDate != null">
                first_picking_date = #{firstPickingDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdateByPrimaryKeySelective">
        <foreach collection="list" item="e" open="" close="" separator=";">
            update ps_delivery_feedback
            <set>
                <if test="e.orderType != null and e.orderType != ''">
                    order_type = #{e.orderType},
                </if>
                <if test="e.orderNo != null and e.orderNo != ''">
                    order_no = #{e.orderNo},
                </if>
                <if test="e.category != null and e.category != ''">
                    category = #{e.category},
                </if>
                <if test="e.quantity != null">
                    quantity = #{e.quantity},
                </if>
                <if test="e.dateEstimatedCompletion != null">
                    date_estimated_completion = #{e.dateEstimatedCompletion},
                </if>
                <if test="e.dateExpectedCompletion != null">
                    date_expected_completion = #{e.dateExpectedCompletion},
                </if>
                <if test="e.dateMaterialEstimatedPrepared != null">
                    date_material_estimated_prepared = #{e.dateMaterialEstimatedPrepared},
                </if>
                <if test="e.dateAllMaterialEstimatedPrepared != null">
                    date_all_material_estimated_prepared = #{e.dateAllMaterialEstimatedPrepared},
                </if>
                <if test="e.dateScheduledProduction != null">
                    date_scheduled_production = #{e.dateScheduledProduction},
                </if>
                <if test="e.timeMaterialPrepared != null">
                    time_material_prepared = #{e.timeMaterialPrepared},
                </if>
                <if test="e.dateAllMaterialPrepared != null">
                    date_all_material_prepared = #{e.dateAllMaterialPrepared},
                </if>
                <if test="e.timeProduction != null">
                    time_production = #{e.timeProduction},
                </if>
                <if test="e.liability != null and e.liability != ''">
                    liability = #{e.liability},
                </if>
                <if test="e.abnormalCategoryFirst != null and e.abnormalCategoryFirst != ''">
                    abnormal_category_first = #{e.abnormalCategoryFirst},
                </if>
                <if test="e.abnormalCategorySecond != null and e.abnormalCategorySecond != ''">
                    abnormal_category_second = #{e.abnormalCategorySecond},
                </if>
                <if test="e.abnormalCategoryThird != null and e.abnormalCategoryThird != ''">
                    abnormal_category_third = #{e.abnormalCategoryThird},
                </if>
                <if test="e.remark != null and e.remark != ''">
                    remark = #{e.remark},
                </if>
                <if test="e.abnormalNo != null and e.abnormalNo != ''">
                    abnormal_no = #{e.abnormalNo},
                </if>
                <if test="e.operateType != null and e.operateType != ''">
                    operate_type = #{e.operateType},
                </if>
                <if test="e.pushStatus != null and e.pushStatus != ''">
                    push_status = #{e.pushStatus},
                </if>
                <if test="e.pushErrorMsg != null and e.pushErrorMsg != ''">
                    push_error_msg = #{e.pushErrorMsg},
                </if>
                <if test="e.createBy != null and e.createBy != ''">
                    create_by = #{e.createBy},
                </if>
                <if test="e.createDate != null">
                    create_date = #{e.createDate},
                </if>
                <if test="e.lastUpdatedBy != null and e.lastUpdatedBy != ''">
                    last_updated_by = #{e.lastUpdatedBy},
                </if>
                <if test="e.lastUpdatedDate != null">
                    last_updated_date = #{e.lastUpdatedDate},
                </if>
                <if test="e.enabledFlag != null and e.enabledFlag != ''">
                    enabled_flag = #{e.enabledFlag},
                </if>
                <if test="e.scheduleFlag != null and e.scheduleFlag != ''">
                    schedule_flag = #{e.scheduleFlag},
                </if>
                <if test="e.notes != null and e.notes != ''">
                    notes = #{e.notes},
                </if>
                <if test="e.firstPickingDate != null">
                    first_picking_date = #{e.firstPickingDate},
                </if>
            </set>
            where id = #{e.id}
        </foreach>
    </update>
    <update id="batchUpdateByPrimaryKey">
        <foreach collection="list" item="e" open="" close="" separator=";">
            update ps_delivery_feedback
            <set>
                <if test="e.orderType != null and e.orderType != ''">
                    order_type = #{e.orderType},
                </if>
                <if test="e.orderNo != null and e.orderNo != ''">
                    order_no = #{e.orderNo},
                </if>
                <if test="e.category != null and e.category != ''">
                    category = #{e.category},
                </if>
                <if test="e.quantity != null">
                    quantity = #{e.quantity},
                </if>
                date_estimated_completion = #{e.dateEstimatedCompletion},
                date_expected_completion = #{e.dateExpectedCompletion},
                date_material_estimated_prepared = #{e.dateMaterialEstimatedPrepared},
                date_all_material_estimated_prepared = #{e.dateAllMaterialEstimatedPrepared},
                date_scheduled_production = #{e.dateScheduledProduction},
                time_material_prepared = #{e.timeMaterialPrepared},
                date_all_material_prepared = #{e.dateAllMaterialPrepared},
                time_production = #{e.timeProduction},
                liability = #{e.liability},
                abnormal_category_first = #{e.abnormalCategoryFirst},
                abnormal_category_second = #{e.abnormalCategorySecond},
                abnormal_category_third = #{e.abnormalCategoryThird},
                remark = #{e.remark},
                abnormal_no = #{e.abnormalNo},
                <if test="e.operateType != null and e.operateType != ''">
                    operate_type = #{e.operateType},
                </if>
                <if test="e.pushStatus != null and e.pushStatus != ''">
                    push_status = #{e.pushStatus},
                </if>
                <if test="e.pushErrorMsg != null and e.pushErrorMsg != ''">
                    push_error_msg = #{e.pushErrorMsg},
                </if>
                <if test="e.createBy != null and e.createBy != ''">
                    create_by = #{e.createBy},
                </if>
                <if test="e.createDate != null">
                    create_date = #{e.createDate},
                </if>
                <if test="e.lastUpdatedBy != null and e.lastUpdatedBy != ''">
                    last_updated_by = #{e.lastUpdatedBy},
                </if>
                <if test="e.lastUpdatedDate != null">
                    last_updated_date = #{e.lastUpdatedDate},
                </if>
                <if test="e.enabledFlag != null and e.enabledFlag != ''">
                    enabled_flag = #{e.enabledFlag},
                </if>
                <if test="e.scheduleFlag != null and e.scheduleFlag != ''">
                    schedule_flag = #{e.scheduleFlag},
                </if>
                <if test="e.notes != null and e.notes != ''">
                    notes = #{e.notes},
                </if>
                <if test="e.firstPickingDate != null">
                    first_picking_date = #{e.firstPickingDate},
                </if>
            </set>
            where id = #{e.id}
        </foreach>
    </update>
</mapper>

        <!-- Ended by AICoder, pid:h446f8d852w995514e0109d3029dbe59c594923c -->