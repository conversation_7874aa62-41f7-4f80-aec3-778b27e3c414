<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushBoardDataProcessRepository">

    <select id="getExistSn" resultType="java.lang.String">
        select sn from push_board_data_process t
        where t.enabled_flag = 'Y'
        and t.sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.business_type = #{businessType}
    </select>

    <select id="getNeedPushSn" resultType="com.zte.interfaces.dto.PushBoardDataProcessDTO">
        select t.id, t.sn, t.business_type, t.push_status, t.push_fail_count, h.prodplan_id, h.task_no, h.item_no, h.sides, h.factory_id, h.customer_name
        from push_board_data_process t, push_board_data_detail d, push_board_data_head h
        where t.enabled_flag = 'Y' and d.enabled_flag = 'Y' and h.enabled_flag = 'Y'
        and t.sn = d.sn
        and d.prodplan_id = h.prodplan_id
        and t.sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.business_type = #{businessType}
        and t.push_status = '0'
    </select>

    <select id="getDataBySnAndBusinessType" resultType="com.zte.interfaces.dto.PushBoardDataProcessDTO">
        select t.sn, t.business_type, t.push_status
        from push_board_data_process t
        where t.enabled_flag = 'Y'
        and t.push_status = '2'
        and t.sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.business_type in
        <foreach collection="businessTypeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getNotCallBackList" resultType="com.zte.interfaces.dto.PushBoardDataProcessDTO">
        select t.id, t.sn, t.business_type, t.last_updated_date, t.push_status,nvl(t.push_number,1) push_Number
        from push_board_data_process t
        where t.enabled_flag = 'Y'
        and t.last_updated_date >= #{startTime}
        and t.push_status in ( '1', '9')
        <if test="businessTypeList !=null and businessTypeList.size()>0">
            and business_type in
            <foreach collection="businessTypeList" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
         </if>
        <if test="sn != null and sn != ''">
            and not exists ( select 1 from push_board_data_process a where t.sn = a.sn and a.sn &lt;= #{sn} and a.last_updated_date = #{startTime})
        </if>
        order by t.last_updated_date, t.sn
        limit #{limit}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into push_board_data_process
        (id,sn,business_type,push_status,push_date)
        values
        <foreach collection="list" item="item" index="index" separator=",">
             (#{item.id},#{item.sn},#{item.businessType},NVL(#{item.pushStatus},0)::numeric,#{item.pushDate})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update push_board_data_process t set
            t.push_status=#{item.pushStatus},
            <if test="item.pushDate != null">
                t.push_date = #{item.pushDate},
            </if>
            <if test="item.errorMsg != null">
                t.error_msg=#{item.errorMsg},
            </if>
            <if test="item.pushFailCount != null">
                t.push_fail_count=#{item.pushFailCount},
            </if>
            <if test="item.pushNumber != null">
                t.PUSH_NUMBER=#{item.pushNumber},
            </if>
            t.last_updated_date=sysdate
            where id = #{item.id}
        </foreach>
    </update>

    <update id="update" parameterType="java.util.List">
        update push_board_data_process t set
        t.push_status=#{pushStatus},
        t.error_msg = #{errorMsg},
        t.push_fail_count = #{pushFailCount},
        <if test="pushDate != null">
            push_date = #{pushDate},
        </if>
        last_updated_date = sysdate
        where id = #{id}
    </update>

    <!-- Started by AICoder, pid:5bf0bm8d5ecb31514359083fd01e9e1fc5232b11 -->
    <insert id="batchInsertIfNotExists" parameterType="java.util.List">
        with T as (
        <foreach collection="list" item="item" separator="UNION ALL">
            select #{item.id} as ID, #{item.sn} as SN, #{item.businessType} as BUSINESS_TYPE
        </foreach>
        )
        insert into push_board_data_process (id, sn, business_type)
        select id, sn, business_type from T
        where not exists (
        select 1 from push_board_data_process A
        where A.sn = T.sn and A.business_type = T.business_type and A.enabled_flag = 'Y'
        )
    </insert>

    <!-- Ended by AICoder, pid:5bf0bm8d5ecb31514359083fd01e9e1fc5232b11 -->

    <!-- Started by AICoder, pid:bbf0b08d5erb31514359083fd01e9e3fc5202b11 -->
    <select id="queryPushDataList" resultType="com.zte.interfaces.dto.PushBoardDataProcessDTO">
        SELECT PBDP.ID, PBDP.SN, PBDP.BUSINESS_TYPE, PBD.PRODPLAN_ID,PBDP.CREATE_DATE
        FROM PUSH_BOARD_DATA_HEAD PBDH
        JOIN PUSH_BOARD_DATA_DETAIL PBD ON PBD.PRODPLAN_ID = PBDH.PRODPLAN_ID
        JOIN PUSH_BOARD_DATA_PROCESS PBDP ON PBDP.SN = PBD.SN
        WHERE PBDH.ENABLED_FLAG = 'Y'
        AND PBDP.ENABLED_FLAG = 'Y'
        AND PBD.ENABLED_FLAG = 'Y'
        AND PBDH.FACTORY_ID = #{factoryId,jdbcType=INTEGER}
        <if test="businessTypeList != null and businessTypeList.size() > 0">
            AND PBDP.BUSINESS_TYPE IN
            <foreach collection="businessTypeList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="customerName != null and customerName != ''">
            AND PBDH.CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR}
        </if>
        <if test="beforeDay != null">
            AND PBD.CREATE_DATE > sysdate - #{beforeDay,jdbcType=INTEGER}
        </if>
        <if test="pushStatusList != null and pushStatusList.size()>0">
            AND PBDP.PUSH_STATUS in
            <foreach collection="pushStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            AND PBDP.CREATE_DATE >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="id != null and id != ''">
            and not exists (
                select 1 from PUSH_BOARD_DATA_PROCESS a where PBDP.id = a.id and a.id
                <![CDATA[ <= #{id,jdbcType=VARCHAR}]]>
            <if test="startTime != null">
                    AND a.create_date = #{startTime,jdbcType=TIMESTAMP}
            </if>
            )
        </if>
        order by PBDP.create_date, PBDP.id
        LIMIT #{rows,jdbcType=INTEGER}
    </select>

    <!-- Ended by AICoder, pid:bbf0b08d5erb31514359083fd01e9e3fc5202b11 -->


</mapper>