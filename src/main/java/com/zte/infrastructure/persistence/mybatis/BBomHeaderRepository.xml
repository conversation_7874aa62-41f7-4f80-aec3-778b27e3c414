<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.BBomHeaderRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.BBomHeader">
        <id column="BOM_HEADER_ID" jdbcType="VARCHAR" property="bomHeaderId"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType"/>
        <result column="ENG_DESC" jdbcType="VARCHAR" property="engDesc"/>
        <result column="CHI_DESC" jdbcType="VARCHAR" property="chiDesc"/>
        <result column="VER_NO" jdbcType="VARCHAR" property="verNo"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="SOURCE_SYSTEM" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="SOURCE_ITEM_ID" jdbcType="VARCHAR" property="sourceItemId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <result column="IMPORTED_CAD_FLAG" jdbcType="VARCHAR" property="importedCadFlag"/>
        <result column="zj_subcard_flag" jdbcType="VARCHAR" property="zjSubcardFlag"/>
        <result column="parse_status" jdbcType="VARCHAR" property="parseStatus"/>
        <result column="CONFIRMED_PCB_FPC" jdbcType="VARCHAR" property="confirmedPcbFpc"/>
        <result column="INCLUDE_PCB_FPC" jdbcType="INTEGER" property="includePcbFpc"/>
        <result column="CONFIRMED_BY" jdbcType="VARCHAR" property="confirmedBy"/>
        <result column="CONFIRMED_DATE" jdbcType="TIMESTAMP" property="confirmedDate"/>
        <result column="cloud_disk_id" jdbcType="VARCHAR" property="cloudDiskId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <collection property="details" resultMap="childBaseResultMap" columnPrefix="line_"/>
    </resultMap>

    <resultMap id="childBaseResultMap" type="com.zte.domain.model.BBomDetail">
        <id column="B_BOM_DETAIL_ID" jdbcType="VARCHAR" property="bBomDetailId"/>
        <result column="BOM_HEADER_ID" jdbcType="VARCHAR" property="bomHeaderId"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="LEVEL_NO" jdbcType="VARCHAR" property="levelNo"/>
        <result column="USAGE_COUNT" jdbcType="DECIMAL" property="usageCount"/>
        <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LOCATION_CODE" jdbcType="VARCHAR" property="locationCode"/>
        <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection"/>
        <result column="CHI_DESC" jdbcType="VARCHAR" property="chiDesc"/>
        <result column="STYLE" jdbcType="VARCHAR" property="style"/>
        <result column="IS_SMT" jdbcType="VARCHAR" property="isSmt"/>
    </resultMap>

    <resultMap id="ProdBindingMap" type="com.zte.interfaces.dto.ProdBindingSettingDTO">
        <id column="SETTING_ID" jdbcType="VARCHAR" property="settingId"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType"/>
        <result column="USAGE_COUNT" jdbcType="DECIMAL" property="usageCount"/>
        <result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode"/>
        <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection"/>
        <result column="SOURCE_SYSTEM" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3"/>
        <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4"/>
        <result column="ATTRIBUTE5" jdbcType="VARCHAR" property="attribute5"/>
    </resultMap>

    <resultMap id="SpecifiedPsTaskVOResultMap" type="com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO">
        <result column="pcb_version" jdbcType="VARCHAR" property="pcbVersion"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    </resultMap>

    <resultMap type="com.zte.interfaces.dto.BoardAssemblyRelationshipDTO" id="boardAssemblyRelationshipDTOMap">
        <result property="itemCode" jdbcType="VARCHAR" column="item_code"/>
        <result property="itemName" jdbcType="VARCHAR" column="item_name"/>
        <result property="pcbVersion" jdbcType="VARCHAR" column="pcb_version"/>
    </resultMap>

    <select id="selectBBomHeaderByItemNoList" parameterType="java.lang.String" resultMap="SpecifiedPsTaskVOResultMap">
        select bbh.product_code , bbh.ver_no pcb_version
        from b_bom_header bbh
        where 1 = 1 and bbh.enabled_flag = 'Y' and bbh.product_code in
        <foreach collection="itemNoList" index="index" item="itemNo" open="(" separator="," close=")">
            #{itemNo}
        </foreach>
    </select>

    <select id="selectBBomHeaderByProductCodeList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from b_bom_header bbh
        where 1 = 1 and bbh.enabled_flag = 'Y' and bbh.product_code in
        <foreach collection="productCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <sql id="Base_Column_List">
        BOM_HEADER_ID, PRODUCT_CODE, PRODUCT_TYPE, ENG_DESC, CHI_DESC, VER_NO, REMARK, SOURCE_SYSTEM,
        SOURCE_ITEM_ID, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG,
        ORG_ID, FACTORY_ID, ENTITY_ID, IMPORTED_CAD_FLAG,PARSE_STATUS,CONFIRMED_PCB_FPC,CONFIRMED_DATE,CONFIRMED_BY
        ,INCLUDE_PCB_FPC,ZJ_SUBCARD_FLAG, cloud_disk_id, file_name
    </sql>


    <select id="selectBBomHeaderById" parameterType="com.zte.domain.model.BBomHeader" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_BOM_HEADER
        where BOM_HEADER_ID = #{bomHeaderId,jdbcType=VARCHAR}
        AND ENABLED_FLAG='Y'
    </select>

    <select id="selectBBomHeaderByProductCode" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="BaseResultMap">
        select distinct
        h.BOM_HEADER_ID, h.PRODUCT_CODE, h.PRODUCT_TYPE, h.ENG_DESC, h.CHI_DESC, h.VER_NO, h.REMARK, h.SOURCE_SYSTEM,
        h.SOURCE_ITEM_ID, h.CREATE_BY, h.CREATE_DATE, h.LAST_UPDATED_BY, h.LAST_UPDATED_DATE, h.ENABLED_FLAG,
        h.ORG_ID, h.FACTORY_ID, h.ENTITY_ID, h.IMPORTED_CAD_FLAG,h.zj_subcard_flag,h.cloud_disk_id,h.file_name
        from B_BOM_HEADER h
        <if test="params.itemCode != null and params.itemCode != ''">
            join B_BOM_DETAIL d on h.bom_header_id=d.bom_header_id
        </if>
        where h.ENABLED_FLAG='Y'
        <if test="params.productCode != null and params.productCode != ''">
            and h.PRODUCT_CODE = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.itemCode != null and params.itemCode != ''">
            and d.ENABLED_FLAG='Y' and d.ITEM_CODE = #{params.itemCode,jdbcType=VARCHAR}
        </if>
        <if test="(params.itemCode == null or params.itemCode == '')
                    and (params.productCode == null or params.productCode == '')">
            and 1=2
        </if>
    </select>

    <select id="bBomHeaderListLike" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="BaseResultMap">
        select distinct
        h.BOM_HEADER_ID, h.PRODUCT_CODE, h.PRODUCT_TYPE, h.ENG_DESC, h.CHI_DESC, h.VER_NO, h.REMARK,
        h.SOURCE_SYSTEM, h.SOURCE_ITEM_ID, h.CREATE_BY, h.CREATE_DATE, h.LAST_UPDATED_BY,
        h.LAST_UPDATED_DATE, h.ENABLED_FLAG, h.ORG_ID, h.FACTORY_ID, h.ENTITY_ID, h.IMPORTED_CAD_FLAG,
        h.zj_subcard_flag
        from
        (select h.*
        from B_BOM_HEADER h
        <if test="params.itemCode != null and params.itemCode != ''">
            join B_BOM_DETAIL d on h.bom_header_id=d.bom_header_id
        </if>
        where h.ENABLED_FLAG='Y'
        <if test="params.productCode != null and params.productCode != ''">
            and h.PRODUCT_CODE like concat(#{params.productCode}::text,'%')
        </if>
        <if test="params.itemCode != null and params.itemCode != ''">
            and d.ENABLED_FLAG='Y' and d.ITEM_CODE = #{params.itemCode,jdbcType=VARCHAR}
        </if>
        limit 1) as h
    </select>


    <select id="selectBBomHeaderByCondition" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="BaseResultMap">
        select distinct
        h.BOM_HEADER_ID, h.PRODUCT_CODE, h.ENG_DESC, h.CHI_DESC, h.LAST_UPDATED_DATE, h.ENABLED_FLAG
        from B_BOM_HEADER h
        where h.ENABLED_FLAG='Y'
        <if test="params.inProductCodes != null and params.inProductCodes.size()>0">
            and h.PRODUCT_CODE in
            <foreach collection="params.inProductCodes" index="index" item="item" open="(" separator="," close=")">
                #{params.inProductCodes[${index}]}
            </foreach>
        </if>
        <if test="params.chiDesc != null and params.chiDesc != ''">
            and h.CHI_DESC like CONCAT(#{params.chiDesc},'%')
        </if>
        <if test="(params.chiDesc == null or params.chiDesc == '') and (params.inProductCodes == null or params.inProductCodes.size() == 0)">
            and 1=2
        </if>
        order by last_updated_date desc
    </select>

    <delete id="deleteBBomHeaderById" parameterType="com.zte.domain.model.BBomHeader">
        delete from B_BOM_HEADER
        where BOM_HEADER_ID = #{bomHeaderId,jdbcType=VARCHAR}
        AND ENABLED_FLAG='Y'
    </delete>

    <insert id="insertBBomHeader" parameterType="com.zte.domain.model.BBomHeader">
        insert into B_BOM_HEADER (BOM_HEADER_ID, PRODUCT_CODE, PRODUCT_TYPE,
        ENG_DESC, CHI_DESC, VER_NO,
        REMARK, SOURCE_SYSTEM, SOURCE_ITEM_ID,
        CREATE_BY, CREATE_DATE, LAST_UPDATED_BY,
        LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID,
        FACTORY_ID, ENTITY_ID)
        values (#{bomHeaderId,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR},
        #{engDesc,jdbcType=VARCHAR}, #{chiDesc,jdbcType=VARCHAR}, #{verNo,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{sourceSystem,jdbcType=VARCHAR}, #{sourceItemId,jdbcType=VARCHAR},
        #{createBy,jdbcType=VARCHAR}, SYSDATE, #{lastUpdatedBy,jdbcType=VARCHAR},
        SYSDATE, #{enabledFlag,jdbcType=VARCHAR}, #{orgId,jdbcType=DECIMAL},
        #{factoryId,jdbcType=DECIMAL}, #{entityId,jdbcType=DECIMAL})
    </insert>

    <insert id="insertBBomHeaderSelective" parameterType="com.zte.domain.model.BBomHeader">
        insert into B_BOM_HEADER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bomHeaderId != null">
                BOM_HEADER_ID,
            </if>

            <if test="productCode != null">
                PRODUCT_CODE,
            </if>

            <if test="productType != null">
                PRODUCT_TYPE,
            </if>

            <if test="engDesc != null">
                ENG_DESC,
            </if>

            <if test="chiDesc != null">
                CHI_DESC,
            </if>

            <if test="verNo != null">
                VER_NO,
            </if>

            <if test="remark != null">
                REMARK,
            </if>

            <if test="sourceSystem != null">
                SOURCE_SYSTEM,
            </if>

            <if test="sourceItemId != null">
                SOURCE_ITEM_ID,
            </if>

            <if test="createBy != null">
                CREATE_BY,
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY,
            </if>

            <if test="orgId != null">
                ORG_ID,
            </if>

            <if test="factoryId != null">
                FACTORY_ID,
            </if>

            <if test="entityId != null">
                ENTITY_ID,
            </if>
            CREATE_DATE,
            LAST_UPDATED_DATE,
            ENABLED_FLAG

        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bomHeaderId != null">
                #{bomHeaderId,jdbcType=VARCHAR},
            </if>

            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>

            <if test="productType != null">
                #{productType,jdbcType=VARCHAR},
            </if>

            <if test="engDesc != null">
                #{engDesc,jdbcType=VARCHAR},
            </if>

            <if test="chiDesc != null">
                #{chiDesc,jdbcType=VARCHAR},
            </if>

            <if test="verNo != null">
                #{verNo,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>

            <if test="sourceSystem != null">
                #{sourceSystem,jdbcType=VARCHAR},
            </if>

            <if test="sourceItemId != null">
                #{sourceItemId,jdbcType=VARCHAR},
            </if>

            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>


            <if test="orgId != null">
                #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                #{factoryId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                #{entityId,jdbcType=DECIMAL},
            </if>
            sysdate,
            sysdate,
            'Y'
        </trim>

    </insert>

    <update id="updateBBomHeaderByIdSelective" parameterType="com.zte.domain.model.BBomHeader">
        update B_BOM_HEADER
        <set>
            LAST_UPDATED_DATE = SYSDATE,
            <if test="productCode != null">
                PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
            </if>

            <if test="productType != null">
                PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
            </if>

            <if test="engDesc != null">
                ENG_DESC = #{engDesc,jdbcType=VARCHAR},
            </if>

            <if test="chiDesc != null">
                CHI_DESC = #{chiDesc,jdbcType=VARCHAR},
            </if>


            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="sourceSystem != null">
                SOURCE_SYSTEM = #{sourceSystem,jdbcType=VARCHAR},
            </if>

            <if test="sourceItemId != null">
                SOURCE_ITEM_ID = #{sourceItemId,jdbcType=VARCHAR},
            </if>

            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="importedCadFlag != null">
                IMPORTED_CAD_FLAG = #{importedCadFlag,jdbcType=VARCHAR},
            </if>

            <if test="zjSubcardFlag != null">
                zj_subcard_flag = #{zjSubcardFlag,jdbcType=VARCHAR},
            </if>

            <if test="cloudDiskId != null">
                cloud_disk_id = #{cloudDiskId,jdbcType=VARCHAR},
            </if>

            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>

        </set>

        where ENABLED_FLAG='Y'
        <!-- 当bomHeaderId不为空时，按bomHeaderId更新；
        当bomHeaderId为空且productCode不为空，按productCode更新；
        当bomHeaderId为空且productCode为空，不更新 -->
        <choose>
            <when test="bomHeaderId != null and bomHeaderId !=''">AND BOM_HEADER_ID = #{bomHeaderId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                <choose>
                    <when test="productCode != null and productCode !=''">AND PRODUCT_CODE =
                        #{productCode,jdbcType=VARCHAR}
                    </when>
                    <otherwise>AND 1=2</otherwise>
                </choose>
            </otherwise>
        </choose>
    </update>


    <update id="batchUpdateParseStatus" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update B_BOM_HEADER
            set PARSE_STATUS = #{item.parseStatus,jdbcType=VARCHAR},
            LAST_UPDATED_DATE = SYSDATE
            where PRODUCT_CODE = #{item.productCode,jdbcType=VARCHAR}
            AND ENABLED_FLAG='Y'
        </foreach>
    </update>

    <update id="updateBBomHeaderById" parameterType="com.zte.domain.model.BBomHeader">
        update B_BOM_HEADER
        set PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
        PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
        ENG_DESC = #{engDesc,jdbcType=VARCHAR},
        CHI_DESC = #{chiDesc,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        SOURCE_SYSTEM = #{sourceSystem,jdbcType=VARCHAR},
        SOURCE_ITEM_ID = #{sourceItemId,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = SYSDATE,
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
        ORG_ID = #{orgId,jdbcType=DECIMAL},
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
        ENTITY_ID = #{entityId,jdbcType=DECIMAL}
        where BOM_HEADER_ID = #{bomHeaderId,jdbcType=VARCHAR}
        AND ENABLED_FLAG='Y'
    </update>

    <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from B_BOM_HEADER
        where ENABLED_FLAG='Y'
        <if test="headerId != null and headerId != ''">and BOM_HEADER_ID = #{headerId}</if>
        <if test="productCode != null and productCode != ''">and PRODUCT_CODE = #{productCode}</if>
        <if test="orgId != null">and ORG_ID = #{orgId}::numeric</if>
        <if test="factoryId != null">and FACTORY_ID = #{factoryId}::numeric</if>
        <if test="entityId != null">and ENTITY_ID = #{entityId}::numeric</if>
        <if test="(headerId == null or headerId == '')
        and (productCode == null or productCode == '')
        and (orgId == null)
        and (factoryId == null)
         and (entityId == null)
          "> and 1=2</if>
        <if test="orderField != null">
            <choose>
                <when test="orderField=='verNo'">order by VER_NO
                    <if test="order != null and order == 'desc'">desc</if>
                </when>
            </choose>
        </if>
    </select>

    <select id="selectBBomHeaderNotCal" resultMap="BaseResultMap">
        select t.
        <include refid="Base_Column_List"/>
        from B_BOM_HEADER t
        WHERE ENABLED_FLAG='Y'
        <if test="productCode != null and productCode != ''">and PRODUCT_CODE = #{productCode}</if>
        <if test="factoryId != null and factoryId != ''">and FACTORY_ID = #{factoryId}::numeric</if>
        <if test="inRemark != null and inRemark != ''">and (REMARK IN ('N','E') OR REMARK IS NULL or REMARK = '')
        </if>
        <if test="inRemark == null">and (REMARK = 'N' OR REMARK IS NULL or REMARK = '')</if>
        limit #{limitNum}
    </select>
    <select id="getNeedCadProductCode" resultMap="BaseResultMap">
        SELECT DISTINCT T.PRODUCT_CODE, T.CHI_DESC, T.VER_NO, ORG_ID, FACTORY_ID, ENTITY_ID
        FROM B_BOM_HEADER T
        JOIN (
        <foreach collection="records" item="header" separator=" UNION ">
            SELECT #{header.productCodePre12,jdbcType=VARCHAR} BH_PRODUCT_CODE_PRE_12,
            #{header.chiDesc,jdbcType=VARCHAR} BH_CHI_DESC, #{header.verNo,jdbcType=VARCHAR} BH_VER_NO
        </foreach>
        ) BH
        ON SUBSTR(T.PRODUCT_CODE,1,12) = BH.BH_PRODUCT_CODE_PRE_12
        AND T.CHI_DESC = BH.BH_CHI_DESC
        AND T.VER_NO = BH.BH_VER_NO
        WHERE T.ENABLED_FLAG = 'Y'
        AND (T.IMPORTED_CAD_FLAG != 'Y' or t.IMPORTED_CAD_FLAG is null or t.IMPORTED_CAD_FLAG = '')
    </select>
    <select id="getHasCadProductCode" resultType="java.lang.String">
        SELECT DISTINCT T.PRODUCT_CODE FROM B_BOM_HEADER T
        WHERE T.ENABLED_FLAG = 'Y'
        AND (T.IMPORTED_CAD_FLAG = 'Y' or T.CONFIRMED_PCB_FPC = 'Y')
        AND PRODUCT_CODE IN
        <foreach collection="productCodes" item="productCode" open="( SELECT" separator=" PRODUCT_CODE  UNION SELECT "
                 close=" PRODUCT_CODE )">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 递归查询料单下所有子卡 -->
    <select id="getAllChildBoard" resultMap="ProdBindingMap">
        WITH RECURSIVE rec_bom AS(
        SELECT
        h.product_code,
        d.item_code,
        d.chi_desc item_name,
        d.usage_count,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i
        WHERE
        h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'

        <if test="pCodeList != null and pCodeList.size > 0">
            AND h.product_code IN
            <foreach collection="pCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pCodeList == null or pCodeList.size == 0">
            and 1=2
        </if>
        UNION ALL
        SELECT
        h.product_code,
        d.item_code,
        d.chi_desc item_name,
        d.usage_count * b.usage_count as usage_count ,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i,
        rec_bom b
        WHERE
        h.product_code = b.item_code
        AND h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'
        )
        SELECT * FROM rec_bom
    </select>

    <update id="updateHeaderRemarkById" parameterType="com.zte.domain.model.BBomHeader">
        update B_BOM_HEADER
        <set>
            LAST_UPDATED_DATE = SYSDATE,
            REMARK = #{remark,jdbcType=VARCHAR},
            LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        </set>
        where BOM_HEADER_ID = #{bomHeaderId,jdbcType=VARCHAR}
        AND ENABLED_FLAG='Y'
    </update>

    <select id="getProductCodeByItemCode" parameterType="java.util.Map" resultMap="BaseResultMap">
        select distinct h.PRODUCT_CODE,h.BOM_HEADER_ID
        from B_BOM_DETAIL d join B_BOM_HEADER h on d.bom_header_id=h.bom_header_id
        where h.ENABLED_FLAG='Y' and d.ENABLED_FLAG='Y' and d.ITEM_CODE = #{itemCode}
    </select>

    <!-- 递归查询料单下所有子卡 -->
    <select id="getAllChildBoardByProductCode" resultMap="BaseResultMap">
        WITH RECURSIVE rec_bom AS(
        SELECT
        1 AS LVLEL,
        h.bom_header_id,
        h.product_code,
        d.item_code,
        d.chi_desc item_name,
        d.usage_count,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i
        WHERE
        h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'
        AND h.product_code = #{productCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        b.LVLEL+1,
        h.bom_header_id,
        h.product_code,
        d.item_code,
        d.chi_desc item_name,
        d.usage_count * b.usage_count as usage_count ,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i,
        rec_bom b
        WHERE
        h.product_code = b.item_code
        AND h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'
        <![CDATA[ AND b.LVLEL < 100]]>
        )
        SELECT distinct rec.item_code PRODUCT_CODE,h.BOM_HEADER_ID FROM rec_bom rec join b_bom_header h on rec.item_code
        = h.product_code
    </select>

    <select id="queryImportCadProduct" parameterType="java.util.List" resultType="java.lang.String">
        SELECT PRODUCT_CODE FROM B_BOM_HEADER
        WHERE ENABLED_FLAG = 'Y'
        AND (IMPORTED_CAD_FLAG = 'Y' or CONFIRMED_PCB_FPC = 'Y')
        AND PRODUCT_CODE IN
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPcbVersionByItemCodes" resultMap="boardAssemblyRelationshipDTOMap">
        select t.product_code as item_code, t.ver_no as pcb_version
        from b_bom_header t
        where t.enabled_flag = 'Y'
        <if test="itemCodeSet != null and itemCodeSet.size > 0">
            and t.product_code in
            <foreach collection="itemCodeSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemCodeSet == null or itemCodeSet.size == 0">
            and 1=2
        </if>
    </select>

    <select id="getItemNameByItemCodes" resultMap="boardAssemblyRelationshipDTOMap">
        select t.product_code as item_code, t.chi_desc as item_name
        from b_bom_header t
        where t.enabled_flag = 'Y'
        <if test="itemCodeList != null and itemCodeList.size > 0">
            and t.product_code in
            <foreach collection="itemCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemCodeList == null or itemCodeList.size == 0">
            and 1=2
        </if>
    </select>


    <select id="selectBatchByHeadId" resultMap="BaseResultMap">
        select product_code,chi_desc,zj_subcard_flag,create_by,last_updated_by from b_bom_header
        where enabled_flag = 'Y'
        AND bom_header_id =#{bomHeaderId}
    </select>

    <!-- 递归查询料单下所有子卡 -->
    <select id="getAllChildByProductCode" resultMap="childBaseResultMap">
        WITH RECURSIVE rec_bom AS(
        SELECT
        1 AS LVLEL,
        h.bom_header_id,
        h.product_code,
        d.item_code,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i
        WHERE
        h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'
        AND h.product_code = #{productCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        b.LVLEL+1,
        h.bom_header_id,
        h.product_code,
        d.item_code,
        i.item_type
        FROM
        b_bom_header h,
        b_bom_detail d,
        bs_item_info i,
        rec_bom b
        WHERE
        h.product_code = b.item_code
        AND h.bom_header_id = d.bom_header_id
        AND h.ENABLED_FLAG = 'Y'
        AND d.ENABLED_FLAG = 'Y'
        AND i.item_no = d.item_code
        AND i.item_type = '1'
        <![CDATA[ AND b.LVLEL < 10]]>
        )
        SELECT distinct rec.item_code,PRODUCT_CODE FROM rec_bom rec
    </select>

    <select id="getBomInfoWithRouteByBomNo" resultType="com.zte.interfaces.dto.BaCraftRoutingInfoDTO">
        select b.bom_header_id bom_id,b.product_code bom_no,b.chi_desc bom_name, b.ver_no as version,c.board_craft_id
        craft_id,d.route_detail craft_desc
        from b_bom_header b
        left join ct_basic c
        on b.product_code = c.item_or_task and c.enabled_flag = 'Y' and c.craft_status = '已提交'
        left join ct_route_head d
        on d.craft_id = c.craft_id and d.enabled_flag = 'Y'
        where b.enabled_flag = 'Y'
        and b.product_code = #{bomNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <update id="updateIncludePcbInfo">
        update b_bom_header set
        CONFIRMED_PCB_FPC = 'Y',
        CONFIRMED_DATE = sysdate,
        LAST_UPDATED_DATE = sysdate,
        CONFIRMED_BY = #{empNo}
        where ENABLED_FLAG = 'Y'
        AND PRODUCT_CODE = #{productCode}
    </update>

    <select id="selectByCondition" parameterType="java.util.List" resultType="java.lang.String">
        SELECT PRODUCT_CODE
        from
        (
        SELECT PRODUCT_CODE
        from
        <foreach collection="list" open="(" separator="union all" close=")" item="item">
            SELECT
            H.PRODUCT_CODE
            FROM B_BOM_HEADER H, B_BOM_DETAIL D
            WHERE H.ENABLED_FLAG = 'Y' AND D.ENABLED_FLAG = 'Y'
            AND H.bom_header_id = D.bom_header_id
            AND H.PRODUCT_CODE = #{item.productCode,jdbcType=VARCHAR}
            AND D.ITEM_CODE IN
            <foreach collection="item.itemList" open="(" item="item2" separator="," close=")">
                #{item2}
            </foreach>
            and D.USAGE_COUNT > 1
        </foreach>
        )

    </select>

    <select id="queryBBomHeadListByBomIds" resultType="com.zte.interfaces.dto.BBomHeaderDTO">
        select t.bom_header_id, t.PRODUCT_CODE, t.chi_desc, t.ver_no
        from B_BOM_HEADER t
        where t.enabled_flag = 'Y'
        and t.bom_header_id in
        <foreach collection="bomIdSet" open="(" item="bomId" separator="," close=")">
            #{bomId}
        </foreach>
    </select>

    <select id="queryBBomHeadIdByProductCode" resultType="java.lang.String">
        select t.bom_header_id
        from B_BOM_HEADER t
        where t.enabled_flag = 'Y'
        and t.product_code = #{productCode}
        limit 1
    </select>

    <select id="selectBBomHeaderByProductCodes" resultMap="BaseResultMap">
        select t.
        <include refid="Base_Column_List"/>
        from B_BOM_HEADER t
        WHERE ENABLED_FLAG='Y' and (REMARK IN ('N','E') OR REMARK IS NULL or REMARK = '')
        <if test="productCodeList != null and productCodeList.size() > 0">
            and t.product_code in
            <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCodeList == null or productCodeList.size() == 0">
            and 1=2
        </if>
        order by last_updated_date DESC
    </select>

    <update id="updateHeaderRemarkByProductCodes">
        update B_BOM_HEADER
        <set>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="lastUpdatedBy != null">
                <if test="remark != null">,</if>
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
            </if>
            <if test="lastUpdatedBy != null">
                <if test="remark != null or lastUpdatedBy != null">,</if>
                LAST_UPDATED_DATE = SYSDATE
            </if>
        </set>
        where ENABLED_FLAG='Y'
        <if test="productCodes != null and productCodes.size() > 0">
            and product_code in
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCodes == null or productCodes.size() == 0">
            and 1=2
        </if>
    </update>


    <select id="queryBomHeaderLike" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="BaseResultMap">
        select distinct
        h.PRODUCT_CODE, h.PRODUCT_TYPE, h.ENG_DESC, h.CHI_DESC, h.VER_NO, h.REMARK,
        h.SOURCE_SYSTEM, h.SOURCE_ITEM_ID, h.CREATE_BY, h.CREATE_DATE, h.LAST_UPDATED_BY,
        h.LAST_UPDATED_DATE, h.ENABLED_FLAG, h.ORG_ID, h.FACTORY_ID, h.ENTITY_ID, h.IMPORTED_CAD_FLAG,
        h.zj_subcard_flag
        from B_BOM_HEADER h
        where h.ENABLED_FLAG='Y'
        <if test="params.productCode != null and params.productCode != ''">
            and h.PRODUCT_CODE like #{params.productCode,jdbcType=VARCHAR} || '%'
        </if>
        <if test="params.productCode == null or params.productCode == ''">
            and 1=2
        </if>
        order by h.PRODUCT_CODE
    </select>
</mapper>
