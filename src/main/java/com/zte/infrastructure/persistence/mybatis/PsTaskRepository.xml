<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.sncabind.PsTaskRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.sncabind.PsTask">
    <id column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="TASK_NO" jdbcType="VARCHAR" property="taskNo" />
    <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="IS_FINISH_GOOD" jdbcType="VARCHAR" property="isFinishGood" />
    <result column="PRODUCT_MODE" jdbcType="VARCHAR" property="productMode" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="IS_LEAD" jdbcType="VARCHAR" property="isLead" />
    <result column="TASK_QTY" jdbcType="DECIMAL" property="taskQty" />
    <result column="COMPLETE_QTY" jdbcType="DECIMAL" property="completeQty" />
    <result column="TASK_STATUS" jdbcType="VARCHAR" property="taskStatus" />
    <result column="INTERNAL_TYPE" jdbcType="VARCHAR" property="internalType" />
    <result column="EXTERNAL_TYPE" jdbcType="VARCHAR" property="externalType" />
    <result column="MO_NO" jdbcType="VARCHAR" property="moNo" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="SOFTWARE_VERSION" jdbcType="VARCHAR" property="softwareVersion" />
    <result column="RELEASE_DATE" jdbcType="TIMESTAMP" property="releaseDate" />
    <result column="PLANNED_FINISH_DATE" jdbcType="TIMESTAMP" property="plannedFinishDate" />
    <result column="FINAL_FINISH_DATE" jdbcType="TIMESTAMP" property="finalFinishDate" />
    <result column="SOURCE_SYS" jdbcType="VARCHAR" property="sourceSys" />
    <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId" />
    <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="DECIMAL" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="VARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="VARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="TIMESTAMP" property="attribute5" />
    <result column="ATTRIBUTE6" jdbcType="TIMESTAMP" property="attribute6" />
    <result column="ATTRIBUTE7" jdbcType="VARCHAR" property="attribute7" />
    <result column="ATTRIBUTE8" jdbcType="VARCHAR" property="attribute8" />
    <result column="ATTRIBUTE9" jdbcType="DECIMAL" property="attribute9" />
    <result column="ATTRIBUTE10" jdbcType="TIMESTAMP" property="attribute10" />
    <result column="PRODPLAN_NO" jdbcType="VARCHAR" property="prodplanNo" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="BOM_ID" jdbcType="VARCHAR" property="bomId" />
    <result column="PLAN_ID" jdbcType="VARCHAR" property="planId" />
    <result column="PLAN_UUID" jdbcType="VARCHAR" property="planUuid" />
    <result column="IS_PARTS" jdbcType="VARCHAR" property="isParts" />
    <result column="PARTS_PLANNO" jdbcType="VARCHAR" property="partsPlanno" />
    <result column="PLAN_SEQUENCE" jdbcType="DECIMAL" property="planSequence" />
    <result column="IS_FINISHEN" jdbcType="VARCHAR" property="isFinishen" />
    <result column="PRICE_DATE" jdbcType="TIMESTAMP" property="priceDate" />
    <result column="SOFTWARE_VER" jdbcType="VARCHAR" property="softwareVer" />
    <result column="LEAD_FLAG" jdbcType="VARCHAR" property="leadFlag" />
    <result column="OUT_FLAG" jdbcType="VARCHAR" property="outFlag" />
    <result column="PLANNER" jdbcType="VARCHAR" property="planner" />
    <result column="BJ_BOM_ID" jdbcType="VARCHAR" property="bjBomId" />
    <result column="DEMAN_END_DATE" jdbcType="TIMESTAMP" property="demanEndDate" />
    <result column="GENERATE_FLAG" jdbcType="VARCHAR" property="generateFlag" />
    <result column="CODE_DESC" jdbcType="VARCHAR" property="codeDesc" />
    <result column="STOVE_TYPE" jdbcType="VARCHAR" property="stoveType" />
    <result column="IS_DISCAVE" jdbcType="VARCHAR" property="isDiscave" />
    <result column="PROD_ADDRESS" jdbcType="VARCHAR" property="prodAddress" />
    <result column="PDM_WORK_GROUP3_NAME" jdbcType="VARCHAR" property="pdmWorkGroup3Name" />
    <result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType" />
    <result column="PROD_STATUS" jdbcType="VARCHAR" property="prodStatus" />
    <result column="PDM_WORK_GROUP1_NAME" jdbcType="VARCHAR" property="pdmWorkGroup1Name" />
    <result column="PDM_WORK_GROUP2_NAME" jdbcType="VARCHAR" property="pdmWorkGroup2Name" />
    <result column="ERP_STATUS" jdbcType="VARCHAR" property="erpStatus" />
    <result column="ENTP_NO" jdbcType="VARCHAR" property="entpNo" />
    <result column="HAS_GENERATE_SN" jdbcType="VARCHAR" property="hasGenerateSn" />
    <result column="ATTRIBUTE11" jdbcType="VARCHAR" property="attribute11" />
    <result column="ATTRIBUTE12" jdbcType="VARCHAR" property="attribute12" />
    <result column="ATTRIBUTE13" jdbcType="VARCHAR" property="attribute13" />
    <result column="ATTRIBUTE14" jdbcType="VARCHAR" property="attribute14" />
    <result column="TO_GRANT_DATE" jdbcType="TIMESTAMP" property="toGrantDate" />
    <result column="GRANT_TIME" jdbcType="TIMESTAMP" property="grantTime" />
    <result column="IS_COMPLETED" jdbcType="VARCHAR" property="isCompleted" />
    <result column="ERP_MODEL" jdbcType="VARCHAR" property="erpModel" />
    <result column="ORIGINAL_TASK" jdbcType="VARCHAR" property="originalTask" />
    <result column="REWORK_SOURCE" jdbcType="VARCHAR" property="reworkSource" />
    <result column="FIRST_WAREHOUSE_DATE" jdbcType="TIMESTAMP" property="firstWarehouseDate" />
    <result column="FIRST_EXPECTED_DELIVERY_DATE" jdbcType="TIMESTAMP" property="firstExpectedDeliveryDate" />
    <result column="LAST_DELIVERY_DATE" jdbcType="TIMESTAMP" property="lastDeliveryDate" />
    <result column="RECEIVE_SEND_MATERIALS_FLAG" jdbcType="VARCHAR" property="receiveSendMaterialsFlag" />
    <result column="ASSEMBLY_REMARK" jdbcType="VARCHAR" property="assemblyRemark" />
    <result column="INFOR_EXE" jdbcType="VARCHAR" property="inforExe" />
    <result column="CONSIGN_PLANNO" jdbcType="VARCHAR" property="consignplanNo" />
    <result column="IS_CONSIGN" jdbcType="INTEGER" property="isConsign" />
    <result column="CONSIGN_BOM_ID" jdbcType="VARCHAR" property="consignBomId" />
    <result column="CONSIGN_BOM_NO" jdbcType="VARCHAR" property="consignBomNo" />
    <result column="ORG_BOM_ID" jdbcType="VARCHAR" property="orgBomId" />
    <result column="STOCK" jdbcType="VARCHAR" property="stock" />
    <result column="GRANT_BY" jdbcType="VARCHAR" property="grantBy" />
  </resultMap>

  <resultMap id="SpecifiedBaseResultMap" type="com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO">
    <result column="TASK_NO" jdbcType="VARCHAR" property="taskNo" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="TASK_STATUS" jdbcType="VARCHAR" property="taskStatus" />
    <result column="TASK_QTY" jdbcType="DECIMAL" property="taskQty" />
    <result column="EXTERNAL_TYPE" jdbcType="VARCHAR" property="externalType" />
    <result column="INTERNAL_TYPE" jdbcType="VARCHAR" property="internalType" />
    <result column="LEAD_FLAG" jdbcType="VARCHAR" property="leadFlag" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="unVer12ItemNo" jdbcType="VARCHAR" property="unVersionItemNo" />
    <result column="ver" jdbcType="VARCHAR" property="ver" />
    <result column="LAST_DELIVERY_DATE" jdbcType="TIMESTAMP" property="lastDeliveryDate" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="source_sys" jdbcType="VARCHAR" property="sourceSys" />
    <result column="get_date" jdbcType="TIMESTAMP" property="getDate" />
    <result column="last_updated_date" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
  </resultMap>

  <sql id="Base_Column_List">
    TASK_ID, TASK_NO, CONTRACT_NO, ITEM_NO, IS_FINISH_GOOD, PRODUCT_MODE, ITEM_NAME,
    IS_LEAD, TASK_QTY, COMPLETE_QTY, TASK_STATUS, INTERNAL_TYPE, EXTERNAL_TYPE, MO_NO,
    TYPE, SOFTWARE_VERSION, RELEASE_DATE, PLANNED_FINISH_DATE, FINAL_FINISH_DATE, SOURCE_SYS,
    PRODUCT_TYPE, REMARK, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
    ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID, ATTRIBUTE1, ATTRIBUTE2, ATTRIBUTE3,
    ATTRIBUTE4, ATTRIBUTE5, ATTRIBUTE6, ATTRIBUTE7, ATTRIBUTE8, ATTRIBUTE9, ATTRIBUTE10,
    PRODPLAN_NO, PRODPLAN_ID, BOM_ID, PLAN_ID, PLAN_UUID, IS_PARTS, PARTS_PLANNO, PLAN_SEQUENCE,
    IS_FINISHEN, PRICE_DATE, SOFTWARE_VER, LEAD_FLAG, OUT_FLAG, PLANNER, BJ_BOM_ID, DEMAN_END_DATE,
    GENERATE_FLAG, CODE_DESC, STOVE_TYPE, IS_DISCAVE, PROD_ADDRESS, PDM_WORK_GROUP3_NAME,
    SOURCE_TYPE, PROD_STATUS, PDM_WORK_GROUP1_NAME, PDM_WORK_GROUP2_NAME, ERP_STATUS,
    ENTP_NO, HAS_GENERATE_SN, ATTRIBUTE11, ATTRIBUTE12, ATTRIBUTE13, ATTRIBUTE14, TO_GRANT_DATE , GRANT_TIME,
    IS_COMPLETED,ERP_MODEL,ORIGINAL_TASK,REWORK_SOURCE,FIRST_WAREHOUSE_DATE,FIRST_EXPECTED_DELIVERY_DATE,
	  LAST_DELIVERY_DATE,RECEIVE_SEND_MATERIALS_FLAG,ASSEMBLY_REMARK,INFOR_EXE,CONSIGN_PLANNO,IS_CONSIGN,
    CONSIGN_BOM_ID,CONSIGN_BOM_NO,ORG_BOM_ID,STOCK,out_source_factory_code,confirmation_status
  </sql>

  <update id="updatePsTaskByIdSelective" parameterType="com.zte.domain.model.sncabind.PsTask">
    update PS_TASK
    <set>
      <if test="taskNo != null">
        TASK_NO = #{taskNo,jdbcType=VARCHAR},
      </if>

      <if test="contractNo != null">
        CONTRACT_NO = #{contractNo,jdbcType=VARCHAR},
      </if>

      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>

      <if test="isFinishGood != null">
        IS_FINISH_GOOD = #{isFinishGood,jdbcType=VARCHAR},
      </if>

      <if test="productMode != null">
        PRODUCT_MODE = #{productMode,jdbcType=VARCHAR},
      </if>

      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>

      <if test="isLead != null">
        IS_LEAD = #{isLead,jdbcType=VARCHAR},
      </if>

      <if test="taskQty != null">
        TASK_QTY = #{taskQty,jdbcType=DECIMAL},
      </if>

      <if test="completeQty != null">
        COMPLETE_QTY = #{completeQty,jdbcType=DECIMAL},
      </if>

      <if test="taskStatus != null">
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>

      <if test="internalType != null">
        INTERNAL_TYPE = #{internalType,jdbcType=VARCHAR},
      </if>

      <if test="externalType != null">
        EXTERNAL_TYPE = #{externalType,jdbcType=VARCHAR},
      </if>

      <if test="moNo != null">
        MO_NO = #{moNo,jdbcType=VARCHAR},
      </if>

      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>

      <if test="softwareVersion != null">
        SOFTWARE_VERSION = #{softwareVersion,jdbcType=VARCHAR},
      </if>

      <if test="releaseDate != null">
        RELEASE_DATE = #{releaseDate,jdbcType=TIMESTAMP},
      </if>

      <if test="plannedFinishDate != null">
        PLANNED_FINISH_DATE = #{plannedFinishDate,jdbcType=TIMESTAMP},
      </if>

      <if test="finalFinishDate != null">
        FINAL_FINISH_DATE = #{finalFinishDate,jdbcType=TIMESTAMP},
      </if>

      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>

      <if test="productType != null">
        PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
      </if>

      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>

      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=DECIMAL},
      </if>

      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>

      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>

      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>

      <if test="attribute6 != null">
        ATTRIBUTE6 = #{attribute6,jdbcType=TIMESTAMP},
      </if>

      <if test="attribute7 != null">
        ATTRIBUTE7 = #{attribute7,jdbcType=VARCHAR},
      </if>

      <if test="attribute8 != null">
        ATTRIBUTE8 = #{attribute8,jdbcType=VARCHAR},
      </if>

      <if test="attribute9 != null">
        ATTRIBUTE9 = #{attribute9,jdbcType=DECIMAL},
      </if>

      <if test="attribute10 != null">
        ATTRIBUTE10 = #{attribute10,jdbcType=TIMESTAMP},
      </if>

      <if test="prodplanNo != null">
        PRODPLAN_NO = #{prodplanNo,jdbcType=VARCHAR},
      </if>

      <if test="prodplanId != null">
        PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR},
      </if>

      <if test="bomId != null">
        BOM_ID = #{bomId,jdbcType=VARCHAR},
      </if>

      <if test="planId != null">
        PLAN_ID = #{planId,jdbcType=VARCHAR},
      </if>

      <if test="planUuid != null">
        PLAN_UUID = #{planUuid,jdbcType=VARCHAR},
      </if>

      <if test="isParts != null">
        IS_PARTS = #{isParts,jdbcType=VARCHAR},
      </if>

      <if test="partsPlanno != null">
        PARTS_PLANNO = #{partsPlanno,jdbcType=VARCHAR},
      </if>

      <if test="planSequence != null">
        PLAN_SEQUENCE = #{planSequence,jdbcType=DECIMAL},
      </if>

      <if test="isFinishen != null">
        IS_FINISHEN = #{isFinishen,jdbcType=VARCHAR},
      </if>

      <if test="priceDate != null">
        PRICE_DATE = #{priceDate,jdbcType=TIMESTAMP},
      </if>

      <if test="softwareVer != null">
        SOFTWARE_VER = #{softwareVer,jdbcType=VARCHAR},
      </if>

      <if test="leadFlag != null">
        LEAD_FLAG = #{leadFlag,jdbcType=VARCHAR},
      </if>

      <if test="outFlag != null">
        OUT_FLAG = #{outFlag,jdbcType=VARCHAR},
      </if>

      <if test="planner != null">
        PLANNER = #{planner,jdbcType=VARCHAR},
      </if>

      <if test="bjBomId != null">
        BJ_BOM_ID = #{bjBomId,jdbcType=VARCHAR},
      </if>

      <if test="demanEndDate != null">
        DEMAN_END_DATE = #{demanEndDate,jdbcType=TIMESTAMP},
      </if>

      <if test="generateFlag != null">
        GENERATE_FLAG = #{generateFlag,jdbcType=VARCHAR},
      </if>

      <if test="codeDesc != null">
        CODE_DESC = #{codeDesc,jdbcType=VARCHAR},
      </if>

      <if test="stoveType != null">
        STOVE_TYPE = #{stoveType,jdbcType=VARCHAR},
      </if>

      <if test="isDiscave != null">
        IS_DISCAVE = #{isDiscave,jdbcType=VARCHAR},
      </if>

      <if test="prodAddress != null">
        PROD_ADDRESS = #{prodAddress,jdbcType=VARCHAR},
      </if>

      <if test="pdmWorkGroup3Name != null">
        PDM_WORK_GROUP3_NAME = #{pdmWorkGroup3Name,jdbcType=VARCHAR},
      </if>

      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      </if>

      <if test="prodStatus != null">
        PROD_STATUS = #{prodStatus,jdbcType=VARCHAR},
      </if>

      <if test="pdmWorkGroup1Name != null">
        PDM_WORK_GROUP1_NAME = #{pdmWorkGroup1Name,jdbcType=VARCHAR},
      </if>

      <if test="pdmWorkGroup2Name != null">
        PDM_WORK_GROUP2_NAME = #{pdmWorkGroup2Name,jdbcType=VARCHAR},
      </if>

      <if test="erpStatus != null">
        ERP_STATUS = #{erpStatus,jdbcType=VARCHAR},
      </if>

      <if test="entpNo != null">
        ENTP_NO = #{entpNo,jdbcType=VARCHAR},
      </if>

      <if test="hasGenerateSn != null">
        HAS_GENERATE_SN = #{hasGenerateSn,jdbcType=VARCHAR},
      </if>
      <if test="toGrantDate != null">
        TO_GRANT_DATE = #{toGrantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantTime != null">
        GRANT_TIME = #{grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute11 != null">
        ATTRIBUTE11 = #{attribute11,jdbcType=VARCHAR},
      </if>
      <if test="attribute12 != null">
        ATTRIBUTE12 = #{attribute12,jdbcType=VARCHAR},
      </if>
      <if test="attribute13 != null">
        ATTRIBUTE13 = #{attribute13,jdbcType=VARCHAR},
      </if>
      <if test="attribute14 != null">
        ATTRIBUTE14 = #{attribute14,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE = SYSDATE,

    </set>

    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </update>

  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getSpecifiedPsTaskCount" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(*)
    from ps_task t
    where 1=1 and enabled_flag='Y'
    <if test="taskNo != null and taskNo != ''"> and t.task_no = #{taskNo}</if>
    <if test="prodplanIdList != null and prodplanIdList.size > 0"> and t.prodplan_id in
      <foreach item="item" collection="prodplanIdList" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
    <if test="itemNoList != null and itemNoList.size > 0"> and t.item_no in
      <foreach item="item" collection="itemNoList" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
  </select>

  <!-- 翻页函数:获取符合条件的记录数 -->
  <select id="getPsTaskCount" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(*)
    from ps_task t
    where 1=1 and enabled_flag='Y'
    <if test="prodplanId != null and prodplanId != ''"> and t.PRODPLAN_ID = #{prodplanId}</if>
    <if test="taskNo != null and taskNo != ''"> and t.task_no = #{taskNo}</if>
    <if test="attribute1 != null and attribute1 != ''"> and t.attribute1 = #{attribute1}</if>
    <if test="itemNo != null and itemNo != ''"> and t.item_no = #{itemNo}</if>
    <if test="releaseDateStart != null"> <![CDATA[and t.RELEASE_DATE >= #{releaseDateStart}]]></if>
    <if test="releaseDateEnd != null"> <![CDATA[and t.RELEASE_DATE <= #{releaseDateEnd}]]></if>
    <if test="toGrantDateStart != null"> <![CDATA[and t.TO_GRANT_DATE >= #{toGrantDateStart}]]></if>
    <if test="toGrantDateEnd != null"> <![CDATA[and t.TO_GRANT_DATE <= #{toGrantDateEnd}]]></if>
    <if test="grantTimeStart != null"> <![CDATA[and t.grant_time >= #{grantTimeStart}]]></if>
    <if test="grantTimeEnd != null"> <![CDATA[and t.grant_time <= #{grantTimeEnd}]]></if>
    <if test="externalType != null and externalType != ''"> and t.EXTERNAL_TYPE = #{externalType}</if>
    <if test="hasGenerateSn != null and hasGenerateSn != ''"> and t.HAS_GENERATE_SN = #{hasGenerateSn}</if>
    <if test="factoryId != null"> and t.FACTORY_ID = #{factoryId}::numeric</if>
    <if test="sourceSys != null and sourceSys != ''">and t.source_sys=#{sourceSys}</if>
    <if test="orgId != null"> and t.ORG_ID = #{orgId}::numeric</if>
    <if test="entityId != null"> and t.ENTITY_ID = #{entityId}::numeric</if>
    <if test="taskStatus != null and taskStatus != ''">and t.TASK_STATUS = #{taskStatus}</if>
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getSpecifiedPsTaskList" parameterType="com.zte.springbootframe.common.model.Page"
          resultMap="SpecifiedBaseResultMap">
    select t.task_no ,
    t.prodplan_id ,
    t.item_no ,
    t.item_name ,
    t.task_status ,
    t.task_qty ,
    t.external_type ,
    t.internal_type ,
    t.lead_flag,
    t.org_id ,
    left(t.item_no,12) unVer12ItemNo,
    right(t.item_no,3) ver,
    t.last_delivery_date ,
    t.attribute1 ,
    t.source_sys,
    t.get_date,
    t.erp_status,
    t.create_date,
    t.last_updated_date
    from ps_task t
    where enabled_flag='Y'
    <if test="params.taskNo != null and params.taskNo != ''"> and t.task_no = #{params.taskNo}</if>
    <if test="params.taskNoList != null and params.taskNoList.size() > 0">
      and t.task_no in
      <foreach item="item" collection="params.taskNoList" separator="," open="(" close=")" index="index">
        #{params.taskNoList[${index}]}
      </foreach>
    </if>
    <if test="params.prodplanIdList != null and params.prodplanIdList.size() > 0 ">
      and t.prodplan_id in
      <foreach item="item" collection="params.prodplanIdList" separator="," open="(" close=")" index="index">
        #{params.prodplanIdList[${index}]}
      </foreach>
    </if>
    <if test="params.itemNoList != null and params.itemNoList.size() > 0">
      and t.item_no in
      <foreach item="item" collection="params.itemNoList" separator="," open="(" close=")" index="index">
        #{params.itemNoList[${index}]}
      </foreach>
    </if>
    <if test="params.taskStatus != null and params.taskStatus != ''"> and t.task_status = #{params.taskStatus}</if>
    <if test="params.createStartDate != null"> <![CDATA[and t.CREATE_DATE >= #{params.createStartDate}]]></if>
    <if test="params.createEndDate != null"> <![CDATA[and t.CREATE_DATE <= #{params.createEndDate}]]></if>
    order by t.CREATE_DATE desc
  </select>

  <!-- 获取符合条件的记录列表  TODO 待优化-->
  <select id="getPsTaskList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.<include refid="Base_Column_List"/>
    from ps_task t where 1=1 and enabled_flag='Y'
    <if test="prodplanId != null and prodplanId != ''"> and t.PRODPLAN_ID = #{prodplanId}</if>
    <if test="taskNo != null and taskNo != ''"> and t.task_no = #{taskNo}</if>
    <if test="attribute1 != null and attribute1 != ''"> and t.attribute1 = #{attribute1}</if>
    <if test="itemNo != null and itemNo != ''"> and t.item_no = #{itemNo}</if>
    <if test="releaseDateStart != null"> <![CDATA[and t.RELEASE_DATE >= #{releaseDateStart}]]></if>
    <if test="releaseDateEnd != null"> <![CDATA[and t.RELEASE_DATE <= #{releaseDateEnd}]]></if>
    <if test="toGrantDateStart != null"> <![CDATA[and t.TO_GRANT_DATE >= #{toGrantDateStart}]]></if>
    <if test="toGrantDateEnd != null"> <![CDATA[and t.TO_GRANT_DATE <= #{toGrantDateEnd}]]></if>
    <if test="grantTimeStart != null"> <![CDATA[and t.grant_time >= #{grantTimeStart}]]></if>
    <if test="grantTimeEnd != null"> <![CDATA[and t.grant_time <= #{grantTimeEnd}]]></if>
    <if test="externalType != null and externalType != ''"> and t.EXTERNAL_TYPE = #{externalType}</if>
    <if test="hasGenerateSn != null and hasGenerateSn != ''"> and t.HAS_GENERATE_SN = #{hasGenerateSn}</if>
    <if test="statusList != null and statusList.size() > 0">
      and t.HAS_GENERATE_SN IN
      <foreach collection="statusList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="factoryId != null"> and t.FACTORY_ID = #{factoryId}::numeric</if>
    <if test="sourceSys != null and sourceSys != ''">and t.source_sys=#{sourceSys}</if>
    <if test="internalType != null and internalType != ''">and t.internal_type=#{internalType}</if>
    <if test="planner != null and planner != ''">and t.planner=#{planner}</if>
    <if test="orgId != null"> and t.ORG_ID = #{orgId}::numeric</if>
    <if test="entityId != null"> and t.ENTITY_ID = #{entityId}::numeric</if>
    <if test="taskStatus != null and taskStatus != ''">and t.TASK_STATUS = #{taskStatus}</if>
    <if test="(prodplanId == null or prodplanId == '') and
       (taskNo == null or taskNo == '')
        and (attribute1 == null or attribute1 == '')
        and (itemNo == null or itemNo == '')
         and (releaseDateStart == null)
         and (releaseDateEnd == null)
         and (toGrantDateStart == null)
         and (toGrantDateEnd == null)
         and (grantTimeStart == null)
         and (grantTimeEnd == null)
         and (externalType == null or externalType == '')
         and (hasGenerateSn == null or hasGenerateSn == '')
         and (statusList == null or statusList.size() == 0)
         and (factoryId == null)
         and (sourceSys == null or sourceSys == '')
         and (internalType == null or internalType == '')
         and (planner == null or planner == '')
         and (orgId == null)
         and (entityId == null)
         and (taskStatus == null or taskStatus == '')
       and (startRow == null or endRow == null)">
      and 1=2
    </if>
    <if test="orderField != null">
      <choose>
        <when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    <if test="startRow != null and endRow != null">limit #{endRow}::numeric-greatest(#{startRow}::numeric, 1)+1 offset greatest(#{startRow}::numeric, 1)-1</if>
  </select>

  <select id="getPsTaskCountWithRouteDetail" parameterType="java.util.Map" resultType="java.lang.Long">
    select count(1) from ps_task t
    left join b_bom_header bbh on t.item_no = bbh.product_code
    left join ct_basic cb on t.item_no = cb.item_or_task  and cb.craft_status = '已提交' and cb.enabled_flag='Y'
    left join ct_route_head crh on cb.craft_id = crh.craft_id and crh.enabled_flag='Y' and ((t.parts_planno is null and crh.process_type = '主板') or (t.parts_planno is not null and crh.process_type = '子板'))
    where t.enabled_flag='Y'
    <if test="prodplanId != null and prodplanId != ''"> and t.PRODPLAN_ID = #{prodplanId}</if>
    <if test="prodplanIdList != null and prodplanIdList.size() > 0">
      and t.PRODPLAN_ID IN
      <foreach collection="prodplanIdList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="taskNo != null and taskNo != ''"> and t.task_no = #{taskNo}</if>
    <if test="attribute1 != null and attribute1 != ''"> and t.attribute1 = #{attribute1}</if>
    <if test="itemNo != null and itemNo != ''"> and t.item_no = #{itemNo}</if>
    <if test="releaseDateStart != null"> <![CDATA[and t.RELEASE_DATE >= #{releaseDateStart}]]></if>
    <if test="releaseDateEnd != null"> <![CDATA[and t.RELEASE_DATE <= #{releaseDateEnd}]]></if>
    <if test="toGrantDateStart != null"> <![CDATA[and t.TO_GRANT_DATE >= #{toGrantDateStart}]]></if>
    <if test="toGrantDateEnd != null"> <![CDATA[and t.TO_GRANT_DATE <= #{toGrantDateEnd}]]></if>
    <if test="grantTimeStart != null"> <![CDATA[and t.grant_time >= #{grantTimeStart}]]></if>
    <if test="grantTimeEnd != null"> <![CDATA[and t.grant_time <= #{grantTimeEnd}]]></if>
    <if test="externalType != null and externalType != ''"> and t.EXTERNAL_TYPE = #{externalType}</if>
    <if test="hasGenerateSn != null and hasGenerateSn != ''"> and t.HAS_GENERATE_SN = #{hasGenerateSn}</if>
    <if test="factoryId != null"> and t.FACTORY_ID = #{factoryId}::numeric</if>
    <if test="sourceSys != null and sourceSys != ''">and t.source_sys=#{sourceSys}</if>
    <if test="orgId != null"> and t.ORG_ID = #{orgId}::numeric</if>
    <if test="entityId != null"> and t.ENTITY_ID = #{entityId}::numeric</if>
    <if test="taskStatus != null and taskStatus != ''">and t.TASK_STATUS = #{taskStatus}</if>
  </select>

  <!-- 获取符合条件的记录列表 -->
  <select id="getPsTaskListWithRouteDetail" parameterType="java.util.Map" resultType="com.zte.domain.model.sncabind.PsTask">
    select bii.item_name, pte.customer_no, t.*,crh.route_detail,bbh.ver_no verNo,cb.last_updated_by as technologist from ps_task t
    left join b_bom_header bbh on t.item_no = bbh.product_code
    left join ct_basic cb on t.item_no = cb.item_or_task  and cb.craft_status = '已提交' and cb.enabled_flag='Y'
    left join ct_route_head crh on cb.craft_id = crh.craft_id and crh.enabled_flag='Y' and ((t.parts_planno is null and crh.process_type = '主板') or (t.parts_planno is not null and crh.process_type = '子板'))
    left join bs_item_info bii on t.item_no = bii.item_no and bii.enabled_flag='Y'
    left join ps_task_extended pte on t.task_no = pte.task_no and pte.enabled_flag = 'Y'
    where t.enabled_flag='Y'
    <if test="prodplanId != null and prodplanId != ''"> and t.PRODPLAN_ID = #{prodplanId}</if>
    <if test="prodplanIdList != null and prodplanIdList.size() > 0">
      and t.PRODPLAN_ID IN
      <foreach collection="prodplanIdList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="taskNo != null and taskNo != ''"> and t.task_no = #{taskNo}</if>
    <if test="attribute1 != null and attribute1 != ''"> and t.attribute1 = #{attribute1}</if>
    <if test="itemNo != null and itemNo != ''"> and t.item_no = #{itemNo}</if>
    <if test="releaseDateStart != null"> <![CDATA[and t.RELEASE_DATE >= #{releaseDateStart}]]></if>
    <if test="releaseDateEnd != null"> <![CDATA[and t.RELEASE_DATE <= #{releaseDateEnd}]]></if>
    <if test="toGrantDateStart != null"> <![CDATA[and t.TO_GRANT_DATE >= #{toGrantDateStart}]]></if>
    <if test="toGrantDateEnd != null"> <![CDATA[and t.TO_GRANT_DATE <= #{toGrantDateEnd}]]></if>
    <if test="grantTimeStart != null"> <![CDATA[and t.grant_time >= #{grantTimeStart}]]></if>
    <if test="grantTimeEnd != null"> <![CDATA[and t.grant_time <= #{grantTimeEnd}]]></if>
    <if test="externalType != null and externalType != ''"> and t.EXTERNAL_TYPE = #{externalType}</if>
    <if test="hasGenerateSn != null and hasGenerateSn != ''"> and t.HAS_GENERATE_SN = #{hasGenerateSn}</if>
    <if test="statusList != null and statusList.size() > 0">
      and t.HAS_GENERATE_SN IN
      <foreach collection="statusList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="factoryId != null"> and t.FACTORY_ID = #{factoryId}::numeric</if>
    <if test="sourceSys != null and sourceSys != ''">and t.source_sys=#{sourceSys}</if>
    <if test="internalType != null and internalType != ''">and t.internal_type=#{internalType}</if>
    <if test="planner != null and planner != ''">and t.planner=#{planner}</if>
    <if test="orgId != null"> and t.ORG_ID = #{orgId}::numeric</if>
    <if test="entityId != null"> and t.ENTITY_ID = #{entityId}::numeric</if>
    <if test="taskStatus != null and taskStatus != ''">and t.TASK_STATUS = #{taskStatus}</if>
    <if test="orderField != null">
      <choose>
        <when test="orderField=='createDate'"> order by t.CREATE_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
        <when test="orderField=='lastUpdatedDate'"> order by t.LAST_UPDATED_DATE <if test="order != null and order == 'desc'"> desc </if> </when>
      </choose>
    </if>
    limit #{endRow}::numeric-greatest(#{startRow}::numeric, 1)+1 offset greatest(#{startRow}::numeric, 1)-1
  </select>


  <select id="searchForProdPlan" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ps_task
    where ENABLED_FLAG = 'Y'
    and PRODPLAN_ID in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

	<select id="searchByTaskNoList" parameterType="java.util.List" resultMap="BaseResultMap">
		select TASK_NO
		from ps_task
		where ENABLED_FLAG = 'Y'
		and TASK_NO in
		<foreach item="item" index="index" collection="list"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

  <update id="updatePsTaskForFactory" parameterType="com.zte.domain.model.sncabind.PsTask">
      update PS_TASK
      <set>
          <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
              LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
          </if>
          <if test="enabledFlag != null and enabledFlag != ''">
              ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
          </if>
          <if test="factoryId != null">
              FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
          </if>
          <if test="taskStatus != null and taskStatus !=''">
              task_status = #{taskStatus},
          </if>
          <if test="grantTime != null">
              grant_time = #{grantTime},
          </if>
          <if test="grantBy != null and grantBy != ''">
            GRANT_BY = #{grantBy,jdbcType=VARCHAR},
          </if>
          LAST_UPDATED_DATE = SYSDATE
      </set>
      where TASK_NO = #{taskNo,jdbcType=VARCHAR} and prodplan_id = #{prodplanId}
  </update>

  <select id="selectPsTaskByTaskNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from PS_TASK
    where task_no = #{taskNo} and prodplan_id = #{prodplanId}
  </select>

  <!-- 更新任务维护信息 -->
  <update id="updateTaskMaintainInfo" parameterType="com.zte.domain.model.sncabind.PsTask">
    update PS_TASK
    <set>
      TASK_NO = #{taskNo,jdbcType=VARCHAR},
      PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR},
      ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      ATTRIBUTE6 = #{attribute6,jdbcType=TIMESTAMP},
      ATTRIBUTE7 = #{attribute7,jdbcType=VARCHAR},
      ATTRIBUTE8 = #{attribute8,jdbcType=VARCHAR},
      ATTRIBUTE10 = #{attribute10,jdbcType=TIMESTAMP},
      ATTRIBUTE11 = #{attribute11,jdbcType=VARCHAR},
	  ASSEMBLY_REMARK = #{assemblyRemark,jdbcType=VARCHAR},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      LAST_UPDATED_DATE = sysdate,
      TO_GRANT_DATE = #{toGrantDate,jdbcType=TIMESTAMP},
      GRANT_TIME = #{grantTime,jdbcType=TIMESTAMP}
    </set>
    where TASK_NO = #{taskNo,jdbcType=VARCHAR} AND PRODPLAN_ID =#{prodplanId,jdbcType=VARCHAR}
  </update>

	<update id="batchUpdate" parameterType="java.util.List">
		<foreach collection="list" separator=";" item="item">
			UPDATE PS_TASK
			<set>
				<if test="item != null and item.assemblyRemark != null and item.assemblyRemark !=''">
					assembly_remark = #{item.assemblyRemark},
				</if>
				<if test="item != null and item.attribute3 != null and item.attribute3 !=''">
					ATTRIBUTE3 = #{item.attribute3},
				</if>
				<if test="item != null and item.attribute5 != null">
					ATTRIBUTE5 = #{item.attribute5},
				</if>
				<if test="item != null and item.attribute6 != null">
					ATTRIBUTE6 = #{item.attribute6},
				</if>
				<if test="item != null and item.attribute7 != null and item.attribute7 !=''">
					ATTRIBUTE7 = #{item.attribute7},
				</if>
				<if test="item != null and item.attribute8 != null and item.attribute8 !=''">
					ATTRIBUTE8 = #{item.attribute8},
				</if>
				<if test="item != null and item.attribute10 != null">
					ATTRIBUTE10 = #{item.attribute10},
				</if>
				<if test="item != null and item.attribute11 != null and item.attribute11 !=''">
					ATTRIBUTE11 = #{item.attribute11},
				</if>
				<if test="item != null and item.toGrantDate != null">
					TO_GRANT_DATE = #{item.toGrantDate},
				</if>
				<if test="item != null and item.lastUpdatedBy != null and item.lastUpdatedBy !=''">
					LAST_UPDATED_BY = #{item.lastUpdatedBy},
				</if>
				LAST_UPDATED_DATE = sysdate,
			</set>
			where TASK_NO = #{item.taskNo,jdbcType=VARCHAR} AND PRODPLAN_ID =#{item.prodplanId,jdbcType=VARCHAR}
		</foreach>
	</update>

  <select id="selectPsTaskList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ps_task
    where ENABLED_FLAG = 'Y' and task_no = #{taskNo} and source_sys = #{sourceSys}
  </select>

  <insert id="insertSelective" parameterType="com.zte.domain.model.sncabind.PsTask">
    insert into PS_TASK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        TASK_ID,
      </if>
      <if test="taskNo != null">
        TASK_NO,
      </if>
      <if test="contractNo != null">
        CONTRACT_NO,
      </if>
      <if test="itemNo != null">
        ITEM_NO,
      </if>
      <if test="isFinishGood != null">
        IS_FINISH_GOOD,
      </if>
      <if test="productMode != null">
        PRODUCT_MODE,
      </if>
      <if test="itemName != null">
        ITEM_NAME,
      </if>
      <if test="isLead != null">
        IS_LEAD,
      </if>
      <if test="taskQty != null">
        TASK_QTY,
      </if>
      <if test="completeQty != null">
        COMPLETE_QTY,
      </if>
      <if test="taskStatus != null">
        TASK_STATUS,
      </if>
      <if test="internalType != null">
        INTERNAL_TYPE,
      </if>
      <if test="externalType != null">
        EXTERNAL_TYPE,
      </if>
      <if test="moNo != null">
        MO_NO,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="softwareVersion != null">
        SOFTWARE_VERSION,
      </if>
      <if test="releaseDate != null">
        RELEASE_DATE,
      </if>
      <if test="plannedFinishDate != null">
        PLANNED_FINISH_DATE,
      </if>
      <if test="finalFinishDate != null">
        FINAL_FINISH_DATE,
      </if>
      <if test="sourceSys != null">
        SOURCE_SYS,
      </if>
      <if test="productType != null">
        PRODUCT_TYPE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="factoryId != null">
        FACTORY_ID,
      </if>
      <if test="entityId != null">
        ENTITY_ID,
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>
      <if test="attribute3 != null">
        ATTRIBUTE3,
      </if>
      <if test="attribute4 != null">
        ATTRIBUTE4,
      </if>
      <if test="attribute5 != null">
        ATTRIBUTE5,
      </if>
      <if test="attribute6 != null">
        ATTRIBUTE6,
      </if>
      <if test="attribute7 != null">
        ATTRIBUTE7,
      </if>
      <if test="attribute8 != null">
        ATTRIBUTE8,
      </if>
      <if test="attribute9 != null">
        ATTRIBUTE9,
      </if>
      <if test="attribute10 != null">
        ATTRIBUTE10,
      </if>
      <if test="prodplanNo != null">
        PRODPLAN_NO,
      </if>
      <if test="prodplanId != null">
        PRODPLAN_ID,
      </if>
      <if test="bomId != null">
        BOM_ID,
      </if>
      <if test="planId != null">
        PLAN_ID,
      </if>
      <if test="planUuid != null">
        PLAN_UUID,
      </if>
      <if test="isParts != null">
        IS_PARTS,
      </if>
      <if test="partsPlanno != null">
        PARTS_PLANNO,
      </if>
      <if test="planSequence != null">
        PLAN_SEQUENCE,
      </if>
      <if test="isFinishen != null">
        IS_FINISHEN,
      </if>
      <if test="priceDate != null">
        PRICE_DATE,
      </if>
      <if test="softwareVer != null">
        SOFTWARE_VER,
      </if>
      <if test="leadFlag != null">
        LEAD_FLAG,
      </if>
      <if test="outFlag != null">
        OUT_FLAG,
      </if>
      <if test="planner != null">
        PLANNER,
      </if>
      <if test="bjBomId != null">
        BJ_BOM_ID,
      </if>
      <if test="demanEndDate != null">
        DEMAN_END_DATE,
      </if>
      <if test="generateFlag != null">
        GENERATE_FLAG,
      </if>
      <if test="codeDesc != null">
        CODE_DESC,
      </if>
      <if test="stoveType != null">
        STOVE_TYPE,
      </if>
      <if test="isDiscave != null">
        IS_DISCAVE,
      </if>
      <if test="prodAddress != null">
        PROD_ADDRESS,
      </if>
      <if test="pdmWorkGroup3Name != null">
        PDM_WORK_GROUP3_NAME,
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE,
      </if>
      <if test="prodStatus != null">
        PROD_STATUS,
      </if>
      <if test="pdmWorkGroup1Name != null">
        PDM_WORK_GROUP1_NAME,
      </if>
      <if test="pdmWorkGroup2Name != null">
        PDM_WORK_GROUP2_NAME,
      </if>
      <if test="erpStatus != null">
        ERP_STATUS,
      </if>
      <if test="entpNo != null">
        ENTP_NO,
      </if>
      <if test="hasGenerateSn != null">
        HAS_GENERATE_SN,
      </if>
      <if test="toGrantDate != null">
        TO_GRANT_DATE,
      </if>
      <if test="grantTime != null">
        GRANT_TIME,
      </if>
      <if test="attribute11 != null">
        ATTRIBUTE11,
      </if>
      <if test="attribute12 != null">
        ATTRIBUTE12,
      </if>
      <if test="attribute13 != null">
        ATTRIBUTE13,
      </if>
      <if test="attribute14 != null">
        ATTRIBUTE14,
      </if>
      <if test="isCompleted != null">
        IS_COMPLETED,
      </if>
      <if test="originalTask != null">
        ORIGINAL_TASK,
      </if>
      <if test="erpModel != null">
        ERP_MODEL,
      </if>
      <if test="reworkSource != null">
        REWORK_SOURCE,
      </if>
      <if test="firstWarehouseDate != null">
        FIRST_WAREHOUSE_DATE,
      </if>
      <if test="firstExpectedDeliveryDate != null">
        FIRST_EXPECTED_DELIVERY_DATE,
      </if>
      <if test="lastDeliveryDate != null">
        LAST_DELIVERY_DATE,
      </if>
      <if test="assemblyRemark != null">
        ASSEMBLY_REMARK,
	  </if>
      CREATE_DATE,
      LAST_UPDATED_DATE,
      ENABLED_FLAG
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskNo != null">
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="isFinishGood != null">
        #{isFinishGood,jdbcType=VARCHAR},
      </if>
      <if test="productMode != null">
        #{productMode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="isLead != null">
        #{isLead,jdbcType=VARCHAR},
      </if>
      <if test="taskQty != null">
        #{taskQty,jdbcType=DECIMAL},
      </if>
      <if test="completeQty != null">
        #{completeQty,jdbcType=DECIMAL},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="internalType != null">
        #{internalType,jdbcType=VARCHAR},
      </if>
      <if test="externalType != null">
        #{externalType,jdbcType=VARCHAR},
      </if>
      <if test="moNo != null">
        #{moNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="softwareVersion != null">
        #{softwareVersion,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="plannedFinishDate != null">
        #{plannedFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finalFinishDate != null">
        #{finalFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceSys != null">
        #{sourceSys,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=DECIMAL},
      </if>
      <if test="factoryId != null">
        #{factoryId,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=DECIMAL},
      </if>
      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        #{attribute5,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute6 != null">
        #{attribute6,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute7 != null">
        #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="attribute8 != null">
        #{attribute8,jdbcType=VARCHAR},
      </if>
      <if test="attribute9 != null">
        #{attribute9,jdbcType=DECIMAL},
      </if>
      <if test="attribute10 != null">
        #{attribute10,jdbcType=TIMESTAMP},
      </if>
      <if test="prodplanNo != null">
        #{prodplanNo,jdbcType=VARCHAR},
      </if>
      <if test="prodplanId != null">
        #{prodplanId,jdbcType=VARCHAR},
      </if>
      <if test="bomId != null">
        #{bomId,jdbcType=VARCHAR},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=VARCHAR},
      </if>
      <if test="planUuid != null">
        #{planUuid,jdbcType=VARCHAR},
      </if>
      <if test="isParts != null">
        #{isParts,jdbcType=VARCHAR},
      </if>
      <if test="partsPlanno != null">
        #{partsPlanno,jdbcType=VARCHAR},
      </if>
      <if test="planSequence != null">
        #{planSequence,jdbcType=DECIMAL},
      </if>
      <if test="isFinishen != null">
        #{isFinishen,jdbcType=VARCHAR},
      </if>
      <if test="priceDate != null">
        #{priceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="softwareVer != null">
        #{softwareVer,jdbcType=VARCHAR},
      </if>
      <if test="leadFlag != null">
        #{leadFlag,jdbcType=VARCHAR},
      </if>
      <if test="outFlag != null">
        #{outFlag,jdbcType=VARCHAR},
      </if>
      <if test="planner != null">
        #{planner,jdbcType=VARCHAR},
      </if>
      <if test="bjBomId != null">
        #{bjBomId,jdbcType=VARCHAR},
      </if>
      <if test="demanEndDate != null">
        #{demanEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateFlag != null">
        #{generateFlag,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="stoveType != null">
        #{stoveType,jdbcType=VARCHAR},
      </if>
      <if test="isDiscave != null">
        #{isDiscave,jdbcType=VARCHAR},
      </if>
      <if test="prodAddress != null">
        #{prodAddress,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup3Name != null">
        #{pdmWorkGroup3Name,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="prodStatus != null">
        #{prodStatus,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup1Name != null">
        #{pdmWorkGroup1Name,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup2Name != null">
        #{pdmWorkGroup2Name,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        #{erpStatus,jdbcType=VARCHAR},
      </if>
      <if test="entpNo != null">
        #{entpNo,jdbcType=VARCHAR},
      </if>
      <if test="hasGenerateSn != null">
        #{hasGenerateSn,jdbcType=VARCHAR},
      </if>
      <if test="toGrantDate != null">
        #{toGrantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantTime != null">
        #{grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute11 != null">
        #{attribute11,jdbcType=VARCHAR},
      </if>
      <if test="attribute12 != null">
        #{attribute12,jdbcType=VARCHAR},
      </if>
      <if test="attribute13 != null">
        #{attribute13,jdbcType=VARCHAR},
      </if>
      <if test="attribute14 != null">
        #{attribute14,jdbcType=VARCHAR},
      </if>
      <if test="isCompleted != null">
        #{isCompleted,jdbcType=VARCHAR},
      </if>
      <if test="originalTask != null">
        #{originalTask,jdbcType=VARCHAR},
      </if>
      <if test="erpModel != null">
        #{erpModel,jdbcType=VARCHAR},
      </if>
      <if test="reworkSource != null">
        #{reworkSource,jdbcType=VARCHAR},
      </if>
      <if test="firstWarehouseDate != null">
        #{firstWarehouseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstExpectedDeliveryDate != null">
        #{firstExpectedDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastDeliveryDate != null">
        #{lastDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assemblyRemark != null">
        #{assemblyRemark,jdbcType=VARCHAR},
      </if>
        sysdate,
        sysdate,
        'Y'
    </trim>
  </insert>

  <sql id="mergeColumn">
    task_id, task_no, prodplan_no, prodplan_id,
    bom_id, plan_id, plan_uuid,
    is_parts, parts_planno, plan_sequence,
    is_finishen, price_date, software_ver, software_version,
    lead_flag, out_flag, planner, create_by,
    bj_bom_id, task_qty, deman_end_date, planned_finish_date,
    release_date, source_type, prod_status,
    pdm_work_group1_name, pdm_work_group2_name, pdm_work_group3_name,
    stove_type, code_desc, is_discave,
    item_no, item_name,
    external_type, internal_type,
    entp_no, attribute1, prod_address,
    org_id, factory_id, source_sys, enabled_flag,
    generate_flag, entity_id, last_updated_by
  </sql>

  <insert id="insertPsTask">
    insert into ps_task(<include refid="mergeColumn"></include>)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=VARCHAR}, #{item.prodplanNo,jdbcType=VARCHAR}, #{item.prodplanNo,jdbcType=VARCHAR}, #{item.prodplanId,jdbcType=VARCHAR},
      #{item.bomId,jdbcType=VARCHAR}, #{item.planId,jdbcType=VARCHAR}, #{item.planUuid,jdbcType=VARCHAR},
      #{item.isParts,jdbcType=VARCHAR}, #{item.partsPlanno,jdbcType=VARCHAR}, #{item.planSequence,jdbcType=DECIMAL},
      #{item.isFinishen,jdbcType=VARCHAR}, #{item.priceDate,jdbcType=TIMESTAMP},
      #{item.softwareVer,jdbcType=VARCHAR}, #{item.softwareVer,jdbcType=VARCHAR},
      #{item.leadFlag,jdbcType=VARCHAR}, #{item.outFlag,jdbcType=VARCHAR}, #{item.planner,jdbcType=VARCHAR}, #{item.planner,jdbcType=VARCHAR},
      #{item.bjBomId,jdbcType=VARCHAR}, #{item.taskQty,jdbcType=DECIMAL}, #{item.demanEndDate,jdbcType=TIMESTAMP}, #{item.demanEndDate,jdbcType=TIMESTAMP},
      #{item.releaseDate,jdbcType=TIMESTAMP}, #{item.sourceType,jdbcType=CHAR}, #{item.prodStatus,jdbcType=VARCHAR},
      #{item.pdmWorkGroup1Name,jdbcType=VARCHAR}, #{item.pdmWorkGroup2Name,jdbcType=VARCHAR}, #{item.pdmWorkGroup3Name,jdbcType=VARCHAR},
      #{item.stoveType,jdbcType=VARCHAR}, #{item.codeDesc,jdbcType=VARCHAR}, #{item.isDiscave,jdbcType=VARCHAR},
      #{item.itemNo,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
      #{item.productClassName,jdbcType=VARCHAR}, #{item.productSmlclassName,jdbcType=VARCHAR},
      #{item.entpNo,jdbcType=VARCHAR}, #{item.prodUnitname,jdbcType=VARCHAR}, #{item.prodAddress,jdbcType=VARCHAR},
      #{item.orgId,jdbcType=DECIMAL}, #{item.factoryId,jdbcType=DECIMAL},
      'STEP', 'Y', 'Y', 2, 'ja_sync')
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zte.domain.model.sncabind.PsTask">
    update PS_TASK
    <set>
      <if test="taskNo != null">
        TASK_NO = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        CONTRACT_NO = #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="isFinishGood != null">
        IS_FINISH_GOOD = #{isFinishGood,jdbcType=VARCHAR},
      </if>
      <if test="productMode != null">
        PRODUCT_MODE = #{productMode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="isLead != null">
        IS_LEAD = #{isLead,jdbcType=VARCHAR},
      </if>
      <choose>
        <when test="splitFlag != null and splitFlag != ''">
          TASK_QTY = TASK_QTY - CAST (#{attribute9,jdbcType=VARCHAR} AS DECIMAL),
        </when>
        <otherwise>
          <if test="taskQty != null">
            TASK_QTY = #{taskQty,jdbcType=DECIMAL},
          </if>
        </otherwise>
      </choose>
      <if test="completeQty != null">
        COMPLETE_QTY = #{completeQty,jdbcType=DECIMAL},
      </if>
      <if test="taskStatus != null">
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="internalType != null">
        INTERNAL_TYPE = #{internalType,jdbcType=VARCHAR},
      </if>
      <if test="externalType != null">
        EXTERNAL_TYPE = #{externalType,jdbcType=VARCHAR},
      </if>
      <if test="moNo != null">
        MO_NO = #{moNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="softwareVersion != null">
        SOFTWARE_VERSION = #{softwareVersion,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        RELEASE_DATE = #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="plannedFinishDate != null">
        PLANNED_FINISH_DATE = #{plannedFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finalFinishDate != null">
        FINAL_FINISH_DATE = #{finalFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceSys != null">
        SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      LAST_UPDATED_DATE = sysdate,
      <if test="enabledFlag != null">
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=DECIMAL},
      </if>
      <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute6 != null">
        ATTRIBUTE6 = #{attribute6,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute7 != null">
        ATTRIBUTE7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="attribute8 != null">
        ATTRIBUTE8 = #{attribute8,jdbcType=VARCHAR},
      </if>
      <if test="attribute9 != null">
        ATTRIBUTE9 = #{attribute9,jdbcType=DECIMAL},
      </if>
      <if test="attribute10 != null">
        ATTRIBUTE10 = #{attribute10,jdbcType=TIMESTAMP},
      </if>
      <if test="prodplanNo != null">
        PRODPLAN_NO = #{prodplanNo,jdbcType=VARCHAR},
      </if>
      <if test="prodplanId != null">
        PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR},
      </if>
      <if test="bomId != null">
        BOM_ID = #{bomId,jdbcType=VARCHAR},
      </if>
      <if test="planId != null">
        PLAN_ID = #{planId,jdbcType=VARCHAR},
      </if>
      <if test="planUuid != null">
        PLAN_UUID = #{planUuid,jdbcType=VARCHAR},
      </if>
      <if test="isParts != null">
        IS_PARTS = #{isParts,jdbcType=VARCHAR},
      </if>
      <if test="partsPlanno != null">
        PARTS_PLANNO = #{partsPlanno,jdbcType=VARCHAR},
      </if>
      <if test="planSequence != null">
        PLAN_SEQUENCE = #{planSequence,jdbcType=DECIMAL},
      </if>
      <if test="isFinishen != null">
        IS_FINISHEN = #{isFinishen,jdbcType=VARCHAR},
      </if>
      <if test="priceDate != null">
        PRICE_DATE = #{priceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="softwareVer != null">
        SOFTWARE_VER = #{softwareVer,jdbcType=VARCHAR},
      </if>
      <if test="leadFlag != null">
        LEAD_FLAG = #{leadFlag,jdbcType=VARCHAR},
      </if>
      <if test="outFlag != null">
        OUT_FLAG = #{outFlag,jdbcType=VARCHAR},
      </if>
      <if test="planner != null">
        PLANNER = #{planner,jdbcType=VARCHAR},
      </if>
      <if test="bjBomId != null">
        BJ_BOM_ID = #{bjBomId,jdbcType=VARCHAR},
      </if>
      <if test="demanEndDate != null">
        DEMAN_END_DATE = #{demanEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateFlag != null">
        GENERATE_FLAG = #{generateFlag,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        CODE_DESC = #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="stoveType != null">
        STOVE_TYPE = #{stoveType,jdbcType=VARCHAR},
      </if>
      <if test="isDiscave != null">
        IS_DISCAVE = #{isDiscave,jdbcType=VARCHAR},
      </if>
      <if test="prodAddress != null">
        PROD_ADDRESS = #{prodAddress,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup3Name != null">
        PDM_WORK_GROUP3_NAME = #{pdmWorkGroup3Name,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="prodStatus != null">
        PROD_STATUS = #{prodStatus,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup1Name != null">
        PDM_WORK_GROUP1_NAME = #{pdmWorkGroup1Name,jdbcType=VARCHAR},
      </if>
      <if test="pdmWorkGroup2Name != null">
        PDM_WORK_GROUP2_NAME = #{pdmWorkGroup2Name,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        ERP_STATUS = #{erpStatus,jdbcType=VARCHAR},
      </if>
      <if test="entpNo != null">
        ENTP_NO = #{entpNo,jdbcType=VARCHAR},
      </if>
      <if test="hasGenerateSn != null">
        HAS_GENERATE_SN = #{hasGenerateSn,jdbcType=VARCHAR},
      </if>
      <if test="toGrantDate != null">
        TO_GRANT_DATE = #{toGrantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantTime != null">
        GRANT_TIME = #{grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute11 != null">
        ATTRIBUTE11 = #{attribute11,jdbcType=VARCHAR},
      </if>
      <if test="attribute12 != null">
        ATTRIBUTE12 = #{attribute12,jdbcType=VARCHAR},
      </if>
      <if test="attribute13 != null">
        ATTRIBUTE13 = #{attribute13,jdbcType=VARCHAR},
      </if>
      <if test="attribute14 != null">
        ATTRIBUTE14 = #{attribute14,jdbcType=VARCHAR},
      </if>
      <if test="isCompleted != null">
        IS_COMPLETED = #{isCompleted,jdbcType=VARCHAR},
      </if>
      <if test="originalTask != null">
        ORIGINAL_TASK = #{originalTask,jdbcType=VARCHAR},
      </if>
      <if test="erpModel != null">
        ERP_MODEL = #{erpModel,jdbcType=VARCHAR},
      </if>
      <if test="reworkSource != null">
        REWORK_SOURCE = #{reworkSource,jdbcType=VARCHAR},
      </if>
      <if test="firstWarehouseDate != null">
        FIRST_WAREHOUSE_DATE = #{firstWarehouseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstExpectedDeliveryDate != null">
        FIRST_EXPECTED_DELIVERY_DATE = #{firstExpectedDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastDeliveryDate != null">
        LAST_DELIVERY_DATE = #{lastDeliveryDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <choose>
      <when test="taskId != null and taskId != ''">
        TASK_ID = #{taskId}
      </when>
      <otherwise>
        ENABLED_FLAG = 'Y'
        <if test="(taskNo == null or taskNo == '') and (prodplanId == null or prodplanId == '')"> AND 1=2</if>
        <if test="taskNo != null and taskNo != ''"> AND TASK_NO = #{taskNo}</if>
        <if test="prodplanId != null and prodplanId != ''"> AND PRODPLAN_ID = #{prodplanId}</if>
        <if test="notStart != null and notStart != ''"> AND (TASK_STATUS IS NULL or TASK_STATUS = '')</if>
      </otherwise>
    </choose>
  </update>

  <update id="updateByPrimaryKey" parameterType="com.zte.domain.model.sncabind.PsTask">
    update PS_TASK
    set TASK_NO = #{taskNo,jdbcType=VARCHAR},
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
        GRANT_BY = #{grantBy,jdbcType=VARCHAR},
        GRANT_TIME = #{grantTime,jdbcType=DATE},
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL}
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </update>

  <select id = "getFactoryIdByProdplanId"  parameterType="java.util.List" resultType="com.zte.interfaces.sncabind.dto.PsTaskDTO">
    select  p.PRODPLAN_ID AS prodplanId,p.task_no , p.FACTORY_ID AS factoryId from PS_TASK p
    where p.PRODPLAN_ID in
    <foreach item="item" collection="prodplanIds" separator="," open="(" close=")" index="">
    #{item}
    </foreach>
    AND p.ENABLED_FLAG = 'Y'
  </select>


  <select id = "getPsTaskByItemNoList"  resultType="com.zte.interfaces.sncabind.dto.PsTaskDTO">
    <foreach collection="itemNoList" separator="union all" open="" close="" item="itemNo">
      select  p.PRODPLAN_ID AS prodplanId, p.FACTORY_ID AS factoryId , p.item_no AS itemNo ,p.item_name AS itemName ,p.task_no AS taskNo ,p.prodplan_no AS prodplanNo ,
      p.release_date AS releaseDate ,
      cf.factory_name factoryName from PS_TASK p
      left join cf_factory cf on cf.factory_id  = p.factory_id
      where p.ENABLED_FLAG = 'Y'
      and p.item_no  like concat(#{itemNo},'%')
    </foreach>
  </select>

  <select id = "getPsTaskByProdplanIdList"  parameterType="java.util.List" resultType="com.zte.interfaces.sncabind.dto.PsTaskDTO">
    select  p.PRODPLAN_ID AS prodplanId, p.FACTORY_ID AS factoryId , p.item_no AS itemNo ,p.item_name AS itemName ,p.task_no AS taskNo ,p.prodplan_no AS prodplanNo ,
    p.release_date AS releaseDate , p.out_source_factory_code AS outSourceFactoryCode,
    cf.factory_name factoryName from PS_TASK p
    left join cf_factory cf on cf.factory_id  = p.factory_id
    where p.ENABLED_FLAG = 'Y'
    and p.prodplan_id in
    <foreach item="item" collection="prodplanIdList" separator="," open="(" close=")" index="">
      #{item}
    </foreach>
  </select>



  <select id="getStandardModelTaskStatus" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.StandardModelTaskDTO">
    SELECT P.TASK_NO AS taskNo, P.PRODPLAN_ID AS prodplanId, P.ITEM_NO AS itemNo, P.TASK_STATUS AS taskStatus,
    P.TASK_QTY AS taskQty, P.TO_GRANT_DATE AS toGrantDate, P.ATTRIBUTE11 AS attribute11, P.LAST_UPDATED_DATE AS lastUpdatedDate
    FROM PS_TASK P
    WHERE P.SOURCE_SYS = 'WMES'
      AND P.TASK_STATUS != '已取消'
    <if test="params != null and params.taskNo !=null and params.taskNo != ''">
      AND P.TASK_NO = #{params.taskNo}
    </if>
    <if test="params != null and params.beginTime !=null and params.beginTime != '' and params.endTime !=null and params.endTime != ''">
      AND P.LAST_UPDATED_DATE BETWEEN to_timestamp(#{params.beginTime},'yyyy-MM-dd hh24:mi:ss') AND to_timestamp(#{params.endTime},'yyyy-MM-dd hh24:mi:ss')
    </if>
    <if test="params != null and params.infoJIT !=null and params.infoJIT != ''">
      AND P.ATTRIBUTE11 = #{params.infoJIT}
    </if>
    order by P.release_date
  </select>

	<!-- 根据任务和批次号查询任务状态 -->
	<select id="queryByTaskNoAndProdplanId" parameterType="com.zte.domain.model.sncabind.PsTask" resultMap="BaseResultMap">
		SELECT task_status,TASK_NO,PRODPLAN_ID
		FROM PS_TASK
		WHERE TASK_NO IN
        <foreach collection="params.taskNoList" separator="," item="taskNo" open="(" close=")" index="index">
            #{params.taskNoList[${index}]}
        </foreach>
        AND PRODPLAN_ID IN
        <foreach collection="params.prodplanIdList" separator="," item="prodplanId" open="(" close=")" index="index">
            #{params.prodplanIdList[${index}]}
        </foreach>
	</select>


  <select id="getExistingPlanId" resultType="java.lang.String">
    select prodplan_id from ps_task
    where prodplan_id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="getExistingTaskNo" resultType="java.lang.String">
    select task_No from ps_task
    where task_No in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getNextProdplanId" resultType="java.lang.String">
    select nextval('prodplan_id_s')
  </select>

  <insert id="insertDataBatch">
    insert into ps_task(
    task_id, task_no, prodplan_no, prodplan_id,
    bom_id, plan_id, plan_uuid,is_parts,
    parts_planno, plan_sequence,is_finishen, price_date,
    software_ver, software_version,lead_flag, out_flag,
    planner, create_by,bj_bom_id, task_qty,
    deman_end_date, planned_finish_date,release_date,
    source_type, prod_status,
    pdm_work_group1_name, pdm_work_group2_name, pdm_work_group3_name,
    stove_type, code_desc, is_discave,
    item_no, item_name,external_type, internal_type,
    entp_no, attribute1, prod_address,
    org_id, factory_id, source_sys, enabled_flag,generate_flag, entity_id,
    last_updated_by,priority,is_urgency,consign_planno,
    consign_bom_id,consign_bom_no,is_consign,org_bom_id,stock,create_date,
    LAST_UPDATED_DATE, out_source_factory_code, zbjprodplan_no,PRODUCT_CLASS,PRODUCT_SMLCLASS)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=VARCHAR}, #{item.taskNo,jdbcType=VARCHAR}, #{item.prodplanNo,jdbcType=VARCHAR}, #{item.prodplanId,jdbcType=VARCHAR},
      #{item.bomId,jdbcType=VARCHAR}, #{item.planId,jdbcType=VARCHAR}, #{item.planUuid,jdbcType=VARCHAR},#{item.isParts,jdbcType=VARCHAR},
      #{item.partsPlanno,jdbcType=VARCHAR}, #{item.planSequence,jdbcType=DECIMAL},#{item.isFinishen,jdbcType=VARCHAR}, #{item.priceDate,jdbcType=TIMESTAMP},
      #{item.softwareVer,jdbcType=VARCHAR}, #{item.softwareVersion,jdbcType=VARCHAR},#{item.leadFlag,jdbcType=VARCHAR}, #{item.outFlag,jdbcType=VARCHAR},
      #{item.planner,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR},#{item.bjBomId,jdbcType=VARCHAR}, #{item.taskQty,jdbcType=DECIMAL},
      #{item.demanEndDate,jdbcType=TIMESTAMP}, #{item.plannedFinishDate,jdbcType=TIMESTAMP},#{item.releaseDate,jdbcType=TIMESTAMP},
      #{item.sourceType,jdbcType=CHAR}, #{item.prodStatus,jdbcType=VARCHAR},
      #{item.pdmWorkGroup1Name,jdbcType=VARCHAR}, #{item.pdmWorkGroup2Name,jdbcType=VARCHAR}, #{item.pdmWorkGroup3Name,jdbcType=VARCHAR},
      #{item.stoveType,jdbcType=VARCHAR}, #{item.codeDesc,jdbcType=VARCHAR}, #{item.isDiscave,jdbcType=VARCHAR},
      #{item.itemNo,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
      #{item.externalType,jdbcType=VARCHAR}, #{item.internalType,jdbcType=VARCHAR},
      #{item.entpNo,jdbcType=VARCHAR}, #{item.attribute1,jdbcType=VARCHAR}, #{item.prodAddress,jdbcType=VARCHAR},
      #{item.orgId,jdbcType=DECIMAL}, #{item.factoryId,jdbcType=DECIMAL},#{item.sourceSys,jdbcType=VARCHAR},'Y', #{item.generateFlag,jdbcType=VARCHAR}, 2,
      #{item.lastUpdatedBy,jdbcType=VARCHAR},#{item.priority,jdbcType=INTEGER},#{item.isUrgency,jdbcType=INTEGER},#{item.consignPlanno,jdbcType=VARCHAR},
      #{item.consignBomId,jdbcType=VARCHAR},#{item.consignBomNo,jdbcType=VARCHAR},#{item.isConsign,jdbcType=INTEGER},#{item.orgBomId,jdbcType=VARCHAR},
      #{item.stock,jdbcType=VARCHAR},sysdate,sysdate,
      #{item.outSourceFactoryCode,jdbcType=VARCHAR},#{item.zbjprodplanNo,jdbcType=VARCHAR},
      #{item.productClass,jdbcType=VARCHAR},#{item.productSmlClass,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="queryTaskByTaskNoList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND TASK_NO IN
    <foreach collection="taskNoList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
    <if test="sourceSys != null">
      and SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR}
    </if>
  </select>

  <!-- 获取料单子卡信息-->
  <select id="querySiMeItemNo" resultMap="BaseResultMap">
    SELECT T.PRODUCT_CODE,T1.ITEM_CODE
    FROM B_BOM_HEADER T
    LEFT JOIN B_BOM_DETAIL T1 ON t.BOM_HEADER_ID = t1.BOM_HEADER_ID
    LEFT JOIN BS_ITEM_INFO B ON T.ITEM_CODE = B.ITEM_NO
    WHERE T.ENABLED_FLAG = 'Y'
    AND B.ENABLED_FLAG = 'Y'
    AND T1.ENABLED_FLAG = 'Y'
    and B.ITEM_TYPE = '1'
    and T.PRODUCT_CODE in
    <foreach collection="productList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateBatchPsTask">
    <foreach collection="psTaskList" separator=";" item="item" >
      update PS_TASK t
      set
      <if test="item.taskQty != null">
        t.task_qty = #{item.taskQty, jdbcType=DECIMAL},
      </if>
      <if test="item.leadFlag != null">
        t.lead_flag = #{item.leadFlag, jdbcType= VARCHAR},
      </if>
      <if test="item.outSourceFactoryCode != null">
        t.out_source_factory_code = #{item.outSourceFactoryCode, jdbcType= VARCHAR},
      </if>
      <if test="item.zbjprodplanNo != null">
        t.zbjprodplan_no = #{item.zbjprodplanNo, jdbcType= VARCHAR},
      </if>
      <if test="item.consignplanNo != null">
        t.CONSIGN_PLANNO = #{item.consignplanNo, jdbcType=VARCHAR},
      </if>
      <if test="item.consignBomNo != null">
        t.CONSIGN_BOM_NO = #{item.consignBomNo, jdbcType=VARCHAR},
      </if>
      <if test="item.consignBomId != null">
        t.CONSIGN_BOM_ID = #{item.consignBomId, jdbcType=VARCHAR},
      </if>
      <if test="item.isConsign != null">
        t.IS_CONSIGN = #{item.isConsign, jdbcType=INTEGER},
      </if>
      <if test="item.partsPlanno != null">
        t.PARTS_PLANNO = #{item.partsPlanno, jdbcType = VARCHAR},
      </if>
      <if test="item.demanEndDate != null">
        t.DEMAN_END_DATE = #{item.demanEndDate, jdbcType = TIMESTAMP},
      </if>
      <if test="item.softwareVersion != null">
        t.SOFTWARE_VERSION = #{item.softwareVersion, jdbcType = VARCHAR},
      </if>
      <if test="item.itemNo != null">
        t.item_no = #{item.itemNo, jdbcType = VARCHAR},
      </if>
      <if test="item.itemName != null">
        t.item_name = #{item.itemName, jdbcType = VARCHAR},
      </if>
      <if test="item.bomId != null">
        t.bom_id = #{item.bomId,jdbcType=VARCHAR},
      </if>
      <if test="item.taskStatus != null">
        t.task_status = #{item.taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="item.firstWarehouse90Date != null">
        t.task_status = #{item.firstWarehouse90Date,jdbcType=VARCHAR},
      </if>
      <if test="item.partFirstWarehouseDate != null">
        t.task_status = #{item.partFirstWarehouseDate,jdbcType=VARCHAR},
      </if>
      <if test="item.partFirstWarehouse90Date != null">
        t.task_status = #{item.partFirstWarehouse90Date,jdbcType=VARCHAR},
      </if>
      t.last_updated_date = sysdate
      where enabled_flag = 'Y'
      and t.TASK_NO = #{item.taskNo, jdbcType = VARCHAR}
    </foreach>
  </update>

  <select id="queryPsTaskBatch" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT PRODPLAN_ID,ATTRIBUTE1
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID IN
    <foreach collection="prodPlanIdList" open="(" item="item" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getBomNoByProdplanId" resultType="java.lang.String">
    SELECT ITEM_NO
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID = #{prodplanId}
  </select>

  <select id="getLeadFlagByProdplanId" resultType="java.lang.String">
    select lead_flag FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID = #{prodplanId}
    limit 1
  </select>

  <select id="getTaskQtyByProdplanId" parameterType="java.util.List" resultMap="BaseResultMap">
    select task_qty, prodplan_id FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID IN
    <foreach collection="prodPlanIdList" open="(" item="item" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectGetDateByProdplanNo"
          resultType="com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO">
    select prodplan_id prodplanId, prodplan_no prodplanNo, get_date getDate
    from ps_task
    where enabled_flag = 'Y'
    and prodplan_no in
    <foreach collection="planNos" open="(" item="planNo" separator="," close=")">
      #{planNo}
    </foreach>
  </select>

  <select id="selectErpStatusByProdplanId" parameterType="java.util.List" resultType="com.zte.interfaces.sncabind.dto.PsTaskDTO">
    select prodplan_id, erp_status
    from ps_task
    where enabled_flag = 'Y'
    and prodplan_id in
    <foreach collection="prodplanList" open="(" item="item" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="queryFactoryIdByProdIdList" resultMap="BaseResultMap">
    select PRODPLAN_ID,FACTORY_ID
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID IN
    <foreach collection="prodPlanIdList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
  </select>

  <select id="queryFactoryIdByTaskNoList" resultMap="BaseResultMap">
    select PRODPLAN_ID,FACTORY_ID,TASK_NO,ITEM_NO
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND TASK_NO IN
    <foreach collection="taskNoList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectPsTaskByProdIdSet" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ps_task t
    where t.enabled_flag ='Y' and t.prodplan_id in
    <foreach collection="prodIdSet" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getPsTaskByTaskNo" parameterType="java.lang.String" resultType="com.zte.interfaces.sncabind.dto.PsTaskDTO">
    select t.task_no, t.prodplan_id, t.release_date, t.get_date,
      t.complete_qty, t.first_warehouse_date, t.task_qty, t.item_no, t.prodplan_no,
      t.factory_id, h.route_detail, v.description_chin factory
    from ps_task t
    left join ct_basic c
      on t.item_no != ''
      and t.item_no is not null
      and t.item_no = c.item_or_task
      and c.craft_status = '已提交'
    left join ct_route_head h
      on c.craft_id = h.craft_id
    left join sys_lookup_values v
      on v.lookup_type = 6682
      and t.factory_id::VARCHAR = v.lookup_meaning
    where t.enabled_flag = 'Y'
      and t.task_no = #{taskNo,jdbcType=VARCHAR}
      and t.source_sys = 'STEP'
    order by c.last_updated_date desc, h.last_updated_date desc
    limit 1
  </select>

  <select id="getFactoryIdByProdId" parameterType="java.util.List" resultMap="BaseResultMap">
    select PRODPLAN_ID,FACTORY_ID,item_no, item_name, task_no, org_id, entity_id
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND PRODPLAN_ID IN
    <foreach collection="prodPlanIdList" open="(" separator="," item="item" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectListByPartFirstWarehouseDate" resultType="com.zte.domain.model.sncabind.PsTask">
    select task_no,prodplan_no,first_warehouse_date,part_first_warehouse_date,first_warehouse90_date,part_first_warehouse90_date,get_date
    from ps_task where
    GET_DATE IS NOT NULL
    AND first_warehouse_date IS NOT NULL
    AND to_char(part_first_warehouse_date, 'yyyy-mm-dd') = #{partFirstWarehouseDate}
  </select>

  <select id="selectOneByProdplanNo" resultType="com.zte.domain.model.sncabind.PsTask">
    select task_no,prodplan_no,first_warehouse_date,part_first_warehouse_date,first_warehouse90_date,part_first_warehouse90_date,get_date
    from ps_task where
    GET_DATE IS NOT NULL
    AND first_warehouse_date IS NOT NULL
    and prodplan_no = #{prodplanNo} limit 1
  </select>

  <select id="selectBrandChangeStatusByTaskNo" resultType="com.zte.interfaces.sncabind.dto.TaskBrandChangeStatusDTO">
    select task_no, prodplan_id,
    decode(infor_exe, '1', 0, 1) modifiable,
    decode(infor_exe, '1', '已产生套料单或调拨单', '可修改') reason
    from ps_task
    where ENABLED_FLAG = 'Y'
    AND task_no = #{taskNo} limit 1
  </select>

  <select id="batchUpdateInforExe">
    update PS_TASK set
    INFOR_EXE=#{inforExe,jdbcType=VARCHAR},
    LAST_UPDATED_DATE = sysdate,
    LAST_UPDATED_BY=#{lastUpdatedBy}
    where ENABLED_FLAG = 'Y'
    and PRODPLAN_ID in
    <foreach collection="sourceTaskList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectItemCodeByProd" resultType="com.zte.domain.model.sncabind.PsTask">
    select t.prodplan_id, t.item_no
    from ps_task t
    where t.prodplan_id in
    <foreach collection="prods" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getSubTaskByTaskNoList" resultType="com.zte.interfaces.dto.task.PsTaskTreeDTO">
    select PRODPLAN_ID, PARTS_PLANNO,TASK_NO,ITEM_NO,FACTORY_ID
    from PS_TASK
    where ENABLED_FLAG = 'Y'
    and parts_planno in (
    select decode(nvl(PARTS_PLANNO,task_no),'',task_NO,nvl(PARTS_PLANNO,task_no)) from ps_task
    where ENABLED_FLAG = 'Y'
    and task_no in
    <foreach collection="taskNos" item="taskNo" open="(" separator="," close=")">
      #{taskNo}
    </foreach>
    )
  </select>

  <select id="getWmesTaskNo" resultType="java.lang.String">
    select task_No from ps_task p
    where p.source_sys = 'WMES' and
    task_No in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="getPsTaskListByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.<include refid="Base_Column_List"/>
    from ps_task t where 1=1 and enabled_flag='Y'
    <if test="createDateStart != null"> <![CDATA[and t.CREATE_DATE >= #{createDateStart}]]></if>
    <if test="createDateEnd != null"> <![CDATA[and t.CREATE_DATE <= #{createDateEnd}]]></if>
    <if test="factoryId != null"> and t.FACTORY_ID = #{factoryId}::numeric</if>
    <if test="sourceSys != null and sourceSys != ''">and t.source_sys=#{sourceSys}</if>
    <if test="notInTaskStatus != null and notInTaskStatus != ''">and t.TASK_STATUS != #{notInTaskStatus}</if>
    <if test="(sourceSys == null or sourceSys == '')
         and (notInTaskStatus == null or notInTaskStatus == '')
         and (factoryId == null or factoryId == '')
       and (createDateStart == null or createDateStart == '')
       and (createDateEnd == null or createDateEnd == '')">
      and 1=2
    </if>
    order by t.CREATE_DATE,t.TASK_ID
  </select>

  <update id="updateBatchPsTaskByCondition">
    <foreach collection="psTaskList" separator=";" item="item" >
      update PS_TASK t
      set
      <if test="item.taskQty != null">
        t.task_qty = #{item.taskQty, jdbcType=DECIMAL},
      </if>
      <if test="item.taskStatus != null">
        t.task_status = #{item.taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="item.factoryId != null">
        t.FACTORY_ID = #{item.factoryId,jdbcType=DECIMAL},
      </if>
      t.remark = #{item.remark,jdbcType=VARCHAR}
      where enabled_flag = 'Y'
      and t.TASK_ID = #{item.taskId, jdbcType = VARCHAR}
    </foreach>
  </update>
  <update id="batchUpdateByPK">
    <foreach collection="list" item="item" separator=";">
      UPDATE ps_task
      <set>
        <if test="item.taskNo != null">task_no = #{item.taskNo},</if>
        <if test="item.contractNo != null">contract_no = #{item.contractNo},</if>
        <if test="item.itemNo != null">item_no = #{item.itemNo},</if>
        <if test="item.isFinishGood != null">is_finish_good = #{item.isFinishGood},</if>
        <if test="item.productMode != null">product_mode = #{item.productMode},</if>
        <if test="item.itemName != null">item_name = #{item.itemName},</if>
        <if test="item.isLead != null">is_lead = #{item.isLead},</if>
        <if test="item.taskQty != null">task_qty = #{item.taskQty},</if>
        <if test="item.completeQty != null">complete_qty = #{item.completeQty},</if>
        <if test="item.taskStatus != null">task_status = #{item.taskStatus},</if>
        <if test="item.internalType != null">internal_type = #{item.internalType},</if>
        <if test="item.externalType != null">external_type = #{item.externalType},</if>
        <if test="item.moNo != null">mo_no = #{item.moNo},</if>
        <if test="item.type != null">type = #{item.type},</if>
        <if test="item.softwareVersion != null">software_version = #{item.softwareVersion},</if>
        <if test="item.releaseDate != null">release_date = #{item.releaseDate},</if>
        <if test="item.plannedFinishDate != null">planned_finish_date = #{item.plannedFinishDate},</if>
        <if test="item.finalFinishDate != null">final_finish_date = #{item.finalFinishDate},</if>
        <if test="item.sourceSys != null">source_sys = #{item.sourceSys},</if>
        <if test="item.productType != null">product_type = #{item.productType},</if>
        <if test="item.remark != null">remark = #{item.remark},</if>
        <if test="item.lastUpdatedBy != null">last_updated_by = #{item.lastUpdatedBy},</if>
        <if test="item.orgId != null">org_id = #{item.orgId},</if>
        <if test="item.factoryId != null">factory_id = #{item.factoryId},</if>
        <if test="item.entityId != null">entity_id = #{item.entityId},</if>
        <if test="item.prodplanNo != null">prodplan_no = #{item.prodplanNo},</if>
        <if test="item.prodplanId != null">prodplan_id = #{item.prodplanId},</if>
        <if test="item.bomId != null">bom_id = #{item.bomId},</if>
        <if test="item.planId != null">plan_id = #{item.planId},</if>
        <if test="item.planUuid != null">plan_uuid = #{item.planUuid},</if>
        <if test="item.isParts != null">is_parts = #{item.isParts},</if>
        <if test="item.partsPlanno != null">parts_planno = #{item.partsPlanno},</if>
        <if test="item.planSequence != null">plan_sequence = #{item.planSequence},</if>
        <if test="item.priceDate != null">price_date = #{item.priceDate},</if>
        <if test="item.softwareVer != null">software_ver = #{item.softwareVer},</if>
        <if test="item.leadFlag != null">lead_flag = #{item.leadFlag},</if>
        <if test="item.outFlag != null">out_flag = #{item.outFlag},</if>
        <if test="item.planner != null">planner = #{item.planner},</if>
        <if test="item.bjBomId != null">bj_bom_id = #{item.bjBomId},</if>
        <if test="item.demanEndDate != null">deman_end_date = #{item.demanEndDate},</if>
        <if test="item.generateFlag != null">generate_flag = #{item.generateFlag},</if>
        <if test="item.codeDesc != null">code_desc = #{item.codeDesc},</if>
        <if test="item.stoveType != null">stove_type = #{item.stoveType},</if>
        <if test="item.isDiscave != null">is_discave = #{item.isDiscave},</if>
        <if test="item.prodAddress != null">prod_address = #{item.prodAddress},</if>
        <if test="item.pdmWorkGroup3Name != null">pdm_work_group3_name = #{item.pdmWorkGroup3Name},</if>
        <if test="item.sourceType != null">source_type = #{item.sourceType},</if>
        <if test="item.prodStatus != null">prod_status = #{item.prodStatus},</if>
        <if test="item.pdmWorkGroup1Name != null">pdm_work_group1_name = #{item.pdmWorkGroup1Name},</if>
        <if test="item.pdmWorkGroup2Name != null">pdm_work_group2_name = #{item.pdmWorkGroup2Name},</if>
        <if test="item.erpStatus != null">erp_status = #{item.erpStatus},</if>
        <if test="item.entpNo != null">entp_no = #{item.entpNo},</if>
        <if test="item.hasGenerateSn != null">has_generate_sn = #{item.hasGenerateSn},</if>
        <if test="item.toGrantDate != null">to_grant_date = #{item.toGrantDate},</if>
        <if test="item.grantTime != null">grant_time = #{item.grantTime},</if>
        <if test="item.isCompleted != null">is_completed = #{item.isCompleted},</if>
        <if test="item.originalTask != null">original_task = #{item.originalTask},</if>
        <if test="item.erpModel != null">erp_model = #{item.erpModel},</if>
        <if test="item.reworkSource != null">rework_source = #{item.reworkSource},</if>
        <if test="item.firstWarehouseDate != null">first_warehouse_date = #{item.firstWarehouseDate},</if>
        <if test="item.firstExpectedDeliveryDate != null">first_expected_delivery_date = #{item.firstExpectedDeliveryDate},</if>
        <if test="item.lastDeliveryDate != null">last_delivery_date = #{item.lastDeliveryDate},</if>
        <if test="item.receiveSendMaterialsFlag != null">receive_send_materials_flag = #{item.receiveSendMaterialsFlag},</if>
        <if test="item.assemblyRemark != null">assembly_remark = #{item.assemblyRemark},</if>
        <if test="item.inforExe != null">infor_exe = #{item.inforExe},</if>
        <if test="item.consignPlanno != null">consign_planno = #{item.consignPlanno},</if>
        <if test="item.consignBomId != null">consign_bom_id = #{item.consignBomId},</if>
        <if test="item.consignBomNo != null">consign_bom_no = #{item.consignBomNo},</if>
        <if test="item.isConsign != null">is_consign = #{item.isConsign},</if>
        <if test="item.orgBomId != null">org_bom_id = #{item.orgBomId},</if>
        <if test="item.stock != null">stock = #{item.stock},</if>
        <if test="item.getDate != null">get_date = #{item.getDate},</if>
        <if test="item.grantBy != null">grant_by = #{item.grantBy},</if>
        <if test="item.firstWarehouse90Date != null">first_warehouse90_date = #{item.firstWarehouse90Date},</if>
        <if test="item.partFirstWarehouseDate != null">part_first_warehouse_date = #{item.partFirstWarehouseDate},</if>
        <if test="item.partFirstWarehouse90Date != null">part_first_warehouse90_date = #{item.partFirstWarehouse90Date},</if>
        <if test="item.outSourceFactoryCode != null">out_source_factory_code = #{item.outSourceFactoryCode},</if>
        <if test="item.zbjprodplanNo != null">zbjprodplan_no = #{item.zbjprodplanNo},</if>
        <if test="item.confirmationStatus != null">confirmation_status = #{item.confirmationStatus},</if>
        <if test="item.attribute2 != null">attribute2 = #{item.attribute2},</if>
        last_updated_date = sysdate
      </set>
      WHERE task_id = #{item.taskId}
    </foreach>
  </update>

  <update id="updateConfirmationStatus">
    update PS_TASK t
      set confirmation_status = #{confirmationStatus}
      where t.enabled_flag = 'Y'
      and t.task_no = #{taskNo}
  </update>

  <select id="getTaskInfoByTaskNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from PS_TASK
    where enabled_flag = 'Y'
    and task_no = #{taskNo}
    limit 1
  </select>
  <select id="selectProdPlanIDWitnSpare" resultType="com.zte.domain.model.sncabind.PsTask">
    select t.task_no,t.prodplan_id, t.org_id ,t.item_no,t.infor_exe
    from ps_task t
    where t.prodplan_id in
    <foreach collection="prodPlanIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="inforList != null and inforList.size > 0"> and t.infor_exe in
      <foreach item="item" collection="inforList" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectProdplanNoByProdplanId" resultType="com.zte.domain.model.sncabind.PsTask">
    select prodplan_id prodplanId, prodplan_no prodplanNo
    from ps_task
    where enabled_flag = 'Y'
    and prodplan_id in
    <foreach collection="prodPlanIdList" open="(" item="planId" separator="," close=")">
      #{planId}
    </foreach>
  </select>
    <select id="pageBySourceSysAndTaskStatus" resultType="com.zte.domain.model.sncabind.PsTask">
      select <include refid="Base_Column_List"/>
      FROM PS_TASK
      WHERE ENABLED_FLAG = 'Y'
      <if test="sourceSys != null">
        and SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR}
      </if>
      <if test="taskStatus != null">
        and task_status = #{taskStatus,jdbcType=VARCHAR}
      </if>
      and last_updated_date <![CDATA[ >= ]]> #{beginDate,jdbcType=TIMESTAMP}
    </select>

  <select id="queryTaskList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    FROM PS_TASK
    WHERE ENABLED_FLAG = 'Y'
    AND (TASK_NO, ORG_ID) IN
    <foreach collection="taskCancelableDTOList" open="(" separator="," item="item" close=")">
      (#{item.taskNo}, #{item.orgId})
    </foreach>
    <if test="sourceSys != null">
      and SOURCE_SYS = #{sourceSys,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="listAlibabaTask" resultType="com.zte.interfaces.VO.OrderTaskInfoVO">
    select
    t.task_id,t.task_no as order_no,t1.business_scene,t.task_qty as quantity,t1.expected_completed_date as date_expected_completion,
    t1.prepare_completed_date as date_estimated_completion,t1.customer_part_type as category,t1.customer_no,
    t1.self_supply_prepare_date as date_material_estimated_prepared,t1.full_prepare_date as date_all_material_estimated_prepared,
    t1.prepare_product_date as date_scheduled_production,t.last_updated_date
    FROM ps_task t
    left join ps_task_extended t1 on t.task_no = t1.task_no
    where
    t.enabled_flag='Y' and t1.enabled_flag ='Y' and t.task_status in ('已开工','已排产','已发放')
    <if test="customerNos !=null and customerNos.size() > 0">
      and t1.customer_no in
      <foreach collection="customerNos" open="(" close=")" item="customerNo" separator=",">
        #{customerNo}
      </foreach>
    </if>
    <if test="taskNos !=null and taskNos.size() > 0">
      and t.task_no in
      <foreach collection="taskNos" open="(" close=")" item="taskNo" separator=",">
        #{taskNo}
      </foreach>
    </if>
    <if test='scheduleFlag != null and scheduleFlag == "Y"'>
      <if test="taskId != null and taskId != ''">
        and not exists ( select 1 from ps_task a where t.task_id = a.task_id and a.task_id &lt;= #{taskId} and a.last_updated_date = #{startTime})
      </if>
      and t.last_updated_date >= #{startTime}
      order by t.last_updated_date, t.task_id
      limit #{limit}
    </if>

  </select>
  <select id="queryTaskQty" resultType="com.zte.interfaces.dto.aps.EntityQueryDTO">
    SELECT
        pt.org_id AS stockOrgId,
        pt.task_no AS entityNo,
        psmd.task_qty AS qty
    FROM
        push_std_model_data psmd
    INNER JOIN
        ps_task pt ON psmd.task_no = pt.task_no
    WHERE
        psmd.enabled_flag = 'Y'
        AND pt.enabled_flag = 'Y'

        <!-- curr_process > beginProcess -->
        <if test="beginProcess != null">
            AND CAST(psmd.curr_process AS INTEGER) <![CDATA[ > ]]> #{beginProcess}
        </if>

        <!-- curr_process < endProcess -->
        <if test="endProcess != null">
            AND CAST(psmd.curr_process AS INTEGER) <![CDATA[ < ]]> #{endProcess}
        </if>

        <!-- (org_id, task_no) IN collection -->
        AND (pt.org_id, pt.task_no) IN
        <foreach collection="orgIdTaskNoPairs" item="pair" separator="," open="(" close=")">
            (#{pair.stockOrgId}, #{pair.entityNo})
        </foreach>
  </select>
</mapper>