<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushBoardDataDetailRepository">

    <sql id="Base_Column_List">
      sn,
      prodplan_id,
      error_msg,
      create_by,
      create_date,
      last_updated_by,
      last_updated_date,
      enabled_flag
    </sql>

    <update id="update">
        update push_board_data_detail set
        push_status = #{pushStatus},
        error_msg = #{errorMsg},
        last_updated_date = sysdate
        where sn = #{sn}
    </update>

    <update id="updateStatusBySn">
        update push_board_data_detail set
        push_status = #{pushStatus},
        last_updated_date = sysdate
        where sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getExistSn" resultType="java.lang.String">
        select sn from push_board_data_detail t
        where t.enabled_flag = 'Y'
        and t.sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into push_board_data_detail (
        sn,
        prodplan_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
         (
          #{item.sn},
          #{item.prodplanId})
        </foreach>
    </insert>

    <!--增量分批查询待推送条码-->
    <select id="getNeedPushSn" resultType="com.zte.interfaces.dto.PushBoardDataDetailDTO">
        select t.sn, t.create_date from push_board_data_detail t
        where t.enabled_flag = 'Y'
        and t.create_date >= #{startTime}
        and t.push_status = '0'
        <if test="sn != null and sn != ''">
            and not exists ( select 1 from push_board_data_detail a where t.sn = a.sn and a.sn &lt;= #{sn} and a.create_date = #{startTime})
        </if>
        order by t.create_date, t.sn
        limit #{limit}
    </select>

    <select id="getNotPushedList" resultType="com.zte.interfaces.dto.PushBoardDataDetailDTO">
        select t.sn, t.last_updated_date, t.push_status
        from push_board_data_detail t
        where t.enabled_flag = 'Y'
        and t.last_updated_date >= #{startTime}
        and t.push_status in ('0', '9')
        <if test="sn != null and sn != ''">
            and not exists ( select 1 from push_board_data_detail a where t.sn = a.sn and a.sn &lt;= #{sn} and a.last_updated_date = #{startTime})
        </if>
        order by t.last_updated_date, t.sn
        limit #{limit}
    </select>


    <!-- Started by AICoder, pid:pfbf9edd4dse51614e61083630aa7c2e72a92776 -->
    <select id="queryNeedInsertProcessList" resultType="com.zte.interfaces.dto.PushBoardDataDetailDTO"
            parameterType="com.zte.interfaces.dto.PushBoardDataProcessDTO">
        select t.SN, t.PRODPLAN_ID
        FROM PUSH_BOARD_DATA_HEAD H
        JOIN PUSH_BOARD_DATA_DETAIL t ON h.PRODPLAN_ID = t.PRODPLAN_ID
        WHERE t.ENABLED_FLAG = 'Y'
        AND H.ENABLED_FLAG = 'Y'
        <if test="customerName != null and customerName != ''">
            AND h.CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR}
        </if>
        <if test="beforeDay != null">
            AND t.create_date >= sysdate - #{beforeDay,jdbcType=INTEGER}
        </if>
        AND t.push_status = '0'
        and not exists
        (   SELECT 1
        FROM push_board_data_process d
        WHERE t.sn = d.sn
        AND d.business_type IN
        <foreach collection="businessTypeList" open="(" close=")" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND D.ENABLED_FLAG = 'Y'
        group by d.SN <![CDATA[having count(1) >= #{businessTypeListSize}]]>
        )
        LIMIT #{rows}
    </select>
    <!-- Ended by AICoder, pid:2fbf9odd4d9e51614e61083630aa7c2e72a92776 -->

    <select id="getPushDoneProdplanId" resultType="java.lang.String">
        select a.prodplan_id from push_board_data_detail a,
        (
        <foreach collection="headList" item="item" separator="union all" >
            select #{item.prodplanId} prodplan_id, #{item.taskQty}::NUMERIC task_qty
        </foreach>
        ) b
        where a.enabled_flag = 'Y'
        and a.prodplan_id = b.prodplan_id
        and a.push_status = '1'
        group by a.prodplan_id
        having count(1) >= max(b.task_qty)
    </select>

</mapper>