<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.SpSpecialityParamItemRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.domain.model.SpSpecialityParamItem" id="spSpecialityParamItemMap">
        <result property="id" column="id"/>
        <result property="specialityParamId" column="speciality_param_id"/>
        <result property="itemData" column="item_data"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="macStart" column="mac_start"/>
        <result property="macEnd" column="mac_end"/>
        <result property="gponStart" column="gpon_start"/>
        <result property="gponEnd" column="gpon_end"/>
        <result property="isUsed" column="is_used"/>
    </resultMap>

    <insert id="insert">
        insert into sp_speciality_param_item (id, speciality_param_id, item_data, create_by, create_date, last_updated_by,
        last_updated_date, enabled_flag, mac_start, mac_end, gpon_start, gpon_end, is_used )
        values (#{id}, #{specialityParamId}, #{itemData}, #{createBy}, #{createDate}, #{lastUpdatedBy},
        #{lastUpdatedDate}, #{enabledFlag}, #{macStart}, #{macEnd}, #{gponStart}, #{gponEnd}, #{isUsed} )
    </insert>

    <insert id="insertBatch">
        insert into sp_speciality_param_item (id, speciality_param_id, barcode, item_data, create_by, last_updated_by,
        mac_start, mac_end, gpon_start, gpon_end, is_used) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.specialityParamId},#{item.barcode},#{item.itemData},#{item.createBy},#{item.lastUpdatedBy},
            #{item.macStart}, #{item.macEnd}, #{item.gponStart}, #{item.gponEnd}, #{item.isUsed})
        </foreach>
    </insert>

    <insert id="insertBatchTemp">
        insert into sp_speciality_param_item_temp (id, speciality_param_id, barcode, item_data, create_by, last_updated_by,
        mac_start, mac_end, gpon_start, gpon_end, is_used) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.specialityParamId},#{item.barcode},#{item.itemData},#{item.createBy},#{item.lastUpdatedBy},
            #{item.macStart}, #{item.macEnd}, #{item.gponStart}, #{item.gponEnd}, #{item.isUsed})
        </foreach>
    </insert>

    <update id="updateById">
        update sp_speciality_param_item
        <set>
            <!--  last_updated_date = SYSDATE,
                  last_updated_by = #{lastUpdatedBy},
             -->
            <if test="id != null and id != ''" >
                id = #{id},
            </if>
            <if test="specialityParamId != null and specialityParamId != ''" >
                speciality_param_id = #{specialityParamId},
            </if>
            <if test="itemData != null and itemData != ''" >
                item_data = #{itemData},
            </if>
            <if test="createBy != null and createBy != ''" >
                create_by = #{createBy},
            </if>
            <if test="createDate != null and createDate != ''" >
                create_date = #{createDate},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''" >
                last_updated_by = #{lastUpdatedBy},
            </if>
            <if test="lastUpdatedDate != null and lastUpdatedDate != ''" >
                last_updated_date = #{lastUpdatedDate},
            </if>
            <if test="enabledFlag != null and enabledFlag != ''" >
                enabled_flag = #{enabledFlag},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="update">
        update sp_speciality_param_item
        <set>
            <!--  last_updated_date = SYSDATE,
                  last_updated_by = #{lastUpdatedBy},
             -->
            <if test="id != null and id != ''" >
                id = #{id},
            </if>
            <if test="specialityParamId != null and specialityParamId != ''" >
                speciality_param_id = #{specialityParamId},
            </if>
            <if test="itemData != null and itemData != ''" >
                item_data = #{itemData},
            </if>
            <if test="createBy != null and createBy != ''" >
                create_by = #{createBy},
            </if>
            <if test="createDate != null and createDate != ''" >
                create_date = #{createDate},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''" >
                last_updated_by = #{lastUpdatedBy},
            </if>
            <if test="lastUpdatedDate != null and lastUpdatedDate != ''" >
                last_updated_date = #{lastUpdatedDate},
            </if>
            <if test="enabledFlag != null and enabledFlag != ''" >
                enabled_flag = #{enabledFlag},
            </if>
            <if test="isUsed != null and isUsed != ''" >
                is_used = #{isUsed},
            </if>
        </set>
        <where>
            <if test="id != null and id != ''" >
                and id = #{id}
            </if>
            <if test="specialityParamId != null and specialityParamId != ''" >
                and speciality_param_id = #{specialityParamId}
            </if>
            <if test="macStart != null and macStart != ''" >
                and mac_start = #{macStart}
            </if>
            <if test="macEnd != null and macEnd != ''" >
                and mac_end = #{macEnd}
            </if>
            <if test="gponStart != null and gponStart != ''" >
                and gpon_start = #{gponStart}
            </if>
            <if test="gponEnd != null and gponEnd != ''" >
                and gpon_end = #{gponEnd}
            </if>
        </where>
    </update>

    <update id="updateIsUsedByIds">
        update sp_speciality_param_item set is_used = 1 where id id
        <foreach collection="ids" open="(" separator="," close=")" index="index" item="item">
            #{item}
        </foreach>
    </update>
    <update id="updateTemp2Item">
        update sp_speciality_param_item set is_used = 1
        from
        ( SELECT * from sp_speciality_param_item_temp ) spit
        where sp_speciality_param_item.mac_start = spit.mac_start
        and sp_speciality_param_item.mac_end = spit.mac_end
        and sp_speciality_param_item.gpon_start = spit.gpon_start
        and sp_speciality_param_item.gpon_end = spit.gpon_end
    </update>

    <delete id="deleteByIds">
        delete from sp_speciality_param_item where id in
        <foreach collection="ids" item="id" index="index"  open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTemp">
        TRUNCATE TABLE sp_speciality_param_item_temp
    </delete>

    <select id="countPage" resultType="java.lang.Long">
        select count(1) from sp_speciality_param_item
        <include refid="pageParam"/>
    </select>
    <select id="queryPage" resultMap="spSpecialityParamItemMap">
        select * from sp_speciality_param_item
        <include refid="pageParam"/>
        order by mac_start
        limit  #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </select>
    <sql id="pageParam">
        <where>
            enabled_flag= 'Y'
            <if test="id != null and id != ''" >
                and id = #{id}
            </if>
            <if test="specialityParamId != null and specialityParamId != ''" >
                and speciality_param_id = #{specialityParamId}
            </if>
            <if test="itemData != null and itemData != ''" >
                and item_data = #{itemData}
            </if>
            <if test="createBy != null and createBy != ''" >
                and create_by = #{createBy}
            </if>
            <if test="createDate != null and createDate != ''" >
                and create_date = #{createDate}
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''" >
                and last_updated_by = #{lastUpdatedBy}
            </if>
            <if test="lastUpdatedDate != null and lastUpdatedDate != ''" >
                and last_updated_date = #{lastUpdatedDate}
            </if>
            <if test="enabledFlag != null and enabledFlag != ''" >
                and enabled_flag = #{enabledFlag}
            </if>
            <if test="isUsed != null" >
                and is_used = #{isUsed}
            </if>
            <!--<if test="startTime != null and endTime != null">
               and create_date between #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            </if>-->
        </where>
    </sql>

    <select id="selectById" resultMap="spSpecialityParamItemMap">
        select * from sp_speciality_param_item where id = #{id}
    </select>

    <select id="selectMaxBarcode" resultType="java.lang.Long">
        select max(barcode) from sp_speciality_param_item where speciality_param_id = #{specialityParamId}
    </select>

    <select id="selectOneBySpecialityParamId" resultMap="spSpecialityParamItemMap">
        select * from sp_speciality_param_item where speciality_param_id = #{specialityParamId} limit 1
    </select>

</mapper>