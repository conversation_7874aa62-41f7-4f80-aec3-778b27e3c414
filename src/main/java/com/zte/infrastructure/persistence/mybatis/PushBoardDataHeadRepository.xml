<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushBoardDataHeadRepository">
    <update id="updateStatusByProdplanId">
        update push_board_data_head t
        set t.push_status = #{pushStatus},
        last_updated_date = sysdate
        where t.enabled_flag = 'Y'
        and t.prodplan_id in
        <foreach collection="prodplanIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getExistProdplanId" resultType="java.lang.String">
        select prodplan_id from push_board_data_head t
        where t.enabled_flag = 'Y'
        and t.prodplan_id in
        <foreach collection="prodplanIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into push_board_data_head (
        prodplan_id,
        task_qty,
        factory_id,
        customer_name,
        task_no,
        item_no,
        sides
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
         (
          #{item.prodplanId},
          #{item.taskQty},
          #{item.factoryId},
          #{item.customerName},
          #{item.taskNo},
          #{item.itemNo},
          #{item.sides})
        </foreach>
    </insert>

    <select id="getNotPushedList" resultType="com.zte.interfaces.dto.PushBoardDataHeadDTO">
        select t.prodplan_id, t.last_updated_date, t.push_status, nvl(a.task_qty, t.task_qty) as task_qty
        from push_board_data_head t
        left join ps_task a
        on t.prodplan_id = a.prodplan_id
        where t.enabled_flag = 'Y'
        and t.last_updated_date >= #{startTime}
        and t.push_status in ('0')
        <if test="lastProdplanId != null and lastProdplanId != ''">
            and not exists ( select 1 from push_board_data_head a where t.prodplan_id = a.prodplan_id and a.prodplan_id &lt;= #{lastProdplanId} and a.last_updated_date = #{startTime})
        </if>
        order by t.last_updated_date, t.prodplan_id
        limit #{limit}
    </select>

    <select id="getNotPushDoneProdplanId" resultType="java.lang.String">
        select prodplan_id from push_board_data_head
        where enabled_flag = 'Y'
        and customer_name in
        <foreach collection="customerNameList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and push_status = '0'
        order by create_date
        limit #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </select>
</mapper>