<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ResourceDetailRepository" >

  <resultMap id="resourceDetailMap" type="com.zte.interfaces.dto.ResourceDetailEntityDTO" >
    <id column="DISTRIBUTION_ID" property="distributionId" jdbcType="VARCHAR" />
    <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR" />
    <result column="RESOURCE_ID" property="resourceId" jdbcType="VARCHAR" />
    <result column="RESOURCE_START" property="resourceStart" jdbcType="VARCHAR" />
    <result column="RESOURCE_STEP" property="resourceStep" jdbcType="DECIMAL" />
    <result column="RESOURCE_SN" property="resourceSn" jdbcType="VARCHAR" />
    <result column="RESOURCE_STATUS" property="resourceStatus" jdbcType="VARCHAR" />
    <result column="WLAN_USERNAME" property="wlanUsername" jdbcType="VARCHAR" />
    <result column="WLAN_PWD" property="wlanPwd" jdbcType="VARCHAR" />
    <result column="LAN_IP" property="lanIp" jdbcType="VARCHAR" />
    <result column="DEVICE_TYPE" property="deviceType" jdbcType="VARCHAR" />
    <result column="WIRELESS_NAME_2G_1" property="wirelessName2g1" jdbcType="VARCHAR" />
    <result column="WIRELESS_PASS_2G_1" property="wirelessPass2g1" jdbcType="VARCHAR" />
    <result column="RATED_VOLTAGE" property="ratedVoltage" jdbcType="VARCHAR" />
    <result column="RATED_CURRENT" property="ratedCurrent" jdbcType="VARCHAR" />
    <result column="DEVICE_SERIAL_NUMBER_PRINT" property="deviceSerialNumberPrint" jdbcType="VARCHAR" />
    <result column="ENABLE_FLAG" property="enableFlag" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_DATE" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="Base_map" type="com.zte.domain.model.ResourceDetail" >
    <id column="DISTRIBUTION_ID" property="distributionId" jdbcType="VARCHAR" />
    <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR" />
    <result column="RESOURCE_ID" property="resourceId" jdbcType="VARCHAR" />
    <result column="RESOURCE_START" property="resourceStart" jdbcType="VARCHAR" />
    <result column="RESOURCE_STEP" property="resourceStep" jdbcType="DECIMAL" />
    <result column="RESOURCE_SN" property="resourceSn" jdbcType="VARCHAR" />
    <result column="RESOURCE_STATUS" property="resourceStatus" jdbcType="VARCHAR" />
    <result column="WLAN_USERNAME" property="wlanUsername" jdbcType="VARCHAR" />
    <result column="WLAN_PWD" property="wlanPwd" jdbcType="VARCHAR" />
    <result column="LAN_IP" property="lanIp" jdbcType="VARCHAR" />
    <result column="DEVICE_TYPE" property="deviceType" jdbcType="VARCHAR" />
    <result column="WIRELESS_NAME_2G_1" property="wirelessName2g1" jdbcType="VARCHAR" />
    <result column="WIRELESS_PASS_2G_1" property="wirelessPass2g1" jdbcType="VARCHAR" />
    <result column="RATED_VOLTAGE" property="ratedVoltage" jdbcType="VARCHAR" />
    <result column="RATED_CURRENT" property="ratedCurrent" jdbcType="VARCHAR" />
    <result column="DEVICE_SERIAL_NUMBER_PRINT" property="deviceSerialNumberPrint" jdbcType="VARCHAR" />
    <result column="ENABLE_FLAG" property="enableFlag" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_DATE" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="additional" property="additional" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="receive_start_time" property="receiveTime" jdbcType="VARCHAR" />
    <result column="model_num_and_code" property="modelNumAndCode" jdbcType="VARCHAR" />
    <result column="scrambling_code" property="scramblingCode" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    DISTRIBUTION_ID, APPLY_ID, RESOURCE_ID, RESOURCE_START, RESOURCE_STEP, RESOURCE_SN,
    RESOURCE_STATUS, WLAN_USERNAME, WLAN_PWD, LAN_IP, DEVICE_TYPE, WIRELESS_NAME_2G_1,
    WIRELESS_PASS_2G_1, RATED_VOLTAGE, RATED_CURRENT, DEVICE_SERIAL_NUMBER_PRINT, ENABLE_FLAG,
    CREATE_DATE, CREATE_USER, UPDATE_DATE, UPDATE_USER, ADDITIONAL
  </sql>

  <delete id="deleteByApplyId">
    DELETE FROM RESOURCE_APPLICATION_DETAIL WHERE APPLY_ID = #{applyId}
  </delete>

    <!-- 资源分配详情统计 -->
  <select id="getResourceDetailCount" resultType="long">
    select
    count(*)
    from MES_CENTERFACTORY.RESOURCE_APPLICATION_DETAIL
    where ENABLE_FLAG = 'Y'
    AND APPLY_ID in
    <foreach collection="applyIdList" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <if test="resourceStatus != null and resourceStatus != ''">
      and resource_status = #{resourceStatus}
    </if>
    <if test="notResourceStatus != null and notResourceStatus != ''">
      and resource_status != #{notResourceStatus}
    </if>
    <if test="sort != null and sort == 'CREATE_DATE'">
      order by CREATE_DATE
      <if test="order != null and order == 'desc'"> desc </if>
    </if>
  </select>

  <!-- 资源分配详情 -->
  <select id="getPageList" resultMap="Base_map">
    select ra.remark, ra.receive_start_time,
    rd.resource_start, rd.resource_step, rd.resource_sn, rd.resource_status, rd.additional, rd.model_num_and_code, rd.scrambling_code
    from RESOURCE_APPLICATION_DETAIL rd left join RESOURCE_APPLICATION ra on ra.apply_id = rd.apply_id
    where rd.ENABLE_FLAG = 'Y'
    AND rd.APPLY_ID = #{applyId,jdbcType=VARCHAR}
    <if test="resourceStatus != null and resourceStatus != ''">
      and rd.resource_status = #{resourceStatus}
    </if>
    <if test="notResourceStatus != null and notResourceStatus != ''">
      and rd.resource_status != #{notResourceStatus}
    </if>
    order by rd.resource_start
    limit  #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
  </select>

  <!-- 导出资源申请数据  -->
  <select id="exportApplyResource" parameterType="com.zte.springbootframe.common.model.Page" resultMap="Base_map">
    select
    RD.RESOURCE_START AS RESOURCE_SN
    from MES_CENTERFACTORY.RESOURCE_APPLICATION RA
    INNER JOIN MES_CENTERFACTORY.RESOURCE_APPLICATION_DETAIL RD
    ON  RA.APPLY_ID = RD.APPLY_ID
    AND RA.ENABLE_FLAG = 'Y' AND RD.ENABLE_FLAG = 'Y'
    WHERE 1=1
    AND RA.APPLY_BILL_NO = #{params.applyBillNo,jdbcType=VARCHAR}
    ORDER BY RD.RESOURCE_START
  </select>

  <!-- 查询文件中的资源号与数据库中的是否一致 -->
  <select id="compareResourceSn" resultType="java.lang.String">
    SELECT RD.RESOURCE_START
    FROM MES_CENTERFACTORY.RESOURCE_APPLICATION_DETAIL RD,
    MES_CENTERFACTORY.RESOURCE_APPLICATION RA
    WHERE RA.APPLY_ID = RD.APPLY_ID
    AND RA.APPLY_BILL_NO = #{applyBillNo,jdbcType=VARCHAR}
    AND RD.RESOURCE_START IN
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    AND RA.ENABLE_FLAG = 'Y'
  </select>

  <!-- 判断临时表是否存在 -->
  <select id="findIsNotEmptyTable" resultType="long">
    SELECT COUNT(1)
    FROM pg_class where relkind = 'r' and relname = 'resource_detail_temp1'
  </select>

  <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="resourceDetailMap">
    select
    <include refid="Base_Column_List" />
    from RESOURCE_APPLICATION_DETAIL where 1=1
    <if test="params != null and params.distributionId != null and params.distributionId != ''">and DISTRIBUTION_ID = #{params.distributionId}</if>
    <if test="params != null and params.applyId != null and params.applyId != ''">and APPLY_ID = #{params.applyId}</if>
    <if test="params != null and params.resourceId != null and params.resourceId != ''">and RESOURCE_ID = #{params.resourceId}</if>
    <if test="params != null and params.resourceStart != null and params.resourceStart != ''">and RESOURCE_START = #{params.resourceStart}</if>
    <if test="params != null and params.resourceStep != null and params.resourceStep != ''">and RESOURCE_STEP = #{params.resourceStep}</if>
    <if test="params != null and params.resourceSn != null and params.resourceSn != ''">and RESOURCE_SN = #{params.resourceSn}</if>
    <if test="params != null and params.resourceStatus != null and params.resourceStatus != ''">and RESOURCE_STATUS = #{params.resourceStatus}</if>
    <if test="params != null and params.wlanUsername != null and params.wlanUsername != ''">and WLAN_USERNAME = #{params.wlanUsername}</if>
    <if test="params != null and params.wlanPwd != null and params.wlanPwd != ''">and WLAN_PWD = #{params.wlanPwd}</if>
    <if test="params != null and params.lanIp != null and params.lanIp != ''">and LAN_IP = #{params.lanIp}</if>
    <if test="params != null and params.deviceType != null and params.deviceType != ''">and DEVICE_TYPE = #{params.deviceType}</if>
    <if test="params != null and params.wirelessName2g1 != null and params.wirelessName2g1 != ''">and WIRELESS_NAME_2G_1 = #{params.wirelessName2g1}</if>
    <if test="params != null and params.wirelessPass2g1 != null and params.wirelessPass2g1 != ''">and WIRELESS_PASS_2G_1 = #{params.wirelessPass2g1}</if>
    <if test="params != null and params.ratedVoltage != null and params.ratedVoltage != ''">and RATED_VOLTAGE = #{params.ratedVoltage}</if>
    <if test="params != null and params.ratedCurrent != null and params.ratedCurrent != ''">and RATED_CURRENT = #{params.ratedCurrent}</if>
    <if test="params != null and params.deviceSerialNumberPrint != null and params.deviceSerialNumberPrint != ''">and DEVICE_SERIAL_NUMBER_PRINT = #{params.deviceSerialNumberPrint}</if>
    <if test="params != null and params.enableFlag != null and params.enableFlag != ''">and ENABLE_FLAG = #{params.enableFlag}</if>
    <if test="params != null and params.createDate != null">and CREATE_DATE = #{params.createDate}</if>
    <if test="params != null and params.createUser != null and params.createUser != ''">and CREATE_USER = #{params.createUser}</if>
    <if test="params != null and params.updateDate != null">and UPDATE_DATE = #{params.updateDate}</if>
    <if test="params != null and params.updateUser != null and params.updateUser != ''">and UPDATE_USER = #{params.updateUser}</if>
  </select>


  <select id="getFirstAndLast" resultMap="Base_map">
    with m as (
      select <include refid="Base_Column_List" />,
      row_number() over(order by resource_start) rn
      from RESOURCE_APPLICATION_DETAIL
      where ENABLE_FLAG = 'Y'
      AND APPLY_ID = #{applyId,jdbcType=VARCHAR}
      order by RESOURCE_START
      limit #{rows}::numeric
    )
    select * from m where rn = 1 or rn = #{rows}::numeric
  </select>

  <!-- 资源申请临时表创建 会话级别-->
  <update id="createResourceDetailTemp">
    create table RESOURCE_DETAIL_TEMP1
    (
    apply_id                   VARCHAR(64),
    device_type                VARCHAR(30),
    resource_sn                VARCHAR(30) not null,
    wireless_name_2g_1         VARCHAR(100),
    wireless_pass_2g_1         VARCHAR(100),
    lan_ip                     VARCHAR(100),
    wlan_username              VARCHAR(60),
    wlan_pwd                   VARCHAR(60),
    rated_voltage              VARCHAR(50),
    rated_current              VARCHAR(50),
    device_serial_number_print VARCHAR(200)
    )
  </update>

  <!-- 资源申请临时表添加索引 -->
  <update id="addUniqueResourceSn">
    CREATE UNIQUE INDEX IDX_UNIQUE_RESOURCE_SN ON RESOURCE_DETAIL_TEMP1 (RESOURCE_SN)
  </update>

  <!-- 资源申请临时表数据添加 -->
  <insert id="insertAllResourceDetailTemp">
    insert INTO RESOURCE_DETAIL_TEMP1
    (APPLY_ID,DEVICE_TYPE,RESOURCE_SN,WIRELESS_NAME_2G_1,
    WIRELESS_PASS_2G_1,LAN_IP,WLAN_USERNAME,WLAN_PWD,RATED_VOLTAGE,
    RATED_CURRENT,DEVICE_SERIAL_NUMBER_PRINT,ADDITIONAL)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.applyId,jdbcType=VARCHAR},#{item.deviceType,jdbcType=VARCHAR},
      #{item.resourceSn,jdbcType=VARCHAR},#{item.wirelessName2g1,jdbcType=VARCHAR},
      #{item.wirelessPass2g1,jdbcType=VARCHAR},#{item.lanIp,jdbcType=VARCHAR},
      #{item.wlanUsername,jdbcType=VARCHAR},#{item.wlanPwd,jdbcType=VARCHAR},
      #{item.ratedVoltage,jdbcType=VARCHAR},#{item.ratedCurrent,jdbcType=VARCHAR},
      #{item.deviceSerialNumberPrint,jdbcType=VARCHAR},#{item.additional,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 更新资源申请详情信息 -->
  <update id="updateResourceDetail">
    with RTT as (SELECT RT.APPLY_ID ,RT.RESOURCE_SN,RT.DEVICE_TYPE,RT.WIRELESS_NAME_2G_1,RT.WIRELESS_PASS_2G_1,RT.LAN_IP,
    RT.WLAN_USERNAME,RT.WLAN_PWD,RT.RATED_VOLTAGE,RT.RATED_CURRENT,RT.DEVICE_SERIAL_NUMBER_PRINT,RT.ADDITIONAL
    FROM RESOURCE_DETAIL_TEMP1 RT)
    update RESOURCE_APPLICATION_DETAIL RD
    SET
    RD.DEVICE_TYPE = RTT.DEVICE_TYPE,
    RD.WIRELESS_NAME_2G_1 = RTT.WIRELESS_NAME_2G_1,
    RD.WIRELESS_PASS_2G_1 = RTT.WIRELESS_PASS_2G_1,
    RD.LAN_IP = RTT.LAN_IP,
    RD.WLAN_USERNAME = RTT.WLAN_USERNAME,
    RD.WLAN_PWD = RTT.WLAN_PWD,
    RD.RATED_VOLTAGE = RTT.RATED_VOLTAGE,
    RD.RATED_CURRENT = RTT.RATED_CURRENT,
    RD.ADDITIONAL = RTT.ADDITIONAL,
    RD.DEVICE_SERIAL_NUMBER_PRINT = RTT.DEVICE_SERIAL_NUMBER_PRINT,
    RD.RESOURCE_STATUS = 'INIT',
    RD.UPDATE_DATE = SYSDATE
    from RTT
    where RTT.RESOURCE_SN = RD.RESOURCE_START
    AND RD.APPLY_ID = RTT.APPLY_ID
    AND RD.ENABLE_FLAG = 'Y'
    AND RD.CREATE_DATE >= to_timestamp(#{partitionTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
  </update>

  <update id="updateApplyId">
    UPDATE RESOURCE_APPLICATION_DETAIL SET APPLY_ID = #{applyId}
    WHERE RESOURCE_START <![CDATA[>=]]> #{resourceStart} and RESOURCE_START <![CDATA[<=]]> #{resourceSn}
  </update>
  <update id="updateNewApplyId">
    UPDATE RESOURCE_APPLICATION_DETAIL SET APPLY_ID = #{newApplyId}
    WHERE APPLY_ID = #{applyId}
  </update>
  <update id="updateBatchOfStatus">
    UPDATE RESOURCE_APPLICATION_DETAIL SET RESOURCE_STATUS = 'USED',
    UPDATE_USER = #{updateUser},
    UPDATE_DATE = SYSDATE
    WHERE APPLY_ID = #{applyId} and RESOURCE_START <![CDATA[>=]]> #{resourceStart} and RESOURCE_START <![CDATA[<=]]> #{resourceSn}
  </update>

  <update id="deleteResourceTemp">
	  TRUNCATE TABLE RESOURCE_DETAIL_TEMP1
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into RESOURCE_APPLICATION_DETAIL (
    DISTRIBUTION_ID,
    APPLY_ID,
    RESOURCE_ID,
    RESOURCE_START,
    RESOURCE_STEP,
    RESOURCE_SN,
    RESOURCE_STATUS,
    ENABLE_FLAG,
    CREATE_DATE,
    CREATE_USER,
    UPDATE_DATE,
    UPDATE_USER,
    MODEL_NUM_AND_CODE,
    SCRAMBLING_CODE
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.distributionId,jdbcType=VARCHAR},
      #{item.applyId,jdbcType=VARCHAR},
      #{item.resourceId,jdbcType=VARCHAR},
      #{item.resourceStart,jdbcType=VARCHAR},
      #{item.resourceStep,jdbcType=DECIMAL},
      #{item.resourceSn,jdbcType=VARCHAR},
      #{item.resourceStatus,jdbcType=VARCHAR},
      'Y',
      sysdate,
      #{item.createUser,jdbcType=VARCHAR},
      sysdate,
      #{item.updateUser,jdbcType=VARCHAR},
      #{item.modelNumAndCode,jdbcType=VARCHAR},
      #{item.scramblingCode,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertRecord" parameterType="com.zte.interfaces.dto.ResourceApplicationRecordDTO">
    insert into RESOURCE_APPLICATION_RECORD (
    ID,
    RESOURCE_NO,
    APPLY_ID,
    RESOURCE_START,
    RESOURCE_END,
    ENABLED_FLAG,
    ERROR_MESSAGE)
    values (
    #{id,jdbcType=VARCHAR},
    #{resourceNo,jdbcType=VARCHAR},
    #{applyId,jdbcType=VARCHAR},
    #{resourceStart,jdbcType=VARCHAR},
    #{resourceEnd,jdbcType=VARCHAR},
    'Y',
    #{errorMessage,jdbcType=VARCHAR})
  </insert>


</mapper>