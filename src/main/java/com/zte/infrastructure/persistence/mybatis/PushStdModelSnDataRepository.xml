<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushStdModelSnDataRepository">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.domain.model.PushStdModelSnData" id="pushStdModelSnDataMap">
        <result property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="taskNo" column="task_no"/>
        <result property="itemNo" column="item_no"/>
        <result property="factoryId" column="factory_id"/>
        <result property="currProcess" column="curr_process"/>
        <result property="pushStatus" column="push_status"/>
        <result property="pushDate" column="push_date"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="pushFailCount" column="push_fail_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, sn, task_no, factory_id, curr_process, push_status, push_date, error_msg, push_fail_count, create_by,
        create_date, last_updated_by, last_updated_date, enabled_flag,item_no,virtual_sn,original_sn
    </sql>

    <insert id="insert">
        insert into push_std_model_sn_data
        (id, sn, task_no, factory_id, curr_process, push_status, push_date, error_msg, push_fail_count, create_by,
        create_date, last_updated_by, last_updated_date, enabled_flag,item_no,virtual_sn,original_sn)
        values
        (#{id, jdbcType=VARCHAR}, #{sn, jdbcType=VARCHAR}, #{taskNo, jdbcType=VARCHAR}, #{factoryId, jdbcType=DECIMAL},
        #{currProcess, jdbcType=VARCHAR}, #{pushStatus, jdbcType=DECIMAL}, #{pushDate, jdbcType=TIMESTAMP},
        #{errorMsg, jdbcType=VARCHAR}, #{pushFailCount, jdbcType=DECIMAL}, #{createBy, jdbcType=VARCHAR},
        #{createDate, jdbcType=TIMESTAMP}, #{lastUpdatedBy, jdbcType=VARCHAR}, #{lastUpdatedDate, jdbcType=TIMESTAMP},
        'Y',#{itemNo,jdbcType=VARCHAR},
        #{virtualSn, jdbcType=VARCHAR},#{originalSn,jdbcType=VARCHAR})
    </insert>


    <insert id="batchInsertOrUpdate">
        WITH T AS (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.id, jdbcType=VARCHAR} AS id,
            #{item.sn, jdbcType=VARCHAR} AS sn,
            #{item.taskNo, jdbcType=VARCHAR} AS taskNo,
            #{item.factoryId, jdbcType=DECIMAL} AS factoryId,
            #{item.currProcess, jdbcType=VARCHAR} AS currProcess,
            #{item.pushStatus, jdbcType=VARCHAR} AS pushStatus,
            SYSDATE AS pushDate,
            #{item.errorMsg, jdbcType=VARCHAR} AS errorMsg,
            #{item.pushFailCount, jdbcType=DECIMAL} AS pushFailCount,
            #{item.lastUpdatedBy, jdbcType=VARCHAR} AS lastUpdatedBy,
            #{item.createBy, jdbcType=VARCHAR} AS createBy,
            SYSDATE AS createDate,
            SYSDATE AS lastUpdatedDate,
            #{item.itemNo, jdbcType=VARCHAR} AS itemNo,
            'Y' AS enabledFlag
        </foreach>
        ),
        updated AS (
        UPDATE push_std_model_sn_data T1
        SET
        T1.LAST_UPDATED_BY = t.lastUpdatedBy,
        T1.LAST_UPDATED_DATE = SYSDATE
        from T
        WHERE T.sn = T1.sn AND T.taskNo = T1.task_No and T.enabledFlag = T1.enabled_flag
        returning t.*
        )
        INSERT INTO push_std_model_sn_data(
        id, sn, task_no, factory_id, curr_process, push_status, push_date, error_msg, push_fail_count, create_by,
        create_date, last_updated_by, last_updated_date, enabled_flag,item_no
        )
        SELECT
        id, sn, taskNo, factoryId, currProcess, pushStatus, pushDate, errorMsg, pushFailCount, createBy, createDate,
        lastUpdatedBy, lastUpdatedDate, enabledFlag,itemNo
        FROM T WHERE NOT EXISTS (SELECT id FROM updated WHERE sn = t.sn and taskNo = t.taskNo and enabledFlag = t.enabledFlag )
    </insert>


    <update id="updateCurrProcessBySnAndTaskNo">
        update push_std_model_sn_data
        <set>
            last_updated_date = sysdate,
            curr_process = #{currProcess, jdbcType=VARCHAR},
            push_status = #{pushStatus},
            push_date = sysdate
        </set>
        where enabled_flag ='Y' and sn = #{sn, jdbcType=VARCHAR} and task_no = #{taskNo, jdbcType=VARCHAR}
    </update>

    <update id="updateById">
        update push_std_model_sn_data
        <set>
            <!--  last_updated_date = SYSDATE,
             -->
            sn = #{sn, jdbcType=VARCHAR},

            task_no = #{taskNo, jdbcType=VARCHAR},

            factory_id = #{factoryId, jdbcType=DECIMAL},

            curr_process = #{currProcess, jdbcType=VARCHAR},

            push_status = #{pushStatus, jdbcType=DECIMAL},

            push_date = #{pushDate, jdbcType=TIMESTAMP},

            error_msg = #{errorMsg, jdbcType=VARCHAR},

            push_fail_count = #{pushFailCount, jdbcType=DECIMAL},

            create_by = #{createBy, jdbcType=VARCHAR},

            create_date = #{createDate, jdbcType=TIMESTAMP},

            last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},

            last_updated_date = #{lastUpdatedDate, jdbcType=TIMESTAMP},

            enabled_flag = #{enabledFlag, jdbcType=VARCHAR},

        </set>
        where id = #{id, jdbcType=VARCHAR}
    </update>

    <delete id="deleteByIds">
        delete from push_std_model_sn_data where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="selectCount" parameterType="com.zte.interfaces.dto.PushStdModelSnDataPageQueryDTO"
            resultType="java.lang.Integer">
        select count(1) from push_std_model_sn_data
        <where>
            enabled_flag= 'Y'
            <if test="id != null and id != ''">
                and id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="sn != null and sn != ''">
                and sn = #{sn, jdbcType=VARCHAR}
            </if>
            <if test="taskNo != null and taskNo != ''">
                and task_no = #{taskNo, jdbcType=VARCHAR}
            </if>
            <if test="factoryId != null and factoryId != ''">
                and factory_id = #{factoryId, jdbcType=DECIMAL}
            </if>
            <if test="currProcess != null and currProcess != ''">
                and curr_process = #{currProcess, jdbcType=VARCHAR}
            </if>
            <if test="pushStatus != null and pushStatus != ''">
                and push_status = #{pushStatus, jdbcType=DECIMAL}
            </if>
            <if test="pushDate != null and pushDate != ''">
                and push_date = #{pushDate, jdbcType=TIMESTAMP}
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                and error_msg = #{errorMsg, jdbcType=VARCHAR}
            </if>
            <if test="pushFailCount != null and pushFailCount != ''">
                and push_fail_count = #{pushFailCount, jdbcType=DECIMAL}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate, jdbcType=TIMESTAMP}
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                and last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR}
            </if>
            <if test="lastUpdatedDate != null and lastUpdatedDate != ''">
                and last_updated_date = #{lastUpdatedDate, jdbcType=TIMESTAMP}
            </if>
            <if test="enabledFlag != null and enabledFlag != ''">
                and enabled_flag = #{enabledFlag, jdbcType=VARCHAR}
            </if>
            <!--
            <if test="startTime != null and endTime != null">
                AND create_date between #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            </if>
            like concat(concat('%', #{name}),'%')
            -->
        </where>
    </select>

    <select id="selectPage" parameterType="com.zte.springbootframe.common.model.Page" resultMap="pushStdModelSnDataMap">
        select
        <include refid="Base_Column_List"/>
        from push_std_model_sn_data
        <where>
            enabled_flag= 'Y'
            <if test="params != null and params.id != null and params.id != ''">
                and id = #{params.id, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.sn != null and params.sn != ''">
                and sn = #{params.sn, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.taskNo != null and params.taskNo != ''">
                and task_no = #{params.taskNo, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.factoryId != null and params.factoryId != ''">
                and factory_id = #{params.factoryId, jdbcType=DECIMAL}
            </if>
            <if test="params != null and params.currProcess != null and params.currProcess != ''">
                and curr_process = #{params.currProcess, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.pushStatus != null and params.pushStatus != ''">
                and push_status = #{params.pushStatus, jdbcType=DECIMAL}
            </if>
            <if test="params != null and params.pushDate != null and params.pushDate != ''">
                and push_date = #{params.pushDate, jdbcType=TIMESTAMP}
            </if>
            <if test="params != null and params.errorMsg != null and params.errorMsg != ''">
                and error_msg = #{params.errorMsg, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.pushFailCount != null and params.pushFailCount != ''">
                and push_fail_count = #{params.pushFailCount, jdbcType=DECIMAL}
            </if>
            <if test="params != null and params.createBy != null and params.createBy != ''">
                and create_by = #{params.createBy, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.createDate != null and params.createDate != ''">
                and create_date = #{params.createDate, jdbcType=TIMESTAMP}
            </if>
            <if test="params != null and params.lastUpdatedBy != null and params.lastUpdatedBy != ''">
                and last_updated_by = #{params.lastUpdatedBy, jdbcType=VARCHAR}
            </if>
            <if test="params != null and params.lastUpdatedDate != null and params.lastUpdatedDate != ''">
                and last_updated_date = #{params.lastUpdatedDate, jdbcType=TIMESTAMP}
            </if>
            <if test="params != null and params.enabledFlag != null and params.enabledFlag != ''">
                and enabled_flag = #{params.enabledFlag, jdbcType=VARCHAR}
            </if>
            <!--
            <if test="params != null and params.startTime != null and params.endTime != null">
                AND create_date between #{params.startTime,jdbcType=TIMESTAMP} AND #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            like concat(concat('%', #{params.name, jdbcType=VARCHAR}),'%')
            -->
        </where>
        order by last_updated_date desc
    </select>

    <select id="selectById" resultMap="pushStdModelSnDataMap">
        select
        <include refid="Base_Column_List"/>
        from push_std_model_sn_data where id = #{id, jdbcType=VARCHAR}
    </select>


    <select id="getNeedPushDataPage" parameterType="com.zte.interfaces.dto.PushStdModelSnDataDTO" resultType="com.zte.interfaces.dto.PushStdModelSnDataDTO">
        select p.id, p.sn, p.task_no, p.factory_id, p.item_no,e.bill_no,e.customer_item_name,p.last_updated_date
        from push_std_model_sn_data p
        left join ps_task_extended e on e.task_no = p.task_no
        where p.enabled_flag = 'Y' and p.curr_process = '10' and p.last_updated_date > sysdate-#{preDays}
        <if test="snList != null and snList.size() > 0">
            and p.sn IN
            <foreach item="sn" index="index" collection="snList" open="(" separator="," close=")">
                #{snList[${index}]}
            </foreach>
        </if>
        <if test="lastUpdatedDate != null ">
            and p.last_updated_date >= #{lastUpdatedDate, jdbcType=TIMESTAMP}
        </if>
        <if test="lastId != null and lastId !='' ">
            and not exists (select 1 from push_std_model_sn_data c where c.id = p.id and c.enabled_flag ='Y' and id &lt;= #{lastId, jdbcType=VARCHAR}
            and c.last_updated_date = #{lastUpdatedDate, jdbcType=TIMESTAMP})
        </if>
        order by p.last_updated_date,p.id
        limit #{limit}
    </select>
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.PushStdModelSnDataDTO">
        <id column="id" property="id"/>
        <result column="sn" property="sn"/>
        <result column="task_no" property="taskNo"/>
        <result column="factory_id" property="factoryId"/>
        <result column="curr_process" property="currProcess"/>
        <result column="push_status" property="pushStatus"/>
        <result column="push_date" property="pushDate"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="push_fail_count" property="pushFailCount"/>
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="last_updated_by" property="lastUpdatedBy" />
        <result column="last_updated_date" property="lastUpdatedDate" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <select id="selectExists" resultMap="BaseResultMap">
        with queryPsmsd as (
        <foreach collection="queryPushStdModelSnDataList" index="index" item="item" separator="union all">
            select #{item.sn} as sn, #{item.taskNo} as task_no
        </foreach>
        )
        select
        psmsd.id,
        psmsd.sn,
        psmsd.task_no,
        psmsd.factory_id,
        psmsd.curr_process,
        psmsd.push_status,
        psmsd.last_updated_date
        from push_std_model_sn_data psmsd
        inner join queryPsmsd t on t.sn = psmsd.sn and t.task_no = psmsd.task_no
        where psmsd.enabled_flag = 'Y' and psmsd.factory_id = #{factoryId}
    </select>

        <select id="selectByQuery" resultMap="BaseResultMap" parameterType="com.zte.interfaces.dto.PushStdModelSnDataQueryDTO">
        <if test="processStatusConditions != null and processStatusConditions.size() > 0">
            WITH processStatusCondition AS (
            <foreach collection="processStatusConditions" index="index" item="item" separator="UNION ALL">
                SELECT #{item.currProcess} as curr_process, #{item.pushStatus} as push_status
            </foreach>
            )
        </if>
        select
        psmsd.id,
        psmsd.sn,
        psmsd.task_no,
        psmsd.factory_id,
        psmsd.curr_process,
        psmsd.push_status,
        psmsd.last_updated_date
        from push_std_model_sn_data psmsd
        <if test="processStatusConditions != null and processStatusConditions.size() > 0">
            inner join processStatusCondition t on t.curr_process = psmsd.curr_process and t.push_status = psmsd.push_status
        </if>
        where psmsd.enabled_flag = 'Y'
        <if test="factoryId != null">
            and psmsd.factory_id = #{factoryId}
        </if>
        <if test="lastUpdatedDateStart != null">
            and psmsd.last_updated_date >= #{lastUpdatedDateStart}
        </if>
        <if test="taskNos != null and taskNos.size() > 0">
            and psmsd.task_no in
            <foreach collection="taskNos" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="snFrom != null and snFrom != ''">
            and not exists (
            select 1 from push_std_model_sn_data psmsd1
            where psmsd1.id = psmsd.id and psmsd1.sn &lt;= #{snFrom}
            <if test="lastUpdatedDateStart != null">
                and psmsd1.last_updated_date = #{lastUpdatedDateStart}
            </if>
            )
        </if>
        order by psmsd.last_updated_date, sn
    </select>

    <resultMap id="ExtResultMap" type="com.zte.interfaces.dto.PushStdModelSnDataExtDTO" extends="BaseResultMap">
        <result column="bill_no" property="taskBillNo"/>
        <result column="fix_bom_id" property="taskFixBomId"/>
        <result column="customer_no" property="taskCustomerNo"/>
        <result column="customer_part_type" property="taskCustomerPartType"/>
        <result column="customer_item_name" property="taskCustomerItemName"/>
        <result column="entity_class" property="taskEntityClass"/>
        <result column="org_id" property="stockOrgId"/>
        <result column="fix_bom_head_id" property="fixBomHeadId"/>
        <result column="cloud_type" property="cloudType"/>
        <result column="material_control" property="materialControl"/>
    </resultMap>

    <select id="selectExtByPrimaryKeys" resultMap="ExtResultMap">
        select
            psmsd.id,
            psmsd.sn,
            psmsd.task_no,
            psmsd.factory_id,
            psmsd.curr_process,
            psmsd.push_status,
            psmsd.last_updated_date,
            pte.bill_no,
            pte.fix_bom_id,
            pte.customer_no,
            pte.customer_part_type,
            pte.customer_item_name,
            pte.entity_class,
            pt.org_id,
            pte.fix_bom_head_id
        from push_std_model_sn_data psmsd
        inner join ps_task_extended pte on psmsd.task_no = pte.task_no and pte.enabled_flag = 'Y'
        inner join ps_task pt on pt.task_no = pte.task_no and pt.enabled_flag = 'Y'
        where psmsd.enabled_flag = 'Y' and psmsd.id in 
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="selectExtByPrimaryKey" resultMap="ExtResultMap" parameterType="string">
        select
            psmsd.id,
            psmsd.sn,
            psmsd.task_no,
            psmsd.factory_id,
            psmsd.curr_process,
            psmsd.push_status,
            psmsd.last_updated_date,
            psmsd.virtual_sn,
            psmsd.original_sn,
            pte.bill_no,
            pte.fix_bom_id,
            pte.customer_no,
            pte.customer_part_type,
            pte.customer_item_name,
            pte.entity_class,
            pte.business_scene,
            pt.org_id,
            pte.fix_bom_head_id,
            pte.cloud_type,
            pte.material_control
        from push_std_model_sn_data psmsd
        inner join ps_task_extended pte on psmsd.task_no = pte.task_no and pte.enabled_flag = 'Y'
        inner join ps_task pt on pt.task_no = pte.task_no and pt.enabled_flag = 'Y'
        where psmsd.enabled_flag = 'Y' and psmsd.id = #{id}
    </select>
    <select id="reportRecords" resultType="com.zte.interfaces.dto.PushStdModelSnDataDTO">
        select
        <include refid="Base_Column_List"/>
        from push_std_model_sn_data
        where ((curr_process='40' and push_status=2) or (curr_process > '40')) and sn = #{sn} and enabled_flag = 'Y'
    </select>

    <insert id="batchInsert" parameterType="arraylist">
        insert into push_std_model_sn_data (
        id, sn, task_no, factory_id, curr_process, push_status, item_no
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.sn},
            #{item.taskNo},
            #{item.factoryId},
            #{item.currProcess},
            #{item.pushStatus},
            #{item.itemNo}
        )
        </foreach>
    </insert>

    <update id="update" parameterType="com.zte.interfaces.dto.PushStdModelSnDataDTO">
        update push_std_model_sn_data t set
        <if test="currProcess != null and currProcess != ''">
            t.curr_process=#{currProcess},
        </if>
        t.push_status=#{pushStatus},
        <if test="pushDate != null">
            t.push_date=#{pushDate},
        </if>
        t.error_msg=#{errorMsg},
        <if test="pushFailCount != null">
            t.push_fail_count=#{pushFailCount},
        </if>
        t.virtual_sn = #{virtualSn},
        t.original_sn = #{originalSn},
        t.last_updated_date=sysdate
        where id = #{id}
    </update>

    <update id="batchUpdate" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update push_std_model_sn_data t set
            <if test="item.currProcess != null and item.currProcess != ''">
                t.curr_process=#{item.currProcess},
            </if>
            t.push_status=#{item.pushStatus},
            <if test="item.pushDate != null">
                t.push_date=#{item.pushDate},
            </if>
            t.error_msg=#{item.errorMsg},
            <if test="item.pushFailCount != null">
                t.push_fail_count=#{item.pushFailCount},
            </if>
            t.last_updated_date=sysdate
            where id = #{item.id}
        </foreach>
    </update>

    <select id="queryPushStdModelSn" resultType="com.zte.interfaces.dto.PushStdModelSnDataDTO">
        select <include refid="Base_Column_List"/>
        FROM
        push_std_model_sn_data
        where enabled_flag = 'Y'
        AND SN = #{sn,jdbcType=VARCHAR}
        AND TASK_NO = #{taskNo,jdbcType=VARCHAR}
        AND FACTORY_ID = #{factoryId,jdbcType=INTEGER}
    </select>

    <update id="replaceSnByCondition" parameterType="com.zte.domain.model.PushStdModelSnData">
        update push_std_model_sn_data
        set sn = #{replaceSn},
        virtual_sn = null,
        original_Sn = null,
        last_updated_date = sysdate
        where enabled_flag = 'Y'
        AND SN = #{originalSn,jdbcType=VARCHAR}
        AND TASK_NO = #{taskNo,jdbcType=VARCHAR}
        AND FACTORY_ID = #{factoryId,jdbcType=INTEGER}
    </update>


    <select id="queryPushStdModelSnList" resultType="com.zte.interfaces.dto.PushStdModelSnDataDTO"
        parameterType="java.util.List">
        select <include refid="Base_Column_List"/>
        FROM push_std_model_sn_data
        WHERE ENABLED_FLAG = 'Y'
        AND (TASK_NO,SN) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.taskNo,jdbcType=VARCHAR},#{item.sn,jdbcType=VARCHAR})
        </foreach>
    </select>
    <select id="getVirtualSn" resultType="java.lang.String">
        select virtual_sn
        from push_std_model_sn_data
        where enabled_flag ='Y'
        and sn = #{sn}
    </select>


    <update id="updateVirtualSnBatch" parameterType="java.util.List">
        with t as (
            <foreach collection="list" item="item" separator="union all">
                select
                #{item.sn} sn,#{item.taskNo} task_no,
                #{item.virtualSn} virtual_sn,#{item.originalSn} original_sn
            </foreach>
        )
        UPDATE PUSH_STD_MODEL_SN_DATA PSMSD
        SET
        PSMSD.VIRTUAL_SN = T.VIRTUAL_SN,
        PSMSD.ORIGINAL_SN = T.ORIGINAL_SN,
        PSMSD.LAST_UPDATED_DATE = sysdate
        FROM T WHERE T.TASK_NO = PSMSD.TASK_NO AND T.SN = PSMSD.SN
    </update>


</mapper>
