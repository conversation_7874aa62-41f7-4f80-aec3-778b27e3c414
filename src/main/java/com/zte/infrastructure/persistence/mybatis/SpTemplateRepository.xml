<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.SpTemplateRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.domain.model.SpTemplate" id="spTemplateMap">
        <result property="templateId" column="template_id"/>
        <result property="templateNo" column="template_no"/>
        <result property="status" column="status"/>
        <result property="templateName" column="template_name"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="orgId" column="org_id"/>
    </resultMap>

    <resultMap type="com.zte.interfaces.dto.SpTemplateDTO" id="spTemplateDTOMap">
        <result property="templateId" column="template_id"/>
        <result property="templateNo" column="template_no"/>
        <result property="templateName" column="template_name"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="orgId" column="org_id"/>
        <result property="status" column="status"/>
        <collection property="itemList" ofType="com.zte.interfaces.dto.SpTemplateItemDTO">
            <result property="itemId" column="item_id"/>
            <result property="templateId" column="template_id"/>
            <result property="paramName" column="param_name"/>
            <result property="paramRule" column="param_rule"/>
            <result property="createTime" column="create_time"/>
            <result property="createBy" column="create_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="enabledFlag" column="enabled_flag"/>
            <result property="orgId" column="org_id"/>
            <result property="orderNum" column="order_num"/>
            <result property="paramType" column="param_type"/>
            <result property="generationMethod" column="generation_method"/>
        </collection>
    </resultMap>

    <select id="getDetail" resultMap="spTemplateDTOMap">
        select st.template_id,st.template_no,st.template_name,st.create_time,st.remark,st.create_time,st.create_by,
        st.update_time,st.update_by,st.enabled_flag,st.org_id,st.status,
        sti.item_id,sti.param_name,sti.param_rule,sti.order_num,sti.param_type,sti.generation_method
        from sp_template_item sti left join sp_template st
        on st.template_id = sti.template_id
        where st.enabled_flag = 'Y' AND sti.enabled_flag = 'Y'
        <if test="templateId != null and templateId != ''">
            and st.template_id = #{templateId}
        </if>
        <if test="templateName != null and templateName != ''">
            and st.template_name like concat('%',#{templateName},'%')
        </if>
        order By st.update_time desc, sti.order_num
    </select>

    <sql id="pageParam">
        <where>
            enabled_flag= 'Y'
            <if test="templateName != null and templateName != ''">
                and template_name like concat('%',#{templateName},'%')
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="startTime != null and endTime != null">
                AND create_time between #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>

    <select id="countPage" resultType="java.lang.Long">
        select count(1) from sp_template
        <include refid="pageParam"/>
    </select>

    <select id="queryPage" resultMap="spTemplateMap">
        select * from sp_template
        <include refid="pageParam"/>
        <if test="sort != null and sort != ''">
            <choose>
                <when test="sort=='updateTime'"> order by update_time
                    <if test="order != null and order == 'desc'"> desc </if>
                </when>
            </choose>
        </if>
        limit  #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </select>

    <insert id="insert">
        insert into sp_template (template_id,template_name,remark,create_by,update_by,org_id)
        values (#{templateId},#{templateName},#{remark},#{createBy},#{updateBy},#{orgId})
    </insert>

    <update id="update">
        update sp_template
        <set>
            update_time = SYSDATE,
            <if test="templateName != null and templateName != ''" >
                template_name = #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''" >
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null and orgId != ''" >
                org_id = #{orgId,jdbcType=INTEGER},
            </if>
        </set>
        where template_id=#{templateId}
    </update>

    <update id="delete">
        update sp_template
        set enabled_flag='N', update_time=SYSDATE, update_by=#{updateBy}
        where template_id=#{templateId}
    </update>

    <select id="countByTemplateName" resultType="java.lang.Long">
        select count(1) from sp_template
        <where>
            enabled_flag = 'Y'
            <if test="templateId != null and templateId != ''">
                and template_id != #{templateId}
            </if>
            <if test="templateName != null and templateName != ''">
                and template_name = #{templateName}
            </if>
        </where>
    </select>

    <update id="updateStatusById">
        update sp_template
        set status=1, update_time=SYSDATE, update_by=#{empNo}
        where template_id=#{templateId}
    </update>

</mapper>