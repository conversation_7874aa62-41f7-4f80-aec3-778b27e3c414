<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushModelSnTestRecordRepository">

    <sql id="Base_Column_List">
        request_id,
        station_id,
        type,
        directive_number,
        workorder_id,
        sn,
        node_sn,
        brand,
        board_sn,
        model,
        station_name,
        started_time,
        finished_time,
        result,
        message,
        oss_file_key,
        manufacturer_name,
        action_msg,
        action_code,
        finish_rework_time,
        rework_time,
        upload_status ,
        upload_msg,
        station_upload_status,
        station_upload_msg,
        file_upload_status,
        file_upload_msg,
        enabled_flag,
        create_date,
        last_updated_date,
        create_by,
        last_updated_by,
        factory_Id
    </sql>

    <insert id="insert">
        insert into push_model_sn_test_record
        (request_id, type, directive_number, workorder_id, sn, brand, board_sn, model, station_name, started_time,
        finished_time, result, message, oss_file_key, manufacturer_name, enabled_flag, create_date, last_updated_date,
        create_by, last_updated_by)
        values
        (#{requestId, jdbcType=VARCHAR}, #{type, jdbcType=VARCHAR}, #{directiveNumber, jdbcType=VARCHAR},
        #{workorderId, jdbcType=VARCHAR}, #{sn, jdbcType=VARCHAR}, #{brand, jdbcType=VARCHAR},
        #{boardSn, jdbcType=VARCHAR}, #{model, jdbcType=VARCHAR}, #{stationName, jdbcType=VARCHAR},
        #{startedTime, jdbcType=TIMESTAMP}, #{finishedTime, jdbcType=TIMESTAMP}, #{result, jdbcType=VARCHAR},
        #{message, jdbcType=VARCHAR}, #{ossFileKey, jdbcType=VARCHAR}, #{manufacturerName, jdbcType=VARCHAR},
        #{enabledFlag, jdbcType=VARCHAR}, #{createDate, jdbcType=TIMESTAMP}, #{lastUpdatedDate, jdbcType=TIMESTAMP},
        #{createBy, jdbcType=VARCHAR}, #{lastUpdatedBy, jdbcType=VARCHAR})
    </insert>

    <insert id="batchInsertOrUpdate">
        WITH T AS (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.requestId, jdbcType=VARCHAR} AS requestId,
            #{item.stationId, jdbcType=VARCHAR} AS stationId,
            #{item.type, jdbcType=VARCHAR} AS type,
            #{item.directiveNumber, jdbcType=VARCHAR} AS directiveNumber,
            #{item.workorderId, jdbcType=VARCHAR} AS workorderId,
            #{item.sn, jdbcType=VARCHAR} AS sn,
            #{item.nodeSn, jdbcType=VARCHAR} AS nodeSn,
            #{item.brand, jdbcType=VARCHAR} AS brand,
            #{item.boardSn, jdbcType=VARCHAR} AS boardSn,
            #{item.model, jdbcType=VARCHAR} AS model,
            #{item.stationName, jdbcType=VARCHAR} AS stationName,
            #{item.startedTime, jdbcType=TIMESTAMP} AS startedTime,
            #{item.finishedTime, jdbcType=TIMESTAMP} AS finishedTime,
            #{item.result, jdbcType=VARCHAR} AS result,
            #{item.message, jdbcType=VARCHAR} AS message,
            #{item.ossFileKey, jdbcType=VARCHAR} AS ossFileKey,
            #{item.manufacturerName, jdbcType=VARCHAR} AS manufacturerName,
            #{item.actionMsg, jdbcType=VARCHAR} AS action_msg,
            #{item.actionCode, jdbcType=VARCHAR} AS action_code,
            #{item.reworkTime, jdbcType=VARCHAR} AS rework_time,
            #{item.finishReworkTime, jdbcType=VARCHAR} AS finish_rework_time,
            SYSDATE AS createDate,
            SYSDATE AS lastUpdatedDate,
            #{item.lastUpdatedBy, jdbcType=VARCHAR} AS lastUpdatedBy,
            #{item.createBy, jdbcType=VARCHAR} AS createBy,
            #{item.factoryId, jdbcType=VARCHAR} AS factoryId,
            'Y' AS enabledFlag
        </foreach>
        ),
        updated AS (
        UPDATE PUSH_MODEL_SN_TEST_RECORD T1
        SET
        T1.LAST_UPDATED_BY = t.lastUpdatedBy,
        T1.LAST_UPDATED_DATE = SYSDATE
        from T
        WHERE T.requestid = T1.request_id
        returning T.*
        )
        INSERT INTO PUSH_MODEL_SN_TEST_RECORD(
        request_id,
        station_id,
        type,
        directive_number,
        workorder_id,
        sn,
        node_sn,
        brand,
        board_sn,
        model,
        station_name,
        started_time,
        finished_time,
        result,
        message,
        oss_file_key,
        manufacturer_name,
        action_msg,
        action_code,
        finish_rework_time,
        rework_time,
        enabled_flag,
        create_date,
        last_updated_date,
        create_by,
        last_updated_by,
        factory_Id
        )
        SELECT
        requestId,
        stationId,
        type,
        directiveNumber,
        workorderId,
        sn,
        nodeSn,
        brand,
        boardSn,
        model,
        stationName,
        startedTime,
        finishedTime,
        result,
        message,
        ossFileKey,
        manufacturerName,
        action_msg,
        action_code,
        finish_rework_time,
        rework_time,
        enabledFlag,
        createDate,
        lastUpdatedDate,
        createBy,
        lastUpdatedBy,
        factoryId
        FROM T WHERE NOT EXISTS (SELECT requestId FROM updated WHERE requestId = T.requestId )
    </insert>

    <update id="updateSelectiveById">
        update push_model_sn_test_record
        <set>
            last_updated_date = SYSDATE,
            <if test="uploadStatus != null">
                upload_status = #{uploadStatus, jdbcType=VARCHAR},
            </if>
            <if test="stationUploadStatus != null">
                station_upload_status = #{stationUploadStatus, jdbcType=VARCHAR},
            </if>
            <if test="fileUploadStatus != null">
                file_upload_status = #{fileUploadStatus, jdbcType=VARCHAR},
            </if>
            <if test="uploadMsg != null">
                upload_msg = #{uploadMsg, jdbcType=VARCHAR},
            </if>
            <if test="stationUploadMsg != null">
                station_upload_msg = #{stationUploadMsg, jdbcType=VARCHAR},
            </if>
            <if test="fileUploadMsg != null">
                file_Upload_msg = #{fileUploadMsg, jdbcType=VARCHAR},
            </if>
        </set>
        where enabled_flag ='Y' and request_id = #{requestId, jdbcType=VARCHAR}
    </update>

    <delete id="deleteByIds">
        delete from push_model_sn_test_record where request_id in
        <foreach collection="requestIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="selectById" resultType="com.zte.domain.model.PushModelSnTestRecord">
        select
        <include refid="Base_Column_List"/>
        from push_model_sn_test_record where enabled_flag='Y' and request_id = #{requestId, jdbcType=VARCHAR}
    </select>

    <select id="selectByIdList" resultType="com.zte.domain.model.PushModelSnTestRecord">
        select
        <include refid="Base_Column_List"/>
        from push_model_sn_test_record where enabled_flag='Y' and station_id  in
        <foreach collection="stationIdList" index="index" item="stationId" open="(" separator="," close=")">
            #{stationId}
        </foreach>
    </select>

    <select id="selectBySn" resultType="com.zte.domain.model.PushModelSnTestRecord">
        select
        <include refid="Base_Column_List"/>
        from push_model_sn_test_record where enabled_flag='Y' and sn = #{sn, jdbcType=VARCHAR}
    </select>

    <select id="queryUploadSuccessList" parameterType="com.zte.interfaces.dto.PushModelSnTestRecordPageQueryDTO"
            resultType="com.zte.domain.model.PushModelSnTestRecord">
        select
        <include refid="Base_Column_List"/>
        from push_model_sn_test_record t
        where  t.enabled_flag= 'Y' and t.upload_status ='1' and t.last_updated_date >= sysdate - #{preDays, jdbcType=DECIMAL}
        <if test="result != null and result != ''">
            and t.result = #{result, jdbcType=VARCHAR}
        </if>
        <if test="stationUploadStatus != null and stationUploadStatus != ''">
            and t.station_upload_status = #{stationUploadStatus, jdbcType=VARCHAR}
        </if>
        <if test="fileUploadStatus != null and fileUploadStatus != ''">
            and t.file_upload_status = #{fileUploadStatus, jdbcType=VARCHAR}
        </if>
        <if test="lastUpdatedDate != null ">
            and t.last_updated_date >= #{lastUpdatedDate, jdbcType=TIMESTAMP}
        </if>
        <if test="lastId != null and lastId !='' ">
            and not exists (select 1 from push_model_sn_test_record c where c.request_id = t.request_id and c.enabled_flag ='Y' and c.request_id &lt;= #{lastId, jdbcType=VARCHAR}
            and c.last_updated_date = #{lastUpdatedDate, jdbcType=TIMESTAMP})
        </if>
        order by last_updated_date,request_id
        limit 1000
    </select>

</mapper>