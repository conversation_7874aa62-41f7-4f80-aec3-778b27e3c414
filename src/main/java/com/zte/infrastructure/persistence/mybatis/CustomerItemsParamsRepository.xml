<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.CustomerItemsParamsRepository">

    <!--cpu 表-->
    <insert id="batchInsertCpuInfo" parameterType="java.util.List">
        INSERT INTO CPU_INFO
        (
            ID,CPU_FREQUENCY,
            CPU_CORES,CPU_THREADS,CPU_TYPE,
            CPU_MICROCODE_VERSION,CREATE_BY,
            CREATE_DATE,LAST_UPDATED_BY,
            LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=VARCHAR},#{item.cpuFrequency,jdbcType=VARCHAR},
                #{item.cpuCores,jdbcType=INTEGER},#{item.cpuThreads,jdbcType=INTEGER},
                #{item.cpuType,jdbcType=VARCHAR},#{item.cpuMicrocodeVersion,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR},SYSDATE,
                #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateCpuInfo" parameterType="com.zte.interfaces.dto.bytedance.CpuInfoDTO">
        UPDATE CPU_INFO
        set
        CPU_FREQUENCY = #{cpuFrequency,jdbcType=VARCHAR},
        CPU_CORES = #{cpuCores,jdbcType=INTEGER},
        CPU_THREADS = #{cpuThreads,jdbcType=INTEGER},
        CPU_TYPE = #{cpuType,jdbcType=VARCHAR},
        CPU_MICROCODE_VERSION = #{cpuMicrocodeVersion,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectCpuInfoById" resultType="com.zte.interfaces.dto.bytedance.CpuInfoDTO">
        SELECT
        ID,CPU_FREQUENCY,
        CPU_CORES,CPU_THREADS,CPU_TYPE,
        CPU_MICROCODE_VERSION,CREATE_BY,
        CREATE_DATE,LAST_UPDATED_BY,
        LAST_UPDATED_DATE
        FROM CPU_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteCpuInfoById" >
        DELETE FROM CPU_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
               and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchCpuInfoByIds" resultType="com.zte.interfaces.dto.bytedance.CpuInfoDTO">
        SELECT
        ID,CPU_FREQUENCY,
        CPU_CORES,CPU_THREADS,CPU_TYPE,
        CPU_MICROCODE_VERSION
        FROM CPU_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!-- 内存表-->
    <insert id="batchInsertMemoryInfo" parameterType="java.util.List">
        INSERT INTO MEMORY_INFO
        (
            ID,CAPACITY,
            FREQUENCY,GENERATION,
            GRADE,CREATE_BY,
            CREATE_DATE,LAST_UPDATED_BY, LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=VARCHAR},#{item.capacity,jdbcType=VARCHAR},
                #{item.frequency,jdbcType=VARCHAR},#{item.generation,jdbcType=VARCHAR},
                #{item.grade,jdbcType=VARCHAR},#{item.createBy,jdbcType=VARCHAR},
                SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateMemoryInfo" parameterType="com.zte.interfaces.dto.bytedance.MemoryInfoDTO">
        UPDATE MEMORY_INFO
        set
        CAPACITY = #{capacity,jdbcType=VARCHAR},
        FREQUENCY = #{frequency,jdbcType=VARCHAR},
        GENERATION = #{generation,jdbcType=VARCHAR},
        GRADE = #{grade,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectMemoryInfoById" resultType="com.zte.interfaces.dto.bytedance.MemoryInfoDTO">
        SELECT
        ID,CAPACITY,
        FREQUENCY,GENERATION,
        GRADE,CREATE_BY,
        CREATE_DATE,LAST_UPDATED_BY, LAST_UPDATED_DATE
        FROM MEMORY_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteMemoryInfoById" >
        DELETE FROM MEMORY_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchMemoryInfoByIds" resultType="com.zte.interfaces.dto.bytedance.MemoryInfoDTO">
        SELECT
        ID,CAPACITY,
        FREQUENCY,GENERATION,
        GRADE
        FROM MEMORY_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--网卡相关-->
    <insert id="batchInsertNetworkCardInfo" parameterType="java.util.List">
        INSERT INTO NETWORK_CARD_INFO
        (
        ID,NETWORK_CARD_INTERFACE,
        PORT_COUNT, PORT_TYPE,
        CHIP_BRAND,CHIP_MODEL, PORT_SPEED,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.networkCardInterface,jdbcType=VARCHAR},
            #{item.portCount,jdbcType=INTEGER},#{item.portType,jdbcType=VARCHAR},
            #{item.chipBrand,jdbcType=VARCHAR},#{item.chipModel,jdbcType=VARCHAR},
            #{item.portSpeed,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateNetworkCardInfo" parameterType="com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO">
        UPDATE NETWORK_CARD_INFO
        set
        NETWORK_CARD_INTERFACE = #{networkCardInterface,jdbcType=VARCHAR},
        PORT_COUNT = #{portCount,jdbcType=INTEGER},
        PORT_TYPE = #{portType,jdbcType=VARCHAR},
        CHIP_BRAND = #{chipBrand,jdbcType=VARCHAR},
        CHIP_MODEL = #{chipModel,jdbcType=VARCHAR},
        PORT_SPEED = #{portSpeed,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectNetworkCardInfoById" resultType="com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO">
        SELECT
        ID,NETWORK_CARD_INTERFACE,
        PORT_COUNT, PORT_TYPE,
        CHIP_BRAND,CHIP_MODEL, PORT_SPEED,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM NETWORK_CARD_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteNetworkCardInfoById" >
        DELETE FROM NETWORK_CARD_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchNetworkCardInfoByIds" resultType="com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO">
        SELECT
        ID,NETWORK_CARD_INTERFACE,
        PORT_COUNT, PORT_TYPE,
        CHIP_BRAND,CHIP_MODEL, PORT_SPEED
        FROM NETWORK_CARD_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--raid_card_info-->
    <insert id="batchInsertRaidCardInfo" parameterType="java.util.List">
        INSERT INTO RAID_CARD_INFO
        (
        ID,RAID_CARD_BRAND,RAID_CARD_MODEL,
        RAID_CARD_INTERFACE,CACHE_SIZE,BATTERY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.raidCardBrand,jdbcType=VARCHAR}, #{item.raidCardModel,jdbcType=VARCHAR},
            #{item.raidCardInterface,jdbcType=VARCHAR},
            #{item.cacheSize,jdbcType=VARCHAR},#{item.battery,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateRaidCardInfo" parameterType="com.zte.interfaces.dto.bytedance.RaidCardInfoDTO">
        UPDATE RAID_CARD_INFO
        set
        RAID_CARD_BRAND = #{raidCardBrand,jdbcType=VARCHAR},
        RAID_CARD_MODEL = #{raidCardModel,jdbcType=VARCHAR},
        RAID_CARD_INTERFACE = #{raidCardInterface,jdbcType=VARCHAR},
        CACHE_SIZE = #{cacheSize,jdbcType=VARCHAR},
        BATTERY= #{battery,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectRaidCardInfoById" resultType="com.zte.interfaces.dto.bytedance.RaidCardInfoDTO">
        SELECT
        ID,RAID_CARD_BRAND,RAID_CARD_MODEL,
        RAID_CARD_INTERFACE,CACHE_SIZE,BATTERY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM RAID_CARD_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteRaidCardInfoById" >
        DELETE FROM RAID_CARD_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchRaidCardInfoByIds" resultType="com.zte.interfaces.dto.bytedance.RaidCardInfoDTO">
        SELECT
        ID,RAID_CARD_BRAND,RAID_CARD_MODEL,
        RAID_CARD_INTERFACE,CACHE_SIZE,BATTERY
        FROM RAID_CARD_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--hard_disk_info-->
    <insert id="batchInsertHardDiskInfo" parameterType="java.util.List">
        INSERT INTO HARD_DISK_INFO
        (
        ID,HARD_DISK_MEDIA,HARD_DISK_SIZE,
        ROTATIONAL_SPEED,TRANSFER_RATE,CAPACITY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG,HARD_DISK_INTERFACE
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.hardDiskMedia,jdbcType=VARCHAR},
            #{item.hardDiskSize,jdbcType=VARCHAR},
            #{item.rotationalSpeed,jdbcType=VARCHAR},
            #{item.transferRate,jdbcType=VARCHAR},#{item.capacity,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y',
            #{item.hardDiskInterface,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateHardDiskInfo" parameterType="com.zte.interfaces.dto.bytedance.HardDiskInfoDTO">
        UPDATE HARD_DISK_INFO
        set
        HARD_DISK_MEDIA = #{hardDiskMedia,jdbcType=VARCHAR},
        HARD_DISK_SIZE = #{hardDiskSize,jdbcType=VARCHAR},
        ROTATIONAL_SPEED = #{rotationalSpeed,jdbcType=VARCHAR},
        TRANSFER_RATE = #{transferRate,jdbcType=VARCHAR},
        CAPACITY = #{capacity,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate,
        HARD_DISK_INTERFACE = #{hardDiskInterface,jdbcType=VARCHAR}
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectHardDiskInfoById" resultType="com.zte.interfaces.dto.bytedance.HardDiskInfoDTO">
        SELECT
        ID,HARD_DISK_MEDIA,HARD_DISK_SIZE,
        ROTATIONAL_SPEED,TRANSFER_RATE,CAPACITY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,HARD_DISK_INTERFACE
        FROM HARD_DISK_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteHardDiskInfoById" >
        DELETE FROM HARD_DISK_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>


    <select id="queryBatchHardDiskInfoByIds" resultType="com.zte.interfaces.dto.bytedance.HardDiskInfoDTO">
        SELECT
        ID,HARD_DISK_MEDIA,HARD_DISK_SIZE,
        ROTATIONAL_SPEED,TRANSFER_RATE,CAPACITY,HARD_DISK_INTERFACE
        FROM HARD_DISK_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--GPU-->
    <insert id="batchInsertGpuInfo" parameterType="java.util.List">
        INSERT INTO GPU_INFO
        (
        ID,MEMORY,PLATFORM,GPU_INTERFACE,CAPACITY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.memory,jdbcType=VARCHAR},
            #{item.platform,jdbcType=VARCHAR},
            #{item.gpuInterface,jdbcType=VARCHAR},
            #{item.capacity,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateGpuInfo" parameterType="com.zte.interfaces.dto.bytedance.GpuInfoDTO">
        UPDATE GPU_INFO
        set
        MEMORY = #{memory,jdbcType=VARCHAR},
        PLATFORM = #{platform,jdbcType=VARCHAR},
        GPU_INTERFACE = #{gpuInterface,jdbcType=VARCHAR},
        CAPACITY = #{capacity,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectGpuInfoById" resultType="com.zte.interfaces.dto.bytedance.GpuInfoDTO">
        SELECT
        ID,MEMORY,PLATFORM,GPU_INTERFACE,CAPACITY,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM GPU_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteGpuInfoById" >
        DELETE FROM GPU_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchGpuInfoByIds" resultType="com.zte.interfaces.dto.bytedance.GpuInfoDTO">
        SELECT
        ID,MEMORY,PLATFORM,GPU_INTERFACE,CAPACITY
        FROM GPU_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--motherboard_info-->
    <insert id="batchInsertMotherboardInfo" parameterType="java.util.List">
        INSERT INTO MOTHERBOARD_INFO
        (
        ID,MOTHER_BOARD_VERSION,pcb_Version,vr_chip_code,vr_chip_power,epld_chip_code,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.motherBoardVersion,jdbcType=VARCHAR},#{item.pcbVersion,jdbcType=VARCHAR},
            #{item.vrChipCode,jdbcType=VARCHAR},#{item.vrChipPower,jdbcType=VARCHAR},#{item.epldChipCode,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateMotherboardInfo" parameterType="com.zte.interfaces.dto.bytedance.MotherboardInfoDTO">
        UPDATE MOTHERBOARD_INFO
        set
        MOTHER_BOARD_VERSION = #{motherBoardVersion,jdbcType=VARCHAR}, PCB_VERSION = #{pcbVersion,jdbcType=VARCHAR},
        vr_chip_code = #{vrChipCode,jdbcType=VARCHAR},vr_chip_power = #{vrChipPower,jdbcType=VARCHAR},epld_chip_code = #{epldChipCode,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectMotherboardInfoById" resultType="com.zte.interfaces.dto.bytedance.MotherboardInfoDTO">
        SELECT
        ID,MOTHER_BOARD_VERSION,PCB_VERSION,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM MOTHERBOARD_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteMotherboardInfoById" >
        DELETE FROM MOTHERBOARD_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchMotherboardInfoByIds" resultType="com.zte.interfaces.dto.bytedance.MotherboardInfoDTO">
        SELECT
        ID,MOTHER_BOARD_VERSION,PCB_VERSION,vr_chip_code,vr_chip_power,epld_chip_code
        FROM MOTHERBOARD_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--machine_info-->
    <insert id="batchInsertMachineInfo" parameterType="java.util.List">
        INSERT INTO MACHINE_INFO
        (
        ID,SERVER_HEIGHT,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.serverHeight,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updateMachineInfo" parameterType="com.zte.interfaces.dto.bytedance.MachineInfoDTO">
        UPDATE MACHINE_INFO
        set
        SERVER_HEIGHT = #{serverHeight,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectMachineInfoById" resultType="com.zte.interfaces.dto.bytedance.MachineInfoDTO">
        SELECT
        ID,SERVER_HEIGHT,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM MACHINE_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteMachineInfoById" >
        DELETE FROM MACHINE_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchMachineInfoByIds" resultType="com.zte.interfaces.dto.bytedance.MachineInfoDTO">
        SELECT
        ID,SERVER_HEIGHT
        FROM MACHINE_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!--POWER_SOURCE_INFO-->
    <insert id="batchInsertPowerSourceInfo" parameterType="java.util.List">
        INSERT INTO POWER_SOURCE_INFO
        (
        ID,POWER_SUPPLY,MAX_INPUT_CURRENT,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED_FLAG
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},#{item.powerSupply,jdbcType=VARCHAR},#{item.maxInputCurrent,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},SYSDATE, #{item.lastUpdatedBy,jdbcType=VARCHAR},SYSDATE,'Y'
            )
        </foreach>
    </insert>

    <update id="updatePowerSourceInfo" parameterType="com.zte.interfaces.dto.bytedance.PowerSourceInfoDTO">
        UPDATE POWER_SOURCE_INFO
        set
        POWER_SUPPLY = #{powerSupply,jdbcType=VARCHAR},
        MAX_INPUT_CURRENT = #{maxInputCurrent,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate
        where id = #{id} AND ENABLED_FLAG = 'Y'
    </update>

    <select id="selectPowerSourceInfoById" resultType="com.zte.interfaces.dto.bytedance.PowerSourceInfoDTO">
        SELECT
        ID,POWER_SUPPLY,MAX_INPUT_CURRENT,
        CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE
        FROM POWER_SOURCE_INFO
        WHERE ID = #{id} AND ENABLED_FLAG = 'Y'
    </select>

    <delete id="deletePowerSourceInfoById" >
        DELETE FROM POWER_SOURCE_INFO
        where  1 = 1
        <choose>
            <when test="id != null and id != ''">
                and  id = #{id}
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </delete>

    <select id="queryBatchPowerSourceInfoByIds" resultType="com.zte.interfaces.dto.bytedance.PowerSourceInfoDTO">
        SELECT
        ID,POWER_SUPPLY,MAX_INPUT_CURRENT
        FROM POWER_SOURCE_INFO
        WHERE ENABLED_FLAG = 'Y'
        <choose>
            <when test="idList != null and idList.size()>0">
                and ID IN
                <foreach collection="idList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

</mapper>
