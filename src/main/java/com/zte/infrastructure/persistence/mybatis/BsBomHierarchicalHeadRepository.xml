<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.BsBomHierarchicalHeadRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.BsBomHierarchicalHead">
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="BOM_CODE" jdbcType="VARCHAR" property="bomCode"/>
        <result column="BOM_NAME" jdbcType="VARCHAR" property="bomName"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <collection property="details" resultMap="BaseResultDetailMap" columnPrefix="d_"/>
    </resultMap>

    <resultMap id="BaseResultDetailMap" type="com.zte.domain.model.BsBomHierarchicalDetail">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode"/>
        <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName"/>
        <result column="TRACE_CODE" jdbcType="VARCHAR" property="traceCode"/>
        <result column="TRACE_NAME" jdbcType="VARCHAR" property="traceName"/>
        <result column="DELIVERY_PROCESS" jdbcType="VARCHAR" property="deliveryProcess"/>
        <result column="IS_PRE_MANU" jdbcType="VARCHAR" property="isPreManu"/>
        <result column="TAG_NUM" jdbcType="VARCHAR" property="tagNum"/>
        <result column="PRE_MANU_TYPE" jdbcType="VARCHAR" property="preManuType"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <result column="SORT_SEQ" jdbcType="DECIMAL" property="sortSeq"/>
        <result column="STYLE" jdbcType="VARCHAR" property="style"/>
        <result column="ABC_TYPE" jdbcType="VARCHAR" property="abcType"/>
        <result column="BOM_CODE" jdbcType="VARCHAR" property="bomCode"/>
        <result column="BOM_NAME" jdbcType="VARCHAR" property="bomName"/>
        <result column="IS_SMT" jdbcType="VARCHAR" property="isSmt"/>
        <result column="SUB_LEVEL" jdbcType="VARCHAR" property="subLevel"/>
        <result column="ITEM_QTY" jdbcType="DECIMAL" property="itemQty"/>
        <result column="SPLIT_BEFORE" jdbcType="VARCHAR" property="deliveryProcessSplitBefore"/>
        <result column="bake_temperature" jdbcType="DECIMAL" property="bakeTemperature"/>
        <result column="bake_time" jdbcType="DECIMAL" property="bakeTime"/>
        <result column="bake_remark" jdbcType="VARCHAR" property="bakeRemark"/>
    </resultMap>

    <sql id="Base_Column_List">
        HEAD_ID, BOM_CODE, BOM_NAME, CREATE_BY, CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
        ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID
    </sql>

    <insert id="batchInsertBsBomHead" parameterType="java.util.List">
        insert into BS_BOM_HIERARCHICAL_HEAD (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.headId,jdbcType=VARCHAR},
            #{item.bomCode,jdbcType=VARCHAR},
            #{item.bomName,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},
            sysdate,
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            sysdate,
            'Y',
            #{item.orgId,jdbcType=DECIMAL},
            #{item.factoryId,jdbcType=DECIMAL},
            #{item.entityId,jdbcType=DECIMAL}
            )
        </foreach>

    </insert>

    <update id="updateBatchByBomCode" parameterType="java.util.List">
        update BS_BOM_HIERARCHICAL_HEAD set ENABLED_FLAG='N', LAST_UPDATED_DATE = sysdate
        WHERE ENABLED_FLAG='Y' AND
        BOM_CODE in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteBatchByBomCode" parameterType="java.util.List">
        delete from BS_BOM_HIERARCHICAL_HEAD
        WHERE ENABLED_FLAG='N' AND
        BOM_CODE in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_date <![CDATA[
            < sysdate - 30
        ]]>
    </delete>

    <select id="getListByProductList" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        H.HEAD_ID,H.BOM_CODE,H.BOM_NAME,H.CREATE_BY,H.CREATE_DATE,H.LAST_UPDATED_BY,H.LAST_UPDATED_DATE,H.ENABLED_FLAG,H.ORG_ID,
        H.FACTORY_ID,H.ENTITY_ID,
        D.ID AS D_ID ,D.HEAD_ID AS D_HEAD_ID ,D.ITEM_NO AS D_ITEM_NO ,D.ITEM_NAME AS D_ITEM_NAME ,D.TYPE_CODE AS
        D_TYPE_CODE ,D.TYPE_NAME AS D_TYPE_NAME ,
        D.TRACE_CODE AS D_TRACE_CODE ,D.TRACE_NAME AS D_TRACE_NAME ,D.DELIVERY_PROCESS AS D_DELIVERY_PROCESS
        ,D.IS_PRE_MANU AS D_IS_PRE_MANU ,D.TAG_NUM AS D_TAG_NUM ,
        D.PRE_MANU_TYPE AS D_PRE_MANU_TYPE ,D.REMARK AS D_REMARK ,D.ATTRIBUTE1 AS D_ATTRIBUTE1 ,D.ATTRIBUTE2 AS
        D_ATTRIBUTE2 ,D.CREATE_BY AS D_CREATE_BY ,
        D.CREATE_DATE AS D_CREATE_DATE ,D.LAST_UPDATED_BY AS D_LAST_UPDATED_BY ,D.LAST_UPDATED_DATE AS
        D_LAST_UPDATED_DATE ,D.ENABLED_FLAG AS D_ENABLED_FLAG ,
        D.ORG_ID AS D_ORG_ID ,D.FACTORY_ID AS D_FACTORY_ID ,D.ENTITY_ID AS D_ENTITY_ID ,D.SORT_SEQ AS D_SORT_SEQ
        ,D.STYLE AS D_STYLE ,D.ABC_TYPE AS D_ABC_TYPE ,
        D.BOM_CODE AS D_BOM_CODE ,D.BOM_NAME AS D_BOM_NAME ,D.IS_SMT AS D_IS_SMT ,D.SUB_LEVEL AS D_SUB_LEVEL ,D.ITEM_QTY
        AS D_ITEM_QTY ,
        D.DELIVERY_PROCESS_SPLIT_BEFORE AS D_SPLIT_BEFORE,D.bake_temperature d_bake_temperature,D.bake_time
        d_bake_time,D.bake_remark d_bake_remark
        FROM BS_BOM_HIERARCHICAL_HEAD H
        LEFT JOIN BS_BOM_HIERARCHICAL_DETAIL D ON H.HEAD_ID = D.HEAD_ID
        WHERE H.ENABLED_FLAG = 'Y' AND
        D.ENABLED_FLAG = 'Y'
        AND H.BOM_CODE IN
        <foreach collection="productCodeList" separator="," index="index" open="(" close=")">
            #{productCodeList[${index}]}
        </foreach>
    </select>

    <select id="getAssembleBomInfo" parameterType="com.zte.domain.model.BsPremanuItemInfo" resultMap="BaseResultMap">
        select
        H.HEAD_ID,H.BOM_CODE,H.BOM_NAME,H.CREATE_BY,H.CREATE_DATE,H.LAST_UPDATED_BY,H.LAST_UPDATED_DATE,H.ENABLED_FLAG,H.ORG_ID,
        H.FACTORY_ID,H.ENTITY_ID,
        D.ID AS D_ID ,D.HEAD_ID AS D_HEAD_ID ,D.ITEM_NO AS D_ITEM_NO ,D.ITEM_NAME AS D_ITEM_NAME ,D.TYPE_CODE AS
        D_TYPE_CODE ,D.TYPE_NAME AS D_TYPE_NAME ,
        D.TRACE_CODE AS D_TRACE_CODE ,D.TRACE_NAME AS D_TRACE_NAME ,D.DELIVERY_PROCESS AS D_DELIVERY_PROCESS
        ,D.IS_PRE_MANU AS D_IS_PRE_MANU ,D.TAG_NUM AS D_TAG_NUM ,
        D.PRE_MANU_TYPE AS D_PRE_MANU_TYPE ,D.REMARK AS D_REMARK ,D.ATTRIBUTE1 AS D_ATTRIBUTE1 ,D.ATTRIBUTE2 AS
        D_ATTRIBUTE2 ,D.CREATE_BY AS D_CREATE_BY ,
        D.CREATE_DATE AS D_CREATE_DATE ,D.LAST_UPDATED_BY AS D_LAST_UPDATED_BY ,D.LAST_UPDATED_DATE AS
        D_LAST_UPDATED_DATE ,D.ENABLED_FLAG AS D_ENABLED_FLAG ,
        D.ORG_ID AS D_ORG_ID ,D.FACTORY_ID AS D_FACTORY_ID ,D.ENTITY_ID AS D_ENTITY_ID ,D.SORT_SEQ AS D_SORT_SEQ
        ,D.STYLE AS D_STYLE ,D.ABC_TYPE AS D_ABC_TYPE ,
        D.BOM_CODE AS D_BOM_CODE ,D.BOM_NAME AS D_BOM_NAME ,D.IS_SMT AS D_IS_SMT ,D.SUB_LEVEL AS D_SUB_LEVEL ,D.ITEM_QTY
        AS D_ITEM_QTY ,
        D.DELIVERY_PROCESS_SPLIT_BEFORE AS D_SPLIT_BEFORE,D.bake_temperature d_bake_temperature,D.bake_time
        d_bake_time,D.bake_remark d_bake_remark
        from bs_bom_hierarchical_head h
        left join bs_bom_hierarchical_detail d on h.head_id=d.head_id
        where 1=1
        and h.enabled_flag='Y'
        and d.DELIVERY_PROCESS_SPLIT_BEFORE = '装焊配送'
        and (d.TYPE_CODE is null or d.TYPE_CODE = '')
        <if test="bomCodeList != null and bomCodeList.size > 0">
            and h.bom_code in
            <foreach item="item" index="index" collection="bomCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bomCodeList == null or bomCodeList.size == 0">and 1=2</if>
    </select>
</mapper>
