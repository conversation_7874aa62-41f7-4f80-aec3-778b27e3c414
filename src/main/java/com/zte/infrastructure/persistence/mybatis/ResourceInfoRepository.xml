<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.ResourceInfoRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.ResourceInfoEntityDTO" id="resourceInfoMap">
        <result property="resourceId" column="RESOURCE_ID" />
        <result property="resourceType" column="RESOURCE_TYPE" />
        <result property="resourceNo" column="RESOURCE_NO" />
        <result property="resourceStart" column="RESOURCE_START" />
        <result property="resourceEnd" column="RESOURCE_END" />
        <result property="productCategory" column="PRODUCT_CATEGORY" />
        <result property="productSmallClass" column="PRODUCT_SMALL_CLASS" />
        <result property="resourceAmount" column="RESOURCE_AMOUNT" />
        <result property="usageScope" column="USAGE_SCOPE" />
        <result property="custInfo" column="CUST_INFO" />
        <result property="sourceSystem" column="SOURCE_SYSTEM" />
        <result property="resourceStatus" column="RESOURCE_STATUS" />
        <result property="remark" column="REMARK" />
        <result property="createBy" column="CREATE_BY" />
        <result property="createDate" column="CREATE_DATE" />
        <result property="lastUpdatedBy" column="LAST_UPDATED_BY" />
        <result property="lastUpdatedDate" column="LAST_UPDATED_DATE" />
        <result property="enabledFlag" column="ENABLED_FLAG" />
        <result property="orgId" column="ORG_ID" />
        <result property="factoryId" column="FACTORY_ID" />
        <result property="entityId" column="ENTITY_ID" />
        <result property="attribute1" column="ATTRIBUTE1" />
        <result property="attribute2" column="ATTRIBUTE2" />
        <result property="attribute3" column="ATTRIBUTE3" />
        <result property="attribute4" column="ATTRIBUTE4" />
        <result property="attribute5" column="ATTRIBUTE5" />
        <result property="availableQuantity" column="AVAILABLE_QUANTITY" />
        <result property="deviceType" column="DEVICE_TYPE" />
        <result property="expiryDate" column="EXPIRY_DATE" />
        <result property="prodAddress" column="PROD_ADDRESS" />
        <result property="productName" column="PRODUCT_NAME" />
        <result property="productLine" column="PRODUCT_LINE" />
        <result property="certName" column="CERT_NAME" />
        <result property="tagApplicant" column="tag_applicant" />
        <result property="backPassRatio" column="back_pass_ratio" />
        <result property="backPassWarning" column="back_pass_warning" />
        <result property="calPassRatioDate" column="cal_pass_ratio_date" />
        <result property="certAdministrator" column="CERT_ADMINISTRATOR" />
        <result property="lowWaterWarningFlag" column="LOW_WATER_WARNING_FLAG" />
        <result property="expiryWarningFlag" column="EXPIRY_WARNING_FLAG" />
        <result property="firstLowWaterWarningDate" column="FIRST_LOW_WATER_WARNING_DATE" />
        <result property="firstExpiryWarningDate" column="FIRST_EXPIRY_WARNING_DATE" />
        <result property="benchmarkQty" column="BENCHMARK_QTY" />
        <result property="modelNumber" column="MODEL_NUMBER" />
    </resultMap>

    <sql id="Base_Column_List">
      RESOURCE_ID,
      RESOURCE_TYPE,
      RESOURCE_NO,
      RESOURCE_START,
      RESOURCE_END,
      PRODUCT_CATEGORY,
      PRODUCT_SMALL_CLASS,
      RESOURCE_AMOUNT,
      USAGE_SCOPE,
      CUST_INFO,
      SOURCE_SYSTEM,
      RESOURCE_STATUS,
      REMARK,
      CREATE_BY,
      CREATE_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATED_DATE,
      ENABLED_FLAG,
      ORG_ID,
      FACTORY_ID,
      ENTITY_ID,
      ATTRIBUTE1,
      ATTRIBUTE2,
      ATTRIBUTE3,
      ATTRIBUTE4,
      ATTRIBUTE5,
      AVAILABLE_QUANTITY,
      DEVICE_TYPE,
      EXPIRY_DATE,
      PROD_ADDRESS,
      PRODUCT_NAME,
      PRODUCT_LINE,
      CERT_NAME,
      CERT_ADMINISTRATOR,
      MODEL_NUMBER
    </sql>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="resourceInfoMap">
      select
        t.RESOURCE_ID,
        t.RESOURCE_TYPE,
        t.RESOURCE_NO,
        t.RESOURCE_START,
        t.RESOURCE_END,
        t.PRODUCT_CATEGORY,
        t.PRODUCT_SMALL_CLASS,
        t.RESOURCE_AMOUNT,
        t.USAGE_SCOPE,
        t.CUST_INFO,
        t.SOURCE_SYSTEM,
        t.RESOURCE_STATUS,
        t.REMARK,
        t.CREATE_BY,
        t.CREATE_DATE,
        t.LAST_UPDATED_BY,
        t.LAST_UPDATED_DATE,
        t.ENABLED_FLAG,
        t.ORG_ID,
        t.FACTORY_ID,
        t.ENTITY_ID,
        t.ATTRIBUTE1,
        t.ATTRIBUTE2,
        t.ATTRIBUTE3,
        t.ATTRIBUTE4,
        t.ATTRIBUTE5,
        t.AVAILABLE_QUANTITY,
        t.DEVICE_TYPE,
        t.EXPIRY_DATE,
        t.PROD_ADDRESS,
        t.PRODUCT_NAME,
        t.PRODUCT_LINE,
        t.CERT_NAME,
        t.MODEL_NUMBER,
        nvl(t.BENCHMARK_QTY,0) BENCHMARK_QTY,
        t.tag_applicant,
        t.back_pass_ratio,
        (CASE WHEN t.back_pass_warning = 'Y' THEN '是' ELSE '否' END ) back_pass_warning,
        t.cal_pass_ratio_date,
        t.CERT_ADMINISTRATOR,
        t1.LOW_WATER_WARNING_FLAG,
        t1.EXPIRY_WARNING_FLAG,
        t1.FIRST_LOW_WATER_WARNING_DATE,
        t1.FIRST_EXPIRY_WARNING_DATE
      from
        RESOURCE_INFO t
      left join
        RESOURCE_WARNING_RECORD t1 on t.RESOURCE_NO = t1.RESOURCE_NO and  t1.ENABLED_FLAG = 'Y'
      where
        1=1
       <if test="params != null and params.modelNumber != null and params.modelNumber != ''">and t.model_number = #{params.modelNumber}</if>
      <if test="params != null and params.resourceId != null and params.resourceId != ''">and t.RESOURCE_ID = #{params.resourceId}</if>
      <if test="params != null and params.resourceType != null and params.resourceType != ''">and t.RESOURCE_TYPE = #{params.resourceType}</if>
      <if test="params != null and params.resourceNo != null and params.resourceNo != ''">and t.RESOURCE_NO = #{params.resourceNo}</if>
	  <if test="params != null and params.resourceStr != null and params.resourceStr != ''">
		  and t.RESOURCE_START between #{params.resourceStrSub} and #{params.resourceStr}
		  and t.RESOURCE_END between #{params.resourceStr} and #{params.resourceStrAdd}
	  </if>
      <if test="params != null and params.resourceStart != null and params.resourceStart != ''">and t.RESOURCE_START = #{params.resourceStart}</if>
      <if test="params != null and params.resourceEnd != null and params.resourceEnd != ''">and t.RESOURCE_END = #{params.resourceEnd}</if>
      <if test="params != null and params.productCategory != null and params.productCategory != ''">and t.PRODUCT_CATEGORY = #{params.productCategory}</if>
      <if test="params != null and params.productSmallClass != null and params.productSmallClass != ''">and t.PRODUCT_SMALL_CLASS = #{params.productSmallClass}</if>
      <if test="params != null and params.resourceAmount != null and params.resourceAmount != ''">and t.RESOURCE_AMOUNT = #{params.resourceAmount}</if>
      <if test="params != null and params.usageScope != null and params.usageScope != ''">and t.USAGE_SCOPE = #{params.usageScope}</if>
      <if test="params != null and params.custInfo != null and params.custInfo != ''">and t.CUST_INFO LIKE concat(#{params.custInfo,jdbcType=VARCHAR},'%')</if>
      <if test="params != null and params.sourceSystem != null and params.sourceSystem != ''">and t.SOURCE_SYSTEM = #{params.sourceSystem}</if>
      <if test="params != null and params.resourceStatus != null and params.resourceStatus != ''">and t.RESOURCE_STATUS = #{params.resourceStatus}</if>
      <if test="params != null and params.remark != null and params.remark != ''">and t.REMARK = #{params.remark}</if>
      <if test="params != null and params.createBy != null and params.createBy != ''">and t.CREATE_BY = #{params.createBy}</if>
      <if test="params != null and params.createDate != null">and t.CREATE_DATE = #{params.createDate}</if>
      <if test="params != null and params.lastUpdatedBy != null and params.lastUpdatedBy != ''">and t.LAST_UPDATED_BY = #{params.lastUpdatedBy}</if>
      <if test="params != null and params.lastUpdatedDate != null">and t.LAST_UPDATED_DATE = #{params.lastUpdatedDate}</if>
      <if test="params != null and params.enabledFlag != null and params.enabledFlag != ''">and t.ENABLED_FLAG = #{params.enabledFlag}</if>
      <if test="params != null and params.orgId != null and params.orgId != ''">and t.ORG_ID = #{params.orgId}</if>
      <if test="params != null and params.factoryId != null and params.factoryId != ''">and t.FACTORY_ID = #{params.factoryId}</if>
      <if test="params != null and params.entityId != null and params.entityId != ''">and t.ENTITY_ID = #{params.entityId}</if>
      <if test="params != null and params.attribute1 != null and params.attribute1 != ''">and t.ATTRIBUTE1 = #{params.attribute1}</if>
      <if test="params != null and params.attribute2 != null and params.attribute2 != ''">and t.ATTRIBUTE2 = #{params.attribute2}</if>
      <if test="params != null and params.attribute3 != null and params.attribute3 != ''">and t.ATTRIBUTE3 = #{params.attribute3}</if>
      <if test="params != null and params.attribute4 != null and params.attribute4 != ''">and t.ATTRIBUTE4 = #{params.attribute4}</if>
      <if test="params != null and params.attribute5 != null and params.attribute5 != ''">and t.ATTRIBUTE5 = #{params.attribute5}</if>
      <if test="params != null and params.startTime != null and params.startTime != ''"> <![CDATA[and t.CREATE_DATE >= to_timestamp(#{params.startTime},'yyyy-mm-dd hh24:mi:ss')]]></if>
      <if test="params != null and params.endTime != null and params.endTime != ''"> <![CDATA[and t.CREATE_DATE <= to_timestamp(#{params.endTime},'yyyy-mm-dd hh24:mi:ss')]]></if>
      <if test="params != null and params.lowWaterWarningFlag != null and params.lowWaterWarningFlag != ''">and t1.LOW_WATER_WARNING_FLAG = #{params.lowWaterWarningFlag}</if>
      <if test="params != null and params.expiryWarningFlag != null and params.expiryWarningFlag != ''">and t1.expiry_warning_flag = #{params.expiryWarningFlag}</if>
        <if test="params != null and params.deviceType != null and params.deviceType != ''">and t.DEVICE_TYPE LIKE concat('%',#{params.deviceType},'%') </if>
        <if test="params != null and params.productLine != null and params.productLine != ''">and t.PRODUCT_LINE = #{params.productLine}</if>
        <if test="params != null and params.backPassWarning != null and params.backPassWarning != ''">and t.back_pass_warning = #{params.backPassWarning}</if>
        order by t.LAST_UPDATED_DATE desc, t.resource_id desc
    </select>

    <select id="selectOverlappingTime" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO" resultType="java.lang.Integer">
        select count(1) from RESOURCE_INFO T where T.ENABLED_FLAG = 'Y' AND
        T.RESOURCE_STATUS != 'LOSE_EFFICACY'
        AND T.RESOURCE_TYPE = #{resourceType}
        <if test="resourceId != null and resourceId != ''">and RESOURCE_ID <![CDATA[!=]]> #{resourceId}</if>
        AND  NOT (T.RESOURCE_START <![CDATA[>]]>#{resourceEnd} or T.RESOURCE_END <![CDATA[<]]> #{resourceStart})
        <!--AND ((T.RESOURCE_START <![CDATA[<=]]> #{resourceStart} AND T.RESOURCE_END <![CDATA[>=]]> #{resourceStart}) OR
        (T.RESOURCE_START <![CDATA[>]]> #{resourceStart} AND T.RESOURCE_START <![CDATA[<=]]> #{resourceEnd}))-->
    </select>

    <select id="getList" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO" resultMap="resourceInfoMap">
        select
        <include refid="Base_Column_List" />
        from RESOURCE_INFO where 1=1 and ENABLED_FLAG = 'Y'
        <if test="resourceId != null and resourceId != ''">and RESOURCE_ID = #{resourceId}</if>
        <if test="resourceType != null and resourceType != ''">and RESOURCE_TYPE = #{resourceType}</if>
        <if test="resourceNo != null and resourceNo != ''">and RESOURCE_NO = #{resourceNo}</if>
        <if test="resourceStart != null and resourceStart != ''">and RESOURCE_START = #{resourceStart}</if>
        <if test="resourceEnd != null and resourceEnd != ''">and RESOURCE_END = #{resourceEnd}</if>
        <if test="productCategory != null and productCategory != ''">and PRODUCT_CATEGORY = #{productCategory}</if>
        <if test="productSmallClass != null and productSmallClass != ''">and PRODUCT_SMALL_CLASS = #{productSmallClass}</if>
        <if test="resourceAmount != null and resourceAmount != ''">and RESOURCE_AMOUNT = #{resourceAmount}</if>
        <if test="usageScope != null and usageScope != ''">and USAGE_SCOPE = #{usageScope}</if>
        <if test="custInfo != null and custInfo != ''">and CUST_INFO = #{custInfo}</if>
        <if test="sourceSystem != null and sourceSystem != ''">and SOURCE_SYSTEM = #{sourceSystem}</if>
        <if test="resourceStatus != null and resourceStatus != ''">and RESOURCE_STATUS = #{resourceStatus}</if>
        <if test="remark != null and remark != ''">and REMARK = #{remark}</if>
        <if test="createBy != null and createBy != ''">and CREATE_BY = #{createBy}</if>
        <if test="createDate != null">and CREATE_DATE = #{createDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">and LAST_UPDATED_BY = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null">and LAST_UPDATED_DATE = #{lastUpdatedDate}</if>
        <if test="enabledFlag != null and enabledFlag != ''">and ENABLED_FLAG = #{enabledFlag}</if>
        <if test="orgId != null and orgId != ''">and ORG_ID = #{orgId}::numeric</if>
        <if test="factoryId != null and factoryId != ''">and FACTORY_ID = #{factoryId}::numeric</if>
        <if test="entityId != null and entityId != ''">and ENTITY_ID = #{entityId}::numeric</if>
        <if test="attribute1 != null and attribute1 != ''">and ATTRIBUTE1 = #{attribute1}</if>
        <if test="attribute2 != null and attribute2 != ''">and ATTRIBUTE2 = #{attribute2}</if>
        <if test="attribute3 != null and attribute3 != ''">and ATTRIBUTE3 = #{attribute3}</if>
        <if test="attribute4 != null and attribute4 != ''">and ATTRIBUTE4 = #{attribute4}</if>
        <if test="attribute5 != null and attribute5 != ''">and ATTRIBUTE5 = #{attribute5}</if>
        <if test="availableQuantity != null and availableQuantity != ''">and available_quantity = #{availableQuantity}</if>
        <if test="gtAvailableQuantity != null and gtAvailableQuantity != ''">and available_quantity <![CDATA[>]]> #{gtAvailableQuantity}</if>
        <if test="sort != null">
            order by create_date
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
    </select>
    <select id="getListByResource" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select
        RESOURCE_ID,
        RESOURCE_NO,
        RESOURCE_TYPE,
        RESOURCE_AMOUNT,
        RESOURCE_STATUS,
        AVAILABLE_QUANTITY,
        DEVICE_TYPE,
        CERT_NAME,
        EXPIRY_DATE,
        model_Number
        FROM RESOURCE_INFO WHERE ENABLED_FLAG = 'Y' AND RESOURCE_NO IN
        <foreach item="resourceList" index="index" collection="resourceList" open="(" separator="," close=")">
            #{resourceList}
        </foreach>
    </select>

    <select id="getListByDeviceType" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select
        RESOURCE_NO,
        AVAILABLE_QUANTITY,
        DEVICE_TYPE,
        EXPIRY_DATE
        FROM RESOURCE_INFO WHERE ENABLED_FLAG = 'Y' AND DEVICE_TYPE IN
        <foreach item="deviceList" index="index" collection="deviceList" open="(" separator="," close=")">
            #{deviceList}
        </foreach>
    </select>

    <select id="getListByGroupDeviceType" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT DEVICE_TYPE deviceType,sum(AVAILABLE_QUANTITY) availableQuantity
        FROM RESOURCE_INFO
        WHERE ENABLED_FLAG = 'Y' AND DEVICE_TYPE IN
        <foreach item="deviceList" index="index" collection="deviceList" open="(" separator="," close=")">
            #{deviceList}
        </foreach>
        GROUP BY DEVICE_TYPE
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO RESOURCE_INFO (
          RESOURCE_ID,
          RESOURCE_TYPE,
          RESOURCE_NO,
          RESOURCE_START,
          RESOURCE_END,
          PRODUCT_CATEGORY,
          PRODUCT_SMALL_CLASS,
          RESOURCE_AMOUNT,
          USAGE_SCOPE,
          CUST_INFO,
          SOURCE_SYSTEM,
          RESOURCE_STATUS,
          REMARK,
          CREATE_BY,
          CREATE_DATE,
          LAST_UPDATED_BY,
          LAST_UPDATED_DATE,
          ENABLED_FLAG,
          ORG_ID,
          FACTORY_ID,
          ENTITY_ID,
          AVAILABLE_QUANTITY,
          DEVICE_TYPE,
          EXPIRY_DATE,
          PROD_ADDRESS,
          PRODUCT_NAME,
          PRODUCT_LINE,
          CERT_NAME,
          CERT_ADMINISTRATOR
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
          #{item.resourceId,jdbcType=VARCHAR},
          #{item.resourceType,jdbcType=VARCHAR},
          #{item.resourceNo,jdbcType=VARCHAR},
          #{item.resourceStart,jdbcType=VARCHAR},
          #{item.resourceEnd,jdbcType=VARCHAR},
          #{item.productCategory,jdbcType=VARCHAR},
          #{item.productSmallClass,jdbcType=VARCHAR},
          #{item.resourceAmount,jdbcType=NUMERIC},
          #{item.usageScope,jdbcType=VARCHAR},
          #{item.custInfo,jdbcType=VARCHAR},
          #{item.sourceSystem,jdbcType=VARCHAR},
          #{item.resourceStatus,jdbcType=VARCHAR},
          #{item.remark,jdbcType=VARCHAR},
          #{item.createBy,jdbcType=VARCHAR},
          sysdate,
          #{item.lastUpdatedBy,jdbcType=VARCHAR},
          sysdate,
          'Y',
          #{item.orgId,jdbcType=NUMERIC},
          #{item.factoryId,jdbcType=NUMERIC},
          #{item.entityId,jdbcType=NUMERIC},
          #{item.availableQuantity,jdbcType=NUMERIC},
          #{item.deviceType},
          #{item.expiryDate,jdbcType=TIMESTAMP},
          #{item.prodAddress},
          #{item.productName},
          #{item.productLine},
          #{item.certName},
          #{item.certAdministrator}
        )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update RESOURCE_INFO t set
              t.PRODUCT_CATEGORY=#{item.productCategory,jdbcType=VARCHAR},
              t.PRODUCT_SMALL_CLASS=#{item.productSmallClass,jdbcType=VARCHAR},
              t.USAGE_SCOPE=#{item.usageScope,jdbcType=VARCHAR},
              t.CUST_INFO=#{item.custInfo,jdbcType=VARCHAR},
              t.REMARK=#{item.remark,jdbcType=VARCHAR},
              t.LAST_UPDATED_BY=#{item.lastUpdatedBy,jdbcType=VARCHAR},
              t.LAST_UPDATED_DATE=sysdate,
              t.AVAILABLE_QUANTITY = #{item.availableQuantity,jdbcType=DECIMAL}
              where ENABLED_FLAG = 'Y'
              and RESOURCE_STATUS =#{item.resourceStatus,jdbcType=VARCHAR}
              and RESOURCE_ID = #{item.resourceId}
        </foreach>
    </update>

    <update id="batchUpdateSelectivity" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update RESOURCE_INFO t set
            <if test="item.resourceStatus != null and item.resourceStatus != ''">t.RESOURCE_STATUS =#{item.resourceStatus,jdbcType=VARCHAR},</if>
            <if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">t.LAST_UPDATED_BY=#{item.lastUpdatedBy,jdbcType=VARCHAR},</if>
            <if test="item.availableQuantity != null">t.AVAILABLE_QUANTITY=#{item.availableQuantity,jdbcType=DECIMAL},</if>
            <if test="item.availableQuantityAdd != null">t.AVAILABLE_QUANTITY=t.AVAILABLE_QUANTITY + #{item.availableQuantityAdd,jdbcType=DECIMAL},</if>
            <if test="item.availableQuantitySubtract != null">t.AVAILABLE_QUANTITY=t.AVAILABLE_QUANTITY - #{item.availableQuantitySubtract,jdbcType=DECIMAL},</if>
            <if test="item.attribute4 != null and item.attribute4 != ''">ATTRIBUTE4 = #{item.attribute4},</if>
            t.LAST_UPDATED_DATE=sysdate
            where ENABLED_FLAG = 'Y'
            <if test="item.resourceOldStatus != null and item.resourceOldStatus != ''"> and t.RESOURCE_STATUS =#{item.resourceOldStatus,jdbcType=VARCHAR}</if>
            <if test="item.resourceId != null and item.resourceId != ''"> and t.RESOURCE_ID =#{item.resourceId,jdbcType=VARCHAR}</if>
            <if test="item.resourceNo != null and item.resourceNo != ''"> and t.RESOURCE_NO =#{item.resourceNo,jdbcType=VARCHAR}</if>
            <if test="(item.resourceNo ==null or item.resourceNo =='') and (item.resourceId ==null or item.resourceId =='') ">and 1=2</if>
        </foreach>
    </update>

    <select id="findDeviceByResourceNo" parameterType="java.lang.String" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT
        t.resource_no,
        t.device_type,
        t.resource_status
        FROM
        resource_info t
        WHERE
        t.enabled_flag = 'Y'
        AND t.resource_no = #{resourceNo}
        limit 1
    </select>

    <select id="findDeviceByResourceNos" parameterType="java.lang.String" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT
        t.resource_no,
        t.device_type
        FROM
        resource_info t
        WHERE
        t.enabled_flag = 'Y'
        and t.resource_no in
            <foreach collection="resourceNos" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
    </select>

    <select id="pageQueryExpiryData" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT
        RESOURCE_ID,
        RESOURCE_NO,
        EXPIRY_DATE
        FROM resource_info
        WHERE enabled_flag = 'Y' AND resource_type = 'NAL' AND resource_type != 'LOSE_EFFICACY' AND expiry_date IS NOT NULL
    </select>

    <update id="batchFailure">
        UPDATE RESOURCE_INFO SET resource_status = 'LOSE_EFFICACY'
        WHERE RESOURCE_ID IN
        <foreach item="ids" index="index" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <update id="updateNetworkType" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        UPDATE RESOURCE_INFO t SET
        <if test="deviceType != null and resourceStatus != ''">t.DEVICE_TYPE =#{deviceType},</if>
        <if test="prodAddress != null and prodAddress != ''">t.PROD_ADDRESS =#{prodAddress},</if>
        <if test="productName != null and productName != ''">t.PRODUCT_NAME =#{productName},</if>
        <if test="expiryDate != null">t.EXPIRY_DATE =#{expiryDate},
            t.resource_status =
            case when
                #{expiryDate} > sysdate
            then
                (case when (
                select
                    count(1)
                from
                    resource_info_detail t1
                where
                    t1.resource_no = t.resource_no
                    <![CDATA[ and t1.status <> '0']]>
                    ) > 0 then 'USING'
                else
                    'INIT'
                end)
            else
                t.resource_status
            end,</if>
        <if test="productLine != null and productLine != ''">t.PRODUCT_LINE =#{productLine},</if>
        <if test="certName != null and certName != ''">t.CERT_NAME =#{certName},</if>
        <if test="certAdministrator != null and certAdministrator != ''">t.CERT_ADMINISTRATOR =#{certAdministrator},</if>
        <if test="benchmarkQty != null">t.BENCHMARK_QTY =#{benchmarkQty},</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">t.LAST_UPDATED_BY =#{lastUpdatedBy},</if>
        <if test="tagApplicant != null and tagApplicant != ''">t.tag_applicant =#{tagApplicant},</if>
        t.LAST_UPDATED_DATE=sysdate
        WHERE
            t.RESOURCE_ID = #{resourceId}
        AND t.ENABLED_FLAG = 'Y'
    </update>


    <select id="selectNoAddressInfo" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT
            RESOURCE_ID,
            RESOURCE_NO,
            EXPIRY_DATE
        FROM
            resource_info
        WHERE
            enabled_flag = 'Y'
        AND resource_type = 'NAL'
        AND resource_status != 'LOSE_EFFICACY'
        AND expiry_date IS NOT NULL
        AND LAST_UPDATED_DATE > (sysdate - 366)
        AND (PROD_ADDRESS is null or  PROD_ADDRESS = '')
        limit 1000
    </select>

    <update id="updateAmount" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        UPDATE
        RESOURCE_INFO
        SET
        RESOURCE_AMOUNT = #{resourceAmount},
        LAST_UPDATED_DATE = sysdate
        WHERE
        RESOURCE_ID = #{resourceId} AND ENABLED_FLAG = 'Y'
    </update>

    <update id="updateProdAddress" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        UPDATE
            RESOURCE_INFO t
        SET
            t.PROD_ADDRESS = #{prodAddress},
            t.LAST_UPDATED_DATE=sysdate
        WHERE
            t.RESOURCE_ID = #{resourceId}
        AND t.ENABLED_FLAG = 'Y'
    </update>

    <update id="updateAvailableQuantityByNal">
        UPDATE resource_info
        SET available_quantity=(SELECT count(1) FROM resource_info_detail WHERE resource_no = #{resourceNo} AND status = '0')
        WHERE resource_no = #{resourceNo} AND resource_type = 'NAL' AND enabled_flag = 'Y'
    </update>

    <select id="fuzzyQueryDeviceType" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
            distinct device_type
        FROM
            resource_info
        WHERE
            enabled_flag = 'Y'
        AND resource_type = 'NAL'
        AND resource_status != 'LOSE_EFFICACY'
        AND expiry_date IS NOT NULL
        AND  device_type LIKE concat('%',#{deviceType},'%')
        limit 100
    </select>

    <select id="findResourceEntityByDeviceType" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        SELECT
            RESOURCE_ID,
            RESOURCE_NO,
            CERT_NAME,
            DEVICE_TYPE,
            MODEL_NUMBER
        FROM
            resource_info
        WHERE
        enabled_flag = 'Y'
        AND resource_type = 'NAL'
        AND resource_status != 'LOSE_EFFICACY'
        AND expiry_date IS NOT NULL
        AND device_type = #{deviceType}
    </select>

    <select id="selectByResourceNo" parameterType="java.lang.String" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select
        <include refid="Base_Column_List" />
        from RESOURCE_INFO  where  ENABLED_FLAG = 'Y'
        AND resource_no = #{resourceNo}
        limit 1
    </select>

    <update id="updateAvailableQuantityByResourceNo">
        UPDATE resource_info
        SET available_quantity=(SELECT count(1) FROM resource_info_detail WHERE resource_no = #{resourceNo} AND status = '0')
        WHERE resource_no = #{resourceNo} AND enabled_flag = 'Y'
    </update>


    <select id="selectValidityByResourceNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
            count(1)
        from
            RESOURCE_INFO
        where
            ENABLED_FLAG = 'Y'
        AND RESOURCE_STATUS in ('USING','INIT')
        AND RESOURCE_NO = #{resourceNo}
    </select>

    <update id="setBenchmarkQuantity">
        with a as (
        select gen_random_uuid() resource_id,'NAL' resource_type,'INIT' resource_status,resource_no,product_name, expiry_date, equipment_adress, device_type, production_line, cert_administrator, cert_name,'ZRS同步' remark,'system' last_updated_by,sysdate last_updated_date,'system' create_by,sysdate create_date,'Y' enabled_flag
        from v_lims_reliable_auth_report a
        where a.RESOURCE_NO IN
        <foreach item="item" index="index" collection="resourceNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        updated as (
        update RESOURCE_INFO b
        SET
        product_name = a.product_name,expiry_date = a.expiry_date,prod_address = a.equipment_adress,
        device_type = a.device_type,product_line= a.production_line,cert_administrator= a.cert_administrator,
        cert_name = a.cert_name,last_updated_date = sysdate,last_updated_by = 'system'
        from a
        where a.resource_no = b.resource_no
        returning a.*)
        insert into RESOURCE_INFO
        (resource_id, resource_type,resource_status, resource_no, product_name, expiry_date, prod_address, device_type, product_line, cert_administrator, cert_name,remark,last_updated_by, last_updated_date, create_by, create_date, enabled_flag,factory_id,available_quantity,resource_amount)
        select resource_id, resource_type,resource_status, resource_no, product_name, expiry_date, equipment_adress, device_type, production_line, cert_administrator, cert_name,remark,last_updated_by, last_updated_date, create_by, create_date, enabled_flag,51,0,0
        from a where NOT EXISTS (SELECT 1 FROM updated WHERE resource_no = a.resource_no)


    </update>

    <select id="getUpdatedApprovalDocuments"  resultType="java.lang.String">
        select
        resource_no
        from
        v_lims_reliable_auth_report
        where
        production_date >= #{productionDate}
        <if test="lastResourceNo != null and lastResourceNo != ''">and resource_no > #{lastResourceNo}</if>
        <if test="resourceNo != null and resourceNo != ''">and resource_no = #{resourceNo}</if>
        order by resource_no
        limit 500
    </select>

    <select id="getProductNameByNum" resultType="java.lang.String">
        select
        product_name
        from
        resource_info t left join resource_info_detail u on t.resource_no = u.resource_no
        where
        t.enabled_flag = 'Y'
        and u.resource_num = #{resourceStr}
        and t.resource_type = #{resourceType} limit 1
    </select>

    <select id="getModelNumberEmptyList" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select
        RESOURCE_ID,
        RESOURCE_NO,
        RESOURCE_TYPE,
        RESOURCE_AMOUNT,
        RESOURCE_STATUS,
        AVAILABLE_QUANTITY,
        DEVICE_TYPE,
        CERT_NAME,
        EXPIRY_DATE,
        model_Number
        FROM RESOURCE_INFO WHERE ENABLED_FLAG = 'Y' and resource_type = 'NAL' and (MODEL_NUMBER is null or MODEL_NUMBER ='')
        <if test="lastResourceNo != null and lastResourceNo != ''">and resource_no > #{lastResourceNo}</if>
        <if test="resourceNo != null and resourceNo != ''">and resource_no = #{resourceNo}</if>
        order by resource_no
        limit 500
    </select>

    <select id="getTheApplicationResourceNumber"  resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select resource_no,benchmark_qty,resource_type,last_updated_by from (
            select
            t.resource_no,
            t.last_updated_by,
            t.available_quantity,
            t.benchmark_qty,
            t.resource_type,
            (
                t.available_quantity +
               nvl(
                    (
                    select
                    sum(nvl(qty,0))
                    from
                    resource_apply_record
                    where
                    enabled_flag = 'Y'
                    and apply_status = '0'
                    and resource_no = t.resource_no)
               ,0)
            ) as qty
            from
            resource_info t
            where ENABLED_FLAG = 'Y' and t.resource_status in('INIT','USING') and resource_type = 'NAL'
             <if test="resourceNo != null and resourceNo != ''">and t.resource_no = #{resourceNo}</if>
        )
        where qty &lt; (nvl(benchmark_qty,0)*0.3)
        <if test="lastResourceNo != null and lastResourceNo != ''">and resource_no > #{lastResourceNo}</if>
        order by resource_no
        limit 100
    </select>

    <update id="batchUpdateBackPassRatio">
        update resource_info a set
        a.back_pass_ratio = T.back_pass_ratio,
        a.back_pass_warning = T.back_pass_warning,
        a.cal_pass_ratio_date = sysdate
        from (
        <foreach collection="updateList" item="item" index="index" separator="union">
            (select #{item.backPassRatio,jdbcType=NUMERIC} back_pass_ratio,
            #{item.backPassWarning,jdbcType=VARCHAR} back_pass_warning,
            #{item.resourceNo,jdbcType=VARCHAR} resource_no)
        </foreach>
        ) T
        where a.enabled_flag = 'Y' and
        a.resource_no = T.resource_no
    </update>


    <update id="batchUpdateModelNumber">
        update resource_info a set
        a.model_number = T.modelNumber,
        a.LAST_UPDATED_BY= T.lastUpdatedBy,
        a.LAST_UPDATED_DATE= sysdate
        from (
        <foreach collection="updateList" item="item" index="index" separator="union">
            (select #{item.modelNumber,jdbcType=VARCHAR} modelNumber,
            #{item.lastUpdatedBy,jdbcType=VARCHAR} lastUpdatedBy,
            #{item.resourceId,jdbcType=VARCHAR} resourceId)
        </foreach>
        ) T
        where a.enabled_flag = 'Y' and
        a.resource_id = T.resourceId
    </update>

    <update id="updateModelNumber" parameterType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        update resource_info a set a.model_number = #{modelNumber,jdbcType=VARCHAR},
        a.LAST_UPDATED_BY=  #{lastUpdatedBy,jdbcType=VARCHAR} ,
        a.LAST_UPDATED_DATE= sysdate
        where a.enabled_flag = 'Y' and
        a.resource_id = #{resourceId}
    </update>

    <select id="resourceNoListInTimeRange" resultType="java.lang.String">
        select distinct r.resource_no from resource_info r
        where r.enabled_flag = 'Y' and r.resource_type ='NAL'
        and r.resource_no in
        <foreach item="resourceNo" index="index" collection="resourceNoList" open="(" separator="," close=")">
            #{resourceNo}
        </foreach>
        <if test="calTimeRangeLeft != null">
            <![CDATA[ and r.create_date >= SYSDATE - concat(#{calTimeRangeLeft},'','mon') ::interval ]]>
        </if>
        <if test="calTimeRangeRight != null">
            <![CDATA[ and r.create_date <= SYSDATE - concat(#{calTimeRangeRight},'','mon') ::interval ]]>
        </if>
    </select>

    <select id="updateResourceInfoByType">
        update resource_info
        set resource_status = 'USING',
        available_quantity = available_quantity - #{num,jdbcType=DECIMAL}::NUMERIC,
        last_updated_date = sysdate
        where enabled_flag = 'Y'
        and resource_type = #{resourceType}
        and resource_id = (
        select resource_id from resource_info
        order by create_date desc
        limit 1
        )
    </select>

    <update id="updateResourceInfoByType1">
        update resource_info
        set resource_status = 'USING',
        available_quantity = available_quantity - #{num,jdbcType=DECIMAL}::NUMERIC,
        last_updated_date = sysdate
        where enabled_flag = 'Y'
        and resource_type = #{resourceType}
        and resource_id = (
        select resource_id from gpon_sn_info_detail
        where resource_num = #{no}
        )
    </update>

    <select id="getResourceInfoByNo" resultType="com.zte.interfaces.dto.ResourceInfoEntityDTO">
        select resource_id, resource_no
        from resource_info
        where enabled_flag = 'Y'
        and resource_no = #{resourceNo}
    </select>

</mapper>