<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.BsPremanuItemInfoRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.BsPremanuItemInfo">
        <id column="RECORD_ID" jdbcType="VARCHAR" property="recordId"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode"/>
        <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName"/>
        <result column="TRACE_CODE" jdbcType="VARCHAR" property="traceCode"/>
        <result column="TRACE_NAME" jdbcType="VARCHAR" property="traceName"/>
        <result column="DELIVERY_PROCESS" jdbcType="VARCHAR" property="deliveryProcess"/>
        <result column="IS_PRE_MANU" jdbcType="VARCHAR" property="isPreManu"/>
        <result column="TAG_NUM" jdbcType="VARCHAR" property="tagNum"/>
        <result column="PRE_MANU_TYPE" jdbcType="VARCHAR" property="preManuType"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="FACTORY_ID" jdbcType="DECIMAL" property="factoryId"/>
        <result column="ENTITY_ID" jdbcType="DECIMAL" property="entityId"/>
        <result column="Abc_Type" jdbcType="VARCHAR" property="abcType"/>
        <result column="STYLE" jdbcType="VARCHAR" property="style"/>
        <result column="SORT_SEQ" jdbcType="DECIMAL" property="sortSeq"/>
        <result column="SUB_LEVEL" jdbcType="VARCHAR" property="subLevel"/>
        <result column="BOM_CODE" jdbcType="VARCHAR" property="bomCode"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="ITEM_QTY" jdbcType="DECIMAL" property="itemQty"/>
        <result column="bake_temperature" jdbcType="DECIMAL" property="bakeTemperature"/>
        <result column="bake_time" jdbcType="DECIMAL" property="bakeTime"/>
        <result column="bake_remark" jdbcType="VARCHAR" property="bakeRemark"/>
    </resultMap>

    <sql id="Base_Column_List">
        RECORD_ID, ITEM_NO, ITEM_NAME, TYPE_CODE, TYPE_NAME, TRACE_CODE, TRACE_NAME, DELIVERY_PROCESS,
        IS_PRE_MANU, TAG_NUM, PRE_MANU_TYPE, REMARK, ATTRIBUTE1, ATTRIBUTE2, CREATE_BY, CREATE_DATE,
        LAST_UPDATED_BY, LAST_UPDATED_DATE, ENABLED_FLAG, ORG_ID, FACTORY_ID, ENTITY_ID,SORT_SEQ,
        BAKE_TEMPERATURE,BAKE_TIME,BAKE_REMARK
    </sql>

    <select id="selectBsPremanuItemInfoById" parameterType="com.zte.domain.model.BsPremanuItemInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from BS_PREMANU_ITEM_INFO
        where RECORD_ID = #{recordId,jdbcType=VARCHAR}
        and ENABLED_FLAG = 'Y'
    </select>

    <delete id="deleteBsPremanuItemInfoById" parameterType="com.zte.domain.model.BsPremanuItemInfo">
        delete from BS_PREMANU_ITEM_INFO
        where RECORD_ID = #{recordId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertBsPremanuItemInfo" parameterType="com.zte.domain.model.BsPremanuItemInfo">
        insert into BS_PREMANU_ITEM_INFO (RECORD_ID, ITEM_NO, ITEM_NAME,
        TYPE_CODE, TYPE_NAME, TRACE_CODE,
        TRACE_NAME, DELIVERY_PROCESS, IS_PRE_MANU,
        TAG_NUM, PRE_MANU_TYPE, REMARK,
        ATTRIBUTE1, ATTRIBUTE2, CREATE_BY,
        CREATE_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE,
        ENABLED_FLAG, ORG_ID, FACTORY_ID,
        ENTITY_ID,SORT_SEQ)
        values (#{recordId,jdbcType=VARCHAR}, #{itemNo,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
        #{typeCode,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR}, #{traceCode,jdbcType=VARCHAR},
        #{traceName,jdbcType=VARCHAR}, #{deliveryProcess,jdbcType=VARCHAR}, #{isPreManu,jdbcType=VARCHAR},
        #{tagNum,jdbcType=VARCHAR}, #{preManuType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
        sysdate, #{lastUpdatedBy,jdbcType=VARCHAR}, sysdate,
        'Y', #{orgId,jdbcType=DECIMAL}, #{factoryId,jdbcType=DECIMAL},
        #{entityId,jdbcType=DECIMAL},#{sortSeq,jdbcType=DECIMAL})
    </insert>

    <insert id="insertBsPremanuItemInfoSelective" parameterType="com.zte.domain.model.BsPremanuItemInfo">
        insert into BS_PREMANU_ITEM_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                RECORD_ID,
            </if>

            <if test="itemNo != null">
                ITEM_NO,
            </if>

            <if test="itemName != null">
                ITEM_NAME,
            </if>

            <if test="typeCode != null">
                TYPE_CODE,
            </if>

            <if test="typeName != null">
                TYPE_NAME,
            </if>

            <if test="traceCode != null">
                TRACE_CODE,
            </if>

            <if test="traceName != null">
                TRACE_NAME,
            </if>

            <if test="deliveryProcess != null">
                DELIVERY_PROCESS,
            </if>

            <if test="isPreManu != null">
                IS_PRE_MANU,
            </if>

            <if test="tagNum != null">
                TAG_NUM,
            </if>

            <if test="preManuType != null">
                PRE_MANU_TYPE,
            </if>

            <if test="remark != null">
                REMARK,
            </if>

            <if test="attribute1 != null">
                ATTRIBUTE1,
            </if>

            <if test="attribute2 != null">
                ATTRIBUTE2,
            </if>

            <if test="createBy != null">
                CREATE_BY,
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY,
            </if>

            <if test="orgId != null">
                ORG_ID,
            </if>

            <if test="factoryId != null">
                FACTORY_ID,
            </if>

            <if test="entityId != null">
                ENTITY_ID,
            </if>

            <if test="sortSeq != null">
                SORT_SEQ,
            </if>
            <if test="bakeTemperature != null">
                bake_temperature,
            </if>
            <if test="bakeTime != null">
                bake_time,
            </if>
            <if test="bakeRemark != null">
                bake_remark,
            </if>

            CREATE_DATE,
            LAST_UPDATED_DATE,
            ENABLED_FLAG

        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">
                #{recordId,jdbcType=VARCHAR},
            </if>

            <if test="itemNo != null">
                #{itemNo,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="typeCode != null">
                #{typeCode,jdbcType=VARCHAR},
            </if>

            <if test="typeName != null">
                #{typeName,jdbcType=VARCHAR},
            </if>

            <if test="traceCode != null">
                #{traceCode,jdbcType=VARCHAR},
            </if>

            <if test="traceName != null">
                #{traceName,jdbcType=VARCHAR},
            </if>

            <if test="deliveryProcess != null">
                #{deliveryProcess,jdbcType=VARCHAR},
            </if>

            <if test="isPreManu != null">
                #{isPreManu,jdbcType=VARCHAR},
            </if>

            <if test="tagNum != null">
                #{tagNum,jdbcType=VARCHAR},
            </if>

            <if test="preManuType != null">
                #{preManuType,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>

            <if test="attribute1 != null">
                #{attribute1,jdbcType=VARCHAR},
            </if>

            <if test="attribute2 != null">
                #{attribute2,jdbcType=VARCHAR},
            </if>

            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>

            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                #{factoryId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="sortSeq != null">
                #{sortSeq,jdbcType=DECIMAL},
            </if>
            <if test="bakeTemperature != null">
                #{bakeTemperature,jdbcType=DECIMAL},
            </if>
            <if test="bakeTime != null">
                #{bakeTime,jdbcType=DECIMAL},
            </if>
            <if test="bakeRemark != null">
                #{bakeRemark,jdbcType=VARCHAR},
            </if>
            sysdate,
            sysdate,
            'Y'
        </trim>

    </insert>

    <update id="updateBsPremanuItemInfoByIdSelective" parameterType="com.zte.domain.model.BsPremanuItemInfo">
        update BS_PREMANU_ITEM_INFO
        <set>
            <if test="itemNo != null">
                ITEM_NO = #{itemNo,jdbcType=VARCHAR},
            </if>

            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>

            <if test="typeCode != null">
                TYPE_CODE = #{typeCode,jdbcType=VARCHAR},
            </if>

            <if test="typeName != null">
                TYPE_NAME = #{typeName,jdbcType=VARCHAR},
            </if>

            <if test="traceCode != null">
                TRACE_CODE = #{traceCode,jdbcType=VARCHAR},
            </if>

            <if test="traceName != null">
                TRACE_NAME = #{traceName,jdbcType=VARCHAR},
            </if>

            <if test="deliveryProcess != null">
                DELIVERY_PROCESS = #{deliveryProcess,jdbcType=VARCHAR},
            </if>

            <if test="isPreManu != null">
                IS_PRE_MANU = #{isPreManu,jdbcType=VARCHAR},
            </if>

            <if test="tagNum != null">
                TAG_NUM = #{tagNum,jdbcType=VARCHAR},
            </if>

            <if test="preManuType != null">
                PRE_MANU_TYPE = #{preManuType,jdbcType=VARCHAR},
            </if>

            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>

            <if test="attribute1 != null">
                ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
            </if>

            <if test="attribute2 != null">
                ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
            </if>

            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>

            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=DECIMAL},
            </if>

            <if test="factoryId != null">
                FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
            </if>

            <if test="entityId != null">
                ENTITY_ID = #{entityId,jdbcType=DECIMAL},
            </if>

            <if test="sortSeq != null">
                SORT_SEQ = #{sortSeq,jdbcType=DECIMAL},
            </if>
            <if test="bakeTemperature != null">
                bake_temperature = #{bakeTemperature,jdbcType=DECIMAL},
            </if>
            <if test="bakeTime != null">
                bake_time = #{bakeTime,jdbcType=DECIMAL},
            </if>
            <if test="bakeRemark != null">
                bake_remark = #{bakeRemark,jdbcType=VARCHAR},
            </if>
            LAST_UPDATED_DATE = sysdate
        </set>

        where 1=1
        AND ENABLED_FLAG = 'Y'
        AND RECORD_ID = #{recordId,jdbcType=VARCHAR}
    </update>

    <update id="updateBsPremanuItemInfoById" parameterType="com.zte.domain.model.BsPremanuItemInfo">
        update BS_PREMANU_ITEM_INFO
        set ITEM_NO = #{itemNo,jdbcType=VARCHAR},
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
        TYPE_CODE = #{typeCode,jdbcType=VARCHAR},
        TYPE_NAME = #{typeName,jdbcType=VARCHAR},
        TRACE_CODE = #{traceCode,jdbcType=VARCHAR},
        TRACE_NAME = #{traceName,jdbcType=VARCHAR},
        DELIVERY_PROCESS = #{deliveryProcess,jdbcType=VARCHAR},
        IS_PRE_MANU = #{isPreManu,jdbcType=VARCHAR},
        TAG_NUM = #{tagNum,jdbcType=VARCHAR},
        PRE_MANU_TYPE = #{preManuType,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = sysdate,
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
        ORG_ID = #{orgId,jdbcType=DECIMAL},
        FACTORY_ID = #{factoryId,jdbcType=DECIMAL},
        ENTITY_ID = #{entityId,jdbcType=DECIMAL},
        SORT_SEQ = #{sortSeq,jdbcType=DECIMAL}
        where RECORD_ID = #{recordId,jdbcType=VARCHAR}
    </update>
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getPremanuInfoCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*)
        FROM BS_PREMANU_ITEM_INFO t
        WHERE 1 = 1
        AND t.ENABLED_FLAG = 'Y'
        <if test="traceCode != null and traceCode != ''">and t.TRACE_CODE = #{traceCode}</if>
        <if test="traceName != null and traceName != ''">and t.TRACE_NAME = #{traceName}</if>
        <if test="typeCode != null and typeCode != ''">and t.TYPE_CODE = #{typeCode}</if>
        <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
        <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
        <if test="deliveryProcess != null and deliveryProcess != ''">and t.DELIVERY_PROCESS = #{deliveryProcess}</if>
        <if test="isPreManu != null and isPreManu != ''">and t.IS_PRE_MANU = #{isPreManu}</if>
        <if test="typeName != null and typeName != ''">and t.TYPE_NAME = #{typeName}</if>
        <if test="orgId != null">and t.ORG_ID = #{orgId}::numeric</if>
        <if test="factoryId != null">and t.FACTORY_ID = #{factoryId}::numeric</if>
        <if test="entityId != null">and t.ENTITY_ID = #{entityId}::numeric</if>
    </select>

    <!-- 获取符合条件的记录列表 -->
    <select id="getPremanuInfoList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT t.*
        FROM BS_PREMANU_ITEM_INFO t
        WHERE 1 = 1
        AND t.ENABLED_FLAG = 'Y'
        <if test="traceCode != null and traceCode != ''">and t.TRACE_CODE = #{traceCode}</if>
        <if test="traceName != null and traceName != ''">and t.TRACE_NAME = #{traceName}</if>
        <if test="typeCode != null and typeCode != ''">and t.TYPE_CODE = #{typeCode}</if>
        <if test="itemNo != null and itemNo != ''">and t.ITEM_NO = #{itemNo}</if>
        <if test="itemName != null and itemName != ''">and t.ITEM_NAME = #{itemName}</if>
        <if test="deliveryProcess != null and deliveryProcess != ''">and t.DELIVERY_PROCESS = #{deliveryProcess}</if>
        <if test="isPreManu != null and isPreManu != ''">and t.IS_PRE_MANU = #{isPreManu}</if>
        <if test="typeName != null and typeName != ''">and t.TYPE_NAME = #{typeName}</if>
        <if test="factoryId != null">and t.FACTORY_ID = #{factoryId}::numeric</if>
        <if test="entityId != null">and t.ENTITY_ID = #{entityId}::numeric</if>
        <if test="(traceCode == null or traceCode == '')
            and(traceName == null or traceName == '')
            and(typeCode == null or typeCode == '')
            and(itemNo == null or itemNo == '')
            and(itemName == null or itemName == '')
            and(deliveryProcess == null or deliveryProcess == '')
            and(isPreManu == null or isPreManu == '')
            and factoryId == null
            and entityId == null">and 1=2</if>
        <if test="orderField != null">
            <choose>
                <when test="orderField=='traceCode'">order by t.TRACE_CODE <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='traceName'">order by t.TRACE_NAME <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='typeCode'">order by t.TYPE_CODE <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='typeName'">order by t.TYPE_NAME <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='itemNo'">order by t.ITEM_NO <if test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='itemName'">order by t.ITEM_NAME <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='deliveryProcess'">order by t.DELIVERY_PROCESS <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='isPreManu'">order by t.IS_PRE_MANU <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='lastUpdatedDate'">order by t.last_updated_date <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='lastUpdatedBy'">order by t.last_updated_by <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='createDate'">order by t.create_date <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="orderField=='createBy'">order by t.create_by <if test="order != null and order == 'desc'">
                    desc
                </if>
                </when>
                <when test="orderField=='sortSeq'">order by t.SORT_SEQ asc</when>
            </choose>
        </if>
    </select>

    <select id="queryPreManuItemInfoNoPage" parameterType="com.zte.domain.model.BsPremanuItemInfo"
            resultMap="BaseResultMap">
        select
        t1.TYPE_NAME,
        t1.TRACE_NAME,
        t1.TRACE_CODE,
        t1.TYPE_CODE,
        t1.TAG_NUM,
        t1.PRE_MANU_TYPE,
        t1.REMARK,
        t1.DELIVERY_PROCESS,
        t1.ATTRIBUTE2,
        t1.ITEM_NO,
        t1.CREATE_DATE,
        t1.CREATE_BY,
        t1.LAST_UPDATED_BY,
        t1.LAST_UPDATED_DATE,
        t1.SORT_SEQ,
        t1.bake_temperature,
        t1.bake_time,
        t1.bake_remark
        from BS_PREMANU_ITEM_INFO t1
        where t1.ENABLED_FLAG='Y'
        <if test="itemNoList != null and itemNoList.size>0">
            AND t1.ITEM_NO IN
            <foreach collection="itemNoList" open="(" close=")" separator="," index="index">
                #{itemNoList[${index}]}
            </foreach>
        </if>
        <if test="itemNoList == null or itemNoList.size == 0">
            AND 1 = 2
        </if>
        limit 500
    </select>

    <select id="getPreManuItemInfo" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
        select t1.ATTRIBUTE1,
        t1.TYPE_NAME,
        t1.TRACE_NAME,
        t1.TRACE_CODE,
        t1.TYPE_CODE,
        t1.DELIVERY_PROCESS,
        t1.ATTRIBUTE2,
        t.Abc_Type,
        t.item_name,
        t.STYLE,
        t1.remark,
        t1.create_date,
        t1.create_by,
        t1.last_updated_by,
        t1.last_updated_date,
        t.ITEM_NO,
        t1.RECORD_ID,
        t1.SORT_SEQ,
        t1.bake_temperature,
        t1.bake_time,
        t1.bake_remark
        from bs_item_info t,bs_premanu_item_info t1
        where t.item_no = t1.ITEM_NO
        and t1.enabled_flag='Y'
        <if test="params.itemNo != null and params.itemNo != ''">and t.ITEM_NO = #{params.itemNo}</if>
        <if test="params.itemName != null and params.itemName != ''">and t.ITEM_NAME = #{params.itemName}</if>
        <if test="params.style != null and params.style != ''">and t.style = #{params.style}</if>
        <if test="params.abcType != null and params.abcType != ''">and t.ABC_TYPE = #{params.abcType}</if>
        <if test="params.typeName != null and params.typeName!= ''">and t1.TYPE_NAME =
            #{params.typeName,jdbcType=VARCHAR}
        </if>
        <if test="params.traceName != null and params.traceName != ''">and t1.TRACE_NAME =
            #{params.traceName,jdbcType=VARCHAR}
        </if>
        <if test="params.deliveryProcess != null and params.deliveryProcess != ''">and t1.DELIVERY_PROCESS =
            #{params.deliveryProcess,jdbcType=VARCHAR}
        </if>
        <if test="params.isPreManu != null and params.isPreManu != ''">and t1.IS_PRE_MANU =
            #{params.isPreManu,jdbcType=VARCHAR}
        </if>
        <if test="params.lastUpdatedBy != null and params.lastUpdatedBy != ''">and t1.last_updated_by =
            #{params.lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        <if test="params.updateStartDate != null and params.updateStartDate != ''">
            <![CDATA[and t1.last_updated_date >=to_timestamp(#{params.updateStartDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
        <if test="params.updateEndDate != null and params.updateEndDate != ''">
            <![CDATA[and t1.last_updated_date <=to_timestamp(#{params.updateEndDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
        <if test="params.itemNoList != null and params.itemNoList.size > 0">
            and t1.ITEM_NO in
            <foreach collection="params.itemNoList" index="index" item="item" open="(" separator="," close=")">
                #{params.itemNoList[${index}],jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="params.orderField != null">
            <choose>
                <when test="params.orderField=='lastUpdatedDate'">order by t1.last_updated_date <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="params.orderField=='lastUpdatedBy'">order by t1.last_updated_by <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="params.orderField=='createDate'">order by t1.create_date <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
                <when test="params.orderField=='createBy'">order by t1.create_by <if
                        test="order != null and order == 'desc'">desc
                </if>
                </when>
            </choose>
        </if>
        <if test="params.orderField == null || params.orderField == ''">order by t1.sort_seq asc</if>
    </select>

    <update id="updatePreItemBatch" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update bs_premanu_item_info T
            <set>
                <trim suffixOverrides=",">
                    <if test="item.sortSeq != null">
                        SORT_SEQ = #{item.sortSeq,jdbcType=DECIMAL},
                    </if>
                </trim>
            </set>
            where RECORD_ID = #{item.recordId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getBsBomInfo" parameterType="com.zte.domain.model.BsPremanuItemInfo" resultMap="BaseResultMap">
        select
        t.bom_code,
        t.BOM_NAME,
        d.ITEM_NO as ITEM_CODE,
        d.ABC_TYPE,
        d.DELIVERY_PROCESS,
        d.SUB_LEVEL,
        d.ITEM_QTY
        from bs_bom_hierarchical_head t
        left join bs_bom_hierarchical_detail d on t.head_id=d.head_id
        where 1=1
        and t.enabled_flag='Y'
        and d.ABC_TYPE in ('B','C')
        and d.DELIVERY_PROCESS_SPLIT_BEFORE ='SMT配送'
        and (d.TYPE_CODE is null or d.TYPE_CODE = '')
        <if test="bomCodeList != null and bomCodeList.size > 0">and t.bom_code in
            <foreach item="item" index="index" collection="bomCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bomCodeList == null or bomCodeList.size == 0">and 1=2</if>
    </select>

    <select id="getDipBomInfo" parameterType="com.zte.domain.model.BsPremanuItemInfo" resultMap="BaseResultMap">
        select
        t.bom_code,
        t.BOM_NAME,
        d.ITEM_NO as ITEM_CODE,
        d.ABC_TYPE,
        d.DELIVERY_PROCESS_SPLIT_BEFORE as DELIVERY_PROCESS,
        d.SUB_LEVEL,
        d.ITEM_QTY
        from bs_bom_hierarchical_head t
        left join bs_bom_hierarchical_detail d on t.head_id=d.head_id
        where 1=1
        and t.enabled_flag='Y'
        and d.DELIVERY_PROCESS_SPLIT_BEFORE !='SMT配送'
        and (d.TYPE_CODE is null or d.TYPE_CODE = '')
        <if test="bomCodeList != null and bomCodeList.size > 0">
            and t.bom_code in
            <foreach item="item" index="index" collection="bomCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bomCodeList == null or bomCodeList.size == 0">and 1=2</if>
    </select>


    <select id="getAssembleBomInfo" parameterType="com.zte.domain.model.BsPremanuItemInfo" resultMap="BaseResultMap">
        select
        t.bom_code,
        t.BOM_NAME,
        d.ITEM_NO as ITEM_CODE,
        d.ABC_TYPE,
        d.DELIVERY_PROCESS_SPLIT_BEFORE as DELIVERY_PROCESS,
        d.SUB_LEVEL,
        d.ITEM_QTY
        from bs_bom_hierarchical_head t
        left join bs_bom_hierarchical_detail d on t.head_id=d.head_id
        where 1=1
        and t.enabled_flag='Y'
        and d.DELIVERY_PROCESS_SPLIT_BEFORE = '装焊配送'
        and (d.TYPE_CODE is null or d.TYPE_CODE = '')
        <if test="bomCodeList != null and bomCodeList.size > 0">
            and t.bom_code in
            <foreach item="item" index="index" collection="bomCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bomCodeList == null or bomCodeList.size == 0">and 1=2</if>
    </select>

    <select id="selectPremanuInfo" resultType="com.zte.domain.model.BsPremanuItemInfo" >
        select ITEM_NO, TYPE_CODE, REMARK,bake_remark
        FROM BS_PREMANU_ITEM_INFO
        WHERE ENABLED_FLAG = 'Y'
        AND TYPE_CODE IN ('CX','XP','HK')
        AND ITEM_NO IN
        <foreach collection="list" open="(" separator="," item="item" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectItemList" resultType="java.lang.String" >
        select distinct ITEM_NO
        FROM BS_PREMANU_ITEM_INFO
        WHERE ENABLED_FLAG = 'Y'
        AND TYPE_CODE IN ('CX','XP','HK')
    </select>

    <select id="queryBsItemPreInfoList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
        select
        bs.RECORD_ID, bs.ITEM_NO, bs.ITEM_NAME, bs.TYPE_CODE, bs.TYPE_NAME, bs.TRACE_CODE, bs.TRACE_NAME, bs.DELIVERY_PROCESS,
        bs.IS_PRE_MANU, bs.TAG_NUM, bs.PRE_MANU_TYPE, bs.REMARK, bs.ATTRIBUTE1, bs.ATTRIBUTE2, bs.CREATE_BY, bs.CREATE_DATE,
        bs.LAST_UPDATED_BY, bs.LAST_UPDATED_DATE, bs.ENABLED_FLAG, bs.ORG_ID, bs.FACTORY_ID, bs.ENTITY_ID,bs.SORT_SEQ,
        bs.bake_temperature,bs.bake_time,bs.bake_remark, t.style
        from bs_premanu_item_info bs
        left join bs_item_info t on bs.item_no = t.item_no
        where bs.ENABLED_FLAG = 'Y'
        <if test="params != null and params.itemNo != null and params.itemNo != ''">and bs.item_no = #{params.itemNo}</if>
        <if test="params != null and params.typeCode != null and params.typeCode != ''">and bs.type_code = #{params.typeCode}</if>
        <if test="params != null and params.traceCode != null and params.traceCode != ''">and bs.trace_code = #{params.traceCode}</if>
        <if test="params != null and params.deliveryProcess != null and params.deliveryProcess != ''">and bs.delivery_process = #{params.deliveryProcess}</if>
        <if test="params != null and params.createStartDate != null and params.createEndDate != null "> and bs.create_date
            between #{params.createStartDate,jdbcType=TIMESTAMP} AND #{params.createEndDate,jdbcType=TIMESTAMP}</if>
        <if test="params != null and params.lastUpdatedStartDate != null and params.lastUpdatedEndDate != null ">and bs.last_updated_date
            between #{params.lastUpdatedStartDate,jdbcType=TIMESTAMP} AND #{params.lastUpdatedEndDate,jdbcType=TIMESTAMP}</if>
        <if test="(params.itemNo == null or params.itemNo == '') and (params.typeCode == null or params.typeCode == '') and (params.traceCode == null or params.traceCode == '')
            and (params.deliveryProcess == null or params.deliveryProcess == '') and (params.createStartDate == null or params.createEndDate == null)
            and (params.lastUpdatedStartDate == null or params.lastUpdatedEndDate == null)">
            and 1=2
        </if>
        order by bs.CREATE_DATE desc
    </select>

    <select id="countBsItemPreExportTotal" parameterType="com.zte.domain.model.BsPremanuItemInfo" resultType="java.lang.Integer">
        select count(1) from bs_premanu_item_info bs
        left join bs_item_info t on bs.item_no = t.item_no
        <where>
            bs.enabled_flag= 'Y'
            <if test="itemNo != null and itemNo != ''">and bs.item_no = #{itemNo}</if>
            <if test="typeCode != null and typeCode != ''">and bs.type_code = #{typeCode}</if>
            <if test="traceCode != null and traceCode != ''">and bs.trace_code = #{traceCode}</if>
            <if test="deliveryProcess != null and deliveryProcess != ''">and bs.delivery_process = #{deliveryProcess}</if>
            <if test="createStartDate != null and createEndDate != null "> and bs.create_date
                between #{createStartDate,jdbcType=TIMESTAMP} AND #{createEndDate,jdbcType=TIMESTAMP}</if>
            <if test="lastUpdatedStartDate != null and lastUpdatedEndDate != null ">and bs.last_updated_date
                between #{lastUpdatedStartDate,jdbcType=TIMESTAMP} AND #{lastUpdatedEndDate,jdbcType=TIMESTAMP}</if>
            <if test="(itemNo == null or itemNo == '')
            and(typeCode == null or typeCode == '')
            and(traceCode == null or traceCode == '')
            and(deliveryProcess == null or deliveryProcess == '')
            and(createStartDate == null or createEndDate == null)
            and(lastUpdatedStartDate == null or lastUpdatedEndDate == null)">and 1=2</if>
        </where>
    </select>
</mapper>
