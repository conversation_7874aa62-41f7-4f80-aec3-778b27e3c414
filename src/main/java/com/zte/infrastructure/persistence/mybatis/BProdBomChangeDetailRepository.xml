<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.zte.domain.model.BProdBomChangeDetailRepository">

    <resultMap id="BaseResultDTOMap"
               type="com.zte.interfaces.dto.BProdBomChangeDetailDTO">
        <id column="BOM_CHANGE_DETAIL_ID" jdbcType="VARCHAR" property="bomChangeDetailId"/>
        <result column="BOM_HEADER_ID" jdbcType="VARCHAR" property="bomHeaderId"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="ORIGINAL_ITEM_CODE" jdbcType="VARCHAR" property="originalItemCode"/>
        <result column="USAGE_COUNT" jdbcType="DECIMAL" property="usageCount"/>
        <result column="CHANGE_ID" jdbcType="VARCHAR" property="changeId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="ORIGINAL_PRODUCT_CODE" jdbcType="VARCHAR" property="originalProductCode"/>
        <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId"/>
    </resultMap>

    <sql id="Base_Column_List">
        BOM_CHANGE_DETAIL_ID, BOM_HEADER_ID, ITEM_CODE, ORIGINAL_ITEM_CODE,
        USAGE_COUNT,CHANGE_ID, CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,
        ENABLED_FLAG
    </sql>


    <!-- Started by AICoder, pid:wf6a763cb6cba2f140c60aeee09aea3597f30f5f -->
    <select id="queryBProdBomDetailChangeList" parameterType="com.zte.interfaces.dto.BProdBomChangeDetailDTO"
            resultType="com.zte.interfaces.dto.BProdBomChangeDetailDTO">
        SELECT distinct
        h.product_code,
        h.original_product_code,
        h.prodplan_id,
        c.item_code,
        d.chi_desc,
        nvl(d.usage_count, 0) usage_count,
        c.last_updated_date,
        c.original_item_code
        FROM b_prod_bom_header h
        JOIN b_prod_bom_change_detail c ON h.BOM_HEADER_ID = c.BOM_HEADER_ID and h.prodplan_id = c.prodplan_id
        JOIN b_prod_bom_detail d ON h.BOM_HEADER_ID = d.BOM_HEADER_ID
        WHERE h.ENABLED_FLAG = 'Y' AND c.ENABLED_FLAG = 'Y' AND d.ENABLED_FLAG = 'Y' AND d.item_code = c.item_code
        <if test="productCode!= null and productCode != ''">and h.product_code = #{productCode,jdbcType=VARCHAR}</if>
        <if test="productCodes != null and productCodes.size() > 0">and h.prodplan_id IN
            <foreach item="item" index="index" collection="productCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemCode!= null and itemCode != ''">and c.item_code = #{itemCode,jdbcType=VARCHAR}</if>
        <if test="prodplanIdList != null and prodplanIdList.size() > 0">and h.prodplan_id IN
            <foreach item="item" index="index" collection="prodplanIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="originalItemCodeList != null and originalItemCodeList.size() > 0">and c.original_item_code IN
            <foreach item="item" index="index" collection="originalItemCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="(productCode == null or productCode == '') and (itemCode == null or itemCode == '') and (prodplanIdList == null or prodplanIdList.size() == 0)
         and (productCodes == null or productCodes.size() == 0) and (originalItemCodeList == null or originalItemCodeList.size() == 0)">
            and 1=2
        </if>
        ORDER BY c.last_updated_date ASC
    </select>
    <!-- Ended by AICoder, pid:wf6a763cb6cba2f140c60aeee09aea3597f30f5f -->

    <select id="selectOriginalByProductCode" parameterType="java.lang.String"
            resultType="com.zte.interfaces.dto.BProdBomHeaderDTO">
        SELECT
        ORIGINAL_PRODUCT_CODE,PRODUCT_CODE,PRODPLAN_ID
        FROM b_prod_bom_header
        where enabled_flag = 'Y'
        AND PRODUCT_CODE = #{productCode}
        order by last_updated_date desc
    </select>

    <select id="selectMBomDetailChangeByHeaderId" resultMap="BaseResultDTOMap">
        select * from (
        select
        BOM_CHANGE_DETAIL_ID, BOM_HEADER_ID, ITEM_CODE, ORIGINAL_ITEM_CODE,
        USAGE_COUNT,CHANGE_ID, CREATE_BY,CREATE_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,
        ENABLED_FLAG
        ,ROW_NUMBER() OVER(PARTITION BY BOM_HEADER_ID,ITEM_CODE, ORIGINAL_ITEM_CODE,
        USAGE_COUNT,CHANGE_ID ORDER BY create_date DESC) AS rn
        from b_prod_bom_change_detail t
        where t.enabled_flag = 'Y'
        and t.BOM_HEADER_ID = #{params.bomHeaderId,jdbcType=VARCHAR}
        <if test="params.itemCode != null and params.itemCode !=''">
            and t.ITEM_CODE = #{params.itemCode,jdbcType=VARCHAR}
        </if>
        ) where rn =1
    </select>

    <!-- Started by AICoder, pid:vbb945486ed079514a70082eb0280731d22445c5 -->
    <select id="queryMBomDetailChangeByProdplanId"
            parameterType="com.zte.interfaces.dto.BProdBomChangeDetailDTO"
            resultType="com.zte.interfaces.dto.BProdBomChangeDetailDTO">
        SELECT
        prodplan_id,
        item_code,
        original_item_code,
        BOM_HEADER_ID
        FROM
        b_prod_bom_change_detail
        WHERE ENABLED_FLAG = 'Y'
        AND prodplan_id in
        <foreach item="item" index="index" collection="prodplanIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- Ended by AICoder, pid:vbb945486ed079514a70082eb0280731d22445c5 -->
    <select id="queryBProdBomDetailChangeByProdplan"
            parameterType="com.zte.interfaces.dto.BProdBomChangeDetailDTO"
            resultType="com.zte.interfaces.dto.BProdBomChangeDetailDTO">
        SELECT
        h.prodplan_id,
        h.product_code,
        h.original_product_code,
        d.item_code,
        nvl(d.usage_count, 0) usage_count,
        d.original_item_code
        FROM
        b_prod_bom_header h,
        b_prod_bom_change_detail d
        WHERE h.enabled_flag = 'Y' and d.ENABLED_FLAG = 'Y'
        and h.BOM_HEADER_ID = d.BOM_HEADER_ID and h.prodplan_id = d.prodplan_id
        AND h.prodplan_id = #{prodplanId}
        <if test="itemCode!= null and itemCode != ''">and d.item_code = #{itemCode,jdbcType=VARCHAR}</if>
    </select>

    <!-- Started by AICoder, pid:fd7c4t4f18k562114e330b1120eeb61313c1f79c -->
    <update id="deleteByProdplanIds">
        UPDATE B_PROD_BOM_CHANGE_DETAIL
        SET ENABLED_FLAG = 'N',
        LAST_UPDATED_DATE = SYSDATE,
        LAST_UPDATED_BY = #{empNo}
        WHERE ENABLED_FLAG = 'Y'
        AND prodplan_id IN
        <foreach collection="prodplanIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- Ended by AICoder, pid:fd7c4t4f18k562114e330b1120eeb61313c1f79c -->

    <!-- Started by AICoder, pid:b947e9399431dac145e40bd7e075fe317bb24c70 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into b_prod_bom_change_detail (
        bom_change_detail_id,
        bom_header_id,
        item_code,
        original_item_code,
        usage_count,
        change_id,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date,
        enabled_flag,
        prodplan_id
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.bomChangeDetailId},
            #{item.bomHeaderId},
            #{item.itemCode},
            #{item.originalItemCode},
            #{item.usageCount},
            #{item.changeId},
            #{item.createBy},
            sysdate,
            #{item.lastUpdatedBy},
            sysdate,
            'Y',
            #{item.prodplanId}
            )
        </foreach>
    </insert>

    <!-- Ended by AICoder, pid:b947e9399431dac145e40bd7e075fe317bb24c70 -->

    <!-- Started by AICoder, pid:j2142466a0a414a14211099ca0bf421109f51110 -->
    <select id="queryByProdplanIdAndChangeId"
            parameterType="java.util.List"
            resultType="com.zte.interfaces.dto.BProdBomChangeDetailDTO">
        SELECT DISTINCT
        d.prodplan_id,
        d.change_id
        FROM
        b_prod_bom_change_detail d
        WHERE (d.prodplan_id, d.change_id) IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            (#{item.prodplanId}, #{item.changeVersion})
        </foreach>
    </select>

    <!-- Ended by AICoder, pid:j2142466a0a414a14211099ca0bf421109f51110 -->

    <select id="selectMBomDetailChangeByProductCodesAndItem" resultMap="BaseResultDTOMap">
        select h.product_code,
        t.item_code,
        t.original_item_code,
        max(t.usage_count) usage_count,
        max(t.create_date) create_date
        from b_prod_bom_header h, b_prod_bom_change_detail t
        where h.enabled_flag = 'Y' and t.enabled_flag = 'Y' and h.bom_header_id = t.bom_header_id and h.prodplan_id = t.prodplan_id
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            AND h.PRODUCT_CODE IN
            <foreach collection="params.productCodes" index="index" open="(" separator="," item="item" close=")">
                #{params.productCodes[${index}],jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.productCode != null and params.productCode != ''">and h.product_code = #{params.productCode,jdbcType=VARCHAR}</if>
        <if test="params.itemCode != null and params.itemCode !=''">
            and t.ITEM_CODE = #{params.itemCode,jdbcType=VARCHAR}
        </if>
        <if test="(params.productCodes == null or params.productCodes.size() == 0) and
            (params.productCode == null or params.productCode == '')">
            and 1=2
        </if>
        group by h.product_code,
        t.item_code,
        t.original_item_code
    </select>
</mapper>
