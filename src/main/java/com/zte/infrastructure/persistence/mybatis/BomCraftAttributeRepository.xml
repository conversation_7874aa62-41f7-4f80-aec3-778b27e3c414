<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.BomCraftAttributeRepository">
  <!-- 可根据自己的需求，是否要使用 -->
  <resultMap id="BaseResultMap" type="com.zte.domain.model.BomCraftAttribute">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOM_NO" jdbcType="VARCHAR" property="bomNo" />
    <result column="BOM_NAME" jdbcType="VARCHAR" property="bomName" />
    <result column="VER_NO" jdbcType="VARCHAR" property="verNo" />
    <result column="SURFACE" jdbcType="VARCHAR" property="surface" />
    <result column="FURNACE_TEMP_NAME" jdbcType="VARCHAR" property="furnaceTempName" />
    <result column="LEAD_FLAG" jdbcType="VARCHAR" property="leadFlag" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="INK_JET_PRINTER_TEMPLATE" jdbcType="VARCHAR" property="inkJetPrinterTemplate" />
    <result column="INK_JET_PRINTER_SUFFIX" jdbcType="VARCHAR" property="inkJetPrinterSuffix" />
    <result column="CRAFT_SECTION" jdbcType="VARCHAR" property="craftSection" />
    <result column="FACTORY_ID" jdbcType="INTEGER" property="factoryId" />
    <result column="LEAD_FLAG_MEAN" jdbcType="VARCHAR" property="leadFlagMean" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
  </resultMap>

  <sql id="Base_Column_List">
    ID,BOM_NO,BOM_NAME,VER_NO,SURFACE,FURNACE_TEMP_NAME,LEAD_FLAG,CREATE_BY,CREATE_DATE,
    LAST_UPDATED_BY,LAST_UPDATED_DATE,INK_JET_PRINTER_TEMPLATE,INK_JET_PRINTER_SUFFIX,CRAFT_SECTION,FACTORY_ID,PRODPLAN_ID
  </sql>

  <select id="getBomCraftAttributePage" parameterType="com.zte.springbootframe.common.model.Page"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    , decode(LEAD_FLAG, '0', '有铅', '无铅') as LEAD_FLAG_MEAN
    from bom_craft_attribute
    where 1=1
    <if test="params.bomNo != null and params.bomNo != ''">
      AND BOM_NO like concat(concat('%', #{params.bomNo,jdbcType=VARCHAR}),'%')
    </if>
    <if test="params.furnaceTempName != null and params.furnaceTempName != ''">
      AND FURNACE_TEMP_NAME like concat(concat('%', #{params.furnaceTempName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="params.surface != null and params.surface != ''">
      AND SURFACE = #{params.surface,jdbcType=VARCHAR}
    </if>
    <if test="params.createBy != null and params.createBy != ''">
      AND CREATE_BY = #{params.createBy,jdbcType=VARCHAR}
    </if>
    <if test="params.prodplanId != null and params.prodplanId != ''">
      AND PRODPLAN_ID = #{params.prodplanId,jdbcType=VARCHAR}
    </if>
    <if test="params.updateStartDate != null and params.updateStartDate != ''">
      <![CDATA[AND LAST_UPDATED_DATE >=to_timestamp(#{params.updateStartDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="params.updateEndDate != null and params.updateEndDate != ''">
      <![CDATA[AND LAST_UPDATED_DATE <=to_timestamp(#{params.updateEndDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="(params.bomNo == null or params.bomNo =='')
                    and (params.prodplanId == null or params.prodplanId =='')
                    and (params.furnaceTempName == null or params.furnaceTempName =='')
                    and (params.updateStartDate == null or params.updateStartDate =='')
                    and (params.updateEndDate == null or params.updateEndDate =='')">
      and 1=2
    </if>
    order by last_updated_date desc
  </select>

  <select id="getBomCraftAttributeList" parameterType="com.zte.interfaces.dto.BomCraftAttributeDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from bom_craft_attribute
    where 1=1
    <if test="craftSection != null and craftSection != ''">
      AND CRAFT_SECTION = #{craftSection,jdbcType=VARCHAR}
    </if>
    <if test="bomNo != null and bomNo != ''">
      AND BOM_NO = #{bomNo,jdbcType=VARCHAR}
    </if>
    <if test="likeItemNo != null and likeItemNo != ''">
      AND BOM_NO like concat('%',#{likeItemNo,jdbcType=VARCHAR},'%')
    </if>
    <if test="furnaceTempName != null and furnaceTempName != ''">
      AND FURNACE_TEMP_NAME = #{furnaceTempName,jdbcType=VARCHAR}
    </if>
    <if test="surface != null and surface != ''">
      AND SURFACE = #{surface,jdbcType=VARCHAR}
    </if>
    <if test="leadFlag != null and leadFlag != ''">
      AND LEAD_FLAG = #{leadFlag,jdbcType=VARCHAR}
    </if>
    <if test="factoryId != null">
      AND FACTORY_ID = #{factoryId,jdbcType=INTEGER}
    </if>
    <if test="prodplanId != null and prodplanId != ''">
      AND PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR}
    </if>
    <if test="(bomNo == null or bomNo =='') and (likeItemNo == null or likeItemNo =='') and (prodplanId == null or prodplanId =='') and (surface == null or surface =='') and (leadFlag == null or leadFlag =='') and (factoryId == null)">
      and 1=2
    </if>
    order by last_updated_date desc
  </select>

  <select id="getCraftAttributeForWorkOrder" resultType="com.zte.interfaces.dto.WorkOrderCraftAttributeDTO">
    select a.bom_no,a.surface,a.furnace_temp_name,
    a.lead_flag,a.ink_jet_printer_template,a.ink_jet_printer_suffix,
    b.craft_section, b.lead_flag_work_order, a.factory_id, a.prodplan_id, b.prodplan_id as prodplanIdCondition
    from bom_craft_attribute a,
    <foreach collection="list" item="item" open="(" separator="union all" close=")">
      select #{item.bomNo,jdbcType=VARCHAR} bom_no,#{item.prodplanId,jdbcType=VARCHAR} prodplan_id,#{item.surface,jdbcType=VARCHAR} surface, #{item.leadFlag,jdbcType=VARCHAR} lead_flag,
      #{item.craftSection,jdbcType=VARCHAR} craft_section,#{item.leadFlagWorkOrder,jdbcType=VARCHAR} lead_flag_work_order,
      #{item.craftSectionCondition,jdbcType=VARCHAR} craft_section_condition
    </foreach>
    b
    where a.bom_no = b.bom_no
    and a.surface = b.surface
    and a.lead_flag = b.lead_flag
    and a.craft_section = b.craft_section_condition
  </select>

  <select id="getExportCount" resultType="java.lang.Integer" parameterType="com.zte.interfaces.dto.BomCraftAttributeDTO">
    select count(1)
    from bom_craft_attribute
    where 1=1
    <if test="bomNo != null and bomNo != ''">
      AND BOM_NO like concat(concat('%', #{bomNo,jdbcType=VARCHAR}),'%')
    </if>
	  <if test="createBy != null and createBy != ''">
		  AND CREATE_BY = #{createBy,jdbcType=VARCHAR}
	  </if>
    <if test="furnaceTempName != null and furnaceTempName != ''">
      AND FURNACE_TEMP_NAME like concat(concat('%', #{furnaceTempName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="surface != null and surface != ''">
      AND SURFACE = #{surface,jdbcType=VARCHAR}
    </if>
    <if test="prodplanId != null and prodplanId != ''">
      AND PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR}
    </if>
    <if test="updateStartDate != null and updateStartDate != ''">
      <![CDATA[AND LAST_UPDATED_DATE >=to_timestamp(#{updateStartDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="updateEndDate != null and updateEndDate != ''">
      <![CDATA[AND LAST_UPDATED_DATE <=to_timestamp(#{updateEndDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')]]></if>
    <if test="(bomNo == null or bomNo =='')
                    and (prodplanId == null or prodplanId =='')
                    and (furnaceTempName == null or furnaceTempName =='')
                    and (createBy == null or createBy =='')
                    and (updateStartDate == null or updateStartDate =='')
                    and (updateEndDate == null or updateEndDate =='')">
      and 1=2
    </if>
  </select>

  <update id="updateBomCraftAttributeById" parameterType="com.zte.interfaces.dto.BomCraftAttributeDTO">
    update bom_craft_attribute
    <set>
      <if test="furnaceTempName != null and furnaceTempName != ''">
        FURNACE_TEMP_NAME = #{furnaceTempName,jdbcType=VARCHAR},
      </if>
      <if test="surface != null and surface != ''">
        SURFACE = #{surface,jdbcType=VARCHAR},
      </if>
      <if test="leadFlag != null and leadFlag != ''">
        LEAD_FLAG = #{leadFlag,jdbcType=VARCHAR},
      </if>
      ink_jet_printer_template = #{inkJetPrinterTemplate,jdbcType=VARCHAR},
      ink_jet_printer_suffix = #{inkJetPrinterSuffix,jdbcType=VARCHAR},
      craft_section = #{craftSection,jdbcType=VARCHAR},
      factory_id = #{factoryId,jdbcType=INTEGER},
      prodplan_id = #{prodplanId,jdbcType=VARCHAR},
      LAST_UPDATED_DATE = sysdate,
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <insert id="insertBomCraftAttributeBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      insert into bom_craft_attribute (
      <if test="item.id != null and item.id != ''">
        id,
      </if>
      <if test="item.bomNo != null and item.bomNo != ''">
        bom_no,
      </if>
      <if test="item.bomName != null and item.bomName != ''">
        bom_name,
      </if>
      <if test="item.verNo != null and item.verNo != ''">
        ver_no,
      </if>
      <if test="item.surface != null and item.surface != ''">
        surface,
      </if>
      <if test="item.furnaceTempName != null and item.furnaceTempName != ''">
        furnace_temp_name,
      </if>
      <if test="item.leadFlag != null and item.leadFlag != ''">
        LEAD_FLAG,
      </if>
      <if test="item.createBy != null and item.createBy != ''">
        CREATE_BY,
      </if>
      <if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">
        LAST_UPDATED_BY,
      </if>
      <if test="item.prodplanId != null and item.prodplanId != ''">
        prodplan_id,
      </if>
      CREATE_DATE,
      LAST_UPDATED_DATE,
      ink_jet_printer_template,
      ink_jet_printer_suffix,
      craft_section,
      factory_id
      )
      values (
      <if test="item.id != null and item.id != ''">
        #{item.id,jdbcType=VARCHAR},
      </if>

      <if test="item.bomNo != null and item.bomNo != ''">
        #{item.bomNo,jdbcType=VARCHAR},
      </if>

      <if test="item.bomName != null and item.bomName != ''">
        #{item.bomName,jdbcType=VARCHAR},
      </if>

      <if test="item.verNo != null and item.verNo != ''">
        #{item.verNo,jdbcType=VARCHAR},
      </if>

      <if test="item.surface != null and item.surface != ''">
        #{item.surface,jdbcType=VARCHAR},
      </if>

      <if test="item.furnaceTempName != null and item.furnaceTempName != ''">
        #{item.furnaceTempName,jdbcType=VARCHAR},
      </if>

      <if test="item.leadFlag != null and item.leadFlag != ''">
        #{item.leadFlag,jdbcType=VARCHAR},
      </if>

      <if test="item.createBy != null">
        #{item.createBy,jdbcType=VARCHAR},
      </if>

      <if test="item.lastUpdatedBy != null">
        #{item.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="item.prodplanId != null and item.prodplanId != ''">
        #{item.prodplanId,jdbcType=VARCHAR},
      </if>
      sysdate,
      sysdate,
      #{item.inkJetPrinterTemplate,jdbcType=VARCHAR},
      #{item.inkJetPrinterSuffix,jdbcType=VARCHAR},
      #{item.craftSection,jdbcType=VARCHAR},
      #{item.factoryId,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <delete id="deleteCraftAttribute" parameterType="com.zte.interfaces.dto.BomCraftAttributeDTO">
    delete from
    bom_craft_attribute
    where 1=1
    <if test="id != null and id != ''"> and id = #{id,jdbcType=VARCHAR}</if>
    <if test="id == null or id == ''"> and 1=2</if>
  </delete>

  <delete id="deleteCraftAttributeSPM" >
    delete from
    bom_craft_attribute
    where CREATE_BY = 'SPM' AND LAST_UPDATED_BY='SPM'
  </delete>

  <select id="getItemNoList" parameterType="com.zte.interfaces.dto.BomCraftAttributeDTO" resultType="java.lang.String">
    select distinct t.bom_no
    from bom_craft_attribute t
    where t.bom_no like concat('%',#{likeItemNo}::text,'%')
    order by t.bom_no
    limit 1000
  </select>

  <select id="getBomInfoList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    , decode(LEAD_FLAG, '0', '有铅', '无铅') as LEAD_FLAG_MEAN
    from bom_craft_attribute
    where 1=1
    <if test="params.bomNo != null and params.bomNo != ''">
      AND BOM_NO like concat('%',#{params.bomNo,jdbcType=VARCHAR},'%')
    </if>
    order by CREATE_DATE desc
  </select>

</mapper>
