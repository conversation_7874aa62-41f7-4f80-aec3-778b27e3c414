<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.PushStdModelDataRepository">

    <sql id="Base_Column_List">
        task_no,
        item_no,
        task_qty,
        factory_id,
        customer_name,
        curr_process,
        push_status,
        push_date,
        error_msg,
        push_fail_count,
        create_by,
        create_date,
        last_updated_by,
        last_updated_date,
        enabled_flag
    </sql>
    <sql id="Column_List">
        psmd.task_no,
        psmd.item_no,
        psmd.task_qty,
        psmd.factory_id,
        psmd.customer_name,
        psmd.curr_process,
        psmd.push_status,
        psmd.push_date,
        psmd.error_msg,
        psmd.push_fail_count,
        psmd.create_by,
        psmd.create_date,
        psmd.last_updated_by,
        psmd.last_updated_date,
        psmd.enabled_flag
    </sql>

    <select id="getExistTaskNo" resultType="java.lang.String">
        select task_no from push_std_model_data t
        where t.enabled_flag = 'Y'
        and t.task_no in
        <foreach collection="taskNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="ExtResultMap" type="com.zte.interfaces.dto.PushStdModelDataExtDTO">
        <id column="task_no" property="taskNo"/>
        <result column="bill_no" property="billNo"/>
        <result column="customer_name" property="customerNo"/>
        <result column="customer_part_type" property="customerPartType"/>
        <result column="task_status" property="taskStatus"/>
        <result column="entity_class" property="entityClass"/>
        <result column="fix_bom_head_id" property="fixBomHeadId"/>
        <result column="cloud_type" property="cloudType"/>
        <result column="material_control" property="materialControl"/>
        <result column="last_updated_date" property="lastUpdatedDate"/>
    </resultMap>

    <select id="selectExtByQuery" resultMap="ExtResultMap" parameterType="com.zte.interfaces.dto.PushStdModelDataQueryDTO">
        <if test="processStatusConditions != null and processStatusConditions.size() > 0">
            WITH processStatusCondition AS (
            <foreach collection="processStatusConditions" index="index" item="item" separator="UNION ALL">
                SELECT #{item.currProcess} as curr_process, #{item.pushStatus} as push_status
            </foreach>
            )
        </if>
        select
            psmd.task_no,
            psmd.customer_name,
            psmd.last_updated_date,
            pt.task_status,
            pte.bill_no,
            pte.entity_class,
            pte.customer_part_type,
            pte.fix_bom_id,
            pte.fix_bom_head_id,
            pte.cloud_type,
            pte.material_control
        from push_std_model_data psmd
        inner join ps_task pt on psmd.task_no = pt.task_no and pt.enabled_flag = 'Y'
        inner join ps_task_extended pte on pte.task_no = pt.task_no and pte.enabled_flag = 'Y'
        <if test="processStatusConditions != null and processStatusConditions.size() > 0">
            inner join processStatusCondition psc on psmd.curr_process = psc.curr_process and psmd.push_status = psc.push_status
        </if>
        where psmd.enabled_flag = 'Y'
        <if test="factoryId != null">
            and psmd.factory_id = #{factoryId}
        </if>
        <if test="customerNos != null and customerNos.size() > 0">
            and psmd.customer_name in
            <foreach collection="customerNos" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="taskNos != null and taskNos.size() > 0">
            and psmd.task_no in
            <foreach collection="taskNos" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="lastUpdatedDateStart != null">
            and psmd.last_updated_date >= #{lastUpdatedDateStart}
        </if>
        <if test="taskNoFrom != null and taskNoFrom != ''">
            and not exists (
                select 1 from push_std_model_data a
                where psmd.task_no = a.task_no and a.task_no &lt;= #{taskNoFrom}
                <if test="lastUpdatedDateStart != null">
                    and a.last_updated_date = #{lastUpdatedDateStart}
                </if>
            )
        </if>
        order by psmd.last_updated_date, psmd.task_no
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into push_std_model_data (
        task_no,
        item_no,
        task_qty,
        factory_id,
        customer_name,
        curr_process)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
          #{item.taskNo},
          #{item.itemNo},
          #{item.taskQty},
          #{item.factoryId},
          #{item.customerName},
          #{item.currProcess})
        </foreach>
    </insert>

    <select id="getNeedPushDataInc" resultType="com.zte.interfaces.dto.PushStdModelDataDTO">
        select
        <include refid="Base_Column_List" />
        from push_std_model_data t
        where t.enabled_flag = 'Y'
        and t.last_updated_date >= #{startTime}
        and t.customer_name in
        <foreach collection="customerNameList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.push_status = '0'
        <if test="taskNo != null and taskNo != ''">
            and not exists ( select 1 from push_std_model_data a where t.task_no = a.task_no and a.task_no &lt;= #{taskNo} and a.last_updated_date = #{startTime})
        </if>
        order by t.last_updated_date, t.task_no
        limit #{limit}
    </select>
    <select id="selectDataByTaskNoLimit" resultType="com.zte.interfaces.dto.PushStdModelDataDTO">
        SELECT <include refid="Column_List" />,pt.org_id,pt.task_qty FROM push_std_model_data psmd
        inner join ps_task pt on psmd.task_no=pt.task_no
        <where>
            <if test="taskNo != null and taskNo != ''">
                and not exists ( select 1 from push_std_model_data as a where psmd.task_no = a.task_no and a.task_no &lt;= #{taskNo} and a.last_updated_date = #{startTime})
            </if>
            and psmd.last_updated_date >= #{startTime}
            and psmd.enabled_flag = 'Y'
            and psmd.curr_process = '20'
            and psmd.push_status = 2
            and psmd.customer_name in
            <foreach collection="customerNameList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and pt.task_status='已完工'
        </where>
        order by psmd.last_updated_date, psmd.task_no
        LIMIT 10
    </select>
    <select id="getPushSnNumByTaskNos" resultType="com.zte.interfaces.dto.PushStdDataTaskAndSnNumDTO">
        select
        psmsd.task_no,
        pte.customer_part_type,
        count(*) sn_num
        from
        ps_task_extended pte
        inner join push_std_model_sn_data psmsd on
        pte.task_no = psmsd.task_no
        where
        pte.task_no = #{taskNo}
        and psmsd.curr_process = '40'
        and psmsd.push_status = 2
        and psmsd.enabled_flag = 'Y'
        group by
        psmsd.task_no,pte.customer_part_type
    </select>
    <select id="selectPushFailData" resultType="com.zte.interfaces.dto.PushStdDataTaskAndSnNumDTO">
        select
        <include refid="Column_List" />,
        pte.customer_part_type
        from
        push_std_model_data psmd inner join ps_task_extended pte
        on psmd.task_no = pte.task_no
        <where>
            <if test="taskNo != null and taskNo != ''">
                and not exists ( select 1 from push_std_model_data as a where psmd.task_no = a.task_no and a.task_no &lt;= #{taskNo} and a.last_updated_date = #{startTime})
            </if>
            and psmd.enabled_flag = 'Y'
            and psmd.curr_process = '50'
            and psmd.push_status = 0
            and psmd.last_updated_date >= #{startTime}
            and psmd.customer_name = 'alibaba'
        </where>
        order by psmd.last_updated_date, psmd.task_no
        LIMIT 10
    </select>
    <select id="getDataByTaskNo" resultType="com.zte.interfaces.dto.PushStdModelDataDTO">
        select
        <include refid="Base_Column_List" />
        from
        push_std_model_data
        where
        task_no = #{taskNo}
        and enabled_flag = 'Y'
    </select>
    <select id="getListByTaskNo" resultType="com.zte.interfaces.dto.PushStdModelDataDTO">
        select
            <include refid="Base_Column_List" />
        from
            push_std_model_data
        where
            task_no in
            <foreach collection="taskNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and enabled_flag = 'Y'
    </select>

    <update id="update">
        update push_std_model_data t set
        <if test="currProcess != null and currProcess != ''">
          t.curr_process=#{currProcess},
        </if>
          t.push_status=#{pushStatus},
        <if test="pushDate != null">
            t.push_date=#{pushDate},
        </if>
          t.error_msg=#{errorMsg},
        <if test="pushFailCount != null">
          t.push_fail_count=#{pushFailCount},
        </if>
          t.last_updated_date=sysdate
          where task_no = #{taskNo}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE push_std_model_data
            <set>
                <if test="item.itemNo != null">item_no = #{item.itemNo},</if>
                <if test="item.taskQty != null">task_qty = #{item.taskQty},</if>
                <if test="item.factoryId != null">factory_id = #{item.factoryId},</if>
                <if test="item.customerName != null">customer_name = #{item.customerName},</if>
                <if test="item.currProcess != null">curr_process = #{item.currProcess},</if>
                <if test="item.pushStatus != null">push_status = #{item.pushStatus},</if>
                <if test="item.pushDate != null">push_date = #{item.pushDate},</if>
                <if test="item.errorMsg != null">error_msg = #{item.errorMsg},</if>
                <if test="item.pushFailCount != null">push_fail_count = #{item.pushFailCount},</if>
                <if test="item.lastUpdatedBy != null">last_updated_by = #{item.lastUpdatedBy},</if>
                last_updated_date = sysdate
            </set>
            WHERE task_no = #{item.taskNo}
        </foreach>
    </update>

    <insert id="batchSaveOrUpdate">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO push_std_model_data (
                task_no,
                item_no,
                task_qty,
                factory_id,
                customer_name,
                curr_process,
                push_status,
                push_date,
                error_msg,
                push_fail_count,
                create_by,
                create_date,
                last_updated_by,
                last_updated_date
            ) VALUES (
                #{item.taskNo},
                #{item.itemNo},
                #{item.taskQty},
                #{item.factoryId},
                #{item.customerName},
                #{item.currProcess},
                #{item.pushStatus},
                #{item.pushDate},
                #{item.errorMsg},
                #{item.pushFailCount},
                #{item.createBy},
                sysdate,
                #{item.lastUpdatedBy},
                sysdate
            )
            ON CONFLICT (task_no) DO UPDATE
            <set>
                <if test="item.itemNo != null">item_no = EXCLUDED.item_no,</if>
                <if test="item.taskQty != null">task_qty = EXCLUDED.task_qty,</if>
                <if test="item.factoryId != null">factory_id = EXCLUDED.factory_id,</if>
                <if test="item.customerName != null">customer_name = EXCLUDED.customer_name,</if>
                <if test="item.currProcess != null">curr_process = EXCLUDED.curr_process,</if>
                <if test="item.pushStatus != null">push_status = EXCLUDED.push_status,</if>
                <if test="item.pushDate != null">push_date = EXCLUDED.push_date,</if>
                <if test="item.errorMsg != null">error_msg = EXCLUDED.error_msg,</if>
                <if test="item.pushFailCount != null">push_fail_count = EXCLUDED.push_fail_count,</if>
                <if test="item.createBy != null">create_by = EXCLUDED.create_by,</if>
                <if test="item.createDate != null">create_date = EXCLUDED.create_date,</if>
                <if test="item.lastUpdatedBy != null">last_updated_by = EXCLUDED.last_updated_by,</if>
                last_updated_date = sysdate
            </set>
        </foreach>
    </insert>
</mapper>
