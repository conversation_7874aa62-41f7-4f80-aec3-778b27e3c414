<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.QualityCodeInfoRepository">
    <select id="getSnExistQualityCode" resultType="java.lang.String">
        select concat(sn,',',task_no) from quality_code_info
        where enabled_flag = 'Y'
        and (sn,task_no)in
        <foreach item="item" index="index" collection="qualityCodeInfoList" open="(" separator="," close=")">(#{item.sn},#{item.taskNo})</foreach>
    </select>
    <select id="getQualityCodeNotGenerated" parameterType="com.zte.interfaces.dto.SaveQualityCodeDTO" resultType="com.zte.interfaces.dto.QualityCodeInfoDTO">
        select sn,id,node_sn,last_updated_date,task_no,customer_no from quality_code_info t
        where t.enabled_flag = 'Y' and (t.quality_code is null or t.quality_code ='') and t.last_updated_date > sysdate-#{preDays}
        <if test="snList != null and snList.size() > 0">
            and t.node_sn IN
            <foreach item="sn" index="index" collection="snList" open="(" separator="," close=")">
                #{snList[${index}]}
            </foreach>
        </if>
        <if test="snList == null or snList.size() == 0">
            <if test="lastUpdatedDate != null ">
                and t.last_updated_date >= #{lastUpdatedDate, jdbcType=TIMESTAMP}
            </if>
            <if test="lastId != null and lastId !='' ">
                and not exists (select 1 from quality_code_info c where c.id = t.id and c.enabled_flag ='Y' and c.id &lt;= #{lastId, jdbcType=VARCHAR}
                and c.last_updated_date = #{lastUpdatedDate, jdbcType=TIMESTAMP})
            </if>
        </if>
        order by last_updated_date,id
        limit 1000

    </select>

    <select id="getQualityCodeBySnAndTaskNo" resultType="com.zte.interfaces.dto.QualityCodeInfoDTO">
        select sn, task_no, quality_code from quality_code_info
        where enabled_flag = 'Y'
        and (sn,task_no) in
        <foreach item="item" index="index" collection="qualityCodeInfoList" open="(" separator="," close=")">
            (#{item.sn},#{item.taskNo})
        </foreach>
    </select>

    <select id="getQualityCode" resultType="com.zte.interfaces.dto.QualityCodeInfoDTO">
        select * from quality_code_info where sn = #{sn} and task_no = #{taskNo} and enabled_flag = 'Y'
    </select>

    <select id="getSnInc" resultType="com.zte.interfaces.dto.QualityCodeInfoDTO">
        select sn,last_updated_date,task_no
        from quality_code_info t
        where t.enabled_flag = 'Y'
        and t.last_updated_date >= to_timestamp(#{lastUpdatedDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss')
        <if test="lastSn != null and lastSn !='' ">
            and not exists (select 1 from quality_code_info c where c.id = t.id and c.enabled_flag ='Y' and c.sn &lt;= #{lastSn, jdbcType=VARCHAR}
            and c.last_updated_date = to_timestamp(#{lastUpdatedDate,jdbcType=VARCHAR},'yyyy-MM-dd hh24:mi:ss'))
        </if>
        order by last_updated_date,sn
        limit #{limit}
    </select>

    <insert id="saveQualityCode" parameterType="java.util.List">
        INSERT INTO quality_code_info (id,sn, task_no, quality_code, customer_no,  create_date, last_updated_date, create_by, last_updated_by, node_sn,enabled_flag)
        VALUES
        <foreach collection="qualityCodeInfoList" item="item" separator=",">
            (
            #{item.id},
            #{item.sn},
            #{item.taskNo},
            #{item.qualityCode},
            #{item.customerNo},
            sysdate,
            sysdate,
            #{item.createBy},
            #{item.lastUpdatedBy},
            #{item.nodeSn},
            'Y'
            )
        </foreach>
    </insert>

    <insert id="updateQualityCode" parameterType="java.util.List">
        update quality_code_info  set quality_code = #{qualityCode},last_updated_date = sysdate where id = #{id} and enabled_flag = 'Y'
    </insert>
</mapper>

