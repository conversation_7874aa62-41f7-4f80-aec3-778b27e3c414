<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.MaterialStorageAttributesRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.MaterialStorageAttributesDTO" >
        <result column="id" property="id" />
        <result column="maintenance_type" property="maintenanceType" />
        <result column="barcode" property="barcode" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="supplier" property="supplier" />
        <result column="storage_method" property="storageMethod" />
        <result column="thawing_duration" property="thawingDuration" />
        <result column="usage_duration" property="usageDuration" />
        <result column="expiration_duration" property="expirationDuration" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="last_updated_by" property="lastUpdatedBy" />
        <result column="last_updated_date" property="lastUpdatedDate" />
        <result column="enabled_flag" property="enabledFlag" />
    </resultMap>

    <sql id="BATCH_LEVEL_QUERY_LIST">
        id, maintenance_type, barcode, item_code, item_name, supplier, storage_method, enabled_flag
    </sql>

    <select id="selectEntityListPage" parameterType="com.zte.interfaces.dto.MaterialStorageAttributesDTO"
            resultType="com.zte.interfaces.dto.MaterialStorageAttributesDTO" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.maintenance_type,
            t.barcode,
            t.item_code,
            t.item_name,
            t.supplier,
            t.storage_method,
            t.thawing_duration,
            t.min_thawing_duration,
            t.max_recover_storage_date,
            t.usage_duration,
            t.expiration_duration,
            t.create_by,
            t.create_date,
            t.last_updated_by,
            t.last_updated_date,
            t.enabled_flag
        FROM material_storage_attributes t
        <include refid="Where_Clause" />
    </select>

    <sql id="Where_Clause">
        WHERE t.enabled_flag = 'Y'
        <if test="null != maintenanceType and '' != maintenanceType">AND t.maintenance_type = #{maintenanceType}</if>
        <if test="null != barcode and '' != barcode">AND t.barcode = #{barcode}</if>
        <if test="barcodeList != null and barcodeList.size()>0">AND t.barcode IN
            <foreach collection="barcodeList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="null != itemCode and '' != itemCode">AND t.item_code = #{itemCode}</if>
        <if test="itemCodeList != null and itemCodeList.size()>0">AND t.item_code IN
            <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="null != itemName and '' != itemName">AND t.item_name = #{itemName}</if>
        <if test="null != supplier and '' != supplier">AND t.supplier = #{supplier}</if>
        <if test="supplierList != null and supplierList.size()>0">AND t.supplier IN
            <foreach collection="supplierList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="null != storageMethod and '' != storageMethod">AND t.storage_method = #{storageMethod}</if>
        <if test="null != thawingDuration and '' != thawingDuration">AND t.thawing_duration = #{thawingDuration}</if>
        <if test="null != usageDuration and '' != usageDuration">AND t.usage_duration = #{usageDuration}</if>
        <if test="null != expirationDuration and '' != expirationDuration">AND t.expiration_duration = #{expirationDuration}</if>
        <if test="null != createBy and '' != createBy">AND t.create_by = #{createBy}</if>
        <if test="null != lastUpdatedBy and '' != lastUpdatedBy">AND t.last_updated_by = #{lastUpdatedBy}</if>
        limit  #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </sql>

    <select id="levelQueryBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="BATCH_LEVEL_QUERY_LIST" />
        FROM material_storage_attributes t
        WHERE t.enabled_flag = 'Y'
        AND t.storage_method = '冷冻'
        AND t.maintenance_type = '条码'
        AND t.barcode in
        <foreach collection="materialList" open="(" item="item" separator="," close=")">
            #{item.barcode}
        </foreach>
        AND t.item_code IS NULL
        UNION ALL
        SELECT
        <include refid="BATCH_LEVEL_QUERY_LIST" />
        FROM material_storage_attributes t
        WHERE t.enabled_flag = 'Y'
        AND t.storage_method = '冷冻'
        AND t.maintenance_type = '物料代码'
        AND t.item_code in
        <foreach collection="materialList" open="(" item="item" separator="," close=")">
            #{item.itemCode}
        </foreach>
        AND t.barcode IS NULL
        AND t.supplier IS NULL
        UNION ALL
        SELECT
        <include refid="BATCH_LEVEL_QUERY_LIST" />
        FROM material_storage_attributes t
        WHERE t.enabled_flag = 'Y'
        AND t.maintenance_type = '物料代码+供应商'
        AND (t.item_code, t.supplier) in
        <foreach collection="materialList" open="(" item="item" separator="," close=")">
            (#{item.itemCode}, #{item.supplier})
        </foreach>
        AND t.barcode IS NULL
    </select>
</mapper>