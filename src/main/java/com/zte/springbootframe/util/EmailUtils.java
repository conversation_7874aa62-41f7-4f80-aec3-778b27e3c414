package com.zte.springbootframe.util;

import com.zte.application.MailLogService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MailLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 邮件服务
 *
 * <AUTHOR>
 */
@Component
public class EmailUtils {
    @Autowired
    private MailLogService mailLogService;

    @Value("${spring.mail.username:<EMAIL>}")
    private String mailAddress;


    public boolean sendMail(String recipent, String subjectCN, String subjectEN, String contentCN,
                            String contentEN) {

        return sendMail(subjectCN, subjectEN, contentCN, contentEN, "", "", "", "", recipent, 1);
    }

    /**
     * 发送邮件
     *
     * @param subjectCN  subjectCN
     * @param contentEN  contentEN
     * @param isLanguage 1 cn 2 en 3 en+cn
     * @return boolean
     */
    public boolean sendMail(String subjectCN, String subjectEN, String contentCN, String contentEN,
                            String clickSeeCN, String clickSeeEN, String linkCN, String linkEN, String recipent,
                            int isLanguage) {

        // 发送状态标志
        boolean sendStatusFlag = true;
        // 获取邮件发送实体
        MailLog mailEntity = getMailLog(subjectCN, subjectEN, contentCN, contentEN, clickSeeCN,
                clickSeeEN, linkCN, linkEN, recipent, isLanguage);
        // 调整用邮件服务发送邮件
        try {
            mailLogService.send(mailEntity);
        } catch (Exception e) {
            sendStatusFlag = false;
        }
        return sendStatusFlag;
    }

    public MailLog getMailLog(String subjectCN, String subjectEN, String contentCN, String contentEN,
                              String clickSeeCN, String clickSeeEN, String linkCN, String linkEN, String recipent,
                              int isLanguage) {
        MailLog mailEntity = new MailLog();
        mailEntity.setMailTo(recipent);
        mailEntity.setText(contentCN);
        mailEntity.setTextEn(contentEN);
        mailEntity.setSubject(subjectCN);
        mailEntity.setSubjectEn(subjectEN);
        mailEntity.setMailFrom(mailAddress);
        mailEntity.setMoreSee(clickSeeCN);
        mailEntity.setMoreSeeEn(clickSeeEN);
        mailEntity.setLinkUrl(linkCN);
        mailEntity.setLinkUrlEn(linkEN);
        mailEntity.setLang(isLanguage); // 中文+英文
        return mailEntity;
    }

    /**
     * 异步发送邮件
     *
     * @param recipient 邮件接收人，多个人分号切割
     * @param subjectCN 主题
     * @param subjectEN 主题英文
     * @param contentCN 正文i
     * @param contentEN 正文i
     * @return
     */
    @Async
    public void sendMailAsyn(String recipient, String subjectCN, String subjectEN, String contentCN, String contentEN) {
        this.sendMail(recipient, subjectCN, subjectEN, contentCN, contentEN);
    }

}
