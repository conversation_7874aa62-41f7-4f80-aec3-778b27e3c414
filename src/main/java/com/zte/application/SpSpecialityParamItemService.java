package com.zte.application;

import com.zte.domain.model.SpSpecialityParamItem;
import com.zte.interfaces.dto.SpRecoverImportDTO;
import com.zte.interfaces.dto.SpSpecialityParamItemPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 个参详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-15 17:12:33
 */
public interface SpSpecialityParamItemService {

    /**
     * 查询分页数据
     *
     * @param query 查询条件
     * @return 返回分页数据
     */
    PageRows<SpSpecialityParamItem> queryPage(SpSpecialityParamItemPageQueryDTO query);
    /**
     * 获取最大条码
     * @param specialityParamId
     * @return
     */
    Long getMaxBarcode(String specialityParamId);

    /**
     * 批量新增
     * @param list
     */
    void addBatch(List<SpSpecialityParamItem> list);

    void exportExcel(HttpServletResponse response, String specialityParamId);

    /**
    * @description: 同步个参数据到mds
    * @author: 00302458
    * @date: 2025/6/23 10:20
    */
    void syncDataToMds(String specialityParamId);
    /**
     * 导入回收Excel
     *
     * @param spRecoverImportDTO
     * @return 返回redis key
     */
    String importRecoverExcel(SpRecoverImportDTO spRecoverImportDTO);
    void importRecover(SpRecoverImportDTO spRecoverImportDTO) throws Exception;

    String getImportMessage(String importKey);

    /**
     * 确定回收
     * @param specialityParamId
     */
    void commitRecover(String specialityParamId);
}

