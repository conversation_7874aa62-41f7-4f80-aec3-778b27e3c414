package com.zte.application.material.impl;

import com.zte.application.material.OverTimeCheckService;

import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.DelayBillRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.material.dto.OverTimeBarcodeDTO;
import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.interfaces.material.dto.OverTimeOutDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_20000;
import static com.zte.common.utils.Constant.WARNING_DAYS;

/**
 * @Deacription 河源三期超期返货
 * <AUTHOR>
 * @Date 2023/7/6 16:46
 **/
@Service
public class OverTimeCheckServiceImpl implements OverTimeCheckService {

    @Autowired
    private DelayBillRepository delayBillRepository;

    @Autowired
    StepTransferRepository stepIscpRepository;

    @Autowired
    InventoryholdRecordRepository inventoryholdRecordRepository;

    @Override
    public ServiceData<List<OverTimeOutDTO>> queryInventory(OverTimeInDTO overTimeInDTO) {
        ServiceData<List<OverTimeOutDTO>> serviceData = new ServiceData<>();
        List<String> listIsN = new ArrayList<>();

        //oracle 获取是超二次继续存储期 的条码
        List<OverTimeBarcodeDTO> overTimeInDTOList = stepIscpRepository.getValidateBarcode(overTimeInDTO);

        //1 都可用
        if (overTimeInDTOList.size() == Constant.INT_0) {
            serviceData.setBo(getListOverTimeDto(overTimeInDTO.getItemBarcode(),listIsN));
            serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
            return serviceData;
        }
        //获取是超二次继续存储期的条码
        List<String> barcodeList = overTimeInDTOList.stream().map(k -> k.getItemBarcodes()).collect(Collectors.toList());
        //获取非超二次继续存储期的条码，设为Y
        List<String> listIsY = overTimeInDTO.getItemBarcode().stream().filter(e -> !barcodeList.contains(e)).collect(Collectors.toList());

        OverTimeInDTO overTimeInNewDTO = new OverTimeInDTO();
        overTimeInNewDTO.setItemBarcode(barcodeList);
        overTimeInNewDTO.setWhseid(overTimeInDTO.getWhseid());
        overTimeInNewDTO.setOrderKey(overTimeInDTO.getOrderKey());
        //获取数据字典
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000059);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
        //获取复检预警天数
        int warningDays = Constant.INT_87;
        SysLookupValuesDTO dayDto= sysLookupValuesDTOList.stream()
                .filter(item -> Constant.LOOKUP_CODE_100005900004.equals(item.getLookupCode()))
                .findFirst().orElse(new SysLookupValuesDTO());
        if(Tools.isNotEmpty(dayDto)){
            warningDays = Integer.parseInt(dayDto.getLookupMeaning());
        }
        overTimeInNewDTO.setWarningDays(warningDays);

        //查询超期条码的检验记录
        List<OverTimeOutDTO> listOverTimeOutDTO = delayBillRepository.queryInventory(overTimeInNewDTO);
        List<String> barcodeListByOver = listOverTimeOutDTO.stream().map(e -> e.getItemBarcode()).collect(Collectors.toList());
        //把未查出数据的设为N
        listIsN = barcodeList.stream().filter(e -> !barcodeListByOver.contains(e)).collect(Collectors.toList());

        //Y & N 输出
        listOverTimeOutDTO.addAll(getListOverTimeDto(listIsY,listIsN));
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        serviceData.setBo(listOverTimeOutDTO);
        return serviceData;
    }

    private List<OverTimeOutDTO> getListOverTimeDto(List<String> itemY,List<String> itemN) {
        List<OverTimeOutDTO> listOverTimeOutDTO = new ArrayList<>();
        for (String barcode : itemY) {
            OverTimeOutDTO overTimeIsYDTO = new OverTimeOutDTO();
            overTimeIsYDTO.setIsOk("Y");
            overTimeIsYDTO.setItemBarcode(barcode);
            listOverTimeOutDTO.add(overTimeIsYDTO);
        }
        for (String barcode : itemN) {
            OverTimeOutDTO overTimeIsNDTO = new OverTimeOutDTO();
            overTimeIsNDTO.setIsOk("N");
            overTimeIsNDTO.setItemBarcode(barcode);
            listOverTimeOutDTO.add(overTimeIsNDTO);
        }
        return listOverTimeOutDTO;
    }
}
