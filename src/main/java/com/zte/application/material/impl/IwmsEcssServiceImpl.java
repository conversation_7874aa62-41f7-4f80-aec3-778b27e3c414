package com.zte.application.material.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.material.IwmsEcssService;
import com.zte.application.onlinefallback.ConstantApplyBillStatus;
import com.zte.application.onlinefallback.impl.OnlineFallBackServiceImpl;
import com.zte.application.step.ZteAlibabaService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.material.IwmsEcssRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.material.ZteAliApprovalRepository;
import com.zte.domain.model.step.ZteAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.RedDotTaskDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.utils.CommonUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_1;
import static com.zte.common.utils.NumConstant.INT_2;
import static com.zte.resourcewarehouse.common.constant.ResponseConstant.ApplicationName.ZTE_MES_RESOURCEWAREHOUSE_BILL;

/**
 * <AUTHOR>
 *
 */
@Service
public class IwmsEcssServiceImpl implements IwmsEcssService {

	Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private IwmsEcssRepository iwmsEcssRepository;
	@Autowired
	private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;
	@Autowired
	private OnlineFallBackServiceImpl onlineFallBackService;
	@Autowired
	private InventoryholdRecordRepository inventoryholdRecordRepository;

	@Autowired
	private ZteAlibabaService zteAlibabaService;


	@Autowired
	private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

	@Autowired
	private ZteAlibabaRepository zteAlibabaRepository;
	@Autowired
	private EmailUtil emailUtil;
	@Value("${ecss.url}")
	private String ecssUrl;
	@Value("${ecss.iwms.auth.value}")
	private String ecssAuth;
	@Value("${ecss.iwms.feederSystem.token}")
	private String ecssToken;

	@Value("${redDot.task.url:}")
	private String url;

	@Value(("${redDot.task.token:}"))
	private String authToken;

	/**
	 * 推送单据信息到ECSS
	 */
	@Override
	public void pushBillToEcss(EcssBillDTO dto) {

		// 前端调用+定时补偿
		List<EcssBillDTO> ecssBillList = iwmsEcssRepository.getAddingBillNo(dto);
		if (CommonUtils.isEmpty(ecssBillList)) {
			return;
		}

		// 循环处理
		for (EcssBillDTO ecssBillDTO : ecssBillList) {

			// 组装数据
			FsDocumentDTO fsDocumentDTO = getFsDocumentDTO(ecssBillDTO.getReferenceNumber());

			// 调用ECSS接口
			ServiceData<?> res = invokeBillToEcss(JSONObject.toJSONString(fsDocumentDTO), ECSS_BILL_ASYNC);

			// 处理ECSSBill信息
			dealEcssBill(ecssBillDTO, fsDocumentDTO, res, dto.getLastUpdatedBy());
		}

	}

	/**
	 * 组装数据
	 */
	public FsDocumentDTO getFsDocumentDTO(String billNo) {

		FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
		fsDocumentDTO.setReferenceNumber(billNo);
		// ECSS单据编码
		fsDocumentDTO.setCode(getCode(billNo));
		String billType = billNo.substring(NumConstant.INT_0, NumConstant.INT_3);
		List<EcssBillDTO> materialInfoList = new ArrayList<>();
		// 来料不合格退货
		if (CommonUtils.equals(STRING_LTH, billType)) {
			fsDocumentDTO.setName(BILL_NAME_LTH);
			fsDocumentDTO.setDocumentTypeCode(BILL_TYPE_CODE_LTH);
			materialInfoList = iwmsEcssRepository.getReturnApplyMaterialInfo(billNo);
		}
		// 在线不合格退货
		if (CommonUtils.equals(STRING_STH, billType)) {
			fsDocumentDTO.setName(BILL_NAME_STH);
			fsDocumentDTO.setDocumentTypeCode(BILL_TYPE_CODE_STH);
			materialInfoList = iwmsEcssRepository.getFallbackApplyMaterialInfo(billNo);
		}

		// 路径
		List<FsCountryDTO> fsCountryDTOList = new ArrayList<>();
		FsCountryDTO fsCountryDTO = FsCountryDTO.builder().build().setCountryISOTwoDigitCode(COUNTRY_DIGIT_CODE);
		fsCountryDTOList.add(fsCountryDTO);
		FsCountryDTO fsCountryDTO1 = FsCountryDTO.builder().build().setCountryISOTwoDigitCode(COUNTRY_DIGIT_CODE);
		fsCountryDTOList.add(fsCountryDTO1);
		fsDocumentDTO.setRoutes(fsCountryDTOList);

		if (CommonUtils.isEmpty(materialInfoList)) {
			return fsDocumentDTO;
		}

		// 合作伙伴
		List<FsDocumentBusinessPartnerDTO> fsDocumentBusinessPartnerDTOList = new ArrayList<>();
		FsDocumentBusinessPartnerDTO fsDocumentBusinessPartnerDTO = FsDocumentBusinessPartnerDTO.
				builder().build().setMasterDataReferenceNumber(materialInfoList.get(NumConstant.INT_0).getCustomerNo())
				.setBusinessPartnerFunctionCode(BUSINESS_PARTNER_FUNCTION_CODE_BPF001)
				.setBusinessPartnerTypeCode(BUSINESS_PARTNER_TYPE_CODE_BPT001_SUPPLIER);
		fsDocumentBusinessPartnerDTOList.add(fsDocumentBusinessPartnerDTO);
		fsDocumentDTO.setFsDocumentBusinessPartnerDTOs(fsDocumentBusinessPartnerDTOList);

		// 物料
		List<FsDocumentMaterialDTO> fsDocumentMaterialDTOList = new ArrayList<>();
		for (EcssBillDTO ecssBillDTO : materialInfoList) {
			FsDocumentMaterialDTO fsDocumentMaterialDTO = new FsDocumentMaterialDTO();
			BeanUtils.copyProperties(ecssBillDTO, fsDocumentMaterialDTO);
			fsDocumentMaterialDTOList.add(fsDocumentMaterialDTO);
		}
		fsDocumentDTO.setFsDocumentMaterialDTOs(fsDocumentMaterialDTOList);

		return fsDocumentDTO;
	}

	public String getCode(String billNo) {

		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss"); // 定义日期格式
		String formattedDate = now.format(formatter);
		return billNo + ONE_SPLIT + formattedDate;
	}

	/**
	 * 调用ECSS推送单据的接口
	 * @return
	 */
	public ServiceData<?> invokeBillToEcss(String params, String interfaceUrl) {

		Map<String, String> header = new HashMap<>(INT_16);
		header.put(X_AUTH_VALUE, ecssAuth);
		header.put(X_FEEDER_SYSTEM_TOKEN, ecssToken);
		header.put(X_LANG_ID, X_LANG_ID_ZH);
		header.put(CONTENT_TYPE, APPLICATION_JSON);
		String url = ecssUrl + interfaceUrl;
		String result = HttpClientUtil.httpPostWithJSON(url, params, header);
		return (ServiceData) JSON.parseObject(result, ServiceData.class);
	}

	/**
	 * 根据ECSS返回消息处理
	 */
	public void dealEcssBill(EcssBillDTO dto, FsDocumentDTO fsDocumentDTO, ServiceData<?> res, String xEmpNo) {

		EcssBillDTO ecssBillDTO = new EcssBillDTO();
		if (CommonUtils.equals(FLAG_N, dto.getEnabledFlag())) {
			BeanUtils.copyProperties(fsDocumentDTO, ecssBillDTO);
		}
		ecssBillDTO.setSendParam(StringUtils.substring(JSONObject.toJSONString(fsDocumentDTO), NumConstant.INT_0, NumConstant.INT_2000));
		ecssBillDTO.setReferenceNumber(dto.getReferenceNumber());
		ecssBillDTO.setBillStatus(ECSS_BILL_ADDING);
		ecssBillDTO.setCreatedBy(xEmpNo);
		ecssBillDTO.setLastUpdatedBy(xEmpNo);
		if (Tools.isNotEmpty(res) && CommonUtils.equals(SUCESS_CODE, res.getCode().getCode())) {
			// 如果成功，更新标识为成功
			ecssBillDTO.setInvokeFlag(NumConstant.INT_1);
			ecssBillDTO.setFailNum(dto.getFailNum());
		} else {
			// 如果失败，更新失败次数和失败原因
			ecssBillDTO.setInvokeFlag(NumConstant.INT_0);
			ecssBillDTO.setFailNum(dto.getFailNum() + NumConstant.INT_1);
			ecssBillDTO.setFailReason(Tools.isEmpty(res)?null:res.getCode().getMsg());
		}
		// 新增或更新单据信息
		iwmsEcssRepository.insertOrUpdateEcssBill(ecssBillDTO);
		if (CommonUtils.equals(FLAG_N, dto.getEnabledFlag())) {
			// 如果是前端调用，需要更新退货申请单状态为“ECSS审核中” ECSSAUDITING
			String billType = dto.getReferenceNumber().substring(NumConstant.INT_0, NumConstant.INT_3);
			dto.setStatus(ECSSAUDITING);
			// 来料不合格退货
			if (CommonUtils.equals(STRING_LTH, billType)) {
				iwmsEcssRepository.updateReturnApplyBill(dto);
			}
			// 在线不合格退货 更新iwms状态为SUBMITED
			if (CommonUtils.equals(STRING_STH, billType)) {
				dto.setIwmsStatus(ConstantApplyBillStatus.SUBMITED);
				iwmsEcssRepository.updateFallbackApplyBill(dto);
			}
		}
		// 如果失败次数达到4次，则发邮件通知相关人员
		if (ecssBillDTO.getFailNum() == INT_4) {
			sendFailMail(ecssBillDTO.getReferenceNumber());
		}
	}

	/**
	 * ESCC校验失败触发红点通知
	 * @param documentCallbackResultDTO
	 * @param billType
	 */
	public void sendEsccRedDotTask(DocumentCallbackResultDTO documentCallbackResultDTO, String billType) {
		try {
			boolean ecssResult = CommonUtils.equals(S, documentCallbackResultDTO.getCStatus()) && ECSS_RELEASED_STATUS.contains(documentCallbackResultDTO.getStatus());
			if (ecssResult) {
				return;
			}
			EcssBillDTO dto = new EcssBillDTO();
			dto.setReferenceNumber(documentCallbackResultDTO.getRefNo());
			List<EcssBillDTO> ecssBillDTOS = new ArrayList<>();
			// 来料不合格退货
			if (CommonUtils.equals(STRING_LTH, billType)) {
				ecssBillDTOS = iwmsEcssRepository.queryReturnApplyInfo(dto);
			}
			// 在线不合格退货
			if (CommonUtils.equals(STRING_STH, billType)) {
				ecssBillDTOS = iwmsEcssRepository.queryFallbackApplyInfo(dto);
			}
			Map<String, String> mapHeader = null;
			RedDotTaskDTO redDotTaskDTO = null;
			StringBuilder desc = null;
			//获取数据字典（需要发送的人员信息）
			SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000072);
			List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
			if (Tools.isEmpty(sysLookupValuesDTOList)) {
				return;
			}
			List<String> lookupMeanings = sysLookupValuesDTOList.stream()
					.map(SysLookupValuesDTO::getLookupMeaning)
					.collect(Collectors.toList());
			String empNo = lookupMeanings.get(Constant.INT_0);
			lookupMeanings.remove(Constant.INT_0);
			String sendTo = String.join(SPLIT, lookupMeanings);
			for (EcssBillDTO ecssBillDTO : ecssBillDTOS) {
				mapHeader = new HashMap<>();
				mapHeader.put(X_FACTORY_ID, STR_51);
				mapHeader.put(X_EMP_NO, empNo);
				mapHeader.put(X_AUTH_VALUE, authToken);
				desc = new StringBuilder(RETURN_APPLYNO).append(SEMICOLON).append(ecssBillDTO.getReferenceNumber()).append(CHINESE_COMMA)
						.append(CREATOR).append(SEMICOLON).append(ecssBillDTO.getCreatedBy()).append(CHINESE_COMMA)
						.append(MATERIAL_CODE).append(SEMICOLON).append(ecssBillDTO.getItemNo()).append(CHINESE_COMMA)
						.append(SOURCE_LOCATION).append(SEMICOLON).append(StringUtils.isEmpty(ecssBillDTO.getWarehouseNo()) ? "" : ecssBillDTO.getWarehouseNo());
				redDotTaskDTO = new RedDotTaskDTO();
				redDotTaskDTO.setEqpCode(RED_DOT_TASK_ESCC);
				redDotTaskDTO.setEqpName(RED_DOT_TASK_ESCC);
				redDotTaskDTO.setFloor(B25);
				redDotTaskDTO.setReddotCode(DISTR02);
				redDotTaskDTO.setDesc(desc.toString());
				redDotTaskDTO.setSendTo(String.join(SPLIT,sendTo,ecssBillDTO.getCreatedBy()));
				redDotTaskDTO.setSendCc(String.join(SPLIT,sendTo,ecssBillDTO.getCreatedBy()));
				redDotTaskDTO.setSource(AUTOMATIC_ORDER);
				redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
				HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
			}
		}catch (Exception ex) {
			logger.error("ESCC校验失败触发红点通知失败：", ex);
		}
	}

	/**
	 * 查询数据字典维护的信息
	 */
	public List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto) {
		return inventoryholdRecordRepository.getLookupValues(dto);
	}
	/**
	 * 失败次数达到4次，则发邮件通知相关人员
	 */
	public void sendFailMail(String referenceNumber) {

		List<StSysLookupValuesDTO> sysLookupValuesDTOList = onlineFallBackApplyBillRepository.getSysLookupValues(LOOKUP_TYPE_1000039);
		if (CommonUtils.isEmpty(sysLookupValuesDTOList)) {
			return;
		}
		List<String> users = sysLookupValuesDTOList.stream().map(StSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());

		//发送邮件
		String content = IWMS_RETURN_BILL + "\r\n" + referenceNumber + "\r\n" + ECSS_SALE_BILL_FAILED_2;
		String receipts = StringUtils.join(
				users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
		emailUtil.sendMail(receipts, ECSS_RETURN_BILL_FAILED_WARN, BLANK, content, BLANK);
	}

	/**
	 * ECSS回调接口
	 */
	@Override
	public void callBackEcssToIwms(DocumentCallbackResultDTO dto) {

		BusiAssertException.isEmpty(dto, MessageId.CALL_BACK_DATA_EMPTY);
		BusiAssertException.isEmpty(dto.getRefNo(), MessageId.BILL_NO_EMPTY);

		List<EcssBillDTO> materialInfoList = new ArrayList<>();
		String billType = dto.getRefNo().substring(NumConstant.INT_0, NumConstant.INT_3);
		// 来料不合格退货
		if (CommonUtils.equals(STRING_LTH, billType)) {
			materialInfoList = iwmsEcssRepository.getReturnApplyMaterialInfo(dto.getRefNo());
		}
		// 在线不合格退货
		if (CommonUtils.equals(STRING_STH, billType)) {
			materialInfoList = iwmsEcssRepository.getFallbackApplyMaterialInfo(dto.getRefNo());
		}
		BusiAssertException.isEmpty(materialInfoList, MessageId.IMWS_QCBAD_RETURN_APPLY_BILL_NOT_EXISTS);
		String status = materialInfoList.get(NumConstant.INT_0).getStatus();
		String ecssResult = materialInfoList.get(NumConstant.INT_0).getEcssResult();
		// 如果退货申请单状态为ECSS审核中/ECSS已审核&不通过，才允许继续执行后续逻辑
		if (CommonUtils.equals(ECSSAUDITING, status) ||
			(CommonUtils.equals(ECSSAUDITED, status) && CommonUtils.equals(NOT_RELEASED_ZH, ecssResult))) {
			updateEcssDTO(dto);
			if (CommonUtils.equals(ECSSAUDITING, status)) {
				//第一次如果ESCC不通过触发红点提醒
				sendEsccRedDotTask(dto, billType);
			}
		}
	}

	/**
	 * 处理业务数据
	 */
	public void updateEcssDTO(DocumentCallbackResultDTO dto) {

		EcssBillDTO ecssBillDTO = new EcssBillDTO();
		ecssBillDTO.setReferenceNumber(dto.getRefNo());
		ecssBillDTO.setReturnParam(StringUtils.substring(JSONObject.toJSONString(dto), NumConstant.INT_0, NumConstant.INT_2000));
		ecssBillDTO.setBillStatus(ECSS_BILL_ADDED);
		ecssBillDTO.setLastUpdatedBy(dto.getLastUpdatedBy());

		// 更新ecssBill表
		iwmsEcssRepository.updateEcssBill(ecssBillDTO);

		// 更新退货申请单
		updateApplyBill(ecssBillDTO, dto);

		// 失效和新增ecss_outputcollection表信息
		EcssOutPutCollectionDTO ecssOutPutCollectionDTO = new EcssOutPutCollectionDTO();
		ecssOutPutCollectionDTO.setReferenceNumber(dto.getRefNo());
		BeanUtils.copyProperties(dto, ecssOutPutCollectionDTO);
		iwmsEcssRepository.failEcssOutputcollection(ecssOutPutCollectionDTO);
		iwmsEcssRepository.insertEcssOutputcollection(ecssOutPutCollectionDTO);

		// 失效和新增ecss_textretruncollection表信息
		iwmsEcssRepository.failEcssTextretruncollection(ecssOutPutCollectionDTO);
		List<EcssTextRetrunCollectionDTO> ecssTextRetrunCollectionDTOList = new ArrayList<>();
		if (CommonUtils.isNotEmpty(dto.getFsFReasonDTOs())) {
			for (FsfReasonDTO fsfReasonDTO : dto.getFsFReasonDTOs()) {
				EcssTextRetrunCollectionDTO ecssTextRetrunCollectionDTO = EcssTextRetrunCollectionDTO.builder().build()
						.setReferenceNumber(dto.getRefNo()).setCreatedBy(dto.getCreatedBy()).setItemNo(fsfReasonDTO.getRefNo())
						.setLaw(fsfReasonDTO.getLaw()).setFReason(fsfReasonDTO.getFReason()).setSType(fsfReasonDTO.getSType());
				ecssTextRetrunCollectionDTOList.add(ecssTextRetrunCollectionDTO);
			}
		}
		if (CommonUtils.isNotEmpty(dto.getFMessages())) {
			for (String str : dto.getFMessages()) {
				EcssTextRetrunCollectionDTO ecssTextRetrunCollectionDTO = EcssTextRetrunCollectionDTO.builder().build()
						.setReferenceNumber(dto.getRefNo()).setCreatedBy(dto.getCreatedBy()).setFMessages(str);
				ecssTextRetrunCollectionDTOList.add(ecssTextRetrunCollectionDTO);
			}
		}
		if (CommonUtils.isEmpty(ecssTextRetrunCollectionDTOList)) {
			return;
		}
		for (List<EcssTextRetrunCollectionDTO> tempList : CommonUtils.splitList(ecssTextRetrunCollectionDTOList, NumConstant.INT_500)) {
			iwmsEcssRepository.insertEcssTextretruncollection(tempList);
		}

	}

	/**
	 * 更新退货申请单
	 */
	public void updateApplyBill(EcssBillDTO ecssBillDTO, DocumentCallbackResultDTO dto) {

		// ECSS扫描结果
		boolean ecssResult = CommonUtils.equals(S, dto.getCStatus()) && ECSS_RELEASED_STATUS.contains(dto.getStatus());
		// 通过/不通过
		if (ecssResult) {
			ecssBillDTO.setEcssResult(RELEASED_ZH);
		} else {
			ecssBillDTO.setEcssResult(NOT_RELEASED_ZH);
		}
		// 更新退货申请单状态为“ECSS已审核” ECSSAUDITED
		ecssBillDTO.setStatus(ECSSAUDITED);
		String billType = ecssBillDTO.getReferenceNumber().substring(NumConstant.INT_0, NumConstant.INT_3);
		// 来料不合格退货
		if (CommonUtils.equals(STRING_LTH, billType)) {
			// 更新退货申请单
			iwmsEcssRepository.updateReturnApplyBill(ecssBillDTO);
		}
		// 在线不合格退货
		if (CommonUtils.equals(STRING_STH, billType)) {
			// 更新退货申请单
			iwmsEcssRepository.updateFallbackApplyBill(ecssBillDTO);
		}
	}

	/**
	 * 退货申请单推送infor
	 */
	@Override
	public void submitApllyToInforSo(EcssBillDTO dto) {

		// 获取需要推送INFOR的退货申请单（状态为ECSS审批通过或者客户审批通过）
		dto.setSubmitTimes(Constant.INT_0);
		List<EcssBillDTO> list = iwmsEcssRepository.getApplyBillToInfor(dto);
		if (CommonUtils.isEmpty(list)) {
			return;
		}
		// ECSS审批通过的单据,先根据单号查询物料代码信息
		List<EcssBillDTO> ecssBillDTOS = iwmsEcssRepository.selectItemNoByBillNo(
				list.stream().filter(item -> ECSSAUDITED.equals(item.getStatus()))
						.map(EcssBillDTO::getReferenceNumber)
						.collect(Collectors.toList()));

		//查询属于阿里管控的物料代码
		List<ItemNoMpnDTO> itemNoMpnDTOList = zteAlibabaRepository.getControlItemNo(new ArrayList<>(ecssBillDTOS
				.stream().map(EcssBillDTO::getMasterDataReferenceNumber)
				.collect(Collectors.toSet())));

		Set<String> controlItemNo = itemNoMpnDTOList.stream().map(ItemNoMpnDTO::getItemNo).collect(Collectors.toSet());
		//执行入库申请
		executeDeDeduction(ecssBillDTOS, new ArrayList<>(controlItemNo));
		//非阿里管控的
		Set<String> noAliControlBillNos = ecssBillDTOS.stream()
				.filter(item -> !controlItemNo.contains(item.getMasterDataReferenceNumber()))
				.map(EcssBillDTO::getReferenceNumber)
				.collect(Collectors.toSet());
		//客户审批通过的单据或者非阿里管控的单据直接推送info
		list = list.stream()
				.filter(item -> STR_APPROVED.equals(item.getStatus())
						|| noAliControlBillNos.contains(item.getReferenceNumber()))
				.collect(Collectors.toList());

		// ECSS扫描通过，退货申请单推送infor
		for (EcssBillDTO ecssBillDTO : list) {
			ecssBillDTO.setStatus(STRING_EMPTY);
			logger.info("submitApllyToInforSo-JOB:{}", ecssBillDTO.getReferenceNumber());
			String billType = ecssBillDTO.getReferenceNumber().substring(NumConstant.INT_0, NumConstant.INT_3);
			try {
				// 来料不合格退货
				if (CommonUtils.equals(STRING_LTH, billType)) {
					Map<Object, Object> params = new HashMap<>();
					params.put(RETURN_APPLY_NO, ecssBillDTO.getReferenceNumber());
					RemoteServiceDataUtil.invokeService(ZTE_MES_RESOURCEWAREHOUSE_BILL, MicroServiceNameEum.VERSION,
							MicroServiceNameEum.SENDTYPEPOST, RETURN_APPLY_SUBMIT_TO_INFOR_URL, JSONObject.toJSONString(params), Tools.newHashMap());
				}
				// 在线不合格退货
				if (CommonUtils.equals(STRING_STH, billType)) {
					onlineFallBackService.submitApllyToInforSo(ecssBillDTO.getReferenceNumber());
				}
			} catch(Exception e) {
				logger.error("submitApllyToInforSo-JOB exception ", e);
			} finally {
				ecssBillDTO.setSubmitTimes(Constant.INT_1);
				// 来料不合格退货
				if (CommonUtils.equals(STRING_LTH, billType)) {
					iwmsEcssRepository.updateReturnApplyBill(ecssBillDTO);
				}
				// 在线不合格退货
				if (CommonUtils.equals(STRING_STH, billType)) {
					iwmsEcssRepository.updateFallbackApplyBill(ecssBillDTO);
				}
			}
		}
		// 推送infor失败4次的退货申请单，邮件告警
		sendInforFailMail();
	}

	private void executeDeDeduction(List<EcssBillDTO> ecssBillDTOS, List<String> controlItemNo) {
		//获取阿里管控物料的单号
		Set<String> billNos = ecssBillDTOS.stream()
				.filter(item -> controlItemNo.contains(item.getMasterDataReferenceNumber()))
				.map(EcssBillDTO::getReferenceNumber).collect(Collectors.toSet());
		//调用阿里执行申请单据
		billNos.forEach(billNo -> {
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(billNo);
			zteDeductionPlanParamDTO.setBusinessType(STR_1);
			zteDeductionPlanParamDTO.setEmpNo(SYSTEM);
			zteAlibabaService.getBillType(zteDeductionPlanParamDTO);
			zteAlibabaService.deductionPlan(zteDeductionPlanParamDTO);
		});
	}


	/**
	 * 推送infor失败次数达到4次，则发邮件通知相关人员
	 */
	public void sendInforFailMail() {

		// 获取推送infor失败4次的退货申请单
		EcssBillDTO dto = EcssBillDTO.builder().build().setSubmitTimes(INT_4);
		List<EcssBillDTO> list = iwmsEcssRepository.getApplyBillToInfor(dto);
		if (CommonUtils.isEmpty(list)) {
			return;
		}
		List<String> billNos = list.stream().map(EcssBillDTO::getReferenceNumber).collect(Collectors.toList());

		List<StSysLookupValuesDTO> sysLookupValuesDTOList = onlineFallBackApplyBillRepository.getSysLookupValues(LOOKUP_TYPE_1000040);
		if (CommonUtils.isEmpty(sysLookupValuesDTOList)) {
			return;
		}
		List<String> users = sysLookupValuesDTOList.stream().map(StSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());

		//发送邮件
		String content = IWMS_RETURN_BILL + "\r\n" + StringUtils.join(billNos,COMMA) + "\r\n" + IWMS_RETURN_BILL_SUBMIT_TO_INFOR_FAILED;
		String receipts = StringUtils.join(
				users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
		emailUtil.sendMail(receipts, IWMS_RETURN_BILL_SUBMIT_TO_INFOR_FAILED_WARN, BLANK, content, BLANK);
	}

}
