package com.zte.application.material.impl;

import com.zte.application.material.BigDataBackService;
import com.zte.domain.model.material.*;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zte.common.utils.Constant.INT_1000;

/**
 * @Deacription 原材料大数据量表归档
 * <AUTHOR>
 * @Date 2023/11/9
 */
@Service
public class BigDataBackServiceImpl implements BigDataBackService {

    private static final Logger logger = LoggerFactory.getLogger(BigDataBackServiceImpl.class);

    @Autowired
    StTallyPackingHeadRepository stTallyPackingHeadRepository;

    @Autowired
    StTallyPackingHeadBackRepository stTallyPackingHeadBackRepository;

    @Autowired
    StTallyPackingDetailBackRepository stTallyPackingDetailBackRepository;

    @Autowired
    StDeliveryBackRepository stDeliveryBackRepository;

    @Autowired
    StDeliveryRepository stDeliveryRepository;

    @Autowired
    StTallyPackingDetailRepository stTallyPackingDetailRepository;

    /**
     * 备份理货打包信息
     */
    @Override
    public void backTallyPacking() {
        try {
            //1.获取理货打包头表信息
            List<StTallyPackingHead> stList=stTallyPackingHeadRepository.getStTallyPackingHead();
            //2.分批插入及删除
            List<List<StTallyPackingHead>> lists = CommonUtils.splitList(stList,INT_1000);

            for (List<StTallyPackingHead> list : lists) {
                //2.1插入头数据
                stTallyPackingHeadBackRepository.insertStTallyPackingHeadBack(list);
                //2.2插入明细数据
                stTallyPackingDetailBackRepository.insertStTallyPackingDetailBack(list);
                //2.3插入st_delivery信息
                stDeliveryBackRepository.insertStDeliveryBack(list);
                //2.4删除st_delivery信息
                stDeliveryRepository.deleteStDelivery(list);
                //2.5删除明细信息
                stTallyPackingDetailRepository.deleteStTallyPackingDetail(list);
                //2.6删除头信息
                stTallyPackingHeadRepository.deleteStTallyPackingHead(list);
            }
        }catch (Exception e) {
            logger.error("backTallyPacking : Exception", e);
        }
    }
}
