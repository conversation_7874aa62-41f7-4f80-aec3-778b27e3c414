package com.zte.application.material;

import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.interfaces.material.dto.OverTimeOutDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * @Deacription 河源三期超期查询
 * <AUTHOR>
 * @Date 2023/7/6 15:56
 **/
public interface OverTimeCheckService {

    /**
     * 河源三期超期查询
     *
     * @param overTimeInDTO 查询条件
     * @return 条码信息
     */
    ServiceData<List<OverTimeOutDTO>> queryInventory(OverTimeInDTO overTimeInDTO);
}
