package com.zte.application.material;

import com.zte.interfaces.step.dto.DocumentCallbackResultDTO;
import com.zte.interfaces.step.dto.EcssBillDTO;

/**
 * <AUTHOR>
 *
 */
public interface IwmsEcssService {

    /**
     * 推送单据信息到ECSS
     */
    void pushBillToEcss(EcssBillDTO dto);

    /**
     * ECSS新增单据回调接口
     */
    void callBackEcssToIwms(DocumentCallbackResultDTO dto);

    /**
     * 提交退货申请单到INFOR(JOB)
     */
    void submitApllyToInforSo(EcssBillDTO dto);

}
