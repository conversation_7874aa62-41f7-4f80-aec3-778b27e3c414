package com.zte.application.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.PushStdModelDataService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.enums.SceneCodeEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_500;

/**
 * <AUTHOR>
 * @date 2025/5/15 19:29
 */
@Component
@Slf4j
public class EntityModifyMsgConsumer extends AbstractMsgConsumer {
    @Resource
    private PsTaskService psTaskService;
    @Resource
    private PlanScheduleRemoteService planScheduleRemoteService;
    @Resource
    private PushStdModelDataService pushStdModelDataService;
    @Resource
    private PsTaskExtendedService psTaskExtendedService;
    @Resource
    private PushStdModelConfirmationService pushStdModelConfirmationService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Override
    protected void process(JSONArray jsonArray) {
        List<EntityQueryDTO> entityQueryDTOList = convert(jsonArray);
        Map<String, PsTask> psTaskMap = getPsTaskMap(entityQueryDTOList);
        doProcess(entityQueryDTOList, psTaskMap);
        transactionTemplate.execute(status -> {
            updateIfChange(psTaskMap.values());
            return null;
        });
    }

    private void updateIfChange(Collection<PsTask> psTasks) {
        List<PsTask> psTaskList = IterUtil.toList(CollUtil.filterNew(psTasks, item -> ObjUtil.isNotNull(item.getTaskQty())));
        updateTask(psTaskList);
        updateIfExistAlibabaTask(psTaskList);
        updateQtyByTaskNo(psTasks);
    }

    private void updateTask(List<PsTask> psTaskList) {
        psTaskList.forEach(item -> item.setLastUpdatedBy(Constant.SYSTEM));
        psTaskService.batchUpdateByPK(psTaskList);
        log.info("完成批量更新任务数量，任务号{}", CollUtil.trans(psTaskList, PsTask::getTaskNo));
    }

    private void updateQtyByTaskNo(Collection<PsTask> psTasks) {
        psTasks = psTasks.stream().filter(item -> BooleanUtil.and(ObjUtil.notEqual(Constant.FACTORY_ID_CENTER, Convert.toStr(item.getFactoryId())), ObjUtil.isNotNull(item.getTaskQty())))
                        .map(item -> {
                            PsTask psTask = new PsTask();
                            psTask.setTaskNo(item.getTaskNo());
                            psTask.setTaskQty(item.getTaskQty());
                            psTask.setAttribute2(item.getAttribute2());
                            psTask.setFactoryId(item.getFactoryId());
                            return psTask;
                        }).collect(Collectors.toList());
        planScheduleRemoteService.updateQtyByTaskNo(psTasks);
        log.info("完成调用远程接口-根据任务号更新任务数量或指令数量，任务号{}", CollUtil.trans(psTasks, PsTask::getTaskNo));
    }

    private void updateIfExistAlibabaTask(List<PsTask> psTaskList) {
        Map<String, Boolean> alibabaTaskMap = psTaskService.judgeAlibabaTask(CollUtil.trans(psTaskList, PsTask::getItemNo));
        psTaskList = psTaskList.stream().filter(item -> alibabaTaskMap.getOrDefault(item.getItemNo(), false)).collect(Collectors.toList());
        List<PushStdModelDataDTO> pushStdModelDataDTOList = getPushStdModelDataDTOList(psTaskList);
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.listByTaskNos(CollUtil.trans(pushStdModelDataDTOList, PushStdModelDataDTO::getTaskNo));
        Map<String, PsTaskExtendedDTO> psTaskExtendedDTOMap = IterUtil.toMap(psTaskExtendedDTOList, PsTaskExtendedDTO::getTaskNo);
        pushStdModelDataService.batchUpdate(pushStdModelDataDTOList);
        pushSchedulingInfo(pushStdModelDataDTOList, psTaskExtendedDTOMap);
        log.info("完成保存阿里任务推送信息，任务号{}", CollUtil.trans(pushStdModelDataDTOList, PushStdModelDataDTO::getTaskNo));
    }

    private void pushSchedulingInfo(List<PushStdModelDataDTO> pushStdModelDataDTOList, Map<String, PsTaskExtendedDTO> psTaskExtendedDTOMap) {
        pushStdModelDataDTOList.forEach(item -> {
            try {
                PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedDTOMap.get(item.getTaskNo());
                if (psTaskExtendedDTO == null){
                    log.warn("任务{}对应的附属信息记录不存在", item.getTaskNo());
                    return;
                }
                BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> pushFunction = getPushFunction(psTaskExtendedDTO);
                Boolean success = pushFunction.apply(item, psTaskExtendedDTO);
                if (BooleanUtil.isFalse(success)){
                    log.error("推送阿里任务失败，任务号：{}，错误信息{}", item.getTaskNo(), item.getErrorMsg());
                    return;
                }
                log.info("推送阿里任务成功，任务号：{}", item.getTaskNo());
            } catch (Exception e) {
                log.error("推送阿里任务失败，任务号：{}，错误信息：{}", item.getTaskNo(), e.getMessage());
                AlarmHelper.alarm("imes_req_monitor_alarm", "1001", AlarmSeverityEnum.CRITICAL, "推送阿里任务失败", String.format("推送阿里任务失败，任务号：%s，错误信息：%s", item.getTaskNo(), e.getMessage()));
            }
        });
    }

    private BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> getPushFunction(PsTaskExtendedDTO psTaskExtendedDTO) {
        if (isBufferTask(psTaskExtendedDTO.getTaskType())){
            PushStdModelConfirmationDTO confirmationDTO = new PushStdModelConfirmationDTO();
            confirmationDTO.setTaskNo(psTaskExtendedDTO.getTaskNo());
            List<PushStdModelConfirmationDTO> pushStdModelConfirmationServiceList = pushStdModelConfirmationService.getList(confirmationDTO);
            List<Integer> focusPushStatus = Arrays.asList(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK, Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
            pushStdModelConfirmationServiceList = pushStdModelConfirmationServiceList.stream().filter(item -> focusPushStatus.contains(item.getPushStatus())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pushStdModelConfirmationServiceList)){
                return (pushStdModelDataDTO, psTaskExtended) -> {
                    try {
                        return pushStdModelDataService.pushSchedulingInfo(pushStdModelDataDTO, psTaskExtended, SceneCodeEnum.FORMAL_SCEDULE);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                };
            }
        }
        return (pushStdModelDataDTO, psTaskExtended) -> {
            try {
                return pushStdModelDataService.pushSchedulingInfo(pushStdModelDataDTO, MapUtil.of(psTaskExtended.getTaskNo(), psTaskExtended));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    private boolean isBufferTask(String taskType) {
        return Constant.STR_TWO.equals(taskType);
    }

    private List<PushStdModelDataDTO> getPushStdModelDataDTOList(List<PsTask> psTaskList) {
        List<String> taskNos = psTaskList.stream().map(PsTask::getTaskNo).distinct().collect(Collectors.toList());
        List<PushStdModelDataDTO> pushStdModelDataDTOList = pushStdModelDataService.getListByTaskNo(taskNos);
        Map<String, PsTask> psTaskMap = IterUtil.toMap(psTaskList, PsTask::getTaskNo);
        return pushStdModelDataDTOList.stream().peek(item -> {
            PsTask psTask = psTaskMap.get(item.getTaskNo());
            item.setTaskQty(Convert.toInt(psTask.getTaskQty()));
        }).collect(Collectors.toList());
    }

    private void doProcess(List<EntityQueryDTO> entityQueryDTOList, Map<String, PsTask> psTaskMap) {
        entityQueryDTOList.forEach(item -> Opt.ofNullable(psTaskMap.get(item.getEntityNo()))
                .filter(psTask -> StrUtil.equals(Convert.toStr(item.getStockOrgId()), Convert.toStr(psTask.getOrgId())))
                .ifPresent(psTask -> {
                    psTask.setTaskQty(NumberUtil.toBigDecimal(item.getQty()));
                    psTask.setAttribute2(psTask.getTaskQty());
                }));
    }

    private Map<String, PsTask> getPsTaskMap(List<EntityQueryDTO> entityQueryDTOList) {
        List<PsTask> psTaskList = psTaskService.getPsTask(entityQueryDTOList.stream().map(EntityQueryDTO::getEntityNo).distinct().collect(Collectors.toList()));
        psTaskList = psTaskList.stream().map(item -> {
            PsTask psTask = new PsTask();
            psTask.setTaskId(item.getTaskId());
            psTask.setTaskNo(item.getTaskNo());
            psTask.setFactoryId(item.getFactoryId());
            psTask.setItemNo(item.getItemNo());
            psTask.setOrgId(item.getOrgId());
            psTask.setTaskQty(null);
            psTask.setAttribute2(null);
            return psTask;
        }).collect(Collectors.toList());
        return IterUtil.toMap(psTaskList, PsTask::getTaskNo);
    }

    private List<EntityQueryDTO> convert(JSONArray jsonArray){
        return jsonArray.stream().map(item -> ((JSONObject)item).toJavaObject(EntityQueryDTO.class)).collect(Collectors.toList());
    }

    @Override
    protected int limitSize() {
        return INT_500;
    }
}
