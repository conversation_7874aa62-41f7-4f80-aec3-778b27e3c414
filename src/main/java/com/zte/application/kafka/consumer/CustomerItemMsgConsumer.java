package com.zte.application.kafka.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.CustomerItemsService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.NoticeCenterService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.NoticeCenter.IcenterInfo;
import com.zte.interfaces.dto.NoticeCenter.NoticeCenterMsgDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


/**
 * <AUTHOR>
 * @description 客户物料消息消费者
 * @date 2025/4/17 15:04
 */
@Component
@Slf4j
public class CustomerItemMsgConsumer extends AbstractMsgConsumer{
    @Resource
    private CustomerItemsService customerItemsService;
    @Resource
    private SysLookupValuesService sysLookupValuesService;
    @Resource
    private NoticeCenterService noticeCenterService;
    @Value("${customerItems.alarmMetric:imes_req_monitor_alarm}")
    private String alarmMetric;
    @Value("${customerItems.alarmKey:1001}")
    private String alarmKey;
    @Value("${customerItems.iCenter.template.id:W2025041815310150458}")
    private String iCenterTemplateId;
    private final Map<String, String> fieldMapping = MapUtil.<String, String>builder().put("itemNo", "zteCode").put("itemName", "zteCodeName")
            .put("customerType", "cooperationMode").put("customerCode", "customerNumber")
            .put("customerItemNo", "customerCode").put("isValid", "enabledFlag").put("customerComponentType", null)
            .put("customerComponentTypeEn", "customerComponentType").build();
    private final Map<String, MappingTarget> keyFieldCovertConfig = MapUtil.<String, MappingTarget>builder()
            .put("customerType", new MappingTarget(createValueMappingFunction(LOOKUP_TYPE_7301.intValue(), SysLookupValues::getAttribute1), "customerType"))
            .put("customerCode", new MappingTarget(createValueMappingFunction(LOOKUP_TYPE_7300.intValue(), SysLookupValues::getAttribute2), "customerName")).build();
    private final Supplier<String[]> noticeToSupplier = () -> getNoticeTos(LOOKUP_TYPE_2025041701.intValue());

    @Override
    protected void process(JSONArray jsonArray) {
        performRemove(jsonArray);
        List<CustomerItemsDTO> sourceList = getSourceList(jsonArray);
        performUpdate(sourceList);
        performSave(sourceList);
    }

    @Override
    protected int limitSize() {
        return INT_100;
    }

    private void performSave(List<CustomerItemsDTO> saveList) {
        saveList.forEach(item -> {
            item.setCreateBy(Constant.SYSTEM);
            item.setLastUpdatedBy(Constant.SYSTEM);
        });
        save(saveList);
        sendNotice(saveList);
    }

    private List<CustomerItemsDTO> getSourceList(JSONArray jsonArray) {
        return jsonArray.stream().map(item -> convert((JSONObject) JSON.toJSON(item))).collect(Collectors.toList());
    }

    private void performRemove(JSONArray jsonArray) {
        List<String> removeList = Lists.newArrayListWithCapacity(50);
        Iterator<Object> iterator = jsonArray.iterator();
        while (iterator.hasNext()) {
            JSONObject customerItemMsg = (JSONObject) iterator.next();
            if (shouldRemove(customerItemMsg)) {
                removeList.add(customerItemMsg.getString("itemNo"));
                iterator.remove();
            }
        }
        remove(removeList);
    }

    private void performUpdate(List<CustomerItemsDTO> sourceList) {
        List<CustomerItemsDTO> updateList = Lists.newArrayListWithCapacity(50);
        List<CustomerItemsDTO> targetList = customerItemsService.queryListByZteCodes(sourceList.stream().map(CustomerItemsDTO::getZteCode).collect(Collectors.toList()));
        Opt.ofEmptyAble(targetList).ifPresent(list -> {
            Map<String, CustomerItemsDTO> targetMap = targetList.stream().collect(Collectors.toMap(CustomerItemsDTO::getZteCode, Function.identity(), (prev, last) -> prev));
            Iterator<CustomerItemsDTO> sourceIterator = sourceList.iterator();
            while (sourceIterator.hasNext()) {
                CustomerItemsDTO source = sourceIterator.next();
                Opt.ofNullable(targetMap.get(source.getZteCode())).ifPresent(target -> {
                    BeanUtil.copyProperties(source, target, CopyOptions.create().ignoreNullValue());
                    target.setStatus(Constant.FLAG_Y);
                    target.setLastUpdatedBy(Constant.SYSTEM);
                    updateList.add(target);
                    sourceIterator.remove();
                });
            }
            update(updateList);
        });
    }

    private void update(List<CustomerItemsDTO> updateList) {
        Opt.ofEmptyAble(updateList).ifPresent(customerItemsService::batchUpdateByZteCode);
    }

    private CustomerItemsDTO convert(JSONObject customerItemMsg) {
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        Map<String, Object> resultMap = keyFieldConvert(customerItemMsg);
        BeanUtil.copyProperties(resultMap, customerItemsDTO, CopyOptions.create().setFieldMapping(fieldMapping));
        return customerItemsDTO;
    }

    private Map<String, Object> keyFieldConvert(JSONObject customerItemMsg) {
        return MapUtil.edit(customerItemMsg, entry -> {
            if (keyFieldCovertConfig.containsKey(entry.getKey())) {
                MappingTarget mappingTarget = keyFieldCovertConfig.get(entry.getKey());
                String targetValue = (String) mappingTarget.mappingFunction.apply(entry.getValue());
                Opt.ofBlankAble(targetValue).ifPresentOrElse(item -> customerItemMsg.put(mappingTarget.targetField, targetValue), () -> {
                    log.warn("{}在数据字典中未配置数据转换关系", entry.getValue());
                    alarm("在数据字典中未配置数据转换关系", String.format("%s在数据字典中未配置数据转换关系", entry.getValue()));
                });
            }
            return entry;
        });
    }

    private void alarm(String alarmTitle, String alarmInfo) {
        AlarmHelper.alarm(alarmMetric, alarmKey, AlarmSeverityEnum.CRITICAL, alarmTitle, alarmInfo);
    }

    private void sendNotice(List<CustomerItemsDTO> customerItemsDTOList) {
        String[] noticeTos = noticeToSupplier.get();
        if (BooleanUtil.or(ArrayUtil.isEmpty(noticeTos), CollUtil.isEmpty(customerItemsDTOList))) {
            return;
        }
        noticeCenterService.sendMsg(createNoticeCenterMsgDto(customerItemsDTOList, noticeTos));
    }

    private NoticeCenterMsgDto<IcenterInfo> createNoticeCenterMsgDto(List<CustomerItemsDTO> customerItemsDTOList, String[] noticeTos) {
        NoticeCenterMsgDto<IcenterInfo> noticeCenterMsgDto = new NoticeCenterMsgDto<>();
        noticeCenterMsgDto.setSendTime(Instant.now());
        noticeCenterMsgDto.setTemplateId(iCenterTemplateId);
        IcenterInfo icenterInfo = new IcenterInfo();
        icenterInfo.setTargetIds(ListUtil.toList(noticeTos));
        noticeCenterMsgDto.setInfo(icenterInfo);
        Map<String, Object> data = MapUtil.<String, Object>builder().put("customerItemNo", StrJoiner.of(StrUtil.COMMA).append(customerItemsDTOList, CustomerItemsDTO::getCustomerCode).toString()).build();
        noticeCenterMsgDto.setData(data);
        return noticeCenterMsgDto;
    }

    private void save(List<CustomerItemsDTO> saveList) {
        customerItemsService.batchInsert(saveList);
    }

    private void remove(List<String> removeList) {
        Opt.ofEmptyAble(removeList).ifPresent(customerItemsService::deleteCustomerItemsByZteCodes);
    }

    private boolean shouldRemove(JSONObject customerItemMsg) {
        return N_STATUS.equals(customerItemMsg.getString("isValid"));
    }

    private String[] getNoticeTos(int type) {
        List<SysLookupValues> sysLookupValues = sysLookupValuesService.selectValuesByType(type);
        if (CollUtil.isEmpty(sysLookupValues)) {
            log.warn("在字典中对应的目录代码{}下没有配置通知用户", type);
            alarm("在数据字典中未配置通知用户", String.format("在字典中对应的目录代码%s下没有配置通知用户", type));
            return new String[0];
        }
        return StrUtil.splitToArray(sysLookupValues.get(0).getLookupMeaning(), StrUtil.C_COMMA);
    }

    private Function<Object, Object> createValueMappingFunction(int type, Function<SysLookupValues, String> keyFunction) {
        return o -> {
            Map<String, String> valueMappingByType = MapUtil.emptyIfNull(sysLookupValuesService.getValueMappingByType(type, keyFunction));
            return valueMappingByType.get((String) o);
        };
    }

    @AllArgsConstructor
    private static class MappingTarget{
        Function<Object, Object> mappingFunction;
        String targetField;
    }
}
