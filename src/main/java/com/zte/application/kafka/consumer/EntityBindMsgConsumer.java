package com.zte.application.kafka.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.kafka.component.KafkaProdListener;
import com.zte.application.sncabind.PsTaskService;
import com.zte.application.sncabind.TaskConfirmationService;
import com.zte.common.enums.ConfirmationStatusEnum;
import com.zte.common.utils.Constant;
import com.zte.common.utils.KafkaConstant;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.aps.EntityBindResultDTO;
import com.zte.itp.msa.message.SpringKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_500;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:32
 */
@Component
@Slf4j
public class EntityBindMsgConsumer extends AbstractMsgConsumer{
    @Resource
    private PsTaskService psTaskService;
    @Resource
    private SpringKafkaProducer springKafkaProducer;
    @Resource
    private TaskConfirmationService taskConfirmationService;
    @Resource
    private PsTaskExtendedService psTaskExtendedService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private PushStdModelConfirmationService pushStdModelConfirmationService;
    @Value("${confirmation.excluded.types:FG_DISAS_2}")
    private Collection<String> excludedTypes;
    private final List<ConfirmationStatusEnum> focusConfirmationStatus = Arrays.asList(ConfirmationStatusEnum.CONFIRMING, ConfirmationStatusEnum.CONFIRMED);
    private final Map<String, String> fieldMapping = MapUtil.<String, String>builder().put("stockOrgId", "orgId").put("entityNo", "taskNo")
            .put("billNo", "billNo").put("messageType", "operation").build();
    /* Started by AICoder, pid:y71f1d0f269c1dd14f270a3e407b09409d308193 */
    private final Map<String, BiConsumer<EntityBindResultDTO, PsTask>> operationMap = MapUtil.<String, BiConsumer<EntityBindResultDTO, PsTask>>builder()
        .put(Constant.OPERATION_CONFIRMATION, this::handleConfirmation)
        .put(Constant.OPERATION_CANCEL, this::handleCancellation)
        .build();

    private void handleConfirmation(EntityBindResultDTO entityBindResultDTO, PsTask psTask) {
        if (psTask == null || excludedTypes.contains(psTask.getType()) || preCheck(psTask)) {
            return;
        }

        if (psTask.isExistInboundBarcode()) {
            updateEntityAndPsTask(entityBindResultDTO, psTask, ConfirmationStatusEnum.PENDING_CONFIRMATION);
        } else {
            updateEntityAndPsTask(entityBindResultDTO, psTask, ConfirmationStatusEnum.CONFIRMED);
        }
    }

    private void handleCancellation(EntityBindResultDTO entityBindResultDTO, PsTask psTask) {
        if (psTask == null) {
            updateEntityResult(entityBindResultDTO, Constant.Y_STATUS, Constant.TIP_APS_NO_TASK);
            return;
        }

        if (preCheck(psTask)) {
            updateEntityResult(entityBindResultDTO, Constant.N_STATUS, String.format(Constant.TIP_APS_CANCELLATION, psTask.getTaskNo()));
            return;
        }

        updateEntityAndPsTask(entityBindResultDTO, psTask, ConfirmationStatusEnum.CANCELLED);
    }

    private void updateEntityAndPsTask(EntityBindResultDTO entityBindResultDTO, PsTask psTask, ConfirmationStatusEnum status) {
        psTask.setConfirmationStatus(status.getCode());
        updateEntityResult(entityBindResultDTO, Constant.Y_STATUS, status.getDesc());
    }

    private void updateEntityResult(EntityBindResultDTO entityBindResultDTO, String success, String result) {
        entityBindResultDTO.setSuccess(success);
        entityBindResultDTO.setResult(result);
    }

    /* Ended by AICoder, pid:y71f1d0f269c1dd14f270a3e407b09409d308193 */

    private boolean preCheck(PsTask psTask) {
        ConfirmationStatusEnum statusEnum = EnumUtil.getBy(ConfirmationStatusEnum.class, confirmationStatusEnum -> confirmationStatusEnum.getCode().equals(psTask.getConfirmationStatus()));
        return focusConfirmationStatus.contains(statusEnum);
    }

    @Override
    protected void process(JSONArray jsonArray) {
        List<EntityBindResultDTO> entityBindResultDTOList = convert(jsonArray);
        Map<String, PsTask> psTaskMap = getPsTaskMap(entityBindResultDTOList);
        doProcess(entityBindResultDTOList, psTaskMap);
        transactionTemplate.execute(status -> {
            updateIfChange(entityBindResultDTOList, psTaskMap);
            sendResponse(entityBindResultDTOList);
            return null;
        });
    }

    private void updateIfChange(List<EntityBindResultDTO> entityBindResultDTOList, Map<String, PsTask> psTaskMap) {
        List<PsTask> psTaskList = entityBindResultDTOList.stream().filter(item -> Constant.FLAG_Y.equals(item.getSuccess())).map(item -> psTaskMap.get(item.getTaskNo())).filter(ObjUtil::isNotNull).collect(Collectors.toList());
        updatePsTask(psTaskList);
        updatePsTaskExtended(entityBindResultDTOList, psTaskList);
        saveAndPushConfirmation(psTaskList);
    }

    private void saveAndPushConfirmation(List<PsTask> psTaskList) {
        // 保存和推送转正任务推送排产记录
        psTaskList.stream().filter(item -> ConfirmationStatusEnum.CONFIRMED.getCode().equals(item.getConfirmationStatus())).forEach(pushStdModelConfirmationService::saveAndPushConfirmation);
    }

    private void updatePsTaskExtended(List<EntityBindResultDTO> entityBindResultDTOList, List<PsTask> psTaskList) {
        Map<String, EntityBindResultDTO> entityBindResultDTOMap = IterUtil.toMap(entityBindResultDTOList, EntityBindResultDTO::getTaskNo);
        Collection<PsTaskExtendedDTO> psTaskExtendedDTOS = CollUtil.trans(psTaskList, item -> {
            PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
            psTaskExtendedDTO.setTaskNo(item.getTaskNo());
            psTaskExtendedDTO.setBillNo(entityBindResultDTOMap.get(item.getTaskNo()).getBillNo());
            return psTaskExtendedDTO;
        });
        psTaskExtendedService.batchUpdateByTaskNo(psTaskExtendedDTOS);
    }

    private void updatePsTask(List<PsTask> psTaskList){
        psTaskService.batchUpdateByPK(psTaskList);
    }

    private void doProcess(List<EntityBindResultDTO> entityBindResultDTOList, Map<String, PsTask> psTaskMap) {
        taskConfirmationService.processInboundFlag(psTaskMap.values());
        entityBindResultDTOList.forEach(item -> {
            if (psTaskMap.containsKey(item.getTaskNo())){
                PsTask psTask = psTaskMap.get(item.getTaskNo());
                if (StrUtil.equals(Convert.toStr(item.getOrgId()), Convert.toStr(psTask.getOrgId()))){
                    operationMap.get(item.getOperation()).accept(item, psTask);
                    return;
                }
            }
            operationMap.get(item.getOperation()).accept(item, null);
        });
    }

    private void sendResponse(List<EntityBindResultDTO> taskConfirmationDTOList) {
        Collection<EntityBindResultDTO> entityBindResultDTOS = CollUtil.filterNew(taskConfirmationDTOList, item -> StrUtil.isNotBlank(item.getSuccess()));
        Opt.ofEmptyAble(entityBindResultDTOS).ifPresent(lst -> {
            try {
                CompletableFuture<Void> sendFuture = new CompletableFuture<>();
                springKafkaProducer.sendMessage(KafkaConstant.CENTERFACTORY, KafkaConstant.TASK_CONFIRMATION_RESULT_KEY, JSONArray.toJSONString(lst), getProducerListener(sendFuture));
                sendFuture.join();
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

    private KafkaProdListener getProducerListener(CompletableFuture<Void> sendFuture) {
        return new KafkaProdListener() {
            @Override
            public void onSuccess(ProducerRecord<String, String> producerRecord, RecordMetadata recordMetadata) {
                super.onSuccess(producerRecord, recordMetadata);
                sendFuture.complete(null);
            }

            @Override
            public void onError(ProducerRecord<String, String> producerRecord, RecordMetadata recordMetadata, Exception exception) {
                super.onError(producerRecord, recordMetadata, exception);
                sendFuture.completeExceptionally(exception);
            }
        };
    }

    private Map<String, PsTask> getPsTaskMap(List<EntityBindResultDTO> entityBindResultDTOList) {
        Collection<PsTask> psTaskList = psTaskService.getPsTask(entityBindResultDTOList.stream().map(EntityBindResultDTO::getTaskNo).distinct().collect(Collectors.toList()));
        psTaskList = CollUtil.trans(psTaskList, item -> {
            PsTask psTask = new PsTask();
            psTask.setTaskId(item.getTaskId());
            psTask.setTaskNo(item.getTaskNo());
            psTask.setOrgId(item.getOrgId());
            psTask.setItemNo(item.getItemNo());
            psTask.setFactoryId(item.getFactoryId());
            psTask.setConfirmationStatus(item.getConfirmationStatus());
            psTask.setTaskQty(item.getTaskQty());
            return psTask;
        });
        return IterUtil.toMap(psTaskList, PsTask::getTaskNo);
    }

    private List<EntityBindResultDTO> convert(JSONArray jsonArray){
        return jsonArray.stream().map(item -> convert((JSONObject)item)).collect(Collectors.toList());
    }

    private EntityBindResultDTO convert(JSONObject jsonObject) {
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        BeanUtil.copyProperties(jsonObject, entityBindResultDTO, CopyOptions.create().setFieldMapping(fieldMapping));
        return entityBindResultDTO;
    }
    @Override
    protected int limitSize() {
        return INT_500;
    }
}
