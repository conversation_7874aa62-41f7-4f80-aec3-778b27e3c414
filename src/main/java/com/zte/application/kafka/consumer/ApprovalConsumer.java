package com.zte.application.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.ApprovalProcessInfoService;
import com.zte.common.utils.Constant;
import com.zte.iss.approval.sdk.listener.impl.DefaultKafkaMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;


/*
 * <AUTHOR>
 * 移动审批服务kafuka
 *  dev调试请取消（类ApprovalCenterKafkaMsgHandler和类ApprovalKafkaConsumer的注释）
 * */
@Slf4j
@Component
public class ApprovalConsumer extends DefaultKafkaMsgListener {

    @Value("${approval.sdk.app.appCode}")
    private String appCode;

    @Autowired
    private ApprovalProcessInfoService approvalProcessInfoService;

    public ApprovalConsumer() {
    }

    /**
     * 重写kafuka方法
     * @param record
     */
    @Override
    public void kafkaMsgReceive(ConsumerRecord<String, String> record) {
        if (record == null) {
            return;
        }
        String key = record.key();
        if (StringUtils.isEmpty(key) || !key.startsWith(appCode)) {
            return;
        }
        String val = record.value();
        JSONObject data = JSONObject.parseObject(val);
        try {
            approvalProcessInfoService.dealScatterMachine(key, data, appCode,val);
        } catch (Exception e) {
       }
    }

}

