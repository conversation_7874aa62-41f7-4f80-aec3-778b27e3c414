package com.zte.application.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.ApprovalProcessInfoService;
import com.zte.common.utils.Constant;
import com.zte.itp.msa.message.annotation.DefaultMessageHandler;
import com.zte.itp.msa.message.annotation.MessageConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/*
 * <AUTHOR>
 * 评审中心服务kafuka
 *  dev调试请取消（类ApprovalCenterKafkaMsgHandler和类ApprovalKafkaConsumer的注释）
 * */
@Slf4j
@Component
public class ReviewApprovalConsumer {
    @Value("${review.appId}")
    private String reviewAppId;

    @Autowired
    private ApprovalProcessInfoService approvalProcessInfoService;

    public ReviewApprovalConsumer() {
    }

    /**
     * kafuka方法
     * @param record
     */
    @MessageConsumer(topic = Constant.REVIEW_TOPIC, key = "*",
            handlerType = DefaultMessageHandler.class, partitionSegmentNum = 1)
    public void kafkaMsgReceive(String record) throws Exception{
        JSONObject data = JSONObject.parseObject(record);
        String key = data.getString(Constant.REVIEW_KEY);
        if (StringUtils.isEmpty(key) || !key.startsWith(reviewAppId)) {
            return;
        }
        approvalProcessInfoService.ReviewDealScatterMachine(key, data,record);
    }
}
