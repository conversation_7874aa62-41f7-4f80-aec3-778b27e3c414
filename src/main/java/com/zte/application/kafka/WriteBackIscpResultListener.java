package com.zte.application.kafka;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.action.iscpedi.model.BarCodeInfo;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.action.iscpedi.model.IscpEdiParam;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.itp.msa.message.MessageProducerListener;

/**
 * @Deacription infor回写iscp结果监听
 * <AUTHOR>
 * @Date 2020/9/1 10:22
 **/
@Component
@ConditionalOnProperty(value = "spring.kafka.enabled", havingValue = "true")
public class WriteBackIscpResultListener implements MessageProducerListener<String, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(WriteBackIscpResultListener.class);

    private static final int SEND_SUCCESS = 0;
    private static final int SEND_FAIL = 2;
    private static final String ACCEPTANCE = "08";
    private static final String ACCEPTANCE_TABLE_NAME = "EDI_PO_R";
    private static final String STEP_ACCEPTANCE_TABLE_NAME = "ST_RECEIVECHECK";
    private static final String WAREHOUSING = "01";
    private static final String WAREHOUSING_TABLE_NAME = "EDI_PO_S";
    private static final String STEP_WAREHOUSING_TABLE_NAME = "ST_RECEIVECHECK_S";
    private static final String WAREHOUE_OUT = "03";
    private static final String WAREHOUE_OUT_TABLE_NAME = "EDI_SO_S";
    private static final String EDI_SO_S_TRANSFER_TYPE = "EDI_SO_S_TRANSFER";
    private static final String UNKNOW_TABLE_TABLE_NAME = "UNKNOW_TABLE";
    private static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Autowired
    private StepIscpRepository stepIscpRepository;

    /**
     * KAFKA 客户端发送消息成功的反馈。在 KAFKA 服务端是否成功，需要根据客户端配置（appliction.yml ->
     * spring.kafka.producer.acks）进行判断
     *
     * <pre>
     * spring.kafka.producer.acks = all 如果客户端发送成功，说明消息已经正常存放到服务端
     * spring.kafka.producer.acks = 0   如果客户端发送成功，只能表明消息被服务端接收了
     * spring.kafka.producer.acks = 1   如果客户端发送成功，表明 KAFKA 的 leader 已经正常存储了消息，但是它的副本不一定，
     *                                                     所以如果出现 leader 崩溃之类的故障，消息是没有正常存储的
     * </pre>
     */
    @Override
    public void onSuccess(ProducerRecord producerRecord, RecordMetadata recordMetadata) {
        // 成功，更新为0
        this.syncMessageState(producerRecord, SEND_SUCCESS);
        LOGGER.info("Success occurs while send message,the message info is {}",producerRecord.value());
    }

    /**
     * KAFKA 客户端发送消息失败的反馈，KAFKA 服务端未能接收该消息
     *
     * @param producerRecord 消息记录
     * @param exception      异常
     */
    @Override
    public void onError(ProducerRecord producerRecord, Exception exception) {
        // 异常，更新为2
        this.syncMessageState(producerRecord, SEND_FAIL);
        LOGGER.error("Error occurs while send message, kafka server can not accept it. the message info is {}, and the exception info is ", producerRecord.value(), exception);
    }

    /**
     * 消息云客户端发送消息失败的反馈，消息还未能进入 KAFKA 客户端
     *
     * @param producerRecord 消息记录
     * @param exception      异常
     */
    @Override
    public void onFail(ProducerRecord producerRecord, Exception exception) {
        // 失败，更新为2
        this.syncMessageState(producerRecord, SEND_FAIL);
        LOGGER.error("Failed to send message, i have not enter into kafka client. the message info is {}, and the exception info is ", producerRecord.value(), exception);
    }

    /**
     * KAFKA 客户端发送消息异常的反馈，由于这里多半是线程中断引发，所以 KAFKA 服务端是否接收该消息无法确定
     * <p>
     * 重要：该过程基本不会发生，但是，一旦发生，意味着消息是否进入 KAKFA 服务端无法确定，所以业务最好能够对该内容作特殊判断
     *
     * @param producerRecord 消息记录
     * @param exception      异常
     */
    @Override
    public void onCancel(ProducerRecord producerRecord, Exception exception) {
        // 仅打印日志，不更改状态
        LOGGER.error("Message has been canceled, i am not sure the message is send or not. the message info is {}, and the exception info is ", producerRecord.value(), exception);
    }

    /**
     * 消息状态变更
     * @param producerRecord 原始消息
     * @param flag 处理标识
     */
    public void syncMessageState(ProducerRecord producerRecord, int flag) {
        String parameter = (String)producerRecord.value();
        List<JSONObject> jsonObjectList = JSON.parseObject(parameter, List.class);
        // 转为需要的对象,实际上只有一个对象
        IscpEdiParam iscpEdiParam = jsonObjectList.get(0).toJavaObject(IscpEdiParam.class);
        // 明细集合
        List<BarCodeInfo> barCodeInfoList = iscpEdiParam.getBarCodeList();
        // 转换为数据库需要的List
        List<IscpEdiLog> iscpEdiLogList = barCodeInfoList.stream().map(e -> {
            IscpEdiLog iscpEdiLog = new IscpEdiLog();
            iscpEdiLog.setSerialkey(e.getSerialkey());
            String sourceTable;
            String stepSourceTable;
            String transferSourceTable;
            switch (iscpEdiParam.getOperateType()) {
                case ACCEPTANCE:
                    sourceTable = ACCEPTANCE_TABLE_NAME;
                    stepSourceTable = STEP_ACCEPTANCE_TABLE_NAME;
                    transferSourceTable = UNKNOW_TABLE_TABLE_NAME;
                    break;
                case WAREHOUSING:
                    sourceTable = WAREHOUSING_TABLE_NAME;
                    stepSourceTable = STEP_WAREHOUSING_TABLE_NAME;
                    transferSourceTable = UNKNOW_TABLE_TABLE_NAME;
                    break;
                case WAREHOUE_OUT:
                    sourceTable = WAREHOUE_OUT_TABLE_NAME;
                    stepSourceTable = UNKNOW_TABLE_TABLE_NAME;
                    transferSourceTable = EDI_SO_S_TRANSFER_TYPE;
                    break;
                default:
                    // 默认赋值个不知道的表就行，不会更新任何记录
                    sourceTable = UNKNOW_TABLE_TABLE_NAME;
                    stepSourceTable = UNKNOW_TABLE_TABLE_NAME;
                    transferSourceTable = UNKNOW_TABLE_TABLE_NAME;
            }
            iscpEdiLog.setSourceTable(sourceTable);
            iscpEdiLog.setTransferSourceTable(transferSourceTable);
            iscpEdiLog.setStepSourceTable(stepSourceTable);
            iscpEdiLog.setIsSend(flag);
            iscpEdiLog.setUpdateDate(new SimpleDateFormat(TIME_FORMAT).format(new Date()));
            return iscpEdiLog;
        }).collect(Collectors.toList());
        inforIwmsIscpRepository.updateIscpEdiLogResult(iscpEdiLogList);
        stepIscpRepository.updateIscpEdiLogResult(iscpEdiLogList);
    }  
}