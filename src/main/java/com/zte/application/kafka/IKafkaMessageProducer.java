package com.zte.application.kafka;

import org.springframework.kafka.support.ProducerListener;

/**
 * @Deacription kafka消息生产者
 * <AUTHOR>
 * @Date 2020/9/1 11:09
 **/
public interface IKafkaMessageProducer {
    /**
     * 可回调的kafka消息发送
     *
     * @param topic            主题
     * @param key              关键字
     * @param message          消息
     * @param producerListener 监听实例
     * @return 发送结果
     */
    boolean sendMessage(String topic, String key, String message, ProducerListener<String, String> producerListener) throws InterruptedException;
}
