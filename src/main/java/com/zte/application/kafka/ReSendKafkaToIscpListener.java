package com.zte.application.kafka;

import com.zte.itp.msa.message.MessageProducerListener;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "spring.kafka.enabled", havingValue = "true")
public class ReSendKafkaToIscpListener extends WriteBackIscpResultListener implements MessageProducerListener<String, String> {
    private static final int RE_SEND_SUCCESS = 30;
    private static final int RE_SEND_FAIL = 32;
    @Override
    public void onSuccess(ProducerRecord producerRecord, RecordMetadata recordMetadata) {this.syncMessageState(producerRecord, RE_SEND_SUCCESS);}

    /**
     * KAFKA 客户端发送消息失败的反馈，KAFKA 服务端未能接收该消息
     *
     * @param producerRecord 消息记录
     * @param exception      异常
     */
    @Override
    public void onError(ProducerRecord producerRecord, Exception exception) {this.syncMessageState(producerRecord, RE_SEND_FAIL);}

    /**
     * 消息云客户端发送消息失败的反馈，消息还未能进入 KAFKA 客户端
     *
     * @param producerRecord 消息记录
     * @param exception      异常
     */
    @Override
    public void onFail(ProducerRecord producerRecord, Exception exception) {this.syncMessageState(producerRecord, RE_SEND_FAIL);}

}
