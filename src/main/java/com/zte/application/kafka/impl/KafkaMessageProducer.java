package com.zte.application.kafka.impl;

import com.zte.application.kafka.IKafkaMessageProducer;
import com.zte.itp.msa.message.SpringKafkaProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.ProducerListener;
import org.springframework.stereotype.Component;

/**
 * @Deacription 消息生产者工具类
 * <AUTHOR>
 * @Date 2020/9/1 11:03
 **/
@Component
public class KafkaMessageProducer implements IKafkaMessageProducer {

    @Autowired
    private SpringKafkaProducer producer;

    @Override
    public boolean sendMessage(String topic, String key, String message, ProducerListener<String, String> producerListener) throws InterruptedException {
        return producer.sendMessage(topic, key, message, producerListener);
    }
}