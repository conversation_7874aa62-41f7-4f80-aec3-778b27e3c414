package com.zte.application.kafka;

import com.zte.application.kafka.consumer.*;
import com.zte.common.ExceptionUtils;
import com.zte.common.utils.KafkaConstant;
import com.zte.itp.msa.message.annotation.MessageConsumer;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * kafka消费服务实例
 *
 * <AUTHOR>
 */
@Service
public class KafkaConsumerService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ServiceLogConsumer serviceLogConsumer;
    @Autowired
    private ApsProdPlanIdConsumer apsProdPlanIdConsumer;
    @Autowired
    private AliCartonChangeConsumer aliCartonChangeConsumer;
    @Autowired
    private AlarmLogConsumer alarmLogConsumer;

    @Autowired
    private SnapshotLogConsumer snapshotLogConsumer;
    @Autowired
    private CustomerItemMsgConsumer customerItemMsgConsumer;
    @Autowired
    private EntityBindMsgConsumer entityBindMsgConsumer;
    @Autowired
    private EntityModifyMsgConsumer entityModifyMsgConsumer;
    @Autowired
    private KafkaLocalMessageService kafkaLocalMessageService;

    @RecordLogAnnotation("告警日志消费")
    @MessageConsumer(topic = KafkaConstant.LOG, key = KafkaConstant.ALARM_LOG, containerFactory = "batchFactory", maxHandleRecords = 100)
    public boolean consumeAlarmLog(List<String> records) throws Exception {
        logger.info("kafka接收到alarmLog消息");
        alarmLogConsumer.batchConsume(records);
        return true;
    }

    /**
     * 必须要配置项
     * 1、topic ：消息topic名
     * 2、key：消息key, * 通配topic的所有key
     * 可选配置项
     * 1、errorCallBack： 消费消息错误回调方法
     * 2、handlerType：统一执行的消息的处理类，如需要特殊统一处理、统一错误回调处理，
     * 需要按KafkaMessageHandler例子重写。默认为DefaultMessageHandler其统一回调不错任何处理
     * 3、partition： 默认为0 消息分区
     * 4、handlerTimeOut：单个处理方法执行时间 默认30秒,用于并发消费超时测算
     *
     */

    /**
     * 接口日志消费-kafka
     */
    @MessageConsumer(topic = KafkaConstant.LOG, key = KafkaConstant.SERVICE_LOG, containerFactory = "batchFactory", maxHandleRecords = 100)
    public boolean serviceLogConsumer(List<String> records) throws Exception {
        logger.info("kafka接收到serviceLog消息");
        serviceLogConsumer.consume(records);
        return true;
    }

    /**
     * APS 批次同步kafka 消息消费
     *
     * @return 消费ACK
     * @throws Exception 业务异常
     */
    @MessageConsumer(topic = KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, key =
            KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC_KEY,
            errorCallBack = "receiveCallBackNotCommite")
    public boolean apsProdPlanIdConsumer(String msg) throws Exception {
        logger.info("kafka接收APS到新增消息 :{}", msg);
        apsProdPlanIdConsumer.consume(msg);
        return true;
    }

    public void receiveCallBackNotCommite(String record) {
        throw new RuntimeException("kafka consumer fail");
    }

    /**
     * APS 批次修改同步kafka 消息消费
     *
     * @return 消费ACK
     * @throws Exception 业务异常
     */
    @RecordLogAnnotation("APS批次修改同步")
    @MessageConsumer(topic = KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, key =
            KafkaConstant.ApsKafkaConstant.APS2_IMES_MODIFY_PROD_PLAN,
            errorCallBack = "receiveCallBackNotCommite")
    public boolean apsProdPlanIdModifyConsumer(String msg) throws Exception {
        logger.info("kafka接收APS到修改消息 :{}", msg);
        apsProdPlanIdConsumer.consumeModify(msg);
        return true;
    }

    /**
     * APS衍生码kafka 消息消费
     *
     * @return 消费ACK
     * @throws Exception 业务异常
     */
    @MessageConsumer(topic = KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, key =
            KafkaConstant.ApsKafkaConstant.APS2_IMES_PROD_PLAN_MODIFY,
            errorCallBack = "receiveCallBackNotCommite")
    public boolean apsDerivedCodeConsumer(String msg) throws Exception {
        logger.info("kafka接收APS到修改消息 :{}", msg);
        apsProdPlanIdConsumer.apsDerivedCodeConsumer(msg);
        return true;
    }


    /**
     * 接口快照持久化消费者
     */
    @MessageConsumer(topic = KafkaConstant.LOG, key = KafkaConstant.SNAPSHOT_LOG)
    public boolean requestSnapshotLogConsumer(String msg) throws Exception {
        logger.info("kafka消费snapshotLog消息");
        snapshotLogConsumer.consume(msg);
        return true;
    }

    @MessageConsumer(topic = KafkaConstant.CUSTOMER_ITEM_MSG_TOPIC, key = KafkaConstant.CUSTOMER_ITEM_MSG_KEY)
    @Retryable(exclude = MesBusinessException.class, recover = "customerItemMsgRecover")
    public void customerItemMsgConsumer(String msg){
        logger.info("kafka接收到采购系统发送的客户代码物料消息{}", msg);
        customerItemMsgConsumer.consume(msg);
    }

    @Recover
    public void customerItemMsgRecover(Throwable t, String msg){
        kafkaLocalMessageService.insertMessage(KafkaConstant.CUSTOMER_ITEM_MSG_TOPIC, KafkaConstant.CUSTOMER_ITEM_MSG_KEY, msg, ExceptionUtils.getMessage(t));
    }

    @MessageConsumer(topic = KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, key = KafkaConstant.ApsKafkaConstant.ENTITY_BIND_KEY)
    @Retryable(exclude = MesBusinessException.class, recover = "entityBindMsgRecover")
    public void entityBindMsgConsumer(String msg){
        logger.info("kafka接收到APS转正或取消转正消息{}", msg);
        entityBindMsgConsumer.consume(msg);
    }

    @Recover
    public void entityBindMsgRecover(Throwable t, String msg){
        kafkaLocalMessageService.insertMessage(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, KafkaConstant.ApsKafkaConstant.ENTITY_BIND_KEY, msg, ExceptionUtils.getMessage(t));
    }

    @MessageConsumer(topic = KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, key = KafkaConstant.ApsKafkaConstant.ENTITY_MODIFY_KEY)
    @Retryable(exclude = MesBusinessException.class, recover = "entityModifyMsgRecover")
    public void entityModifyMsgConsumer(String msg){
        logger.info("kafka接收到APS数量变更消息{}", msg);
        entityModifyMsgConsumer.consume(msg);
    }

    @Recover
    public void entityModifyMsgRecover(Throwable t, String msg){
        kafkaLocalMessageService.insertMessage(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, KafkaConstant.ApsKafkaConstant.ENTITY_MODIFY_KEY, msg, ExceptionUtils.getMessage(t));
    }

    /**
     * 阿里箱包变化监听 消息消费
     *
     * @return 消费ACK
     * @throws Exception 业务异常
     */
    @RecordLogAnnotation("阿里箱包变化")
    @MessageConsumer(topic = KafkaConstant.CARTON_NO_CHANGE_TOPIC, key = KafkaConstant.CARTON_NO_CHANGE_KEY)
    @Retryable(exclude = MesBusinessException.class, recover = "cartonNoMsgRecover")
    public boolean aliCartonNoChangeConsumer(String msg) {
        logger.info("kafka接收条码中心的消息 :{}", msg);
        aliCartonChangeConsumer.consumeChangeCartonNo(msg);
        return true;
    }

    @Recover
    public void cartonNoMsgRecover(Throwable t, String msg){
        kafkaLocalMessageService.insertMessage(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC, KafkaConstant.ApsKafkaConstant.ENTITY_BIND_KEY, msg, ExceptionUtils.getMessage(t));
    }
}
