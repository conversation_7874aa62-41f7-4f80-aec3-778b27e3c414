package com.zte.application.sequence.impl;

import com.zte.application.sequence.ISequenceService;
import com.zte.domain.model.material.Sequence;
import com.zte.domain.model.material.SequenceRepository;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static com.zte.common.utils.NumConstant.*;
import static com.zte.resourcewarehouse.common.constant.ResponseConstant.DateFormate.YYYY_MM_DD;

/**
 * 获取序列号
 * <AUTHOR>
 */
@Service
public class SequenceServiceImpl implements ISequenceService {
    @Autowired
    private SequenceRepository sequenceRepository;

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public String getSequenceWithDate(String id) {
        String returnSeqStr = NULLSTR;
        Sequence sequence = sequenceRepository.selectByPrimaryKey(id);
        if (Tools.isNotEmpty(sequence)) {
            LocalDate now = LocalDate.now();
            LocalDate sequenceDate = LocalDate.of(sequence.getYear(), sequence.getMonth(), sequence.getDay());
            String prefixStr = sequence.getSequencePrefix();
            String suffixStr = sequence.getSequenceSuffix();
            int currNoStr = sequence.getSequenceCurrNo();
            if(now.isAfter(sequenceDate)){
                currNoStr = INT_0;
            }
            int stepStr = sequence.getSequenceStep();
            int seqNoLengthStr = sequence.getSequenceLength();
            String nextNoDecimal = (new BigDecimal(currNoStr).add(new BigDecimal(stepStr))).toPlainString();
            currNoStr = Integer.parseInt(nextNoDecimal);
            // 前缀
            if (Tools.isNotEmpty(prefixStr)) {
                returnSeqStr = returnSeqStr + prefixStr;
            }

            String pace = ZERO_D + seqNoLengthStr + D;
            String date = now.format(DateTimeFormatter.ofPattern(YYYYMMDD));
            returnSeqStr += date;
            // 操作值
            returnSeqStr += String.format(pace, Integer.parseInt(nextNoDecimal));

            // 后缀
            if (Tools.isNotEmpty(suffixStr)) {
                returnSeqStr = returnSeqStr + suffixStr;
            }
            Sequence temp = Sequence.builder().build()
                            .setSequenceId(sequence.getSequenceId())
                            .setSequenceCurrNo(Integer.parseInt(nextNoDecimal))
                            .setVersion(sequence.getVersion())
                            .setYear(Integer.parseInt(date.substring(INT_0,INT_4)))
                            .setMonth(now.getMonthValue())
                            .setDay(now.getDayOfMonth())
                            .setSequenceCode(returnSeqStr)
                            .setSequenceCurrNo(currNoStr);
            int operInt = sequenceRepository.updateByPrimaryKeyAndVersion(temp);
            if (operInt == INT_1) {
                return returnSeqStr;
            }
            return null;
        }
        return null;
    }

    @Override
    public String getSequenceWithOutDate(String id) {
        String returnSeqStr = NULLSTR;
        Sequence sequence = sequenceRepository.selectByPrimaryKey(id);
        if (Tools.isNotEmpty(sequence)) {
            LocalDate now = LocalDate.now();
            String prefixStr = sequence.getSequencePrefix();
            String suffixStr = sequence.getSequenceSuffix();
            int currNoStr = sequence.getSequenceCurrNo();
            int stepStr = sequence.getSequenceStep();
            int seqNoLengthStr = sequence.getSequenceLength();
            String nextNoDecimal = (new BigDecimal(currNoStr).add(new BigDecimal(stepStr))).toPlainString();
            currNoStr = Integer.parseInt(nextNoDecimal);
            if(currNoStr>=Integer.MAX_VALUE-INT_1){
                currNoStr=INT_0;
            }
            // 前缀
            if (Tools.isNotEmpty(prefixStr)) {
                returnSeqStr = returnSeqStr + prefixStr;
            }

            String pace = ZERO_D + seqNoLengthStr + D;
            // 操作值
            returnSeqStr += String.format(pace, Integer.parseInt(nextNoDecimal));

            // 后缀
            if (Tools.isNotEmpty(suffixStr)) {
                returnSeqStr = returnSeqStr + suffixStr;
            }
            Sequence temp = Sequence.builder().build()
                    .setSequenceId(sequence.getSequenceId())
                    .setSequenceCurrNo(Integer.parseInt(nextNoDecimal))
                    .setVersion(sequence.getVersion())
                    .setYear(now.getYear())
                    .setMonth(now.getMonthValue())
                    .setDay(now.getDayOfMonth())
                    .setSequenceCode(returnSeqStr)
                    .setSequenceCurrNo(currNoStr);
            int operInt = sequenceRepository.updateByPrimaryKeyAndVersion(temp);
            if (operInt == INT_1) {
                return returnSeqStr;
            }
            return null;
        }
        return null;
    }

}
