package com.zte.application;

import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.SplitItemNoDTO;
import com.zte.interfaces.dto.SrmPageRows;
import com.zte.interfaces.dto.bytedance.CustomerImportDTO;
import com.zte.springbootframe.common.model.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
public interface CustomerItemsService {

    int newCustomerItems(CustomerItemsDTO dto);

    int updateCustomerItems(CustomerItemsDTO dto);

    int deleteCustomerItems(String id, String empNo);

    Page<CustomerItemsDTO> pageCustomerItemsInfo(CustomerItemsDTO dto);

    void exportCustomerItems(CustomerItemsDTO dto, HttpServletResponse response) throws Exception;

    List<CustomerItemsDTO> getCustomerItemsInfo(CustomerItemsDTO dto);

    SrmPageRows<SplitItemNoDTO> queryItemBrandName(String itemNo) throws Exception;

    List<CustomerItemsDTO> getCustomerItemsInfoSelfDevelopedByItemNoList(CustomerItemsDTO customerItemsDTO);

    List<CustomerItemsDTO> queryListByCustomerList(CustomerItemsDTO customerItemsDTO);

    void exportTaskExcel(HttpServletResponse response, String tableName);

    List<CustomerImportDTO> uploadCustomerImportBatch(InputStream inputStream, String tableName);


    List<CustomerImportDTO> checkTaskBatch(List<CustomerImportDTO> requestBody);

    void batchInsert(List<CustomerImportDTO> customerImportDTOList, List<CustomerImportDTO> confirmResult, String empNo) throws Exception;;

    List<CustomerItemsDTO> queryListByCustomerAndItemNoList(CustomerItemsDTO customerItemsDTO);

    int batchInsert(List<CustomerItemsDTO> customerItemsDTOList);

    Integer deleteCustomerItemsByZteCodes(List<String> zteCodes);

    List<CustomerItemsDTO> queryListByZteCodes(List<String> zteCodes);

    List<CustomerItemsDTO> getZteCodeByCustomerName(List<String> customerNameList, int rows, int page);

    /**
     * 根据zteCode获取数据同一物料的zteCode
     * @param zteCodeList
     * @param customerNumberList
     * @return
     */
    List<CustomerItemsDTO> getSameItemOfZteCode(List<String> zteCodeList, List<String> customerNumberList);

    int batchUpdateByZteCode(List<CustomerItemsDTO> customerItemsDTOList);

    List<CustomerItemsDTO> getZteCodeOfSameItem(List<String> itemSupplierNoList);
}
