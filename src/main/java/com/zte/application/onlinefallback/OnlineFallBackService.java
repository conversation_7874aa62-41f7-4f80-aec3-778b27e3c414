package com.zte.application.onlinefallback;

import com.zte.interfaces.onlinefallback.dto.ApplyBillStatusDTO;
import com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO;
import com.zte.interfaces.onlinefallback.dto.SupplyResultDTO;
import com.zte.itp.msa.core.model.ServiceData;

/**
 * [描述] <br>
 * 
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年5月21日 <br>
 * @see com.zte.application.onlinefallback <br>
 */
public interface OnlineFallBackService {
    ServiceData<?> getNonconformingBill(NonconformingBillInputDTO input);

    ServiceData<?> scheduleUpdateFallbackSrcSoStatus(String fallbackNo);

    ServiceData<?> scheduleDealFallbackNo(String fallbackNo);

    ServiceData<?> produceApplyBillToDB(String fallbackNo);

    ServiceData<?> submitApplyBillToISRM(String applybillNo);

    ServiceData<?> acceptApplyBillSupplyResult(SupplyResultDTO supplyResultDTO);

    ServiceData<?> acceptApplyBillStatus(ApplyBillStatusDTO billStatusDTO);

    ServiceData<?> feedbackApplyBillSoResult(String applybillNo);

    ServiceData<?> scheduleDealWaitFeedApplyNo(String applybillNo);

    ServiceData<?> submitApllyToInforSo(String applybillNo);

    ServiceData<?> failReelIdBarcodeAndFeedbackResult(String applybillNo, String xEmpNo, String xAuthValue);

	ServiceData<?> autoSubmitApplyBillToISRM();

}
