package com.zte.application.onlinefallback;

public final class ConstantApplyBillStatus {
    /**
     * 新建<br>
     */
    public final static String NEW = "NEW";

    /**
     * 已提交ISRM<br>
     */
    public final static String SUBMITED = "SUBMITED";   

    /**
     * SUPPLIEER_DEALED 供方已处理<br>
     */
    public final static String SUPPLIEER_DEALED = "SUPPLIEERDEALED";

    /**
     * 关闭<br>
     */
    public final static String CLOSED = "CLOSED";

    /**
     * 提交出库<br>
     */
    public final static String SUBMIT_SO = "SUBMIT_SO";

    /**
     * PurWaitTmHandle 待技术经理处理<br>
     */
    public final static String PUR_WAIT_TM_HANDLE = "PurWaitTmHandle";

    /**
     * PurWaitTmrHandle 待技术经理处理（退回）<br>
     */
    public final static String PUR_WAIT_TMR_HANDLE = "PurWaitTmrHandle";

    /**
     * PurWaitSupConfirm 待供方处理<br>
     */
    public final static String PUR_WAIT_SUP_CONFIRM = "PurWaitSupConfirm";

    /**
     * PurWaitBuyerHandle 待采购员处理<br>
     */
    public final static String PUR_WAIT_BUYER_HANDLE = "PurWaitBuyerHandle";

    /**
     * PurWaitReturn 待退货<br>
     */
    public final static String PUR_WAIT_RETURN = "PurWaitReturn";

    /**
     * PurWaitSupFeedback 待供方收货反馈<br>
     */
    public final static String PUR_WAIT_SUP_FEEDBACK = "PurWaitSupFeedback";

    /**
     * PurClose 关闭<br>
     */
    public final static String PUR_CLOSE = "PurClose";

    /**
     * PurCanceled 已作废<br>
     */
    public final static String PUR_CANCELED = "PurCanceled";

    public final static String GET_RETURN_INFO_FROM_IWMS = "getReturnInfoFromIwms";

    public final static String GET_RETURN_INFO_FROM_IWMS_IMPL = "getReturnInfoFromIwmsImpl";

    public final static String GET_OUT_GOODS_FROM_IWMS = "getOutGoodsFromIwms";

    public final static String GET_RETURN_COUNTS_FROM_ISCP = "getReturnCountsFromIscp";


}
