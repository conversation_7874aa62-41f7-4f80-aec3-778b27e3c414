package com.zte.application.onlinefallback.impl;

import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;

import com.zte.application.material.impl.IwmsEcssServiceImpl;
import com.zte.resourcewarehouse.common.springbootframe.annotation.LogAnnotation;
import com.zte.resourcewarehouse.common.utils.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.onlinefallback.ConstantApplyBillStatus;
import com.zte.application.onlinefallback.OnlineFallBackService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.IterfaceConstructParamUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.IscpEsbLog;
import com.zte.domain.model.infor.IscpEsbLogRepository;
import com.zte.domain.model.infor.OnlineFallbackInforRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.material.StFallbackApplybillDetail;
import com.zte.domain.model.material.StFallbackApplybillHead;
import com.zte.domain.model.step.OnlineFallBackRepository;
import com.zte.interfaces.onlinefallback.dto.ApplyBillDTO;
import com.zte.interfaces.onlinefallback.dto.ApplyBillItemDTO;
import com.zte.interfaces.onlinefallback.dto.ApplyBillSoResultDTO;
import com.zte.interfaces.onlinefallback.dto.ApplyBillSoResultItemDTO;
import com.zte.interfaces.onlinefallback.dto.ApplyBillSoResultItemReelSnDTO;
import com.zte.interfaces.onlinefallback.dto.ApplyBillStatusDTO;
import com.zte.interfaces.onlinefallback.dto.FallbackNoInfo;
import com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO;
import com.zte.interfaces.onlinefallback.dto.NonconformingBillOutputDTO;
import com.zte.interfaces.onlinefallback.dto.NonconformingInIsrmQtyDTO;
import com.zte.interfaces.onlinefallback.dto.NonconformingOutIsrmQtyDTO;
import com.zte.interfaces.onlinefallback.dto.ReceiptDetailDTO;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.interfaces.onlinefallback.dto.SupplyResultDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.ConstantInfor;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.XstreamUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.MsgBody;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.MsgHeader;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.MsgSend;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoBizCont;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;

@Service
public class OnlineFallBackServiceImpl implements OnlineFallBackService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    OnlineFallBackRepository onlineFallBackRepository;

    @Autowired
    OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Autowired
    OnlineFallbackInforRepository onlineFallbackInforRepository;

    @Autowired
    IscpEsbLogRepository iscpEsbLogRepository;

    @Autowired
    IwmsEcssServiceImpl iwmsEcssService;

    @Override
    public ServiceData<?> getNonconformingBill(NonconformingBillInputDTO input) {
        	List<NonconformingBillOutputDTO> list1 = onlineFallBackRepository.getNonconformingBill(input);
        	BusiAssertException.isEmpty(list1,"Nonconforming.info.004");
        	NonconformingBillOutputDTO nonconformingBillOutputDTO = list1.get(0);
        	boolean isTrue = NumConstant.STR_01.equals(nonconformingBillOutputDTO.getType()) &&
                    !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_10) && !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_11)
                    && !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_12);

        	boolean flag = NumConstant.STR_02.equals(nonconformingBillOutputDTO.getType())&&
                    !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_01) && !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_02)
                    && !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_03) && !nonconformingBillOutputDTO.getStatus().equals(NumConstant.STR_04);
            BusiAssertException.isTrue(isTrue||flag,"Nonconforming.info.005");
            
            if(NumConstant.STR_01.equals(nonconformingBillOutputDTO.getType()) || NumConstant.STR_02.equals(nonconformingBillOutputDTO.getType())){
            	List<NonconformingBillOutputDTO> list2 = onlineFallBackRepository.queryProcessedBySkuManager(input);
            	BusiAssertException.isEmpty(list2,"Nonconforming.info.006");
            }
            
            List<NonconformingBillOutputDTO> list = onlineFallBackRepository.queryNonconformingBill(input);
            BusiAssertException.isEmpty(list,"Nonconforming.info.001");

            if(list.size() == NumConstant.INT_1){
            	String value = list.get(0).getItemBarcode();
                BusiAssertException.isTrue(value.equals(Constant.FLAG_NO),"Nonconforming.info.003");
            }

            return ServiceDataUtil.getSuccess(list);
    }

    @Override
    public ServiceData<?> scheduleUpdateFallbackSrcSoStatus(String fallbackNo) {
            FallbackNoInfo fallbackNoInfo = new FallbackNoInfo();
            if (StringUtils.isNotBlank(fallbackNo)) {
                fallbackNoInfo.setFallbackNo(fallbackNo);
            }
            fallbackNoInfo.setIndex(null);

            List<FallbackNoInfo> list = onlineFallBackApplyBillRepository.queryWaitFallback(fallbackNoInfo);
            Map<String, List<String>> mapWhseidNo = new HashMap<String, List<String>>(64);
            list.forEach(ret -> {
                if (mapWhseidNo.containsKey(ret.getSourcelocation())) {
                    mapWhseidNo.get(ret.getSourcelocation()).add(ret.getFallbackNo());
                }
                else {
                    List<String> tmp = new ArrayList<String>();
                    tmp.add(ret.getFallbackNo());
                    mapWhseidNo.put(ret.getSourcelocation(), tmp);
                }
            });
            // 去INFOR 判断是否已经出库完成
            mapWhseidNo.forEach((k, v) -> {
                List<List<String>> splits = CommonUtils.splitList(v, NumConstant.INT_500);
                for (List<String> spList : splits) {
                    List<FallbackNoInfo> listSo = onlineFallbackInforRepository.queryInforSoStatus(k, spList);
                    if (null != listSo && !listSo.isEmpty()) {
                        onlineFallBackApplyBillRepository.updateBatchFallbackStatus(listSo);
                    }
                }
            });
            return ServiceDataUtil.getSuccess();
    }

    @Override
    public ServiceData<?> scheduleDealFallbackNo(String fallbackNo) {
            FallbackNoInfo fallbackNoInfo = new FallbackNoInfo();
            if (StringUtils.isNotBlank(fallbackNo)) {
                fallbackNoInfo.setFallbackNo(fallbackNo);
            }
            fallbackNoInfo.setIndex("1");
            // 获取待生成的申请单的退库单
            List<FallbackNoInfo> list = onlineFallBackApplyBillRepository.queryWaitFallback(fallbackNoInfo);
            if(Tools.isNotEmpty(list)){
                for (List<FallbackNoInfo> tempList : CommonUtils.splitList(list, NumConstant.INT_500)) {
                    // 获取全部接收的退库
                    List<FallbackNoInfo> listPoFallbackNo = onlineFallbackInforRepository.queryDealWarehouseReceived(tempList);
                    listPoFallbackNo.forEach(info -> {
                        onlineFallBackApplyBillRepository.updateFallbackStatus(info);
                        ServiceData<?> applyRes = produceApplyBillToDB(info.getFallbackNo());
                        if (RetCode.SUCCESS_CODE.equals(applyRes.getCode().getCode())) {
                            submitApplyBillToISRM(ObjectTransform.objectToStr(applyRes.getBo()));
                        }
                    });
                }
                return ServiceDataUtil.getSuccess();
            }
            return ServiceDataUtil.getServerError("no data");
    }

    /**
     * [方法描述] 从退库单获取信息生成申请单至DB<br>
     *
     * <AUTHOR>
     * @param fallbackNo
     * @return <br>
     */
    @Override
    public ServiceData<?> produceApplyBillToDB(String fallbackNo) {
        if (StringUtils.isBlank(fallbackNo)) {
            return ServiceDataUtil.getBusinessError("fallback_no is empty!");
        }
        try {
            // 获取退库头信息
            StFallbackApplybillHead stFallbackApplybillHead = getStFallbackApplybillHead(fallbackNo);
            if (null == stFallbackApplybillHead) {
                return ServiceDataUtil.getBusinessError("can not get fallbackNo [" + fallbackNo + "] fall back head info !");
            }
            stFallbackApplybillHead.init();

            // 获取退库明细信息
            List<StFallbackApplybillDetail> listDetail = onlineFallBackApplyBillRepository.queryFallbackDetail(fallbackNo);

            //通过外部单号
            if (null == listDetail || listDetail.isEmpty()) {
                return ServiceDataUtil.getBusinessError("can not get fallbackNo [" + fallbackNo + "] fall back detail info !");
            }
            // 插入申请单信息
            listDetail=getLpnFromInfor(listDetail,fallbackNo,stFallbackApplybillHead);
            if (null == listDetail || listDetail.isEmpty()) {
                return ServiceDataUtil.getBusinessError("can not get fallbackNo [" + fallbackNo + "] fall back detail info !");
            }

            //读取箱号和数量
            int rowno = 1;
            for (StFallbackApplybillDetail ret : listDetail) {
            	String itemBarcode=ret.getItemBarcode();
            	String lpn=ret.getToId();
            	BigDecimal qty=ret.getQty();
                ret.init();
                ret.setItemBarcode(itemBarcode);
                ret.setToId(lpn);
                ret.setQty(qty);
                ret.setApplybillno(stFallbackApplybillHead.getApplybillNo());
                ret.setRowno(rowno++);
            }

            // 在生成退库申请单时，获取34仓最新的lottable03组织和lottable06货主分类
            stFallbackApplybillHead.setOrgId(listDetail.get(NumConstant.INT_0).getLottable03());
            stFallbackApplybillHead.setLottable06(listDetail.get(NumConstant.INT_0).getLottable06());

            // 插入申请单信息
            onlineFallBackApplyBillRepository.insertStFallbackApplybillHead(stFallbackApplybillHead);
            onlineFallBackApplyBillRepository.insertStFallbackApplybillDetail(listDetail);
            // 更新st_fallback_product的lottable06货主分类
            onlineFallBackApplyBillRepository.updateStFallbackProductByNonconformingProductNo(stFallbackApplybillHead);

            String applybillNo = stFallbackApplybillHead.getApplybillNo();
            return ServiceDataUtil.getSuccess(applybillNo);
        }
        catch (Exception e) {
            logger.error("submitFallBackApplyBill Exception : ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }

    }
    /**
     * 通过送货单号获取infor收货数据
     * @throws CloneNotSupportedException
     * */
    public List<StFallbackApplybillDetail> getLpnFromInfor(List<StFallbackApplybillDetail> listDetail,String fallbackNo,StFallbackApplybillHead stFallbackApplybillHead) throws CloneNotSupportedException {
    	List<StFallbackApplybillDetail> reStFallbackApplybillDetails=new ArrayList<>();
    	int i=1;
	    //拆分多个，直接覆盖  //只有一个条码
		ReceiptDetailDTO record=new ReceiptDetailDTO();
		record.setExternreceiptkey(fallbackNo);
		record.setLottable02(listDetail.get(0).getItemBarcode());
        record.setTargetLocation(stFallbackApplybillHead.getTargetLocation());
		List<ReceiptDetailDTO> receiptDetailDTOs =onlineFallbackInforRepository.queryDetailInforReceived(record);
		for (ReceiptDetailDTO receiptDetailDTO : receiptDetailDTOs) {
			StFallbackApplybillDetail copyDto=(StFallbackApplybillDetail)listDetail.get(0).clone();
			copyDto.setToId(receiptDetailDTO.getToid());
			copyDto.setRowno(i++);
			copyDto.setQty(receiptDetailDTO.getQtyreceived());
            copyDto.setLottable03(receiptDetailDTO.getLottable03()); //获取34仓最新的组织（送货单的组织）
            copyDto.setLottable06(receiptDetailDTO.getLottable06()); //获取34仓最新的货主分类
			reStFallbackApplybillDetails.add(copyDto);
		}
		return reStFallbackApplybillDetails;
	}

    /**
     * [方法描述] 从申请单表获取信息提交至ISRM<br>
     *
     * <AUTHOR>
     * @param applyBillNo
     * @return <br>
     */
    @Override
    public ServiceData<?> submitApplyBillToISRM(String applyBillNo) {
        try {
            String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            logger.info("submitApplyBillToISRM applyBillNo:{}",applyBillNo);
            String param = constructParam(applyBillNo, createTime);
            if (null == param) {
                return ServiceDataUtil.getBusinessError("can not get applybillNo [" + applyBillNo + "] ApplyBill info !");
            }
            ServiceData<?> res = RemoteServiceDataUtil.invokeService(MicroServiceNameEum.ZTE_SCM_ISCP_BFF_SERVICE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, MicroServiceNameEum.BFF_ENTRY, param, new HashMap<String, String>());

            String error = JSONObject.toJSONString(res);
            StFallbackApplybillHead stFallbackApplybillHead = new StFallbackApplybillHead(applyBillNo, "ISRM", createTime,
                (error.length() > 3000 ? error.substring(0, 2999) : error));
            // 更新状态
            if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
                stFallbackApplybillHead.setStatus(ConstantApplyBillStatus.SUBMITED);
            } else {
            	onlineFallBackApplyBillRepository.insertApplyBillNoLog(applyBillNo);
            }
            onlineFallBackApplyBillRepository.updateStFallbackApplybillHead(stFallbackApplybillHead);
            return res;
        }
        catch (Exception e) {
            logger.error("submitApplyBillToISRM Exception : ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }
    }
    
    /**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @return <br>
	 */ 
	@Override
	public ServiceData<?> autoSubmitApplyBillToISRM() {
		List<String> waitPushList = onlineFallBackApplyBillRepository.queryWaitPushList();
		int flag = 0;
		if(Tools.isNotEmpty(waitPushList)){
			for(String applyBillNo : waitPushList){
				ServiceData<?> res = submitApplyBillToISRM(applyBillNo);
				if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
					flag = 1;
	            } else {
	            	flag = 0;
	            }
				
				onlineFallBackApplyBillRepository.updateApplyBillNoLog(applyBillNo,flag);
			}
		}
		return ServiceDataUtil.getSuccess();
	}

    @LogAnnotation(module = "在线退",action = "constructParam")
    public String constructParam(String applybillNo, String createTime) {
        try {
            List<ApplyBillDTO> listH = onlineFallBackApplyBillRepository.queryFallbackApplybillHead(applybillNo);
            List<ApplyBillItemDTO> listD = onlineFallBackApplyBillRepository.queryFallbackApplybillDetail(applybillNo);
            if (listH == null || listH.isEmpty() || listD == null || listD.isEmpty()) {
                return null;
            }
            ApplyBillDTO data = listH.get(0);
            data.setCreateTime(createTime);
            data.setItems(listD);
            return IterfaceConstructParamUtil.constructIsrmParam(ConstantApplyBillStatus.GET_RETURN_INFO_FROM_IWMS,
                ConstantApplyBillStatus.GET_RETURN_INFO_FROM_IWMS_IMPL, data, null);
        }
        catch (Exception e) {
            logger.error("constructParam Exception : ", e);
            return null;
        }
    }

    public StFallbackApplybillHead getStFallbackApplybillHead(String fallbackNo) throws Exception {
        List<StFallbackApplybillHead> listHead = onlineFallBackApplyBillRepository.queryFallbackHead(fallbackNo);
        if (null == listHead || listHead.isEmpty()) {
            return null;
        }
        StFallbackApplybillHead stFallbackApplybillHead = listHead.get(0);
        stFallbackApplybillHead.setApplybillNo(getApplyBillNo());
        stFallbackApplybillHead.setType("OnlineUnqualified");
        return stFallbackApplybillHead;
    }

    public String getApplyBillNo() throws Exception {
         return RedisSerialNoUtil.getDateIncreaseIdPattern(STH,5,YYMMDD);
    }

    @Override
    public ServiceData<?> acceptApplyBillStatus(ApplyBillStatusDTO billStatusDTO) {
        if (billStatusDTO.getBillNo() == null || billStatusDTO.getBillStatus() == null) {
            return ServiceDataUtil.getBusinessError("acceptApplyBillStatus info is in valid !");
        }
        StFallbackApplybillHead stFallbackApplybillHead = new StFallbackApplybillHead();
        stFallbackApplybillHead.setApplybillNo(billStatusDTO.getBillNo());
        stFallbackApplybillHead.setStatus(billStatusDTO.getBillStatus());
        if (StringUtils.isNotBlank(billStatusDTO.getModifyTime())) {
            stFallbackApplybillHead.setModifyTime(billStatusDTO.getModifyTime());
        }
        stFallbackApplybillHead.setLastUpdatedBy(billStatusDTO.getUpdateUserId());
        stFallbackApplybillHead.setLastUpdatedDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        stFallbackApplybillHead.setCloseRemark(billStatusDTO.getCloseRemark());
        stFallbackApplybillHead.setIwmsStatus(billStatusDTO.getIwmsStatus());

        // 当在IWMS异常关闭退货申请单时，如果退货申请单的状态不是"克服使用","报废","同意"状态；iWMS自动生成新的退货申请单，传递到iSRM系统继续流转。
        reSaveOnlineFallBackApplyBill(billStatusDTO);

        onlineFallBackApplyBillRepository.updateStFallbackApplybillHead(stFallbackApplybillHead);

        // 当关闭退货申请单时，调用ECSS的接口，废弃单据
        if (com.zte.utils.CommonUtils.equals(STR_CLOSED, billStatusDTO.getBillStatus())) {
            iwmsEcssService.invokeBillToEcss(JSONObject.toJSONString(Arrays.asList(billStatusDTO.getBillNo())), ECSS_BILL_TERMINATE);
        }

        return ServiceDataUtil.getSuccess();
    }

    /**
     * iWMS自动生成新的退货申请单，传递到iSRM系统继续流转。
     */
    public void reSaveOnlineFallBackApplyBill(ApplyBillStatusDTO billStatusDTO) {
        List<ApplyBillSoResultDTO> applyBillSoResultDTOList = onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(billStatusDTO.getBillNo());
        BusiAssertException.isEmpty(applyBillSoResultDTOList, INFOR_FALLBACK_APPLY_BILL_NOT_EXIST);
        List<StSysLookupValuesDTO> sysLookupValuesDTOList = onlineFallBackApplyBillRepository.getSysLookupValues(STR_1000034);
        BusiAssertException.isEmpty(applyBillSoResultDTOList, INFOR_FALLBACK_SYSLOOKUP_VALUES_NOT_EXIST);
        List<String> sysLookupValues = sysLookupValuesDTOList.stream().map(StSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());
        if (sysLookupValues.contains(applyBillSoResultDTOList.get(NumConstant.INT_0).getHandlingOpinions())) {
            return;
        }
        ServiceData<?> applyRes = produceApplyBillToDB(applyBillSoResultDTOList.get(NumConstant.INT_0).getFallbackNo());
        if (RetCode.SUCCESS_CODE.equals(applyRes.getCode().getCode())) {
            submitApplyBillToISRM(ObjectTransform.objectToStr(applyRes.getBo()));
        }
    }

    @Override
    public ServiceData<?> scheduleDealWaitFeedApplyNo(String applybillNo) {
        List<ServiceData<?>> listResult = Tools.newArrayList();
        try {
            // 获取状态为 SUBMIT_SO 的申请单
            List<ApplyBillDTO> list = onlineFallBackApplyBillRepository.queryWaitFeedApplyNo(applybillNo);
            BusiAssertException.isEmpty(list,INFOR_NO_WAIT_FEED_APPLYNO);
            List<String> totalOrderKeyList = Tools.newArrayList();
            // 获取待处理库出库完成的申请单
            List<List<ApplyBillDTO>> splitList = CommonUtils.splitList(list,NumConstant.INT_500);
            splitList.forEach(t->{
                List<String> externalOrderKeyList = onlineFallbackInforRepository.queryDealWarehouseSo(t);
                if(Tools.isEmpty(externalOrderKeyList)){return;}
                totalOrderKeyList.addAll(externalOrderKeyList);
            });

            if(Tools.isEmpty(totalOrderKeyList)){
                return ServiceDataUtil.getSuccess(listResult);
            }
            List<String> dealOrderKeyList = CommonUtils.splitList(totalOrderKeyList,INT_50).get(0);
            dealOrderKeyList.forEach(t->{
                String handlingOptions = onlineFallBackApplyBillRepository.queryStFallbackApplybillHeadHandlingOptions(t);
                if(Constant.STR_AGREE.equals(handlingOptions) || Constant.STR_SCRAP.equals(handlingOptions)){
                    iscpEsbLogRepository.insertListApplybillNo(t);
                }
                listResult.add(feedbackApplyBillSoResult(t));
            });

            return ServiceDataUtil.getSuccess(listResult);
        }
        catch (Exception e) {
            logger.error("acceptApplyBillStatus Exception : ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }
    }

    @Override
    public ServiceData<?> acceptApplyBillSupplyResult(SupplyResultDTO supplyResultDTO) {
        if (supplyResultDTO.getOrigId() < 0 || supplyResultDTO.getBillNo() == null || supplyResultDTO.getReturnComments() == null) {
            return ServiceDataUtil.getBusinessError("acceptApplyBillSupplyResult info is in valid !");
        }
        StFallbackApplybillHead stFallbackApplybillHead = new StFallbackApplybillHead();
        stFallbackApplybillHead.setId(supplyResultDTO.getOrigId());
        stFallbackApplybillHead.setApplybillNo(supplyResultDTO.getBillNo());
        stFallbackApplybillHead.setStatus(ConstantApplyBillStatus.SUPPLIEER_DEALED);
        stFallbackApplybillHead.setModifyTime(supplyResultDTO.getModifyTime());
        stFallbackApplybillHead.setLastUpdatedBy("ISRM");
        stFallbackApplybillHead.setLastUpdatedDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        stFallbackApplybillHead.setReturnComments(supplyResultDTO.getReturnComments());
        stFallbackApplybillHead.setReturnModel(supplyResultDTO.getReturnModel());
        stFallbackApplybillHead.setHandlingOpinions(supplyResultDTO.getHandlingOpinions());
        stFallbackApplybillHead.setScrappingReason(supplyResultDTO.getScrappingReason());
        stFallbackApplybillHead.setRemark(supplyResultDTO.getRemark());
        stFallbackApplybillHead.setPickStyle(supplyResultDTO.getPickStyle());
        stFallbackApplybillHead.setReceiver(supplyResultDTO.getReceiver());
        stFallbackApplybillHead.setReceiverContactNo(supplyResultDTO.getReceiverContactNo());
        stFallbackApplybillHead.setReceiverAddress(supplyResultDTO.getReceiverAddress());
        stFallbackApplybillHead.setCarrier(supplyResultDTO.getCarrier());
        stFallbackApplybillHead.setReturnAddress(supplyResultDTO.getReturnAddress());
        stFallbackApplybillHead.setPickAddress(supplyResultDTO.getPickAddress());
        try {
            onlineFallBackApplyBillRepository.updateStFallbackApplybillHead(stFallbackApplybillHead);
            // 同意 自动生成出库单
            if (!Constant.STR_AGREE.equals(stFallbackApplybillHead.getReturnComments())) {
                return ServiceDataUtil.getSuccess();
            }
            // submitApllyToInforSo(stFallbackApplybillHead.getApplybillNo()); 提交infor改为ECSS回传单据处理结果后自动触发
            // 接收处理结果更新到数据后返回对端成功,不考虑是否自动生产了待处理库出库单
            return ServiceDataUtil.getSuccess();
        }
        catch (Exception e) {
            logger.error("acceptApplyBillSupplyResult Exception : ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }
    }

    @Override
    public ServiceData<?> feedbackApplyBillSoResult(String applybillNo) {
        try {
            List<ApplyBillSoResultDTO> listH = onlineFallBackApplyBillRepository.queryApplyBillSoResultHead(applybillNo);
            List<ApplyBillSoResultItemDTO> listD = onlineFallBackApplyBillRepository.queryApplyBillSoResultDetail(applybillNo);
            List<Map<String, String>> listR = onlineFallBackApplyBillRepository.queryApplyBillSoResultReelSn(applybillNo);
            if (null == listH || listH.isEmpty() || listD == null || listD.isEmpty()) {
                return ServiceDataUtil.getBusinessError("applyBillSoResultFeedback applybillNo[" + applybillNo + "] can not gen info !");
            }
            
            //出库数量更新为INFOR的实际出库数量
            for(ApplyBillSoResultItemDTO applyBillSoResultItemDTO : listD){
            	double realOutNumber = onlineFallbackInforRepository.queryRealOutNumber(applybillNo,applyBillSoResultItemDTO.getRowNo());
            	applyBillSoResultItemDTO.setRealOutNumber(realOutNumber);
            }
            
            Map<String, List<ApplyBillSoResultItemReelSnDTO>> map = new HashMap<String, List<ApplyBillSoResultItemReelSnDTO>>();
            listR.forEach(ret -> {
                String rowNo = ret.get("rowNo");
                String reelSn = ret.get("reelSn");
                if (StringUtils.isNotBlank(rowNo) && StringUtils.isNotBlank(reelSn)) {
                    if (!map.containsKey(rowNo)) {
                        List<ApplyBillSoResultItemReelSnDTO> list = new ArrayList<ApplyBillSoResultItemReelSnDTO>();
                        list.add(new ApplyBillSoResultItemReelSnDTO(reelSn));
                        map.put(rowNo, list);
                    }
                    else {
                        map.get(rowNo).add(new ApplyBillSoResultItemReelSnDTO(reelSn));
                    }
                }
            });
            listD.forEach(ret -> {
                String rowNo = ret.getLpn();
                ret.setLpn(rowNo.split("\\|")[0]);
                if (map.containsKey(rowNo)) {
                    ret.setSns(map.get(rowNo));
                }
            });
            ApplyBillSoResultDTO data = listH.get(0);
            data.setItems(listD);
            logger.info("feedbackApplyBillSoResult:applybillNo {} data:{}",applybillNo, JsonUtil.toJSONString(data));
            ServiceData<?> res = RemoteServiceDataUtil.invokeService(MicroServiceNameEum.ZTE_SCM_ISCP_BFF_SERVICE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, MicroServiceNameEum.BFF_ENTRY,
                IterfaceConstructParamUtil.constructIsrmParam(ConstantApplyBillStatus.GET_OUT_GOODS_FROM_IWMS,
                    ConstantApplyBillStatus.GET_RETURN_INFO_FROM_IWMS_IMPL, data, null),
                Tools.newHashMap());

            // 出库反馈完 更新状态为关闭
            String error = JSONObject.toJSONString(res);
            StFallbackApplybillHead tmp = new StFallbackApplybillHead(applybillNo, "IWMS",
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), (error.length() > 3000 ? error.substring(0, 2999) : error));
            if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
                updateStatusAndQty(applybillNo, listD, tmp);
            }
            tmp.setProcessCount(tmp.getProcessCount()+1);
            onlineFallBackApplyBillRepository.updateStFallbackApplybillHead(tmp);
            return res;
        }
        catch (Exception e) {
            logger.error("applyBillSoResultFeedback Exception : ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }
    }

    public void updateStatusAndQty(String applybillNo, List<ApplyBillSoResultItemDTO> listD, StFallbackApplybillHead tmp) {
        tmp.setStatus(ConstantApplyBillStatus.CLOSED);
        tmp.setSubmitInforStatus(ConstantApplyBillStatus.CLOSED);
        for(int i = NumConstant.INT_0; i < listD.size(); i++){
           onlineFallBackApplyBillRepository.syncRealOutNumber(listD.get(i).getRowNo(), applybillNo, listD.get(i).getRealOutNumber());
        }
    }

    @Override
    public ServiceData<?> submitApllyToInforSo(String applybillNo) {
        String updateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        try {
            // 提交出库
            logger.info("submitApllyToInforSo:{}",applybillNo);

            ServiceData<?> res = WebServiceClient.submitInfor(onlineFallBackApplyBillRepository.getWsdlUrl(), getXmlMessage(applybillNo));
            // 更新状态
            String error = JSONObject.toJSONString(res);
            StFallbackApplybillHead tmp = new StFallbackApplybillHead(applybillNo, "IWMS", updateTime,
                (error.length() > 3000 ? error.substring(0, 2999) : error));
            if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
                tmp.setStatus(ConstantApplyBillStatus.SUBMIT_SO);
                tmp.setSubmitInforStatus(ConstantApplyBillStatus.SUBMIT_SO);
            }
            onlineFallBackApplyBillRepository.updateStFallbackApplybillHead(tmp);
            return ServiceDataUtil.getSuccess(error);
        }
        catch (Exception e) {
            logger.error("submitApllyToInforSo exception ", e);
            return ServiceDataUtil.getBusinessError(e.toString());
        }
    }

    @Override
    public ServiceData<?> failReelIdBarcodeAndFeedbackResult(String applybillNo, String xEmpNo, String xAuthValue){
        logger.info("param[" + applybillNo + "] param[" + xEmpNo + "]"+"param["+xAuthValue+"]");
        //根据单号获取listApplybillNo，如果传入单号为空值，则从数据库中捞取servicecode不为'0000' 且 Processcount < 4的EXTERNRECEIPTKEY数据集，
        //若单号不为空值，输入不存在的单号返回值为空；若单号存在，则对当前单号进行查询
        List<String> applybillNoList = iscpEsbLogRepository.queryListApplybillNo(applybillNo);
        if(!CollectionUtils.isEmpty(applybillNoList)) {
            List<List<String>> applybillNoSplitList = CommonUtils.splitList(applybillNoList, NumConstant.INT_500);
            List<ApplyBillDTO> barcodeList = new ArrayList<>();
            for (List<String> splitList : applybillNoSplitList) {
                //对数据集中的每一个单号所对应的barcode集合进行barcode的重组
                List<ApplyBillDTO> barcodeSplitList = onlineFallBackApplyBillRepository.queryStFallbackDetailBarcode(splitList);
                if (!CollectionUtils.isEmpty(barcodeSplitList)) {
                    barcodeList.addAll(barcodeSplitList);
                }
            }
            //调用条码中心接口
            encapsulatingDataandInvokingtheBarcodeCenter(barcodeList, xEmpNo, xAuthValue);
            return ServiceDataUtil.getSuccess(MessageId.INVOKE_SUCCESS);
        }else{
            return ServiceDataUtil.getSuccess(MessageId.NO_NUMBER_RETRIEVED);
        }
    }

    public void encapsulatingDataandInvokingtheBarcodeCenter(List<ApplyBillDTO> barcodeList,String xEmpNo,String xAuthValue) {
        List<List<ApplyBillDTO>> barcodeSplitList = CommonUtils.splitList(barcodeList, NumConstant.INT_500);
        for (List<ApplyBillDTO> applyBillDTOList : barcodeSplitList) {
            List<Map<String, String>> list = new ArrayList<>();

            for (ApplyBillDTO applyBillDTO : applyBillDTOList) {
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                map.put(Constant.BARCODE, applyBillDTO.getReelid());
                // reelidFlag = 1,reelid
                if (applyBillDTO.getReelidFlag().compareTo(new BigDecimal(NumConstant.INT_1)) == NumConstant.INT_0) {
                    map.put(Constant.PARENT_CATEGORY_CODE, Constant.REEL_MP_CODE);
                }
                // reelidFlag = 2,sn
                if (applyBillDTO.getReelidFlag().compareTo(new BigDecimal(NumConstant.INT_2)) == NumConstant.INT_0) {
                    map.put(Constant.PARENT_CATEGORY_CODE, Constant.SN_CODE);
                }
                list.add(map);
            }
            ServiceData<?> res = invokeBarcodeInterface(list,xEmpNo,xAuthValue);
            //日志表更新值
            //servicecode，editdate与processcount不用获取，resultmessage为ListBarcode需要截取2000字符以内,获取resultmessage2为msgId": "RetCode.Success","msg": "操作成功"
            Set<String> applyNoSet = new HashSet<>();
            for(ApplyBillDTO abd : applyBillDTOList){
                if (!applyNoSet.contains(abd.getBillNo())) {
                    updateIscpEsbLog(abd, res);
                    applyNoSet.add(abd.getBillNo());
                }
            }

        }
    }

    public void updateIscpEsbLog(ApplyBillDTO abd, ServiceData<?> res) {
        //获取日志表更新值
        //servicecode，editdate与processcount不用获取，resultmessage为ListBarcode需要截取2000字符以内,获取resultmessage2为msgId": "RetCode.Success","msg": "操作成功"
        String serviceCode = res.getCode().getCode();
        //将请求参数转换为String类型
        String resultMessage = JSONObject.toJSONString(abd.getReelid());
        //如果请求参数字符串长度大于2000，则去掉尾部多余部分
        String subStringResultMessage = resultMessage.length() > NumConstant.INT_2000 ? resultMessage.substring(NumConstant.INT_0, NumConstant.INT_1999) : resultMessage;
        //获取返回结果
        String resultmessage2 = JSONObject.toJSONString(res);
        //对更新的字段进行组合
        IscpEsbLog iel = new IscpEsbLog(abd.getBillNo(), serviceCode, subStringResultMessage, resultmessage2);
        //写入iscp_esb_log表中
        iscpEsbLogRepository.updateFailReelIDLog(iel);
    }

    @LogAnnotation(module = "条码中心接口",action = "invokeBarcodeInterface")
    public ServiceData<?> invokeBarcodeInterface(List<Map<String, String>> list,String xEmpNo,String xAuthValue) {
        //条码中心接口按applybillNo每条进行调用，
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put(Constant.X_TENANT_ID, NumConstant.STR_10001);
        mapHeader.put(Constant.X_EMP_NO,  xEmpNo);
        mapHeader.put(Constant.X_AUTH_VALUE, xAuthValue);
        return RemoteServiceDataUtil.invokeService(Constant.ZTE_ISS_BARCODECENTER_BARCODE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, Constant.ZTE_ISS_BARCODECENTER_BARCODE_ENTRY,JSONObject.toJSONString(list),
                mapHeader);
    }

    public String getXmlMessage(String applybillNo) {
        String curTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        MsgHeader msgHeader = new MsgHeader();
        msgHeader.setSourceSystemId(ConstantInfor.IWMS_SOURCESYSTEMID);
        msgHeader.setSourceSystemName(ConstantInfor.IWMS_SOURCESYSTEMNAME);
        msgHeader.setUserId(ConstantInfor.IWMS_SOURCESYSTEMID);
        msgHeader.setUserName(ConstantInfor.IWMS_SOURCESYSTEMNAME);
        msgHeader.setSubmitDate(curTime);
        List<SoHeader> listSoHeader = onlineFallBackApplyBillRepository.getSoHeader(applybillNo);
        List<SoDetail> listSoDetail = onlineFallBackApplyBillRepository.getSoDetail(applybillNo);

        if (listSoHeader.size() != 1 || listSoDetail.isEmpty()) {
            return "";
        }

        listSoHeader.get(0).setListSoDetail(listSoDetail);
        listSoHeader.get(0).setRequestedShipDate(curTime);
        SoBizCont bizCont = new SoBizCont();
        bizCont.setSoHeader(listSoHeader.get(0));

        MsgBody<SoBizCont> msgBody = new MsgBody<SoBizCont>();
        msgBody.setServiceCode(ConstantInfor.SCATTER_OUT_SERVICECODE);
        msgBody.setMsgid(UUID.randomUUID().toString());
        msgBody.setBizCont(bizCont);

        MsgSend msgSend = new MsgSend();
        msgSend.setMsgHeader(msgHeader);
        msgSend.setMsgBody(msgBody);

        return XstreamUtil.toXML(msgSend);
    }

}
