package com.zte.application.sncabind.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.AsyncUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.CustomerItemsService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.sncabind.AbstractMesPsTaskService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import com.zte.interfaces.dto.aps.TasksQueryDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.interfaces.dto.mbom.PdmProdInstanceResDTO;
import com.zte.springbootframe.common.model.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.LOOKUP_TYPE_7300;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/4/30 16:38
 */
@Slf4j
@Service
public class AdditionalAttributesSyncMesPsTaskServiceImpl extends AbstractMesPsTaskService {
    @Resource
    private ApsInOneClient apsInOneClient;
    @Resource
    private PsTaskExtendedService psTaskExtendedService;
    @Resource
    private CustomerItemsService customerItemsService;
    @Resource
    private CpqdRemoteService cpqdRemoteService;
    @Value("${lookup.type.taskType:2025050601}")
    private Integer taskTypeType;
    @Value("${lookup.type.entityClass:2025051511}")
    private String entityClassType;
    
    @Override
    protected List<PsTask> filter(List<PsTask> rows) {
        Map<String, PsTask> psTaskMap = rows.stream().collect(Collectors.toMap(PsTask::getTaskNo, Function.identity(), (o1, o2) -> o1));
        Collection<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.listByTaskNos(CollUtil.trans(rows, PsTask::getTaskNo));
        return ListUtil.toList(MapUtil.removeAny(psTaskMap, ArrayUtil.toArray(CollUtil.trans(psTaskExtendedDTOList, PsTaskExtendedDTO::getTaskNo), String.class)).values());
    }

    @Override
    protected void process(List<PsTask> filteredRows, List<PsTask> allRows) {
        if (CollUtil.isEmpty(filteredRows)){
            log.warn("没有查询到满足条件的计划任务");
            return;
        }
        AsyncUtil.waitAll(CompletableFuture.runAsync(() -> updateCustomItemInfo(filteredRows)), CompletableFuture.runAsync(() -> savePsTaskExtended(filteredRows)));
    }

    private void savePsTaskExtended(List<PsTask> psTasks) {
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = queryPsTaskExtendedDTOList(psTasks);
        if (CollectionUtils.isEmpty(psTaskExtendedDTOList)){
            log.warn("没有查询到计划任务的附加信息");
            return;
        }
        complement(psTaskExtendedDTOList);
        save(psTaskExtendedDTOList);
    }

    private void updateCustomItemInfo(List<PsTask> psTasks) {
        List<String> itemNos = CollUtil.distinct(CollUtil.trans(psTasks, PsTask::getItemNo));
        List<PdmProdInstanceResDTO> pdmProdInstanceResDTOList = pdmRemoteService.getPdmProdInstanceResDTOList(itemNos);
        List<CustomerItemsDTO> customerItemsDTOS = customerItemsService.queryListByZteCodes(itemNos);
        Map<String, CustomerItemsDTO> customerItemsDTOMap = IterUtil.toMap(customerItemsDTOS, CustomerItemsDTO::getZteCode);
        Map<String, String> customerCodeToNameMap = sysLookupValuesService.getValueMappingByType(LOOKUP_TYPE_7300.intValue(), SysLookupValues::getAttribute2);
        customerItemsDTOS = pdmProdInstanceResDTOList.stream().map(item -> {
            CustomerItemsDTO customerItemsDTO = customerItemsDTOMap.getOrDefault(item.getInstanceNo(), new CustomerItemsDTO());
            customerItemsDTO.setZteCode(item.getInstanceNo());
            customerItemsDTO.setCustomerName(Assert.notBlank(customerCodeToNameMap.get(item.getCustomNo()), () -> new IllegalArgumentException(String.format("客户编码%s对应的客户名称不存在", item.getCustomNo()))));
            customerItemsDTO.setProjectType(Constant.STR_THREE);
            customerItemsDTO.setProjectName(Constant.ALIBABA_PUBLIC_CLOUD);
            customerItemsDTO.setStatus(Constant.FLAG_Y);
            customerItemsDTO.setLastUpdatedBy(Constant.SYSTEM);
            customerItemsDTO.setCreateBy(Constant.SYSTEM);
            return customerItemsDTO;
        }).collect(Collectors.toList());
        List<CustomerItemsDTO> updateList = CollUtil.removeWithAddIf(customerItemsDTOS, item -> ObjUtil.isNotNull(item.getId()));
        customerItemsService.batchUpdateByZteCode(updateList);
        customerItemsService.batchInsert(customerItemsDTOS);
    }

    private void complement(List<PsTaskExtendedDTO> psTaskExtendedDTOList) {
        psTaskExtendedDTOList.forEach(item -> {
            item.setLastUpdatedBy(Constant.SYSTEM);
            item.setCreateBy(Constant.SYSTEM);
        });
    }

    private void save(List<PsTaskExtendedDTO> psTaskExtendedDTOList) {
        psTaskExtendedService.batchSave(psTaskExtendedDTOList);
    }

    private List<PsTaskExtendedDTO> queryPsTaskExtendedDTOList(List<PsTask> rows) {
        // 1. 获取任务扩展信息
        Page<PsTaskExtendedDTO> page = queryTaskExtendedInfo(rows);
        
        // 2. 获取云类型信息
        Map<String, String> billNoToCloudTypeMap = buildCloudTypeMapping(page);
        
        // 3. 获取数据字典映射
        Map<String, String> taskTypeMap = getTaskTypeMapping();
        Collection<String> entityClasses = getEntityClasses();
        
        // 4. 处理数据
        return processTaskExtendedData(page, billNoToCloudTypeMap, taskTypeMap, entityClasses);
    }

    /**
     * 查询任务扩展信息
     */
    private Page<PsTaskExtendedDTO> queryTaskExtendedInfo(List<PsTask> rows) {
        return apsInOneClient.customerTaskQuery(new TasksQueryDTO().setEntityNoAddStockOrgIdList(CollUtil.trans(rows, item -> {
            EntityQueryDTO entityQueryDTO = new EntityQueryDTO();
            entityQueryDTO.setEntityNo(item.getTaskNo());
            entityQueryDTO.setStockOrgId(Convert.toInt(item.getOrgId()));
            return entityQueryDTO;
        })));
    }

    /**
     * 构建云类型映射
     */
    private Map<String, String> buildCloudTypeMapping(Page<PsTaskExtendedDTO> page) {
        CpqdQueryDTO queryDTO = buildCloudTypeQueryCondition(page);
        List<CpqdGbomDTO> gbomDTOList = cpqdRemoteService.queryGbomList(queryDTO);
        
        // 构建云类型映射关系：以billNo为key，cloudType为value
        return CollUtil.emptyIfNull(gbomDTOList).stream()
                .filter(gbom -> StringUtils.isNotBlank(gbom.getBillNo()) && StringUtils.isNotBlank(gbom.getCloudType()))
                .collect(Collectors.toMap(
                        CpqdGbomDTO::getBillNo,
                        CpqdGbomDTO::getCloudType,
                        (existing, replacement) -> existing // 如果有重复的billNo，保留第一个
                ));
    }

    /**
     * 构建云类型查询条件
     */
    private CpqdQueryDTO buildCloudTypeQueryCondition(Page<PsTaskExtendedDTO> page) {
        CpqdQueryDTO queryDTO = new CpqdQueryDTO();
        List<PsTaskExtendedDTO> pageRows = CollUtil.emptyIfNull(page.getRows());
        
        if (CollUtil.isNotEmpty(pageRows)) {
            // 提取所有的billNo
            List<String> billNos = pageRows.stream()
                    .map(PsTaskExtendedDTO::getBillNo)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(billNos)) {
                queryDTO.setBillNo(billNos);
            }
        }
        return queryDTO;
    }

    /**
     * 获取任务类型映射
     */
    private Map<String, String> getTaskTypeMapping() {
        List<SysLookupValues> sysLookupValues = sysLookupValuesService.selectValuesByType(taskTypeType);
        return sysLookupValues.stream().collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getAttribute1));
    }

    /**
     * 获取任务分类
     */
    private Collection<String> getEntityClasses() {
        return sysLookupValuesService.getMeaningsByType(entityClassType);
    }

    /**
     * 处理任务扩展数据
     */
    private List<PsTaskExtendedDTO> processTaskExtendedData(Page<PsTaskExtendedDTO> page, 
                                                           Map<String, String> billNoToCloudTypeMap,
                                                           Map<String, String> taskTypeMap,
                                                           Collection<String> entityClasses) {
        return CollUtil.emptyIfNull(page.getRows()).stream()
                .filter(item -> filterByTaskType(item, taskTypeMap))
                .filter(item -> filterByEntityClass(item, entityClasses))
                .peek(item -> setCloudType(item, billNoToCloudTypeMap)) // 直接在这里设置云类型
                .collect(Collectors.toList());
    }

    /**
     * 根据任务类型过滤
     */
    private boolean filterByTaskType(PsTaskExtendedDTO item, Map<String, String> taskTypeMap) {
        if (taskTypeMap.containsKey(item.getBusinessScene())) {
            item.setTaskType(taskTypeMap.get(item.getBusinessScene()));
            return true;
        }
        log.error(String.format("在数据字典中未配置业务场景%s与任务类型的转换关系", item.getBusinessScene()));
        AlarmHelper.alarm("imes_req_monitor_alarm", "1001", AlarmSeverityEnum.CRITICAL, 
                "在数据字典中未配置业务场景与任务类型的转换关系", 
                String.format("在数据字典中未配置业务场景%s与任务类型的转换关系", item.getBusinessScene()));
        return false;
    }

    /**
     * 根据任务分类过滤
     */
    private boolean filterByEntityClass(PsTaskExtendedDTO item, Collection<String> entityClasses) {
        if (entityClasses.contains(item.getEntityClass())) {
            return true;
        }
        log.error(String.format("任务分类%s不在定义范围内", item.getEntityClass()));
        AlarmHelper.alarm("imes_req_monitor_alarm", "1001", AlarmSeverityEnum.CRITICAL, 
                "任务分类不在定义范围内", String.format("任务分类%s不在定义范围内", item.getEntityClass()));
        return false;
    }

    /**
     * 设置云类型
     */
    private void setCloudType(PsTaskExtendedDTO item, Map<String, String> billNoToCloudTypeMap) {
        String cloudType = null;
        
        // 通过billNo匹配云类型
        if (StringUtils.isNotBlank(item.getBillNo()) && billNoToCloudTypeMap.containsKey(item.getBillNo())) {
            cloudType = billNoToCloudTypeMap.get(item.getBillNo());
            log.debug("通过billNo[{}]匹配到云类型[{}]", item.getBillNo(), cloudType);
        } else {
            log.warn("未找到任务[{}]对应的云类型信息，billNo:[{}]", 
                    item.getTaskNo(), item.getBillNo());
        }
        
        item.setCloudType(cloudType);
    }
}
