/**
 * 项目名称 : SnCaBinding
 * 创建日期 : 2019-07-11
 * 修改历史 :
 * 1. [2019-07-11] 创建文件 by 10243397
 **/
package com.zte.application.sncabind.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.*;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.ExcelUtils;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.model.ResultData;
import com.zte.common.utils.ThreadUtil;
import com.zte.common.utils.*;
import com.zte.domain.model.*;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.assembler.IfisTaskAssembler;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.aps.*;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.interfaces.dto.task.PsTaskTreeDTO;
import com.zte.interfaces.sncabind.dto.*;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.km.udm.common.util.MD5Util;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.model.NumConstant.NUM_HUNDRED;
import static com.zte.common.model.NumConstant.NUM_ZERO;
import static com.zte.common.utils.Constant.*;


/**
 * 批次实现类
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class PsTaskServiceImpl implements PsTaskService {

    private static final String TASK_NO_SUFFIX = "ABBABBll";

    private static final String ITEM_NAME = "ZXV10 B860AV2D1PR STBAB";

    private static final BigDecimal TASK_QTY = new BigDecimal(100);

    private static final int PRODPLAN_ID = 10000000;

    private static final BigDecimal ENTITY_ID = new BigDecimal(2);

    private static final String SOURCE_SYS = "WMES";

    private static final Logger LOG = LoggerFactory.getLogger(PsTaskServiceImpl.class);
    private static final String ERROR_EXPORT_MODEL = "  exportModel  :";

    @Autowired
    private PsTaskRepository psTaskRepository;

    @Autowired
    private FactoryConfig factoryConfig;

    @Autowired
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Autowired
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Autowired
    private CenterFactoryCallSiteService centerFactoryCallSiteService;

    @Autowired
    private DatawbRemoteService datawbRemoteService;

    @Autowired
    private BBomHeaderRepository bBomHeaderRepository;
    @Autowired
    private ProductionmgmtRemoteService productionmgmtRemoteService;
    @Autowired
    private CFFactoryService cFFactoryService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private ApsRemoteService apsRemoteService;
    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;
    @Autowired
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;

    @Autowired
    private IMESLogService imesLogService;
    @Autowired
    private BProdBomHeaderService bProdBomHeaderService;

    @Autowired
    private OpenApiRemoteService openApiRemoteService;
    @Autowired
    private BProdBomChangeDetailService bomChangeDetailService;
    @Autowired
    private PsTaskExtendedService psTaskExtendedService;
    @Autowired
    private PdmRemoteService pdmRemoteService;
    @Value("${batch.exhaustion.early.warning.value:5000}")
    private Integer batchExhaustionEarlyWarningValue;
    @Value("${pdm.prod.instance.api.open:true}")
    private boolean pdmApiOpen;

    @Autowired
    private CpqdService cpqdService;

    @Override
    public long getPsTaskCountWithRouteDetail(Map<String, Object> map) {
        return psTaskRepository.getPsTaskCountWithRouteDetail(map);
    }

    /**
     * getList方法
     *
     * @param map        参数集
     * @param orderField 排序
     * @param order      order
     * @param curPage    当前页
     * @param pageSize   页值
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<PsTask> getPsTaskList(Map<String, Object> map, String orderField, String order, Long curPage,
                                      Long pageSize) throws Exception {

        map.put("orderField", orderField);
        map.put("order", order);
        curPage = curPage < 1 ? 1 : curPage;
        pageSize = pageSize < 1 ? NumConstant.LONG_5000 : pageSize;
        map.put("startRow", (curPage - 1) * pageSize + 1);
        map.put("endRow", curPage * pageSize);
        handlerInParams(map);
        List<PsTask> list = psTaskRepository.getPsTaskList(map);

        return list;
    }

    /**
     * 任务数量有变更时，更新本地工厂相关信息
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public DealPsWipInfoParamDTO updateLocalFactoryQty(PsTaskDTO dto) throws Exception {
        if(StringUtils.isEmpty(dto.getTaskNo()) || StringUtils.isEmpty(dto.getProdplanId())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.PARAMS_ERROR);
        }
        PsTask psTask = psTaskRepository.selectPsTaskByTaskNo(dto.getTaskNo(),dto.getProdplanId());
        if(psTask == null){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.TASK_NO_IS_NOT_EXISTS,new Object[]{dto.getTaskNo()});
        }
        psTask.setDelSns(dto.getDelSns().replaceAll(Constant.ENTER, COMMA));
        return openApiRemoteService.updateLocalFactoryQty(psTask);
    }
    public void setQtyChangeFlag(List<PsTask> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<BigDecimal, List<PsTask>> orderMap = list.stream().filter(e->e.getFactoryId() != null).collect(Collectors.groupingBy(PsTask::getFactoryId));
        Map<String,List<String>>  taskMap =  new HashMap<>();
        for(Map.Entry<BigDecimal , List<PsTask>> entry : orderMap.entrySet())
        {
            BigDecimal factoryId = entry.getKey();
            List<PsTask>  tempList = entry.getValue();
            if(factoryId.compareTo(new BigDecimal(Constant.FACTORY_ID_CENTER)) == Constant.NUM_ZERO){
                continue;
            }
            taskMap.put(factoryId.toString(),tempList.stream().map(e->e.getTaskNo()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList()));
        }
        List<PsTask> psTaskList = openApiRemoteService.queryTaskInfo(taskMap);
        Map<String, BigDecimal> sumMap = psTaskList.stream()
                .collect(Collectors.groupingBy(PsTask::getTaskNo,
                        Collectors.reducing(BigDecimal.ZERO, PsTask::getTaskQty, BigDecimal::add)));
        for (PsTask psTask : list) {
            //和
            BigDecimal localQty = sumMap.get(psTask.getTaskNo());
            if(localQty == null || localQty.compareTo(psTask.getTaskQty()) == Constant.NUM_ZERO){
                continue;
            }
            psTask.setQtyChangeFlag(Constant.FLAG_Y);
        }
    }

    @Override
    public List<PsTask> getPsTaskListWithRouteDetail(Map<String, Object> map, String orderField, String order, Long curPage,
                                                     Long pageSize) throws Exception {

        map.put("orderField", orderField);
        map.put("order", order);
        curPage = curPage < 1 ? 1 : curPage;
        pageSize = pageSize < 1 ? NumConstant.LONG_5000 : pageSize;
        map.put("startRow", (curPage - 1) * pageSize + 1);
        map.put("endRow", curPage * pageSize);
        List<PsTask> list = psTaskRepository.getPsTaskListWithRouteDetail(map);
        setEmpName(list);
        setCustomerNo(list);
        this.setQtyChangeFlag(list);
        setMBom(list);
        setMaterialSignInfo(list);
        return list;
    }

    private void setCustomerNo(List<PsTask> list) {
        if (!pdmApiOpen){
            return;
        }
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> itemNos = list.stream().filter(item->StringUtils.isBlank(item.getCustomerNo())).map(PsTask::getItemNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(itemNos)){
            return;
        }
        Map<String, String> itemNoToCustomerNumberMap = pdmRemoteService.getItemNoToCustomerNumberMap(itemNos);
        list.forEach(item->{
            item.setCustomerNo(itemNoToCustomerNumberMap.getOrDefault(item.getItemNo(),null));
        });
    }

    @Override
    public boolean setProdListByMBom (PsTaskDTO dto) {
        if (StringUtils.isEmpty(dto.getItemNo())) {
            return true;
        }
        // 获取制造bom的批次清单
        List<BProdBomHeaderDTO> bomList = bomChangeDetailService.selectOriginalByProductCode(dto.getItemNo());
        // 未获取到不处理
        if (org.apache.commons.collections.CollectionUtils.isEmpty(bomList)) {
            return true;
        }
        List<String> prodList = bomList.stream().map(BProdBomHeaderDTO::getProdplanId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        // 未传批次则直接赋值
        if (StringUtils.isEmpty(dto.getProdplanId())) {
            dto.setItemNo(null);
            dto.setProdplanIdList(prodList);
            return true;
        }
        // 批次不在清单中直接返回空结果
        if (!prodList.contains(dto.getProdplanId())) {
            return false;
        }
        // 在清单时将制造bom条件置空
        dto.setItemNo(null);
        return true;
    }

    private void setMBom(List<PsTask> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> prodPlanIdList = list.stream().map(PsTask::getProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = bProdBomHeaderService.queryProductCodeByProdPlanIdList(prodPlanIdList);
        Map<String,String> prodToMBomMap = bProdBomHeaderDTOList.stream().collect(Collectors.toMap(k -> k.getProdplanId(), v -> v.getProductCode(), (k1, k2) -> k1));
        for (PsTask entity : list) {
            entity.setmBom(entity.getItemNo());
            String mBom = prodToMBomMap.get(entity.getProdplanId());
            if (StringUtils.isNotBlank(mBom)) {
                entity.setmBom(mBom);
            }
        }
    }


    private void setEmpName(List<PsTask> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> empNoList = new ArrayList<>();
        List<String> empNoPlanner = list.stream().
                filter(e -> StringUtils.isNotEmpty(e.getPlanner())).map(PsTask::getPlanner).distinct().collect(Collectors.toList());
        List<String> empNoGrant = list.stream().
                filter(e -> StringUtils.isNotEmpty(e.getGrantBy())).map(PsTask::getGrantBy).distinct().collect(Collectors.toList());
        List<String> technologist = list.stream().
                filter(e -> StringUtils.isNotEmpty(e.getTechnologist())).map(PsTask::getTechnologist).distinct().collect(Collectors.toList());
        empNoList.addAll(empNoPlanner);
        empNoList.addAll(empNoGrant);
        empNoList.addAll(technologist);
        List<String> userIdList = empNoList.stream().distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(userIdList);
        if (null == hrmPersonInfoDTOMap) {
            return;
        }
        list.forEach(p -> {
            if (!StringUtils.isEmpty(p.getPlanner()) && null != hrmPersonInfoDTOMap.get(p.getPlanner())) {
                p.setPlannerName(hrmPersonInfoDTOMap.get(p.getPlanner()).getEmpName() + p.getPlanner());
            }
            if (!StringUtils.isEmpty(p.getGrantBy()) && null != hrmPersonInfoDTOMap.get(p.getGrantBy())) {
                p.setGrantByName(hrmPersonInfoDTOMap.get(p.getGrantBy()).getEmpName() + p.getGrantBy());
            }
            if (null != hrmPersonInfoDTOMap.get(p.getTechnologist())) {
                p.setTechnologist(hrmPersonInfoDTOMap.get(p.getTechnologist()).getEmpName() + p.getTechnologist());
            }
        });
    }


    @Override
    public Page<SpecifiedPsTaskVO> getSpecifiedPsTaskList(SpecifiedPsTaskDTO specifiedPsTaskDTO) {
        // 参数校验
        this.checkParams(specifiedPsTaskDTO);
        this.setTaskNoList(specifiedPsTaskDTO);
        // 处理条码
        String sn = specifiedPsTaskDTO.getSn();
        if (StringUtils.isNotBlank(sn)) {
            if (sn.length() < Constant.INT_7) {
                return new Page<>();
            }
            String prodplanId = sn.substring(0, 7);
            List<String> prodplanIdList = specifiedPsTaskDTO.getProdplanIdList();
            if (prodplanIdList == null) {
                prodplanIdList = new LinkedList<>();
            }
            prodplanIdList.add(prodplanId);
            specifiedPsTaskDTO.setProdplanIdList(prodplanIdList);
        }
        Page<SpecifiedPsTaskVO> page = new Page<>();
        page.setCurrent(Integer.parseInt(specifiedPsTaskDTO.getPage()));
        page.setPageSize(Integer.parseInt(specifiedPsTaskDTO.getRows()));
        page.setParams(specifiedPsTaskDTO);
        List<SpecifiedPsTaskVO> list = psTaskRepository.getSpecifiedPsTaskList(page);
        if (CollectionUtils.isEmpty(list)) {
            return page;
        }
        // lead_flag对应字典
        Map<String, String> leadMap = getLeadMap();
        //pcbVersion版本数据字典
        Map<String, String> pcbVersion = getPcbVersionMap(list);
        for (SpecifiedPsTaskVO specifiedPsTaskVO : list) {
            String leadName;
            // "STEP"是单板 lead_flag 需字典转换
            // "WMS"是整机 is_lead 直接取字段
            leadName = specifiedPsTaskVO.getIsLead();
            if (Constant.SYS_TYPE_STEP.equals(specifiedPsTaskVO.getSourceSys())) {
                leadName = leadMap.get(specifiedPsTaskVO.getLeadFlag());
            }
            specifiedPsTaskVO.setLeadName(leadName);
            specifiedPsTaskVO.setPcbVersion(pcbVersion.get(specifiedPsTaskVO.getItemNo()));
        }
        page.setRows(list);
        return page;
    }

    /**
     * @return
     * <AUTHOR>
     * 处理taskNo批量条件
     * @Date 2023/2/1 15:49
     * @Param [com.zte.interfaces.sncabind.dto.SpecifiedPsTaskDTO]
     **/
    private void setTaskNoList(SpecifiedPsTaskDTO specifiedPsTaskDTO) {
        if (StringUtils.isEmpty(specifiedPsTaskDTO.getTaskNo())) {
            return;
        }
        // 将字符串以逗号分隔转为List
        List<String> taskNoList = Arrays.asList(specifiedPsTaskDTO.getTaskNo().split(Constant.COMMA));
        specifiedPsTaskDTO.setTaskNo(null);
        specifiedPsTaskDTO.setTaskNoList(taskNoList);
    }

    /**
     * 校验参数
     *
     * @param specifiedPsTaskDTO 入参实体
     * @throws MesBusinessException 业务异常
     */
    private void checkParams(SpecifiedPsTaskDTO specifiedPsTaskDTO) {
        long curPage = Long.parseLong(specifiedPsTaskDTO.getPage());
        long pageSize = Long.parseLong(specifiedPsTaskDTO.getRows());
        if (pageSize > Constant.INT_1000 || curPage <= 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PAGE_SIZE_LAGER_THAN_1000);
        }
        // 任务号、批次号、料单代码、条码必传一个
        if (StringUtils.isBlank(specifiedPsTaskDTO.getTaskNo())
                && StringUtils.isBlank(specifiedPsTaskDTO.getSn())
                && CollectionUtils.isEmpty(specifiedPsTaskDTO.getItemNoList())
                && CollectionUtils.isEmpty(specifiedPsTaskDTO.getProdplanIdList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_PARAMS_LOST);
        }
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    @Override
    public long updatePsTaskByIdSelective(PsTask record) {
        return psTaskRepository.updatePsTaskByIdSelective(record);
    }

    /**
     * 返工扫描，产品大类查询,点对点
     *
     * @param transferList
     **/
    @Override
    public ServiceData searchForProdPlan(List<String> transferList) {
        ServiceData ret = new ServiceData();
        if (transferList == null || transferList.size() == Constant.INT_0) {
            ret.setCode(new RetCode(RetCode.SUCCESS_CODE, MessageId.RE_WOREK_ERROR));
            return ret;
        }
        List<PsTask> retrunlist = psTaskRepository.searchForProdPlan(transferList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        ret.setBo(retrunlist);
        return ret;
    }

    @Override
    public int updateTaskMaintainInfo(PsTask entity) {
        // 更新当前任务批次信息
        return psTaskRepository.updateTaskMaintainInfo(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("标模任务拆分")
    public int splitTaskProcess(PsTaskDTO taskDto) throws Exception {
        validateRequestParam(taskDto);
        PsTaskDTO queryDto = new PsTaskDTO();
        queryDto.setTaskNo(taskDto.getTaskNo());
        queryDto.setSourceSys(taskDto.getSourceSys());
        List<PsTask> retList = psTaskRepository.selectPsTaskList(taskDto.getTaskNo(), taskDto.getSourceSys());
        if (CollectionUtils.isEmpty(retList)) {
            //系统找不到要拆分数据，数据可能已经被删除
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPLIT_TASK_NO_DATA, new String[]{taskDto.getTaskNo(), taskDto.getSourceSys()});
        }
        int maxValue = 0;
        PsTask preEntity = null;
        for (PsTask dto : retList) {
            String prodplanId = dto.getProdplanId();
            maxValue = getMaxValue(prodplanId, maxValue);
            String dtoKeyStr = dto.getTaskNo() + Constant.UNDER_LINE + dto.getProdplanId();
            String taskDtoKeyStr = taskDto.getTaskNo() + Constant.UNDER_LINE + taskDto.getProdplanId();
            if (taskDtoKeyStr.equals(dtoKeyStr)) {
                preEntity = dto;
                taskDto.setTaskId(dto.getTaskId());
            }
        }
        if (preEntity == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPLIT_TASK_NO_DATA, new String[]{taskDto.getTaskNo(), taskDto.getSourceSys()});
        }
        boolean isMes = StringUtils.equalsIgnoreCase(Constant.SYS_TYPE_WMES, preEntity.getSourceSys());
        validateSplitForAlibabaTask(preEntity, isMes);
        Date curDate = new Date();
        PsTask entity = new PsTask();
        //生成拆分任务
        convertPsTask(taskDto, maxValue, preEntity, curDate, entity);
        int count = psTaskRepository.insertSelective(entity);
        count += changSplitTaskInfo(taskDto, taskDto.getEmpNo(), curDate);
        return count;
    }

    private void validateSplitForAlibabaTask(PsTask psTask, boolean isMes) {
        if (isMes && isAlibabaTask(psTask.getItemNo())){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPLIT_NOT_ALLOWED, new String[]{psTask.getTaskNo()});
        }
    }

    /**
     * @param taskDto
     */
    private void validateRequestParam(PsTaskDTO taskDto) {
        if (StringUtils.isBlank(taskDto.getTaskNo()) || StringUtils.isBlank(taskDto.getSourceSys())
                || taskDto.getTaskQty().compareTo(taskDto.getAttribute9()) < Constant.INT_0) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }

    }

    /**
     * @param taskDto
     * @param maxValue
     * @param preEntity
     * @param curDate
     * @param entity
     */
    private void convertPsTask(PsTaskDTO taskDto, int maxValue, PsTask preEntity, Date curDate, PsTask entity) {
        BeanUtils.copyProperties(preEntity, entity);
        entity.setTaskId(UUID.randomUUID().toString());
        maxValue = maxValue + Constant.INT_1;
        String maxValueStr = maxValue < Constant.INT_10 ? Constant.STR_0 + maxValue : String.valueOf(maxValue);
        entity.setProdplanId(entity.getTaskNo() + Constant.STR_V + maxValueStr);
        entity.setEnabledFlag(Constant.FLAG_Y);
        entity.setTaskQty(taskDto.getAttribute9());
        entity.setOriginalTask(preEntity.getOriginalTask());
        entity.setProductType(preEntity.getProductType());
        entity.setReworkSource(preEntity.getReworkSource());
        entity.setOriginalTask(preEntity.getOriginalTask());
        entity.setProductType(preEntity.getProductType());
        entity.setReworkSource(preEntity.getReworkSource());
        entity.setAttribute3(taskDto.getAttribute3());
        entity.setAssemblyRemark(taskDto.getAssemblyRemark());
        entity.setAttribute5(null);
        entity.setAttribute6(null);
        entity.setAttribute10(null);
        entity.setAttribute9(null);
        entity.setFactoryId(taskDto.getFactoryId());
        entity.setCreateBy(taskDto.getEmpNo());
        entity.setLastUpdatedBy(taskDto.getEmpNo());
        entity.setCreateDate(curDate);
        entity.setLastUpdatedDate(curDate);
        entity.setToGrantDate(preEntity.getToGrantDate());
        entity.setGrantTime(preEntity.getGrantTime());
        entity.setEntityId(new BigDecimal(factoryConfig.getCommonEntityId()));
    }

    /**
     * 更新原来数量
     *
     * @param taskDto
     * @param empNo
     * @param curDate
     * @return
     */
    public int changSplitTaskInfo(PsTaskDTO taskDto, String empNo, Date curDate) {
        PsTask psTask = new PsTask();
        BeanUtils.copyProperties(taskDto, psTask);
        psTask.setLastUpdatedBy(empNo);
        psTask.setLastUpdatedDate(curDate);
        psTask.setSplitFlag(Constant.FLAG_Y);
        return psTaskRepository.updateByPrimaryKeySelective(psTask);
    }

    /**
     * 获取子批次序列号
     *
     * @param prodplanId
     * @param maxValue
     * @return
     */
    private int getMaxValue(String prodplanId, int maxValue) {
        if (prodplanId != null) {
            String[] prodplanIdArr = prodplanId.split("-V");
            if (prodplanIdArr.length == Constant.INT_2) {
                int tmpMaxValue = Integer.parseInt(prodplanIdArr[1]);
                if (tmpMaxValue > maxValue) {
                    maxValue = tmpMaxValue;
                }
            }
        }
        return maxValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData updatePsTaskForFactory(PsTask psTask) throws Exception {
        if (StringUtils.isBlank(psTask.getSourceSys())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_SOURCE_CANNOT_BE_EMPTY);
        }
        //单板任务
        boolean isStep = StringUtils.equalsIgnoreCase(Constant.SYS_TYPE_STEP, psTask.getSourceSys());
        if (StringUtils.isBlank(psTask.getTaskNo()) || StringUtils.isBlank(psTask.getProdplanId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_IS_NULL);
        }
        PsTask psTaskModel = psTaskRepository.selectPsTaskByTaskNo(psTask.getTaskNo(), psTask.getProdplanId());
        if (psTaskModel.getTaskQty() == null || BigDecimal.ZERO.compareTo(psTaskModel.getTaskQty()) >= Constant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_QTY_IS_NULL);
        }
        if ((!Constant.TASK_STATUS_TOGRANT.equals(psTaskModel.getTaskStatus())) && !isStep) {
            String[] params = {psTask.getTaskNo()};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASKSTATUS_NOT_ALLOW_TO_MODIFY, params);
        }
        validateForAlibabaTask(psTaskModel, !isStep);
        //更新中心工厂数据
        psTaskModel.setGrantTime(new Date());
        psTaskModel.setFactoryId(psTask.getFactoryId());
        psTaskModel.setLastUpdatedBy(psTask.getLastUpdatedBy());
        psTaskModel.setGrantBy(psTask.getGrantBy());
        psTaskModel.setAttribute3(psTask.getAttribute3());
        psTaskModel.setAssemblyRemark(psTask.getAssemblyRemark());
        //工厂ID为空根据组织找工厂ID并写入
        setSelectFactoryId(psTask, psTaskModel);
        if (!isStep) {
            Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
            requestParam.put(FIELD_LOOKUP_TYPE, Constant.LOOK_UP_CODE_1245);
            List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
            //标模任务选择了本地工厂 校验是否和组织id对应的工厂id一致 外协工厂不校验
            if (checkFactoryIdIsEqual(psTask, psTaskModel, valuesList)) {
                //写入本地工厂
                syncPsTaskToSys(psTaskModel, valuesList, Constant.OPERATE_GRANT, isStep);
            }
        }
        //写入本地PsTask的状态为空，中心工厂状态为已发放
        psTaskModel.setTaskStatus(Constant.TASK_STATUS_GRANTED);
        Integer result = psTaskRepository.updatePsTaskForFactory(psTaskModel);
        //单板任务
        if (isStep) {
            //单板如果存在技改单以及锁定单，需要一起迁移到发放工厂，并且取消发放任务
            this.dealForExistTecinalOrLockInfo(psTask);
            updatePsTaskToSiteFactoryForStep(psTask, psTaskModel);

        }
        return ServiceDataUtil.getSuccess(result);
    }

    private void validateForAlibabaTask(PsTask psTask, boolean isMes) {
        /* Started by AICoder, pid:aa7e437d54r7b9b1493109cfa08d3816d3f3a94a */
        if (Constant.TASK_STATUS_TOGRANT.equals(psTask.getTaskStatus()) && isMes) {
            if (isAlibabaTask(psTask.getItemNo())){
                List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.listByTaskNos(Collections.singletonList(psTask.getTaskNo()));
                psTaskExtendedDTOList = psTaskExtendedDTOList.stream()
                        .filter(item -> Constants.FLAG_Y.equals(item.getFixBomComplete()))
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(psTaskExtendedDTOList)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_INCOMPLETE, new String[]{psTask.getTaskNo()});
                }
            }
        }
        /* Ended by AICoder, pid:aa7e437d54r7b9b1493109cfa08d3816d3f3a94a */
    }

    private boolean isAlibabaTask(String itemNo) {
        Map<String, Boolean> resultMap = judgeAlibabaTask(Collections.singletonList(itemNo));
        return resultMap.getOrDefault(itemNo, false);
    }

    @Override
    public Map<String, Boolean> judgeAlibabaTask(Collection<String> itemNos){
        if (!pdmApiOpen){
            return Collections.emptyMap();
        }
        itemNos = itemNos.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(itemNos)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_CODE_CAN_NOT_BE_EMPTY);
        }
        Map<String, String> itemNoToCustomerNumberMap = pdmRemoteService.getItemNoToCustomerNumberMap(itemNos);
        Collection<String> meanings = sysLookupValuesService.getMeaningsByType(LOOKUP_TYPE_1004115.toString());
        return itemNos.stream().collect(Collectors.toMap(Function.identity(), itemNo -> meanings.contains(itemNoToCustomerNumberMap.get(itemNo))));
    }

    @Override
    public List<EntityQueryDTO> queryPlannedTaskQty(List<EntityQueryDTO> entityQueryDTOS) {
        List<EntityQueryDTO> entityQueryDTOList = psTaskRepository.queryTaskQty(entityQueryDTOS, INT_10, null);
        Map<String, EntityQueryDTO> entityQueryDTOMap = IterUtil.toMap(entityQueryDTOList, this::getKey);
        entityQueryDTOS.forEach(item -> Opt.ofNullable(entityQueryDTOMap.get(getKey(item)))
                .ifPresentOrElse(
                        entityQueryDTO -> {
                            item.setQty(entityQueryDTO.getQty());
                            item.setPlanned(true);
                        },
                        () -> item.setPlanned(false)
                ));
        return entityQueryDTOS;
    }

    private String getKey(EntityQueryDTO entityQueryDTO) {
        return StrJoiner.of(StrUtil.UNDERLINE).append(entityQueryDTO.getEntityNo()).append(entityQueryDTO.getStockOrgId()).toString();
    }

    /**
     * 单板如果存在技改单以及锁定单，需要一起迁移到发放工厂，并且取消发放任务
     *
     * @param psTask
     * @throws Exception
     */
    public void dealForExistTecinalOrLockInfo(PsTask psTask) throws Exception {
        if (!StringUtils.equals(psTask.getNeedMoveTenicAndLock(), Constant.FLAG_Y)) {
            return;
        }
        //单板如果存在技改单以及锁定单，需要一起迁移到发放工厂，并且取消发放任务
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = this.getTechnicalAndLockInfoByProdplanId(psTask.getProdplanId());
        if (technicalAndLockInfoDTO == null) {
            return;
        }
        //校验存在普通锁定单
        verifyExistLockBill(technicalAndLockInfoDTO);

        BigDecimal bigFactoryId = psTask.getFactoryId();
        if (bigFactoryId == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL);
        }
        technicalAndLockInfoDTO.setToFactoryId(bigFactoryId.toString());
        if (StringUtils.equals(technicalAndLockInfoDTO.getFormFactoryId(), bigFactoryId.toString())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_SAME);
        }

        this.dealForExistTecinalOrLockInfo(psTask, technicalAndLockInfoDTO);
    }

    public void dealForExistTecinalOrLockInfo(PsTask psTask, TechnicalAndLockInfoDTO technicalAndLockInfoDTO) throws Exception {
        technicalAndLockInfoDTO.setEmpNo(psTask.getLastUpdatedBy());
        //迁移技改信息
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.moveTechnicalAndLockInfoByProdplanId);
        String msg = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(technicalAndLockInfoDTO), headerParamsMap);
        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId(psTask.getProdplanId());
        psTaskDTO.setTaskNo(psTask.getTaskNo());
        psTaskDTO.setSourceSys(psTask.getSourceSys());
        psTaskDTO.setFromFactoryId(technicalAndLockInfoDTO.getFormFactoryId());
        this.cancelGrant(psTaskDTO);
    }

    /**
     * 校验存在普通锁定单
     *
     * @param technicalAndLockInfoDTO
     */
    public void verifyExistLockBill(TechnicalAndLockInfoDTO technicalAndLockInfoDTO) {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOKUP_TYPE_6736);
        // 查询3410数据字典获取数据类型对应配置
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        if (CollectionUtils.isEmpty(valuesList)) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new String[]{Constant.LOOKUP_TYPE_6736});
        }
        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = technicalAndLockInfoDTO.getBarcodeLockHeadEntityDTOList();
        if (CollectionUtils.isEmpty(barcodeLockHeadEntityDTOList)) {
            return;
        }
        List<BarcodeLockHeadEntityDTO> existLockList = new ArrayList<>();
        for (BarcodeLockHeadEntityDTO barcodeLockHeadEntityDTO : barcodeLockHeadEntityDTOList) {
            for (SysLookupTypesDTO sysLookupTypesDTO : valuesList) {
                if (StringUtils.startsWith(barcodeLockHeadEntityDTO.getBillNo(), sysLookupTypesDTO.getLookupMeaning())) {
                    existLockList.add(barcodeLockHeadEntityDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(existLockList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BATCH_HAS_A_LOCK_ORDER, new String[]{StringUtils.join(existLockList.stream().map(e -> e.getBillNo()).distinct().collect(Collectors.toList()), COMMA)});
        }
    }

    /**
     * 设置工厂id 如果页面选择了工厂id
     *
     * @param psTask
     * @param psTaskModel
     */
    public void setSelectFactoryId(PsTask psTask, PsTask psTaskModel) {
        if (psTask == null || psTaskModel == null) {
            return;
        }
        if (psTask.getFactoryId() != null) {
            psTaskModel.setSelectedFactoryId(psTask.getFactoryId());
        }
    }

    /**
     * 校验所选工厂是否任务组织id对应的可发放工厂
     *
     * @param psTask
     * @param psTaskModel
     * @param valuesList
     * @throws MesBusinessException
     */
    public boolean checkFactoryIdIsEqual(PsTask psTask, PsTask psTaskModel, List<SysLookupTypesDTO> valuesList) {
        if (psTask.getFactoryId() != null) {
            //如果选择工厂不是数据字典配置，说明是外协工厂，不处理
            List<SysLookupTypesDTO> matchSysLookupTypesList = valuesList.stream().filter(value ->
                    StringUtils.equals(value.getLookupMeaning(), psTask.getFactoryId().toString())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchSysLookupTypesList)) {
                return false;
            }
            //如果不是外协工厂，需校验组织ID是否是配置范围
            List<SysLookupTypesDTO> tempList = matchSysLookupTypesList.stream().filter(value -> psTaskModel.getOrgId() != null
                    && value.getRemarkV().contains(String.valueOf(psTaskModel.getOrgId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SELECTED_FACTORY_NOT_THE_FACTORY_CORRESPONDING_OF_TASK);
            }
        }
        return true;
    }


    /**
     * 校验所有工厂ps_task表是否已存在此任务
     */
    private void checkTaskExist(List<SysLookupTypesDTO> factoryList, String taskNo) throws Exception {
        SysLookupTypesDTO factoryInfo = getTaskExist(factoryList, taskNo);
        if (factoryInfo != null) {
            String[] params = {taskNo, factoryInfo.getDescriptionChinV()};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_HAS_BEEN_ISSUED_TO_THE_LOCAL_FACTORY, params);
        }
    }

    /**
     * 查找所有工厂ps_task表是否已存在此任务
     */
    private SysLookupTypesDTO getTaskExist(List<SysLookupTypesDTO> factoryList, String taskNo) throws Exception {
        //根据任务所属factoryId查询所有工厂ps_task表是否已存在此任务，如果已经存在则报错“XX任务已发放至本地工厂，请确认”
        for (SysLookupTypesDTO sysLookupTypesDTO : factoryList) {
            PsTask psTaskExist = centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(taskNo, sysLookupTypesDTO.getLookupMeaning());
            if (psTaskExist != null) {
                return sysLookupTypesDTO;
            }
        }
        return null;
    }

    /**
     * 更新本地工厂数据  单板任务
     *
     * @param psTask
     * @param psTaskModel
     * @throws Exception
     */
    public void updatePsTaskToSiteFactoryForStep(PsTask psTask, PsTask psTaskModel) throws Exception {
        String factoryId = psTask.getFactoryId().toString();
        Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, psTask.getFactoryId().toString());
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE, psTask.getFactoryId().toString());
        psTaskModel.setOperateType(Constant.OPERATE_GRANT);
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put(FIELD_LOOKUP_TYPE, Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        if (CollectionUtils.isEmpty(valuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_CODE_1245});
        }
        SysLookupTypesDTO sysLookupTypesDTO = valuesList.stream().filter(p -> com.alibaba.druid.util.StringUtils.equals(factoryId, p.getLookupMeaning())).findFirst().orElse(null);
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_NOT_CONFIGURED_IN_1245_DATA_DICTIONARY);
        }
        checkTaskExist(valuesList, psTask.getTaskNo());
        planScheduleRemoteService.sendCenterPsTaskToSys(sysLookupTypesDTO.getRemark(), psTaskModel, requestHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelGrant(PsTaskDTO taskDto) throws Exception {

        if (StringUtils.isBlank(taskDto.getSourceSys())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_SOURCE_CANNOT_BE_EMPTY);
        }
        //单板任务
        boolean isStep = StringUtils.equalsIgnoreCase(Constant.SYS_TYPE_STEP, taskDto.getSourceSys());
        PsTask psTaskModel = psTaskRepository.selectPsTaskByTaskNo(taskDto.getTaskNo(), taskDto.getProdplanId());
        if (psTaskModel == null || (!Constant.TASK_STATUS_GRANTED.equals(psTaskModel.getTaskStatus()) && !isStep)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NOT_EXIST_OR_NOT_ALLOW_TO_MODIFY);
        }
        //查询分工厂是否已经执行添加缓冲池等后续操作
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        //单板只查询对应分工厂
        if (isStep) {
            valuesList = getSysLookupTypesDTOS(taskDto, psTaskModel, valuesList);
        }
        for (SysLookupTypesDTO lookupTypesDto : valuesList) {
            Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, lookupTypesDto.getLookupMeaning());
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE, lookupTypesDto.getLookupMeaning());
            List<PsWorkOrderBasicDTO> workOrderBasicList = PlanScheduleRemoteService.getWorkOrderInfo(lookupTypesDto.getRemark(),
                    taskDto.getProdplanId(), requestHeader);
            if (CollectionUtils.isNotEmpty(workOrderBasicList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODPLANID_IS_PRODUCTION, new String[]{taskDto.getProdplanId()});
            }
        }
        int result = updatePsTask(taskDto, psTaskModel);
        //更新本地工厂
        syncPsTaskToSys(psTaskModel, valuesList, Constant.OPERATE_CANCEL, isStep);
        return result;
    }

    public int updatePsTask(PsTaskDTO taskDto, PsTask psTaskModel) {
        int result = NUM_ZERO;
        if (StringUtils.isEmpty(taskDto.getFromFactoryId())) {
            //更新中心工厂状态
            psTaskModel.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
            psTaskModel.setGrantBy(Constant.STRING_EMPTY);
            psTaskModel.setGrantTime(null);
            psTaskModel.setFactoryId(new BigDecimal(Constant.FACTORY_ID_CENTER));
            result = psTaskRepository.updateByPrimaryKey(psTaskModel);
        }
        return result;
    }

    /**
     * 根据任务工厂id获取数据字典1245配置
     *
     * @param taskDto
     * @param psTaskModel
     * @param valuesList
     * @return
     * @throws Exception
     */
    private List<SysLookupTypesDTO> getSysLookupTypesDTOS(PsTaskDTO taskDto, PsTask psTaskModel, List<SysLookupTypesDTO> valuesList) throws Exception {
        // 如果发放到的工厂id为空或者51，从本地工厂依次查找
        if (psTaskModel.getFactoryId() == null || Constant.FACTORY_ID_51.equals(psTaskModel.getFactoryId())) {
            SysLookupTypesDTO factoryInfo = getTaskExist(valuesList, taskDto.getTaskNo());
            if (factoryInfo != null) {
                psTaskModel.setFactoryId(new BigDecimal(factoryInfo.getLookupMeaning()));
            }
        }
        //加开关
        if (StringUtils.equals(getSwitchStr(), Constant.FLAG_Y) && datawbRemoteService.validateSubmitStatus(taskDto.getProdplanId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_BATCH_HAS_GENERATED_A_NESTING_LIST, new Object[]{taskDto.getProdplanId()});
        }
        String factoryId = getFactoryId(taskDto, psTaskModel);
        valuesList = valuesList.stream().filter(p -> com.alibaba.druid.util.StringUtils.equals(factoryId, p.getLookupMeaning())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(valuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_CURRENT_BATCH_IS_ISSUED);
        }
        return valuesList;
    }

    public String getFactoryId(PsTaskDTO taskDto, PsTask psTaskModel) {
        String factoryId = taskDto.getFromFactoryId();
        if (StringUtils.isEmpty(factoryId)) {
            factoryId = psTaskModel.getFactoryId() == null ? "" : psTaskModel.getFactoryId().toString();
        }
        return factoryId;
    }

    /**
     * 获取开关 是否调spm校验套料单
     *
     * @return
     */
    public String getSwitchStr() {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_6672);
        requestParam.put("lookupCode", Constant.LOOK_UP_CODE_6672005);
        List<SysLookupTypesDTO> sysLookupTypesDTOS = sysLookupTypesRepository.getList(requestParam);
        String switchStr = CollectionUtils.isEmpty(sysLookupTypesDTOS) || StringUtils.isEmpty(sysLookupTypesDTOS.get(NumConstant.NUM_ZERO).getLookupMeaning()) ? Constant.FLAG_N : sysLookupTypesDTOS.get(NumConstant.NUM_ZERO).getLookupMeaning();
        return switchStr;
    }

    /**
     * @param psTaskModel
     * @param valuesList
     * @param operateGrant
     * @throws Exception
     */
    private void syncPsTaskToSys(PsTask psTaskModel, List<SysLookupTypesDTO> valuesList, String operateGrant, boolean isStep) throws Exception {
        List<SysLookupTypesDTO> matchSysLookupTypesList = valuesList;
        if (psTaskModel.getSelectedFactoryId() != null && !isStep) {
            matchSysLookupTypesList = valuesList.stream().filter(value -> psTaskModel.getSelectedFactoryId() != null
                    && value.getLookupMeaning().equals(String.valueOf(psTaskModel.getSelectedFactoryId()))).collect(Collectors.toList());
        } else if (!isStep) {
            matchSysLookupTypesList = valuesList.stream().filter(value -> psTaskModel.getOrgId() != null
                    && value.getRemarkV().contains(String.valueOf(psTaskModel.getOrgId()))).collect(Collectors.toList());
            this.setFactoryId(psTaskModel, matchSysLookupTypesList);
        }
        if (CollectionUtils.isEmpty(matchSysLookupTypesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_NOT_FIND);
        }
        //写入本地工厂
        for (SysLookupTypesDTO lookupTypesDto : matchSysLookupTypesList) {
            Map<String, String> requestHeader = MESHttpHelper.getHttpRequestHeader();
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, lookupTypesDto.getLookupMeaning());
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE, lookupTypesDto.getLookupMeaning());
            psTaskModel.setOperateType(operateGrant);
            planScheduleRemoteService.sendCenterPsTaskToSys(Constant.HTTP + lookupTypesDto.getAttribute1(), psTaskModel, requestHeader);
        }
    }

    public void setFactoryId(PsTask psTaskModel, List<SysLookupTypesDTO> matchSysLookupTypesList) {
        if (CollectionUtils.isEmpty(matchSysLookupTypesList)) {
            return;
        }
        String factoryId = matchSysLookupTypesList.get(NUM_ZERO).getLookupMeaning();
        if(StringUtils.isEmpty(factoryId)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{Constant.LOOK_UP_CODE_1245});
        }
        psTaskModel.setFactoryId(new BigDecimal(factoryId));
    }

    /**
     * 自动化测试增加实体数据
     *
     * @param
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertAutoTest(String empNo, String factoryId, PsTask psTask) {
        String prodplanIdStr = this.judgeProdplanId(PRODPLAN_ID);
        // 插入任务信息
        PsTask record = new PsTask();
        record.setTaskId(UUID.randomUUID().toString());
        record.setTaskNo(CommonUtils.getRandom(NumConstant.NUM_TWELVE, NumConstant.NUM_NINE, NumConstant.NUM_ONE) + TASK_NO_SUFFIX);
        record.setItemNo(psTask.getItemNo());
        record.setItemName(ITEM_NAME);
        record.setTaskQty(TASK_QTY);
        record.setProdplanNo(psTask.getItemNo());
        record.setProdplanId(prodplanIdStr);
        record.setEntityId(ENTITY_ID);
        record.setFactoryId(new BigDecimal(factoryId));
        record.setIsLead(psTask.getIsLead());
        record.setLeadFlag(psTask.getLeadFlag());
        record.setCreateBy(empNo);
        record.setLastUpdatedBy(empNo);
        record.setTaskStatus(Constant.TASK_STATUS);
        record.setSourceSys(SOURCE_SYS);
        record.setOrgId(psTask.getOrgId());
        psTaskRepository.insertSelective(record);

        return prodplanIdStr;
    }

    /**
     * 批次号从1000000开始累加1，递归判断批次号是否存在
     *
     * @param prodplanId
     **/
    private String judgeProdplanId(int prodplanId) {
        PsTask entity = new PsTask();
        Map<String, Object> map = new HashMap<>();
        boolean exist = true;
        List<PsTask> list = null;
        while (exist) {
            map.put("prodplanId", Constant.PRODPLAN_ID_PREFIX + prodplanId);
            list = psTaskRepository.getPsTaskList(map);
            if (!org.springframework.util.CollectionUtils.isEmpty(list) && list.size() != NumConstant.NUM_ZERO) {
                prodplanId++;
            } else {
                exist = false;
            }
        }
        return Constant.PRODPLAN_ID_PREFIX + prodplanId;
    }

    /**
     * 根据批次查询所派发工厂
     *
     * @return java.lang.String
     * @Author: 10307315陈俊熙
     * @date 2021/9/9 下午2:09
     */
    @Override
    public List<PsTaskDTO> getFactoryIdByProdplanId(List<String> prodplanIds) throws Exception {
        List<List<String>> tempList = CommonUtils.splitList(prodplanIds, Constant.INT_500);
        List<PsTaskDTO> resultList = new ArrayList<>();
        for (List<String> entityList : tempList) {
            resultList.addAll(psTaskRepository.getFactoryIdByProdplanId(entityList));
        }
        return resultList;
    }

    /**
     * 根据料单查询任务信息
     */
    @Override
    public List<PsTaskDTO> getPsTaskByItemNoList(List<String> itemNoList) {
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemNoList)) {
            return psTaskDTOList;
        }
        List<List<String>> lists = CommonUtils.splitList(itemNoList, Constant.INT_50);
        for (List<String> list :lists) {
            List<PsTaskDTO> tempList = psTaskRepository.getPsTaskByItemNoList(list);
            if (!CollectionUtils.isEmpty(tempList)) {
                psTaskDTOList.addAll(tempList);
            }
        }
        return psTaskDTOList;
    }

    /**
     * 根据批次查询任务信息
     */
    @Override
    public List<PsTaskDTO> getPsTaskByProdplanIdList(List<String> prodplanIdList) {
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodplanIdList)) {
            return psTaskDTOList;
        }
        for (List<String> prodplanIdTempList : CommonUtils.splitList(prodplanIdList, Constant.BATCH_SIZE)) {
            List<PsTaskDTO> tempList = psTaskRepository.getPsTaskByProdplanIdList(prodplanIdTempList);
            if (!CollectionUtils.isEmpty(tempList)) {
                psTaskDTOList.addAll(tempList);
            }
        }
        return psTaskDTOList;
    }

    /***
     *
     *@Author: 10307315陈俊熙
     *@date 2021/10/9 上午10:48
     *@return java.util.Date
     */
    private Date getFinalDeliveryDate(Date date) {
        Date today = new Date();
        if (StringHelper.isEmpty(date)) {
            return today;
        }
        return today.compareTo(date) >= 0 ? today : date;
    }


    /**
     * 获取标模任务状态
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    @RecordLogAnnotation("获取标模任务状态")
    public List<StandardModelTaskDTO> getStandardModelTaskStatus(HttpServletRequest request,
                                                                 GetStandardModelTaskStatusDTO dto) {
        String taskNo = dto.getTaskNo();
        String beginTime = dto.getBeginTime();
        String endTime = dto.getEndTime();
        String infoJIT = dto.getInfoJIT();
        Integer page = dto.getPage();
        Integer rows = dto.getRows();
        //校验开始结束时间和任务号，必须有一个不为空。
        checkTaskNoAndTime(taskNo, beginTime, endTime);
        //检验开始结束时间格式,如果正确则进行转化。
        checkBeginTimeAndEndTime(beginTime, endTime);
        //获取页码和行数。
        HashMap<String, Integer> map = getPageAndRows(request, page, rows);
        Integer pageNew = map.get("page");
        Integer rowsNew = map.get("rows");
        if (rowsNew > Constant.BATCH_SIZE_500) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        //校验结束，开始查询。
        Page<StandardModelTaskDTO> pageInfo = new Page<>(pageNew, rowsNew);
        pageInfo.setParams(dto);
        pageInfo.setHaveCountSql(false);
        List<StandardModelTaskDTO> standardModelTaskDTOS = psTaskRepository.getStandardModelTaskStatus(pageInfo);
        return standardModelTaskDTOS;
    }

    /**
     * iFIS任务信息获取
     */
    @Override
    public List<IfisTaskDTO> getPsTaskForIfis(String taskNo, String prodplanId) {
        if (StringUtils.isEmpty(taskNo) && StringUtils.isEmpty(prodplanId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASKNO_PRODPLAN_IS_NULL);
        }
        Map<String, Object> map = new HashMap<>(Constant.INT_2);
        map.put("prodplanId", prodplanId);
        map.put("taskNo", taskNo);
        List<PsTask> psTasks = psTaskRepository.getPsTaskList(map);
        if (CollectionUtils.isEmpty(psTasks)) {
            return new ArrayList<>();
        }
        // lead_flag对应字典
        Map<String, String> leadMap = getLeadMap();
        List<IfisTaskDTO> dtoList = new ArrayList<>();
        for (PsTask psTask : psTasks) {
            IfisTaskDTO ifisTaskDTO = IfisTaskAssembler.toDTO(psTask);
            dtoList.add(ifisTaskDTO);
            String leadInfo;
            // "STEP"是单板 lead_flag 需字典转换
            // "WMS"是整机 is_lead 直接取字段
            if (Constant.SYS_TYPE_STEP.equals(psTask.getSourceSys())) {
                leadInfo = leadMap.get(psTask.getLeadFlag());
            } else {
                leadInfo = psTask.getIsLead();
            }
            ifisTaskDTO.setLeadInfo(leadInfo);
        }
        return dtoList;
    }

    /**
     * 获取lead_flag对应字典
     */
    private Map<String, String> getLeadMap() {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put(FIELD_LOOKUP_TYPE, Constant.LOOK_UP_CODE_1036);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        Map<String, String> leadMap = new HashMap<>();
        for (SysLookupTypesDTO sysLookupTypesDTO : valuesList) {
            leadMap.put(sysLookupTypesDTO.getLookupMeaning(), sysLookupTypesDTO.getDescriptionChinV());
        }
        return leadMap;
    }

    /**
     * 获取lead_flag对应字典
     */
    private Map<String, String> getPcbVersionMap(List<SpecifiedPsTaskVO> list) {
        //pcbVersion版本数据字典
        List<String> itemNoList = new ArrayList<>();
        for (SpecifiedPsTaskVO specifiedPsTaskVO : list) {
            itemNoList.add(specifiedPsTaskVO.getItemNo());
        }
        List<SpecifiedPsTaskVO> specifiedPsTaskVOList = bBomHeaderRepository.selectBBomHeaderByItemNoList(itemNoList);
        Map<String, String> getPcbVersionMap = new HashMap<>();
        for (SpecifiedPsTaskVO specifiedPsTaskVO : specifiedPsTaskVOList) {
            getPcbVersionMap.put(specifiedPsTaskVO.getProductCode(), specifiedPsTaskVO.getPcbVersion());
        }
        return getPcbVersionMap;
    }

    /**
     * 校验时间格式
     *
     * @param beginTime
     * @param endTime
     * @throws Exception
     */
    private void checkBeginTimeAndEndTime(String beginTime, String endTime) {
        Date beginDate = null;
        Date endDate = null;
        if (StringHelper.isEmpty(beginTime)) {
            return;
        }
        beginDate = DateUtil.convertStringToDate(beginTime, DateUtil.DATE_FORMATE_FULL);
        endDate = DateUtil.convertStringToDate(endTime, DateUtil.DATE_FORMATE_FULL);
        if (beginDate == null || endDate == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TIME_FORMAT_ERROR);
        }
        if (endDate.before(beginDate)) {
            String[] params = {"beginTime is not before endTime "};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID, params);
        }
    }

    /**
     * 校验任务号和开始结束时间至少一个不为空。
     *
     * @param taskNo
     * @param beginTime
     * @param endTime
     * @throws Exception
     */
    private void checkTaskNoAndTime(String taskNo, String beginTime, String endTime) {
        if (StringHelper.isEmpty(beginTime) && StringHelper.isNotEmpty(endTime)) {
            String[] params = {"beginTime is empty, but endTime is not empty"};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID, params);
        }
        if (StringHelper.isNotEmpty(beginTime) && StringHelper.isEmpty(endTime)) {
            String[] params = {"endTime is empty, but beginTime is not empty"};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID, params);
        }
        if (StringHelper.isEmpty(taskNo) && StringHelper.isEmpty(beginTime)) {
            String[] params = {"beginTime(endTime) and taskNo, one of which must not be empty"};
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID, params);
        }
        return;
    }

    /**
     * 校验并获取页码和行数
     *
     * @param request
     * @return
     */
    private HashMap<String, Integer> getPageAndRows(HttpServletRequest request, Integer page, Integer rows) {
        if (StringHelper.isEmpty(request)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        //初始化hashmap大小为2。
        HashMap<String, Integer> map = new HashMap<>(Constant.INT_2);
        if (StringHelper.isEmpty(page) || StringHelper.isEmpty(rows)) {
            try {
                map.put("page", Integer.parseInt(request.getHeader("page")));
                map.put("rows", Integer.parseInt(request.getHeader("rows")));
                return map;
            } catch (Exception e) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
            }
        }
        map.put("page", page);
        map.put("rows", rows);
        return map;
    }


    /**
     * 中试条码测试记录上传
     *
     * @param testInfoList
     * @throws MesBusinessException
     */
    @Override
    public void pilotTestBarcodeTestRecordUpload(List<TestInfoDTO> testInfoList, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(testInfoList)) {
            return;
        }
        List<String> snList = testInfoList.stream().map(e -> e.getSn()).distinct().collect(Collectors.toList());
        List<String> notSevenNumberList = new ArrayList<>();
        Set<String> prodPlanIdSet = new HashSet<>();
        snList.forEach(p -> {
            if (p.length() < NumConstant.NUM_SEVEN) {
                notSevenNumberList.add(p);
            } else {
                prodPlanIdSet.add(p.substring(NumConstant.NUM_ZERO, NumConstant.NUM_SEVEN));
            }
        });

        if (CollectionUtils.isNotEmpty(notSevenNumberList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_BARCODE_LENGTH_IS_LESS_THAN_7_DIGITS, new Object[]{StringUtils.join(notSevenNumberList, Constant.COMMA)});
        }
        if (CollectionUtils.isEmpty(prodPlanIdSet)) {
            return;
        }
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        CommonUtils.splitList(new ArrayList<>(prodPlanIdSet), Constant.BATCH_SIZE_500).forEach(tempList -> {
            List<PsTaskDTO> tempDTOList = psTaskRepository.getFactoryIdByProdplanId(tempList);
            if (CollectionUtils.isNotEmpty(prodPlanIdSet)) {
                psTaskDTOList.addAll(tempDTOList);
            }
        });
        if (CollectionUtils.isEmpty(psTaskDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_FACTORY_ID, new Object[]{StringUtils.join(snList, Constant.COMMA)});
        }
        Map<String, PsTaskDTO> psTaskDTOMap = psTaskDTOList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId,
                e -> e, (newVal, oldVal) -> newVal));
        List<String> notExistTaskList = new ArrayList<>();
        for (TestInfoDTO testInfoDTO : testInfoList) {
            PsTaskDTO psTaskDTO = psTaskDTOMap.get(testInfoDTO.getSn().substring(NumConstant.NUM_ZERO, NumConstant.NUM_SEVEN));
            if (psTaskDTO == null || psTaskDTO.getFactoryId() == null) {
                notExistTaskList.add(testInfoDTO.getSn());
            } else {
                testInfoDTO.setFactoryId(psTaskDTO.getFactoryId().toString());
            }
        }
        if (CollectionUtils.isNotEmpty(notExistTaskList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_FACTORY_ID, new Object[]{StringUtils.join(notExistTaskList, Constant.COMMA)});
        }
        productionmgmtRemoteService.callTheLocalFactoryToWriteTestRecords(testInfoList, empNo);
    }

    /**
     * 导入模板下载
     */
    @Override
    public void exportTaskExcel(HttpServletResponse response) {
        String[] title = Constant.STANDARD_MODEL_TASK_TITLE;
        String sheetName = Constant.STANDARD_MODEL_TASK;
        String fileName = Constant.STANDARD_MODEL_TASK_XLS;
        HSSFWorkbook hssfWorkbook = ExcelUtils.getHSSFWorkbook(sheetName, title, null, null, new HashMap<>());
        HSSFCellStyle style0 = hssfWorkbook.createCellStyle();
        HSSFDataFormat format = hssfWorkbook.createDataFormat();
        style0.setDataFormat(format.getFormat("@"));
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(4, style0);
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(5, style0);
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(6, style0);
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            ExcelUtils.setResponseHeaders(response, fileName);
            hssfWorkbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            LOG.error(ERROR_EXPORT_MODEL, e);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    LOG.error(Constant.ABNORMAL_STREAM_SHUT_DOWN, e);
                }
            }
            if (null != hssfWorkbook) {
                try {
                    hssfWorkbook.close();
                } catch (IOException e) {
                    LOG.error(Constant.ABNORMAL_STREAM, e);
                }
            }
        }
    }

    /**
     * 批量解析
     *
     * @param inputStream 工号
     */
    @Override
    public List<PsTask> uploadStandardModelTaskBatch(InputStream inputStream) {
        ResultData resultData;
        String[] propNames = Constant.STANDARD_MODEL_TASK_PROPNAMES;
        String[] headers = Constant.STANDARD_MODEL_TASK_TITLE;
        resultData = ExcelUtils.resolveExcel(inputStream, PsTask.class, propNames);
        if (!com.zte.common.utils.NumConstant.STRING_ZERO.equals(resultData.getCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXCEL_READ_FAILED);
        }
        if (null == resultData || null == resultData.getData()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        List<String> headerList = new ArrayList(Arrays.asList(headers));
        List<String> arrayList = resultData.getHeader() == null ? Lists.newArrayList() : resultData.getHeader().subList(0, 11);
        if (!arrayList.equals(headerList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TABLE_COLUME_ERROR);
        }
        List<PsTask> excelInfoList = (List<PsTask>) resultData.getData();
        List<PsTask> checkList = this.checkExcelInfo(excelInfoList);
        return checkList;
    }

    public List<PsTask> checkExcelInfo(List<PsTask> excelInfoList) {
        if (CollectionUtils.isEmpty(excelInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        //单次导入不超过500条
        if (excelInfoList.size() > NumConstant.NUM_FIVE_HUNDRED) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_LENGTH_EXCEEDS_500);
        }
        return excelInfoList;
    }

    /**
     * 文件内容校验
     *
     * @param list 参数
     */
    @Override
    public List<PsTask> checkTaskBatch(List<PsTask> list) {
        // 校验必填字段+去除任务-批次字段可能存在的前后空格
        for (PsTask psTask : list) {
            //任务和批次字段不能为空
            if (StringHelper.isEmpty(psTask.getTaskNo()) || StringHelper.isEmpty(psTask.getProdplanId())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASKNO_OR_PRODPLANID_IS_EMPTY);
            }
            //校验装配备注等字段是否任一不为空
            if (this.checkRequiredFields(psTask)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIELDS_CAN_NOT_BE_EMPTY, new String[]{psTask.getTaskNo(), psTask.getProdplanId()});
            }
            psTask.setTaskNo(psTask.getTaskNo().trim());
            psTask.setProdplanId(psTask.getProdplanId().trim());
        }
        //校验是否输入了重复任务-批次组
        List<PsTask> checkRepeatList = new ArrayList<>();
        checkRepeatList.addAll(list);
        checkRepeatList = checkRepeatList.stream().collect(Collectors.collectingAndThen(Collectors
                .toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getTaskNo() + "-" + o.getProdplanId()))), ArrayList::new));
        if (checkRepeatList.size() < list.size()) {
            list.removeAll(checkRepeatList);
            String errorResult = this.errorResult(list);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DUPLICATE_DATE_IN_THE_TABLE, new String[]{errorResult});
        }
        //获取批次列表
        List<String> prodplanIdList = list.stream().filter(item -> StringUtils.isNotBlank(item.getProdplanId()))
                .map(PsTask::getProdplanId)
                .collect(Collectors.toList());
        //获取任务号列表
        List<String> taskNoList = list.stream().filter(item -> StringUtils.isNotBlank(item.getTaskNo()))
                .map(PsTask::getTaskNo)
                .collect(Collectors.toList());
        //查找批次-任务组是否匹配
        PsTask temp = new PsTask();
        temp.setProdplanIdList(prodplanIdList);
        temp.setTaskNoList(taskNoList);
        List<PsTask> statusList = psTaskRepository.queryByTaskNoAndProdplanId(temp);
        //	批量查找表内存在的任务号
        List<String> checkTaskNoList = list.stream().map(e -> e.getTaskNo()).distinct().collect(Collectors.toList());
        List<String> realTaskNoList = psTaskRepository.searchByTaskNoList(checkTaskNoList);
        //	不存在的标模任务号报错
        if (checkTaskNoList.size() > realTaskNoList.size()) {
            checkTaskNoList.removeAll(realTaskNoList);
            String errorResult = checkTaskNoList.stream().map(n -> String.valueOf(n)).collect(Collectors.joining(","));
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_IS_NOT_EXISTS, new String[]{errorResult});
        }
        //两个列表匹配成功的剔出来
        List<PsTask> filterResult = list.stream()
                .filter(f -> statusList.stream()
                        .noneMatch(o -> Objects.equals(f.getTaskNo(), o.getTaskNo()) && Objects.equals(f.getProdplanId(), o.getProdplanId())))
                .collect(Collectors.toList());
        if (filterResult.size() != 0) {
            String errorResult = this.errorResult(filterResult);
            String errorResultTwo = filterResult.stream().filter(item -> StringUtils.isNotBlank(item.getProdplanId()))
                    .map(PsTask::getProdplanId).collect(Collectors.joining(","));
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASKNO_DO_NOT_MATCH_PRODPLANID, new String[]{errorResult, errorResultTwo});
        }
        //查找批次-任务组的任务状态是否有已发放的
        List<PsTask> statusCheckList = statusList.stream().filter(psTask -> Constant.TASK_STATUS_GRANTED.equals(psTask.getTaskStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(statusCheckList)) {
            String errorResult = this.errorResult(statusCheckList);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STATUS_IS_RELEASED, new String[]{errorResult});
        }
        return list;
    }

    /**
     * 校验非必填字段：装配备注、计调备注、要求装配完工时间等是否任一不为空
     *
     * @return
     * @throws
     */
    public boolean checkRequiredFields(PsTask psTask) {

        return StringUtils.isBlank(psTask.getAssemblyRemark()) && StringUtils.isBlank(psTask.getAttribute3())
                && psTask.getAttribute5() == null && psTask.getAttribute6() == null
                && psTask.getAttribute10() == null && StringUtils.isBlank(psTask.getAttribute7())
                && StringUtils.isBlank(psTask.getAttribute8()) && StringUtils.isBlank(psTask.getAttribute11()) && psTask.getToGrantDate() == null;
    }

    public String errorResult(List<PsTask> list) {
        return list.stream().filter(item -> StringUtils.isNotBlank(item.getTaskNo()))
                .map(PsTask::getTaskNo).collect(Collectors.joining(","));
    }

    @Override
    public int batchUpdate(List<PsTask> list, String factoryId, String empNo) throws Exception {
        list.forEach(dto -> {
            dto.setLastUpdatedBy(empNo);
            dto.setFactoryId(new BigDecimal(factoryId));
        });
        int count = psTaskRepository.batchUpdate(list);
        return count;
    }

    @Override
    @AsyncExport(functionName = "任务信息查询导出")
    public void exportTaskQueryTable(HttpServletResponse response, PsTaskExportDTO dto) throws Exception {
        validateParamOfPsTask(dto, false);
        if (!this.setProdListByMBom(dto)) {
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + MpConstant.TASK_INFO_QUERY_EXPORT.TASK_INFO_QUERY_XLSX;
        // 导出类
        BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
        // 获取导出列
        Pair<String[], String[]> pair = this.filterExportTitle(dto);
        // 新建文件
        bigExcelProcesser.createOrAppend(pair.getSecond(), new ArrayList<>(),
                pair.getFirst());

        List<PsTask> resultList = new ArrayList<>();
        Integer page = 0;
        // TL确认
        dto.setRows(String.valueOf(Constant.INT_5000));
        while (page == 0 || resultList.size() >= Constant.INT_5000) {
            page += 1;
            dto.setPage(String.valueOf(page));
            // 分页查询
            resultList = prepareList(dto);
            if (org.springframework.util.CollectionUtils.isEmpty(resultList)) {
                continue;
            }
            setMBom(resultList);
            setEmpName(resultList);
            // 转化数据 11和12 临时存放了转化所需参数
            transferList(resultList, dto.getAttribute11(), dto.getAttribute12());
            // 转换单板任务环保属性
            transferLeadFlag(resultList);
            setMaterialSignInfo(resultList);
            bigExcelProcesser.createOrAppend(pair.getSecond(), resultList,
                    pair.getFirst());
        }
        String filePath = FileUtils.tempPath + System.currentTimeMillis() + Constant.EMAIL_SPLIT + fileName;
        FileUtils.checkFilePath(filePath);
        bigExcelProcesser.saveAsFile(filePath);
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(fileName, filePath);
    }

    @Override
    public void validateParams(PsTaskDTO dto) {
        validateParamOfPsTask(dto, true);
    }

    @Override
    public TaskBrandChangeStatusDTO getBrandChangeStatusByTaskNo(String taskNo) throws Exception {
        // 查询iMES是否产生套料单或调拨单
        TaskBrandChangeStatusDTO brandChangeStatus = psTaskRepository.selectBrandChangeStatusByTaskNo(taskNo);
        if (brandChangeStatus == null || StringUtils.isBlank(brandChangeStatus.getProdplanId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.TASK_NO_IS_NOT_EXISTS, new String[]{taskNo});
        }
        Map<String, Object> lookMap = new HashMap<>();
        lookMap.put(FIELD_LOOKUP_TYPE, Constant.LOOK_UP_1129);
        List<SysLookupValues> list = sysLookupValuesRepository.getList(lookMap);
        if (CollectionUtils.isNotEmpty(list)) {
            List<SysLookupValues> tempList = list.stream().filter(e -> StringUtils.equals(taskNo, e.getLookupMeaning())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tempList)) {
                imesLogService.log(tempList, Constant.APS_WHITE_LIST);
                brandChangeStatus.setModifiable(NumConstant.STR_ONE);
                brandChangeStatus.setReason(Constant.APS_WHITE_LIST);
                return brandChangeStatus;
            }
        }
        if (Constant.STR_0.equals(brandChangeStatus.getModifiable())) {
            return brandChangeStatus;
        }
        // 查询MES是否产生套料单
        if (datawbRemoteService.validateSubmitStatus(brandChangeStatus.getProdplanId())) {
            brandChangeStatus.setModifiable(Constant.STR_0);
            brandChangeStatus.setReason(Constant.TASK_HAS_TRANSFER_ORDER);
        }
        return brandChangeStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateInforExe(AllocationQueryDTO allocationQueryDTO) {
        List<String> sourceTaskList = allocationQueryDTO.getSourceTaskList();
        if (CollectionUtils.isEmpty(sourceTaskList)) {
            return 0;
        }
        List<List<String>> splitList = CommonUtils.splitList(sourceTaskList);
        for (List<String> list : splitList) {
            psTaskRepository.batchUpdateInforExe(list, allocationQueryDTO.getInforExe(), allocationQueryDTO.getLastUpdatedBy());
        }
        return sourceTaskList.size();
    }

    private void validateParamOfPsTask(PsTaskDTO psTaskDTO, boolean queryFlag) {
        if (psTaskDTO == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.CENTER_TASK_QUERY_PARAM_IS_NULL);
        }
        if (queryFlag && (StringUtils.isEmpty(psTaskDTO.getPage()) || Integer.valueOf(psTaskDTO.getPage()) <= 0
                || StringUtils.isEmpty(psTaskDTO.getRows()) || Integer.valueOf(psTaskDTO.getRows()) <= 0)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.CENTER_TASK_QUERY_PAGE_OR_ROW_ILLEGAL);
        }
        // 释放日、预计发放日、发放日、任务号、批次必须输入其一，如果任务号、批次为空，时间段查询，时间必须2年内，BA确认
        if (StringUtils.isEmpty(psTaskDTO.getTaskNo()) && StringUtils.isEmpty(psTaskDTO.getProdplanId())) {
            if (!checkHasTimes(psTaskDTO)) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,
                        MessageId.CENTER_TASK_QUERY_FIVE_PARAMS_NOT_ALL_NULL);
            }
            if (!checkTimesThreshold(psTaskDTO)) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE,
                        MessageId.CENTER_TASK_QUERY_TIME_LARGER_2_YEARS_WHEN_NOT_PROD_AND_TASK);
            }
        }
    }

    /**
     * @Description: 释放日、预计发放日、发放日 任意一个条件满足2年内则返回true
     * @Param: [psTaskDTO]
     * @return: boolean
     * @Author: Saber[10307315]
     * @Date: 2023/4/13 下午5:16
     */
    private boolean checkTimesThreshold(PsTaskDTO psTaskDTO) {
        if (psTaskDTO.getReleaseDateStart() != null && psTaskDTO.getReleaseDateEnd() != null
                && (psTaskDTO.getReleaseDateEnd().getTime() - psTaskDTO.getReleaseDateStart().getTime() <= Constant.TIME_STAMP_365_X_2_DAY)) {
            return true;
        }
        if (psTaskDTO.getToGrantDateStart() != null && psTaskDTO.getToGrantDateEnd() != null
                && (psTaskDTO.getToGrantDateEnd().getTime() - psTaskDTO.getToGrantDateStart().getTime() <= Constant.TIME_STAMP_365_X_2_DAY)) {
            return true;
        }
        if (psTaskDTO.getGrantTimeStart() != null && psTaskDTO.getGrantTimeEnd() != null
                && (psTaskDTO.getGrantTimeEnd().getTime() - psTaskDTO.getGrantTimeStart().getTime() <= Constant.TIME_STAMP_365_X_2_DAY)) {
            return true;
        }
        return false;
    }

    /**
     * @Description: 释放日、预计发放日、发放日 任意一个条件有输入返回true
     * @Param: [psTaskDTO]
     * @return: boolean
     * @Author: Saber[10307315]
     * @Date: 2023/4/13 下午5:07
     */
    private boolean checkHasTimes(PsTaskDTO psTaskDTO) {
        return (psTaskDTO.getReleaseDateStart() != null && psTaskDTO.getReleaseDateEnd() != null)
                || (psTaskDTO.getToGrantDateStart() != null && psTaskDTO.getToGrantDateEnd() != null)
                || (psTaskDTO.getGrantTimeStart() != null && psTaskDTO.getGrantTimeEnd() != null);
    }

    private void transferLeadFlag(List<PsTask> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<SysLookupValues> leadList = sysLookupValuesRepository.selectValuesByType(Integer.parseInt(Constant.LOOK_UP_CODE_1036));
        Map<String, String> leadMap = leadList.stream().collect(Collectors.toMap(k -> k.getLookupMeaning(), v -> v.getDescriptionChin()));
        for (PsTask entity : list) {
            if (StringUtils.isEmpty(entity.getIsLead())) {
                String value = leadMap.get(entity.getLeadFlag()) == null ? entity.getLeadFlag() : leadMap.get(entity.getLeadFlag());
                entity.setIsLead(value);
            }
        }
    }

    /**
     * 获取导出选择列
     *
     * @param dto 请求参数
     * @return 导出列
     */
    private Pair<String[], String[]> filterExportTitle(PsTaskExportDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getTableColumns())) {
            List<String> titles = Arrays.asList(MpConstant.TASK_INFO_QUERY_EXPORT.TITLE);
            List<String> props = Arrays.asList(MpConstant.TASK_INFO_QUERY_EXPORT.PROPS);
            List<Integer> indexList = this.getFilterColumns(props, dto.getTableColumns());
            if (!indexList.isEmpty()) {
                String[] targetProps = new String[indexList.size()];
                String[] targetTitles = new String[indexList.size()];
                for (int i = 0; i < indexList.size(); i++) {
                    targetProps[i] = props.get(indexList.get(i));
                    targetTitles[i] = titles.get(indexList.get(i));
                }
                return Pair.of(targetProps, targetTitles);
            }
        }
        return Pair.of(MpConstant.TASK_INFO_QUERY_EXPORT.PROPS, MpConstant.TASK_INFO_QUERY_EXPORT.TITLE);
    }

    /**
     * 获取用户选择类
     *
     * @param props        属性
     *                     <<<<<<< Updated upstream
     * @param =======
     * @param tableColumns redis 数据
     *                     >>>>>>> Stashed changes
     */
    private List<Integer> getFilterColumns(List<String> props, List<TableColumnsDTO> tableColumns) {
        List<Integer> indexList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tableColumns)) {
            for (int i = 0; i < tableColumns.size(); i++) {
                TableColumnsDTO tableColumnsDTO = tableColumns.get(i);
                if (Boolean.TRUE.equals(tableColumnsDTO.getShowCol())) {
                    int indexOf = props.indexOf(tableColumnsDTO.getProp());
                    if (indexOf > com.zte.springbootframe.constants.NumConstant.NUM_MINUS_ONE) {
                        indexList.add(indexOf);
                    }
                }
            }
        }
        return indexList;
    }

    private void transferList(List<PsTask> list, String produceUnit, String fromImesFlag) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取工厂ID对应的工厂名称
        Map<String, Object> map = new HashMap<>();
        List<CFFactory> factoryList = cFFactoryService.getList(map);
        Map<Integer, String> factoryIdToNameMap = new HashMap<>();
        for (int i = 0; i < factoryList.size(); i++) {
            CFFactory entity = factoryList.get(i);
            if (entity.getFactoryId() == null) {
                continue;
            }
            factoryIdToNameMap.put(entity.getFactoryId(), entity.getFactoryName());
        }

        // 备料状态转换和工厂名称设置
        for (int i = 0; i < list.size(); i++) {
            PsTask task = list.get(i);
            if (Constant.INFOR_EXE_STATUS_1.equals(task.getInforExe())) {
                task.setInforExe(Constant.INFOR_EXE_STATUS_1_MEANING);
            } else if (Constant.INFOR_EXE_STATUS_2.equals(task.getInforExe())) {
                task.setInforExe(Constant.INFOR_EXE_STATUS_2_MEANING);
            } else if (Constant.INFOR_EXE_STATUS_3.equals(task.getInforExe())) {
                task.setInforExe(Constant.INFOR_EXE_STATUS_3_MEANING);
            }
            if (task.getFactoryId() == null) {
                continue;
            }
            if (Constant.FLAG_Y.equals(fromImesFlag)) {
                Integer key = task.getFactoryId().intValue();
                task.setFactoryName(factoryIdToNameMap.get(key));
            } else {
                task.setFactoryName(produceUnit);
            }
        }
    }

    private List<PsTask> prepareList(PsTaskDTO psTaskDTO) {
        Map<String, Object> paramsMap = new HashMap<>(16);
        paramsMap.put("prodplanId", psTaskDTO.getProdplanId());
        paramsMap.put("prodplanIdList", psTaskDTO.getProdplanIdList());
        paramsMap.put("factoryId", psTaskDTO.getFactoryId());
        paramsMap.put("taskNo", psTaskDTO.getTaskNo());
        paramsMap.put("attribute1", psTaskDTO.getAttribute1());
        paramsMap.put("itemNo", psTaskDTO.getItemNo());
        paramsMap.put("releaseDateStart", psTaskDTO.getReleaseDateStart());
        paramsMap.put("releaseDateEnd", psTaskDTO.getReleaseDateEnd());
        paramsMap.put("toGrantDateStart", psTaskDTO.getToGrantDateStart());
        paramsMap.put("toGrantDateEnd", psTaskDTO.getToGrantDateEnd());
        paramsMap.put("grantTimeStart", psTaskDTO.getGrantTimeStart());
        paramsMap.put("grantTimeEnd", psTaskDTO.getGrantTimeEnd());
        paramsMap.put("externalType", psTaskDTO.getExternalType());
        paramsMap.put("sourceSys", psTaskDTO.getSourceSys());
        paramsMap.put("internalType", psTaskDTO.getInternalType());
        paramsMap.put("planner", psTaskDTO.getPlanner());
        // 已经生产SN，N-未生成，Y-已生成，E-错误，S-不需要生成，H-历史数据
        paramsMap.put("inStatus", psTaskDTO.getInStatus());
        paramsMap.put("taskStatus", psTaskDTO.getTaskStatus());
        paramsMap.put("orderField", psTaskDTO.getSort());
        paramsMap.put("order", psTaskDTO.getOrder());
        paramsMap.put("startRow", (Integer.valueOf(psTaskDTO.getPage()) - 1) * (Integer.valueOf(psTaskDTO.getRows())) + 1);
        paramsMap.put("endRow", Integer.valueOf(psTaskDTO.getPage()) * Integer.valueOf(psTaskDTO.getRows()));
        return psTaskRepository.getPsTaskListWithRouteDetail(paramsMap);
    }

    /**
     * 获取redis缓存
     *
     * @param redisKey redis
     */
    @Override
    public Object queryCacheRows(String redisKey) {
        return redisTemplate.opsForValue().get(redisKey);
    }

    /**
     * 保存redis 缓存
     *
     * @param redisCacheDTO 缓存
     */
    @Override
    public void saveCacheRows(RedisCacheDTO redisCacheDTO) {
        if (StringUtils.isBlank(redisCacheDTO.getRedisKey()) || Objects.isNull(redisCacheDTO.getValue())) {
            return;
        }
        redisTemplate.opsForValue().set(redisCacheDTO.getRedisKey(), redisCacheDTO.getValue());
    }

    @Override
    public void deleteCacheRows(String redisKey) {
        redisTemplate.delete(redisKey);
    }

    /**
     * 同步拉取任务到imes
     *
     * @param handlerFlag 手动标识
     * @param msg         kafka 消息
     * @throws Exception 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pullTaskNoFromAps(Boolean handlerFlag, String msg) throws Exception {
        if (StringUtils.isBlank(msg)) {
            return;
        }
        try {
            List<ApsResponseDTO> apsList = JacksonJsonConverUtil.jsonToListBean(msg, new TypeReference<List<ApsResponseDTO>>() {
            });
            // 过滤错误的数据
            this.checkErrorData(apsList);
            List<String> lockKeyList = new LinkedList<>();
            // 获取任务锁
            this.tryLockTaskNoList(apsList, lockKeyList);
            try {
                this.pullTaskNo(handlerFlag, apsList);
            } finally {
                redisTemplate.delete(lockKeyList);
            }
        } catch (Exception e) {
            this.addRetryNumberOrSaveKafkaMsg(e, msg, handlerFlag);
        }
    }

    /**
     * 过滤出错误的数据
     *
     * @param apsList 任务下发信息
     * @throws MesBusinessException 业务异常
     */
    private void checkErrorData(List<ApsResponseDTO> apsList) {
        if (CollectionUtils.isEmpty(apsList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APS_PROPLANID_MSG_ERROR);
        }
        List<ApsResponseDTO> otherChangeTypeList = new LinkedList<>();
        List<ApsResponseDTO> taskNoEmptyList = new LinkedList<>();
        for (ApsResponseDTO apsResponseDTO : apsList) {
            if (StringUtils.isBlank(apsResponseDTO.getProdplanNo())) {
                taskNoEmptyList.add(apsResponseDTO);
                continue;
            }
            if (Constant.ADD_STR.equals(apsResponseDTO.getChangeType())) {
                continue;
            }
            otherChangeTypeList.add(apsResponseDTO);
        }
        if (CollectionUtils.isNotEmpty(otherChangeTypeList) || CollectionUtils.isNotEmpty(taskNoEmptyList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APS_PROPLANID_MSG_ERROR);
        }
    }

    /**
     * 设置数据任务锁
     *
     * @param apsList     任务信息
     * @param lockKeyList 锁集合
     * @throws MesBusinessException 业务异常
     */
    private void tryLockTaskNoList(List<ApsResponseDTO> apsList, List<String> lockKeyList) {
        for (ApsResponseDTO apsResponseDTO : apsList) {
            String redisKey = String.format(Constant.RedisKey.PULL_TASK_FROM_APS, apsResponseDTO.getProdplanNo());
            Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(redisKey, new Date(), Constant.INT_1, TimeUnit.HOURS);
            if (Boolean.TRUE.equals(setIfAbsent)) {
                lockKeyList.add(redisKey);
            } else {
                redisTemplate.delete(lockKeyList);
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_PULLING,
                        new Object[]{apsResponseDTO.getProdplanNo()});
            }
        }
    }

    /**
     * 记录kafka 消费重试次数， 达到最大重试次数时，存入数据库本地表，待后续处理
     *
     * @param e           异常
     * @param msg         kafka 消息
     * @param handlerFlag 手动表标识
     * @throws Exception 业务异常
     */
    private void addRetryNumberOrSaveKafkaMsg(Exception e, String msg, Boolean handlerFlag) throws Exception {
        String hexMd5ByBytes = DigestUtils.sha256Hex(msg);
        String formatKey = String.format(Constant.RedisKey.RETRY_TASK_KEY, hexMd5ByBytes);
        Object o = redisTemplate.opsForValue().get(formatKey);
        if (null == o) {
            redisTemplate.opsForValue().set(formatKey, Constant.INT_1, Constant.INT_10, TimeUnit.MINUTES);
        } else {
            int retryNumber = Integer.parseInt(o.toString());
            redisTemplate.opsForValue().set(formatKey, ++retryNumber, Constant.INT_10, TimeUnit.MINUTES);
            if (retryNumber > Constant.INT_3) {
                KafkaLocalMessageDTO entry = new KafkaLocalMessageDTO();
                entry.setId(UUID.randomUUID().toString());
                entry.setTopic(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC);
                entry.setTopicKey(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC_KEY);
                entry.setEnabledFlag(Constant.FLAG_Y);
                entry.setCreatedBy(Constant.SYSTEM);
                entry.setLastUpdatedBy(Constant.SYSTEM);
                entry.setMessageContext(msg);
                entry.setErrorMessage(this.getString(e, e.toString()));
                List<KafkaLocalMessageDTO> list = new LinkedList<>();
                list.add(entry);
                kafkaLocalMessageRepository.batchInsertData(list);
                // 邮件通知消费消息存入本地表
                this.sendEmailMsg(e, CommonUtils.getLmbMessage(MessageId.KAFKA_MSG_SAVE_DATABASE,
                        new String[]{entry.getId()}));
                return;
            }
        }
        throw e;
    }

    /**
     * 发送邮件
     *
     * @param e          异常信息
     * @param emailTitle 邮件主题
     */
    private void sendEmailMsg(Exception e, String emailTitle) {
        try {
            Map<String, Object> lookMap = new HashMap<>();
            lookMap.put("lookupType", Constant.LookKey.LOOK_1004095);
            List<SysLookupValues> list = sysLookupValuesRepository.getList(lookMap);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            String emails = list.stream().filter(item -> Constant.EMAIL.equals(item.getAttribute1())
                            && StringUtils.isNotBlank(item.getLookupMeaning()))
                    .map(SysLookupValues::getLookupMeaning).collect(Collectors.joining(";"));
            if (StringUtils.isBlank(emails)) {
                return;
            }
            String msg = e.toString();
            msg = this.getString(e, msg);
            emailUtils.sendMail(emails, emailTitle, StringUtils.EMPTY, msg, "");
            // 发送icenter 通知,任务执行失败，请查看邮件通知
            String iCenter = emails.replaceAll(";", ",");
            iCenterRemoteService.sendMessage(emailTitle, msg, iCenter);
        } catch (Exception exception) {
            LOG.error("send email", exception);
        }
    }

    private String getString(Exception e, String msg) {
        if (e instanceof MesBusinessException) {
            MesBusinessException e1 = (MesBusinessException) e;
            String exMsgId = e1.getExMsgId();
            Object[] params = e1.getParams();
            String[] paramsArray = new String[1];
            if (params != null) {
                paramsArray = new String[params.length];
                for (int i = 0; i < params.length; i++) {
                    paramsArray[i] = JSON.toJSONString(params[i]);
                }
            }
            msg = CommonUtils.getLmbMessage(exMsgId, paramsArray);
        }
        return msg;
    }

    /**
     * 拉取任务实现
     *
     * @param handlerFlag
     * @param taskNoList  任务信息
     * @throws Exception 业务异常
     */
    @Override
    public void pullTaskNo(Boolean handlerFlag, List<ApsResponseDTO> taskNoList) throws Exception {
        // 1. 查询ps_task 是否已经存在任务 不能再拉取
        List<String> prodPlanIdList = taskNoList.stream().map(ApsResponseDTO::getProdplanNo)
                .distinct().collect(Collectors.toList());
        List<String> existingTaskNo = psTaskRepository.getExistingTaskNo(prodPlanIdList);
        if (CollectionUtils.isNotEmpty(existingTaskNo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_EXIST_IMES, new Object[]{existingTaskNo});
        }
        // 2. 查询aps 计划信息
        ApsQueryDTO params = new ApsQueryDTO();
        List<List<String>> lists = CommonUtils.splitList(prodPlanIdList, Constant.INT_90);
        List<ApsResponseDTO> prodPlanList = new LinkedList<>();
        for (List<String> list : lists) {
            params.setProdplanNoList(list);
            List<ApsResponseDTO> tempList = apsRemoteService.getBatchProdPlanSendBomHead(params);
            prodPlanList.addAll(tempList);
        }
        if (CollectionUtils.isEmpty(prodPlanList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                    new Object[]{params.getProdplanNoList()});
        }
        // 手动拉取任务，只能是 已经释放
        if (Boolean.TRUE.equals(handlerFlag)) {
            List<String> collect = prodPlanList.stream()
                    .filter(item -> StringUtils.isBlank(item.getChangeVersion()))
                    .map(ApsResponseDTO::getProdplanNo).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APS_TASK_STATUS_ERROR,
                        new Object[]{collect.toString()});
            }
        }
        List<String> apsList = prodPlanList.stream().map(ApsResponseDTO::getProdplanNo)
                .distinct().collect(Collectors.toList());
        prodPlanIdList.removeAll(apsList);
        if (CollectionUtils.isNotEmpty(prodPlanIdList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                    new Object[]{prodPlanIdList});
        }

        // 2.1 获取配置地组织id 配置项
        Map<String, Object> lookMap = new HashMap<>();
        lookMap.put("lookupType", Constant.LookKey.LOOK_2225);
        List<SysLookupValues> list = sysLookupValuesRepository.getList(lookMap);
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LookKey.LOOK_2225});
        }
        // 设置新增数据
        List<PsTaskDTO> insertList = new LinkedList<>();
        Map<String, ApsResponseDTO> apsResponseDTOMap = taskNoList.stream()
                .collect(Collectors.toMap(ApsResponseDTO::getProdplanNo, value -> value, (k1, k2) -> k1));
        for (ApsResponseDTO apsResponseDTO : prodPlanList) {
            PsTaskDTO psTaskDTO = this.buildPsTaskData(apsResponseDTO, list,apsResponseDTOMap);
            insertList.add(psTaskDTO);
        }
        this.sendEmailAlarm(insertList);
        //处理衍生码
        List<PsTaskDTO> needInsertList = this.generateManufacturingBOM(insertList);
        PsTaskService service = SpringContextUtil.getBean("psTaskServiceImpl", PsTaskService.class);
        service.insertDataBatch(needInsertList);

        List<PsTaskDTO> collect = this.dealNoNeedPropertiesDTO(insertList);
        kafkaMessageProducer.sendMsg(JSON.toJSONString(collect), KafkaConstant.CENTERFACTORY,
                KafkaConstant.ApsKafkaConstant.PROD_PLAN_ID_PRODUCER);
    }

    /* Started by AICoder, pid:05c21c7231l2b5a143d7088390a0286f82862073 */
    private List<PsTaskDTO> generateManufacturingBOM(List<PsTaskDTO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return Collections.emptyList();
        }

        // 创建查询列表
        List<ApsDerivativeCodeQueryDTO> queryList = insertList.stream()
                .map(psTaskDTO -> {
                    ApsDerivativeCodeQueryDTO queryDTO = new ApsDerivativeCodeQueryDTO();
                    queryDTO.setProdplanNo(psTaskDTO.getProdplanNo());
                    queryDTO.setChangeVersion(psTaskDTO.getChangeVersion());
                    queryDTO.setProdplanId(psTaskDTO.getProdplanId());
                    queryDTO.setItemNo(psTaskDTO.getItemNo());
                    queryDTO.setFactoryId(psTaskDTO.getFactoryId() == null ? "" : psTaskDTO.getFactoryId().toString());
                    return queryDTO;
                })
                .collect(Collectors.toList());

        // 获取远程服务结果
        List<ApsDerivativeCodeDTO> resultDTOList = apsRemoteService.getApsDerivativeCodeDTOS(queryList);

        // 构建已存在的MBOM映射
        Map<String, ApsDerivativeCodeDTO> existMBomMap = resultDTOList.stream()
                .collect(Collectors.toMap(
                        e -> e.getProdplanNo() + Constant.UNDER_LINE + e.getChangeVersion(),
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        // 过滤出需要处理的项
        List<ApsDerivativeCodeQueryDTO> filteredList = queryList.stream()
                .filter(e -> existMBomMap.containsKey(e.getProdplanNo() + Constant.UNDER_LINE + e.getChangeVersion()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            return insertList;
        }

        // 处理制造BOM
        List<ApsDerivativeCodeQueryDTO> processedList = bProdBomHeaderService.dealApsDerivedCode(filteredList, false);

        // 构建处理后的查询映射
        Map<String, ApsDerivativeCodeQueryDTO> processedMap = processedList.stream()
                .collect(Collectors.toMap(
                        e -> e.getProdplanNo() + Constant.UNDER_LINE + e.getChangeVersion(),
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        // 设置处理结果
        for (PsTaskDTO psTaskDTO : insertList) {
            setDealResult(psTaskDTO, processedMap);
        }

        // 过滤出需要插入的项
        List<PsTaskDTO> needInsertList = insertList.stream()
                .filter(e -> !Constant.FLAG_N.equals(e.getDealResult()))
                .collect(Collectors.toList());

        return needInsertList;
    }

    private void setDealResult(PsTaskDTO psTaskDTO, Map<String, ApsDerivativeCodeQueryDTO> queryDTOMap) {
        String key = psTaskDTO.getProdplanNo() + Constant.UNDER_LINE + psTaskDTO.getChangeVersion();
        ApsDerivativeCodeQueryDTO queryDTO = queryDTOMap.get(key);
        if (queryDTO != null && StringUtils.isNotEmpty(queryDTO.getErrorMsg())) {
            psTaskDTO.setDealResult(Constant.FLAG_N);
        }
    }

    /* Ended by AICoder, pid:05c21c7231l2b5a143d7088390a0286f82862073 */

    /**
     * 发送批次预警通知
     * @param insertList 批次列表
     */
    private void sendEmailAlarm(List<PsTaskDTO> insertList) {
        insertList.sort(Comparator.comparing(PsTaskDTO::getProdplanId));
        String prodPlanId = insertList.get(insertList.size() - 1).getProdplanId();
        int prodplanId = Integer.parseInt(prodPlanId);
        int futureMax = prodplanId + batchExhaustionEarlyWarningValue;
        String max = prodPlanId.substring(0, 1);
        double pow = (Double.parseDouble(max) + 1.0) * Math.pow(10.0, (prodPlanId.length() - 1) * 1.0);
        if (futureMax > (int) pow) {
            MesBusinessException mesBusinessException = new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.PROD_PLAN_SEQUENCE_OVER, new Object[]{prodPlanId, (int) pow,batchExhaustionEarlyWarningValue});
            this.sendEmailMsg(mesBusinessException, Constant.APS_PROD_PLAN_MESSAGE);
        }
    }

    /**
     * @param apsResponseDTO 指令信息
     * @param list           數據字典
     * @throws MesBusinessException 业务异常报错
     */
    private PsTaskDTO buildPsTaskData(ApsResponseDTO apsResponseDTO, List<SysLookupValues> list,Map<String, ApsResponseDTO> apsResponseDTOMap) throws MesBusinessException {
        // 3. 属性赋值
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setTaskId(UUID.randomUUID().toString());
        this.setChangeVersion(apsResponseDTO, apsResponseDTOMap, psTaskDTO);
        psTaskDTO.setTaskNo(apsResponseDTO.getProdplanNo());
        psTaskDTO.setTaskQty(apsResponseDTO.getQty());
        psTaskDTO.setSourceSys(Constant.SYS_TYPE_STEP);
        psTaskDTO.setExternalType(apsResponseDTO.getProductClassName());
        psTaskDTO.setInternalType(apsResponseDTO.getProductSmlClassName());
        psTaskDTO.setSoftwareVersion(apsResponseDTO.getSoftwareVer());
//        psTaskDTO.setReleaseDate(apsResponseDTO.getRealProdDate());
        Date date = new Date();
        psTaskDTO.setReleaseDate(date);
        psTaskDTO.setPlannedFinishDate(apsResponseDTO.getDemandEndDate());
        psTaskDTO.setDemanEndDate(apsResponseDTO.getDemandEndDate());
        psTaskDTO.setCreateBy(apsResponseDTO.getPlanner());
        psTaskDTO.setCreateDate(date);
        psTaskDTO.setLastUpdatedBy(apsResponseDTO.getLastUpdatedBy());
        psTaskDTO.setLastUpdatedDate(date);
        psTaskDTO.setEnabledFlag(Constant.FLAG_Y);
        psTaskDTO.setProdAddress(apsResponseDTO.getProdAddress());
        psTaskDTO.setOrgBomId(apsResponseDTO.getBomId());
        psTaskDTO.setStock(apsResponseDTO.getStock());
        psTaskDTO.setOutSourceFactoryCode(apsResponseDTO.getOutSourceFactoryCode());
        psTaskDTO.setZbjprodplanNo(apsResponseDTO.getZbjprodplanNo());
        psTaskDTO.setProductClass(apsResponseDTO.getProductClass());
        psTaskDTO.setProductSmlClass(apsResponseDTO.getProductSmlClass());
        Map<String, SysLookupValues> collectMap = list.stream()
                .collect(Collectors.toMap(SysLookupValues::getLookupMeaning, value -> value, (k1, k2) -> k1));
        SysLookupValues sysLookup = collectMap.get(apsResponseDTO.getProdAddress());
        if (Objects.isNull(sysLookup)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROD_ADDRESS_EMPTY,
                    new Object[]{apsResponseDTO.getProdAddress()});
        }
        // 设置部分固定属性
        this.setSomeProperties(apsResponseDTO, psTaskDTO, sysLookup);
        // 转换任务状态
        this.coverProdStatus(apsResponseDTO, psTaskDTO);
        // 批次序列
        this.setProPlanIdProperties(psTaskDTO);
        return psTaskDTO;
    }

    private void setChangeVersion(ApsResponseDTO apsResponseDTO, Map<String, ApsResponseDTO> apsResponseDTOMap, PsTaskDTO psTaskDTO) {
        ApsResponseDTO responseDTO = apsResponseDTOMap.get(apsResponseDTO.getProdplanNo());
        if(responseDTO!= null){
            psTaskDTO.setChangeVersion(responseDTO.getChangeVersion());
        }
        if(StringUtils.isEmpty(psTaskDTO.getChangeVersion())){
            psTaskDTO.setChangeVersion(apsResponseDTO.getChangeVersion());
        }
    }

    /**
     * 设置并校验批次序列
     *
     * @param psTaskDTO 批次信息
     */
    private void setProPlanIdProperties(PsTaskDTO psTaskDTO) {
        PsTaskService service = SpringContextUtil.getBean("psTaskServiceImpl", PsTaskService.class);
        String nextProdplanId = service.getNextProdplanId();
        // 校验批次是否存在中心工厂
        List<String> prodplanIdList = new LinkedList<>();
        prodplanIdList.add(nextProdplanId);
        Map<String, Object> param = new HashMap<>();
        param.put("prodplanIdList", prodplanIdList);
        long specifiedPsTaskCount = psTaskRepository.getSpecifiedPsTaskCount(param);
        if (specifiedPsTaskCount > 0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PROD_PLAN_EXIST_CENTER_FACTORY,
                    new Object[]{prodplanIdList});
        }
        psTaskDTO.setProdplanId(nextProdplanId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public String getNextProdplanId() {
        String nextProdplanId = psTaskRepository.getNextProdplanId();
        return nextProdplanId;
    }


    /**
     * 设置部分属性
     *
     * @param apsResponseDTO 任务信息
     * @param psTaskDTO      批次信息
     * @param sysLookup      数据字典
     * @throws MesBusinessException 业务异常信息
     */
    private void setSomeProperties(ApsResponseDTO apsResponseDTO, PsTaskDTO psTaskDTO, SysLookupValues sysLookup) throws MesBusinessException {
        psTaskDTO.setOrgId(new BigDecimal(sysLookup.getAttribute1()));
        psTaskDTO.setFactoryId(new BigDecimal(sysLookup.getAttribute2()));
        psTaskDTO.setEntityId(new BigDecimal(2));
        psTaskDTO.setAttribute1(apsResponseDTO.getProdUnitName());
        psTaskDTO.setSoftwareVer(apsResponseDTO.getSoftwareVer());
        psTaskDTO.setProdplanNo(apsResponseDTO.getProdplanNo());
        if (StringUtils.isBlank(apsResponseDTO.getConsignbomId())) {
            psTaskDTO.setBomId(apsResponseDTO.getBomId());
        } else {
            psTaskDTO.setBomId(apsResponseDTO.getConsignbomId());
        }
        BBomHeader bomHeader = bBomHeaderRepository.selectBatchByHeadId(psTaskDTO.getBomId());
        if (Objects.isNull(bomHeader)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_ID_MISS,
                    new Object[]{psTaskDTO.getBomId(), apsResponseDTO.getProdplanNo()});
        }
        psTaskDTO.setConsignBomNo(bomHeader.getProductCode());
        psTaskDTO.setItemNo(bomHeader.getProductCode());
        psTaskDTO.setItemName(bomHeader.getChiDesc());
        psTaskDTO.setConsignBomId(apsResponseDTO.getConsignbomId());
        psTaskDTO.setPriority(apsResponseDTO.getProdPriority());
        psTaskDTO.setIsUrgency(apsResponseDTO.getIsUrgency());
        psTaskDTO.setIsConsign(apsResponseDTO.getIsConsign());
        psTaskDTO.setConsignPlanno(apsResponseDTO.getConsignplanNo());
        psTaskDTO.setPlanUuid(apsResponseDTO.getPlanUuid());
        psTaskDTO.setIsParts(apsResponseDTO.getIsBj());
        psTaskDTO.setPartsPlanno(apsResponseDTO.getBjprodplanNo());
        psTaskDTO.setPlanSequence(apsResponseDTO.getPlanSequence());
        psTaskDTO.setPdmWorkGroup1Name(apsResponseDTO.getPdmWorkGroup1Name());
        psTaskDTO.setPdmWorkGroup2Name(apsResponseDTO.getPdmWorkGroup2Name());
        psTaskDTO.setPdmWorkGroup3Name(apsResponseDTO.getPdmWorkGroup3Name());
        psTaskDTO.setEntpNo(apsResponseDTO.getEntpNo());
        psTaskDTO.setBjBomId(apsResponseDTO.getBjBomId());
        psTaskDTO.setLeadFlag(apsResponseDTO.getLeadFlag());
        psTaskDTO.setOutFlag(apsResponseDTO.getIsSendItem());
        psTaskDTO.setPlanner(apsResponseDTO.getPlanner());
        if (StringUtils.isBlank(apsResponseDTO.getConsignplanNo())) {
            psTaskDTO.setPlanId(apsResponseDTO.getPlanNo());
        } else {
            psTaskDTO.setPlanId(apsResponseDTO.getConsignplanNo());
        }
        if (Constant.R_STATUS.equals(apsResponseDTO.getSourceType())) {
            psTaskDTO.setSourceType(Constant.YES);
        } else {
            psTaskDTO.setSourceType(Constant.NO);
        }
    }

    /**
     * 转换任务状态
     *
     * @param apsResponseDTO 任务信息
     * @param psTaskDTO      任务信息
     */
    private void coverProdStatus(ApsResponseDTO apsResponseDTO, PsTaskDTO psTaskDTO) {
        if (StringUtils.isNotBlank(apsResponseDTO.getProdStatus())) {
            switch (apsResponseDTO.getProdStatus()) {
                case "01":
                    psTaskDTO.setProdStatus(Constant.ProdStatus.PROD_STATUS_01);
                    break;
                case "02":
                    psTaskDTO.setProdStatus(Constant.ProdStatus.PROD_STATUS_02);
                    break;
                case "03":
                    psTaskDTO.setProdStatus(Constant.ProdStatus.PROD_STATUS_03);
                    break;
                case "05":
                    psTaskDTO.setProdStatus(Constant.ProdStatus.PROD_STATUS_05);
                    break;
                case "07":
                    psTaskDTO.setProdStatus(Constant.ProdStatus.PROD_STATUS_07);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 消费 APS kafka 消息产生批次数据
     *
     * @param msg 消息
     */
    @Override
    @RecordLogAnnotation("APS kafka消息")
    @Transactional(rollbackFor = Exception.class)
    public void consumerProdPlanIdMsg(String msg) throws Exception {
        this.pullTaskNoFromAps(false, msg);
    }

    /**
     * 消费 APS kafka 消息修改任务信息数据
     *
     * @param msg 消息
     * @throws Exception 业务异常
     */
    @Override
    @RecordLogAnnotation("APS 任务修改kafka消息")
    @TransmittableHeader
    public void consumerModifyProdPlanNoMsg(String msg) throws Exception {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        try {
            // 转化msg
            List<ApsResponseDTO> taskNoList = JacksonJsonConverUtil.jsonToListBean(msg, new TypeReference<List<ApsResponseDTO>>() {
            });
            List<ApsResponseDTO> resultList = handleUpdateTaskNo(taskNoList);
            msg = JSON.toJSONString(resultList);
            kafkaMessageProducer.sendMsg(msg, KafkaConstant.CENTERFACTORY,
                    KafkaConstant.ApsKafkaConstant.PROD_PLAN_MODIFY_PRODUCER);
        } catch (Exception e) {
            addRetryNumberOrSaveKafkaMsg(e, msg, KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC,
                    KafkaConstant.ApsKafkaConstant.APS2_IMES_MODIFY_PROD_PLAN, MessageId.KAFKA_MSG_SAVE_DATABASE_TASK_UPDATE);
        }
    }

    /**
     * @param e        异常信息
     * @param msg      kafka消息实体
     * @param topic    kafka主题
     * @param topicKey kafka主题键
     * @param msgId    邮件提醒的内容，需带一个参数
     * @throws Exception
     */
    @Override
    public void addRetryNumberOrSaveKafkaMsg(Exception e, String msg, String topic, String topicKey, String msgId) throws Exception {
        // 消息加密
        String md5EncodeMsg = DigestUtils.sha256Hex(msg);
        String msgRedisCache = String.format(Constant.RedisKey.RETRY_TASK_KEY, md5EncodeMsg);

        Object value = redisTemplate.opsForValue().get(msgRedisCache);
        if (null == value) {
            redisTemplate.opsForValue().set(msgRedisCache, 1, Constant.INT_10, TimeUnit.MINUTES);
        } else {
            int retryCount = Integer.parseInt(value.toString());
            redisTemplate.opsForValue().set(msgRedisCache, ++retryCount, Constant.INT_10, TimeUnit.MINUTES);
            if (retryCount > Constant.INT_3) {
                KafkaLocalMessageDTO kafkaMsgRecord = new KafkaLocalMessageDTO();
                kafkaMsgRecord.setId(UUID.randomUUID().toString());
                kafkaMsgRecord.setTopic(topic);
                kafkaMsgRecord.setTopicKey(topicKey);
                kafkaMsgRecord.setEnabledFlag(Constant.FLAG_Y);
                kafkaMsgRecord.setCreatedBy(Constant.SYSTEM);
                kafkaMsgRecord.setLastUpdatedBy(Constant.SYSTEM);
                kafkaMsgRecord.setMessageContext(msg);
                kafkaMsgRecord.setErrorMessage(this.getString(e, e.toString()));
                List<KafkaLocalMessageDTO> list = new LinkedList<>();
                list.add(kafkaMsgRecord);
                kafkaLocalMessageRepository.batchInsertData(list);
                // 邮件通知消费消息存入本地表
                this.sendEmailMsg(e, CommonUtils.getLmbMessage(msgId,
                        new String[]{kafkaMsgRecord.getId()}));
                return;
            }
        }
        throw e;
    }

    private List<ApsResponseDTO> handleUpdateTaskNo(List<ApsResponseDTO> taskNoList) throws Exception {
        return handleTaskChangeValidateOrUpdate(taskNoList, true);
    }


    /**
     * 触发批次kafka信息 给aps
     *
     * @param taskNoList 任务号
     * @throws Exception 业务异常信息
     */
    @Override
    @RecordLogAnnotation("补发kafka 批次回写信息到APS")
    public void sendTaskMsgToAps(List<String> taskNoList) throws Exception {
        if (CollectionUtils.isEmpty(taskNoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_IS_NULL);
        }
        List<PsTask> psTaskList = psTaskRepository.queryTaskByTaskNoList(Constant.SYS_TYPE_STEP, taskNoList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_EXIST_IMES,
                    new Object[]{taskNoList});
        }
        List<PsTask> collect = psTaskList.stream()
                // 如果是研发领散料场景（以-t结尾的任务号），不需要生成批次给iAPS；
                .filter(item -> StringUtils.isNotBlank(item.getTaskNo()) && !item.getTaskNo().endsWith(Constant.T_SIGN))
                .filter(item -> StringUtils.isNotBlank(item.getProdplanId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        // 查询aps 计划信息
        ApsQueryDTO params = new ApsQueryDTO();
        List<List<String>> lists = CommonUtils.splitList(taskNoList, Constant.INT_90);
        List<ApsResponseDTO> prodPlanList = new LinkedList<>();
        for (List<String> list : lists) {
            params.setProdplanNoList(list);
            List<ApsResponseDTO> tempList = apsRemoteService.getBatchProdPlanSendBomHead(params);
            prodPlanList.addAll(tempList);
        }
        Map<String, ApsResponseDTO> apsMap = prodPlanList.stream().collect(Collectors.toMap(ApsResponseDTO::getProdplanNo, a -> a, (k1, k2) -> k1));

        collect = this.dealNoNeedProperties(collect,apsMap);
        kafkaMessageProducer.sendMsg(JSON.toJSONString(collect), KafkaConstant.CENTERFACTORY,
                KafkaConstant.ApsKafkaConstant.PROD_PLAN_ID_PRODUCER);
    }

    /**
     * 处理掉不需要的数据
     *
     * @param psTaskList 任务
     * @return 处理完成的数据
     */
    private List<PsTask> dealNoNeedProperties(List<PsTask> psTaskList,Map<String, ApsResponseDTO> apsMap) {
        List<PsTask> resultList = new LinkedList<>();
        for (PsTask psTask : psTaskList) {
            PsTask temp = new PsTask();
            temp.setProdplanId(psTask.getProdplanId());
            temp.setProdplanNo(psTask.getProdplanNo());
            temp.setDealResult(Constant.FLAG_Y);
            ApsResponseDTO apsResponseDTO = apsMap.get(psTask.getProdplanNo());
            if(apsResponseDTO != null){
                temp.setChangeVersion(apsResponseDTO.getChangeVersion());
            }
            resultList.add(temp);
        }
        return resultList;
    }

    /**
     * 处理掉不需要的数据
     *
     * @param psTaskList 任务
     * @return 处理完成的数据
     */
    private List<PsTaskDTO> dealNoNeedPropertiesDTO(List<PsTaskDTO> psTaskList) {
        List<PsTaskDTO> resultList = new LinkedList<>();
        for (PsTaskDTO psTask : psTaskList) {
            PsTaskDTO temp = new PsTaskDTO();
            temp.setProdplanNo(psTask.getProdplanNo());
            temp.setChangeVersion(psTask.getChangeVersion());
            temp.setProdplanId(psTask.getProdplanId());
            temp.setDealResult(StringUtils.isNotEmpty(psTask.getDealResult())?psTask.getDealResult():Constant.FLAG_Y);
            resultList.add(temp);
        }
        return resultList;
    }

    /**
     * APS 查询任务是否可以修改
     *
     * @param originalList 任务号信息
     * @throws Exception 业务异常
     */
    @Override
    @TransmittableHeader
    public List<ApsResponseDTO> taskInfoChangeableQuery(List<ApsResponseDTO> originalList) throws Exception {
        return handleTaskChangeValidateOrUpdate(originalList, false);
    }

    /**
     * APS 查询任务是否可以修改
     *
     * @param originalList 任务号信息
     * @throws Exception 业务异常
     */
    private List<ApsResponseDTO> handleTaskChangeValidateOrUpdate(List<ApsResponseDTO> originalList, boolean updateFlag) throws Exception {
        if (CollectionUtils.isEmpty(originalList)) {
            return new ArrayList<>();
        }
        // 拷贝最开始的列表，用于处理，列表的成员对象的errorMsg会记录校验结果
        // 注意采用LinkedList存储，遍历使用迭代器，这样每一次过滤移除元素效率提高很多
        List<ApsResponseDTO> filterList = new LinkedList<>(originalList);
        // 基础必填性校验
        basicValidateApsList(filterList, updateFlag);
        // 区分多工厂。
        Map<BigDecimal, List<ApsResponseDTO>> factoryIdToListMap = classifyByFactory(filterList);
        // 多个工厂并行调用。
        List<Future> resultFutureList = new LinkedList<>();
        // 获取工号,设置更新人
        String empNo = getEmpNo(originalList.get(0), updateFlag);
        // 执行分工厂校验
        for (Map.Entry<BigDecimal, List<ApsResponseDTO>> entity : factoryIdToListMap.entrySet()) {
            BigDecimal factoryId = entity.getKey();
            List<ApsResponseDTO> localFactoryTaskNos = entity.getValue();
            Future<Object> submit = ThreadUtil.TASK_INFO_CHANGE_ABLE_QUERY_EXECUTOR.submit(
                    () -> delegateToLocalFactory(factoryId, empNo, localFactoryTaskNos, updateFlag));
            resultFutureList.add(submit);
        }
        // 结果汇总
        Map<String, ApsResponseDTO> taskNoToPsTaskEntityMap = filterList.stream().collect(Collectors
                .toMap(k -> k.getProdplanNo(), v -> v, (oldValue, newValue) -> newValue));
        for (Future future : resultFutureList) {
            List<ApsResponseDTO> locationFacResult = (List<ApsResponseDTO>) future.get();
            // 分工厂的处理结果，填入到结果列表的对象中。
            for (ApsResponseDTO entity : locationFacResult) {
                ApsResponseDTO apsResponseDTO = taskNoToPsTaskEntityMap.get(entity.getProdplanNo());
                apsResponseDTO.setErrorMsg(entity.getErrorMsg());
            }
        }
        // 只保留需要的输出字段
        return convertResult(originalList);
    }

    private String getEmpNo(ApsResponseDTO aps, boolean updateFlag) {
        if (updateFlag) {
            return aps.getEmpNo();
        } else {
            // 获取工号,设置更新人
            Map<String, String> headerMap = MESHttpHelper.getHttpRequestHeader();
            return headerMap.get(Constant.X_EMP_NO_SMALL);
        }
    }

    private List<ApsResponseDTO> convertResult(List<ApsResponseDTO> originalList) {
        List<ApsResponseDTO> resultList = new ArrayList<>();
        for (int i = 0; i < originalList.size(); i++) {
            ApsResponseDTO entity = originalList.get(i);
            ApsResponseDTO dto = new ApsResponseDTO();
            dto.setErrorMsg(entity.getErrorMsg() == null ? Constant.STRING_EMPTY : entity.getErrorMsg());
            dto.setProdplanNo(entity.getProdplanNo());
            dto.setProdplanModifyNo(entity.getProdplanModifyNo());
            dto.setChangeVersion(entity.getChangeVersion());
            resultList.add(dto);
        }
        return resultList;
    }

    /**
     * 将任务委派给所属地方工厂进行校验。
     *
     * @param factoryId
     * @param localFactoryTaskNos
     * @return
     */
    private List<ApsResponseDTO> delegateToLocalFactory(BigDecimal factoryId, String empNo, List<ApsResponseDTO> localFactoryTaskNos,
                                                        boolean updateFlag) throws Exception {
        List<ApsResponseDTO> resultList = new ArrayList<>();
        // 如果中心工厂查询到有任务的工厂为空，该分组任务号全部记录异常
        if (factoryId == null) {
            for (int i = 0; i < localFactoryTaskNos.size(); i++) {
                ApsResponseDTO entity = localFactoryTaskNos.get(i);
                entity.setErrorMsg(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_OF_CENTER_PSTASK_IS_NULL));
            }
            return localFactoryTaskNos;
        }
        //根据工厂ID获得主机地址，
        String address = sysLookupValuesService.getAddressByFactoryId(factoryId.toString());
        // 如果中心工厂查询到有任务的工厂不在数据字典1245中，那么该分组任务号全部记录异常
        if (StringUtils.isEmpty(address)) {
            for (int i = 0; i < localFactoryTaskNos.size(); i++) {
                ApsResponseDTO entity = localFactoryTaskNos.get(i);
                entity.setErrorMsg(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY));
            }
            return localFactoryTaskNos;
        }
        // 远程调用分工厂。
        if (updateFlag) {
            resultList = planScheduleRemoteService.delegateToLocalFactoryUpdate(factoryId, empNo, address, localFactoryTaskNos);
        } else {
            resultList = planScheduleRemoteService.delegateToLocalFactoryValidate(factoryId, empNo, address, localFactoryTaskNos);
        }
        return resultList;
    }

    /**
     * 验证任务在中心工厂是否存在,不存在的会被过滤，存在的会被分类成不同工厂中。
     *
     * @param list
     * @return
     */
    private Map<BigDecimal, List<ApsResponseDTO>> classifyByFactory(List<ApsResponseDTO> list) {
        Map<BigDecimal, List<ApsResponseDTO>> resultMap = new HashMap<>();
        if (CollectionUtil.isEmpty(list)) {
            return resultMap;
        }
        List<String> taskNoList = list.stream().map(e -> e.getProdplanNo()).collect(Collectors.toList());
        List<PsTask> psTaskList = psTaskRepository.queryTaskByTaskNoList(Constant.SYS_TYPE_STEP, taskNoList);
        Map<String, PsTask> taskNoToPsTaskEntityMap = psTaskList.stream().collect(Collectors
                .toMap(k -> k.getTaskNo(), v -> v, (oldValue, newValue) -> newValue));
        // 验证任务在中心工厂是否存在,不存在的会被过滤，存在的会被分类
        Iterator<ApsResponseDTO> itr = list.iterator();
        while (itr.hasNext()) {
            ApsResponseDTO entity = itr.next();
            if (entity == null) {
                continue;
            }
            if (taskNoToPsTaskEntityMap.keySet().contains(entity.getProdplanNo())) {
                // 得到所属工厂Id
                BigDecimal factoryId = taskNoToPsTaskEntityMap.get(entity.getProdplanNo()).getFactoryId();
                List<ApsResponseDTO> apsResponseDTOs = resultMap.get(factoryId);
                // 工厂首个任务，需新建list避免空指针。
                if (apsResponseDTOs == null) {
                    apsResponseDTOs = new ArrayList<>();
                    resultMap.put(factoryId, apsResponseDTOs);
                }
                apsResponseDTOs.add(entity);
            }
            // 如果不在map中，说明不在中心工厂，填入错误信息后，过滤。
            else {
                entity.setErrorMsg(CommonUtils.getLmbMessage(MessageId.CENTER_FACTORY_NOT_EXIST_THE_TASK_NO));
                itr.remove();
            }
        }
        return resultMap;
    }

    /**
     * 基础必填性校验
     *
     * @param list
     * @throws Exception
     */
    private void basicValidateApsList(List<ApsResponseDTO> list, boolean updateFlag) {
        if (CollectionUtil.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_IS_NULL);
        }
        Iterator<ApsResponseDTO> itr = list.iterator();
        while (itr.hasNext()) {
            ApsResponseDTO entity = itr.next();
            if (entity == null) {
                continue;
            }
            if (StringUtils.isEmpty(entity.getProdplanNo())) {
                entity.setErrorMsg(CommonUtils.getLmbMessage(MessageId.PRODPLANNO_NOT_NULL));
                itr.remove();
                continue;
            }
            if (updateFlag && StringUtils.isEmpty(entity.getProdplanModifyNo())) {
                entity.setErrorMsg(CommonUtils.getLmbMessage(MessageId.PRODPLANMODIFYNO_NOT_NULL));
                itr.remove();
                continue;
            }
        }
    }

    /**
     * 批量新增批次
     *
     * @param list 集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertDataBatch(List<PsTaskDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<List<PsTaskDTO>> lists = CommonUtils.splitList(list, Constant.INT_50);
        for (List<PsTaskDTO> psTaskDTOS : lists) {
            psTaskRepository.insertDataBatch(psTaskDTOS);
        }
    }

    @Override
    public void callBackCenterUpdatePsTask(List<PsTask> psTaskList) {
        if (org.springframework.util.CollectionUtils.isEmpty(psTaskList)) {
            return;
        }
        psTaskRepository.updateBatchPsTask(psTaskList);
    }

    /**
     * 批量获取批次信息，少量字段
     *
     * @param prodPlanIdList 批次
     * @return
     * @throws Exception
     */
    @Override
    public List<PsTask> queryPsTaskBatch(List<String> prodPlanIdList) {
        return psTaskRepository.queryPsTaskBatch(prodPlanIdList);
    }

    @Override
    public List<PsTask> getTaskQtyByProdplanId(List<String> prodPlanIdList) {
        return psTaskRepository.getTaskQtyByProdplanId(prodPlanIdList);
    }

    @Override
    public String getBomNoByProdplanId(String prodplanId) {
        return psTaskRepository.getBomNoByProdplanId(prodplanId);
    }

    @Override
    public String getLeadFlagDescByProdplanId(String prodplanId) {
        String leadFlag = psTaskRepository.getLeadFlagByProdplanId(prodplanId);
        if (StringUtils.isNotEmpty(leadFlag)) {
            List<SysLookupValues> list = sysLookupValuesService.selectValuesByType(Constant.LOOK_UP_TYPE_1036);
            if (CollectionUtils.isEmpty(list)) {
                return "";
            }
            for (SysLookupValues sysLookupValue : list) {
                if (leadFlag.equals(sysLookupValue.getLookupMeaning())) {
                    return sysLookupValue.getDescriptionChin();
                }
            }
        }
        return "";
    }

    @Override
    public List<BoardInstructionCycleDataCreateDTO> selectGetDateByProdplanNo(List<String> planNos) {
        return psTaskRepository.selectGetDateByProdplanNo(planNos);
    }

    @Override
    public List<PsTaskDTO> selectErpStatusByProdplanId(List<String> prodplanList) {
        if (CollectionUtils.isEmpty(prodplanList)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        return psTaskRepository.selectErpStatusByProdplanId(prodplanList);
    }


    @Override
    public List<PsTask> queryFactoryIdByProdIdList(List<String> prodPlanIdList) {
        return psTaskRepository.queryFactoryIdByProdIdList(prodPlanIdList);
    }

    /* Started by AICoder, pid:n2243xd593rf4f614fae095780281e2088a2fa31 */
    @Override
    public List<PsTask> queryFactoryIdByTaskNoList(List<String> taskNoList) {
        // 如果输入列表为空或为null，直接返回空列表
        if (CollectionUtils.isEmpty(taskNoList)) {
            return Collections.emptyList();
        }

        List<PsTask> result = new ArrayList<>();

        // 将任务号列表按每100个一组进行分割
        for (List<String> batch : CommonUtils.splitList(taskNoList, NUM_HUNDRED)) {
            // 查询每个批次的任务
            List<PsTask> batchTasks = psTaskRepository.queryFactoryIdByTaskNoList(batch);

            // 如果查询结果不为空且不为空列表，则添加到结果列表中
            if (CollectionUtils.isNotEmpty(batchTasks)) {
                result.addAll(batchTasks);
            }
        }

        return result;
    }

    /* Ended by AICoder, pid:n2243xd593rf4f614fae095780281e2088a2fa31 */

    @Override
    public List<PsTask> selectPsTaskByProdIdSet(Set<String> prodIdSet) {
        List<PsTask> resultList = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(prodIdSet)) {
            return resultList;
        }
        return psTaskRepository.selectPsTaskByProdIdSet(prodIdSet);
    }

    /**
     * @return
     * <AUTHOR>
     * 根据计划跟踪单号获取任务信息
     * @Date 2023/2/7 14:08
     * @Param [java.lang.String]
     **/
    @Override
    public PsTaskDTO getPsTaskByTaskNo(String taskNo) {
        if (StringUtils.isEmpty(taskNo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_CANNOT_BE_NULL);
        }
        return psTaskRepository.getPsTaskByTaskNo(taskNo);
    }

    @Override
    public List<PsTask> getFactoryIdByProdId(List<String> prodPlanIdList) {
        return psTaskRepository.getFactoryIdByProdId(prodPlanIdList);
    }

    @Override
    public List<PsTask> selectItemCodeByProd(List<String> prods) {
        return psTaskRepository.selectItemCodeByProd(prods);
    }

    public TechnicalAndLockInfoDTO getTechnicalAndLockInfoByProdplanId(String prodplanId) throws Exception {
        if (StringUtils.isEmpty(prodplanId)) {
            return null;
        }
        List<String> prodIdList = new ArrayList<>();
        prodIdList.add(prodplanId);
        List<PsTaskDTO> psTaskDTOList = psTaskRepository.getFactoryIdByProdplanId(prodIdList);
        if (CollectionUtils.isEmpty(psTaskDTOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_TASK_INFO_ERROR, new Object[]{prodplanId});
        }
        PsTaskDTO taskDTO = psTaskDTOList.get(NumConstant.NUM_ZERO);
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        String url = ConstantInterface.getUrlStatic(InterfaceEnum.getTechnicalAndLockInfoByProdplanId);
        Map<String, String> paramsMap = new HashMap<>();
        //查询分工厂是否已经执行添加缓冲池等后续操作
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);

        SysLookupTypesDTO factoryInfo = getTaskExist(valuesList, taskDTO.getTaskNo());
        if (factoryInfo == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_CURRENT_BATCH_IS_ISSUED);
        }
        String factoryIdStr = factoryInfo.getLookupMeaning();
        //循环之后没找到本地任务信息，报错
        if (StringUtils.isEmpty(factoryIdStr)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_CURRENT_BATCH_IS_ISSUED);
        }
        paramsMap.put(Constant.PRODPLAN_ID, prodplanId);
        paramsMap.put(Constant.FACTORY_ID, factoryIdStr);
        String msg = HttpClientUtil.httpGet(url, paramsMap, headerParamsMap);
        String bo = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
        if (StringUtils.isBlank(bo)) {
            return null;
        }
        return getTechnicalAndLockInfoDTO(prodplanId, factoryInfo, bo);
    }

    public TechnicalAndLockInfoDTO getTechnicalAndLockInfoDTO(String prodplanId, SysLookupTypesDTO factoryInfo, String bo) {
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = JacksonJsonConverUtil.jsonToBean(bo, TechnicalAndLockInfoDTO.class);
        if (technicalAndLockInfoDTO != null) {
            technicalAndLockInfoDTO.setFormFactoryId(factoryInfo.getLookupMeaning());
            technicalAndLockInfoDTO.setFormFactoryName(factoryInfo.getDescriptionChinV());
            technicalAndLockInfoDTO.setProdplanId(prodplanId);
        }
        return technicalAndLockInfoDTO;
    }

    /**
     * 获取任务号对应子卡批次信息 树形结构
     *
     * @param taskNos  任务号
     * @param needRoot 是否需要完成树，还是从本身开始，默认完成
     * @return 树形结构数据
     */
    @Override
    public List<PsTaskTreeDTO> getSubTaskTreeByTaskNo(List<String> taskNos, String needRoot) {
        // 所有子卡的主卡 全部是顶层主卡
        List<PsTaskTreeDTO> resultList = new LinkedList<>();
        List<PsTaskTreeDTO> psTaskList = new LinkedList<>();
        List<List<String>> splitList = CommonUtils.splitList(taskNos, Constant.BATCH_SIZE);
        for (List<String> list : splitList) {
            List<PsTaskTreeDTO> tempList = psTaskRepository.getSubTaskByTaskNoList(list);
            if (CollectionUtils.isNotEmpty(tempList)) {
                psTaskList.addAll(tempList);
            }
        }
        // 主子卡是平铺结构，parts_planno 全是顶层主卡 的任务号
        Map<String, List<PsTaskTreeDTO>> collectMap = psTaskList.stream().collect(Collectors.groupingBy(PsTaskTreeDTO::getPartsPlanno));
        collectMap.forEach((k, v) -> {
            TreeMap<Integer, List<PsTaskTreeDTO>> childTree = new TreeMap<>();
            // 获取层级分组
            for (PsTaskTreeDTO psTask : v) {
                String taskNo = psTask.getTaskNo();
                String suffix = taskNo.replaceFirst(k + "-", StringUtils.EMPTY);
                String[] split = suffix.split("-");
                int level = split.length - 1;
                List<PsTaskTreeDTO> psTasks = childTree.computeIfAbsent(level, k1 -> new LinkedList<>());
                psTasks.add(psTask);
            }
            // 顶层主卡
            PsTaskTreeDTO first = new PsTaskTreeDTO();
            first.setTaskNo(k);
            for (int i = 0; i < childTree.size(); i++) {
                if (i == childTree.size() - 1) {
                    break;
                }
                this.linkTreeRoot(childTree, i);
            }
            first.setSubChildList(childTree.get(0));
            resultList.add(first);
        });
        // 需要根节点标识，再进行拆分
        return Constant.FLAG_N.equals(needRoot) ? this.filterNeedRootList(taskNos, resultList) : resultList;
    }

    @Override
    public List<String> getWmesTaskNo(List<String> taskNoList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return new ArrayList<>();
        }
        return psTaskRepository.getWmesTaskNo(taskNoList);
    }

    @Override
    public List<PsTask> getPsTask(List<String> taskNoList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return Collections.emptyList();
        }
        return psTaskRepository.queryTaskByTaskNoList(SOURCE_SYS, taskNoList);
    }

    /**
     * 构建树形结构
     *
     * @param treeChild 子卡列表
     * @param i         数组index
     */
    private void linkTreeRoot(TreeMap<Integer, List<PsTaskTreeDTO>> treeChild, int i) {
        List<PsTaskTreeDTO> firstList = treeChild.get(i);
        List<PsTaskTreeDTO> nextLevelList = treeChild.get(i + 1);
        if (CollectionUtils.isEmpty(nextLevelList) || CollectionUtils.isEmpty(firstList)) {
            return;
        }
        for (PsTaskTreeDTO psTask : firstList) {
            psTask.setSubChildList(new LinkedList<>());
            for (PsTaskTreeDTO child : nextLevelList) {
                if (child.getTaskNo().startsWith(psTask.getTaskNo())) {
                    List<PsTaskTreeDTO> subChildList = psTask.getSubChildList();
                    subChildList.add(child);
                    psTask.setSubChildList(subChildList);
                }
            }
        }
    }

    /**
     * 过滤出实际的根节点数据
     *
     * @param taskNos    任务号
     * @param resultList 完整树结构
     * @return 实际需要的接口树
     */
    private List<PsTaskTreeDTO> filterNeedRootList(List<String> taskNos, List<PsTaskTreeDTO> resultList) {
        List<PsTaskTreeDTO> newResultList = new ArrayList<>();
        // 从自身开始的树节点，而非从跟节点
        List<String> addList = new LinkedList<>();
        for (String taskNo : taskNos) {
            List<PsTaskTreeDTO> collect = resultList.stream()
                    .filter(item -> taskNo.startsWith(item.getTaskNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }
            // 获取自身跟节点
            this.findShelfNode(newResultList, taskNo, collect, addList);
        }
        return newResultList;
    }

    /**
     * 自身开始的树节点，而非从跟节点
     *
     * @param newResultList 新 的节点树
     * @param taskNo        任务号
     * @param collect       子集
     * @param addList       已经找到树节点的任务号
     */
    private void findShelfNode(List<PsTaskTreeDTO> newResultList, String taskNo, List<PsTaskTreeDTO> collect, List<String> addList) {
        for (PsTaskTreeDTO psTask : collect) {
            if (addList.contains(taskNo)) {
                return;
            }
            if (StringUtils.equals(taskNo, psTask.getTaskNo())) {
                newResultList.add(psTask);
                addList.add(taskNo);
                break;
            }
            List<PsTaskTreeDTO> subChildList = psTask.getSubChildList();
            if (CollectionUtils.isEmpty(subChildList)) {
                continue;
            }
            this.findShelfNode(newResultList, taskNo, subChildList, addList);
        }
    }

    /**
     * 处理未发放到本地工厂的批次
     * 1.查中心工厂ps_tasksource_sys为STEP,factory_id为51,任务状态不为已废止的任务（根据入参筛选处理的时间）
     * 2.将任务调用接口批量查询APS数据，筛选任务数量为0的任务,修改IMES中心工厂任务状态为已废止，数量改为0,后续处理剔除这部分任务
     * 3.查询所有本地工厂是否存在这些批次的数据，若存在，将中心工厂factory_Id改为对应工厂id,(中心工厂org_id是否需要更新),后续处理剔除这部分任务
     * 4.org_id不为空，将批次信息同步到org_id对应的本地工厂,任务状态改为已完工（在本地工厂无该批次的情况下），备注是本次处理的数据
     * 5.org_id为空，将批次信息统一同步到深圳工厂,任务状态改为已完工（深圳工厂原本没有该批次的情况下），备注是本次处理的数据
     * 6.中心工厂任务状态改为已完工
     * @param dto
     * @throws Exception
     */
    @Override
    public int dealHisDataWithNoFactoryId(PsTaskDTO dto) throws Exception {
        int count = 0;
        Map<String, Object> map = new HashMap<>();
        map.put("sourceSys","STEP");
        map.put("factoryId","51");
        map.put("notInTaskStatus","已废止");
        map.put("createDateStart", DateUtils.parseDate(dto.getCreateDateStart(),"yyyy-MM-dd HH:mm:ss"));
        map.put("createDateEnd", DateUtils.parseDate(dto.getCreateDateEnd(),"yyyy-MM-dd HH:mm:ss"));
        List<PsTask> psTasks = psTaskRepository.getPsTaskListByCondition(map);
        if (CollectionUtils.isEmpty(psTasks)) {
            imesLogService.log(psTasks, "处理未发放到本地工厂的批次-无待处理批次");
            return psTasks.size();
        }
        List<String> prodplanNoList = psTasks.stream().map(PsTask::getProdplanNo)
                .distinct().collect(Collectors.toList());
        List<List<String>> lists = CommonUtils.splitList(prodplanNoList, Constant.INT_90);
        List<ApsResponseDTO> prodPlanList = new LinkedList<>();
        ApsQueryDTO params = new ApsQueryDTO();
        for (List<String> list : lists) {
            params.setProdplanNoList(list);
            List<ApsResponseDTO> apsTempList = apsRemoteService.getBatchProdPlanSendBomHead(params);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(apsTempList)){
                prodPlanList.addAll(apsTempList);
            }
        }
        // 在APS存在的批次
        filterNoQtyTaskFromAps(prodPlanList, psTasks);
        List<String> needDealProdPlanIds = psTasks.stream().map(PsTask::getProdplanId).distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(needDealProdPlanIds, com.zte.common.utils.NumConstant.NUM_100);
        // 查分工厂哪些批次存在
        List<SysLookupValues> sysLookValues = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1245);
        Map<String, String> factoryToAddress = sysLookValues.stream().collect(Collectors.toMap
                (k -> k.getLookupMeaning(), v -> v.getAttribute2(), (o, n) -> n));
        filterLocalFactoryTask(sysLookValues, splitList, psTasks);
        // 剩下的批次根据组织id确定分发到哪些工厂
        setFactoryIdForTask(psTasks, sysLookValues);
        Map<BigDecimal,List<PsTask>> dealTaskByFactoryMap = psTasks.stream().filter(e->null != e.getFactoryId()).collect(Collectors.groupingBy(PsTask::getFactoryId));
        for (Map.Entry<BigDecimal, List<PsTask>> entry : dealTaskByFactoryMap.entrySet()) {
            List<PsTask> dealTasks = entry.getValue();
            String address =factoryToAddress.get(entry.getKey().toString());
            List<List<PsTask>> splitTasks = CommonUtils.splitList(dealTasks, NUM_HUNDRED);
            for (List<PsTask> splitTask : splitTasks) {
                psTaskRepository.updateBatchPsTaskByCondition(splitTask);
                count += planScheduleRemoteService.dealPsTaskToLocalFactoryUpdate(entry.getKey(),"00286523",address,splitTask);
            }
        }
        return count;
    }

    private void filterLocalFactoryTask(List<SysLookupValues> sysLookValues, List<List<String>> splitList, List<PsTask> psTasks) throws Exception {
        for (SysLookupValues sysLookValue : sysLookValues) {
            List<PsTask> taskList = new ArrayList<>();
            for (List<String> prodplanIds : splitList) {
                List<PsTask> tempList = planScheduleRemoteService.getPsTaskByProdPlanIdList(sysLookValue.getLookupMeaning(),prodplanIds);
                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(tempList)){
                    taskList.addAll(tempList);
                }
            }
            if (CollectionUtils.isNotEmpty(taskList)) {
                // 在分工厂存在的批次也剔除掉，同时更新这部分任务在中心工厂的状态
                List<String> existInLocal = taskList.stream().map(PsTask::getProdplanId).distinct().collect(Collectors.toList());
                imesLogService.log(existInLocal, "处理未发放到本地工厂的批次-本地工厂存在的批次");
                taskList.forEach(p -> {
                    p.setFactoryId(new BigDecimal(sysLookValue.getLookupMeaning()));
                    p.setRemark(Constant.DEAL_HISDATA_BY_NO_FACTORY);
                });
                List<List<PsTask>> splitTaskFromLocalFactory = CommonUtils.splitList(taskList, NUM_HUNDRED);
                for (List<PsTask> splitTaskFromLocalTemp : splitTaskFromLocalFactory) {
                    psTaskRepository.updateBatchPsTaskByCondition(splitTaskFromLocalTemp);
                }
                psTasks.removeIf(item -> existInLocal.contains(item.getProdplanId()));
            }
        }
    }

    private void filterNoQtyTaskFromAps(List<ApsResponseDTO> prodPlanList, List<PsTask> psTasks) {
        List<String> noQtyProdplanNos = prodPlanList.stream().filter(i ->null == i.getQty() || new BigDecimal(com.zte.common.utils.NumConstant.NUM_ZERO).compareTo(i.getQty()) >= NUM_ZERO)
                .map(ApsResponseDTO::getProdplanNo).distinct().collect(Collectors.toList());
        List<PsTask> notNeedDealTasks = psTasks.stream().filter(p->noQtyProdplanNos.contains(p.getProdplanNo())).collect(Collectors.toList());
        // 更新上游数据为0的任务，将任务状态更新为已废止且数量更新为0
        imesLogService.log(notNeedDealTasks, "处理未发放到本地工厂的批次-上游已取消的批次");
        notNeedDealTasks.forEach(p -> {
            p.setTaskStatus(Constant.STATUS_SCRAPPED);
            p.setTaskQty(new BigDecimal(com.zte.common.utils.NumConstant.NUM_ZERO));
            p.setRemark(Constant.DEAL_HISDATA_BY_NO_FACTORY);
        });
        List<List<PsTask>> splitTaskFromAps = CommonUtils.splitList(notNeedDealTasks, NUM_HUNDRED);
        for (List<PsTask> splitTaskTemp : splitTaskFromAps) {
            psTaskRepository.updateBatchPsTaskByCondition(splitTaskTemp);
        }
        List<String> notNeedDealProdNoList = notNeedDealTasks.stream().map(PsTask::getProdplanNo).distinct().collect(Collectors.toList());
        // 剔除APS不存在或没有数量的任务
        psTasks.removeIf(item -> notNeedDealProdNoList.contains(item.getProdplanNo()));
    }

    private void setFactoryIdForTask(List<PsTask> psTasks, List<SysLookupValues> sysLookValues) {
        for (PsTask psTask : psTasks) {
            for (SysLookupValues sysLookValue : sysLookValues) {
                List<String> orgIdList = Arrays.asList(sysLookValue.getRemark().split(COMMA));
                if (Objects.isNull(psTask.getOrgId())){
                    psTask.setFactoryId(new BigDecimal("53"));
                } else if (orgIdList.contains(psTask.getOrgId().toString())) {
                    psTask.setFactoryId(new BigDecimal(sysLookValue.getLookupMeaning()));
                }
                psTask.setTaskStatus(Constant.TASK_COMPLETED);
                psTask.setRemark(Constant.DEAL_HISDATA_BY_NO_FACTORY);
            }
        }
    }

    private void handlerInParams(Map<String, Object> map) {
        String inStatus = (String)map.get("inStatus");
        if (StringUtils.isNotBlank(inStatus)) {
            List<String> statusList = Arrays.asList(inStatus.replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
            map.put("statusList", statusList);
        }
    }

    @Override
    public PsTask getTaskInfoByTaskNo(String taskNo) {
        if (StringUtils.isBlank(taskNo)) {
            return null;
        }
        return psTaskRepository.getTaskInfoByTaskNo(taskNo);
    }

    @Override
    public String taskBomChangeableQuery(String taskNo) {
        // 基础校验
        if (StringUtils.isEmpty(taskNo)) {
            return CommonUtils.getLmbMessage(MessageId.APS_TASK_NO_IS_NULL);
        }
        // 查询所属工厂
        List<PsTask> psTaskList = psTaskRepository.selectPsTaskList(taskNo, Constant.SYS_TYPE_STEP);
        if (CollectionUtils.isEmpty(psTaskList)) {
            return CommonUtils.getLmbMessage(MessageId.TASK_NO_INFO_IS_NULL);
        }
        PsTask psTaskInfo = psTaskList.get(0);
        if (psTaskInfo.getFactoryId() == null) {
            return CommonUtils.getLmbMessage(MessageId.APS_TASK_NO_INFO_FACTORY_ID_IS_NULL);
        }
        //根据工厂ID获得主机地址，
        String factoryId = psTaskInfo.getFactoryId().toString();
        String address = sysLookupValuesService.getAddressByFactoryId(factoryId);
        if (StringUtils.isEmpty(address)) {
            return CommonUtils.getLmbMessage(MessageId.FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY);
        }
        // 调用openApi，通过其分发到本地工厂取校验。
        String result = openApiRemoteService.taskBomChangeableQueryDelegate(taskNo, factoryId, address);
        return result;
    }

    /**
     * 批量新增批次
     *
     * @param list 集合
     */
    @Override
    public List<PsTask> selectProdPlanIDBySpare(List<String> list, List<String> inforList) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<PsTask> result = new ArrayList<>();
        List<List<String>> lists = CommonUtils.splitList(list, Constant.INT_50);
        for (List<String> prodPlanidList : lists) {
            List<PsTask> psTasks = psTaskRepository.selectProdPlanIDWitnSpare(prodPlanidList, inforList);
            result.addAll(psTasks);
        }
        return result;
    }

    @Override
    public void updateConfirmationStatus(String confirmationStatus, String taskNo) {
        if (StringUtils.isBlank(taskNo) || StringUtils.isBlank(confirmationStatus)) {
            return;
        }
        psTaskRepository.updateConfirmationStatus(confirmationStatus, taskNo);
    }
    @Override
    public void batchUpdateByPK(List<PsTask> psTaskList) {
        CollUtil.split(psTaskList, BATCH_SIZE).forEach(psTaskRepository::batchUpdateByPK);
    }

    @Override
    public List<EntityQueryDTO> taskStockQuery(List<EntityQueryDTO> entityQueryDTOS) {
        // 获取所有 EntityNo 对应的 PsTask
        List<PsTask> psTaskList = getPsTask(ListUtil.toList(CollUtil.trans(entityQueryDTOS, EntityQueryDTO::getEntityNo)));
        Map<String, PsTask> taskMap = IterUtil.toMap(psTaskList, PsTask::getTaskNo);
        Map<String, Set<String>> factoryIdGroup = psTaskList.stream().filter(item -> ObjUtil.isNotNull(item.getFactoryId())).collect(Collectors.groupingBy(item -> Convert.toStr(item.getFactoryId()), Collectors.mapping(PsTask::getTaskNo, Collectors.toSet())));
        factoryIdGroup.remove(Constant.FACTORY_ID_CENTER);

        // 获取库存信息
        Collection<WarehouseEntryInfoDTO> warehouseEntryInfoDTOS = getWarehouseEntryInfoDTOS(factoryIdGroup, Arrays.asList("0", "2"));

        Map<String, Long> stockQtyMap = IterUtil.toMap(warehouseEntryInfoDTOS, WarehouseEntryInfoDTO::getTaskNo, WarehouseEntryInfoDTO::getStockQty);

        // 处理每个 DTO
        entityQueryDTOS.forEach(item -> {
            String entityNo = item.getEntityNo();
            PsTask psTask = taskMap.get(entityNo);

            long stockQty = Optional.ofNullable(psTask)
                    .filter(task -> StrUtil.equals(Convert.toStr(item.getStockOrgId()), Convert.toStr(task.getOrgId())))
                    .map(task -> stockQtyMap.get(entityNo))
                    .orElse(0L);

            item.setStockQty(stockQty);
        });

        return entityQueryDTOS;
    }

    private Collection<WarehouseEntryInfoDTO> getWarehouseEntryInfoDTOS(Map<String, Set<String>> factoryIdGroup, Collection<String> statuses) {
        return factoryIdGroup.entrySet().stream().flatMap(entry -> {
            WarehouseBatchQueryDTO warehouseBatchQueryDTO = new WarehouseBatchQueryDTO();
            warehouseBatchQueryDTO.setTaskNos(entry.getValue());
            warehouseBatchQueryDTO.setStatuses(statuses);
            return StreamUtil.of(productionmgmtRemoteService.taskStockQuery(warehouseBatchQueryDTO, entry.getKey()));
        }).collect(Collectors.toList());
    }

    private void setMaterialSignInfo(List<PsTask> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> itemNos = list.stream()
            .map(PsTask::getItemNo)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemNos)) {
            return;
        }

        try {
            CpqdQueryDTO queryDTO = new CpqdQueryDTO();
            queryDTO.setInstanceNo(itemNos);
            List<CpqdGbomDTO> gbomList = cpqdService.queryGbomList(queryDTO);

            if (CollectionUtils.isEmpty(gbomList)) {
                return;
            }
            
            Map<String, CpqdGbomDTO> itemNoToGbomMap = gbomList.stream()
                    .filter(gbom -> StringUtils.isNotBlank(gbom.getInstanceNo()))
                    .collect(Collectors.toMap(
                        CpqdGbomDTO::getInstanceNo,
                        item -> item,
                        (v1, v2) -> v1
                    ));

            for (PsTask psTask : list) {
                CpqdGbomDTO gbomDTO = itemNoToGbomMap.get(psTask.getItemNo());
                if (gbomDTO != null) {
                    psTask.setMaterialSign(gbomDTO.getMaterialSign());
                    psTask.setMaterialSignName(gbomDTO.getMaterialSignName());
                }
            }
        } catch (Exception e) {
            log.error("设置物料标识信息时发生异常", e);
        }
    }
}
