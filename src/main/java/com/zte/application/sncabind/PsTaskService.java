/**
 * 项目名称 : SnCaBinding
 * 创建日期 : 2019-07-11
 * 修改历史 :
 * 1. [2019-07-11] 创建文件 by 10243397
 **/
package com.zte.application.sncabind;

import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.aps.ApsResponseDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import com.zte.interfaces.dto.task.PsTaskTreeDTO;
import com.zte.interfaces.sncabind.dto.*;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 批次服务
 *
 * <AUTHOR>
 **/
public interface PsTaskService {
    long getPsTaskCountWithRouteDetail(Map<String, Object> map);

    /**
     * getList方法
     *
     * @param map        参数集
     * @param orderField 排序
     * @param order      order
     * @param curPage    当前页
     * @param pageSize   页值
     * @return list
     * @throws Exception 异常
     */
    List<PsTask> getPsTaskList(Map<String, Object> map, String orderField, String order, Long curPage,
                               Long pageSize) throws Exception;

    List<PsTask> getPsTaskListWithRouteDetail(Map<String, Object> map, String orderField, String order, Long curPage,
                                              Long pageSize) throws Exception;

    boolean setProdListByMBom (PsTaskDTO dto);

    /**
     * getList方法
     *
     * @param specifiedPsTaskDTO 页值
     * @return list
     */
    Page<SpecifiedPsTaskVO> getSpecifiedPsTaskList(SpecifiedPsTaskDTO specifiedPsTaskDTO);

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     * @return
     */
    long updatePsTaskByIdSelective(PsTask record);

    ServiceData searchForProdPlan(List<String> transferList);

    /**
     * 更新任务生产单位
     *
     * @param psTask
     * @return
     */
    ServiceData updatePsTaskForFactory(PsTask psTask) throws Exception;

    /**
     * 更新任务维护信息
     *
     * @param entity
     * @return
     */
    int updateTaskMaintainInfo(PsTask entity);

    /**
     * 任务拆分
     *
     * @param taskDto
     * @return
     */
    int splitTaskProcess(PsTaskDTO taskDto) throws Exception;

    /**
     * 取消发放
     *
     * @param taskDto
     * @return
     */
    int cancelGrant(PsTaskDTO taskDto) throws Exception;

    /**
     * 自动化测试增加实体数据
     *
     * @param
     **/
    String insertAutoTest(String empNo, String factoryId, PsTask psTask);

    /**
     * 根据批次查询所派发工厂
     *
     * @param prodplanIds
     * @return List<PsTaskDTO>
     * @Author: 10307315陈俊熙
     * @date 2021/9/9 下午2:06
     */
    List<PsTaskDTO> getFactoryIdByProdplanId(List<String> prodplanIds) throws Exception;

    List<PsTaskDTO> getPsTaskByItemNoList(List<String> itemNoList);

    List<PsTaskDTO> getPsTaskByProdplanIdList(List<String> prodplanIdList);

    /**
     * 查看标模任务状态
     *
     * @param request
     * @return
     */
    List<StandardModelTaskDTO> getStandardModelTaskStatus(HttpServletRequest request,
                                                          GetStandardModelTaskStatusDTO dto);

    /**
     * iFIS任务信息获取
     */
    List<IfisTaskDTO> getPsTaskForIfis(String taskNo, String prodplanId);

    void pilotTestBarcodeTestRecordUpload(List<TestInfoDTO> testInfoList, String empNo) throws Exception;

    /**
     * 导入模板下载
     *
     * @param response
     */
    void exportTaskExcel(HttpServletResponse response) throws Exception;

    /**
     * 文件解析
     **/
    List<PsTask> uploadStandardModelTaskBatch(InputStream inputStream);

    /**
     * 文件校验
     **/
    List<PsTask> checkTaskBatch(List<PsTask> list);

    /**
     * 批量更新
     **/
    int batchUpdate(List<PsTask> list, String factoryId, String empNo) throws Exception;

    void exportTaskQueryTable(HttpServletResponse response, PsTaskExportDTO dto) throws Exception;

    /**
     * 获取redis缓存
     *
     * @param redisKey key
     */
    Object queryCacheRows(String redisKey);

    /**
     * 保存redis 缓存
     *
     * @param redisCacheDTO 缓存
     */
    void saveCacheRows(RedisCacheDTO redisCacheDTO);


    /**
     * 清除redis 缓存
     *
     * @param redisKey key
     */
    void deleteCacheRows(String redisKey);

    /**
     * 同步拉取任务到imes
     *
     * @param handlerFlag 手动标识
     * @param msg         kafka 消息
     * @throws Exception 业务异常
     */
    void pullTaskNoFromAps(Boolean handlerFlag, String msg) throws Exception;

    String getNextProdplanId();

    /**
     * 拉取任务实现
     *
     * @param handlerFlag
     * @param taskNoList  任务信息
     * @throws Exception 业务异常
     */
    void pullTaskNo(Boolean handlerFlag, List<ApsResponseDTO> taskNoList) throws Exception;

    /**
     * 消费 APS kafka 消息产生批次数据
     *
     * @param msg 消息
     * @throws Exception 业务异常
     */
    void consumerProdPlanIdMsg(String msg) throws Exception;

    /**
     * 消费 APS kafka 消息修改任务信息数据
     *
     * @param msg 消息
     * @throws Exception 业务异常
     */
    void consumerModifyProdPlanNoMsg(String msg) throws Exception;

    /**
     * 触发批次kafka信息 给aps
     *
     * @param taskNoList 任务号
     * @throws Exception 业务异常信息
     */
    void sendTaskMsgToAps(List<String> taskNoList) throws Exception;

    /**
     * APS 查询任务是否可以修改
     *
     * @param list 任务号
     * @throws Exception 业务异常
     */
    List<ApsResponseDTO> taskInfoChangeableQuery(List<ApsResponseDTO> list) throws Exception;

    /**
     * 批量新增批次
     *
     * @param list 集合
     */
    void insertDataBatch(List<PsTaskDTO> list);

    void callBackCenterUpdatePsTask(List<PsTask> psTaskList);

    /**
     * 批量获取批次信息，少量字段
     *
     * @param prodPlanIdList 批次
     * @return
     */
    List<PsTask> queryPsTaskBatch(List<String> prodPlanIdList);

    List<PsTask> getTaskQtyByProdplanId(List<String> prodPlanIdList);

    /**
     * 根据批次获取料单代码
     *
     * @param prodPlanId
     * @return
     */
    String getBomNoByProdplanId(String prodPlanId);

    /**
     * 根据批次查询环保属性
     *
     * @param prodPlanId
     * @return
     */
    String getLeadFlagDescByProdplanId(String prodPlanId);

    List<BoardInstructionCycleDataCreateDTO> selectGetDateByProdplanNo(List<String> planNos);

    List<PsTaskDTO> selectErpStatusByProdplanId(List<String> prodplanList);

    List<PsTask> queryFactoryIdByProdIdList(List<String> prodPlanIdList);

    List<PsTask> selectPsTaskByProdIdSet(Set<String> prodIdSet);

    PsTaskDTO getPsTaskByTaskNo(String taskNo);

    List<PsTask> getFactoryIdByProdId(List<String> prodPlanIdList);

    void validateParams(PsTaskDTO dto);

    TaskBrandChangeStatusDTO getBrandChangeStatusByTaskNo(String taskNo) throws Exception;

    /**
     * 更新套料标识
     *
     * @param allocationQueryDTO 参数
     * @return
     */
    int batchUpdateInforExe(AllocationQueryDTO allocationQueryDTO);

    List<PsTask> selectItemCodeByProd(List<String> prods);

    TechnicalAndLockInfoDTO getTechnicalAndLockInfoByProdplanId(String prodplanId) throws Exception;

    /**
     * 获取任务号对应子卡批次信息 树形结构
     *
     * @param taskNos  任务号
     * @param needRoot 是否需要完成树，还是从本身开始，默认完成
     * @return 树形结构数据
     */
    List<PsTaskTreeDTO> getSubTaskTreeByTaskNo(List<String> taskNos, String needRoot);

    List<String> getWmesTaskNo(List<String> taskNoList);

    List<PsTask> getPsTask(List<String> taskNoList);
    DealPsWipInfoParamDTO updateLocalFactoryQty(PsTaskDTO dto)throws Exception;

    int dealHisDataWithNoFactoryId(PsTaskDTO dto) throws Exception;

    PsTask getTaskInfoByTaskNo(String taskNo);

    String taskBomChangeableQuery(String taskNo);

    List<PsTask> queryFactoryIdByTaskNoList(List<String> taskNoList);

    void addRetryNumberOrSaveKafkaMsg(Exception e, String msg, String topic, String topicKey, String msgId) throws Exception;

    /**
     * 查询产生调拨单的批次
     * @param list 批次集合
     * @param inforList 调拨状态
     * @return
     */
    List<PsTask> selectProdPlanIDBySpare(List<String> list,List<String> inforList);

    /**
     * 按主键批量更新
     * @param psTaskList
     */
    void batchUpdateByPK(List<PsTask> psTaskList);

    /**
     * 根据任务号更新任务转正状态
     * @param confirmationStatus 转正状态
     * @param taskNo 任务号
     */
    void updateConfirmationStatus(String confirmationStatus, String taskNo);

    /**
     * 根据任务号和组织ID查询入库数量
     *
     * @param entityQueryDTOS
     * @return
     */
    List<EntityQueryDTO> taskStockQuery(List<EntityQueryDTO> entityQueryDTOS);

    /**
     * 根据料单代码判断是否阿里任务
     * @param itemNos
     * @return
     */
    Map<String, Boolean> judgeAlibabaTask(Collection<String> itemNos);

    List<EntityQueryDTO> queryPlannedTaskQty(List<EntityQueryDTO> entityQueryDTOS);
}
