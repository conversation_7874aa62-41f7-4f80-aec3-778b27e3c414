package com.zte.application;

import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;

import java.util.List;

/**
 * 单板推送信息明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-24 15:30:55
 */
public interface PushBoardDataDetailService {

    int merge(List<PushBoardDataDetailDTO> list);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<PushBoardDataDetailDTO> list);

    void pushAoiDataToB2B(int preDays);

    /**
     * 同步数据推送状态
     * @param pushBoardDataProcessDTO  方法
     */
    void syncPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    void syncHeadPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    void syncDetailPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO);
}

