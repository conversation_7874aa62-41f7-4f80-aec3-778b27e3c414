package com.zte.application.tally;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.zte.common.utils.Constant;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.tally.TallyPackingService;
import com.zte.domain.model.infor.SkuRepository;
import com.zte.domain.model.material.StDeliveryRepository;
import com.zte.domain.model.material.StTallyPackingDetailRepository;
import com.zte.domain.model.material.StTallyPackingHeadRepository;
import com.zte.domain.model.material.StWarehouseRepository;
import com.zte.interfaces.infor.dto.SkuQueryDTO;
import com.zte.interfaces.tally.dto.BoxDetailDTO;
import com.zte.interfaces.tally.dto.BoxQtyDTO;
import com.zte.interfaces.tally.dto.InforBillDTO;
import com.zte.interfaces.tally.dto.InterfaceFreightOrderContainerDTO;
import com.zte.interfaces.tally.dto.InterfaceFreightOrderContainerItemDTO;
import com.zte.interfaces.tally.dto.InterfaceFreightOrderDTO;
import com.zte.interfaces.tally.dto.InterfaceResponseDTO;
import com.zte.interfaces.tally.dto.StockDTO;
import com.zte.interfaces.tally.dto.StockQueryDTO;
import com.zte.interfaces.tally.dto.TallyDetailDTO;
import com.zte.interfaces.tally.dto.TallyHeadDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusinessConstant;

/**
 * 理货打包服务
 * <AUTHOR>
 * */
@Service
public class TallyPackingServiceImpl implements TallyPackingService {

	@Autowired
	private StTallyPackingDetailRepository stTallyPackingDetailRepository;

	@Autowired
	private StTallyPackingHeadRepository stTallyPackingHeadRepository;

	@Autowired
	private StDeliveryRepository stDeliveryRepository;

	@Autowired
	private StWarehouseRepository stWarehouseRepository;

	@Autowired
	private SkuRepository skuRepository;

	Logger logger = LoggerFactory.getLogger(this.getClass());


	/**
	 * 获取箱数和总物料数量
	 * <AUTHOR>
	 * */
	private BoxQtyDTO getQty(String sourceNo,String sourceResp) {
		BoxQtyDTO boxQtyDTO=new BoxQtyDTO();
		// 查询所有的外部单号
		int boxNum = 0;
		int pieceNum = 0;
		List<String> pList = new ArrayList<>();
		pList.add(sourceNo);
		List<TallyDetailDTO> tallyDetailDTOs= stTallyPackingDetailRepository.selectStTallyPackingDetailList(pList);
		if (Tools.isEmpty(tallyDetailDTOs)) {
			boxQtyDTO.setBoxNum(boxNum);
			boxQtyDTO.setPieceNum(pieceNum);
			return boxQtyDTO;
		}
		List<String> boxList = new ArrayList<>();
		for (TallyDetailDTO tallyDetailDTO : tallyDetailDTOs) {
			boxList.add(tallyDetailDTO.getContainerNo());
		}
		List<BoxDetailDTO> boxDetailDTOs=new ArrayList<>();
		boxQtyDTO.setStartNo(tallyDetailDTOs.get(0).getStartNo());
		switch (tallyDetailDTOs.get(0).getStartNo()) {
			case BusinessConstant.OTHER_STR:
				boxNum = 1;
				pieceNum = 1;
				break;
			case BusinessConstant.WMS_STR:
				boxNum = 1;
				boxDetailDTOs = stDeliveryRepository.selectStDeliveryList(boxList);
				pieceNum = boxDetailDTOs.stream().mapToInt(mapper -> mapper.getPiece()).sum();
				break;
			case BusinessConstant.OMS_STR:
				boxNum = 1;
				boxDetailDTOs = stDeliveryRepository.selectStDeliveryList(boxList);
				pieceNum = boxDetailDTOs.stream().mapToInt(mapper -> mapper.getPiece()).sum();
				break;
			case BusinessConstant.LPN_STR:
				boxNum = tallyDetailDTOs.size();
				boxDetailDTOs = stDeliveryRepository.selectStDeliveryList(boxList);
				pieceNum = boxDetailDTOs.stream().mapToInt(mapper -> mapper.getPiece()).sum();
				break;
			case BusinessConstant.LFID_STR:
				// 通过班组获取仓库
				StockQueryDTO record = new StockQueryDTO();
				record.setAreaName(sourceResp);
				StockDTO stockDTO = stWarehouseRepository.selectStWarehouse(record);
				if (null == stockDTO) {
					pieceNum = 0;
				} else {
					String whseId = stockDTO.getStockName();
					List<SkuQueryDTO> skuQueryDTOs = new ArrayList<>();
					for (TallyDetailDTO tallyDetailDTO : tallyDetailDTOs) {
						SkuQueryDTO skuQueryDTO = new SkuQueryDTO();
						skuQueryDTO.setSku(tallyDetailDTO.getContainerNo());
						skuQueryDTO.setWhseid(whseId);
						skuQueryDTOs.add(skuQueryDTO);
					}
					boxDetailDTOs = skuRepository.selectLfid(skuQueryDTOs);
				}
				boxNum=boxDetailDTOs.stream().collect(Collectors.groupingBy(BoxDetailDTO::getToId)).size();
				pieceNum = boxDetailDTOs.stream().mapToInt(mapper -> mapper.getPiece()).sum();
				break;
			default:
				List<InforBillDTO> inforBillDTOs = skuRepository.selectEdiSoAll(boxList);
				if (Tools.isEmpty(inforBillDTOs)) {
					pieceNum = 0;
				} else {
					boxDetailDTOs = skuRepository.selectExtNo(inforBillDTOs);
				}
				boxNum=boxDetailDTOs.stream().collect(Collectors.groupingBy(BoxDetailDTO::getContainerNo)).size();
				pieceNum = boxDetailDTOs.stream().mapToInt(mapper -> mapper.getPiece()).sum();
				break;
		}
		boxQtyDTO.setBoxNum(boxNum);
		boxQtyDTO.setPieceNum(pieceNum);
		boxQtyDTO.setBoxDetailDTOs(boxDetailDTOs);
		boxQtyDTO.setTallyDetailDTOs(tallyDetailDTOs);
		return boxQtyDTO;

	}
	/**
	 * 查询货运扩展信息
	 * */
	@Override
	public InterfaceResponseDTO getInterfaceResponse(String sourceNo) {
		List<String> sourceNos =new ArrayList<>();
		sourceNos.add(sourceNo);
		InterfaceResponseDTO interfaceResponseDTO=new InterfaceResponseDTO();
		List<TallyHeadDTO> tallyHeadDTOs=stTallyPackingHeadRepository.selectStTallyPackingHeadList(sourceNos);
		if(Tools.isEmpty(tallyHeadDTOs)){
			return interfaceResponseDTO;
		}
		BoxQtyDTO qtyDTO=getQty(sourceNo, tallyHeadDTOs.get(0).getSourceRepository());
		TallyHeadDTO tallyHeadDTO=tallyHeadDTOs.get(0);
		InterfaceFreightOrderDTO orderBase=new InterfaceFreightOrderDTO();
		orderBase.setSourceNo(tallyHeadDTO.getSourceNo());
		orderBase.setBusinessRegion("business_region_domestic");
		orderBase.setBusinessType("dic_business_type_level_one_2");
		orderBase.setBusinessTypeLevelTwo("dic_business_type_level_two_4");
		orderBase.setOrderStatus("101_draft");
		orderBase.setFeeBelongDepartment(tallyHeadDTO.getFeeBelongDepartment());
		orderBase.setPalletNum(1);
		orderBase.setUrgentDispatchFlag("E");
		orderBase.setWeight(tallyHeadDTO.getWeight());
		orderBase.setVolume(tallyHeadDTO.getVolume());
		orderBase.setShippingFromPlaceNo(tallyHeadDTO.getShippingFromPlaceNo());
		orderBase.setShippingToPlaceNo(tallyHeadDTO.getShippingToPlaceNo());
		orderBase.setConsigneeContact(tallyHeadDTO.getConsigneeContact());
		orderBase.setConsigneeName(tallyHeadDTO.getConsigneeName());
		orderBase.setConsigneeCompany(BusinessConstant.SEND_COMPANY);//配送部 收货人单位
		orderBase.setConsignorName(tallyHeadDTO.getConsignorName());
		orderBase.setConsignorContact(tallyHeadDTO.getConsignorContact());
		orderBase.setCreatedOrderBy(tallyHeadDTO.getCreatedOrderBy());
		orderBase.setCreatedOrderByName(tallyHeadDTO.getCreatedOrderByName());
		orderBase.setCreatedOrderDate(tallyHeadDTO.getCreatedOrderDate());
		orderBase.setOrderCreateType("1");//指令创建方式
		orderBase.setOrderInterfaceSource("iWMS");//指令接口来源
		String requireTransportTypeLevelOne=getTransferType(tallyHeadDTO.getShippingType(),BusinessConstant.LANG_CN, Constant.SHIPPING_TYPE_LEVEL_ONE);
		if(StringUtils.isEmpty(requireTransportTypeLevelOne)){
			requireTransportTypeLevelOne=getTransferType(tallyHeadDTO.getShippingType(),BusinessConstant.LANG_EN, Constant.SHIPPING_TYPE_LEVEL_ONE);
		}
		orderBase.setRequireTransportTypeLevelOne(requireTransportTypeLevelOne);

		orderBase.setBoxNum(qtyDTO.getBoxNum());//箱总数
		orderBase.setPieceNum(qtyDTO.getPieceNum());//装箱数量
		//要求运输方式大类
		interfaceResponseDTO.setOrderBase(orderBase);
		List<InterfaceFreightOrderContainerDTO> palletList=new ArrayList<>();
		InterfaceFreightOrderContainerDTO interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
		interfaceFreightOrderContainerDTO.setSourceNo(tallyHeadDTO.getSourceNo());
		interfaceFreightOrderContainerDTO.setContainerNo(tallyHeadDTO.getSourceNo());
		interfaceFreightOrderContainerDTO.setPalletFlag(Constant.STR_YES);
		interfaceFreightOrderContainerDTO.setBulkVolume(tallyHeadDTO.getVolume());
		interfaceFreightOrderContainerDTO.setBulkWeight(tallyHeadDTO.getWeight());

		interfaceFreightOrderContainerDTO.setBoxNum(qtyDTO.getBoxNum());//箱总数
		interfaceFreightOrderContainerDTO.setPieceNum(qtyDTO.getPieceNum());//装箱数量


		String boxName=Constant.STR_BAG;
		if(!boxName.equals(tallyHeadDTO.getPackageTypeName())){
			boxName=Constant.STR_BOX;
		}
		interfaceFreightOrderContainerDTO.setPackageTypeName(boxName);
		palletList.add(interfaceFreightOrderContainerDTO);
		interfaceResponseDTO.setPalletList(palletList);
		if(qtyDTO.getTallyDetailDTOs()==null ||qtyDTO.getTallyDetailDTOs().isEmpty()){
			return interfaceResponseDTO;
		}
		if(qtyDTO.getBoxDetailDTOs()==null ||qtyDTO.getBoxDetailDTOs().isEmpty()){
			return interfaceResponseDTO;
		}
		interfaceResponseDTO=geContainerList(tallyHeadDTO.getSourceNo(),interfaceResponseDTO,qtyDTO.getTallyDetailDTOs(),qtyDTO.getBoxDetailDTOs(),qtyDTO.getStartNo());
		return interfaceResponseDTO;

	}
	/**
	 * 返回去重的箱数
	 * */
	private List<String> getBoxList(List<BoxDetailDTO> boxDetailDTOs ){
		List<String> list=new ArrayList<>();
		for (BoxDetailDTO boxDetailDTO : boxDetailDTOs) {
			if(!list.contains(boxDetailDTO.getToId())){
				list.add(boxDetailDTO.getToId());
			}
		}
		return list;
	}
	/**
	 * 查询箱信息和想明细信息
	 * */
	private InterfaceResponseDTO geContainerList(String platNo,InterfaceResponseDTO interfaceResponseDTO,List<TallyDetailDTO> tallyDetailDTOs,List<BoxDetailDTO> boxDetailDTOs,String startNo){
		List<InterfaceFreightOrderContainerDTO> list=new ArrayList<>();
		for (TallyDetailDTO tallyDetailDTO : tallyDetailDTOs) {
			InterfaceFreightOrderContainerDTO interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
			int pieceNum=0;
			switch (startNo) {
				case BusinessConstant.OTHER_STR:
				case BusinessConstant.WMS_STR:
				case BusinessConstant.OMS_STR:
					interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
					interfaceFreightOrderContainerDTO.setSourceNo(tallyDetailDTO.getSourceNo());
					interfaceFreightOrderContainerDTO.setParentContainerNo(tallyDetailDTO.getParentContainerNo());
					interfaceFreightOrderContainerDTO.setBulkVolume(tallyDetailDTO.getBulkVolume());
					interfaceFreightOrderContainerDTO.setBulkWeight(tallyDetailDTO.getBulkWeight());
					interfaceFreightOrderContainerDTO.setContainerNo(tallyDetailDTO.getContainerNo());//箱号
					interfaceFreightOrderContainerDTO.setBoxNum(1);
					interfaceFreightOrderContainerDTO.setPieceNum(1);
					list.add(interfaceFreightOrderContainerDTO);
					break;
				case BusinessConstant.LPN_STR:
					interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
					interfaceFreightOrderContainerDTO.setSourceNo(tallyDetailDTO.getSourceNo());
					interfaceFreightOrderContainerDTO.setParentContainerNo(tallyDetailDTO.getParentContainerNo());
					interfaceFreightOrderContainerDTO.setBulkVolume(tallyDetailDTO.getBulkVolume());
					interfaceFreightOrderContainerDTO.setBulkWeight(tallyDetailDTO.getBulkWeight());
					interfaceFreightOrderContainerDTO.setContainerNo(tallyDetailDTO.getContainerNo());//箱号
					pieceNum = boxDetailDTOs.stream().filter(predicate->predicate.getContainerNo().equals(tallyDetailDTO.getContainerNo())).mapToInt(mapper -> mapper.getPiece()).sum();
					interfaceFreightOrderContainerDTO.setBoxNum(1);
					interfaceFreightOrderContainerDTO.setPieceNum(pieceNum);
					list.add(interfaceFreightOrderContainerDTO);
					break;
				case BusinessConstant.LFID_STR:
					List<String> boxList=getBoxList(boxDetailDTOs);
					for (String str : boxList) {
						interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
						interfaceFreightOrderContainerDTO.setSourceNo(tallyDetailDTO.getSourceNo());
						interfaceFreightOrderContainerDTO.setParentContainerNo(tallyDetailDTO.getParentContainerNo());
						interfaceFreightOrderContainerDTO.setBulkVolume(tallyDetailDTO.getBulkVolume());
						interfaceFreightOrderContainerDTO.setBulkWeight(tallyDetailDTO.getBulkWeight());
						interfaceFreightOrderContainerDTO.setContainerNo(str);
						pieceNum = boxDetailDTOs.stream().filter(p->p.getToId().equals(str)).mapToInt(mapper -> mapper.getPiece()).sum();
						interfaceFreightOrderContainerDTO.setBoxNum(1);
						interfaceFreightOrderContainerDTO.setPieceNum(pieceNum);
						list.add(interfaceFreightOrderContainerDTO);
					}
					break;
				default:
					List<String> boxListExt=getBoxList(boxDetailDTOs);
					for (String str : boxListExt) {
						interfaceFreightOrderContainerDTO=new InterfaceFreightOrderContainerDTO();
						interfaceFreightOrderContainerDTO.setSourceNo(tallyDetailDTO.getSourceNo());
						interfaceFreightOrderContainerDTO.setParentContainerNo(tallyDetailDTO.getParentContainerNo());
						interfaceFreightOrderContainerDTO.setBulkVolume(tallyDetailDTO.getBulkVolume());
						interfaceFreightOrderContainerDTO.setBulkWeight(tallyDetailDTO.getBulkWeight());
						interfaceFreightOrderContainerDTO.setContainerNo(str);
						pieceNum = boxDetailDTOs.stream().filter(p->p.getToId().equals(str)).mapToInt(mapper -> mapper.getPiece()).sum();
						interfaceFreightOrderContainerDTO.setBoxNum(1);
						interfaceFreightOrderContainerDTO.setPieceNum(pieceNum);
						list.add(interfaceFreightOrderContainerDTO);
					}
					break;
			}
		}
		interfaceResponseDTO.setBoxList(list);
		if(Tools.isNotEmpty(boxDetailDTOs)){
			interfaceResponseDTO.setBoxItemList(geContainerItemList(boxDetailDTOs));
		}
		return interfaceResponseDTO;
	}
	/**
	 * 查询箱明细信息
	 * */
	private List<InterfaceFreightOrderContainerItemDTO> geContainerItemList(List<BoxDetailDTO> boxDetailDTOs){
		List<InterfaceFreightOrderContainerItemDTO> list=new ArrayList<>();

		if(Tools.isEmpty(boxDetailDTOs)){
			return list;
		}
		for (BoxDetailDTO boxDetailDTO : boxDetailDTOs) {
			InterfaceFreightOrderContainerItemDTO dto=new InterfaceFreightOrderContainerItemDTO();
			//箱号
			dto.setContainerNo(boxDetailDTO.getContainerNo());
			//物料代码
			dto.setMaterialCode(boxDetailDTO.getMaterialCode());
			//物料名称
			dto.setMaterialName(boxDetailDTO.getMaterialName());
			//数量
			dto.setPiece(boxDetailDTO.getPiece());
			list.add(dto);
		}
		return list;
	}
	/**
	 * 查询运输方式大类，中文英文两次获取，默认中文
	 * */
	private String getTransferType(String name, String language, String dictionaryTypeCode){
		//BusinessConstant.LANG_CN
		Map<String, String> headerParamsMap = new HashMap<String, String>();
		headerParamsMap.put("Content-Type",MicroServiceNameEum.CONTENT_TYPE);
		String params="{ \"code\": \"\", \"dictionaryTypeCode\": \""+dictionaryTypeCode+"\", \"dictionaryValue\": \"\", \"enabledFlag\": \"\", \"language\": \""+language+"\", \"name\": \""+name+"\", \"pageCurrent\": 0, \"pageSize\": 0}";
		String url=MicroServiceNameEum.LMS_FIND_DIC;
		ServiceData<?> result= RemoteServiceDataUtil.invokeService(MicroServiceNameEum.ILMSSERVICE, MicroServiceNameEum.VERSION,
				MicroServiceNameEum.SENDTYPEPOST, url, params, headerParamsMap);

		if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
			logger.error("获取字典表失败"+result.getCode().getMsg());
			return "";
		}
		List<JSONObject> invokeMsg= (List<JSONObject>) result.getBo();
		if(Tools.isEmpty(invokeMsg)){
			logger.error("获取字典表失败，数据为空");
			return "";
		}
		//只取第一条
		int singleDataIndex=0;
		JSONObject json=invokeMsg.get(singleDataIndex);
		Object code= json.get("code");
		if(null!=code){
			return code.toString();
		}
		return "";
	}

}
