package com.zte.application.impl;

import com.zte.application.PushBoardDataHeadService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushBoardDataHeadRepository;
import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service("pushBoardDataHeadService")
public class PushBoardDataHeadServiceImpl implements PushBoardDataHeadService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PushBoardDataHeadRepository pushBoardDataHeadrepository;

    @Autowired
    private PushBoardDataHeadService pushBoardDataHeadService;


    @Override
    public int merge(List<PushBoardDataHeadDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumConstant.NUM_ZERO;
        }
        List<String> prodplanIdList = list.stream().map(PushBoardDataHeadDTO::getProdplanId).distinct().collect(Collectors.toList());
        // 查询已存在批次
        List<String> existProdplanIdList = pushBoardDataHeadrepository.getExistProdplanId(prodplanIdList);
        // 筛选需要新增批次
        List<PushBoardDataHeadDTO> notExistList = list.stream().filter(curr -> !existProdplanIdList.contains(curr.getProdplanId())).collect(Collectors.toList());
        // 批量新增(直接调用,无需事务控制)
        return this.batchInsert(notExistList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<PushBoardDataHeadDTO> list) {
        int count = NumConstant.NUM_ZERO;
        if (CollectionUtils.isEmpty(list)) {
            return count;
        }
        // 分批
        List<List<PushBoardDataHeadDTO>> listOfList = CommonUtils.splitList(list);
        for (List<PushBoardDataHeadDTO> tempList : listOfList) {
            count += pushBoardDataHeadrepository.batchInsert(tempList);
        }
        return count;
    }

    @Override
    public List<PushBoardDataHeadDTO> getNotPushedList(Date startTime, String lastProdplanId, int limit) {
        return pushBoardDataHeadrepository.getNotPushedList(startTime, lastProdplanId, limit);
    }

    @Override
    public void updateStatusByProdplanId(List<String> prodplanIdList, int pushStatus) {
        pushBoardDataHeadrepository.updateStatusByProdplanId(prodplanIdList, pushStatus);
    }
    @Override
    public List<String> getNotPushDoneProdplanId(List<String> customerNameList, int rows, int page) {
        if (CollectionUtils.isEmpty(customerNameList)) {
            return new ArrayList<>();
        }
        if (rows <= NumConstant.NUM_ZERO || rows > NumConstant.NUM_500) {
            rows = NumConstant.NUM_500;
        }

        if (page <= NumConstant.NUM_ZERO) {
            page = NumConstant.NUM_ONE;
        }
        return pushBoardDataHeadrepository.getNotPushDoneProdplanId(customerNameList, rows, page);
    }
}