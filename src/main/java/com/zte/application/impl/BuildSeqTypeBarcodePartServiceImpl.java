package com.zte.application.impl;

import com.zte.application.BuildBarcodePartService;
import com.zte.application.SimpleFactoryService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.StringConstant;
import com.zte.domain.model.BarcodeParamDefinition;
import com.zte.interfaces.dto.BarcodeGenerateDTO;
import com.zte.springbootframe.common.redis.RedisLock;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;


/**
 * 按日期类型生成条码部分字符串
 * <AUTHOR>
 */
@Service("buildSeqTypeBarcodePartService")
public class BuildSeqTypeBarcodePartServiceImpl implements BuildBarcodePartService {

    @Autowired
    private  RedisTemplate<String, Object> redisTemplate ;

    @Value("#{${imes.category.subcategory.labelname.to.start.seq:{'通用条码-itemSN-物料条码':'100'}}}")
    private Map<String, Integer> categoryAndSubCateAndLabelNameToStartSeq;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String TYPE= "1";

    @Autowired
    private SimpleFactoryService simpleFactoryService;

    @PostConstruct
    public void init(){
        simpleFactoryService.registerBuildBarcodePartService(TYPE,this);
    }

    @Override
    public String buildPart(BarcodeGenerateDTO dto, BarcodeParamDefinition definition,Object ...objects) throws
            Exception {
        String redisKey = generateKey(dto.getCategory(),dto.getSubCategory(),dto.getLabelName(),
                definition.getInitCycle(),objects);
        logger.info("buildPart:redisKey is {}",redisKey);
        //起始值要从startSeq-1开始，因为如果起始值是1,后面生成的序列号都是每次+1
        //如果不减一，则第一个序列号就是2
        long startSeq = Long.valueOf(definition.getStartSeq())-1;
        initSeq(redisKey,startSeq);
        StringBuilder partBuilder = new StringBuilder();
        for(int i=0; i < dto.getBarcodeNum(); i++){
            long seq = getNextSeq(redisKey, definition, dto.getCategory(), dto.getSubCategory(), dto.getLabelName());
            String part = StringUtils.leftPad(String.valueOf(seq),definition.getStartSeq().length(),StringConstant.STRING_0);
            partBuilder.append(part).append(",");
        }
        return partBuilder.substring(0,partBuilder.length()-1);
    }

    private  String generateKey(String category,String subCategory,String labelName,String initCycle,Object ...objects) throws NoSuchAlgorithmException {
        StringBuilder keyBuilder = new StringBuilder();
        String cycle = loadCycle(initCycle);
        keyBuilder.append(this.getClass().getSimpleName())
                .append(category).append(subCategory).append(labelName)
                .append(cycle);
        for(Object obj : objects){
            keyBuilder.append(String.valueOf(obj));
        }
        String key = keyBuilder.toString() ;
        return DigestUtils.sha256Hex(key);
    }



    private void initSeq(String redisKey,long startSeq){
        Object seq = redisTemplate.boundValueOps(redisKey).get();
        if(seq == null ){
            String redisLockKey = redisKey + "_lock";
            RedisLock redisLock = new RedisLock(redisLockKey);
            if(redisLock.lock()){
                seq = redisTemplate.boundValueOps(redisKey).get();
                if(seq == null){
                    redisTemplate.boundValueOps(redisKey).set(startSeq);
                }
            }
        }
    }

    /**
     * 获取下一个序列号
     * @param redisKey Redis键
     * @param definition 条码参数定义
     * @param category 类别
     * @param subCategory 子类别
     * @param labelName 标签名称
     * @return 下一个序列号
     */
    private long getNextSeq(String redisKey, BarcodeParamDefinition definition, String category, String subCategory, String labelName) {
        // 获取当前Redis中的值
        Object currentValue = redisTemplate.boundValueOps(redisKey).get();
        long currentSeq = 0L;
        
        if (currentValue != null) {
            currentSeq = Long.valueOf(currentValue.toString());
        }

        // 构建配置键
        String key = category + Constant.LINE + subCategory + Constant.LINE + labelName;
        Integer threshold = categoryAndSubCateAndLabelNameToStartSeq.get(key);
        
        // 如果没有找到配置，则跳过不比较，
        if (threshold == null) {
            return redisTemplate.boundValueOps(redisKey).increment(1L);
        }
        
        // 判断是否小于阈值
        if (currentSeq < threshold) {
            // 如果小于阈值，使用配置的默认值
            long defaultValue = threshold;
            logger.info("Current seq {} is less than threshold {}, using default value: {}", 
                       currentSeq, threshold, defaultValue);
            
            // 设置新的值并返回
            redisTemplate.boundValueOps(redisKey).set(defaultValue);
            return defaultValue;
        } else {
            // 如果大于等于阈值，则加1返回
            long nextSeq = redisTemplate.boundValueOps(redisKey).increment(1L);
            logger.info("Current seq {} is greater than or equal to threshold {}, incrementing to: {}", 
                       currentSeq, threshold, nextSeq);
            return nextSeq;
        }
    }

    private static String loadCycle(String initCycle){
        LocalDateTime ldt = LocalDateTime.now();
        String pattern = "";
        if(StringConstant.STRING_0.equals(initCycle)){
            pattern = StringConstant.FORMATTER_YYYYMMDD;
        } else if(StringConstant.STRING_1.equals(initCycle)){
            pattern = StringConstant.FORMATTER_YYYYW;
        } else if(StringConstant.STRING_2.equals(initCycle)){
            pattern = StringConstant.FORMATTER_YYYYMM;
        } else if(StringConstant.STRING_3.equals(initCycle)){
            pattern = StringConstant.FORMATTER_YYYY;
        } else {
            return "" ;
        }
        return  ldt.format(DateTimeFormatter.ofPattern(pattern));
    }



}
