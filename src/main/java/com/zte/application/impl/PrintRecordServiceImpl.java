package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.HrmUserCenterService;
import com.zte.application.PrintRecordService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.PrintRecord;
import com.zte.domain.model.PrintRecordRepository;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Author: panXu
 * @Date: 2020/7/23 10:18
 * @Description:
 */
@Service("printRecordService")
public class PrintRecordServiceImpl extends AbstractExportTaskHandler<PrintRecordDTO, PrintRecord> implements PrintRecordService {
    @Autowired
    private PrintRecordRepository printRecordRepository;

    @Autowired
    private HrmUserCenterService hrmUserCenterService;

    @Override
    public ServiceData queryRecordById(String recordId) {
        PrintRecord printRecord =  printRecordRepository.selectByPrimaryKey(recordId);
        if(StringUtils.isNotBlank(printRecord.getPrintData()))
        {
            Object object = JSON.parseObject(printRecord.getPrintData());
            printRecord.setPrintDataJson(object);
        }
        return ServiceDataUtil.getSuccess(printRecord);
    }

    @Override
    public ServiceData queryRecordBySn(String sn) {
        PrintRecord printRecord =  printRecordRepository.queryRecordBySn(sn);
        if(printRecord!=null &&StringUtils.isNotBlank(printRecord.getPrintData()))
        {
            Object object = JSON.parseObject(printRecord.getPrintData());
            printRecord.setPrintDataJson(object);
        }
        return ServiceDataUtil.getSuccess(printRecord);
    }

    @Override
    public ServiceData insertRecord(PrintRecord printRecord) {
        printRecord.setPrintId(StringUtils.isBlank(printRecord.getPrintId()) ? UUID.randomUUID().toString() : printRecord.getPrintId());
        PrintRecord printRecordModel = printRecordRepository.selectByPrimaryKey(printRecord.getPrintId());
        Integer result;
        if(printRecordModel == null)
        {
            result = printRecordRepository.insertSelective(printRecord);
        }
        else
        {
            result = printRecordRepository.updateByPrimaryKey(printRecord);
        }
        return ServiceDataUtil.getSuccess(result);
    }

    //当日对应模板打印数量
    @Override
    public ServiceData getPrintTemplateIdToday(String printTemplateId){
        ServiceData serviceData = ServiceDataBuilderUtil.success();
        serviceData.setBo(printRecordRepository.getPrintTemplateIdToday(printTemplateId));
        return serviceData;
    }

    @Override
    public ServiceData getTrayCodeByPalletNo(String palletNo){
        ServiceData serviceData = ServiceDataBuilderUtil.success();
        serviceData.setBo(printRecordRepository.getTrayCodeByPalletNo(palletNo));
        return serviceData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceData batchInsert(List<PrintRecord> list){
        ServiceData data = ServiceDataBuilderUtil.success();
        List<List<PrintRecord>> lists= CommonUtils.splitList(list, NumConstant.NUM_HUNDRED);
        long insertCount=0L;
        for(List<PrintRecord> tempList:lists){
            insertCount+= printRecordRepository.batchInsert(tempList);
        }
        data.setBo(insertCount);
        return data;
    }

    /**
     * 根据条码获取补打数据
     */
    @Override
    public List<PrintRecord> getListBySns(String sn)throws Exception{
        if(StringUtils.isEmpty(sn)){
            return new ArrayList<>();
        }
        List<String> inSnList = Arrays.asList(sn.replaceAll("'","").split(Constant.COMMA));
        return printRecordRepository.getListBySns(inSnList);
    }

    /**
     * 根据条码获取补打数据
     */
    @Override
    public Page<PrintRecord> getPage(PrintRecordDTO dto)throws Exception{
        //至少有一个条件
        if (StringUtils.isEmpty(dto.getCreateDateStart()) && StringUtils.isEmpty(dto.getUpdateDateStart())
                && CollectionUtils.isEmpty(dto.getSnList()) && CollectionUtils.isEmpty(dto.getItemCodeList())) {
           throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_ERROR);
        }
        Page<PrintRecord> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        pageInfo.setParams(dto);

        List<PrintRecord> list = printRecordRepository.getList(pageInfo);

        //转化工号为姓名+工号
        try {
            setUserName(list);
        }catch (Exception e){
        }
        pageInfo.setRows(list);
        return pageInfo;
    }

    //转化工号
    private void setUserName(List<PrintRecord> printRecords) throws Exception {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(printRecords)){
            return;
        }
        //处理人
        Set set=new HashSet();
        printRecords.forEach(p->{
            if(!StringUtils.isEmpty(p.getUpdateBy())){
                set.add(p.getUpdateBy());
            }
            if(!StringUtils.isEmpty(p.getCreateBy())){
                set.add(p.getCreateBy());
            }
        });
        List<String> userIdList=new ArrayList<>(set);
        //获取姓名
        Map<String, HrmPersonInfoDTO> bsPubHrMap = hrmUserCenterService.getHrmPersonInfo(userIdList);
        printRecords.forEach(p->{
            if(!StringUtils.isEmpty(p.getUpdateBy())&&bsPubHrMap!=null&&bsPubHrMap.get(p.getUpdateBy())!=null){
                p.setUpdateBy(bsPubHrMap.get(p.getUpdateBy()).getEmpName()+p.getUpdateBy());
            }
            if(!StringUtils.isEmpty(p.getCreateBy())&&bsPubHrMap!=null&&bsPubHrMap.get(p.getCreateBy())!=null){
                p.setCreateBy(bsPubHrMap.get(p.getCreateBy()).getEmpName()+p.getCreateBy());
            }
        });
    }

    /**
     * 根据条码获取补打数据
     */
    @Override
    public void batchUpdateBySnList(PrintRecordDTO dto)throws Exception{
        //为空
        if (dto == null) {
            return ;
        }
        List<String> snList = dto.getSnList();
        if (CollectionUtils.isEmpty(snList)) {
            return ;
        }
        CommonUtils.splitList(snList,NumConstant.NUM_100).forEach(p->{
            PrintRecordDTO tempDto = new PrintRecordDTO();
            tempDto.setSnList(p);
            tempDto.setUpdateBy(dto.getUpdateBy());
            printRecordRepository.batchUpdateLastUpdateDateBySns(tempDto);
        });
    }

    @Override
    public Integer countExportTotal(PrintRecordDTO dto) {
        //校验导出参数
        checkExportDto(dto);
        Page<PrintRecord> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        pageInfo.setParams(dto);
        printRecordRepository.getList(pageInfo);
        return pageInfo.getTotal();
    }

    @Override
    public List<PrintRecord> queryExportData(PrintRecordDTO dto, int pageNo, int pageSize) {
        //校验导出参数
        checkExportDto(dto);
        Page<PrintRecord> pageDetailDTO = new Page<>(pageNo, pageSize);
        pageDetailDTO.setParams(dto);
        pageDetailDTO.setSearchCount(false);
        List<PrintRecord> entityDTOList = printRecordRepository.getList(pageDetailDTO);
        try {
            setUserName(entityDTOList);
        }catch (Exception e){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_OBTAIN_HR_INFO);
        }
        return entityDTOList;
    }

    /**
     * 校验导出参数
     * @param dto
     */
    private void checkExportDto(PrintRecordDTO dto) {
        ///料单代码不能为空
        if (CollectionUtils.isEmpty(dto.getItemCodeList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_ITEMLISTNO_EMPTY);
        }
        //导出时间不能超过3个月 (两个时间都存在校验两个时间)
        boolean timeFlag = validateDateRange(dto.getUpdateDateStart(), dto.getUpdateDateEnd(), false);
        validateDateRange(dto.getCreateDateStart(), dto.getCreateDateEnd(), timeFlag);
    }

    /**
     * 校验导出时间不能超过3个月
     * @param startDateStr
     * @param endDateStr
     */
    private boolean validateDateRange(String startDateStr, String endDateStr, boolean timeFlag) {
        if (StringUtils.isEmpty(startDateStr) || StringUtils.isEmpty(endDateStr)) {
            // 同时为空报错
            if (timeFlag) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIME_CAN_NOT_NULL);
            }
            timeFlag = true;
        } else {
            Date startTime = DateUtil.convertStringToDate(startDateStr, DateUtil.DATE_FORMATE_FULL);
            Date endTime = DateUtil.convertStringToDate(endDateStr, DateUtil.DATE_FORMATE_FULL);
            if (DateUtil.caldaysByDate(endTime, startTime, Constant.INT_0).compareTo(Constant.BIG_DECIMAL_90DAYS) == Constant.INT_1) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_DRTAIL_TIME_INTERVAL_ERROR);
            }
        }
        return timeFlag;
    }

}
