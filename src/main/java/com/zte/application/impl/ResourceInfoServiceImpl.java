package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.HrmUserCenterService;
import com.zte.application.IMESLogService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.ResourceApplyRecordService;
import com.zte.application.ResourceDetailService;
import com.zte.application.ResourceInfoDetailService;
import com.zte.application.ResourceInfoService;
import com.zte.application.ResourceOptLogService;
import com.zte.application.ResourceUseInfoService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.constant.RedisKeyConstant;
import com.zte.domain.model.NetworkLicenseExcelModelBO;
import com.zte.domain.model.ResourceApplyRecord;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.ResourceWarningRecord;
import com.zte.domain.model.ResourceWarningRecordRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.infrastructure.remote.CallingB2BUniversalRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.ApplicationNetworkIdentificationNumberDTO;
import com.zte.interfaces.dto.BarcodeNetSignDTO;
import com.zte.interfaces.dto.DownloadNetworkIdentificationNumberDTO;
import com.zte.interfaces.dto.ModelNumberLicenceInfoDTO;
import com.zte.interfaces.dto.ModelNumberParamDTO;
import com.zte.interfaces.dto.ModelNumberResponseDTO;
import com.zte.interfaces.dto.NetworkAccessIdentificationNumberApplicationDTO;
import com.zte.interfaces.dto.ResourceApplyRecordPageQueryDTO;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.interfaces.dto.ResourceOptLogDTO;
import com.zte.interfaces.dto.ResourceUseInfoDTO;
import com.zte.interfaces.dto.SpSpecialityNalDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ThreadUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Future;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.AVAILABLE_QTY;
import static com.zte.common.utils.Constant.BACK_PASS_RATIO;
import static com.zte.common.utils.Constant.BASE_QTY;
import static com.zte.common.utils.Constant.BATCH_SIZE_500;
import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.CORE_POOL_SIZE;
import static com.zte.common.utils.Constant.DEVICE_TYPE;
import static com.zte.common.utils.Constant.EXPIRE_TIME_DAY;
import static com.zte.common.utils.Constant.FIRST;
import static com.zte.common.utils.Constant.FLAG_Y;
import static com.zte.common.utils.Constant.FOUR;
import static com.zte.common.utils.Constant.FOUR_ZORE;
import static com.zte.common.utils.Constant.INDEX;
import static com.zte.common.utils.Constant.INT_1;
import static com.zte.common.utils.Constant.INT_12;
import static com.zte.common.utils.Constant.INT_3;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_6824001;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_6846001;
import static com.zte.common.utils.Constant.MAX_PRODUCT_MAC_NUM;
import static com.zte.common.utils.Constant.NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE;
import static com.zte.common.utils.Constant.NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE_END;
import static com.zte.common.utils.Constant.NUM_ZERO;
import static com.zte.common.utils.Constant.NetWorkResourceApplyStatus;
import static com.zte.common.utils.Constant.REDIS_KEY_NETWORK_ALLOCATION;
import static com.zte.common.utils.Constant.RESOURCE_NO;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.Constant.STR_0;
import static com.zte.common.utils.Constant.STR_ONE;
import static com.zte.common.utils.Constant.STR_PERCENT;
import static com.zte.common.utils.Constant.STR_TWO;
import static com.zte.common.utils.Constant.SYSTEM;
import static com.zte.common.utils.Constant.THESE_RESOURCE_NO_BACK_PASS_RATIO_UNDER_STANDARD_VALUE;
import static com.zte.common.utils.StringConstant.FORMATTER_YYYYMMDD;


@Service("resourceInfoService")
public class ResourceInfoServiceImpl extends AbstractExportTaskHandler<ResourceInfoEntityDTO, ResourceInfoEntityDTO> implements ResourceInfoService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ResourceInfoRepository resourceInfoRepository;

    @Autowired
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;

    @Autowired
    private ResourceWarningRecordRepository resourceWarningRecordRepository;


    @Autowired
    private ResourceInfoDetailRepository infoDetailRepository;
    @Autowired
    ResourceInfoDetailServiceImpl resourceInfoDetailService;
    @Autowired
    private ResourceOptLogService resourceOptLogService;
    @Autowired
    private ResourceDetailService resourceDetailService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    DatawbRemoteService datawbRemoteService;

    @Autowired
    ResourceUseInfoService resourceUseInfoService;

    @Autowired
    SysLookupValuesService sysLookupValuesService;

    @Autowired
    ResourceApplyRecordService resourceApplyRecordService;

    @Autowired
    CallingB2BUniversalRemoteService callingB2BUniversalRemoteService;

    @Autowired
    IMESLogService imesLogService;

    @Autowired
    CloudDiskHelper cloudDiskHelper;

    @Autowired
    ResourceInfoService resourceInfoService;

    @Autowired
    private SysLookupValuesRepository lookupValuesRepository;

    @Autowired
    private EmailUtils emailUtils;

    @Value("${automatically.download.identification.hour:6}")
    private Integer autoDownLoadHour;

    @Value("${automatically.download.alarm.hour:24}")
    private Integer autoDownloadAlarmHour;

    @Value("${automatically.download.overdue.day:7}")
    private Integer autoDownloadOverdueDay;

    @Value("${automatically.apply.batch.quantity:100000}")
    private Long applyForBatchQuantity;

    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Override
    public Integer countExportTotal(ResourceInfoEntityDTO info) {
        //1.导出时资源类型必选
        if (StringUtils.isEmpty(info.getResourceType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_TYPE_CAN_NOT_BE_NULL);
        }
        // 入网类型（入网许可）查询资源号  先查明细表转成资源编号继续查询
        setResourceNo(info);
        Page<ResourceInfoEntityDTO> page = new Page<>(info.getPage(), info.getRows());
        page.setParams(info);
        resourceInfoRepository.pageList(page);
        //导出总量不能超过1W
        if(page.getTotal() > NumConstant.NUM_10000){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_LIMIT_1W);
        }
        return page.getTotal();
    }

    @Override
    public List<ResourceInfoEntityDTO> queryExportData(ResourceInfoEntityDTO info, int pageNo, int pageSize) {
        // 入网类型（入网许可）查询资源号  先查明细表转成资源编号继续查询
        setResourceNo(info);
        Page<ResourceInfoEntityDTO> pageEntityDTO = new Page<>(pageNo, pageSize);
        pageEntityDTO.setParams(info);
        pageEntityDTO.setSearchCount(false);
        List<ResourceInfoEntityDTO> pageInfo = resourceInfoRepository.pageList(pageEntityDTO);
        if (CollectionUtils.isEmpty(pageInfo)) {
            return pageInfo;
        }
        try {
            this.setUserName(pageInfo);
            this.setBackPassRatio(pageInfo);
            //转化资源类型和状态
            changeTypeAndStatus(pageInfo);
        } catch (Exception e) {
            logger.error("资源类型或状态数据字典未配置", e);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_EXPORT_ERROR);
        }
        return pageInfo;
    }

    /**
     * 转化资源类型和状态
     * @param pageInfo
     */
    public void changeTypeAndStatus(List<ResourceInfoEntityDTO> pageInfo){
        // 设置资源类型  6668
        Map<String, Object> map = new HashMap<>();
        BigDecimal lookUpCode = new BigDecimal(Constant.LOOKUP_CODE_6668);
        Map<String, String> typeLookMeanAndChinMap = resourceInfoDetailService.getLookUpVaulesList(map, lookUpCode);
        // 设置资源状态  7002
        lookUpCode = new BigDecimal(Constant.LOOKUP_CODE_7002);
        Map<String, String> statusLookMeanAndChinMap = resourceInfoDetailService.getLookUpVaulesList(map, lookUpCode);
        for(ResourceInfoEntityDTO dto : pageInfo){
            dto.setResourceType(typeLookMeanAndChinMap.get(dto.getResourceType()));
            dto.setResourceStatus(statusLookMeanAndChinMap.get(dto.getResourceStatus()));
        }
    }




    /**
     * 先查明细表转成资源编号继续查询
     * @param info
     */
    private void setResourceNo(ResourceInfoEntityDTO info) {
        if (StringUtils.isNotEmpty(info.getResourceStr()) && MpConstant.NAL.equals(info.getResourceType())) {
            transformNalResourceNumAndReturn(info);
        }
    }
    @Override
    public Page<ResourceInfoEntityDTO> pageList(ResourceInfoEntityDTO record) throws Exception {
        //查询条件校验
        checkQueryInfo(record);
        if (StringUtils.isEmpty(record.getResourceType()) && StringUtils.isNotEmpty(record.getResourceStr())) {
            //输入了资源号，没有选择类型的直接报错
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_TYPE_SHOULD_NOT_BE_NULL);
        }
        Page<ResourceInfoEntityDTO> pageInfo = new Page<>(record.getPage(), record.getRows());

        if (StringUtils.isNotEmpty(record.getResourceType()) && StringUtils.isNotEmpty(record.getResourceStr())) {
            if (MpConstant.NAL.equals(record.getResourceType())){
                // 入网类型查询资源号  先查明细表转成资源编号继续查询
               if(transformNalResourceNumAndReturn(record)){
                   pageInfo.setRows(new ArrayList<>());
                   return pageInfo;
               }
            } else {
                Map<String, String> transValueMap = this.transformHexadecimal(record.getResourceType(), record.getResourceStr());
                record.setResourceStrAdd(transValueMap.get("resourceStrAdd"));
                record.setResourceStrSub(transValueMap.get("resourceStrSub"));
            }
        }
        pageInfo.setParams(record);
        List<ResourceInfoEntityDTO> resourceInfolist = resourceInfoRepository.pageList(pageInfo);
        setBackPassRatio(resourceInfolist);
        //setWarningInfo(resourceInfolist);
        setUserName(resourceInfolist);
        pageInfo.setRows(resourceInfolist);
        return pageInfo;
    }

    //转化比例
    public void setBackPassRatio(List<ResourceInfoEntityDTO> resourceInfolist) throws Exception {
        if (CollectionUtils.isEmpty(resourceInfolist)) {
            return;
        }
        resourceInfolist.forEach(p -> {
            if (!Objects.isNull(p.getBackPassRatio()) && p.getBackPassRatio().compareTo(new BigDecimal(STR_0)) > 0) {
                String ratioValue = String.format("%.1f%%", p.getBackPassRatio().doubleValue() * 100);
                p.setBackPassRatioValue(ratioValue);
            }else {
                p.setBackPassRatioValue(STR_0 + STR_PERCENT);
            }
        });
    }

    /**
     * 查询条件校验
     * @param record
     */
    private static void checkQueryInfo(ResourceInfoEntityDTO record) {
        //查询时资源编号、资源号和创建时间必有一个
        if (StringUtils.isEmpty(record.getResourceStr()) && StringUtils.isEmpty(record.getResourceNo())) {
            if(StringUtils.isEmpty(record.getStartTime()) || StringUtils.isEmpty(record.getEndTime())){
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_NO_STR_TIME_SHOULD_NOT_BE_NULL);
            } else {
                //单以创建时间为查询条件时间范围不能超过2年
                Date startTime = DateUtil.convertStringToDate(record.getStartTime(), DateUtil.DATE_FORMATE_FULL);
                Date endTime = DateUtil.convertStringToDate(record.getEndTime(), DateUtil.DATE_FORMATE_FULL);
                if (DateUtil.caldaysByDate(endTime, startTime, Constant.INT_0).compareTo(Constant.TWO_YEAR_BIG) == Constant.INT_1) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SEARCH_TIME_LIMIT);
                }
            }
        }
    }


    public boolean transformNalResourceNumAndReturn(ResourceInfoEntityDTO record){
        String resourceNo = infoDetailRepository.findResourceNoByNum(record.getResourceStr());
        record.setResourceStr(null);
        if(StringUtils.isEmpty(resourceNo)){
            return true;
        }
        if(StringUtils.isEmpty(record.getResourceNo())){
            record.setResourceNo(resourceNo);
        }
        if(!resourceNo.equals(record.getResourceNo())){
            return true;
        }
        return false;
    }

    /**
     * 将输入的资源号先转换为10进制加减千万
     *
     * @param resourceType，resourceStr
     * @return resourceStrPro
     */
    public static Map<String, String> transformHexadecimal(String resourceType, String resourceStr) {
        //校验号段格式
        Pattern pattern = getPattern(resourceType);
        // 字符串是否与正则表达式相匹配
        if (!pattern.matcher(resourceStr).matches()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_MATCH_FAILED);
        }
        // 转为10进制
        BigInteger resourceStrTransNum = convertDecimal(resourceType, resourceStr);
        //转换回16进制再进行组装
        Map<String, String> transValueMap = new HashMap<>();
        transValueMap.put("resourceStrAdd", transformDecimal(resourceType, resourceStrTransNum.add(NumConstant.TEN_MILLION), resourceStr));
        transValueMap.put("resourceStrSub", transformDecimal(resourceType, resourceStrTransNum.subtract(NumConstant.TEN_MILLION), resourceStr));
        return transValueMap;
    }

    /**
     * 十六进制转为十进制
     *
     * @return 十进制
     */
    public static BigInteger convertDecimal(String resourceType, String resourceStartOrEnd) {
        String resourceStr;
        int radix = NumConstant.NUM_TEN;
        if (StringUtils.equals(resourceType, MpConstant.MAC)) {
            resourceStr = resourceStartOrEnd.replaceAll(Constant.HORIZON, Constant.STRING_EMPTY).replaceAll(Constant.SEMICOLON, Constant.STRING_EMPTY);
            radix = NumConstant.NUM_SIXTEEN;
        } else if (StringUtils.equals(resourceType, MpConstant.GPON_SN)) {
            resourceStr = resourceStartOrEnd.replaceAll(MpConstant.ZTEG, Constant.STRING_EMPTY);
            radix = NumConstant.NUM_SIXTEEN;
        } else {
            resourceStr = resourceStartOrEnd;
        }
        //十六进制转换为十进制进行运算
        return new BigInteger(resourceStr, radix);
    }

    /**
     * 转换16进制并组装
     *
     * @param resourceType，resourceStr
     * @return resourceStrPro
     */
    public static String transformDecimal(String resourceType, BigInteger resourceStrTransNum, String resourceStr) {
        String resourceStrPro;
        if (StringUtils.equals(resourceType, MpConstant.MAC)) {
            resourceStrPro = String.format(Constant.HEX_STRING_PREFIX_12, resourceStrTransNum);
            resourceStrPro = getMacAdr(resourceStrPro.toUpperCase());
        } else if (StringUtils.equals(resourceType, MpConstant.GPON_SN)) {
            resourceStrPro = Constant.GPON_SN_PREFIX + String.format(Constant.HEX_STRING_PREFIX_08, resourceStrTransNum);
            resourceStrPro = resourceStrPro.toUpperCase();
        } else {
            resourceStrPro = resourceStrTransNum.toString();
        }
        return resourceStrPro;
    }

    /**
     * 组成mac地址
     *
     * @param resourceStrPro 十六进制
     * @return mac地址
     */
    private static String getMacAdr(String resourceStrPro) {
        StringBuilder result = new StringBuilder();
        for (int i = Constant.INT_1; i <= Constant.INT_12; i++) {
            result.append(resourceStrPro.charAt(i - Constant.INT_1));
            if (i % Constant.INT_2 == Constant.INT_0) {
                result.append(Constant.HORIZON);
            }
        }
        return result.substring(Constant.INT_0, Constant.INT_17);
    }

    public void setWarningInfo(List<ResourceInfoEntityDTO> resourceInfolist) {
        if (CollectionUtils.isEmpty(resourceInfolist)) {
            return;
        }
        List<String> resourceList = resourceInfolist.stream().map(ResourceInfoEntityDTO::getResourceNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        List<ResourceWarningRecord> resourceWarningRecords = resourceWarningRecordRepository.selectRecordByResourceNos(resourceList);
        Map<String, ResourceWarningRecord> recordMap = resourceWarningRecords.stream().collect(Collectors.toMap(ResourceWarningRecord::getResourceNo, d -> d, (k1, k2) -> k1));
        resourceInfolist.forEach(e -> {
            if (recordMap.containsKey(e.getResourceNo())) {
                e.setLowWaterWarningFlag(recordMap.get(e.getResourceNo()).getLowWaterWarningFlag());
                e.setFirstLowWaterWarningDate(recordMap.get(e.getResourceNo()).getFirstLowWaterWarningDate());
                e.setExpiryWarningFlag(recordMap.get(e.getResourceNo()).getExpiryWarningFlag());
                e.setFirstExpiryWarningDate(recordMap.get(e.getResourceNo()).getFirstExpiryWarningDate());
            }
        });
    }

    //转化工号
    public void setUserName(List<ResourceInfoEntityDTO> resourceInfolist) throws Exception {
        if (CollectionUtils.isEmpty(resourceInfolist)) {
            return;
        }
        //处理人
        Set set = new HashSet();
        resourceInfolist.forEach(p -> {
            addUserId(p.getCreateBy(), set);
            addUserId(p.getLastUpdatedBy(), set);
            addUserId(p.getTagApplicant(), set);
        });
        List<String> userIdList = new ArrayList<>(set);
        //获取姓名
        Map<String, String> bsPubHrMap = programVerifyConfirmInfoService.getUserNew(userIdList);
        resourceInfolist.forEach(p -> {
            if (matchUserName(p.getCreateBy(), bsPubHrMap)) {
                p.setCreateBy(bsPubHrMap.get(p.getCreateBy()) + p.getCreateBy());
            }
            if (matchUserName(p.getLastUpdatedBy(), bsPubHrMap)) {
                p.setLastUpdatedBy(bsPubHrMap.get(p.getLastUpdatedBy()) + p.getLastUpdatedBy());
            }
            if (matchUserName(p.getTagApplicant(), bsPubHrMap)) {
                p.setTagApplicantName(bsPubHrMap.get(p.getTagApplicant()) + p.getTagApplicant());
            }
        });
    }

    private boolean matchUserName(String user, Map<String, String> bsPubHrMap) {
        return !StringUtils.isEmpty(user) && bsPubHrMap != null && bsPubHrMap.get(user) != null;
    }

    private void addUserId(String p, Set set) {
        if (!StringUtils.isEmpty(p)) {
            set.add(p);
        }
    }

    private static Pattern getPattern(String resourceType) throws MesBusinessException {
        switch (resourceType) {
            case MpConstant.GPON_SN:
                return Pattern.compile(MpConstant.GPON_SN_PATTERN);
            case MpConstant.MAC:
                return Pattern.compile(MpConstant.MAC_PATTERN);
            case MpConstant.CTEI:
                return Pattern.compile(MpConstant.CTEI_PATTERN);
            case MpConstant.CMEI:
                return Pattern.compile(MpConstant.CMEI_PATTERN);
            case MpConstant.CUEI:
                return Pattern.compile(MpConstant.CUEI_PATTERN);
            case MpConstant.IMEI:
                return Pattern.compile(MpConstant.IMEI_PATTERN);
            case MpConstant.D_SN:
                return Pattern.compile(MpConstant.D_SN_PATTERN);
            case MpConstant.NAL:
                return Pattern.compile(MpConstant.NAL_PATTERN);
            case MpConstant.SN:
                return Pattern.compile(MpConstant.SN_PATTERN);
            case MpConstant.STB_SN:
                return Pattern.compile(MpConstant.STB_SN_PATTERN);
            case MpConstant.SO_NET_SN:
                return Pattern.compile(MpConstant.SO_NET_SN_PATTERN);
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_TYPE_NOT_EXISTS);
        }
    }

    //校验号段格式
    private void checkResourceStartAndEnd(ResourceInfoEntityDTO dto) throws MesBusinessException {
        Pattern pattern = getPattern(dto.getResourceType());
        // 字符串是否与正则表达式相匹配
        if (!pattern.matcher(dto.getResourceStart()).matches() || !pattern.matcher(dto.getResourceEnd()).matches()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_MATCH_FAILED);
        }
        //校验资源开始小于资源结束
        if (dto.getResourceStart().compareTo(dto.getResourceEnd()) >= NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.START_SEGMENT_MUST_LESS_END_SEGMENT);
        }
        //资源号段存在重复，请确认
        if (resourceInfoRepository.selectOverlappingTime(dto) > NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DUPLICATE_RESOURCE_NUMBER_SEGMENT);
        }
    }

    //计算可用数量
    private BigInteger getResourceAmount(ResourceInfoEntityDTO resourceInfoEntityDTO) {
        String start;
        String end;
        int radix = NumConstant.NUM_TEN;
        if (StringUtils.equals(resourceInfoEntityDTO.getResourceType(), MpConstant.MAC)) {
            start = resourceInfoEntityDTO.getResourceStart().replaceAll(Constant.HORIZON, Constant.STRING_EMPTY);
            end = resourceInfoEntityDTO.getResourceEnd().replaceAll(Constant.HORIZON, Constant.STRING_EMPTY);
            radix = NumConstant.NUM_SIXTEEN;
        } else if (StringUtils.equals(resourceInfoEntityDTO.getResourceType(), MpConstant.GPON_SN)) {
            start = resourceInfoEntityDTO.getResourceStart().replaceAll(MpConstant.ZTEG, Constant.STRING_EMPTY);
            end = resourceInfoEntityDTO.getResourceEnd().replaceAll(MpConstant.ZTEG, Constant.STRING_EMPTY);
            radix = NumConstant.NUM_SIXTEEN;
        } else {
            start = resourceInfoEntityDTO.getResourceStart();
            end = resourceInfoEntityDTO.getResourceEnd();
        }
        BigInteger startNum = new BigInteger(start, radix);
        BigInteger endNum = new BigInteger(end, radix);
        return endNum.subtract(startNum).add(BigInteger.ONE);
    }

    @Override
    public int save(ResourceInfoEntityDTO record) throws Exception {
        int result = 0;
        if(MpConstant.NAL.equals(record.getResourceType())){
            result = resourceInfoRepository.updateNetworkType(record);
            return result;
        }
        //校验号段格式
        checkResourceStartAndEnd(record);
        //设置资源编号
        if (StringUtils.isEmpty(record.getResourceNo())) {
            record.setResourceNo(generateResourceNo(record));
        }
        //计算可用数量以及资源数量
        BigInteger num = getResourceAmount(record);
        record.setResourceAmount(num);
        record.setAvailableQuantity(num);
        if (!StringUtils.isEmpty(record.getResourceId())) {
            List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
            updateList.add(record);
            result = resourceInfoRepository.batchUpdate(updateList);
        } else {
            // 写头表
            List<ResourceInfoEntityDTO> insertList = new ArrayList<ResourceInfoEntityDTO>();
            record.setResourceId(UUID.randomUUID().toString());
            insertList.add(record);
            result = resourceInfoRepository.batchInsert(insertList);
        }
        return result;
    }

    @Override
    public int update(ResourceInfoEntityDTO record) throws Exception {
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        updateList.add(record);
        return resourceInfoRepository.batchUpdateSelectivity(updateList);
    }

    @Override
    public String getNextResource(String resourceType) {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType(resourceType);
        dto.setSort("create_date");
        dto.setGtAvailableQuantity(BigInteger.ZERO);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOS = resourceInfoRepository.getList(dto);
        if (CollectionUtils.isNotEmpty(resourceInfoEntityDTOS)) {
            ResourceInfoEntityDTO resourceInfoEntityDTO = resourceInfoEntityDTOS.get(NumConstant.NUM_ZERO);
            BigInteger resourceStrTransNum = convertDecimal(resourceType, resourceInfoEntityDTO.getAttribute4() != null ? resourceInfoEntityDTO.getAttribute4() : resourceInfoEntityDTO.getResourceStart());
            String nextResource = transformDecimal(resourceType, resourceStrTransNum.add(NumConstant.TEN_MILLION), resourceInfoEntityDTO.getResourceStart());
            ResourceInfoEntityDTO updateInfo = new ResourceInfoEntityDTO();
            updateInfo.setResourceId(resourceInfoEntityDTO.getResourceId());
            updateInfo.setAttribute4(nextResource);
            updateInfo.setAvailableQuantity(resourceInfoEntityDTO.getAvailableQuantity().subtract(BigInteger.ONE));
            resourceInfoRepository.batchUpdateSelectivity(Collections.singletonList(updateInfo));
            return nextResource;
        }
        return null;
    }

    /**
     * 通过资源号获取产品中文名
     */
    @Override
    public String getProductNameByNum(String resourceNum) throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType(MpConstant.NAL);
        dto.setResourceStr(resourceNum);
        return resourceInfoRepository.getProductNameByNum(dto);
    }

    /**
     * 单号生成规则：资源类型+产品大类+YYYYMMDD+四位流水码（每天都是从0001开始）
     *
     * @return
     * @throws Exception
     */
    public String generateResourceNo(ResourceInfoEntityDTO dto) throws Exception {
        //工厂ID
        StringBuilder sb = new StringBuilder(dto.getResourceType() + dto.getProductCategory());
        //8位日期
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FORMATTER_YYYYMMDD);
        String formatDate = simpleDateFormat.format(new Date());
        //4位流水码
        sb.append(formatDate).append(getRedisId(MpConstant.RESOURCE_KEY_VALUE_PREFIX_NO + formatDate));
        return sb.toString();
    }

    //获取4位流水号
    private String getRedisId(String redisKey) throws Exception {
        String lockKey = this.getClass().getSimpleName() + MpConstant.RESOURCE_KEY_VALUE_PREFIX_NO;
        RedisLock redisLock = new RedisLock(lockKey);
        String redisId = null;
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new Exception();
            }
            Integer maxCountValue = findMaxCount(redisKey);
            RedisCacheUtils.set(redisKey, maxCountValue + 1, EXPIRE_TIME_DAY);
            StringBuilder serialCode = new StringBuilder(FOUR_ZORE);
            serialCode = serialCode.append(maxCountValue);
            redisId = serialCode.substring(serialCode.length() - FOUR, serialCode.length());
        } finally {
            redisLock.unlock();
        }
        return redisId;
    }

    //根据key获取最大值
    public Integer findMaxCount(String redisKey) {
        Integer maxCountValue = RedisCacheUtils.get(redisKey, Integer.class);
        if (maxCountValue == null) {
            return NumConstant.NUM_ONE;
        }
        return maxCountValue;
    }


    @Override
    public ResourceInfoEntityDTO findDeviceByResourceNo(String resourceNo) {
        if(StringUtils.isEmpty(resourceNo)){
            return null;
        }
        return resourceInfoRepository.findDeviceByResourceNo(resourceNo);
    }


    @Override
    public List<ResourceInfoEntityDTO> findDeviceByResourceNos(List<String> resourceNos) {
        if(CollectionUtils.isEmpty(resourceNos)){
            return new ArrayList<>();
        }
        return resourceInfoRepository.findDeviceByResourceNos(resourceNos);
    }

    @Override
    public List<String> fuzzyQueryDeviceType(String deviceType) {
        return resourceInfoRepository.fuzzyQueryDeviceType(deviceType);
    }

    @Override
    public List<ResourceInfoEntityDTO> findResourceEntityByDeviceType(String deviceType) {
        return resourceInfoRepository.findResourceEntityByDeviceType(deviceType);
    }

    /**
     * 入网分配接口
     * @param resourceNo
     * @return
     */
    @Override
    public void networkAllocation(String resourceNo, Integer qty, String businessId, String empNo)throws Exception {
        if(qty < NumConstant.NUM_ONE){
            return;
        }
        RedisLock redisLock = new RedisLock(REDIS_KEY_NETWORK_ALLOCATION+resourceNo,NumConstant.NUM_360);
        if(!redisLock.lock()){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENT_RESOURCE_NUMBER_IS_OPERATION);
        }
        try {
            ResourceInfoEntityDTO resourceInfoEntityDTO = resourceInfoRepository.selectByResourceNo(resourceNo);
            if (resourceInfoEntityDTO == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY);
            }
            if (StringUtils.isEmpty(resourceInfoEntityDTO.getModelNumber())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_MODEL_CODE_IS_EMPTY);
            }
            BigInteger availableQuantity = resourceInfoEntityDTO.getAvailableQuantity();
            if (availableQuantity == null) {
                availableQuantity = BigInteger.ZERO;
            }
            if (availableQuantity.compareTo(new BigInteger(String.valueOf(qty))) < NumConstant.NUM_ZERO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_NO_AVAILABLE_QUANTITY_NOT_ENOUGH, new Object[]{resourceNo, qty});
            }
            //预分配
            ResourceInfoService resourceInfoService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class);
            resourceInfoService.preAllocationByBusinessId(resourceNo, qty, businessId,empNo);
            Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
            resourceInfoEntityDTO.setHeaderParamsMap(headerParamsMap);

            List<Future<String>> futureList = new ArrayList<>();
            int cc = qty%NumConstant.NUM_5000 >NumConstant.NUM_ZERO?NumConstant.NUM_ONE:NumConstant.NUM_ZERO;
			int page = qty/NumConstant.NUM_5000 + cc;
            for (int i = NumConstant.NUM_ONE; i <= page; i++) {
                Integer pageSize = i;
                Future<String> submit = ThreadUtil.EXECUTOR.submit(() -> {
                    return this.batchProcessing(resourceNo, pageSize, businessId, empNo, resourceInfoEntityDTO);
                });
                futureList.add(submit);
            }
            this.checkResult(futureList);
        }finally {
            if(redisLock != null){
                redisLock.unlock();
            }
        }
    }

    public void checkResult(List<Future<String>> futureList) throws Exception {
        StringBuilder sb = new StringBuilder();
        for (Future<String> future : futureList) {
            String errorMsg = future.get();
            if (StringUtils.isNotEmpty(errorMsg)) {
                sb.append(errorMsg);
            }
        }
        String errorMsg = sb.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{errorMsg});
        }
    }

    private String batchProcessing(String resourceNo, Integer current, String businessId, String empNo, ResourceInfoEntityDTO resourceInfoEntityDTO) {
        String msg = "";
        try {
            Page page = new Page();
            page.setSearchCount(false);
            page.setPageSize(NumConstant.NUM_5000);
            ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
            resourceInfoDetailDTO.setResourceNo(resourceNo);
            resourceInfoDetailDTO.setBusinessId(businessId);
            page.setParams(resourceInfoDetailDTO);
            page.setCurrent(current);
            List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = infoDetailRepository.queryByBusinessId(page);
            if(CollectionUtils.isEmpty(resourceInfoDetailDTOList)){
                return "";
            }
            ResourceInfoService infoService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class);
            infoService.batchProcessingOfResourceNumberInformation(empNo, resourceInfoEntityDTO, resourceInfoDetailDTOList);
        } catch (Exception e) {
            msg = StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : e.toString();
        }
        return msg;
    }

    /**
     * 分批处理资源信息
     * @param empNo
     * @param resourceInfoEntityDTO
     * @param resourceInfoDetailDTOList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchProcessingOfResourceNumberInformation(String empNo, ResourceInfoEntityDTO resourceInfoEntityDTO, List<ResourceInfoDetailDTO> resourceInfoDetailDTOList) {
        //写操作日志
        this.batchInsertResourceOptLog(empNo, resourceInfoEntityDTO, resourceInfoDetailDTOList, NumConstant.STRING_ONE);
        //回写mes
        this.uploadResourceInfoToMes(empNo, resourceInfoEntityDTO, resourceInfoDetailDTOList);
    }

    /**
     * 回写mes资源信息
     * @param empNo
     * @param resourceInfoEntityDTO
     * @param resourceInfoDetailDTOList
     */
    private void uploadResourceInfoToMes(String empNo,ResourceInfoEntityDTO resourceInfoEntityDTO, List<ResourceInfoDetailDTO> resourceInfoDetailDTOList) {
        List<SpSpecialityNalDTO> spSpecialityNalDTOList = new ArrayList<>();
        for (ResourceInfoDetailDTO infoDetailDTO : resourceInfoDetailDTOList) {
            SpSpecialityNalDTO spSpecialityNalDTO = new SpSpecialityNalDTO();
            spSpecialityNalDTO.setCreatedBy(empNo);
            spSpecialityNalDTO.setParamHeaderId(new BigDecimal(infoDetailDTO.getBusinessId()));
            spSpecialityNalDTO.setNetPermit(infoDetailDTO.getResourceNo());
            spSpecialityNalDTO.setNetAccessSignNum(infoDetailDTO.getResourceNum());
            spSpecialityNalDTO.setScrambleCode(infoDetailDTO.getScramblingCode());
            spSpecialityNalDTO.setEqpModel(resourceInfoEntityDTO.getDeviceType());
            spSpecialityNalDTO.setModelNumber(resourceInfoEntityDTO.getModelNumber());
            spSpecialityNalDTOList.add(spSpecialityNalDTO);
        }
        datawbRemoteService.uploadResourceInfoToMes(spSpecialityNalDTOList,resourceInfoEntityDTO.getHeaderParamsMap());
    }

    /**
     * 批量写操作日志
     * @param empNo
     * @param resourceInfoEntityDTO
     * @param resourceInfoDetailDTOList
     */
    public void batchInsertResourceOptLog(String empNo, ResourceInfoEntityDTO resourceInfoEntityDTO,
                                          List<ResourceInfoDetailDTO> resourceInfoDetailDTOList, String status) {
        List<ResourceOptLogDTO> resourceOptLogDTOList = new ArrayList<>();
        for (ResourceInfoDetailDTO infoDetailDTO : resourceInfoDetailDTOList) {
            ResourceOptLogDTO optLogDTO = new ResourceOptLogDTO();
            optLogDTO.setId(idGenerator.snowFlakeId());
            optLogDTO.setResourceNum(infoDetailDTO.getResourceNum());
            optLogDTO.setResourceNo(infoDetailDTO.getResourceNo());
            optLogDTO.setResourceType(resourceInfoEntityDTO.getResourceType());
            //操作类型0:标签打印;1:标签补打;2:入网分配（家端个参使用）;3:入网标签分配
            optLogDTO.setOptType(NumConstant.NUM_TWO);
            optLogDTO.setStatus(status);
            optLogDTO.setCreateBy(empNo);
            resourceOptLogDTOList.add(optLogDTO);
        }
        resourceOptLogService.batchInsert(resourceOptLogDTOList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void preAllocationByBusinessId(String resourceNo, Integer qty, String businessId, String empNo) {

        Integer count = infoDetailRepository.countByBusinessId(resourceNo, businessId);
        //不存在就先预分配
        if(count <= NumConstant.NUM_ZERO){
            infoDetailRepository.preAllocationByBusinessId(resourceNo, qty, businessId,empNo);
            //更新可用数量
            resourceInfoRepository.updateAvailableQuantityByResourceNo(resourceNo);
        }
    }

    /**
     * 家端个参绑定
     * @param empNo
     */
    @Override
    public void homeEndIndividualParameterBinding(String empNo,Integer preDay,String resourceNo) {
        String lockKey = RedisKeyConstant.HOME_END_INDIVIDUAL_PARAMETER_BINDING;
        RedisLock redisLock = new RedisLock(lockKey,NumConstant.NUM_3600);
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }
            //循环处理
            ResourceInfoDetailDTO param = new ResourceInfoDetailDTO();
            param.setFirst(FIRST);
            param.setPreDay(preDay);
            param.setResourceNo(resourceNo);
            while(true){
                List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = infoDetailRepository.queryForBusinessIdNotEmpty(param);
                if(CollectionUtils.isEmpty(resourceInfoDetailDTOList)){
                    break;
                }
                this.batchProcessing(empNo, resourceInfoDetailDTOList);
                //取最后的数据
                ResourceInfoDetailDTO resourceInfoDetailDTO = resourceInfoDetailDTOList.get(resourceInfoDetailDTOList.size()-NumConstant.NUM_ONE);
                //取这次处理数据最大资源号
                String resourceNum = resourceInfoDetailDTO.getResourceNum();
                param.setFirst("");
                param.setResourceNum(resourceNum);
            }
        } finally {
            redisLock.unlock();
        }

    }

    @Override
    public void homeEndIndividualParameterBind(String empNo) throws ParseException {
        String lockRedisKey = RedisKeyConstant.HOME_END_INDIVIDUAL_PARAMETER_BINDING;
        RedisLock redisLock = new RedisLock(lockRedisKey,NumConstant.NUM_3600);
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }
            // 查询数据字典记录的上一次同步的时间。
            SysLookupValues sysLookupValues = sysLookupValuesService.selectSysLookupValuesById(new SysLookupValues() {{
                setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_1003021001));
            }});
            if (sysLookupValues == null || StringUtils.isBlank(sysLookupValues.getLookupMeaning())) {
                return;
            }
            String lastSynchronizeTime = sysLookupValues.getLookupMeaning();
            SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
            // 取的就是最后一条同步的数据的时间，所以不冗余
            Date lastUpdatedTime = new Date(sdf.parse(lastSynchronizeTime).getTime());
            Date endTime = null;
            if (!StringUtils.isBlank(sysLookupValues.getAttribute1())) {
                endTime = sdf.parse(sysLookupValues.getAttribute1());
            }
            // 将时间作为最后更新时间的标准，取MES视图查询数据。
            BarcodeNetSignDTO params = new BarcodeNetSignDTO();
            params.setLastUpdatedTime(lastUpdatedTime);
            params.setEndTime(endTime);
            params.setRow(Constant.INT_1000);
            // 根据net_access_sign_num 作为排序字段，最后更新时间和net_access_sign_num作为筛选字段，取前1000条数据。
            List<BarcodeNetSignDTO> barcodeNetSignList = datawbRemoteService.selectBarAccSignForSchTask(params);
            params.setFirstFlag(false);
            while(!CollectionUtils.isEmpty(barcodeNetSignList)) {
                // 处理这1000条数据，根据1000数据中的net_access_sign_num 字段，查询中心工厂的resource_info_detail状态为“已分配”的数据集，
                Set<String> netAccessSignNumSet = barcodeNetSignList.stream().map(k -> k.getNetAccessSignNum()).collect(Collectors.toSet());
                List<ResourceInfoDetailDTO> infoDetailList = infoDetailRepository.listAllocatedByNetAccessSigns(netAccessSignNumSet);
                // 筛选后，将满足条件的分批写使用记录和更新状态。
                resourceInfoService.handlerData(infoDetailList, barcodeNetSignList, empNo);
                // 查询数据作为下次处理的数据
                params.setLastUpdatedTime(barcodeNetSignList.get(barcodeNetSignList.size() - 1).getLastUpdatedTime());
                params.setNetAccessSignNum(barcodeNetSignList.get(barcodeNetSignList.size() - 1).getNetAccessSignNum());
                // 更新时间同步，优化为每一次循环都更改时间，跟踪更新进度。
                updateLookupValue(sysLookupValues, params);
                barcodeNetSignList = datawbRemoteService.selectBarAccSignForSchTask(params);
            }
            // 下一次同步时，最后更新时间为上一次同步最后一次循环的时间。
            updateLookupValue(sysLookupValues, params);
        } finally {
            redisLock.unlock();
        }
    }

    private void updateLookupValue(SysLookupValues sysLookupValues, BarcodeNetSignDTO params) {
        Date nextSyncLastUpdatedTime = params.getLastUpdatedTime();
        // 最后更新同步时间。
        SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_TIME_FORMATE_FULL);
        String lookMeaning = sdf.format(nextSyncLastUpdatedTime);
        sysLookupValues.setLookupMeaning(lookMeaning);
        sysLookupValuesService.updateSysLookupValuesById(sysLookupValues);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlerData(List<ResourceInfoDetailDTO> infoDetailList, List<BarcodeNetSignDTO> barcodeNetSignList, String empNo) {
        if(CollectionUtils.isEmpty(infoDetailList)){
            return;
        }
        List<ResourceUseInfoDTO> resourceUseInfoDTOList  = new ArrayList<>();
        Map<String, String> signNumToMac = new HashMap<>();
        Map<String, String> signNumToCreatedBy = new HashMap<>();
        for (BarcodeNetSignDTO entity : barcodeNetSignList) {
            signNumToMac.put(entity.getNetAccessSignNum(), entity.getMac1Addr());
            signNumToCreatedBy.put(entity.getNetAccessSignNum(), entity.getCreatedBy());
        }
        for (ResourceInfoDetailDTO infoDetailDTO : infoDetailList) {
            if(infoDetailDTO == null){
                continue;
            }
            ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
            resourceUseInfoDTO.setId(idGenerator.snowFlakeId());
            resourceUseInfoDTO.setResourceNo(infoDetailDTO.getResourceNo());
            resourceUseInfoDTO.setResourceNum(infoDetailDTO.getResourceNum());
            resourceUseInfoDTO.setBarcodeType(NumConstant.STRING_ONE);
            resourceUseInfoDTO.setBarcode(signNumToMac.get(infoDetailDTO.getResourceNum()));
            resourceUseInfoDTO.setContractNumber("");
            String deviceTypeCode = infoDetailDTO.getResourceNo();
            if(StringUtils.isNotEmpty(deviceTypeCode) && deviceTypeCode.length() > NumConstant.NUM_SIX){
                deviceTypeCode = deviceTypeCode.substring(deviceTypeCode.length() - NumConstant.NUM_SIX);
            }
            resourceUseInfoDTO.setDeviceTypeCode(deviceTypeCode);
            resourceUseInfoDTO.setEntityName("");
            resourceUseInfoDTO.setDistributeDate(infoDetailDTO.getLastUpdatedDate());
            resourceUseInfoDTO.setBindWay(NumConstant.STRING_ZERO);
            resourceUseInfoDTO.setCreateBy(signNumToCreatedBy.get(infoDetailDTO.getResourceNum()));
            resourceUseInfoDTO.setIsRework(NumConstant.STRING_ZERO);
            resourceUseInfoDTO.setLastUpdatedBy(empNo);
            resourceUseInfoDTOList.add(resourceUseInfoDTO);
        }
        resourceUseInfoService.batchInsert(resourceUseInfoDTOList);

        //更新资源号状态
        List<String> resourceNumberList = resourceUseInfoDTOList.stream().map(e->e.getResourceNum()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(resourceNumberList)){
            return;
        }
        for (List<String> tempList : Lists.partition(resourceNumberList, BATCH_SIZE_500)) {
            infoDetailRepository.updateStatusByResourceNum(tempList,NumConstant.STRING_TWO, empNo);
        }
    }

    public void batchProcessing(String empNo, List<ResourceInfoDetailDTO> resourceInfoDetailDTOList) {
        //按资源号分组
        Map<String, ResourceInfoDetailDTO> resourceInfoDetailDTOMap = resourceInfoDetailDTOList.stream().collect(Collectors.toMap(k -> k.getResourceNum(), v -> v, (oldValue, newValue) -> newValue));
        //查mes资源号信息
        List<String> resourceNumList = resourceInfoDetailDTOList.stream().map(e->e.getResourceNum()).distinct().collect(Collectors.toList());
        List<SpSpecialityNalDTO> spSpecialityNalDTOList = datawbRemoteService.getMacByResourceNumber(resourceNumList);
        //分批写使用记录以及更新状态
        ResourceInfoService infoService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class);
        infoService.batchProcessingResourceNumber(empNo, resourceInfoDetailDTOMap, spSpecialityNalDTOList);
    }

    /**
     * 分批写使用记录以及更新状态
     * @param empNo
     * @param resourceInfoDetailDTOMap
     * @param spSpecialityNalDTOList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchProcessingResourceNumber(String empNo, Map<String, ResourceInfoDetailDTO> resourceInfoDetailDTOMap, List<SpSpecialityNalDTO> spSpecialityNalDTOList) {
        if(CollectionUtils.isEmpty(spSpecialityNalDTOList)){
            return;
        }
        List<ResourceUseInfoDTO> resourceUseInfoDTOList  = new ArrayList<>();
        for (SpSpecialityNalDTO spSpecialityNalDTO : spSpecialityNalDTOList) {
            ResourceInfoDetailDTO resourceInfoDetailDTO = resourceInfoDetailDTOMap.get(spSpecialityNalDTO.getNetAccessSignNum());
            if(resourceInfoDetailDTO == null){
                continue;
            }
            ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
            resourceUseInfoDTO.setId(idGenerator.snowFlakeId());
            resourceUseInfoDTO.setResourceNo(resourceInfoDetailDTO.getResourceNo());
            resourceUseInfoDTO.setResourceNum(resourceInfoDetailDTO.getResourceNum());
            resourceUseInfoDTO.setOutsourceFactory(spSpecialityNalDTO.getOutsourceFactory());
            resourceUseInfoDTO.setBarcodeType(NumConstant.STRING_ONE);
            resourceUseInfoDTO.setBarcode(spSpecialityNalDTO.getMac());
            resourceUseInfoDTO.setContractNumber("");
            String deviceTypeCode = resourceInfoDetailDTO.getResourceNo();
            if(StringUtils.isNotEmpty(deviceTypeCode) && deviceTypeCode.length() > NumConstant.NUM_SIX){
                deviceTypeCode = deviceTypeCode.substring(deviceTypeCode.length() - NumConstant.NUM_SIX);
            }
            resourceUseInfoDTO.setDeviceTypeCode(deviceTypeCode);
            resourceUseInfoDTO.setEntityName("");
            resourceUseInfoDTO.setDistributeDate(resourceInfoDetailDTO.getLastUpdatedDate());
            resourceUseInfoDTO.setBindWay(NumConstant.STRING_ZERO);
            resourceUseInfoDTO.setCreateBy(spSpecialityNalDTO.getBinder());
            resourceUseInfoDTO.setIsRework(NumConstant.STRING_ZERO);
            resourceUseInfoDTO.setLastUpdatedBy(empNo);
            resourceUseInfoDTOList.add(resourceUseInfoDTO);
        }
        resourceUseInfoService.batchInsert(resourceUseInfoDTOList);

        //更新资源号状态
        List<String> resourceNumberList = spSpecialityNalDTOList.stream().map(e->e.getNetAccessSignNum()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(resourceNumberList)){
            return;
        }
        for (List<String> tempList : Lists.partition(resourceNumberList, BATCH_SIZE_500)) {
            infoDetailRepository.updateStatusByResourceNum(tempList,NumConstant.STRING_TWO, empNo);
        }
    }

    @Override
    public Boolean checkResourceNo(String resourceNo) {
        ResourceInfoEntityDTO infoEntityDTO = resourceInfoRepository.selectByResourceNo(resourceNo);
        if (Objects.isNull(infoEntityDTO)) {
            return null;
        }
        return Constant.STR_USING.equals(infoEntityDTO.getResourceStatus()) || Constant.STR_INIT.equals(infoEntityDTO.getResourceStatus());
    }

    /**
     * 同步批文信息
     *
     * @param resourceNo
     */
    @Override
    @RecordLogAnnotation("同步批文信息")
    @Transactional(rollbackFor = Exception.class)
    public void setBenchmarkQuantity(String resourceNo)throws Exception {
        String lockKey = RedisKeyConstant.SET_BENCHMARK_QUANTITY_KEY;
        RedisLock redisLock = new RedisLock(lockKey);
        try {
            boolean lock = redisLock.lock(NumConstant.NUM_360);
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }
            Date nowDate = new Date();
            //上次执行时间 第一次默认一年内
            Date lastExecutionTime = DateUtil.getAfterYears(new Date(), -NumConstant.NUM_ONE);
            SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(LOOK_UP_CODE_6824001);
            if (sysLookupValues == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG);
            }
            if (StringUtils.isNotEmpty(sysLookupValues.getLookupMeaning())) {
                //往前2小时
                lastExecutionTime = DateUtils.addMinutes(DateUtil.convertStringToDate(sysLookupValues.getLookupMeaning(), DateUtil.DATE_FORMATE_FULL), NumConstant.NUM_MINUS_TEN);
            }
            String lastResourceNo = "";
            int count = NumConstant.NUM_ZERO;
            while (true) {
                List<String> resourceNoList = resourceInfoRepository.getUpdatedApprovalDocuments(lastExecutionTime, resourceNo, lastResourceNo, nowDate);
                if (CollectionUtils.isEmpty(resourceNoList)) {
                    break;
                }
                count += resourceNoList.size();
                lastResourceNo = resourceNoList.get(resourceNoList.size() - NumConstant.NUM_ONE);
                resourceInfoRepository.setBenchmarkQuantity(resourceNoList);
            }
            this.updateSysLookupValuesById(nowDate, sysLookupValues, count, resourceNo);
        } finally {
            redisLock.unlock();
        }

    }

    /**
     * 更新模型编码
     * @param resourceNo
     */
    @Override
    public void updateModelNumber(String resourceNo)throws Exception {
        String lockKey = RedisKeyConstant.UPDATE_MODEL_ENCODING;
        RedisLock redisLock = new RedisLock(lockKey);
        try {
            boolean lock = redisLock.lock(NumConstant.NUM_3600);
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }
            String lastResourceNo = StringUtils.EMPTY;
            while (true) {
                List<ResourceInfoEntityDTO> resourceNoList = resourceInfoRepository.getModelNumberEmptyList(lastResourceNo, resourceNo);
                if (CollectionUtils.isEmpty(resourceNoList)) {
                    break;
                }
                lastResourceNo = resourceNoList.get(resourceNoList.size() - NumConstant.NUM_ONE).getResourceNo();
                //调B2B接口查询模型编码信息
                ModelNumberParamDTO dto = new ModelNumberParamDTO();
                dto.setBizCode(Constant.B126);
                dto.setSessionId(UUID.randomUUID().toString());
                for (ResourceInfoEntityDTO resourceInfoEntityDTO : resourceNoList) {
                    dto.setLicence(resourceInfoEntityDTO.getResourceNo());
                    this.updateLicenceInfo(resourceInfoEntityDTO, dto);
                    Thread.sleep(NumConstant.NUM_1000);
                }
            }
        }finally {
            redisLock.unlock();
        }
    }

    public void batchUpdate(List<ResourceInfoEntityDTO> updateList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (List<ResourceInfoEntityDTO> resourceInfoEntityDTOS : Lists.partition(updateList, Constant.BATCH_SIZE)) {
                resourceInfoRepository.batchUpdateModelNumber(resourceInfoEntityDTOS);
            }
        }
    }

    public void updateLicenceInfo(ResourceInfoEntityDTO resourceInfoEntityDTO, ModelNumberParamDTO dto) throws Exception {
        try {
            String result = callingB2BUniversalRemoteService.callB2B(Constant.ZTEIMES_MIIT_GETLICENCENO, dto);
            ModelNumberResponseDTO modelNumberResponseDTO = JSON.parseObject(result, ModelNumberResponseDTO.class);
            ModelNumberLicenceInfoDTO modelNumberLicenceInfoDTO = modelNumberResponseDTO == null || modelNumberResponseDTO.getLicenceInfo() == null ? new ModelNumberLicenceInfoDTO() : modelNumberResponseDTO.getLicenceInfo();
            if (StringUtils.isNotEmpty(modelNumberLicenceInfoDTO.getEquipmentNum())) {
                resourceInfoEntityDTO.setModelNumber(modelNumberLicenceInfoDTO.getEquipmentNum());
                //更新模型编码
                resourceInfoRepository.updateModelNumber(resourceInfoEntityDTO);
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            imesLogService.log(String.format(Constant.OBTAIN_MODEL_CODE,JSON.toJSONString(dto),e.getMessage()),Constant.ZTEIMES_MIIT_GETLICENCENO);
        }
    }

    /**
     * 更新上次执行时间
     *
     * @param nowDate
     * @param sysLookupValues
     * @param count
     */
    public int updateSysLookupValuesById(Date nowDate, SysLookupValues sysLookupValues, int count, String resourceNo) {
        if (count > NumConstant.NUM_ZERO && StringUtils.isEmpty(resourceNo)) {
            sysLookupValues.setLookupMeaning(DateUtil.convertDateToString(nowDate, DateUtil.DATE_FORMATE_FULL));
            return sysLookupValuesService.updateSysLookupValuesById(sysLookupValues);
        }
        return NumConstant.NUM_ZERO;
    }

    /**
     * 自动申请标志号
     *
     * @param resourceNo
     */
    @Override
    @RecordLogAnnotation("自动申请标志号")
    public void automaticApplicationOfIdentificationNumber(String resourceNo) throws Exception {
        String lockKey = RedisKeyConstant.AUTOMATIC_APPLICATION_OF_IDENTIFICATION_NUMBER_KEY;
        RedisLock redisLock = new RedisLock(lockKey);
        try {
            boolean lock = redisLock.lock(NumConstant.NUM_3600);
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }

            String lastResourceNo = "";
            while (true) {
                // 获取状态为“初始化”，资源类型为“入网许可”的资源号，统计其可用数量（状态为“初始化")+已申请未下载的标志号数量，如可用数量低于该资源编号基准数量的30%，
                // 则需触发自动申请标志号动作，申请数量为基准数量。基准数量较大时需分批申请。
                List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = resourceInfoRepository.getTheApplicationResourceNumber(resourceNo, lastResourceNo);
                if (CollectionUtils.isEmpty(resourceInfoEntityDTOList)) {
                    break;
                }
                lastResourceNo = resourceInfoEntityDTOList.get(resourceInfoEntityDTOList.size() - NumConstant.NUM_ONE).getResourceNo();
                for (ResourceInfoEntityDTO resourceInfoEntityDTO : resourceInfoEntityDTOList) {
                    this.applicationIdentificationNumber(resourceInfoEntityDTO);
                }
            }
        } finally {
            redisLock.unlock();
        }

    }

    private void applicationIdentificationNumber(ResourceInfoEntityDTO resourceInfoEntityDTO) throws Exception {
        List<NetworkAccessIdentificationNumberApplicationDTO> dtoList = new ArrayList<>();
        Long benchmarkQty = resourceInfoEntityDTO.getBenchmarkQty();
        if (benchmarkQty == null || benchmarkQty < NumConstant.NUM_ONE) {
            return;
        }
        //调B2B接口申请标识号
        while (benchmarkQty > NumConstant.NUM_ZERO) {
            Long qty = benchmarkQty > applyForBatchQuantity ? applyForBatchQuantity : benchmarkQty;
            NetworkAccessIdentificationNumberApplicationDTO dto = this.generateNetworkAccessIdentificationNumberApplicationDTO(resourceInfoEntityDTO, qty);
            dtoList.add(dto);
            benchmarkQty -= applyForBatchQuantity;
        }

        for (NetworkAccessIdentificationNumberApplicationDTO dto : dtoList) {
            try {
                ResourceInfoService resourceInfoService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class);
                resourceInfoService.callB2B(resourceInfoEntityDTO, dto);
            } catch (MesBusinessException e) {
                String errorMsg = this.getErrorMsg(e);
                this.writeErrorRecords(resourceInfoEntityDTO, dto, errorMsg);
                //邮件告警
                sendEmail(resourceInfoEntityDTO,e);
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                this.writeErrorRecords(resourceInfoEntityDTO, dto, errorMsg);
            }
        }

    }

    private String getErrorMsg(MesBusinessException e) {
        Object[] params = e.getParams();
        String errorMsg = params == null || params.length < NumConstant.NUM_ONE ? e.getMessage() : params[NumConstant.NUM_ZERO].toString();
        return errorMsg;
    }

    /**
     * 错误
     *
     * @param resourceInfoEntityDTO
     * @param dto
     * @param errorMsg
     */
    private void writeErrorRecords(ResourceInfoEntityDTO resourceInfoEntityDTO, NetworkAccessIdentificationNumberApplicationDTO dto, String errorMsg) {
        ResourceApplyRecord resourceApplyRecord = this.generateResourceApplyRecord(resourceInfoEntityDTO, dto);
        resourceApplyRecord.setApplyStatus(NetWorkResourceApplyStatus.ABNORMAL_APPLICATION);
        resourceApplyRecord.setErrMsg(StringUtils.substring(errorMsg, NumConstant.NUM_ZERO, NumConstant.NUM_2000));
        resourceApplyRecordService.insert(resourceApplyRecord);
    }

    @Override
    @AlarmAnnotation(alarmName = "automatic_download_identification_number_error", alarmKey = "9920", alarmTitle = "入网自动申请或下载标识超期告警")
    public int callB2B(ResourceInfoEntityDTO resourceInfoEntityDTO, NetworkAccessIdentificationNumberApplicationDTO dto) throws Exception {
        ResourceApplyRecord resourceApplyRecord = this.generateResourceApplyRecord(resourceInfoEntityDTO, dto);
        resourceApplyRecord.setApplyStatus(NetWorkResourceApplyStatus.APPLIED);
        callingB2BUniversalRemoteService.callB2B(Constant.ZTEIMES_MIIT_APPLICATION, dto);
        return resourceApplyRecordService.insert(resourceApplyRecord);
    }

    private void sendEmail(ResourceInfoEntityDTO resourceInfoEntityDTO,MesBusinessException e) throws Exception{
        //收件人
        String mailTo = resourceInfoEntityDTO.getLastUpdatedBy();
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(LOOK_UP_CODE_6846001);
        if (sysLookupValues == null) {
            return;
        }
        String lookupMeaning = sysLookupValues.getLookupMeaning();
        if (StringUtils.isNotEmpty(lookupMeaning)) {
            mailTo = mailTo + Constant.COLON + lookupMeaning;
        }
        //错误信息
        String errorMsg = Constant.STRING_EMPTY;
        Object[] params = e.getParams();
        if (params != null  && params.length > NumConstant.NUM_ZERO){
            errorMsg = params[NumConstant.NUM_ZERO].toString();
        }
        String errorInfo = Constant.STRING_EMPTY;
        if (StringUtils.isNotEmpty(errorMsg)){
            ApplicationNetworkIdentificationNumberDTO applicationNetworkIdentificationNumberDTO = JSON.parseObject(errorMsg, ApplicationNetworkIdentificationNumberDTO.class);
            if(applicationNetworkIdentificationNumberDTO==null){
                return;
            }
            errorInfo = applicationNetworkIdentificationNumberDTO.getErrorInfo();
        }
        String content =  String.format(Constant.APPLICATION_NETWORK_IDENTIFICATION_NUMBER_FAILED_DETAIL,resourceInfoEntityDTO.getResourceNo(),errorInfo);
        StringBuilder html = new StringBuilder("<br/><div>");
        html.append("<p style='margin:0;font-size:13pt'>").append(content).append("</p>");
        html.append("<br/></div>");
        emailUtils.sendMail(mailTo, Constant.APPLICATION_NETWORK_IDENTIFICATION_NUMBER_FAILED, "", html.toString(), "");
    }

    private ResourceApplyRecord generateResourceApplyRecord(ResourceInfoEntityDTO resourceInfoEntityDTO, NetworkAccessIdentificationNumberApplicationDTO dto) {
        ResourceApplyRecord resourceApplyRecord = new ResourceApplyRecord();
        resourceApplyRecord.setApplyId(dto.getOrderId());
        resourceApplyRecord.setResourceType(resourceInfoEntityDTO.getResourceType());
        resourceApplyRecord.setResourceNo(resourceInfoEntityDTO.getResourceNo());
        resourceApplyRecord.setQty(dto.getQuantity());
        resourceApplyRecord.setCreateBy(SYSTEM);
        resourceApplyRecord.setLastUpdatedBy(SYSTEM);
        return resourceApplyRecord;
    }

    private NetworkAccessIdentificationNumberApplicationDTO generateNetworkAccessIdentificationNumberApplicationDTO(ResourceInfoEntityDTO resourceInfoEntityDTO, Long qty) {
        NetworkAccessIdentificationNumberApplicationDTO dto = new NetworkAccessIdentificationNumberApplicationDTO();
        String orderId = UUID.randomUUID().toString();
        dto.setOrderCategory(Constant.ZTE);
        dto.setOrderId(orderId);
        dto.setSessionId(orderId);
        dto.setLicence(resourceInfoEntityDTO.getResourceNo());
        dto.setQuantity(qty);
        dto.setLabelType(Constant.E);
        dto.setCustomNumber(Constant.ZTE);
        dto.setBizCode(Constant.B107);
        dto.setMailMethod(NumConstant.STRING_THREE);
        return dto;
    }


    /**
     * 自动下载标志号
     *
     * @param resourceNo
     * @throws Exception
     */
    @Override
    @RecordLogAnnotation("自动下载标志号")
    @AlarmAnnotation(alarmName = "automatic_download_identification_number_error", alarmKey = "9921", alarmTitle = "入网自动申请或下载标识超期告警")
    public void automaticDownloadFlagNumber(String resourceNo) throws Exception {
        String lockKey = RedisKeyConstant.AUTOMATICALLY_DOWNLOAD_IDENTIFICATION_NUMBER;
        RedisLock redisLock = new RedisLock(lockKey);
        try {
            boolean lock = redisLock.lock(NumConstant.NUM_3600);
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT);
            }
            //申请时间已超过7天还未下载成功，需触发邮件、icenter告警,更新状态为下载超期
            this.dealApplicationTimeHasExceeded7Days(resourceNo);
            // 定时获取状态为已申请且申请时间已超过6小时的入网申请单
            Date endTime = DateUtils.addHours(new Date(), -autoDownLoadHour);
            String lastApplyId = "";
            while (true) {
                List<ResourceApplyRecord> resourceApplyRecordList = resourceApplyRecordService.queryList(endTime, resourceNo, lastApplyId);
                if (CollectionUtils.isEmpty(resourceApplyRecordList)) {
                    break;
                }
                lastApplyId = resourceApplyRecordList.get(resourceApplyRecordList.size() - NumConstant.NUM_ONE).getApplyId();
                this.callB2BDownload(resourceApplyRecordList);
            }
            //申请时间已超过24小时还未下载成功，需触发邮件、icenter告警
            this.dealApplicationTimeHasExceeded24Hours(resourceNo);
        } finally {
            redisLock.unlock();
        }

    }

    /**
     * 处理申请记录
     *
     * @param resourceApplyRecordList
     */
    public void callB2BDownload(List<ResourceApplyRecord> resourceApplyRecordList) {
        for (ResourceApplyRecord resourceApplyRecord : resourceApplyRecordList) {
            try {
                ResourceInfoService resourceInfoService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class);
                resourceInfoService.callB2BDownload(resourceApplyRecord);
            } catch (MesBusinessException e) {
                String errorMsg = this.getErrorMsg(e);
                resourceApplyRecord.setErrMsg(errorMsg);
                this.updateApplyRecord(resourceApplyRecord);
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                resourceApplyRecord.setErrMsg(errorMsg);
                this.updateApplyRecord(resourceApplyRecord);
            }
        }
    }


    /**
     * /申请时间已超过7天还未下载成功，需触发邮件、icenter告警,更新状态为下载超期
     */
    private void dealApplicationTimeHasExceeded7Days(String resourceNo) {
        Date endTime = DateUtils.addDays(new Date(), -autoDownloadOverdueDay);
        String lastApplyId = "";
        while (true) {
            List<ResourceApplyRecord> resourceApplyRecordList = resourceApplyRecordService.queryList(endTime, resourceNo, lastApplyId);
            if (CollectionUtils.isEmpty(resourceApplyRecordList)) {
                break;
            }
            lastApplyId = resourceApplyRecordList.get(resourceApplyRecordList.size() - NumConstant.NUM_ONE).getApplyId();

            List<String> applyIdList = resourceApplyRecordList.stream().map(e -> e.getApplyId()).distinct().collect(Collectors.toList());
            String alarmInfo = String.format(Constant.DOWNLOAD_OVERDUE_BUT_UNSUCCESSFUL_TWO, autoDownloadOverdueDay, StringUtils.join(applyIdList, COMMA));
            AlarmHelper.alarm("automatic_download_identification_number_error", "9922", AlarmSeverityEnum.CRITICAL, Constant.AUTOMATIC_DOWNLOAD_IDENTIFICATION_NUMBER_NETWORK, alarmInfo);
            resourceApplyRecordService.batchUpdateStatusByIdList(applyIdList, SYSTEM, NetWorkResourceApplyStatus.DOWNLOAD_OVERDUE);
        }
    }

    /**
     * 申请时间已超过24小时还未下载成功，需触发邮件、icenter告警
     */
    private void dealApplicationTimeHasExceeded24Hours(String resourceNo) {
        Date endTime = DateUtils.addHours(new Date(), -autoDownloadAlarmHour);
        ResourceApplyRecordPageQueryDTO recordPageQueryDTO = this.initResourceApplyRecordPageQueryDTO(endTime, NumConstant.NUM_5000, resourceNo);
        while (true) {
            PageRows<ResourceApplyRecord> resourceApplyRecordPageRows = resourceApplyRecordService.queryPage(recordPageQueryDTO);
            List<ResourceApplyRecord> resourceApplyRecords = resourceApplyRecordPageRows == null ? null : resourceApplyRecordPageRows.getRows();
            if (CollectionUtils.isEmpty(resourceApplyRecords)) {
                break;
            }
            recordPageQueryDTO.setPage(recordPageQueryDTO.getPage() + NumConstant.NUM_ONE);
            String alarmInfo = String.format(Constant.DOWNLOAD_OVERDUE_BUT_UNSUCCESSFUL, autoDownloadAlarmHour, resourceApplyRecords.stream().map(e -> e.getApplyId()).collect(Collectors.joining(COMMA)));
            AlarmHelper.alarm("automatic_download_identification_number_error", "9922", AlarmSeverityEnum.CRITICAL, Constant.AUTOMATIC_DOWNLOAD_IDENTIFICATION_NUMBER_NETWORK, alarmInfo);
        }
    }

    /**
     * 初始化查询参数
     *
     * @param endTime
     * @return
     */
    private ResourceApplyRecordPageQueryDTO initResourceApplyRecordPageQueryDTO(Date endTime, Integer rows, String resourceNo) {
        ResourceApplyRecordPageQueryDTO resourceApplyRecordPageQueryDTO = new ResourceApplyRecordPageQueryDTO();
        resourceApplyRecordPageQueryDTO.setPage(NumConstant.NUM_ONE);
        resourceApplyRecordPageQueryDTO.setRows(rows);
        resourceApplyRecordPageQueryDTO.setApplyStatus(NetWorkResourceApplyStatus.APPLIED);
        resourceApplyRecordPageQueryDTO.setResourceType(MpConstant.NAL);
        resourceApplyRecordPageQueryDTO.setEndTime(endTime);
        resourceApplyRecordPageQueryDTO.setSearchCount(false);
        resourceApplyRecordPageQueryDTO.setResourceNo(resourceNo);
        return resourceApplyRecordPageQueryDTO;
    }

    @Override
    public int callB2BDownload(ResourceApplyRecord resourceApplyRecord) throws Exception {
        NetworkAccessIdentificationNumberApplicationDTO dto = new NetworkAccessIdentificationNumberApplicationDTO();
        dto.setBizCode(Constant.B124);
        dto.setSessionId(UUID.randomUUID().toString());
        dto.setOrderId(resourceApplyRecord.getApplyId());
        String result = callingB2BUniversalRemoteService.callB2B(Constant.ZTEIMES_MIIT_DOWNLOADDATA, dto);
        DownloadNetworkIdentificationNumberDTO downloadNetworkIdentificationNumberDTO = JSON.parseObject(result, DownloadNetworkIdentificationNumberDTO.class);
        if (downloadNetworkIdentificationNumberDTO == null || StringUtils.isEmpty(downloadNetworkIdentificationNumberDTO.getDownPath())) {
            resourceApplyRecord.setErrMsg(String.format(Constant.FAILED_TO_DOWNLOAD_IDENTIFICATION_NUMBER, JSON.toJSONString(downloadNetworkIdentificationNumberDTO)));
            resourceApplyRecord.setLastUpdatedBy(SYSTEM);
            this.updateApplyRecord(resourceApplyRecord);
            return NUM_ZERO;
        }
        String fileUrl = downloadNetworkIdentificationNumberDTO.getDownPath();
        String filePath = FileUtils.createFilePathAndCheck(System.currentTimeMillis() + ".txt");
        FileUtils.downloadFile(filePath, fileUrl);
        String docId = cloudDiskHelper.fileUpload(filePath, "");
        resourceApplyRecord.setLastUpdatedBy(SYSTEM);
        resourceApplyRecord.setDocId(docId);
        File file = new File(filePath);



        String line = "";
        List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = new ArrayList<>();
        List<Integer> numList = new ArrayList<>();
        int lineNum = NumConstant.NUM_ZERO;
        try (InputStream in = Files.newInputStream(file.toPath());
             InputStreamReader isr = new InputStreamReader(in);
             BufferedReader br = new BufferedReader(isr)){
            while ((line = br.readLine()) != null && lineNum < MAX_PRODUCT_MAC_NUM) {
                lineNum++;
                try {
                    if (line != null) {
                        String[] arr = StringUtils.split(line, COMMA);
                        if (this.checkLineData(arr)) {
                            numList.add(lineNum);
                        } else {
                            ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
                            resourceInfoDetailDTO.setResourceNum(arr[NumConstant.NUM_ZERO]);
                            resourceInfoDetailDTO.setResourceNo(resourceApplyRecord.getResourceNo());
                            resourceInfoDetailDTO.setStatus(NumConstant.STRING_ZERO);
                            resourceInfoDetailDTO.setCreateBy(SYSTEM);
                            resourceInfoDetailDTO.setLastUpdatedBy(SYSTEM);
                            resourceInfoDetailDTO.setScramblingCode(StringUtils.substring(arr[NumConstant.NUM_ONE], NumConstant.NUM_ZERO, NumConstant.NUM_FIFTEEN));
                            resourceInfoDetailDTO.setApplyId(resourceApplyRecord.getApplyId());
                            resourceInfoDetailDTOList.add(resourceInfoDetailDTO);
                        }
                    }
                } catch (Exception e) {
                    numList.add(lineNum);
                }
                //5000条数据批量写一次
                if (resourceInfoDetailDTOList.size() >= NumConstant.NUM_5000) {
                    this.batchInserOrUpdate(resourceInfoDetailDTOList);
                }
            }
        }
        //写资源号信息以及更新申请记录状态
        this.writeResourceInfoAndUpdateApplyStatus(resourceApplyRecord, resourceInfoDetailDTOList, numList);
        return lineNum;
    }

    private boolean checkLineData(String[] arr) {
        return arr == null || arr.length != NumConstant.NUM_TWO || StringUtils.isEmpty(arr[NumConstant.NUM_ZERO]) || StringUtils.length(arr[NumConstant.NUM_ONE]) < NumConstant.NUM_FIFTEEN;
    }

    /**
     * 写资源号信息以及更新申请记录状态
     */
    public void writeResourceInfoAndUpdateApplyStatus(ResourceApplyRecord resourceApplyRecord, List<ResourceInfoDetailDTO> resourceInfoDetailDTOList, List<Integer> numList) {
        //最终还存在解析后数据就新增资源号信息
        if (CollectionUtils.isNotEmpty(resourceInfoDetailDTOList)) {
            this.batchInserOrUpdate(resourceInfoDetailDTOList);
        }
        //存在错误数据，更新申请记录状态为文件处理异常，否则更新为已下载
        if (CollectionUtils.isNotEmpty(numList)) {
            resourceApplyRecord.setErrMsg(String.format(Constant.IF_DOWNSTREAM_PROCESSING_FAILS, StringUtils.join(numList, COMMA)));
            resourceApplyRecord.setApplyStatus(NetWorkResourceApplyStatus.FILE_PROCESSING_EXCEPTION);
        } else {
            resourceApplyRecord.setErrMsg("");
            resourceApplyRecord.setApplyStatus(NetWorkResourceApplyStatus.DOWNLOADED);
        }
        this.updateApplyRecord(resourceApplyRecord);
    }

    public void closeStream(InputStream in, InputStreamReader isr, BufferedReader br) throws IOException {
        if (in != null) {
            in.close();
        }
        if (br != null) {
            br.close();
        }
        if (isr != null) {
            isr.close();
        }
    }

    /**
     * 保存资源号信息
     *
     * @param resourceInfoDetailDTOList
     */
    private static void batchInserOrUpdate(List<ResourceInfoDetailDTO> resourceInfoDetailDTOList) {
        ResourceInfoDetailService resourceInfoDetailService = SpringContextUtil.getBean(Constant.RESOURCE_INFO_DETAIL_SERVICE, ResourceInfoDetailService.class);
        resourceInfoDetailService.batchInserOrUpdate(resourceInfoDetailDTOList);
        resourceInfoDetailDTOList.clear();
    }

    /**
     * 更新资源申请记录
     *
     * @param resourceApplyRecord
     */
    private void updateApplyRecord(ResourceApplyRecord resourceApplyRecord) {
        ResourceApplyRecordService applyRecordService = SpringContextUtil.getBean(Constant.RESOURCE_APPLY_RECORD_SERVICE, ResourceApplyRecordService.class);
        applyRecordService.updateById(resourceApplyRecord);
    }


    /**
     * 入网证总体回传比例计算(定时任务)
     * @throws Exception
     */
    @Override
    public void backPassRatioCalForAll() throws Exception {
        // 数据字典查询回传比例计算最小时间往前
        Map<String, Object> map = calTimeRangeMap(Constant.LOOKUP_CODE_652301001);
        // 查询区间内的有效资源编号，再分批查询数量
        int countAll = 0;
        int countBackPass = 0;
        List<String> resourceNoList = infoDetailRepository.resourceNoListByTimeRange(map);
        if(CollectionUtils.isEmpty(resourceNoList)){
            return;
        }
        List<List<String>> lists = CommonUtils.splitList(resourceNoList, CORE_POOL_SIZE);
        List<String> statusBackPass = new ArrayList<>();
        // 资源状态。0:初始化;1:已分配;2.已绑定;3:已登记;8:已作废;9:已失效(虚拟状态,不实际更新为该值,头表资源状态为已失效且明细表资源状态为0时表示已失效)
        statusBackPass.add(Constant.STR_THREE);
        List<String> statusAll = new ArrayList<>();
        statusAll.add(Constant.STR_0);
        statusAll.add(STR_ONE);
        statusAll.add(STR_TWO);
        statusAll.add(Constant.STR_THREE);
        for (List<String> resourceListTemp : lists) {
            // 查询整体数量
            countAll += infoDetailRepository.backPassNumForAll(resourceListTemp,statusAll);
            countBackPass += infoDetailRepository.backPassNumForAll(resourceListTemp,statusBackPass);
        }
        // 计算比例
        double ratio = countAll == 0 ? 0 : (double)countBackPass / (double)countAll;
        double ratioForAll =(double)Math.round(ratio * 1000) / 1000;
        // 存数据字典
        SysLookupValues sysLookupValue = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_652301002);
        if (!Objects.isNull(sysLookupValue)) {
            sysLookupValue.setLookupMeaning(String.valueOf(ratioForAll));
            sysLookupValuesRepository.updateSysLookupValuesById(sysLookupValue);
        }
    }

    /**
     * 入网证批文回传比例计算(定时任务)
     * @param dto
     * @throws Exception
     */
    @Override
    public void backPassRatioCalWithResourceNo(ResourceInfoEntityDTO dto) throws Exception {
        // 根据数据字典配置需要计算的时间跨度
        Map<String, Object> map = calTimeRangeMap(Constant.LOOKUP_CODE_652302001);
        // 根据数据字典配置需要计算的时间跨度
        Map<String, Object> mapUsefulRange = calTimeRangeMap(Constant.LOOKUP_CODE_652302004);
        map.put("resourceNo",dto.getResourceNo());
        // 查询有效条件下的资源编号（批文）
        List<String> resourceNoUseful = getUsefulResourceNoList(map, mapUsefulRange);
        if(CollectionUtils.isEmpty(resourceNoUseful)){
            return;
        }
        SysLookupValues sysRatio = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_652302002);
        BigDecimal ratio = new BigDecimal("0.8");
        if (!Objects.isNull(sysRatio) && StringUtils.isNotEmpty(sysRatio.getLookupMeaning())) {
            ratio = new BigDecimal(sysRatio.getLookupMeaning());
        }
        String standardValue = String.valueOf(ratio.doubleValue() * 100);
        // 根据资源编号分组计算比例
        List<ResourceInfoEntityDTO> resourceInfoDetailDTOList = new ArrayList<>();
        List<List<String>> lists = CommonUtils.splitList(resourceNoUseful, CORE_POOL_SIZE);
        for (List<String> resourceNos : lists) {
            List<ResourceInfoEntityDTO> resourceTemp = infoDetailRepository.calBackPassRatioWithResourceNo(resourceNos,ratio);
            List<ResourceInfoEntityDTO> ratioUnder = resourceTemp.stream()
                    .filter(item -> FLAG_Y.equals(item.getBackPassWarning()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ratioUnder)){
                resourceInfoDetailDTOList.addAll(ratioUnder);
            }
            // 比例更新到头表
            resourceInfoRepository.batchUpdateBackPassRatio(resourceTemp);
        }
        // 不符合回传比例要求的发送邮件
        sentMailForUnderRatio(resourceInfoDetailDTOList,standardValue);
    }

    private List<String> getUsefulResourceNoList(Map<String, Object> map, Map<String, Object> mapUsefulRange) {
        List<String> resourceNoUseful = new ArrayList<>();
        List<String> resourceNoList = infoDetailRepository.resourceNoListByTimeRange(map);
        if(CollectionUtils.isEmpty(resourceNoList)){
            return resourceNoUseful;
        }
        List<List<String>> resourceLists = CommonUtils.splitList(resourceNoList, CORE_POOL_SIZE);
        for (List<String> resourceList : resourceLists) {
            mapUsefulRange.put("resourceNoList",resourceList);
            List<String> resourceNoUsefulTemp  = resourceInfoRepository.resourceNoListInTimeRange(mapUsefulRange);
            if(CollectionUtils.isNotEmpty(resourceNoUsefulTemp)){
                resourceNoUseful.addAll(resourceNoUsefulTemp);
            }
        }
        return resourceNoUseful;
    }

    private Map<String, Object> calTimeRangeMap(Integer lookupCode) {
        int calTimeRangeLeft = INT_12;
        int calTimeRangeRight = INT_3;
        SysLookupValues sysTimeRange = sysLookupValuesService.findByLookupCode(lookupCode);
        if (!Objects.isNull(sysTimeRange) && StringUtils.isNotEmpty(sysTimeRange.getLookupMeaning())) {
            String timeRange = sysTimeRange.getLookupMeaning();
            // 例：3-12表示3个月前12个月内
            String[] rangeList = timeRange.split(Constant.LINE);
            calTimeRangeRight = StringUtils.isEmpty(rangeList[0])? calTimeRangeRight : Integer.valueOf(rangeList[0]);
            calTimeRangeLeft = StringUtils.isEmpty(rangeList[1])? calTimeRangeLeft : Integer.valueOf(rangeList[1]);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("calTimeRangeLeft", calTimeRangeLeft);
        map.put("calTimeRangeRight", calTimeRangeRight);
        return map;
    }

    public void sentMailForUnderRatio(List<ResourceInfoEntityDTO> ratioUnder,String standardValue) {
        String content = StringUtils.EMPTY;
        try {
            content = getContent(ratioUnder,standardValue);
        } catch (Exception e) {
            content = e.getMessage();
        }
        if (StringUtils.isBlank(content)) {
            return;
        }
        List<String> tagApplicationList = ratioUnder.stream().filter(t -> StringUtils.isNotEmpty(t.getTagApplicant()))
                .map(ResourceInfoEntityDTO::getTagApplicant).distinct().collect(Collectors.toList());
        SysLookupValues sysEmail = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_652302003);
        if(!Objects.isNull(sysEmail) && StringUtils.isNotEmpty(sysEmail.getLookupMeaning())){
            List<String> users = Arrays.asList(StringUtils.split(sysEmail.getLookupMeaning(),COMMA));
            tagApplicationList.addAll(users);
        }
        List<String> sendList  = tagApplicationList.stream().distinct().collect(Collectors.toList());
        sendEmail(sendList, content, standardValue);
    }

    private void sendEmail(List<String> sendList, String contentCN,String standardValue) {
        try {
            String title = NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE +
                    standardValue +STR_PERCENT +NETWORK_IDENTIFICATION_BACK_PASS_RATIO_UNDER_STANDARD_VALUE_END;
            StringBuilder recipenter = new StringBuilder();
            for (String sender : sendList) {
                recipenter.append(sender + Constant.MAILBOX_SUFFIX + Constant.COLON);
            }
            String sendMailer = recipenter.toString();
            if (StringUtils.isEmpty(sendMailer)) {
                return;
            }
            String[] sendMailerArr = sendMailer.split(Constant.COLON);
            List<String> sendMailerList = Arrays.asList(sendMailerArr).stream().distinct().collect(Collectors.toList());
            emailUtils.sendMail(StringUtils.join(sendMailerList, Constant.COLON), title, "", contentCN, "");
        } catch (Exception e) {
            imesLogService.log(e.getStackTrace(), Constant.NETWORK_IDENTIFICATION_BACK_PASS_RATIO_SEND_EMAIL_FAILED);
        }
    }

    private String getContent(List<ResourceInfoEntityDTO> ratioUnder,String standardValue) throws Exception {
        if(CollectionUtils.isEmpty(ratioUnder)){
            return STRING_EMPTY;
        }
        this.setBackPassRatio(ratioUnder);
        StringBuilder html = new StringBuilder("<br/><div>");
        html.append("<p style='margin:0;font-size:13pt'>" + THESE_RESOURCE_NO_BACK_PASS_RATIO_UNDER_STANDARD_VALUE + standardValue + STR_PERCENT + "</p>");
        html.append("<table style='border:1px solid black;width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        html.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        html.append("<td>" + INDEX + "</td>");
        html.append("<td>" + RESOURCE_NO + "</td>");
        html.append("<td>" + DEVICE_TYPE + "</td>");
        html.append("<td>" + BASE_QTY + "</td>");
        html.append("<td>" + AVAILABLE_QTY + "</td>");
        html.append("<td>" + BACK_PASS_RATIO + "</td>");
        html.append("</tr>");
        // 技改详细信息
        for (int i = 0; i < ratioUnder.size(); i++) {
            ResourceInfoEntityDTO detailDTO = ratioUnder.get(i);
            html.append("<tr style='border:1px solid black;'>");
            html.append("<td style='border:1px solid black;'>" + (i + INT_1) + "</td>");
            html.append("<td style='border:1px solid black;'>" + StringUtils.defaultIfEmpty(detailDTO.getResourceNo(),STRING_EMPTY) + "</td>");
            html.append("<td style='border:1px solid black;'>" + StringUtils.defaultIfEmpty(detailDTO.getDeviceType(),STRING_EMPTY) + "</td>");
            html.append("<td style='border:1px solid black;'>" + detailDTO.getBenchmarkQty() + "</td>");
            html.append("<td style='border:1px solid black;'>" + detailDTO.getAvailableQuantity() + "</td>");
            html.append("<td style='border:1px solid black;'>" + detailDTO.getBackPassRatioValue() + "</td>");
            html.append("</tr>");
        }
        html.append("</table>");
        html.append("<br/></div>");
        return html.toString();
    }
    /**
     * 下载模板
     **/
    @Override
    public void downLoadExcelTemplate(HttpServletResponse response) throws Exception {
        ImesExcelUtil.setResponseHeader(response, Constant.RESOURCE_INFO_MODEL_NAME );
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), NetworkLicenseExcelModelBO.class)
                .excelType(ExcelTypeEnum.XLSX).build();
        WriteSheet build = EasyExcelFactory.writerSheet(0, "0").build();
        List<NetworkLicenseExcelModelBO> result = new LinkedList<>();
        result.add(new NetworkLicenseExcelModelBO());
        excelWriter.write(result, build);
        excelWriter.finish();
    }
}