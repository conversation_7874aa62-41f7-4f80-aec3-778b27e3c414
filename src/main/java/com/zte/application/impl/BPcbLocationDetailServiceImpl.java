package com.zte.application.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.BPcbLocationBomService;
import com.zte.application.BPcbLocationDetailService;
import com.zte.application.BsAsyncDataService;
import com.zte.application.BsItemInfoService;
import com.zte.application.BsPremanuBomInfoService;
import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.StringConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.craftTech.CtRouteHeadRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.CommonRedisUtil;
import com.zte.springbootframe.util.EmailUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.STRING_EMPTY;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
public class BPcbLocationDetailServiceImpl implements BPcbLocationDetailService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;

    @Autowired
    private BBomDetailRepository bBomDetailRepository;

    @Autowired
    private CloudDiskHelper cloudDiskHelper;

    @Autowired
    private BBomHeaderRepository bBomHeaderRepository;

    @Autowired
    private PcbLocationLogRepository pcbLocationLogRepository;

    @Autowired
    private BsItemInfoRepository bsItemInfoRepository;

    @Autowired
    private BPcbLocationBomService bPcbLocationBomService;

    @Autowired
    private BPcbLocationBomServiceImpl bPcbLocationBomServiceImpl;

    @Autowired
    private CadUploadRecordRepository cadUploadRecordRepository;
    @Autowired
    private BsAsyncDataService bsAsyncDataService;
    @Autowired
    private BsPremanuItemInfoService bsPremanuItemInfoService;
    @Autowired
    private IMESLogService imesLogService;
    @Autowired
    private CtRouteHeadRepository ctRouteHeadRepository;
    @Autowired
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Autowired
    private BsPremanuBomInfoService bsPremanuBomInfoService;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private MdsRemoteService mdsRemoteService;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private BsItemInfoService itemInfoService;

    @Autowired
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @Autowired
    private CommonRedisUtil commonRedisUtil;

    public void setbPcbLocationDetailRepository(BPcbLocationDetailRepository bPcbLocationDetailRepository) {
        this.bPcbLocationDetailRepository = bPcbLocationDetailRepository;
    }

    /**
     * 通过料单代码和位号获取物料代码-返修中心
     * @param bPcbLocationDetailDTOList
     * @param empNo
     * @return
     */
    @Override
    public List<BPcbLocationDetailDTO> bitNumInfoQuery(List<BPcbLocationDetailDTO> bPcbLocationDetailDTOList, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(bPcbLocationDetailDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_IS_NULL);
        }
        // 校验数量是否超过10条
        if (bPcbLocationDetailDTOList.size() > Constant.INT_10){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.A_MAXIMUM_OF_TEN_DATA_CAN_BE_QUERIED);
        }
        // 如果BOM有位号数据，但是没匹配上对应的位号，触发一次BOM位号解析再查询数据
        // 把多个位号的字符串转换为单个位号的List
        List<BPcbLocationDetailDTO> selectDtoList = new ArrayList<>();
        for (BPcbLocationDetailDTO bPcbLocationDetailDTO : bPcbLocationDetailDTOList) {
            if (StringUtils.isBlank(bPcbLocationDetailDTO.getProductCode()) || StringUtils.isBlank(bPcbLocationDetailDTO.getPointLoc())){
                // 校验产品代码或者位号是否为空
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHECK_THAT_THE_PRODUCT_CODE_IS_EMPTY);
            }
            List<String> pointLocList = Arrays.asList(bPcbLocationDetailDTO.getPointLoc().split(Constant.STR_SPLIT));
            bPcbLocationDetailDTO.setPointLocList(pointLocList);
            pointLocList.forEach(p -> {
                BPcbLocationDetailDTO tempDto = new BPcbLocationDetailDTO();
                tempDto.setProductCode(bPcbLocationDetailDTO.getProductCode());
                tempDto.setPointLoc(p);
                selectDtoList.add(tempDto);
            });
        }
        // 批量查询BPcbLocationDetail map<productCode,List<BPcbLocationDetail>>
        List<String> productCodeList = bPcbLocationDetailDTOList.stream().map(m -> m.getProductCode()).collect(Collectors.toList());
        List<BPcbLocationDetail> bPcbLocationDetails = bPcbLocationDetailRepository.batchSelectBPcbLocationDetail(productCodeList);
        Map<String, List<BPcbLocationDetail>> groupedByProductCode = bPcbLocationDetails.stream().collect(Collectors.groupingBy(BPcbLocationDetail::getProductCode));
        // 查找有位号数据，但是没匹配上对应位号的BPcbLocationDetail
        // 构建List 存储需要Bom解析的productCode
        List<String> needAnalyze = new ArrayList<>();
        for (BPcbLocationDetailDTO bPcbLocationDetailDTO : bPcbLocationDetailDTOList) {
            groupedByProductCode.forEach((productCode, details) -> {
                boolean isAnalyze = false;
                if (productCode.equals(bPcbLocationDetailDTO.getProductCode())){
                    List<String> stringList = details.stream().map(m -> m.getPointLoc()).collect(Collectors.toList());
                    isAnalyze = !stringList.containsAll(bPcbLocationDetailDTO.getPointLocList());
                }
                if (isAnalyze){
                    needAnalyze.add(productCode);
                }
            });
        }
        this.batchAnalyzeBom(needAnalyze,empNo);
        // 根据产品代码和位号查询物料代码
        return bPcbLocationDetailRepository.selectByProductCodesAndPointLocs(selectDtoList);
    }

    public void batchAnalyzeBom(List<String> needAnalyze, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(needAnalyze)) {
            return;
        }
        // 待分解的数据 REMARK IN ('N','E') OR REMARK IS NULL or REMARK = ''的数据就是待分解数据
        // 把所有productCode对应待分解数据查出来 B_BOM_HEADER product_code有多个 取最后更新时间最新的product_code
        List<BBomHeader> bBomHeaderList = bBomHeaderRepository.selectBBomHeaderByProductCodes(needAnalyze);
        if (CollectionUtils.isEmpty(bBomHeaderList)){
            return;
        }
        // 对product_code批量加锁
        List<String> lockList = bBomHeaderList.stream().map(m -> Constant.REDIS_KEY_BPCB_LOCATION_BOM + m.getProductCode()).collect(Collectors.toList());
        String uuid = commonRedisUtil.batchLockRedisKey(lockList, Constant.ONE_MINUTES);
        List<String> strings = null;
        try {
            // 返回没有解析的产品代码
            strings = bPcbLocationBomServiceImpl.batchInsertPcbLocation(bBomHeaderList, empNo);
        } finally {
            commonRedisUtil.batchUnLockRedis(needAnalyze, uuid);// 释放锁
        }
        if (!CollectionUtils.isEmpty(strings)){
            // 没有解析的产品代码Remark改为E
            bBomHeaderRepository.updateHeaderRemarkByProductCodes(strings, Constant.E_STATUS, empNo);
        }
        // 去除没有解析的产品代码
        needAnalyze.removeAll(strings);
        // 解析的产品代码Remark改为Y
        bBomHeaderRepository.updateHeaderRemarkByProductCodes(needAnalyze, Constant.Y_STATUS, empNo);

    }

    /**
     * 位号拆分
     * 每个BOM头要加个标识 解析过的标记
     * 解析连续的位号 比如D1-D8 解析成D1,D2,...,D8
     * 不符合规则的跳过
     */
    @Override
    public String batchInsertFromBom(BPcbLocationDetailDTO bPcbLocationDetail, int limitNum) throws Exception {

        // errorMsg: 错误信息返回值
        // insertManual: 只拆分一个bom，由前台传入，需要返回错误信息
        String errorMsg = BusinessConstant.EMPTY_STRING;
        boolean insertManual = false;
        String empNo = bPcbLocationDetail.getCreateUser();
        String productCode = bPcbLocationDetail.getProductCode();
        Map<String, Object> headerMap = new HashMap<>();
        if (limitNum <= NumConstant.NUM_ZERO) {
            limitNum = NumConstant.NUM_TEN;
        }
        headerMap.put("limitNum", limitNum);
        if (StringUtils.isNotBlank(productCode)) {
            headerMap.put("limitNum", NumConstant.NUM_ONE);
            headerMap.put("productCode", productCode);
            headerMap.put("inRemark", MpConstant.INREMARK_STRS);
            insertManual = true;
        }
        //待分解的数据
        List<BBomHeader> bBomHeaderList = bBomHeaderRepository.selectBBomHeaderNotCal(headerMap);
        if (CollectionUtils.isEmpty(bBomHeaderList)) {
            errorMsg = CommonUtils.getLmbMessage(MessageId.NO_BOM_DATA_NEED_SPLIT);
            writeLog(errorMsg, bPcbLocationDetail.getProductCode(), Integer.toString(limitNum), empNo);
            return errorMsg;
        }
        for (BBomHeader bomHeader : bBomHeaderList) {
            RedisLock lock = new RedisLock(Constant.REDIS_KEY_BPCB_LOCATION_BOM + bomHeader.getProductCode());
            boolean isLock = lock.lock();
            if (!isLock && StringUtils.isNotEmpty(productCode)) {
                errorMsg = CommonUtils.getLmbMessage(MessageId.PRODUCTCODE_IS_DEALING, productCode);
                return errorMsg;
            } else if (!isLock) {
                continue;
            }
            try {
                bomHeader.setLastUpdatedBy(empNo);
                try {
                    bPcbLocationBomService.insertFromOne(bomHeader, empNo);
                } catch (Exception e) {
                    writeLog(e.getMessage(), bomHeader.getProductCode(), Integer.toString(limitNum), empNo);
                    bomHeader.setRemark(Constant.E_STATUS);
                    bBomHeaderRepository.updateHeaderRemarkById(bomHeader);
                    if (insertManual) {
                        return e.getMessage();
                    }
                    continue;
                }
                bomHeader.setRemark(Constant.Y_STATUS);
                bBomHeaderRepository.updateHeaderRemarkById(bomHeader);
            } finally {
                lock.unlock();
            }
        }
        return errorMsg;
    }


    public void writeLog(String errMsg, String productCode, String itemCode, String empNo) {
        try {
            PcbLocationLog log = new PcbLocationLog();
            log.setRecordId(java.util.UUID.randomUUID().toString());
            log.setErrorMsg(errMsg);
            log.setProductCode(productCode);
            log.setItemCode(itemCode);
            log.setCreateBy(empNo);
            log.setLastUpdatedBy(empNo);
            log.setEnabledFlag(Constant.FLAG_Y);
            pcbLocationLogRepository.insertPcbLocationLog(log);
        } catch (Exception e) {
            logger.error("位号拆分日志写入异常: {} {} {} {}", productCode, itemCode, errMsg, e.getMessage());
        }

    }

    /**
     * <AUTHOR>
     */
    @Override
    public BPcbLocationDetail getLocationDetailByLocationCode(BPcbLocationDetail bPcbLocationDetail) {
        return bPcbLocationDetailRepository.getLocationDetailByLocationCode(bPcbLocationDetail);
    }


    @Override
    public boolean isExists(BPcbLocationDetail bPcbLocationDetail) {
        List<BPcbLocationDetail> list = bPcbLocationDetailRepository.getCountByCondition(bPcbLocationDetail);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (BPcbLocationDetail location : list) {
            if (location.getLocationCode().contains(bPcbLocationDetail.getLocationCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询位号
     *
     * @param entity
     * @return
     */
    @Override
    public List<BPcbLocationDetail> getTagNumByCondition(BPcbLocationDetail entity) {
        return bPcbLocationDetailRepository.getCountByCondition(entity);
    }



    /* Started by AICoder, pid:w479bv356a0022e140170a9c809cd14567d94c83 */
    /**
     * 获取位号信息getList方法
     *
     * @param map        参数集
     * @param prodplanId
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<BPcbLocationDetail> getList(Map map, String prodplanId) throws Exception {
        List<BPcbLocationDetail> bPcbLocationDetailList = bPcbLocationDetailRepository.getPage(map);
        if (StringUtils.isBlank(prodplanId)) {
            return bPcbLocationDetailList;
        }
        //维修录入优化，如果输入批次，则取制造BOM
        List<String> itemCodeList = bPcbLocationDetailList.stream().map(BPcbLocationDetail::getItemCode).collect(Collectors.toList());
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setProdplanIdList(Collections.singletonList(prodplanId));
        bProdBomChangeDetailDTO.setOriginalItemCodeList(itemCodeList);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS = bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(bProdBomChangeDetailDTO);
        if (CollectionUtils.isEmpty(bProdBomChangeDetailDTOS)) {
            return bPcbLocationDetailList;
        }
        for (BPcbLocationDetail bPcbLocationDetail : bPcbLocationDetailList) {
            for (BProdBomChangeDetailDTO prodBomChangeDetailDTO : bProdBomChangeDetailDTOS) {
                //替换物料代码
                if (bPcbLocationDetail.getItemCode().equals(prodBomChangeDetailDTO.getOriginalItemCode())) {
                    bPcbLocationDetail.setItemCode(prodBomChangeDetailDTO.getItemCode());
                    bPcbLocationDetail.setItemName(prodBomChangeDetailDTO.getChiDesc());
                    bPcbLocationDetail.setChiDesc(prodBomChangeDetailDTO.getChiDesc());
                }
            }
        }
        return bPcbLocationDetailList;
    }
    /* Ended by AICoder, pid:w479bv356a0022e140170a9c809cd14567d94c83 */



    /**
     * 位号信息分页查询方法：获取条数
     *
     * @param map 参数集
     * @return Long
     * @throws Exception 异常
     */
    @Override
    public Long getCount(Map map) throws Exception {
        return bPcbLocationDetailRepository.getCount(map);
    }

    /**
     * 获取位号信息getList方法
     *
     * @param map 参数集
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<BPcbLocationDetail> getPage(Map map) throws Exception {
        return bPcbLocationDetailRepository.getPage(map);
    }

    /**
     * 上传解析CAD文件
     *
     * @param dto 参数
     * @return list
     * @throws Exception 异常
     */
    @Override
    public BsAsyncDataDTO uploadCadFile(BPcbLocationDetailDTO dto) throws Exception {
        BsAsyncDataDTO dataDTO;
        try (BufferedInputStream bis = new BufferedInputStream(dto.getFile().getInputStream())){
            dataDTO = this.handleCad(bis, dto);
        }
        return dataDTO;
    }

    /**
     * 下载CAD文件
     * @param productCode
     * @param empNo
     * @return
     * @throws Exception
     */
    @Override
    public String downloadCadFile(String productCode, String empNo) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productCode", productCode);
        List<BBomHeader> list = bBomHeaderRepository.getList(map);
        if (CollectionUtils.isEmpty(list)){
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_IS_NOT_IMPORT_CAD_FILE);
        }
        return cloudDiskHelper.getFileDownloadUrl(list.get(0).getCloudDiskId(), list.get(0).getFileName(), empNo);
    }

    /**
     * 修改 B_BOM_HEADER 表 IMPORTED_CAD_FLAG，标记CAD已导入
     *
     * @param productCode the product code
     */
    public void updateBBomHeader(String productCode) {
        try {
            bBomHeaderRepository.updateBBomHeaderByIdSelective(new BBomHeader() {{
                setProductCode(productCode);
                setImportedCadFlag(Constant.FLAG_Y);
            }});
        } catch (Exception e) {
        }
    }

    private BPcbLocationDetail generateBPcbLoc(String str, Integer factoryId, String empNo, Integer entityId,
                                               String productCode) {
        String[] strArr = str.split("!");
        if (strArr.length < Constant.CAD_FILE_LISTSIZE || !Constant.PACKAGE.equals(strArr[Constant.INT_7])) {
            return null;
        }
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetail.setProductCode(productCode);
        bPcbLocationDetail.setFactoryId(factoryId);
        bPcbLocationDetail.setEntityId(entityId);
        bPcbLocationDetail.setLastUpdatedBy(empNo);
        bPcbLocationDetail.setPointLoc(strArr[1]);
        //NO表示SMT-A 贴片A面,YES代表SMT-B 贴片B面
        bPcbLocationDetail.setCraftSection(Constant.YSE_FLAG.equals(strArr[9]) ? Constant.SMT_B_FLAG : Constant.SMT_A_FLAG);
        bPcbLocationDetail.setProcessCode(Constant.YSE_FLAG.equals(strArr[9]) ? StringConstant.STRING_2 : StringConstant.STRING_1);
        bPcbLocationDetail.setDirection(strArr[10]);
        return bPcbLocationDetail;
    }

    /**
     * 位号汇总信息：获取条数
     *
     * @param map 参数集
     * @return Long
     * @throws Exception 异常
     */
    @Override
    public Long getGroupCount(Map map) throws Exception {
        return bPcbLocationDetailRepository.getGroupCount(map);
    }

    /**
     * 获取汇总信息：分页
     *
     * @param map 参数集
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<BPcbLocationDetail> getGroupPage(Map map) throws Exception {
        return bPcbLocationDetailRepository.getGroupPage(map);
    }

    /**
     * 获取infor需求单物料明细
     *
     * @param map
     * @return
     */
    @Override
    public List<BPcbLocationDetailDTO> getInforItemList(Map map) {
        return bPcbLocationDetailRepository.getInforItemList(map);
    }

    /**
     * 按物料代码和工艺段分组
     *
     * @param map 参数集
     * @return list
     * @throws Exception 异常
     */
    @Override
    public List<BPcbLocationDetail> getGroupListByProductCode(Map map) throws Exception {
        return bPcbLocationDetailRepository.getGroupListByProductCode(map);
    }

    @Override
    public ServiceData getProcessCraftionNumber(String productCode) throws Exception {
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        List<BPcbLocationDetailDTO> processCraftionNumber = bPcbLocationDetailRepository.getProcessCraftionNumber(productCode);
        if (CollectionUtils.isEmpty(processCraftionNumber)) {
            return ret;
        }
        int all = 0;
        Map<String, Object> resultMap = new HashMap<>();
        for (BPcbLocationDetailDTO bPcbLocationDetailDTO : processCraftionNumber) {
            if (Constant.SMTA.equals(bPcbLocationDetailDTO.getCraftSection())) {
                resultMap.put("SMT_A", bPcbLocationDetailDTO.getUsageCount());
                all += Integer.valueOf(bPcbLocationDetailDTO.getUsageCount());
            } else if (Constant.SMTB.equals(bPcbLocationDetailDTO.getCraftSection())) {
                resultMap.put("SMT_B", bPcbLocationDetailDTO.getUsageCount());
                all += Integer.valueOf(bPcbLocationDetailDTO.getUsageCount());
            } else if (Constant.DIP.equals(bPcbLocationDetailDTO.getCraftSection())) {
                resultMap.put("DIP", bPcbLocationDetailDTO.getUsageCount());
                all += Integer.valueOf(bPcbLocationDetailDTO.getUsageCount());
            } else {
                all += Integer.valueOf(bPcbLocationDetailDTO.getUsageCount());
            }
        }
        resultMap.put("ALL", all);
        ret.setBo(resultMap);
        return ret;
    }

    /**
     * 根据料单代码集合获取
     *
     * @param productCodes
     * @return the by product codes
     */
    @Override
    public List<BPcbLocationDetailDTO> getByProductCodes(List<String> productCodes) {
        return bPcbLocationDetailRepository.getByProductCodes(productCodes);
    }

    @Override
    public BsAsyncDataDTO handleCad(InputStream bis, BPcbLocationDetailDTO dto) throws Exception {
        String productCode = dto.getProductCode();
        String empNo = dto.getCreateUser();
        Integer factoryId = dto.getFactoryId();
        Integer entityId = dto.getEntityId();
        BufferedReader in = null;
        String msg = null;
        String msgId = null;
        boolean isSuccess = false;
        // 等待处理标识
        boolean writeProcess = false;
        List<BPcbLocationDetail> pcbLocationDetailList = new ArrayList<>();
        BsAsyncDataDTO bsAsyncDataDTO = new BsAsyncDataDTO();
        bsAsyncDataDTO.setLastUpdateBy(empNo);
        bsAsyncDataDTO.setCreateBy(empNo);
        // 是否有正在处理的任务
        if (extractedPrecessing(dto, bsAsyncDataDTO)) {
            return bsAsyncDataDTO;
        }

        try {
            // 先校验是否已解析位号
            Long locationCount = bPcbLocationDetailRepository.getCount(new HashMap() {{
                put("productCode", dto.getProductCode());
            }});
            if (locationCount == null || locationCount < Constant.INT_1) {
                msgId = MessageId.NO_LOCATION_DATA;
                throw new Exception();
            }
            //10M缓存
            in = new BufferedReader(new InputStreamReader(bis, Constant.ENCODE_UTF_8), MpConstant.BYTES_10M);
            String str;
            Map<String, Integer> params = new HashMap<>();
            params.put("factoryId", factoryId);
            params.put("entityId", entityId);
            extracted(productCode, empNo, params, in, pcbLocationDetailList);
            if (CollectionUtils.isEmpty(pcbLocationDetailList)) {
                // 解析文件位号信息为空
                msgId = MessageId.CAD_POINT_EMPTY;
                throw new Exception();
            }
            // 自动CAD 导入
            if (StringUtils.isNotBlank(dto.getAutoFlag())) {
                isSuccess = isSuccessMethod(productCode, pcbLocationDetailList);
            } else {
                List<BPcbLocationDetail> bPcbLocationDetails = getbPcbLocationDetails(productCode);
                if (CollectionUtils.isEmpty(bPcbLocationDetails)) {
                    isSuccess = isSuccessMethod(productCode, pcbLocationDetailList);
                    this.updateAsyncStatus(dto, msg, bsAsyncDataDTO);
                } else {
                    Map<String, List<BPcbLocationDetail>> pointLocGroup = bPcbLocationDetails.stream()
                            .collect(Collectors.groupingBy(item -> item.getPointLoc() + Constant.SMT_STR));
                    Map<String, List<BPcbLocationDetail>> cadGroup = pcbLocationDetailList.stream()
                            .collect(Collectors.groupingBy(item -> item.getPointLoc() + Constant.SMT_STR));
                    // 判断所有的SMT点位是否CAD 文件中
                    List<BPcbLocationDetail> lostPointList = new LinkedList<>();
                    extracted(pointLocGroup, cadGroup, lostPointList);
                    // CAD 文件有问题部分 位号缺失需要用户确认
                    if (!CollectionUtils.isEmpty(lostPointList)) {
                        writeProcess = isWriteProcess(dto, pcbLocationDetailList, bsAsyncDataDTO, lostPointList);
                    } else {
                        isSuccess = isSuccessMethod(productCode, pcbLocationDetailList);
                    }
                }
            }
        } catch (Exception e) {
            msg = getString(msg, msgId, e);
        } finally {
            extracted(dto, in, msg, writeProcess, bsAsyncDataDTO);
        }
        syncXPFromMds(bsAsyncDataDTO, isSuccess);
        extracted(dto, productCode, isSuccess);
        // CAD文件上传文档云
        this.uploadCadFileForCloudDisk(dto, isSuccess);
        return bsAsyncDataDTO;
    }

    public void uploadCadFileForCloudDisk(BPcbLocationDetailDTO dto, boolean isSuccess) {
        if (isSuccess){
            MultipartFile file = dto.getFile();
            String empNo = dto.getCreateUser();
            String fileId = "";
            // 上传文档云
            try {
                fileId = cloudDiskHelper.fileUpload(file, empNo);
            } catch (Exception e) {
                throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.CLOUD_DISK_INPUT_ERR);
            }
            String finalFileId = fileId;
            String originalFilename = file.getOriginalFilename();
            // 保存文档云ID
            bBomHeaderRepository.updateBBomHeaderByIdSelective(new BBomHeader() {{
                setProductCode(dto.getProductCode());
                setFileName(originalFilename);
                setCloudDiskId(finalFileId);
            }});
        }

    }

    private void syncXPFromMds(BsAsyncDataDTO bsAsyncDataDTO, boolean isSuccess) throws Exception {
        List<SysLookupValues> sysList = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_TYPE_652307);
        if (!CollectionUtils.isEmpty(sysList)) {
            boolean syncXPFlag = sysList.stream().anyMatch(e->new BigDecimal(Constant.LOOK_UP_TYPE_652307001).compareTo(e.getLookupCode()) == NumConstant.NUM_ZERO && Constant.FLAG_Y.equals(e.getLookupMeaning()));
            if (syncXPFlag && isSuccess) {
                preManuMDSProgrammingSync(bsAsyncDataDTO);
            }
        }
    }

    private void extracted(Map<String, List<BPcbLocationDetail>> pointLocGroup, Map<String, List<BPcbLocationDetail>> cadGroup, List<BPcbLocationDetail> lostPointList) {
        pointLocGroup.keySet().forEach(pointIoc -> {
            if (CollectionUtils.isEmpty(cadGroup.get(pointIoc))) {
                lostPointList.addAll(pointLocGroup.get(pointIoc));
            }
        });
    }

    private List<BPcbLocationDetail> getbPcbLocationDetails(String productCode) throws Exception {
        // 手动导CAD 判断位号信息
        List<BPcbLocationDetail> bPcbLocationDetails = bPcbLocationDetailRepository.selectBPcbLocationDetailByProductCode(productCode);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(bPcbLocationDetails)) {
            throw new Exception();
        }
        // 对比是不是还有没有位号信息的SMT
        bPcbLocationDetails = bPcbLocationDetails.stream()
                // 过滤出 SMT 的位号信息
                .filter(bPcbLocationDetail -> bPcbLocationDetail != null && Constant.SMT_STR.equals(bPcbLocationDetail.getCraftSection()))
                .collect(Collectors.toList());
        return bPcbLocationDetails;
    }

    private String getString(String msg, String msgId, Exception e) {
        String s = JSON.toJSONString(e);
        s = s.length() > Constant.INT_1000 ? s.substring(Constant.INT_0, Constant.INT_1000) : s;
        msg = StringUtils.isNotBlank(msgId) ? CommonUtils.getLmbMessage(msgId) : s;
        return msg;
    }

    private void extracted(BPcbLocationDetailDTO dto, BufferedReader in, String msg, boolean writeProcess, BsAsyncDataDTO bsAsyncDataDTO) throws IOException {
        if (in != null) {
            in.close();
        }
        if (!writeProcess) {
            this.updateAsyncStatus(dto, msg, bsAsyncDataDTO);
        }
    }

    private void extracted(BPcbLocationDetailDTO dto, String productCode, boolean isSuccess) {
        // 触发BOM 分阶
        if (isSuccess && !MpConstant.ENABLED_FLAG_Y.equals(dto.getAutoFlag())) {
            List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS = new LinkedList<>();
            PsWorkOrderBasicDTO b1 = new PsWorkOrderBasicDTO();
            b1.setItemNo(productCode);
            b1.setItemName(productCode);
            psWorkOrderBasicDTOS.add(b1);
            bsPremanuItemInfoService.asyncCycleSaveSubLevelPremanuInfo(psWorkOrderBasicDTOS);
        }
    }

    private void extracted(String productCode, String empNo, Map<String, Integer> params, BufferedReader in, List<BPcbLocationDetail> pcbLocationDetailList) throws IOException {
        String str;
        Integer factoryId = params.get("factoryId");
        Integer entityId = params.get("entityId");
        while (!StringUtils.isEmpty(str = in.readLine()) && null != str) {
            if (str.contains(Constant.PACKAGE)) {
                BPcbLocationDetail bPcbLocationDetail = generateBPcbLoc(str, factoryId, empNo, entityId, productCode);
                if (null != bPcbLocationDetail) {
                    pcbLocationDetailList.add(bPcbLocationDetail);
                }
            }
        }
    }

    /**
     * 组装待用户确认数据
     *
     * @param dto
     * @param pcbLocationDetailList
     * @param bsAsyncDataDTO
     * @param lostPointList
     * @return
     */
    public boolean isWriteProcess(BPcbLocationDetailDTO dto, List<BPcbLocationDetail> pcbLocationDetailList, BsAsyncDataDTO bsAsyncDataDTO, List<BPcbLocationDetail> lostPointList) {
        Map<String, List<BPcbLocationDetail>> collect = lostPointList.stream()
                .collect(Collectors.groupingBy(BPcbLocationDetail::getItemCode));
        StringBuffer errorMsg = new StringBuffer();
        StringBuffer itemBuffer = new StringBuffer();
        StringBuffer tempPoint = new StringBuffer();
        collect.entrySet().forEach(entity -> {
            itemBuffer.append(entity.getKey()).append(Constant.COMMA);
            entity.getValue().forEach(item -> {
                tempPoint.append(item.getPointLoc()).append(Constant.COMMA);
            });
        });
        String point = tempPoint.toString();
        point = point.substring(Constant.INT_0, point.length() == Constant.INT_0 ? Constant.INT_0 : point.length() - Constant.INT_1);
        String itemString = itemBuffer.toString();
        itemString = itemString.substring(Constant.INT_0, itemString.length() == Constant.INT_0 ? Constant.INT_0 : itemString.length() - Constant.INT_1);
        errorMsg.append(CommonUtils.getLmbMessage(MessageId.CAD_POINT_LOST_ERROR, new String[]{point, itemString}));
        SimpleDateFormat simple = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        errorMsg.append(CommonUtils.getLmbMessage(MessageId.CAD_POINT_LOST_LAST_INFOR,
                        new String[]{simple.format(new Date()).toString(), dto.getCreateUser()}))
                .append(CommonUtils.getLmbMessage(MessageId.CAD_POINT_LOST_ERROR_SURE));
        bsAsyncDataDTO.setRetCode(errorMsg.toString());
        bsAsyncDataDTO.setParams(JSON.toJSONString(pcbLocationDetailList));
        bsAsyncDataDTO.setStatus(MpConstant.AsyncConstant.WAITING);
        bsAsyncDataService.updateDataById(bsAsyncDataDTO);
        return true;
    }

    /**
     * 更新异步表状态
     *
     * @param dto
     * @param msg
     * @param bsAsyncDataDTO
     */
    private void updateAsyncStatus(BPcbLocationDetailDTO dto, String msg, BsAsyncDataDTO bsAsyncDataDTO) {
        // 不是自动导入CAD
        if (StringUtils.isBlank(dto.getAutoFlag())) {
            // msg 为空标识处理成功
            bsAsyncDataDTO.setStatus(StringUtils.isNotBlank(msg) ? MpConstant.AsyncConstant.FAILED : MpConstant.AsyncConstant.SUCCESS);
            bsAsyncDataDTO.setRetCode(msg);
            bsAsyncDataService.updateDataById(bsAsyncDataDTO);
        }
    }

    private boolean isSuccessMethod(String productCode, List<BPcbLocationDetail> pcbLocationDetailList) {
        CommonUtils.splitList(pcbLocationDetailList, Constant.BATCH_SIZE).forEach(item -> {
            bPcbLocationDetailRepository.batchUpdateFromCAD(item);
        });
        // 修改 B_BOM_HEADER 表 IMPORTED_CAD_FLAG，标记CAD已导入
        this.updateBBomHeader(productCode);
        return true;
    }

    /**
     * 是否有正在处理中/或者等待用户操作的的任务
     *
     * @param dto
     * @return
     */
    private boolean extractedPrecessing(BPcbLocationDetailDTO dto, BsAsyncDataDTO bsAsyncDataDTO) {
        // 不是CAD 自动导入
        if (!MpConstant.ENABLED_FLAG_Y.equals(dto.getAutoFlag())) {
            // 存在解析中和等待用户确认的数据
            bsAsyncDataDTO.setServiceKey(dto.getProductCode());
            bsAsyncDataDTO.setServiceType(MpConstant.AsyncConstant.CAD_ASYNC_TYPE);
            bsAsyncDataDTO.getStatusList().add(MpConstant.AsyncConstant.PRECESSING);
            bsAsyncDataDTO.getStatusList().add(MpConstant.AsyncConstant.WAITING);
            // 是否有正在解析的任务
            List<BsAsyncDataDTO> bsAsyncDataDTOS = bsAsyncDataService.queryAsyncByCondition(bsAsyncDataDTO);
            if (!CollectionUtils.isEmpty(bsAsyncDataDTOS)) {
                // 有处理中的数据直接返回等待
                BeanUtils.copyProperties(bsAsyncDataDTOS.get(Constant.INT_0), bsAsyncDataDTO);
                return true;
            }
            // 新增处理中记录到异步表
            bsAsyncDataDTO.setStatus(MpConstant.AsyncConstant.PRECESSING);
            bsAsyncDataDTO.setHeadId(UUID.randomUUID().toString());
            bsAsyncDataService.insertSingleData(bsAsyncDataDTO);
        }
        return false;
    }

    /**
     * 获取异常信息
     *
     * @param e
     */
    private String getExMsg(Exception e) {
        String msg = ExceptionUtils.getStackTrace(e);
        if (StringUtils.isNotBlank(msg) && msg.length() > NumConstant.NUM_3000) {
            msg = msg.substring(NumConstant.NUM_ZERO, NumConstant.NUM_3000);
        }
        return msg;

    }

    @Override
    @RecordLogAnnotation("删除垃圾数据")
    public void deleteCadDetailsByProductCode(List<String> productCodeList) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return;
        }
        productCodeList.forEach(this::deleteAnyWhere);
    }

    private void deleteAnyWhere(String productCode) {
        bPcbLocationDetailRepository.deleteCadDetailsByProductCode(productCode);
        bBomHeaderRepository.updateBBomHeaderByIdSelective(new BBomHeader() {
            {
                setProductCode(productCode);
            }

            {
                setRemark(Constant.N_STATUS);
            }
        });
    }


    @Override
    public List<BsAsyncDataDTO> asyncQueryCadExpireResult(BsAsyncDataDTO bsAsyncDataDTO) {
        return bsAsyncDataService.queryAsyncByCondition(bsAsyncDataDTO);
    }

    @Override
    public List<BsAsyncDataDTO> queryAsyncByConditionNoParams(BsAsyncDataDTO bsAsyncDataDTO) {
        return bsAsyncDataService.queryAsyncByConditionNoParams(bsAsyncDataDTO);
    }

    @Override
    public List<BPcbLocationDetail> selectBPcbLocationDetailByProductCode(String productCode) {
        return bPcbLocationDetailRepository.selectBPcbLocationDetailByProductCode(productCode);
    }

    @Override
    public List<String> getPointLocPage(BPcbLocationDetailDTO dto) {
        return bPcbLocationDetailRepository.getPointLocPage(dto);
    }

    /**
     * 用户取消操作
     *
     * @param bsAsyncDataDTO
     * @param factoryEmpnoPair
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelCadExpireResult(BsAsyncDataDTO bsAsyncDataDTO, Pair<String, String> factoryEmpnoPair) throws Exception {
        bsAsyncDataDTO.setStatus(MpConstant.AsyncConstant.CANCEL);
        bsAsyncDataDTO.setLastUpdateBy(factoryEmpnoPair.getSecond());
        return bsAsyncDataService.updateDataById(bsAsyncDataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String confirmCadExpireResult(BsAsyncDataDTO bsAsyncDataDTO, Pair<String, String> factoryEmpnoPair) throws Exception {
        List<BsAsyncDataDTO> bsAsyncDataDTOS = bsAsyncDataService.queryAsyncByCondition(new BsAsyncDataDTO() {{
            setHeadId(bsAsyncDataDTO.getHeadId());
        }});
        if (CollectionUtils.isEmpty(bsAsyncDataDTOS)) {
            return Constant.SUCCESS;
        }
        String params = bsAsyncDataDTOS.get(Constant.INT_0).getParams();
        List<BPcbLocationDetail> bPcbLocationDetails = JSON.parseArray(params, BPcbLocationDetail.class);
        if (!CollectionUtils.isEmpty(bPcbLocationDetails)) {
            // 更新 CAD 解析记录
            CommonUtils.splitList(bPcbLocationDetails, Constant.BATCH_SIZE).forEach(item -> {
                bPcbLocationDetailRepository.batchUpdateFromCAD(item);
            });
            // 修改 B_BOM_HEADER 表 IMPORTED_CAD_FLAG，标记CAD已导入
            this.updateBBomHeader(bsAsyncDataDTO.getServiceKey());
        }
        // 更新异步表
        bsAsyncDataDTO.setStatus(MpConstant.AsyncConstant.SUCCESS);
        bsAsyncDataDTO.setLastUpdateBy(factoryEmpnoPair.getSecond());
        bsAsyncDataService.updateDataById(bsAsyncDataDTO);
        try {
            syncXPFromMds(bsAsyncDataDTO, true);
        } catch (MesBusinessException e) {
            Object[] errorParams = e.getParams();
            String errorMsg = errorParams == null || errorParams.length < NumConstant.NUM_ONE ? e.getMessage() : errorParams[NumConstant.NUM_ZERO].toString();
            return errorMsg;
        }
        try {
            List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS = new LinkedList<>();
            PsWorkOrderBasicDTO b1 = new PsWorkOrderBasicDTO();
            b1.setItemNo(bsAsyncDataDTO.getServiceKey());
            b1.setItemName(bsAsyncDataDTO.getServiceKey());
            psWorkOrderBasicDTOS.add(b1);
            bsPremanuItemInfoService.asyncCycleSaveSubLevelPremanuInfo(psWorkOrderBasicDTOS);
        } catch (Exception e) {
            logger.error(JSON.toJSONString(e));
        }
        return Constant.SUCCESS;
    }

    /**
     * MDS触发iMES前加工数据更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLockAnnotation(redisPrefix = "preManuUpdateProgrammingFromMDS", redisLockTime = 3600,
            lockFailMsgZh = Constant.MDS_PROGRAM_XPING, redisLockParam = {@RedisLockParamAnnotation(paramName = "dto", propertiesString = "productCode")})
    public void preManuUpdateProgrammingFromMDS(BPcbLocationDetailDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(dto.getProgrammingList())) {
            return;
        }
        dto.setResultMessage(Constant.STRING_EMPTY);
        // 写片详细信息按照位号去重（入参一次一个料单）
        List<PreManuMDSProgrammingDTO> programmingList = dto.getProgrammingList().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(PreManuMDSProgrammingDTO::getLocation))), ArrayList::new));
        // 入参料单和写片详细信息中料单不一致，则报错
        boolean productCodeNotSameFlag = programmingList.stream().anyMatch(i -> !Objects.equals(i.getPcbMaterial(), dto.getProductCode()));
        if (productCodeNotSameFlag) {
            String errorMessage = dto.getProductCode() + CommonUtils.getLmbMessage(MessageId.PRODUCT_CODE_NOT_SAME_ERROR);
            List<String> locations = programmingList.stream().map(PreManuMDSProgrammingDTO::getLocation).collect(Collectors.toList());
            imesLogService.log(String.join(Constant.LINE, locations) + Constant.LINE + errorMessage, Constant.MDS_PROGRAM_IN + dto.getProductCode());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_NOT_SAME_ERROR);
        }
        // 根据料单长度执行不同场景
        switch (dto.getProductCode().length()) {
            case Constant.INT_12:
                dealProgrammingCodeLengthTwelve(dto, programmingList);
                break;
            case Constant.INT_15:
                dealProgrammingCodeLengthFifteen(dto, programmingList);
                break;
            default:
                String errorMessage = dto.getProductCode() + CommonUtils.getLmbMessage(MessageId.PRODUCT_CODE_LENGTH_ERROR);
                List<String> locations = programmingList.stream().map(PreManuMDSProgrammingDTO::getLocation).collect(Collectors.toList());
                imesLogService.log(String.join(Constant.LINE, locations) + Constant.LINE + errorMessage, Constant.MDS_PROGRAM_IN + dto.getProductCode());
                String emailTitle = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_TITLE_ONE, new String[]{dto.getProductCode()});
                sendFailedMsgEmail(emailTitle,errorMessage,dto.getCreateUser());
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_LENGTH_ERROR);
        }
    }

    /**
     * MDS触发iMES前加工数据更新-12位料单处理方法
     */
    private void dealProgrammingCodeLengthTwelve(BPcbLocationDetailDTO dto, List<PreManuMDSProgrammingDTO> programmingList) throws Exception {
        Map<String, Object> qryCtRouteMap = new HashMap<>();
        qryCtRouteMap.put("itemNo", dto.getProductCode());
        List<CtRouteHeadDTO> ctRouteHeadDTOS = ctRouteHeadRepository.getList(qryCtRouteMap);
        if (CollectionUtils.isEmpty(ctRouteHeadDTOS)) {
            return;
        }
        List<String> fifteenLengthProductCodes = ctRouteHeadDTOS.stream().map(CtRouteHeadDTO::getItemNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 24.7.15新增，比对15位料单pcb和中试传入pcb版本，相同的更新数据
        List<String> pcbVersionFromMDS = programmingList.stream().map(PreManuMDSProgrammingDTO::getPcbVersion).collect(Collectors.toList());
        List<BsItemInfo> items = itemInfoService.getItemNameAndPCBVersion(fifteenLengthProductCodes);
        List<String> currentPcbItemNos = items.stream().filter(i -> pcbVersionFromMDS.contains(i.getPcbVersion())).map(BsItemInfo::getItemNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(currentPcbItemNos)) {
            return;
        }
        if (currentPcbItemNos.size() > Constant.INT_10) {
            String errorMessage = dto.getProductCode() + CommonUtils.getLmbMessage(MessageId.PRODUCT_CODE_NUM_OVER_TEN);
            List<String> locations = programmingList.stream().map(PreManuMDSProgrammingDTO::getLocation).collect(Collectors.toList());
            imesLogService.log(String.join(Constant.LINE, locations) + Constant.LINE + errorMessage, Constant.MDS_PROGRAM_IN + dto.getProductCode());
            String emailTitle = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_TITLE_ONE, new String[]{dto.getProductCode()});
            sendFailedMsgEmail(emailTitle,errorMessage,dto.getCreateUser());
            return;
        }
        // 查询12位料单对应的15位料单的前加工信息
        List<BsPremanuBomInfo> bsPreManuBomInfoList = bsPremanuBomInfoRepository.selectPreBomInfoForMDSProgramming(currentPcbItemNos);
        // 分料单逐条执行方法（只有MDS调用且料单为12位时，会有多个料单）
        for (String productCode : currentPcbItemNos) {
            dealProgrammingForPreManu(dto, dto.getProgrammingList(), productCode, bsPreManuBomInfoList);
            // 存在新增场景，推送MES
            List<PreManuMDSProgrammingDTO> addPreManuList = dto.getProgrammingList().stream().filter(e-> e.isNeedAddPreManu()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(addPreManuList)) {
                bsPremanuBomInfoService.pushPreBomToMes(productCode);
            }
            // 执行BOM分阶
            List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS = new LinkedList<>();
            PsWorkOrderBasicDTO b1 = new PsWorkOrderBasicDTO();
            b1.setItemNo(productCode);
            b1.setItemName(productCode);
            psWorkOrderBasicDTOS.add(b1);
            bsPremanuItemInfoService.asyncCycleSaveSubLevelPremanuInfo(psWorkOrderBasicDTOS);
        }
    }

    /**
     * 自动写片-通用处理写片数据方法
     */
    private void dealProgrammingForPreManu(BPcbLocationDetailDTO dto, List<PreManuMDSProgrammingDTO> programmingList, String productCode, List<BsPremanuBomInfo> bsPreManuBomInfoList) {
        // 按照写片信息分位号逐条执行处理方法
        List<BsPremanuBomInfo> addProgramPreManus = new ArrayList<>();
        for (PreManuMDSProgrammingDTO programmingDTO : programmingList) {
            List<BsPremanuBomInfo> addPreManusTemp = new ArrayList<>();
            if (programmingDTO.getLocation().contains(Constant.COMMA)) {
                // 位号存在逗号，则是多位号，需要分割并额外处理
                List<String> locationSplit = Arrays.asList(programmingDTO.getLocation().split(Constant.COMMA));
                addPreManusTemp = dealProgrammingForPreManuMultiple(dto, locationSplit, productCode, bsPreManuBomInfoList, programmingDTO);
            } else {
                // 不存在逗号，则组装料单，位号，物料数据，执行单位号处理方法
                PreManuMDSProgrammingDTO preManuMDSProgrammingDTO = new PreManuMDSProgrammingDTO();
                preManuMDSProgrammingDTO.setLocation(programmingDTO.getLocation());
                preManuMDSProgrammingDTO.setPcbMaterial(productCode);
                List<String> locations = new ArrayList<>();
                locations.add(programmingDTO.getLocation());
                // 单位号场景校验位号工艺段和物料，方法返回为空时说明已在dto字段中记录异常，进行下一个循环
                List<BPcbLocationDetail> bPcbLocationDetails = getAndCheckLocationDetails(dto, productCode, locations);
                if (CollectionUtils.isEmpty(bPcbLocationDetails)) {
                    continue;
                }
                preManuMDSProgrammingDTO.setMaterialCode(bPcbLocationDetails.get(0).getItemCode());
                addPreManusTemp.add(dealProgrammingForPreManuSingle(dto, bsPreManuBomInfoList, preManuMDSProgrammingDTO));
            }
            addPreManusTemp = addPreManusTemp.stream().filter(Objects::nonNull).collect(Collectors.toList());
            addProgramPreManus.addAll(addPreManusTemp);
            programmingDTO.setNeedAddPreManu(!CollectionUtils.isEmpty(addPreManusTemp));
        }
        addProgramPreManus = addProgramPreManus.stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 批量处理执行结果
        dealResultAndInsertPreManu(dto, addProgramPreManus);
    }

    /**
     * 自动写片-批量处理执行结果
     */
    private void dealResultAndInsertPreManu(BPcbLocationDetailDTO dto, List<BsPremanuBomInfo> addProgramPreManus) {
        List<String> locations = addProgramPreManus.stream().map(BsPremanuBomInfo::getTagNum).collect(Collectors.toList());
        // 处理resultMessage，记录日志
        dealResultMessage(dto, locations);
        // 新增list为空，则位号不需要新增，直接返回
        if (CollectionUtils.isEmpty(addProgramPreManus)) {
            return;
        }
        setNameAndPcbVer(addProgramPreManus);
        for (List<BsPremanuBomInfo> tempList : CommonUtils.splitList(addProgramPreManus, Constant.INT_100)) {
            bsPremanuBomInfoRepository.insertBsPremanuBomInfoBatch(tempList);
        }
    }


    /**
     * 自动写片-记录日志方法
     */
    private void dealResultMessage(BPcbLocationDetailDTO dto, List<String> locations) {
        if (StringUtils.isEmpty(dto.getResultMessage())) {
            return;
        }
        // 如果resultMessage字段不为空，说明执行过程中存在异常，处理异常
        // cadUploader字段没有值，则是MDS触发iMES写片前加工场景，记录sys_record_log
        if (StringUtils.isBlank(dto.getCadUploader())) {
            String loctionMessage = CollectionUtils.isEmpty(locations) ? Constant.STRING_EMPTY : String.join(Constant.COMMA, locations);
            imesLogService.log(loctionMessage + Constant.LINE + dto.getResultMessage(), Constant.MDS_PROGRAM_IN + dto.getProductCode());
            String emailTitle = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_TITLE_ONE, new String[]{dto.getProductCode()});
            sendFailedMsgEmail(emailTitle,dto.getResultMessage(),dto.getCreateUser());
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MDS_IN_PROGRAM_ERROR, new String[]{dto.getResultMessage()});
        } else {
            // cadUploader字段有值，为CAD导入触发场景，记录日志
            String emailTitle = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_TITLE_TWO, new String[]{dto.getProductCode(),dto.getChiDesc(),dto.getVersion()});
            sendFailedMsgEmail(emailTitle,dto.getResultMessage(),dto.getCreateUser());
            insertSyncLog(dto,dto.getResultMessage(),Constant.FAILED);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.XP_AUTO_SYNC_FAILED_MSG, new Object[]{dto.getResultMessage()});
        }
    }

    /**
     * 自动写片-组装物料名称和pcb版本信息
     */
    private void setNameAndPcbVer(List<BsPremanuBomInfo> addProgramPreManus) {
        // 通过料单代码和物料代码查询bs_item_info数据
        List<String> itemNos = addProgramPreManus.stream().map(BsPremanuBomInfo::getBomCode).distinct().collect(Collectors.toList());
        itemNos.addAll(addProgramPreManus.stream().map(BsPremanuBomInfo::getItemNo).distinct().collect(Collectors.toList()));
        List<BsItemInfo> bsItemInfoList = bsItemInfoRepository.getItemNameAndPCBVersion(itemNos);
        Map<String, BsItemInfo> bsItemInfoMap = bsItemInfoList.stream().collect(Collectors.toMap(BsItemInfo::getItemNo, v -> v, (v1, v2) -> v1));
        for (BsPremanuBomInfo dto : addProgramPreManus) {
            BsItemInfo productDTO = bsItemInfoMap.get(dto.getBomCode());
            if (productDTO != null) {
                dto.setBomName(productDTO.getItemName());
                dto.setVerNo(productDTO.getPcbVersion());
            }
            BsItemInfo itemDTO = bsItemInfoMap.get(dto.getItemNo());
            if (itemDTO != null) {
                dto.setItemName(itemDTO.getItemName());
            }
        }
    }

    /**
     * 自动写片-处理多位号场景
     */
    private List<BsPremanuBomInfo> dealProgrammingForPreManuMultiple(BPcbLocationDetailDTO dto, List<String> locationSplit, String productCode, List<BsPremanuBomInfo> bsPreManuBomInfoList, PreManuMDSProgrammingDTO programmingDTO) {
        List<BsPremanuBomInfo> addProgramPreManus = new ArrayList<>();
        // 根据料单代码和位号查询位号信息，并进行校验
        List<BPcbLocationDetail> bPcbLocationDetails = getAndCheckLocationDetails(dto, productCode, locationSplit);
        // 位号按照主工序分组
        Map<String, List<BPcbLocationDetail>> bPcbLocationGroupByCraftSection = bPcbLocationDetails.stream().collect(Collectors.groupingBy(BPcbLocationDetail::getCraftSection));
        for (Map.Entry<String, List<BPcbLocationDetail>> entry : bPcbLocationGroupByCraftSection.entrySet()) {
            List<String> locations = entry.getValue().stream().map(BPcbLocationDetail::getPointLoc).sorted(String::compareTo).collect(Collectors.toList());
            // 比较同工艺段下位号对应物料是否一致，不一致报错
            boolean itemCodeNotSameFlag = entry.getValue().stream().map(BPcbLocationDetail::getItemCode).distinct().count() != Constant.INT_1;
            if (itemCodeNotSameFlag) {
                String errorMessage = String.format(Constant.LOCATION_SAME_CRAFT_ITME_CODE_NOT_SAME, dto.getProductCode(), String.join(Constant.COMMA, locations));
                dto.setResultMessage(dto.getResultMessage() + Constant.LINE + errorMessage);
                return new ArrayList<>();
            }
            // 位号排序按照iMES前加工存储方法排序（便于后续全匹配比较和保存）
            String location = String.join(Constant.COMMA, locations);
            PreManuMDSProgrammingDTO preManuMDSProgrammingDTO = new PreManuMDSProgrammingDTO();
            preManuMDSProgrammingDTO.setMaterialCode(entry.getValue().get(0).getItemCode());
            preManuMDSProgrammingDTO.setLocation(location);
            preManuMDSProgrammingDTO.setPcbMaterial(productCode);
            addProgramPreManus.add(dealProgrammingForPreManuSingle(dto, bsPreManuBomInfoList, preManuMDSProgrammingDTO));
        }
        return addProgramPreManus;
    }

    /**
     * 自动写片-校验位号数据
     */
    private List<BPcbLocationDetail> getAndCheckLocationDetails(BPcbLocationDetailDTO dto, String productCode, List<String> locationSplit) {
        List<BPcbLocationDetail> bPcbLocationDetails = bPcbLocationDetailRepository.selectBPcbLocationDetailForMDSProgramming(productCode, locationSplit);
        List<String> notInLocation = new ArrayList<>();
        for (String location : locationSplit) {
            boolean hasLocation = bPcbLocationDetails.stream().anyMatch(i -> location.equals(i.getPointLoc()));
            if (!hasLocation) {
                notInLocation.add(location);
            }
        }
        // 存在位号没有对应物料代码，记录异常并返回空表
        if (!CollectionUtils.isEmpty(notInLocation)) {
            // 入参料单为12时，说明是MDS调用场景（CAD导入接口只会传15位料单）可能存在12位对应的15位料单本身没有这个位号的场景，直接返回不处理这个位号
            if (dto.getProductCode().length() == Constant.INT_12) {
                return new ArrayList<>();
            }
            String errorMessage = String.format(Constant.LOCATION_NO_ITEM_CODE, dto.getProductCode(), String.join(Constant.COMMA, notInLocation));
            dto.setResultMessage(dto.getResultMessage() + Constant.LINE + errorMessage);
            return new ArrayList<>();
        }
        List<BPcbLocationDetail> duplicates = bPcbLocationDetails.stream().filter(i -> StringUtils.isNotBlank(i.getItemCode()))
                .collect(Collectors.groupingBy(BPcbLocationDetail::getPointLoc))
                .entrySet().stream()
                .filter(e -> e.getValue().size() > 1)
                .flatMap(e -> e.getValue().stream())
                .collect(Collectors.toList());
        // 位号对应物料存在多个，记录异常并返回空表
        if (!CollectionUtils.isEmpty(duplicates)) {
            List<String> duplicateLocation = duplicates.stream().map(BPcbLocationDetail::getPointLoc).distinct().collect(Collectors.toList());
            String errorMessage = String.format(Constant.LOCATION_DUPLICATE_ITEM_CODE, dto.getProductCode(), String.join(Constant.COMMA, duplicateLocation));
            dto.setResultMessage(dto.getResultMessage() + Constant.LINE + errorMessage);
            return new ArrayList<>();
        }
        // 位号对应工艺段为空或者不是SMT段，记录异常并返回空表
        List<String> notSMTLocations = bPcbLocationDetails.stream().filter(i -> StringUtils.isBlank(i.getCraftSection())
                || !i.getCraftSection().contains(Constant.SMT_STR)).map(BPcbLocationDetail::getPointLoc).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notSMTLocations)) {
            String errorMessage = String.format(Constant.LOCATION_CRAFT_SECTION_NOT_SMT, dto.getProductCode(), String.join(Constant.COMMA, notSMTLocations));
            dto.setResultMessage(dto.getResultMessage() + Constant.LINE + errorMessage);
            return new ArrayList<>();
        }
        return bPcbLocationDetails;
    }

    /**
     * 自动写片-单位号处理场景
     */
    private BsPremanuBomInfo dealProgrammingForPreManuSingle(BPcbLocationDetailDTO dto, List<BsPremanuBomInfo> bsPreManuBomInfoList, PreManuMDSProgrammingDTO programmingDTO) {
        // 对应位号存在写片记录（全匹配）， 则跳过
        boolean locationExistFlag = bsPreManuBomInfoList.stream().anyMatch(i -> Objects.equals(programmingDTO.getLocation(), i.getTagNum()) && Constant.XP_CODE.equals(i.getTypeCode()) && Objects.equals(Constant.SMT_DELIVERY, i.getDeliveryProcess()) && Objects.equals(i.getBomCode(), programmingDTO.getPcbMaterial()));
        if (locationExistFlag) {
            return null;
        }
        // 对应位号在iMES系统存在部分匹配写片前加工记录的数据，报错
        for (String location : programmingDTO.getLocation().split(Constant.COMMA)) {
            boolean locationRepeatFlag = bsPreManuBomInfoList.stream().anyMatch(i -> Arrays.asList(i.getTagNum().split(COMMA)).contains(location) && Objects.equals(Constant.SMT_DELIVERY, i.getDeliveryProcess()) && Constant.XP_CODE.equals(i.getTypeCode()) && Objects.equals(i.getBomCode(), programmingDTO.getPcbMaterial()));
            if (locationRepeatFlag) {
                String errorMessage = String.format(Constant.LOCATION_REPEAT, dto.getProductCode(), programmingDTO.getLocation());
                dto.setResultMessage(dto.getResultMessage() + Constant.LINE + errorMessage);
                return null;
            }
        }
        programmingDTO.setNeedAddPreManu(true);
        // 组装新增前加工数据
        BsPremanuBomInfo newProgramPreManu = new BsPremanuBomInfo();
        newProgramPreManu.setRecordId(java.util.UUID.randomUUID().toString());
        newProgramPreManu.setBomCode(programmingDTO.getPcbMaterial());
        newProgramPreManu.setTagNum(programmingDTO.getLocation());
        newProgramPreManu.setIsPreManu(Constant.FLAG_Y);
        newProgramPreManu.setItemNo(programmingDTO.getMaterialCode());
        newProgramPreManu.setDeliveryProcess(Constant.SMT_DELIVERY);
        newProgramPreManu.setTraceCode(Constant.XP1_CODE);
        newProgramPreManu.setTraceName(Constant.XP1_NAME);
        newProgramPreManu.setTypeCode(Constant.XP_CODE);
        newProgramPreManu.setTypeName(Constant.XP_NAME);
        newProgramPreManu.setCreateBy(dto.getCreateUser());
        newProgramPreManu.setLastUpdatedBy(dto.getCreateUser());
        // 同料单下存在物料相同，位号相同，但不是写片的前加工信息时，维护同代码多前加工排序为最大maxSort + 1
        boolean preManuSeqFlag = bsPreManuBomInfoList.stream().anyMatch(i -> Objects.equals(programmingDTO.getLocation(), i.getTagNum())
                && !Constant.XP_CODE.equals(i.getTypeCode()) && Objects.equals(i.getBomCode(), programmingDTO.getPcbMaterial()));
        if (preManuSeqFlag) {
            BigDecimal maxSort = bsPreManuBomInfoList.stream().map(BsPremanuBomInfo::getSortSeq).filter(Objects::nonNull).max(Comparator.comparing(i -> i)).orElse(new BigDecimal(0));
            newProgramPreManu.setSortSeq(maxSort.add(new BigDecimal(1)));
        } else {
            newProgramPreManu.setSortSeq(new BigDecimal(Constant.INT_1));
        }
        return newProgramPreManu;
    }

    /**
     * 自动写片-15位料单处理方法
     */
    private void dealProgrammingCodeLengthFifteen(BPcbLocationDetailDTO dto, List<PreManuMDSProgrammingDTO> programmingList) throws Exception {
        List<String> productCodes = new ArrayList<>();
        productCodes.add(dto.getProductCode());
        // 查询料单对应所有前加工信息
        List<BsPremanuBomInfo> bsPreManuBomInfoList = bsPremanuBomInfoRepository.selectPreBomInfoForMDSProgramming(productCodes);
        // 按照写片信息逐条处理数据
        dealProgrammingForPreManu(dto, programmingList, dto.getProductCode(), bsPreManuBomInfoList);
        // 存在新增场景，推送MES
        List<PreManuMDSProgrammingDTO> addPreManuList = programmingList.stream().filter(e-> e.isNeedAddPreManu()).collect(Collectors.toList());
        String succeccMsg = Constant.SYNC_SUCCESS;
        if(!CollectionUtils.isEmpty(addPreManuList)) {
            bsPremanuBomInfoService.pushPreBomToMes(dto.getProductCode());
            String addLocation = addPreManuList.stream().map(PreManuMDSProgrammingDTO::getLocation).distinct().collect(Collectors.joining(COMMA));
            succeccMsg = Constant.SYNC_SUCCESS_WITH_NEW_PRE_INFO + addLocation;
        }
        if (StringUtils.isEmpty(dto.getCadUploader())) {
            // 执行BOM分阶
            List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS = new LinkedList<>();
            PsWorkOrderBasicDTO b1 = new PsWorkOrderBasicDTO();
            b1.setItemNo(dto.getProductCode());
            b1.setItemName(dto.getProductCode());
            psWorkOrderBasicDTOS.add(b1);
            bsPremanuItemInfoService.asyncCycleSaveSubLevelPremanuInfo(psWorkOrderBasicDTOS);
        } else {
            insertSyncLog(dto,succeccMsg,Constant.STR_SUCCESS);
        }
    }

    /**
     *导入CAD后同步MDS写片前加工数据
     */
    public void preManuMDSProgrammingSync(BsAsyncDataDTO bsAsyncDataDTO) throws Exception {
        // 根据15位料单查询MDS写片信息
        PreManuMDSProgrammingDTO preManuByFullBomCode = new PreManuMDSProgrammingDTO();
        preManuByFullBomCode.setPcbMaterial(bsAsyncDataDTO.getServiceKey());
        // 根据数据字典配置获取版本类型列表
        List<SysLookupValues> sysList = sysLookupValuesService.selectValuesByType(Constant.LOOK_UP_TYPE_652305);
        if (CollectionUtils.isEmpty(sysList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_TYPE_652305});
        }
        List<String> versionList = sysList.stream().filter(item -> StringUtils.isNotBlank(item.getLookupMeaning()))
                .map(SysLookupValues::getLookupMeaning).distinct()
                .collect(Collectors.toList());
        preManuByFullBomCode.setVersionList(versionList);
        // 获取料单代码对应的PCB版本
        Map<String, Object> record = new HashMap<>();
        record.put("productCode", bsAsyncDataDTO.getServiceKey());
        List<BBomHeader> bBomHeaderList = bBomHeaderRepository.getList(record);
        if (CollectionUtils.isEmpty(bBomHeaderList)) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST);
        }
        String verNo = bBomHeaderList.get(NumConstant.NUM_ZERO).getVerNo();
        String pcbName = bBomHeaderList.get(NumConstant.NUM_ZERO).getChiDesc();
        preManuByFullBomCode.setPcbVersion(verNo);
        preManuByFullBomCode.setFiringType(Constant.FIRING_TYPE);

        BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
        dto.setCadUploader(bsAsyncDataDTO.getLastUpdateBy());
        dto.setCreateUser(bsAsyncDataDTO.getLastUpdateBy());
        dto.setProductCode(bsAsyncDataDTO.getServiceKey());
        dto.setChiDesc(pcbName);
        dto.setVersion(verNo);
        dto.setResultMessage(Constant.STRING_EMPTY);
        List<PreManuMDSProgrammingDTO> fullBomCodePreManuList = mdsRemoteService.getPreManuMDSProgrammingList(preManuByFullBomCode);
        // 1 有数据
        if (!CollectionUtils.isEmpty(fullBomCodePreManuList)) {
            // 1.1 逐条解析
            dealProgrammingCodeLengthFifteen(dto,fullBomCodePreManuList);
        } else {
            // 2.15位料单MDS无数据,根据12位料单加PCB版本查询MDS写片信息
            PreManuMDSProgrammingDTO preManuByPartBomCode = new PreManuMDSProgrammingDTO();
            preManuByPartBomCode.setPcbMaterial(bsAsyncDataDTO.getServiceKey().substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE));
            preManuByPartBomCode.setPcbVersion(verNo);
            preManuByPartBomCode.setVersionList(versionList);
            preManuByPartBomCode.setFiringType(Constant.FIRING_TYPE);
            List<PreManuMDSProgrammingDTO> partBomCodePreManuList = mdsRemoteService.getPreManuMDSProgrammingList(preManuByPartBomCode);
            if (CollectionUtils.isEmpty(partBomCodePreManuList)) {
                // 2.2 MDS没有12位料单写片前加工数据，进一步查询12位料单在IMES是否存在写片前加工信息
                hasNoPreInfoByTwelve(bsAsyncDataDTO, dto, pcbName, verNo);
            }else {
                // 2.1 MDS12位料单有数据
                hasPreInfoByTwelve(bsAsyncDataDTO, partBomCodePreManuList, dto);
            }
        }
    }

    private void hasNoPreInfoByTwelve(BsAsyncDataDTO bsAsyncDataDTO, BPcbLocationDetailDTO dto, String pcbName, String verNo) {
        BsPremanuBomInfoDTO bsPremanuBom = new BsPremanuBomInfoDTO();
        String likeBomCode = bsAsyncDataDTO.getServiceKey().substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE);
        bsPremanuBom.setLikeBomCode(likeBomCode);
        bsPremanuBom.setTypeCode(Constant.XP_CODE);
        bsPremanuBom.setStartRow(0L);
        bsPremanuBom.setEndRow(1L);
        List<BsPremanuBomInfo> bsPremanuBomInfoList = bsPremanuBomInfoRepository.getBsPremanuBomInfoList(bsPremanuBom);
        if (CollectionUtils.isEmpty(bsPremanuBomInfoList)) {
            // 2.2.1 IMES没有12位料单（模糊匹配）的写片前加工信息，认为不需写片或未启用写片，写日志
            insertSyncLog(dto,Constant.DO_NOT_NEED_XP,Constant.STR_SUCCESS);
        } else {
            // 2.2.1 IMES有15位料单前加工信息，报错，推送邮件，记录失败日志
            String emailTitle = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_TITLE_THREE, new String[]{bsAsyncDataDTO.getServiceKey(), pcbName, verNo});
            String errorMsg = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_MSG_TWO, new String[]{bsAsyncDataDTO.getServiceKey()});
            sendFailedMsgEmail(emailTitle, errorMsg, bsAsyncDataDTO.getLastUpdateBy());
            insertSyncLog(dto,errorMsg,Constant.FAILED);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.XP_AUTO_SYNC_FAILED_MSG, new Object[]{errorMsg});
        }
    }

    private void hasPreInfoByTwelve(BsAsyncDataDTO bsAsyncDataDTO, List<PreManuMDSProgrammingDTO> partBomCodePreManuList, BPcbLocationDetailDTO dto) throws Exception {
        // 2.1 MDS12位料单有数据，对返回的位号去重，再判断下是否存在于15位料单的BOM清单中
        Map<String,PreManuMDSProgrammingDTO> locationMap = partBomCodePreManuList.stream().collect(Collectors.toMap(PreManuMDSProgrammingDTO::getLocation, a -> a, (k1, k2) -> k1));
        // 查询料单的BOM位号清单
        List<BPcbLocationDetail> bPcbLocationDetails = bPcbLocationDetailRepository.selectBPcbLocationDetailByProductCode(bsAsyncDataDTO.getServiceKey());
        List<PreManuMDSProgrammingDTO> hasLocList = new ArrayList<>();
        List<String> bomLocationList = bPcbLocationDetails.stream().filter(e-> StringUtils.isNotEmpty(e.getPointLoc()))
                .map(BPcbLocationDetail::getPointLoc).distinct().collect(Collectors.toList());
        for (Map.Entry<String, PreManuMDSProgrammingDTO> mdsProgrammingDTOEntry : locationMap.entrySet()) {
            List<String> locations = Arrays.asList(mdsProgrammingDTOEntry.getKey().split(COMMA));
            boolean allInBom = true;
            for (String location : locations) {
                if(!bomLocationList.contains(location)) {
                    allInBom = false;
                    break;
                }
            }
            if (allInBom) {
                hasLocList.add(mdsProgrammingDTOEntry.getValue());
            }
        }
        if (CollectionUtils.isEmpty(hasLocList)) {
            // 记录日志，MDS该15位料单下没有写片信息
            String errorMsg = CommonUtils.getLmbMessage(MessageId.XP_AUTO_SYNC_FAILED_MSG_THREE, new String[]{bsAsyncDataDTO.getServiceKey()});
            insertSyncLog(dto,errorMsg,Constant.STR_SUCCESS);
        } else {
            // 逐条解析
            dealProgrammingCodeLengthFifteen(dto,hasLocList);
        }
    }

    private void insertSyncLog(BPcbLocationDetailDTO dto ,String errorMsg, String syncStatus) {
        CadUploadRecord cadUploadRecord = new CadUploadRecord();
        cadUploadRecord.setRecordId(UUID.randomUUID().toString());
        cadUploadRecord.setCreateBy(dto.getCreateUser());
        cadUploadRecord.setLastUpdatedBy(dto.getCreateUser());
        // 3表示MDS写片信息同步
        cadUploadRecord.setDataSource(Constant.STR_THREE);
        cadUploadRecord.setDataSourceRemark(Constant.CAD_UPLOAD_REMARK_THREE);
        cadUploadRecord.setProductCode(dto.getProductCode());
        cadUploadRecord.setProductCodePre12(dto.getProductCode().substring(NumConstant.NUM_ZERO, NumConstant.NUM_TWELVE));
        cadUploadRecord.setChiDesc(dto.getChiDesc());
        cadUploadRecord.setVerNo(dto.getVersion());
        cadUploadRecord.setUploadStatus(syncStatus);
        cadUploadRecord.setUploadResultMsg(errorMsg);
        cadUploadRecordRepository.insertSelective(cadUploadRecord);
    }

    /**
     * 推送邮件
     * @param emailTitle
     * @param errorMsg
     * @param empNo
     * @throws Exception
     */
    public void sendFailedMsgEmail(String emailTitle, String errorMsg, String empNo) {
        StringBuilder contentCnForSendEmail = new StringBuilder("<br/><div>");
        contentCnForSendEmail.append("<p style='margin:0;font-size:12pt'>" + emailTitle + "</p>");
        contentCnForSendEmail.append("<p style='margin:0;font-size:10pt'>" + errorMsg + "</p>");
        contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + STRING_EMPTY + "</p>");
        //需要处理的人发送邮件
        StringBuilder currentHandlerSb = new StringBuilder();
        currentHandlerSb.append(empNo).append(Constant.MAILBOX_SUFFIX).append(Constant.COLON);
        List<SysLookupValues> sysList = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_TYPE_652308);
        if (!CollectionUtils.isEmpty(sysList)) {
            String emailHolder = sysList.stream().map(SysLookupValues::getLookupMeaning).distinct().collect(Collectors.joining(COMMA));
            if (StringUtils.isNotEmpty(emailHolder)) {
                String[] currentHandlerArr = emailHolder.split(Constant.COMMA);
                for (String currentHandlerStr : currentHandlerArr) {
                    currentHandlerSb.append(currentHandlerStr).append(Constant.MAILBOX_SUFFIX).append(Constant.COLON);
                }
            }
        }
        emailUtils.sendMail(currentHandlerSb.toString(), emailTitle, "", contentCnForSendEmail.toString(), "");
    }


}
