package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.interfaces.dto.ApsCustInstructBomDTO;
import com.zte.interfaces.dto.ApsCustomerTaskDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.FinishedProductStorageItemDTO;
import com.zte.interfaces.dto.ProcessStatusConditionDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import com.zte.interfaces.dto.aps.TasksQueryDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.itp.msa.core.exception.GlobalDefaultBaseExceptionHandler.getTrace;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/13 15:53
 */
@Slf4j
@Service
public class FinishedProductStorageDataHandleServiceImpl extends BasePushStdModelSnDataHandleService<FinishedProductStorageDTO> {

    private static final String MATERIAL_BILL_TYPE_REWORK_2 = "NEW_INSTRUCT_BOM";
    @Resource
    ApsInOneClient apsInOneClient;
    @Value("${need.customer.component.type.list:server.configmodel}")
    private List<String> needCustomerComponentTypeList;

    @Override
    public boolean match(String currProcess) {
        return finishedProductStorageCurrProcess.equals(currProcess);
    }
    
    private boolean isTaskNoHasNotCallbackSnData(String taskNo) {
        ProcessStatusConditionDTO processStatusCondition = new ProcessStatusConditionDTO();

        processStatusCondition.setCurrProcess(finishedProductStorageCurrProcess);
        processStatusCondition.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);

        PushStdModelSnDataQueryDTO query = new PushStdModelSnDataQueryDTO();

        query.setProcessStatusConditions(Lists.newArrayList(processStatusCondition));
        query.setTaskNos(Lists.newArrayList(taskNo));
        List<PushStdModelSnDataDTO> unCallbackList = pushStdModelSnDataRepository.selectByQuery(query);
        return CollectionUtils.isNotEmpty(unCallbackList);
    }

    private void handlePushStdModelSnDatas(
            String taskNo, List<PushStdModelSnDataExtDTO> extDtos, Map<String, PushStdModelSnDataHandleDTO> handleMap,
            List<SysLookupValues> stockConfigs, List<FixBomDetailDTO> fixBomDetails) {
        List<PushStdModelSnDataExtDTO> effectList = Lists.newArrayList();
        List<PushStdModelSnDataExtDTO> defectList = Lists.newArrayList();
        // 按良品不良品进行分组
        for (PushStdModelSnDataExtDTO ext : extDtos) {
            PushStdModelSnDataHandleDTO handle = handleMap.get(ext.getId());
            if (handle != null) {
                ext.setStockName(handle.getStockName());
            }
            if (this.isDefectiveProductWarehouse(ext.getStockName(), stockConfigs)) {
                defectList.add(ext);
            } else {
                effectList.add(ext);
            }
        }
        // 上报良品
        if (this.handlePushStdModelSnDatas(taskNo, effectList, handleMap, fixBomDetails, false)) {
            // 良品上报成功， 跳过不良品上报
            return;
        }
        // 上报不良品
        this.handlePushStdModelSnDatas(taskNo, defectList, handleMap, fixBomDetails, true);
    }

    private boolean handlePushStdModelSnDatas(String taskNo, List<PushStdModelSnDataExtDTO> extDtos, Map<String, PushStdModelSnDataHandleDTO> handleMap,
                      List<FixBomDetailDTO> fixBomDetails, boolean isDefectiveProductWarehouse) {
        // 没有数据， 认为上报失败
        if (CollectionUtils.isEmpty(extDtos)) {
            return false;
        }
        // 存在未回调的数据， 认为上报成功
        if (this.isTaskNoHasNotCallbackSnData(taskNo)) {
            return true;
        }
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = extDtos.get(0);
        Map<String, PushStdModelSnDataDTO> updatePushStdModelSnDataMap = Maps.newHashMap();
        Map<String, FinishedProductStorageDTO> uploadFinishedProductStorageMap = Maps.newHashMap();
        extDtos.forEach(extDto -> {
            String extDtoId = extDto.getId();
            PushStdModelSnDataHandleDTO handle = handleMap.get(extDtoId);
            if (handle == null) {
                return;
            }
            String currProcess = handle.getCurrProcess();
            List<WipExtendIdentificationDTO> wipExtendIdentifications = handle.getWipExtendIdentifications();
            PushStdModelSnDataDTO pushStdModelSnData = new PushStdModelSnDataDTO();

            pushStdModelSnData.setId(extDtoId);
            pushStdModelSnData.setCurrProcess(currProcess);
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);
            pushStdModelSnData.setPushFailCount(extDto.getPushFailCount());
            pushStdModelSnData.setPushDate(new Date());
            try {
                FinishedProductStorageDTO finishedProductStorage =
                        wrapPushMessageData(extDto, wipExtendIdentifications, fixBomDetails, isDefectiveProductWarehouse);
                // 校验数据是否正常
                if (!validatePushMessageData(currProcess, finishedProductStorage)) {
                    // 设置校验失败
                    pushStdModelSnData.setErrorMsg("标模任务SN推送数据校验异常");
                    this.wrapFailCountAndPushStatus(pushStdModelSnData);
                } else {
                    pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);
                    uploadFinishedProductStorageMap.put(extDtoId, finishedProductStorage);
                }
            } catch (Exception e) {
                log.error("组装标模任务SN推送未知异常", e);
                pushStdModelSnData.setErrorMsg("组装标模任务SN推送未知异常:" + StringUtils.substring(getTrace(e), 0, NumConstant.NUM_1500));
                this.wrapFailCountAndPushStatus(pushStdModelSnData);
            }
            updatePushStdModelSnDataMap.put(extDtoId, pushStdModelSnData);
        });
        // 封装并推送消息
        boolean result = this.wrapAndPushMessage(pushStdModelSnDataExt, uploadFinishedProductStorageMap, updatePushStdModelSnDataMap);
        List<PushStdModelSnDataDTO> updatePushStdModelSnDataList = Lists.newArrayList(updatePushStdModelSnDataMap.values());
        if (CollectionUtils.isNotEmpty(updatePushStdModelSnDataList)) {
            pushStdModelSnDataRepository.batchUpdate(updatePushStdModelSnDataList);
        }
        return result;
    }

    private boolean wrapAndPushMessage(PushStdModelSnDataExtDTO pushStdModelSnDataExt,
                      Map<String, FinishedProductStorageDTO> uploadFinishedProductStorageMap,
                      Map<String, PushStdModelSnDataDTO> updatePushStdModelSnDataMap) {
        List<FinishedProductStorageDTO> uploadFinishedProductStorages = Lists.newArrayList(uploadFinishedProductStorageMap.values());
        if (CollectionUtils.isEmpty(uploadFinishedProductStorages)) {
            return false;
        }
        List<FinishedProductStorageItemDTO> materialBillList = Lists.newArrayList();
        uploadFinishedProductStorages.forEach(item -> materialBillList.addAll(item.getMaterialBillList()));

        FinishedProductStorageDTO finishedProductStorage = uploadFinishedProductStorages.get(0);
        finishedProductStorage.setQuantityCompleted(materialBillList.size());
        finishedProductStorage.setMaterialBillList(materialBillList);

        // 消息推送
        try {
            return this.doPushMessage(pushStdModelSnDataExt, finishedProductStorage);
        } catch (Exception e) {
            log.error("标模任务SN推送未知异常", e);
            uploadFinishedProductStorageMap.keySet().forEach(extDtoId -> {
                PushStdModelSnDataDTO pushStdModelSnData = updatePushStdModelSnDataMap.get(extDtoId);
                if (pushStdModelSnData == null) {
                    return;
                }
                pushStdModelSnData.setErrorMsg("标模任务SN推送未知异常:" + StringUtils.substring(getTrace(e), 0, NumConstant.NUM_1500));
                this.wrapFailCountAndPushStatus(pushStdModelSnData);
            });
            return false;
        }
    }

    @Override
    @RedisDistributedLockAnnotation(redisKey = Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE, redisLockTime = 10 * 60)
    public boolean handlePushStdModelSnDatas(List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles) {
        if (CollectionUtils.isEmpty(pushStdModelSnDataHandles)) {
            return false;
        }
        Map<String, PushStdModelSnDataHandleDTO> pushStdModelSnDataHandleMap =
                pushStdModelSnDataHandles.stream().collect(Collectors.toMap(PushStdModelSnDataHandleDTO::getId, v -> v, (k1, k2) -> k1));
        List<String> inputIds = Lists.newArrayList(pushStdModelSnDataHandleMap.keySet());
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = pushStdModelSnDataRepository.selectExtByPrimaryKeys(inputIds).stream()
                .filter(item -> Constant.PUSH_STATUS.NOT_PUSHED == item.getPushStatus())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pushStdModelSnDataList)) {
            return false;
        }
        // 获取库位配置相关信息
        List<SysLookupValues> stockConfigs = sysLookupValuesRepository.selectValuesByType(Integer.valueOf(Constant.LOOKUP_TYPE_7502));
        Map<String, List<PushStdModelSnDataExtDTO>> taskNoGroupMap = pushStdModelSnDataList.stream()
                .collect(Collectors.groupingBy(PushStdModelSnDataExtDTO::getTaskNo));
        // 按任务进行分组
        for (Map.Entry<String, List<PushStdModelSnDataExtDTO>> entry : taskNoGroupMap.entrySet()) {
            List<PushStdModelSnDataExtDTO> extDtos = entry.getValue();
            PushStdModelSnDataExtDTO firstExtDto = extDtos.get(0);
            // 根据fixBomHeadId获取MBOM详情
            List<FixBomDetailDTO> fixBomDetails = fixBomCommonService.queryTreeNodeByHeadId(firstExtDto.getFixBomHeadId());
            this.handlePushStdModelSnDatas(entry.getKey(), extDtos, pushStdModelSnDataHandleMap, stockConfigs, fixBomDetails);
        }
        return true;
    }

    @Override
    protected FinishedProductStorageDTO wrapPushMessageData(PushStdModelSnDataExtDTO pushStdModelSnDataExt,
                                                            List<WipExtendIdentificationDTO> wipExtendIdentifications,
                                                            List<FixBomDetailDTO> fixBomDetails) {
        // 库位信息缺失 默认为不良品
        boolean isDefectiveProductWarehouse = true;
        // 如果有库位信息传入
        String subStock = pushStdModelSnDataExt.getStockName();
        if (StringUtils.isNotBlank(subStock)) {
            // 获取库位相关信息
            List<SysLookupValues> stockConfigs = sysLookupValuesRepository.selectValuesByType(Integer.valueOf(Constant.LOOKUP_TYPE_7502));
            isDefectiveProductWarehouse = this.isDefectiveProductWarehouse(subStock, stockConfigs);
        }

        return this.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails, isDefectiveProductWarehouse);
    }

    private FinishedProductStorageDTO wrapPushMessageData(
            PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications,
            List<FixBomDetailDTO> fixBomDetails, boolean isDefectiveProductWarehouse) {
        // 组装数据
        FinishedProductStorageItemDTO topSnItem = this.wrapFinishedProductStorageItem(pushStdModelSnDataExt, fixBomDetails,wipExtendIdentifications);
        FinishedProductStorageDTO finishedProductStorage = new FinishedProductStorageDTO();

        finishedProductStorage.setSeqNo(idGenerator.snowFlakeIdStr());
        finishedProductStorage.setManufactureOrderNo(pushStdModelSnDataExt.getTaskNo());
        finishedProductStorage.setCategory(pushStdModelSnDataExt.getTaskCustomerPartType());
        finishedProductStorage.setQuantityCompleted(NumConstant.NUM_ONE);
        finishedProductStorage.setMaterialBillList(Lists.newArrayList(topSnItem));
        finishedProductStorage.setMaterialQuality(isDefectiveProductWarehouse ? NumConstant.NUM_TWO : NumConstant.NUM_ONE);

        return finishedProductStorage;
    }

    private boolean isDefectiveProductWarehouse(String subStock, List<SysLookupValues> stockConfigs) {
        if (StringUtils.isBlank(subStock)) {
            return true;
        }
        String[] split = stockConfigs.stream()
                .filter(item -> Constant.LOOKUP_CODE_BAD_WAREHOUSE.equals(item.getLookupCode().toString()))
                .map(SysLookupValues::getAttribute2)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(",")).split(",");
        List<String> list = Arrays.asList(split);

        return list.contains(subStock);
    }

    private FinishedProductStorageItemDTO wrapFinishedProductStorageItem(PushStdModelSnDataExtDTO pushStdModelSnDataExt,
                                                                         List<FixBomDetailDTO> fixBomDetails,
                                                                         List<WipExtendIdentificationDTO> wipExtendIdentifications) {
        // 任务维度获取APS数据
        Map<String, ApsCustInstructBomDTO> customerMaterialNameApsCustInstructBomMap = Maps.newHashMap();
        ApsCustInstructBomDTO topApsCustInstructBom = this.getTopApsCustInstructBomMap(pushStdModelSnDataExt);
        this.wrapCustomerMaterialNameApsCustInstructBomMap(customerMaterialNameApsCustInstructBomMap, topApsCustInstructBom);
        //设置moc条码
        FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = pushStdModelSnDataService.getFinishedProductStorageCargoTransportDTO(wipExtendIdentifications,fixBomDetails,false, pushStdModelSnDataExt.getCloudType());
        // 组装数据
        FinishedProductStorageItemDTO topSnItem = new FinishedProductStorageItemDTO();
        topSnItem.setOriginalBarcode(finishedProductStorageCargoTransportDTO.getOriginalBarcode());
        topSnItem.setSnNo(finishedProductStorageCargoTransportDTO.getBarcode());
        // APS指令BOM接口中获取materialsign
        topSnItem.setMaterialSign(topApsCustInstructBom == null ? null : topApsCustInstructBom.getMaterialSign());
        // fixbom数据接口-客户物料名称 如果是第一层整机时需要获取mes_centerfactory.ps_task_extended附加属性表的三段机型 customer_item_name
        topSnItem.setMaterialName(pushStdModelSnDataExt.getTaskCustomerItemName());
        // 第一层设置为fixBomId
        topSnItem.setMaterialBom(pushStdModelSnDataExt.getTaskFixBomId());
        topSnItem.setMaterialBillList(Lists.newArrayList());
        topSnItem.setMaterialCategory(finishedProductStorageCargoTransportDTO.getCustomerComponentType());
        List<FinishedProductStorageItemDTO> materialBillList = topSnItem.getMaterialBillList();
        this.setMaterialBillList(finishedProductStorageCargoTransportDTO, materialBillList);
        return topSnItem;
    }

    private static void setMaterialBillList(FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO, List<FinishedProductStorageItemDTO> materialBillList) {
        if(CollectionUtils.isNotEmpty(finishedProductStorageCargoTransportDTO.getMaterialBillList())){
            for (FinishedProductStorageCargoTransportDTO productStorageCargoTransportDTO : finishedProductStorageCargoTransportDTO.getMaterialBillList()) {
                FinishedProductStorageItemDTO snItem = new FinishedProductStorageItemDTO();
                snItem.setSnNo(productStorageCargoTransportDTO.getBarcode());
                snItem.setMaterialCategory(productStorageCargoTransportDTO.getCustomerComponentType());
                snItem.setMaterialName(productStorageCargoTransportDTO.getMaterialName());
                snItem.setMaterialBillList(Lists.newArrayList());
                materialBillList.add(snItem);
            }
        }
    }


    @Override
    public <T> boolean validatePushMessageData(String currProcess, T pushMessageData) {
        if (!(pushMessageData instanceof FinishedProductStorageDTO)) {
            return false;
        }
        FinishedProductStorageDTO finishedProductStorage = (FinishedProductStorageDTO) pushMessageData;
        if (!validatePushMessageDataCommonField(finishedProductStorage)) {
            return false;
        }
        return true;
    }

    private ApsCustInstructBomDTO getTopApsCustInstructBomMap(PushStdModelSnDataExtDTO pushStdModelSnDataExt) {
        String taskEntityClass = pushStdModelSnDataExt.getTaskEntityClass();
        // 拆解任务
        if (Constant.FG_DISAS_2.equals(taskEntityClass)) {
            return null;
        }
        // 任务维度获取APS数据
        EntityQueryDTO entityQueryDTO = new EntityQueryDTO();
        entityQueryDTO.setEntityNo(pushStdModelSnDataExt.getTaskNo());
        entityQueryDTO.setStockOrgId(pushStdModelSnDataExt.getStockOrgId());
        Page<ApsCustomerTaskDTO> psTaskExtendedPage = apsInOneClient.customerTaskQueryWithDetail(new TasksQueryDTO().setEntityNoAddStockOrgIdList(Lists.newArrayList(entityQueryDTO)));
        if (psTaskExtendedPage == null || CollectionUtils.isEmpty(psTaskExtendedPage.getRows())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                    new Object[]{pushStdModelSnDataExt.getTaskNo()});
        }
        ApsCustomerTaskDTO apsCustomerTask = psTaskExtendedPage.getRows().get(0);
        // buffer指令
        if (BusinessSceneEnum.BUFFER.getCode().equals(apsCustomerTask.getBusinessScene())) {
            return null;
        }
        List<ApsCustInstructBomDTO> apsCustomerTaskBomList = apsCustomerTask.getBomList();
        if (CollectionUtils.isEmpty(apsCustomerTaskBomList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                    new Object[]{pushStdModelSnDataExt.getTaskNo()});
        }
        // 制造任务
        if (BusinessSceneEnum.MANUFACTURE.getCode().equals(apsCustomerTask.getBusinessScene())) {
            return apsCustomerTaskBomList.stream()
                    .filter(item -> item.getMaterialBillType() == null)
                    .findAny()
                    .orElseThrow(() -> new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                            new Object[]{pushStdModelSnDataExt.getTaskNo()}));
        }
        return apsCustomerTaskBomList.stream()
                .filter(item -> MATERIAL_BILL_TYPE_REWORK_2.equals(item.getMaterialBillType()))
                .findAny()
                .orElseThrow(() -> new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_EXIST_APS,
                        new Object[]{pushStdModelSnDataExt.getTaskNo()}));
    }

    private boolean validatePushMessageDataCommonField(FinishedProductStorageDTO finishedProductStorage) {
        if (StringUtils.isBlank(finishedProductStorage.getCategory())) {
            return false;
        }
        if (StringUtils.isBlank(finishedProductStorage.getManufactureOrderNo())) {
            return false;
        }
        if (!Arrays.asList(NumConstant.NUM_ONE, NumConstant.NUM_TWO)
                .contains(finishedProductStorage.getMaterialQuality())) {
            return false;
        }
        return finishedProductStorage.getQuantityCompleted() != null;
    }

    @Override
    protected String getPushStdModelSnDataMessageType() {
        return Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE;
    }

    private void wrapCustomerMaterialNameApsCustInstructBomMap(Map<String, ApsCustInstructBomDTO> customerMaterialNameApsCustInstructBomMap,
                                                               ApsCustInstructBomDTO apsCustInstructBom) {
        if (apsCustInstructBom == null) {
            return;
        }
        String customerMaterialName = apsCustInstructBom.getCustomerMaterialName();
        if (StringUtils.isNotBlank(customerMaterialName)) {
            customerMaterialNameApsCustInstructBomMap.put(customerMaterialName, apsCustInstructBom);
        }
        List<ApsCustInstructBomDTO> bomList = apsCustInstructBom.getBomList();
        if (CollectionUtils.isEmpty(bomList)) {
            return;
        }
        bomList.forEach(bom -> this.wrapCustomerMaterialNameApsCustInstructBomMap(customerMaterialNameApsCustInstructBomMap, bom));
    }
}
