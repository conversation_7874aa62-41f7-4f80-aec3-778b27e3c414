package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.AuxMaterialReqRelChangeRecordService;
import com.zte.application.AuxMaterialRequisitionRelationshipService;
import com.zte.common.ExcelUtils;
import com.zte.common.utils.Constant;
import com.zte.application.HrmUserCenterService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.AuxMaterialReqRelChangeRecord;
import com.zte.domain.model.AuxMaterialRequisitionRelationship;
import com.zte.domain.model.AuxMaterialRequisitionRelationshipRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.AuxMaterialRequisitionRelationshipPageQueryDTO;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.approval.AppendixDTO;
import com.zte.interfaces.dto.approval.ApprovalFlowStartDTO;
import com.zte.iss.approval.sdk.bean.OpinionDTO;
import com.zte.iss.approval.sdk.bean.QueryTaskParaDTO;
import com.zte.iss.approval.sdk.bean.TaskVo;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EasyExcelUtils;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.io.BufferedOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.FOUR;
import static com.zte.common.utils.Constant.FOUR_ZORE;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.StringConstant.FORMATTER_YYYYMMDD;

/**
 * 辅料领用关系表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-28 16:30:54
 */
@Service("auxMaterialRequisitionRelationshipService")
public class AuxMaterialRequisitionRelationshipServiceImpl extends AbstractExportTaskHandler<AuxMaterialRequisitionRelationshipPageQueryDTO, AuxMaterialRequisitionRelationship> implements AuxMaterialRequisitionRelationshipService {

    @Autowired
    private AuxMaterialRequisitionRelationshipRepository auxMaterialRequisitionRelationshipRepository;
    @Autowired
    private AuxMaterialReqRelChangeRecordService auxMaterialReqRelChangeRecordService;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    private ConstantInterface constantInterface;
    @Autowired
    private BsItemInfoRepository bsItemInfoRepository;
    @Autowired
    CloudDiskHelper cloudDiskHelper;
    @Value("${approval.flowCode.aux.material.requisition.code:zte-mes-manufactureshare-aux-material-req-entry}")
    private String auxMaterialRequisitionRelationshipFlowCode;
    @Autowired
    private ApprovalRemoteService approvalRemoteService;
    @Autowired
    private ApprovalProcessInfoRepository approvalProcessInforepository;
    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;
    @Autowired
    private EmailUtils emailUtils;
    private static final Logger LOG = LoggerFactory.getLogger(AuxMaterialRequisitionRelationshipServiceImpl.class);
    @Override
    public PageRows<AuxMaterialRequisitionRelationship> queryPage(AuxMaterialRequisitionRelationshipPageQueryDTO query) throws Exception {
        this.checkQueryData(query);
        if(Objects.equals(Constant.TRUE, query.getHandledByMe())) {
            query.setCurrentProcessor(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        }
        Page<AuxMaterialRequisitionRelationshipPageQueryDTO> page = new Page<>(query.getPage(), query.getRows());
        page.setParams(query);

        PageRows<AuxMaterialRequisitionRelationship> pageRows = new PageRows<>();
        Integer count = auxMaterialRequisitionRelationshipRepository.selectCount(query);
        List<AuxMaterialRequisitionRelationship> pageList = auxMaterialRequisitionRelationshipRepository.selectPage(page);
        pageRows.setRows(pageList);
        pageRows.setTotal(count);
        this.transName(pageRows.getRows());
        this.transStatus(pageRows.getRows());
        pageRows.setCurrent(query.getPage());
        return pageRows;
    }

    private void checkQueryData(AuxMaterialRequisitionRelationshipPageQueryDTO query) {
        if (query.getCreateStartDate() != null || query.getCreateEndDate() != null) {
            if (calDaysByDate(query.getCreateEndDate(), query.getCreateStartDate(), Constant.INT_0).compareTo(Constant.TIME_YEAR) > Constant.INT_0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DAY_OVER_ONE_YEAR);
            }
        }
        if (query.getLastUpdatedStartDate() != null || query.getLastUpdatedEndDate() != null) {
            if (calDaysByDate(query.getLastUpdatedEndDate(), query.getLastUpdatedStartDate(), Constant.INT_0).compareTo(Constant.TIME_YEAR) > Constant.INT_0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DAY_OVER_ONE_YEAR);
            }
        }

    }
    private BigDecimal calDaysByDate(Date endDate, Date startDate, int scale) {
        BigDecimal bg1000 = new BigDecimal(NumConstant.NUM_1000);
        if ((null == endDate) || (null == startDate)){
            return new BigDecimal(Constant.INT_0);
        }
        long days = ((endDate.getTime() - startDate.getTime()) * NumConstant.NUM_1000) / Constant.DAY_TIME;
        return new BigDecimal(days).divide(bg1000,scale, BigDecimal.ROUND_HALF_UP);
    }
    @Override
    public List<AuxMaterialRequisitionRelationship> selectByNo(String changeOrderNo) {
        return auxMaterialRequisitionRelationshipRepository.selectByNo(changeOrderNo);
    }

    /**
     * 获取变更中，启用中状态的单据
     *
     * @param dto
     * @return
     */
    @Override
    public List<AuxMaterialRequisitionRelationship> getRequisitionRelaByCondition(PsTaskDTO dto) {
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        AuxMaterialRequisitionRelationship query = new AuxMaterialRequisitionRelationship();
        query.setItemNo(dto.getItemNo().substring(Constant.INT_0, Constant.ITEM_PREFIX_LENGTH));
        query.setProductCategory(dto.getExternalType());
        list = auxMaterialRequisitionRelationshipRepository.queryByCondition(query);
        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        //先找15位料单的数据
        List<AuxMaterialRequisitionRelationship> filterList = list.stream().filter(e -> StringUtils.isNotEmpty(e.getItemNo()) && Constant.INT_15 == e.getItemNo().length()
                && e.getItemNo().equals(dto.getItemNo())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(filterList)){
            return filterList;
        }
        // 再找按12位料单维护的数据--料单代码有值的数据
        List<AuxMaterialRequisitionRelationship> filterListTwelve = list.stream().filter(e -> StringUtils.isNotEmpty(e.getItemNo())
                && Constant.ITEM_PREFIX_LENGTH == e.getItemNo().length()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(filterListTwelve)){
            return filterListTwelve;
        }
        return list.stream().filter(e -> StringUtils.isNotEmpty(e.getProductCategory()) && e.getProductCategory().equals(dto.getExternalType())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOperate(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship) throws Exception {
        if (CollectionUtils.isEmpty(auxMaterialRequisitionRelationship.getApproveList())) {
            return;
        }
        this.checkInputDataAndDeal(auxMaterialRequisitionRelationship);
        auxMaterialRequisitionRelationship.setChangeOrderNo(this.generateBillNo());
        auxMaterialRequisitionRelationship.setCreateBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        auxMaterialRequisitionRelationship.setLastUpdatedBy(auxMaterialRequisitionRelationship.getCreateBy());
        this.batchInsert(auxMaterialRequisitionRelationship);
        this.transStatus(auxMaterialRequisitionRelationship.getApproveList());
        this.startApproval(auxMaterialRequisitionRelationship);
        this.sendEmail(auxMaterialRequisitionRelationship);
    }

    private void batchInsert(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship) {
        List<AuxMaterialRequisitionRelationship> approveList = auxMaterialRequisitionRelationship.getApproveList();
        AuxMaterialRequisitionRelationship splitInsertDTO = new AuxMaterialRequisitionRelationship();
        BeanUtils.copyProperties(auxMaterialRequisitionRelationship, splitInsertDTO);
        for (List<AuxMaterialRequisitionRelationship> tempList : CommonUtils.splitList(approveList, Constant.INT_100)) {
            splitInsertDTO.setApproveList(tempList);
            auxMaterialRequisitionRelationshipRepository.batchInsert(splitInsertDTO);
        }
    }

    private void checkInputDataAndDeal(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship) {
        this.checkInputData(auxMaterialRequisitionRelationship.getApproveList());
        List<String> messageList = auxMaterialRequisitionRelationship.getApproveList().stream().map(AuxMaterialRequisitionRelationship::getVerifyResult).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(messageList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RELATIONSHIP_VERIFY_NOT_PASS, new String[] {String.join(Constant.COMMA, messageList)});
        }
    }

    // 发送邮件
    private void sendEmail(AuxMaterialRequisitionRelationship dto) throws Exception {
        Date sendTime = new Date();
        String empName = this.setEmailName(dto.getCreateBy());
        String title = String.format(Constant.AUX_MATERIAL_REQUISITION_ENTRY_EMAIL_TITLE, dto.getChangeOrderNo(),empName
                , DateUtil.convertDateToString(sendTime, DateUtil.DATE_FORMATE_FULL));
        emailUtils.sendMail(dto.getCurrentProcessor(), title, "", this.getEmailContent(dto.getApproveList()), "");
    }

    private String setEmailName(String lastUpdatedBy) throws Exception {
        List<String> userList = new ArrayList<>();
        userList.add(lastUpdatedBy);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(userList);
        HrmPersonInfoDTO dto = hrmPersonInfoDTOMap.get(lastUpdatedBy);
        if (dto != null) {
            return dto.getEmpName() + lastUpdatedBy;
        }else{
            return lastUpdatedBy;
        }
    }

    private String generateBillNo() throws Exception {
        //工厂ID
        StringBuilder sb = new StringBuilder(Constant.AUX_BILL_NO_PREFIX);
        //8位日期
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FORMATTER_YYYYMMDD);
        String formatDate = simpleDateFormat.format(new Date());
        //4位流水码
        sb.append(formatDate).append(getRedisId(MpConstant.KEY_VALUE_AUX_REQUISITON_BILL + formatDate));
        return sb.toString();
    }

    private String getRedisId(String redisKey) throws Exception {

        String lockKey = this.getClass().getSimpleName() + MpConstant.KEY_VALUE_AUX_REQUISITON_BILL;
        RedisLock redisLock = new RedisLock(lockKey);
        String redisId = null;
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new Exception();
            }
            Integer maxCountValue = findMaxCount(redisKey);
            RedisCacheUtils.set(redisKey, maxCountValue + 1, Constant.EXPIRE_TIME_DAY);
            StringBuilder serialCode = new StringBuilder(FOUR_ZORE);
            serialCode = serialCode.append(maxCountValue);
            redisId = serialCode.substring(serialCode.length() - FOUR, serialCode.length());
        } finally {
            redisLock.unlock();
        }
        return redisId;
    }

    public Integer findMaxCount(String redisKey) {
        Integer maxCountValue = RedisCacheUtils.get(redisKey, Integer.class);
        if (maxCountValue == null) {
            return 0;
        }
        return maxCountValue;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeOperate(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship) throws Exception {
        if (CollectionUtils.isEmpty(auxMaterialRequisitionRelationship.getApproveList())) {
            return;
        }
        if (Constant.STR_ONE.equals(auxMaterialRequisitionRelationship.getChangeType())) {
            this.checkInputDataAndDeal(auxMaterialRequisitionRelationship);
        }
        this.transStatus(auxMaterialRequisitionRelationship.getApproveList());
        auxMaterialRequisitionRelationship.setChangeOrderNo(this.generateBillNo());
        auxMaterialRequisitionRelationship.setLastUpdatedBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        auxMaterialRequisitionRelationship.setStatus(Constant.INT_2);
        auxMaterialRequisitionRelationship.setAuxMaterialCode(auxMaterialRequisitionRelationship.getApproveList().get(0).getAuxMaterialCode());
        auxMaterialRequisitionRelationship.setAuxMaterialCodeAfter(auxMaterialRequisitionRelationship.getApproveList().get(0).getAuxMaterialCodeAfter());
        auxMaterialRequisitionRelationship.setUnitUsageAfter(auxMaterialRequisitionRelationship.getApproveList().get(0).getUnitUsageAfter());
        auxMaterialRequisitionRelationship.setUnitAfter(auxMaterialRequisitionRelationship.getApproveList().get(0).getUnitAfter());
        auxMaterialRequisitionRelationship.setLastUpdatedBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        auxMaterialRequisitionRelationshipRepository.updateById(auxMaterialRequisitionRelationship);
        this.startApproval(auxMaterialRequisitionRelationship);
        this.sendEmail(auxMaterialRequisitionRelationship);
    }
    @Override
    public Integer deleteByIds(List<String> ids) {
        return auxMaterialRequisitionRelationshipRepository.deleteByIds(ids);
    }

    @Override
    public Integer countExportTotal(AuxMaterialRequisitionRelationshipPageQueryDTO query) {
        return auxMaterialRequisitionRelationshipRepository.selectCount(query);
    }

    @Override
    public List<AuxMaterialRequisitionRelationship> queryExportData(AuxMaterialRequisitionRelationshipPageQueryDTO query, int pageNo, int pageSize) {
        Page<AuxMaterialRequisitionRelationshipPageQueryDTO> page = new Page<>(pageNo, pageSize);
        page.setParams(query);
        page.setSearchCount(false);
        List<AuxMaterialRequisitionRelationship> pageList = auxMaterialRequisitionRelationshipRepository.selectPage(page);
        try {
            this.transName(pageList);
            this.transStatus(pageList);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_EXPORT_ERROR);
        }
        return pageList;
    }

    private void transStatus(List<AuxMaterialRequisitionRelationship> pageList) {
        List<SysLookupValues> relationshipDimensionList = getLookup(Constant.LOOK_UP_CODE_7580014);
        List<SysLookupValues> auxMaterialTypeList = getLookup(Constant.LOOK_UP_CODE_1004107);
        List<SysLookupValues> statusList = getLookup(Constant.LOOK_UP_CODE_7580015);
        Map<String, String> dimensionMap = relationshipDimensionList.stream().collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getDescriptionChin));
        Map<String, String> auxMaterialTypeMap = auxMaterialTypeList.stream().collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getDescriptionChin));
        Map<String, String> statusMap = statusList.stream().collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getDescriptionChin));
        for (AuxMaterialRequisitionRelationship dto : pageList) {
            String dimensionName = dimensionMap.get(String.valueOf(dto.getRelationshipDimension()));
            String auxMaterialTypeName = auxMaterialTypeMap.get(String.valueOf(dto.getAuxMaterialType()));
            String statusName = statusMap.get(String.valueOf(dto.getStatus()));
            dto.setStatusName(StringUtils.isNotBlank(statusName) ? statusName : String.valueOf(dto.getStatus()));
            dto.setUnitUsageStr(dto.getUnitUsage());
            dto.setAuxMaterialTypeName(StringUtils.isNotBlank(auxMaterialTypeName) ? auxMaterialTypeName : String.valueOf(dto.getAuxMaterialType()));
            dto.setRelationshipDimensionName(StringUtils.isNotBlank(dimensionName) ? dimensionName : String.valueOf(dto.getRelationshipDimension()));
        }
    }
    private void transImportName(List<AuxMaterialRequisitionRelationship> pageList) {
        List<SysLookupValues> relationshipDimensionList = getLookup(Constant.LOOK_UP_CODE_7580014);
        List<SysLookupValues> auxMaterialTypeList = getLookup(Constant.LOOK_UP_CODE_1004107);
        Map<String, String> dimensionMap = relationshipDimensionList.stream().collect(Collectors.toMap(SysLookupValues::getDescriptionChin, SysLookupValues::getLookupMeaning));
        Map<String, String> auxMaterialTypeMap = auxMaterialTypeList.stream().collect(Collectors.toMap(SysLookupValues::getDescriptionChin, SysLookupValues::getLookupMeaning));
        for (AuxMaterialRequisitionRelationship dto : pageList) {
            if (Constant.ITEM_NO.equals(dto.getRelationshipDimensionName())) {
                dto.setRelationshipDimension(1);
            } else if (Constant.AuxMaterialRequisitionDetail.PRODUCT_CATEGORY.equals(dto.getRelationshipDimensionName())) {
                dto.setRelationshipDimension(0);
            }
            String dimension = dimensionMap.get(dto.getRelationshipDimensionName());
            String auxMaterialType = auxMaterialTypeMap.get(dto.getAuxMaterialTypeName());
            dto.setUnitUsageStr(dto.getUnitUsage());
            if(auxMaterialType != null) {
                dto.setAuxMaterialType(Integer.parseInt(auxMaterialType));
            }
            dto.setRelationshipDimensionName(dimension);
        }
    }
    @Override
    public void approvalOperateKafka(AuxMaterialRequisitionRelationship dto) throws Exception {
        List<AuxMaterialRequisitionRelationship> dtoList = auxMaterialRequisitionRelationshipRepository.selectByNo(dto.getChangeOrderNo());
        if(CollectionUtils.isEmpty(dtoList)) {
            // 记得改提示 单号没数据，报错
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHANGE_ORDER_NO_DATA_NULL);
        }
        if (Objects.equals(dto.getOperateType(), Constant.ApprovalStatus.APPROVAL_COMPLETED)) {
            return;
        }
        AuxMaterialRequisitionRelationship approveDTO = dtoList.get(0);
        approveDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        approveDTO.setApprovedOpinions(dto.getApprovedOpinions());
        approveDTO.setOperateType(dto.getOperateType());
        approveDTO.setApprovedFlag(Constant.FLAG_Y);
        approveDTO.setApproveList(dtoList);
        this.approvalOperate(approveDTO);
    }

    @Override
    public void approvalOperateSys(AuxMaterialRequisitionRelationship dto) throws Exception {
        this.approvalOperate(dto);
        if (Objects.equals(dto.getChangeType(), Constant.STR_THREE)) {
            approvalRemoteService.revoke(dto.getChangeOrderNo(), dto.getLastUpdatedBy(), auxMaterialRequisitionRelationshipFlowCode);
        } else {
            this.submitTheApproval(dto);
        }
    }

    // 审批操作
    @Transactional(rollbackFor = Exception.class)
    public void approvalOperate(AuxMaterialRequisitionRelationship dto) throws Exception {
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setBillNo(dto.getChangeOrderNo());
        List<ApprovalProcessInfoEntityDTO> approvalProcessLit = approvalProcessInforepository.getList(approvalProcessInfoEntityDTO);
        if(CollectionUtils.isEmpty(approvalProcessLit)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHANGE_ORDER_NO_DATA_NULL);
        }
        // 根据单据号获取审批流数据库表中的数据，其中remark字段为该单据生成时的 变更状态
        String billChangeType = approvalProcessLit.get(0).getRemark().substring(0,1);
        dto.setChangeType(StringUtils.isBlank(dto.getChangeType()) ? billChangeType : dto.getChangeType());
        AuxMaterialReqRelChangeRecord auxMaterialReqRelChangeRecord;
        // 根据变更类型执行不同场景
        switch (dto.getChangeType()) {
                // 新建
            case Constant.STR_0:
                // 新建场景，审批通过时才会新增操作历史
                if (Constant.AGREE.equals(dto.getOperateType())) {
                    dto.setStatus(Constant.INT_1);
                    auxMaterialReqRelChangeRecord = assembleRecordData(dto);
                    auxMaterialReqRelChangeRecordService.addNewRecordWhenApprove(auxMaterialReqRelChangeRecord);
                } else {
                    dto.setStatus(Constant.INT_3);
                    dto.setEnabledFlag(Constant.FLAG_N);
                }
                auxMaterialRequisitionRelationshipRepository.updateAddApprovalByChangeOrderNo(dto);
                this.sendEmailAfterApproval(dto);
                break;
            // 变更
            case Constant.STR_ONE:
                auxMaterialReqRelChangeRecord = assembleRecordData(dto);
                if (Constant.AGREE.equals(dto.getOperateType())) {
                    List<AuxMaterialRequisitionRelationship> dataBefore = auxMaterialRequisitionRelationshipRepository.selectByNo(dto.getChangeOrderNo());
                    dto.setAuxMaterialCode(dataBefore.get(0).getAuxMaterialCodeAfter());
                    dto.setUnit(dataBefore.get(0).getUnitAfter());
                    dto.setUnitUsage(dataBefore.get(0).getUnitUsageAfter());
                }
                dto.setAuxMaterialCodeAfter(null);
                dto.setUnitAfter(null);
                dto.setUnitUsageAfter(Constant.STR_0);
                dto.setStatus(Constant.INT_1);
                auxMaterialRequisitionRelationshipRepository.updateByChangeOrderNo(dto);
                auxMaterialReqRelChangeRecordService.addNewRecordWhenApprove(auxMaterialReqRelChangeRecord);
                this.sendEmailAfterApproval(dto);
                break;
            // 删除
            case Constant.STR_TWO:
                if (Constant.AGREE.equals(dto.getOperateType())) {
                    dto.setStatus(Constant.INT_3);
                    dto.setEnabledFlag(Constant.FLAG_N);
                } else {
                    dto.setStatus(Constant.INT_1);
                    dto.setEnabledFlag(Constant.FLAG_Y);
                }
                auxMaterialReqRelChangeRecord = assembleRecordData(dto);
                auxMaterialRequisitionRelationshipRepository.updateAddApprovalByChangeOrderNo(dto);
                auxMaterialReqRelChangeRecordService.addNewRecordWhenApprove(auxMaterialReqRelChangeRecord);
                this.sendEmailAfterApproval(dto);
                break;
            // 撤销审批
            case Constant.STR_THREE:
                // 如果单据为新增单据，则直接失效
                if (billChangeType.equals(Constant.STR_0)) {
                    dto.setStatus(Constant.INT_3);
                    dto.setEnabledFlag(Constant.FLAG_N);
                // 如果单据为变更/删除单据，则根据记录的变更前代码恢复数据
                } else {
                    dto.setStatus(Constant.INT_1);
                    dto.setAuxMaterialCodeAfter(null);
                    dto.setUnitAfter(null);
                    dto.setUnitUsageAfter(Constant.STR_0);
                }
                auxMaterialRequisitionRelationshipRepository.updateByChangeOrderNo(dto);
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.THE_APPROVAL_TYPE_IS_INCORRECT);
        }
    }

    private AuxMaterialReqRelChangeRecord assembleRecordData(AuxMaterialRequisitionRelationship dto) {
        AuxMaterialReqRelChangeRecord auxMaterialReqRelChangeRecord = new AuxMaterialReqRelChangeRecord();
        BeanUtils.copyProperties(dto, auxMaterialReqRelChangeRecord);
        auxMaterialReqRelChangeRecord.setApprovedStatus(dto.getOperateType());
        auxMaterialReqRelChangeRecord.setApprovedOpinions(dto.getApprovedOpinions());
        return auxMaterialReqRelChangeRecord;
    }

    //共用，去审批中心获取taskId用于更新
    private String getTaskVoForCommon(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship){
        // 先获取待审批的taskId
        QueryTaskParaDTO queryTaskParaDTO = new QueryTaskParaDTO();
        queryTaskParaDTO.setHandler(auxMaterialRequisitionRelationship.getLastUpdatedBy());
        queryTaskParaDTO.setEmpNo(auxMaterialRequisitionRelationship.getLastUpdatedBy());
        queryTaskParaDTO.setFlowCode(auxMaterialRequisitionRelationshipFlowCode);
        queryTaskParaDTO.setBusinessId(auxMaterialRequisitionRelationship.getChangeOrderNo());
        PageRows<TaskVo> taskVoPageRows = ApprovalTaskClient.queryTask(queryTaskParaDTO);
        //先查审批中心待审批数据获取对应的taskId
        if (taskVoPageRows == null || CollectionUtils.isEmpty(taskVoPageRows.getRows()) || null == taskVoPageRows.getRows().get(NumConstant.NUM_ZERO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER);
        }
        TaskVo taskVo = taskVoPageRows.getRows().get(NumConstant.NUM_ZERO);
        return taskVo.getTaskId();
    }
    // 关闭单据方法
    public void submitTheApproval(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship){
        String approvalResult = Constant.AGREE.equals(auxMaterialRequisitionRelationship.getOperateType()) ? Constant.FLAG_Y : Constant.FLAG_N;
        // 先获取待审批的taskId
        String taskID = this.getTaskVoForCommon(auxMaterialRequisitionRelationship);
        OpinionDTO opinionDTO = new OpinionDTO();
        opinionDTO.setTaskId(taskID);
        opinionDTO.setResult(approvalResult);
        opinionDTO.setHandler(auxMaterialRequisitionRelationship.getLastUpdatedBy());
        ApprovalTaskClient.submit(opinionDTO);
    }
    private void startApproval(AuxMaterialRequisitionRelationship dto) throws Exception {
        List<String> hrmList = new ArrayList<>();
        hrmList.add(dto.getCurrentProcessor());
        Map<String, HrmPersonInfoDTO> hrmMap = hrmUserCenterService.getHrmPersonInfo(hrmList);
        String empName = null;
        HrmPersonInfoDTO approvalHrmDTO = hrmMap.get(dto.getCurrentProcessor());
        if (approvalHrmDTO != null) {
            empName = approvalHrmDTO.getEmpName();
        }
        List<AppendixDTO> appendix = buildExcelWithBarcodeInfo(dto.getCurrentProcessor(), dto.getChangeOrderNo(), dto.getApproveList());
        ApprovalFlowStartDTO approvalFlowStartDTO = new ApprovalFlowStartDTO();
        approvalFlowStartDTO.setFlowCode(auxMaterialRequisitionRelationshipFlowCode);
        approvalFlowStartDTO.setBusinessId(dto.getChangeOrderNo());
        approvalFlowStartDTO.setHandler(dto.getLastUpdatedBy());
        Map<String, Object> approvalParam = new HashMap<>();
        approvalParam.put(Constant.BILL_NO, dto.getChangeOrderNo());
        approvalParam.put(Constant.EMP_NO, dto.getLastUpdatedBy());
        approvalParam.put(Constant.APPENDIX, appendix);
        approvalParam.put(Constant.APPROVER_ID, dto.getCurrentProcessor());
        String changeTypeName = transferChangeType(dto);
        dto.setChangeTypeName(changeTypeName);
        approvalParam.put(Constant.TITLE, String.format(Constant.AUX_MATERIAL_REQUISITION_ENTRY_TITLE, changeTypeName, dto.getChangeOrderNo()));
        approvalParam.put(Constant.APPROVE_LIST, dto.getApproveList());
        approvalFlowStartDTO.setParams(approvalParam);
        approvalFlowStartDTO.setAppendix(appendix);

        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setId(UUID.randomUUID().toString());
        approvalProcessInfoEntityDTO.setBillNo(dto.getChangeOrderNo());
        approvalProcessInfoEntityDTO.setCreateBy(dto.getLastUpdatedBy());
        approvalProcessInfoEntityDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        //待审批
        approvalProcessInfoEntityDTO.setStatus(Constant.STR_0);
        approvalProcessInfoEntityDTO.setApproverId(dto.getCurrentProcessor());
        approvalProcessInfoEntityDTO.setApproverName(empName);
        approvalProcessInfoEntityDTO.setNodeCode(Constant.APPROVER);
        approvalProcessInfoEntityDTO.setNodeName(Constant.APPROVER);
        approvalProcessInfoEntityDTO.setSeq(NumConstant.NUM_ZERO);
        approvalProcessInfoEntityDTO.setSubSeq(NumConstant.NUM_ZERO);
        // 将变更种类存到approve表中，用于后续kafka消费后更新单据状态使用
        String remark = dto.getChangeType();
        approvalProcessInfoEntityDTO.setRemark(remark);
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        approvalFlowStartDTO.setApprovalProcessInfoEntityDTOList(approvalProcessInfoEntityDTOList);
        approvalProcessInforepository.batchInsert(approvalProcessInfoEntityDTOList);
        approvalRemoteService.start(approvalFlowStartDTO);
    }

    private String transferChangeType(AuxMaterialRequisitionRelationship dto) {
        if (StringUtils.isBlank(dto.getChangeType())) {
            return STRING_EMPTY;
        }
        switch (dto.getChangeType()) {
            case Constant.STR_0:
                return Constant.CREATE;
            case Constant.STR_ONE:
                return Constant.CHANGE;
            case Constant.STR_TWO:
                return Constant.DELETE;
            default:
                return STRING_EMPTY;
        }
    }

    private List<AppendixDTO> buildExcelWithBarcodeInfo(String empNo, String billNo, List<AuxMaterialRequisitionRelationship> approveList) throws Exception {
        this.transName(approveList);
        String fileName = billNo + MpConstant.AuxMaterialRequisitionExcel.FILE_NAME;
        SXSSFWorkbook swb = new SXSSFWorkbook(NumConstant.NUM_2000);
        SXSSFSheet sheet = ExcelCommonUtils.createSheet(swb, MpConstant.AuxMaterialRequisitionExcel.HEADER_LIST);
        ExcelCommonUtils.appendSXSSFWorkbookByRows(sheet, approveList, MpConstant.AuxMaterialRequisitionExcel.PROPS, NumConstant.NUM_ZERO);
        Pair<String, String> uploadInfo = this.uploadCloudDisk(empNo, fileName, swb);
        List<AppendixDTO> appendix = new ArrayList<>();
        String fileUrl = constantInterface.getUrl(InterfaceEnum.downLoadFileByKey);
        AppendixDTO dto = new AppendixDTO();
        dto.setFileUrl(fileUrl + Constant.VIRGULE + uploadInfo.getFirst());
        dto.setName(fileName);
        dto.setMd5(uploadInfo.getFirst());
        dto.setUid(uploadInfo.getFirst());
        appendix.add(dto);
        return appendix;
    }

    private void transName(List<AuxMaterialRequisitionRelationship> approveList) throws Exception {
        if (CollectionUtils.isEmpty(approveList)) {
            return;
        }
        List<String> userIdList = new LinkedList<>();
        for (AuxMaterialRequisitionRelationship item : approveList) {
            if (StringUtils.isNotBlank(item.getCurrentProcessor())) {
                userIdList.add(item.getCurrentProcessor());
            }
            if (StringUtils.isNotBlank(item.getCreateBy())) {
                userIdList.add(item.getCreateBy());
            }
            if (StringUtils.isNotBlank(item.getLastUpdatedBy())) {
                userIdList.add(item.getLastUpdatedBy());
            }
        }
        List<String> distinctList = userIdList.stream().distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(distinctList);
        for (AuxMaterialRequisitionRelationship item : approveList) {
            HrmPersonInfoDTO hrmPersonInfo = hrmPersonInfoDTOMap.get(item.getCurrentProcessor());
            if (hrmPersonInfo != null) {
                item.setCurrentProcessor(hrmPersonInfo.getEmpName() + item.getCurrentProcessor());
            }
            HrmPersonInfoDTO hrmPersonInfo1 = hrmPersonInfoDTOMap.get(item.getCreateBy());
            if (hrmPersonInfo1 != null) {
                item.setCreateBy(hrmPersonInfo1.getEmpName() + item.getCreateBy());
            }
            HrmPersonInfoDTO hrmPersonInfo2 = hrmPersonInfoDTOMap.get(item.getLastUpdatedBy());
            if (hrmPersonInfo2 != null) {
                item.setLastUpdatedBy(hrmPersonInfo2.getEmpName() + item.getLastUpdatedBy());
            }
        }
    }

    private Pair<String, String> uploadCloudDisk(String empNo, String fileName, SXSSFWorkbook swb) throws Exception {
        // 将文件暂存本地
        String filePathTemp = FileUtils.createFilePathAndCheck(fileName);
        try (BufferedOutputStream os = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePathTemp)))) {
            swb.write(os);
            os.flush();
            swb.dispose();
        }
        // 上传文档云，并获取文档路径
        String fileKey = cloudDiskHelper.fileUpload(filePathTemp, empNo, CloudDiskHelper.MAX_RETRY_TIMES);
        // 上传成功后删除本地文件
        FileUtils.deleteFile(filePathTemp);
        String fileUrl = cloudDiskHelper.getFileDownloadUrl(fileKey, null, empNo);
        return Pair.of(fileKey != null ? fileKey : Constant.STRING_EMPTY, fileUrl != null ? fileUrl : Constant.STRING_EMPTY);
    }

    // 界面/审批中心审批完成后，邮件通知提单人
    @Override
    public void sendEmailAfterApproval(AuxMaterialRequisitionRelationship dto) throws Exception {
        String title = Constant.STRING_EMPTY;
        if(Constant.AGREE.equals(dto.getOperateType())){
            title = String.format(Constant.AUX_MATERIAL_REQUISITION_ENTRY_AGREE_EMAIL_TITLE, dto.getChangeOrderNo());
        } else {
            title = String.format(Constant.AUX_MATERIAL_REQUISITION_ENTRY_REFUSE_EMAIL_TITLE, dto.getChangeOrderNo());
        }
        emailUtils.sendMail(dto.getCreateBy(), title, "", this.getEmailContent(dto.getApproveList()), "");
    }

    // 组装邮件内容
    private String getEmailContent(List<AuxMaterialRequisitionRelationship> approveList) throws Exception{
        this.transName(approveList);
        StringBuffer html = new StringBuffer();
        String div = "<br/><br/><div>";
        String htmlTitle = "<p style='margin:0;font-size:13pt'>" + Constant.AUX_MATERIAL_REQUISITION_ENTRY_INFO + "</p>";
        html.append(div + htmlTitle + "<table style='border:1px rgb(204,204,204);width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        html.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        html.append("<td>"+Constant.INDEX+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.AUX_MATERIAL_TYPE+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.RELATIONSHIP_DIMENSION+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.ITEM_NO+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.ITEM_NAME+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.PRODUCT_CATEGORY+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.AUX_MATERIAL_CODE+"</td>");
        html.append("<td>"+Constant.AuxMaterialRequisitionDetail.AUX_MATERIAL_CODE_AFTER+"</td>");
        html.append("</tr>");
        for (int index = 0; index < approveList.size(); index++) {
            int num = index+1;
            html.append("<tr>");
            html.append("<td>" +  num + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getAuxMaterialTypeName()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getRelationshipDimensionName()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getItemNo()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getItemName()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getProductCategory()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getAuxMaterialCode()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(approveList.get(index).getAuxMaterialCodeAfter()) + "</td>");
            html.append("</tr>");
        }
        html.append("</table>");
        html.append("<hr style='height:5px;border:none;background-color:rgb(0,102,153);'/></div>");
        return html.toString();
    }

    /**
     * 下载模板
     **/
    @Override
    public void downLoadExcelTemplate(HttpServletResponse response) throws IOException {
        ImesExcelUtil.setResponseHeader(response, Constant.AUX_MATERIAL_REQUISITION_MODEL_NAME);
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), AuxMaterialRequisitionRelationshipPageQueryDTO.class)
                .excelType(ExcelTypeEnum.XLSX).build();
        WriteSheet build = EasyExcelFactory.writerSheet(0, "0").build();
        List<AuxMaterialRequisitionRelationshipPageQueryDTO> result = new LinkedList<>();
        result.add(new AuxMaterialRequisitionRelationshipPageQueryDTO());
        excelWriter.write(result, build);
        excelWriter.finish();
    }
    /**
     * 获取数据字典
     **/
    private List<SysLookupValues> getLookup(String lookUpCode) {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", lookUpCode);
        return sysLookupValuesRepository.getList(requestParam);
    }
    @Override
    public List<AuxMaterialRequisitionRelationship> uploadExcel(MultipartFile file) throws IOException {
        List<AuxMaterialRequisitionRelationship> resultList = new LinkedList<>();
        EasyExcelUtils.read(file.getInputStream(), AuxMaterialRequisitionRelationship.class, new AnalysisEventListener() {
            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                if (Objects.isNull(o)) {
                    return;
                }
                AuxMaterialRequisitionRelationship item = (AuxMaterialRequisitionRelationship) o;
                resultList.add(item);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet().doRead();

        this.transImportName(resultList);
        return this.checkInputData(resultList);
    }

    @Override
    public List<AuxMaterialRequisitionRelationship> checkInputData(List<AuxMaterialRequisitionRelationship> resultList) {
        // 导入时候把重复数据去重
        Set<AuxMaterialRequisitionRelationship> uniqueInputSet = new HashSet<>(resultList);
        List<AuxMaterialRequisitionRelationship> uniqueList = new ArrayList<>(uniqueInputSet);
        for (AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship : uniqueList) {
            this.checkInputDataRequired(auxMaterialRequisitionRelationship);
        }
        this.checkInputDataExist(uniqueList);
        return uniqueList;

    }

    private void checkInputDataRequired(AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship) {
        StringBuilder verifyResult = new StringBuilder();
        // 辅料条码必填
        if (StringUtils.isBlank(auxMaterialRequisitionRelationship.getAuxMaterialCode())) {
            verifyResult.append(Constant.AuxMaterialRequisitionDetail.CODE_REQUIRED);
        }
        // 维度是产品大类时，产品大类必填，料单代码不能填
        if (Constant.INT_0 == auxMaterialRequisitionRelationship.getRelationshipDimension()) {
            if (StringUtils.isNotBlank(auxMaterialRequisitionRelationship.getItemNo())) {
                verifyResult.append(Constant.AuxMaterialRequisitionDetail.ITEM_NO_NOT_REQUIRED);
            }
            if (StringUtils.isBlank(auxMaterialRequisitionRelationship.getProductCategory())) {
                verifyResult.append(Constant.AuxMaterialRequisitionDetail.CATEGORY_REQUIRED);
            }
        }
        // 维度是料单代码时，料单代码必填，产品大类不能填
        if (Constant.INT_1 == auxMaterialRequisitionRelationship.getRelationshipDimension()) {
            if (StringUtils.isNotBlank(auxMaterialRequisitionRelationship.getProductCategory())) {
                verifyResult.append(Constant.AuxMaterialRequisitionDetail.CATEGORY_NOT_REQUIRED);
            }
            if (StringUtils.isBlank(auxMaterialRequisitionRelationship.getItemNo())) {
                verifyResult.append(Constant.AuxMaterialRequisitionDetail.ITEM_NO_REQUIRED);
            }

        }
        auxMaterialRequisitionRelationship.setVerifyResult(verifyResult.toString());
    }

    private void checkInputDataExist(List<AuxMaterialRequisitionRelationship> resultList) {
        List<String> itemNos = resultList.stream().map(AuxMaterialRequisitionRelationship::getItemNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<BsItemInfo> itemInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemNos)) {
            for(List<String> splitList : CommonUtils.splitList(itemNos, Constant.INT_100)) {
                List<BsItemInfo> tempList = bsItemInfoRepository.getItemNameAndPCBVersion(splitList);
                itemInfos.addAll(tempList);
            }
        }
        Map<String, String> itemNosExist = itemInfos.stream().filter(i -> !StringUtils.isEmpty(i.getItemName())).collect(Collectors.toMap(BsItemInfo::getItemNo, BsItemInfo::getItemName, (v1, v2) -> v1));
        List<String> auxMaterialCodes = resultList.stream().map(AuxMaterialRequisitionRelationship::getAuxMaterialCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        auxMaterialCodes.addAll(resultList.stream().map(AuxMaterialRequisitionRelationship::getAuxMaterialCodeAfter).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        List<AuxMaterialRequisitionRelationship> relationshipExistList = auxMaterialRequisitionRelationshipRepository.selectByAuxMaterialCodes(auxMaterialCodes); // 分页
        // 将 辅料代码 + 关系维度 + 辅料类型 + 生成大类 +料单代码 作为key，用于数据重复校验（改成Set）
        Set<String> checkExistSet= relationshipExistList.stream().map(i -> i.getAuxMaterialCode() + i.getRelationshipDimension() + i.getAuxMaterialType() + i.getProductCategory() + i.getItemNo()).collect(Collectors.toSet());
        Set<Integer> lengthCheckSet = new HashSet<Integer>(){ {add(Constant.INT_12); add(Constant.INT_15);}};
        for (AuxMaterialRequisitionRelationship dto : resultList) {
            String checkKey = StringUtils.isBlank(dto.getAuxMaterialCodeAfter()) ? dto.getAuxMaterialCode() + dto.getRelationshipDimension() + dto.getAuxMaterialType() + dto.getProductCategory() + dto.getItemNo() :
                    dto.getAuxMaterialCodeAfter() + dto.getRelationshipDimension() + dto.getAuxMaterialType() + dto.getProductCategory() + dto.getItemNo();
            if (checkExistSet.contains(checkKey)) {
                dto.setVerifyResult(dto.getVerifyResult() + (Constant.AuxMaterialRequisitionDetail.AUX_MATERIAL_RELATIONSHIP_EXIST));
            }
            if (StringUtils.isBlank(dto.getItemNo())) {
                continue;
            }
            if (!lengthCheckSet.contains(dto.getItemNo().length())) {
                dto.setVerifyResult(dto.getVerifyResult() + (Constant.AuxMaterialRequisitionDetail.ITEM_NO_LENGTH));
            }
            String itemName = itemNosExist.get(dto.getItemNo());
            if (itemName == null) {
                dto.setVerifyResult(dto.getVerifyResult() + (Constant.AuxMaterialRequisitionDetail.ITEM_NOT_EXIST));
            } else {
                dto.setItemName(itemName);
            }
        }
    }
}
