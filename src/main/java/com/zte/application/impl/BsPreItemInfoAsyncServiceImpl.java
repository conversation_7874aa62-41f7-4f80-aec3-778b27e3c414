package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.BsPreItemInfoAsyncService;
import com.zte.application.CommonService;
import com.zte.application.CommonTransactionalService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BPcbLocationDetailRepository;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.BProdBomDetailRepository;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.BsAsyncDataRespository;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.domain.model.BsPremanuItemInfoRepository;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.AsyncBomDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.BsAsyncDataDTO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.IdGenerator;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 多线程异步BOM 分阶
 *
 * <AUTHOR>
 * @date 2021-03-30 14:42
 */
@Log4j
@Service
public class BsPreItemInfoAsyncServiceImpl implements BsPreItemInfoAsyncService {
    @Autowired
    private BBomHeaderRepository bomHeaderRepository;
    @Autowired
    private BBomDetailRepository bBomDetailRepository;
    @Autowired
    private BsItemInfoRepository bsItemInfoRepository;
    @Autowired
    private BsAsyncDataRespository bsAsyncDataRespository;
    @Autowired
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Autowired
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;
    @Autowired
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private CommonTransactionalService commonTransactionalService;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;
    @Value("${bom.exception.message.to:}")
    private String bomExceptionMessageTo;
    @Autowired
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;
    @Autowired
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Autowired
    private BProdBomDetailRepository bProdBomDetailRepository;
    @Value("${bom.run.waite.max.second:60}")
    private Integer bomRunWaiteMaxSecond;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IdGenerator idGenerator;

    /**
     * 运行BOM 分阶 异步逻辑
     *
     * @param pair        工厂 工号信息（1，工厂，2.工号）
     * @param pairRequest 需要跑BOM分阶的料单明细,2. 异步表信息
     */
    @Override
    public void runBomCodeAsync(Pair<String, String> pair, Pair<List<AsyncBomDTO>, BsAsyncDataDTO> pairRequest) {
        this.executeBomLevel(pair, pairRequest.getFirst(), pairRequest.getSecond());
    }

    /**
     * 执行BOM 分阶
     *
     * @param pair pair
     * @param list list
     * @param bs bs
     */
    private void executeBomLevel(Pair<String, String> pair, List<AsyncBomDTO> list, BsAsyncDataDTO bs) {
        String errorMsg = Constant.OK_STATUS;
        String status = MpConstant.AsyncConstant.SUCCESS;
        List<String> collectRun = new LinkedList<>();
        Map<String, Object> paramsMap = new HashMap<>();
        // 先根据料单代码+批次对集合去重
        ArrayList<AsyncBomDTO> targetBomList = list.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(o -> o.getItemNo() + o.getProdPlanId()))),
                        ArrayList::new)
        );
        // 根据制造料单获取原始料单，设置对应属性
        this.queryProdBomThenSetProdProperties(targetBomList);
        // 获取原始料单代码
        List<String> targetList = targetBomList.stream().map(AsyncBomDTO::getItemNo).distinct().collect(Collectors.toList());
        // 已经导 CAD 的料单
        List<String> hasCadItemNos = bomHeaderRepository.getHasCadProductCode(targetList);
        targetList.retainAll(hasCadItemNos);
        try {
            // 获取锁
            collectRun.addAll(this.getRedisLocks(targetList));
            if (CollectionUtils.isNotEmpty(collectRun)) {
                Map<String, List<AsyncBomDTO>> itemGroup = targetBomList.stream().collect(Collectors.groupingBy(AsyncBomDTO::getItemNo));
                for (Map.Entry<String, List<AsyncBomDTO>> entry : itemGroup.entrySet()) {
                    List<AsyncBomDTO> value = entry.getValue();
                    // 可执行CAD
                    if (collectRun.contains(entry.getKey())) {
                        // 执行原始料单BOM 分阶
                        BsBomHierarchicalHead head = this.singCircleBom(pair, value.get(0));
                        this.executeProdBom(head, value);
                    }
                }
            }
        } finally {
            collectRun.forEach(item -> redisTemplate.delete(String.format(Constant.BOM_RUN_LIST, item)));
            // 更新异步表
            bs.setRetCode(errorMsg + JSON.toJSONString(paramsMap));
            bs.setLastUpdateDate(new Date());
            bs.setStatus(status);
            bsAsyncDataRespository.updateDataById(bs);
        }
    }

    /**
     * 执行制造BOM 分阶
     * @param head bom分阶头明细
     * @param value 待运行数据
     */
    private void executeProdBom(BsBomHierarchicalHead head, List<AsyncBomDTO> value) {
        // 原始BOM 异常则全部异常
        if (CollectionUtils.isEmpty(head.getDetails())) {
            List<String> errorList = new LinkedList<>();
            for (AsyncBomDTO item : value) {
                errorList.add(item.getItemNo());
                // 制造BOM
                if(StringUtils.isNotBlank(item.getProductCode())){
                    errorList.add(item.getProductCode());
                }
            }
            commonTransactionalService.insertBomLevelData(new LinkedList<>(), new LinkedList<>(), errorList);
        } else {
            // 保存原始BOM 分阶
            commonTransactionalService.insertBomLevelData(Collections.singletonList(head), head.getDetails(), new LinkedList<>());
            List<AsyncBomDTO> originalList = value.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getProductCode()))
                    .collect(Collectors.toList());
            for (AsyncBomDTO asyncBomDTO : originalList) {
                BsBomHierarchicalHead head1 = commonService.generatedProdBomHierarchical(head, asyncBomDTO.getProdPlanId());
                if (Objects.nonNull(head1)) {
                    // 保存制造BOM
                    commonTransactionalService.insertBomLevelData(Collections.singletonList(head1),head1.getDetails(), new LinkedList<>());
                }
            }
        }
    }

    /* Started by AICoder, pid:y312ffc3d17f7831410c0acf6070f84fbac6c58e */

    /**
     * 根据条件查询制造BOM 并设置相关制造BOM 属性
     *
     * @param list 料单集合
     */
    private void queryProdBomThenSetProdProperties(List<AsyncBomDTO> list) {
        // 1.优先按批次查询制造BOM
        this.queryProdByPlanId(list);
        // 2.分批查询料单编号对应的制造BOM原始料单
        this.queryAndSetProdByBom(list);
    }

    /**
     * 通过料单查询制造BOM
     * @param list BOM 分阶
     */
    private void queryAndSetProdByBom(List<AsyncBomDTO> list) {
        List<AsyncBomDTO> bomAsList = list.stream()
                .filter(item -> StringUtils.isBlank(item.getProdPlanId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bomAsList)) {
            List<String> bomList = bomAsList.stream()
                    .map(AsyncBomDTO::getItemNo).distinct()
                    .collect(Collectors.toList());
            List<BProdBomHeaderDTO> prodResultList = new LinkedList<>();
            List<List<String>> splitList = CommonUtils.splitList(bomList, Constant.INT_100);
            for (List<String> items : splitList) {
                List<BProdBomHeaderDTO> changList = bProdBomHeaderRepository.queryBProdBomListBatch(null, items);
                if (CollectionUtils.isNotEmpty(changList)) {
                    prodResultList.addAll(changList);
                }
            }
            Map<String, BProdBomHeaderDTO> bomMap = prodResultList.stream()
                    .collect(Collectors.toMap(BProdBomHeaderDTO::getProductCode, v -> v, (k1, k2) -> k1));
            for (AsyncBomDTO asyncBomDTO : bomAsList) {
                BProdBomHeaderDTO headerDTO = bomMap.get(asyncBomDTO.getItemNo());
                if (Objects.nonNull(headerDTO)) {
                    asyncBomDTO.setProductCode(headerDTO.getProductCode());
                    asyncBomDTO.setItemNo(headerDTO.getOriginalProductCode());
                    asyncBomDTO.setProdPlanId(headerDTO.getProdplanId());
                }
            }
        }
    }

    /**
     * 根据批次查询制造bom 数据
     * @param list bom
     */
    private void queryProdByPlanId(List<AsyncBomDTO> list) {
        List<AsyncBomDTO> prodList = list.stream()
                .filter(item -> StringUtils.isNotBlank(item.getProdPlanId()))
                .collect(Collectors.toList());
        List<String> sourceList = prodList.stream().map(AsyncBomDTO::getProdPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sourceList)) {
            List<BProdBomHeaderDTO> prodResultList = new LinkedList<>();
            List<List<String>> splitList = CommonUtils.splitList(sourceList, Constant.INT_100);
            for (List<String> items : splitList) {
                List<BProdBomHeaderDTO> changList = bProdBomHeaderRepository.queryBProdBomListBatch(items, null);
                if (CollectionUtils.isNotEmpty(changList)) {
                    prodResultList.addAll(changList);
                }
            }
            // 设置属性
            Map<String, BProdBomHeaderDTO> prodMap = prodResultList.stream()
                    .collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, v -> v,
                            (k1, k2) -> k1));
            for (AsyncBomDTO item : prodList) {
                BProdBomHeaderDTO prodBomHeaderDTO = prodMap.get(item.getProdPlanId());
                if (Objects.nonNull(prodBomHeaderDTO)) {
                    item.setItemNo(prodBomHeaderDTO.getOriginalProductCode());
                    item.setProductCode(prodBomHeaderDTO.getProductCode());
                }
            }
        }
    }

    /* Ended by AICoder, pid:y312ffc3d17f7831410c0acf6070f84fbac6c58e */

    /**
     * 料单运行加锁
     * @param productList 料单集合
     * @return 加锁成功料单
     */
    private List<String> getRedisLocks(List<String> productList) {
        List<String> lockKeyList = new LinkedList<>();
        if (CollectionUtils.isEmpty(productList)) {
            return productList;
        }
        // 自旋锁
        for (String productCode : productList) {
            String redisKey = String.format(Constant.BOM_RUN_LIST, productCode);
            RedisLock redisLock = new RedisLock(redisKey, redisKey.getBytes(), Constant.INT_3600);
            if (redisLock.lock(bomRunWaiteMaxSecond)) {
                lockKeyList.add(productCode);
            }
        }
        return lockKeyList;
    }

    /**
     * 运行BOM 分阶 异步逻辑 批量执行BOM 分阶
     *
     * @param pair 工号信息
     * @param list BOM 分阶数据集合
     * @param bs   异步表信息
     */
    @Override
    public void runBomCode(Pair<String, String> pair, List<AsyncBomDTO> list, BsAsyncDataDTO bs) {
        this.executeBomLevel(pair, list, bs);
    }

    /**
     * 单个执行BOM 分阶
     *
     * @param pair        更新人
     * @param asyncBomDTO BOM 分阶数据
     */
    private BsBomHierarchicalHead singCircleBom(Pair<String, String> pair, AsyncBomDTO asyncBomDTO) {
        // 1. 执行BOM 分阶拆分
        List<BsPremanuItemInfoDTO> splitBomDetails = this.execBomSplit(asyncBomDTO);
        BsBomHierarchicalHead head = new BsBomHierarchicalHead();
        head.setCreateBy(pair.getSecond());
        head.setLastUpdatedBy(pair.getSecond());
        head.setFactoryId(new BigDecimal(pair.getFirst()));
        head.setHeadId(idGenerator.snowFlakeIdStr());
        head.setBomCode(asyncBomDTO.getItemNo());
        head.setBomName(asyncBomDTO.getItemName());
        // 前加工及装焊 BOM 分阶集合
        List<BsBomHierarchicalDetail> preDetails = new LinkedList<>();
        // 配送工序为SMT配送，非前加工数据
        List<BsBomHierarchicalDetail> smtList = new LinkedList<>();
        splitBomDetails.forEach(item -> {
            BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
            BeanUtils.copyProperties(item, detail);
            detail.setHeadId(head.getHeadId());
            detail.setId(idGenerator.snowFlakeIdStr());
            detail.setIsPreManu(item.getIsPreManu());
            detail.setIsSmt(item.getIsSmt());
            detail.setDeliveryProcessSplitBefore(item.getDeliveryProcess());
            // 配送工序为SMT配送的数据作为贴片段物料，通过料单代码+主工序（SMT-A 、SMT-B）查询位号表得到CAD解析记录
            if (Constant.SMT_DELIVERY.equals(detail.getDeliveryProcess()) && StringUtils.isBlank(detail.getTypeCode())) {
                smtList.add(detail);
            } else {
                preDetails.add(detail);
            }
        });

        // 3. 根据b_pcblocationdetail 拆分贴片SMT-A,SMT-B数量表
        this.splitCadInfo(preDetails, smtList);
        preDetails.addAll(smtList);
        // 4. ABC 分类赋值,备注字段切割
        this.addDefaultAbcType(preDetails, pair.getFirst());
        // 5. 对比BOM 分阶用量和标准用量数据是否一致， 否则不产生调拨记录异常code
        List<String> errorBomList = this.comparableItemNoUsage(preDetails);
        if (CollectionUtils.isNotEmpty(errorBomList)) {
            preDetails.clear();
        }
        head.setDetails(preDetails);
        return head;
    }

    /**
     * BOm 数据和 BOM 清单数据对不， 数量不相等加上错误标识，以免产生调拨
     *
     * @param details 详情信息
     */
    private List<String> comparableItemNoUsage(List<BsBomHierarchicalDetail> details) {
        List<String> bomCodeList = new LinkedList<>();
        // 2. 比较计算bom 分阶 和BOM 用量数据， 获取分阶异常 数据
        this.compareBomQty(details, bomCodeList);
        return bomCodeList;
    }

    /**
     * 比较计算bom 分阶 和BOM 用量数据， 获取分阶异常 数据
     *
     * @param details details
     * @param bomCodeList bomCodeList
     */
    private void compareBomQty(List<BsBomHierarchicalDetail> details, List<String> bomCodeList) {
        // 取出bom 分阶数据
        details.stream().collect(Collectors.groupingBy(BsBomHierarchicalDetail::getBomCode)).forEach((productCode, bsBomHierarchicalDetails) -> {
            // 过滤出前加工物料信息
            List<BsBomHierarchicalDetail> preDetails = bsBomHierarchicalDetails.stream()
                    .filter(boms -> StringUtils.isNotBlank(boms.getTypeCode())).collect(Collectors.toList());
            // 移除前加工物料信息
            bsBomHierarchicalDetails.removeAll(preDetails);
            preDetails.stream().collect(Collectors.groupingBy(pre -> pre.getItemNo() + pre.getTagNum())).forEach((s1, bsBomHierarchicalDetails1) -> {
                BigDecimal minValue =
                        bsBomHierarchicalDetails1.stream().filter(bs -> bs.getSortSeq() != null)
                                .map(BsBomHierarchicalDetail::getSortSeq).min(BigDecimal::compareTo).get();

                List<BsBomHierarchicalDetail> collect1 = bsBomHierarchicalDetails1.stream().filter(bsBom -> bsBom.getSortSeq() != null)
                        .filter(bsBomHierarchicalDetailDTO -> bsBomHierarchicalDetailDTO.getSortSeq().compareTo(minValue) == 0)
                        .collect(Collectors.toList());
                bsBomHierarchicalDetails.addAll(collect1);
            });
            // 异常数据判断
            this.calculatedExceptionBom(bomCodeList, productCode, bsBomHierarchicalDetails);
        });
    }

    /**
     * 计算bom 用量和 bom 分阶用量不相等数据判断
     *
     * @param bomCodeList              异常数据集合
     * @param productCode              料单代码
     * @param bsBomHierarchicalDetails bsBomHierarchicalDetails
     */
    private void calculatedExceptionBom(List<String> bomCodeList, String productCode,
                                        List<BsBomHierarchicalDetail> bsBomHierarchicalDetails) {
        // 计算BOM 分阶总用量信息
        Map<String, BigDecimal> bomCalMap = new HashMap<>();
        bsBomHierarchicalDetails.stream().collect(Collectors.groupingBy(BsBomHierarchicalDetail::getItemNo))
                .forEach((itemCode, bsBomHierarchicalDetails1) -> {
                    bomCalMap.put(itemCode, new BigDecimal(Constant.INT_0));
                    bsBomHierarchicalDetails1.forEach(bsBomHierarchicalDetail -> {
                        BigDecimal bomQty = bsBomHierarchicalDetail.getItemQty() == null
                                ? new BigDecimal(Constant.INT_0) : bsBomHierarchicalDetail.getItemQty();
                        BigDecimal totalQty = bomCalMap.get(itemCode);
                        totalQty = totalQty == null ? new BigDecimal(Constant.INT_0) : totalQty;
                        bomCalMap.put(itemCode, totalQty.add(bomQty));
                    });
                });

        // 取出bom 用量
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        bBomDetailDTO.setProductCode(productCode);
        List<BBomDetailDTO> bomItemList = bBomDetailRepository.getBomItemList(bBomDetailDTO);
        // 1.1 BOM 分阶有效数据为空、bom数据为空
        if (CollectionUtils.isEmpty(bomItemList) || MapUtils.isEmpty(bomCalMap)) {
            bomCodeList.add(productCode);
            return;
        }
        // 1.2 个数不相等
        if (bomItemList.size() != bomCalMap.size()) {
            bomCodeList.add(productCode);
            return;
        }
        // 1.3 个别代码 用量不相等
        this.buildErrorMsg(bomCodeList, productCode, bomCalMap, bomItemList);
    }

    /* Started by AICoder, pid:8a9730537cwb12f14e280accf0b74e261b01d0f4 */
    private void buildErrorMsg(List<String> bomCodeList, String productCode, Map<String, BigDecimal> bomCalMap,
                         List<BBomDetailDTO> bomItemList) {
        List<String> errorList = new LinkedList<>();
        for (BBomDetailDTO bomDetailDTO : bomItemList) {
            BigDecimal bomCalQty = bomCalMap.get(bomDetailDTO.getItemCode());
            if (bomCalQty == null || bomCalQty.compareTo(bomDetailDTO.getUsageCount()) !=0) {
                errorList.add(CommonUtils.getLmbMessage(MessageId.BOM_ITEM_USE_ACCOUNT,
                        new String[]{bomDetailDTO.getItemCode(), String.valueOf(bomCalQty),
                                String.valueOf(bomDetailDTO.getUsageCount())}));
                if (!bomCodeList.contains(productCode)) {
                    bomCodeList.add(productCode);
                }
            }
        }
        if (!errorList.isEmpty()) {
            String context = CommonUtils.getLmbMessage(MessageId.BOM_EXCEPTION_MESSAGE, new String[]{productCode,
                    String.join(", ", errorList)});
            if (StringUtils.isNotBlank(bomExceptionMessageTo)) {
                iCenterRemoteService.sendMessage("BOM error", context, bomExceptionMessageTo);
            }
        }
    }

    /* Ended by AICoder, pid:8a9730537cwb12f14e280accf0b74e261b01d0f4 */

    /**
     * ABC 分类赋值
     *
     * @param smtLevelList
     * @param factoryId
     */
    private void addDefaultAbcType(List<BsBomHierarchicalDetail> smtLevelList, String factoryId) {
        smtLevelList.forEach(bsBomHierarchicalDetail -> {
            // ABC 分类
            bsBomHierarchicalDetail.setAbcType(bsBomHierarchicalDetail.getAbcType() == null ? NumConstant.STRING_A : bsBomHierarchicalDetail.getAbcType());
            // 工厂id 赋值
            bsBomHierarchicalDetail.setFactoryId(new BigDecimal(factoryId));
            String remark = bsBomHierarchicalDetail.getRemark();
            if (StringUtils.isNotBlank(remark) && remark.length() > Constant.INT_1000) {
                bsBomHierarchicalDetail.setRemark(remark.substring(Constant.INT_0, Constant.INT_800));
            }
        });
    }

    /**
     * 拆分SMT 子工序， SMT-A,SMT-B
     *
     * @param preDetails 前加工数据
     * @param smtList    贴片数据
     */
    private void splitCadInfo(List<BsBomHierarchicalDetail> preDetails, List<BsBomHierarchicalDetail> smtList) {
        if (CollectionUtils.isEmpty(smtList)) {
            return;
        }
        List<BsBomHierarchicalDetail> smtListNew = new ArrayList<>();
        // 查询SMT-A.SMT-B 贴片数据 等于SMT 配送的需要拆分
        List<BPcbLocationDetailDTO> itemList =
                bPcbLocationDetailRepository.querySubLevelList(smtList.get(Constant.INT_0).getBomCode());
        if (CollectionUtils.isNotEmpty(itemList)) {
            // CAD 解析根据物料代码进行分组
            Map<String, List<BPcbLocationDetailDTO>> cadDetails
                    = itemList.stream().collect(Collectors.groupingBy(BPcbLocationDetailDTO::getItemCode));
            smtList.forEach(item -> {
                List<BPcbLocationDetailDTO> itemCadDetails =
                        cadDetails.get(item.getItemNo());
                if (CollectionUtils.isEmpty(itemCadDetails)) {
                    smtListNew.add(item);
                } else {
                    // 比较并新增CAD 记录
                    this.compareThenAddNew(smtListNew, itemCadDetails, item, preDetails);
                }
            });
        } else {
            smtListNew.addAll(smtList);
        }
        smtList.clear();
        smtList.addAll(smtListNew);
    }

    /**
     * 比较用量 然后拆分AB 面数据
     *
     * @param smtListNew              新SMT 数组
     * @param itemCadDetails          当前物料 CAD解析位号信息
     * @param bsBomHierarchicalDetail bom分阶明细
     * @param preDetails              前加工数据
     */
    private void compareThenAddNew(List<BsBomHierarchicalDetail> smtListNew, List<BPcbLocationDetailDTO> itemCadDetails,
                                   BsBomHierarchicalDetail bsBomHierarchicalDetail, List<BsBomHierarchicalDetail> preDetails) {
        // 当前物料代码前技工数据
        List<BsBomHierarchicalDetail> preList = this.getItemNoPreList(bsBomHierarchicalDetail, preDetails);
        // 该物料位号总用量
        BigDecimal numSmt = new BigDecimal(0);
        // 移除前加工的站位cad 记录
        List<BPcbLocationDetailDTO> itemPreLocationList = new LinkedList<>();
        for (BPcbLocationDetailDTO bPcbLocationDetailDTO : itemCadDetails) {
            preList.forEach(bsBom -> {
                if (bsBom.getTagNum() == null
                        || Arrays.asList(bsBom.getTagNum().split(Constant.COMMA))
                        .contains(bPcbLocationDetailDTO.getPointLoc())) {
                    itemPreLocationList.add(bPcbLocationDetailDTO);
                }
            });
        }
        // 移除前加工用量
        itemCadDetails.removeAll(itemPreLocationList);
        // 位号用量信息
        for (BPcbLocationDetailDTO bPcbLocationDetailDTO : itemCadDetails) {
            // 前加工用量不计算
            numSmt = numSmt.add(new BigDecimal(bPcbLocationDetailDTO.getUsageCount()));
        }
        // 同一个面的用量叠加
        List<BPcbLocationDetailDTO> bPcbLocationDetailNew = new LinkedList<>();
        // 根据子工序分组
        itemCadDetails.stream().collect(Collectors.groupingBy(BPcbLocationDetailDTO::getCraftSection))
                .forEach((s, cadDetails) -> {
                    BigDecimal use = new BigDecimal(Constant.INT_0);
                    for (BPcbLocationDetailDTO bPcbLocationDetailDTO : cadDetails) {
                        use = use.add(new BigDecimal(bPcbLocationDetailDTO.getUsageCount()));
                    }
                    BPcbLocationDetailDTO bPcbLocationDetailDTO = cadDetails.get(NumConstant.NUM_ZERO);
                    bPcbLocationDetailDTO.setUsageCount(String.valueOf(use));
                    bPcbLocationDetailNew.add(bPcbLocationDetailDTO);
                });
        // 贴片数量等于 CAD 解析记录 则进行拆分，添加记录
        this.calculatedAmount(smtListNew, bsBomHierarchicalDetail, numSmt, bPcbLocationDetailNew);
    }

    /**
     * 获取当前物料的前加工数据
     *
     * @param bsBomHierarchicalDetail
     * @param preDetails
     * @return
     */
    private List<BsBomHierarchicalDetail> getItemNoPreList(BsBomHierarchicalDetail bsBomHierarchicalDetail,
                                                           List<BsBomHierarchicalDetail> preDetails) {
        List<BsBomHierarchicalDetail> preList = new LinkedList<>();
        Map<String, List<BsBomHierarchicalDetail>> collectPre = preDetails.stream()
                .filter(item -> StringUtils.isNotBlank(item.getItemNo()))
                .collect(Collectors.groupingBy(item -> item.getItemNo()));
        if (MapUtils.isNotEmpty(collectPre)) {
            List<BsBomHierarchicalDetail> temp = collectPre.get(bsBomHierarchicalDetail.getItemNo());
            preList.addAll(temp == null ? new LinkedList<>() : temp);
        }
        return preList;
    }

    /**
     * 贴片数量等于 CAD 解析记录 则进行拆分，添加记录
     *
     * @param smtListNew smtListNew
     * @param bsBomHierarchicalDetail bsBomHierarchicalDetail
     * @param numSmt numSmt
     * @param bPcbLocationDetailNew bPcbLocationDetailNew
     */
    private void calculatedAmount(List<BsBomHierarchicalDetail> smtListNew, BsBomHierarchicalDetail bsBomHierarchicalDetail,
                                  BigDecimal numSmt, List<BPcbLocationDetailDTO> bPcbLocationDetailNew) {
        BigDecimal zero = new BigDecimal(Constant.INT_0);
        if (numSmt.compareTo(bsBomHierarchicalDetail.getItemQty()) == 0 && zero.compareTo(numSmt) != Constant.INT_0) {
            bPcbLocationDetailNew.forEach(bPcbLocationDetailDTO -> {
                BsBomHierarchicalDetail newData = new BsBomHierarchicalDetail();
                BeanUtils.copyProperties(bsBomHierarchicalDetail, newData);
                newData.setItemQty(new BigDecimal(bPcbLocationDetailDTO.getUsageCount()));
                newData.setDeliveryProcess(MpConstant.SMT.equals(bPcbLocationDetailDTO.getCraftSection()) ? MpConstant.SMT_DELIVERY : bPcbLocationDetailDTO.getCraftSection());
                newData.setId(UUID.randomUUID().toString());
                smtListNew.add(newData);
            });
        } else {
            smtListNew.add(bsBomHierarchicalDetail);
        }
    }

    /**
     * 制造BOM 分阶
     *
     * @param bomData
     * @return
     */
    private List<BsPremanuItemInfoDTO> execProdBomSplit(AsyncBomDTO bomData) {
        // 1.获取料单代码对应的基础信息
        BProdBomHeaderDTO bProdBomHeader = new BProdBomHeaderDTO();
        bProdBomHeader.setProductCode(bomData.getItemNo());
        // 制造BOM基础信息
        List<BProdBomHeaderDTO> bomHeaderDTOS = bProdBomHeaderRepository.queryBProdBomHeader(bProdBomHeader);
        if (CollectionUtils.isEmpty(bomHeaderDTOS)) {
            return new LinkedList<>();
        }
        BProdBomHeaderDTO bProdBomHeaderDTO = bomHeaderDTOS.get(Constant.INT_0);
        BBomHeader bomHeader = new BBomHeader();
        BeanUtils.copyProperties(bProdBomHeaderDTO, bomHeader);
        bomData.setItemName(bomHeader.getChiDesc());
        // 制造BOM详情信息
        List<BProdBomDetailDTO> mBomDetailList = bProdBomDetailRepository.getMBomDetailList(null, bomHeader.getProductCode(), null);
        if (CollectionUtils.isEmpty(mBomDetailList)) {
            return new LinkedList<>();
        }
        List<BBomDetail> bBomDetails = new ArrayList<>();
        for (BProdBomDetailDTO bProdBomDetailDTO : mBomDetailList) {
            BBomDetail bBomDetail = new BBomDetail();
            BeanUtils.copyProperties(bProdBomDetailDTO, bBomDetail);
            bBomDetails.add(bBomDetail);
        }
        return executeBomDetails(bomHeader, bBomDetails);
    }

    /**
     * BOM 分阶
     *
     * @param bomData
     * @return
     */
    @Override
    public List<BsPremanuItemInfoDTO> execBomSplit(AsyncBomDTO bomData) {
        // 1.获取料单代码对应的基础信息
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("productCode", bomData.getItemNo());
        List<BBomHeader> headerList = bomHeaderRepository.getList(queryMap);
        if (CollectionUtils.isEmpty(headerList)) {
            return new LinkedList<>();
        }
        BBomHeader bomHeader = headerList.get(Constant.INT_0);
        bomData.setItemName(bomHeader.getChiDesc());
        // 2. 获取 BOM 详情
        List<BBomDetail> bBomDetails = bBomDetailRepository.selectDetailsByProductCode(bomHeader.getProductCode());
        if (CollectionUtils.isEmpty(bBomDetails)) {
            return new LinkedList<>();
        }
        return executeBomDetails(bomHeader,bBomDetails);
    }

    private List<BsPremanuItemInfoDTO> executeBomDetails(BBomHeader bomHeader, List<BBomDetail> bBomDetails) {
        // 4. 获取物料代码基础信息
        List<BsItemInfo> itemList = new LinkedList<>();
        // 4.1 获取物料基础信息
        this.queryItemInfo(bBomDetails, itemList);

        // 物料明细信息
        Map<String, List<BsItemInfo>> itemDetailsMap = itemList.stream().collect(Collectors.groupingBy(BsItemInfo::getItemNo));
        // 5. 组装料单级前加工数据信息
        List<BsPremanuItemInfoDTO> resultList = new LinkedList<>();
        this.queryBomPreInfo(bomHeader, bBomDetails, itemDetailsMap, resultList);

        // 6. 过滤掉已经产生料单级前加工 的物料代码
        List<String> preBomItemNos = resultList.stream().map(BsPremanuItemInfoDTO::getItemNo).distinct().collect(Collectors.toList());
        // 需要查询物料级前加工的BOM 明细数据
        List<BBomDetail> remainBomDetails = bBomDetails.stream()
                .filter(item -> !preBomItemNos.contains(item.getItemCode()))
                .collect(Collectors.toList());

        // 7. 获取物料级前加工数据, 物料级前加工维护， 全部物料去前加工，用量为bom 的用量
        this.queryItemPreInfo(remainBomDetails, resultList, bomHeader);

        // 8. 剔除料单级物料级 前加工数据，剩下的物料全部去贴片
        this.builderSmtData(bomHeader, itemDetailsMap, resultList, remainBomDetails);


        // 9 赋值abc 分类 和 style
        resultList.forEach(item -> {
            List<BsItemInfo> itemInfoList = itemDetailsMap.get(item.getItemNo());
            item.setBomName(bomHeader.getChiDesc());
            if (CollectionUtils.isNotEmpty(itemInfoList)) {
                BsItemInfo itemInfo = itemInfoList.get(Constant.INT_0);
                item.setStyle(itemInfo.getStyle());
                item.setItemName(itemInfo.getItemName());
                item.setAbcType(itemInfo.getAbcType());
            }
        });
        return resultList;
    }

    /**
     * 组装smt 物料 BOM 分阶信息
     *
     * @param bomInfo          料单信息
     * @param itemDetailsMap   物料基础信息
     * @param resultList       BOM 分阶结果
     * @param remainBomDetails 去除 料单级前加工信息的bom 详情
     */
    private void builderSmtData(BBomHeader bomInfo, Map<String, List<BsItemInfo>> itemDetailsMap,
                                List<BsPremanuItemInfoDTO> resultList, List<BBomDetail> remainBomDetails) {
        if (CollectionUtils.isEmpty(remainBomDetails)) {
            return;
        }
        List<String> preItems =
                resultList.stream().map(BsPremanuItemInfoDTO::getItemNo).distinct().collect(Collectors.toList());
        List<BBomDetail> smtItemList =
                remainBomDetails.stream().filter(item -> !preItems.contains(item.getItemCode())).collect(Collectors.toList());

        smtItemList.forEach(item -> this.buildItemNoMessage(bomInfo, itemDetailsMap, resultList, item));
    }

    /**
     * 组装数据
     *
     * @param bomInfo
     * @param itemDetailsMap
     * @param resultList
     * @param item
     */
    private void buildItemNoMessage(BBomHeader bomInfo, Map<String, List<BsItemInfo>> itemDetailsMap, List<BsPremanuItemInfoDTO> resultList, BBomDetail item) {
        BsPremanuItemInfoDTO addEntity = new BsPremanuItemInfoDTO();
        BeanUtils.copyProperties(item, addEntity);
        addEntity.setItemNo(item.getItemCode());
        addEntity.setItemQty(item.getUsageCount());
        List<BsItemInfo> itemInfoList = itemDetailsMap.get(item.getItemCode());
        addEntity.setBomCode(bomInfo.getProductCode());
        addEntity.setBomName(bomInfo.getChiDesc());
        if (CollectionUtils.isNotEmpty(itemInfoList)) {
            BsItemInfo itemInfo = itemInfoList.get(Constant.INT_0);
            addEntity.setAbcType(itemInfo.getAbcType());
            addEntity.setStyle(itemInfo.getStyle());
            addEntity.setItemName(itemInfo.getItemName());
            addEntity.setCreateBy(itemInfo.getCreateBy());
            addEntity.setCreateDate(itemInfo.getCreateDate());
            addEntity.setLastUpdatedBy(itemInfo.getLastUpdatedBy());
            addEntity.setLastUpdatedDate(itemInfo.getLastUpdatedDate());
            if (null != itemInfo.getIsSmt()) {
                if (Constant.LEVEL_CODE.equals(itemInfo.getIsSmt().toString())) {
                    addEntity.setDeliveryProcess(Constant.DIP_DELIVERY);
                } else if (Constant.NUMBER_ONE.equals(itemInfo.getIsSmt().toString())) {
                    addEntity.setDeliveryProcess(Constant.SMT_DELIVERY);
                }
            }
        }
        if (StringUtils.isBlank(addEntity.getSubLevel())) {
            addEntity.setSubLevel(Constant.SMT_LEVEL);
        }
        // 9.1. 物料代码是否以“040”开头  不是前加工的 如果是则前加工为“PCB贴标签”，配送工序为“SMT配送”，前加工去向“综合配送组”，用量=bom用量，分阶来源“物料级”
        if (addEntity.getItemNo().startsWith(Constant.ITEM_CODE_040)) {
            addEntity.setIsPreManu(Constant.FLAG_Y);
            addEntity.setSubLevel(Constant.ITEM_LEVEL);
            addEntity.setTypeCode("PCB");
            addEntity.setTypeName(Constant.TYPE_PCB_NAME);
            addEntity.setTraceName(Constant.TRACE_NAME);
            addEntity.setTraceCode("PCB1");
            addEntity.setDeliveryProcess(Constant.SMT_DELIVERY);
            addEntity.setSortSeq(new BigDecimal(1));
            item.setLastUpdatedBy(Constant.SYSTEM_040);
            item.setLastUpdatedDate(new Date());
        }
        resultList.add(addEntity);
    }

    /**
     * 查询物料级前加工数据
     *
     * @param remainBomDetails 剩下的BOM 数据
     * @param resultList       BOM 分阶数据
     * @param bomHeader        料单信息
     */
    private void queryItemPreInfo(List<BBomDetail> remainBomDetails, List<BsPremanuItemInfoDTO> resultList, BBomHeader bomHeader) {
        if (CollectionUtils.isEmpty(remainBomDetails)) {
            return;
        }
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        dto.setItemNoList(remainBomDetails.stream().map(BBomDetail::getItemCode).collect(Collectors.toList()));
        // 7.1 物料级前加工数据
        List<BsPremanuItemInfo> preItemInfoList = bsPremanuItemInfoRepository.queryPreManuItemInfoNoPage(dto);
        if (CollectionUtils.isEmpty(preItemInfoList)) {
            preItemInfoList = new LinkedList<>();
        }
        // 7.2 根据物料代码分组
        Map<String, List<BsPremanuItemInfo>> preItemList = preItemInfoList.stream()
                .collect(Collectors.groupingBy(BsPremanuItemInfo::getItemNo));
        BigDecimal zero = new BigDecimal(Constant.INT_0);
        for (Map.Entry<String, List<BsPremanuItemInfo>> entry : preItemList.entrySet()) {
            List<BsPremanuItemInfo> value = entry.getValue();
            BBomDetail bBomDetail = remainBomDetails.stream()
                    .filter(item -> item.getItemCode().equals(value.get(Constant.INT_0).getItemNo())).findFirst()
                    .orElse(new BBomDetail());
            // 实际用量
            BigDecimal actualAmount = bBomDetail.getUsageCount() == null ? zero : bBomDetail.getUsageCount();
            value.forEach(item -> item.setItemQty(actualAmount));
        }
        preItemInfoList.forEach(item -> {
            BsPremanuItemInfoDTO newItem = new BsPremanuItemInfoDTO();
            BeanUtils.copyProperties(item, newItem);
            newItem.setBomCode(bomHeader.getProductCode());
            newItem.setBomName(bomHeader.getChiDesc());
            newItem.setSubLevel(Constant.ITEM_LEVEL);
            resultList.add(newItem);
        });
    }

    /**
     * 获取物料基础信息
     *
     * @param listDetail  bom 明细信息
     * @param itemList    物料基础信息
     */
    private void queryItemInfo(List<BBomDetail> listDetail, List<BsItemInfo> itemList) {
        List<String> itemNoList = listDetail.stream().map(BBomDetail::getItemCode)
                .distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(itemNoList, Constant.INT_100);
        for (List<String> items : splitList) {
            BsItemInfo itemInfo = new BsItemInfo();
            itemInfo.setItemCodeList(items);
            List<BsItemInfo> infoList = bsItemInfoRepository.getInfoList(itemInfo);
            itemList.addAll(infoList);
        }
    }

    /**
     * 处理料单级前加工信息
     *
     * @param bomInfo        料单代码信息
     * @param listDetail     BOM 数据详情
     * @param itemDetailsMap 物料基础信息
     */
    private void queryBomPreInfo(BBomHeader bomInfo, List<BBomDetail> listDetail,
                                 Map<String, List<BsItemInfo>> itemDetailsMap, List<BsPremanuItemInfoDTO> resultList) {
        // 5. 首先查询料单级前加工信息
        List<BsPremanuBomInfo> preBomDetails =
                bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(bomInfo.getProductCode());
        if (CollectionUtils.isEmpty(preBomDetails)) {
            return;
        }
        List<String> itemList = listDetail.stream().map(BBomDetail::getItemCode).collect(Collectors.toList());
        preBomDetails = preBomDetails.stream().filter(item -> itemList.contains(item.getItemNo())).collect(Collectors.toList());
        // 5.2 map记录每个itemNo的usageCount总数，若总数小于detail中的usageCount，需新增一条是否贴片数据
        preBomDetails.forEach(item -> item.setSubLevel(Constant.BOM_LEVEL));
        // 5.3 根据物料代码分组
        Map<String, List<BsPremanuBomInfo>> preCollectMap = preBomDetails.stream()
                .collect(Collectors.groupingBy(BsPremanuBomInfo::getItemNo));
        List<BsPremanuBomInfo> preBomDetailAll = new LinkedList<>();
        for (Map.Entry<String, List<BsPremanuBomInfo>> entry : preCollectMap.entrySet()) {
            List<BsPremanuBomInfo> value = entry.getValue();

            BBomDetail bBomDetail = listDetail.stream()
                    .filter(item -> item.getItemCode().equals(value.get(Constant.INT_0).getItemNo())).findFirst()
                    .orElse(new BBomDetail());
            // 获取同代码多前加工 最小排序,为改物料代码的标准用量
            List<BsPremanuBomInfo> collectSeq = value.stream()
                    .filter(item -> item.getSortSeq() != null).collect(Collectors.toList());
            /// 递增排序
            List<BsPremanuBomInfo> collect = collectSeq.stream()
                    .sorted(Comparator.comparing(BsPremanuBomInfo::getSortSeq))
                    .collect(Collectors.toList());
            // 默认排序 1
            BigDecimal sortOne = new BigDecimal(Constant.INT_1);
            if (CollectionUtils.isNotEmpty(collect)) {
                sortOne = collect.get(Constant.INT_0).getSortSeq();
            }
            // 实际用量
            BigDecimal actualAmount = bBomDetail.getUsageCount() == null ? new BigDecimal(Constant.INT_0) :
                    bBomDetail.getUsageCount();
            BigDecimal usageAmount = this.getBigDecimal(preBomDetailAll, value, bBomDetail, sortOne);
            // 如果用量小于标准用量需要添加是否贴片信息
            if (actualAmount.compareTo(usageAmount) > Constant.INT_0) {
                this.addNewSmtData(bomInfo, itemDetailsMap, preBomDetailAll, actualAmount.subtract(usageAmount), entry);
            }
        }
        preBomDetailAll.forEach(item -> {
            BsPremanuItemInfoDTO newItem = new BsPremanuItemInfoDTO();
            BeanUtils.copyProperties(item, newItem);
            resultList.add(newItem);
        });
    }

    /**
     * 获取用量信息
     *
     * @param preBomDetailAll
     * @param value
     * @param bBomDetail
     * @param sortOne
     * @return
     */
    private BigDecimal getBigDecimal(List<BsPremanuBomInfo> preBomDetailAll, List<BsPremanuBomInfo> value, BBomDetail bBomDetail, BigDecimal sortOne) {
        // 前加工用量
        BigDecimal usageAmount = new BigDecimal(Constant.INT_0);
        BigDecimal zero = new BigDecimal(Constant.INT_0);
        for (BsPremanuBomInfo item : value) {
            // 位号不为空的前加工数据
            if (StringUtils.isNotBlank(item.getTagNum())) {
                String[] split = item.getTagNum().split(Constant.COMMA);
                item.setItemQty(new BigDecimal(split.length));
            } else {
                item.setItemQty(bBomDetail.getUsageCount() == null ? zero : bBomDetail.getUsageCount());
            }
            preBomDetailAll.add(item);
            // 排序为 1
            if (sortOne.compareTo(item.getSortSeq()) == Constant.INT_0) {
                usageAmount = usageAmount.add(item.getItemQty());
            }
        }
        return usageAmount;
    }

    /**
     * 料单级前加工增加新的SMT 用量数据
     *
     * @param bomInfo         料单信息
     * @param itemDetailsMap  物料详细信息
     * @param preBomDetailAll 料单级前加工数据集合
     * @param usageAmount     用量
     * @param entry           物料料单级前加工信息
     */
    private void addNewSmtData(BBomHeader bomInfo, Map<String, List<BsItemInfo>> itemDetailsMap,
                               List<BsPremanuBomInfo> preBomDetailAll, BigDecimal usageAmount,
                               Map.Entry<String, List<BsPremanuBomInfo>> entry) {
        BsPremanuBomInfo addItem = new BsPremanuBomInfo();
        List<BsItemInfo> itemList = itemDetailsMap.get(entry.getKey());
        if (CollectionUtils.isNotEmpty(itemList)) {
            BsItemInfo itemInfo = itemList.get(Constant.INT_0);
            addItem.setAbcType(itemInfo.getAbcType());
            addItem.setStyle(itemInfo.getStyle());
            addItem.setItemName(itemInfo.getItemName());
            addItem.setRemark(itemInfo.getRemark());
            if (null != itemInfo.getIsSmt()) {
                if (Constant.LEVEL_CODE.equals(String.valueOf(itemInfo.getIsSmt()))) {
                    addItem.setDeliveryProcess(Constant.DIP_DELIVERY);
                } else if (Constant.NUMBER_ONE.equals(String.valueOf(itemInfo.getIsSmt()))) {
                    addItem.setDeliveryProcess(Constant.SMT_DELIVERY);
                }
            }
        }
        // 获取物料配送工序信息
        this.searcherBomPreDelivery(entry, addItem);
        addItem.setItemQty(usageAmount);
        addItem.setItemNo(entry.getKey());
        addItem.setBomCode(bomInfo.getProductCode());
        addItem.setBomName(bomInfo.getChiDesc());
        addItem.setSubLevel(Constant.SMT_LEVEL);
        preBomDetailAll.add(addItem);
    }

    /**
     * 取前加工 创建时间最早的 一条的配送去向
     *
     * @param entry   料单级前加工集合
     * @param addItem 贴片数据
     */
    private void searcherBomPreDelivery(Map.Entry<String, List<BsPremanuBomInfo>> entry,
                                        BsPremanuBomInfo addItem) {
        List<BsPremanuBomInfo> collect = entry.getValue().stream()
                .sorted(Comparator.comparing(BsPremanuBomInfo::getCreateDate))
                .collect(Collectors.toList());
        addItem.setDeliveryProcess(collect.get(Constant.INT_0).getDeliveryProcess());
    }

    /**
     * 执行BOM 分级操作 不保存数据库
     *
     * @param productCode    料单代码
     * @param secondConfirm
     * @param factoryEmpPair 工厂id+工号
     * @return BOM 分阶信息
     */
    @Override
    public BsBomHierarchicalHead doSubLevelNoPcb(String productCode, Boolean secondConfirm, Pair<String, String> factoryEmpPair) {
        if (StringUtils.isBlank(productCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_IS_EMPTY);
        }
        // 1. 查询中心工厂 b_bom_header 表字段 INCLUDE_PCB_FPC,包含pcb 不能执行
        BProdBomHeaderDTO bomHeaderDTO = this.checkIncludePcbProperties(productCode,secondConfirm);
        // 2. 执行BOM 分阶计算
        AsyncBomDTO asyncBomDTO = new AsyncBomDTO();
        asyncBomDTO.setItemNo(bomHeaderDTO.getOriginalProductCode());
        asyncBomDTO.setProdPlanId(bomHeaderDTO.getProdplanId());
        BsBomHierarchicalHead head = this.singCircleBom(factoryEmpPair, asyncBomDTO);
        BsBomHierarchicalHead prodHead = commonService.generatedProdBomHierarchical(head, bomHeaderDTO.getProdplanId());
        if (Objects.nonNull(prodHead)) {
            // 存在则去制造BOM 分阶数据
            head = prodHead;
        }
        Date date = new Date();
        head.getDetails().forEach(item -> item.setBomRunDate(date));
        return head;
    }

    /**
     * 校验料单包含PCB
     *
     * @param productCode   料单代码
     * @param secondConfirm
     * @return BProdBomHeaderDTO
     */
    private BProdBomHeaderDTO checkIncludePcbProperties(String productCode, Boolean secondConfirm) {
        // 先查询制造bom表获取对应的原始料单
        List<BProdBomHeaderDTO> prodBomHeaderDTOList = bProdBomChangeDetailRepository.selectOriginalByProductCode(productCode);
        BProdBomHeaderDTO prodBomHeaderDTO = null;
        if (CollectionUtils.isNotEmpty(prodBomHeaderDTOList)) {
            prodBomHeaderDTO = prodBomHeaderDTOList.get(Constant.INT_0);
        }
        if (Objects.isNull(prodBomHeaderDTO)) {
            prodBomHeaderDTO = new BProdBomHeaderDTO();
            prodBomHeaderDTO.setOriginalProductCode(productCode);
        }
        List<String> productCodeList = new LinkedList<>();
        productCodeList.add(prodBomHeaderDTO.getOriginalProductCode());
        List<BBomHeader> headerList = bomHeaderRepository.selectBBomHeaderByProductCodeList(productCodeList);
        if (CollectionUtils.isEmpty(headerList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST);
        }
        // 1.1 不是无pcb 料单不能执行
        BBomHeader bomHeader = headerList.get(0);
        Integer includePcbFpc = bomHeader.getIncludePcbFpc();
        if ((Objects.isNull(includePcbFpc) || includePcbFpc != Constant.INT_0) && !secondConfirm) {
            // 包含PCB 不能执行该操作
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_INCLUDE_PCB,
                    new Object[]{productCode});
        }
        return prodBomHeaderDTO;
    }

    /**
     * 保存BOM 分阶数据
     *
     * @param head BOM 分阶数据
     */
    @Override
    @RedisDistributedLockAnnotation(lockFailMsgZh = "当前料单正在执行BOM分阶请稍后再试", redisPrefix = "factory:bom:run", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "head", propertiesString = "bomCode")}, lockFailMsgEn = "The BOM is being divided. Please try again later.")
    @Transactional(rollbackFor = Exception.class)
    public void saveBomLevelData(BsBomHierarchicalHead head) {
        if (CollectionUtils.isEmpty(head.getDetails())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BOM_DETAILS_DATA);
        }
        // 1.校验数据
        BProdBomHeaderDTO headerDTO = this.checkIncludePcbProperties(head.getBomCode(), true);
        // 2. 保存BOM 分阶数据
        commonTransactionalService.insertBomLevelData(Arrays.asList(head), head.getDetails(), new LinkedList<>());
        // 3. 更新标识bom_header
        bomHeaderRepository.updateIncludePcbInfo(headerDTO.getOriginalProductCode(), head.getCreateBy());
    }

    /**
     * 根据料单代码批量获取BOM分阶信息
     *
     * @param detailList detailList
     * @return BOM 分阶信息
     */
    @Override
    public List<BsBomHierarchicalDetail> selectSubLevelBatch(List<BsBomHierarchicalDetail> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_IS_EMPTY);
        }
        // 制造BOM料单代码替换
        prodBomProductCodeSlipt(detailList);
        // 根据bom分组,存在形同bom则value取第一个
        Map<String, BsBomHierarchicalDetail> bomCodeMap = detailList.stream().collect(Collectors.toMap(BsBomHierarchicalDetail::getBomCode, a -> a, (k1, k2) -> k1));
        List<BsBomHierarchicalDetail> preDetails = new ArrayList<>();
        Map<String, List<BsBomHierarchicalDetail>> bomMap = new HashMap<>();
        // 1.调用现有方法执行bom分阶
        for (Map.Entry<String, BsBomHierarchicalDetail> entry : bomCodeMap.entrySet()) {
            String bomCode = entry.getKey();
            BsBomHierarchicalDetail paramDetail = entry.getValue();
            List<BsBomHierarchicalDetail> preDetailList = new ArrayList<>();
            AsyncBomDTO asyncBomDTO = new AsyncBomDTO();
            asyncBomDTO.setItemNo(bomCode);
            List<BsPremanuItemInfoDTO> splitBomDetails;
            if (bomCode.contains(Constant.EMAIL_SPLIT)) {
                // 制造BOM
                splitBomDetails = this.execProdBomSplit(asyncBomDTO);
            } else {
                splitBomDetails = this.execBomSplit(asyncBomDTO);
            }
            splitBomDetails.forEach(item -> {
                BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
                BeanUtils.copyProperties(item, detail);
                // 若includeItemLevelAndTypeCodeEnpty为false则剔除分阶来源为物料级且前加工类型为空的数据
                if (paramDetail.isIncludeItemLevelAndTypeCodeEmpty() || !Constant.ITEM_LEVEL.equals(detail.getSubLevel()) || StringUtils.isNotEmpty(detail.getTypeCode())) {
                    detail.setDeliveryProcessSplitBefore(detail.getDeliveryProcess());
                    detail.setOriginalProductCode(paramDetail.getOriginalProductCode());
                    preDetailList.add(detail);
                }
            });
            bomMap.put(bomCode, preDetailList);
        }
        Map<String, String> productMap = detailList.stream().filter(e -> StringUtils.isNotEmpty(e.getSourceTask())).collect(Collectors.toMap(BsBomHierarchicalDetail::getSourceTask, BsBomHierarchicalDetail::getBomCode, (k, y) -> k));
        for (Map.Entry<String, String> entry : productMap.entrySet()) {
            List<BsBomHierarchicalDetail> details1 = bomMap.get(entry.getValue());
            if (CollectionUtils.isEmpty(details1)) {
                continue;
            }
            List<BsBomHierarchicalDetail> details = JSON.parseArray(JSON.toJSONString(details1), BsBomHierarchicalDetail.class);
            details.forEach(e -> e.setSourceTask(entry.getKey()));
            preDetails.addAll(details);
        }
        return preDetails;
    }

    /**
     * 制造BOM 料单代码替换
     *
     * @param detailList detailList
     */
    private void prodBomProductCodeSlipt(List<BsBomHierarchicalDetail> detailList) {
        List<String> prodPlanIdList = detailList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getSourceTask()))
                .map(BsBomHierarchicalDetail::getSourceTask).distinct()
                .collect(Collectors.toList());
        // 查询制造BOM料单
        List<BProdBomHeaderDTO> changList = bProdBomHeaderRepository.queryBProdBomListBatch(prodPlanIdList, null);
        if (CollectionUtils.isEmpty(changList)) {
            return;
        }
        Map<String, String> prodPlanMap = changList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId,
                BProdBomHeaderDTO::getProductCode, (k1, k2) -> k1));
        Map<String, String> originalProductCodeMap = changList.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId,
                BProdBomHeaderDTO::getOriginalProductCode, (k1, k2) -> k1));
        for (BsBomHierarchicalDetail bsBomHierarchicalDetail : detailList) {
            if (prodPlanMap.containsKey(bsBomHierarchicalDetail.getSourceTask())) {
                bsBomHierarchicalDetail.setBomCode(prodPlanMap.get(bsBomHierarchicalDetail.getSourceTask()));
            }
            if (originalProductCodeMap.containsKey(bsBomHierarchicalDetail.getSourceTask())) {
                bsBomHierarchicalDetail.setOriginalProductCode(originalProductCodeMap.get(bsBomHierarchicalDetail.getSourceTask()));
            }
        }
    }
}
