package com.zte.application.impl;

import com.zte.application.PushBoardDataProcessService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushBoardDataDetailRepository;
import com.zte.domain.model.PushBoardDataProcessRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.NUM_200;
import static com.zte.common.utils.NumConstant.NUM_ZERO;


@Service("pushBoardDataProcessService")
public class PushBoardDataProcessServiceImpl implements PushBoardDataProcessService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PushBoardDataProcessRepository pushBoardDataProcessRepository;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private PushBoardDataDetailRepository pushBoardDataDetailRepository;

    @Override
    public List<PushBoardDataProcessDTO> getAndInitNeedPushData(List<String> snList, String businessType) {
        if (CollectionUtils.isEmpty(snList) || StringUtils.isBlank(businessType)) {
            return new ArrayList<>();
        }
        List<String> existSnList = pushBoardDataProcessRepository.getExistSn(snList, businessType);
        // 不存在新增
        List<String> notExistSnList = snList.stream().filter(sn -> !existSnList.contains(sn)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistSnList)) {
            List<PushBoardDataProcessDTO> insertList = new ArrayList<>();
            for (String sn : notExistSnList) {
                PushBoardDataProcessDTO pushBoardDataProcessDTO = new PushBoardDataProcessDTO();
                pushBoardDataProcessDTO.setId(idGenerator.snowFlakeIdStr());
                pushBoardDataProcessDTO.setSn(sn);
                pushBoardDataProcessDTO.setBusinessType(businessType);
                insertList.add(pushBoardDataProcessDTO);
            }
            // 分批插入
            List<List<PushBoardDataProcessDTO>> listOfList = CommonUtils.splitList(insertList);
            for (List<PushBoardDataProcessDTO> list : listOfList) {
                pushBoardDataProcessRepository.batchInsert(list);
            }
        }

        return pushBoardDataProcessRepository.getNeedPushSn(snList, businessType);
    }

    @Override
    public void update(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        pushBoardDataProcessRepository.update(pushBoardDataProcessDTO);
    }

    @Override
    public void batchUpdate(List<PushBoardDataProcessDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<List<PushBoardDataProcessDTO>> listOfList = CommonUtils.splitList(list);
        for (List<PushBoardDataProcessDTO> tempList : listOfList) {
            pushBoardDataProcessRepository.batchUpdate(tempList);
        }
    }

    @Override
    public void syncProcessPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        Integer preDays = pushBoardDataProcessDTO.getBeforeDay();
        // 参数校验
        if (preDays <= NUM_ZERO) {
            preDays = NumConstant.NUM_SEVEN;
        }
        // 记录上次同步最后时间,用于增量分页查询
        Date startTime = DateUtil.addDay(-preDays).getTime();
        String sn = "";
        int limit = NUM_200;
        boolean first = true;
        List<PushBoardDataProcessDTO> processList = new ArrayList<>();
        while (first || processList.size() >= limit){
            first = false;
            // 增量查询已推送未回调条码
            processList = pushBoardDataProcessRepository.getNotCallBackList(startTime, sn, limit, pushBoardDataProcessDTO.getBusinessTypeList());
            if (CollectionUtils.isEmpty(processList)) {
                continue;
            }
            // 下次增量查询参数
            PushBoardDataProcessDTO lastData = processList.get(processList.size() - 1);
            startTime = lastData.getLastUpdatedDate();
            sn = lastData.getSn();

            // 根据条码、项目阶段查询B2B调用记录
            List<String> snList = processList.stream().map(PushBoardDataProcessDTO::getSn).distinct().collect(Collectors.toList());
            List<String> projectPhaseList = processList.stream().map(PushBoardDataProcessDTO::getBusinessType).distinct().collect(Collectors.toList());
            List<CustomerDataLogDTO> tradeDataList = tradeDataLogService.getDataBySn(snList, projectPhaseList);
             if (CollectionUtils.isEmpty(tradeDataList)) {
                continue;
            }
            // 按照条码和项目阶段分组
            Map<String, List<CustomerDataLogDTO>> map = tradeDataList.stream()
                    .collect(Collectors.groupingBy(item -> item.getSn() + Constant.UNDER_LINE + item.getProjectPhase()));
            List<PushBoardDataProcessDTO> needUpdateList = new ArrayList<>();
            for (PushBoardDataProcessDTO processDTO : processList) {
                this.checkNeedUpdate(processDTO, map, needUpdateList);
            }
            this.batchUpdate(needUpdateList);
        }
    }

    private void checkNeedUpdate(PushBoardDataProcessDTO processDTO, Map<String, List<CustomerDataLogDTO>> map, List<PushBoardDataProcessDTO> needUpdateList) {
        // 标记是否全部回调成功
        String key = processDTO.getSn() + Constant.UNDER_LINE + processDTO.getBusinessType();
        List<CustomerDataLogDTO> list = map.get(key);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取成功的数据集合
        Map<String, List<CustomerDataLogDTO>> statusSnMap = list.stream()
                .collect(Collectors.groupingBy(CustomerDataLogDTO::getStatus));
        if (Constant.INT_1 >= processDTO.getPushNumber()) {
            // 只推送一条数据，只要成功就成功了
            if (statusSnMap.containsKey(Constant.PUSH_B2B_STATUS.CY)) {
                processDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
                needUpdateList.add(processDTO);
            } else if (statusSnMap.containsKey(Constant.PUSH_B2B_STATUS.CN)) {
                // 处理失败
                processDTO.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
                needUpdateList.add(processDTO);
            }
        } else {
            // 一条数据关联多条推送数据
            List<CustomerDataLogDTO> successList = statusSnMap.getOrDefault(Constant.PUSH_B2B_STATUS.CY,
                    new LinkedList<>());
            List<CustomerDataLogDTO> failList = statusSnMap.getOrDefault(Constant.PUSH_B2B_STATUS.CN,
                    new LinkedList<>());
            if (successList.size() >= processDTO.getPushNumber()) {
                processDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
                needUpdateList.add(processDTO);
            } else if (CollectionUtils.isNotEmpty(failList)) {
                processDTO.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
                needUpdateList.add(processDTO);
            }
        }
    }

    /* Started by AICoder, pid:s1668c1b2df60b91418209bf304f4b3b2a12a01c */
    /**
     * @param list 新增集合
     * @return 新增条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBoardProcessDataBatch(List<PushBoardDataProcessDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Constant.INT_0;
        }
        List<List<PushBoardDataProcessDTO>> splitList = CommonUtils.splitList(list, Constant.INT_100);
        splitList.forEach(item -> pushBoardDataProcessRepository.batchInsert(item));
        return list.size();
    }
    /* Ended by AICoder, pid:s1668c1b2df60b91418209bf304f4b3b2a12a01c */


    /* Started by AICoder, pid:c0d51dd7a8n1317141990b04300dbb510f91fb18 */
    /**
     * 定时新增推送中间表
     *
     * @param pushBoardDataProcessDTO 工序集合
     * @return
     */
    @Override
    @RecordLogAnnotation(value = "scheduledInsertPushProcessTask")
    @RedisDistributedLockAnnotation(redisKey = "scheduledInsertPushProcessTask", redisLockTime = 360000)
    @AlarmAnnotation(alarmName = "insert_push_data_push_error", alarmKey = "1004002", alarmTitle = "推送数据新增异常")
    public Integer scheduledInsertPushProcessTask(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        if (StringUtils.isBlank(pushBoardDataProcessDTO.getCustomerName())
                || CollectionUtils.isEmpty(pushBoardDataProcessDTO.getBusinessTypeList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        int insertSize = 0;
        // 一次最多处理 1800 * rows 数据
        int count = Constant.INT_1800;
        pushBoardDataProcessDTO.setBusinessTypeListSize(pushBoardDataProcessDTO.getBusinessTypeList().size());
        do {
            List<PushBoardDataProcessDTO> insertList = new LinkedList<>();
            List<PushBoardDataDetailDTO> list = pushBoardDataDetailRepository.queryNeedInsertProcessList(pushBoardDataProcessDTO);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            // 组装需要处理的数据
            for (PushBoardDataDetailDTO detailDTO : list) {
                List<String> businessTypeList = pushBoardDataProcessDTO.getBusinessTypeList();
                for (String item : businessTypeList) {
                    PushBoardDataProcessDTO temp = new PushBoardDataProcessDTO();
                    temp.setBusinessType(item);
                    temp.setProdplanId(detailDTO.getProdplanId());
                    temp.setSn(detailDTO.getSn());
                    temp.setId(idGenerator.snowFlakeIdStr());
                    insertList.add(temp);
                }
            }
            List<List<PushBoardDataProcessDTO>> splitList = CommonUtils.splitList(insertList, Constant.INT_100);
            for (List<PushBoardDataProcessDTO> items : splitList) {
                pushBoardDataProcessRepository.batchInsertIfNotExists(items);
            }
            insertSize += list.size();
            count--;
            if (list.size() < pushBoardDataProcessDTO.getRows()) {
                break;
            }
        } while (count > 0);
        return insertSize;
    }
    /* Ended by AICoder, pid:c0d51dd7a8n1317141990b04300dbb510f91fb18 */

    /* Started by AICoder, pid:4258fn371dh0b8a1446e0b4d108ec610d2687733 */
    /**
     * 批量更新数据
     *
     * @param updateList 更细数据集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBoardDataProcessBatch(List<PushBoardDataProcessDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        List<List<PushBoardDataProcessDTO>> splitList = CommonUtils.splitList(updateList, Constant.INT_50);
        splitList.forEach(item -> pushBoardDataProcessRepository.batchUpdate(item));
    }
    /* Ended by AICoder, pid:4258fn371dh0b8a1446e0b4d108ec610d2687733 */

    @Override
    public List<PushBoardDataProcessDTO> getDataBySnAndBusinessType(List<String> snList, List<String> businessTypeList) {
        if (CollectionUtils.isEmpty(snList) || CollectionUtils.isEmpty(businessTypeList)) {
            return new ArrayList<>();
        }
        return pushBoardDataProcessRepository.getDataBySnAndBusinessType(snList, businessTypeList);
    }

    /**
     * 查询推送进程统计数据
     *
     * @param processDTO 查询参数
     * @return 请求
     */
    @Override
    public List<PushBoardDataProcessDTO> queryPushDataList(PushBoardDataProcessDTO processDTO) {
        String factoryId = RequestHeadValidationUtil.validaFactoryId();
        processDTO.setFactoryId(Integer.valueOf(factoryId));
        return pushBoardDataProcessRepository.queryPushDataList(processDTO);
    }

}