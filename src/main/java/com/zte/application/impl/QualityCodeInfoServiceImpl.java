package com.zte.application.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.QualityCodeInfoService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.QualityCodeInfoRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.AliBabaQualityDataDTO;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.QualityCodeInfoDTO;
import com.zte.interfaces.dto.SaveQualityCodeDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.BATCH_SIZE_500;
import static com.zte.common.utils.Constant.SYSTEM;
import static com.zte.common.utils.Constant.ZTEIMES_ALIBABA_TYPE;
import static com.zte.common.utils.NumConstant.NUM_ONE;

/* Started by AICoder, pid:8e752wab2fq26cb141d30b5ed118f02f95971549 */

@Service
public class QualityCodeInfoServiceImpl implements QualityCodeInfoService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private QualityCodeInfoRepository qualityCodeInfoRepository;

    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    protected IdGenerator idGenerator;
    @Autowired
    protected MdsRemoteService mdsRemoteService;
    /**
     * 获取存在质量码的
     * @param qualityCodeInfoList
     * @return
     */
    @Override
    public List<String> getSnExistQualityCode(List<QualityCodeInfoDTO> qualityCodeInfoList) {
        if(CollectionUtils.isEmpty(qualityCodeInfoList) )  {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        return qualityCodeInfoRepository.getSnExistQualityCode(qualityCodeInfoList);
    }

    @Override
    public List<String> filtSnNotExistQualityCode(List<QualityCodeInfoDTO> qualityCodeInfoList) {
        if (CollectionUtils.isEmpty(qualityCodeInfoList)) {
            return new ArrayList<>();
        }
        List<QualityCodeInfoDTO> list = qualityCodeInfoRepository.getQualityCodeBySnAndTaskNo(qualityCodeInfoList);
        List<String> existSnAndTaskNoList = new ArrayList<>();
        for (QualityCodeInfoDTO qualityCodeInfoDTO : list) {
            existSnAndTaskNoList.add(qualityCodeInfoDTO.getSn() + Constant.COMMA + qualityCodeInfoDTO.getTaskNo());
        }

        // 1.筛选质量码为空条码(一个主条码可能对应多个虚拟子条码，每个子条码都需存在对应质量码)
        List<String> result = list.stream().filter(temp -> StringUtils.isBlank(temp.getQualityCode()))
                .map(QualityCodeInfoDTO::getSn).distinct().collect(Collectors.toList());

        // 2.筛选条码在质量码表不存在的条码
        result.addAll(qualityCodeInfoList.stream()
                .filter(temp -> !existSnAndTaskNoList.contains(temp.getSn() + Constant.COMMA + temp.getTaskNo()))
                .map(QualityCodeInfoDTO::getSn)
                .distinct().collect(Collectors.toList()));
        return result;
    }

    /**
     * 根据条码获取阿里质量码
     * @param dto
     * @throws Exception
     */
    @Override
    public void generateQualityCode(SaveQualityCodeDTO dto)throws Exception {
        while (true) {
            List<QualityCodeInfoDTO> qualityCodeInfoDTOList = qualityCodeInfoRepository.getQualityCodeNotGenerated(dto);
            if (CollectionUtils.isEmpty(qualityCodeInfoDTOList)) {
                break;
            }
            dto.setLastUpdatedDate(qualityCodeInfoDTOList.get(qualityCodeInfoDTOList.size() - NUM_ONE).getLastUpdatedDate());
            dto.setLastId(qualityCodeInfoDTOList.get(qualityCodeInfoDTOList.size() - NUM_ONE).getId());
            List<CustomerDataLogDTO> dataList = new ArrayList<>(qualityCodeInfoDTOList.size());
            //组装推送阿里数据
            for (QualityCodeInfoDTO qualityCodeInfoDTO : qualityCodeInfoDTOList) {
                String keyWord = IdUtil.randomUUID();
                CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
                customerDataLogDTO.setId(keyWord);
                customerDataLogDTO.setTaskNo(qualityCodeInfoDTO.getTaskNo());
                customerDataLogDTO.setSn(qualityCodeInfoDTO.getNodeSn());
                customerDataLogDTO.setCustomerName(Constant.ALIBABA);
                customerDataLogDTO.setOrigin(Constant.IMES);
                customerDataLogDTO.setProjectName(Constant.ALIBABA);
                customerDataLogDTO.setProjectPhase(Constant.GET_QUALITY_CODE);
                customerDataLogDTO.setMessageType(Constant.ZTEIMES_ALIBABA_QUALITYCODE_KAFKA);
                customerDataLogDTO.setCreateBy(Constant.IMES);
                customerDataLogDTO.setFactoryId(Integer.parseInt(Constant.FACTORY_ID_CENTER));
                customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
                customerDataLogDTO.setKeywords(qualityCodeInfoDTO.getId());

                Map<String, Object> params = new HashMap<>(16);
                params.put(Constant.SN, qualityCodeInfoDTO.getNodeSn());
                params.put(Constant.TYPE, ZTEIMES_ALIBABA_TYPE);
                customerDataLogDTO.setJsonData(JSON.toJSONString(params));
                logger.info("#######推送B2B数据:" + JSON.toJSONString(params));
                dataList.add(customerDataLogDTO);
            }
            // 推送数据至B2B
            tradeDataLogService.pushDataOfExceptionRollback(dataList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQualityCode(SaveQualityCodeDTO dto)throws Exception {
        List<QualityCodeInfoDTO> qualityCodeInfoList = dto.getQualityCodeInfoList();
        if(StringUtils.isEmpty(dto.getCustomerName()) ||  CollectionUtils.isEmpty(qualityCodeInfoList))  {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_CODE, com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_MSGID);
        }
        List<String> snList = qualityCodeInfoList.stream().map(e->e.getSn()).filter(e->StringUtils.isNotEmpty(e)).distinct().collect(Collectors.toList());
        Map<String, List<SspTaskInfoDTO>> sspTaskMap = this.getSspTaskMap(snList);

        //组装推送阿里数据
        List<QualityCodeInfoDTO> dataList = new ArrayList<>();
        for (QualityCodeInfoDTO qualityCodeInfoDTO : dto.getQualityCodeInfoList()) {
            List<SspTaskInfoDTO> sspTaskInfoDTOS = sspTaskMap.get(qualityCodeInfoDTO.getSn());
            if(CollectionUtils.isEmpty(sspTaskInfoDTOS)){
                continue;
            }
            for (SspTaskInfoDTO sspTaskInfoDTO : sspTaskInfoDTOS) {
                QualityCodeInfoDTO qualityCodeInfo = new QualityCodeInfoDTO();
                BeanUtils.copyProperties(qualityCodeInfoDTO,qualityCodeInfo);
                qualityCodeInfo.setId(idGenerator.snowFlakeIdStr());
                qualityCodeInfo.setCreateBy(SYSTEM);
                qualityCodeInfo.setLastUpdatedBy(SYSTEM);
                qualityCodeInfo.setNodeSn(sspTaskInfoDTO.getNodeSn());
                dataList.add(qualityCodeInfo);
            }
        }
        this.batchInsert(dataList);
    }

    private void batchInsert(List<QualityCodeInfoDTO> dataList) {
        if(CollectionUtils.isNotEmpty(dataList)){
            for (List<QualityCodeInfoDTO> qualityCodeInfoDTOS : CommonUtils.splitList(dataList, BATCH_SIZE_500)) {
                qualityCodeInfoRepository.saveQualityCode(qualityCodeInfoDTOS);
            }
        }
    }

    private Map<String, List<SspTaskInfoDTO>> getSspTaskMap(List<String> snList) {
        List<SspTaskInfoDTO> sspTaskInfoDTOList = mdsRemoteService.querySspTaskInfoForAli(snList);
        List<SspTaskInfoDTO> cnList = sspTaskInfoDTOList.stream().filter(e->StringUtils.startsWith(e.getNodeNumber(),Constant.CN)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(cnList)){
            return new HashMap<>();
        }
        for (SspTaskInfoDTO sspTaskInfoDTO : cnList) {
            String nodeNumber = sspTaskInfoDTO.getNodeNumber().substring(Constant.CN.length());
            sspTaskInfoDTO.setNodeSn(this.generateNodeSn(sspTaskInfoDTO.getSn(), nodeNumber));
        }
        Map<String, List<SspTaskInfoDTO>> sspTaskMap = cnList.stream()
                .collect(Collectors.groupingBy(SspTaskInfoDTO::getSn, Collectors.mapping(Function.identity(), Collectors.toList())));
        return sspTaskMap;
    }

    private String generateNodeSn(String barcode, String nodeNumber) {
        if(StringUtils.isNotEmpty(nodeNumber)){
            return barcode+Constant.HORIZON+Constant.STR_0+ nodeNumber;
        }
        return barcode;
    }

    @Override
    public List<QualityCodeInfoDTO> getSnInc(String lastUpdatedDate, String lastSn, int limit) {
        if (lastUpdatedDate == null) {
            return Collections.emptyList();
        }
        if (limit <= NumConstant.NUM_ZERO || limit > NumConstant.NUM_500) {
            limit = NumConstant.NUM_500;
        }
        return qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit);
    }


    /**
     * 根据条码获取阿里质量码，回调方法写入质量码对应表
     * @param b2bCallBackNewDTO
     */
    @Override
    public void saveQualityCodeCallBack(B2bCallBackNewDTO b2bCallBackNewDTO){
        logger.info("saveQualityCodeCallBack,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        String id = b2bCallBackNewDTO.getMessageId();
        if (StringUtils.isBlank(id) || !b2bCallBackNewDTO.isSuccess()) {
            return;
        }
        //解析阿里接口返回的质量码
        String qualityCode= parseAliBabaData(b2bCallBackNewDTO.getData());
        if(StringUtils.isNotEmpty(qualityCode)) {
            QualityCodeInfoDTO qualityCodeInfo = new QualityCodeInfoDTO();
            qualityCodeInfo.setId(b2bCallBackNewDTO.getKeywords());
            qualityCodeInfo.setQualityCode(qualityCode);
            qualityCodeInfoRepository.updateQualityCode(qualityCodeInfo);
        }
    }
    /**
     * 解析阿里接口返回的质量码
     * @param result
     */
    public String parseAliBabaData(String result) {
        String qualityCode= "";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(result);
            JsonNode dataNode = rootNode.get("result");
            if (dataNode != null) {
                JsonNode resultData = dataNode.get("data");
                AliBabaQualityDataDTO aLiBabaQualityDTO = objectMapper.treeToValue(resultData, AliBabaQualityDataDTO.class);
                /* Started by AICoder, pid:afb17o47994b79d14e000a0da0d4c912b788b5c0 */
                if (aLiBabaQualityDTO != null) {
                    qualityCode = aLiBabaQualityDTO.getQualityCode();
                }
                /* Ended by AICoder, pid:afb17o47994b79d14e000a0da0d4c912b788b5c0 */
            }
        } catch (Exception e) {
            logger.error("阿里回调获取质量码异常：{}："+e.getMessage());
        }
        return qualityCode;
    }

    @Override
    public QualityCodeInfoDTO getQualityCode(QualityCodeInfoDTO qualityCodeInfoDTO) {
        return qualityCodeInfoRepository.getQualityCode(qualityCodeInfoDTO);
    }
}

/* Ended by AICoder, pid:8e752wab2fq26cb141d30b5ed118f02f95971549 */
