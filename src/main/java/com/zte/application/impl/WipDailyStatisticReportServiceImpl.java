package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.BProdBomHeaderService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.WipDailyStatisticReportService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.common.utils.constant.RedisKeyConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WipDailyStatisticReportRepository;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.WipDailyStatisticQueryDTO;
import com.zte.interfaces.dto.WipDailyStatisticReportEntityDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.UNDER_LINE;
import static com.zte.itp.msa.core.exception.GlobalDefaultBaseExceptionHandler.getTrace;


@Service("wipDailyStatisticReportService")
public class WipDailyStatisticReportServiceImpl implements WipDailyStatisticReportService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static Integer rows = NumConstant.NUM_5000;
    private static Integer exportMaximumDirectly = NumConstant.NUM_50000;

    @Autowired
    private WipDailyStatisticReportRepository wipDailyStatisticReportrepository;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Autowired
    private BProdBomHeaderService bProdBomHeaderService;
    @Autowired
    private PsTaskService psTaskService;
    /**
     * 分页查询
     *
     * @param wipDailyStatisticReportEntityDTO
     * @return
     * @throws Exception
     */
    @Override
    public Page<WipDailyStatisticReportEntityDTO> pageList(WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO) throws Exception {
        setProductTypeName(wipDailyStatisticReportEntityDTO);
        Page<WipDailyStatisticReportEntityDTO> pageInfo = new Page<>(wipDailyStatisticReportEntityDTO.getPage(), wipDailyStatisticReportEntityDTO.getRows());
        pageInfo.setParams(wipDailyStatisticReportEntityDTO);
        pageInfo.setSearchCount(wipDailyStatisticReportEntityDTO.isSearchCount());
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportList = wipDailyStatisticReportrepository.pageList(pageInfo);
        // 外协标志赋值
        this.setOutFlag(wipDailyStatisticReportList);
        // 根据批次获取制造BOM
        this.setMBomProductCode(wipDailyStatisticReportList);
        pageInfo.setRows(wipDailyStatisticReportList);
        return pageInfo;
    }
    private void setOutFlag(List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportList) {
        // 获取外协标志为空的数据，根据批次获取外协编码信息
        List<WipDailyStatisticReportEntityDTO> wipDailyOuterFlagEmptyList = wipDailyStatisticReportList.stream().filter(item -> item.getOuterFlag() == null || item.getOuterFlag().isEmpty()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wipDailyOuterFlagEmptyList)) {
            return;
        }
        List<String> prodplanIds = wipDailyOuterFlagEmptyList.stream().map(WipDailyStatisticReportEntityDTO::getProdplanId).distinct().collect(Collectors.toList());
        List<PsTaskDTO> psTaskDTOList = psTaskService.getPsTaskByProdplanIdList(prodplanIds);
        Map<String, String> outSourceInfoByProdplanIdMap = psTaskDTOList.stream().collect(Collectors.toMap(PsTaskDTO::getProdplanId, dto -> Optional.ofNullable(dto.getOutSourceFactoryCode()).orElse(""), (v1, v2) -> v1));
        for(WipDailyStatisticReportEntityDTO dto : wipDailyOuterFlagEmptyList) {
            // 外协厂编码
            String outSourceFactoryCode = outSourceInfoByProdplanIdMap.get(dto.getProdplanId());
            // 工艺路径包含“外协”
            String outSourceInfo = dto.getSourceSysNo();
            boolean containOutSourceFlag = outSourceInfo.contains(Constant.OUT_SOURCE);
            // 判断外协标志是否为空。当外协厂编码或工艺路径含“外协”满足任意一个条件，外协标志为“是”。否则为否。
            if (StringUtils.isEmpty(outSourceFactoryCode) && containOutSourceFlag == false) {
                dto.setOuterFlag(Constant.NO);
            } else {
                dto.setOuterFlag(Constant.YES);
            }
        }
    }

    private void setMBomProductCode(List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportList) {
        List<String> prodplanIds = wipDailyStatisticReportList.stream().map(WipDailyStatisticReportEntityDTO::getProdplanId).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = bProdBomHeaderService.queryProductCodeByProdPlanIdList(prodplanIds);
        Map<String, String> mBomProductCodeByProdplanIdMap = bProdBomHeaderDTOS.stream().collect(Collectors.toMap(BProdBomHeaderDTO::getProdplanId, BProdBomHeaderDTO::getProductCode, (v1, v2) -> v1));
        for(WipDailyStatisticReportEntityDTO dto : wipDailyStatisticReportList) {
            String mBomProductCode = mBomProductCodeByProdplanIdMap.get(dto.getProdplanId());
            dto.setMBomProductCode(mBomProductCode == null ? dto.getItemNo() : mBomProductCode);
        }
    }

    /**
     * 设置产品类型
     *
     * @param wipDailyStatisticReportEntityDTO
     */
    private void setProductTypeName(WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO) {
        if (StringUtils.equals(wipDailyStatisticReportEntityDTO.getProductType(), NumConstant.STRING_ONE)) {
            wipDailyStatisticReportEntityDTO.setProductTypeName(Constant.MOBILE_PHONE);
        } else if (StringUtils.equals(wipDailyStatisticReportEntityDTO.getProductType(), NumConstant.STRING_TWO)) {
            wipDailyStatisticReportEntityDTO.setProductTypeName(Constant.CRAFT_POWER);
        } else if (StringUtils.equals(wipDailyStatisticReportEntityDTO.getProductType(), NumConstant.STRING_THREE)) {
            wipDailyStatisticReportEntityDTO.setProductTypeName(Constant.SYSTEM_PRODUCTS);
        }
    }

    @Override
    @RecordLogAnnotation("高级日报导出")
    @AsyncExport(functionName = "高级日报导出-中心工厂")
    public void export(HttpServletResponse response, WipDailyStatisticQueryDTO dto) throws Exception {
        //校验参数 时间范围
        //锁1个小时
        RedisLock redisLock = new RedisLock(RedisKeyConstant.WIP_DAILY_STATISTIC_REPORT_EXPORT + dto.getEmpNo(), NumConstant.NUM_3600);
        try {
            if (!redisLock.lock()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CURRENTLY_EXPORTING);
            }

            // 设置单板日报统计字段数据字典配置信息
            setTitleAndProps(dto);

            SimpleDateFormat sdf = new SimpleDateFormat(Constant.DATE_MIN_FORMATE_SHORT);
            String fileName = sdf.format(new Date()) + Constant.AdvancedDailyReportExport.FILE_NAME;
            //先查一次

            WipDailyStatisticReportEntityDTO pageInfo = new WipDailyStatisticReportEntityDTO();
            BeanUtils.copyProperties(dto, pageInfo);
            pageInfo.setRows(rows);
            pageInfo.setPage(NumConstant.NUM_ONE);
            Page<WipDailyStatisticReportEntityDTO> firstPage = this.pageList(pageInfo);
            //查询线体数据字典
            if (firstPage == null || CollectionUtils.isEmpty(firstPage.getRows())) {
                BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
                // 把单元格设置为数值格式
                bigExcelProcesser.setNumberFromat(true);
                bigExcelProcesser.createOrAppend(dto.getTitle(), new ArrayList<>(), dto.getProps());
                this.uploadFileThenClearLocalFileUpdateLog(fileName, bigExcelProcesser);
                return;
            }
            export(response, dto, fileName, pageInfo, firstPage);
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 导出
     * @param response
     * @param dto
     * @param fileName
     * @param pageInfo
     * @param firstPage
     * @throws Exception
     */
    private void export(HttpServletResponse response, WipDailyStatisticQueryDTO dto, String fileName,
                        WipDailyStatisticReportEntityDTO pageInfo, Page<WipDailyStatisticReportEntityDTO> firstPage) throws Exception {
        this.transformCode(firstPage.getRows());
        directExport(response, dto, fileName, pageInfo, firstPage);
    }

    /**
     * 邮件导出
     * @param dto
     * @param pageInfo
     * @param firstPage
     */
    private void mailExport(WipDailyStatisticQueryDTO dto, WipDailyStatisticReportEntityDTO pageInfo, Page<WipDailyStatisticReportEntityDTO> firstPage) {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        ThreadUtil.EXECUTOR.execute(() -> {
            // 文件写成功后，将文档云文件路径写到reids
            try {
                MESHttpHelper.setHttpRequestHeader(header);
                StringBuilder stringBuilder = new StringBuilder();
                //多次分批导出
                multipleMailExport(dto, pageInfo, firstPage, stringBuilder);
                logger.info(stringBuilder.toString(), Constant.AdvancedDailyReportExport.ERROR_INFO);
                // 发送邮件通知
                emailUtils.sendMail(dto.getEmpNo(), Constant.AdvancedDailyReportExport.EMAIL_TITLE_ZH, null,
                        stringBuilder.toString(), null);
            } catch (Exception e) {
                // 发送邮件通知
                emailUtils.sendMail(dto.getEmpNo(), Constant.AdvancedDailyReportExport.EMAIL_TITLE_ZH_ERROR, null,
                        getTrace(e), null);
                logger.info(e.getMessage(), Constant.AdvancedDailyReportExport.ERROR_INFO);
            } finally {
                MESHttpHelper.removeHttpRequestHeader();
            }
        });
    }

    /**
     * 少于5万直接导出
     * @param response
     * @param dto
     * @param fileName
     * @param pageInfo
     * @param firstPage
     * @return
     * @throws Exception
     */
    private void directExport(HttpServletResponse response, WipDailyStatisticQueryDTO dto, String fileName, WipDailyStatisticReportEntityDTO pageInfo, Page<WipDailyStatisticReportEntityDTO> firstPage) throws Exception {
        BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
        bigExcelProcesser.setNumberFromat(true);
        bigExcelProcesser.createOrAppend(dto.getTitle(), firstPage.getRows(), dto.getProps());
        //只有一页直接返回
        if (firstPage.getTotalPage() < NumConstant.NUM_TWO) {
            this.uploadFileThenClearLocalFileUpdateLog(fileName, bigExcelProcesser);
            return ;
        }
        //多页分批查询
        for (int i = NumConstant.NUM_TWO; i <= firstPage.getTotalPage(); i++) {
            pageInfo.setPage(i);
            Page<WipDailyStatisticReportEntityDTO> tempExportList = this.pageList(pageInfo);
            if (CollectionUtils.isEmpty(tempExportList.getRows())) {
                break;
            }
            this.transformCode(tempExportList.getRows());
            bigExcelProcesser.createOrAppend(dto.getTitle(), tempExportList.getRows(), dto.getProps());
        }
        this.uploadFileThenClearLocalFileUpdateLog(fileName, bigExcelProcesser);
    }

    private void uploadFileThenClearLocalFileUpdateLog(String fileName, BigExcelProcesser bigExcelProcesser) throws Exception {
        String filePath = FileUtils.tempPath + System.currentTimeMillis()+Constant.EMAIL_SPLIT+ fileName;
        FileUtils.checkFilePath(filePath);
        bigExcelProcesser.saveAsFile(filePath);
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(fileName, filePath);
    }

    /**
     * 将是否齐套，是否排产，组织ID转换成对应的中文
     *
     * @param list
     */
    public void transformCode(List<WipDailyStatisticReportEntityDTO> list) {
        // 查数据字典配置
        List<SysLookupValues> sysLookUpValue = sysLookupValuesService.findByLookupType(Constant.LOOKUP_TYPE_2222);
        // 组织映射
        Map<String, SysLookupValues> orgMap = sysLookUpValue.stream()
                .collect(Collectors.toMap(SysLookupValues::getLookupMeaning, value -> value, (k1, k2) -> k1));
        for (WipDailyStatisticReportEntityDTO dto : list) {
            if (Constant.FLAG_Y.equals(dto.getIsComplete())) {
                dto.setIsComplete(Constant.YES);
            } else if (Constant.FLAG_N.equals(dto.getIsComplete())) {
                dto.setIsComplete(Constant.NO);
            }

            if (Constant.FLAG_Y.equals(dto.getIsScheduled())) {
                dto.setIsScheduled(Constant.YES);
            } else if (Constant.FLAG_N.equals(dto.getIsScheduled())) {
                dto.setIsScheduled(Constant.NO);
            }
            this.matchOrgId(orgMap, dto);
        }
    }

    /**
     * 转换组织id
     *
     * @param orgMap
     * @param dto
     */
    private void matchOrgId(Map<String, SysLookupValues> orgMap, WipDailyStatisticReportEntityDTO dto) {
        if (dto.getOrgId() == null) {
            return;
        }
        SysLookupValues sysLookupTypesDTO = orgMap.get(dto.getOrgId().intValue() + Constant.STRING_EMPTY);
        if (ObjectUtils.isEmpty(sysLookupTypesDTO)) {
            return;
        }
        dto.setOrgName(sysLookupTypesDTO.getDescriptionChin());
    }

    /**
     * 多次分批导出
     *
     * @param dto
     * @param pageInfo
     * @param firstPage
     * @param stringBuilder
     * @throws Exception
     */
    private void multipleMailExport(WipDailyStatisticQueryDTO dto, WipDailyStatisticReportEntityDTO pageInfo,
                                    Page<WipDailyStatisticReportEntityDTO> firstPage, StringBuilder stringBuilder) throws Exception {
        dto.setCount(NumConstant.NUM_ONE);
        pageInfo.setSearchCount(false);
        BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
        bigExcelProcesser.createOrAppend(dto.getTitle(), firstPage.getRows(), dto.getProps());
        for (int i = NumConstant.NUM_TWO; i <= firstPage.getTotalPage(); i++) {
            pageInfo.setPage(i);
            Page<WipDailyStatisticReportEntityDTO> tempPage= this.pageList(pageInfo);
            if (CollectionUtils.isEmpty(tempPage.getRows())) {
                break;
            }
            this.transformCode(tempPage.getRows());
            bigExcelProcesser.createOrAppend(dto.getTitle(), tempPage.getRows(), dto.getProps());
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String exportFileName = simpleDateFormat.format(new Date()) + UNDER_LINE + dto.getCount() + Constant.AdvancedDailyReportExport.FILE_NAME;
        String filePath = FileUtils.tempPath + System.currentTimeMillis()+UNDER_LINE+exportFileName;
        FileUtils.checkFilePath(filePath);
        bigExcelProcesser.saveAsFile(filePath);
        // 上传文档云，并获取文档路径
        String fileKey = cloudDiskHelper.fileUpload(filePath, dto.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
        // 上传成功后删除本地文件
        FileUtils.deleteFile(filePath);
        String fileUrl = cloudDiskHelper.getFileDownloadUrl(fileKey, null, dto.getEmpNo());
        // 消息内容
        stringBuilder.append(Constant.AdvancedDailyReportExport.EMAIL_PREFIX)
                .append(exportFileName).append(Constant.AdvancedDailyReportExport.LEFT_BRACKET)
                .append(Constant.AdvancedDailyReportExport.VALID_FOR_SEVEN_DAYS)
                .append(Constant.AdvancedDailyReportExport.RIGHT_BRACKET)
                .append(Constant.AdvancedDailyReportExport.EMAIL_PREFIX_A)
                .append(fileUrl)
                .append(Constant.AdvancedDailyReportExport.EMAIL_COLO)
                .append(Constant.AdvancedDailyReportExport.CLICK_DOWN)
                .append(Constant.AdvancedDailyReportExport.EMAIL_SUFFIX)
                .append(Constant.STR_WRAP);
    }

    /**
     * 设置单板日报统计字段数据字典配置信息
     *
     * @param dto
     */
    private void setTitleAndProps(WipDailyStatisticQueryDTO dto) {
        List<SysLookupValues> lookupValueList = sysLookupValuesService.findByLookupType(Constant.WIP_DAILY_SYS_TYPE);

        if (CollectionUtils.isEmpty(lookupValueList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOOKUP_6001_EMPTY);
        }

        // 拼接配置动态表头
        String[] title = this.appendExcelTitle(lookupValueList, dto.getSelectedFields());
        dto.setTitle(title);
        // 拼接配置动态表头对应的字段
        String[] props = this.appendExcelField(lookupValueList, dto.getSelectedFields());
        dto.setProps(props);
    }


    /**
     * 拼接表头 分三段 中间动态
     *
     * @return java.util.List<java.lang.String>
     */
    private String[] appendExcelTitle(List<SysLookupValues> lookupValueList, List<String> selectedFields) throws MesBusinessException {
        List<String> title = Lists.newArrayList(ExcelName.WIPDAILYSTATISTIC_TITLE_ONE);
        // 动态表头按配置的展示顺序排序， 需过滤Attribute2=Y可展示
        List<String> dynamicTitle = lookupValueList.stream()
                .filter(e -> Constant.FLAG_Y.equals(e.getAttribute2()))
                // 前端选择展示的结存字段才导出
                .filter(e -> selectedFields.contains(e.getAttribute4()))
                .sorted(Comparator.comparing(SysLookupValues::getSortSeq))
                .map(SysLookupValues::getDescriptionChin)
                .distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dynamicTitle)) {
            title.addAll(dynamicTitle);
        }
        title.addAll(Lists.newArrayList(ExcelName.WIPDAILYSTATISTIC_TITLE_TWO));
        return title.toArray(new String[title.size()]);
    }

    /**
     * 拼接配置动态表头对应的字段
     * 展示与否、排序与表头一致 保证字段值正确对应表头
     *
     * @return java.util.List<java.lang.String>
     */
    private String[] appendExcelField(List<SysLookupValues> lookupValueList, List<String> selectedFields) throws MesBusinessException {
        // 展示与否、排序与表头一致 保证字段值正确对应表头 动态表头按配置的展示顺序排序， 需过滤Attribute2=Y可展示
        List<String> dynamicField = lookupValueList.stream()
                .filter(e -> Constant.FLAG_Y.equals(e.getAttribute2()))
                // 前端选择展示的结存字段才导出
                .filter(e -> selectedFields.contains(e.getAttribute4()))
                .sorted(Comparator.comparing(SysLookupValues::getSortSeq))
                .map(SysLookupValues::getAttribute4)
                .distinct().collect(Collectors.toList());
        List<String> valueFields = Lists.newArrayList(ExcelName.WIPDAILYSTATISTIC_VALUE_ONE);
        if (!CollectionUtils.isEmpty(dynamicField)) {
            valueFields.addAll(dynamicField);
        }
        valueFields.addAll(Lists.newArrayList(ExcelName.WIPDAILYSTATISTIC_VALUE_TWO));
        return valueFields.toArray(new String[valueFields.size()]);
    }

    /**
     * 保存
     *
     * @param wipDailyStatisticReportEntityDTOList
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList) throws Exception {
        int result = NumConstant.NUM_ZERO;
        if (CollectionUtils.isEmpty(wipDailyStatisticReportEntityDTOList)) {
            return result;
        }
        for (List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOS : CommonUtils.splitList(wipDailyStatisticReportEntityDTOList, NumConstant.NUM_100)) {
            List<String> prodplanIdList = wipDailyStatisticReportEntityDTOS.stream().map(WipDailyStatisticReportEntityDTO::getProdplanId).distinct().collect(Collectors.toList());
            Date date = wipDailyStatisticReportEntityDTOS.get(NumConstant.NUM_ZERO).getStatisticDate();
            //批次加时间是否存在数据，存在先删除，再新增
            wipDailyStatisticReportrepository.deleteByDateAndProdplanIdList(prodplanIdList, date);
            wipDailyStatisticReportEntityDTOS.forEach(p -> p.setId(UUID.randomUUID().toString()));
            result += wipDailyStatisticReportrepository.batchInsert(wipDailyStatisticReportEntityDTOS);
        }
        return result;
    }

}