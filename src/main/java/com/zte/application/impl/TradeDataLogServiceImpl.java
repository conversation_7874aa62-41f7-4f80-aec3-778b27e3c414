package com.zte.application.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.HrmUserCenterService;
import com.zte.application.TradeDataLogService;
import com.zte.application.kafka.producer.CommonProducer;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.KafkaConstant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.gei.common.utils.JsonUtils;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.B2bDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.message.SpringKafkaProducer;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.NUM_ONE;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/3 15:36
 */

@Service
public class TradeDataLogServiceImpl implements TradeDataLogService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Autowired
    private TradeDataLogRepository tradeDataLogRepository;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    protected SpringKafkaProducer kafkaProducer;
    @Autowired
    private CommonProducer commonProducer;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;
    @Value("${kafka.msg.max.size:1024000}")
    private Integer size;
    @Value("#{${request.default.head.map:{'x-mes-bff':'iMes-bff-authorization'}}}")
    private Map<String, String> requestDefaultHeadMap;
    @Value("#{${tradedata.log.alarm:{}}}")
    private Map<String, String> alarmConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushDataOfExceptionRollback(CustomerDataLogDTO dataLogDTO) {
        // 1、保存数据
        dataLogDTO.setStatus(Constants.EMPTY_STRING);
        dataLogDTO.setPushType(Constant.PUSH_TYPE.KAFKA);
        if (StringUtils.isBlank(dataLogDTO.getId())) {
            dataLogDTO.setId(IdUtil.randomUUID());
        }
        tradeDataLogRepository.batchInsert(Collections.singletonList(dataLogDTO));

        // 2、组装数据格式
        B2bDTO b2bDTO = new B2bDTO();
        b2bDTO.setUuid(dataLogDTO.getId());
        b2bDTO.setKeywords(StringUtils.defaultString(dataLogDTO.getKeywords(), dataLogDTO.getId()));
        b2bDTO.setMessageType(dataLogDTO.getMessageType());
        b2bDTO.setJsonMsgContent(Collections.singletonMap("data", checkSizeAndGetJsonMsg(dataLogDTO.getJsonData())));

        // 3、推送kafka
        commonProducer.sendMsgSync(JSON.toJSONString(b2bDTO), KafkaConstant.CENTERFACTORY, null, KafkaConstant.COMMON_TRADE_MESSAGE);

        // 4、更新推送状态
        tradeDataLogRepository.updateStatusAndErrMsgById(Constants.EMPTY_STRING, Constant.PUSH_B2B_STATUS.PY, dataLogDTO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushDataOfExceptionRollback(List<CustomerDataLogDTO> dataList) {
        List<List<CustomerDataLogDTO>> lists = CommonUtils.splitList(dataList, Constant.BATCH_SIZE);
        // 1、保存数据
        for (List<CustomerDataLogDTO> list : lists) {
            list.forEach(dto -> {
                if (StringUtils.isBlank(dto.getId())) {
                    dto.setId(IdUtil.randomUUID());
                }
                dto.setStatus(Constants.EMPTY_STRING);
                dto.setPushType(Constant.PUSH_TYPE.KAFKA);
            });

            tradeDataLogRepository.batchInsert(list);
        }

        // 单个数据循环推送
        for (CustomerDataLogDTO dto : dataList) {
            // 2、组装数据格式
            B2bDTO b2bDTO = new B2bDTO();
            b2bDTO.setUuid(dto.getId());
            b2bDTO.setKeywords(StringUtils.defaultString(dto.getKeywords(), dto.getId()));
            b2bDTO.setMessageType(dto.getMessageType());
            b2bDTO.setJsonMsgContent(Collections.singletonMap("data", checkSizeAndGetJsonMsg(dto.getJsonData())));

            // 3、推送kafka
            commonProducer.sendMsgSync(JSON.toJSONString(b2bDTO), KafkaConstant.CENTERFACTORY, null, KafkaConstant.COMMON_TRADE_MESSAGE);

            // 4、更新推送状态
            tradeDataLogRepository.updateStatusAndErrMsgById(Constants.EMPTY_STRING, Constant.PUSH_B2B_STATUS.PY, dto.getId());
        }
    }

    @Override
    public void pushData(List<CustomerDataLogDTO> dataList) {
        List<List<CustomerDataLogDTO>> lists = CommonUtils.splitList(dataList, Constant.BATCH_SIZE);
        // 1、保存数据
        for (List<CustomerDataLogDTO> list : lists) {
            list.forEach(dto -> {
                if (StringUtils.isBlank(dto.getId())) {
                    dto.setId(IdUtil.randomUUID());
                }
                dto.setStatus(Constants.EMPTY_STRING);
                dto.setPushType(Constant.PUSH_TYPE.KAFKA);
            });

            tradeDataLogRepository.batchInsert(list);
        }

        // 单个数据循环推送
        dataList.forEach(dto -> {
            // 2、组装数据格式
            B2bDTO b2bDTO = new B2bDTO();
            b2bDTO.setUuid(dto.getId());
            b2bDTO.setKeywords(StringUtils.defaultString(dto.getKeywords(), dto.getId()));
            b2bDTO.setMessageType(dto.getMessageType());
            b2bDTO.setJsonMsgContent(Collections.singletonMap("data", checkSizeAndGetJsonMsg(dto.getJsonData())));

            try {
                // 3、推送kafka
                kafkaProducer.sendMessage(KafkaConstant.CENTERFACTORY, null, KafkaConstant.COMMON_TRADE_MESSAGE, JSON.toJSONString(b2bDTO));

                // 4、更新推送状态
                tradeDataLogRepository.updateStatusAndErrMsgById(Constants.EMPTY_STRING, Constant.PUSH_B2B_STATUS.PY, dto.getId());
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("kafka发送交易数据异常", e);
                AlarmHelper.alarm("imes_dependent_service_error", "1001", AlarmSeverityEnum.CRITICAL, "推送交易数据异常", e.getMessage() + JSON.toJSONString(dto));
                if (e instanceof MesBusinessException) {
                    MesBusinessException exception = (MesBusinessException) e;
                    String msgId = exception.getExMsgId();
                    String msg = CommonUtils.getLmbMessage(msgId);
                    tradeDataLogRepository.updateStatusAndErrMsgById(msg, Constant.PUSH_B2B_STATUS.PN, dto.getId());
                } else {
                    tradeDataLogRepository.updateStatusAndErrMsgById(e.getMessage(), Constant.PUSH_B2B_STATUS.PN, dto.getId());
                }
            }
        });
    }

    private Object checkSizeAndGetJsonMsg(String msg) {
        byte[] bytes = msg.getBytes();
        //kafka消息限制1MB, 这里限制不能大于1024*1000字节
        double lengthInMb = new Double(bytes.length) / size;
        if (lengthInMb > NumConstant.NUM_ONE) {
            return Constants.EMPTY_STRING;
        }
        return JSON.parse(msg);
    }

    @Override
    public void handleB2bCallback(B2bCallBackNewDTO b2bCallBackNewDTO) {
        // 先把回调结果保存。回调业务接口不影响保存逻辑
        TradeDataLogService service = SpringContextUtil.getBean(TradeDataLogService.class);
        if (null != service) {
            service.handleB2bCallbackOfSaveResult(b2bCallBackNewDTO);
        }

        // 如果有请求
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        if (CollectionUtils.isNotEmpty(valuesList)) {
            Optional<SysLookupTypesDTO> found = valuesList.stream()
                    .filter(sysLookupTypesDTO -> sysLookupTypesDTO.getLookupMeaning().equals(b2bCallBackNewDTO.getMessageType()))
                    .findFirst();
            if (found.isPresent()) {
                SysLookupTypesDTO result = found.get();
                String serviceName = result.getAttribute1();
                String url = result.getAttribute2();
                String sendType = result.getAttribute3();
                String appcode = result.getAttribute4();

                try {
                    Map<String, String> headerParamsMap = getStringStringMap(b2bCallBackNewDTO);
                    if (StringUtils.isNotBlank(serviceName)) {
                        String msg = MicroServiceRestUtil.invokeService(serviceName, MicroServiceNameEum.VERSION, sendType, url, JacksonJsonConverUtil.beanToJson(b2bCallBackNewDTO), headerParamsMap);
                        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
                    } else {
                        if (StringUtils.isNotBlank(appcode)) {
                            headerParamsMap.put(Constant.APP_CODE, appcode);
                        }
                        String responseStr = HttpRemoteService.remoteExe(JsonUtils.toMap(b2bCallBackNewDTO), headerParamsMap, url, sendType);
                        ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseStr);
                    }
                } catch (MesBusinessException e) {
                    tradeDataLogRepository.updateStatusAndRemarkById(e.getParams()[0].toString(), Constant.PUSH_B2B_STATUS.CE, b2bCallBackNewDTO.getMessageId());
                    throw e;
                }
            }
        }
    }

    private Map<String, String> getStringStringMap(B2bCallBackNewDTO b2bCallBackNewDTO) {
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.putAll(requestDefaultHeadMap);
        CustomerDataLogDTO tradeDataLogById = tradeDataLogRepository.getTradeDataLogById(b2bCallBackNewDTO.getMessageId());
        if (Objects.nonNull(tradeDataLogById)) {
            Integer factoryId = tradeDataLogById.getFactoryId();
            if (Objects.nonNull(factoryId)) {
                headerParamsMap.put(Constant.X_FACTORY_ID, factoryId.toString());
            }
        }
        return headerParamsMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleB2bCallbackOfSaveResult(B2bCallBackNewDTO b2bCallBackNewDTO) {
        if (StringUtils.isBlank(b2bCallBackNewDTO.getMessageId())) {
            CustomerDataLogDTO dto = new CustomerDataLogDTO();
            dto.setId(IdUtil.randomUUID());
            dto.setJsonData(b2bCallBackNewDTO.getData());
            dto.setErrMsg(JSON.toJSONString(b2bCallBackNewDTO));
            dto.setMessageType(b2bCallBackNewDTO.getMessageType());
            dto.setStatus(Constant.PUSH_B2B_STATUS.CY);
            dto.setOrigin(b2bCallBackNewDTO.getSource());
            dto.setCustomerName(Constants.EMPTY_STRING);
            dto.setProjectName(Constants.EMPTY_STRING);
            dto.setPushType(Constants.EMPTY_STRING);
            dto.setFactoryId(Constants.NUM_ZERO);
            dto.setCreateBy(b2bCallBackNewDTO.getSource());
            dto.setLastUpdatedBy(b2bCallBackNewDTO.getSource());
            b2bCallBackNewDTO.setMessageId(dto.getId());
            tradeDataLogRepository.insert(dto);
        } else {
            if (b2bCallBackNewDTO.isSuccess() && RetCode.SUCCESS_CODE.equals(b2bCallBackNewDTO.getCode())) {
                tradeDataLogRepository.updateStatusAndErrMsgById(JSON.toJSONString(b2bCallBackNewDTO), Constant.PUSH_B2B_STATUS.CY, b2bCallBackNewDTO.getMessageId());
                // 如果该条数据的TID不为空，则要将ID为该TID的最上层数据更新为CY，
                tradeDataLogRepository.updateParentStatusById(Constant.PUSH_B2B_STATUS.CY, b2bCallBackNewDTO.getMessageId());
            } else {
                AlarmHelper.alarm("imes_dependent_service_error", "1001", AlarmSeverityEnum.CRITICAL, Constant.B2B_ALARM_TITLE, JSON.toJSONString(b2bCallBackNewDTO));
                tradeDataLogRepository.updateStatusAndErrMsgById(JSON.toJSONString(b2bCallBackNewDTO), Constant.PUSH_B2B_STATUS.CN, b2bCallBackNewDTO.getMessageId());

                CustomerDataLogDTO tradeDataLog = tradeDataLogRepository.getTradeDataLogById(b2bCallBackNewDTO.getMessageId());
                iCenterRemoteService.sendMessage(Constant.B2B_ALARM_TITLE, (tradeDataLog != null ? tradeDataLog.getProjectName() + tradeDataLog.getContractNo() : Constants.EMPTY_STRING) + Constants.COLON + JSON.toJSONString(b2bCallBackNewDTO),
                        alarmConfig.get(b2bCallBackNewDTO.getMessageType()));
            }
        }

    }

    @Override
    public List<CustomerDataLogDTO> getPushErrorData(CustomerDataLogDTO customerDataLogDTO) {
        Page page = new Page(NUM_ONE, NumConstant.NUM_500);
        // 默认同步方式，不支持指定其他参数
        page.setParams(customerDataLogDTO);
        return tradeDataLogRepository.getPushErrorData(page);
    }

    @Override
    public List<CustomerDataLogDTO> getDataBySn(List<String> snList, List<String> projectPhaseList) {
        if (CollectionUtils.isEmpty(snList)) {
            return new ArrayList<>();
        }
        if (snList.size() > NumConstant.NUM_1000) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID, "snList exceed 1000");
        }
        return tradeDataLogRepository.getDataBySn(snList, projectPhaseList);
    }

    @Override
    public Object getPushData(String uuid) {
        String data = tradeDataLogRepository.getPushData(uuid);
        if (StringUtils.isNotBlank(data)) {
            return com.alibaba.fastjson2.JSON.parse(data);
        }
        return null;
    }

    @Override
    public void repush(List<CustomerDataLogDTO> paramList) {
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        paramList.forEach(item -> {
            item.setTid(item.getId());
            item.setId(IdUtil.randomUUID());
            item.setCreateBy(empNo);
            item.setLastUpdatedBy(empNo);
        });
        pushDataOfExceptionRollback(paramList);
    }

    @Override
    public Page<CustomerDataLogDTO> queryLogData(CustomerDataLogDTO param) throws Exception {
        this.checkQueryLogDataParam(param);
        Page<CustomerDataLogDTO> page = new Page<>(param.getPage(), param.getRows());
        page.setParams(param);
        List<CustomerDataLogDTO> queryList = tradeDataLogRepository.pageList(page);
        this.setEmpNoWithName(queryList);
        page.setRows(queryList);
        return page;
    }

    /**
     * 校验查询参数
     *
     * <AUTHOR>
     */
    private void checkQueryLogDataParam(CustomerDataLogDTO param) {
        // 参数不能全为空
        if (isEmptyCheck(param.getCustomerName(), param.getContractNo(), param.getTaskNo(), param.getMessageType(), param.getSn(), param.getStatus()) &&
                null == param.getStartDate() && null == param.getEndDate()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_QUERY_PARAM_NULL);
        }
        // 推送时间查询跨度不能超过180天
        if (null != param.getStartDate() && null != param.getEndDate()
                && DateUtil.caldaysByDate(param.getEndDate(), param.getStartDate()).compareTo(Constant.TIME_INTERVAL) > Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PUSH_TIME_WITHIN_180_DAYS);
        }
        // 合同号，条码，任务号可以单独查询
        if (!isEmptyCheck(param.getContractNo(), param.getTaskNo(), param.getSn())) {
            return;
        }
        // 客户名称，数据类别，推送状态须和时间一起查询
        if ((null == param.getStartDate() || null == param.getEndDate()) &&
                (!isEmptyCheck(param.getCustomerName(), param.getMessageType(), param.getStatus()))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME);
        }
    }

    /**
     * 批量校验字符串是否为空
     *
     * <AUTHOR>
     */
    private boolean isEmptyCheck(String... strings) {
        for (String str : strings) {
            if (StringUtils.isNotBlank(str)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 工号姓名组装
     *
     * <AUTHOR>
     */
    private void setEmpNoWithName(List<CustomerDataLogDTO> queryList) throws Exception {
        List<String> userIdList = queryList.stream().map(CustomerDataLogDTO::getCreateBy).collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = null;
        hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(userIdList);
        for (CustomerDataLogDTO customerDataLogDTO : queryList) {
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(customerDataLogDTO.getCreateBy());
            if (hrmPersonInfoDTO != null) {
                customerDataLogDTO.setCreateBy(hrmPersonInfoDTO.getEmpName() + customerDataLogDTO.getCreateBy());
            }
        }
    }

    @Override
    public List<CustomerDataLogDTO> selectFirstDateOfTaskNo(List<CustomerDataLogDTO> params, String messageType) {
        if (CollectionUtils.isEmpty(params) || StringUtils.isBlank(messageType)) {
            return Collections.emptyList();
        }
        return tradeDataLogRepository.selectFirstDateOfTaskNo(params, messageType);
    }

    @Override
    public CustomerDataLogDTO getTradeDataLogById(String id) {
        return tradeDataLogRepository.getTradeDataLogById(id);
    }

}
