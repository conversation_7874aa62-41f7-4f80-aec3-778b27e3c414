package com.zte.application.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.zte.application.*;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.enums.EntityClassEnum;
import com.zte.common.enums.SceneCodeEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushStdModelConfirmationRepository;
import com.zte.domain.model.PushStdModelDataRepository;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.NoticeCenterService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.aps.TasksQueryDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.NoticeCenterUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.zte.common.model.NumConstant.NUM_ONE;
import static com.zte.common.model.NumConstant.NUM_TWELVE;
import static com.zte.common.model.NumConstant.*;
import static com.zte.common.utils.NumConstant.NUM_NINE;
import static com.zte.common.utils.NumConstant.NUM_TWO;
import static com.zte.common.utils.NumConstant.NUM_ZERO;
import static com.zte.common.utils.NumConstant.*;


@Service("pushStdModelDataService")
public class PushStdModelDataServiceImpl implements PushStdModelDataService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PushStdModelDataRepository pushStdModelDataRepository;
    @Autowired
    private PushStdModelConfirmationRepository pushStdModelConfirmationRepository;
    @Autowired
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;
    @Autowired
    private PsTaskExtendedService psTaskExtendedService;
    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private NoticeCenterService noticeCenterService;
    @Autowired
    private NoticeCenterUtils noticeCenterUtils;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Autowired
    private FixBomCommonService fixBomCommonService;

    @Autowired
    private PdmRemoteService pdmRemoteService;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private IscpRemoteService iscpRemoteService;

    @Resource
    private ApsInOneClient apsInOneClient;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final int DATA_CHECK_OR_PUSH_FAIL = 8;

    private static final Validator VALID = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 推送失败阈值
     */
    @Value("${imes.std.push.fail.threshold:3}")
    private int pushFailThreshold;

    @Value("${imes.std.push.default.control.level:3}")
    private Integer defaultControlLevel;

    @Value("${imes.std.push.ali.control.level:1}")
    private Integer aliControlLevel;

    // 标模任务业务推送顺序 10:排产信息 20:工单发放
    @Value("${imes.std.task.push.seq:10,20}")
    private List<String> pushSeq;

    /**
     * 特定物料类型
     */
    @Value("${imes.std.task.specific.itemType:成品料}")
    private List<String> specificItemTypeList;

    /**
     * 特定物料分类
     */
    @Value("${imes.std.task.specific.materialCategory:L6包}")
    private List<String> specificMaterialCategoryList;

    /**
     * 特定客户部件类型
     */
    @Value("${imes.std.task.specific.CustomerComponentType:server.configmodel.moc}")
    private List<String> specificCustomerComponentType;

    @Value("${imes.aps.prepared.time.miss.alarm.title:%s:任务对应厂商自供料预计齐套日期或全部物料预计齐套日期在APS不存在}")
    private String alarmTitle;

    /**
     * 工厂id和工厂名称映射
     */
    @Value("#{${imes.factory.id.name.map:{'58':'ZTE101','55':'ZTE101','52':'ZTE101','53':'ZTE101','56':'ZTE101'}}}")
    private Map<Integer, String> factoryIdToNameMap;


    @Override
    public int mergeStdModelTask(List<PushStdModelDataDTO> list, List<String> customerNoList) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(customerNoList)) {
            return NumConstant.NUM_ZERO;
        }
        List<String> originlTaskNoList = list.stream().map(PushStdModelDataDTO::getTaskNo).distinct().collect(Collectors.toList());
        // 判断任务是否需要推送(任务属于指定客户则需要推送)
        List<PsTaskExtendedDTO> needPushTaskList = psTaskExtendedService.filterListByCustomerNo(originlTaskNoList, customerNoList);
        if (CollectionUtils.isEmpty(needPushTaskList)) {
            return NumConstant.NUM_ZERO;
        }

        Map<String, String> taskNoCustomerMap = needPushTaskList.stream()
                .collect(Collectors.toMap(
                        PsTaskExtendedDTO::getTaskNo,
                        PsTaskExtendedDTO::getCustomerNo
                ));

        List<String> needPushTaskNoList = new ArrayList<>(taskNoCustomerMap.keySet());
        // 判断推送表是否已存在任务，存在忽略，不存在新增
        List<String> existTaskNoList = pushStdModelDataRepository.getExistTaskNo(needPushTaskNoList);

        // 筛选需插入数据
        List<PushStdModelDataDTO> needAddlist = list.stream().filter(curr -> needPushTaskNoList.contains(curr.getTaskNo()) && !existTaskNoList.contains(curr.getTaskNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needAddlist)) {
            return NumConstant.NUM_ZERO;
        }
        for (PushStdModelDataDTO pushStdModelDataDTO : needAddlist) {
            // 设置当前推送进程为首个进程
            pushStdModelDataDTO.setCurrProcess(pushSeq.get(NumConstant.NUM_ZERO));
            pushStdModelDataDTO.setCustomerName(taskNoCustomerMap.get(pushStdModelDataDTO.getTaskNo()));
        }
        int count = pushStdModelDataRepository.batchInsert(needAddlist);
        return count;
    }

    @Override
    public int pushStdModelData(List<String> customerNameList, int preDays) {
        if (CollectionUtils.isEmpty(customerNameList)) {
            return NumConstant.NUM_ZERO;
        }
        // 参数校验
        if (preDays <= NUM_ZERO) {
            preDays = NumConstant.NUM_100;
        }

        // 记录上次同步最后时间,用于增量分页查询
        Date startTime = DateUtil.addDay(-preDays).getTime();
        String taskNo = "";
        int limit = NUM_500;
        // 记录成功推送的数量
        int count = NumConstant.NUM_ZERO;
        boolean first = true;
        List<PushStdModelDataDTO> needPushList = new ArrayList<>();
        while (first || needPushList.size() >= limit) {
            first = false;
            // 查找待推送任务
            needPushList = pushStdModelDataRepository.getNeedPushDataInc(startTime, customerNameList, taskNo, NumConstant.NUM_100);
            if (CollectionUtils.isEmpty(needPushList)) {
                continue;
            }
            // 根据任务号查询任务扩展信息
            List<String> taskNoList = needPushList.stream().map(PushStdModelDataDTO::getTaskNo).distinct().collect(Collectors.toList());
            List<PsTaskExtendedDTO> psTaskExtendedList = psTaskExtendedService.queryByTaskNos(taskNoList);
            // 转换成Map
            Map<String, PsTaskExtendedDTO> taskExtendedMap = psTaskExtendedList.stream().collect(Collectors.toMap(PsTaskExtendedDTO::getTaskNo, a -> a, (k1, k2) -> k1));
            // 下次增量查询参数
            PushStdModelDataDTO lastData = needPushList.get(needPushList.size() - 1);
            startTime = lastData.getLastUpdatedDate();
            taskNo = lastData.getTaskNo();
            count += this.pushData(needPushList, taskExtendedMap);

        }
        return count;
    }

    @Override
    public PageRows<PushStdModelDataExtDTO> queryPushStdModelDataExt(PushStdModelDataQueryDTO query) {
        String taskNoFrom = query.getTaskNoFrom();
        Date lastUpdatedDateStart = query.getLastUpdatedDateStart();

        int pageNum = query.getPageNum() == null || query.getPageNum() < NumConstant.NUM_ONE ? NumConstant.NUM_ONE : query.getPageNum();
        int pageSize = query.getPageSize() == null || query.getPageSize() > NumConstant.NUM_500 ? NumConstant.NUM_500 : query.getPageSize();

        // 分页逻辑是否需要计算总数
        boolean countFlag = !org.springframework.util.StringUtils.hasText(taskNoFrom) || lastUpdatedDateStart == null;
        PageMethod.startPage(pageNum, pageSize, countFlag);
        Page<PushStdModelDataExtDTO> pushStdModelDataExtPage = (Page<PushStdModelDataExtDTO>) pushStdModelDataRepository.selectExtByQuery(query);

        PageRows<PushStdModelDataExtDTO> pushStdModelDataExtPageRows = new PageRows<>();

        pushStdModelDataExtPageRows.setCurrent(pushStdModelDataExtPage.getPageNum());
        pushStdModelDataExtPageRows.setTotal(pushStdModelDataExtPage.getTotal());
        pushStdModelDataExtPageRows.setRows(pushStdModelDataExtPage.getResult());

        return pushStdModelDataExtPageRows;
    }

    @Override
    public List<String> getExistTaskNo(List<String> taskNoList) {
        return Opt.ofEmptyAble(taskNoList).map(pushStdModelDataRepository::getExistTaskNo).orElse(Collections.emptyList());
    }

    @Override
    public void batchUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList) {
        Opt.ofEmptyAble(pushStdModelDataDTOList).ifPresent(pushStdModelDataRepository::batchUpdate);
    }

    @Override
    public List<PushStdModelDataDTO> getListByTaskNo(List<String> taskNos) {
        return Opt.ofEmptyAble(taskNos).map(pushStdModelDataRepository::getListByTaskNo).orElse(Collections.emptyList());
    }

    public <T extends PushStdModelDTO> void sendAlarmEmail(List<T> needWarnList, String title) {
        String alarmInfo = this.packAlarmInfo(needWarnList);
        Map<String, Object> data = new HashMap<>();
        data.put(Constant.STR_TITLE, title);
        data.put(Constant.STR_CONTENT, alarmInfo);
        // 查询数据字典获取模版id和收件人
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002);
        if (sysLookupValues == null || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOK_UP_VALUE_7599002});
        }
        Set<String> mailToSet = new HashSet<>();
        mailToSet.addAll(Arrays.asList(StringUtils.split(StringUtils.defaultString(sysLookupValues.getAttribute1()), Constant.COMMA)));
        mailToSet.addAll(Arrays.asList(StringUtils.split(StringUtils.defaultString(sysLookupValues.getAttribute2()), Constant.COMMA)));
        mailToSet.addAll(Arrays.asList(StringUtils.split(StringUtils.defaultString(sysLookupValues.getAttribute3()), Constant.COMMA)));
        mailToSet.addAll(Arrays.asList(StringUtils.split(StringUtils.defaultString(sysLookupValues.getAttribute4()), Constant.COMMA)));
        // 筛选不为空数据
        List<String> mailToList = mailToSet.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        noticeCenterService.sendEmail(sysLookupValues.getLookupMeaning(), StringUtils.join(mailToList, Constant.COMMA), data);
    }

    /**
     * 组装告警信息
     *
     * @param needWarnList
     * @return
     */
    private <T extends PushStdModelDTO> String packAlarmInfo(List<T> needWarnList) {
        StringBuffer html = new StringBuffer();
        //有产出数据
        String div = "<br/><br/><div>";
        String htmlTitle = "<p style='margin:0;font-size:13pt'>" + Constant.WORK_ORDER_CALL_BACK_ERROR_TASK_NO + "</p>";
        html.append(div + htmlTitle + "<table style='border:1px rgb(204,204,204);width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        html.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        html.append("<td>" + Constant.INDEX + "</td>");
        html.append("<td>" + Constant.PUSH_ERROR_TASK_NO + "</td>");
        html.append("<td>" + Constant.PUSH_ERROR_MSG + "</td>");
        html.append("</tr>");
        for (int index = 0; index < needWarnList.size(); index++) {
            PushStdModelDTO detailDTO = needWarnList.get(index);
            int num = index + 1;
            html.append("<tr>");
            html.append("<td>" + num + "</td>");
            html.append("<td>" + CommonUtils.getStrTransNull(detailDTO.getTaskNo()) + "</td>");
            html.append("<td>" + CommonUtils.getStrTransNull(detailDTO.getErrorMsg()) + "</td>");
            html.append("</tr>");
        }
        html.append("</table>");
        html.append("<hr style='height:5px;border:none;background-color:rgb(0,102,153);'/></div>");
        return html.toString();
    }

    /* Started by AICoder, pid:u9995ud32f655dd14251098b50528b2eb5c36921 */
    @Override
    public void dispatchManufactureOrderCallBack(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("dispatchManufactureOrderCallBack,data:{}", JSON.toJSON(b2bCallBackNewDTO));

        if (StringUtils.isBlank(b2bCallBackNewDTO.getKeywords())) {
            return;
        }

        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo(b2bCallBackNewDTO.getKeywords());

        boolean success = this.checkSuccess(b2bCallBackNewDTO);

        pushStdModelDataDTO.setPushStatus(success ? NUM_TWO : NUM_NINE);

        if (!success) {
            pushStdModelDataDTO.setErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), NUM_ZERO, NumConstant.NUM_1500));
        }

        pushStdModelDataRepository.update(pushStdModelDataDTO);

        if (!success) {
            sendAlarmEmail(Collections.singletonList(pushStdModelDataDTO), Constant.DISPATCH_MANUFACTURE_ORDER_TITLE);
        }
    }
    /* Ended by AICoder, pid:u9995ud32f655dd14251098b50528b2eb5c36921 */

    private boolean checkSuccess(B2bCallBackNewDTO b2bCallBackNewDTO) {
        if (b2bCallBackNewDTO.isSuccess()) {
            WorkOrderWriteCallBackDTO orderWriteCallBackDTO = JSON.parseObject(b2bCallBackNewDTO.getData(), WorkOrderWriteCallBackDTO.class);
            WorkOrderWriteCallBackDataDTO orderWriteCallBackDataDTO = JSON.parseObject(orderWriteCallBackDTO.getData(), WorkOrderWriteCallBackDataDTO.class);
            if (orderWriteCallBackDataDTO.getSuccess()) {
                WorkOrderWriteCallBackResultDTO orderWriteCallBackResultDTO = JSON.parseObject(orderWriteCallBackDataDTO.getResult(), WorkOrderWriteCallBackResultDTO.class);
                if (orderWriteCallBackResultDTO.getSuccess()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void orderScheduleCallBack(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("orderScheduleCallBack,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        if (StringUtils.isBlank(b2bCallBackNewDTO.getKeywords())) {
            return;
        }

        // 使用$拆分任务号和场景
        String[] params = b2bCallBackNewDTO.getKeywords().split(Constant.COMMA);
        if (params.length < NUM_TWO) {
            return;
        }
        String taskNo = params[NUM_ZERO];
        String type = params[NUM_ONE];

        boolean success = this.checkSuccess(b2bCallBackNewDTO);
        switch (type) {
            case Constant.SCHEDULE_INFO :
                this.updatePushStatus(b2bCallBackNewDTO, success, taskNo);
                break;
            case Constant.CONFIRMATION_INFO :
                this.updateConfirmationPushStatus(b2bCallBackNewDTO, success, taskNo);
                break;
            default:
                break;
        }
    }

    private void updatePushStatus(B2bCallBackNewDTO b2bCallBackNewDTO, boolean success, String taskNo) {
        PushStdModelDataDTO pushStdModelDataDTO = pushStdModelDataRepository.getDataByTaskNo(taskNo);
        if (pushStdModelDataDTO == null || !StringUtils.equals(pushStdModelDataDTO.getCurrProcess(), pushSeq.get(NUM_ZERO))) {
            // 推送工序已不在首工序不更新
            return;
        }
        if (success) {
            // 修改为下一个工序(20)
            pushStdModelDataDTO.setCurrProcess(pushSeq.get(NUM_ONE));
            pushStdModelDataDTO.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);
            pushStdModelDataDTO.setPushFailCount(NUM_ZERO);
            pushStdModelDataDTO.setErrorMsg("");
            pushStdModelDataRepository.update(pushStdModelDataDTO);

            List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.queryByTaskNos(Arrays.asList(taskNo));
            if (CollectionUtils.isEmpty(psTaskExtendedDTOList)) {
                return;
            }
            PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedDTOList.get(0);
            String pushedKey = String.format(Constant.PUSHED_KEY, psTaskExtendedDTO.getFixBomId());
            // 记录已推送成功fixbomx,使可并发推送
            redisTemplate.opsForValue().set(pushedKey, psTaskExtendedDTO.getTaskNo(), NumConstant.NUM_THIRTY, TimeUnit.DAYS);
        } else {
            pushStdModelDataDTO.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
            pushStdModelDataDTO.setErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), NUM_ZERO, NumConstant.NUM_1500));
            pushStdModelDataRepository.update(pushStdModelDataDTO);
            this.sendAlarmEmail(Collections.singletonList(pushStdModelDataDTO), Constant.ORDER_SCHEDULE_WARN_TITLE);
        }
    }

    private void updateConfirmationPushStatus(B2bCallBackNewDTO b2bCallBackNewDTO, boolean success, String taskNo) {
        PushStdModelConfirmationDTO pushStdModelConfirmationDTO = new PushStdModelConfirmationDTO();
        pushStdModelConfirmationDTO.setTaskNo(taskNo);
        if (success) {
            pushStdModelConfirmationDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
            pushStdModelConfirmationDTO.setPushFailCount(NUM_ZERO);
            pushStdModelConfirmationRepository.update(pushStdModelConfirmationDTO);
        } else {
            pushStdModelConfirmationDTO.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
            pushStdModelConfirmationDTO.setErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), NUM_ZERO, NumConstant.NUM_1500));
            pushStdModelConfirmationRepository.update(pushStdModelConfirmationDTO);
            this.sendAlarmEmail(Collections.singletonList(pushStdModelConfirmationDTO), Constant.ORDER_SCHEDULE_WARN_TITLE);
        }
    }

    /**
     * 定时任务推送数据
     * @throws Exception
     */
    @Override
    public void ticketClosed() throws Exception {
        // 第二次推送失败数据
        processBatchData(
                (startTime,taskNo,stringList) -> pushStdModelDataRepository.selectPushFailData(taskNo,startTime),
                dto -> {
                    try {
                        pushData(dto, dto.getCustomerPartType());
                        dto.setCurrProcess(STR_FIFTY);
                        dto.setPushStatus(NUM_ONE);
                        pushStdModelDataRepository.update(dto);
                    } catch (Exception e) {
                        dto.setPushStatus(NUM_ZERO);
                        dto.setPushFailCount(dto.getPushFailCount()+1);
                        if (dto.getPushFailCount()>=pushFailThreshold){
                            dto.setPushStatus(Constant.PUSH_STATUS.DATA_CHECK_OR_PUSH_FAIL);
                        }
                        dto.setErrorMsg(e.getMessage().substring(0,Math.min(1000,e.getMessage().length())));
                        pushStdModelDataRepository.update(dto);
                        logger.error("工单关闭定时任务失败数据重新推送异常："+dto.getTaskNo());
                    }
                }
        );
        // 第一次推送数据
        processBatchData(
                (startTime,taskNo,stringList) -> pushStdModelDataRepository.selectDataByTaskNoLimit(taskNo,stringList,startTime),
                dto-> {
                    try {
                        pushFaildData(dto);
                    } catch (Exception e) {
                        dto.setPushStatus(NUM_ZERO);
                        dto.setErrorMsg(e.getMessage().substring(0,Math.min(1000,e.getMessage().length())));
                        dto.setCurrProcess(STR_FIFTY);
                        dto.setPushFailCount(NUM_ONE);
                        pushStdModelDataRepository.update(dto);
                        logger.error("工单关闭定时任务首次推送数据异常："+dto.getTaskNo());
                    }
                }
        );

    }

    public void pushFaildData(PushStdModelDataDTO dto) throws Exception {
        //查询下面的sn条数以及客户类型
        if (StringUtils.isBlank(dto.getTaskNo())){
            return;
        }
        PushStdDataTaskAndSnNumDTO snNumDTO = pushStdModelDataRepository.getPushSnNumByTaskNos(dto.getTaskNo());
        if (Objects.isNull(snNumDTO)){
            return;
        }
        //判断sn条数是否符合大于等于任务的数量
        //处理数据
        if (snNumDTO.getSnNum() < dto.getTaskQty()) {
            return;
        }
        WipEntityInfoDTO wipEntityInfoDTO = new WipEntityInfoDTO();
        wipEntityInfoDTO.setTaskNo(dto.getTaskNo());
        wipEntityInfoDTO.setOrgId(dto.getOrgId().shortValueExact());
        List<WipEntityInfoDTO> taskList= new ArrayList<>();
        taskList.add(wipEntityInfoDTO);
        List<WipEntityInfoDTO> wipEntityInfos = DatawbRemoteService.getTaskNoStatusByErp(taskList);
        if (CollectionUtils.isEmpty(wipEntityInfos)){
            return;
        }
        String statusType = wipEntityInfos.get(0).getStatusType();
        if (!Objects.equals(String.valueOf(NUM_TWELVE),statusType)){
            return;
        }
        pushData(dto, snNumDTO.getCustomerPartType());
        dto.setPushStatus(NUM_ONE);
        dto.setCurrProcess(STR_FIFTY);
        dto.setPushFailCount(NUM_ONE);
        pushStdModelDataRepository.update(dto);
    }

        /**
         * 通用批处理逻辑
         * @param dataFetcher 数据获取函数
         * @param dataProcessor 数据处理函数
         */
        private <T> void processBatchData (TriFunction<Date, String,List<String>,List<T>> dataFetcher, Consumer< T > dataProcessor) throws Exception {
            String taskNo = "";
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -1);
            Date oneYearAgo = calendar.getTime();
            List<SysLookupValues> sysLookupTypesList = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_TYPE_ALIBABA);
            List<String> stringList = sysLookupTypesList.stream().map(SysLookupValues::getLookupMeaning).collect(Collectors.toList());
            do {
                List<T> dataList = dataFetcher.apply(oneYearAgo,taskNo,stringList);
                // 边界条件判断
                if (CollectionUtils.isEmpty(dataList)) {
                    return;
                }
                // 获取最后一个taskNo用于下次查询
                PushStdModelDataDTO taskDails = (PushStdModelDataDTO) dataList.get(dataList.size()-1);
                taskNo = taskDails.getTaskNo();
                if (StringUtils.isBlank(taskNo)) {
                    return;
                }
                oneYearAgo = taskDails.getLastUpdatedDate();
                // 处理数据
                dataList.forEach(dataProcessor);
            } while (StringUtils.isNotBlank(taskNo));
        }


    /**
     * 推送失败数据
     *
     * @param pushStdModelDataDTO 失败数据
     * @param customerPartType
     * @throws Exception
     */
    private void pushData(PushStdModelDataDTO pushStdModelDataDTO, String customerPartType) throws Exception {
        Map<String, String> data = new HashMap<>();
        data.put("manufacture_order_no",pushStdModelDataDTO.getTaskNo());
        data.put("category",customerPartType);
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
        customerDataLogDTO.setKeywords(pushStdModelDataDTO.getTaskNo());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setCustomerName(pushStdModelDataDTO.getCustomerName());
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setProjectPhase(STR_FIFTY);
        customerDataLogDTO.setMessageType(Constant.ZT_EI_MES_ALIBABA_CLOSE_MANUFACTURE_ORDER);
        customerDataLogDTO.setTaskNo(pushStdModelDataDTO.getTaskNo());
        customerDataLogDTO.setItemNo(pushStdModelDataDTO.getItemNo());
        customerDataLogDTO.setFactoryId(pushStdModelDataDTO.getFactoryId());
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(data));
        tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTO);
        // 推送B2B
        logger.info("#######工单关闭定时任务推送B2B数据:{}",  JSON.toJSONString(customerDataLogDTO));
    }

    @Override
    public void ticketClosedCallBack(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("orderScheduleCallBack,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        if (StringUtils.isBlank(b2bCallBackNewDTO.getKeywords())) {
            return;
        }
        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo(b2bCallBackNewDTO.getKeywords());
        boolean success = this.checkSuccess(b2bCallBackNewDTO);
        if (success) {
            // 修改为下一个工序(50)
            pushStdModelDataDTO.setCurrProcess(NumConstant.NUM_FIFTY + "");
            pushStdModelDataDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
            pushStdModelDataDTO.setPushFailCount(NUM_ZERO);
            pushStdModelDataDTO.setErrorMsg("");
            pushStdModelDataRepository.update(pushStdModelDataDTO);
        }else {
            //查询数据
            pushStdModelDataDTO.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
            pushStdModelDataDTO.setErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), NUM_ZERO, NumConstant.NUM_1500));
            pushStdModelDataRepository.update(pushStdModelDataDTO);
        }
    }

    @Override
    public void batchSaveOrUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList) {
        CollUtil.split(pushStdModelDataDTOList, Constant.BATCH_SIZE).forEach(pushStdModelDataRepository::batchSaveOrUpdate);
    }

    private int pushData(List<PushStdModelDataDTO> needPushList, Map<String, PsTaskExtendedDTO> taskExtendedMap) {
        List<PushStdModelDataDTO> needWarnList = new ArrayList<>();
        int count = NUM_ZERO;
        for (PushStdModelDataDTO pushStdModelDataDTO : needPushList) {
            boolean result = true;
            try {
                switch (pushStdModelDataDTO.getCurrProcess()) {
                    case "10" :
                        // 推送排产信息
                        result = this.pushSchedulingInfo(pushStdModelDataDTO, taskExtendedMap);
                        break;
                    case "20" :
                        // 推送工单发放信息
                        result = this.dispatchManufactureOrder(pushStdModelDataDTO, taskExtendedMap);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                logger.error("标模任务推送未知异常", e);
                pushStdModelDataDTO.setErrorMsg("标模任务推送未知异常:" + StringUtils.substring(e.getMessage(), NUM_ZERO, NumConstant.NUM_1500));
                this.checkFailTimeAndUpdate(pushStdModelDataDTO);
                continue;
            }
            if (result) {
                // 更新状态为已推送
                pushStdModelDataDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);
                pushStdModelDataDTO.setPushDate(new Date());
                pushStdModelDataDTO.setErrorMsg(Constant.STRING_EMPTY);
                pushStdModelDataRepository.update(pushStdModelDataDTO);
                count++;
            } else {
                needWarnList.add(pushStdModelDataDTO);
            }
        }
        // 发送邮件告警
        if (CollectionUtils.isNotEmpty(needWarnList)) {
            this.sendAlarmEmail(needWarnList, Constant.STD_MODEL_PUSH_ERROR_TITLE);
        }
        return count;
    }

    public <T extends PushStdModelDTO> boolean pushSchedulingInfo(T pushStdModelDTO, Map<String, PsTaskExtendedDTO> taskExtendedMap) throws Exception {
        PsTaskExtendedDTO psTaskExtendedDTO = taskExtendedMap.get(pushStdModelDTO.getTaskNo());
        if (psTaskExtendedDTO == null) {
            pushStdModelDTO.setErrorMsg("任务扩展属性信息不存在");
            this.checkFailTimeAndUpdate(pushStdModelDTO);
            return false;
        }
        //如果是混合云任务，则需要查询是否有领料单，没有不进行推送
        if (Constant.CLOUD_TYPE_HC.equals(psTaskExtendedDTO.getCloudType())) {
            PickListQueryDTO queryDTO = new PickListQueryDTO();
            queryDTO.setTaskNos(Lists.newArrayList(psTaskExtendedDTO.getTaskNo()));
            List<ProdPickListMainDTO> prodPickListMainDTOList = DatawbRemoteService.queryPickListByTaskNos(queryDTO);
            if (CollectionUtils.isEmpty(prodPickListMainDTOList)) {
                pushStdModelDTO.setErrorMsg("该任务没有领料单,本次定时任务周期不推送,下次定时任务会再次推送");
                pushStdModelDataRepository.update((PushStdModelDataDTO) pushStdModelDTO);
                return false;
            }
        }
        String pushedKey = String.format(Constant.PUSHED_KEY, psTaskExtendedDTO.getFixBomId());
        if (StringUtils.isBlank(redisTemplate.opsForValue().get(pushedKey))) {
            // 未推送过,需判断fixbom是否有其它指令正在推送(同一个fixbom首次推送不能并发)
            String pushingKey = String.format(Constant.PUSHING_KEY, psTaskExtendedDTO.getFixBomId());
            if(!redisTemplate.opsForValue().setIfAbsent(pushingKey, psTaskExtendedDTO.getTaskNo(), Constant.INT_1, TimeUnit.MINUTES)) {
                // 加锁失败则不推送
                pushStdModelDTO.setErrorMsg("相同fixbom任务正在推送,本次定时任务周期不推送,下次定时任务会再次推送");
                return false;
            }
        }

        // 阿里数据DTO
        ManufactureOrderDTO manufactureOrderDTO = new ManufactureOrderDTO();
        boolean validResult;
        if (pushStdModelDTO instanceof PushStdModelConfirmationDTO) {
            // 转正任务推送
            validResult = handleConfirmationStdModel(pushStdModelDTO, psTaskExtendedDTO, manufactureOrderDTO);
        } else {
            validResult = handleStdModelData(pushStdModelDTO, psTaskExtendedDTO, manufactureOrderDTO);
        }
        if (validResult) {
            pushData(pushStdModelDTO, psTaskExtendedDTO, manufactureOrderDTO);
        }
        return validResult;
    }

    @Override
    public boolean pushSchedulingInfo(PushStdModelDTO pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, SceneCodeEnum sceneCodeEnum) throws Exception {
        if (ObjUtil.hasNull(pushStdModelDTO, psTaskExtendedDTO, sceneCodeEnum)){
            logger.error("the parameter pushStdModelDTO or psTaskExtendedDTO or sceneCodeEnum is null");
            return false;
        }
        ManufactureOrderDTO manufactureOrderDTO = new ManufactureOrderDTO();
        boolean validResult = this.validAndPackPushData(pushStdModelDTO, psTaskExtendedDTO, sceneCodeEnum, manufactureOrderDTO);
        if (validResult) {
            pushData(pushStdModelDTO, psTaskExtendedDTO, manufactureOrderDTO);
        }
        return validResult;
    }

    private void pushData(PushStdModelDTO pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, ManufactureOrderDTO manufactureOrderDTO) {
        String projectPhase = pushStdModelDTO instanceof PushStdModelConfirmationDTO ? Constant.CONFIRMATION_INFO : Constant.SCHEDULE_INFO;
        CustomerDataLogDTO customerDataLogDTO = createCustomerDataLogDTO(pushStdModelDTO, psTaskExtendedDTO, manufactureOrderDTO, projectPhase);
        tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTO);
        // 推送B2B
        logger.info("#######推送B2B数据:" + JSONObject.toJSONString(customerDataLogDTO));
    }

    private CustomerDataLogDTO createCustomerDataLogDTO(PushStdModelDTO pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, ManufactureOrderDTO manufactureOrderDTO, String projectPhase) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
        customerDataLogDTO.setProjectPhase(projectPhase);
        customerDataLogDTO.setKeywords(pushStdModelDTO.getTaskNo() + Constant.COMMA + customerDataLogDTO.getProjectPhase());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setCustomerName(psTaskExtendedDTO.getCustomerNo());
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_MANUFACTURE_ORDER_SCHEDULE);
        customerDataLogDTO.setTaskNo(pushStdModelDTO.getTaskNo());
        customerDataLogDTO.setItemNo(pushStdModelDTO.getItemNo());
        customerDataLogDTO.setFactoryId(pushStdModelDTO.getFactoryId());
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(manufactureOrderDTO));
        return customerDataLogDTO;
    }

    private <T extends PushStdModelDTO> boolean handleConfirmationStdModel(T pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, ManufactureOrderDTO manufactureOrderDTO) throws Exception {
        PushStdModelConfirmationDTO pushStdModelConfirmationDTO = (PushStdModelConfirmationDTO) pushStdModelDTO;
        if (pushStdModelConfirmationDTO.getConfirmationType() == NUM_TWO) {
            PushStdModelSnDataQueryDTO queryDTO = new PushStdModelSnDataQueryDTO();
            queryDTO.setTaskNos(Collections.singletonList(pushStdModelDTO.getTaskNo()));
            List<PushStdModelSnDataDTO> snDataDTOList = pushStdModelSnDataRepository.selectByQuery(queryDTO);
            snDataDTOList = snDataDTOList.stream().filter(snData -> {
                        int currProcess = Convert.toInt(snData.getCurrProcess());
                        return currProcess > Constant.INT_40 || (currProcess == Constant.INT_40 && Constant.INT_2 == snData.getPushStatus());
                    }).collect(Collectors.toList());
            if (snDataDTOList.size() < pushStdModelDTO.getTaskQty()) {
                pushStdModelDTO.setErrorMsg("转正入库单条码未完成入库推送");
                this.checkFailTimeAndUpdate(pushStdModelDTO);
                return false;
            }
        } else {
            PushStdModelDataDTO pushStdModelDataDTO = pushStdModelDataRepository.getDataByTaskNo(pushStdModelDTO.getTaskNo());
            if (pushStdModelDataDTO == null || Convert.toInt(pushStdModelDataDTO.getCurrProcess()) <= NumConstant.NUM_TEN) {
                pushStdModelDTO.setErrorMsg("未推送排产");
                this.checkFailTimeAndUpdate(pushStdModelDTO);
                return false;
            }
        }
        // 场景5
        return this.validAndPackPushData(pushStdModelDTO, psTaskExtendedDTO, SceneCodeEnum.BUFFER_TOTALLY_BIND, manufactureOrderDTO);
    }

    private <T extends PushStdModelDTO> boolean handleStdModelData(T pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, ManufactureOrderDTO manufactureOrderDTO) throws Exception {
        // 根据businessScene和entityClass判断场景
        BusinessSceneEnum businessSceneEnum = BusinessSceneEnum.getEnum(psTaskExtendedDTO.getBusinessScene());
        if (businessSceneEnum == null) {
            pushStdModelDTO.setErrorMsg(psTaskExtendedDTO.getBusinessScene() + "业务场景不存在");
            this.checkFailTimeAndUpdate(pushStdModelDTO);
            return false;
        }
        boolean validResult = false;
        switch (businessSceneEnum) {
            case MANUFACTURE:
            case MODIFIED:
            case RETURN:
            case DISASSEMBLY:
                // 场景1(formal_scedule)
                validResult = this.validAndPackPushData(pushStdModelDTO, psTaskExtendedDTO, SceneCodeEnum.FORMAL_SCEDULE, manufactureOrderDTO);
                break;
            case BUFFER:
                EntityClassEnum entityClassEnum = EntityClassEnum.getEnum(psTaskExtendedDTO.getEntityClass());
                if (entityClassEnum == null) {
                    pushStdModelDTO.setErrorMsg(psTaskExtendedDTO.getEntityClass() + "任务分类不存在");
                    this.checkFailTimeAndUpdate(pushStdModelDTO);
                    return false;
                }
                switch (entityClassEnum) {
                    case REWORK_2:
                    case FG_DISAS_2:
                        // 场景2(rework_without_md)
                        validResult = this.validAndPackPushData(pushStdModelDTO, psTaskExtendedDTO, SceneCodeEnum.REWORK_WITHOUT_MD, manufactureOrderDTO);
                        break;
                    case STD_UNIT_2:
                        // 场景3(buffer_scedule_without_md)
                        validResult = this.validAndPackPushData(pushStdModelDTO, psTaskExtendedDTO, SceneCodeEnum.BUFFER_SCEDULE_WITHOUT_MD, manufactureOrderDTO);
                        break;
                    default:
                        pushStdModelDTO.setErrorMsg(psTaskExtendedDTO.getEntityClass() + "场景不支持");
                        this.checkFailTimeAndUpdate(pushStdModelDTO);
                        return false;
                }
                break;
            default:
                pushStdModelDTO.setErrorMsg(psTaskExtendedDTO.getBusinessScene() + "任务分类不支持");
                this.checkFailTimeAndUpdate(pushStdModelDTO);
                return false;
        }
        return validResult;
    }

    private boolean dispatchManufactureOrder(PushStdModelDataDTO pushStdModelDataDTO, Map<String, PsTaskExtendedDTO> taskExtendedMap) throws Exception {
        PsTaskExtendedDTO psTaskExtendedDTO = taskExtendedMap.get(pushStdModelDataDTO.getTaskNo());
        if (psTaskExtendedDTO == null) {
            pushStdModelDataDTO.setErrorMsg("任务附属信息不存在");
            return false;
        }

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.HORIZON, ""));
        customerDataLogDTO.setTaskNo(pushStdModelDataDTO.getTaskNo());
        customerDataLogDTO.setCustomerName(pushStdModelDataDTO.getCustomerName());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setProjectName("alibaba");
        customerDataLogDTO.setProjectPhase(Constant.DISPATCH_ORDER_PROJECT_PHASE);
        customerDataLogDTO.setFactoryId(pushStdModelDataDTO.getFactoryId());
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_DISPATCH_MANUFACTURE_ORDER);
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setKeywords(pushStdModelDataDTO.getTaskNo());

        Map<String, Date> startAndEndTimeMap = centerFactoryCallSiteService.queryScheduleStartAndEndTime(pushStdModelDataDTO.getTaskNo(), String.valueOf(pushStdModelDataDTO.getFactoryId()));
        Date dateScheduleDProduction = startAndEndTimeMap != null ? startAndEndTimeMap.get("scheduleStartDate") : null;

        WorkOrderWriteBackDTO workOrderWriteBackDTO = new WorkOrderWriteBackDTO();
        workOrderWriteBackDTO.setManufactureOrderNo(pushStdModelDataDTO.getTaskNo());
        workOrderWriteBackDTO.setCategory(psTaskExtendedDTO.getCustomerPartType());
        workOrderWriteBackDTO.setDateScheduleDProduction(dateScheduleDProduction);

        customerDataLogDTO.setJsonData(JSON.toJSONString(workOrderWriteBackDTO));

        tradeDataLogService.pushDataOfExceptionRollback(Collections.singletonList(customerDataLogDTO));
        return true;
    }

    private <T extends PushStdModelDTO> boolean validAndPackPushData(T pushStdModelDataDTO, PsTaskExtendedDTO psTaskExtendedDTO, SceneCodeEnum sceneCodeEnum, ManufactureOrderDTO manufactureOrderDTO) throws Exception {
        // 设置公有字段字段
        manufactureOrderDTO.setSceneCode(sceneCodeEnum.getCode());
        // 根据ps_task_extended.entity_class任务分类转换得到(基于APS场景区分来转换) 如：entity_class = STD_UNIT_2 则业务场景为制造，对应值为1
        manufactureOrderDTO.setBusinessScene(EntityClassEnum.getBusinessSceneByCode(psTaskExtendedDTO.getEntityClass()));
        manufactureOrderDTO.setManufactureOrderNo(pushStdModelDataDTO.getTaskNo());
        // 根据料单代码查询PDM接口获取客户部件类型,并更新至任务服务信息表
        this.setAndUpdateCustomPartType(manufactureOrderDTO, pushStdModelDataDTO.getItemNo(), psTaskExtendedDTO);
        manufactureOrderDTO.setManufactureOrderType(Integer.parseInt(StringUtils.defaultString(psTaskExtendedDTO.getTaskType(), NumConstant.STRING_ONE)));
        manufactureOrderDTO.setFixBom(psTaskExtendedDTO.getFixBomId());
        manufactureOrderDTO.setQuantityProduce(pushStdModelDataDTO.getTaskQty());
        // 设置厂商自供料预计齐套日期和全部物料预计齐套日期,不存在需从APS取并更新至psTaskExtended
        this.setPreparedTime(manufactureOrderDTO, psTaskExtendedDTO);
        // 设置预计投产日期和预计完工日期
        this.setScheduleDate(manufactureOrderDTO, pushStdModelDataDTO.getTaskNo(), String.valueOf(pushStdModelDataDTO.getFactoryId()));
        // 设置物料清单
        this.setMaterialBillList(manufactureOrderDTO, psTaskExtendedDTO);
        manufactureOrderDTO.setFactoryCode(factoryIdToNameMap.get(pushStdModelDataDTO.getFactoryId()));
        Set<ConstraintViolation<ManufactureOrderDTO>> paramErrors = null;
        switch (sceneCodeEnum) {
            case FORMAL_SCEDULE:
                // 指令编号
                manufactureOrderDTO.setManufactureDirectiveNo(psTaskExtendedDTO.getBillNo());
                paramErrors = VALID.validate(manufactureOrderDTO, ManufactureOrderDTO.FormalScedule.class);
                break;
            case REWORK_WITHOUT_MD:
                manufactureOrderDTO.setOriginFixbom(psTaskExtendedDTO.getRelNo());
                manufactureOrderDTO.setMaterialName(psTaskExtendedDTO.getCustomerItemName());
                // 设置库存来源(调用MES接口查询)
                this.setInventorySource(manufactureOrderDTO, pushStdModelDataDTO.getTaskNo());
                paramErrors = VALID.validate(manufactureOrderDTO, ManufactureOrderDTO.ReworkWithoutMd.class);
                break;
            case BUFFER_SCEDULE_WITHOUT_MD:
                manufactureOrderDTO.setMaterialName(psTaskExtendedDTO.getCustomerItemName());
                paramErrors = VALID.validate(manufactureOrderDTO, ManufactureOrderDTO.BufferSceduleWithoutMd.class);
                break;
            case BUFFER_TOTALLY_BIND:
                // 场景五
                manufactureOrderDTO.setBusinessScene(null);
                manufactureOrderDTO.setManufactureOrderType(NumConstant.NUM_ONE);
                // 指令编号
                manufactureOrderDTO.setManufactureDirectiveNo(psTaskExtendedDTO.getBillNo());
                paramErrors = VALID.validate(manufactureOrderDTO, ManufactureOrderDTO.ConfirmationScedule.class);
                break;
            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(paramErrors)) {
            List<String> errorMsg = paramErrors.stream().map(error -> error.getMessage()).collect(Collectors.toList());
            pushStdModelDataDTO.setErrorMsg(errorMsg.toString());
            // 参数校验不通过不更新状态和失败次数,只更新失败信息
            if (pushStdModelDataDTO instanceof PushStdModelDataDTO) {
                pushStdModelDataRepository.update((PushStdModelDataDTO) pushStdModelDataDTO);
            } else if (pushStdModelDataDTO instanceof PushStdModelConfirmationDTO) {
                pushStdModelConfirmationRepository.update((PushStdModelConfirmationDTO) pushStdModelDataDTO);
            }
            return false;
        }
        // 校验参数
        return true;
    }

    /**
     * 设置厂商自供料预计齐套日期和全部物料预计齐套日期
     * 不存在需从APS取并更新至psTaskExtended
     * 若APS也不存在则需要发送告警邮件
     * @param manufactureOrderDTO
     * @param psTaskExtendedDTO
     */
    private void setPreparedTime(ManufactureOrderDTO manufactureOrderDTO, PsTaskExtendedDTO psTaskExtendedDTO) {
        if (psTaskExtendedDTO.getSelfSupplyPrepareDate() == null || psTaskExtendedDTO.getFullPrepareDate() == null) {
            // 不存在则从APS获取
            TasksQueryDTO taskQueryDTO = new TasksQueryDTO();
            taskQueryDTO.setEntityNoList(Arrays.asList(psTaskExtendedDTO.getTaskNo()));
            com.zte.springbootframe.common.model.Page<PsTaskExtendedDTO> page = apsInOneClient.customerTaskQuery(taskQueryDTO);
            if (page == null || CollectionUtils.isEmpty(page.getRows())) {
                // APS不存在告警
                noticeCenterUtils.sendEmail(String.format(alarmTitle, psTaskExtendedDTO.getTaskNo()), Constant.STRING_EMPTY, Constant.LOOK_UP_VALUE_7599005);
                return;
            }
            PsTaskExtendedDTO tempExt = page.getRows().get(0);
            if (tempExt.getSelfSupplyPrepareDate() == null || tempExt.getFullPrepareDate() == null) {
                // APS不存在告警
                noticeCenterUtils.sendEmail(String.format(alarmTitle, psTaskExtendedDTO.getTaskNo()), Constant.STRING_EMPTY, Constant.LOOK_UP_VALUE_7599005);
                return;
            }
            psTaskExtendedDTO.setSelfSupplyPrepareDate(tempExt.getSelfSupplyPrepareDate());
            psTaskExtendedDTO.setFullPrepareDate(tempExt.getFullPrepareDate());
            // 更新
            psTaskExtendedService.batchUpdateByTaskNo(Arrays.asList(psTaskExtendedDTO));
        }
        // 设置厂商自供料预计齐套日期和全部物料预计齐套日期
        manufactureOrderDTO.setDateMaterialEstimatedPrepared(psTaskExtendedDTO.getSelfSupplyPrepareDate());
        manufactureOrderDTO.setDateAllMaterialEstimatedPrepared(psTaskExtendedDTO.getFullPrepareDate());
    }

    private void setInventorySource(ManufactureOrderDTO manufactureOrderDTO, String taskNo) {
        List<PickListResultDTO> list = DatawbRemoteService.queryMaterialOrderNoByTaskNo(taskNo);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        manufactureOrderDTO.setInventorySource(list.get(NUM_ZERO).getInventorySource());
    }

    /**
     * 设置物料清单
     * 树状结构
     * @param manufactureOrderDTO
     * @param psTaskExtendedDTO
     */
    private void setMaterialBillList(ManufactureOrderDTO manufactureOrderDTO, PsTaskExtendedDTO psTaskExtendedDTO) throws Exception {
        manufactureOrderDTO.setMaterialBillList(new ArrayList<>());
        List<FixBomDetailDTO> fixBomDetailList = fixBomCommonService.queryFixBomDetailByHeadId(psTaskExtendedDTO.getFixBomHeadId());
        // 只保留itemSeq不为空且itemLevel为数字的数据且deleteFlag不为Y的数据
        fixBomDetailList = fixBomDetailList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getItemSeq()) && StringUtils.isNumeric(item.getItemLevel()) && !Constant.FLAG_Y.equals(item.getDeleteFlag()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fixBomDetailList)) {
            return;
        }
        // 根据fixBomDetail生成原始树
        List<FixBomDetailDTO> originalNode = fixBomCommonService.getTreeByFixBomDetail(fixBomDetailList);
        // 修正fixBom用量,初始倍率1
        fixBomCommonService.adjustItemNumber(originalNode, NumConstant.NUM_ONE);

        // 将fixBomDetail转换为materialBill
        List<MaterialBillDTO> list = new ArrayList<>();
        // 获取fixBomDetailList中itemLevel最小值(表示顶层)
        String topLevel = fixBomDetailList.stream().map(item -> Integer.parseInt(item.getItemLevel())).min(Integer::compare).get().toString();
        // 保留第一层和fixBomRequired为Y的数据
        fixBomDetailList = fixBomDetailList.stream()
                .filter(fixBomDetailDTO -> Constant.FLAG_Y.equals(fixBomDetailDTO.getFixBomRequired()) || topLevel.equals(fixBomDetailDTO.getItemLevel()))
                .collect(Collectors.toList());

        List<String> itemNoList = fixBomDetailList.stream().map(FixBomDetailDTO::getZteCode).distinct().collect(Collectors.toList());
        List<CivControlInfoDTO> civControlInfoList = iscpRemoteService.queryAliControlInfoList(itemNoList);
        List<String> aliControlItemNos = civControlInfoList.stream()
                .filter(dto -> Objects.equals(NumConstant.NUM_ONE,dto.getControl()))
                .map(CivControlInfoDTO::getItemNo)
                .distinct().collect(Collectors.toList());

        for (FixBomDetailDTO fixBomDetailDTO : fixBomDetailList) {
            // 只保留参数校验通过数据
            this.packAndValidData(psTaskExtendedDTO, fixBomDetailDTO, topLevel, aliControlItemNos, list);
        }
        List<MaterialBillDTO> node = this.getMaterialBillList(list);
        if (CollectionUtils.isNotEmpty(node)) {
            manufactureOrderDTO.getMaterialBillList().addAll(node.get(NUM_ZERO).getMaterialBillList());
        }
    }

    /**
     * 组装并校验MaterialBillDTO
     * @param psTaskExtendedDTO
     * @param fixBomDetailDTO
     * @param topLevel
     * @param list
     */
    private void packAndValidData(PsTaskExtendedDTO psTaskExtendedDTO, FixBomDetailDTO fixBomDetailDTO, String topLevel, List<String> aliControlItemNos, List<MaterialBillDTO> list) {
        Set<ConstraintViolation<MaterialBillDTO>> paramErrors;
        MaterialBillDTO materialBillDTO = new MaterialBillDTO();
        if (topLevel.equals(fixBomDetailDTO.getItemLevel())) {
            // 物料名称:最上层传三段码,其余叶子节点传mpn(供应商料号)
            materialBillDTO.setMaterialName(psTaskExtendedDTO.getCustomerItemName());
            // 物料BOM:最上层传fixbom编号 其余不传
            materialBillDTO.setMaterialBom(psTaskExtendedDTO.getFixBomId());
        } else {
            materialBillDTO.setMaterialName(fixBomDetailDTO.getItemSupplierNo());
        }
        materialBillDTO.setMaterialCategory(fixBomDetailDTO.getCustomerComponentType());
        materialBillDTO.setInstructionBomNumber(psTaskExtendedDTO.getBillNo());
        materialBillDTO.setQuantityUnit(Integer.parseInt(StringUtils.defaultString(fixBomDetailDTO.getItemNumber(), NumConstant.STRING_ONE)));
        materialBillDTO.setItemType(fixBomDetailDTO.getItemType());
        this.setControlLevel(psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);
        materialBillDTO.setItemLevel(fixBomDetailDTO.getItemLevel());
        materialBillDTO.setItemSeq(fixBomDetailDTO.getItemSeq());
        materialBillDTO.setMaterialBillList(new ArrayList<>());
        paramErrors = VALID.validate(materialBillDTO);
        if (CollectionUtils.isNotEmpty(paramErrors)) {
            List<String> errorMsg = paramErrors.stream().map(error -> error.getMessage()).collect(Collectors.toList());
            logger.info("materialBillDTO参数校验失败: " + errorMsg + "###" + JSONObject.toJSONString(materialBillDTO));
            // 参数校验不通过排除
            return;
        }
        list.add(materialBillDTO);
    }

    /**
     * 不是成品料且是buffer任务control_level传1,其余不传
     * @param psTaskExtendedDTO
     * @param fixBomDetailDTO
     * @param materialBillDTO
     */
    private void setControlLevel(PsTaskExtendedDTO psTaskExtendedDTO, FixBomDetailDTO fixBomDetailDTO, MaterialBillDTO materialBillDTO, List<String> aliControlItemNos) {
        if (!BusinessSceneEnum.BUFFER.getCode().equals(psTaskExtendedDTO.getBusinessScene())) {
            // 非BUFFER无需传control_level
            return;
        }

        if (specificItemTypeList.contains(fixBomDetailDTO.getItemType())) {
            // 成品料无需传control_level
            return;
        }

        // 是buffer任务且不是成品料时，是阿里管控物料传1，非阿里管控料传3
        if (aliControlItemNos.contains(fixBomDetailDTO.getZteCode())) {
            materialBillDTO.setControlLevel(aliControlLevel);
        } else {
            // 不是成品料且是buffer任务传3,其余不传
            materialBillDTO.setControlLevel(defaultControlLevel);
        }
    }

    /**
     * 根据层级关系生成物料代码树
     * 中间断层则连接到更上层
     *
     * @param list
     * @return
     */
    private List<MaterialBillDTO> getMaterialBillList(List<MaterialBillDTO> list) {
        List<MaterialBillDTO> node = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return node;
        }
        // 记录L6成品料对应的itemSeq
        List<String> l6SeqList = list.stream().filter(temp -> specificItemTypeList.contains(temp.getItemType()) && specificMaterialCategoryList.contains(temp.getMaterialCategory()))
                .map(MaterialBillDTO::getItemSeq).distinct().collect(Collectors.toList());
        // 筛除L6成品料记录
        list = list.stream().filter(temp -> !(specificItemTypeList.contains(temp.getItemType()) && specificMaterialCategoryList.contains(temp.getMaterialCategory()))).collect(Collectors.toList());
        Map<String, List<MaterialBillDTO>> groupMap = list.stream()
                .collect(Collectors.groupingBy(MaterialBillDTO::getItemLevel));
        List<String> levelList = new ArrayList<>(groupMap.keySet());
        levelList.sort(Comparator.comparing(Integer::parseInt));
        // 记录已在树中存在的元素,key:itemSeq
        Map<String, MaterialBillDTO> seqMaterialMap = new HashMap<>();

        List<MaterialBillDTO> topList = groupMap.get(levelList.get(NUM_ZERO));
        topList.sort(Comparator.comparing(MaterialBillDTO::getItemSeq));
        node.addAll(topList);
        for (MaterialBillDTO materialBillDTO : topList) {
            seqMaterialMap.put(materialBillDTO.getItemSeq(), materialBillDTO);
        }

        // 记录moc物料的原父物料
        List<MaterialBillDTO> mocOriginalParentItemList = new ArrayList<>();
        // 从第二层开始遍历
        for (int i = NumConstant.NUM_ONE; i < levelList.size(); i++) {
            List<MaterialBillDTO> tempList = groupMap.get(levelList.get(i));
            tempList.sort(Comparator.comparing(MaterialBillDTO::getItemSeq));
            dealByLevel(tempList, seqMaterialMap, l6SeqList, mocOriginalParentItemList);
        }
        for (MaterialBillDTO mocOriginalParentItem : mocOriginalParentItemList) {
            if (!specificItemTypeList.contains(mocOriginalParentItem.getItemType())) {
                // 非成品料不处理
                continue;
            }
            if (!CollectionUtils.isEmpty(mocOriginalParentItem.getMaterialBillList())) {
                // 存在其它子部件，不处理
                continue;
            }
            // 是成品料且不存在其它子部件，需移除该节点
            String upstairsSeq = StringUtils.substring(mocOriginalParentItem.getItemSeq(), NUM_ZERO, mocOriginalParentItem.getItemSeq().lastIndexOf(Constant.HORIZON));
            MaterialBillDTO upstairsItem = this.getUpstairsItem(seqMaterialMap, upstairsSeq, l6SeqList);
            if (upstairsItem == null || upstairsItem.getMaterialBillList() == null) {
                return node;
            }
            upstairsItem.getMaterialBillList().remove(mocOriginalParentItem);
        }
        return node;
    }

    /**
     * 按层级顺序处理元素
     *
     * @param tempList                  当前层级全部元素
     * @param seqMaterialMap            记录已在树中存在的元素,key:itemSeq
     * @param l6SeqList                 记录L6成品料对应的itemSeq
     * @param mocOriginalParentItemList 记录moc物料的原父物料
     */
    private void dealByLevel(List<MaterialBillDTO> tempList, Map<String, MaterialBillDTO> seqMaterialMap, List<String> l6SeqList, List<MaterialBillDTO> mocOriginalParentItemList) {
        for (MaterialBillDTO temp : tempList) {
            // 获取上层元素
            String upstairsSeq = StringUtils.substring(temp.getItemSeq(), NUM_ZERO, temp.getItemSeq().lastIndexOf(Constant.HORIZON));
            MaterialBillDTO upstairsItem = this.getUpstairsItem(seqMaterialMap, upstairsSeq, l6SeqList);
            if (upstairsItem == null) {
                // 没找到上层元素,丢弃
                continue;
            }
            if (specificCustomerComponentType.contains(temp.getMaterialCategory())) {
                mocOriginalParentItemList.add(upstairsItem);
                // 如果客户部件类型是server.configmodel.moc，则将该行的数据放到整机的顶层下。
                upstairsSeq = StringUtils.substring(temp.getItemSeq(), NUM_ZERO, temp.getItemSeq().indexOf(Constant.HORIZON));
                upstairsItem = this.getUpstairsItem(seqMaterialMap, upstairsSeq, l6SeqList);
            }
            if (upstairsItem == null || upstairsItem.getMaterialBillList() == null) {
                return;
            }
            upstairsItem.getMaterialBillList().add(temp);
            seqMaterialMap.put(temp.getItemSeq(), temp);
        }
    }

    /**
     * 根据itemSeq查找上层元素
     * 若中间断层则衔接到更上层
     * 例:itemSeq为1-1-1的元素,先找itemSeq为1-1的元素,若不存在则继续找itemSeq为1(顶层)的元素
     * @param seqMaterialMap
     * @param upstairsSeq
     * @return
     */
    private MaterialBillDTO getUpstairsItem(Map<String, MaterialBillDTO> seqMaterialMap, String upstairsSeq, List<String> l6SeqList) {
        if (l6SeqList.contains(upstairsSeq)) {
            // 物料分类为L6包，且物料类型为成品料，需要排除此记录，同时将其下层物料直接挂到第一层
            String nextItemSeq = StringUtils.substring(upstairsSeq, NUM_ZERO, upstairsSeq.indexOf(Constant.HORIZON));
            return this.getUpstairsItem(seqMaterialMap, nextItemSeq, l6SeqList);
        } else if (!seqMaterialMap.containsKey(upstairsSeq)) {
            if (!StringUtils.contains(upstairsSeq, Constant.HORIZON)) {
                // 找不到上层元素且不存在更上一层,则返回空
                return null;
            }
            String nextItemSeq = StringUtils.substring(upstairsSeq, NUM_ZERO, upstairsSeq.lastIndexOf(Constant.HORIZON));
            return this.getUpstairsItem(seqMaterialMap, nextItemSeq, l6SeqList);
        }
        return seqMaterialMap.get(upstairsSeq);
    }

    private void setScheduleDate(ManufactureOrderDTO manufactureOrderDTO, String taskNo, String factoryId) throws Exception {
        Map<String, Date> startAndEndTimeMap = centerFactoryCallSiteService.queryScheduleStartAndEndTime(taskNo, factoryId);
        Date dateScheduleDProduction = startAndEndTimeMap != null ? startAndEndTimeMap.get("scheduleStartDate") : null;
        Date dateEstimation = startAndEndTimeMap != null ? startAndEndTimeMap.get("scheduleEndDate") : null;
        // 预计完工日期 需查询本地服务计划计划结束时间
        manufactureOrderDTO.setDateEstimatedCompletion(dateEstimation);
        // 预计投产日期 需查询本地服务计划计划开始时间
        manufactureOrderDTO.setDateScheduledProduction(dateScheduleDProduction);
    }

    /**
     * 设置客户部件类型，若任务服务表无该属性数据则从PDM获取并更新至任务附属信息表
     * @param manufactureOrderDTO
     * @param itemNo
     * @param psTaskExtendedDTO
     */
    private void setAndUpdateCustomPartType(ManufactureOrderDTO manufactureOrderDTO, String itemNo, PsTaskExtendedDTO psTaskExtendedDTO) {
        if (StringUtils.isNotBlank(psTaskExtendedDTO.getCustomerPartType())) {
            // 附属信息则直接设置
            manufactureOrderDTO.setCategory(psTaskExtendedDTO.getCustomerPartType());
            return;
        }
        String customPartType = pdmRemoteService.getCategory(itemNo);
        if (StringUtils.isNotBlank(customPartType)) {
            manufactureOrderDTO.setCategory(customPartType);
            psTaskExtendedDTO.setCustomerPartType(customPartType);
            // 根据任务号更新任务服务信息表
            psTaskExtendedService.updateCustomPartType(customPartType, psTaskExtendedDTO.getTaskNo());
        }
    }

    /**
     * 校验失败次数并更新数据
     * @param pushStdModelDTO
     */
    private <T extends PushStdModelDTO> void checkFailTimeAndUpdate(T pushStdModelDTO) {
        if (pushStdModelDTO instanceof PushStdModelDataDTO) {
            PushStdModelDataDTO pushStdModelDataDTO = (PushStdModelDataDTO) pushStdModelDTO;
            pushStdModelDataDTO.setPushFailCount(pushStdModelDataDTO.getPushFailCount() + NumConstant.NUM_ONE);
            if (pushStdModelDataDTO.getPushFailCount() >= pushFailThreshold) {
                // 失败3次才修改状态,修改后不会再捞取
                pushStdModelDataDTO.setPushStatus(DATA_CHECK_OR_PUSH_FAIL);
            }
            pushStdModelDataRepository.update(pushStdModelDataDTO);
        } else if (pushStdModelDTO instanceof PushStdModelConfirmationDTO) {
            PushStdModelConfirmationDTO confirmationDTO = (PushStdModelConfirmationDTO) pushStdModelDTO;
            confirmationDTO.setPushFailCount(confirmationDTO.getPushFailCount() + NumConstant.NUM_ONE);
            if (confirmationDTO.getPushFailCount() >= pushFailThreshold) {
                // 失败3次才修改状态,修改后不会再捞取
                confirmationDTO.setPushStatus(DATA_CHECK_OR_PUSH_FAIL);
            }
            pushStdModelConfirmationRepository.update(confirmationDTO);
        }
    }

}
