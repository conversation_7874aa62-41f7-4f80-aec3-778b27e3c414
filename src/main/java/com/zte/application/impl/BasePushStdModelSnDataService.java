package com.zte.application.impl;

import com.zte.application.PsTaskExtendedService;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.PushStdModelSnDataSubRepository;
import com.zte.springbootframe.util.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/13 15:58
 */
@Slf4j
public abstract class BasePushStdModelSnDataService {

    @Value("${pushStdModelSnData.currProcess.productSnReport:30}")
    protected String productSnReportCurrProcess;

    @Value("${pushStdModelSnData.currProcess.finishedProductStorage:40}")
    protected String finishedProductStorageCurrProcess;

    @Value("${pushStdModelSnData.currProcess.sortedList:25,30,40}")
    protected List<String> currProcessSortedList;

    @Resource
    protected IdGenerator idGenerator;
    @Resource
    protected PushStdModelSnDataRepository pushStdModelSnDataRepository;
    @Resource
    protected PushStdModelSnDataSubRepository pushStdModelSnDataSubRepository;

    @Autowired
    PsTaskExtendedService psTaskExtendedService;
}
