package com.zte.application.impl;

import cn.hutool.core.util.RandomUtil;
import com.zte.application.AlibabaPushService;
import com.zte.application.CustomerItemsService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.infrastructure.remote.ErpRemoteService;
import com.zte.infrastructure.remote.WmsRemoteService;
import com.zte.interfaces.dto.AlibabaOnHandQtyVO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerInventoryListVO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.erp.GetOnhandQtyDTO;
import com.zte.interfaces.dto.erp.GetOnhandQtyResultLineDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyDTO;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyResultDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.NoticeCenterUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("AlibabaPushService")
@RequiredArgsConstructor
@Log4j2
public class AlibabaPushServiceImpl implements AlibabaPushService {
    /**
     * log日志
     */
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    /**
     * 数据字典
     */
    private final SysLookupValuesService sysLookupValuesService;
    /**
     * wms
     */
    private final WmsRemoteService wmsRemoteService;
    /**
     * B2B
     */
    private final TradeDataLogService tradeDataLogService;
    /**
     * 物料
     */
    private final CustomerItemsService customerItemsService;
    /**
     * CQPD
     */
    private final CpqdRemoteService cpqdRemoteService;
    /**
     * 通知中心
     */
    private final NoticeCenterUtils noticeCenterUtils;
    /**
     * erp
     */
    private final ErpRemoteService erpRemoteService;

    private Set<String> errList;

    @Override
    public ArrayList<AlibabaOnHandQtyVO> onHandQtyPush(String isGetData) throws Exception {
        errList = new HashSet<>();
        // 获取仓库
        SysLookupValues warehouseLookup = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_CODE_7504001);
        if (null == warehouseLookup){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_7504});
        }
        if (StringUtils.isBlank(warehouseLookup.getAttribute2())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_7504});
        }


        // 查zte_code
        /*
          <fixbomid,<customerItemName,List<ItemCode>>>
         */
        Map<String, String> zteCodeCustomerItemNameMap = new HashMap<>();
        Map<String, List<String>> fixBomIdItemCodeMap = getFixBomIdItemCodeMap(zteCodeCustomerItemNameMap);

        // 查库存
        //<仓库类型字典项编码,<fixbomid,<customerItemName,onHandQty>>>
        Map<String, List<GetOnhandQtyResultLineDTO>> itemCodeOnHandQtyMap = getItemCodeOnHandQtyMap(warehouseLookup, fixBomIdItemCodeMap);

        // 构建返回体
        ArrayList<AlibabaOnHandQtyVO> alibabaOnHandQtyList = buildAlibabaOnHandQtyHead(warehouseLookup, itemCodeOnHandQtyMap, fixBomIdItemCodeMap, zteCodeCustomerItemNameMap);

        if (!Constant.FLAG_Y.equals(isGetData)) {
            pushToB2B(alibabaOnHandQtyList);
        }
        if (!errList.isEmpty()) {
            LOG.error(String.join(Constant.COMMA, errList));
            noticeCenterUtils.sendEmail(Constant.LOOK_UP_VALUE_7599003, String.join(Constant.EMAIL_PREFIX, errList));
        }

        return !Constant.FLAG_Y.equals(isGetData) ? null : alibabaOnHandQtyList;
    }

    private Map<String, List<GetOnhandQtyResultLineDTO>> getItemCodeOnHandQtyMap(SysLookupValues warehouseErpMap, Map<String, List<String>> fixBomIdItemCodeMap) {
        GetOnhandQtyDTO getOnhandQtyDTO = new GetOnhandQtyDTO();
        getOnhandQtyDTO.setSubinventoryCode(warehouseErpMap.getAttribute2());
        List<String> itemCodeList = fixBomIdItemCodeMap.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        getOnhandQtyDTO.setItemCode(String.join(Constant.COMMA, itemCodeList));
        // 查在库
        return erpRemoteService.getOnHandQty(getOnhandQtyDTO).stream().collect(Collectors.groupingBy(GetOnhandQtyResultLineDTO::getSegment1));
    }

    private Map<String, List<String>> getFixBomIdItemCodeMap(Map<String, String> zteCodeCustomerItemNameMap) {
        CpqdQueryDTO cpqdQueryDTO = new CpqdQueryDTO();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName(Constant.ALIBABA);
        customerItemsDTO.setProjectName(Constant.ALIBABA_PUBLIC_CLOUD);
        customerItemsDTO.setProjectTypeList(Collections.singletonList(NumConstant.STR_3));
        customerItemsDTO.setRows(Constant.BATCH_SIZE_1000);
        Page<CustomerItemsDTO> customerItemsDTOPage;
        Map<String, List<String>> fixBomIdItemCodeMap = new HashMap<>();
        do {
            customerItemsDTOPage = customerItemsService.pageCustomerItemsInfo(customerItemsDTO);
            if (null == customerItemsDTOPage.getRows() || customerItemsDTOPage.getRows().isEmpty()) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_NULL);
            }
            customerItemsDTOPage.getRows().forEach(customerItemsDTO1 -> {
                // 过滤zte_code为空的
                if (StringUtils.isBlank(customerItemsDTO1.getZteCode())) {
                    LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES);
                    errList.add(lmb.getMessage(MessageId.ITEM_NO_NULL));
                }
            });
            cpqdQueryDTO.setInstanceNo(customerItemsDTOPage.getRows().stream().map(CustomerItemsDTO::getZteCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            List<CpqdGbomDTO> cpqdGbomDTOS = cpqdRemoteService.queryGbomList(cpqdQueryDTO);
            this.forEachGboms(zteCodeCustomerItemNameMap, cpqdGbomDTOS, cpqdQueryDTO, fixBomIdItemCodeMap);
            // 没有查询到的数据
            cpqdQueryDTO.getInstanceNo().forEach(zteCode -> {
                LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES);
                errList.add(lmb.getMessage(MessageId.ZTE_CODE_FIXBOMID_IS_NULL, new Object[]{zteCode}));
            });
            customerItemsDTO.setPage(customerItemsDTO.getPage() + NumConstant.NUM_ONE);
        } while (customerItemsDTO.getPage() <= customerItemsDTOPage.getTotalPage());
        return fixBomIdItemCodeMap;
    }

    private void forEachGboms(Map<String, String> zteCodeCustomerItemNameMap, List<CpqdGbomDTO> cpqdGboList, CpqdQueryDTO cpqdQueryDTO, Map<String, List<String>> fixBomIdItemCodeMap) {
        for (CpqdGbomDTO cpqdGbomDTO : cpqdGboList) {
            if (StringUtils.isNotBlank(cpqdGbomDTO.getInstanceNo()) && StringUtils.isNotBlank(cpqdGbomDTO.getCbomCode())) {
                // 去除查询到的数据，获得没有查询到的数据
                cpqdQueryDTO.getInstanceNo().remove(cpqdGbomDTO.getInstanceNo());
                fixBomIdItemCodeMap.computeIfAbsent(cpqdGbomDTO.getCbomCode(), key -> new ArrayList<>()).add(cpqdGbomDTO.getInstanceNo());
                if (StringUtils.isNotBlank(cpqdGbomDTO.getCustomerItemName())) {
                    zteCodeCustomerItemNameMap.put(cpqdGbomDTO.getInstanceNo(), cpqdGbomDTO.getCustomerItemName());
                }
            }
        }
    }

    private void pushToB2B(ArrayList<AlibabaOnHandQtyVO> alibabaOnHandQtyVOS) throws Exception {
        ArrayList<CustomerDataLogDTO> customerDataLogDTOS = new ArrayList<>();
        alibabaOnHandQtyVOS.forEach(vo -> {
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
            customerDataLogDTO.setOrigin(Constant.IMES);
            customerDataLogDTO.setCustomerName(Constant.ALIBABA);
            customerDataLogDTO.setProjectName(Constant.ALIBABA);
            customerDataLogDTO.setProjectPhase(Constant.MESSAGE_TYPE_SYNC_CUSTOMER_INVENTORY_POST.substring(Constant.MESSAGE_TYPE_SYNC_CUSTOMER_INVENTORY_POST.lastIndexOf(Constant.LINE) + NumConstant.NUM_ONE));
            customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_SYNC_CUSTOMER_INVENTORY_POST);
            customerDataLogDTO.setFactoryId(Integer.valueOf(Constant.FACTORY_ID_CENTER));
            customerDataLogDTO.setCreateBy(Constant.IMES);
            customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
            customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(vo));
            customerDataLogDTOS.add(customerDataLogDTO);
        });
        tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTOS);
    }

    /**
     * 构建返回头
     * @param warehouseLookup 仓库类型字典
     * @param itemCodeOnHandQtyMap <仓库类型字典项编码,<fixbomid,<customerItemName,onHandQty>>>
     * @param fixBomIdItemCodeMap <fixbomid,<customerItemName,List<ItemCode>>>
     * @return
     */
    @NotNull
    private ArrayList<AlibabaOnHandQtyVO> buildAlibabaOnHandQtyHead(SysLookupValues warehouseLookup, Map<String, List<GetOnhandQtyResultLineDTO>> itemCodeOnHandQtyMap, Map<String, List<String>> fixBomIdItemCodeMap, Map<String, String> zteCodeCustomerItemNameMap) {
        ArrayList<AlibabaOnHandQtyVO> alibabaOnHandQtyVOS = new ArrayList<>();
        AlibabaOnHandQtyVO alibabaOnHandQtyVO = new AlibabaOnHandQtyVO();
        String warehouseTypeValue = warehouseLookup.getLookupMeaning();
        alibabaOnHandQtyVO.setInventoryType(Integer.valueOf(warehouseTypeValue));
        String inventoryDirectiveStr = Constant.ZTE + DateUtil.convertNowToString("yyyyMMddHHmmssSSS") + RandomUtil.randomNumbers(NumConstant.NUM_TEN);
        alibabaOnHandQtyVO.setInventoryDirective(inventoryDirectiveStr);
        SysLookupValues factoryCodeLookup = sysLookupValuesService.findByLookupCode(Integer.valueOf(Constant.LOOKUP_CODE_7505001));
        if (null == factoryCodeLookup) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_LOOKUP_VALUE_ERROR, new Object[]{Constant.LOOKUP_CODE_7505001});
        }
        alibabaOnHandQtyVO.setFactoryCode(factoryCodeLookup.getLookupMeaning());
        List<CustomerInventoryListVO> customerInventoryListVO = buildCustomerInventoryLine(itemCodeOnHandQtyMap, fixBomIdItemCodeMap, zteCodeCustomerItemNameMap);
        alibabaOnHandQtyVO.setCustomerInventoryList(customerInventoryListVO);
        alibabaOnHandQtyVOS.add(alibabaOnHandQtyVO);
        return alibabaOnHandQtyVOS;
    }

    /**
     * 构建返回明细
     * @param itemCodeOnHandQtyMap
     * @param fixBomIdItemCodeMap
     * @return
     */
    private List<CustomerInventoryListVO> buildCustomerInventoryLine(Map<String, List<GetOnhandQtyResultLineDTO>> itemCodeOnHandQtyMap, Map<String, List<String>> fixBomIdItemCodeMap, Map<String, String> zteCodeCustomerItemNameMap) {
        List<CustomerInventoryListVO> customerInventoryListVOS = new ArrayList<>();
        int customerInventoryLineNumber = NumConstant.NUM_ONE;
        for (Map.Entry<String, List<String>> fixbomItemCodeEntry : fixBomIdItemCodeMap.entrySet()) {
            String fixBomId = fixbomItemCodeEntry.getKey();
            List<String> itemCodes = fixbomItemCodeEntry.getValue().stream().distinct().collect(Collectors.toList());
            CustomerInventoryListVO customerInventoryListVO = new CustomerInventoryListVO();
            customerInventoryListVO.setCustomerInventoryLineNumber(customerInventoryLineNumber);
            customerInventoryListVO.setMpn(fixBomId);
            customerInventoryListVO.setConfigModel(zteCodeCustomerItemNameMap.get(itemCodes.get(0)));
            /* Started by AICoder, pid:o637707bb494115143cb0888b05515072db544a0 */
            List<GetOnhandQtyResultLineDTO> onhandQtyList = itemCodes.stream()
                    .map(itemCodeOnHandQtyMap::get) // 获取每个键的列表
                    .filter(Objects::nonNull) // 过滤掉null值
                    .flatMap(List::stream) // 展平嵌套的列表
                    .collect(Collectors.toList());
            /* Ended by AICoder, pid:o637707bb494115143cb0888b05515072db544a0 */
            customerInventoryListVO.setVendorInventoryQuantity(onhandQtyList.stream().mapToInt(GetOnhandQtyResultLineDTO::getQuantity).sum());
            WmsQueryItemQtyDTO wmsQueryItemQtyDTO = new WmsQueryItemQtyDTO();
            wmsQueryItemQtyDTO.setItemNos(itemCodes);
            customerInventoryListVO.setDeliveredQuantity(wmsRemoteService.selectWarehouseToWms(wmsQueryItemQtyDTO).stream().mapToInt(WmsQueryItemQtyResultDTO::getDeliveryQty).sum());
            customerInventoryListVOS.add(customerInventoryListVO);
            customerInventoryLineNumber++;

        }
        return customerInventoryListVOS;
    }
}
