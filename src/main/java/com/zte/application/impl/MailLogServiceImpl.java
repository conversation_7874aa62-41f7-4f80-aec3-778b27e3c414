package com.zte.application.impl;

import com.sun.mail.smtp.SMTPAddressFailedException;
import com.zte.application.MailLogService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MailLog;
import com.zte.domain.model.MailLogRepository;
import com.zte.interfaces.dto.MailLogPageQueryDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.MESHttpHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.Address;
import javax.mail.SendFailedException;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 邮件日志服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-28 17:02:02
 */
@Slf4j
@Service("mailLogService")
public class MailLogServiceImpl implements MailLogService {

    @Autowired
    private MailLogRepository mailLogRepository;
    @Autowired
    private MailProperties mailProperties;
    @Autowired
    private JavaMailSender javaMailSender;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;

    private static final String WARM_REMINDER_EN = "Warm Reminder: This email is automatically sent by the system.Please do not reply to this email.";
    private static final String MORE_SEE_EN = "More see,";
    private static final String MORE_SEE_CLICK_EN = "click";
    private static final String HTML_A = "<a style=\"color: rgb(0, 160, 233); text-decoration-line: none;\" target=\"_blank\" href=\"";

    @Override
    public void send(MailLog mailLog) {
        if (StringUtils.isBlank(mailLog.getMailTo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMAIL_TO_IS_EMPTY);
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        mailLog.setId(UUID.randomUUID().toString());
        mailLog.setCreateBy(empNo);
        mailLog.setLastUpdatedBy(empNo);
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = null;
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
            // 邮件发送来源
            mimeMessageHelper.setFrom(StringUtils.isNotEmpty(mailLog.getMailFrom()) ? mailLog.getMailFrom() : mailProperties.getUsername());
            // 邮件发送目标
            mailLog.setMailToList(splitEmail(mailLog.getMailTo()));
            mimeMessageHelper.setTo(mailLog.getMailToList().toArray(new String[0]));
            if (StringUtils.isNotEmpty(mailLog.getMailCc())) {
                mailLog.setMailCcList(splitEmail(mailLog.getMailCc()));
                mimeMessageHelper.setCc(mailLog.getMailCcList().toArray(new String[0]));
            }
            if (StringUtils.isNotEmpty(mailLog.getMailBcc())) {
                mailLog.setMailBccList(splitEmail(mailLog.getMailBcc()));
                mimeMessageHelper.setBcc(mailLog.getMailBccList().toArray(new String[0]));
            }
            // 设置标题
            String subject = mailLog.getSubject();
            if (NumConstant.NUM_TWO == mailLog.getLang()) {
                subject = StringUtils.isNotBlank(mailLog.getSubjectEn()) ? mailLog.getSubjectEn() : mailLog.getSubject();
            } else if (NumConstant.NUM_THREE == mailLog.getLang()) {
                subject = subject + "/" + mailLog.getSubjectEn();
            }
            mimeMessageHelper.setSubject(subject);

            // 设置内容 (温馨提示 + 内容 + 查看更多)
            mimeMessageHelper.setText(getMailText(mailLog), true);
            // 发送
            javaMailSender.send(mimeMessage);
            // 保存
            mailLog.setStatus(NumConstant.NUM_ONE);
            mailLogRepository.insertSelective(mailLog);
        } catch (Exception e) {
            if(mimeMessageHelper==null){
                return;
            }
            mailLog.setErrMsg(e.getMessage());
            HashSet<String> failedMails = new HashSet<>();
            if (mailSendExceptionHandler(mailLog, mimeMessage, mimeMessageHelper, failedMails, e)) {
                // 去掉无效的地址，重置发送成功!
                return;
            }
            mailLog.setStatus(NumConstant.NUM_TWO);
            mailLog.setErrMsg(e.getMessage() + ";Invalid email:" + failedMails);
            mailLogRepository.insertSelective(mailLog);
            mailLog.setErrMsg(e.getMessage() + ";Invalid email:" + failedMails);
            log.error("Send mail error: ", e);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMAIL_SEND_ERR, new String[]{e.getMessage()});
        }
    }

    private boolean mailSendExceptionHandler(MailLog mailLog, MimeMessage mimeMessage, MimeMessageHelper mimeMessageHelper,Set<String> failedMails, Exception e) {
        try {
            if (!(e instanceof MailSendException)) {
                return false;
            }

            ((MailSendException) e).getFailedMessages().values().forEach(exception -> {
                if (exception instanceof SMTPAddressFailedException) {
                    failedMails.add(((SMTPAddressFailedException) exception).getAddress().toString());
                } else if (exception instanceof SendFailedException) {
                    for (Address address : ((SendFailedException) exception).getInvalidAddresses()) {
                        failedMails.add(address.toString());
                    }
                }
            });

            List<String> to = mailLog.getMailToList();
            to.removeAll(failedMails);
            if (!to.isEmpty()) {
                mimeMessageHelper.setTo(to.toArray(new String[0]));
                if (StringUtils.isNotEmpty(mailLog.getMailCc())) {
                    List<String> cc = mailLog.getMailCcList();
                    cc.removeAll(failedMails);
                    mimeMessageHelper.setCc(cc.toArray(new String[0]));
                }
                if (StringUtils.isNotEmpty(mailLog.getMailBcc())) {
                    List<String> bcc = mailLog.getMailBccList();
                    bcc.removeAll(failedMails);
                    mimeMessageHelper.setBcc(bcc.toArray(new String[0]));
                }
                javaMailSender.send(mimeMessage);
                mailLog.setStatus(NumConstant.NUM_TWO);
                mailLog.setErrMsg("Invalid email:" + failedMails);
                mailLogRepository.insertSelective(mailLog);
                return true;
            }
        } catch (Exception e1) {
            if (expcetionHandder(mailLog, mimeMessage, mimeMessageHelper, failedMails, e1)) {
                return true;
            }
        }
        return false;
    }

    private boolean expcetionHandder(MailLog mailLog, MimeMessage mimeMessage, MimeMessageHelper mimeMessageHelper, Set<String> failedMails, Exception e1) {
        mailLog.setErrNum(null == mailLog.getErrNum() ? NumConstant.NUM_ONE : (mailLog.getErrNum() + NumConstant.NUM_ONE));
        if (mailLog.getErrNum() < NumConstant.NUM_TEN && mailSendExceptionHandler(mailLog, mimeMessage, mimeMessageHelper, failedMails, e1)) {
            return true;
        }
        mailLog.setErrMsg(e1.getMessage());
        return false;
    }
    private List<String> splitEmail(String email) {
        String[] emailArr = email.split(Constants.SEMICOLON + "|" + Constants.COMMA);
        List<String> tmp = new ArrayList<>();//新建List
        for (String str : emailArr) {
            if (str != null && str.length() != 0) {
                if (str.matches("\\d+")) {
                    str = str + Constant.ZTE_MAILBOX_DOMAIN_NAME;
                }
                tmp.add(str);
            }
        }
        return tmp;
    }

    private String getMailText(MailLog mailLog) {
        StringBuilder text = new StringBuilder("<div style=\"color:#5497c5\">");
        if (NumConstant.NUM_ONE == mailLog.getLang() || NumConstant.NUM_THREE == mailLog.getLang()) {
            text.append(StringUtils.isNotEmpty(mailLog.getWarmReminder()) ? mailLog.getWarmReminder() : Constant.WARM_REMINDER)
                    .append("</div><br>").append(mailLog.getText());
            if (StringUtils.isNotBlank(mailLog.getLinkUrl())) {
                text.append("<br>");
                if (StringUtils.isNotBlank(mailLog.getMoreSee())) {
                    text.append(HTML_A).append(mailLog.getLinkUrl()).append("\">").append(mailLog.getMoreSee()).append("</a>");
                } else {
                    text.append(Constant.MORE_SEE).append(HTML_A).append(mailLog.getLinkUrl()).append("\">").append(Constant.MORE_SEE_CLICK).append("</a>");
                }
                text.append("<br><iframe src=\"").append(mailLog.getLinkUrl()).append("\" frameborder=\"0\" width=\"100%\" style=\"border-width: initial; border-style: none; min-height: 800px;\" />");
            }
        }
        if (NumConstant.NUM_THREE == mailLog.getLang()) {
            text.append("<hr>");
            text.append("<div style=\"color:#5497c5\">");
        }
        appendEnTexy(mailLog, text);
        return text.toString();
    }

    private void appendEnTexy(MailLog mailLog, StringBuilder text) {
        if (NumConstant.NUM_TWO == mailLog.getLang() || NumConstant.NUM_THREE == mailLog.getLang()) {
            text.append(StringUtils.isNotEmpty(mailLog.getWarmReminderEn()) ? mailLog.getWarmReminderEn() : WARM_REMINDER_EN)
                    .append("</div><br>").append(StringUtils.isNotBlank(mailLog.getTextEn()) ? mailLog.getTextEn() : mailLog.getText());
            if (StringUtils.isNotBlank(mailLog.getLinkUrlEn())) {
                text.append("<br>");
                if (StringUtils.isNotBlank(mailLog.getMoreSeeEn())) {
                    text.append(HTML_A).append(mailLog.getLinkUrlEn()).append("\">").append(mailLog.getMoreSeeEn()).append("</a>");
                } else {
                    text.append(MORE_SEE_EN).append(HTML_A).append(mailLog.getLinkUrlEn()).append("\">").append(MORE_SEE_CLICK_EN).append("</a>");
                }
                text.append("<br><iframe src=\"").append(mailLog.getLinkUrlEn()).append("\" frameborder=\"0\" width=\"100%\" style=\"border-width: initial; border-style: none; min-height: 800px;\" />");
            }
        }
    }

    @Override
    public PageRows<MailLog> queryPage(MailLogPageQueryDTO query) {
        PageRows<MailLog> pageRows = new PageRows<>();
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(mailLogRepository.countPage(query));
        if (pageRows.getTotal() > NumConstant.LONG_ZERO) {
            pageRows.setRows(mailLogRepository.queryPage(query));
        }
        return pageRows;
    }

    @Override
    public MailLog getById(String id) {
        return mailLogRepository.selectById(id);
    }

    @Override
    public void add(MailLog mailLog) {
        mailLogRepository.insert(mailLog);
    }

    @Override
    public void updateById(MailLog mailLog) {
        mailLogRepository.updateById(mailLog);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        mailLogRepository.deleteByIds(ids);
    }

    /**
    * @Description: 根据文档ID从文档云获取文件
    * @Param: [docId]
    * @return: void
    * @Author: Saber[10307315]
    * @Date: 2023/4/18 上午11:03
    */
    @Override
    public void getFileOfCloudDiskByDocId(HttpServletResponse response, String docId) throws Exception {
        cloudDiskHelper.downloadFile(response, docId, null, null);
    }
}
