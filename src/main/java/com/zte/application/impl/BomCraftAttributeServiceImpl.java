package com.zte.application.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.zte.application.BomCraftAttributeService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BomCraftAttribute;
import com.zte.domain.model.BomCraftAttributeRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.assembler.BomCraftAttributeAssembler;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BomCraftAttributeDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.WorkOrderCraftAttributeDTO;
import com.zte.interfaces.dto.datawb.BoardStoveMaintenanceDTO;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.HAND_PASTE;
import static com.zte.common.utils.Constant.INT_0;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_1245;
import static com.zte.common.utils.Constant.SEMICOLON;
import static com.zte.common.utils.Constant.SMTA;
import static com.zte.common.utils.Constant.SMTB;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.Constant.X_FACTORY_ID;
import static com.zte.common.utils.constant.RedisKeyConstant.BOM_CRAFT_ATTRIBUTE_EXPORT;
import static com.zte.common.utils.constant.RedisKeyConstant.LOCK;

@Service
public class BomCraftAttributeServiceImpl implements BomCraftAttributeService {

    private static final Logger logger = LoggerFactory.getLogger(CfInventoryServiceImpl.class);

    @Autowired
    private BomCraftAttributeRepository bomCraftAttributeRepository;
    @Autowired
    private BBomHeaderRepository bBomHeaderRepository;
    @Autowired
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    EmailUtils emailUtils;

    @Override
    public Page<BomCraftAttributeDTO> getBomFurnaceTempPage(BomCraftAttributeDTO record) throws Exception {
        Page<BomCraftAttributeDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        this.checkQueryParam(record);
        pageInfo.setParams(record);
        List<BomCraftAttribute> bomList = bomCraftAttributeRepository.getBomCraftAttributePage(pageInfo);

        // 转换人员工号及环保属性
        this.transEmpAndLead(bomList);

        pageInfo.setRows(BomCraftAttributeAssembler.toBomFurnaceTempRelDTOList(bomList));
        return pageInfo;
    }

    private void checkQueryParam(BomCraftAttributeDTO record){
    	if(StringUtils.isEmpty(record.getBomNo()) && StringUtils.isEmpty(record.getFurnaceTempName())
				&& (StringUtils.isEmpty(record.getUpdateStartDate()) || StringUtils.isEmpty(record.getUpdateEndDate()))){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY);
		}
	}

    private void transEmpAndLead(List<BomCraftAttribute> bomList) throws Exception {
		List<String> strings = new ArrayList<>();
		List<String> createByList = bomList.stream().filter(item -> StringUtils.isNotBlank(item.getCreateBy()))
				.map(BomCraftAttribute::getCreateBy).distinct()
				.collect(Collectors.toList());
		List<String> lastUpdateByList = bomList.stream().filter(item -> StringUtils.isNotBlank(item.getLastUpdatedBy()))
				.map(BomCraftAttribute::getLastUpdatedBy).distinct()
				.collect(Collectors.toList());
		strings.addAll(createByList);
		strings.addAll(lastUpdateByList);
		List<String> params = strings.stream().distinct().collect(Collectors.toList());
		Map<String, HrmPersonInfoDTO> bsPubHrMap = hrmUserCenterService.getHrmPersonInfo(params);
        List<SysLookupValues> sysLookupValues = sysLookupValuesRepository.selectValuesByType(Integer.parseInt(LOOK_UP_CODE_1245));
        Map<String, String> factoryMap = sysLookupValues.stream()
                .collect(Collectors.toMap(
                        SysLookupValues::getLookupMeaning,
                        SysLookupValues::getDescriptionChin,
                        (k1, k2) -> k1
                ));
		bomList.forEach(p -> {
			p.setLastUpdatedByName(p.getLastUpdatedBy());
			if (!StringUtils.isEmpty(p.getLastUpdatedBy()) && bsPubHrMap != null && bsPubHrMap.get(p.getLastUpdatedBy()) != null) {
				p.setLastUpdatedByName(bsPubHrMap.get(p.getLastUpdatedBy()).getEmpName() + p.getLastUpdatedBy());
			}
			p.setCreateByName(p.getCreateBy());
			if (!StringUtils.isEmpty(p.getCreateBy()) && bsPubHrMap != null && bsPubHrMap.get(p.getCreateBy()) != null) {
				p.setCreateByName(bsPubHrMap.get(p.getCreateBy()).getEmpName() + p.getCreateBy());
			}
			p.setLeadFlagMean(StringUtils.equals(p.getLeadFlag(), Constant.STR_0) ? Constant.IS_LEAD : Constant.LEAD_FREE);
            if(null != p.getFactoryId()){
                p.setFactoryName(factoryMap.getOrDefault(String.valueOf(p.getFactoryId()),STRING_EMPTY));
            }
		});
	}

    @Override
    public List<BomCraftAttribute> getBomFurnaceTempList(BomCraftAttributeDTO record) {
        List<BomCraftAttribute> bomList = bomCraftAttributeRepository.getBomCraftAttributeList(record);
        return bomList;
    }

    @Override
    public int updateBomFurnaceTemp(BomCraftAttributeDTO record) throws Exception {
        if (StringUtils.isEmpty(record.getLeadFlag()) || StringUtils.isEmpty(record.getFurnaceTempName())
                || StringUtils.isEmpty(record.getSurface())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        String redisKey = Constant.BOM_TEMP_REDIS_KEY + record.getBomNo();
        RedisLock redisLock = new RedisLock(redisKey, NumConstant.NUM_TWO * NumConstant.NUM_60);
        boolean lock = redisLock.lock();
        if (!lock) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.IS_UPDATING_PLEASE_WAIT);
        }
        try {
            List<BomCraftAttribute> bomList = bomCraftAttributeRepository.getBomCraftAttributeList(record);
            // 筛选完全匹配数据,避免先维护了批次或工厂后无法再维护批次和工厂为空数据
            bomList = bomList.stream()
                    .filter(curr -> ((record.getFactoryId() == null && curr.getFactoryId() == null) || (record.getFactoryId() != null && record.getFactoryId().equals(curr.getFactoryId())))
                            && StringUtils.equals(StringUtils.defaultString(record.getProdplanId()), StringUtils.defaultString(curr.getProdplanId())) && !StringUtils.equals(record.getId(), curr.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(bomList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_TEMPERATURE_IS_EXITED,
                        new String[]{record.getCraftSection(),record.getBomNo(), record.getSurface(),
                                StringUtils.equals(record.getLeadFlag(), Constant.STR_0) ? Constant.IS_LEAD : Constant.LEAD_FREE,
                                record.getFactoryName(), record.getProdplanId()});
            }
            int count = bomCraftAttributeRepository.updateBomCraftAttributeById(record);
            return count;
        } finally {
            redisLock.unlock();
        }
    }

    @Override
    public int insertBomFurnaceTemp(BomCraftAttributeDTO record) throws Exception {
        if (StringUtils.isEmpty(record.getCraftSection()) ||StringUtils.isEmpty(record.getLeadFlag()) ||
                StringUtils.isEmpty(record.getFurnaceTempName()) || CollectionUtils.isEmpty(record.getSurfaceList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        String redisKey = Constant.BOM_TEMP_REDIS_KEY + record.getBomNo();
        RedisLock redisLock = new RedisLock(redisKey, NumConstant.NUM_FIVE * NumConstant.NUM_60);
        boolean lock = redisLock.lock();
        if (!lock) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.IS_UPDATING_PLEASE_WAIT);
        }
        try {
            Page<BBomHeaderDTO> pageInfo = new Page<>(1, 10);
            BBomHeaderDTO bBomHeaderDTO = new BBomHeaderDTO();
            bBomHeaderDTO.setProductCode(record.getBomNo());
            pageInfo.setParams(bBomHeaderDTO);
            List<BBomHeader> tempList = bBomHeaderRepository.selectBBomHeaderByProductCode(pageInfo);
            if (CollectionUtils.isEmpty(tempList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_BOM_INFORMATION_IS_FOUND);
            }
            String errorSide = Constant.STRING_EMPTY;
            List<String> sideList = record.getSurfaceList();
            List<BomCraftAttributeDTO> insertList = new ArrayList<>();
            for (String str : sideList) {
                BomCraftAttributeDTO bomCraftAttributeDTO = new BomCraftAttributeDTO();
                bomCraftAttributeDTO.setCraftSection(record.getCraftSection());
                bomCraftAttributeDTO.setBomNo(record.getBomNo());
                bomCraftAttributeDTO.setProdplanId(record.getProdplanId());
                bomCraftAttributeDTO.setLeadFlag(record.getLeadFlag());
                bomCraftAttributeDTO.setSurface(str);
                bomCraftAttributeDTO.setFactoryId(record.getFactoryId());
                List<BomCraftAttribute> bomList = bomCraftAttributeRepository.getBomCraftAttributeList(bomCraftAttributeDTO);
                // 筛选完全匹配数据,避免先维护了批次或工厂后无法再维护批次和工厂为空数据
                bomList = bomList.stream()
                        .filter(curr -> ((record.getFactoryId() == null && curr.getFactoryId() == null) || (record.getFactoryId() != null && record.getFactoryId().equals(curr.getFactoryId())))
                                && StringUtils.equals(StringUtils.defaultString(record.getProdplanId()), StringUtils.defaultString(curr.getProdplanId())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(bomList)) {
                    errorSide = errorSide + Constant.COMMA + str;
                }
                bomCraftAttributeDTO.setId(UUID.randomUUID().toString().replaceAll(Constant.HORIZON, Constant.STRING_EMPTY));
                bomCraftAttributeDTO.setBomName(tempList.get(NumConstant.NUM_ZERO).getChiDesc());
                bomCraftAttributeDTO.setFurnaceTempName(record.getFurnaceTempName());
                if(record.getCraftSection().equals("SMT")){
                    bomCraftAttributeDTO.setVerNo(tempList.get(NumConstant.NUM_ZERO).getVerNo());
                }
                bomCraftAttributeDTO.setCreateBy(record.getCreateBy());
                bomCraftAttributeDTO.setLastUpdatedBy(record.getLastUpdatedBy());
                bomCraftAttributeDTO.setInkJetPrinterTemplate(record.getInkJetPrinterTemplate());
                bomCraftAttributeDTO.setInkJetPrinterSuffix(record.getInkJetPrinterSuffix());
                bomCraftAttributeDTO.setCraftSection(record.getCraftSection());
                bomCraftAttributeDTO.setFactoryId(record.getFactoryId());
                insertList.add(bomCraftAttributeDTO);
            }
            if (StringUtils.isNotEmpty(errorSide)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_TEMPERATURE_IS_EXITED,
                        new String[]{record.getCraftSection(),record.getBomNo(), errorSide,
                                StringUtils.equals(record.getLeadFlag(), Constant.STR_0) ? Constant.IS_LEAD : Constant.LEAD_FREE,
                                record.getFactoryName(), record.getProdplanId()});
            }
            int count = bomCraftAttributeRepository.insertBomCraftAttributeBatch(insertList);
            return count;
        } finally {
            redisLock.unlock();
        }
    }

    @Override
    public int deleteFurnaceTempRel(BomCraftAttributeDTO record) throws Exception {
        if (StringUtils.isEmpty(record.getBomNo())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        String redisKey = Constant.BOM_TEMP_REDIS_KEY + record.getBomNo();
        RedisLock redisLock = new RedisLock(redisKey, NumConstant.NUM_TWO * NumConstant.NUM_60);
        boolean lock = redisLock.lock();
        if (!lock) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.IS_UPDATING_PLEASE_WAIT);
        }
        try {
            int count = bomCraftAttributeRepository.deleteCraftAttribute(record);
            return count;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 从SPM 迁移炉温数据一次性接口
     *
     * @throws Exception 业务异常
     */
    @Override
    @RedisDistributedLockAnnotation(redisKey = "pullFurnaceFromSpm", redisLockTime = 3000, redisLockParam = {})
    public void pullFurnaceFromSpm() throws Exception {
        bomCraftAttributeRepository.deleteCraftAttributeSPM();
        // 获取环保属性数据字典
        List<String> lookList = new LinkedList<>();
        lookList.add(Constant.LOOK_UP_CODE_1036);
        lookList.add(Constant.LOOK_UP_CODE_28650003);
        List<SysLookupValues> list = sysLookupValuesRepository.selectByTypeBatch(lookList);
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        Map<String, String> leadMap = new HashMap<>();
        Map<String, String> coverMap = new HashMap<>();
        this.buildLeadData(list, leadMap, coverMap);
        int current = 1;
        do {
            Page<BoardStoveMaintenanceDTO> pageResult = DatawbRemoteService.postBoardStoveMaintenanceSpm(current);
            if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRows())) {
                break;
            }
            List<BoardStoveMaintenanceDTO> rows = pageResult.getRows();
            List<BomCraftAttributeDTO> insertList = new LinkedList<>();
            // 组装数据
            this.buildInsertData(leadMap, coverMap, rows, insertList);
            // 获取bom 版本
            this.addVerProperties(insertList);
            List<List<BomCraftAttributeDTO>> lists = CommonUtils.splitList(insertList, Constant.INT_50);
            for (List<BomCraftAttributeDTO> tempRelDTOList : lists) {
                bomCraftAttributeRepository.insertBomCraftAttributeBatch(tempRelDTOList);
            }
            current++;
        } while (true);
    }

    @Override
    public List<WorkOrderCraftAttributeDTO> getCraftAttributeForWorkOrder(List<WorkOrderCraftAttributeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<SysLookupValues> leadCgf = sysLookupValuesRepository.selectValuesByType(Integer.parseInt(Constant.LOOK_UP_CODE_28650003))
                .stream().filter(l -> StringUtils.isNotBlank(l.getLookupMeaning())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leadCgf)) {
            return null;
        }
        List<WorkOrderCraftAttributeDTO> retList = new ArrayList();
        List<List<WorkOrderCraftAttributeDTO>> lists = CommonUtils.splitList(list);
        for (List<WorkOrderCraftAttributeDTO> subList: lists) {
            // 转换环保属性和面别
            subList = subList.stream()
                    // SMT,手贴指令需要查询
                    .filter(e -> SMTA.equals(e.getCraftSection()) || SMTB.equals(e.getCraftSection()) || HAND_PASTE.equals(e.getCraftSection()))
                    .filter(e -> StringUtils.isNotBlank(e.getLeadFlagWorkOrder()))
                    // 只有满足配置的环保属性需要查询
                    .filter(e -> leadCgf.stream().anyMatch(l -> l.getLookupMeaning().contains(e.getLeadFlagWorkOrder())))
                    .peek(e -> e.setSurface(SMTA.equals(e.getCraftSection())? "A": "B"))
                    .peek(e -> e.setSurface(HAND_PASTE.equals(e.getCraftSection())? Constant.HAND_PASTE : e.getSurface()))
                    .peek(e -> e.setCraftSectionCondition(Constant.SMT_FLAG))
                    .peek(e -> e.setLeadFlag(leadCgf.stream().filter(l -> l.getLookupMeaning().contains(e.getLeadFlagWorkOrder())).findFirst().orElse(new SysLookupValues()).getAttribute1()))
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subList)) {
                continue;
            }
            List<WorkOrderCraftAttributeDTO> subRe = bomCraftAttributeRepository.getCraftAttributeForWorkOrder(subList);
            if (CollectionUtils.isEmpty(subRe)) {
                continue;
            }
            // 分组并优先取批次相同数据，次取工厂id相同数据,最后取工厂id为空数据(每个分组只保留一条)
            List<WorkOrderCraftAttributeDTO> result = this.filterData(subRe);

            retList.addAll(result);
        }
        return retList;
    }

    private List<WorkOrderCraftAttributeDTO> filterData(List<WorkOrderCraftAttributeDTO> subRe) {
        // 获取头信息工厂id,如果为空默认为-1,避免报错
        Integer factoryId = Integer.parseInt(StringUtils.defaultString(MESHttpHelper.getHttpRequestHeader().get(X_FACTORY_ID), String.valueOf(NumConstant.NUM_MINUS_ONE)));
        // 分组并优先取批次相同数据，次取工厂id相同数据,最后取工厂id为空数据(每个分组只保留一条)
        List<WorkOrderCraftAttributeDTO> result = subRe.stream()
                .collect(Collectors.groupingBy(p -> p.getBomNo() + Constant.UNDER_LINE + p.getSurface() + Constant.UNDER_LINE + p.getLeadFlag()))
                .values().stream()
                .map(group -> {
                    // 先找批次不为空且批次和查询条件相同数据
                    Optional<WorkOrderCraftAttributeDTO> optionalProdplanId = group.stream()
                            .filter(p -> StringUtils.isNotBlank(p.getProdplanId()) && StringUtils.equals(p.getProdplanId(), p.getProdplanIdCondition()))
                            .findFirst();
                    // 如果找到，直接返回
                    if (optionalProdplanId.isPresent()) {
                        return optionalProdplanId.get();
                    }
                    // 再找工厂id相同数据
                    Optional<WorkOrderCraftAttributeDTO> optional = group.stream()
                            .filter(p -> factoryId.equals(p.getFactoryId()) && StringUtils.isBlank(p.getProdplanId()))
                            .findFirst();
                    // 如果找到，直接返回
                    if (optional.isPresent()) {
                        return optional.get();
                    }
                    // 最后找工厂id和批次都为空数据
                    return group.stream()
                            .filter(p -> p.getFactoryId() == null && StringUtils.isBlank(p.getProdplanId()))
                            .findFirst()
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public Integer getExportCount(BomCraftAttributeDTO dto) {
        this.checkQueryParam(dto);
        return bomCraftAttributeRepository.getExportCount(dto);
    }

    @Override
    public void export(HttpServletResponse response, BomCraftAttributeDTO dto) throws Exception {
        Page<BomCraftAttributeDTO> pageInfo = new Page<>();
        pageInfo.setPageSize(NumConstant.NUM_5000);
        pageInfo.setParams(dto);
        List<BomCraftAttribute> bomList = new ArrayList<>();
        int currentPage = INT_0;
        do {
            pageInfo.setCurrent(++currentPage);
            pageInfo.setSearchCount(false);
            List<BomCraftAttribute> tempList = bomCraftAttributeRepository.getBomCraftAttributePage(pageInfo);
            if (CollectionUtils.isEmpty(tempList)) {
                break;
            }
            this.transEmpAndLead(tempList);
            bomList.addAll(tempList);
        } while (currentPage < NumConstant.NUM_TEN);
        // 导出
        String fileName = Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_SHEET_NAME;
        ImesExcelUtil.exportExcelModel(fileName, Constant.BomCraftAttribute.TITLE, bomList, Constant.BomCraftAttribute.PROPS, Constant.BomCraftAttribute.SHEETNAME, response);
    }

    /**
     * 分页写文件
     * @param dto
     * @param excelWriter
     * @param pageSize 每页行数
     * @param maxPage 最大页数
     */
    public void writeExcelData(BomCraftAttributeDTO dto, ExcelWriter excelWriter, int pageSize, int maxPage) throws Exception {
        Page<BomCraftAttributeDTO> pageInfo = new Page();
        pageInfo.setPageSize(pageSize);
        pageInfo.setParams(dto);
        List<BomCraftAttribute> bomList;
        WriteSheet build = EasyExcelFactory.writerSheet(INT_0, Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_SHEET_NAME).build();
        int currentPage = INT_0;
        do {
            pageInfo.setCurrent(++currentPage);
            pageInfo.setSearchCount(false);
            bomList = bomCraftAttributeRepository.getBomCraftAttributePage(pageInfo);
            if (CollectionUtils.isEmpty(bomList)) {
                break;
            }
            this.transEmpAndLead(bomList);
            excelWriter.write(bomList, build);
        } while (currentPage < maxPage);
    }

    @Override
    public void exportByEmail(BomCraftAttributeDTO dto, String empNo) {
        ThreadUtil.EXECUTOR.execute(() -> {
            // 导出加锁，和实时导出公用锁
            RedisLock lock = new RedisLock(LOCK + BOM_CRAFT_ATTRIBUTE_EXPORT + SEMICOLON + empNo, NumConstant.NUM_1000);
            boolean isLock = lock.lock();
            if (!isLock) {
                throw new MesBusinessException(com.zte.springbootframe.common.model.RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
            }
            String tempFilePath = FileUtils.createFilePathAndCheck(Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_FILE_NAME);
            ExcelWriter excelWriter = EasyExcel.write(tempFilePath).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(BomCraftAttributeDTO.class).build();
            try {
                // 写excel 每页5000，最多200页，100万行
                this.writeExcelData(dto, excelWriter, NumConstant.NUM_5000, NumConstant.NUM_200);
                excelWriter.finish();
                // 上传文档云，并获取下载链接
                String fileKey = cloudDiskHelper.fileUpload(tempFilePath, empNo, CloudDiskHelper.MAX_RETRY_TIMES);
                String downloadURL = cloudDiskHelper.getFileDownloadUrl(fileKey, Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_FILE_NAME, empNo);
                // 邮件发送下载链接
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(Constant.EMAIL_PREFIX)
                        .append(Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_FILE_NAME)
                        .append(Constant.EMAIL_PREFIX_A)
                        .append(downloadURL)
                        .append(Constant.EMAIL_COLO)
                        .append(Constant.CLICK_DOWN)
                        .append(Constant.EMAIL_SUFFIX);
                emailUtils.sendMail(dto.getExportEmail(), Constant.BOM_CRAFT_ATTRIBUTE_EXPORT_FILE_NAME,
                        StringUtils.EMPTY, stringBuffer.toString(), StringUtils.EMPTY);
            } catch (Exception e) {
                logger.error(BOM_CRAFT_ATTRIBUTE_EXPORT, e);
            } finally {
                CommonUtils.closeExcelWriter(excelWriter);
                try {
                    FileUtils.deleteFile(tempFilePath);
                } catch (Exception e) { }
                lock.unlock();
            }
        });
    }

    /**
     * 获取PCB 版本数据
     *
     * @param insertList 新增数据
     */
    private void addVerProperties(List<BomCraftAttributeDTO> insertList) {
        List<String> bomList = insertList.stream().map(BomCraftAttributeDTO::getBomNo)
                .distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(bomList, Constant.IN_MAX_SIZE);
        List<SpecifiedPsTaskVO> taskResList = new LinkedList<>();
        for (List<String> itemList : splitList) {
            List<SpecifiedPsTaskVO> taskVOS = bBomHeaderRepository.selectBBomHeaderByItemNoList(itemList);
            if (CollectionUtils.isNotEmpty(taskVOS)) {
                taskResList.addAll(taskVOS);
            }
        }
        Map<String, String> verMap = taskResList.stream()
                .filter(item->StringUtils.isNotBlank(item.getPcbVersion()))
                .collect(Collectors.toMap(SpecifiedPsTaskVO::getProductCode,
                        SpecifiedPsTaskVO::getPcbVersion));
        insertList.forEach(item -> item.setVerNo(verMap.get(item.getBomNo())));
    }

    /**
     * 组装新增数据
     *
     * @param leadMap    1036 环保属性数据字典
     * @param coverMap   装换关系
     * @param rows       查询行
     * @param insertList 新增数据
     */
    private void buildInsertData(Map<String, String> leadMap, Map<String, String> coverMap, List<BoardStoveMaintenanceDTO> rows, List<BomCraftAttributeDTO> insertList) {
        BomCraftAttributeDTO temp;
        for (BoardStoveMaintenanceDTO item : rows) {
            if (StringUtils.isBlank(item.getBomNo())) {
                continue;
            }
            String leadValue = leadMap.get(item.getSmtStoveType());
            if (StringUtils.isNotBlank(item.getSmtAStove())) {
                temp = new BomCraftAttributeDTO();
                temp.setLeadFlag(coverMap.get(leadValue));
                temp.setBomNo(item.getBomNo());
                temp.setBomName(item.getBomName());
                temp.setCreateBy(Constant.SPM);
                temp.setLastUpdatedBy(Constant.SPM);
                temp.setId(UUID.randomUUID().toString().replaceAll(Constant.HORIZON, StringUtils.EMPTY));
                temp.setFurnaceTempName(item.getSmtAStove());
                temp.setSurface(Constant.A_TYPE);
                insertList.add(temp);
            }
            if (StringUtils.isNotBlank(item.getSmtBStove())) {
                temp = new BomCraftAttributeDTO();
                temp.setBomNo(item.getBomNo());
                temp.setBomName(item.getBomName());
                temp.setLeadFlag(coverMap.get(leadValue));
                temp.setCreateBy(Constant.SPM);
                temp.setId(UUID.randomUUID().toString().replaceAll(Constant.HORIZON, StringUtils.EMPTY));
                temp.setLastUpdatedBy(Constant.SPM);
                temp.setSurface(Constant.B_TYPE);
                temp.setFurnaceTempName(item.getSmtBStove());
                insertList.add(temp);
            }
        }
    }

    /**
     * 组装环保属性数据
     *
     * @param lookUpValuesBatch 环保属性配置项
     * @param leadMap           环保map
     * @param coverMap          真实reelId
     */
    private void buildLeadData(List<SysLookupValues> lookUpValuesBatch, Map<String, String> leadMap, Map<String,
            String> coverMap) {
        for (SysLookupValues upValuesBatch : lookUpValuesBatch) {
            // 环保属性
            if (Constant.LOOK_UP_CODE_1036.equals(upValuesBatch.getLookupType().toString())) {
                leadMap.put(upValuesBatch.getDescriptionChin(), upValuesBatch.getLookupMeaning());
            }
            // 对应关系有铅无铅
            if (Constant.LOOK_UP_CODE_28650003.equals(upValuesBatch.getLookupType().toString())) {
                String lookupMeaning = upValuesBatch.getLookupMeaning();
                String[] split = lookupMeaning.split(COMMA);
                for (int i = 0; i < split.length; i++) {
                    coverMap.put(split[i], upValuesBatch.getAttribute1());
                }
            }
        }
    }

    /**
     * 模糊查询料单代码
     */
    @Override
    public List<String> getItemNoList(String likeItemNo){
            List<String> itemNoList = bomCraftAttributeRepository.getItemNoList(likeItemNo);
            return itemNoList;
    }

    /**
     * 根据料单代码查询料单与炉温关系信息
     */
    @Override
    public Page<BomCraftAttributeDTO> bomInfoSearchByItemNo (BomCraftAttributeDTO dto) throws Exception {
        Page<BomCraftAttributeDTO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        pageInfo.setParams(dto);
        List<BomCraftAttributeDTO> bomCraftAttributeDTOList = bomCraftAttributeRepository.getBomInfoList(pageInfo);
        pageInfo.setRows(bomCraftAttributeDTOList);
        return pageInfo;
    }
}
