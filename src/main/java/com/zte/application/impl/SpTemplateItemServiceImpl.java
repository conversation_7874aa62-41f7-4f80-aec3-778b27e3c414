package com.zte.application.impl;

import com.zte.application.SpTemplateItemService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SpecialityParamConstant;
import com.zte.common.utils.TemplateFunctionExpress;
import com.zte.domain.model.SpTemplateItem;
import com.zte.domain.model.SpTemplateItemRepository;
import com.zte.interfaces.dto.PreviewParamsInfoDTO;
import com.zte.interfaces.dto.SpTemplateDTO;
import com.zte.interfaces.dto.SpTemplateItemDTO;
import com.zte.interfaces.dto.SpTemplateItemQueryDTO;
import com.zte.interfaces.dto.SpTemplatePreviewDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.DEFAULT_AA;
import static com.zte.common.utils.Constant.DELIMITER_SHORTBAR;
import static com.zte.common.utils.Constant.DELIMITER_STAR;
import static com.zte.common.utils.Constant.SPECIAL_FFFF;
import static com.zte.common.utils.Constant.STBIDPREFIX_C3END_INDEX;
import static com.zte.common.utils.Constant.STBIDPREFIX_C3START_INDEX;
import static com.zte.common.utils.Constant.STBIDPREFIX_CPART_LENGTH;
import static com.zte.common.utils.Constant.STBIDPREFIX_LENGTH;
import static com.zte.common.utils.Constant.STBID_CFG_LENGTH;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/6 17:20
 * @Description
 */
@Service
public class SpTemplateItemServiceImpl implements SpTemplateItemService {

    @Autowired
    private SpTemplateItemRepository spTemplateItemRepository;
    @Autowired
    private SpSpecialityParamServiceImpl spSpecialityParamService;

    private static final Map<String, String> TEMPLATE_PREVIEW_DEFAULT;
    private static final String TEMPLATE_PREVIEW_GETSTARTMAC = "00-19-C6-51-00-0F";
    private static final String TEMPLATE_PREVIEW_GETENDMAC = "00-19-C6-51-00-13";

    static {
        Map<String, String> tempFunction = new HashMap<>();
        tempFunction.put(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE + SpecialityParamConstant.PARENTHESES, "210032822429");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETSTARTGPONSN, "ZTEG09000001");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETENDGPONSN, "ZTEG09000020");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETIMEI, "111065572340412");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETCTEI, "111002107096707");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETCMEI, "CMEI01010101000");
        tempFunction.put(SpecialityParamConstant.RESOURCE_POOL_GETDSN, "ZT2201EG9A000001");
        TEMPLATE_PREVIEW_DEFAULT = Collections.unmodifiableMap(tempFunction);
    }

    @Override
    public PageRows<SpTemplateItem> queryPage(SpTemplateItemQueryDTO query) {
        PageRows<SpTemplateItem> pageRows = new PageRows<>();
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(spTemplateItemRepository.countPage(query));
        if (pageRows.getTotal() > Constant.LONG_ZERO) {
            pageRows.setRows(spTemplateItemRepository.queryPage(query));
        }
        return pageRows;
    }

    @Override
    public void addBatch(List<SpTemplateItem> spTemplateItemList) {
        if (spTemplateItemList.isEmpty()) {
            return;
        }
        spTemplateItemRepository.insertBatch(spTemplateItemList);
    }

    @Override
    public void updateById(SpTemplateItem item) {
        item.setUpdateTime(new Date());
        spTemplateItemRepository.updateById(item);
    }

    @Override
    public void deleteNotItemIdByTemplateIdAnd(String templateId, List<String> itemIds, String updateBy) {
        spTemplateItemRepository.deleteNotItemIdByTemplateId(templateId, itemIds, updateBy);
    }

    @Override
    public void deleteByTemplateId(String templateId, String updateBy) {
        spTemplateItemRepository.deleteByTemplateId(templateId, updateBy);
    }

    @Override
    public void checkTemplateItem(List<SpTemplateItemDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_NULL);
        }
        long nameDistinctTotal = itemList.stream().map(SpTemplateItemDTO::getParamName).distinct().count();
        if (itemList.size() != nameDistinctTotal) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_NAME_REPEAT);
        }

        itemList.forEach(item -> {
            if (!StringUtils.equals(item.getGenerationMethod(), Constant.GenerationMethod.ASSIGNMENT_TEXT)) {
                TemplateFunctionExpress.checkExpression(item.getParamRule());
            }
        });
        // 入网参数校验
        List<String> netWorkList = new ArrayList<>();
        for (SpTemplateItemDTO templateItemDTO : itemList) {
            if (StringUtils.equals(templateItemDTO.getParamType(), Constant.ParamType.NASN)) {
                netWorkList.add(Constant.ParamType.NASN);
            } else if (StringUtils.equals(templateItemDTO.getParamType(), Constant.ParamType.SCRAMBLING_CODE)) {
                netWorkList.add(Constant.ParamType.SCRAMBLING_CODE);
            } else if (StringUtils.equals(templateItemDTO.getParamType(), Constant.ParamType.NACC)) {
                netWorkList.add(Constant.ParamType.NACC);
            }
        }
        if (CollectionUtils.isNotEmpty(netWorkList) && !netWorkList.containsAll(Constant.NET_WORK_PARAMS_LIST)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NET_WORK_PARAMS_MUST_EXISTING_SIMULTANEOUSLY);
        }
    }

    @Override
    public List<SpTemplatePreviewDTO> previewOrAdd(SpTemplateDTO spTemplateDTO) {
        List<SpTemplateItemDTO> itemList = spTemplateDTO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_NULL);
        }
        List<SpTemplatePreviewDTO> list = new ArrayList<>();
        // 新增
        if (spTemplateDTO.isWhetherAdd()) {
            for (SpTemplateItemDTO itemDTO : itemList) {
                if (!itemDTO.isWhetherAdd()) {
                    continue;
                }
                TemplateFunctionExpress.checkTemplateItem(itemDTO);
                SpTemplatePreviewDTO previewDTO = new SpTemplatePreviewDTO();
                previewDTO.setParamName(itemDTO.getParamName());
                this.addTemplateItem(itemDTO, previewDTO);
                list.add(previewDTO);
            }
        } else {
            // 预览
            list = this.preview(itemList);
        }
        return list;
    }

    private List<SpTemplatePreviewDTO> preview(List<SpTemplateItemDTO> itemList) {
        List<SpTemplatePreviewDTO> list = new ArrayList<>();
        PreviewParamsInfoDTO previewParamsInfoDTO = new PreviewParamsInfoDTO();
        // 设置预览参数信息
        this.getPreviewParamsInfo(itemList, previewParamsInfoDTO);
        List<SpTemplateItemDTO> prePreviewList = itemList.stream()
                .filter(item -> !Constant.POST_PREVIEW_LIST.contains(item.getGenerationMethod()))
                .collect(Collectors.toList());
        // 最后生成的预览List
        List<SpTemplateItemDTO> postPreviewList = itemList.stream()
                .filter(item -> Constant.POST_PREVIEW_LIST.contains(item.getGenerationMethod()))
                .collect(Collectors.toList());
        // 循环生成预览数据
        for (SpTemplateItemDTO itemDTO : prePreviewList) {
            SpTemplatePreviewDTO previewDTO = new SpTemplatePreviewDTO();
            previewDTO.setParamName(itemDTO.getParamName());
            previewDTO.setParamRule(itemDTO.getParamRule());
            if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.NACC)) {
                previewDTO.setSpecificValue(previewParamsInfoDTO.getSubConsent());
            }
            this.previewTemplateItem(itemDTO, previewDTO, previewParamsInfoDTO);
            list.add(previewDTO);
        }
        for (SpTemplateItemDTO spTemplateItemDTO : postPreviewList) {
            SpTemplatePreviewDTO previewDTO = new SpTemplatePreviewDTO();
            previewDTO.setParamName(spTemplateItemDTO.getParamName());
            previewDTO.setParamRule(spTemplateItemDTO.getParamRule());
            this.previewTemplateItemFinally(list, spTemplateItemDTO, previewDTO, previewParamsInfoDTO);
            list.add(previewDTO);
        }
        // 保持原有参数顺序返回
        Map<String, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < itemList.size(); i++) {
            orderMap.put(itemList.get(i).getParamName(), i);
        }
        return list.stream()
                .sorted(Comparator.comparingInt(
                        o -> orderMap.getOrDefault(o.getParamName(), Integer.MAX_VALUE)
                ))
                .collect(Collectors.toList());
    }

    // 获取预览参数信息
    private void getPreviewParamsInfo(List<SpTemplateItemDTO> itemList, PreviewParamsInfoDTO previewParamsInfoDTO) {
        for (SpTemplateItemDTO itemDTO : itemList) {
            if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.MAC)) {
                if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_END)) {
                    String step = itemDTO.getParamRule().replace(Constant.GenerationMethod.MAC_END, Constant.STRING_EMPTY)
                            .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                            .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                    previewParamsInfoDTO.setStep(step);
                    continue;
                }
                this.previewMacStartParams(previewParamsInfoDTO, itemDTO);
            }
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.NASN)) {
                String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                int lastIndex = tempStr.lastIndexOf(Constant.HORIZON);
                String subConsent = tempStr.substring(lastIndex + 1);
                previewParamsInfoDTO.setSubConsent(subConsent);
            }
            if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.GPON_SN)) {
                if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.TELMEX_GPON_SN)) {
                    continue;
                }
                String gponSnFixedValue = itemDTO.getParamRule().replace(SpecialityParamConstant.GPON_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                previewParamsInfoDTO.setGponSnFixedValue(gponSnFixedValue);
            }
        }
    }

    private void previewMacStartParams(PreviewParamsInfoDTO previewParamsInfoDTO, SpTemplateItemDTO item) {
        String tempStr = item.getParamRule().replace(Constant.GenerationMethod.MAC_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String separator = "";
        String specialEnding = "";
        if (StringUtils.equals(item.getGenerationMethod(), Constant.GenerationMethod.MAC_START)) {
            separator = tempStr;
        } else if (StringUtils.equals(item.getGenerationMethod(), Constant.GenerationMethod.CUSTOMIZED_MAC_START)) {
            separator = tempStr.substring(0, tempStr.indexOf(','));
            specialEnding = tempStr.substring(tempStr.indexOf(',') + 1);
        }
        previewParamsInfoDTO.setSeparator(separator);
        previewParamsInfoDTO.setSpecialEnding(specialEnding);
    }

    private void previewTemplateItemFinally(List<SpTemplatePreviewDTO> list, SpTemplateItemDTO spTemplateItemDTO, SpTemplatePreviewDTO previewDTO, PreviewParamsInfoDTO previewParamsInfoDTO) {
        switch (spTemplateItemDTO.getParamType()) {
            case Constant.ParamType.MAC:
                this.previewMacAdd(spTemplateItemDTO, previewDTO, list, previewParamsInfoDTO);
                break;
            case Constant.ParamType.CUSTOMIZE:
                this.previewCustomize(spTemplateItemDTO, previewDTO, list);
                break;
            case Constant.ParamType.ASSIGNMENT:
                this.previewAssignmentParam(list, spTemplateItemDTO, previewDTO);
                break;
            case Constant.ParamType.INTERVAL_VALUE:
                this.previewIntervalValueParam(list, spTemplateItemDTO, previewDTO);
                break;
            case Constant.ParamType.DEVICE_SERIAL_NUMBER:
                this.previewDeviceSerialNumberParam(list, spTemplateItemDTO, previewDTO);
                break;
            default:
                break;
        }
    }

    private void previewAssignmentParam(List<SpTemplatePreviewDTO> list, SpTemplateItemDTO spTemplateItemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(spTemplateItemDTO.getGenerationMethod(), Constant.GenerationMethod.ASSIGNMENT_VARIABLE)) {
            String variableName = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_VARIABLE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            for (SpTemplatePreviewDTO templatePreviewDTO : list) {
                if (StringUtils.equals(templatePreviewDTO.getParamName(), variableName)) {
                    previewDTO.setParamValue(templatePreviewDTO.getParamValue());
                    break;
                }
            }
        } else if (StringUtils.equals(spTemplateItemDTO.getGenerationMethod(), Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE)) {
            String tempStr = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_TEXT_AND_RANGE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            String[] strings = tempStr.split(Constant.STR_SPLIT, Constant.INT_B1);
            String variable1 = "";
            String variable2 = "";
            if (StringUtils.isNotBlank(strings[1])) {
                String[] splits = strings[1].split(Constant.VIRGULE);
                variable1 = this.getVariableValue(list, splits);
            }
            if (StringUtils.isNotBlank(strings[Constant.INT_2])) {
                String[] splits = strings[Constant.INT_2].split(Constant.VIRGULE);
                variable2 = this.getVariableValue(list, splits);
            }
            String value = strings[0] + variable1 + variable2 + strings[3];
            previewDTO.setParamValue(value);
        }
    }

    private String getVariableValue(List<SpTemplatePreviewDTO> list, String[] splits) {
        String targetVaule = "";
        for (SpTemplatePreviewDTO previewDTO : list) {
            if (StringUtils.equals(previewDTO.getParamName(), splits[0])) {
                targetVaule = previewDTO.getParamValue();
                break;
            }
        }
        if (StringUtils.isBlank(targetVaule)) {
            return targetVaule;
        }
        // Mac参数下，去掉分隔符
        String value = this.handleByMac(targetVaule);
        int startIndex = Integer.parseInt(splits[Constant.INT_1]);
        int endIndex = Integer.parseInt(splits[Constant.INT_2]);
        return value.substring(startIndex - 1, startIndex + endIndex - 1);
    }


    // Mac参数下，去掉分隔符
    private String handleByMac(String targetVariableVaule) {
        long count = targetVariableVaule.chars().filter(c -> (c == '-' || c == ':')).count();
        if (count == Constant.INT_5) {
            targetVariableVaule = targetVariableVaule.replace(Constant.HORIZON, Constant.STRING_EMPTY)
                    .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        }
        return targetVariableVaule;
    }

    private void previewTemplateItem(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, PreviewParamsInfoDTO previewParamsInfoDTO) {
        switch (itemDTO.getParamType()) {
            case Constant.ParamType.MAC:
                this.previewMACParam(itemDTO, previewDTO, previewParamsInfoDTO);
                break;
            case Constant.ParamType.GPON_SN:
                this.previewGponSn(itemDTO, previewDTO);
                break;
            case Constant.ParamType.ASSIGNMENT:
                this.previewAssignmentParam(itemDTO, previewDTO);
                break;
            case Constant.ParamType.RANDOM_TYPE:
                this.previewRandomParam(itemDTO, previewDTO);
                break;
            case Constant.ParamType.DEVICE_SERIAL_NUMBER:
                this.previewDeviceSerialNumber(itemDTO, previewDTO, previewParamsInfoDTO);
                break;
            case Constant.ParamType.D_SN:
                this.previewDSN(itemDTO, previewDTO);
                break;
            case Constant.ParamType.NASN:
                this.previewNASN(previewDTO);
                break;
            case Constant.ParamType.SCRAMBLING_CODE:
                this.previewScramblingCode(previewDTO);
                break;
            case Constant.ParamType.NACC:
                this.previewNACC(previewDTO);
                break;
            case Constant.ParamType.WHOLE_DEVICE_CODE:
                this.previewWholeDeviceCode(itemDTO, previewDTO);
                break;
            case Constant.ParamType.STBID:
                this.previewStbid(itemDTO, previewDTO, previewParamsInfoDTO);
                break;
            default:
                break;
        }
    }

    private void addTemplateItem(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        switch (itemDTO.getParamType()) {
            case Constant.ParamType.MAC:
                this.addMACParams(itemDTO, previewDTO);
                break;
            case Constant.ParamType.GPON_SN:
                this.addGponSnParams(itemDTO, previewDTO);
                break;
            case Constant.ParamType.ASSIGNMENT:
                this.addAssignmentParams(itemDTO, previewDTO);
                break;
            case Constant.ParamType.INTERVAL_VALUE:
                this.addIntervalValue(itemDTO, previewDTO);
                break;
            case Constant.ParamType.RANDOM_TYPE:
                this.addRandom(itemDTO, previewDTO);
                break;
            case Constant.ParamType.WHOLE_DEVICE_CODE:
                this.addWholeDeviceCode(itemDTO, previewDTO);
                break;
            case Constant.ParamType.DEVICE_SERIAL_NUMBER:
                this.addDeviceSerialNumber(itemDTO, previewDTO);
                break;
            case Constant.ParamType.D_SN:
                this.addDSN(itemDTO, previewDTO);
                break;
            case Constant.ParamType.NASN:
                this.addNASN(itemDTO, previewDTO);
                break;
            case Constant.ParamType.SCRAMBLING_CODE:
                this.addScramblingCode(itemDTO, previewDTO);
                break;
            case Constant.ParamType.NACC:
                this.addNACC(itemDTO, previewDTO);
                break;
            case Constant.ParamType.CUSTOMIZE:
                this.addCustomize(itemDTO, previewDTO);
                break;
            case Constant.ParamType.STBID:
                this.addStbid(itemDTO, previewDTO);
                break;
            default:
                break;
        }
    }


    private void previewAssignmentParam(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(Constant.GenerationMethod.ASSIGNMENT_DB, itemDTO.getGenerationMethod())) {
            this.previewAssignmentDBParam(itemDTO, previewDTO);
        } else if (StringUtils.equals(Constant.GenerationMethod.ASSIGNMENT_TEXT, itemDTO.getGenerationMethod())) {
            String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_TEXT_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            previewDTO.setParamValue(tempStr);
        }
    }

    private void previewDeviceSerialNumberParam(List<SpTemplatePreviewDTO> list, SpTemplateItemDTO spTemplateItemDTO, SpTemplatePreviewDTO previewDTO) {
        String substring = spTemplateItemDTO.getParamRule().substring(spTemplateItemDTO.getParamRule().indexOf('(') + 1);
        String tempStr = substring.replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] strings = tempStr.split(Constant.STR_SPLIT, Constant.INT_B1);
        if (StringUtils.equals(Constant.GenerationMethod.DSN_CUCC, spTemplateItemDTO.getGenerationMethod())) {
            this.previewCUCCParam(list, previewDTO, strings);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC_SN_EMPTY, spTemplateItemDTO.getGenerationMethod())) {
            this.previewCTCCSNParam(list, previewDTO, tempStr);
        }
    }

    private void previewCTCCSNParam(List<SpTemplatePreviewDTO> list, SpTemplatePreviewDTO previewDTO, String tempStr) {
        String tempValue = "";
        for (SpTemplatePreviewDTO templatePreviewDTO : list) {
            if (StringUtils.equals(tempStr, templatePreviewDTO.getParamName())) {
                tempValue = templatePreviewDTO.getParamValue();
            }
        }
        if (StringUtils.isBlank(tempValue)) {
            return;
        }
        String prefix = tempValue.substring(0, tempValue.indexOf(Constant.HORIZON) + Constant.INT_1);
        String suffix = tempValue.substring(tempValue.indexOf(Constant.HORIZON) + Constant.INT_1);
        StringBuilder formattedSuffix = new StringBuilder();
        for (int i = 0; i < suffix.length(); i += Constant.INT_4) {
            if (i > 0) {
                formattedSuffix.append(" ");
            }
            formattedSuffix.append(suffix, i, Math.min(i + Constant.INT_4, suffix.length()));
        }
        String value = prefix + formattedSuffix;
        previewDTO.setParamValue(value);
    }

    private void previewCUCCParam(List<SpTemplatePreviewDTO> list, SpTemplatePreviewDTO previewDTO, String[] strings) {
        String fixedValue = strings[0];
        if (StringUtils.isBlank(fixedValue)) {
            fixedValue = SpecialityParamConstant.MAC_PREVIEW_VALUE.substring(0,Constant.INT_6);
        }
        String cuccSn = strings[1].chars()
                .filter(c -> !Character.isWhitespace(c))
                .collect(StringBuilder::new,
                        StringBuilder::appendCodePoint,
                        StringBuilder::append)
                .toString();
        String variableValue = "";
        for (SpTemplatePreviewDTO templatePreviewDTO : list) {
            if (StringUtils.equals(strings[Constant.INT_2], templatePreviewDTO.getParamName())) {
                variableValue = templatePreviewDTO.getParamValue();
                break;
            }
        }
        if (StringUtils.isBlank(variableValue)) {
            return;
        }
        variableValue = variableValue.replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        String tempValue = fixedValue + Constant.HORIZON + cuccSn + variableValue;
        char[] charArray = tempValue.toCharArray();
        int xorResult = 0;
        // 前30位计算异或校验值
        for (char c : charArray) {
            xorResult ^= c;
        }
        String xorResultStr = String.format("%02X", xorResult);
        String value = tempValue + xorResultStr;
        previewDTO.setParamValue(value);
    }

    private void previewIntervalValueParam(List<SpTemplatePreviewDTO> list, SpTemplateItemDTO spTemplateItemDTO, SpTemplatePreviewDTO previewDTO) {
        String tempStr = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.INTERVAL_VALUE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] strings = tempStr.split(Constant.STR_SPLIT, Constant.INT_B1);
        String[] splits = strings[1].split(Constant.VIRGULE);
        String variable = this.getVariableValue(list, splits);
        // 大小写转换
        variable = this.toggleCase(strings[Constant.INT_2], variable);
        String value = strings[0] + variable + strings[Constant.INT_3];
        previewDTO.setParamValue(value);
    }

    private String toggleCase(String type, String variable) {
        if (StringUtils.isBlank(type)) {
            return variable;
        }
        if (StringUtils.equals(Constant.STR_0, type)) {
            variable = variable.toUpperCase();
        } else {
            variable = variable.toLowerCase();
        }
        return variable;
    }

    private void previewRandomParam(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        String randomValue = this.getRandomType(itemDTO);
        previewDTO.setParamValue(randomValue);
    }

    private void previewDSN(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.D_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        previewDTO.setParamValue(tempStr);
    }

    private String getRandomType(SpTemplateItemDTO itemDTO) {
        String result = "";
        if (!StringUtils.equals(Constant.ParamType.RANDOM_TYPE, itemDTO.getParamType())) {
            return result;
        }
        if (StringUtils.isBlank(itemDTO.getParamRule())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        return spSpecialityParamService.getRandomData(itemDTO);
    }

    private void previewAssignmentDBParam(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_BD_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String type = tempStr.substring(0, tempStr.indexOf(','));
        String fixedValue =  tempStr.substring(tempStr.indexOf(',') + 1);
        switch(type) {
            case Constant.ResourceType.CTEI:
            case Constant.ResourceType.CUEI:
            case Constant.ResourceType.CMEI:
            case Constant.ResourceType.IMEI:
            case Constant.ResourceType.STB_SN:
                previewDTO.setParamValue(String.format(SpecialityParamConstant.DB_RESOURCE_PREVIEW, fixedValue));
                break;
            case Constant.ResourceType.SN:
                if (fixedValue.length() == Constant.INT_6) {
                    previewDTO.setParamValue(String.format(SpecialityParamConstant.SN_NINE_DIGIT_PREVIEW, fixedValue));
                } else if (fixedValue.length() == Constant.INT_18) {
                    previewDTO.setParamValue(String.format(SpecialityParamConstant.SN_SEVEN_DIGIT_PREVIEW, fixedValue));
                }
                break;
            case Constant.ResourceType.SO_NET_SN:
                previewDTO.setParamValue(String.format(SpecialityParamConstant.SO_NET_SN_PREVIEW, fixedValue));
                break;
            default:
                break;
        }
    }

    private void previewDeviceSerialNumber(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, PreviewParamsInfoDTO previewParamsInfoDTO) {
        String substring = itemDTO.getParamRule().substring(itemDTO.getParamRule().indexOf('(') + 1);
        String tempStr = substring.replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String tempMac = SpecialityParamConstant.MAC_PREVIEW_VALUE;
        if (StringUtils.isNotBlank(previewParamsInfoDTO.getSpecialEnding())) {
            tempMac = tempMac.replace(Constant.F, previewParamsInfoDTO.getSpecialEnding());
        }
        if (StringUtils.equals(Constant.GenerationMethod.DSN_CMCC, itemDTO.getGenerationMethod())) { // 移动
            // MAC前六位
            String prefix = "";
            if (StringUtils.isBlank(tempStr)) {
                prefix = tempMac.substring(0,Constant.INT_6);
            } else {
                prefix = tempStr;
            }
            previewDTO.setParamValue(prefix + Constant.HORIZON + SpecialityParamConstant.GPON_SN_PREVIEW_VALUE);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC, itemDTO.getGenerationMethod())) { // 电信智能
            String macFirstSix = tempMac.substring(0,Constant.INT_6);
            String randomNum = spSpecialityParamService.getRandom3or4();
            String randomStr = spSpecialityParamService.getRandomHexChar();
            String dsnCTCCProductType = tempStr.substring(0, tempStr.indexOf(','));
            String dsnCTCCProvinceCode = tempStr.substring(tempStr.indexOf(',') + 1);
            String dsnCTCCValue = macFirstSix + Constant.HORIZON + randomNum + randomStr + dsnCTCCProductType + dsnCTCCProvinceCode + tempMac;
            previewDTO.setParamValue(dsnCTCCValue);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC_FTTO, itemDTO.getGenerationMethod())) { // 电信FTTO
            String[] strings = tempStr.split(Constant.STR_SPLIT);
            String equipmentCode = strings[0];
            String provinceCode = strings[1];
            String macFirstSix = tempMac.substring(0,Constant.INT_6);
            String value = macFirstSix + Constant.HORIZON + Constant.STRING_ZERO_ONE + equipmentCode + provinceCode + tempMac;
            previewDTO.setParamValue(value);
        }
    }
    private void previewCustomize(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, List<SpTemplatePreviewDTO> list) {
        previewDTO.setParamValue(this.ruleHander(itemDTO.getParamName(), itemDTO.getParamRule(), list));
    }

    private void previewMacAdd(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, List<SpTemplatePreviewDTO> list, PreviewParamsInfoDTO previewParamsInfoDTO) {
        if (StringUtils.equals(Constant.GenerationMethod.MAC_ADD, itemDTO.getGenerationMethod())) {
            String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.MAC_ADD_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            String[] strings = tempStr.split(Constant.STR_SPLIT);
            String variableValue = "";
            for (SpTemplatePreviewDTO templatePreviewDTO : list) {
                if (StringUtils.equals(strings[0], templatePreviewDTO.getParamName())) {
                    variableValue = templatePreviewDTO.getParamValue();
                }
            }
            if (StringUtils.isBlank(variableValue)) {
                return ;
            }
            String macAddress = this.incrementMacAddress(variableValue, previewParamsInfoDTO.getSeparator(), Long.parseLong(previewParamsInfoDTO.getStep()));
            previewDTO.setParamValue(macAddress);
        }
    }

    private void previewWholeDeviceCode(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        String fixedValue = itemDTO.getParamRule().replace(SpecialityParamConstant.WHOLE_DEVICE_CODE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        previewDTO.setParamValue(String.format(SpecialityParamConstant.WHOLE_DEVICE_CODE_PREVIEW, fixedValue));
    }

    private void previewNACC(SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamValue(previewDTO.getSpecificValue() + SpecialityParamConstant.NACC_PREVIEW_VALUE);
    }

    private void previewScramblingCode(SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamValue(SpecialityParamConstant.SCRAMBLING_CODE_PREVIEW_VALUE);
    }

    private void previewNASN(SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamValue(SpecialityParamConstant.SCRAMBLING_CODE_PREVIEW_VALUE + Constant.MODEL_NUMBER);
    }

    private void previewGponSn(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.TELMEX_GPON_SN)) {
            LocalDate currentDate = LocalDate.now();
            LocalDate localDate = currentDate.plusWeeks(0);
            int currentYear = localDate.getYear();
            String yearPart = String.format("%02d", currentYear % 100);
            WeekFields weekFields = WeekFields.of(Locale.getDefault());
            int weekOfYear = localDate.get(weekFields.weekOfYear());
            String weekPart = String.format("%02d", weekOfYear);
            String telmexEigenValue = Constant.GPON_SN_PREFIX + yearPart + weekPart + Constant.STR_0001;
            previewDTO.setParamValue(telmexEigenValue);
            return;
        }
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.GPON_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        previewDTO.setParamValue(SpecialityParamConstant.GPON_SN_PREVIEW_VALUE.replace(SpecialityParamConstant.GPON_SN_FIXED_VALUE, tempStr));
    }

    private void previewMACParam(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, PreviewParamsInfoDTO previewParamsInfoDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.MAC_START)) {
            previewDTO.setParamValue(TEMPLATE_PREVIEW_GETSTARTMAC.replace(Constant.HORIZON, previewParamsInfoDTO.getSeparator()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.CUSTOMIZED_MAC_START)) {
            previewDTO.setParamValue(TEMPLATE_PREVIEW_GETSTARTMAC.replace(Constant.HORIZON, previewParamsInfoDTO.getSeparator())
                    .replace(Constant.F, previewParamsInfoDTO.getSpecialEnding()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.MAC_END)) {
            String tempMac = TEMPLATE_PREVIEW_GETSTARTMAC;
            if (StringUtils.isNotBlank(previewParamsInfoDTO.getSpecialEnding())) {
                tempMac = tempMac.replace(Constant.F, previewParamsInfoDTO.getSpecialEnding());
            }
            String macAddress = this.incrementMacAddress(tempMac, previewParamsInfoDTO.getSeparator(), Long.parseLong(previewParamsInfoDTO.getStep()));
            previewDTO.setParamValue(macAddress);
        }
    }

    private void previewStbid(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO, PreviewParamsInfoDTO previewParamsInfoDTO) {
        String fixedValue = itemDTO.getParamRule().replace(SpecialityParamConstant.STBID_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] variables = fixedValue.split(Constant.COMMA);
        if (variables.length != STBID_CFG_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDTEMPLATE_ERROR);
        }
        String templeteStbidPrefix = processString(variables[1]);
        String templeteMacStart = TEMPLATE_PREVIEW_GETSTARTMAC.replace(Constant.HORIZON, previewParamsInfoDTO.getSeparator());
        previewDTO.setParamValue(DEFAULT_AA + templeteStbidPrefix + templeteMacStart);
    }

    public static String processString(String input) {
        String[] parts = input.split(DELIMITER_SHORTBAR, -1);
        if (parts.length != STBIDPREFIX_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDCFG_ERROR);
        }
        String cPart = parts[1];
        if (StringUtils.length(cPart) != STBIDPREFIX_CPART_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDCFG_ERROR);
        }
        if (StringUtils.equals(StringUtils.substring(cPart, STBIDPREFIX_C3START_INDEX, STBIDPREFIX_C3END_INDEX), DELIMITER_STAR)) {
            cPart = cPart.replace(DELIMITER_STAR, "1");
        }
        parts[1] = cPart;
        String fPart = parts[4];
        if (StringUtils.equals(fPart, SPECIAL_FFFF)) {
            fPart = DateTimeFormatter.ofPattern("yyMM").format(LocalDate.now());
        }
        parts[4] = fPart;
        return String.join(StringUtils.EMPTY, parts);
    }

    private void addIntervalValue(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.INTERVAL_VALUE_FUNCTION, itemDTO.getParamValue()));
    }

    private void addRandom(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.isNotEmpty(itemDTO.getParamValue())) {
            itemDTO.setParamValue(itemDTO.getParamValue().replace("\u200B", "").replace(" ", ""));
        }
        previewDTO.setParamRule(String.format(Constant.RANDOM_FUNCTION, itemDTO.getParamValue()));
    }

    private void addAssignmentParams(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.ASSIGNMENT_DB)) {
            previewDTO.setParamRule(String.format(Constant.ASSIGNMENT_DB_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.ASSIGNMENT_TEXT)) {
            previewDTO.setParamRule(String.format(Constant.ASSIGNMENT_TEXT_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.ASSIGNMENT_VARIABLE)) {
            previewDTO.setParamRule(String.format(Constant.ASSIGNMENT_VARIABLE_FUNCTION, itemDTO.getParamValue()));
        } else {
            previewDTO.setParamRule(String.format(Constant.ASSIGNMENT_TEXT_AND_RANGE_FUNCTION, itemDTO.getParamValue()));
        }
    }

    private void addWholeDeviceCode(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.WHOLE_DEVICE_CODE_FUNCTION, itemDTO.getParamValue()));
    }

    private void addNASN(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.NASN_FUNCTION, itemDTO.getParamValue()));
    }

    private void addScramblingCode(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.SCRAMBLING_CODE_FUNCTION, itemDTO.getParamValue()));
    }

    private void addNACC(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.NACC_FUNCTION, itemDTO.getParamValue()));
    }

    private void addCustomize(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(itemDTO.getParamRule());
    }

    private void addStbid(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.STBID_FUNCTION, itemDTO.getParamValue()));
    }

    private void addDeviceSerialNumber(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.DSN_CMCC)) {
            previewDTO.setParamRule(String.format(Constant.DSN_CMCC_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.DSN_CUCC)) {
            previewDTO.setParamRule(String.format(Constant.DSN_CUCC_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.DSN_CTCC)) {
            previewDTO.setParamRule(String.format(Constant.DSN_CTCC_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.DSN_CTCC_FTTO)) {
            previewDTO.setParamRule(String.format(Constant.DSN_FTTO_FUNCTION, itemDTO.getParamValue()));
        } else {
            previewDTO.setParamRule(String.format(Constant.DSN_CTCC_SN_FUNCTION, itemDTO.getParamValue()));
        }
    }

    private void addDSN(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        previewDTO.setParamRule(String.format(Constant.D_SN_FUNCTION, itemDTO.getParamValue()));
    }

    private void addGponSnParams(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.GPON_SN)) {
            previewDTO.setParamRule(String.format(Constant.GPON_SN_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(),Constant.GenerationMethod.CUSTOMIZED_GPON_SN)) {
            previewDTO.setParamRule(String.format(Constant.GPON_SN_CUSTOMIZED_FUNCTION, itemDTO.getParamValue()));
        } else {
            previewDTO.setParamRule(Constant.GPON_SN_TELMEX_FUNCTION);
        }
    }

    private void addMACParams(SpTemplateItemDTO itemDTO, SpTemplatePreviewDTO previewDTO) {
        if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_START)) {
            previewDTO.setParamRule(String.format(Constant.MAC_START_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.CUSTOMIZED_MAC_START)) {
            previewDTO.setParamRule(String.format(Constant.MAC_START_CUSTOMIZED_FUNCTION, itemDTO.getParamValue()));
        } else if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_END)) {
            previewDTO.setParamRule(String.format(Constant.MAC_END_FUNCTION, itemDTO.getParamValue()));
        } else {
            previewDTO.setParamRule(String.format(Constant.MAC_ADD_FUNCTION, itemDTO.getParamValue()));
        }
    }

    private String getMacSplitParam(String rule) {
        return rule.replace(SpecialityParamConstant.RESOURCE_POOL_GETSTARTMAC, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.RESOURCE_POOL_GETENDMAC, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY)
                .replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY);
    }

    @Override
    public List<SpTemplateItem> queryList(String templateId) {
        return spTemplateItemRepository.queryList(templateId);
    }

    private String ruleHander(String paramName, String rule, List<SpTemplatePreviewDTO> list) {
        // 替换ROWNUMBER()函数
        if (rule.contains(SpecialityParamConstant.FUNCTION_ROWNUMBER + SpecialityParamConstant.PARENTHESES)) {
            rule = rule.replace(SpecialityParamConstant.FUNCTION_ROWNUMBER + SpecialityParamConstant.PARENTHESES, NumConstant.STRING_ZERO);
        }
        //变量替换
        for (SpTemplatePreviewDTO oItem2 : list) {
            //存在变量，则替换
            if (rule.contains(oItem2.getParamName())) {
                rule = rule.replace(SpecialityParamConstant.BRACKETS_START + oItem2.getParamName() + SpecialityParamConstant.BRACKETS_END,
                        Constant.SINGLE_QUOTE + oItem2.getParamValue().replace(Constant.HORIZON, Constant.STRING_EMPTY) + Constant.SINGLE_QUOTE);
            }
        }
        if (rule.contains(SpecialityParamConstant.BRACKETS_START)) {
            // 判断参数名是否替换完
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_ERROR, new Object[]{paramName});
        }
        try {
            return new TemplateFunctionExpress(rule).caculate();
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{rule});
        }
    }

    /**
     * 递增MAC
     *
     */
    public String incrementMacAddress(String macValue, String separator, long plus) {
        macValue = macValue.replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        long macLong = Long.parseLong(macValue, Constant.INT_16);
        macLong += plus;
        // 递增MAC地址的数值
        String tempMac = String.format("%012X", macLong).toUpperCase();
        // 将长整型转换回MAC地址字符串格式
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tempMac.length(); i += Constant.INT_2) {
            sb.append(tempMac, i, i + Constant.INT_2);
            if (i < tempMac.length() - Constant.INT_2) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }
}
