package com.zte.application.impl;

import com.zte.application.MaterialMaintenanceService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MaterialMaintenanceRepository;
import com.zte.interfaces.dto.MaterialMaintenanceDTO;
import com.zte.interfaces.dto.SolderInfo;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class MaterialMaintenanceServiceImpl implements MaterialMaintenanceService {

    @Autowired
    private MaterialMaintenanceRepository materialRepository;

    @Override
    public Page<MaterialMaintenanceDTO> queryMaterial(MaterialMaintenanceDTO dto) {
        Page<MaterialMaintenanceDTO> page = new Page<>(dto.getCurrent(), dto.getPageSize());
        page.setParams(dto);
        page.setRows(materialRepository.queryMaterial(page));
        return page;
    }
    @Override
    public List<String> queryMaterialByGlue(){
        return materialRepository.queryMaterialByGlue();
    }

    @Override
    public List<String> queryItemCodeByItemName(String itemName){
        return materialRepository.queryItemCodeByItemName(itemName);
    }

    @Override
    public List<String> querySupplierByItemNo(String itemNo){
        return materialRepository.querySupplierByItemNo(itemNo);
    }
    @Override
    public void updateMaterial(MaterialMaintenanceDTO dto) {
        checkParam(dto);
        checkExist(dto);
        materialRepository.updateMaterial(dto);
    }

    @Override
    public void addMaterial(MaterialMaintenanceDTO dto) {
        checkParam(dto);
        checkExist(dto);
        materialRepository.addMaterial(dto);
    }

    @Override
    public int delMaterial(MaterialMaintenanceDTO dto) {
        return materialRepository.delMaterial(dto);
    }

    @Override
    public List<MaterialMaintenanceDTO> queryMaterialByItemNoOrBarcode(String itemNo, String barcode) {
        List<MaterialMaintenanceDTO> materialMaintenanceDTOS = materialRepository.queryMaterialByItemNoOrBarcode("", barcode);
        if (materialMaintenanceDTOS.isEmpty()){
            materialMaintenanceDTOS =  materialRepository.queryMaterialByItemNoOrBarcode(itemNo, "");
        }
        return materialMaintenanceDTOS;
    }

    @Override
    public List<MaterialMaintenanceDTO> getMaterialsBySolderInfoList(List<SolderInfo> solderInfoList) {
        List<MaterialMaintenanceDTO> allMaterialMaintenanceDTOS = new ArrayList<>();

        // 提取barcode和itemNo
        List<String> barcodes = new ArrayList<>();
        List<String> itemNos = new ArrayList<>();

        for (SolderInfo solderInfo : solderInfoList) {
            if (StringUtils.isNotBlank(solderInfo.getBarcode())) {
                barcodes.add(solderInfo.getBarcode());
            }
            if (StringUtils.isNotBlank(solderInfo.getItemNo())) {
                itemNos.add(solderInfo.getItemNo());
            }
        }

        // 如果有有效的barcode，执行第一个查询
        if (!barcodes.isEmpty()) {
            List<MaterialMaintenanceDTO> materialsByBarcode = materialRepository.queryMaterialsByBarcodes(barcodes);
            if (!materialsByBarcode.isEmpty()) {
                allMaterialMaintenanceDTOS.addAll(materialsByBarcode);
            }
        }

        // 如果有有效的itemNo，执行第二个查询
        if (!itemNos.isEmpty()) {
            List<MaterialMaintenanceDTO> materialsByItemNo = materialRepository.queryMaterialsByItemNos(itemNos);
            if (!materialsByItemNo.isEmpty()) {
                allMaterialMaintenanceDTOS.addAll(materialsByItemNo);
            }
        }

        return allMaterialMaintenanceDTOS;
    }


    private void checkParam(MaterialMaintenanceDTO dto) {
        if (StringUtils.isBlank(dto.getMaintenanceType()) || StringUtils.isBlank(dto.getStorageMethod())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        if (Constant.GLUE_FREEZING.equals(dto.getStorageMethod()) && dto.getThawingDuration() == null) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        if (Constant.GLUE_ITEM.equals(dto.getMaintenanceType())) {
            checkItem(dto);
        } else if (Constant.GLUE_BARCODE.equals(dto.getMaintenanceType())) {
            checkBarcode(dto);
        } else {
            checkItemSup(dto);
        }
    }

    private void checkItemSup(MaterialMaintenanceDTO dto) {
        dto.setMaintenanceType(Constant.GLUE_ITEM_SUP);
        if (StringUtils.isBlank(dto.getItemCode()) || StringUtils.isBlank(dto.getSupplier())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        dto.setBarcode(null);
    }

    private void checkBarcode(MaterialMaintenanceDTO dto) {
        if (StringUtils.isBlank(dto.getBarcode())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        dto.setItemCode(null);
        dto.setSupplier(null);
    }

    private void checkItem(MaterialMaintenanceDTO dto) {
        if (StringUtils.isBlank(dto.getItemCode())) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        dto.setBarcode(null);
        dto.setSupplier(null);
    }

    private void checkExist(MaterialMaintenanceDTO dto) {
        if (materialRepository.checkExist(dto) > Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DUPLICATE_MATERIAL_STORAGE_ATTRIBUTES);
        }
    }
}


