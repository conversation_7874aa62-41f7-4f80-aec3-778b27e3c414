package com.zte.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/12 19:34
 */
@Primary
@Service
public class PushStdModelSnDataHandleServiceCompositeImpl implements PushStdModelSnDataHandleService<Object> {

    @Resource
    List<PushStdModelSnDataHandleService<?>> pushStdModelSnDataHandleServices;

    @Override
    public boolean match(String currProcess) {
        return false;
    }

    @Override
    public boolean handlePushStdModelSnData(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle) {
        for (PushStdModelSnDataHandleService<?> pushStdModelSnDataHandleService : pushStdModelSnDataHandleServices) {
            if (pushStdModelSnDataHandleService.match(pushStdModelSnDataHandle.getCurrProcess())) {
                return pushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);
            }
        }
        return false;
    }

    @Override
    public Object getPushMessageData(String currProcess, PushStdModelSnDataExtDTO pushStdModelSnDataExt,
                                     List<WipExtendIdentificationDTO> wipExtendIdentifications) throws JsonProcessingException {
        for (PushStdModelSnDataHandleService<?> pushStdModelSnDataHandleService : pushStdModelSnDataHandleServices) {
            if (pushStdModelSnDataHandleService.match(currProcess)) {
                return pushStdModelSnDataHandleService.getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
            }
        }
        return null;
    }

    @Override
    public boolean validatePushMessageData(String currProcess, Object pushMessageData) {
        for (PushStdModelSnDataHandleService<?> pushStdModelSnDataHandleService : pushStdModelSnDataHandleServices) {
            if (pushStdModelSnDataHandleService.match(currProcess)) {
                return pushStdModelSnDataHandleService.validatePushMessageData(currProcess, pushMessageData);
            }
        }
        return false;
    }

    @Override
    public boolean handlePushStdModelSnDatas(List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles) {
        if (CollectionUtils.isEmpty(pushStdModelSnDataHandles)) {
            return false;
        }
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = pushStdModelSnDataHandles.get(0);
        for (PushStdModelSnDataHandleService<?> pushStdModelSnDataHandleService : pushStdModelSnDataHandleServices) {
            if (pushStdModelSnDataHandleService.match(pushStdModelSnDataHandle.getCurrProcess())) {
                return pushStdModelSnDataHandleService.handlePushStdModelSnDatas(
                    pushStdModelSnDataHandles.stream()
                    .filter(p -> pushStdModelSnDataHandle.getCurrProcess().equals(p.getCurrProcess()))
                    .collect(Collectors.toList()));
            }
        }
        return false;
    }
}
