package com.zte.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.PsDeliveryFeedbackService;
import com.zte.application.TradeDataLogService;
import com.zte.application.impl.helper.DeliveryFeedbackServiceHelper;
import com.zte.common.enums.*;
import com.zte.common.excel.SimpleAnalysisEventListener;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsDeliveryFeedbackDO;
import com.zte.domain.model.PsDeliveryFeedbackRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.gei.common.utils.JsonUtils;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackDownImportVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackVO;
import com.zte.interfaces.VO.PsDeliveryRemarkVO;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackAliDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackSearchDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.MESHttpHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.EXCEL_RESOLUTION_FAILURE;
import static com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/9 下午2:55
 */
@Slf4j
@Service
public class PsDeliveryFeedbackServiceImpl extends AbstractExportTaskHandler<PsDeliveryFeedbackSearchDTO, PsDeliveryFeedbackVO> implements PsDeliveryFeedbackService {
    @Autowired
    private PsDeliveryFeedbackRepository psDeliveryFeedbackRepository;
    @Autowired
    private DeliveryFeedbackServiceHelper serviceHelper;
    @Autowired
    private PsTaskRepository taskRepository;
    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private HrmUserCenterServiceImpl hrmUserInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int save(PsDeliveryFeedbackDO record) {
        //通过任务号查询任务详情
        String orderNo = record.getOrderNo();
        List<String> abnormalCategory = record.getAbnormalCategory();
        if (CollectionUtils.size(abnormalCategory) == NumConstant.NUM_THREE) {
            record.setAbnormalCategoryFirst(abnormalCategory.get(0));
            record.setAbnormalCategorySecond(abnormalCategory.get(1));
            record.setAbnormalCategoryThird(abnormalCategory.get(2));
        }

        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(Collections.singletonList(orderNo));
        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = serviceHelper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);
        //校验数据
        PsDeliveryFeedbackDownImportVO importVO = BeanUtil.copyProperties(record, PsDeliveryFeedbackDownImportVO.class);
        importVO.setLiability(LiabilityEnum.getNameFromCode(importVO.getLiability()));
        String validateResult = serviceHelper.validateData(importVO, orderTaskInfoVOMap);
        log.info("[生产交期反馈]校验结果：{}", validateResult);
        if (StringUtils.isNotBlank(validateResult)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_FAILED, new Object[]{validateResult});
        }

        //转移属性
        record.setScheduleFlag(Constant.FLAG_N);
        List<OrderTaskInfoVO> alibabaTasks = BeanUtil.copyToList(Collections.singletonList(record), OrderTaskInfoVO.class);

        //将任务发送邮件或推送阿里并保存反馈数据
        if (CollectionUtils.isNotEmpty(alibabaTasks)) {
            alibabaTasks.forEach(t -> t.setCustomerNo(orderTaskInfoVOMap.getOrDefault(t.getOrderNo(), new OrderTaskInfoVO()).getCustomerNo()));
            return serviceHelper.sendEmailOrPushAliSaveMessage(alibabaTasks, false);
        }
        return 0;
    }

    @Override
    public Page<PsDeliveryFeedbackVO> listByPage(PsDeliveryFeedbackSearchDTO deliveryFeedbackSearchDTO) throws Exception {
        //校验查询参数
        this.validateQuery(deliveryFeedbackSearchDTO);

        Page<PsDeliveryFeedbackVO> pageInfo = new Page<>(deliveryFeedbackSearchDTO.getPage(), deliveryFeedbackSearchDTO.getRows());
        pageInfo.setParams(deliveryFeedbackSearchDTO);

        List<PsDeliveryFeedbackVO> psDeliveryFeedbackVOS = psDeliveryFeedbackRepository.listByPage(pageInfo);

        // 根据工号获取姓名
        List<String> createEmpNoList = psDeliveryFeedbackVOS.stream().map(PsDeliveryFeedbackVO::getCreateBy).distinct().collect(Collectors.toList());
        List<String> updateEmpNoList = psDeliveryFeedbackVOS.stream().map(PsDeliveryFeedbackVO::getLastUpdatedBy).distinct().collect(Collectors.toList());
        createEmpNoList.addAll(updateEmpNoList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserInfoService.getHrmPersonInfo(createEmpNoList);

        //格式化字典等数据
        this.formatData(psDeliveryFeedbackVOS, hrmPersonInfoDTOMap);

        pageInfo.setRows(psDeliveryFeedbackVOS);
        return pageInfo;
    }

    private void formatData(List<PsDeliveryFeedbackVO> psDeliveryFeedbackVOS, Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap) {
        for (PsDeliveryFeedbackVO psDeliveryFeedbackVO : psDeliveryFeedbackVOS) {
            psDeliveryFeedbackVO.setOrderTypeName(OrderTypeEnum.getNameFromCode(psDeliveryFeedbackVO.getOrderType()));
            psDeliveryFeedbackVO.setLiabilityName(LiabilityEnum.getNameFromCode(psDeliveryFeedbackVO.getLiability()));
            psDeliveryFeedbackVO.setPushStatusName(PushStatusEnum.getNameFromCode(Integer.parseInt(psDeliveryFeedbackVO.getPushStatus())));
            psDeliveryFeedbackVO.setBusinessSceneName(BusinessSceneEnum.getNameFromCode(psDeliveryFeedbackVO.getBusinessScene()));

            // 设置姓名工号
            HrmPersonInfoDTO createHrmPersonInfoDTO = hrmPersonInfoDTOMap.getOrDefault(psDeliveryFeedbackVO.getCreateBy(), new HrmPersonInfoDTO());
            psDeliveryFeedbackVO.setCreateBy(StringUtils.defaultString(createHrmPersonInfoDTO.getEmpName()) + psDeliveryFeedbackVO.getCreateBy());
            HrmPersonInfoDTO updateHrmPersonInfoDTO = hrmPersonInfoDTOMap.getOrDefault(psDeliveryFeedbackVO.getLastUpdatedBy(), new HrmPersonInfoDTO());
            psDeliveryFeedbackVO.setLastUpdatedBy(StringUtils.defaultString(updateHrmPersonInfoDTO.getEmpName()) + psDeliveryFeedbackVO.getLastUpdatedBy());

            //翻译延期原因数据字典
            if (StringUtils.isNoneBlank(psDeliveryFeedbackVO.getLiability(), psDeliveryFeedbackVO.getAbnormalCategoryFirst(), psDeliveryFeedbackVO.getAbnormalCategorySecond(),
                    psDeliveryFeedbackVO.getAbnormalCategoryThird())) {
                List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = this.searchRemark(psDeliveryFeedbackVO.getLiability());
                PsDeliveryRemarkVO firstDeliveryRemarkVO = psDeliveryRemarkVOS.stream().filter(t -> StringUtils.equals(t.getValue(), psDeliveryFeedbackVO.getAbnormalCategoryFirst())).findFirst()
                        .orElse(new PsDeliveryRemarkVO());
                psDeliveryFeedbackVO.setAbnormalCategoryFirstName(firstDeliveryRemarkVO.getLabel());
                if (CollectionUtils.isNotEmpty(firstDeliveryRemarkVO.getChildren())) {
                    PsDeliveryRemarkVO secondDeliveryRemarkVO = firstDeliveryRemarkVO.getChildren().stream().filter(t -> StringUtils.equals(t.getValue(), psDeliveryFeedbackVO.getAbnormalCategorySecond())).findFirst()
                            .orElse(new PsDeliveryRemarkVO());
                    psDeliveryFeedbackVO.setAbnormalCategorySecondName(secondDeliveryRemarkVO.getLabel());
                    if (CollectionUtils.isNotEmpty(secondDeliveryRemarkVO.getChildren())) {
                        PsDeliveryRemarkVO thirdDeliveryRemarkVO = secondDeliveryRemarkVO.getChildren().stream().filter(t -> StringUtils.equals(t.getValue(), psDeliveryFeedbackVO.getAbnormalCategoryThird())).findFirst()
                                .orElse(new PsDeliveryRemarkVO());
                        psDeliveryFeedbackVO.setAbnormalCategoryThirdName(thirdDeliveryRemarkVO.getLabel());
                    }
                }
            }

            psDeliveryFeedbackVO.setAbnormalCategory(Lists.newArrayList(psDeliveryFeedbackVO.getAbnormalCategoryFirst(),
                    psDeliveryFeedbackVO.getAbnormalCategorySecond(), psDeliveryFeedbackVO.getAbnormalCategoryThird()));
        }
    }

    /**
     * 查询导出数量
     *
     * @param psDeliveryFeedbackSearchDTO
     * @return
     */
    @Override
    public Integer countExportTotal(PsDeliveryFeedbackSearchDTO psDeliveryFeedbackSearchDTO) {
        //校验查询参数
        this.validateQuery(psDeliveryFeedbackSearchDTO);

        return psDeliveryFeedbackRepository.countSparePartAllocationQuery(psDeliveryFeedbackSearchDTO);
    }

    /**
     * 导出组件导出方法
     *
     * @param psDeliveryFeedbackSearchDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public List<PsDeliveryFeedbackVO> queryExportData(PsDeliveryFeedbackSearchDTO psDeliveryFeedbackSearchDTO, int pageNo, int pageSize) {
        //校验查询参数
        this.validateQuery(psDeliveryFeedbackSearchDTO);

        Page<PsDeliveryFeedbackVO> record = new Page<>(pageNo, pageSize);
        record.setParams(psDeliveryFeedbackSearchDTO);
        List<PsDeliveryFeedbackVO> psDeliveryFeedbackVOS = psDeliveryFeedbackRepository.listByPage(record);

        // 根据工号获取姓名
        List<String> createEmpNoList = psDeliveryFeedbackVOS.stream().map(PsDeliveryFeedbackVO::getCreateBy).distinct().collect(Collectors.toList());
        List<String> updateEmpNoList = psDeliveryFeedbackVOS.stream().map(PsDeliveryFeedbackVO::getLastUpdatedBy).distinct().collect(Collectors.toList());
        createEmpNoList.addAll(updateEmpNoList);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap;
        try {
            hrmPersonInfoDTOMap = hrmUserInfoService.getHrmPersonInfo(createEmpNoList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //格式化字典等数据
        this.formatData(psDeliveryFeedbackVOS, hrmPersonInfoDTOMap);

        return psDeliveryFeedbackVOS;
    }

    void validateQuery(PsDeliveryFeedbackSearchDTO deliveryFeedbackSearchDTO) {
        //校验：按照工单类型或者反馈人查询必须选择反馈时间,最大查询100个任务号
        if (StringUtils.isNotBlank(deliveryFeedbackSearchDTO.getTaskNo())) {
            List<String> taskNos = Arrays.asList(deliveryFeedbackSearchDTO.getTaskNo().split("[,\\n]"));
            deliveryFeedbackSearchDTO.setTaskNos(taskNos);
            if (CollectionUtils.size(taskNos) > NumConstant.NUM_HUNDRED) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAX_SEARCH_TASKS, new Object[]{NumConstant.NUM_HUNDRED});
            }
        } else {
            boolean existNotEmpty = !StringUtils.isAllBlank(deliveryFeedbackSearchDTO.getBusinessScene(), deliveryFeedbackSearchDTO.getCreateBy());
            if (existNotEmpty && ObjectUtils.allNull(deliveryFeedbackSearchDTO.getCreateStartDate(), deliveryFeedbackSearchDTO.getCreateEndDate())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DELIVERY_FEEDBACK_IS_MUST);
            }
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) throws IOException {
        // 设置响应内容类型和头部信息
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("生产交期反馈模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 使用EasyExcel写操作
        EasyExcel.write(response.getOutputStream(), PsDeliveryFeedbackDownImportVO.class)
                .sheet("Sheet")
                .doWrite(Collections.emptyList()); // 写入空数据，只生成表头
    }

    @Override
    public List<PsDeliveryFeedbackDownImportVO> resolve(MultipartFile file) {
        List<PsDeliveryFeedbackDownImportVO> importVOList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), PsDeliveryFeedbackDownImportVO.class, new SimpleAnalysisEventListener<>(importVOList)).sheet().doRead();
        } catch (Exception e) {
            throw new MesBusinessException(BUSINESSERROR_CODE, EXCEL_RESOLUTION_FAILURE, new Object[]{e.getMessage()});
        }

        return this.getPsDeliveryFeedbackDownImportVOS(importVOList);
    }

    List<PsDeliveryFeedbackDownImportVO> getPsDeliveryFeedbackDownImportVOS(List<PsDeliveryFeedbackDownImportVO> importVOList) {
        log.info("[生产交期反馈导入]本地待导入数据：{}", JsonUtils.toJsonString(importVOList));

        if (CollectionUtils.isEmpty(importVOList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FILE_PARSING_ERROR);
        }
        if (importVOList.size() > NumConstant.NUM_ONE_THOUSAND) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_DATA_SIZE_OVER, new Object[]{"1000"});
        }
        //任务号和延期编号都不能重复
        List<String> taskNoCountErrors = importVOList.stream()
                .filter(t -> StringUtils.isNotBlank(t.getOrderNo()))
                .collect(Collectors.groupingBy(PsDeliveryFeedbackDownImportVO::getOrderNo, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() >= 2)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<String> abnormalNoCountErrors = importVOList.stream()
                .filter(t -> StringUtils.isNotBlank(t.getAbnormalNo()))
                .collect(Collectors.groupingBy(PsDeliveryFeedbackDownImportVO::getAbnormalNo, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() >= 2)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(taskNoCountErrors)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_REPEAT);
        }
        if (CollectionUtils.isNotEmpty(abnormalNoCountErrors)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ABNORMAL_NO_REPEAT);
        }

        //通过任务号查询任务详情
        List<String> orderNos = importVOList.stream().map(PsDeliveryFeedbackDownImportVO::getOrderNo).collect(Collectors.toList());
        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(orderNos);
        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = serviceHelper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);

        //异常的数据
        List<PsDeliveryFeedbackDownImportVO> validatedList = Lists.newArrayList();

        for (PsDeliveryFeedbackDownImportVO importVO : importVOList) {
            importVO.setLiabilityName(importVO.getLiability());
            //校验excel数据
            String validateResult = serviceHelper.validateData(importVO, orderTaskInfoVOMap);
            log.info("[生产交期反馈导入]校验结果：{}", validateResult);
            if (StringUtils.isNotBlank(validateResult)) {
                importVO.setValidateMsg(validateResult);
            }
            validatedList.add(importVO);
        }
        return validatedList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer batchSubmit(List<PsDeliveryFeedbackDownImportVO> importVOList) {
        Date now = new Date();
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());

        //通过任务号查询任务详情
        List<String> orderNos = importVOList.stream().map(PsDeliveryFeedbackDownImportVO::getOrderNo).collect(Collectors.toList());
        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(orderNos);
        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = serviceHelper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);

        List<OrderTaskInfoVO> passValidateList = Lists.newArrayList();
        for (PsDeliveryFeedbackDownImportVO importVO : importVOList) {
            //校验excel数据
            String validateResult = serviceHelper.validateData(importVO, orderTaskInfoVOMap);
            log.info("[生产交期反馈导入]校验结果：{}", validateResult);
            if (StringUtils.isNotBlank(validateResult)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SUBMIT_FAILED, new Object[]{validateResult});
            }
            //原因分类字典翻译
            List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = this.searchRemark(LiabilityEnum.getCodeFromName(importVO.getLiability()));
            Optional<PsDeliveryRemarkVO> firstOpt = psDeliveryRemarkVOS.stream().filter(t -> (StringUtils.equals(t.getLabel(), importVO.getAbnormalCategoryFirst())
                    || StringUtils.equals(t.getValue(), importVO.getAbnormalCategoryFirst()))).findFirst();
            if (firstOpt.isPresent()) {
                importVO.setAbnormalCategoryFirst(firstOpt.get().getValue());
                Optional<PsDeliveryRemarkVO> secondOpt = firstOpt.get().getChildren().stream().filter(t -> (StringUtils.equals(t.getLabel(), importVO.getAbnormalCategorySecond())
                        || StringUtils.equals(t.getValue(), importVO.getAbnormalCategorySecond()))).findFirst();
                if (secondOpt.isPresent()) {
                    importVO.setAbnormalCategorySecond(secondOpt.get().getValue());
                    Optional<PsDeliveryRemarkVO> thirdOpt = secondOpt.get().getChildren().stream().filter(t -> (StringUtils.equals(t.getLabel(), importVO.getAbnormalCategoryThird())
                            || StringUtils.equals(t.getValue(), importVO.getAbnormalCategoryThird()))).findFirst();
                    thirdOpt.ifPresent(psDeliveryRemarkVO -> importVO.setAbnormalCategoryThird(psDeliveryRemarkVO.getValue()));
                }
            }

            OrderTaskInfoVO orderTaskInfoVO = orderTaskInfoVOMap.getOrDefault(importVO.getOrderNo(), new OrderTaskInfoVO());
            OrderTaskInfoVO taskInfoVO = BeanUtil.copyProperties(importVO, OrderTaskInfoVO.class);
            taskInfoVO.setCreateBy(empNo);
            taskInfoVO.setCreateDate(now);
            taskInfoVO.setLastUpdatedBy(empNo);
            taskInfoVO.setLastUpdatedDate(now);
            taskInfoVO.setOperateType(OperateTypeEnum.MANUAL.getCode());
            taskInfoVO.setPushStatus(String.valueOf(PushStatusEnum.PUSHED_NOT_CALLBACK.getCode()));
            taskInfoVO.setOrderType(orderTaskInfoVO.getOrderType());
            taskInfoVO.setBusinessScene(orderTaskInfoVO.getBusinessScene());
            taskInfoVO.setCategory(orderTaskInfoVO.getCategory());
            taskInfoVO.setQuantity(orderTaskInfoVO.getQuantity());
            taskInfoVO.setDateExpectedCompletion(orderTaskInfoVO.getDateExpectedCompletion());
            taskInfoVO.setCustomerNo(orderTaskInfoVO.getCustomerNo());
            taskInfoVO.setLiability(LiabilityEnum.getCodeFromName(importVO.getLiability()));
            taskInfoVO.setEnabledFlag(Constant.FLAG_Y);
            taskInfoVO.setScheduleFlag(Constant.FLAG_N);
            passValidateList.add(taskInfoVO);
        }

        //将任务发送邮件或推送阿里并保存反馈数据
        if (CollectionUtils.isNotEmpty(passValidateList)) {
            return serviceHelper.sendEmailOrPushAliSaveMessage(passValidateList, false);
        }
        return 0;
    }

    @Override
    public void handleB2bCallback(B2bCallBackNewDTO b2bCallBackNewDTO) {
        Date now = new Date();
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        PsDeliveryFeedbackDO deliveryFeedbackDO = new PsDeliveryFeedbackDO();
        deliveryFeedbackDO.setPushStatus(String.valueOf(PushStatusEnum.PUSHED_AND_CALLBACK.getCode()));
        deliveryFeedbackDO.setPushErrorMsg(Constant.HORIZON);
        deliveryFeedbackDO.setLastUpdatedBy(empNo);
        deliveryFeedbackDO.setLastUpdatedDate(now);

        if (!b2bCallBackNewDTO.isSuccess() || !RetCode.SUCCESS_CODE.equals(b2bCallBackNewDTO.getCode())) {
            deliveryFeedbackDO.setPushStatus(String.valueOf(PushStatusEnum.CALLBACK_ERROR.getCode()));
            deliveryFeedbackDO.setPushErrorMsg(JSON.toJSONString(b2bCallBackNewDTO));
        }

        //获取任务号
        String keywords = b2bCallBackNewDTO.getKeywords();
        List<PsDeliveryFeedbackVO> existDeliveryFeedbacks = psDeliveryFeedbackRepository.listByTask(Collections.singletonList(keywords));
        //转换为Map,key-任务号，value-对象
        Map<String, PsDeliveryFeedbackVO> existOrderMap = existDeliveryFeedbacks.stream().collect(Collectors.toMap(PsDeliveryFeedbackVO::getOrderNo, v -> v, (v1, v2) -> v1));
        PsDeliveryFeedbackVO deliveryFeedbackVO = existOrderMap.get(keywords);
        if (Objects.isNull(deliveryFeedbackVO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_NOT_MATCH_RECORD, new Object[]{keywords});
        }
        deliveryFeedbackDO.setId(deliveryFeedbackVO.getId());
        //更新状态
        log.info("[生产交期反馈回调]B2B回调接口，待更新的任务:{}", JsonUtils.toJsonString(deliveryFeedbackDO));
        psDeliveryFeedbackRepository.updateByPrimaryKeySelective(deliveryFeedbackDO);
    }

    @Override
    public List<PsDeliveryRemarkVO> searchRemark(String liability) {
        //通过责任类型查询数据字典节点数据
        if (LiabilityEnum.COMMAND.isMe(liability)) {
            // 阿里责任一级原因及子节点数据字典
            return serviceHelper.getClassNodes(Constant.LOOK_UP_CODE_2025060301, Constant.LOOK_UP_CODE_2025060302, Constant.LOOK_UP_CODE_2025060303);
        }
        if (LiabilityEnum.WORK_ORDER.isMe(liability)) {
            // 厂商责任一级原因及子节点数据字典
            return serviceHelper.getClassNodes(Constant.LOOK_UP_CODE_2025060311, Constant.LOOK_UP_CODE_2025060312, Constant.LOOK_UP_CODE_2025060313);
        }
        return Collections.emptyList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void schedule() {
        StopWatch stopWatch = new StopWatch();
        log.info("[生产交期反馈]定时任务开始");
        stopWatch.start();

        // 阿里任务数据字典
        List<SysLookupValues> sysList = sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(Constant.LOOK_UP_CODE_1004115));
        log.info("[生产交期反馈]查询阿里任务数据字典:{}", JsonUtils.toJsonString(sysList));
        List<String> aliCustomers = sysList.stream().map(SysLookupValues::getLookupMeaning).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        int limit = 100;
        String taskId = null;
        Date startTime = DateUtil.offsetDay(new Date(), -14);
        List<OrderTaskInfoVO> alibabaTasks;
        do {
            //查询阿里任务
            PsDeliveryFeedbackAliDTO feedbackScheduleDTO = new PsDeliveryFeedbackAliDTO();
            feedbackScheduleDTO.setScheduleFlag(Constant.FLAG_Y);
            feedbackScheduleDTO.setCustomerNos(aliCustomers);
            feedbackScheduleDTO.setStartTime(startTime);
            feedbackScheduleDTO.setTaskId(taskId);
            feedbackScheduleDTO.setLimit(limit);

            alibabaTasks = taskRepository.listAlibabaTask(feedbackScheduleDTO);
            if (CollectionUtils.isEmpty(alibabaTasks)) {
                continue;
            }
            //获取最大时间的一个任务，获取偏移开始时间和对应任务ID
            startTime = alibabaTasks.get(alibabaTasks.size() - 1).getLastUpdatedDate();
            taskId = alibabaTasks.get(alibabaTasks.size() - 1).getTaskId();

            List<String> taskNos = alibabaTasks.stream().map(OrderTaskInfoVO::getOrderNo).collect(Collectors.toList());

            //获取定时任务需要排除的任务号
            List<String> excludeTaskNos = psDeliveryFeedbackRepository.listTaskNosOfScheduleExclude(taskNos);

            List<OrderTaskInfoVO> finalAlibabaTasks = alibabaTasks.stream().filter(t -> !excludeTaskNos.contains(t.getOrderNo())).collect(Collectors.toList());
            //将任务发送邮件或推送阿里并保存反馈数据，更新为已调度
            if (CollectionUtils.isEmpty(finalAlibabaTasks)) {
                continue;
            }

            //填充已存在的交期记录数据(筛选定时任务创建的任务)
            this.fillExistRecordData(taskNos, finalAlibabaTasks);

            serviceHelper.sendEmailOrPushAliSaveMessage(finalAlibabaTasks, true);
        } while (alibabaTasks.size() >= limit);

        stopWatch.stop();
        log.info("[生产交期反馈]定时任务结束，耗时:{} ms", stopWatch.getTotalTimeMillis());
    }

    private void fillExistRecordData(List<String> taskNos, List<OrderTaskInfoVO> finalAlibabaTasks) {
        List<PsDeliveryFeedbackVO> psDeliveryFeedbackVOS = psDeliveryFeedbackRepository.listByTask(taskNos);
        Map<String, PsDeliveryFeedbackVO> taskMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(psDeliveryFeedbackVOS)) {
            psDeliveryFeedbackVOS = psDeliveryFeedbackVOS.stream().filter(t -> OperateTypeEnum.SCHEDULED_TASK.isMe(t.getOperateType())).collect(Collectors.toList());
            taskMap = psDeliveryFeedbackVOS.stream().collect(Collectors.toMap(PsDeliveryFeedbackVO::getOrderNo, t -> t, (t1, t2) -> t1));
        }
        for (OrderTaskInfoVO alibabaTask : finalAlibabaTasks) {
            alibabaTask.setOrderType(String.valueOf(NumConstant.NUM_TWO));
            alibabaTask.setScheduleFlag(Constant.FLAG_Y);
            this.fillExistDataOfNull(taskMap, alibabaTask);
        }
    }

    private void fillExistDataOfNull(Map<String, PsDeliveryFeedbackVO> taskMap, OrderTaskInfoVO alibabaTask) {
        if (taskMap.containsKey(alibabaTask.getOrderNo())) {
            PsDeliveryFeedbackVO existDeliveryFeedbackVO = taskMap.get(alibabaTask.getOrderNo());
            alibabaTask.setLiability(existDeliveryFeedbackVO.getLiability());
            alibabaTask.setAbnormalCategoryFirst(existDeliveryFeedbackVO.getAbnormalCategoryFirst());
            alibabaTask.setAbnormalCategorySecond(existDeliveryFeedbackVO.getAbnormalCategorySecond());
            alibabaTask.setAbnormalCategoryThird(existDeliveryFeedbackVO.getAbnormalCategoryThird());
            alibabaTask.setRemark(existDeliveryFeedbackVO.getRemark());
            alibabaTask.setAbnormalNo(existDeliveryFeedbackVO.getAbnormalNo());

            if (Objects.isNull(alibabaTask.getDateEstimatedCompletion())) {
                alibabaTask.setDateEstimatedCompletion(existDeliveryFeedbackVO.getDateEstimatedCompletion());
            }
            if (Objects.isNull(alibabaTask.getDateMaterialEstimatedPrepared())) {
                alibabaTask.setDateMaterialEstimatedPrepared(existDeliveryFeedbackVO.getDateMaterialEstimatedPrepared());
            }
            if (Objects.isNull(alibabaTask.getDateAllMaterialEstimatedPrepared())) {
                alibabaTask.setDateAllMaterialEstimatedPrepared(existDeliveryFeedbackVO.getDateAllMaterialEstimatedPrepared());
            }
            if (Objects.isNull(alibabaTask.getDateScheduledProduction())) {
                alibabaTask.setDateScheduledProduction(existDeliveryFeedbackVO.getDateScheduledProduction());
            }
        }
    }

}
