package com.zte.application.impl;

import com.alibaba.druid.util.StringUtils;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.utils.Constant;
import com.zte.domain.model.CFFactoryRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.AssemblyPushByHandRecordEntityDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.consts.SysConst;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 中心工厂调本地工厂
 */
@Service
public class CenterFactoryCallSiteServiceImpl implements CenterFactoryCallSiteService {

    @Autowired
    private CFFactoryRepository cFFactoryRepository;

    @Autowired
    private SysLookupTypesRepository sysLookupTypesRepository;


    //查询本地工厂是否存在调拨单
    @Override
    public Map queryInfoTransferOrder(String prodplanId) throws Exception {

        Map returnMap=new HashMap();
        Map<String , Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType" , Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        for(SysLookupTypesDTO lookupTypesDto : valuesList)
        {
            Map<String , String>  requestHeader = MESHttpHelper.getHttpRequestHeader();
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, lookupTypesDto.getLookupMeaning());
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE , lookupTypesDto.getLookupMeaning());
            List<PsWorkOrderBasicDTO> workOrderBasicList = PlanScheduleRemoteService.getWorkOrderInfo(lookupTypesDto.getRemark() ,
                    prodplanId , requestHeader);
            //不存在指令，当没有调拨单处理
            if(CollectionUtils.isEmpty(workOrderBasicList)){
                returnMap.put(lookupTypesDto.getLookupMeaning(),Constant.FLAG_N);
            }else {
                String inforExeFlag = workOrderBasicList.stream().filter(e -> Constant.FLAG_Y.equals(e.getInforExeFlag())).findAny().orElse(new PsWorkOrderBasicDTO()).getInforExeFlag();
                returnMap.put(lookupTypesDto.getLookupMeaning(), inforExeFlag);
            }
        }
        return returnMap;

    }

    /**
     * 获取任务信息
     * @param taskNo
     * @param factoryId
     * @return
     * @throws Exception
     */
    @Override
    public PsTask queryPsTaskInfoByTaskNo(String taskNo,String factoryId) throws Exception {
        Map<String , Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType" , Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        SysLookupTypesDTO sysLookupTypesDTO = valuesList.stream().filter(p -> StringUtils.equals(factoryId,p.getLookupMeaning())).findFirst().orElse(null);
        if(sysLookupTypesDTO == null){
            return  null;
        }

        Map<String , String>  requestHeader = MESHttpHelper.getHttpRequestHeader();
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, sysLookupTypesDTO.getLookupMeaning());
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE , sysLookupTypesDTO.getLookupMeaning());
        return PlanScheduleRemoteService.getPsTaskInfo(sysLookupTypesDTO.getRemark() ,
                taskNo , requestHeader);
    }

    /**
     * 获取任务信息
     * @param taskNo
     * @param factoryId
     * @return
     * @throws Exception
     */
    @Override
    public Map<String , Date> queryScheduleStartAndEndTime(String taskNo, String factoryId) throws Exception {
        Map<String , Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType" , Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        SysLookupTypesDTO sysLookupTypesDTO = valuesList.stream().filter(p -> StringUtils.equals(factoryId,p.getLookupMeaning())).findFirst().orElse(null);
        if(sysLookupTypesDTO == null){
            return  null;
        }

        Map<String , String>  requestHeader = MESHttpHelper.getHttpRequestHeader();
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, sysLookupTypesDTO.getLookupMeaning());
        requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE , sysLookupTypesDTO.getLookupMeaning());
        requestHeader.put("X-Mes-Bff", "iMes-bff-authorization");
        return PlanScheduleRemoteService.getScheduleStartAndEndTime(sysLookupTypesDTO.getRemark(), taskNo , requestHeader);
    }

    /**
     * 根据料单以及版本 查询各分工厂以及mes是否存在推送记录
     * @param itemCode
     * @param itemVersion
     * @return
     * @throws Exception
     */
    @Override
    public List<AssemblyPushByHandRecordEntityDTO> queryAssemblyResult(String itemCode, String itemVersion) throws Exception {
        //根据数据字典配置循环调用各分工厂查询推送记录
        Map<String , Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType" , Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        List<AssemblyPushByHandRecordEntityDTO> assemblyPushByHandRecordEntityDTOS=new ArrayList<>();
        for(SysLookupTypesDTO lookupTypesDto : valuesList)
        {
            Map<String , String>  requestHeader = MESHttpHelper.getHttpRequestHeader();
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID, lookupTypesDto.getLookupMeaning());
            requestHeader.put(SysConst.HTTP_HEADER_X_FACTORY_ID_LOW_CASE , lookupTypesDto.getLookupMeaning());
            String url = lookupTypesDto.getRemark()+"/zte-mes-manufactureshare-productionmgmtsys/assemblyresult/getListByItemAndVersion";
            AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO= ProductionmgmtRemoteService.queryAssemblyResult(lookupTypesDto.getDescriptionChin(),itemCode ,
                    itemVersion , requestHeader,url);
            if(assemblyPushByHandRecordEntityDTO!=null){
                //设置工厂id 名称 url
                assemblyPushByHandRecordEntityDTO.setBaseUrl(lookupTypesDto.getRemark());
                assemblyPushByHandRecordEntityDTO.setFactoryName(lookupTypesDto.getDescriptionChinV());
                assemblyPushByHandRecordEntityDTO.setFactoryId(new BigDecimal(lookupTypesDto.getLookupMeaning()));
                assemblyPushByHandRecordEntityDTOS.add(assemblyPushByHandRecordEntityDTO);
            }
        }
        //查询MES的推送记录
        AssemblyPushByHandRecordEntityDTO mesDto=DatawbRemoteService.getListByItemAndVersion(Constant.SOURCE_SYSTEM_MES,itemCode,itemVersion);
        if(mesDto!=null){
            //设置工厂id 名称 url
            mesDto.setFactoryName(Constant.SOURCE_SYSTEM_MES);
            assemblyPushByHandRecordEntityDTOS.add(mesDto);
        }
        return assemblyPushByHandRecordEntityDTOS;

    }
}
