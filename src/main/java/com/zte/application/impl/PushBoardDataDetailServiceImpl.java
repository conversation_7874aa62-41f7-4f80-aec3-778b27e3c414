package com.zte.application.impl;

import com.zte.application.CustomerItemsService;
import com.zte.application.PushBoardDataDetailService;
import com.zte.application.PushBoardDataHeadService;
import com.zte.application.PushBoardDataProcessService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushBoardDataDetailRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.AoiPushDataDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.MctComponentDTO;
import com.zte.interfaces.dto.MdsAoiTestDataDTO;
import com.zte.interfaces.dto.MdsRepairInfoResponseDTO;
import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.interfaces.dto.RepairPushDataDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.NoticeCenterUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.NUM_200;
import static com.zte.common.utils.NumConstant.NUM_500;
import static com.zte.common.utils.NumConstant.NUM_TWENTY;
import static com.zte.common.utils.NumConstant.NUM_ZERO;


@Service("pushBoardDataDetailService")
public class PushBoardDataDetailServiceImpl implements PushBoardDataDetailService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PushBoardDataDetailRepository pushBoardDataDetailrepository;

    @Autowired
    private PushBoardDataHeadService pushBoardDataHeadService;

    @Autowired
    private PushBoardDataProcessService pushBoardDataProcessService;

    @Autowired
    private MdsRemoteService mdsRemoteService;

    @Autowired
    private CustomerItemsService customerItemsService;

    @Autowired
    private NoticeCenterUtils noticeCenterUtils;

    @Autowired
    private SysLookupValuesService sysLookupValuesService;

    @Autowired
    private TradeDataLogService tradeDataLogService;

    // 已推送未回调
    private static final int PUSHED_NOT_CALLBACK = 1;
    // 已推送回调成功
    private static final int PUSHED = 2;

    /**
     * 数据校验失败或推送异常
     */
    private static final int DATA_CHECK_OR_PUSH_FAIL = 8;
    // 回调结果异常
    private static final int CALLBACK_ERROR = 9;

    /**
     * AOI检测结果通过标识
     */
    private static final String PASS = "Pass";

    private static final String MCT = "MCT";
    private static final String BOARD_CAPTURE = "BOARD_CAPTURE";

    /**
     * AOI推送失败阈值
     */
    @Value("${imes.aoi.push.fail.threshold:3}")
    private int pushFailThreshold;

    /**
     * iMES面别和中试AOI记录工序名称映射关系
     */
    @Value("#{${imes.mds.side.station.map:{'SMT-A':'AOI-T','SMT-B':'AOI-B'}}}")
    private Map<String, String> sideToStationMap;

    /**
     * 工厂id和工厂名称映射
     */
    @Value("#{${imes.factory.id.name.map:{'58':'ZTE101','55':'ZTE101','52':'ZTE101','53':'ZTE101','56':'ZTE101'}}}")
    private Map<Integer, String> factoryIdToNameMap;

    @Value("${imes.board.need.push.process:AOI,ICT,Test}")
    private List<String> needPushBusiTypeList;

    @Override
    public int merge(List<PushBoardDataDetailDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumConstant.NUM_ZERO;
        }
        List<String> snList = list.stream().map(PushBoardDataDetailDTO::getSn).distinct().collect(Collectors.toList());
        // 查询已存在条码
        List<String> existSnList = pushBoardDataDetailrepository.getExistSn(snList);
        // 筛选需要新增条码
        List<PushBoardDataDetailDTO> notExistList = list.stream().filter(curr -> !existSnList.contains(curr.getSn())).collect(Collectors.toList());
        // 批量新增(直接调用,无需事务控制)
        return this.batchInsert(notExistList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<PushBoardDataDetailDTO> list) {
        int count = NumConstant.NUM_ZERO;
        if (CollectionUtils.isEmpty(list)) {
            return count;
        }
        // 分批
        List<List<PushBoardDataDetailDTO>> listOfList = CommonUtils.splitList(list);
        for (List<PushBoardDataDetailDTO> tempList : listOfList) {
            count += pushBoardDataDetailrepository.batchInsert(tempList);
        }
        return count;
    }

    @Override
    public void pushAoiDataToB2B(int preDays) {
        // 参数校验
        if (preDays <= NUM_ZERO) {
            preDays = NumConstant.NUM_SEVEN;
        }

        // 记录上次同步最后时间,用于增量分页查询
        Date startTime = DateUtil.addDay(-preDays).getTime();
        String sn = "";
        int limit = NUM_500;
        boolean first = true;
        List<PushBoardDataDetailDTO> detailList = new ArrayList<>();
        while (first || detailList.size() >= limit){
            first = false;
            detailList = pushBoardDataDetailrepository.getNeedPushSn(startTime, sn, limit);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            // 下次增量查询参数
            PushBoardDataDetailDTO lastData = detailList.get(detailList.size() - 1);
            startTime = lastData.getCreateDate();
            sn = lastData.getSn();

            List<String> needPushSnList = detailList.stream().map(PushBoardDataDetailDTO::getSn).distinct().collect(Collectors.toList());
            List<PushBoardDataProcessDTO> needPushDataList = pushBoardDataProcessService.getAndInitNeedPushData(needPushSnList, Constant.AOI);
            if (CollectionUtils.isEmpty(needPushDataList)) {
                continue;
            }

            // 根据12位料单获取客户物料代码维护信息
            Set<String> itemNo12Set = new HashSet<>();
            for (PushBoardDataProcessDTO boardDataProcessDTO : needPushDataList) {
                String itemNo12 = StringUtils.substring(boardDataProcessDTO.getItemNo(), NUM_ZERO, NumConstant.NUM_TWELVE);
                itemNo12Set.add(itemNo12);
                boardDataProcessDTO.setItemNo12(itemNo12);
            }
            List<CustomerItemsDTO> customerItemsDTOS = customerItemsService.queryListByZteCodes(new ArrayList<>(itemNo12Set));
            // 分组
            Map<String, String> itemNo12MaterialTypeMap = customerItemsDTOS.stream()
                    .collect(Collectors.toMap(
                            // 键：12位料单
                            CustomerItemsDTO::getZteCode,
                            // 值：客户物料型号
                            CustomerItemsDTO::getCustomerMaterialType
                    ));

            // 根据条码查询中试AOI检测记录
            List<PushBoardDataProcessDTO> needWarnList = new ArrayList<>();
            for (PushBoardDataProcessDTO boardDataProcessDTO : needPushDataList) {
                boardDataProcessDTO.setCustomerMaterialType(itemNo12MaterialTypeMap.get(boardDataProcessDTO.getItemNo12()));
                try {
                    this.checkAndPush(boardDataProcessDTO, needWarnList);
                } catch (Exception e) {
                    logger.error("推送AOI数据未知异常", e);
                    boardDataProcessDTO.setErrorMsg("未知异常:" + StringUtils.substring(e.getMessage(), NUM_ZERO, NumConstant.NUM_1500));
                    needWarnList.add(boardDataProcessDTO);
                    this.checkFailTimeAndUpdate(boardDataProcessDTO);
                }
            }
            // 发送邮件告警
            this.sendAlarmEmail(needWarnList);
        }
    }

    @Override
    public void syncPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        // 根据B2B回调结果更新process状态
        pushBoardDataProcessService.syncProcessPushStatus(pushBoardDataProcessDTO);
        // 条码下所有子项都推送且回调成功后更新detail表状态
        this.syncDetailPushStatus(pushBoardDataProcessDTO);
        // 批次下所有条码都推送成功后更新head表状态
        this.syncHeadPushStatus(pushBoardDataProcessDTO);
    }

    @Override
    public void syncHeadPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        Date startTime = this.initStartTime(pushBoardDataProcessDTO);
        String lastProdplanId = "";
        int limit = NUM_TWENTY;
        boolean first = true;
        List<PushBoardDataHeadDTO> headList = new ArrayList<>();
        while (first || headList.size() >= limit) {
            first = false;
            // 增量查询已推送未回调条码
            headList = pushBoardDataHeadService.getNotPushedList(startTime, lastProdplanId, limit);
            if (CollectionUtils.isEmpty(headList)) {
                continue;
            }
            // 下次增量查询参数
            PushBoardDataHeadDTO lastData = headList.get(headList.size() - 1);
            startTime = lastData.getLastUpdatedDate();
            lastProdplanId = lastData.getProdplanId();

            // 查询已推送完成批次
            List<String> prodplanIdList = pushBoardDataDetailrepository.getPushDoneProdplanId(headList);
            if (CollectionUtils.isEmpty(prodplanIdList)) {
                continue;
            }
            pushBoardDataHeadService.updateStatusByProdplanId(prodplanIdList, NumConstant.NUM_ONE);
        }
    }

    @Override
    public void syncDetailPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        Date startTime = this.initStartTime(pushBoardDataProcessDTO);
        String lastSn = "";
        int limit = NUM_200;
        boolean first = true;
        List<PushBoardDataDetailDTO> detailList = new ArrayList<>();
        while (first || detailList.size() >= limit){
            first = false;
            // 增量查询已推送未回调条码
            detailList = pushBoardDataDetailrepository.getNotPushedList(startTime, lastSn, limit);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            // 下次增量查询参数
            PushBoardDataDetailDTO lastData = detailList.get(detailList.size() - 1);
            startTime = lastData.getLastUpdatedDate();
            lastSn = lastData.getSn();

            // 根据条码查询各业务类型推送数据
            List<String> snList = detailList.stream().map(PushBoardDataDetailDTO::getSn).distinct().collect(Collectors.toList());
            List<PushBoardDataProcessDTO> processList = pushBoardDataProcessService.getDataBySnAndBusinessType(snList, needPushBusiTypeList);
            List<String> needUpdateSnList = new ArrayList<>();
            // 按条码和业务类型分组
            Map<String, List<PushBoardDataProcessDTO>> snAndBusiTypeMap = processList.stream()
                    .collect(Collectors.groupingBy(process -> process.getSn() + Constant.UNDER_LINE + process.getBusinessType()));
            this.checkAndGetNeedUpdateSn(snList, snAndBusiTypeMap, needUpdateSnList);
            if (CollectionUtils.isEmpty(needUpdateSnList)) {
                continue;
            }
            pushBoardDataDetailrepository.updateStatusBySn(needUpdateSnList, NumConstant.NUM_ONE);
        }
    }

    private Date initStartTime(PushBoardDataProcessDTO pushBoardDataProcessDTO) {
        Integer preDays = pushBoardDataProcessDTO.getBeforeDay();
        // 参数校验
        if (preDays <= NUM_ZERO) {
            preDays = NumConstant.NUM_SEVEN;
        }
        // 记录上次同步最后时间,用于增量分页查询
        Date startTime = DateUtil.addDay(-preDays).getTime();
        return startTime;
    }

    private void checkAndGetNeedUpdateSn(List<String> snList, Map<String, List<PushBoardDataProcessDTO>> snAndBusiTypeMap, List<String> needUpdateSnList) {
        for (String sn : snList) {
            this.checkAndGetNeedUpdateSn(snAndBusiTypeMap, needUpdateSnList, sn);
        }
    }

    private void checkAndGetNeedUpdateSn(Map<String, List<PushBoardDataProcessDTO>> snAndBusiTypeMap, List<String> needUpdateSnList, String sn) {
        for (String businessType : needPushBusiTypeList) {
            List<PushBoardDataProcessDTO> tempList = snAndBusiTypeMap.get(sn + Constant.UNDER_LINE + businessType);
            if (CollectionUtils.isEmpty(tempList)) {
                // 存在为空数据则表示未全部推送成功
                return;
            }
        }
        needUpdateSnList.add(sn);
    }

    private void sendAlarmEmail(List<PushBoardDataProcessDTO> needWarnList) {
        String alarmInfo = this.packAlarmInfo(needWarnList);
        noticeCenterUtils.sendEmail(Constant.STRING_EMPTY, alarmInfo, Constant.LOOK_UP_VALUE_7599001);
    }

    /**
     * 组装告警信息
     * @param needWarnList
     * @return
     */
    private String packAlarmInfo(List<PushBoardDataProcessDTO> needWarnList) {
        StringBuffer html = new StringBuffer();
        //有产出数据
        String div = "<br/><br/><div>";
        String htmlTitle = "<p style='margin:0;font-size:13pt'>" + Constant.AOI_PUSH_ERROR_SN + "</p>";
        html.append(div + htmlTitle + "<table style='border:1px rgb(204,204,204);width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        html.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        html.append("<td>"+Constant.INDEX+"</td>");
        html.append("<td>"+Constant.PUSH_ERROR_SN+"</td>");
        html.append("<td>"+Constant.PUSH_ERROR_MSG+"</td>");
        html.append("<td>"+Constant.PUSH_ERROR_TIMES+"</td>");
        html.append("</tr>");
        for (int index = 0; index < needWarnList.size(); index++) {
            PushBoardDataProcessDTO detailDTO = needWarnList.get(index);
            int num = index+1;
            html.append("<tr>");
            html.append("<td>" +  num + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(detailDTO.getSn()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(detailDTO.getErrorMsg()) + "</td>");
            html.append("<td>" +  CommonUtils.getStrTransNull(detailDTO.getPushFailCount()) + "</td>");
            html.append("</tr>");
        }
        html.append("</table>");
        html.append("<hr style='height:5px;border:none;background-color:rgb(0,102,153);'/></div>");
        return html.toString();
    }

    /**
     * 校验并推送AOI数据
     * 同时更新推送状态等
     * @param boardDataProcessDTO
     */
    private void checkAndPush(PushBoardDataProcessDTO boardDataProcessDTO, List<PushBoardDataProcessDTO> needWarnList) throws Exception {
        List<MdsAoiTestDataDTO> aoiTestDataList = mdsRemoteService.getAoiTestData(boardDataProcessDTO.getSn());
        if (CollectionUtils.isEmpty(aoiTestDataList)) {
            boardDataProcessDTO.setErrorMsg("中试AOI数据不存在");
            needWarnList.add(boardDataProcessDTO);
            this.checkFailTimeAndUpdate(boardDataProcessDTO);
            return;
        }

        // 按面别分组，
        Map<String, List<MdsAoiTestDataDTO>> groupedAndSorted = aoiTestDataList.stream()
                .collect(Collectors.groupingBy(
                        MdsAoiTestDataDTO::getStationName, // 按面别分组
                        Collectors.collectingAndThen(
                                Collectors.toList(), // 先收集到一个列表
                                list -> {
                                    list.sort(Comparator.comparing(MdsAoiTestDataDTO::getFinishedTime)); // 按测试时间排序
                                    return list;
                                }
                        )
                ));
        if (StringUtils.isBlank(boardDataProcessDTO.getSides())) {
            // 无面别数据默认校验2面
            boardDataProcessDTO.setSides(Constant.SMT_A_B);
        }
        List<MdsAoiTestDataDTO> needPushAoiList = new ArrayList<>();
        for (String side : boardDataProcessDTO.getSides().split(Constant.COMMA)) {
            String stationName = sideToStationMap.get(side);
            List<MdsAoiTestDataDTO> aoiList = groupedAndSorted.get(stationName);
            if (CollectionUtils.isEmpty(aoiList)) {
                boardDataProcessDTO.setErrorMsg(String.format("%s面AOI数据不存在", stationName));
                needWarnList.add(boardDataProcessDTO);
                this.checkFailTimeAndUpdate(boardDataProcessDTO);
                return;
            }
            // 校验AOI信息
            if (!this.checkAoi(aoiList, boardDataProcessDTO)) {
                needWarnList.add(boardDataProcessDTO);
                this.checkFailTimeAndUpdate(boardDataProcessDTO);
                return;
            }
            needPushAoiList.addAll(aoiList);
        }
        // 全部校验通过后推送AOI记录和维修记录
        this.pushDataToB2B(needPushAoiList, boardDataProcessDTO);
    }

    /**
     * 校验维修记录
     * 如果单板AOI有Fail结果则要求一定要有对应维修记录
     * @param aoiList
     * @return
     */
    private boolean checkAoi(List<MdsAoiTestDataDTO> aoiList, PushBoardDataProcessDTO boardDataProcessDTO) {
        // 最后一条记录检测必须为Pass
        MdsAoiTestDataDTO lastAoiData = aoiList.get(aoiList.size() - 1);
        if (!StringUtils.equals(PASS, lastAoiData.getResult())) {
            boardDataProcessDTO.setErrorMsg(String.format("%s面最后一次AOI测试时间是%s，结果不为Pass，需要确认数据", lastAoiData.getStationName(), DateUtil.convertDateToString(lastAoiData.getFinishedTime(), DateUtil.DATE_FORMATE_FULL)));
            return false;
        }

        // 筛选检测结果不通过记录
        List<MdsAoiTestDataDTO> failList = aoiList.stream().filter(curr -> !StringUtils.equals(PASS, curr.getResult())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failList)) {
            return true;
        }
        // 如果单板AOI有Fail结果则要求一定要维修记录
        // 根据条码查询维修记录
        List<MdsRepairInfoResponseDTO> repairInfoList = mdsRemoteService.getRepairInfoBySn(boardDataProcessDTO.getSn());
        if (CollectionUtils.isEmpty(repairInfoList)) {
            boardDataProcessDTO.setErrorMsg("AOI存在Fail记录但无对应维修记录");
            return false;
        }
        // 按inspectId分组
        Map<String, List<MdsRepairInfoResponseDTO>> map = repairInfoList.stream().collect(Collectors.groupingBy(MdsRepairInfoResponseDTO::getInspectId));
        for (MdsAoiTestDataDTO failAoiData : failList) {
            List<MdsRepairInfoResponseDTO> repairList = map.get(failAoiData.getInspectId());
            if (CollectionUtils.isEmpty(repairList)) {
                boardDataProcessDTO.setErrorMsg("AOI存在Fail记录但无对应维修记录");
                return false;
            }
            // 设置失败AOI对应维修记录用于后续推送
            failAoiData.setRepairInfoList(repairList);
        }
        // 全部校验通过
        return true;
    }

    private void pushDataToB2B(List<MdsAoiTestDataDTO> needPushAoiList, PushBoardDataProcessDTO boardDataProcessDTO) throws Exception {
        // 实际推送B2B数据列表
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (MdsAoiTestDataDTO aoiTestDataDTO : needPushAoiList) {
            // 组装推送阿里数据
            CustomerDataLogDTO pushCustomerDataLogDTO = this.getCustomerDataLogDTO(boardDataProcessDTO, aoiTestDataDTO);
            logger.info("AOI推送数据：" + JacksonJsonConverUtil.beanToJson(pushCustomerDataLogDTO));
            dataList.add(pushCustomerDataLogDTO);

            if (!CollectionUtils.isEmpty(aoiTestDataDTO.getRepairInfoList())) {
                for (MdsRepairInfoResponseDTO repairInfo : aoiTestDataDTO.getRepairInfoList()) {
                    // 组装维修数据
                    CustomerDataLogDTO customerDataLogDTO = this.getCustomerDataLogDTO(boardDataProcessDTO, repairInfo);
                    logger.info("AOI维修推送数据：" + JacksonJsonConverUtil.beanToJson(customerDataLogDTO));
                    dataList.add(customerDataLogDTO);
                }
            }
        }
        // 推送数据至B2B
        tradeDataLogService.pushDataOfExceptionRollback(dataList);
        boardDataProcessDTO.setPushStatus(PUSHED_NOT_CALLBACK);
        boardDataProcessDTO.setPushDate(new Date());
        boardDataProcessDTO.setErrorMsg("");
        boardDataProcessDTO.setPushNumber(dataList.size());
        pushBoardDataProcessService.update(boardDataProcessDTO);
    }

    private CustomerDataLogDTO getCustomerDataLogDTO(PushBoardDataProcessDTO boardDataProcessDTO, MdsRepairInfoResponseDTO repairInfo) {
        RepairPushDataDTO repairPushData = this.packRepairPushData(boardDataProcessDTO, repairInfo);
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(Constant.AOI_REPAIR + repairPushData.getProblemRequestId());
        customerDataLogDTO.setKeywords(repairPushData.getProblemRequestId());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setCustomerName(boardDataProcessDTO.getCustomerName());
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setProjectPhase(Constant.AOI);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_AOI_REPAIR_DATA_PUSH);
        customerDataLogDTO.setTaskNo(boardDataProcessDTO.getTaskNo());
        customerDataLogDTO.setItemNo(boardDataProcessDTO.getItemNo());
        customerDataLogDTO.setSn(boardDataProcessDTO.getSn());
        customerDataLogDTO.setFactoryId(boardDataProcessDTO.getFactoryId());
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(repairPushData));
        return customerDataLogDTO;
    }

    private CustomerDataLogDTO getCustomerDataLogDTO(PushBoardDataProcessDTO boardDataProcessDTO, MdsAoiTestDataDTO aoiTestDataDTO) {
        AoiPushDataDTO aoiPushData = this.packAoiPushData(boardDataProcessDTO, aoiTestDataDTO);
        CustomerDataLogDTO pushCustomerDataLogDTO = new CustomerDataLogDTO();
        pushCustomerDataLogDTO.setId(Constant.AOI + aoiPushData.getRequestId());
        pushCustomerDataLogDTO.setKeywords(aoiPushData.getRequestId());
        pushCustomerDataLogDTO.setOrigin(Constant.IMES);
        pushCustomerDataLogDTO.setCustomerName(boardDataProcessDTO.getCustomerName());
        pushCustomerDataLogDTO.setProjectName(Constant.ALIBABA);
        pushCustomerDataLogDTO.setProjectPhase(Constant.AOI);
        pushCustomerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_AOI_DATA_PUSH);
        pushCustomerDataLogDTO.setTaskNo(boardDataProcessDTO.getTaskNo());
        pushCustomerDataLogDTO.setItemNo(boardDataProcessDTO.getItemNo());
        pushCustomerDataLogDTO.setSn(boardDataProcessDTO.getSn());
        pushCustomerDataLogDTO.setFactoryId(boardDataProcessDTO.getFactoryId());
        pushCustomerDataLogDTO.setCreateBy(Constant.IMES);
        pushCustomerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        pushCustomerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(aoiPushData));
        logger.info("AOI推送数据：" + JacksonJsonConverUtil.beanToJson(aoiPushData));
        return pushCustomerDataLogDTO;
    }

    private RepairPushDataDTO packRepairPushData(PushBoardDataProcessDTO boardDataProcessDTO, MdsRepairInfoResponseDTO repairInfo) {
        RepairPushDataDTO repairPushDataDTO = new RepairPushDataDTO();
        repairPushDataDTO.setProblemRequestId(repairInfo.getInspectId() + repairInfo.getComponentName());
        repairPushDataDTO.setType(BOARD_CAPTURE);
        repairPushDataDTO.setStationName(repairInfo.getStationName());
        repairPushDataDTO.setSn(boardDataProcessDTO.getSn());
        repairPushDataDTO.setBrand(Constant.ZTE);
        repairPushDataDTO.setErrorCode(repairInfo.getErrorCode());
        repairPushDataDTO.setErrorPosition(repairInfo.getComponentName());
        repairPushDataDTO.setErrorMsg(repairInfo.getErrorMsg());
        repairPushDataDTO.setActionCode(repairInfo.getActionCode());
        repairPushDataDTO.setActionMsg(repairInfo.getActionMsg());
        if (StringUtils.isNotBlank(repairInfo.getNewComponentSn())) {
            // 存在部件替换
            MctComponentDTO preComponent = new MctComponentDTO();
            preComponent.setComponentSn(repairInfo.getOldComponentSn());
            preComponent.setComponentType(repairInfo.getOldComponentType());
            preComponent.setDateCode(repairInfo.getOldDateCode());
            preComponent.setComponentModel(repairInfo.getOldComponentModel());
            preComponent.setComponentBrand(repairInfo.getOldComponentBrand());
            preComponent.setComponentMpn(repairInfo.getOldComponentMpn());

            MctComponentDTO newComponent = new MctComponentDTO();
            newComponent.setComponentSn(repairInfo.getNewComponentSn());
            newComponent.setComponentType(repairInfo.getNewComponentType());
            newComponent.setDateCode(repairInfo.getNewDateCode());
            newComponent.setComponentModel(repairInfo.getNewComponentModel());
            newComponent.setComponentBrand(repairInfo.getNewComponentBrand());
            newComponent.setComponentMpn(repairInfo.getNewComponentMpn());

            Map<String, MctComponentDTO> map = new HashMap<>();
            map.put("pre_component", preComponent);
            map.put("new_component", newComponent);
            repairPushDataDTO.getReplaceComponents().add(map);
        }
//                repairPushData.put("replace_components", []);
        return repairPushDataDTO;
    }

    private AoiPushDataDTO packAoiPushData(PushBoardDataProcessDTO boardDataProcessDTO, MdsAoiTestDataDTO aoiTestDataDTO) {
        AoiPushDataDTO aoiPushDataDTO = new AoiPushDataDTO();
        aoiPushDataDTO.setRequestId(aoiTestDataDTO.getInspectId());
        aoiPushDataDTO.setType(MCT);
        aoiPushDataDTO.setWorkorderId(boardDataProcessDTO.getTaskNo());
        aoiPushDataDTO.setBoardSn(boardDataProcessDTO.getSn());
        aoiPushDataDTO.setBrand(Constant.ZTE);
        aoiPushDataDTO.setStationName(aoiTestDataDTO.getStationName());
        aoiPushDataDTO.setStartedTime(aoiTestDataDTO.getStartedTime() == null ? "" : DateUtil.convertDateToString(aoiTestDataDTO.getStartedTime(), DateUtil.DATE_FORMATE_FULL));
        aoiPushDataDTO.setFinishedTime(aoiTestDataDTO.getFinishedTime() == null ? "" : DateUtil.convertDateToString(aoiTestDataDTO.getFinishedTime(), DateUtil.DATE_FORMATE_FULL));
        aoiPushDataDTO.setResult(aoiTestDataDTO.getResult());
        aoiPushDataDTO.setMessage(aoiTestDataDTO.getMessage());
//        aoiPushDataDTO.setOssFileKey(Arrays.asList(aoiTestDataDTO.getOssFileKey()));
        aoiPushDataDTO.setManufacturerName(StringUtils.defaultString(factoryIdToNameMap.get(boardDataProcessDTO.getFactoryId()), boardDataProcessDTO.getFactoryId() + ""));
        aoiPushDataDTO.setBoardMpn(boardDataProcessDTO.getCustomerMaterialType());
        aoiPushDataDTO.setLine(aoiTestDataDTO.getLineName());
        return aoiPushDataDTO;
    }

    /**
     * 校验失败次数并更新数据
     * @param boardDataProcessDTO
     */
    private void checkFailTimeAndUpdate(PushBoardDataProcessDTO boardDataProcessDTO) {
        boardDataProcessDTO.setPushFailCount(boardDataProcessDTO.getPushFailCount() + NumConstant.NUM_ONE);
        if (boardDataProcessDTO.getPushFailCount() >= pushFailThreshold) {
            // 失败3次才修改状态,修改后不会再捞取
            boardDataProcessDTO.setPushStatus(DATA_CHECK_OR_PUSH_FAIL);
        }
        pushBoardDataProcessService.update(boardDataProcessDTO);
    }
}