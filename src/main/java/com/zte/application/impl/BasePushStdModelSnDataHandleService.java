package com.zte.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Maps;
import com.zte.application.CustomerItemsService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.application.PushStdModelSnDataService;
import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushStdModelSnDataSub;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.ProductSnReportDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zte.itp.msa.core.exception.GlobalDefaultBaseExceptionHandler.getTrace;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/13 15:58
 */
@Slf4j
public abstract class BasePushStdModelSnDataHandleService<T> extends BasePushStdModelSnDataService implements PushStdModelSnDataHandleService<T> {

    /**
     * 推送失败阈值
     */
    @Value("${imes.std.push.fail.threshold:3}")
    protected int pushFailThreshold;

    @Resource
    FixBomCommonService fixBomCommonService;
    @Resource
    TradeDataLogService tradeDataLogService;

    @Resource
    PsTaskExtendedService psTaskExtendedService;
    @Resource
    PushStdModelSnDataService pushStdModelSnDataService;

    @Resource
    protected SysLookupValuesRepository sysLookupValuesRepository;

    @Resource
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Resource
    IscpRemoteService iscpRemoteService;

    @Resource
    private CustomerItemsService customerItemsService;

    @Override
    public boolean handlePushStdModelSnData(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle) {
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = pushStdModelSnDataRepository.selectExtByPrimaryKey(pushStdModelSnDataHandle.getId());
        if (pushStdModelSnDataExt == null || Constant.PUSH_STATUS.NOT_PUSHED != pushStdModelSnDataExt.getPushStatus()) {
            // 标模任务SN推送消息不存在 返回失败标识
            return false;
        }
        pushStdModelSnDataExt.setStockName(pushStdModelSnDataHandle.getStockName());

        boolean result = false;
        PushStdModelSnDataDTO pushStdModelSnData = new PushStdModelSnDataDTO();

        pushStdModelSnData.setId(pushStdModelSnDataExt.getId());
        pushStdModelSnData.setCurrProcess(pushStdModelSnData.getCurrProcess());
        pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);
        pushStdModelSnData.setPushFailCount(pushStdModelSnDataExt.getPushFailCount());
        List<WipExtendIdentificationDTO> identifications = pushStdModelSnDataHandle.getWipExtendIdentifications();
        if (Objects.isNull(identifications)) {
            identifications = new ArrayList<>();
        }
        try {
            result = this.doHandlePushStdModelSnData(pushStdModelSnDataHandle.getCurrProcess(), pushStdModelSnDataExt, identifications);
            if (result) {
                pushStdModelSnData.setPushDate(new Date());
                pushStdModelSnData.setVirtualSn(pushStdModelSnDataExt.getVirtualSn());
                pushStdModelSnData.setOriginalSn(pushStdModelSnDataExt.getOriginalSn());
                // 更新状态为已推送未回调
                pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);
                // 更新推送失败次数为 0
                pushStdModelSnData.setPushFailCount(0);
            } else {
                // 设置校验失败
                pushStdModelSnData.setErrorMsg("标模任务SN推送数据校验异常");
                this.wrapFailCountAndPushStatus(pushStdModelSnData);
            }
        } catch (Exception e) {
            log.error("标模任务SN推送未知异常", e);
            if (e instanceof MesBusinessException) {
                MesBusinessException e1 = (MesBusinessException) e;
                LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES);
                String msg = lmb.getMessage(e1.getExMsgId(), e1.getParams());
                pushStdModelSnData.setErrorMsg("标模任务SN推送未知异常:" + StringUtils.substring(msg, 0, NumConstant.NUM_1500));
            } else {
                pushStdModelSnData.setErrorMsg("标模任务SN推送未知异常:" + StringUtils.substring(getTrace(e), 0, NumConstant.NUM_1500));
            }
            this.wrapFailCountAndPushStatus(pushStdModelSnData);
        }
        pushStdModelSnDataRepository.update(pushStdModelSnData);
        return result;
    }

    @Override
    public T getPushMessageData(String currProcess, PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications) throws JsonProcessingException {
        // 根据fixBomHeadId获取MBOM详情
        List<FixBomDetailDTO> fixBomDetails = fixBomCommonService.queryTreeNodeByHeadId(pushStdModelSnDataExt.getFixBomHeadId());
        fixBomCommonService.adjustItemNumber(fixBomDetails, NumConstant.NUM_ONE);
        // 组装数据
        return this.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);
    }

    private boolean doHandlePushStdModelSnData(String currProcess,
                                               PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications) throws JsonProcessingException {
        // 产品条码上报
        if (productSnReportCurrProcess.equals(currProcess)) {
            return reportSn(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
        } else {
            // 组装数据
            return pushData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
        }
    }

    private boolean reportSn(String currProcess, PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications) throws JsonProcessingException {
        List<FixBomDetailDTO> list = fixBomCommonService.queryFixBomDetailByHeadId(pushStdModelSnDataExt.getFixBomHeadId());
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_DETAIL_LOST, new Object[]{pushStdModelSnDataExt.getFixBomHeadId()});
        }
        List<T> pushDataList = new ArrayList<>();
        // 组装数据
        T pushMessageData = this.getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
        pushDataList.add(pushMessageData);
        this.setVirAndOriSn(pushStdModelSnDataExt, pushDataList);
        return pushListData(pushStdModelSnDataExt, pushDataList, currProcess);
    }

    /**
     * moc机型需更新虚拟mock条码和虚拟mock条码原始条码至推送信息表
     * @param pushStdModelSnDataExt
     * @param pushDataList
     */
    private void setVirAndOriSn(PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<T> pushDataList) {
        if (CollectionUtils.isEmpty(pushDataList) || !(pushDataList.get(0) instanceof ProductSnReportDTO)) {
            return;
        }
        ProductSnReportDTO productSnReportDTO = (ProductSnReportDTO) pushDataList.get(0);
        if (productSnReportDTO.getProductSn() != null && StringUtils.endsWith(productSnReportDTO.getProductSn().getSnNo(), Constant.MOC_SUFFIX)) {
            // 如果主条码为moc，更新至推送表
            String mocSn = productSnReportDTO.getProductSn().getSnNo();
            pushStdModelSnDataExt.setVirtualSn(mocSn);
            pushStdModelSnDataExt.setOriginalSn(StringUtils.substring(mocSn, Constant.NUM_ZERO, mocSn.indexOf(Constant.MOC_SUFFIX)));
        }
    }

    private boolean pushListData(PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<T> pushDataList, String currProcess) {
        for (int i = 0; i < pushDataList.size(); i++) {
            T pushMessageData = pushDataList.get(i);
            // 校验数据是否正常
            if (!validatePushMessageData(currProcess, pushMessageData)) {
                // 校验不通过， 返回失败标识
                return false;
            }
        }
        // 最多只有两个集合元素
        for (int i = 0; i < pushDataList.size(); i++) {
            T pushMessageData = pushDataList.get(i);
            if (i == 0) {
                // 消息推送
                this.doPushMessage(pushStdModelSnDataExt, pushMessageData);
            } else {
                // 消息推送
                CustomerDataLogDTO customerDataLogDTO = getDataLogDTO(pushStdModelSnDataExt, pushMessageData);
                int i1 = pushStdModelSnDataSubRepository.selectBySnPriority(pushStdModelSnDataExt.getSn(), i, pushStdModelSnDataExt.getTaskNo());
                // 不存在推送数据
                if (i1 == 0) {
                    // 优先级低的数据 插入子表
                    PushStdModelSnDataSub dataSub = new PushStdModelSnDataSub();
                    dataSub.setId(idGenerator.snowFlakeIdStr());
                    dataSub.setSn(pushStdModelSnDataExt.getSn());
                    dataSub.setTradeDataLogId(customerDataLogDTO.getId());
                    dataSub.setPushStatus(Constant.INT_1);
                    dataSub.setPushDate(new Date());
                    dataSub.setPriority(i);
                    dataSub.setCurrProcess(Constant.STR_20);
                    dataSub.setTaskNo(pushStdModelSnDataExt.getTaskNo());
                    pushStdModelSnDataSubRepository.insertDataList(Collections.singletonList(dataSub));
                    customerDataLogDTO.setKeywords(dataSub.getId() + Constant.CALL_BACK_DATA_SUB);
                    // 推送B2B
                    tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTO);
                }
                break;
            }
        }
        return true;
    }

    private CustomerDataLogDTO getDataLogDTO(PushStdModelSnDataExtDTO pushStdModelSnDataExt, T pushMessageData) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setKeywords(pushStdModelSnDataExt.getId());
        customerDataLogDTO.setTaskNo(pushStdModelSnDataExt.getTaskNo());
        customerDataLogDTO.setFactoryId(pushStdModelSnDataExt.getFactoryId());
        customerDataLogDTO.setCustomerName(pushStdModelSnDataExt.getTaskCustomerNo());
        customerDataLogDTO.setMessageType(this.getPushStdModelSnDataMessageType());
        customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setProjectPhase(StringUtils.substring(this.getPushStdModelSnDataMessageType(),0,Constant.INT_30));
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setSn(pushStdModelSnDataExt.getSn());
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(pushMessageData));
        return customerDataLogDTO;
    }

    private boolean pushData(String currProcess, PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications) throws JsonProcessingException {
        // 组装数据
        T pushMessageData = this.getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
        // 校验数据是否正常
        if (!validatePushMessageData(currProcess, pushMessageData)) {
            // 校验不通过， 返回失败标识
            return false;
        }
        // 消息推送
        return this.doPushMessage(pushStdModelSnDataExt, pushMessageData);
    }

    protected boolean doPushMessage(PushStdModelSnDataExtDTO pushStdModelSnDataExt, T pushMessageData) {
        // 消息推送
        CustomerDataLogDTO customerDataLogDTO = getDataLogDTO(pushStdModelSnDataExt, pushMessageData);
        // 推送B2B
        tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTO);

        // 推送消息成功， 返回成功标识
        return true;
    }

    /**
     * 封装失败次数和推送状态
     *
     * @param pushStdModelSnData pushStdModelSnData
     */
    protected void wrapFailCountAndPushStatus(PushStdModelSnDataDTO pushStdModelSnData) {
        Integer pushFailCount = pushStdModelSnData.getPushFailCount();
        pushStdModelSnData.setPushFailCount(pushFailCount == null ? NumConstant.NUM_ONE : pushFailCount + NumConstant.NUM_ONE);
        if (pushStdModelSnData.getPushFailCount() >= pushFailThreshold) {
            // 失败3次才修改状态,修改后不会再捞取
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.DATA_CHECK_OR_PUSH_FAIL);
        }
    }

    /**
     * 封装推送消息数据
     *
     * @param pushStdModelSnDataExt pushStdModelSnDataExt
     * @param wipExtendIdentifications wipExtendIdentifications
     * @param fixBomDetails fixBomDetails
     * @return 推送消息数据
     */
    protected abstract T wrapPushMessageData(
            PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications,
            List<FixBomDetailDTO> fixBomDetails) throws JsonProcessingException;

    /**
     * 获取标模SN数据推送消息消息类型
     *
     * @return 标模SN数据推送消息消息类型
     */
    protected abstract String getPushStdModelSnDataMessageType();

    protected Map<String, WipExtendIdentificationDTO> getSnWipExtendIdentificationMapAndWrapItemNoSnMap(
            List<WipExtendIdentificationDTO> wipExtendIdentifications, Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap) {
        if (CollectionUtils.isEmpty(wipExtendIdentifications)) {
            return Maps.newHashMap();
        }
        return wipExtendIdentifications.stream()
                .filter(wipExtendIdentification -> StringUtils.isNotBlank(wipExtendIdentification.getSn()))
                .collect(Collectors.toMap(WipExtendIdentificationDTO::getSn, wipExtendIdentification -> {
                    String wipExtendIdentificationItemNo = wipExtendIdentification.getItemNo();
                    if (StringUtils.isNotBlank(wipExtendIdentificationItemNo)) {
                        if (itemNoSnMap.containsKey(wipExtendIdentificationItemNo)) {
                            itemNoSnMap.get(wipExtendIdentificationItemNo).add(wipExtendIdentification);
                        } else {
                            List<WipExtendIdentificationDTO> list = new ArrayList<>();
                            list.add(wipExtendIdentification);
                            itemNoSnMap.put(wipExtendIdentificationItemNo, list);
                        }
                    }
                    return wipExtendIdentification;
                }, (k1, k2) -> k1));
    }
}
