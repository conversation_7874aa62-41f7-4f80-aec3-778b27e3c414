package com.zte.application.impl.cbom;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.PackingListConfigRepository;
import com.zte.domain.model.PsTaskExtendedRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.cbom.FixBomDetailRepository;
import com.zte.domain.model.cbom.FixBomHeadRepository;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.infrastructure.remote.IccRemoteService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PackingListConfigDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.interfaces.dto.mbom.FixBomHeadDTO;
import com.zte.interfaces.dto.mbom.GbomDetailDTO;
import com.zte.interfaces.dto.mbom.MbomQueryDTO;
import com.zte.interfaces.dto.mbom.MbomResDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class FixBomCommonServiceImpl implements FixBomCommonService {

    @Value("${refresh.fix.bom.before.day:180}")
    private Integer refreshFixBomBeforeDay;
    @Autowired
    private PsTaskExtendedRepository psTaskExtendedRepository;
    @Autowired
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Autowired
    private IccRemoteService iccRemoteService;
    @Autowired
    private CpqdRemoteService cpqdRemoteService;
    @Autowired
    private PdmRemoteService pdmRemoteService;
    @Autowired
    private CustomerItemsRepository customerItemsRepository;
    @Autowired
    private PackingListConfigRepository packingListConfigRepository;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private FixBomHeadRepository fixBomHeadRepository;
    @Autowired
    private FixBomDetailRepository fixBomDetailRepository;
    @Autowired
    private ICenterRemoteService iCenterRemoteService;
    @Autowired
    private PsTaskExtendedService psTaskExtendedService;
    @Value("${imes.system.message.icenter:0668000851}")
    private String imesSystemMessageIcenter;
    @Value("${fix.bom.splitCharacters://}")
    private String splitCharacters;

    /**
     * 特定物料类型(成品料)
     */
    @Value("${imes.std.task.specific.itemType:成品料}")
    private List<String> specificItemTypeList;

    /* Started by AICoder, pid:52faedd742i7d4c141170abb600af96c8fb872c9 */

    /**
     * 重新刷新FixBom detail
     *
     * @param taskNo 任务号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLockAnnotation(redisKey = "refreshFixBomId", redisLockTime = 3000)
    public void reCreateFixBomByTaskNo(String taskNo) {
        String empNo = RequestHeadValidationUtil.validaEmpno();

        // 获取任务扩展表信息
        PsTaskExtendedDTO psTaskExtendedDTO = this.getPsTaskExtendedByTaskNo(taskNo);

        String fixBomHeadId = psTaskExtendedDTO.getFixBomHeadId();
        if (StringUtils.isBlank(fixBomHeadId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_DETAIL_LOST,
                    new Object[]{psTaskExtendedDTO.getTaskNo()});
        }

        List<SysLookupValues> sysList = sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(Constant.LOOK_UP_CODE_1004115));
        List<String> aliCustomerList = sysList.stream()
                .map(SysLookupValues::getLookupMeaning)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        MbomResDTO mbomResDTO = getMbomResDTO(psTaskExtendedDTO, aliCustomerList);
        if (StringUtils.isNotBlank(psTaskExtendedDTO.getCbomErrorMessage())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG,
                    new Object[]{psTaskExtendedDTO.getCbomErrorMessage()});
        }

        if (mbomResDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_DETAIL_LOST,
                    new Object[]{psTaskExtendedDTO.getTaskNo()});
        }

        // 删除老数据
        fixBomHeadRepository.deleteByIdList(Collections.singletonList(fixBomHeadId), empNo);
        fixBomDetailRepository.deleteByHeadIdList(Collections.singletonList(fixBomHeadId), empNo);

        FixBomHeadDTO mbomMain = mbomResDTO.getMbomMain();
        mbomMain.setId(idGenerator.snowFlakeIdStr());
        mbomMain.setTaskNo(psTaskExtendedDTO.getTaskNo());
        psTaskExtendedDTO.setFixBomHeadId(mbomMain.getId());

        List<FixBomDetailDTO> mbomItemList = mbomResDTO.getMbomItemList();
        for (FixBomDetailDTO item : mbomItemList) {
            item.setId(idGenerator.snowFlakeIdStr());
            item.setFixBomId(mbomMain.getFixBomId());
            item.setHeadId(mbomMain.getId());
        }

        // 新增头
        fixBomHeadRepository.insertBatch(Collections.singletonList(mbomMain));

        // 新增明细
        List<List<FixBomDetailDTO>> splitList = CommonUtils.splitList(mbomItemList, Constant.INT_50);
        splitList.forEach(fixBomDetailRepository::insertBatch);

        psTaskExtendedDTO.setLastUpdatedBy(empNo);
        // 更新头状态
        psTaskExtendedRepository.updatePsExtendedBatch(Collections.singletonList(psTaskExtendedDTO));
    }

    /* Ended by AICoder, pid:52faedd742i7d4c141170abb600af96c8fb872c9 */

    /**
     * 处理标模fixBom逻辑类
     */
    @Override
    @AlarmAnnotation(alarmName = "refresh_fix_bom_id_error", alarmKey = "1004001", alarmTitle = "生成fixBom数据定时任务异常",
            alarmTime = 30000)
    @RedisDistributedLockAnnotation(redisKey = "refreshFixBomId", redisLockTime = 30000)
    public void refreshFixBomId() {
        List<PsTaskExtendedDTO> list = psTaskExtendedRepository.queryTaskExtendedList(refreshFixBomBeforeDay);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.refreshPsTasksFixBom(list);
    }

    private PsTaskExtendedDTO getPsTaskExtendedByTaskNo(String taskNo) {
        // 获取任务扩展表信息
        PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedRepository.queryExtendedByTaskNo(taskNo);
        if (psTaskExtendedDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_NO_IS_NULL);
        }
        return psTaskExtendedDTO;
    }

    private void refreshPsTasksFixBom(List<PsTaskExtendedDTO> taskExtendedList) {
        // 阿里任务数据字典
        List<SysLookupValues> sysList = sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(Constant.LOOK_UP_CODE_1004115));
        List<String> aliCustomerList = sysList.stream().map(SysLookupValues::getLookupMeaning).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        for (PsTaskExtendedDTO taskExtendedDTO : taskExtendedList) {
            MbomResDTO mbomResDTO = this.getMbomResDTO(taskExtendedDTO, aliCustomerList);
            // 处理数据然后更新/新增到数据库
            this.buildThenSaveToDataBase(taskExtendedDTO, mbomResDTO);
        }
        // 处理失败发送通知
        this.sendCenterMsg(taskExtendedList);
    }

    @Override
    public void createFixBomByTaskNo(String taskNo) {
        // 获取任务扩展表信息
        this.refreshPsTasksFixBom(Lists.newArrayList(this.getPsTaskExtendedByTaskNo(taskNo)));
    }

    /**
     * 处理失败发送icenter 消息通知
     *
     * @param list 通知
     */
    private void sendCenterMsg(List<PsTaskExtendedDTO> list) {
        StringBuilder stringBuilder = new StringBuilder();
        list.stream().filter(item -> Constant.FLAG_N.equals(item.getFixBomComplete()))
                .forEach(item -> stringBuilder.append(CommonUtils.getLmbMessage(MessageId.TASK_FIX_BOM_ERROR,
                        new String[]{item.getTaskNo(), item.getCbomErrorMessage()})));
        if (StringUtils.isNotBlank(stringBuilder)) {
            iCenterRemoteService.sendMessage(CommonUtils.getLmbMessage(MessageId.TASK_FIX_BOM_SUBJECT), stringBuilder.toString(),
                    imesSystemMessageIcenter);
        }
    }

    private MbomResDTO getMbomResDTO(PsTaskExtendedDTO taskExtendedDTO, List<String> aliCustomerList) {
        MbomResDTO mbomResDTO = null;
        try {
            // 已清空上次报错信息
            taskExtendedDTO.setCbomErrorMessage(null);
            taskExtendedDTO.setFixBomComplete(Constant.FLAG_N);
            // 不是阿里的任务默认处理完成
            if (!aliCustomerList.contains(taskExtendedDTO.getCustomerNo())) {
                taskExtendedDTO.setFixBomComplete(Constant.FLAG_Y);
            } else {
                // 根据任务的物料代码查询CPQD,接口获取任务的FixbomID【ZTE-XXX.XXX.XXX-####】，客户物料名称（三段码）查询阿里指令和大G和FIXBOM编码关联关系
                this.queryAndSetFixBomCode(taskExtendedDTO);
                // 每个都需要重新处理
                mbomResDTO = this.buildMbomDetail(taskExtendedDTO);
                // 处理数据数 删除标识可能需要 重新为N
                this.refreshDeleteFlag(mbomResDTO);
                taskExtendedDTO.setFixBomComplete(Constant.FLAG_Y);

            }
        } catch (Exception e) {
            log.error("getMbomResDTO" + taskExtendedDTO.getTaskNo(), e);
            this.setErrorMsg(taskExtendedDTO, e);
            mbomResDTO = null;
        }
        return mbomResDTO;
    }

    private void setErrorMsg(PsTaskExtendedDTO taskExtendedDTO, Exception e) {
        taskExtendedDTO.setFixBomId(null);
        taskExtendedDTO.setFixBomHeadId(null);
        taskExtendedDTO.setFixBomComplete(Constant.FLAG_N);
        String message = e.getMessage();
        if (e instanceof MesBusinessException) {
            MesBusinessException error = (MesBusinessException) e;
            Object[] params = error.getParams();
            if (Objects.isNull(params)) {
                params = new Object[]{};
            }
            String[] parStr = new String[params.length];
            for (int i = 0; i < params.length; i++) {
                parStr[i] = String.valueOf(params[i]);
            }
            message = CommonUtils.getLmbMessage(error.getExMsgId(), parStr);
        }
        taskExtendedDTO.setCbomErrorMessage(message);
    }

    private Map<String, GbomDetailDTO> getPdmPartNoGbomDetailMap(PsTaskExtendedDTO taskExtendedDTO) {
        // 根据任务物料代码从PDM获得物料清单 B0823.1 v1 GBOM下层或上层关系查询
        List<GbomDetailDTO> gbomDetail = pdmRemoteService.queryGbomDetail(taskExtendedDTO.getItemNo());
        // 将物料清单根据imes客户物料代码基础表+客户，记录zte代码对应的MPN（客户物料代码）、客户部件类型、合作模式
        if (CollectionUtils.isEmpty(gbomDetail)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDM_MBOM_LOST,
                    new Object[]{taskExtendedDTO.getItemNo()});
        }
        // 物料代码
        return gbomDetail.stream().collect(Collectors.toMap(GbomDetailDTO::getPartNo, item -> item, (k1, k2) -> k1));
    }

    private List<CustomerItemsDTO> getCustomerItemsList(PsTaskExtendedDTO taskExtendedDTO, List<String> itemNoList) {
        // 将物料清单根据imes客户物料代码基础表+客户，记录zte代码对应的MPN（客户物料代码）、客户部件类型、合作模式
        List<CustomerItemsDTO> resultCustomList = new LinkedList<>();
        List<List<String>> splitList = CommonUtils.splitList(itemNoList, Constant.INT_5);
        for (List<String> items : splitList) {
            List<CustomerItemsDTO> customerItemsDTOList = customerItemsRepository.queryListByZteCodAndCustomerNumber(items, taskExtendedDTO.getCustomerNo());
            if (CollectionUtils.isNotEmpty(customerItemsDTOList)) {
                resultCustomList.addAll(customerItemsDTOList);
            }
        }
        return resultCustomList;
    }

    private MbomResDTO buildMbomDetail(PsTaskExtendedDTO taskExtendedDTO) {
        // 获取pdm物料代码映射
        Map<String, GbomDetailDTO> pdmItemMap = this.getPdmPartNoGbomDetailMap(taskExtendedDTO);

        // 将物料清单根据imes客户物料代码基础表+客户，记录zte代码对应的MPN（客户物料代码）、客户部件类型、合作模式
        List<CustomerItemsDTO> customItemsList = this.getCustomerItemsList(taskExtendedDTO, Lists.newArrayList(pdmItemMap.keySet()));
        Map<String, CustomerItemsDTO> customerItemsMap = this.getCustomerItemsDTOMap(customItemsList);
        List<String> customerComponentTypeList = customItemsList.stream().map(CustomerItemsDTO::getCustomerComponentType).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        // 根据客户部件类型+客户，从iMES FixBOM&箱单配置表获得箱单是否上传、是否计价物料、箱单优先级字段
        String customerNo = taskExtendedDTO.getCustomerNo();
        List<PackingListConfigDTO> packList = packingListConfigRepository.queryPackListByCustomer(customerNo, customerComponentTypeList);
        Map<String, PackingListConfigDTO> packMap = packList.stream().collect(Collectors.toMap(PackingListConfigDTO::getCustomerComponentType, v -> v, (k1, k2) -> k1));

        // 三段码
        String customerItemName = taskExtendedDTO.getCustomerItemName();
        // 二段码
        String customerSub = customerItemName.substring(0, customerItemName.lastIndexOf("."));
        // 查询Icc BOM明细信息
        Map<String, MbomResDTO> mbomResMap = this.queryIccBom(taskExtendedDTO);
        // 二段码bom详情
        MbomResDTO customerSubMbomRes = this.getMbmoFromMapByProductName(mbomResMap, customerNo, customerSub);
        // 二段码 bom清单
        List<FixBomDetailDTO> customerSubMbomItemList = customerSubMbomRes.getMbomItemList();
        // 三段码 bom详情 bom 清单映射关系
        // 混合云没有三段码的物料，所以不需要查询
        List<FixBomDetailDTO> customerItemNameMbomItemList = new ArrayList<>();
        if (!Constant.CLOUD_TYPE_HC.equals(taskExtendedDTO.getCloudType())){
            customerItemNameMbomItemList = this.getMbmoFromMapByProductName(mbomResMap, customerNo, customerItemName).getMbomItemList();
        }
        // 获取所有三段码清单物料名称集合
        Set<String> customerItemNameFixBomDetailItemNameSet = customerItemNameMbomItemList.stream().map(FixBomDetailDTO::getItemName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 将二段码中不存在的物料 从三段码中获取合并
        this.mergeFixBomDetails(customerSubMbomItemList, customerItemNameMbomItemList, customItemsList, packMap, customerNo);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = Lists.newArrayList();
        Map<String, FixBomDetailDTO> itemSeqFixBomDetailMap = Maps.newHashMap();
        // MBOM中MPN不为空且物料类型=厂商部件的记录（供应商料号）与转了ERP物料清单中的MPN的物料比对，记录对应的ZTE代码，找不到zte代码的删除。MBOM中MPN为空的记录说明有下层节点，需要进行保留。
        Set<String> set = new HashSet<>();
        for (FixBomDetailDTO fixBomDetail : customerSubMbomItemList) {
            itemSeqFixBomDetailMap.put(fixBomDetail.getItemSeq(), fixBomDetail);
            // 设置bomdetail 的 itemsupplierNo
            this.setFixBomDetailItemSupplierNo(fixBomDetail, customerItemName, customerItemNameFixBomDetailItemNameSet);
            // 没有维护 默认删除标识为Y
            fixBomDetail.setDeleteFlag(Constant.FLAG_Y);
            // 物料类型=厂商部件/成品料 的才处理
            if (!Constant.COMPONENTS_OF_THE_MANUFACTURER.equals(fixBomDetail.getItemType())
                    && !Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(fixBomDetail.getItemType())) {
                continue;
            }
            // 根据itemSupplierNo设置属性， 并封装bomRequired的bom清单
            this.setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails(
                    fixBomDetail, customerItemsMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,set));
        }
        // 向上递归遍历 赋值成品料 fixBomRequired 与 boxBomRequired
        fixOrBoxBomRequiredFixBomDetails.forEach(fixBomDetail -> this.wrapParentFixOrBoxBomRequired(itemSeqFixBomDetailMap, fixBomDetail));
        return customerSubMbomRes;
    }

    private Map<String, CustomerItemsDTO> getCustomerItemsDTOMap(List<CustomerItemsDTO> customItemsList) {
        Map<String, CustomerItemsDTO> customerItemsMap = new HashMap<>();
        for (CustomerItemsDTO customerItemsDTO : customItemsList) {
            String customerCodeStr = customerItemsDTO.getCustomerCode();
            customerItemsDTO.setCustomerCodeList(Lists.newArrayList());
            if(StringUtils.isEmpty(customerCodeStr)){
                continue;
            }
            String[] customerCodeArr = customerCodeStr.split(splitCharacters);
            customerItemsDTO.setCustomerCodeList(Lists.newArrayList(customerCodeArr));
            for (String customerCode : customerCodeArr) {
                CustomerItemsDTO dto = new CustomerItemsDTO();
                BeanUtils.copyProperties(customerItemsDTO,dto);
                dto.setCustomerCode(customerCode);
                dto.setWholeCustomerCode(customerCodeStr);
                customerItemsMap.put(customerCode,dto);
            }
        }
        return customerItemsMap;
    }

    private void setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails(
            FixBomDetailDTO fixBomDetail, Map<String, CustomerItemsDTO> customerMap, Map<String, PackingListConfigDTO> packMap,
            Map<String, GbomDetailDTO> pdmItemMap, Pair<List<FixBomDetailDTO>,Set<String>> pair) {
        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = pair.getFirst();
        Set<String> set = pair.getSecond();
        String itemSupplierNo = fixBomDetail.getItemSupplierNo();
        if (StringUtils.isBlank(itemSupplierNo)) {
            return;
        }
        CustomerItemsDTO dto = customerMap.get(itemSupplierNo);
        if (Objects.isNull(dto)) {
            return;
        }
        if (set.contains(dto.getWholeCustomerCode())) {
            return;
        }
        set.add(dto.getWholeCustomerCode());
        fixBomDetail.setZteCode(dto.getZteCode());
        // 设置客户属性
        this.setCustomerProperties(fixBomDetail, dto, packMap, pdmItemMap);
        if (Constant.FLAG_Y.equals(fixBomDetail.getFixBomRequired())
                || Constant.FLAG_Y.equals(fixBomDetail.getBoxBomRequired())) {
            fixBomDetail.setDeleteFlag(Constant.FLAG_N);
            fixOrBoxBomRequiredFixBomDetails.add(fixBomDetail);
        }
    }


    private void setFixBomDetailItemSupplierNo(
            FixBomDetailDTO fixBomDetailDTO, String customerItemName, Set<String> customerItemNameFixBomDetailItemNameSet) {
        if (!Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(fixBomDetailDTO.getItemType())) {
            // 非成品料不需要处理
            return;
        }
        // 成品料的客户部件类型固定设置为server.configmodel
        fixBomDetailDTO.setCustomerComponentType(Constant.CUSTOMER_COMPONENT_TYPE_CONFIG_MODEL);
        if (Constant.STRING_ZERO.equals(fixBomDetailDTO.getItemLevel())) {
            // 整机 MPN取任务三段机型
            fixBomDetailDTO.setItemSupplierNo(customerItemName);
            return;
        }
        String itemName = fixBomDetailDTO.getItemName();
        if (!StringUtils.isNotBlank(itemName)) {
            return;
        }
        // 中间层的 成品料 MPN 赋值为三段码 （根据成品料的itemName作为二段码任意匹配一个三段码）
        for (String customerItemNameFixBomDetailItemName : customerItemNameFixBomDetailItemNameSet) {
            if (customerItemNameFixBomDetailItemName.split("\\.").length == NumConstant.NUM_THREE
                    && itemName.equals(customerItemNameFixBomDetailItemName.substring(0, customerItemNameFixBomDetailItemName.lastIndexOf(".")))) {
                fixBomDetailDTO.setItemSupplierNo(customerItemNameFixBomDetailItemName);
                return;
            }
        }
    }

    private List<FixBomDetailDTO> getExtraCustomerItemNameFixBomDetails(
            List<CustomerItemsDTO> extraCustomerItemsList, List<FixBomDetailDTO> customerItemNameFixBomDetails,
            Map<String, PackingListConfigDTO> packMap, String customerNo) {
        Map<String, FixBomDetailDTO> customerItemNameMbomItemMap = customerItemNameFixBomDetails.stream()
                .filter(item -> StringUtils.isNotBlank(item.getItemSupplierNo()))
                .collect(Collectors.toMap(FixBomDetailDTO::getItemSupplierNo, v -> v, (k1, k2) -> k1));
        return extraCustomerItemsList.stream()
                .filter(item -> {
                    return threePieceCode(packMap, customerNo, item, customerItemNameMbomItemMap);
                }).map(item -> customerItemNameMbomItemMap.get(item.getCustomerCode()))
                .sorted(this.getFixBomDetailComparator()).collect(Collectors.toList());
    }

    private boolean threePieceCode(Map<String, PackingListConfigDTO> packMap, String customerNo, CustomerItemsDTO item, Map<String, FixBomDetailDTO> customerItemNameMbomItemMap) {
        String customerCode = item.getCustomerCode();
        // 三段码中存在这个mpn任一一个则需要合并，否则校验客户部件类型是否需要上传以及计价物料
        for (String code : item.getCustomerCodeList()) {
            if (customerItemNameMbomItemMap.containsKey(code)) {
                return true;
            }
        }
        if (this.isCustomerItemsFixBomRequired(item, packMap) || this.isCustomerItemsBoxBomRequired(item, packMap)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PDM_MBOM_ITEM_ICC_MBOM_LOST,
                    new Object[]{customerNo, customerCode });
        }
        return false;
    }

    private void doMergeFixBomDetails(
            List<FixBomDetailDTO> customerSubFixBomDetails, List<FixBomDetailDTO> extraMbomItems, int maxSeqNo) {
        Map<String, FixBomDetailDTO> originItemSeqFixBomDetailMap = extraMbomItems.stream()
                .collect(Collectors.toMap(FixBomDetailDTO::getItemSeq, v -> v, (k1, k2) -> k1));
        // 新序号对应子级数量
        Map<String, Integer> newItemSeqChildCountMap = Maps.newHashMap();
        for (FixBomDetailDTO extraMbomItem : extraMbomItems) {
            String originItemSeq = extraMbomItem.getItemSeq();
            String originParentItemSeq = originItemSeq.substring(0, originItemSeq.lastIndexOf(Constant.LINE));
            FixBomDetailDTO parentMbomItem = originItemSeqFixBomDetailMap.get(originParentItemSeq);
            if (parentMbomItem != null) {
                String parentNewItemSeq = parentMbomItem.getItemSeq();
                Integer parentNewItemSeqChildSize =
                        Optional.ofNullable(newItemSeqChildCountMap.get(parentNewItemSeq)).orElse(0) + NumConstant.NUM_ONE;

                extraMbomItem.setItemLevel(String.valueOf(Integer.parseInt(parentMbomItem.getItemLevel()) + NumConstant.NUM_ONE));
                extraMbomItem.setItemSeq(parentNewItemSeq + Constant.LINE + parentNewItemSeqChildSize);
                newItemSeqChildCountMap.put(parentNewItemSeq, parentNewItemSeqChildSize);
            } else {
                maxSeqNo = maxSeqNo + NumConstant.NUM_ONE;

                extraMbomItem.setItemLevel(Constant.STR_ONE);
                extraMbomItem.setItemSeq(Constant.STR_ONE + Constant.LINE + maxSeqNo);
                newItemSeqChildCountMap.put(extraMbomItem.getItemSeq(), NumConstant.NUM_ZERO);
            }
            // 将三段码物料添加到二段码物料清单
            customerSubFixBomDetails.add(extraMbomItem);
        }
    }

    private void mergeFixBomDetails(
            List<FixBomDetailDTO> customerSubFixBomDetails, List<FixBomDetailDTO> customerItemNameFixBomDetails,
            List<CustomerItemsDTO> customItemsList, Map<String, PackingListConfigDTO> packMap, String customerNo) {
        Map<String, FixBomDetailDTO> itemSupplierNoFixBomDetailMap = Maps.newHashMap();
        // 找出二段码bom中第一层中最大序号编号并封装MPN与bomDetail映射关系
        int maxSeqNo = this.getFixBomLevel1MaxSeqNoAndWrapItemSupplierNoDetailMap(customerSubFixBomDetails, itemSupplierNoFixBomDetailMap);
        // 找出需要匹配三段码的客户物料数据
        List<CustomerItemsDTO> extraCustomerItemsList = customItemsList.stream()
                .filter(item -> {
                    return twoPieceCode(item, itemSupplierNoFixBomDetailMap);
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(extraCustomerItemsList)) {
            return;
        }
        // 获取需要添加到二段码清单中的三段码清单
        List<FixBomDetailDTO> extraMbomItems =
                this.getExtraCustomerItemNameFixBomDetails(extraCustomerItemsList, customerItemNameFixBomDetails, packMap, customerNo);
        // 调用合并逻辑
        this.doMergeFixBomDetails(customerSubFixBomDetails, extraMbomItems, maxSeqNo);
    }

    private boolean twoPieceCode(CustomerItemsDTO item, Map<String, FixBomDetailDTO> itemSupplierNoFixBomDetailMap) {
        String customerCode = item.getCustomerCode();
        if (StringUtils.isBlank(customerCode)) {
            return false;
        }
        // 检查itemSupplierNoFixBomDetailMap的key是否包含customerCodeArr中的任何一个
        for (String code : item.getCustomerCodeList()) {
            if (itemSupplierNoFixBomDetailMap.containsKey(code)) {
                // 如果包含任何一个，则过滤掉
                return false;
            }
        }
        // 如果不包含任何一个，则保留
        return true;
    }

    private Comparator<FixBomDetailDTO> getFixBomDetailComparator() {
        return (item1, item2) -> {
            String o1 = item1.getItemSeq();
            String o2 = item2.getItemSeq();
            /* Started by AICoder, pid:s0700kfc5b08d4e141630b1990ce5d437a92bf21 */
            // 分割字符串，获取层级部分
            String[] parts1 = o1.split(Constant.LINE);
            String[] parts2 = o2.split(Constant.LINE);

            // 比较层级（连字符的数量）
            int levelComparison = Integer.compare(parts1.length, parts2.length);
            if (levelComparison != 0) {
                return levelComparison;
            }

            // 在同一层级内，按数字部分的字典序排序
            for (int i = 0; i < parts1.length; i++) {
                int partComparison = Integer.compare(Integer.parseInt(parts1[i]), Integer.parseInt(parts2[i]));
                if (partComparison != 0) {
                    return partComparison;
                }
            }

            // 如果所有部分都相同，返回0
            return 0;
            /* Ended by AICoder, pid:s0700kfc5b08d4e141630b1990ce5d437a92bf21 */
        };
    }

    private int getFixBomLevel1MaxSeqNoAndWrapItemSupplierNoDetailMap(
            List<FixBomDetailDTO> fixBomDetailList, Map<String, FixBomDetailDTO> itemSupplierNoFixBomDetailMap) {
        int maxSeqNo = 0;
        for (FixBomDetailDTO fixBomDetail : fixBomDetailList) {
            if (StringUtils.isNotBlank(fixBomDetail.getItemSupplierNo())) {
                itemSupplierNoFixBomDetailMap.put(fixBomDetail.getItemSupplierNo(), fixBomDetail);
            }
            if (Constant.STR_ONE.equals(fixBomDetail.getItemLevel())) {
                String itemSeq = fixBomDetail.getItemSeq();
                maxSeqNo = Math.max(maxSeqNo, Integer.parseInt(itemSeq.substring(itemSeq.lastIndexOf(Constant.LINE) + 1)));
            }
        }
        return maxSeqNo;
    }

    private void wrapParentFixOrBoxBomRequired(Map<String, FixBomDetailDTO> itemSeqFixBomDetailMap, FixBomDetailDTO fixBomDetail) {
        String itemSeq = fixBomDetail.getItemSeq();
        String fixBomRequired = fixBomDetail.getFixBomRequired();
        String boxBomRequired = fixBomDetail.getBoxBomRequired();
        this.wrapParentFixOrBoxBomRequired(itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);
    }

    private void wrapParentFixOrBoxBomRequired(Map<String, FixBomDetailDTO> itemSeqFixBomDetailMap,
                                               String itemSeq, String fixBomRequired, String boxBomRequired) {
        // 如果是顶层则直接退出
        if (!itemSeq.contains(Constant.LINE)) {
            return;
        }
        String parentItemSeq = itemSeq.substring(0, itemSeq.lastIndexOf(Constant.LINE));
        FixBomDetailDTO parentFixBomDetail = itemSeqFixBomDetailMap.get(parentItemSeq);
        if (parentFixBomDetail == null) {
            // 父级不存在 直接退出
            return;
        }

        String parentItemType = parentFixBomDetail.getItemType();
        if (Constant.COMPONENTS_OF_THE_MANUFACTURER.equals(parentItemType)
                || Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(parentItemType)) {
            if (!Constant.FLAG_Y.equals(parentFixBomDetail.getFixBomRequired())) {
                parentFixBomDetail.setFixBomRequired(fixBomRequired);
            }
            if (!Constant.FLAG_Y.equals(parentFixBomDetail.getBoxBomRequired())) {
                parentFixBomDetail.setBoxBomRequired(boxBomRequired);
            }
        }

        this.wrapParentFixOrBoxBomRequired(itemSeqFixBomDetailMap, parentItemSeq, fixBomRequired, boxBomRequired);
    }

    /**
     * 重新遍历物料明细 确认启用的物料 设置删除标识
     *
     * @param mbomResDTO 树
     */
    private void refreshDeleteFlag(MbomResDTO mbomResDTO) {
        List<FixBomDetailDTO> mbomItemList = mbomResDTO.getMbomItemList();
        Map<String, List<FixBomDetailDTO>> bomGroup =
                mbomItemList.stream().collect(Collectors.groupingBy(FixBomDetailDTO::getItemLevel));
        List<String> levelList = new ArrayList<>(bomGroup.keySet());
        levelList.sort(Comparator.comparing(Integer::parseInt));

        List<String> enableList = new LinkedList<>();
        for (int i = levelList.size() - 1; i >= 0; i--) {
            String index = String.valueOf(i);
            List<FixBomDetailDTO> orDefault = bomGroup.getOrDefault(index, new ArrayList<>());
            orDefault.forEach(item -> {
                // 恢复不删除
                if (enableList.contains(item.getItemSeq())) {
                    item.setDeleteFlag(Constant.FLAG_N);
                }
            });
            // 清除上层记录
            enableList.clear();
            // 同层子节点 分组
            Map<String, List<FixBomDetailDTO>> floorGroup = orDefault.stream()
                    .collect(Collectors.groupingBy(item -> {
                        // O 层不带-
                        String itemSeq = item.getItemSeq();
                        return itemSeq.contains(Constant.LINE) ? item.getItemSeq().substring(0,
                                item.getItemSeq().lastIndexOf(Constant.LINE)) : item.getItemSeq();
                    }));
            for (Map.Entry<String, List<FixBomDetailDTO>> entry : floorGroup.entrySet()) {
                FixBomDetailDTO fixBom = entry.getValue().stream()
                        .filter(item -> Constant.FLAG_N.equals(item.getDeleteFlag()))
                        .findAny().orElse(null);
                if (Objects.nonNull(fixBom)) {
                    enableList.add(entry.getKey());
                }
            }
        }
    }

    /**
     * 保存数据
     *
     * @param taskExtendedDTO 扩展属性
     * @param mbomResDTO      MES物料清单数据
     */
    private void buildThenSaveToDataBase(PsTaskExtendedDTO taskExtendedDTO, MbomResDTO mbomResDTO) {
        FixBomHeadDTO mbomMain = null;
        List<FixBomDetailDTO> mbomItemList = null;
        // 处理成功的数据
        if (Objects.nonNull(mbomResDTO)) {
            mbomMain = mbomResDTO.getMbomMain();
            mbomMain.setId(idGenerator.snowFlakeIdStr());
            mbomMain.setFixBomId(taskExtendedDTO.getFixBomId());
            mbomMain.setTaskNo(taskExtendedDTO.getTaskNo());
            mbomMain.setCreationDate(new Date());
            mbomMain.setLastUpdateDate(new Date());
            taskExtendedDTO.setFixBomHeadId(mbomMain.getId());
            mbomItemList = mbomResDTO.getMbomItemList();
            for (FixBomDetailDTO item : mbomItemList) {
                item.setId(idGenerator.snowFlakeIdStr());
                item.setFixBomId(mbomMain.getFixBomId());
                item.setHeadId(mbomMain.getId());
                item.setLastUpdateDate(new Date());
                item.setCreationDate(new Date());
            }
        }
        // 数据库操作
        FixBomCommonServiceImpl bean = SpringContextUtil.getBean(FixBomCommonServiceImpl.class);
        if (bean != null) {
            bean.insertAndUpdate(mbomMain, mbomItemList, Collections.singletonList(taskExtendedDTO));
        }
    }

    private boolean isCustomerItemsFixBomRequired(CustomerItemsDTO customerItems, Map<String, PackingListConfigDTO> packMap) {
        PackingListConfigDTO packDefault = packMap.getOrDefault(customerItems.getCustomerComponentType(), new PackingListConfigDTO());
        // 看iMES FixBOM&箱单配置表.是否计价物料的值，如果=Y，则该代码Fixbom需要字段=Y
        return Constant.FLAG_Y.equals(packDefault.getIsPricedMaterial());
    }

    private boolean isCustomerItemsBoxBomRequired(CustomerItemsDTO customerItems, Map<String, PackingListConfigDTO> packMap) {
        PackingListConfigDTO packDefault = packMap.getOrDefault(customerItems.getCustomerComponentType(), new PackingListConfigDTO());
        // 如果客户部件类型对应的箱单是否上传=Y，则该代码的箱单BOM需要=Y，箱单优先级=iMES FixBOM&箱单配置表.箱单优先级字段
        return Constant.FLAG_Y.equals(packDefault.getRequireMaterialUpload());
    }

    /**
     * 设置客户代码属性
     *
     * @param fixBomDetailDTO fixbom 明细信息
     * @param dto             客户物料代码维护
     * @param packMap         fixBom箱单信息维护
     * @param pdmItemMap PDM 清单
     */
    private void setCustomerProperties(FixBomDetailDTO fixBomDetailDTO, CustomerItemsDTO dto,
                                       Map<String, PackingListConfigDTO> packMap, Map<String, GbomDetailDTO> pdmItemMap) {
        fixBomDetailDTO.setZteCodeName(dto.getZteCodeName());
        fixBomDetailDTO.setCustomerComponentType(dto.getCustomerComponentType());
        if (this.isCustomerItemsFixBomRequired(dto, packMap)) {
            fixBomDetailDTO.setFixBomRequired(Constant.FLAG_Y);
        }
        if (this.isCustomerItemsBoxBomRequired(dto, packMap)) {
            fixBomDetailDTO.setBoxBomRequired(Constant.FLAG_Y);
        }
        // 看iMES FixBOM&箱单配置表.是否计价物料的值，如果=Y，则该代码Fixbom需要字段=Y
        PackingListConfigDTO packDefault = packMap.getOrDefault(dto.getCustomerComponentType(), new PackingListConfigDTO());
        // 箱单优先级字段
        fixBomDetailDTO.setBoxPriority(packDefault.getPriority());
        // 是否计价物件
        fixBomDetailDTO.setIsPricedMaterial(packDefault.getIsPricedMaterial());
        // 箱单按SN上传
        fixBomDetailDTO.setUploadBySn(packDefault.getUploadBySn());
        // 是否上传物料
        fixBomDetailDTO.setRequireMaterialUpload(packDefault.getRequireMaterialUpload());
    }

    /**
     * 根据料单查询 任务三段码 和 fixBomId 信息
     *
     * @param taskExtendedDTO 任务扩展信息
     */
    private void queryAndSetFixBomCode(PsTaskExtendedDTO taskExtendedDTO) {
        CpqdQueryDTO queryDTO = new CpqdQueryDTO();
        queryDTO.setInstanceNo(Collections.singletonList(taskExtendedDTO.getItemNo()));
        List<CpqdGbomDTO> cbomList = cpqdRemoteService.queryGbomList(queryDTO);
        CpqdGbomDTO cpqdGbomDTO = cbomList.stream()
                .filter(item -> StringUtils.equals(taskExtendedDTO.getItemNo(), item.getInstanceNo()))
                .findFirst()
                .orElseThrow(() -> new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.CPQD_INSTANCE_NO_LOST, new Object[]{taskExtendedDTO.getItemNo()}));
        // id
        taskExtendedDTO.setFixBomId(cpqdGbomDTO.getCbomCode());
        // 客户物料名称（三段码）
        taskExtendedDTO.setCustomerItemName(cpqdGbomDTO.getCustomerItemName());
    }

    /* Ended by AICoder, pid:0ee7cce6ae9f764149640936e059671eb3591c32 */

    /**
     * 查询客户协同iCC mbom 明细信息
     *
     * @param taskExtendedDTO 扩展信息
     * @return bom信息
     */
    private Map<String, MbomResDTO> queryIccBom(PsTaskExtendedDTO taskExtendedDTO) {
        // 将得到客户物料名称（三段码），然后将客户物料名称截取最后一个”.“的前面部分，得到二段码。
        String customerItemName = taskExtendedDTO.getCustomerItemName();
        String customerSub = customerItemName.substring(0, customerItemName.lastIndexOf("."));
        // 根据二段码+客户编码 调用ICC接口，输入二段码，得到二段码的MBOM
        MbomQueryDTO param = new MbomQueryDTO();
        param.setCustNo(taskExtendedDTO.getCustomerNo());
//        customerSub = "F91AT1C49.N2.C0V1P4U2";
        param.setProductNameList(Lists.newArrayList(customerItemName, customerSub));
        List<MbomResDTO> mbomDetail = iccRemoteService.queryMbomDetail(param);
        if (CollectionUtils.isEmpty(mbomDetail)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ICC_MBOM_LOST,
                    new Object[]{taskExtendedDTO.getCustomerNo(), customerSub});
        }
        return mbomDetail.stream()
                .collect(Collectors.toMap(item -> item.getMbomMain().getProductName(), v -> v, (k1, k2) -> k1));
    }

    /* Ended by AICoder, pid:0ee7cce6ae9f764149640936e059671eb3591c32 */

    private MbomResDTO getMbmoFromMapByProductName(Map<String, MbomResDTO> productNameMbomResMap, String customerNo, String productName) {
        MbomResDTO mbomResDTO = productNameMbomResMap.get(productName);
        if (mbomResDTO == null || CollectionUtils.isEmpty(mbomResDTO.getMbomItemList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ICC_MBOM_LOST,
                    new Object[]{customerNo, productName});
        }
        return mbomResDTO;
    }

    /* Started by AICoder, pid:bf510id1c9h727114d0b0b5da0480a418b573fb0 */

    /**
     * @param fixBomHeadDTO fixHead
     * @param list          明细
     * @param extendList    扩展表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAndUpdate(FixBomHeadDTO fixBomHeadDTO, List<FixBomDetailDTO> list, List<PsTaskExtendedDTO> extendList) {
        // 新增头
        if (Objects.nonNull(fixBomHeadDTO)) {
            fixBomHeadRepository.insertBatch(Collections.singletonList(fixBomHeadDTO));
        }
        if (CollectionUtils.isNotEmpty(list)) {
            // 新增明细
            List<List<FixBomDetailDTO>> splitList = CommonUtils.splitList(list, Constant.INT_50);
            splitList.forEach(fixBomDetailRepository::insertBatch);
        }
        // 更新头状态
        psTaskExtendedRepository.updatePsExtendedBatch(extendList);
    }

    /**
     * 批量删除FixBom头和明细数据
     *
     * @param idList 头id List
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFixBomHeadAndDetailById(List<String> idList) {
        String empNo = RequestHeadValidationUtil.validaEmpno();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        fixBomHeadRepository.deleteByIdList(idList, empNo);
        fixBomDetailRepository.deleteByHeadIdList(idList, empNo);
    }
    /* Ended by AICoder, pid:bf510id1c9h727114d0b0b5da0480a418b573fb0 */


    /**
     * 通过fixBomId 查询fixBom明细信息
     *
     * @param fixBomId fixBomId
     * @return 明细
     */
    @Override
    public List<FixBomDetailDTO> queryFixBomDetailByFixBomId(String fixBomId) {
        if (StringUtils.isBlank(fixBomId)) {
            return new ArrayList<>();
        }
        return fixBomDetailRepository.selectIdByFixBomId(fixBomId);
    }

    /**
     * 根据headId查询fixBomDetail
     *
     * @param headId
     * @return
     */
    @Override
    public List<FixBomDetailDTO> queryFixBomDetailByHeadId(String headId) {
        if (StringUtils.isBlank(headId)) {
            return new ArrayList<>();
        }
        return fixBomDetailRepository.selectIdByHeadId(headId);
    }
    /* Started by AICoder, pid:s6f26f7ea9c3378142040bfa606c534a5e55d41f */
    /**
     * 获取树形结构
     *
     * @param fixBomId fixbomId
     * @return 树状结构
     */
    @Override
    public List<FixBomDetailDTO> queryTreeNodeByFixBomId(String fixBomId) {
        List<FixBomDetailDTO> list = fixBomDetailRepository.selectIdByFixBomId(fixBomId);
        return getTreeByFixBomDetail(list);
    }
    /**
     * 根据headId查询FixBomDetail树形结构
     *
     * @param headId
     * @return
     */
    @Override
    public List<FixBomDetailDTO> queryTreeNodeByHeadId(String headId) {
        List<FixBomDetailDTO> list = fixBomDetailRepository.selectIdByHeadId(headId);
        return getTreeByFixBomDetail(list);
    }

    /**
     * 根据fixBomDetail生成树
     * @param fixBomDetailList
     * @return
     */
    @Override
    public List<FixBomDetailDTO> getTreeByFixBomDetail(List<FixBomDetailDTO> fixBomDetailList) {
        if (CollectionUtils.isEmpty(fixBomDetailList)) {
            return Collections.emptyList();
        }
        Map<String, List<FixBomDetailDTO>> groupMap = fixBomDetailList.stream()
                .collect(Collectors.groupingBy(FixBomDetailDTO::getItemLevel));
        List<String> levelList = new ArrayList<>(groupMap.keySet());
        levelList.sort(Comparator.comparing(Integer::parseInt));

        List<FixBomDetailDTO> node = new ArrayList<>();
        for (int i = 0; i < levelList.size(); i++) {
            if (i == 0) {
                node.addAll(groupMap.get(levelList.get(i)));
                childNode(node, groupMap.get(levelList.get(i + 1)));
                continue;
            }
            if (i == levelList.size() - 1) {
                break;
            }
            childNode(groupMap.get(levelList.get(i)), groupMap.get(levelList.get(i + 1)));
        }
        return node;
    }

    private void childNode(List<FixBomDetailDTO> floor, List<FixBomDetailDTO> second) {
        Map<String, List<FixBomDetailDTO>> childGroup = second.stream().collect(Collectors.groupingBy(item -> {
            String itemSeq = item.getItemSeq();
            return itemSeq.substring(0, itemSeq.lastIndexOf(Constant.LINE));
        }));
        for (FixBomDetailDTO fixBomDetailDTO : floor) {
            List<FixBomDetailDTO> orDefault = childGroup.getOrDefault(fixBomDetailDTO.getItemSeq(), new ArrayList<>());
            orDefault.sort((o1, o2) -> {
                String[] strings1 = o1.getItemSeq().split(Constant.LINE);
                String[] strings2 = o2.getItemSeq().split(Constant.LINE);
                Integer integer1 = new Integer(strings1[strings1.length - NumConstant.NUM_ONE]);
                Integer integer2 = new Integer(strings2[strings2.length - NumConstant.NUM_ONE]);
                return integer1.compareTo(integer2);
            });
            orDefault.forEach(p->p.setParentId(fixBomDetailDTO.getId()));
            fixBomDetailDTO.setChildNodes(orDefault);
        }
    }
    /* Ended by AICoder, pid:s6f26f7ea9c3378142040bfa606c534a5e55d41f */

    /**
     * 根据fixbom树调整物料用量
     * @param node fixBom树
     * @param multiple 当前倍数
     */
    @Override
    public void adjustItemNumber(List<FixBomDetailDTO> node, int multiple) {
        if (CollectionUtils.isEmpty(node)) {
            return;
        }
        for (FixBomDetailDTO fixBomDetail : node) {
            int nextMultiple = multiple;
            if (specificItemTypeList.contains(fixBomDetail.getItemType())) {
                // 遇到成品料后倍率归一,且成品料不调整用量
                multiple = NumConstant.NUM_ONE;
            } else {
                int qty = Integer.parseInt(StringUtils.defaultString(fixBomDetail.getItemNumber(), NumConstant.STRING_ONE));
                fixBomDetail.setItemNumber((multiple * qty) + "");
                nextMultiple = qty * multiple;
            }
            // 递归调整
            adjustItemNumber(fixBomDetail.getChildNodes(), nextMultiple);
        }
    }

    @Override
    public List<FixBomDetailDTO> getFixBomByTaskNo(String taskNo) {
        if (StringUtils.isEmpty(taskNo)) {
            return new ArrayList<>();
        }
        return fixBomDetailRepository.getFixBomByTaskNo(taskNo);
    }

    @Override
    public List<FixBomDetailDTO> adjustItemNumberByTaskNo(String taskNo) {
        if (StringUtils.isEmpty(taskNo)) {
            return new ArrayList<>();
        }
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.queryByTaskNos(Lists.newArrayList(taskNo));
        if (CollectionUtils.isEmpty(psTaskExtendedDTOList)) {
            return new ArrayList<>();
        }
        List<FixBomDetailDTO> fixBomDetailDTOList = this.getTreeByFixBomDetail(fixBomDetailRepository.selectIdByFixBomId(psTaskExtendedDTOList.get(NumConstant.NUM_ZERO).getFixBomId()));
        this.adjustItemNumber(fixBomDetailDTOList,NumConstant.NUM_ONE);
        return fixBomDetailDTOList;
    }

    /**
     *
     * @param taskNo   任务编号，可以为空。
     * @param fixBomId 修复BOM ID，可以为空。
     * @return 修复BOM详情列表，可能为null。
     * @throws MesBusinessException 如果taskNo和fixBomId都为空时抛出的业务异常。
     */
    @Override
    public List<FixBomDetailDTO> queryFixBomByTaskNoOrFixBomId(String taskNo, String fixBomId) {
        if (StrUtil.isAllBlank(taskNo, fixBomId)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REQUIRED_TASK_NO_OR_FIX_BOM_ID);
        }
        if (StrUtil.isNotBlank(taskNo)) {
            PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedService.queryExtendedByTaskNoAndFixBomId(taskNo, fixBomId);
            if (null == psTaskExtendedDTO) {
                return null;
            }
            fixBomId = psTaskExtendedDTO.getFixBomId();
        }
        List<FixBomDetailDTO> fixBomDetailDTOS = queryTreeNodeByFixBomId(fixBomId);
        if(fixBomDetailDTOS.isEmpty()){
            return null;
        }
        FixBomDetailDTO root = fixBomDetailDTOS.get(NumConstant.NUM_ZERO);
        reattachChildNodes(root);
        reattachFinishedAndL6Tree(root);
        reCodeChild(root);
        return fixBomDetailDTOS;
    }

    /**
     * 根据任务号或者headId查询fixBom
     *
     * @param taskNo
     * @param headId
     * @return
     */
    @Override
    public List<FixBomDetailDTO> queryFixBomByTaskNoOrHeadId(String taskNo, String headId) {
        if (StrUtil.isAllBlank(taskNo, headId)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.REQUIRED_TASK_NO_OR_FIX_BOM_ID);
        }
        if (StrUtil.isNotBlank(taskNo)) {
            PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedService.queryExtendedByTaskNoAndFixBomHeadId(taskNo, headId);
            if (null == psTaskExtendedDTO) {
                return new ArrayList<>();
            }
            headId = psTaskExtendedDTO.getFixBomHeadId();
        }
        List<FixBomDetailDTO> fixBomDetailList = queryTreeNodeByHeadId(headId);
        if (fixBomDetailList.isEmpty()) {
            return new ArrayList<>();
        }
        FixBomDetailDTO root = fixBomDetailList.get(NumConstant.NUM_ZERO);
        reattachChildNodes(root);
        reattachFinishedAndL6Tree(root);
        reCodeChild(root);
        return fixBomDetailList;
    }

    /**
     * 对树重新排序
     * @param root 根节点
     */
    private void reCodeChild(FixBomDetailDTO root) {
        if (null == root.getChildNodes()) {
            return;
        }
        Integer currentLevel = new Integer(root.getItemLevel()) + NumConstant.NUM_ONE;
        List<FixBomDetailDTO> childNodes = root.getChildNodes();
        for (int i = 0; i < childNodes.size(); i++) {
            FixBomDetailDTO childNode = childNodes.get(i);
            childNode.setItemLevel(String.valueOf(currentLevel));
            childNode.setOriginalItemSeq(childNode.getItemSeq());
            childNode.setItemSeq(root.getItemSeq() + Constant.LINE + (i + NumConstant.NUM_ONE));
            reCodeChild(childNode);
        }
    }


    public static void reattachChildNodes(FixBomDetailDTO root) {
        if (null == root.getChildNodes()) {
            return;
        }

        List<FixBomDetailDTO> nodesToReattach = new ArrayList<>();
        List<FixBomDetailDTO> nodesToRemove = new ArrayList<>();
        for (FixBomDetailDTO child : root.getChildNodes()) {
            if (Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(child.getItemType()) && Constant.ITEM_TYPE_L6_PKG.equals(child.getItemMaterialType())) {
                continue;
            }
            if ("Y".equals(child.getDeleteFlag())) {
                // 将子节点的子节点提升为当前节点的子节点
                if (child.getChildNodes() != null) {
                    nodesToReattach.addAll(child.getChildNodes().stream().filter(i-> "N".equals(i.getDeleteFlag())).collect(Collectors.toList()));
                }
                nodesToRemove.add(child);
            }
            reattachChildNodes(child);
        }
        root.getChildNodes().addAll(nodesToReattach);
        root.getChildNodes().removeAll(nodesToRemove);
    }

    public static void reattachFinishedAndL6Tree(FixBomDetailDTO root) {
        if (null == root.getChildNodes()) {
            return;
        }
        List<FixBomDetailDTO> childNodes = root.getChildNodes();
        List<FixBomDetailDTO> nodesToRemove = new ArrayList<>();
        int size = childNodes.size();
        for (int i = 0; i < size; i++) {
            FixBomDetailDTO childNode = childNodes.get(i);
            if (Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(childNode.getItemType()) && Constant.ITEM_TYPE_L6_PKG.equals(childNode.getItemMaterialType())) {
                processFinishedAndL6Node(root, childNode);
                nodesToRemove.add(childNode);
            } else {
                reattachFinishedAndL6Tree(childNode);
            }
        }
        root.getChildNodes().removeAll(nodesToRemove);
    }

    private static void processFinishedAndL6Node(FixBomDetailDTO root, FixBomDetailDTO currentNode) {
        if (null == root.getChildNodes() || null == currentNode.getChildNodes()) {
            return;
        }
        for (FixBomDetailDTO childNode : currentNode.getChildNodes()) {
            if ("N".equals(childNode.getDeleteFlag())) {
                reattachChildNodes(childNode);
                root.getChildNodes().add(childNode);
            }
        }
    }
}
