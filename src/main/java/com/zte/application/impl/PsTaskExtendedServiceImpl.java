package com.zte.application.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.zte.application.PsTaskExtendedService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsTaskExtendedRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskExtendedDTO;
import com.zte.interfaces.dto.TaskNoFromWmesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PsTaskExtendedServiceImpl implements PsTaskExtendedService {
    @Resource
    private PsTaskExtendedRepository psTaskExtendedRepository;
    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private SysLookupValuesServiceImpl lookupValuesService;

    @Value("${task.change.config.type:REWORK_2}")
    private String changeConfigType;

    /**
     * 批量通过任务号查询任务信息
     * @param taskNos 任务号
     * @return key 为任务号，val为查询详情
     */
    @Override
    public  List<TaskExtendedDTO> bulkQueriesByTaskNos(List<String> taskNos) {
        List<TaskExtendedDTO> psTaskExtendeds = psTaskExtendedRepository.bulkQueriesByTaskNos(taskNos);
        return Optional.of(psTaskExtendeds).orElse(Collections.emptyList());
    }

    @Override
    public List<PsTaskExtendedDTO> filterListByCustomerNo(List<String> taskNoList, List<String> customerNoList) {
        if (CollectionUtils.isEmpty(taskNoList) || CollectionUtils.isEmpty(customerNoList)) {
            return new ArrayList<>();
        }
        return psTaskExtendedRepository.filterListByCustomerNo(taskNoList, customerNoList);
    }

    @Override
    public List<PsTaskExtendedDTO> queryByTaskNos(List<String> taskNoList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return new ArrayList<>();
        }
        return psTaskExtendedRepository.queryByTaskNos(taskNoList);
    }

    @Override
    public boolean getReconfigurationFlag (String taskNo) {
        return psTaskExtendedRepository.getReconfigurationOrSpecificFlag(taskNo, changeConfigType) != null;
    }

    @Override
    public void updateCustomPartType(String customPartType, String taskNo) {
        psTaskExtendedRepository.updateCustomPartType(customPartType, taskNo);
    }

    @Override
    public List<PsTaskExtendedDTO> getSpecificTaskExtended (PsTaskExtendedDTO dto) {
        List<SysLookupValues> lookupValues = lookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115);
        List<String> specificCustomerNoList = lookupValues.stream().map(SysLookupValues::getLookupMeaning)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specificCustomerNoList)) {
            return new ArrayList<>();
        }
        return psTaskExtendedRepository.getSpecificTaskExtended(dto.getTaskNo(), dto.getTaskNoList(), specificCustomerNoList);
    }


    @Override
    public void batchSave(List<PsTaskExtendedDTO> psTaskExtendedDTOList) {
        psTaskExtendedDTOList.forEach(item -> item.setId(idGenerator.snowFlakeIdStr()));
        psTaskExtendedRepository.batchSave(psTaskExtendedDTOList);
    }

    @Override
    public PsTaskExtendedDTO queryExtendedByTaskNo(String taskNo) {
        return Opt.ofBlankAble(taskNo).map(psTaskExtendedRepository::queryExtendedByTaskNo).orElse(null);
    }

    @Override
    public List<PsTaskExtendedDTO> listByTaskNos(Collection<String> taskNos) {
        return Opt.ofEmptyAble(taskNos).map(psTaskExtendedRepository::listByTaskNos).orElse(Collections.emptyList());
    }

    /* Started by AICoder, pid:61b886aca4q313c142b90a70c08d7e2ec7153d59 */
    @Override
    public List<String> filterSpecificTaskNo(List<String> taskNoList) {
        if (CollectionUtils.isEmpty(taskNoList)) {
            return new ArrayList<>();
        }

        List<SysLookupValues> lookupValues = lookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115);
        List<String> specificCustomerNoList = lookupValues.stream().filter(curr -> StringUtils.isNotBlank(curr.getLookupMeaning()))
                .map(SysLookupValues::getLookupMeaning).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specificCustomerNoList)) {
            return new ArrayList<>();
        }

        return psTaskExtendedRepository.filterSpecificTaskNo(taskNoList, specificCustomerNoList);
    }

    @Override
    public void batchUpdateByTaskNo(Collection<PsTaskExtendedDTO> psTaskExtendedDTOS) {
        CollUtil.split(psTaskExtendedDTOS, Constant.BATCH_SIZE).forEach(psTaskExtendedRepository::batchUpdateByTaskNo);
    }

    @Override
    public PsTaskExtendedDTO queryExtendedByTaskNoAndFixBomId(String taskNo, String fixBomId) {
        return psTaskExtendedRepository.queryExtendedByTaskNoAndFixBomId(taskNo,fixBomId);
    }
    /* Ended by AICoder, pid:61b886aca4q313c142b90a70c08d7e2ec7153d59 */

    /* Started by AICoder, pid:j828flc1b92b78b1410d0b5a20dbf23013e18ce5 */
    /**
     * 获取阿里标模任务号
     * @param dto
     * @return
     */
    @Override
    public List<String> getTaskNoFromWmes (TaskNoFromWmesDTO dto)throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("lookupType", Constant.LOOKUP_VALUE_TASK_TYPE);
        List<SysLookupTypesDTO> lookUpCodeList=  BasicsettingRemoteService.getSysLookUpValue(map);
        if(CollectionUtils.isEmpty(lookUpCodeList) || StringUtils.isEmpty(dto.getCustomerNo()) || CollectionUtils.isEmpty(dto.getTaskNoList()))  {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_CODE, com.zte.itp.msa.core.model.RetCode.VALIDATIONERROR_MSGID);
        }
        List<String> entityClassList= lookUpCodeList.stream().map(SysLookupTypesDTO::getLookupMeaning).collect(Collectors.toList());
        return psTaskExtendedRepository.getTaskNoFromWmes(dto.getCustomerNo(), dto.getTaskNoList(), entityClassList);
    }

    /* Ended by AICoder, pid:j828flc1b92b78b1410d0b5a20dbf23013e18ce5 */

    /**
     * 根据任务号或者headid查询任务扩展信息
     *
     * @param taskNo
     * @param fixBomHeadId
     * @return
     */
    @Override
    public PsTaskExtendedDTO queryExtendedByTaskNoAndFixBomHeadId(String taskNo, String fixBomHeadId) {
        return psTaskExtendedRepository.queryExtendedByTaskNoAndFixBomHeadId(taskNo, fixBomHeadId);
    }
}
