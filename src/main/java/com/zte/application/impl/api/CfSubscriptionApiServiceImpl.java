package com.zte.application.impl.api;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zte.application.api.CfSubscriptionApiService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.api.CfSubscriptionApiMapperRepository;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.api.CfSubscriptionApiDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/* Started by AICoder, pid:18bd9a05ca10432ba44f1270b81103f0 */
@Service
@Slf4j
public class CfSubscriptionApiServiceImpl extends AbstractExportTaskHandler<CfSubscriptionApiDTO, CfSubscriptionApiDTO> implements CfSubscriptionApiService {

    @Autowired
    private CfSubscriptionApiMapperRepository cfSubscriptionApiMapper;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private HrmUserInfoService hrmUserInfoService;
    @Value("${cf.api.user.permissions.list:}")
    private List<String> cfApiUserPermissionsList;

    @Value("${check.api.resource:true}")
    private Boolean checkApiResource;

    @Override
    public void add(CfSubscriptionApiDTO apiBO) {
        // 检验用户操作权限
        this.checkUserPermissions();
        // 校验API 资源
        this.checkSysResource(Collections.singletonList(apiBO));
        String empNo = RequestHeadValidationUtil.validaEmpno();
        apiBO.setCreateBy(empNo);
        apiBO.setLastUpdatedBy(empNo);
        apiBO.setId(idGenerator.snowFlakeIdStr());
        cfSubscriptionApiMapper.insert(apiBO);
    }

    // ...其他接口方法实现

    private void checkSysResource(List<CfSubscriptionApiDTO> addList) {
        addList.stream().filter(item -> Constant.IMES.equalsIgnoreCase(item.getSystemCode()))
                .filter(item -> StringUtils.isBlank(item.getResources())
                        && Boolean.TRUE.equals(checkApiResource))
                .findAny().ifPresent(item -> {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CF_API_RESOURCE_EMPTY);
                });
    }

    /**
     * 根据主键id 更新数据
     *
     * @param cfSubscriptionApiDTO 参数
     */
    @Override
    public void updateById(CfSubscriptionApiDTO cfSubscriptionApiDTO) {
        // 检验用户操作权限
        this.checkUserPermissions();
        String empNo = RequestHeadValidationUtil.validaEmpno();
        cfSubscriptionApiDTO.setLastUpdatedBy(empNo);
        this.checkSysResource(Collections.singletonList(cfSubscriptionApiDTO));
        cfSubscriptionApiMapper.updateById(cfSubscriptionApiDTO);
    }

    /**
     * 批量新增api 數據
     *
     * @param cfSubscriptionApiDTOList api 數據
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<CfSubscriptionApiDTO> cfSubscriptionApiDTOList) {
        // 检验用户操作权限
        this.checkUserPermissions();
        this.checkSysResource(cfSubscriptionApiDTOList);
        String empNo = RequestHeadValidationUtil.validaEmpno();
        List<List<CfSubscriptionApiDTO>> splitList = CommonUtils.splitList(cfSubscriptionApiDTOList, Constant.INT_100);
        for (List<CfSubscriptionApiDTO> list : splitList) {
            for (CfSubscriptionApiDTO item : list) {
                item.setId(idGenerator.snowFlakeIdStr());
                item.setCreateBy(empNo);
                item.setLastUpdatedBy(empNo);
            }
            cfSubscriptionApiMapper.batchAdd(list);
        }
    }

    /**
     * 根據api id 批量失效数据
     *
     * @param idList id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteById(List<String> idList) {
        // 检验用户操作权限
        this.checkUserPermissions();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        cfSubscriptionApiMapper.batchDeleteById(idList, RequestHeadValidationUtil.validaEmpno());
    }

    /**
     * 分页查询API订阅数据
     *
     * @param params 请求参数
     * @return 数据
     */
    @Override
    public Page<CfSubscriptionApiDTO> queryByPage(Page<CfSubscriptionApiDTO> params) {
        List<CfSubscriptionApiDTO> list = cfSubscriptionApiMapper.queryByPage(params);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> userIdList = new LinkedList<>();
            for (CfSubscriptionApiDTO cfSubscriptionApiDTO : list) {
                userIdList.add(cfSubscriptionApiDTO.getCreateBy());
                userIdList.add(cfSubscriptionApiDTO.getLastUpdatedBy());
                cfSubscriptionApiDTO.setMatchTypeDesc(Constant.ACCURATE_DESC);
                if (Constant.STRING_ONE.equalsIgnoreCase(cfSubscriptionApiDTO.getMatchType())) {
                    cfSubscriptionApiDTO.setMatchTypeDesc(Constant.REGEX_DESC);
                }
            }
            userIdList = userIdList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<List<String>> splitList = CommonUtils.splitList(userIdList, Constant.BATCH_SIZE_500);
            List<BsPubHrvOrgId> resultList = new LinkedList<>();
            for (List<String> items : splitList) {
                try {
                    List<BsPubHrvOrgId> bsPubHrvOrgIdInfo = hrmUserInfoService.getBsPubHrvOrgIdInfo(items);
                    if (CollectionUtils.isNotEmpty(bsPubHrvOrgIdInfo)) {
                        resultList.addAll(bsPubHrvOrgIdInfo);
                    }
                } catch (Exception e) {
                    log.error("getBsPubHrvOrgIdInfo", e);
                }
            }
            this.coverUserName(resultList, list);
        }
        params.setRows(list);
        return params;
    }

    private void coverUserName(List<BsPubHrvOrgId> resultList, List<CfSubscriptionApiDTO> list) {
        Map<String, BsPubHrvOrgId> userMap = resultList.stream().collect(Collectors.toMap(BsPubHrvOrgId::getUserId, value -> value));
        for (CfSubscriptionApiDTO item : list) {
            BsPubHrvOrgId bsPubHrvOrgId = userMap.get(item.getCreateBy());
            if (Objects.nonNull(bsPubHrvOrgId)) {
                item.setCreateBy(bsPubHrvOrgId.getUserName() + bsPubHrvOrgId.getUserId());
            }
            bsPubHrvOrgId = userMap.get(item.getLastUpdatedBy());
            if (Objects.nonNull(bsPubHrvOrgId)) {
                item.setLastUpdatedBy(bsPubHrvOrgId.getUserName() + bsPubHrvOrgId.getUserId());
            }
        }
    }

    @Override
    public Integer countExportTotal(CfSubscriptionApiDTO cfSubscriptionApiDTO) {
        Page<CfSubscriptionApiDTO> page = new Page<>(Constant.INT_1, Constant.INT_1);
        page.setParams(cfSubscriptionApiDTO);
        cfSubscriptionApiMapper.queryByPage(page);
        return page.getTotal();
    }

    @Override
    public List<CfSubscriptionApiDTO> queryExportData(CfSubscriptionApiDTO cfSubscriptionApiDTO, int i, int i1) {
        Page<CfSubscriptionApiDTO> queryPage = new Page<>(i, i1);
        queryPage.setParams(cfSubscriptionApiDTO);
        queryPage.setSearchCount(false);
        return cfSubscriptionApiMapper.queryByPage(queryPage);
    }

    /**
     * 解析数据
     *
     * @param file 文件
     * @return 解析数据
     */
    @Override
    public List<CfSubscriptionApiDTO> upLoadExcelApiData(MultipartFile file) throws IOException {
        // 校验用户权限
        this.checkUserPermissions();
        List<CfSubscriptionApiDTO> resultList = new LinkedList<>();
        EasyExcelFactory.read(file.getInputStream(), CfSubscriptionApiDTO.class, new AnalysisEventListener() {
            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                CfSubscriptionApiDTO o1 = (CfSubscriptionApiDTO) o;
                o1.setRowNo(resultList.size() + 1);
                resultList.add((CfSubscriptionApiDTO) o);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).doReadAll();
        return filterApiData(resultList);
    }

    /* Ended by AICoder, pid:3856dk0689gb40814b3d0bdfc08eda274c19a23b */
    private List<CfSubscriptionApiDTO> filterApiData(List<CfSubscriptionApiDTO> resultList) {
        List<CfSubscriptionApiDTO> failList = new LinkedList<>();
        try (ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            Validator validator = validatorFactory.getValidator();
            for (CfSubscriptionApiDTO item : resultList) {
                Set<ConstraintViolation<CfSubscriptionApiDTO>> validateList = validator.validate(item);
                if (CollectionUtils.isNotEmpty(validateList)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (ConstraintViolation<CfSubscriptionApiDTO> violation : validateList) {
                        stringBuilder.append(violation.getPropertyPath().toString()).append(Constant.SEMICOLON)
                                .append(violation.getMessage()).append(Constant.SPACE_STR);
                    }
                    item.setErrorMsg(stringBuilder.toString());
                    failList.add(item);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(failList)) {
            return failList;
        }
        return resultList;
    }
    /* Ended by AICoder, pid:3856dk0689gb40814b3d0bdfc08eda274c19a23b */

    /* Started by AICoder, pid:u7a46rfef3z7db114fca0b71b0280111b7768cbb */

    /**
     * 根据最后更新时间查询是否有数据变更
     *
     * @param params 参数
     * @return 受影响条数
     */
    @Override
    public Date selectMaxDateByCondition(CfSubscriptionApiDTO params) {
        return cfSubscriptionApiMapper.selectMaxDateByCondition(params);
    }
    /* Ended by AICoder, pid:u7a46rfef3z7db114fca0b71b0280111b7768cbb */


    /* Started by AICoder, pid:s6d8e63f1alec6a14b700a15407e7126b860093e */

    /**
     * Validates that the user has the necessary import permissions.
     * Throws an exception if the user does not have permission.
     */
    private void checkUserPermissions() {
        String empNo = RequestHeadValidationUtil.validaEmpno();
        if (!hasPermission(empNo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.USER_NO_PERMISSIONS);
        }
    }

    /**
     * Checks if the given employee number has the required permissions.
     *
     * @param empNo the employee number to check
     * @return true if the employee has permission, false otherwise
     */
    private boolean hasPermission(String empNo) {
        return cfApiUserPermissionsList.contains(empNo);
    }

    /* Ended by AICoder, pid:s6d8e63f1alec6a14b700a15407e7126b860093e */
}
// serviceImpl实现类
/* Ended by AICoder, pid:18bd9a05ca10432ba44f1270b81103f0 */