package com.zte.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.CustomerItemsService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtils;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.model.ResultData;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.common.utils.constant.RedisKeyConstant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.CustomerItemsParamsRepository;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SplitItemNoDTO;
import com.zte.interfaces.dto.SrmPageRows;
import com.zte.interfaces.dto.bytedance.CpuInfoDTO;
import com.zte.interfaces.dto.bytedance.CustomerImportDTO;
import com.zte.interfaces.dto.bytedance.GpuInfoDTO;
import com.zte.interfaces.dto.bytedance.HardDiskInfoDTO;
import com.zte.interfaces.dto.bytedance.MachineInfoDTO;
import com.zte.interfaces.dto.bytedance.MemoryInfoDTO;
import com.zte.interfaces.dto.bytedance.MotherboardInfoDTO;
import com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO;
import com.zte.interfaces.dto.bytedance.PowerSourceInfoDTO;
import com.zte.interfaces.dto.bytedance.RaidCardInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.Normalizer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_0;
import static com.zte.common.utils.Constant.INT_1000;
import static com.zte.itp.msa.core.exception.GlobalDefaultBaseExceptionHandler.getTrace;

/**
 * <AUTHOR>
 * @Date 2023/5/4 17:08
 */
@Log4j2
@Service
public class CustomerItemsServiceImpl implements CustomerItemsService {
    private static final Logger LOG = LoggerFactory.getLogger(CustomerItemsServiceImpl.class);
    private static final String ERROR_EXPORT_MODEL = "  exportModel  :";
    @Autowired
    private CustomerItemsRepository repository;
    @Autowired
    private SysLookupValuesRepository lookupValuesRepository;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private CustomerItemsParamsRepository customerItemsParamsRepository;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    private BsItemInfoRepository bsItemInfoRepository;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;

    /**
     * @return
     * <AUTHOR>
     * 新增一条客户物料信息
     * @Date 2023/5/6 16:44
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    @Override
    public int newCustomerItems(CustomerItemsDTO dto) {
        StringBuilder sb = new StringBuilder();
        // 校验入参,生成锁key
        String key = this.checkNewParams(dto, sb);
        RedisLock redisLock = new RedisLock(key);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE);
        }
        try {
            // 校验数据是否已存在
            //整机-主板类型5   主键客户名称 +ZTE代码＋类型
            Integer exist = repository.checkItemsExist(dto);
            if (!Objects.isNull(exist) && exist > 0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_EXIST, new Object[]{Constant.STR_ADD, sb});
            }
            // 新增一条记录
            dto.setId(idGenerator.snowFlakeIdStr());
            // 新增属性表信息
            this.insertParamsInfo(dto);
            return repository.newCustomerItems(dto);
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 新增一条客户物料信息
     *
     * @param customerItemsDTOList 客户物料信息
     * @return 更新成功条数
     */
    public int batchInsert(List<CustomerItemsDTO> customerItemsDTOList) {
        if (CollUtil.isEmpty(customerItemsDTOList)){
            return 0;
        }
        customerItemsDTOList.forEach(item -> {
            item.setId(idGenerator.snowFlakeIdStr());
            item.setStatus(Constant.FLAG_Y);
            item.setRemark(Constant.STRING_EMPTY);
        });
        repository.batchInsertCustomerItems(customerItemsDTOList);
        return customerItemsDTOList.size();
    }

    private void insertParamsInfo(CustomerItemsDTO dto) {
        if (Objects.isNull(dto.getParamsDetail())) {
            return;
        }
        // 新增参数信息 7306 表配置新增
        Map<String, String> paramsMap = this.queryLookUpDict();
        // 没有属性表不处理
        String tableName = paramsMap.get(dto.getCustomerComponentType());
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        String id = this.insertCustomerParams(dto, tableName);
        dto.setAdditionalInfoId(id);
    }

    private Map<String, String> queryLookUpDict() {
        List<SysLookupValues> sysLookupValues = lookupValuesRepository.selectValuesByType(Constant.LOOKUP_TYPE_7306.intValue());
        if (CollectionUtils.isEmpty(sysLookupValues)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_7306});
        }
        // attribute1 是表名
        Map<String, String> paramsMap = sysLookupValues.stream()
                .filter(item -> StringUtils.isNotBlank(item.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getAttribute1));
        return paramsMap;
    }

    /**
     * @return
     * <AUTHOR>
     * 校验提交参数
     * @Date 2023/5/4 17:10
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    public String checkNewParams(CustomerItemsDTO dto, StringBuilder sb) {
        if (Objects.isNull(dto)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_PARAMS_NULL);
        }
        this.checkProjType(dto);
        boolean chekFlag;
        String key = RedisKeyConstant.CUSTOMER_ITEMS_LOCK;
        String messageId;
        if (StringUtils.isEmpty(dto.getProjectType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_TYPE_NULL);
        } else if (StringUtils.equals(Constant.STRING_FOUR, dto.getProjectType())) {
            // 其他物料  24.7.17当类型为其他物料时，不再校验ZTE供应商、ZTE规格型号为必填
            chekFlag = StringUtils.isEmpty(dto.getCustomerName()) || StringUtils.isEmpty(dto.getZteCode());
            messageId = MessageId.CUSTOMER_PARAMS_NULL_FOUR;
            sb.append(Constant.CUSTOMER_NAME).append(Constant.DON).append(Constant.ZTE_CODE).append(Constant.DON);
            key += Constant.STRING_FOUR + dto.getCustomerName() + dto.getZteCode() + dto.getZteSupplier() + dto.getZteBrandStyle();
        } else if (StringUtils.equals(Constant.STR_THREE, dto.getProjectType())) {
            // 整机
            if (StringUtils.isEmpty(dto.getCustomerModel())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ZJ_CUSTOMER_MODEL_NULL);
            }
            chekFlag = StringUtils.isEmpty(dto.getCustomerName()) || StringUtils.isEmpty(dto.getProjectName()) || StringUtils.isEmpty(dto.getZteCode());
            messageId = MessageId.CUSTOMER_PARAMS_NULL_OTHER;
            sb.append(Constant.CUSTOMER_NAME).append(Constant.DON).append(Constant.ZTE_CODE);
            key += dto.getProjectType() + dto.getCustomerName() + dto.getZteCode();
        } else {
            chekFlag = StringUtils.isEmpty(dto.getZteCode());
            messageId = MessageId.CUSTOMER_PARAMS_NULL_OTHER;
            sb.append(Constant.ZTE_CODE);
            key += dto.getProjectType() + dto.getZteCode();
        }

        if (chekFlag) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, messageId, new Object[]{dto.getStrProjectType()});
        }
        return key;
    }

    /**
     * 校验项目类型
     *
     * @param dto
     */
    public void checkProjType(CustomerItemsDTO dto) {
        //项目类型字段，仅自研主板、自研背板、自研子卡必填
        if ((StringUtils.equals(Constant.STR_TWO, dto.getProjectType()) || StringUtils.equals(Constant.STR_ONE, dto.getProjectType()) || StringUtils.equals(Constant.STR_0, dto.getProjectType()))
                && StringUtils.isEmpty(dto.getProjType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, new Object[]{dto.getStrProjectType()});
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 分页获取客户物料信息
     * @Date 2023/5/4 19:20
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    @Override
    public Page<CustomerItemsDTO> pageCustomerItemsInfo(CustomerItemsDTO dto) {
        // 获取类型中文
        Map<String, String> typeMap = getTypeMap(Constant.LOOKUP_TYPE_7303);
        Map<String, String> projectTypeMap = getTypeMap(Constant.LOOKUP_TYPE_6817);
        Map<String, String> customerComponentTypeMap = getTypeMap(Constant.LOOKUP_TYPE_7306);
        Map<String, String> boardTypeMap = getTypeMap(Constant.LOOKUP_TYPE_7307);
        return this.pageInfo(dto, typeMap, customerComponentTypeMap, boardTypeMap, projectTypeMap);
    }

    private Page<CustomerItemsDTO> pageInfo(CustomerItemsDTO dto, Map<String, String> typeMap, Map<String, String> componentTypeMap,
                                            Map<String, String> boardTypeMap, Map<String, String> projectTypeMap) {
        this.checkQryParams(dto);
        Page<CustomerItemsDTO> page = new Page<>(dto.getPage(), dto.getRows());
        page.setSearchCount(dto.isSearchCount());
        page.setParams(dto);
        // 分页获取客户物料信息
        List<CustomerItemsDTO> dtoList = repository.pageCustomerItemsInfo(page);
        // 获取类型中文
        for (CustomerItemsDTO customerItemsDTO : dtoList) {
            String type = typeMap.get(customerItemsDTO.getProjectType()) == null ? customerItemsDTO.getProjectType() : typeMap.get(customerItemsDTO.getProjectType());
            String boardType = boardTypeMap.get(customerItemsDTO.getBoardType()) == null ? customerItemsDTO.getBoardType() : boardTypeMap.get(customerItemsDTO.getBoardType());
            String componentType = componentTypeMap.get(customerItemsDTO.getCustomerComponentType()) == null ? customerItemsDTO.getCustomerComponentType() : componentTypeMap.get(customerItemsDTO.getCustomerComponentType());
            customerItemsDTO.setStrProjectType(type);
            customerItemsDTO.setStrBoardType(boardType);
            customerItemsDTO.setStrCustomerComponentType(componentType);
            String projectType = projectTypeMap.get(customerItemsDTO.getProjType()) == null ? customerItemsDTO.getProjType() : projectTypeMap.get(customerItemsDTO.getProjType());
            customerItemsDTO.setStrProjType(projectType);
            if (StringUtils.equals(Constant.FLAG_Y, customerItemsDTO.getStatus())) {
                customerItemsDTO.setStrStatus(Constant.NO);
            } else {
                customerItemsDTO.setStrStatus(Constant.YES);
            }
        }
        // 获取扩展属性
        this.queryAndSetProperties(dtoList);
        // 获取人员信息
        this.tranceHrmInfo(dtoList);
        page.setRows(dtoList);
        return page;
    }

    private void tranceHrmInfo(List<CustomerItemsDTO> dtoList) {
        List<String> userIdList = new LinkedList<>();
        for (CustomerItemsDTO item : dtoList) {
            if (StringUtils.isNotBlank(item.getCreateBy())) {
                userIdList.add(item.getCreateBy());
            }
            if (StringUtils.isNotBlank(item.getLastUpdatedBy())) {
                userIdList.add(item.getLastUpdatedBy());
            }
        }
        List<String> distinctList = userIdList.stream().distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        try {
            hrmPersonInfoMap = hrmUserCenterService.getHrmPersonInfo(distinctList);
        } catch (Exception e) {
            log.error(e);
        }
        for (CustomerItemsDTO item : dtoList) {
            String createBy = item.getCreateBy();
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoMap.get(createBy);
            if (Objects.nonNull(hrmPersonInfoDTO)) {
                item.setCreateBy(hrmPersonInfoDTO.getEmpName() + createBy);
            }
            String lastUpdatedBy = item.getLastUpdatedBy();
            HrmPersonInfoDTO userTemp = hrmPersonInfoMap.get(lastUpdatedBy);
            if (Objects.nonNull(userTemp)) {
                item.setLastUpdatedBy(userTemp.getEmpName() + lastUpdatedBy);
            }
        }
    }

    private void queryAndSetProperties(List<CustomerItemsDTO> dtoList) {
        Map<String, List<CustomerItemsDTO>> collectMap = dtoList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getAdditionalInfoId()))
                .collect(Collectors.groupingBy(CustomerItemsDTO::getCustomerComponentType));
        if (MapUtils.isEmpty(collectMap)) {
            return;
        }
        List<SysLookupValues> sysLookupValues = lookupValuesRepository.selectValuesByType(Constant.LOOKUP_TYPE_7306.intValue());
        if (CollectionUtils.isEmpty(sysLookupValues)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_7306});
        }
        // attribute1 是表名
        Map<String, String> paramsMap = sysLookupValues.stream()
                .filter(item -> StringUtils.isNotBlank(item.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getAttribute1, (key1, key2) -> key1));
        for (Map.Entry<String, List<CustomerItemsDTO>> entry : collectMap.entrySet()) {
            List<String> idList = entry.getValue().stream().map(CustomerItemsDTO::getAdditionalInfoId).collect(Collectors.toList());
            String tableName = paramsMap.get(entry.getKey());
            if (StringUtils.isBlank(tableName)) {
                continue;
            }
            switch (tableName) {
                // 1. CPU :   数据库表cpu_info
                case Constant.CustomerParamsTable.CPU_INFO:
                    this.queryAndTranceCpuInfo(entry, idList);
                    break;
                // 内存： 数据库表memory_info
                case Constant.CustomerParamsTable.MEMORY_INFO:
                    this.queryAndTranceMemoryInfo(entry, idList);
                    break;
                // 网卡：数据库表network_card_info
                case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                    this.queryAndTranceNetWorkInfo(entry, idList);
                    break;
                // RAID卡：数据库表raid_card_info
                case Constant.CustomerParamsTable.RAID_CARD_INFO:
                    this.queryAndTranceRaidCardInfo(entry, idList);
                    break;
                // 硬盘：数据库表hard_disk_info (注意itemType 三个都匹配硬盘详情表)  NVME也可以是SSD或SATA
                case Constant.CustomerParamsTable.HARD_DISK_INFO:
                    this.queryAndTranceHardDisk(entry, idList);
                    break;
                // GPU卡: 数据库表gpu_info
                case Constant.CustomerParamsTable.GPU_INFO:
                    this.queryAndTranceGpuInfo(entry, idList);
                    break;
                // 主板：数据库表motherboard_info
                case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                    this.queryAndTraceMotherInfo(entry, idList);
                    break;
                // 整机：数据库表machine_info
                case Constant.CustomerParamsTable.MACHINE_INFO:
                    this.queryAndTraceMachineInfo(entry, idList);
                    break;
                // 电源：数据库表power_source_info
                case Constant.CustomerParamsTable.POWER_SOURCE:
                    this.queryAndTracePowerSource(entry, idList);
                    break;
                default:
                    break;
            }
        }
    }

    void queryAndTracePowerSource(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<PowerSourceInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<PowerSourceInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchPowerSourceInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, PowerSourceInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(PowerSourceInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            PowerSourceInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    void queryAndTraceMachineInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<MachineInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<MachineInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchMachineInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, MachineInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(MachineInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            MachineInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceGpuInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<GpuInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<GpuInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchGpuInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, GpuInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(GpuInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            GpuInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTraceMotherInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<MotherboardInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<MotherboardInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchMotherboardInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, MotherboardInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(MotherboardInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            MotherboardInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceHardDisk(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<HardDiskInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<HardDiskInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchHardDiskInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, HardDiskInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(HardDiskInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            HardDiskInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceRaidCardInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<RaidCardInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<RaidCardInfoDTO> raidCardList = customerItemsParamsRepository.queryBatchRaidCardInfoByIds(list);
            if (CollectionUtils.isEmpty(raidCardList)) {
                continue;
            }
            resultMemoryList.addAll(raidCardList);
        }
        Map<String, RaidCardInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(RaidCardInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            RaidCardInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceNetWorkInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<NetworkCardInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<NetworkCardInfoDTO> networkCardList =
                    customerItemsParamsRepository.queryBatchNetworkCardInfoByIds(list);
            if (CollectionUtils.isEmpty(networkCardList)) {
                continue;
            }
            resultMemoryList.addAll(networkCardList);
        }
        Map<String, NetworkCardInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(NetworkCardInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            NetworkCardInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceCpuInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<CpuInfoDTO> resultList = new LinkedList<>();
        List<List<String>> splitList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitList) {
            List<CpuInfoDTO> cpuInfoDTOS = customerItemsParamsRepository.queryBatchCpuInfoByIds(list);
            if (CollectionUtils.isEmpty(cpuInfoDTOS)) {
                continue;
            }
            resultList.addAll(cpuInfoDTOS);
        }
        Map<String, CpuInfoDTO> cpuMap = resultList.stream().collect(Collectors.toMap(CpuInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            CpuInfoDTO memoryInfo = cpuMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private void queryAndTranceMemoryInfo(Map.Entry<String, List<CustomerItemsDTO>> entry, List<String> idList) {
        List<MemoryInfoDTO> resultMemoryList = new LinkedList<>();
        List<List<String>> splitMeList = CommonUtils.splitList(idList, Constant.INT_100);
        for (List<String> list : splitMeList) {
            List<MemoryInfoDTO> memoryInfoList = customerItemsParamsRepository.queryBatchMemoryInfoByIds(list);
            if (CollectionUtils.isEmpty(memoryInfoList)) {
                continue;
            }
            resultMemoryList.addAll(memoryInfoList);
        }
        Map<String, MemoryInfoDTO> memoryMap =
                resultMemoryList.stream().collect(Collectors.toMap(MemoryInfoDTO::getId, value -> value));
        for (CustomerItemsDTO itemsDTO : entry.getValue()) {
            MemoryInfoDTO memoryInfo = memoryMap.get(itemsDTO.getAdditionalInfoId());
            if (Objects.isNull(memoryInfo)) {
                continue;
            }
            itemsDTO.setParamsDetailStr(JacksonJsonConverUtil.beanToJson(memoryInfo));
            itemsDTO.setParamsDetail(BeanUtil.beanToMap(memoryInfo));
        }
    }

    private Map<String, String> getTypeMap(BigDecimal lookupType) {
        Map<String, Object> map = new HashMap<>();
        map.put(Constant.FIELD_LOOKUP_TYPE, lookupType);
        List<SysLookupValues> values = lookupValuesRepository.getList(map);
        return values.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning()) && StringUtils.isNotEmpty(e.getDescriptionChin())).collect(Collectors
                .toMap(SysLookupValues::getLookupMeaning, SysLookupValues::getDescriptionChin, (oldValue, newValue) -> newValue));
    }

    /**
     * @return
     * <AUTHOR>
     * 校验查询条件
     * @Date 2023/5/5 16:59
     * @Param []
     **/
    private void checkQryParams(CustomerItemsDTO dto) {
        boolean createNull = Objects.isNull(dto.getStartCreateDate()) || Objects.isNull(dto.getEndCreateDate());
        boolean updateNull = Objects.isNull(dto.getStartUpdateDate()) || Objects.isNull(dto.getEndUpdateDate());
        if (StringUtils.isEmpty(dto.getProjectName()) && createNull && updateNull) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_QRY_PARAMS_NULL);
        }
        // 创建时间不为空
        if (!createNull) {
            // 校验时间跨度
            checkTimeGreaterThanTwoYear(dto.getStartCreateDate(), dto.getEndCreateDate());
        }
        // 更新时间不为空
        if (!updateNull) {
            checkTimeGreaterThanTwoYear(dto.getStartUpdateDate(), dto.getEndUpdateDate());
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 校验时间跨度是否超过两年
     * @Date 2023/5/5 17:16
     * @Param [java.util.Date, java.util.Date]
     **/
    private void checkTimeGreaterThanTwoYear(Date startTime, Date endTime) {
        //开始时间加2年
        Date endDate = DateUtils.addYears(startTime, NumConstant.NUM_TWO);
        //大于2年报错
        if (endDate.before(endTime)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.QRY_TIME_CAN_NOT_GREATER_ONE_YEAR);
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 修改客户物料信息
     * @Date 2023/5/4 19:35
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    @Override
    public int updateCustomerItems(CustomerItemsDTO dto) {
        StringBuilder sb = new StringBuilder();
        String key = this.checkNewParams(dto, sb);
        RedisLock redisLock = new RedisLock(key);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE);
        }
        try {
            // 校验数据唯一键数据是否发生改变
            boolean keyChange = checkSourceKeyChange(dto);
            // 校验数据是否已存在
            if (!keyChange) {
                Integer exist = repository.checkItemsExist(dto);
                if (!Objects.isNull(exist) && exist > 0) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_EXIST, new Object[]{Constant.STR_UPDATE, sb});
                }
            }
            // 更新参数属性信息
            this.updateCustomerParams(dto);
            // 更新数据一条记录
            repository.updateCustomerItems(dto);
            return Constant.INT_1;
        } finally {
            redisLock.unlock();
        }
    }

    /* Started by AICoder, pid:n6e39ia838vdfa11407f082920b54c175c23cca5 */
    @Override
    public List<CustomerItemsDTO> getZteCodeByCustomerName(List<String> customerNameList, int rows, int page) {
        if (CollectionUtils.isEmpty(customerNameList)) {
            return new ArrayList<>();
        }
        if (rows <= NumConstant.NUM_ZERO || rows > NumConstant.NUM_FIVE_HUNDRED) {
            rows = NumConstant.NUM_FIVE_HUNDRED;
        }

        if (page <= NumConstant.NUM_ZERO) {
            page = NumConstant.NUM_ONE;
        }
        return repository.getZteCodeByCustomerName(customerNameList, rows, page);
    }
    /* Ended by AICoder, pid:n6e39ia838vdfa11407f082920b54c175c23cca5 */


    @Override
    public List<CustomerItemsDTO> getSameItemOfZteCode(List<String> zteCodeList, List<String> customerNumberList) {
        if (CollectionUtils.isEmpty(zteCodeList)) {
            return new ArrayList<>();
        }
        List<CustomerItemsDTO> customerItemsDTOS = repository.getSameItemOfZteCode(zteCodeList, customerNumberList);
        Set<String> customerCodeSet = new HashSet<>();
        Map<String, String> customerCodeAndOriginalCustomerCodeMap = new HashMap<>();
        // customerCode可能存在类似8369B和8369B-WC1的情况，仅在sql中通过like匹配可能不准确，需分割后再次匹配筛选
        customerItemsDTOS.stream().filter(curr -> StringUtils.isNotBlank(curr.getCustomerCode()))
                .forEach(customerItems -> {
                    List<String> list = Arrays.asList(StringUtils.split(customerItems.getCustomerCode(), Constant.SLASH_TWO));
                    if (StringUtils.equals(customerItems.getCustomerCode(), customerItems.getOriginalCustomerCode()) || list.contains(customerItems.getOriginalCustomerCode())) {
                        Set<String> tempSet = new HashSet<>();
                        // 收集包含主代码和衍生码的所有mpn
                        tempSet.add(customerItems.getCustomerCode());
                        tempSet.addAll(list);
                        customerCodeSet.addAll(tempSet);
                        tempSet.forEach(p -> {
                            customerCodeAndOriginalCustomerCodeMap.put(p, customerItems.getOriginalCustomerCode());
                        });
                    }
                });
        // 根据customerCode获取customerItems数据
        List<CustomerItemsDTO> resultList = repository.getByCustomerCode(new ArrayList<>(customerCodeSet));
        resultList.forEach(p -> {
            // 设置原始客户编码用于后续分组使用
            p.setOriginalCustomerCode(customerCodeAndOriginalCustomerCodeMap.get(p.getCustomerCode()));
        });
        return resultList;
    }
    @Override
    public int batchUpdateByZteCode(List<CustomerItemsDTO> customerItemsDTOList){
        return CollUtil.split(customerItemsDTOList, Constant.BATCH_SIZE).stream().map(repository::batchUpdateByZteCode).mapToInt(e -> e).sum();
    }

    private void updateCustomerParams(CustomerItemsDTO dto) {
        if (StringUtils.isBlank(dto.getId())) {
            return;
        }
        // 查询变更前的记录
        CustomerItemsDTO customerItemsDTO = repository.queryCustomerItemsById(dto.getId());
        if (Objects.isNull(customerItemsDTO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_DELETE_REFRESH_PAGE);
        }
        //  7306 表配置新增
        Map<String, String> paramsMap = this.queryLookUpDict();
        // id 删除参数
        this.deleteOldParams(dto, customerItemsDTO, paramsMap);
        if (Objects.isNull(dto.getParamsDetail())) {
            return;
        }
        // 没有属性表不处理
        String tableName = paramsMap.get(dto.getCustomerComponentType());
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        if (StringUtils.equals(dto.getCustomerComponentType(), customerItemsDTO.getCustomerComponentType())
                && StringUtils.isNotBlank(dto.getAdditionalInfoId())) {
            // 更新数据
            this.updateCustomerByTable(dto, tableName);
        } else {
            // 新增数据
            String id = this.insertCustomerParams(dto, tableName);
            dto.setAdditionalInfoId(id);
        }
    }

    private void updateCustomerByTable(CustomerItemsDTO dto, String tableName) {
        String jsonDetail = JSON.toJSONString(dto.getParamsDetail());
        switch (tableName) {
            // 1. CPU :   数据库表cpu_info
            case Constant.CustomerParamsTable.CPU_INFO:
                CpuInfoDTO cpuInfoDTO = JSON.parseObject(jsonDetail, CpuInfoDTO.class);
                cpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateCpuInfo(cpuInfoDTO);
                break;
            // 内存： 数据库表memory_info
            case Constant.CustomerParamsTable.MEMORY_INFO:
                MemoryInfoDTO memoryInfoDTO = JSON.parseObject(jsonDetail, MemoryInfoDTO.class);
                memoryInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateMemoryInfo(memoryInfoDTO);
                break;
            // 网卡：数据库表network_card_info
            case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                NetworkCardInfoDTO networkCardInfoDTO = JSON.parseObject(jsonDetail, NetworkCardInfoDTO.class);
                networkCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateNetworkCardInfo(networkCardInfoDTO);
                break;
            // RAID卡：数据库表raid_card_info
            case Constant.CustomerParamsTable.RAID_CARD_INFO:
                RaidCardInfoDTO raidCardInfoDTO = JSON.parseObject(jsonDetail, RaidCardInfoDTO.class);
                raidCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateRaidCardInfo(raidCardInfoDTO);
                break;
            // 硬盘：数据库表hard_disk_info (注意itemType 三个都匹配硬盘详情表)  NVME也可以是SSD或SATA
            case Constant.CustomerParamsTable.HARD_DISK_INFO:
                HardDiskInfoDTO hardDiskInfoDTO = JSON.parseObject(jsonDetail, HardDiskInfoDTO.class);
                hardDiskInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateHardDiskInfo(hardDiskInfoDTO);
                break;
            // GPU卡: 数据库表gpu_info
            case Constant.CustomerParamsTable.GPU_INFO:
                GpuInfoDTO gpuInfoDTO = JSON.parseObject(jsonDetail, GpuInfoDTO.class);
                gpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateGpuInfo(gpuInfoDTO);
                break;
            // 主板：数据库表motherboard_info
            case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                MotherboardInfoDTO motherboardInfoDTO = JSON.parseObject(jsonDetail, MotherboardInfoDTO.class);
                motherboardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateMotherboardInfo(motherboardInfoDTO);
                break;
            // 整机：数据库表machine_info
            case Constant.CustomerParamsTable.MACHINE_INFO:
                MachineInfoDTO machineInfoDTODTO = JSON.parseObject(jsonDetail, MachineInfoDTO.class);
                machineInfoDTODTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updateMachineInfo(machineInfoDTODTO);
                break;
            // 电源：数据库表power_source_info
            case Constant.CustomerParamsTable.POWER_SOURCE:
                PowerSourceInfoDTO powerSourceInfoDTO = JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class);
                powerSourceInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                customerItemsParamsRepository.updatePowerSourceInfo(powerSourceInfoDTO);
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_PARAM_TABLE_ERROR,
                        new Object[]{dto.getCustomerComponentType(), tableName});
        }
    }

    /**
     * 新增参数信息
     *
     * @param dto       请求参数
     * @param tableName 配置表名
     */
    private String insertCustomerParams(CustomerItemsDTO dto, String tableName) {
        String jsonDetail = JSON.toJSONString(dto.getParamsDetail());
        String id = idGenerator.snowFlakeIdStr();

        switch (tableName) {
            case Constant.CustomerParamsTable.CPU_INFO:
                return saveCpuInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.MEMORY_INFO:
                return saveMemoryInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                return saveNetworkCardInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.RAID_CARD_INFO:
                return saveRaidCardInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.HARD_DISK_INFO:
                return saveHardDiskInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.GPU_INFO:
                return saveGpuInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                return saveMotherboardInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.MACHINE_INFO:
                return saveMachineInfo(dto, jsonDetail, id);
            case Constant.CustomerParamsTable.POWER_SOURCE:
                return savePowerSourceInfo(dto, jsonDetail, id);
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.CUSTOMER_PARAM_TABLE_ERROR,
                        new Object[]{dto.getCustomerComponentType(), tableName});
        }
    }

    private String saveCpuInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        CpuInfoDTO cpuInfoDTO = JSON.parseObject(jsonDetail, CpuInfoDTO.class);
        cpuInfoDTO.setId(id);
        cpuInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        cpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertCpuInfo(Arrays.asList(cpuInfoDTO));
        return id;
    }

    private String saveMemoryInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        MemoryInfoDTO memoryInfoDTO = JSON.parseObject(jsonDetail, MemoryInfoDTO.class);
        memoryInfoDTO.setId(id);
        memoryInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        memoryInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertMemoryInfo(Arrays.asList(memoryInfoDTO));
        return id;
    }

    private String saveNetworkCardInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        NetworkCardInfoDTO networkCardInfoDTO = JSON.parseObject(jsonDetail, NetworkCardInfoDTO.class);
        networkCardInfoDTO.setId(id);
        networkCardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        networkCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertNetworkCardInfo(Arrays.asList(networkCardInfoDTO));
        return id;
    }

    private String saveRaidCardInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        RaidCardInfoDTO raidCardInfoDTO = JSON.parseObject(jsonDetail, RaidCardInfoDTO.class);
        raidCardInfoDTO.setId(id);
        raidCardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        raidCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertRaidCardInfo(Arrays.asList(raidCardInfoDTO));
        return id;
    }

    private String saveHardDiskInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        HardDiskInfoDTO hardDiskInfoDTO = JSON.parseObject(jsonDetail, HardDiskInfoDTO.class);
        hardDiskInfoDTO.setId(id);
        hardDiskInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        hardDiskInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertHardDiskInfo(Arrays.asList(hardDiskInfoDTO));
        return id;
    }

    private String saveGpuInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        GpuInfoDTO gpuInfoDTO = JSON.parseObject(jsonDetail, GpuInfoDTO.class);
        gpuInfoDTO.setId(id);
        gpuInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        gpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertGpuInfo(Arrays.asList(gpuInfoDTO));
        return id;
    }

    private String saveMotherboardInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        MotherboardInfoDTO motherboardInfoDTO = JSON.parseObject(jsonDetail, MotherboardInfoDTO.class);
        motherboardInfoDTO.setId(id);
        motherboardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        motherboardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertMotherboardInfo(Arrays.asList(motherboardInfoDTO));
        return id;
    }

    private String saveMachineInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        MachineInfoDTO machineInfoDTO = JSON.parseObject(jsonDetail, MachineInfoDTO.class);
        machineInfoDTO.setId(id);
        machineInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        machineInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertMachineInfo(Arrays.asList(machineInfoDTO));
        return id;
    }

    private String savePowerSourceInfo(CustomerItemsDTO dto, String jsonDetail, String id) {
        PowerSourceInfoDTO powerSourceInfoDTO = JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class);
        powerSourceInfoDTO.setId(id);
        powerSourceInfoDTO.setCreateBy(dto.getLastUpdatedBy());
        powerSourceInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
        customerItemsParamsRepository.batchInsertPowerSourceInfo(Arrays.asList(powerSourceInfoDTO));
        return id;
    }


    /**
     * 删变更历史
     *
     * @param dto              入参
     * @param customerItemsDTO 数据库数据
     * @param paramsMap        数据表配置
     */
    private void deleteOldParams(CustomerItemsDTO dto, CustomerItemsDTO customerItemsDTO, Map<String, String> paramsMap) {
        // 类型出现变更
        if (!StringUtils.equals(customerItemsDTO.getAdditionalInfoId(), dto.getAdditionalInfoId())) {
            String additionalInfoId = customerItemsDTO.getAdditionalInfoId();
            String tableName = paramsMap.get(customerItemsDTO.getCustomerComponentType());
            if (StringUtils.isNotBlank(tableName) && StringUtils.isNotBlank(additionalInfoId)) {
                switch (tableName) {
                    // 1. CPU :   数据库表cpu_info
                    case Constant.CustomerParamsTable.CPU_INFO:
                        customerItemsParamsRepository.deleteCpuInfoById(additionalInfoId);
                        break;
                    // 内存： 数据库表memory_info
                    case Constant.CustomerParamsTable.MEMORY_INFO:
                        customerItemsParamsRepository.deleteMemoryInfoById(additionalInfoId);
                        break;
                    // 网卡：数据库表network_card_info
                    case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                        customerItemsParamsRepository.deleteNetworkCardInfoById(additionalInfoId);
                        break;
                    // RAID卡：数据库表raid_card_info
                    case Constant.CustomerParamsTable.RAID_CARD_INFO:
                        customerItemsParamsRepository.deleteRaidCardInfoById(additionalInfoId);
                        break;
                    // 硬盘：数据库表hard_disk_info (注意itemType 三个都匹配硬盘详情表)  NVME也可以是SSD或SATA
                    case Constant.CustomerParamsTable.HARD_DISK_INFO:
                        customerItemsParamsRepository.deleteHardDiskInfoById(additionalInfoId);
                        break;
                    // GPU卡: 数据库表gpu_info
                    case Constant.CustomerParamsTable.GPU_INFO:
                        customerItemsParamsRepository.deleteGpuInfoById(additionalInfoId);
                        break;
                    // 主板：数据库表motherboard_info
                    case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                        customerItemsParamsRepository.deleteMotherboardInfoById(additionalInfoId);
                        break;
                    // 整机：数据库表machine_info
                    case Constant.CustomerParamsTable.MACHINE_INFO:
                        customerItemsParamsRepository.deleteMachineInfoById(additionalInfoId);
                        break;
                    // 电源：数据库表power_source_info
                    case Constant.CustomerParamsTable.POWER_SOURCE:
                        customerItemsParamsRepository.deletePowerSourceInfoById(additionalInfoId);
                        break;
                    default:
                        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_PARAM_TABLE_ERROR,
                                new Object[]{dto.getCustomerComponentType(), tableName});
                }
            }
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 修改校验唯一键数据是否改变
     * @Date 2023/5/8 14:10
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    private boolean checkSourceKeyChange(CustomerItemsDTO dto) {
        if (Constant.STRING_FOUR.equals(dto.getProjectType())) {
            return StringUtils.equals(dto.getSourceZteCode(), dto.getZteCode())
                    && StringUtils.equals(dto.getSourceZteSupplier(), dto.getZteSupplier())
                    && StringUtils.equals(dto.getSourceZteBrandStyle(), dto.getZteBrandStyle());
        } else {
            return StringUtils.equals(dto.getSourceZteCode(), dto.getZteCode());
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 根据id删除一条记录
     * @Date 2023/5/4 19:38
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    @Override
    public int deleteCustomerItems(String id, String empNo) {
        if (StringUtils.isEmpty(id)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_ID_CANNOT_DELETE);
        }
        repository.deleteCustomerItems(id, empNo);
        return Constant.INT_1;
    }


    /**
     * @return
     * <AUTHOR>
     * 导出客户物料信息
     * @Date 2023/5/6 10:36
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO, java.lang.String, javax.servlet.http.HttpServletResponse]
     **/
    @Override
    public void exportCustomerItems(CustomerItemsDTO dto, HttpServletResponse response) throws Exception {
        String key = RedisKeyConstant.CUSTOMER_ITEMS_LOCK + dto.getEmpNo();
        RedisLock redisLock = new RedisLock(key);
        if (!redisLock.lock()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE);
        }
        try {
            dto.setPage(Constant.INT_1);
            dto.setRows(Constant.INT_1);
            Map<String, String> projectTypeMap = getTypeMap(Constant.LOOKUP_TYPE_7303);
            Map<String, String> customerComponentTypeMap = getTypeMap(Constant.LOOKUP_TYPE_7306);
            Map<String, String> boardTypeMap = getTypeMap(Constant.LOOKUP_TYPE_7307);
            Map<String, String> projTypeMap = getTypeMap(Constant.LOOKUP_TYPE_6817);
            dto.setProjTypeMap(projTypeMap);
            Page<CustomerItemsDTO> page = pageInfo(dto, projectTypeMap, customerComponentTypeMap, boardTypeMap, projTypeMap);
            dto.setSearchCount(false);
            BigExcelProcesser bigExcelProcesser = new BigExcelProcesser();
            // 新建文件
            bigExcelProcesser.createOrAppend(Constant.CustomerItems.HEADER, new ArrayList<>(), Constant.CustomerItems.PROPS);
            SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
            String fileName = sdf.format(new Date()) + Constant.CustomerItems.TITLE;
            if (page.getTotal() <= Constant.INT_0) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_TO_EXPORT);
            } else if (page.getTotal() <= Constant.INT_50000) {
                this.exportItemsInfo(dto, projectTypeMap, bigExcelProcesser, customerComponentTypeMap, boardTypeMap);
                bigExcelProcesser.returnByResponse(fileName, response);
            } else {
                this.exportItemsInfoToEmail(dto, projectTypeMap, bigExcelProcesser, customerComponentTypeMap, boardTypeMap);
            }
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 邮件导出客户物料信息
     * @Date 2023/5/6 10:15
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO, com.zte.springbootframe.util.BigExcelProcesser]
     **/
    private void exportItemsInfoToEmail(CustomerItemsDTO dto, Map<String, String> typeMap, BigExcelProcesser bigExcelProcesser,
                                        Map<String, String> customerComponentTypeMap, Map<String, String> boardTypeMap) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = simpleDateFormat.format(new Date()) + Constant.CustomerItems.TITLE;
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        ThreadUtil.EXECUTOR.execute(() -> {
            try {
                MESHttpHelper.setHttpRequestHeader(header);
                this.exportItemsInfo(dto, typeMap, bigExcelProcesser, customerComponentTypeMap, boardTypeMap);
                StringBuilder sb = new StringBuilder();
                String filePath = FileUtils.tempPath + System.currentTimeMillis() + fileName;// 上传成功后删除本地文件
                FileUtils.checkFilePath(filePath);
                bigExcelProcesser.saveAsFile(filePath);
                String fileKey = cloudDiskHelper.fileUpload(filePath, dto.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
                FileUtils.deleteFile(filePath);
                // 使用本地URL
                String fileUrl = cloudDiskHelper.getFileDownloadUrl(fileKey, fileName, dto.getEmpNo());
                String emailUrl = StringUtils.isEmpty(dto.getEmailUrl()) ? dto.getEmpNo() : dto.getEmailUrl();
                sb.append(Constant.EMAIL_PREFIX)
                        .append(fileName)
                        .append(Constant.EMAIL_PREFIX_A)
                        .append(fileUrl)
                        .append(Constant.EMAIL_COLO)
                        .append(Constant.CLICK_DOWN)
                        .append(Constant.EMAIL_SUFFIX)
                        .append(Constant.STR_WRAP);
                emailUtils.sendMail(emailUrl, Constant.CustomerItems.EMAIL_TITLE_ZH, null,
                        sb.toString(), null);
            } catch (Exception e) {
                // 发送邮件通知
                emailUtils.sendMail(dto.getEmailUrl(), Constant.CustomerItems.EMAIL_TITLE_ZH_ERROR, null,
                        getTrace(e), null);
            } finally {
                MESHttpHelper.removeHttpRequestHeader();
            }
        });
    }

    /**
     * @return
     * <AUTHOR>
     * 导出客户物料数据到excel
     * @Date 2023/5/6 10:01
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO, com.zte.springbootframe.util.BigExcelProcesser]
     **/
    private void exportItemsInfo(CustomerItemsDTO dto, Map<String, String> typeMap, BigExcelProcesser bigExcelProcesser, Map<String, String> customerComponentTypeMap, Map<String, String> boardTypeMap) throws Exception {
        List<CustomerItemsDTO> dtoList;
        int page = INT_0;
        dto.setRows(INT_1000);
        do {
            page++;
            dto.setPage(page);
            Page<CustomerItemsDTO> pageInfo = pageInfo(dto, typeMap, customerComponentTypeMap, boardTypeMap, dto.getProjTypeMap());
            dtoList = pageInfo.getRows();
            if (CollectionUtils.isEmpty(dtoList)) {
                continue;
            }
            bigExcelProcesser.createOrAppend(Constant.CustomerItems.HEADER, dtoList, Constant.CustomerItems.PROPS);
        } while (dtoList.size() >= INT_1000);
    }

    /**
     * @return
     * <AUTHOR>
     * 按ZTE代码查客户信息
     * @Date 2023/5/6 16:33
     * @Param [com.zte.interfaces.dto.CustomerItemsDTO]
     **/
    @Override
    public List<CustomerItemsDTO> getCustomerItemsInfo(CustomerItemsDTO dto) {
        if (CollectionUtils.isEmpty(dto.getItemNoList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_LIST_NULL);
        }
        if (dto.getItemNoList().size() > Constant.INT_500) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAX_ITEM_NO_IS_ONCE);
        }
        // 数据字典获取子公司/客户名称对应关系
        Map<String, Object> map = new HashMap<>();
        map.put(Constant.FIELD_LOOKUP_TYPE, Constant.LOOKUP_TYPE_7300);
        List<SysLookupValues> values = lookupValuesRepository.getList(map);
        if (CollectionUtils.isEmpty(values)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_TYPE_7300});
        }
        Map<String, String> typeMap = values.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning()) && StringUtils.isNotEmpty(e.getAttribute1())).collect(Collectors
                .toMap(SysLookupValues::getAttribute1, SysLookupValues::getLookupMeaning, (oldValue, newValue) -> newValue));
        if (StringUtils.isNotBlank(dto.getCustomerSubName())) {
            String customerName = typeMap.get(dto.getCustomerSubName());
            dto.setCustomerName(customerName);
            if (StringUtils.isBlank(customerName)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.SUB_CUSTOMER_CONFIG_LOST,
                        new Object[]{dto.getCustomerSubName(),Constant.LOOKUP_TYPE_7300});
            }
        }
        List<CustomerItemsDTO> resultList = repository.getSubCustomerItemsInfo(dto);
        if (CollectionUtils.isNotEmpty(resultList)) {
            // 获取扩展属性
            this.queryAndSetProperties(resultList);
        }
        return resultList;
    }

    /**
     * @return
     * <AUTHOR>
     * 获取ZTE代码供应商/规格型号
     * @Date 2023/5/8 20:24
     * @Param [com.zte.interfaces.dto.SplitItemNoDTO]
     **/
    public SrmPageRows<SplitItemNoDTO> queryItemBrandName(String itemNo) {
        if (StringUtils.isEmpty(itemNo)) {
            return new SrmPageRows<>();
        }
        SplitItemNoDTO splitItemNo = new SplitItemNoDTO();
        splitItemNo.setItemNo(itemNo);
        SysLookupValues values = new SysLookupValues();
        values.setLookupCode(Constant.VALUE_1004067001);
        SysLookupValues lookupValues = lookupValuesRepository.selectSysLookupValuesById(values);
        if (lookupValues == null || StringUtils.isEmpty(lookupValues.getLookupMeaning())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.VALUE_1004067001});
        }
        String url = lookupValues.getLookupMeaning();
        String result = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(splitItemNo), MESHttpHelper.getHttpRequestHeader());
        String s = ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(result);
        return JacksonJsonConverUtil.jsonToListBean(Normalizer.normalize(s,
                Normalizer.Form.NFKC), new TypeReference<SrmPageRows<SplitItemNoDTO>>() {
        });
    }

    /**
     * 获取客户基础信息
     *
     * @return
     * @Param customerItemsDTO
     **/
    @Override
    public List<CustomerItemsDTO> getCustomerItemsInfoSelfDevelopedByItemNoList(CustomerItemsDTO customerItemsDTO) {
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        List<String> itemNoList = customerItemsDTO.getItemNoList();
        CustomerItemsDTO param = new CustomerItemsDTO();
        param.setCustomerName(customerItemsDTO.getCustomerName());
        param.setCustomerSupplier(customerItemsDTO.getCustomerSupplier());
        if (CollectionUtils.isEmpty(itemNoList)) {
            List<CustomerItemsDTO> tempList = repository.getCustomerItemsInfoSelfDevelopedByItemNoList(param);
            if (!CollectionUtils.isEmpty(tempList)) {
                customerItemsDTOList.addAll(tempList);
            }

        } else {
            for (List<String> itemNoTempList : CommonUtils.splitList(itemNoList, Constant.BATCH_SIZE)) {
                param.setItemNoList(itemNoTempList);
                List<CustomerItemsDTO> tempList = repository.getCustomerItemsInfoSelfDevelopedByItemNoList(param);
                if (!CollectionUtils.isEmpty(tempList)) {
                    customerItemsDTOList.addAll(tempList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(customerItemsDTOList)) {
            // 获取扩展属性
            this.queryAndSetProperties(customerItemsDTOList);
        }
        return customerItemsDTOList;
    }

    @Override
    public List<CustomerItemsDTO> queryListByCustomerList(CustomerItemsDTO customerItemsDTO) {
        return repository.queryListByCustomerList(customerItemsDTO);
    }

    /**
     * 导入模板下载
     * @param response
     * @param tableName
     */
    @Override
    public void exportTaskExcel(HttpServletResponse response, String tableName) {
        String[] title;
        StringBuilder fileName = new StringBuilder(Constant.MATERICAL_CODE_MAINTENANCE);
        switch (tableName) {
            case Constant.CustomerParamsTable.CPU_INFO:
                title = Constant.CPU_INFO_TITLES;
                fileName.append(Constant.ExcelName.CPU_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.MEMORY_INFO:
                title = Constant.MEMORY_INFO_TITLES;
                fileName.append(Constant.ExcelName.MEMORY_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                title = Constant.NETWORK_CARD_INFO_TITLES;
                fileName.append(Constant.ExcelName.NETWORK_CARD_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.RAID_CARD_INFO:
                title = Constant.RAID_CARD_INFO_TITLES;
                fileName.append(Constant.ExcelName.RAID_CARD_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.HARD_DISK_INFO:
                title = Constant.HARD_DISK_INFO_TITLES;
                fileName.append(Constant.ExcelName.HARD_DISK_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.GPU_INFO:
                title = Constant.GPU_INFO_TITLES;
                fileName.append(Constant.ExcelName.GPU_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                title = Constant.MOTHERBOARD_INFO_TITLES;
                fileName.append(Constant.ExcelName.MOTHERBOARD_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.MACHINE_INFO:
                title = Constant.MACHINE_INFO_TITLES;
                fileName.append(Constant.ExcelName.MACHINE_INFO).append(Constant.POINT_XLSX);
                break;
            case Constant.CustomerParamsTable.POWER_SOURCE:
                title = Constant.POWER_SOURCE_INFO_TITLES;
                fileName.append(Constant.ExcelName.POWER_SOURCE).append(Constant.POINT_XLSX);
                break;
            default:
                title = Constant.COMMON_TITLES;
                fileName.append(Constant.POINT_XLSX);
                break;
        }
        String sheetName = Constant.MATERICAL_CODE_MAINTENANCE;
        HSSFWorkbook hssfWorkbook = ExcelUtils.getHSSFWorkbook(sheetName, title, null, null, new HashMap<>());
        HSSFCellStyle style0 = hssfWorkbook.createCellStyle();
        HSSFDataFormat format = hssfWorkbook.createDataFormat();
        style0.setDataFormat(format.getFormat("@"));
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(4, style0);
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(5, style0);
        hssfWorkbook.getSheetAt(0).setDefaultColumnStyle(6, style0);
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            ExcelUtils.setResponseHeaders(response, fileName.toString());
            hssfWorkbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            LOG.error(ERROR_EXPORT_MODEL, e);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    LOG.error(Constant.ABNORMAL_STREAM_SHUT_DOWN, e);
                }
            }
            if (null != hssfWorkbook) {
                try {
                    hssfWorkbook.close();
                } catch (IOException e) {
                    LOG.error(Constant.ABNORMAL_STREAM, e);
                }
            }
        }
    }

    /**
     * 批量解析
     * @param inputStream
     * @param tableName
     * @return
     */
    @Override
    public List<CustomerImportDTO> uploadCustomerImportBatch(InputStream inputStream, String tableName) {
        ResultData resultData;
        String[] propNames;
        String[] headers;
        switch (tableName) {
            case Constant.CustomerParamsTable.CPU_INFO:
                propNames = Constant.CPU_INFO_PROPNAMES;
                headers = Constant.CPU_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.MEMORY_INFO:
                propNames = Constant.MEMORY_INFO_PROPNAMES;
                headers = Constant.MEMORY_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                propNames = Constant.NETWORK_CARD_INFO_PROPNAMES;
                headers = Constant.NETWORK_CARD_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.RAID_CARD_INFO:
                propNames = Constant.RAID_CARD_INFO_PROPNAMES;
                headers = Constant.RAID_CARD_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.HARD_DISK_INFO:
                propNames = Constant.HARD_DISK_INFO_PROPNAMES;
                headers = Constant.HARD_DISK_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.GPU_INFO:
                propNames = Constant.GPU_INFO_PROPNAMES;
                headers = Constant.GPU_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                propNames = Constant.MOTHERBOARD_INFO_PROPNAMES;
                headers = Constant.MOTHERBOARD_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.MACHINE_INFO:
                propNames = Constant.MACHINE_INFO_PROPNAMES;
                headers = Constant.MACHINE_INFO_TITLES;
                break;
            case Constant.CustomerParamsTable.POWER_SOURCE:
                propNames = Constant.POWER_SOURCE_INFO_PROPNAMES;
                headers = Constant.POWER_SOURCE_INFO_TITLES;
                break;
            default:
                propNames = Constant.COMMON_PROPNAMES;
                headers = Constant.COMMON_TITLES;
                break;
        }

        resultData = ExcelUtils.resolveExcel(inputStream, CustomerImportDTO.class, propNames);
        if (null == resultData || null == resultData.getData()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        // 判断Excel读取结果
        if (!com.zte.common.utils.NumConstant.STRING_ZERO.equals(resultData.getCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXCEL_READ_FAILED);
        }
        // 检查表头
        List<String> headerList = new ArrayList<>(Arrays.asList(headers));
        ArrayList<List> resultList = new ArrayList<>(Arrays.asList(resultData.getHeader()));
        List<String> resultHeaderList = resultList.stream().filter(Objects::nonNull).filter(item -> item instanceof List)
                .flatMap(item -> ((List<?>) item).stream()).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
        if (!new HashSet<>(resultHeaderList).equals(new HashSet<>(headerList))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHECK_THE_FILE_AND_THE_SELECTED_CUSTOMER_PART_TYPE);
        }
        List<String> arrayList = resultData.getHeader() == null ? Lists.newArrayList() : resultData.getHeader().subList(0, headerList.size());
        if (!arrayList.equals(headerList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TABLE_COLUME_ERROR);
        }
        // 获取数据并进行检查
        List<CustomerImportDTO> excelInfoList = (List<CustomerImportDTO>) resultData.getData();
        List<CustomerImportDTO> checkList = this.checkExcelInfo(excelInfoList);
        return checkList;
    }

    /**
     * @param excelInfoList
     * @return
     */
    public List<CustomerImportDTO> checkExcelInfo(List<CustomerImportDTO> excelInfoList) {
        if (CollectionUtils.isEmpty(excelInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        //单次导入不超过500条
        if (excelInfoList.size() > NumConstant.NUM_FIVE_HUNDRED) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_LENGTH_EXCEEDS_500);
        }
        return excelInfoList;
    }

    /**
     * 批量校验
     * @param requestBody
     * @return
     */
    @Override
    public List<CustomerImportDTO> checkTaskBatch(List<CustomerImportDTO> requestBody) {
        List<String> customerNames = getSysLookupDescriptions(Constant.LOOKUP_TYPE_7300.intValue());
        List<String> cooperationModes = getSysLookupDescriptions(Constant.LOOKUP_TYPE_7301.intValue());
        List<String> projectPhases = getSysLookupDescriptions(Constant.LOOKUP_TYPE_7302.intValue());
        Map<String, String> projTypeMap = getSysLookupMap(Constant.LOOKUP_TYPE_6817.intValue());
        Map<String, String> projectTypeMap = getSysLookupMap(Constant.LOOKUP_TYPE_7303.intValue());
        Map<String, String> boardTypeMap = getSysLookupMap(Constant.LOOKUP_TYPE_7307.intValue());

        // 遍历列表，执行校验逻辑
        for (CustomerImportDTO customerImportDTO : requestBody) {
            StringBuilder checkResult = new StringBuilder();

            // 调用校验方法
            validateBasicInfoOne(customerImportDTO, checkResult, customerNames, cooperationModes, projectPhases);
            validateBasicInfoTwo(customerImportDTO, checkResult, projTypeMap, projectTypeMap);
            validateProjectRelatedInfo(customerImportDTO, checkResult, boardTypeMap);
            // 设置校验结果到 checkResult 字段
            customerImportDTO.setCheckResult(checkResult.toString());
        }

        // 每批次大小
        int batchSize = 100;
        int totalSize = requestBody.size();

        // 遍历请求体，进行批量处理
        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, totalSize);
            List<CustomerImportDTO> batch = requestBody.subList(i, end);

            // 批量校验ZTE代码和名称
            validateZteCodesAndNames(batch);

            // 校验数据是否存在数据库中
            validateDatabaseExistence(batch);
        }

        return requestBody;
    }

    /**
     * 校验基本信息，如客户名称、合作模式、项目类型等
     */
    private void validateBasicInfoOne(CustomerImportDTO dto, StringBuilder sb, List<String> customerNames, List<String> cooperationModes, List<String> projectPhases) {
        // 校验 customerName
        if (StringUtils.isEmpty(dto.getCustomerName())) {
            sb.append(Constant.CUSTOMER_NAME_CANNOT_BE_EMPTY);
        } else if (!customerNames.contains(dto.getCustomerName())) {
            sb.append(Constant.CUSTOMER_NAME_MUST_BE).append(customerNames).append(Constant.COLON).append(Constant.STRING_EMPTY_ONE);
        }

        // 校验 cooperationMode
        if (!StringUtils.isEmpty(dto.getCooperationMode()) && !cooperationModes.contains(dto.getCooperationMode())) {
            sb.append(Constant.COOPERATION_MODE_MUST_BE).append(cooperationModes).append(Constant.COLON).append(Constant.STRING_EMPTY_ONE);
        }

        // 校验 projectPhase
        if (!StringUtils.isEmpty(dto.getProjectPhase()) && !projectPhases.contains(dto.getProjectPhase())) {
            sb.append(Constant.PROJECT_PHASE_MUST_BE).append(projectPhases).append(Constant.COLON).append(Constant.STRING_EMPTY_ONE);
        }
    }

    private void validateBasicInfoTwo(CustomerImportDTO dto, StringBuilder sb, Map<String, String> projTypeMap, Map<String, String> projectTypeMap) {
        // 校验 projType
        String projTypeNumber = projTypeMap.get(dto.getProjType());
        if (StringUtils.isEmpty(dto.getProjType())) {
            sb.append(Constant.PROJECT_TYPE_CANNOT_BE_EMPTY);
        } else if (projTypeNumber == null) {
            sb.append(Constant.PROJECT_TYPE_INPUT_ERROR);
        } else {
            dto.setProjType(projTypeNumber);
        }

        // 校验 projectType
        if (StringUtils.isEmpty(dto.getProjectType())) {
            sb.append(Constant.TYPE_CANNOT_BE_EMPTY);
        } else {
            String projectTypeNumber = projectTypeMap.get(dto.getProjectType());
            if (projectTypeNumber == null) {
                sb.append(Constant.TYPE_INPUT_ERROR);
            } else {
                dto.setProjectType(projectTypeNumber);
            }
        }
    }

    private List<String> getSysLookupDescriptions(int lookupType) {
        List<SysLookupValues> sysLookupValues = lookupValuesRepository.selectValuesByType(lookupType);
        if (CollectionUtils.isEmpty(sysLookupValues)) {
            throw new MesBusinessException(
                    RetCode.BUSINESSERROR_CODE,
                    MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{lookupType}
            );
        }
        return sysLookupValues.stream()
                .map(SysLookupValues::getDescriptionChin)
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<String, String> getSysLookupMap(int lookupType) {
        List<SysLookupValues> sysLookupValues = lookupValuesRepository.selectValuesByType(lookupType);
        if (CollectionUtils.isEmpty(sysLookupValues)) {
            throw new MesBusinessException(
                    RetCode.BUSINESSERROR_CODE,
                    MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{lookupType}
            );
        }
        return sysLookupValues.stream()
                .collect(Collectors.toMap(
                        SysLookupValues::getDescriptionChin,
                        SysLookupValues::getLookupMeaning,
                        (existingValue, newValue) -> existingValue
                ));
    }



    /**
     * 校验项目相关信息，如板码类型、客户型号、ZTE 代码等
     */
    private void validateProjectRelatedInfo(CustomerImportDTO dto, StringBuilder sb, Map<String, String> boardTypeMap) {
        // 类型为0, 1, 2的进行板码类型校验
        boolean isValidProjectType = (Constant.STRING_ZERO.equals(dto.getProjectType()) ||
                Constant.STRING_ONE.equals(dto.getProjectType()) ||
                Constant.STRING_TWO.equals(dto.getProjectType()));
        if (isValidProjectType && !StringUtils.isEmpty(dto.getBoardType())) {
            String boardType = boardTypeMap.get(dto.getBoardType());
            if (boardType == null) {
                sb.append(Constant.BOARD_TYPE_INPUT_ERROR);
            } else {
                dto.setBoardType(boardType);
            }
        }
        if (!isValidProjectType && !StringUtils.isEmpty(dto.getBoardType())) {
            sb.append(Constant.PLEASE_REMOVE_BOARD_TYPE);
        }

        // 类型为3 整机的时候，客户型号不能为空
        if (Constant.STRING_THREE.equals(dto.getProjectType()) && StringUtils.isEmpty(dto.getCustomerModel())) {
            sb.append(Constant.CUSTOMER_MODEL_CANNOT_BE_EMPTY);
        }

        // 校验 zteCode
        if (StringUtils.isEmpty(dto.getZteCode())) {
            sb.append(Constant.ZTE_CODE_CANNOT_BE_EMPTY);
        }

        // 校验CustomerComponentType
        if (StringUtils.isEmpty(dto.getCustomerComponentType())) {
            sb.append(Constant.CUSTOMER_COMPONENT_TYPE_CANNOT_BE_EMPTY);
            return;
        }
        if (!dto.getCustomerComponentType().equals(dto.getStrCustomerComponentType())) {
            sb.append(Constant.CUSTOMER_COMPONENT_TYPE_NOT_CONSISTENT);
        }
    }

    /**
     * 校验ZTE代码和ZTE代码名称
     */
    private void validateZteCodesAndNames(List<CustomerImportDTO> batch) {
        // 批量查询 ZTE 代码
        List<String> zteCodes = batch.stream()
                .map(CustomerImportDTO::getZteCode)
                .collect(Collectors.toList());

        // 批量查询数据库，假设我们有一个方法可以根据 ZTE 代码批量获取信息
        List<BsItemInfo> bsItemInfoList = bsItemInfoRepository.selectBsItemInfoByNos(zteCodes);

        // 转换 List<BsItemInfo> 为 Map<String, BsItemInfo>
        Map<String, BsItemInfo> bsItemInfoMap = bsItemInfoList.stream()
                .collect(Collectors.toMap(BsItemInfo::getItemNo, bsItemInfo -> bsItemInfo));

        // 进行校验
        for (CustomerImportDTO dto : batch) {
            StringBuilder sb = new StringBuilder(dto.getCheckResult());
            BsItemInfo bsItemInfo = bsItemInfoMap.get(dto.getZteCode());

            if (bsItemInfo == null) {
                sb.append(Constant.ZTE_CODE_NOT_EXIST);
            } else {
                // 校验 zteCodeName
                if (StringUtils.isEmpty(dto.getZteCodeName())) {
                    dto.setZteCodeName(bsItemInfo.getItemName());
                }
                if (!dto.getZteCodeName().replaceAll(Constant.ESCAPE_S, Constant.STRING_EMPTY_ONE)
                        .equals((bsItemInfo.getItemName()).replaceAll(Constant.ESCAPE_S, Constant.STRING_EMPTY_ONE))) {
                    sb.append(Constant.ZTE_CODE_OR_NAME_INPUT_ERROR);
                }
            }

            // 将校验结果设置到 checkResult 字段
            dto.setCheckResult(sb.toString());
        }
    }

    private void validateDatabaseExistence(List<CustomerImportDTO> batch) {
        // 1. 使用 generateUniqueKey 动态生成唯一标识符
        Map<String, Integer> uniqueKeyCountMap = new HashMap<>();
        for (CustomerImportDTO dto : batch) {
            // 转换为 CustomerItemsDTO 并生成唯一标识符
            CustomerItemsDTO tempDto = BeanUtil.copyProperties(dto, CustomerItemsDTO.class);
            String uniqueKey = generateUniqueKey(tempDto);

            // 使用 Map 统计每个唯一标识符的出现次数
            uniqueKeyCountMap.put(uniqueKey, uniqueKeyCountMap.getOrDefault(uniqueKey, 0) + 1);
        }

        // 2. 找出重复的唯一标识符
        Set<String> duplicateKeys = uniqueKeyCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1) // 出现次数大于 1 的为重复
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 3. 将导入数据转换为 CustomerItemsDTO 列表
        List<CustomerItemsDTO> customerItemsDTOs = batch.stream()
                .map(dto -> BeanUtil.copyProperties(dto, CustomerItemsDTO.class))
                .collect(Collectors.toList());

        // 4. 批量查询数据库
        List<CustomerItemsDTO> queryResults = repository.checkItemsExistBatch(customerItemsDTOs);

        // 5. 将查询结果转换为 Set<String>，表示已存在的唯一标识符
        Set<String> existenceKeys = queryResults.stream()
                .map(this::generateUniqueKey)
                .collect(Collectors.toSet());

        // 6. 遍历批次数据进行校验
        for (CustomerImportDTO dto : batch) {
            // 获取之前的校验结果
            StringBuilder sb = new StringBuilder(dto.getCheckResult());

            // 动态生成唯一标识符
            CustomerItemsDTO tempDto = BeanUtil.copyProperties(dto, CustomerItemsDTO.class);
            String uniqueKey = generateUniqueKey(tempDto);

            // 校验是否在数据库存在
            if (existenceKeys.contains(uniqueKey)) {
                sb.append(Constant.CUSTOMER_ITEMS_EXIST);
            }

            // 校验是否在导入数据中重复
            if (duplicateKeys.contains(uniqueKey)) {
                sb.append(Constant.DUPLICATE_DATA_WARNING);
            }

            // 设置合并后的校验结果到 checkResult 字段
            dto.setCheckResult(sb.toString());
        }
    }


    /**
     * 动态生成唯一标识符
     *
     * @param customerItemsDTO 客户项目信息实体类
     * @return 生成的唯一标识符
     */
    private String generateUniqueKey(CustomerItemsDTO customerItemsDTO) {
        // 从实体类中获取所需参数
        String projectType = customerItemsDTO.getProjectType();
        String zteCode = customerItemsDTO.getZteCode();
        String customerName = customerItemsDTO.getCustomerName();
        String zteSupplier = customerItemsDTO.getZteSupplier();
        String zteBrandStyle = customerItemsDTO.getZteBrandStyle();
        String projectName = customerItemsDTO.getProjectName();

        // 默认的唯一标识符
        String uniqueKey = zteCode + Constant.PIPE + projectType;

        if (Constant.STRING_FOUR.equals(projectType)) {
            // 如果 project_type 是 4，拼接 customer_name, zte_supplier, zte_Brand_Style
            uniqueKey += Constant.PIPE
                    + safeValue(customerName)
                    + Constant.PIPE
                    + safeValue(zteSupplier)
                    + Constant.PIPE
                    + safeValue(zteBrandStyle);
        } else if (Constant.STRING_FIVE.equals(projectType)) {
            // 如果 project_type 是 5，拼接 customer_name, zte_Brand_Style
            uniqueKey += Constant.PIPE
                    + safeValue(customerName)
                    + Constant.PIPE
                    + safeValue(zteBrandStyle);
        } else if (Constant.STRING_THREE.equals(projectType)) {
            // 如果 project_type 是 3，拼接 customer_name, project_name
            uniqueKey += Constant.PIPE
                    + safeValue(customerName)
                    + Constant.PIPE
                    + safeValue(projectName);
        }

        return uniqueKey;
    }

    /**
     * 处理空值的安全方法
     *
     * @param value 待处理的值
     * @return 字符串值，如果为空则返回""
     */
    private String safeValue(Object value) {
        return value == null ? Constant.STRING_EMPTY : value.toString();
    }


    /**
     * 批量导入写数据
     * @param reCheckResult
     * @param list
     * @param empNo
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<CustomerImportDTO> reCheckResult , List<CustomerImportDTO> list, String empNo) throws Exception {
        // 校验任务批次
        List<CustomerImportDTO> customerImportDTOS = this.checkTaskBatch(reCheckResult );

        // 校验是否有检查错误
        for (CustomerImportDTO customerImportDTO : customerImportDTOS) {
            if (!StringUtils.isEmpty(customerImportDTO.getCheckResult())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CHECK_ERROR_PLEASE_RESELECT_FILE_PARSING);
            }
        }

        // 按 100 条数据分批处理
        int batchSize = 100;
        List<CustomerItemsDTO> customerItemsBatch = new ArrayList<>();
        List<CustomerImportDTO> customerImportBatch = new ArrayList<>();

        // 锁集合
        List<String> lockKeys = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            CustomerImportDTO customerImportDTO = list.get(i);

            // 构建 CustomerItemsDTO
            CustomerItemsDTO dto = BeanUtil.copyProperties(customerImportDTO, CustomerItemsDTO.class);
            dto.setId(idGenerator.snowFlakeIdStr());
            dto.setStatus(Constant.FLAG_Y);
            dto.setRemark(Constant.STRING_EMPTY);
            dto.setCreateBy(empNo);
            dto.setLastUpdatedBy(empNo);

            // 校验参数并生成锁 Key
            StringBuilder sb = new StringBuilder();
            String key = this.checkNewParams(dto, sb);
            lockKeys.add(key);

            // 加入批次集合
            customerItemsBatch.add(dto);
            customerImportBatch.add(customerImportDTO);

            // 每 100 条数据处理一次
            if ((i + 1) % batchSize == 0 || i == list.size() - 1) {
                // 加锁
                if (!lockKeys.isEmpty()) {
                    RedisLock redisLock = new RedisLock(lockKeys.toString());
                    if (!redisLock.lock()) {
                        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE);
                    }
                    try {
                        // 批量新增属性表信息
                        this.batchInsertCustomerParams(customerItemsBatch, customerImportBatch);

                        // 批量新增 CustomerItems
                        repository.batchInsertCustomerItems(customerItemsBatch);
                    } finally {
                        redisLock.unlock();
                    }
                }

                // 清空批次集合
                customerItemsBatch.clear();
                customerImportBatch.clear();
                lockKeys.clear();
            }
        }
    }

    public void batchInsertCustomerParams(List<CustomerItemsDTO> customerItemsBatch, List<CustomerImportDTO> customerImportBatch) {
        Map<String, List<Object>> paramsMap = new HashMap<>();

        // 遍历客户项和导入数据，构建分组数据
        for (int i = 0; i < customerItemsBatch.size(); i++) {
            CustomerItemsDTO dto = customerItemsBatch.get(i);
            CustomerImportDTO customerImportDTO = customerImportBatch.get(i);

            String tableName = customerImportDTO.getTableName();
            String jsonDetail = JSON.toJSONString(customerImportDTO);
            String id = idGenerator.snowFlakeIdStr();
            dto.setAdditionalInfoId(id);

            // 处理并按表分组
            handleTableDataGrouping(paramsMap, dto, tableName, jsonDetail, id);
        }

        // 批量插入各属性表
        insertCustomerParamsByTable(paramsMap);
    }

    private void handleTableDataGrouping(Map<String, List<Object>> paramsMap, CustomerItemsDTO dto, String tableName, String jsonDetail, String id) {
        switch (tableName) {
            case Constant.CustomerParamsTable.CPU_INFO: {
                CpuInfoDTO cpuInfoDTO = JSON.parseObject(jsonDetail, CpuInfoDTO.class);
                cpuInfoDTO.setId(id);
                cpuInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                cpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.CPU_INFO, k -> new ArrayList<>()).add(cpuInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.MEMORY_INFO: {
                MemoryInfoDTO memoryInfoDTO = JSON.parseObject(jsonDetail, MemoryInfoDTO.class);
                memoryInfoDTO.setId(id);
                memoryInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                memoryInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.MEMORY_INFO, k -> new ArrayList<>()).add(memoryInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.NETWORK_CARD_INFO: {
                NetworkCardInfoDTO networkCardInfoDTO = JSON.parseObject(jsonDetail, NetworkCardInfoDTO.class);
                networkCardInfoDTO.setId(id);
                networkCardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                networkCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.NETWORK_CARD_INFO, k -> new ArrayList<>()).add(networkCardInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.RAID_CARD_INFO: {
                RaidCardInfoDTO raidCardInfoDTO = JSON.parseObject(jsonDetail, RaidCardInfoDTO.class);
                raidCardInfoDTO.setId(id);
                raidCardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                raidCardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.RAID_CARD_INFO, k -> new ArrayList<>()).add(raidCardInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.HARD_DISK_INFO: {
                HardDiskInfoDTO hardDiskInfoDTO = JSON.parseObject(jsonDetail, HardDiskInfoDTO.class);
                hardDiskInfoDTO.setId(id);
                hardDiskInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                hardDiskInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.HARD_DISK_INFO, k -> new ArrayList<>()).add(hardDiskInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.GPU_INFO: {
                GpuInfoDTO gpuInfoDTO = JSON.parseObject(jsonDetail, GpuInfoDTO.class);
                gpuInfoDTO.setId(id);
                gpuInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                gpuInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.GPU_INFO, k -> new ArrayList<>()).add(gpuInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.MOTHERBOARD_INFO: {
                MotherboardInfoDTO motherboardInfoDTO = JSON.parseObject(jsonDetail, MotherboardInfoDTO.class);
                motherboardInfoDTO.setId(id);
                motherboardInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                motherboardInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.MOTHERBOARD_INFO, k -> new ArrayList<>()).add(motherboardInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.MACHINE_INFO: {
                MachineInfoDTO machineInfoDTO = JSON.parseObject(jsonDetail, MachineInfoDTO.class);
                machineInfoDTO.setId(id);
                machineInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                machineInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.MACHINE_INFO, k -> new ArrayList<>()).add(machineInfoDTO);
                break;
            }
            case Constant.CustomerParamsTable.POWER_SOURCE: {
                PowerSourceInfoDTO powerSourceInfoDTO = JSON.parseObject(jsonDetail, PowerSourceInfoDTO.class);
                powerSourceInfoDTO.setId(id);
                powerSourceInfoDTO.setCreateBy(dto.getLastUpdatedBy());
                powerSourceInfoDTO.setLastUpdatedBy(dto.getLastUpdatedBy());
                paramsMap.computeIfAbsent(Constant.CustomerParamsTable.POWER_SOURCE, k -> new ArrayList<>()).add(powerSourceInfoDTO);
                break;
            }
            default:
                break;
        }
    }

    public void insertCustomerParamsByTable(Map<String, List<Object>> paramsMap) {
        paramsMap.forEach((tableName, paramListPre) -> {
            switch (tableName) {
                case Constant.CustomerParamsTable.CPU_INFO:
                    List<CpuInfoDTO> cpuInfoDTO = paramListPre.stream()
                            .map(obj -> (CpuInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertCpuInfo(cpuInfoDTO);
                    break;

                case Constant.CustomerParamsTable.MEMORY_INFO:
                    List<MemoryInfoDTO> memoryInfoDTO = paramListPre.stream()
                            .map(obj -> (MemoryInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertMemoryInfo(memoryInfoDTO);
                    break;

                case Constant.CustomerParamsTable.NETWORK_CARD_INFO:
                    List<NetworkCardInfoDTO> networkCardInfoDTO = paramListPre.stream()
                            .map(obj -> (NetworkCardInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertNetworkCardInfo(networkCardInfoDTO);
                    break;

                case Constant.CustomerParamsTable.RAID_CARD_INFO:
                    List<RaidCardInfoDTO> raidCardInfoDTO = paramListPre.stream()
                            .map(obj -> (RaidCardInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertRaidCardInfo(raidCardInfoDTO);
                    break;

                case Constant.CustomerParamsTable.HARD_DISK_INFO:
                    List<HardDiskInfoDTO> hardDiskInfoDTO = paramListPre.stream()
                            .map(obj -> (HardDiskInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertHardDiskInfo(hardDiskInfoDTO);
                    break;

                case Constant.CustomerParamsTable.GPU_INFO:
                    List<GpuInfoDTO> gpuInfoDTO = paramListPre.stream()
                            .map(obj -> (GpuInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertGpuInfo(gpuInfoDTO);
                    break;

                case Constant.CustomerParamsTable.MOTHERBOARD_INFO:
                    List<MotherboardInfoDTO> motherboardInfoDTO = paramListPre.stream()
                            .map(obj -> (MotherboardInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertMotherboardInfo(motherboardInfoDTO);
                    break;

                case Constant.CustomerParamsTable.MACHINE_INFO:
                    List<MachineInfoDTO> machineInfoDTO = paramListPre.stream()
                            .map(obj -> (MachineInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertMachineInfo(machineInfoDTO);
                    break;

                case Constant.CustomerParamsTable.POWER_SOURCE:
                    List<PowerSourceInfoDTO> powerSourceInfoDTO = paramListPre.stream()
                            .map(obj -> (PowerSourceInfoDTO) obj)
                            .collect(Collectors.toList());
                    customerItemsParamsRepository.batchInsertPowerSourceInfo(powerSourceInfoDTO);
                    break;
                default:
                    break;
            }
        });
    }


    @Override
    public List<CustomerItemsDTO> queryListByCustomerAndItemNoList(CustomerItemsDTO customerItemsDTO) {
        List<CustomerItemsDTO> resultList = new ArrayList<>();
        List<String> itemNoList = customerItemsDTO.getItemNoList();
        CustomerItemsDTO customerItems = new CustomerItemsDTO();
        customerItems.setCustomerName(customerItemsDTO.getCustomerName());
        for (List<String> itemNoTempList : CommonUtils.splitList(itemNoList, Constant.BATCH_SIZE)) {
            customerItems.setItemNoList(itemNoTempList);
            List<CustomerItemsDTO> tempList = repository.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItems);
            resultList.addAll(tempList);
        }
        return resultList;
    }

    @Override
    public Integer deleteCustomerItemsByZteCodes(List<String> zteCodes){
        if (CollUtil.isEmpty(zteCodes)){
            return 0;
        }
        return repository.deleteCustomerItemsByZteCodes(zteCodes);
    }

    @Override
    public List<CustomerItemsDTO> queryListByZteCodes(List<String> zteCodes){
        if (CollUtil.isEmpty(zteCodes)){
            return Collections.emptyList();
        }
        return repository.queryListByZteCodes(zteCodes);
    }


    @Override
    public List<CustomerItemsDTO> getZteCodeOfSameItem(List<String> itemSupplierNoList) {
        if (CollectionUtils.isEmpty(itemSupplierNoList)) {
            return new ArrayList<>();
        }
        List<SysLookupValues> lookupValues = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115);
        List<String> specificCustomerNoList = lookupValues.stream().map(SysLookupValues::getLookupMeaning)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        return repository.getZteCodeOfSameItem(itemSupplierNoList, specificCustomerNoList);
    }
}
