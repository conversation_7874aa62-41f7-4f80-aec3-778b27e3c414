package com.zte.application.impl;

import com.zte.application.ResourceDetailService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.ResourceDetail;
import com.zte.domain.model.ResourceDetailRepository;
import com.zte.interfaces.dto.ResourceApplicationRecordDTO;
import com.zte.interfaces.dto.ResourceDetailDTO;
import com.zte.interfaces.dto.ResourceDetailEntityDTO;
import com.zte.interfaces.dto.ResourceInfoDetailEntityDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.interfaces.dto.TechnicalChangeExecInfoEntityDTO;
import com.zte.interfaces.dto.bytedance.GpuInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Future;

@Service
@Log4j2
public class ResourceDetailServiceImpl implements ResourceDetailService {

    @Autowired
    private ResourceDetailRepository resourceDetailRepository;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;


    @Override
    public long getResourceDetailCount(ResourceDetailDTO record) throws Exception{
        if(CollectionUtils.isEmpty(record.getApplyIdList())){
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        return resourceDetailRepository.getResourceDetailCount(record);
    }

    @Override
    public List<ResourceDetail> getPageList(ResourceDetailDTO record) throws Exception{
        if(Objects.isNull(record)){
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        return resourceDetailRepository.getPageList(record);
    }

    @Override
    public Page<ResourceDetailEntityDTO> pageList(ResourceDetailEntityDTO record) throws Exception{
        Page<ResourceDetailEntityDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<ResourceDetailEntityDTO> resourceDetailList = resourceDetailRepository.pageList(pageInfo);
        pageInfo.setRows(resourceDetailList);
        return pageInfo;
    }

    @Override
    public int save(ResourceDetailEntityDTO record) throws Exception{
        return 0;
    }

    @Override
    public int update(ResourceDetailEntityDTO record) throws Exception{
        return 0;
    }

    @Override
    public void updateBatchOfStatus(ResourceDetail resourceDetail) {
        resourceDetailRepository.updateBatchOfStatus(resourceDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long batchInsert(List<ResourceDetailEntityDTO> recordList) throws Exception
    {
        long result = Constant.INT_0;
        List<List<ResourceDetailEntityDTO>> detailEntryList = CommonUtils.splitList(recordList, Constant.BATCH_SIZE_200);
        List<Future<Integer>> futureList = new ArrayList<>();
        for(List<ResourceDetailEntityDTO> child : detailEntryList)
        {
            Future<Integer> future = taskExecutor.submit(() -> {
                long startDate = System.currentTimeMillis();
                try
                {
                    resourceDetailRepository.batchInsert(child);
                }
                catch (Exception e)
                {
                    String errMsg = StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > Constant.INT_800 ?
                            e.getMessage().substring(Constant.INT_0 , Constant.INT_800) : e.getMessage();
                    insertRecord(child, errMsg);
                }
                return Constant.INT_1;
            });
            futureList.add(future);
        }
        for(Future<Integer> future : futureList)
        {
            if(future == null || future.get() == null)
            {
                continue;
            }
            result += future.get();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertDetailInfo(List<ResourceDetailEntityDTO> detailsList) throws Exception {
        List<List<ResourceDetailEntityDTO>> splitList = CommonUtils.splitList(detailsList, Constant.BATCH_SIZE_100);
        for (List<ResourceDetailEntityDTO> detailList : splitList) {
            resourceDetailRepository.batchInsert(detailList);
        }
    }

    /**
     *
     * @param child 请求数量
     * @param errMsg
     */
    public void insertRecord(List<ResourceDetailEntityDTO> child, String errMsg) {
        ResourceDetailEntityDTO detailEntityDTO = child.get(Constant.INT_0);
        ResourceDetailEntityDTO endEntityDTO = child.get(child.size() - Constant.INT_1);
        ResourceApplicationRecordDTO recordDTO = new ResourceApplicationRecordDTO();
        recordDTO.setId(UUID.randomUUID().toString());
        recordDTO.setApplyId(detailEntityDTO.getApplyId());
        recordDTO.setResourceStart(detailEntityDTO.getResourceStart());
        recordDTO.setResourceEnd(endEntityDTO.getResourceStart());
        recordDTO.setResourceNo(String.valueOf(child.size()));
        recordDTO.setErrorMessage(errMsg);
        resourceDetailRepository.insertRecord(recordDTO);
    }

    @Override
    public long searchRecord() {
        return resourceDetailRepository.searchRecord();
    }
}
