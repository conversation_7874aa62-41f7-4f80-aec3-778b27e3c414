package com.zte.application.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.NetworkLicenseService;
import com.zte.common.CommonUtils;
import com.zte.common.DataPusher;
import com.zte.common.enums.ResourceStatusEnum;
import com.zte.common.excel.SimpleAnalysisEventListener;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NetworkB2B;
import com.zte.common.utils.NetworkLicenseUploadResultB2B;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.NetworkLicenseExcelBO;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoDetailTempRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.ResourceUseInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.NetworkLicenseBindingDTO;
import com.zte.interfaces.dto.NetworkLicenseUploadResultDTO;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.interfaces.dto.ResourceUseInfoDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.km.udm.common.httpclient.HttpClientUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.EXCEL_RESOLUTION_FAILURE;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_BINDING_ALLOCATED;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_BINDING_ISNULL;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_BINDING_REPEATED;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_CERT_NAME;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_FILE_ERROR;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_FILE_IMPORT;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_FILE_MAX;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_FILE_NAME_ERROR;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_RESOURCE_TYPE;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_SIGN_EXITS;
import static com.zte.common.model.MessageId.NETWORK_LICENSE_SIGN_FILE_EXITS;
import static com.zte.common.model.MessageId.RESOURCE_MATCH_FAILED;
import static com.zte.common.model.MessageId.RESOURCE_WARNING_RESOURCE_NO_IS_NOT_EMPTY;
import static com.zte.common.model.NumConstant.NUM_SIX;
import static com.zte.common.model.NumConstant.STR_ZERO;
import static com.zte.common.utils.BusinessConstant.HTTP_HEAD_X_FACTORY_ID;
import static com.zte.common.utils.Constant.BATCH_SIZE_500;
import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.INT_1000;
import static com.zte.common.utils.Constant.INT_300000;
import static com.zte.common.utils.Constant.INT_31;
import static com.zte.common.utils.Constant.MADE_IN;
import static com.zte.common.utils.Constant.NETWORK_LICENSE_SIGN_IMPORT_LOCK;
import static com.zte.common.utils.Constant.NETWORK_LICENSE_SIGN_LOCK;
import static com.zte.common.utils.Constant.NUM_TWO;
import static com.zte.common.utils.Constant.NetWorkUploadResult;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.Constant.STR_ONE;
import static com.zte.common.utils.Constant.STR_SPLIT;
import static com.zte.common.utils.Constant.SUCCESS;
import static com.zte.common.utils.Constant.SUCCESS_CODE_0000;
import static com.zte.common.utils.Constant.SYMBOL_COLON;
import static com.zte.common.utils.IntegerConstant.INT_EIGHTEEN;
import static com.zte.common.utils.MpConstant.CERT_NAME;
import static com.zte.common.utils.MpConstant.NAL;
import static com.zte.common.utils.MpConstant.NET_LICENSE;
import static com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE;

/**
 * <AUTHOR>
 * @description 入网证
 * @date 2023/9/15
 */
@Slf4j
@Service
public class NetworkLicenseServiceImpl implements NetworkLicenseService {

    @Autowired
    private ResourceInfoRepository resourceInfoRepository;
    @Autowired
    private ResourceInfoDetailTempRepository resourceInfoDetailTempRepository;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ResourceUseInfoRepository resourceUseInfoRepository;
    @Autowired
    private ResourceInfoDetailRepository resourceInfoDetailRepository;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private IMESLogService imesLogService;
    @Autowired
    private NetworkB2B networkB2B;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    private NetworkLicenseUploadResultB2B networkLicenseUploadResultB2B;
    @Autowired
    private EmailUtils emailUtils;
    @Autowired
    private SysLookupValuesServiceImpl sysLookupValuesService;

    public static String SIGN_IMPORT_ERROR_KEY = "NETWORK_LICENSE_SIGN_IMPORT_ERROR:";


    @Override
    public List<NetworkLicenseExcelBO> importResult(MultipartFile file) {
        List<NetworkLicenseExcelBO> result = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), NetworkLicenseExcelBO.class, new SimpleAnalysisEventListener<>(result)).sheet().doRead();
        } catch (Exception e) {
            throw new MesBusinessException(BUSINESSERROR_CODE, EXCEL_RESOLUTION_FAILURE, new Object[]{getErrMsg(e)});
        }
        checkLicense(result, false);
        setDate(result);
        return result;
    }

    @Override
    public void save(List<NetworkLicenseExcelBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        checkLicense(list, true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        String factoryId = MESHttpHelper.getHttpRequestHeader().get(HTTP_HEAD_X_FACTORY_ID);

        for (NetworkLicenseExcelBO bo : list) {
            ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
            BeanUtils.copyProperties(bo, dto);
            dto.setResourceId(UUID.randomUUID().toString());
            dto.setResourceType(NAL);
            dto.setFactoryId(Integer.valueOf(factoryId));
            dto.setCreateBy(empNo);
            dto.setLastUpdatedBy(empNo);
            dto.setResourceStatus(ResourceStatusEnum.INIT.name());
            dto.setResourceAmount(BigInteger.ZERO);
            dto.setAvailableQuantity(BigInteger.ZERO);
            resourceInfoEntityDTOList.add(dto);
        }

        Lists.partition(resourceInfoEntityDTOList, NumConstant.NUM_100).forEach(e -> resourceInfoRepository.batchInsert(e));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String binding(NetworkLicenseBindingDTO bindingDTO) {
        RedisLock redisLock = new RedisLock(NETWORK_LICENSE_SIGN_LOCK + bindingDTO.getBarcode(), NumConstant.NUM_60 * NumConstant.NUM_60);
        if (!redisLock.lock()) {
            throw new MesBusinessException(BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK);
        }

        try {
            ResourceInfoDetailDTO resourceInfoDetailDTO = resourceInfoDetailRepository.queryResourceNoByNum(bindingDTO.getResourceNum());
            if (null == resourceInfoDetailDTO || StringUtils.isNotBlank(resourceInfoDetailDTO.getBusinessId())) {
                throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_BINDING_ISNULL, new Object[]{bindingDTO.getResourceNum()});
            }

            if (!STR_ONE.equals(resourceInfoDetailDTO.getStatus())) {
                throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_BINDING_ALLOCATED, new Object[]{bindingDTO.getResourceNum()});
            }
            String resourceNo = resourceInfoDetailDTO.getResourceNo();
            if (StringUtils.isBlank(resourceNo)) {
                throw new MesBusinessException(BUSINESSERROR_CODE, RESOURCE_WARNING_RESOURCE_NO_IS_NOT_EMPTY);
            }

            insertUseInfo(bindingDTO, resourceNo);

            ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
            resourceInfoEntityDTO.setResourceNo(resourceNo);
            return resourceInfoRepository.getList(resourceInfoEntityDTO).get(0).getCertName();
        } finally {
            redisLock.unlock();
        }
    }

    private void insertUseInfo(NetworkLicenseBindingDTO bindingDTO, String resourceNo) {
        ResourceUseInfoDTO useQuery = new ResourceUseInfoDTO();
        useQuery.setBarcode(bindingDTO.getBarcode());
        useQuery.setResourceNo(resourceNo);
        useQuery.setBarcodeType(STR_ZERO);
        List<ResourceUseInfoDTO> resourceUses = resourceUseInfoRepository.queryPage(useQuery);
        if (CollectionUtils.isNotEmpty(resourceUses)) {
            throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_BINDING_REPEATED, new Object[]{bindingDTO.getBarcode(), resourceNo});
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());

        ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
        BeanUtils.copyProperties(bindingDTO, resourceUseInfoDTO);
        resourceUseInfoDTO.setId(idGenerator.snowFlakeId());
        resourceUseInfoDTO.setResourceNo(resourceNo);
        resourceUseInfoDTO.setBindWay(STR_ONE);
        resourceUseInfoDTO.setBarcodeType(STR_ZERO);
        resourceUseInfoDTO.setRegistStatus(STR_ZERO);
        resourceUseInfoDTO.setReportStatus(STR_ZERO);
        String deviceNo = resourceNo.substring(resourceNo.length() - 6);
        resourceUseInfoDTO.setDeviceTypeCode(deviceNo);
        resourceUseInfoDTO.setReportStatus(STR_ZERO);
        resourceUseInfoDTO.setCreateBy(empNo);
        resourceUseInfoDTO.setLastUpdatedBy(empNo);
        resourceUseInfoRepository.insertEntity(resourceUseInfoDTO);
        resourceInfoDetailRepository.batchBinding(Collections.singletonList(bindingDTO.getResourceNum()));
    }

    @Override
    @SuppressWarnings("squid:S3776")
    public String signImport(MultipartFile file) {
        ResourceInfoEntityDTO resourceInfo = getResourceNo(file);
        String redisErrorKey = UUID.randomUUID() + SIGN_IMPORT_ERROR_KEY;

        RedisLock redisLock = new RedisLock(NETWORK_LICENSE_SIGN_IMPORT_LOCK, NumConstant.NUM_60 * NumConstant.NUM_60);
        if (!redisLock.lock()) {
            throw new MesBusinessException(BUSINESSERROR_CODE, MessageId.NETWORK_LICENSE_SIGN_LOCK_ERROR);
        }

        MESHttpHelper.resetHttpRequestHeader();
        resourceInfoDetailTempRepository.truncateTemp();
        ThreadUtil.EXECUTOR.execute(() -> {
            int count = 0;
            List<String> lines = new ArrayList<>();
            try (InputStreamReader read = new InputStreamReader((file.getInputStream()), StandardCharsets.UTF_8); BufferedReader bufferedReader = new BufferedReader(read)) {
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    String trimLine = line.trim();
                    if (trimLine.length() != INT_31) {
                        continue;
                    }
                    lines.add(trimLine);
                    count++;
                    if (count > INT_300000) {
                        redisTemplate.opsForValue().set(redisErrorKey, CommonUtils.getLmbMessage(NETWORK_LICENSE_FILE_MAX), 300L, TimeUnit.SECONDS);
                        throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_FILE_MAX);
                    }

                    if (lines.size() == BATCH_SIZE_500) {
                        handleSign(lines, resourceInfo.getResourceNo(), redisErrorKey);
                        lines.clear();
                    }
                }
                handleSign(lines, resourceInfo.getResourceNo(), redisErrorKey);
                resourceInfoDetailRepository.insertByTemp();
                resourceInfoRepository.updateAvailableQuantityByNal(resourceInfo.getResourceNo());
                resourceInfo.setResourceAmount(resourceInfo.getResourceAmount().add(BigInteger.valueOf(count)));
                resourceInfoRepository.updateAmount(resourceInfo);
                redisTemplate.opsForValue().set(redisErrorKey, SUCCESS, 300L, TimeUnit.SECONDS);
            } catch (MesBusinessException ignored) {
            } catch (Exception e) {
                redisTemplate.opsForValue().set(redisErrorKey, CommonUtils.getLmbMessage(NETWORK_LICENSE_FILE_IMPORT) + e.getMessage(), 300L, TimeUnit.SECONDS);
                imesLogService.log(e.getMessage(),"资源文件导入失败");
                throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_FILE_IMPORT);
            } finally {
                redisLock.unlock();
            }
        });

        MESHttpHelper.removeHttpRequestHeader();
        return redisErrorKey;
    }

    private ResourceInfoEntityDTO getResourceNo(MultipartFile file) {
        String filename = file.getOriginalFilename();
        //文件名前14位为资源编号加上后缀.txt,也就是文件名至少18位
        if (StringUtils.isBlank(filename) || filename.length() < INT_EIGHTEEN) {
            throw new MesBusinessException(BUSINESSERROR_CODE, NETWORK_LICENSE_FILE_NAME_ERROR);
        }
        String resourceNo = filename.substring(0, 14);
        List<ResourceInfoEntityDTO> resource = resourceInfoRepository.getListByResource(Collections.singletonList(resourceNo));
        if (CollectionUtils.isEmpty(resource)) {
            throw new MesBusinessException(BUSINESSERROR_CODE, MessageId.NETWORK_LICENSE_RESOURCE_NOT_FOUNT);
        }
        return resource.get(0);
    }

    @Override
    public void resourceFailure() {
        Integer totalPage = null;
        for (int i = 1; ; i++) {
            Page pageInfo = new Page<>(i, 500);
            if (null != totalPage) {
                pageInfo.setSearchCount(false);
            }
            List<ResourceInfoEntityDTO> infoEntityDTOS = resourceInfoRepository.pageQueryExpiryData(pageInfo);

            if (null == totalPage) {
                totalPage = pageInfo.getTotalPage();
            }
            if (CollectionUtils.isEmpty(infoEntityDTOS)) {
                return;
            }

            resourceFailure(infoEntityDTOS);
            if (i >= totalPage) {
                break;
            }
        }
    }

    private void resourceFailure(List<ResourceInfoEntityDTO> infoEntityDTOS) {
        List<ResourceInfoEntityDTO> result = new ArrayList<>();
        for (ResourceInfoEntityDTO dto : infoEntityDTOS) {
            LocalDate expiryDate = dto.getExpiryDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate nowData = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            long between = ChronoUnit.DAYS.between(nowData, expiryDate);
            if (between < 1) {
                result.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            List<String> ids = result.stream().map(ResourceInfoEntityDTO::getResourceId).collect(Collectors.toList());
            resourceInfoRepository.batchFailure(ids);
        }
    }

    private void handleSign(List<String> signList, String resourceNo, String redisErrorKey) {
        if (CollectionUtils.isEmpty(signList)) {
            return;
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        List<ResourceInfoDetailDTO> result = new ArrayList<>();
        Set<String> numSet = new HashSet<>();
        List<String> errorList = new ArrayList<>();
        for (String sign : signList) {
            String[] fields = sign.split(STR_SPLIT);
            if (fields.length != NUM_TWO) {
                redisTemplate.opsForValue().set(redisErrorKey, sign + CommonUtils.getLmbMessage(NETWORK_LICENSE_FILE_ERROR), 300L, TimeUnit.SECONDS);
                throw new MesBusinessException(BUSINESSERROR_CODE, sign + CommonUtils.getLmbMessage(NETWORK_LICENSE_FILE_ERROR));
            }
            boolean isAdd = numSet.add(fields[0]);
            if (!isAdd) {
                errorList.add(fields[0]);
            }
            ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
            resourceInfoDetailDTO.setResourceNum(fields[0]);
            resourceInfoDetailDTO.setScramblingCode(fields[1]);
            resourceInfoDetailDTO.setResourceNo(resourceNo);
            resourceInfoDetailDTO.setCreateBy(empNo);
            resourceInfoDetailDTO.setLastUpdatedBy(empNo);
            resourceInfoDetailDTO.setStatus(STR_ZERO);
            result.add(resourceInfoDetailDTO);
        }
        List<ResourceInfoDetailDTO> detailList = resourceInfoDetailRepository.getListByResourceNum(numSet);
        List<ResourceInfoDetailDTO> detailTempList = resourceInfoDetailTempRepository.getListByResourceNum(numSet);
        if (CollectionUtils.isNotEmpty(detailList)) {
            String num = detailList.stream().map(ResourceInfoDetailDTO::getResourceNum).collect(Collectors.joining(STR_SPLIT));
            resourceInfoDetailTempRepository.truncateTemp();
            redisTemplate.opsForValue().set(redisErrorKey, num + CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_EXITS), 300L, TimeUnit.SECONDS);
            throw new MesBusinessException(BUSINESSERROR_CODE, num + CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_EXITS));
        }
        if (CollectionUtils.isNotEmpty(detailTempList) || !errorList.isEmpty()) {
            String num = detailList.stream().map(ResourceInfoDetailDTO::getResourceNum).collect(Collectors.joining(STR_SPLIT)) + String.join(STR_SPLIT, errorList);
            resourceInfoDetailTempRepository.truncateTemp();
            redisTemplate.opsForValue().set(redisErrorKey, num + CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_FILE_EXITS), 300L, TimeUnit.SECONDS);
            throw new MesBusinessException(BUSINESSERROR_CODE, num + CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_FILE_EXITS));
        }
        resourceInfoDetailTempRepository.insertEntities(result);
    }


    private void checkLicense(List<NetworkLicenseExcelBO> result, boolean isInsert) {
        if (result.size() > INT_1000) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_MSGID, MessageId.RESOURCE_WARNING_IMPORT_MAX_1000);
        }
        Map<String, List<NetworkLicenseExcelBO>> resultMap = result.stream().filter(e -> StringUtils.isNotBlank(e.getResourceNo())).collect(Collectors.groupingBy(NetworkLicenseExcelBO::getResourceNo));
        if (null == resultMap || resultMap.size() == 0) {
            throw new MesBusinessException(BUSINESSERROR_CODE, MessageId.RESOURCE_WARNING_RESOURCE_NO_IS_NOT_EMPTY);
        }
        List<ResourceInfoEntityDTO> resourceList = resourceInfoRepository.getListByResource(new ArrayList<>(resultMap.keySet()));
        Set<String> resourceNos = resourceList.stream().map(ResourceInfoEntityDTO::getResourceNo).collect(Collectors.toSet());
        for (NetworkLicenseExcelBO bo : result) {
            StringBuilder errMsg = new StringBuilder();
            try {
                checkAll(bo, errMsg);
            } catch (Exception e) {
                errMsg.append(e.getMessage());
            }
            // Excel类资源号重复及数据库重复校验
            resourceCheck(resultMap, resourceNos, bo, errMsg);
            if (!Arrays.asList(CERT_NAME).contains(bo.getCertName())) {
                errMsg.append(CommonUtils.getLmbMessage(NETWORK_LICENSE_CERT_NAME));
            }
            if (!NET_LICENSE.equals(bo.getResourceType())) {
                errMsg.append(CommonUtils.getLmbMessage(NETWORK_LICENSE_RESOURCE_TYPE));
            }
            bo.setErrorMsg(errMsg.toString());
            if (isInsert && StringUtils.isNotBlank(errMsg)) {
                throw new MesBusinessException(BUSINESSERROR_CODE, errMsg.toString());
            }
        }
    }

    private void resourceCheck(Map<String, List<NetworkLicenseExcelBO>> resultMap, Set<String> resourceNos, NetworkLicenseExcelBO bo, StringBuilder errMsg) {
        if (StringUtils.isBlank(bo.getResourceNo())) {
            return;
        }
//        Pattern pattern = Pattern.compile(NAL_PATTERN);
//        // 资源号段格式与资源类型不匹配
//        if (!pattern.matcher(bo.getResourceNo()).matches()) {
//            errMsg.append(CommonUtils.getLmbMessage(RESOURCE_MATCH_FAILED));
//        }
        if (bo.getResourceNo().length() < NUM_SIX) {
            errMsg.append(CommonUtils.getLmbMessage(RESOURCE_MATCH_FAILED));
        }
        if (resultMap.containsKey(bo.getResourceNo()) && resultMap.get(bo.getResourceNo()).size() > 1) {
            errMsg.append(bo.getResourceNo()).append(CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_FILE_EXITS));
        }
        if (resourceNos.contains(bo.getResourceNo())) {
            errMsg.append(bo.getResourceNo()).append(CommonUtils.getLmbMessage(NETWORK_LICENSE_SIGN_FILE_EXITS));
        }
    }

    private void checkAll(NetworkLicenseExcelBO bo, StringBuilder errMsg) throws IllegalAccessException {
        Field[] declaredFields = bo.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (ObjectUtil.isNotNull(annotation)) {
                field.setAccessible(true);
                Object value = field.get(bo);
                if (null == value) {
                    errMsg.append(annotation.value()[0]).append(CommonUtils.getLmbMessage(MessageId.DATA_IS_EMPTY));
                }
            }
        }
    }

    private String getErrMsg(Throwable throwable) {
        if (null == throwable.getCause()) {
            return throwable.getMessage();
        }
        return getErrMsg(throwable.getCause());
    }


    @Override
    public void fillProductAddress() throws Exception {
        // 获取没有地产信息的 资源
        List<ResourceInfoEntityDTO> list = resourceInfoRepository.selectNoAddressInfo();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // B2B获取地产信息
        DataPusher retryUtil = new DataPusher(networkB2B);
        // 填充地产信息
        for (ResourceInfoEntityDTO dto : list) {
            Map<String, String> params = new HashMap<>();
            params.put(Constant.SERVER_LICENCE, dto.getResourceNo());
			params.put(Constant.BIZ_CODE_UPPER, Constant.E100);
            retryUtil.pushData(params);
            String result = retryUtil.getResult();
            if(StringUtils.isBlank(result)){
            	continue;
			}
            this.updateProdAddress(result,dto);
        }
    }

    private void updateProdAddress(String result,ResourceInfoEntityDTO dto){
		String prodAddress = JSONObject.parseObject(result).getString(MADE_IN);
		//["广东省深圳市"]
		if(StringUtils.isNotBlank(prodAddress)){
			String src=StringUtils.strip(prodAddress,"[]");
			dto.setProdAddress(src.replace("\"", ""));
			resourceInfoRepository.updateProdAddress(dto);
		}
	}

    /**
     * 时间需要设置为23：59：59
     */
    public void setDate(List<NetworkLicenseExcelBO> result) {
        result.forEach(e -> {
            if (null == e.getExpiryDate()) {
                return;
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(e.getExpiryDate());
            calendar.add(Calendar.HOUR, 23);
            calendar.add(Calendar.MINUTE, 59);
            calendar.add(Calendar.SECOND, 59);
            e.setExpiryDate(calendar.getTime());
        });
    }


    @Override
    public void registrationObtain(ResourceUseInfoDTO resourceUseInfoDTO) throws Exception {
        // 1.获取资源类型为“入网许可”、上报状态为"已上报"，登记状态为"已登记"的使用记录（分页）
        resourceUseInfoDTO.setPage(NumConstant.NUM_ONE);
        resourceUseInfoDTO.setRows(NumConstant.NUM_3000);
        Page<ResourceUseInfoDTO> pageInfo = new Page<>(resourceUseInfoDTO.getPage(), resourceUseInfoDTO.getRows());
        pageInfo.setSearchCount(false);
        ResourceUseInfoDTO temp;
        while (true) {
            pageInfo.setParams(resourceUseInfoDTO);
            List<ResourceUseInfoDTO> records = resourceUseInfoRepository.queryNotResult(pageInfo);
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            temp = records.get(records.size() - 1);
            // 根据jobId挨个处理
            handleOneByOne(records);
            resourceUseInfoDTO.setStartTime(temp.getLastUpdatedDate());
            resourceUseInfoDTO.setId(temp.getId());
        }
    }

    public void handleOneByOne(List<ResourceUseInfoDTO> records) throws Exception {
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        Map<String, String> exceptionMap = new HashMap<>();
        List<String> jobIds = records.stream().map(ResourceUseInfoDTO::getJobId).distinct().collect(Collectors.toList());
        for (String jobId : jobIds) {
            try {
                // 调B2B接口获取返回值
                DataPusher retryUtil = new DataPusher(networkLicenseUploadResultB2B);
                Map<String, String> params = new HashMap<>();
                params.put(NetWorkUploadResult.JOB_ID, jobId);
                params.put(NetWorkUploadResult.BIZCODE, NetWorkUploadResult.BIZCODE_E102);
                retryUtil.pushData(params);
                // 区分返回值 1待处理，3处理完毕，9有问题的。只需要处理3,9
                NetworkLicenseUploadResultDTO result = JSONObject.parseObject(retryUtil.getResult(), NetworkLicenseUploadResultDTO.class);
                List<String> normalResultCode = Arrays.asList(NetWorkUploadResult.RESULT_VALID);
                if (null == result) {
                    exceptionMap.put(jobId, NetWorkUploadResult.B2B_RESP_ERROR_MSG);
                    continue;
                }
                if (!SUCCESS_CODE_0000.equals(result.getRspCode())) {
                    exceptionMap.put(jobId, result.getRemark());
                    continue;
                }
                if (!normalResultCode.contains(result.getResult())) {
                    continue;
                }
                handleB2BReturnFile(empNo, exceptionMap, jobId, result);
            } catch (Exception e) {
                exceptionMap.put(jobId, NetWorkUploadResult.B2B_CALLED_FAILED + e.getMessage());
            }
        }
        sendEmailWithFailedMsg(exceptionMap);
    }

    public void handleB2BReturnFile(String empNo, Map<String, String> exceptionMap, String jobId, NetworkLicenseUploadResultDTO result) {
        if (NumConstant.NUM_ZERO == result.getFailAmount()) {
            // jobId下全部成功
            ResourceUseInfoDTO updateResource = new ResourceUseInfoDTO();
            updateResource.setJobId(jobId);
            updateResource.setRegistStatus(NetWorkUploadResult.REGISTER_SUCCESS_STATUS);
            updateResource.setRegistMsg(NetWorkUploadResult.REGISTER_SUCCESS_MSG);
            updateResource.setLastUpdatedBy(empNo);
            resourceUseInfoRepository.updateRegistStatusByJobId(updateResource);
            resourceInfoDetailRepository.updateDetailStatusForJobIdUse(jobId);
        } else {
            // 根据返回的文档云ID下载文件
            String fileDownloadUrl = result.getFileName();
            if (StringUtils.isEmpty(fileDownloadUrl)) {
                exceptionMap.put(jobId, NetWorkUploadResult.B2B_RETURN_NO_FILEID);
                return;
            }
            // 读取文件内容
            log.info("登记结果返回的文档云下载链接" + fileDownloadUrl);
            readFile(empNo, exceptionMap, jobId, fileDownloadUrl);
        }
    }

    private void readFile(String empNo, Map<String, String> exceptionMap, String jobId, String fileDownloadUrl) {
        try {
            Map headerParamsMap = HttpClientUtil.initDiskHeaders(empNo);
            String filePath = FileUtils.createFilePathAndCheck(jobId);
            File file= new File(filePath);
            byte[] bytes = HttpClientUtil.getStream(fileDownloadUrl, headerParamsMap);
            org.apache.commons.io.FileUtils.writeByteArrayToFile(file, bytes);
            List<String> resultLines = new ArrayList<>();
            try (InputStream inputStream = new FileInputStream(file);InputStreamReader read = new InputStreamReader(inputStream, StandardCharsets.UTF_8); BufferedReader bufferedReader = new BufferedReader(read)) {
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    String trimLine = line.trim();
                    resultLines.add(trimLine);
                }
                // 处理每一行的数据，更新jobId下的使用记录登记状态和登记结果
                handlerLine(empNo, jobId, resultLines);
            } catch (Exception e) {
                exceptionMap.put(jobId, NetWorkUploadResult.PARSING_FILE_FAILED + e.getMessage());
            } finally {
                FileUtils.deleteFileWithParentDic(filePath);
            }
        } catch (Exception e) {
            exceptionMap.put(jobId, e.getMessage() + NetWorkUploadResult.DOWNLOAD_CLOUD_FILE_FAILED + fileDownloadUrl);
        }
    }

    public void sendEmailWithFailedMsg(Map<String, String> exceptionMap) throws Exception {
        if(null == exceptionMap || exceptionMap.size() == NumConstant.NUM_ZERO) {
            return;
        }
        StringBuilder contentCnForSendEmail = new StringBuilder("<br/><div>");
        contentCnForSendEmail.append("<p style='margin:0;font-size:12pt'>" + Constant.NetWorkUploadResult.EMAIL_TITLE + "</p>");
        for (Map.Entry<String, String> entry : exceptionMap.entrySet()) {
            contentCnForSendEmail.append("<p style='margin:0;font-size:10pt'>" +
                    Constant.NetWorkUploadResult.JOB_ID + Constant.SEMICOLON + entry.getKey() + Constant.COMMA +
                    Constant.NetWorkUploadResult.EMAIL_LINE_MSG_PRE + Constant.SEMICOLON + entry.getValue() + "</p>");
        }
        contentCnForSendEmail.append("<p style='margin:0;font-size:13pt'>" + STRING_EMPTY + "</p>");
        //需要处理的人发送邮件
        StringBuilder currentHandlerSb = new StringBuilder();
        List<SysLookupValues> sysList = sysLookupValuesService.findByLookupType(NetWorkUploadResult.LOOKUP_TYPE_286522);
        if (CollectionUtils.isNotEmpty(sysList)) {
            String emailHolder = sysList.stream().map(SysLookupValues::getLookupMeaning).collect(Collectors.joining(COMMA));
            if (StringUtils.isNotEmpty(emailHolder)) {
                String[] currentHandlerArr = emailHolder.split(Constant.COMMA);
                for (String currentHandlerStr : currentHandlerArr) {
                    currentHandlerSb.append(currentHandlerStr).append(Constant.MAILBOX_SUFFIX).append(Constant.COLON);
                }
                emailUtils.sendMail(currentHandlerSb.toString(), NetWorkUploadResult.EMAIL_TITLE, "", contentCnForSendEmail.toString(), "");
            }
        }
    }

    public void handlerLine(String empNo, String jobId, List<String> resultLines) {
        List<ResourceUseInfoDTO> jobBarcodeList = resourceUseInfoRepository.queryByJobId(jobId);
        if (CollectionUtils.isEmpty(jobBarcodeList)) {
            return;
        }
        List<ResourceUseInfoDTO> updateList = new ArrayList<>();
        List<String> failedMsg = new ArrayList<>();
        for (String resultLine : resultLines) {
            String[] fields = resultLine.split(STR_SPLIT);
            if (NumConstant.NUM_TWO != fields.length) {
                continue;
            }
            String resultOne = fields[0];
            String resultTwo = fields[1];
            int indexOne = resultOne.indexOf(SYMBOL_COLON);
            int indexTwo = resultTwo.indexOf(SYMBOL_COLON);
            if (indexOne < NumConstant.NUM_ZERO || indexTwo < NumConstant.NUM_ZERO ) {
                continue;
            }
            String barcode = resultOne.substring(indexOne + NumConstant.NUM_ONE);
            String resourceNum = resultTwo.substring(NumConstant.NUM_ZERO, indexTwo);
            String barcodeMsg = resultTwo.substring(indexTwo + NumConstant.NUM_ONE);
            ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
            resourceUseInfoDTO.setJobId(jobId);
            resourceUseInfoDTO.setRegistStatus(NetWorkUploadResult.REGISTER_ERROR_STATUS);
            resourceUseInfoDTO.setBarcode(barcode);
            resourceUseInfoDTO.setResourceNum(resourceNum);
            resourceUseInfoDTO.setRegistMsg(barcodeMsg);
            resourceUseInfoDTO.setLastUpdatedBy(empNo);
            updateList.add(resourceUseInfoDTO);
            failedMsg.add(resourceNum + barcode);
        }
        List<String> registerSuccessList = new ArrayList<>();
        jobBarcodeList.forEach(job -> {
            if (!failedMsg.contains(job.getResourceNum() + job.getBarcode())) {
                job.setRegistStatus(NetWorkUploadResult.REGISTER_SUCCESS_STATUS);
                job.setRegistMsg(NetWorkUploadResult.REGISTER_SUCCESS_MSG);
                job.setLastUpdatedBy(empNo);
                updateList.add(job);
                registerSuccessList.add(job.getResourceNum());
            }
        });
        updateRegistAndDetailStatus(updateList, registerSuccessList);
    }

    private void updateRegistAndDetailStatus(List<ResourceUseInfoDTO> updateList, List<String> registerSuccessList) {
        if(CollectionUtils.isNotEmpty(updateList)) {
            List<List<ResourceUseInfoDTO>> splitUpdateList = CommonUtils.splitList(updateList, NumConstant.NUM_100);
            for (List<ResourceUseInfoDTO> resourceUseInfoDTOS : splitUpdateList) {
                resourceUseInfoRepository.batchUpdateWithRegistMsg(resourceUseInfoDTOS);
            }
        }
        if (CollectionUtils.isNotEmpty(registerSuccessList)){
            List<List<String>> splitUpdateDetailList = CommonUtils.splitList(registerSuccessList, NumConstant.NUM_100);
            for (List<String> updateDetails : splitUpdateDetailList) {
                resourceInfoDetailRepository.batchUpdateForRegisterStatus(updateDetails);
            }
        }
    }
}
