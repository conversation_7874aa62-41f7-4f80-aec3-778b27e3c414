/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.BBomHeaderService;
import com.zte.application.BsPreItemInfoAsyncService;
import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.IMESLogService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.BsAsyncDataRespository;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsBomHierarchicalHeadRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.domain.model.BsPremanuItemInfoRepository;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.assembler.BsPremanuItemInfoAssembler;
import com.zte.interfaces.dto.AsyncBomDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BBomInfoDTO;
import com.zte.interfaces.dto.BsAsyncDataDTO;
import com.zte.interfaces.dto.BsPremanuBomInfoDTO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.datawb.ProdSmtWriteDTO;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 前加工信息维护接口实现
 * 755
 *
 * <AUTHOR>
 **/
@Service
public class BsPremanuItemInfoServiceImpl extends AbstractExportTaskHandler<BsPremanuItemInfo, BsPremanuItemInfo> implements BsPremanuItemInfoService {
    @Autowired
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;
    @Autowired
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Autowired
    private BBomHeaderService bBomHeaderService;
    @Autowired
    private BsItemInfoRepository bsItemInfoRepository;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IMESLogService imesLogService;
    @Autowired
    private BsPreItemInfoAsyncService bsPreItemInfoAsyncService;
    @Autowired
    private BsAsyncDataRespository bsAsyncDataRespository;
    @Autowired
    private DatawbRemoteService datawbRemoteService;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Autowired
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;
    /**
     * 增加实体数据
     *
     * @param record
     **/
    @Override
    public int insertBsPremanuItemInfo(BsPremanuItemInfo record) {
        return bsPremanuItemInfoRepository.insertBsPremanuItemInfo(record);
    }

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    @Override
    public int insertBsPremanuItemInfoSelective(BsPremanuItemInfo record) {
        return bsPremanuItemInfoRepository.insertBsPremanuItemInfoSelective(record);
    }

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBsPremanuItemInfoById(BsPremanuItemInfo record) throws Exception {
        //取出该物料代码的前加工记录以升序排列,取出来的排序值正好为1、2、3、4...
        List<BsPremanuItemInfo> preList = getPreList(record.getItemNo());
        int start = record.getSortSeq().intValue();
        //如果删除的排序值等于list的大小则说明删除的是最后一个前加工记录，不需要调整顺序，否则当前删除的排序值后的记录都需调整
        //举例：删除的排序值为1，则从2开始排序值都要减1
        if (start != preList.size()) {
            for (int i = start; i < preList.size(); i++) {
                BsPremanuItemInfo preInfo = preList.get(i);
                preInfo.setSortSeq(preInfo.getSortSeq().subtract(new BigDecimal(Constant.NUMBER_ONE)));
            }
            bsPremanuItemInfoRepository.updatePreItemBatch(preList);
        }
        int i = bsPremanuItemInfoRepository.deleteBsPremanuItemInfoById(record);
        this.syncDataToMes(record.getItemNo());
        return i;
    }

    /**
     * 同步数据到MES
     *
     * @param itemNo 物料代码
     * @throws Exception 业务异常
     */
    private void syncDataToMes(String itemNo) throws Exception {
        // 获取物料前加工数据
        List<BsPremanuItemInfo> reelList = getPreList(itemNo);
        if (CollectionUtils.isEmpty(reelList)) {
            // 删完了
            datawbRemoteService.deletePreItemByItemNoList(Arrays.asList(itemNo));
        } else {
            List<ProdSmtWriteDTO> itemProdList = new LinkedList<>();
            BsPremanuItemInfo itemInfo = reelList.get(0);
            ProdSmtWriteDTO prodSmtWriteDTO = new ProdSmtWriteDTO();
            prodSmtWriteDTO.setItemNo(itemInfo.getItemNo());
            prodSmtWriteDTO.setCreatedBy(itemInfo.getCreateBy());
            for (BsPremanuItemInfo item : reelList) {
                if (Constant.XP_CODE.equals(item.getTypeCode())) {
                    prodSmtWriteDTO.setIsWritetablet(Constant.INT_1);
                }
                if (Constant.CX_CODE.equals(item.getTypeCode())) {
                    prodSmtWriteDTO.setIsMolding(Constant.INT_1);
                }
                if (Constant.HK_CODE.equals(item.getTypeCode())) {
                    prodSmtWriteDTO.setIsRoast(Constant.INT_1);
                    prodSmtWriteDTO.setRoastconditions(item.getBakeRemark());
                }
            }
            itemProdList.add(prodSmtWriteDTO);
            datawbRemoteService.insertPreItemBatch(itemProdList);
        }
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    @Override
    public int updateBsPremanuItemInfoByIdSelective(BsPremanuItemInfo record) {
        return bsPremanuItemInfoRepository.updateBsPremanuItemInfoByIdSelective(record);
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    @Override
    public int updateBsPremanuItemInfoById(BsPremanuItemInfo record) {
        return bsPremanuItemInfoRepository.updateBsPremanuItemInfoById(record);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return BsPremanuItemInfo
     **/
    @Override
    public BsPremanuItemInfo selectBsPremanuItemInfoById(BsPremanuItemInfo record) {
        return bsPremanuItemInfoRepository.selectBsPremanuItemInfoById(record);
    }

    /**
     * 查询总数
     */
    @Override
    public long getPremanuInfoCount(Map<String, Object> map) {
        return bsPremanuItemInfoRepository.getPremanuInfoCount(map);
    }

    /**
     * list查询
     */
    @Override
    public List<BsPremanuItemInfo> getPremanuInfoList(Map<String, Object> map) {
        return bsPremanuItemInfoRepository.getPremanuInfoList(map);
    }

    @Override
    public Page<BsPremanuItemInfoDTO> getPreManuItemInfo(BsPremanuItemInfoDTO record) {
        Page<BsPremanuItemInfoDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        if (StringUtils.isNotEmpty(record.getInItemNo())) {
            List<String> itemNoList = Arrays.asList(record.getInItemNo().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
            record.setItemNoList(itemNoList);
        }
        pageInfo.setParams(record);
        List<BsPremanuItemInfo> preInfoList = bsPremanuItemInfoRepository.getPreManuItemInfo(pageInfo);
        pageInfo.setRows(BsPremanuItemInfoAssembler.toBsPremanuItemInfoDTOList(preInfoList));
        return pageInfo;
    }

    /**
     * bom分阶查询
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @Override
    public Page<BsPremanuItemInfoDTO> getSubLevelPremanuInfo(BsPremanuItemInfoDTO entity) {
        Page<BsPremanuItemInfoDTO> pageItem = new Page<>(entity.getPage(), entity.getRows());
        // 获取料单代码对应的基础信息
        BsItemInfo bomInfo = new BsItemInfo();
        bomInfo.setItemNo(entity.getBomCode());
        bomInfo = getBsItemInfo(bomInfo);
        // 获取bom 分阶详细信息
        Pair<StringBuilder, List<BsPremanuItemInfoDTO>> returnPair = getitemNosAndList(entity);
        StringBuilder itemSb = returnPair.getFirst();
        // 所有物料信息
        List<BsPremanuItemInfoDTO> queryList = returnPair.getSecond();
        if (CollectionUtils.isEmpty(queryList) || itemSb.length() == 0) {
            return pageItem; // 若当前料单代码没有对应的物料代码，返回空
        }
        String inItemNo = itemSb.toString().substring(0, itemSb.length() - 1);
        List<BsItemInfo> itemList = getBsItemInfos(inItemNo);
        if (CollectionUtils.isEmpty(itemList)) {
            return pageItem;
        }
        Map<String, BsItemInfo> itemMap = new HashMap<>();
        for (BsItemInfo item : itemList) {
            itemMap.put(item.getItemNo(), item);
        }
        entity.setItemNo(Constant.STRING_EMPTY);  // 使用inItemNo来查询
        entity.setInItemNo(inItemNo);
        // 首先查询料单级前加工信息
        List<BsPremanuBomInfo> preBomList = getBsPremanuBomInfos(entity);
        Map<String, BsPremanuItemInfoDTO> itemUseCountMap = new HashMap<>();
        for (BsPremanuItemInfoDTO item : queryList) {
            itemUseCountMap.put(item.getItemNo(), item);
        }
        addUsageCountForPreInfo(preBomList, itemMap, itemUseCountMap, bomInfo); // 处理前加工用量信息
        // 筛选掉已查出料单级前加工信息的物料代码
        filterItemNoWithBomInfo(queryList, preBomList, entity);
        List<BsPremanuItemInfoDTO> totalList = getPreItemByBomInfo(preBomList);
        if (CollectionUtils.isEmpty(queryList)) {
            //若没有需要查询的物料代码，则查询的都是料单级前加工信息，返回
            setItemInfoForSubLevel(totalList, itemMap, queryList, bomInfo, false);
            sortPreItemInfoByItemNo(BsPremanuItemInfoAssembler.toBsPremanuItemInfoList(totalList));
            pageItem.setCurrent(entity.getPage());
            pageItem.setRows(totalList);
            pageItem.setTotal(totalList.size());
            return pageItem;
        }
        // 用剩下来的物料代码查询物料级前加工信息
        if (StringUtils.isNotEmpty(entity.getInItemNo())) {
            List<String> itemNoList = Arrays.asList(entity.getInItemNo().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
            entity.setItemNoList(itemNoList);
        }
        pageItem.setParams(entity);
        List<BsPremanuItemInfo> preItemList = bsPremanuItemInfoRepository.getPreManuItemInfo(pageItem);
        // 处理前加工用量信息
        addUsageCountForPreItemInfo(preItemList, itemMap, itemUseCountMap, bomInfo);
        // 筛选掉已查出物料级前加工信息的物料代码
        if (!CollectionUtils.isEmpty(preItemList)) {
            totalList.addAll(BsPremanuItemInfoAssembler.toBsPremanuItemInfoDTOList(preItemList));
        }
        filterItemNoWithItemInfo(queryList, preItemList, entity);
        if (CollectionUtils.isEmpty(queryList)) {
            //若没有需要查询的物料代码，则查询的都是料单级前加工信息，返回
            setItemInfoForSubLevel(totalList, itemMap, queryList, bomInfo, false);
            sortPreItemInfoByItemNo(BsPremanuItemInfoAssembler.toBsPremanuItemInfoList(totalList));
            pageItem.setCurrent(entity.getPage());
            pageItem.setRows(totalList);
            pageItem.setTotal(totalList.size());
            return pageItem;
        }
        // 剩下的物料代码查询物料基础信息，获取是否贴片信息
        setItemInfoForSubLevel(totalList, itemMap, queryList, bomInfo, true);
        sortPreItemInfoByItemNo(BsPremanuItemInfoAssembler.toBsPremanuItemInfoList(totalList));
        pageItem.setCurrent(entity.getPage());
        pageItem.setRows(totalList);
        pageItem.setTotal(totalList.size());
        return pageItem;
    }

    private List<BsItemInfo> getBsItemInfos(String inItemNo) {
        // 查询物料基础信息
        List<String> itemCodeList = Arrays.asList(inItemNo.replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
        List<List<String>> lists = CommonUtils.splitList(itemCodeList, Constant.BATCH_SIZE_500);
        // TODO: 2020/11/17  待优化， 一个料单， inItemNo可能有1000+ 物料代码需要分批
        List<BsItemInfo> itemList = new ArrayList<>();
        BsItemInfo itemInfo = new BsItemInfo();
        for (List<String> list : lists) {
            itemInfo.setItemCodeList(list);
            itemList.addAll(bsItemInfoRepository.getInfoList(itemInfo));
        }
        return itemList;
    }

    private List<BsPremanuBomInfo> getBsPremanuBomInfos(BsPremanuItemInfoDTO entity) {
        Page<BsPremanuBomInfoDTO> pageBom = new Page<>(entity.getPage(), entity.getRows());
        BsPremanuBomInfoDTO queryBom = new BsPremanuBomInfoDTO();
        BeanUtils.copyProperties(entity, queryBom);
        queryBom.setOrderField(entity.getSort());
        convertParams(queryBom);
        pageBom.setParams(queryBom);
        // TODO: 2020/11/17  待优化， 一个料单，  inItemNo可能有1000+ 物料代码需要分批
        List<BsPremanuBomInfo> preBomList = bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(pageBom);
        return preBomList;
    }
    private void convertParams(BsPremanuBomInfoDTO record) {
        if (record != null && org.apache.commons.lang3.StringUtils.isNotBlank(record.getInItemNo())) {
            record.setItemNoList(Arrays.asList(record.getInItemNo().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA)));
        }
    }

    private BsItemInfo getBsItemInfo(BsItemInfo bomInfo) {
        List<BsItemInfo> bom = bsItemInfoRepository.getInfoList(bomInfo);
        if (CollectionUtils.isNotEmpty(bom)) {
            bomInfo = bom.get(NumConstant.NUM_ZERO);
        }
        return bomInfo;
    }

    /**
     * BOM分阶查询-增加用量信息
     *
     * @param preBomList
     * @param itemMap
     * @param itemUseCountMap
     * @param bomInfo
     */
    private void addUsageCountForPreInfo(List<BsPremanuBomInfo> preBomList, Map<String, BsItemInfo> itemMap,
                                         Map<String, BsPremanuItemInfoDTO> itemUseCountMap, BsItemInfo bomInfo) {
        if (CollectionUtils.isEmpty(preBomList)) {
            return;
        }

        // preBomList排序，将位号为空的数据排在最后做处理
        sortPreInfoByItemNo1(preBomList);
        // map记录每个itemNo的usageCount总数，若总数小于detail中的usageCount，需新增一条是否贴片数据
        Map<String, Integer> countMap = new HashMap<>();
        for (BsPremanuBomInfo bom : preBomList) {
            bom.setSubLevel(Constant.BOM_LEVEL);
            if (!countMap.containsKey(bom.getItemNo())) {
                // 不存在集合中
                notContainMap(itemUseCountMap, countMap, bom);
            } else {
                // 包含key值
                containKey(itemUseCountMap, countMap, bom);
            }
        }
        // 循环处理过后的map，itemNo对应的usageCount小于detail中usageCount时，新增一条数据。
        addNewInfoForPreManu(preBomList, itemMap, itemUseCountMap, bomInfo, countMap);
    }

    private void containKey(Map<String, BsPremanuItemInfoDTO> itemUseCountMap, Map<String, Integer> countMap, BsPremanuBomInfo bom) {
        // 包含key值
        if (StringUtils.isNotEmpty(bom.getTagNum())) {
            String[] split = bom.getTagNum().split(Constant.COMMA);
            // 位号不为空
            if (Constant.INT_1 == bom.getSortSeq().intValue()) {
                // 多前加工用量排序， 取最排序 小用量数据,存入map 中。
                countMap.put(bom.getItemNo(), countMap.get(bom.getItemNo()) + split.length);
            }
            bom.setItemQty(new BigDecimal(split.length));
        } else {
            // 位号为空
            BsPremanuItemInfoDTO temp = itemUseCountMap.get(bom.getItemNo());
            bom.setItemQty(temp == null ? new BigDecimal("0") : temp.getItemQty());
            if (Constant.INT_1 == bom.getSortSeq().intValue()) {
                // 多前加工用量排序， 取最排序 小用量数据,存入map 中。
                countMap.put(bom.getItemNo(), countMap.get(bom.getItemNo()) + bom.getItemQty().intValue());
            }
        }
    }

    private void notContainMap(Map<String, BsPremanuItemInfoDTO> itemUseCountMap, Map<String, Integer> countMap, BsPremanuBomInfo bom) {

        if (StringUtils.isNotEmpty(bom.getTagNum())) {
            // map中不存在itemNo的key且位号不为空，插入数据，usageCount为1
            String[] split = bom.getTagNum().split(Constant.COMMA);
            if (Constant.INT_1 == bom.getSortSeq().intValue()) {
                // 多前加工用量排序， 取最排序 小用量数据,存入map 中。
                countMap.put(bom.getItemNo(), split.length);
            }
            bom.setItemQty(new BigDecimal(split.length));
        } else {
            // map中不存在itemNo的key且位号为空，插入数据，usageCount为detail中的usageCount
            if (Constant.INT_1 == bom.getSortSeq().intValue()) {
                // 多前加工用量排序， 取最排序 小用量数据,存入map 中。
                BsPremanuItemInfoDTO dto = itemUseCountMap.get(bom.getItemNo());
                countMap.put(bom.getItemNo(), dto == null ? Constant.NUM_ZERO : dto.getItemQty().intValue());
            }
            BsPremanuItemInfoDTO dto = itemUseCountMap.get(bom.getItemNo());
            bom.setItemQty(dto == null ? new BigDecimal("0") : dto.getItemQty());
        }
    }

    /**
     * @param preBomList
     */
    private void sortPreInfoByItemNo1(List<BsPremanuBomInfo> preBomList) {
        Collections.sort(preBomList, (dtoFir, dtoSec) -> {
            if (dtoFir.getTagNum() == null) {
                return 1;
            }
            if (dtoSec.getTagNum() == null) {
                return -1;
            }
            return dtoFir.getTagNum().compareTo(dtoSec.getTagNum());
        });
    }

    /**
     * 新增一条信息
     *
     * @param preBomList
     * @param itemMap
     * @param itemUseCountMap
     * @param bomInfo
     * @param countMap
     */
    private void addNewInfoForPreManu(List<BsPremanuBomInfo> preBomList, Map<String, BsItemInfo> itemMap,
                                      Map<String, BsPremanuItemInfoDTO> itemUseCountMap, BsItemInfo bomInfo, Map<String, Integer> countMap) {
        for (Map.Entry<String, Integer> dealMap : countMap.entrySet()) {
            BsPremanuItemInfoDTO bsPremanuItemInfoDTO = itemUseCountMap.get(dealMap.getKey());
            if (null == bsPremanuItemInfoDTO || null == bsPremanuItemInfoDTO.getItemQty()
                    || dealMap.getValue() >= bsPremanuItemInfoDTO.getItemQty().intValue()) {
                continue;
            }
            BsItemInfo itemInfo = itemMap.get(dealMap.getKey());
            BsPremanuBomInfo addItem = new BsPremanuBomInfo();
            if (null != itemInfo) {
                addItem.setAbcType(itemInfo.getAbcType());
                addItem.setStyle(itemInfo.getStyle());
                addItem.setItemName(itemInfo.getItemName());
                this.seacherBomPreDelivery(preBomList, itemInfo, addItem);
            }
            addItem.setItemQty(bsPremanuItemInfoDTO.getItemQty().subtract(new BigDecimal(dealMap.getValue())));
            addItem.setItemNo(dealMap.getKey());
            addItem.setBomCode(bomInfo.getItemNo());
            addItem.setBomName(bomInfo.getItemName());
            addItem.setSubLevel(Constant.SMT_LEVEL);
            preBomList.add(addItem);
        }
    }

    /**
     * 取前加工 创建时间最早的 一条的配送去向
     *
     * @param preBomList 料单级前加工集合
     * @param itemInfo   基础信息
     * @param addItem    贴片数据
     */
    private void seacherBomPreDelivery(List<BsPremanuBomInfo> preBomList, BsItemInfo itemInfo, BsPremanuBomInfo addItem) {

        String itemNo = itemInfo.getItemNo();
        List<BsPremanuBomInfo> bomPreList = preBomList.stream().filter(bsPremanuBomInfo -> StringUtils.isNotBlank(itemNo) && itemNo.equals(bsPremanuBomInfo.getItemNo()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(bomPreList)) {
            bomPreList.stream().sorted(Comparator.comparing(BsPremanuBomInfo::getCreateDate));
            addItem.setDeliveryProcess(bomPreList.get(Constant.INT_0).getDeliveryProcess());
            return;
        }
        if (null != itemInfo.getIsSmt()) {
            if (Constant.LEVEL_CODE.equals(itemInfo.getIsSmt().toString())) {
                addItem.setDeliveryProcess(Constant.DIP_DELIVERY);
            } else if (Constant.NUMBER_ONE.equals(itemInfo.getIsSmt().toString())) {
                addItem.setDeliveryProcess(Constant.SMT_DELIVERY);
            }
        }
    }


    /**
     * @param preItemList
     */
    private void sortPreItemInfoByItemNo1(List<BsPremanuItemInfo> preItemList) {
        Collections.sort(preItemList, (dtoFir, dtoSec) -> {
            if (dtoFir.getTagNum() == null) {
                return 1;
            }
            if (dtoSec.getTagNum() == null) {
                return -1;
            }
            return dtoFir.getTagNum().compareTo(dtoSec.getTagNum());
        });
    }

    /**
     * 前加工排序
     *
     * @param preItemList
     */
    private void sortPreItemInfoByItemNo(List<BsPremanuItemInfo> preItemList) {

        Collections.sort(preItemList, (dtoFir, dtoSec) -> (dtoFir.getItemNo() == null ?
                Constant.STRING_EMPTY + Long.MAX_VALUE : dtoFir.getItemNo())
                .compareTo(dtoSec.getItemNo() == null ? Constant.STRING_EMPTY + Long.MAX_VALUE : dtoSec.getItemNo()));
    }

    /**
     * 将返回的信息赋值物料基础信息
     *
     * @param totalList
     * @param itemMap
     * @param queryList
     */
    private void setItemInfoForSubLevel(List<BsPremanuItemInfoDTO> totalList, Map<String, BsItemInfo> itemMap,
                                        List<BsPremanuItemInfoDTO> queryList, BsItemInfo bomInfo, boolean addNew) {

        // 为totalList中的所有itemNo赋基础信息
        for (BsPremanuItemInfoDTO item : totalList) {
            BsItemInfo itemInfo = itemMap.get(item.getItemNo());
            if (null != itemInfo) {
                String createBy = item.getCreateBy();
                Date createDate = item.getCreateDate();
                String lastUpdatedBy = item.getLastUpdatedBy();
                Date lastUpdatedDate = item.getLastUpdatedDate();
                BeanUtils.copyProperties(itemInfo, item);
                item.setAbcType(itemInfo.getAbcType());
                item.setStyle(itemInfo.getStyle());
                item.setItemName(itemInfo.getItemName());
                item.setBomName(bomInfo.getItemName());
                item.setBomCode(bomInfo.getItemNo());
                item.setLastUpdatedDate(lastUpdatedDate);
                item.setLastUpdatedBy(lastUpdatedBy);
                item.setCreateDate(createDate);
                item.setCreateBy(createBy);
            }
            if (StringUtils.isEmpty(item.getSubLevel())) {
                item.setSubLevel(Constant.ITEM_LEVEL);
            }
        }
        // 为queryList中的item新增一条记录
        if (CollectionUtils.isNotEmpty(queryList) && addNew) {
            addSmtInfoForSubLevelQuery(totalList, itemMap, queryList, bomInfo);
        }
    }

    /**
     * 加入是否贴片信息 基础物料 新增 非前加工
     *
     * @param totalList
     * @param itemMap
     * @param queryList
     * @param bomInfo
     */
    private void addSmtInfoForSubLevelQuery(List<BsPremanuItemInfoDTO> totalList, Map<String, BsItemInfo> itemMap,
                                            List<BsPremanuItemInfoDTO> queryList, BsItemInfo bomInfo) {
        for (BsPremanuItemInfoDTO item : queryList) {
            BsItemInfo itemInfo = itemMap.get(item.getItemNo());
            BsPremanuItemInfoDTO addItem = new BsPremanuItemInfoDTO();
            if (null != itemInfo) {
                BeanUtils.copyProperties(itemInfo, addItem);
                addItem.setAbcType(itemInfo.getAbcType());
                addItem.setStyle(itemInfo.getStyle());
                addItem.setItemName(itemInfo.getItemName());
                if (null != itemInfo.getIsSmt()) {
                    if (Constant.LEVEL_CODE.equals(itemInfo.getIsSmt().toString())) {
                        addItem.setDeliveryProcess(Constant.DIP_DELIVERY);
                    } else if (Constant.NUMBER_ONE.equals(itemInfo.getIsSmt().toString())) {
                        addItem.setDeliveryProcess(Constant.SMT_DELIVERY);
                    }
                }
            }
            addItem.setItemNo(item.getItemNo());
            addItem.setBomCode(bomInfo.getItemNo());
            addItem.setBomName(bomInfo.getItemName());
            addItem.setSubLevel(Constant.SMT_LEVEL);
            addItem.setItemQty(item.getItemQty());
            totalList.add(addItem);
        }
    }

    /**
     * 获取前加工
     *
     * @param queryList
     * @param preItemList
     * @param entity
     */
    private void filterItemNoWithItemInfo(List<BsPremanuItemInfoDTO> queryList, List<BsPremanuItemInfo> preItemList,
                                          BsPremanuItemInfoDTO entity) {

        StringBuilder sbItem = new StringBuilder();
        // 筛选掉queryList中已查询到的itemNo
        List<BsPremanuItemInfoDTO> queryListNew = new LinkedList<>();
        for (BsPremanuItemInfoDTO bsPremanuItemInfoDTO : queryList) {
            boolean exsist = false;
            for (BsPremanuItemInfo item : preItemList) {
                if (bsPremanuItemInfoDTO.getItemNo().equals(item.getItemNo())) {
                    exsist = true;
                    break;
                }
            }
            if (!exsist) {
                queryListNew.add(bsPremanuItemInfoDTO);
                sbItem.append(Constant.SINGLE_QUOTE).append(bsPremanuItemInfoDTO.getItemNo()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
            }
        }
        queryList.clear();
        queryList.addAll(queryListNew);

        if (sbItem.length() > 0) {
            entity.setInItemNo(sbItem.toString().substring(0, sbItem.length() - 1));
        }
    }

    /**
     * 料单级转物料级
     *
     * @param preBomList
     * @return
     */
    private List<BsPremanuItemInfoDTO> getPreItemByBomInfo(List<BsPremanuBomInfo> preBomList) {

        List<BsPremanuItemInfoDTO> renturnList = new ArrayList<>();
        for (BsPremanuBomInfo bom : preBomList) {
            BsPremanuItemInfoDTO item = new BsPremanuItemInfoDTO();
            BeanUtils.copyProperties(bom, item);
            // item.setSubLevel(Constant.BOM_LEVEL);
            renturnList.add(item);
        }
        return renturnList;
    }

    /**
     * 获取前加工
     *
     * @param queryList
     * @param preBomList
     * @param entity
     */
    private void filterItemNoWithBomInfo(List<BsPremanuItemInfoDTO> queryList, List<BsPremanuBomInfo> preBomList,
                                         BsPremanuItemInfoDTO entity) {

        StringBuilder sbItem = new StringBuilder();
        // 筛选掉queryList中已查询到的itemNo
        List<BsPremanuItemInfoDTO> queryListNew = new LinkedList<>();
        for (BsPremanuItemInfoDTO bsPremanuItemInfoDTO : queryList) {
            boolean exsist = false;
            for (BsPremanuBomInfo bom : preBomList) {
                if (bsPremanuItemInfoDTO.getItemNo().equals(bom.getItemNo())) {
                    exsist = true;
                    break;
                }
            }
            if (!exsist) {
                sbItem.append(Constant.SINGLE_QUOTE).append(bsPremanuItemInfoDTO.getItemNo()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
                queryListNew.add(bsPremanuItemInfoDTO);
            }
        }
        queryList.clear();
        queryList.addAll(queryListNew);
        if (sbItem.length() > 0) {
            entity.setInItemNo(sbItem.toString().substring(0, sbItem.length() - 1));
        }
    }

    /**
     * 获取分阶查询所需的数据集合
     *
     * @param entity
     * @return
     */
    private Pair<StringBuilder, List<BsPremanuItemInfoDTO>> getitemNosAndList(BsPremanuItemInfoDTO entity) {

        StringBuilder itemSb = new StringBuilder();
        List<BsPremanuItemInfoDTO> queryList = new ArrayList<>();
        BBomInfoDTO bomQuery = new BBomInfoDTO();
        bomQuery.setProductCode(entity.getBomCode());
        List<BBomInfoDTO> bomList = bBomHeaderService.getBBomInfoList(bomQuery);
        if (StringUtils.isEmpty(entity.getItemNo())) {
            // 未传物料代码时需要查询料单代码对应的所有物料代码
            if (!CollectionUtils.isEmpty(bomList)) {
                List<BBomDetailDTO> itemList = bomList.get(NumConstant.NUM_ZERO).getListDetail();
                for (BBomDetailDTO detail : itemList) {
                    BsPremanuItemInfoDTO addEntity = new BsPremanuItemInfoDTO();
                    BeanUtils.copyProperties(detail, addEntity);
                    addEntity.setItemNo(detail.getItemCode());
                    addEntity.setItemQty(detail.getUsageCount());
                    // in拼接物料代码用于查询
                    itemSb.append(Constant.SINGLE_QUOTE).append(detail.getItemCode()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
                    queryList.add(addEntity);
                }
            }
        } else {

            BsPremanuItemInfoDTO addEntity = new BsPremanuItemInfoDTO();
            BeanUtils.copyProperties(entity, addEntity);
            if (!CollectionUtils.isEmpty(bomList)) {
                List<BBomDetailDTO> itemList = bomList.get(NumConstant.NUM_ZERO).getListDetail();
                // 找出itemNo对应的usageCount
                List<BBomDetailDTO> currItem =
                        itemList.stream().filter(pro -> entity.getItemNo().equals(pro.getItemCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(bomList) && CollectionUtils.isNotEmpty(currItem)) {
                    addEntity.setItemQty(currItem.get(NumConstant.NUM_ZERO).getUsageCount());
                }
            }
            queryList.add(addEntity);
            itemSb.append(Constant.SINGLE_QUOTE).append(addEntity.getItemNo()).append(Constant.SINGLE_QUOTE).append(Constant.COMMA);
        }
        return Pair.of(itemSb, queryList);
    }

    //物料级前加工维护：在该物料代码在没有维护前加工信息的情况下（前加工类型不为空），允许只单独维护配送工序（只能维护一条记录）
    private RetCode checkAddPreManu(List<BsPremanuItemInfo> preList, BsPremanuItemInfoDTO dto) {
        //在物料前加工维护操作栏中只选择配送工序点击“提交”按钮时，校验该物料代码在物料级前加工表中是否有数据，如果有则报错，没有则允许提交
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isEmpty(dto.getTraceCode()) && StringUtils.isEmpty(dto.getTypeCode())
                && StringUtils.isNotEmpty(dto.getDeliveryProcess()) && CollectionUtils.isNotEmpty(preList)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_CODE_IS_MAINTAINED_PRE_PROCESSING_DATA);
            // 在物料前加工维护操作栏中前加工类型、前加工去向、配送工序点击“提交”按钮时增加校验： 校验该物料代码在物料级前加工表中是否有前加工类型为空、
            // 配送去向不为空的数据，如果有则报错，没有则继续进行其他校验
        } else if (StringUtils.isNotEmpty(dto.getTraceCode()) && StringUtils.isNotEmpty(dto.getTypeCode())
                && StringUtils.isNotEmpty(dto.getDeliveryProcess()) && judgeOnlyDistributionOperation(preList, sb)) {
            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.MATERIAL_CODE_IS_MAINTAINED_DISTRIBUTION_OPERATION, new Object[]{sb.toString()});
        }
//        else if (StringUtils.isNotEmpty(dto.getTraceCode()) && StringUtils.isNotEmpty(dto.getTypeCode())
//                && StringUtils.isNotEmpty(dto.getDeliveryProcess())&&judgeExistDeliveryProcessDataNotEquals(preList,dto,sb)) {
//            return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TAGNUM_DISTRIBUTION_OPERATION_INCONSISTENT,new Object[]{sb.toString()});
//        }
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
    }


    //判断存在只维护了配送工序的记录（在料单级前加工记录表中存在位号、前加工类型、前加工去向为空，配送工序不为空的记录）则报错
    private boolean judgeOnlyDistributionOperation(List<BsPremanuItemInfo> preList, StringBuffer deliveryProcess) {
        if (CollectionUtils.isEmpty(preList)) {
            return false;
        }
        for (BsPremanuItemInfo bsPremanuItemInfo : preList) {
            if (!StringUtils.isEmpty(bsPremanuItemInfo.getDeliveryProcess()) && StringUtils.isEmpty(bsPremanuItemInfo.getTypeCode())
                    && StringUtils.isEmpty(bsPremanuItemInfo.getTraceCode())) {
                deliveryProcess.append(bsPremanuItemInfo.getDeliveryProcess());
                return true;
            }
        }
        return false;
    }

    //查询所有物料代码在数据库信息,并分组
    private void setDbMap(Set<String> keys, Map<String, List<BsPremanuItemInfo>> dbMap) {
        for (String itemNo : keys) {
            // 查询同物料代码下的所有前加工信息
            //存在就不需要再查
            List tempList = dbMap.get(itemNo);
            if (tempList != null) {
                continue;
            }
            List<BsPremanuItemInfo> preList = getPreList(itemNo);
            dbMap.put(itemNo, preList);
        }
    }

    //按物料代码分组
    private void setMap(List<BsPremanuItemInfoDTO> list, Map<String, List<BsPremanuItemInfoDTO>> map) {
        for (BsPremanuItemInfoDTO dto : list) {
            String itemNo = dto.getItemNo();
            List tempList = map.get(itemNo);
            if (tempList == null) {
                tempList = new ArrayList();
            }
            tempList.add(dto);
            map.put(itemNo, tempList);
        }
    }

    //获取最大排序值
    private BigDecimal getMaxSortSeq(List<BsPremanuItemInfo> tempList) {
        BigDecimal maxSortSeq = new BigDecimal(NumConstant.STRING_ZERO);
        if (CollectionUtils.isEmpty(tempList)) {
            return maxSortSeq;
        }
        for (BsPremanuItemInfo bpbi : tempList) {
            BigDecimal sortSeq = bpbi.getSortSeq();
            if (sortSeq.compareTo(maxSortSeq) > NumConstant.NUM_ZERO) {
                maxSortSeq = sortSeq;
            }
        }
        return maxSortSeq;
    }

    /**
     * 调整前加工排序
     *
     * @param record
     * @param preList
     */
    public void dealSortSeqNew(BsPremanuItemInfoDTO record, List<BsPremanuItemInfo> preList, List<BsPremanuItemInfoDTO> filterList) {
        //最大排序值+1
        BigDecimal maxSortSeq = getMaxSortSeq(preList).add(new BigDecimal(NumConstant.STRING_ONE));
        //调整后的同代码前加工排序 调整后的排序是基于数据库来选的，不可能大于最大排序
        BigDecimal newSortSeq = record.getSortSeq();
        //排序比newSortSeq大的自动加一
        if (CollectionUtils.isNotEmpty(preList)) {
            for (BsPremanuItemInfo bsPremanuItemInfo : preList) {
                BigDecimal sortSeq = bsPremanuItemInfo.getSortSeq();
                if (sortSeq.compareTo(newSortSeq) >= NumConstant.NUM_ZERO) {
                    bsPremanuItemInfo.setSortSeq(sortSeq.add(new BigDecimal(NumConstant.STRING_ONE)));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(filterList)) {
            for (BsPremanuItemInfoDTO bsPremanuItemInfo : filterList) {
                BigDecimal sortSeq = bsPremanuItemInfo.getSortSeq();
                if (sortSeq.compareTo(newSortSeq) >= NumConstant.NUM_ZERO) {
                    bsPremanuItemInfo.setSortSeq(sortSeq.add(new BigDecimal(NumConstant.STRING_ONE)));
                }
            }
        }
    }

    //批量新增前加工数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addPreManuItemBatch(List<BsPremanuItemInfoDTO> list) throws Exception {
        StringBuffer errorMsg = new StringBuffer();
        //按物料代码分组
        Map<String, List<BsPremanuItemInfoDTO>> map = new HashMap<>();
        setMap(list, map);
        Map<String, List<BsPremanuItemInfo>> dbMap = new HashMap<>();
        Set<String> keys = map.keySet();
        //查询所有物料代码在数据库信息,并分组
        setDbMap(keys, dbMap);
        //需更新list
        List<BsPremanuItemInfo> updateList = new ArrayList<>();
        //设置前加工序号
        setSortSeqForList(map, dbMap, updateList);
        for (int i = NumConstant.NUM_ZERO; i < list.size(); i++) {
            BsPremanuItemInfoDTO dto = list.get(i);
            List<BsPremanuItemInfoDTO> tempList = map.get(dto.getItemNo());
            List<BsPremanuItemInfo> tempEntityList = BsPremanuItemInfoAssembler.toBsPremanuItemInfoList(tempList);
            //过滤掉自己
            List<BsPremanuItemInfo> filterList = tempEntityList.stream().filter(o -> !StringUtils.equals(dto.getRecordId(), o.getRecordId())).collect(Collectors.toList());
            List<BsPremanuItemInfo> dbList = dbMap.get(dto.getItemNo());
            filterList.addAll(dbList);
            //最终过滤list为新增的数据(不包含自己)+数据库的数据
            RetCode code = addPreManuItemNew(dto, filterList);
            if (StringUtils.equals(code.getCode(), RetCode.BUSINESSERROR_CODE)) {
                errorMsg.append(CommonUtils.getLmbMessage(MessageId.ERROR_MSG_SET, new String[]{(i + NumConstant.NUM_ONE) + Constant.STRING_EMPTY, code.getMsg()})).append(MpConstant.LINE_FEED);
            }
        }
        if (!StringUtils.isEmpty(errorMsg.toString())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ADD_PRE_MANU_INFO_BATCH_FAILED, new Object[]{errorMsg.toString()});
        }
        //最后更新数据
        updatePreItemList(updateList);
        // 目前只能单个物料新增
        this.syncDataToMes(list.get(0).getItemNo());
        return errorMsg.toString();
    }

    //分批更新数据
    private void updatePreItemList(List<BsPremanuItemInfo> updateList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<BsPremanuItemInfo>> splitList = CommonUtils.splitList(updateList, NumConstant.NUM_HUNDRED);
            for (List<BsPremanuItemInfo> tempList : splitList) {
                bsPremanuItemInfoRepository.updatePreItemBatch(tempList);
            }
        }
    }

    //设置前加工序号
    private void setSortSeqForList(Map<String, List<BsPremanuItemInfoDTO>> map, Map<String, List<BsPremanuItemInfo>> dbMap, List<BsPremanuItemInfo> updateList) {
        Set<String> keys = map.keySet();
        for (String key : keys) {
            List<BsPremanuItemInfoDTO> keyList = map.get(key);
            List<BsPremanuItemInfo> dbList = dbMap.get(key);
            if (CollectionUtils.isEmpty(keyList)) {
                continue;
            }
            List<BsPremanuItemInfoDTO> notNullSoftSeqList = keyList.stream().filter(o -> null != o.getSortSeq()).collect(Collectors.toList());
            //升序排列
            extracted(updateList, dbList, notNullSoftSeqList);
            //获取最大序号，因为新增数据选择序号是基于旧序号，因此不会比dbList的更大
            //将为空的设置为最大序号，递增
            BigDecimal maxSortSeq = getMaxSortSeq(dbList).add(new BigDecimal(NumConstant.STRING_ONE));
            for (BsPremanuItemInfoDTO bsPremanuItemInfoDTO : keyList) {
                if (null == bsPremanuItemInfoDTO.getSortSeq()) {
                    bsPremanuItemInfoDTO.setSortSeq(maxSortSeq);
                    maxSortSeq = maxSortSeq.add(new BigDecimal(NumConstant.STRING_ONE));
                }
            }
        }
    }

    private void extracted(List<BsPremanuItemInfo> updateList, List<BsPremanuItemInfo> dbList, List<BsPremanuItemInfoDTO> notNullSoftSeqList) {
        if (CollectionUtils.isNotEmpty(notNullSoftSeqList)) {
            notNullSoftSeqList.sort(Comparator.comparing(BsPremanuItemInfoDTO::getSortSeq));
            List<BsPremanuItemInfoDTO> filterList = new ArrayList<>();
            for (BsPremanuItemInfoDTO bpiid : notNullSoftSeqList) {
                dealSortSeqNew(bpiid, dbList, filterList);
                filterList.add(bpiid);
            }
            //循环完，更新数据
            if (CollectionUtils.isNotEmpty(dbList)) {
                updateList.addAll(dbList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public RetCode addPreManuItemNew(BsPremanuItemInfoDTO record, List<BsPremanuItemInfo> preList) {
        BigDecimal sortSeqForAdd = null;
        for (BsPremanuItemInfo preInfo : preList) {
            //同物料代码只允许有一个前加工类型
            if (record.getTypeName().equals(preInfo.getTypeName())) {
                String[] params = new String[]{preInfo.getTypeName()};
                return new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.PREMANU_TYPE_EXISTS, params);
            }
        }
        RetCode checkResult = checkAddPreManu(preList, record);
        if (checkResult != null && Constant.BUSINESSERROR.equals(checkResult.getCode())) {
            return checkResult;
        }

        BsPremanuItemInfo premanuItemInfo = BsPremanuItemInfoAssembler.toEntity(record);
        bsPremanuItemInfoRepository.insertBsPremanuItemInfoSelective(premanuItemInfo);
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RetCode addPreManuItem(BsPremanuItemInfoDTO record) {
        BigDecimal sortSeqForAdd = null;
        List<BsPremanuItemInfo> preList = getPreList(record.getItemNo());
        for (BsPremanuItemInfo preInfo : preList) {
            //同物料代码只允许有一个前加工类型
            if (record.getTypeName().equals(preInfo.getTypeName())) {
                String[] params = new String[]{preInfo.getTypeName()};
                return new RetCode(RetCode.SERVERERROR_CODE, MessageId.PREMANU_TYPE_EXISTS, params);
            }
        }
        RetCode checkResult = checkAddPreManu(preList, record);
        if (checkResult != null && Constant.BUSINESSERROR.equals(checkResult.getCode())) {
            return checkResult;
        }

        if (CollectionUtils.isEmpty(preList)) {
            sortSeqForAdd = new BigDecimal(Constant.NUMBER_ONE);
        } else {
            //新增时的排序值默认去当前最大排序数加1
            sortSeqForAdd = preList.get(preList.size() - 1).getSortSeq().add(new BigDecimal(Constant.NUMBER_ONE));
        }

        if (null == record.getSortSeq()) {
            //如果页面上没有选择排序则不要调整其他前加工的排序，直接取当前最大的排序加1
            record.setSortSeq(sortSeqForAdd);
        } else {
            //页面上选择了排序则需要调整其他已经排序的前加工记录顺序，新增记录的排序值作为老的序号，页面上选择的排序值作为新的序号处理
            record.setOldSortSeq(sortSeqForAdd);
            dealSortSeq(record, preList);
        }
        BsPremanuItemInfo premanuItemInfo = BsPremanuItemInfoAssembler.toEntity(record);
        bsPremanuItemInfoRepository.insertBsPremanuItemInfoSelective(premanuItemInfo);
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);

    }

    //排查所选数据
    private List<BsPremanuItemInfo> getUpdatePreList(List<BsPremanuItemInfo> preList, BsPremanuItemInfoDTO dto) {
        List<BsPremanuItemInfo> updatePreList = new ArrayList<>();
        for (BsPremanuItemInfo bsPremanuBomInfo : preList) {
            if (!com.alibaba.druid.util.StringUtils.equals(dto.getRecordId(), bsPremanuBomInfo.getRecordId())) {
                updatePreList.add(bsPremanuBomInfo);
            }
        }
        return updatePreList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RetCode updatePreManuItem(BsPremanuItemInfoDTO record) throws Exception {
        //校验前加工类型，前加工去向数据是否正常
        checkParams(record);
        //将物料代码的前加工记录按升序排列取出
        List<BsPremanuItemInfo> preList = getPreList(record.getItemNo());
        List<BsPremanuItemInfo> updatePreList = getUpdatePreList(preList, record);
        RetCode checkResult = checkAddPreManu(updatePreList, record);
        if (checkResult != null && Constant.BUSINESSERROR.equals(checkResult.getCode())) {
            return checkResult;
        }
        //找出当前修改记录保存的排序值
        for (BsPremanuItemInfo preInfo : preList) {
            //同物料代码只允许有一个前加工类型
            if (record.getTypeName().equals(preInfo.getTypeName()) && !preInfo.getRecordId().equals(record.getRecordId())) {
                String[] params = new String[]{preInfo.getTypeName()};
                return new RetCode(RetCode.SERVERERROR_CODE, MessageId.PREMANU_TYPE_EXISTS, params);
            }
            //找出当前修改的前加工记录调整前的数序值
            if (preInfo.getRecordId().equals(record.getRecordId())) {
                record.setOldSortSeq(preInfo.getSortSeq());
            }
        }
        //当前修改记录保存的排序值不等于页面上选择的排序值，则需要调整排序
        if (record.getSortSeq().compareTo(record.getOldSortSeq()) != 0) {
            dealSortSeq(record, preList);
        }

        BsPremanuItemInfo premanuItemInfo = BsPremanuItemInfoAssembler.toEntity(record);
        //处理对应的料单级前加工 占时屏蔽 待后续优化
        // dealBom(premanuItemInfo);
        bsPremanuItemInfoRepository.updateBsPremanuItemInfoByIdSelective(premanuItemInfo);
        this.syncDataToMes(premanuItemInfo.getItemNo());
        return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
    }

    private void checkParams(BsPremanuItemInfoDTO record) throws MesBusinessException {
        boolean b = (StringUtils.isNotBlank(record.getTypeName()) && StringUtils.isBlank(record.getTypeCode()))
                || (StringUtils.isNotBlank(record.getTypeCode()) && StringUtils.isBlank(record.getTypeName()))
                || (StringUtils.isNotBlank(record.getTraceCode()) && StringUtils.isBlank(record.getTraceName()))
                || (StringUtils.isNotBlank(record.getTraceName()) && StringUtils.isBlank(record.getTraceCode()));
        if (b) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TYPE_CODE_NAME_TRACE_CODE_NAME_CAN_NOT_BE_EMPTY_ALONE);
        }
    }

    /**
     * 根据物料代码获取物料前加工记录，按排序字段值排序
     *
     * @param itemNo
     * @return
     */
    public List<BsPremanuItemInfo> getPreList(String itemNo) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("itemNo", itemNo);
        //排序字段
        map.put("orderField", "sortSeq");
        List<BsPremanuItemInfo> preList = bsPremanuItemInfoRepository.getPremanuInfoList(map);
        return preList;
    }

    /**
     * 调整前加工排序
     *
     * @param record
     * @param preList
     */
    public void dealSortSeq(BsPremanuItemInfoDTO record, List<BsPremanuItemInfo> preList) {
        //调整前的同代码前加工排序
        int oldSortSeq = record.getOldSortSeq().intValue();
        //调整后的同代码前加工排序
        int newSortSeq = record.getSortSeq().intValue();
        //调整后的顺序小于调整前的顺序（往前调），则将排在两个顺序间的数据集体往后调整一个位置，
        //假设存在排序为1、2、3、4，将4调整到1，则将原来的1到3都往后调整一个位置
        if (newSortSeq < oldSortSeq) {
            //list下标从0开始需减1处理
            for (int i = newSortSeq - 1; i < oldSortSeq - 1; i++) {
                BsPremanuItemInfo preInfo = preList.get(i);
                preInfo.setSortSeq(preInfo.getSortSeq().add(new BigDecimal(1)));
            }
        } else if (newSortSeq > oldSortSeq) {
            //调整后的顺序大于调整前的顺序（往后调），则将排在两个顺序间的数据集体往前调整一个位置
            for (int i = oldSortSeq; i < newSortSeq; i++) {
                BsPremanuItemInfo preInfo = preList.get(i);
                preInfo.setSortSeq(preInfo.getSortSeq().subtract(new BigDecimal(1)));
            }
        }
        bsPremanuItemInfoRepository.updatePreItemBatch(preList);
    }


    /**
     * BOM分阶查询-增加用量信息 物料级前加工信息
     *
     * @param preItemList
     * @param itemMap
     * @param itemUseCountMap
     * @param bomInfo
     */
    private void addUsageCountForPreItemInfo(List<BsPremanuItemInfo> preItemList, Map<String, BsItemInfo> itemMap,
                                             Map<String, BsPremanuItemInfoDTO> itemUseCountMap, BsItemInfo bomInfo) {

        if (CollectionUtils.isEmpty(preItemList)) {
            return;
        }
        // preBomList排序，将位号为空的数据排在最后做处理
        sortPreItemInfoByItemNo1(preItemList);
        // map记录每个itemNo的usageCount总数，若总数小于detail中的usageCount，需新增一条是否贴片数据
        Map<String, Integer> countMap = new HashMap<>();
        for (BsPremanuItemInfo item : preItemList) {
            extracted(itemUseCountMap, countMap, item);
        }
        // 循环处理过后的map，itemNo对应的usageCount小于detail中usageCount时，新增一条数据。
        addNewInfoForPreItemManu(preItemList, itemMap, itemUseCountMap, bomInfo, countMap);
    }

    private void extracted(Map<String, BsPremanuItemInfoDTO> itemUseCountMap, Map<String, Integer> countMap, BsPremanuItemInfo item) {
        if (!countMap.containsKey(item.getItemNo())) {
            if (StringUtils.isNotEmpty(item.getTagNum())) {
                // map中不存在itemNo的key且位号不为空，插入数据，usageCount为1
                countMap.put(item.getItemNo(), 1);
                item.setItemQty(new BigDecimal(1));
            } else {
                if (itemUseCountMap.get(item.getItemNo()) != null && itemUseCountMap.get(item.getItemNo()).getItemQty() != null) {
                    // map中不存在itemNo的key且位号为空，插入数据，usageCount为detail中的usageCount
                    countMap.put(item.getItemNo(), itemUseCountMap.get(item.getItemNo()).getItemQty().intValue());
                    item.setItemQty(itemUseCountMap.get(item.getItemNo()).getItemQty());
                }
            }
        } else {
            // 包含key值
            if (StringUtils.isNotEmpty(item.getTagNum())) {
                // 位号不为空
                countMap.put(item.getItemNo(), countMap.get(item.getItemNo()) + 1);
                item.setItemQty(new BigDecimal(1));
            } else {
                // 位号为空
                item.setItemQty(itemUseCountMap.get(item.getItemNo()).getItemQty().subtract(new BigDecimal(countMap.get(item.getItemNo()))));
                countMap.put(item.getItemNo(), countMap.get(item.getItemNo()) + item.getItemQty().intValue());
            }
        }
    }

    /**
     * 新增一条信息 料单级前加工 新增贴片信息， 配送工序取该物料代码最晚的配送工序
     *
     * @param preItemList
     * @param itemMap
     * @param itemUseCountMap
     * @param bomInfo
     * @param countMap
     */
    private void addNewInfoForPreItemManu(List<BsPremanuItemInfo> preItemList, Map<String, BsItemInfo> itemMap,
                                          Map<String, BsPremanuItemInfoDTO> itemUseCountMap, BsItemInfo bomInfo, Map<String, Integer> countMap) {
        for (Map.Entry<String, Integer> dealMap : countMap.entrySet()) {
            BsPremanuItemInfoDTO bsPremanuItemInfoDTO = itemUseCountMap.get(dealMap.getKey());
            if (null == bsPremanuItemInfoDTO || null == bsPremanuItemInfoDTO.getItemQty()
                    || dealMap.getValue() >= bsPremanuItemInfoDTO.getItemQty().intValue()) {
                continue;
            }
            BsItemInfo itemInfo = itemMap.get(dealMap.getKey());
            BsPremanuItemInfo addItem = new BsPremanuItemInfo();
            if (null != itemInfo) {
                BeanUtils.copyProperties(itemInfo, addItem);
                addItem.setAbcType(itemInfo.getAbcType());
                addItem.setStyle(itemInfo.getStyle());
                addItem.setItemName(itemInfo.getItemName());
                this.searchItemNoDelivery(preItemList, itemInfo, addItem);
            }
            addItem.setItemQty(bsPremanuItemInfoDTO.getItemQty().subtract(new BigDecimal(dealMap.getValue())));
            addItem.setItemNo(dealMap.getKey());
            addItem.setBomCode(bomInfo.getItemNo());
            addItem.setBomName(bomInfo.getItemName());
            addItem.setSubLevel(Constant.SMT_LEVEL);
            preItemList.add(addItem);
        }
    }

    /**
     * 新增贴片信息，配送去向取值逻辑， 如果有前加工
     *
     * @param preItemList 物料级前加工 信息
     * @param itemInfo    物料基础信息
     * @param addItem     bom 基础实体
     */
    private void searchItemNoDelivery(List<BsPremanuItemInfo> preItemList, BsItemInfo itemInfo, BsPremanuItemInfo addItem) {
        if (CollectionUtils.isNotEmpty(preItemList)) {
            String itemNo = itemInfo.getItemNo();
            List<BsPremanuItemInfo> itemPreList = preItemList.stream()
                    .filter(bs -> StringUtils.isNotBlank(itemNo) && itemNo.equals(bs.getItemNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemPreList)) {
                this.setDeliveryStr(itemInfo, addItem);
                return;
            }
            itemPreList.sort(Comparator.comparing(BsPremanuItemInfo::getCreateDate));
            addItem.setDeliveryProcess(itemPreList.get(Constant.INT_0).getDeliveryProcess());
            return;
        }
        this.setDeliveryStr(itemInfo, addItem);
    }

    /**
     * 根据基础信息设置配送工序
     *
     * @param itemInfo
     * @param addItem
     */
    private void setDeliveryStr(BsItemInfo itemInfo, BsPremanuItemInfo addItem) {
        if (null != itemInfo.getIsSmt()) {
            if (Constant.LEVEL_CODE.equals(itemInfo.getIsSmt().toString())) {
                addItem.setDeliveryProcess(Constant.SMT_DELIVERY);
            } else if (Constant.NUMBER_ONE.equals(itemInfo.getIsSmt().toString())) {
                addItem.setDeliveryProcess(Constant.DIP_DELIVERY);
            }
        }
    }


    /**
     * 导出
     *
     * @param fileName
     * @param title
     * @param rows
     * @param props
     * @param response
     */
    @Override
    public void exportModel(String fileName, String[] title, List rows, String[] props,
                            HttpServletResponse response) {
        String sheetName = ExcelName.BSPREMANUITEMINFO_EXPORT_SHEETNAME;
        HSSFWorkbook wb = ExcelUtils.getHSSFWorkbook(sheetName, title, rows, null, props);
        String msgHead = "getHSSFWorkbook:";
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            ImesExcelUtil.setResponseHeader(response, fileName);
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error(msgHead, e);
        } finally {
            if (null != os) {
                try {
                    os.close();
                } catch (Exception e) {
                    logger.error(msgHead, e);
                }
            }

            if (null != wb) {
                try {
                    wb.close();
                } catch (IOException e) {
                    logger.error("getHSSFWorkbook:", e);
                }
            }
        }
    }

    /**
     * 直接查询BS_BOM_HIERARCHICAL_HEAD以及DETAIL获取分阶信息
     */
    @Override
    public List<BsPremanuItemInfo> getBsBomInfo(BsPremanuItemInfo dto) throws Exception {
        if (StringUtils.isEmpty(dto.getBomCode())) {
            return new ArrayList<>();
        }
        List<String> bomCodeList = Arrays.asList(dto.getBomCode().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
        return bsPremanuItemInfoRepository.getBsBomInfo(bomCodeList);
    }

    /**
     * DIP调拨-直接查询BS_BOM_HIERARCHICAL_HEAD以及DETAIL获取分阶信息
     */
    @Override
    public List<BsPremanuItemInfo> getDipBomInfo(BsPremanuItemInfo dto) {
        if (StringUtils.isEmpty(dto.getBomCode())) {
            return new ArrayList<>();
        }
        List<String> bomCodeList = Arrays.asList(dto.getBomCode().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
        return bsPremanuItemInfoRepository.getDipBomInfo(bomCodeList);
    }

    @Override
    public BsBomHierarchicalHead getAssembleBomInfo(BsPremanuItemInfo dto) {
        if (StringUtils.isEmpty(dto.getBomCode())) {
            return new BsBomHierarchicalHead();
        }
        List<String> bomCodeList = Arrays.asList(dto.getBomCode().replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY).split(Constant.COMMA));
        return bsBomHierarchicalHeadRepository.getAssembleBomInfo(bomCodeList);
    }
    /* Started by AICoder, pid:yd11b60687p08f41418f0a046004a118dd071831 */
    /**
     * 通过批次获取 DIP 料
     *
     * @param prodPlanIdList 批次信息
     * @return DIP 物料清单
     */
    @Override
    public List<BsPremanuItemInfo> queryDipBomInfoByProdPlanIdBatch(List<String> prodPlanIdList) {
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return Collections.emptyList();
        }
        // 查询制造BOM 料单。或者原始料单
        List<String> bomCodeList = bProdBomHeaderRepository.queryOriginalCodeBatch(prodPlanIdList);
        return bsPremanuItemInfoRepository.getDipBomInfo(bomCodeList);
    }
    /* Ended by AICoder, pid:yd11b60687p08f41418f0a046004a118dd071831 */

    @Override
    @TransmittableHeader
    public void asyncCycleSaveSubLevelPremanuInfo(List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS) {
        List<AsyncBomDTO> list = new LinkedList<>();
        for (PsWorkOrderBasicDTO psWorkOrderBasicDTO : psWorkOrderBasicDTOS) {
            AsyncBomDTO temp = new AsyncBomDTO();
            temp.setItemNo(psWorkOrderBasicDTO.getItemNo());
            temp.setProdPlanId(psWorkOrderBasicDTO.getSourceTask());
            list.add(temp);
        }
        this.runBomCodeAsync(list);
    }

    /**
     * 运行BOM 分阶 异步逻辑
     *
     * @param list
     * @return
     */
    @Override
    @TransmittableHeader
    public BsAsyncDataDTO runBomCodeAsync(List<AsyncBomDTO> list) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        BsAsyncDataDTO bs = new BsAsyncDataDTO();
        bs.setHeadId(idGenerator.snowFlakeIdStr());
        bs.setStatus(MpConstant.AsyncConstant.PRECESSING);
        bs.setServiceKey(bs.getHeadId());
        bs.setServiceType(MpConstant.AsyncConstant.ALLOC_BOM);
        bs.setCreateBy(pair.getSecond());
        bs.setCreateDate(new Date());
        bs.setLastUpdateBy(pair.getSecond());
        bs.setLastUpdateDate(new Date());
        bs.setParams(JSON.toJSONString(list));
        bsAsyncDataRespository.insertDataBatch(new LinkedList<BsAsyncDataDTO>() {{
            add(bs);
        }});
        Pair<List<AsyncBomDTO>, BsAsyncDataDTO> dataDTOPair = Pair.of(list, bs);
        ThreadUtil.BOM_SPLIT_THREAD_POOL_EXECUTOR.execute(() -> {
            try {
                bsPreItemInfoAsyncService.runBomCodeAsync(pair, dataDTOPair);
            } catch (Exception e) {
                imesLogService.log(JSON.toJSONString(e), "bom async error");
            }
        });
        return bs;
    }

    @Override
    public void cycleSaveSubLevelPremanuInfo(List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        List<AsyncBomDTO> list = new LinkedList<>();
        for (PsWorkOrderBasicDTO item : psWorkOrderBasicDTOS) {
            AsyncBomDTO temp = new AsyncBomDTO();
            temp.setItemNo(item.getItemNo());
            temp.setProdPlanId(item.getSourceTask());
            list.add(temp);
        }
        bsPreItemInfoAsyncService.runBomCode(pair, list, new BsAsyncDataDTO());
    }

    @Override
    public Page<BsPremanuItemInfo> getBsItemPreInfo(BsPremanuItemInfo dto) throws Exception{
        this.checkQueryParams(dto);
        Page<BsPremanuItemInfo> pageInfo = new Page<>(dto.getPageInt(), dto.getRowsInt());
        pageInfo.setParams(dto);
        List<BsPremanuItemInfo> bsItemInfoList = bsPremanuItemInfoRepository.queryBsItemPreInfoList(pageInfo);
        this.setUserName(bsItemInfoList);
        pageInfo.setRows(bsItemInfoList);
        return pageInfo;
    }

    private void setUserName(List<BsPremanuItemInfo> list) throws Exception {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> userIdList = new ArrayList<>();
        for (BsPremanuItemInfo item : list) {
            userIdList.add(item.getCreateBy());
            userIdList.add(item.getLastUpdatedBy());

        }
        List<String> userDistinctList = userIdList.stream().distinct().collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(userDistinctList);
        for (BsPremanuItemInfo item : list) {
            String createBy = item.getCreateBy();
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(createBy);
            if (hrmPersonInfoDTO != null) {
                item.setCreateBy(hrmPersonInfoDTO.getEmpName() + createBy);
            }
            String lastUpdatedBy = item.getLastUpdatedBy();
            HrmPersonInfoDTO hrmPersonInfo = hrmPersonInfoDTOMap.get(lastUpdatedBy);
            if (hrmPersonInfo != null) {
                item.setLastUpdatedBy(hrmPersonInfo.getEmpName() + lastUpdatedBy);
            }
        }
    }
    private void checkQueryParams(BsPremanuItemInfo dto) {
        if (StringUtils.isEmpty(dto.getItemNo()) && StringUtils.isEmpty(dto.getTypeCode())) {
            if (StringUtils.isEmpty(dto.getTraceCode()) && StringUtils.isEmpty(dto.getDeliveryProcess())) {
                checkDate(dto);
            }
        }
    }

    private void checkDate(BsPremanuItemInfo dto) {
        if (dto.getCreateStartDate() == null && dto.getCreateEndDate() == null) {
            checkLastUpdatedDate(dto);
        } else {
            if (calDaysByDate(dto.getCreateEndDate(), dto.getCreateStartDate(), Constant.INT_0).compareTo(Constant.TIME_YEAR) > Constant.INT_0) {
                throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.DAY_OVER_ONE_YEAR);
            }
        }
    }

    private void checkLastUpdatedDate(BsPremanuItemInfo dto) {
        if (dto.getLastUpdatedStartDate() == null && dto.getLastUpdatedEndDate() == null) {
            throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.PLEASE_INPUT_ONE_CONDITION);
        } else {
            if (calDaysByDate(dto.getLastUpdatedEndDate(), dto.getLastUpdatedStartDate(), Constant.INT_0).compareTo(Constant.TIME_YEAR) > Constant.INT_0) {
                throw new MesBusinessException(com.zte.itp.msa.core.model.RetCode.BUSINESSERROR_CODE, MessageId.DAY_OVER_ONE_YEAR);
            }
        }
    }

    private BigDecimal calDaysByDate(Date endDate, Date startDate, int scale) {
        BigDecimal bg1000 = new BigDecimal(NumConstant.NUM_1000);
        long days = ((endDate.getTime() - startDate.getTime()) * NumConstant.NUM_1000) / Constant.DAY_TIME;
        return new BigDecimal(days).divide(bg1000,scale, BigDecimal.ROUND_HALF_UP);
    }
    @Override
    public Integer countExportTotal(BsPremanuItemInfo bsPremanuItemInfo) {
        return bsPremanuItemInfoRepository.countBsItemPreExportTotal(bsPremanuItemInfo);
    }

    @Override
    public List<BsPremanuItemInfo> queryExportData(BsPremanuItemInfo bsPremanuItemInfo, int pageNo, int pageSize) {
        this.checkQueryParams(bsPremanuItemInfo);
        Page<BsPremanuItemInfo> page = new Page<>(pageNo, pageSize);
        page.setParams(bsPremanuItemInfo);
        page.setSearchCount(false);
        List<BsPremanuItemInfo> pageList = bsPremanuItemInfoRepository.queryBsItemPreInfoList(page);
        if (CollectionUtils.isEmpty(pageList)){
            return null;
        }
        try {
            this.setUserName(pageList);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_OBTAIN_HR_INFO);
        }
        return pageList;
    }
}
