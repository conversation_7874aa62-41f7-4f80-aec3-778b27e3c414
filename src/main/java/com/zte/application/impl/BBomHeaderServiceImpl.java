/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.impl;

import com.zte.application.BBomDetailService;
import com.zte.application.BBomHeaderService;
import com.zte.application.BPcbLocationBomService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.infrastructure.feign.DatawbFeignService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.assembler.BBomDetailAssembler;
import com.zte.interfaces.assembler.BBomHeaderAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
public class BBomHeaderServiceImpl implements BBomHeaderService {

    @Autowired
    private BBomHeaderRepository bBomHeaderRepository;

    @Autowired
    private BBomDetailService bBomDetailService;

    @Autowired
    private BPcbLocationBomService bPcbLocationBomService;
    @Autowired
    private DatawbFeignService datawbFeignService;

    @Autowired
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    /**
     * 批量更新状态
     * @param bBomHeaderList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public long batchUpdateParseStatus(List<BBomHeader> bBomHeaderList) {
        if(CollectionUtils.isEmpty(bBomHeaderList)){
            return NumConstant.NUM_ZERO;
        }
        long count = NumConstant.NUM_ZERO;
        for (List<BBomHeader> bBomHeaders : CommonUtils.splitList(bBomHeaderList, Constant.INT_100)) {
            count+= bBomHeaderRepository.batchUpdateParseStatus(bBomHeaders);
        }
        return count;
    }

    /**
     * 批量查询bom信息
     *
     * @param productCodeList
     * @return List<BBomHeader>
     **/
    @Override
    public List<BBomHeader> selectBBomHeaderByProductCodeList(List<String> productCodeList) {
        List<BBomHeader> bBomHeaderList = new ArrayList<>();
        if(CollectionUtils.isEmpty(productCodeList)){
            return bBomHeaderList;
        }
        for (List<String> tempList : CommonUtils.splitList(productCodeList, Constant.NUMBER_900)) {
            List<BBomHeader> bBomHeaders = bBomHeaderRepository.selectBBomHeaderByProductCodeList(tempList);
            if(!CollectionUtils.isEmpty(bBomHeaders)){
                bBomHeaderList.addAll(bBomHeaders);
            }
        }

        return bBomHeaderList;
    }

    public void setbBomHeaderRepository(BBomHeaderRepository bBomHeaderRepository) {

        this.bBomHeaderRepository = bBomHeaderRepository;
    }

    public void setbPcbLocationBomService(BPcbLocationBomService bPcbLocationBomService) {
        this.bPcbLocationBomService = bPcbLocationBomService;
    }

    /**
     * 增加实体数据
     *
     * @param record
     **/
    public int insertBBomHeader(BBomHeader record) {

        return bBomHeaderRepository.insertBBomHeader(record);
    }

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    public int insertBBomHeaderSelective(BBomHeader record) {

        return bBomHeaderRepository.insertBBomHeaderSelective(record);
    }

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    public int deleteBBomHeaderById(BBomHeader record) {

        return bBomHeaderRepository.deleteBBomHeaderById(record);
    }

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    @Transactional(rollbackFor = Exception.class)
    public int updateBBomHeaderByIdSelective(BBomHeader record) {
        BBomHeader bBomHeader = bBomHeaderRepository.selectBatchByHeadId(record.getBomHeaderId());
        if (null == bBomHeader) {
            return Constant.NUM_ZERO;
        }
        int res = bBomHeaderRepository.updateBBomHeaderByIdSelective(record);
        if (res == NumConstant.NUM_ONE && (Constant.FLAG_Y.equals(bBomHeader.getZjSubcardFlag()) || Constant.FLAG_Y.equals(record.getZjSubcardFlag()))) {
            List<SyssubBomInfoDTO> list = new ArrayList<>();
            SyssubBomInfoDTO dto = new SyssubBomInfoDTO();
            dto.setBomNo(bBomHeader.getProductCode());
            dto.setCrrationBy(null != bBomHeader.getCreateBy() ? bBomHeader.getCreateBy() : Constant.STRING_EMPTY);
            dto.setLastUpdatedBy(null != bBomHeader.getLastUpdatedBy() ? bBomHeader.getLastUpdatedBy() : Constant.STRING_EMPTY);
            dto.setEnabledFlag(record.getZjSubcardFlag());
            list.add(dto);
            ServiceData serviceData = datawbFeignService.syssubbominfoSave(list);
            if (!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, serviceData.getCode().getMsg());
            }
            // note: 存在是否事物一致性问题，之后用TCC模式
        }
        return res;
    }

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    public int updateBBomHeaderById(BBomHeader record) {

        return bBomHeaderRepository.updateBBomHeaderById(record);
    }



    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return BBomHeader
     **/
    public BBomHeader selectBBomHeaderById(BBomHeader record) {

        return bBomHeaderRepository.selectBBomHeaderById(record);
    }

    /**
     * 增加实体数据
     *
     * @param record
     * @return List<BBomHeader>
     **/
    public Page<BBomHeaderDTO> selectBBomHeaderByProductCode(BBomHeaderDTO record) {

        Page<BBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<BBomHeader> bomList = bBomHeaderRepository.selectBBomHeaderByProductCode(pageInfo);
        pageInfo.setRows(BBomHeaderAssembler.toBBomHeaderDTOList(bomList));
        return pageInfo;
    }

	/**
	 * 查询单板对应的价格
	 *
	 * @param record
	 * @return List<BBomHeader>
	 **/
	public Page<BBomHeaderDTO> selectBoardPrice(BBomHeaderDTO record) throws Exception {
		Page<BBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
		// 和前端校验保持一致，输入的料单代码不得超过10个
		if(record.getInProductCodes().size() > Constant.INT_10){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NOS_MORE_THAN_TEN);
		}
		// 料单代码和料单名称必输其一
		if(record.getInProductCodes().size() == Constant.INT_0 && StringUtils.isBlank(record.getChiDesc())){
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_CAN_NOT_BE_NULL);
		}
		pageInfo.setParams(record);
		List<BBomHeader> bomList = bBomHeaderRepository.selectBBomHeaderByCondition(pageInfo);
		// 查询到结果后需要去关联查价格
		if (!CollectionUtils.isEmpty(bomList)) {
			List<String> productCodeList = bomList.stream().map(e -> e.getProductCode()).collect(Collectors.toList());
			if(productCodeList.size() > Constant.INT_1000){
				throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NOS_MORE_THAN_ONE_THOUSAND);
			}
			List<BaBomHeadDTO> baBomHeadDTOList = DatawbRemoteService.getBomInfoByBomNoList(productCodeList);
			// 如果查询到价格信息就匹配展示
			if(!CollectionUtils.isEmpty(baBomHeadDTOList)){
				//合并两个list
				bomList.stream().map(m -> {
					baBomHeadDTOList.stream().filter(m2-> Objects.equals(m.getProductCode(),m2.getBomNo())).forEach(m2-> {
						m.setStandardCost(m2.getStandardCost());
					});
					return m;
				}).collect(Collectors.toList());
			}
		}
		pageInfo.setRows(BBomHeaderAssembler.toBBomHeaderDTOList(bomList));
		return pageInfo;
	}

    /**
     * getList bom头带bom明细
     *
     * @param record
     * @return List<BBomHeader>
     * <AUTHOR>
     **/
    public List<BBomInfoDTO> getBBomInfoList(BBomInfoDTO record) {

        List<BBomInfoDTO> listInfo = null;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("headerId", record.getBomHeaderId());
        map.put("productCode", record.getProductCode());
        map.put("orderField", record.getSort());
        map.put("order", record.getOrder());

        List<BBomHeader> list = bBomHeaderRepository.getList(map);
        if (!CollectionUtils.isEmpty(list)) {
            listInfo = BBomHeaderAssembler.toBBomInfoList(list);
            for (BBomInfoDTO info : listInfo) {
                List<BBomDetail> listDetail = bBomDetailService.getList(info);
                if (record.isGetDetail() && !CollectionUtils.isEmpty(listDetail)) {
                    info.setListDetail(BBomDetailAssembler.toBBomDetailDTOList(listDetail));
                }
            }
        }
        return listInfo;
    }

    @Override
    public List<BBomDetailDTO> getItemNoByPositionAndBomNo(String position, String bomNo,String prodplanId) throws MesBusinessException {
        List<BBomDetailDTO> itemNo = new ArrayList<>();
        if (StringUtils.isBlank(position) || StringUtils.isBlank(bomNo)) {
            String[] params = {"params position and bomNo can not be blank"};
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_ERROR, params);
        }

        String[] positionArray = position.split(Constant.DON);
        if (positionArray.length < NumConstant.NUM_ONE || StringUtils.isBlank(positionArray[NumConstant.NUM_ZERO])) {
            return itemNo;
        }

        String firstPosition = positionArray[NumConstant.NUM_ZERO].trim();

        // 根据料单代码查询料单头信息
        Map<String, Object> record = new HashMap<>();
        record.put("productCode", bomNo);
        List<BBomHeader> bBomHeaderList = bBomHeaderRepository.getList(record);
        if (CollectionUtils.isEmpty(bBomHeaderList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST);
        }

        // 查询变更明细
        BProdBomChangeDetailDTO dto = new BProdBomChangeDetailDTO();
        dto.setProdplanId(prodplanId);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS = bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(dto);

        for (BBomHeader bomHeader : bBomHeaderList) {
            // 查询bom详情信息
            List<BBomDetailDTO> bBomDetailList = bBomDetailService.selectDetailByHeaderId(bomHeader.getBomHeaderId());
            if (CollectionUtils.isEmpty(bBomDetailList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST);
            }
            if (CollectionUtils.isNotEmpty(bProdBomChangeDetailDTOS)) {
                bBomDetailList = mbomItemCodeChange(bProdBomChangeDetailDTOS, bBomDetailList);
            }

            itemNo = getString(position, itemNo, positionArray, firstPosition, bBomDetailList);
            if (CollectionUtils.isEmpty(itemNo)) {
                // 如果已匹配到输入的位号物料代码
                break;
            }
        }
        return itemNo;
    }

    private List<BBomDetailDTO> mbomItemCodeChange(List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS, List<BBomDetailDTO> bBomDetailList) {
        Map<String, String> bomChangeItemCode =
                bProdBomChangeDetailDTOS.stream().collect(Collectors.toMap(m -> m.getOriginalItemCode(), m -> m.getItemCode(),
                        (m1, m2) -> m1));
        for (BBomDetailDTO bBomDetailDTO : bBomDetailList) {
            bBomDetailDTO.setItemCode(bomChangeItemCode.getOrDefault(bBomDetailDTO.getItemCode(),
                    bBomDetailDTO.getItemCode()));
        }
        Map<String, BBomDetailDTO> codeMap = new HashMap<>();
        for (BBomDetailDTO bBomDetailDTO : bBomDetailList) {
            BBomDetailDTO dto = codeMap.getOrDefault(bBomDetailDTO.getItemCode(), null);
            if (dto == null) {
                codeMap.put(bBomDetailDTO.getItemCode(), bBomDetailDTO);
                continue;
            }
            bBomDetailDTO.setPositionExt(bBomDetailDTO.getPositionExt() + Constant.COMMA + dto.getPositionExt());
            bBomDetailDTO.setPlaceNoExt1(bBomDetailDTO.getPlaceNoExt1() + Constant.COMMA + dto.getPlaceNoExt1());
            bBomDetailDTO.setPlaceNoExt2(bBomDetailDTO.getPlaceNoExt2() + Constant.COMMA + dto.getPlaceNoExt2());
            bBomDetailDTO.setPlaceNoExt3(bBomDetailDTO.getPlaceNoExt3() + Constant.COMMA + dto.getPlaceNoExt3());
            bBomDetailDTO.setPlaceNoExt4(bBomDetailDTO.getPlaceNoExt4() + Constant.COMMA + dto.getPlaceNoExt4());
            bBomDetailDTO.setPlaceNoExt5(bBomDetailDTO.getPlaceNoExt5() + Constant.COMMA + dto.getPlaceNoExt5());
            bBomDetailDTO.setPlaceNoExt6(bBomDetailDTO.getPlaceNoExt6() + Constant.COMMA + dto.getPlaceNoExt6());
            bBomDetailDTO.setPlaceNoExt7(bBomDetailDTO.getPlaceNoExt7() + Constant.COMMA + dto.getPlaceNoExt7());
            codeMap.put(bBomDetailDTO.getItemCode(), bBomDetailDTO);
        }
        return codeMap.values().stream().collect(Collectors.toList());
    }

    private List<BBomDetailDTO> getString(String position, List<BBomDetailDTO> itemNo, String[] positionArray, String firstPosition, List<BBomDetailDTO> bBomDetailList) throws MesBusinessException {
        for (BBomDetailDTO detail : bBomDetailList) {
            List<String> positionList = bPcbLocationBomService.getPositionList(detail);
            // 判断是否包含第一个位号,不包含继续循环
            if (!positionList.contains(firstPosition)) {
                continue;
            }
            // 判断是否包含所有位号(不包含则报错)
            if (!positionList.containsAll(Arrays.asList(positionArray))) {
                String[] params = {detail.getItemCode(), firstPosition, position};
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_CONTAINS_ONE_POSITION_BUT_NOT_ALL_POSITION, params);
            }
            detail.setItemName(detail.getChiDesc());
            itemNo.add(detail);
        }
        return itemNo;
    }

    @Override
    public List<String> getHasCadProductCode(List<String> productCodes) {
        return bBomHeaderRepository.getHasCadProductCode(productCodes);
    }

    @Override
    public List<ProdBindingSettingDTO> getAllChildBoard(List<String> productCodeList) {
        List<ProdBindingSettingDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(productCodeList)) {
            return list;
        }
        List<List<String>> listOfList = CommonUtils.splitList(productCodeList);
        for (List<String> pCodeList : listOfList) {
            List<ProdBindingSettingDTO> tempList = bBomHeaderRepository.getAllChildBoard(pCodeList);
            if (!CollectionUtils.isEmpty(tempList)) {
                list.addAll(tempList);
            }
        }
        return list;
    }

    @Override
    public List<BBomHeader> getProductCodeByItemCode(String itemCode) {
        return bBomHeaderRepository.getProductCodeByItemCode(itemCode);
    }

    @Override
    public List<BBomHeader> getAllChildBoardByProductCode(String productCode) {
        return bBomHeaderRepository.getAllChildBoardByProductCode(productCode);
    }

    /**
     * 获取已经导入CAD 文件的料单代码
     *
     * @param productCodeList 料单代码
     * @return 已经导入CAD 料单
     */
    @Override
    public List<String> queryImportCadProduct(List<String> productCodeList) {
        List<String> resultList = new LinkedList<>();
        if (CollectionUtils.isEmpty(productCodeList)) {
            return resultList;
        }
        List<List<String>> splitList = CommonUtils.splitList(productCodeList, Constant.BATCH_SIZE_500);
        for (List<String> list : splitList) {
            List<String> productList = bBomHeaderRepository.queryImportCadProduct(list);
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            resultList.addAll(productList);
        }
        return resultList;
    }

    /**
     * 根据料单代码获取单板及工艺路径信息
     *
     * @param bomNo 料单代码
     * @return BaCraftRoutingValidateDTO
     **/
    @Override
    public BaCraftRoutingValidateDTO getBomInfoWithRouteByBomNo(String bomNo){
        RetCode retCode = null;
        BaCraftRoutingValidateDTO baCraftRoutingValidateDTO = new BaCraftRoutingValidateDTO();

        if(bomNo.length() != NumConstant.NUM_FIFTEEN){
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.MATERAIL_LIST_MUST_BE_CONTAIN_15_CHARACTERS));
            baCraftRoutingValidateDTO.setRetCode(retCode);
            return baCraftRoutingValidateDTO;
        }

        BaCraftRoutingInfoDTO baCraftRoutingInfoDTO = bBomHeaderRepository.getBomInfoWithRouteByBomNo(bomNo);

        if(baCraftRoutingInfoDTO == null){
            retCode = new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            retCode.setMsg(CommonUtils.getLmbMessage(MessageId.NO_BOM_INFORMATION_OR_NO_ROUTE));
        }else {
            retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
            baCraftRoutingInfoDTO.setCraftDesc(StringUtils.replace(baCraftRoutingInfoDTO.getCraftDesc(),"->", ">"));
            baCraftRoutingValidateDTO.setBaCraftRoutingInfoDTO(baCraftRoutingInfoDTO);
        }
        baCraftRoutingValidateDTO.setRetCode(retCode);

        return baCraftRoutingValidateDTO;
    }

    @Override
    public List<BBomHeaderDTO> queryBBomHeadListByBomIds(Set<String> bomIdSet) {
        if (CollectionUtils.isEmpty(bomIdSet)) {
            return new ArrayList<>();
        }
        return bBomHeaderRepository.queryBBomHeadListByBomIds(bomIdSet);
    }

    @Override
    public String queryBBomHeadIdByProductCode(String productCode) {
        return bBomHeaderRepository.queryBBomHeadIdByProductCode(productCode);
    }

    public Page<BBomHeaderDTO> bBomHeaderListLike(BBomHeaderDTO record) {
        Page<BBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<BBomHeader> bomList = bBomHeaderRepository.bBomHeaderListLike(pageInfo);
        pageInfo.setRows(BBomHeaderAssembler.toBBomHeaderDTOList(bomList));
        return pageInfo;
    }
}
