package com.zte.application.impl;

import com.alibaba.fastjson2.JSON;
import com.zte.application.BBomDetailService;
import com.zte.application.BBomHeaderService;
import com.zte.application.BProdBomChangeDetailService;
import com.zte.application.BProdBomDetailService;
import com.zte.application.BProdBomHeaderService;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.KafkaConstant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BProdBomDetail;
import com.zte.domain.model.BProdBomHeader;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.KafkaLocalMessageDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeWriteBackDTO;
import com.zte.springbootframe.common.annotation.AsyncExport;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.FIELD_LOOKUP_TYPE;

/**
 * ClassName: BProdBomHeaderServiceImpl
 * Description:
 *
 * <AUTHOR>
 * @date 2024/12/13 下午2:39
 */
@Service
public class BProdBomHeaderServiceImpl implements BProdBomHeaderService {

    @Autowired
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Autowired
    private BProdBomDetailService bProdBomDetailService;
    @Autowired
    private BProdBomChangeDetailService bProdBomChangeDetailService;
    @Autowired
    private CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Autowired
    private OpenApiRemoteService openApiRemoteService;
    @Autowired
    private PsTaskService psTaskService;
    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;
    @Autowired
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;

    @Autowired
    private BBomDetailService bBomDetailService;
    @Autowired
    private BBomHeaderService bBomHeaderervice;
    @Autowired
    protected IdGenerator idGenerator;
    @Autowired
    private ApsRemoteService apsRemoteService;
    @Autowired
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Autowired
    private BBomHeaderRepository bBomHeaderRepository;
    @Autowired
    private AsyncExportFileCommonService asyncExportFileCommonService;

    @Value("${aps.deal.derivedCode.maxNum:20}")
    private String maxNum;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("aps衍生码变更处理")
    public List<ApsDerivativeCodeQueryDTO> apsDerivedCodeConsumer(String data) throws Exception {
        if (StringUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = JSON.parseArray(data, ApsDerivativeCodeQueryDTO.class);
        try {
            if (CollectionUtils.isEmpty(apsDerivativeCodeQueryDTOList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR);
            }
            if (apsDerivativeCodeQueryDTOList.size() > Integer.parseInt(maxNum)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MORE_THAN_MAX_NUM, new Object[Integer.parseInt(maxNum)]);
            }
            this.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
            this.writeBackAPS(data, apsDerivativeCodeQueryDTOList);
        } catch (Exception e) {
            psTaskService.addRetryNumberOrSaveKafkaMsg(e, data, KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC,
                    KafkaConstant.ApsKafkaConstant.APS2_IMES_PROD_PLAN_MODIFY, MessageId.APS_DERIVATIVECODE_NO_INFO_FACTORY_ID_IS_NULL);
            this.writeBackAPS(data, apsDerivativeCodeQueryDTOList, e);
        }
        return apsDerivativeCodeQueryDTOList;
    }

    private void writeBackAPS(String data, List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList, Exception e) throws Exception {
        if (CollectionUtils.isEmpty(apsDerivativeCodeQueryDTOList)) {
            return;
        }
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : apsDerivativeCodeQueryDTOList) {
            apsDerivativeCodeQueryDTO.setErrorMsg(e.getMessage());
        }
        this.writeBackAPS(data, apsDerivativeCodeQueryDTOList);
    }

    private void writeBackAPS(String data, List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList) throws Exception {
        List<ApsDerivativeCodeWriteBackDTO> apsDerivativeCodeWriteBackDTOS = new ArrayList<>(apsDerivativeCodeQueryDTOList.size());
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : apsDerivativeCodeQueryDTOList) {
            ApsDerivativeCodeWriteBackDTO apsDerivativeCodeWriteBackDTO = new ApsDerivativeCodeWriteBackDTO();
            BeanUtils.copyProperties(apsDerivativeCodeQueryDTO, apsDerivativeCodeWriteBackDTO);
            apsDerivativeCodeWriteBackDTO.setDealResult(StringUtils.isEmpty(apsDerivativeCodeQueryDTO.getErrorMsg()) ? Constant.FLAG_Y : Constant.FLAG_N);
            apsDerivativeCodeWriteBackDTOS.add(apsDerivativeCodeWriteBackDTO);
        }
        kafkaMessageProducer.sendMsg(JSON.toJSONString(apsDerivativeCodeWriteBackDTOS), KafkaConstant.CENTERFACTORY,
                KafkaConstant.ApsKafkaConstant.PROD_PLAN_ID_PRODUCER);
        batchInsertKafkaData(data, apsDerivativeCodeQueryDTOList);
    }

    /**
     * 保存kafka消息
     *
     * @param data
     * @param apsDerivativeCodeQueryDTOList
     */
    private void batchInsertKafkaData(String data, List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList) {
        KafkaLocalMessageDTO kafkaLocalMessageDTO = new KafkaLocalMessageDTO();
        kafkaLocalMessageDTO.setId(UUID.randomUUID().toString());
        kafkaLocalMessageDTO.setTopic(KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC);
        kafkaLocalMessageDTO.setTopicKey(KafkaConstant.ApsKafkaConstant.APS2_IMES_PROD_PLAN_MODIFY);
        kafkaLocalMessageDTO.setEnabledFlag(Constant.FLAG_Y);
        kafkaLocalMessageDTO.setCreatedBy(Constant.SYSTEM);
        kafkaLocalMessageDTO.setLastUpdatedBy(Constant.SYSTEM);
        kafkaLocalMessageDTO.setMessageContext(data);
        kafkaLocalMessageDTO.setErrorMessage(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        List<KafkaLocalMessageDTO> list = new LinkedList<>();
        list.add(kafkaLocalMessageDTO);
        kafkaLocalMessageRepository.batchInsertData(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ApsDerivativeCodeQueryDTO> dealApsDerivedCode(List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList,boolean isModify) {
        // 1 校验任务是否已释放到imes,并且校验任务是否已是否
        List<ApsDerivativeCodeQueryDTO> surplusCorrectList = apsDerivativeCodeQueryDTOList;
        if(isModify){
            //衍生码变更才需要校验,任务释放时处理衍生码不需要
            surplusCorrectList = this.verifyIfTheTaskHasBeenReleased(apsDerivativeCodeQueryDTOList);
            if (CollectionUtils.isEmpty(surplusCorrectList)) {
                return apsDerivativeCodeQueryDTOList;
            }
        }
        //过滤已处理过的
        surplusCorrectList = filterIfItHasBeenProcessed(surplusCorrectList);
        if (CollectionUtils.isEmpty(surplusCorrectList)) {
            return apsDerivativeCodeQueryDTOList;
        }
        // 2 如果存在，查询是否存在已调拨或者正在调拨
        surplusCorrectList = this.verifyTaskAllocation(surplusCorrectList);
        if (CollectionUtils.isEmpty(surplusCorrectList)) {
            return apsDerivativeCodeQueryDTOList;
        }
        // 3 如果存在，调aps接口查询衍生码信息,如果是衍生码变更，删除未查到的批次对应制造BOM
        surplusCorrectList = this.getDerivativeCodeInformation(surplusCorrectList,isModify);
        if (CollectionUtils.isEmpty(surplusCorrectList)) {
            return apsDerivativeCodeQueryDTOList;
        }
        // 4 查询原始bom信息
        surplusCorrectList = this.getOriginalBomInfo(surplusCorrectList);
        if (CollectionUtils.isEmpty(surplusCorrectList)) {
            return apsDerivativeCodeQueryDTOList;
        }
        //5 查询批次已生产制造BOM信息,存在删除
        Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap = this.deleteTheOriginalManufacturingBOM(surplusCorrectList);

        // 6 生成制造BOM
        this.generateManufacturingBOM(surplusCorrectList, bProdBomHeaderDTOHashMap);

        return apsDerivativeCodeQueryDTOList;
    }

    private Map<String, List<BProdBomHeaderDTO>> deleteTheOriginalManufacturingBOM(List<ApsDerivativeCodeQueryDTO> surplusCorrectList) {
        List<String> productCodeList = surplusCorrectList.stream().map(e -> e.getItemNo()).distinct().collect(Collectors.toList());
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = bProdBomHeaderRepository.queryByItemNoList(productCodeList);
        Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap = new HashMap<>();
        if (CollectionUtils.isEmpty(bProdBomHeaderDTOS)) {
            return bProdBomHeaderDTOHashMap;
        }
        bProdBomHeaderDTOHashMap = bProdBomHeaderDTOS.stream().collect(Collectors.groupingBy(BProdBomHeaderDTO::getOriginalProductCode));
        //变更批次
        List<String> prodplanIdList = surplusCorrectList.stream().map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        //变更批次已存在制造bom记录
        List<String> existProdplanIdList = bProdBomHeaderDTOS.stream().filter(e -> prodplanIdList.contains(e.getProdplanId())).map(e -> e.getProdplanId()).distinct().collect(Collectors.toList());
        this.deleteByProdplanIds(existProdplanIdList);
        return bProdBomHeaderDTOHashMap;
    }

    private void deleteByProdplanIds(List<String> existProdplanIdList) {
        if (CollectionUtils.isNotEmpty(existProdplanIdList)) {
            bProdBomHeaderRepository.deleteByProdplanIds(existProdplanIdList);
            bProdBomChangeDetailService.deleteByProdplanIds(existProdplanIdList, Constant.SYSTEM);
        }
    }

    /**
     * 过滤
     *
     * @param surplusCorrectList
     * @return
     */
    private List<ApsDerivativeCodeQueryDTO> filterIfItHasBeenProcessed(List<ApsDerivativeCodeQueryDTO> surplusCorrectList) {
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOList = bProdBomChangeDetailService.queryByProdplanIdAndChangeVersion(surplusCorrectList);
        if (CollectionUtils.isEmpty(bProdBomChangeDetailDTOList)) {
            return surplusCorrectList;
        }
        Map<String, List<BProdBomChangeDetailDTO>> map = bProdBomChangeDetailDTOList.stream().collect(Collectors.groupingBy(k -> k.getProdplanId() + Constant.UNDER_LINE + k.getChangeId()));
        return surplusCorrectList.stream().filter(e -> map.get(e.getProdplanId() + Constant.UNDER_LINE + e.getChangeVersion()) == null).collect(Collectors.toList());
    }

    /**
     * 生成制造BOM
     *
     * @param surplusCorrectList
     * @param bProdBomHeaderDTOHashMap
     */
    private void generateManufacturingBOM(List<ApsDerivativeCodeQueryDTO> surplusCorrectList, Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap) {
        List<BProdBomHeader> insertHeaderList = new ArrayList<>();
        List<BProdBomDetail> insertDetailList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> insertChangeList = new ArrayList<>();
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : surplusCorrectList) {
            BBomHeader bBomHeader = apsDerivativeCodeQueryDTO.getBBomHeader();
            if (bBomHeader == null) {
                continue;
            }
            //制造bom明细
            List<BBomDetailDTO> bBomDetailDTOList = apsDerivativeCodeQueryDTO.getBBomDetailList();
            if (CollectionUtils.isEmpty(bBomDetailDTOList)) {
                continue;
            }
            List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = apsDerivativeCodeQueryDTO.getApsDerivativeCodeDTOList();
            if (CollectionUtils.isEmpty(apsDerivativeCodeDTOList)) {
                continue;
            }
            bBomDetailDTOList.forEach(p->p.setOriginalItemCode(p.getItemCode()));
            //衍生码最后更新人
            String lastUpdatedBy = apsDerivativeCodeDTOList.get(NumConstant.NUM_ZERO).getLastUpdatedBy();
            BProdBomHeader bProdBomHeader = this.generateBProdBomHeader(bProdBomHeaderDTOHashMap, apsDerivativeCodeQueryDTO, apsDerivativeCodeDTOList, bBomHeader);
            bProdBomHeader.setLastUpdatedBy(lastUpdatedBy);
            insertHeaderList.add(bProdBomHeader);
            if(apsDerivativeCodeQueryDTO.isThereANewAddition()){
                Map<String, BProdBomDetail> bProdBomDetailHashMap = this.generateBProdBomDetailMap(apsDerivativeCodeDTOList, bBomDetailDTOList, bProdBomHeader.getBomHeaderId());
                insertDetailList.addAll(bProdBomDetailHashMap.values());
            }
            //制造bom变更记录
            this.generateChangeList(apsDerivativeCodeQueryDTO, bBomDetailDTOList, apsDerivativeCodeDTOList, bProdBomHeader.getBomHeaderId(), insertChangeList);
        }
        this.batchInsert(insertHeaderList);
        bProdBomChangeDetailService.batchInsert(insertChangeList);
        bProdBomDetailService.batchInsert(insertDetailList);
    }

    private BProdBomHeader generateBProdBomHeader(Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap,
                                                  ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO,
                                                  List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList,
                                                  BBomHeader bBomHeader) {
        BProdBomHeader bProdBomHeader = null;
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = bProdBomHeaderDTOHashMap.get(apsDerivativeCodeQueryDTO.getItemNo());
        if(bProdBomHeaderDTOList == null){
            bProdBomHeaderDTOList = new ArrayList<>();
        }
        BProdBomHeaderDTO bProdBomHeaderDTO = this.getSameBProdHeader(bProdBomHeaderDTOList, apsDerivativeCodeDTOList);
        if(bProdBomHeaderDTO == null){
            apsDerivativeCodeQueryDTO.setThereANewAddition(true);
            String headerId = idGenerator.snowFlakeIdStr();
            //制造bom头信息
            bProdBomHeader = this.generateBProdBonHeader(bProdBomHeaderDTOHashMap, apsDerivativeCodeQueryDTO, bBomHeader, headerId);
        }else{
            apsDerivativeCodeQueryDTO.setThereANewAddition(false);
            bProdBomHeader = new BProdBomHeader();
            BeanUtils.copyProperties(bProdBomHeaderDTO,bProdBomHeader);
            bProdBomHeader.setProdplanId(apsDerivativeCodeQueryDTO.getProdplanId());
        }
        for (ApsDerivativeCodeDTO apsDerivativeCodeDTO : apsDerivativeCodeDTOList) {
            BProdBomHeaderDTO bomHeaderDTO = new BProdBomHeaderDTO();
            BeanUtils.copyProperties(bProdBomHeader,bomHeaderDTO);
            bomHeaderDTO.setOriginalItemCode(apsDerivativeCodeDTO.getItemNo());
            bomHeaderDTO.setItemCode(apsDerivativeCodeDTO.getDeriveItemNo());
            bProdBomHeaderDTOList.add(bomHeaderDTO);
        }
        bProdBomHeaderDTOHashMap.put(apsDerivativeCodeQueryDTO.getItemNo(),bProdBomHeaderDTOList);
        return bProdBomHeader;
    }

    private BProdBomHeaderDTO getSameBProdHeader(List<BProdBomHeaderDTO> bProdBomHeaderDTOList, List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList) {
        if(CollectionUtils.isEmpty(bProdBomHeaderDTOList)){
           return null;
        }
        //按批次分组
        Map<String, List<BProdBomHeaderDTO>> prodplanIdMap = bProdBomHeaderDTOList.stream().collect(Collectors.groupingBy(BProdBomHeaderDTO::getProdplanId));
        for (Map.Entry<String, List<BProdBomHeaderDTO>> entry : prodplanIdMap.entrySet()) {
            List<BProdBomHeaderDTO> prodplanIdList = entry.getValue();
            if(prodplanIdList.size() != apsDerivativeCodeDTOList.size()){
                continue;
            }
            boolean isSame = true;
            List<String> existItemInfoList = prodplanIdList.stream().map(e->(e.getOriginalItemCode()+Constant.UNDER_LINE+e.getItemCode())).collect(Collectors.toList());
            for (ApsDerivativeCodeDTO apsDerivativeCodeDTO : apsDerivativeCodeDTOList) {
                String key = apsDerivativeCodeDTO.getItemNo()+Constant.UNDER_LINE+apsDerivativeCodeDTO.getDeriveItemNo();
                if(!existItemInfoList.contains(key)){
                    isSame = false;
                    break;
                }
            }
            if(isSame){
                return prodplanIdList.get(NumConstant.NUM_ZERO);
            }
        }
        return null;
    }

    private void generateChangeList(ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO, List<BBomDetailDTO> bBomDetailDTOList, List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList, String headerId, List<BProdBomChangeDetailDTO> insertChangeList) {
        Map<String, BBomDetailDTO> detailDTOMap = bBomDetailDTOList.stream().collect(Collectors.toMap(k -> k.getOriginalItemCode(), v -> v, (oldValue, newValue) -> newValue));
        for (ApsDerivativeCodeDTO apsDerivativeCodeDTO : apsDerivativeCodeDTOList) {
            BBomDetailDTO detailDTO = detailDTOMap.get(apsDerivativeCodeDTO.getItemNo());
            BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
            bProdBomChangeDetailDTO.setBomChangeDetailId(idGenerator.snowFlakeIdStr());
            bProdBomChangeDetailDTO.setBomHeaderId(headerId);
            bProdBomChangeDetailDTO.setProdplanId(apsDerivativeCodeQueryDTO.getProdplanId());
            bProdBomChangeDetailDTO.setItemCode(apsDerivativeCodeDTO.getDeriveItemNo());
            bProdBomChangeDetailDTO.setOriginalItemCode(apsDerivativeCodeDTO.getItemNo());
            bProdBomChangeDetailDTO.setUsageCount(BigDecimal.ZERO);
            if (detailDTO != null) {
                bProdBomChangeDetailDTO.setUsageCount(detailDTO.getUsageCount());
            }
            bProdBomChangeDetailDTO.setChangeId(apsDerivativeCodeQueryDTO.getChangeVersion());
            bProdBomChangeDetailDTO.setCreateBy(apsDerivativeCodeDTO.getCreatedBy());
            bProdBomChangeDetailDTO.setLastUpdatedBy(apsDerivativeCodeDTO.getLastUpdatedBy());
            insertChangeList.add(bProdBomChangeDetailDTO);
        }
    }

    private Map<String, BProdBomDetail> generateBProdBomDetailMap(List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList, List<BBomDetailDTO> bBomDetailDTOList, String headerId) {
        Map<String, ApsDerivativeCodeDTO> apsDerivativeCodeDTOMap = apsDerivativeCodeDTOList.stream().collect(Collectors.toMap(k -> k.getItemNo(), v -> v, (oldValue, newValue) -> newValue));

        Map<String, BProdBomDetail> bProdBomDetailHashMap = new HashMap<>(bBomDetailDTOList.size());
        for (BBomDetailDTO bBomDetailDTO : bBomDetailDTOList) {
            String itemCode = bBomDetailDTO.getItemCode();
            //获取aps衍生码变更信息
            ApsDerivativeCodeDTO apsDerivativeCodeDTO = apsDerivativeCodeDTOMap.get(itemCode);
            if (apsDerivativeCodeDTO != null) {
                bBomDetailDTO.setItemCode(apsDerivativeCodeDTO.getDeriveItemNo());
                itemCode = apsDerivativeCodeDTO.getDeriveItemNo();
                apsDerivativeCodeDTO.setUsageCount(bBomDetailDTO.getUsageCount());
            }
            BProdBomDetail bProdBomDetail = null;
            BigDecimal usageCount = bBomDetailDTO.getUsageCount() == null ? BigDecimal.ZERO : bBomDetailDTO.getUsageCount();
            if (bProdBomDetailHashMap.keySet().contains(itemCode)) {
                bProdBomDetail = bProdBomDetailHashMap.get(bBomDetailDTO.getItemCode());
                bProdBomDetail.setUsageCount(bProdBomDetail.getUsageCount().add(usageCount));
                bProdBomDetailHashMap.put(itemCode, bProdBomDetail);
            } else {
                bProdBomDetail = new BProdBomDetail();
                BeanUtils.copyProperties(bBomDetailDTO, bProdBomDetail);
                bProdBomDetail.setBomHeaderId(headerId);
                bProdBomDetail.setBomDetailId(idGenerator.snowFlakeIdStr());
                bProdBomDetailHashMap.put(itemCode, bProdBomDetail);
            }
        }
        return bProdBomDetailHashMap;
    }

    private BProdBomHeader generateBProdBonHeader(Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap, ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO, BBomHeader bBomHeader, String headerId) {
        BProdBomHeader bProdBomHeader = new BProdBomHeader();
        BeanUtils.copyProperties(bBomHeader, bProdBomHeader);
        bProdBomHeader.setBomHeaderId(headerId);
        bProdBomHeader.setProdplanId(apsDerivativeCodeQueryDTO.getProdplanId());
        bProdBomHeader.setOriginalProductCode(bBomHeader.getProductCode());
        this.setProductCode(bProdBomHeaderDTOHashMap, bProdBomHeader);
        return bProdBomHeader;
    }

    private void batchInsert(List<BProdBomHeader> insertHeaderList) {
        for (List<BProdBomHeader> bProdBomHeaders : CommonUtils.splitList(insertHeaderList, NumConstant.NUM_HUNDRED)) {
            bProdBomHeaderRepository.batchInsert(bProdBomHeaders);
        }
    }

    /**
     * 生成版本号
     *
     * @param bProdBomHeaderDTOHashMap
     * @param bProdBomHeader
     */
    private void setProductCode(Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap, BProdBomHeader bProdBomHeader) {
        String originalProductCode = bProdBomHeader.getOriginalProductCode();
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = bProdBomHeaderDTOHashMap.get(originalProductCode);
        //当前制造bom料单
        if (CollectionUtils.isEmpty(bProdBomHeaderDTOList)) {
            bProdBomHeader.setProductCode(originalProductCode + Constant.HORIZON + NumConstant.STR_ONE);
            this.addMap(bProdBomHeaderDTOHashMap, bProdBomHeader,bProdBomHeaderDTOList);
            return;
        }
        List<Integer> currProductCodeList = bProdBomHeaderDTOList.stream().map(e -> Integer.parseInt(StringUtils.split(e.getProductCode(), Constant.HORIZON)[NumConstant.NUM_ONE])).distinct().sorted().collect(Collectors.toList());

        bProdBomHeader.setProductCode(originalProductCode + Constant.HORIZON + (currProductCodeList.get(currProductCodeList.size()-NumConstant.NUM_ONE) + NumConstant.NUM_ONE));
        this.addMap(bProdBomHeaderDTOHashMap, bProdBomHeader,bProdBomHeaderDTOList);
    }

    private static void addMap(Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap, BProdBomHeader bProdBomHeader,List<BProdBomHeaderDTO> bProdBomHeaderDTOList) {
        if(CollectionUtils.isEmpty(bProdBomHeaderDTOList)){
            bProdBomHeaderDTOList = new ArrayList<>();
        }
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        BeanUtils.copyProperties(bProdBomHeader,bProdBomHeaderDTO);
        bProdBomHeaderDTOList.add(bProdBomHeaderDTO);
        bProdBomHeaderDTOHashMap.put(bProdBomHeader.getOriginalProductCode(),bProdBomHeaderDTOList);
    }

    /**
     * 查询原始BOM信息
     *
     * @param surplusCorrectList
     * @return
     */
    private List<ApsDerivativeCodeQueryDTO> getOriginalBomInfo(List<ApsDerivativeCodeQueryDTO> surplusCorrectList) {
        List<String> itemNoList = surplusCorrectList.stream().map(e -> e.getItemNo()).distinct().collect(Collectors.toList());
        List<BBomHeader> bBomHeaderList = bBomHeaderervice.selectBBomHeaderByProductCodeList(itemNoList);
        bBomHeaderList = CollectionUtils.isEmpty(bBomHeaderList) ? new ArrayList<>() : bBomHeaderList;
        //查询bom明细
        Map<String, BBomHeader> bomHeaderMap = bBomHeaderList.stream().collect(Collectors.toMap(BBomHeader::getProductCode, a -> a, (k1, k2) -> k1));
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : surplusCorrectList) {
            BBomHeader bBomHeader = bomHeaderMap.get(apsDerivativeCodeQueryDTO.getItemNo());
            if (bBomHeader == null) {
                apsDerivativeCodeQueryDTO.setErrorMsg(CommonUtils.getLmbMessage(MessageId.ORIGINAL_BOM_INFORMATION_NOT_QUERIED));
                continue;
            }
            apsDerivativeCodeQueryDTO.setBBomHeader(bBomHeader);
            List<BBomDetailDTO> bBomDetailDTOList = bBomDetailService.selectDetailByHeaderId(bBomHeader.getBomHeaderId());
            apsDerivativeCodeQueryDTO.setBBomDetailList(bBomDetailDTOList);
            if (CollectionUtils.isEmpty(bBomDetailDTOList)) {
                apsDerivativeCodeQueryDTO.setErrorMsg(CommonUtils.getLmbMessage(MessageId.ORIGINAL_BOM_INFORMATION_NOT_QUERIED));
            }
        }
        return surplusCorrectList.stream().filter(e -> StringUtils.isEmpty(e.getErrorMsg())).collect(Collectors.toList());
    }

    /**
     * 获取衍生码变更信息
     *
     * @param surplusCorrectList
     */
    private List<ApsDerivativeCodeQueryDTO> getDerivativeCodeInformation(List<ApsDerivativeCodeQueryDTO> surplusCorrectList,boolean isModify) {
        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = apsRemoteService.getApsDerivativeCodeDTOS(surplusCorrectList);
        Map<String, List<ApsDerivativeCodeDTO>> apsDerivativeCodeDTOMap = apsDerivativeCodeDTOList.stream().collect(Collectors.groupingBy(ApsDerivativeCodeDTO::getProdplanNo));

        List<String> needDeleteList = new ArrayList<>();
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : surplusCorrectList) {
            List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOS = apsDerivativeCodeDTOMap.get(apsDerivativeCodeQueryDTO.getProdplanNo());
            apsDerivativeCodeQueryDTO.setApsDerivativeCodeDTOList(apsDerivativeCodeDTOS);
            if (CollectionUtils.isEmpty(apsDerivativeCodeDTOS)) {
                needDeleteList.add(apsDerivativeCodeQueryDTO.getProdplanId());
            }
        }

        this.deleteProdBomByProdplanIdList(isModify, needDeleteList);
        return surplusCorrectList.stream().filter(e -> !needDeleteList.contains(e.getProdplanId())).collect(Collectors.toList());
    }

    private void deleteProdBomByProdplanIdList(boolean isModify, List<String> needDeleteList) {
        if(!isModify){
          return;
        }
        if(CollectionUtils.isEmpty(needDeleteList)){
            return;
        }

        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = bProdBomHeaderRepository.getBProdListByProdplanIdList(needDeleteList);
        if(CollectionUtils.isEmpty(bProdBomHeaderDTOList)){
            return;
        }
        List<String> prodplanIdList = bProdBomHeaderDTOList.stream().map(BProdBomHeaderDTO::getProdplanId).distinct().collect(Collectors.toList());
        //衍生码变更时删除没有衍生码信息的批次对应制造BOM
        bProdBomHeaderRepository.deleteByProdplanIds(prodplanIdList);
    }


    /**
     * 校验任务是否已释放
     *
     * @param apsDerivativeCodeQueryDTOList
     * @return
     */
    private List<ApsDerivativeCodeQueryDTO> verifyIfTheTaskHasBeenReleased(List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList) {
        List<String> taskNoList = apsDerivativeCodeQueryDTOList.stream().map(e -> e.getProdplanNo()).distinct().collect(Collectors.toList());
        List<PsTask> psTaskList = psTaskService.queryFactoryIdByTaskNoList(taskNoList);
        if (CollectionUtils.isEmpty(psTaskList)) {
            psTaskList = new ArrayList<>();
        }
        //按任务号分组
        Map<String, PsTask> psTaskMap = psTaskList.stream().collect(Collectors.toMap(PsTask::getTaskNo, e -> e, (k1, k2) -> k1));
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : apsDerivativeCodeQueryDTOList) {
            PsTask psTask = psTaskMap.get(apsDerivativeCodeQueryDTO.getProdplanNo());
            if (psTask == null) {
                apsDerivativeCodeQueryDTO.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TASK_NO_INFO_IS_NULL));
            } else {
                apsDerivativeCodeQueryDTO.setProdplanId(psTask.getProdplanId());
                apsDerivativeCodeQueryDTO.setItemNo(psTask.getItemNo());
                apsDerivativeCodeQueryDTO.setFactoryId(psTask.getFactoryId() == null ? "" : psTask.getFactoryId().toString());
            }
        }
        List<ApsDerivativeCodeQueryDTO> surplusCorrectList = apsDerivativeCodeQueryDTOList.stream().filter(e -> StringUtils.isEmpty(e.getErrorMsg())).collect(Collectors.toList());
        return surplusCorrectList;
    }

    /**
     * 校验任务是否调拨
     *
     * @param surplusCorrectList
     * @return
     */
    private List<ApsDerivativeCodeQueryDTO> verifyTaskAllocation(List<ApsDerivativeCodeQueryDTO> surplusCorrectList) {
        List<PsTask> psTaskFromSiteList = this.getPsTaskFromSiteList(surplusCorrectList);
        if (CollectionUtils.isEmpty(psTaskFromSiteList)) {
            return surplusCorrectList;
        }
        //已调拨或者正在调拨任务号
        List<String> transferringInProgressList = psTaskFromSiteList.stream().filter(e -> Constant.ALREADY_GENERATED_OR_CURRENTLY_BEING_GENERATED_LIST.contains(e.getInforExe())).map(PsTask::getTaskNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transferringInProgressList)) {
            return surplusCorrectList;
        }
        for (ApsDerivativeCodeQueryDTO apsDerivativeCodeQueryDTO : surplusCorrectList) {
            if (transferringInProgressList.contains(apsDerivativeCodeQueryDTO.getProdplanNo())) {
                apsDerivativeCodeQueryDTO.setErrorMsg(CommonUtils.getLmbMessage(MessageId.TASK_NO_TRANSFERRING));
            }
        }
        return surplusCorrectList.stream().filter(e -> StringUtils.isEmpty(e.getErrorMsg())).collect(Collectors.toList());
    }

    private List<PsTask> getPsTaskFromSiteList(List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList) {
        Map<String, List<ApsDerivativeCodeQueryDTO>> factoryIdMap = apsDerivativeCodeQueryDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getFactoryId())).collect(Collectors.groupingBy(ApsDerivativeCodeQueryDTO::getFactoryId));
        Map<String, List<String>> taskNoMap = new HashMap<>(factoryIdMap.size());

        for (Map.Entry<String, List<ApsDerivativeCodeQueryDTO>> entry : factoryIdMap.entrySet()) {
            taskNoMap.put(entry.getKey().toString(), entry.getValue().stream().filter(e -> StringUtils.isNotEmpty(e.getProdplanNo())).map(e -> e.getProdplanNo()).collect(Collectors.toList()));
        }

        List<PsTask> psTaskFromSiteList = openApiRemoteService.queryInfoTransferOrder(taskNoMap);
        return psTaskFromSiteList;
    }


    @Override
    public List<String> convertAndIntegrateMBom(String prodplanId, List<String> originalItemNoList) {
        List<BProdBomHeaderDTO> resultList = new ArrayList<>();
        List<List<String>> lists = CommonUtils.splitList(originalItemNoList, Constant.BATCH_SIZE);
        for (List<String> list : lists) {
            List<BProdBomHeaderDTO> tempList = bProdBomHeaderRepository.getBProdBomList(prodplanId, list);
            resultList.addAll(tempList);
        }
        Set<String> originalHaveRecordSet = new HashSet<>();
        Set<String> mBomProductCodeSet = new HashSet<>();
        for (BProdBomHeaderDTO temp : resultList) {
            originalHaveRecordSet.add(temp.getOriginalProductCode());
            mBomProductCodeSet.add(temp.getProductCode());
        }
        // 先从整个原始料单列表中剔除存在制造BOM的原始料单。
        Set<String> originalItemNoSet = new HashSet<>(originalItemNoList);
        originalItemNoSet.removeAll(originalHaveRecordSet);
        // 在整合制造料单。
        originalItemNoSet.addAll(mBomProductCodeSet);
        return new ArrayList<>(originalItemNoSet);

    }

    @Override
    public String queryMBomHeadIdByProdplanIdAndProductCode(String prodplanId, String productCode) {
        return bProdBomHeaderRepository.queryMBomHeadIdByProdplanIdAndProductCode(prodplanId, productCode);
    }

    @Override
    public List<BProdBomHeaderDTO> getBProdBomHeader(BProdBomHeaderDTO bProdBomHeaderDTO) {
        if (!check4BProdBomParam(bProdBomHeaderDTO)) {
            return Collections.emptyList();
        }
        return bProdBomHeaderRepository.getBProdBomHeader(bProdBomHeaderDTO.getProdplanId()
                , bProdBomHeaderDTO.getOriginalProductCode());
    }

    private boolean check4BProdBomParam(BProdBomHeaderDTO bProdBomHeaderDTO) {
        if (ObjectUtils.isEmpty(bProdBomHeaderDTO)) {
            return false;
        }
        if (StringUtils.isEmpty(bProdBomHeaderDTO.getProdplanId())) {
            return false;
        }
        return true;
    }

    /* Started by AICoder, pid:eb0e0efae7dc1f714ec20892f07cfb16a1165b3c */

    /**
     * 根据制造料单代码或研发料单代码获取制造料单头信息
     *
     * @param record
     * @return
     */
    @Override
    public Page<BProdBomHeaderDTO> getMbomHeader(BProdBomHeaderDTO record) {
        Page<BProdBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<BProdBomHeaderDTO> mBomList = bProdBomHeaderRepository.getMbomList(pageInfo);
        pageInfo.setRows(mBomList);
        return pageInfo;
    }

    /* Ended by AICoder, pid:eb0e0efae7dc1f714ec20892f07cfb16a1165b3c */

    /* Started by AICoder, pid:z237dgaa5arbd891496708bfa0ce1e2a32841f88 */

    /**
     * 通过批次批量获取制造BOM
     *
     * @param prodPlanIdList 批次信息列表
     * @return 制造BOM头信息列表
     */
    @Override
    public List<BProdBomHeaderDTO> queryProductCodeByProdPlanIdList(List<String> prodPlanIdList) {
        if (CollectionUtils.isEmpty(prodPlanIdList)) {
            return Collections.emptyList();
        }

        List<BProdBomHeaderDTO> resultList = new ArrayList<>();
        // 分批处理批次信息
        for (List<String> batch : CommonUtils.splitList(prodPlanIdList, Constant.INT_100)) {
            List<BProdBomHeaderDTO> tempResult = bProdBomHeaderRepository.queryBProdBomListBatch(batch, null);
            if (CollectionUtils.isNotEmpty(tempResult)) {
                resultList.addAll(tempResult);
            }
        }

        return resultList;
    }

    /* Ended by AICoder, pid:z237dgaa5arbd891496708bfa0ce1e2a32841f88 */

    /* Started by AICoder, pid:806b6s86b5n667214c740b50903cc5274dd99050 */

    /**
     * 通过任务号批量查询制造BOM变更版本号
     *
     * @param taskNoList 任务号列表
     * @return 制造BOM列表
     */
    @Override
    public List<BProdBomHeaderDTO> queryProductCodeByTaskNoList(List<String> taskNoList) {
        // 如果任务号列表为空或为null，直接返回空列表
        if (CollectionUtils.isEmpty(taskNoList)) {
            return Collections.emptyList();
        }

        // 使用ArrayList以提高性能
        List<BProdBomHeaderDTO> resultList = new ArrayList<>();

        // 将任务号列表分割成多个子列表，每个子列表最多包含100个元素
        List<List<String>> splitLists = CommonUtils.splitList(taskNoList, Constant.INT_100);

        // 遍历每个子列表并查询对应的制造BOM信息
        for (List<String> batch : splitLists) {
            List<BProdBomHeaderDTO> tempResult = bProdBomHeaderRepository.queryProductCodeByTaskNoList(batch);
            if (CollectionUtils.isNotEmpty(tempResult)) {
                resultList.addAll(tempResult);
            }
        }

        return resultList;
    }

    /* Ended by AICoder, pid:806b6s86b5n667214c740b50903cc5274dd99050 */

    /**
     * 根据制造料单代码或研发料单代码获取制造料单头信息
     *
     * @param record getMbomOrBomHeader
     * @return
     */
    @Override
    public Page<BProdBomHeaderDTO> getMbomOrBomHeader(BProdBomHeaderDTO record) {
        check4BProdBomOrBomParam(record);
        if (StringUtils.isNotEmpty(record.getProductCode())) {
            return queryMBomHeaderLike(record);
        } else if (StringUtils.isNotEmpty(record.getDevProductCode())) {
            return queryBomHeaderLike(record);
        }
        return new Page<>();
    }

    /**
     * 根据制造BOM料单 右模糊查询
     * @param record
     * @return
     */
    private Page<BProdBomHeaderDTO> queryMBomHeaderLike(BProdBomHeaderDTO record) {
        Page<BProdBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        // 根据制造料单代码 去重查询
        List<BProdBomHeaderDTO> mBomList = bProdBomHeaderRepository.queryMBomHeaderLike(pageInfo);
        if (CollectionUtils.isEmpty(mBomList)){
            return pageInfo;
        }
        List<String> productCodeList = mBomList.stream()
                .map(BProdBomHeaderDTO::getOriginalProductCode)
                .filter(StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());
        // 通过原始料单代码，查询料单代码相关数据
        getBomInfoByBomNoList(mBomList,productCodeList);
        pageInfo.setRows(mBomList);
        return pageInfo;
    }
    /**
     * 根据研发料单代码获取研发BOM头信息
     *
     * @param record record
     * @return
     */
    private Page<BProdBomHeaderDTO> queryBomHeaderLike(BProdBomHeaderDTO record) {
        BBomHeaderDTO bomHeaderDTO = new BBomHeaderDTO();
        bomHeaderDTO.setProductCode(record.getDevProductCode());
        bomHeaderDTO.setPage(record.getPage());
        bomHeaderDTO.setRows(record.getRows());
        Page<BBomHeaderDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(bomHeaderDTO);
        List<BBomHeader> bomList = bBomHeaderRepository.queryBomHeaderLike(pageInfo);

        Page<BProdBomHeaderDTO> headerDTOPage = new Page<>();
        BeanUtils.copyProperties(pageInfo, headerDTOPage);
        if (CollectionUtils.isEmpty(bomList)){
            return headerDTOPage;
        }
        // 将BBomHeader列表转为BProdBomHeaderDTO
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bomList.forEach(item -> {
            BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
            BeanUtils.copyProperties(item, bProdBomHeaderDTO);
            bProdBomHeaderDTOList.add(bProdBomHeaderDTO);

        });
        // 根据研发BOM料单代码查询料单相关数据
        List<String> productCodeList =
                bProdBomHeaderDTOList.stream()
                        .map(BProdBomHeaderDTO::getProductCode)
                        .filter(StringUtils::isNotEmpty)
                        .distinct().collect(Collectors.toList());
        getBomInfoByBomNoList(bProdBomHeaderDTOList, productCodeList);

        headerDTOPage.setRows(bProdBomHeaderDTOList);
        return headerDTOPage;
    }

    private void getBomInfoByBomNoList(List<BProdBomHeaderDTO> mBomList,List<String> productCodeList) {
        if (CollectionUtils.isEmpty(mBomList) || CollectionUtils.isEmpty(productCodeList)) {
            return;
        }
        List<BaBomHeadDTO> baBomHeadDTOList = DatawbRemoteService.queryBomInfoByBomNoList(productCodeList);
        Map<String, BaBomHeadDTO> map = baBomHeadDTOList.stream().collect(Collectors.toMap(BaBomHeadDTO::getBomNo, e -> e, (k1, k2) -> k1));
        Map<String, String> leadMap = this.getLeadMap();
        mBomList.forEach(item -> {
            // 兼容制造BOM原始料单
            String productCode = item.getOriginalProductCode();
            if (StringUtils.isEmpty(productCode)){
                productCode = item.getProductCode();
            }
            BaBomHeadDTO baBomHeadDTO = map.getOrDefault(productCode, null);
            if (baBomHeadDTO == null) {
                return;
            }
            item.setCodeDesc(baBomHeadDTO.getCodeDesc());
            item.setIsLead(leadMap.get(baBomHeadDTO.getIsLead()));
            item.setCreatedBy(baBomHeadDTO.getCreatedBy());
            item.setArchiver(baBomHeadDTO.getArchiver());
            item.setArchiveDate(baBomHeadDTO.getArchiveDate());
            item.setRemark(baBomHeadDTO.getRemark());
            item.setStyle(baBomHeadDTO.getStyle());
            item.setFirstrow(baBomHeadDTO.getFirstrow());
            item.setSecondrow(baBomHeadDTO.getSecondrow());
            item.setArchiveDate(baBomHeadDTO.getArchiveDate());
        });
    }
    /**
     * 获取lead_flag对应字典
     */
    private Map<String, String> getLeadMap() {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put(FIELD_LOOKUP_TYPE, Constant.LOOK_UP_CODE_1036);
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        Map<String, String> leadMap = new HashMap<>();
        for (SysLookupTypesDTO sysLookupTypesDTO : valuesList) {
            leadMap.put(sysLookupTypesDTO.getLookupMeaning(), sysLookupTypesDTO.getDescriptionChinV());
        }
        return leadMap;
    }

    @Override
    @AsyncExport(functionName = "MBOM导出Excel接口")
    public void exportExcel(HttpServletResponse response, BProdBomHeaderDTO record) throws Exception {
        check4BProdBomOrBomParam(record);
        record.setPage(Constant.INT_1);
        record.setRows(Constant.INT_5000);
        Page<BProdBomHeaderDTO> headerDTOPage;
        BProdBomDetailDTO detailDto = new BProdBomDetailDTO();
        detailDto.setPage(Constant.INT_1);
        detailDto.setRows(Constant.INT_5000);
        String productCode;
        if (StringUtils.isNotEmpty(record.getProductCode())) {
            productCode = record.getProductCode();
            headerDTOPage = queryMBomHeaderLike(record);
        } else {
            productCode = record.getDevProductCode();
            headerDTOPage = queryBomHeaderLike(record);
        }
        // BOM详情
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = headerDTOPage.getRows();
        List<BProdBomDetailDTO> detailListRows = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bProdBomHeaderDTOList)) {
            bProdBomHeaderDTOList =
                    bProdBomHeaderDTOList.stream().
                            filter(item -> StringUtils.equals(item.getProductCode(), productCode))
                            .collect(Collectors.toList());
            List<String> productCodeList =
                    bProdBomHeaderDTOList.stream()
                            .map(BProdBomHeaderDTO::getProductCode)
                            .distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productCodeList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PRODUCT_CODE_NOT_EXIST);
            }
            detailDto.setProductCodes(productCodeList);
            Page<BProdBomDetailDTO> mbomOrBomDetailList;
            if (StringUtils.isNotEmpty(record.getProductCode())) {
                mbomOrBomDetailList = bProdBomDetailService.selectMBOMDetailByByProductCodeListAndItemCode(detailDto);
            } else {
                mbomOrBomDetailList = bProdBomDetailService.selectDetailByProductCodeListAndItemCode(detailDto);
            }
            detailListRows = mbomOrBomDetailList.getRows();
        }

        // 设置文件名称
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDD);
        String fileName = sdf.format(new Date()) + UUID.randomUUID().toString().replace("-", "") + Constant.MBOM_EXCEL_FILE;
        writeExcel(bProdBomHeaderDTOList, detailListRows, fileName, response);
    }

    /**
     * 导出电子表格
     */
    public void writeExcel(List<BProdBomHeaderDTO> itemList, List<BProdBomDetailDTO> itemDetailList,
                           String fileName, HttpServletResponse resp) throws Exception {
        String filePath = FileUtils.tempPath + fileName;
        FileUtils.checkFilePath(filePath);
        try (SXSSFWorkbook wb = new SXSSFWorkbook();
             FileOutputStream fileOutputStream = new FileOutputStream(filePath);
             BufferedOutputStream os = new BufferedOutputStream(fileOutputStream)) {
            wb.createSheet(Constant.MBOM_HEADER_SHEET);
            ExcelCommonUtils.writeSheet(wb.getSheet(Constant.MBOM_HEADER_SHEET), itemList, ExcelName.MBOM_HEADER_SHEET_TITLE,
                    ExcelName.MBOM_HEADER_SHEET_VALUE);
            wb.createSheet(Constant.MBOM_DETAIL_SHEET);
            ExcelCommonUtils.writeSheet(wb.getSheet(Constant.MBOM_DETAIL_SHEET), itemDetailList, ExcelName.MBOM_DETAIL_SHEET_TITLE, ExcelName.MBOM_DETAIL_SHEET_VALUE);
            wb.write(os);
            os.flush();
            wb.dispose();
        }
        asyncExportFileCommonService.uploadFileThenClearLocalFileUpdateLog(fileName, filePath);
    }

    private void check4BProdBomOrBomParam(BProdBomHeaderDTO record) {
        if (StringUtils.isNotEmpty(record.getProductCode()) && StringUtils.isNotEmpty(record.getDevProductCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR);
        }
        if (StringUtils.isEmpty(record.getProductCode()) && StringUtils.isEmpty(record.getDevProductCode())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARMS_ERR);
        }
    }
}
