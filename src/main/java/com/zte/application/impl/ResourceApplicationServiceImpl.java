package com.zte.application.impl;

import cn.afterturn.easypoi.util.UnicodeInputStream;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.ResourceApplicationService;
import com.zte.application.ResourceDetailService;
import com.zte.application.SpTagsParamService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.enums.ResourceStatusEnum;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.uac.ssoclient.util.MD5;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.common.utils.IntegerConstant.INT_ZERO;

@Slf4j
@Service
public class ResourceApplicationServiceImpl implements ResourceApplicationService {

    @Autowired
    private SpTagsParamService spTagsParamService;

    @Autowired
    private ResourceApplicationRepository resourceApplicationRepository;

    @Autowired
    private ResourceDetailRepository resourceDetailRepository;

    @Autowired
    private SpSpecialityParamRepository spSpecialityParamRepository;

    @Autowired
    private ResourceDetailService resourceDetailService;

    @Autowired
    private ResourceInfoRepository resourceInfoRepository;

    @Autowired
    private ResourceInfoDetailRepository resourceInfoDetailRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private CloudDiskHelper cloudDiskHelper;
//    private static final Map<String, Integer> ResourceTypeMap;
//
//    static {
//        Map<String, Integer> tempMap = new HashMap<>();
//        tempMap.put(Constant.ResourceType.MAC, 0);
//        tempMap.put(Constant.ResourceType.GPON_SN, 1);
//        tempMap.put(Constant.ResourceType.CTEI, 2);
//        tempMap.put(Constant.ResourceType.CMEI, 3);
//        tempMap.put(Constant.ResourceType.DEVICE_SERIAL_NUMBER, 4);
//        tempMap.put(Constant.ResourceType.CUEI, 5);
//        ResourceTypeMap = Collections.unmodifiableMap(tempMap);
//    }

    /**
     * @param record 检索参数
     * @return 统计数
     * @throws Exception
     */
    @Override
    public long getResourceApplicationCount(ResourceApplicationDTO record) throws Exception {
        if (Objects.isNull(record)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        return resourceApplicationRepository.getResourceApplicationCount(record);
    }

    /**
     * @param record 检索参数
     * @return 资源申请数据集合
     * @throws Exception
     */
    @Override
    public List<ResourceApplication> getPageList(ResourceApplicationDTO record) throws Exception {
        if (Objects.isNull(record)) {
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID);
        }
        return resourceApplicationRepository.getPageList(record);
    }

    @Override
    public List<ResourceApplicationEntityDTO> getList(String applyTask) {
        return resourceApplicationRepository.taskList(applyTask);
    }

    @Override
    public List<ResourceApplicationEntityDTO> getApplyingList(String applyTask) {
        List<String> applyIdList = resourceApplicationRepository.getIdByTask(applyTask);
        if (CollectionUtils.isEmpty(applyIdList)) {
            return null;
        }
        return resourceApplicationRepository.getApplyingList(applyIdList);
    }

    /**
     * 资源申请导出
     *
     * @param record 检索参数
     * @return 路径
     */
    @Override
    public String exportThread(ResourceApplicationDTO record) {
        String key = Constant.RESOURCE_EXPORT + MD5.getMD5Code(JSON.toJSONString(record)) + record.getFormat();
        if (redisTemplate.hasKey(key)) {
            return key;
        }
        // 先写空值，超时时间5分钟
        redisTemplate.opsForValue().set(key, "", 300L, TimeUnit.SECONDS);
        // 判断所需导出的格式
        if (Constant.XLS.equals(record.getFormat()) || Constant.XLSX.equals(record.getFormat())) {
            ThreadUtil.EXECUTOR.execute(() -> {
                // 文件写成功后，将文档云文件路径写到reids
                try {
                    String filePath = writeExcel(record, key);
                    redisTemplate.delete(key);
                    redisTemplate.opsForValue().set(key, filePath, 300L, TimeUnit.SECONDS);
                } catch (Exception e) {
                    redisTemplate.delete(key);
                }
            });
            return key;
        } else if (Constant.CSV.equals(record.getFormat()) || Constant.TXT.equals(record.getFormat())) {
            ThreadUtil.EXECUTOR.execute(() -> {
                // 文件写成功后，将文档云文件路径写到reids
                try {
                    String filePath = writeCsvFile(record, key);
                    redisTemplate.delete(key);
                    redisTemplate.opsForValue().set(key, filePath, 300L, TimeUnit.SECONDS);
                } catch (Exception e) {
                    redisTemplate.delete(key);
                }
            });
            return key;
        }
        return null;
    }

    /**
     * @param fileKey 文件路径KEY值
     * @return 文件路径
     * @throws MesBusinessException
     */
    @Override
    public String getFilePath(String fileKey) throws MesBusinessException {
        if (!redisTemplate.hasKey(fileKey)) {
            // redis 5分钟失效后，提示导出超时
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIMEOUT);
        }
        return (String) redisTemplate.opsForValue().get(fileKey);
    }

    /**
     * @param fileKey KEY值
     * @return 文件路径
     * @throws MesBusinessException
     */
    @Override
    public String getImportSuccessKey(String fileKey) throws Exception {
        return (String) redisTemplate.opsForValue().get(fileKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(ResourceApplicationDTO record) throws Exception {
        String key = Constant.RESOURCE_IMPORT + MD5.getMD5Code(JSON.toJSONString(record.getApplyBillNo()));
        record.setKey(key);
        record.setInputStream(record.getFile().getInputStream());
        // 先写空值，超时时间5分钟
        redisTemplate.opsForValue().set(key, "", 300L, TimeUnit.SECONDS);
        ThreadUtil.EXECUTOR.execute(() -> {
            // 文件写成功后，将文档云文件路径写到reids
            try {
                String fileKey = importThread(record);
            } catch (Exception e) {
            }
        });
        return key;
    }


    /**
     * 文件流解析资源分配信息
     *
     * @param record 检索数据
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importThread(ResourceApplicationDTO record) throws Exception {
        LocaleContextHolder.setLocale(record.getLocal(), true);
        // 创建临时表--PG不适用
//        this.createTempTable();
        // 分区时间
        String partitionTime = this.getPartitionTime(record.getApplyBillNo());
        if (record.getInputStream() == null) {
            return null;
        }
        List<String> resourceSnList = new ArrayList<>(NumConstant.NUM_TEN);
        List<ResourceDetail> resourceDetailList = new ArrayList<>(NumConstant.NUM_TEN);
        // 添加分布式锁（防止同时上传导致数据重复）
        final String redisKey = Constant.REDIS_KEY_UPLOAD_PROGRAM_FILE + Constant.UNDER_LINE + record.getApplyBillNo() + record.getApplyId();
        RedisLock redisLock = new RedisLock(redisKey);

        try {
            // 锁超时时间20分钟
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.IS_UPLOADING_PLEASE_WAIT);
            }
            List<SpTagsParam> paramList = getAndCheckSpTagsParams(record);

            String fileName = record.getFile().getOriginalFilename();
            String ext = fileName.substring(fileName.lastIndexOf('.') + MpConstant.INT_ONE);
            record.setResourceStatus(ResourceStatusEnum.IMPORTED.name());
            record.setFormat(ext);
            if (ext.equalsIgnoreCase(Constant.XLS) || ext.equalsIgnoreCase(Constant.XLSX)) {
                excelHandle(record, partitionTime, resourceSnList, resourceDetailList, paramList);
            } else if (Constant.CSV.equalsIgnoreCase(ext) || Constant.TXT.equalsIgnoreCase(ext)) {
                csvAndTextHandle(record, partitionTime, resourceSnList, resourceDetailList, paramList);
            }
        } catch (Exception e) {
            log.error("file data analysis error :{}", e);
            redisTemplate.opsForValue().set(record.getKey(), e.getMessage(), 120L, TimeUnit.SECONDS);
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.IMPORT_DATA_ERROR));
        } finally {
            resourceDetailList.clear();
            resourceSnList.clear();
            redisLock.unlock();
        }
        redisTemplate.opsForValue().set(record.getKey(), RetCode.SUCCESS_CODE, 120L, TimeUnit.SECONDS);
        return null;
    }

    private void csvAndTextHandle(ResourceApplicationDTO record, String partitionTime, List<String> resourceSnList,
                                  List<ResourceDetail> resourceDetailList, List<SpTagsParam> paramList) throws Exception {
        try (UnicodeInputStream unicodeInputStream = new UnicodeInputStream(record.getInputStream(), true);
             BufferedInputStream bis = new BufferedInputStream(unicodeInputStream);
             InputStreamReader inputStreamReader = new InputStreamReader(bis, StandardCharsets.UTF_8);
             BufferedReader in = new BufferedReader(inputStreamReader, MpConstant.BYTES_10M)) {
            String str;
            int num = NumConstant.NUM_ZERO;
            while (!StringUtils.isEmpty(str = in.readLine())) {
                record.setRows(num);
                this.splitMessage(resourceSnList, resourceDetailList, paramList, record, str);
                num++;
                if (num == NumConstant.NUM_1000) {
                    // 批量添加数据
                    this.insertResourceDetail(resourceSnList, resourceDetailList, record, partitionTime, record.getKey());
                    num = NumConstant.NUM_ZERO;
                }
            }
            if (CollectionUtils.isNotEmpty(resourceDetailList)) {
                this.insertResourceDetail(resourceSnList, resourceDetailList, record, partitionTime, record.getKey());
            }
        }
    }

    private void excelHandle(ResourceApplicationDTO record, String partitionTime, List<String> resourceSnList,
                             List<ResourceDetail> resourceDetailList, List<SpTagsParam> paramList) throws Exception {
        Workbook wb = ExcelUtil.initWorkbook(record.getInputStream(), record.getFormat().toLowerCase());
        //读取sheet 0
        if (null == wb) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.WORKBOOK_INIT_FAIL));
        }
        Sheet sheet = wb.getSheetAt(INT_ZERO);
        int rowNum = sheet.getLastRowNum();
        for (int i = IntegerConstant.INT_ZERO; i <= rowNum; i++) {
            Row row = sheet.getRow(i);
            ResourceDetail resourceDetail = new ResourceDetail();
            resourceDetail.setApplyId(record.getApplyId());
            HashMap<String, String> additional = getAdditional(resourceDetailList, paramList, resourceDetail, row, i);
            if (StringUtils.isNotBlank(resourceDetail.getResourceSn())) {
                resourceDetail.setAdditional(JSON.toJSONString(additional));
                resourceDetailList.add(resourceDetail);
                resourceSnList.add(resourceDetail.getResourceSn());
            }

            if (i > 0 && i % NumConstant.NUM_1000 == NumConstant.NUM_ZERO) {
                this.insertResourceDetail(resourceSnList, resourceDetailList, record, partitionTime, record.getKey());
            }
        }
        if (CollectionUtils.isNotEmpty(resourceDetailList)) {
            this.insertResourceDetail(resourceSnList, resourceDetailList, record, partitionTime, record.getKey());
        }
        wb.close();
    }

    private HashMap<String, String> getAdditional(List<ResourceDetail> resourceDetailList, List<SpTagsParam> paramList, ResourceDetail resourceDetail, Row row, int i) throws MesBusinessException {
        HashMap<String, String> additional = new HashMap();
        for (int j = NumConstant.NUM_ZERO; j < paramList.size(); j++) {
            Cell cell = row.getCell(j);
            String val = cell == null ? "" : cell.toString();
            // 检查文件头行
            if (CollectionUtils.isEmpty(resourceDetailList) && i == NumConstant.NUM_ZERO) {
                if (!paramList.get(j).getParamName().equals(val)) {
                    throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.RESOURCE_FILE_HEADER_TAGPARAM_ERROR,
                            new String[]{val, paramList.get(j).getParamName()}));
                }
                continue;
            }

            if (paramList.get(j).getParamType() == NumConstant.NUM_ONE) {
                resourceDetail.setResourceSn(val);
            } else {
                additional.put(paramList.get(j).getParamName(), val);
            }
        }
        return additional;
    }


    private List<SpTagsParam> getAndCheckSpTagsParams(ResourceApplicationDTO record) throws MesBusinessException {
        SpTagsParamDTO query = new SpTagsParamDTO();
        query.setTagName(record.getTagName());
        List<SpTagsParam> paramList = spTagsParamService.queryList(query);
        if (CollectionUtils.isEmpty(paramList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TAGS_PARAM_IS_NULL);
        }
        String resourceParam = paramList.stream().filter(param -> param.getParamType() == NumConstant.NUM_ONE).findFirst().orElse(new SpTagsParam()).getParamName();
        if (StringUtils.isBlank(resourceParam)) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR), new Object[]{resourceParam});
        }
        return paramList;
    }

    /**
     * 批量添加数据
     */
    private void insertResourceDetail(List<String> list, List<ResourceDetail> resourceDetailList,
                                      ResourceApplicationDTO record, String partitionTime, String key) throws Exception {
        // 分批次比较数据库中的资源号与文件中的是否一致
        List<String> resourceSnList = resourceDetailRepository.compareResourceSn(record.getApplyBillNo(), list);
        if (resourceSnList.size() != list.size()) {
            list.removeAll(resourceSnList);
            String strPro;
            if (list.size() > Constant.BATCH_SIZE_100) {
                List<String> subList = list.subList(Constant.NUM_ZERO, Constant.BATCH_SIZE_100);
                String result = subList.stream().map(String::valueOf).collect(Collectors.joining(","));
                strPro = result + "...";
            } else {
                String result = list.stream().map(String::valueOf).collect(Collectors.joining(","));
                strPro = result;
            }
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, CommonUtils.getLmbMessage(MessageId.RESOURCE_NO_DOES_NOT_MATCH, strPro));
        }
        // 更新资源申请头表信息
        resourceApplicationRepository.updateResourceApplication(record);
        // 批量对临时表写入数据
        int numOne = resourceDetailRepository.insertAllResourceDetailTemp(resourceDetailList);
        int numTwo = resourceDetailRepository.updateResourceDetail(partitionTime);
        // 清除临时表数据
        resourceDetailRepository.deleteResourceTemp();
        resourceDetailList.clear();
        list.clear();
    }

    /**
     * @param resourceSnList     资源号集合
     * @param resourceDetailList 资源详情信息
     * @param paramList          标签参数列表
     * @param message            资源详情字符串
     */
    private void splitMessage(List<String> resourceSnList, List<ResourceDetail> resourceDetailList, List<SpTagsParam> paramList, ResourceApplicationDTO record, String message) throws Exception {
        List<String> msg = Stream.of(message.split(Constant.COMMA)).collect(Collectors.toList());
        if (msg.size() != paramList.size()) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.RESOURCE_FILE_FORMAT,
                    new String[]{record.getTagName(), String.valueOf(paramList.size()), String.valueOf(msg.size())}));
        }
        ResourceDetail resourceDetail = new ResourceDetail();
        resourceDetail.setApplyId(record.getApplyId());

        HashMap<String, String> additional = new HashMap();
        for (int i = NumConstant.NUM_ZERO; i < paramList.size(); i++) {
            // 判断是否为头行，若是则检查文件头行
            if (NumConstant.NUM_ZERO == record.getRows()) {
                if (!paramList.get(i).getParamName().equals((msg.get(i)))) {
                    throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.RESOURCE_FILE_HEADER_TAGPARAM_ERROR,
                            new String[]{msg.get(i), paramList.get(i).getParamName()}));
                }
                continue;
            }

            if (paramList.get(i).getParamType() == NumConstant.NUM_ONE) {
                resourceDetail.setResourceSn(msg.get(i));
            } else {
                additional.put(paramList.get(i).getParamName(), msg.get(i));
            }
        }

        if (StringUtils.isNotBlank(resourceDetail.getResourceSn())) {
            resourceDetail.setAdditional(JSON.toJSONString(additional));
            resourceDetailList.add(resourceDetail);
            resourceSnList.add(resourceDetail.getResourceSn());
        }
    }

    /**
     * @return 分区时间
     */
    private String getPartitionTime(String applyBillNo) {
        return resourceApplicationRepository.getPartitionTime(applyBillNo);
    }


    /**
     * 判断临时表是否创建
     */
    private void createTempTable() {
        // 判断临时表是否存在
        if (resourceDetailRepository.findIsNotEmptyTable() <= NumConstant.NUM_ZERO) {
            resourceDetailRepository.createResourceDetailTemp();
            resourceDetailRepository.addUniqueResourceSn();
        }
    }

    /**
     * 分批获取申请单号下的所有资源号
     *
     * @param record 检索数据
     * @return 资源号集合
     * @throws Exception
     */
    public Page<ResourceDetail> getApplyBillNo(ResourceApplicationDTO record) throws Exception {
        Page<ResourceDetail> page = new Page<>(record.getPage(), record.getRows());
        page.setParams(record);
        page.setRows(resourceDetailRepository.exportApplyResource(page));
        return page;
    }

    /**
     * @param record 检索数据
     * @param key    导出文件KEY值
     * @return 文件路径
     * @throws Exception
     */
    public String writeExcel(ResourceApplicationDTO record, String key) throws Exception {
        Sheet sheet = null;
        Workbook workbook = null;
        try {
            if (Constant.XLS.equals(record.getFormat())) {
                workbook = new HSSFWorkbook();
                sheet = workbook.createSheet();
            } else {
                // 大数据导出Excel专用，默认内存中存100条数据，可设置，其他保存在硬盘零时文件
                workbook = new SXSSFWorkbook(ExcelCommonUtils.getWorkbook(Constant.RESOURCE_EXPORT_NAME, Constant.RESOURCE_EXPORT_TITLE), Constant.BATCH_SIZE_1000);
                sheet = workbook.getSheetAt(Constant.INT_0);
            }

            // 写入头信息
            List<SpTagsParam> paramList = getAndCheckSpTagsParams(record);
            writeExcelHead(sheet, paramList);

            // 每批1000，最大1000次
            int pageNum = Constant.INT_1;
            record.setRows(NumConstant.NUM_10000);
            Page<ResourceDetail> page;
            do {
                // 5分钟文件仍未写完，返回超时
                if (!redisTemplate.hasKey(key)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIMEOUT);
                }
                record.setPage(pageNum);
                page = this.getApplyBillNo(record);
                if (page == null || CollectionUtils.isEmpty(page.getRows())) {
                    continue;
                }
                ExcelCommonUtils.appendSXSSFWorkbook(sheet, page.getRows(), Constant.RESOURCE_EXPORT_FIELD, Constant.INT_1+(pageNum - Constant.INT_1) * NumConstant.NUM_10000, paramList);
            } while (page != null && pageNum++ < page.getTotalPage() && pageNum <= NumConstant.NUM_100);
            // 上传文档云，并获取文档路径
            String downloadUrl = null;
            if (Constant.XLS.equals(record.getFormat())) {
                downloadUrl = this.uploadCloudDisk(record.getEmpNo(), record.getApplyBillNo() + Constant.RESOURCE_EXPORT_XLS, workbook);
            } else {
                downloadUrl = this.uploadCloudDisk(record.getEmpNo(), record.getApplyBillNo() + Constant.RESOURCE_EXPORT_XLSX, workbook);
            }

            //更新导出状态
            this.updateExportStatus(record);

            return downloadUrl;
        } finally {
            if (null != workbook) {
                workbook.close();
            }
        }
    }

    private void writeExcelHead(Sheet sheet, List<SpTagsParam> paramList) {
        Row row = sheet.createRow(NumConstant.NUM_ZERO);
        for (int i = 0; i < paramList.size(); i++) {
            row.createCell(i).setCellValue(paramList.get(i).getParamName());
        }
    }

    /**
     * 将SXSSFWorkbook生成本地文件上传文档云并关闭流
     *
     * @param empNo
     * @param fileName
     * @param swb
     * @return 文档云下载地址
     * @throws Exception
     */
    private String uploadCloudDisk(String empNo, String fileName, Workbook swb) throws Exception {
        // 将文件暂存本地
        String tempfilePath = FileUtils.createFilePathAndCheck(fileName);
        try (BufferedOutputStream os = new BufferedOutputStream(new FileOutputStream(tempfilePath))) {
            swb.write(os);
            os.flush();
//            swb.dispose();
        }
        // 上传文档云，并获取文档路径
        String fileKey = cloudDiskHelper.fileUpload(tempfilePath, empNo, CloudDiskHelper.MAX_RETRY_TIMES);
        // 上传成功后删除本地文件
        FileUtils.deleteFile(tempfilePath);
        return cloudDiskHelper.getFileDownloadUrl(fileKey, null, empNo);
    }

    private void updateExportStatus(ResourceApplicationDTO record) {
        ResourceApplicationDTO resourceApply = new ResourceApplicationDTO();
        resourceApply.setApplyBillNo(record.getApplyBillNo());
        resourceApply.setCreateUser(record.getEmpNo());
        resourceApply.setIsExported(Constant.Y_STATUS);
        this.resourceApplicationRepository.updateResourceApplication(resourceApply);
    }

    /**
     * @param record 检索数据
     * @return 文件路径
     * @throws Exception
     */
    public String writeCsvFile(ResourceApplicationDTO record, String key) throws Exception {
        String fileName = null;
        if (Constant.TXT.equals(record.getFormat())) {
            fileName = record.getApplyBillNo() + Constant.RESOURCE_EXPORT_TXT;
        } else {
            fileName = record.getApplyBillNo() + Constant.RESOURCE_EXPORT_CSV;
        }
        String fileId = null;
        // 5分钟文件仍未写完，返回超时
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIMEOUT);
        }
        File csvFile = new File(MpConstant.USR_PATH + fileName);
        try (OutputStream out = new FileOutputStream(csvFile, true);
             OutputStreamWriter outputStreamWriter = new OutputStreamWriter(out, StandardCharsets.UTF_8);
             BufferedWriter csvFileOutputStream = new BufferedWriter(outputStreamWriter, NumConstant.NUM_1024)) {
            //定义文件名格式并创建
            // 防止Excel打开乱码
            csvFileOutputStream.write(new String(Constant.BYTES));
            record.setRows(NumConstant.NUM_10000);
            // 写入头信息
            List<SpTagsParam> paramList = getAndCheckSpTagsParams(record);
            writeCsvHead(csvFileOutputStream, paramList, record.getFormat());
            Page<ResourceDetail> page;
            // 每批1000，最大1000次
            int pageNum = Constant.INT_1;
            do {
                // 5分钟文件仍未写完，返回超时
                if (!redisTemplate.hasKey(key)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EXPORT_TIMEOUT);
                }
                record.setPage(pageNum);
                page = this.getApplyBillNo(record);
                if (page == null || CollectionUtils.isEmpty(page.getRows())) {
                    continue;
                }
                extracted(record, csvFileOutputStream, page, paramList);
                csvFileOutputStream.flush();
            } while (page != null && pageNum++ < page.getTotalPage() && pageNum <= NumConstant.NUM_100);
            // 将文件上传到文档云
            fileId = cloudDiskHelper.fileUpload(MpConstant.USR_PATH + fileName, record.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
        } catch (IOException e) {
            log.error("generate csv file failed", e);
            throw new RuntimeException("generate csv file failed");
        }

        //更新导出状态
        this.updateExportStatus(record);

        return cloudDiskHelper.getFileDownloadUrl(fileId, null, record.getEmpNo());
    }

    private void writeCsvHead(BufferedWriter csvFileOutputStream, List<SpTagsParam> paramList, String format) throws Exception {
        for (SpTagsParam spTagsParam : paramList) {
            csvFileOutputStream.write(spTagsParam.getParamName() + Constant.COMMA);
        }
        if (Constant.TXT.equals(format)) {
            csvFileOutputStream.write(Constant.FEED);
        } else {
            csvFileOutputStream.newLine();
        }
    }

    private void extracted(ResourceApplicationDTO record, BufferedWriter csvFileOutputStream, Page<ResourceDetail> page, List<SpTagsParam> paramList) throws Exception {
        // 写入内容
        for (int j = 0; j < page.getRows().size(); j++) {
            for (int i = 0; i < Constant.RESOURCE_EXPORT_FIELD.length; i++) {
                String value = CSVUtils.getValue(page.getRows().get(j), Constant.RESOURCE_EXPORT_FIELD[i]);
                this.writeFile(csvFileOutputStream, record, value, paramList);
            }
        }
    }

    public void writeFile(BufferedWriter csvFileOutputStream, ResourceApplicationDTO record, String value, List<SpTagsParam> paramList) throws Exception {
        for (SpTagsParam spTagsParam : paramList) {
            if (spTagsParam.getParamType() == NumConstant.NUM_ONE) {
                csvFileOutputStream.write(value + Constant.COMMA);
            } else {
                csvFileOutputStream.write(Constant.COMMA);
            }
        }
        if (Constant.TXT.equals(record.getFormat())) {
            csvFileOutputStream.write(Constant.FEED);
        } else {
            csvFileOutputStream.newLine();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void continueGenerateApplicationDetailInfo(ResourceApplicationEntityDTO record, List<SpTemplateItemDTO> itemList) throws Exception {
        // 已生成部分申请信息的情况下，继续完成剩余部分
        if (StringUtils.isNotBlank(record.getResourceApplyEnd()) ) {
            if (StringUtils.equals(record.getResourceType(), Constant.ResourceType.NETWORK_ACCESS)) {
                ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
                resourceDetailDTO.setApplyIdList(new ArrayList() {{
                    add(record.getApplyId());
                }});
                long count = resourceDetailRepository.getResourceDetailCount(resourceDetailDTO);
                record.setRemainQty(record.getApplyQty().subtract(BigDecimal.valueOf(count)));
            } else {
                BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(record.getResourceType(), record.getResourceApplyEnd());
                BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(record.getResourceType(), record.getResourceEnd());
                // 剩余申请数量
                BigInteger surplusNum = resourceEndNum.subtract(resourceStartNum);
                if (surplusNum.compareTo(BigInteger.ZERO) <= Constant.INT_0) {
                    return;
                }
                // 开始资源号
                BigInteger tempStartNum = resourceStartNum.add(BigInteger.ONE);
                String resourceStart = ResourceInfoServiceImpl.transformDecimal(record.getResourceType(), tempStartNum, "");
                String resourceEnd = ResourceInfoServiceImpl.transformDecimal(record.getResourceType(), resourceEndNum, "");
                record.setResourceStart(resourceStart);
                record.setResourceEnd(resourceEnd);
                // record.setApplyAmount(new BigDecimal(surplusNum.toString()));
                record.setUpdateDate(new Date());
            }
        }
        this.setSpParamInfo(record, itemList);
        record.setResourceStatus(Constant.STR_INIT);
        // 写申请明细
        this.insertDetailInfo(record);
        ResourceInfoEntityDTO resourceInfo = new ResourceInfoEntityDTO();
        // 更新资源池信息
//        this.updateResourceInfo(record, resourceInfo);
        // 更新申请单状态为初始化  APPLYING -> INIT
        ResourceApplicationEntityDTO dto = new ResourceApplicationEntityDTO();
        dto.setApplyId(record.getApplyId());
        dto.setResourceStatus(Constant.STR_INIT);
        resourceApplicationRepository.updateApplicationInfo(dto);
    }

    private void setSpParamInfo(ResourceApplicationEntityDTO record, List<SpTemplateItemDTO> itemList) {
        for (SpTemplateItemDTO itemDTO : itemList) {
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_START)) {
                String tempStr = itemDTO.getParamRule().replace(Constant.GenerationMethod.MAC_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                String separator = "";
                if (tempStr.contains(Constant.STR_SPLIT)) {
                    separator = tempStr.substring(0, tempStr.indexOf(','));
                } else {
                    separator = tempStr;
                }
                record.setSeparator(separator);
            }
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.GPON_SN)) {
                record.setMCode(itemDTO.getParamRule().replace(SpecialityParamConstant.GPON_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY));
            }
            if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.NASN)) {
                String consent = itemDTO.getParamRule().replace(SpecialityParamConstant.NASN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                record.setResourceNo(consent);
            }
        }
    }

    @Override
    public Page<ResourceApplicationEntityDTO> pageList(ResourceApplicationEntityDTO record) throws Exception {
        Page<ResourceApplicationEntityDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<ResourceApplicationEntityDTO> resourceApplicationList = resourceApplicationRepository.pageList(pageInfo);
        pageInfo.setRows(resourceApplicationList);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("插入资源申请")
    public long save(ResourceApplicationEntityDTO record) throws Exception {
        long result;
        try {
            ResourceInfoEntityDTO resourceInfo = validateRequestData(record,true);
            record.setResourceType(resourceInfo.getResourceType());
            //从资源池表信息中得到十六进制的资源开始和资源结束并转换为十进制
            BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(resourceInfo.getResourceType(), resourceInfo.getResourceStart());
            BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(resourceInfo.getResourceType(), resourceInfo.getResourceEnd());
            BigDecimal applyTotalQty = record.getApplyQty().multiply(record.getStandardQty());
            record.setApplyAmount(applyTotalQty);
            //根据resourceId查出所有从资源池已申请资源的结束
            ResourceApplicationEntityDTO entityModel = resourceApplicationRepository.selectOneOfNewestValid(record.getResourceId(), record.getResourceType());
            if (null != entityModel) {
                //查询最后一条，获取结束的资源号
                resourceStartNum = ResourceInfoServiceImpl.convertDecimal(resourceInfo.getResourceType(), entityModel.getResourceEnd());
                //并把下一条当做当前申请的开始资源号
                resourceStartNum = resourceStartNum.add(BigInteger.ONE);
            }
            //当前申请的结束MAC地址,算上开始的MAC地址本身
            BigInteger applyEndNum = resourceStartNum.add(applyTotalQty.toBigInteger()).subtract(BigInteger.ONE);
            //校验申请的资源数量n（申请数量*步距）是否大于资源可用总数
            if (applyEndNum.compareTo(resourceEndNum) > Constant.INT_0) {
                BigInteger surplusNum = resourceEndNum.subtract(resourceStartNum).add(BigInteger.ONE);
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APPLY_QTY_EXCEED_CAN_USE_QTY, new String[]{surplusNum.toString()});
            }
            //转为十六进制存入申请表中
            String resourceApplyStart = ResourceInfoServiceImpl.transformDecimal(resourceInfo.getResourceType(), resourceStartNum, resourceInfo.getResourceStart());
            String resourceApplyEnd = ResourceInfoServiceImpl.transformDecimal(resourceInfo.getResourceType(), applyEndNum, resourceInfo.getResourceEnd());
            record.setResourceStart(resourceApplyStart);
            record.setResourceEnd(resourceApplyEnd);
            convertResourceApplication(record, resourceInfo);//生成表基本信息
            result = resourceApplicationRepository.batchInsert(Collections.singletonList(record));
            //更新资源池数据可用数量
            resourceInfo.setAvailableQuantity(resourceEndNum.subtract(applyEndNum));
            //状态说明：新增时状态为“初始化”，已有申请记录状态为“使用中”
            if (ResourceStatusEnum.INIT.name().equals(resourceInfo.getResourceStatus())) {
                resourceInfo.setResourceStatus(ResourceStatusEnum.USING.name());
            }
            resourceInfoRepository.batchUpdateSelectivity(Collections.singletonList(resourceInfo));
            //更新行信息
            List<ResourceDetailEntityDTO> detailEntityDTOList = new ArrayList<>();
            BigInteger detailResourceStart = null;
            long standardQty = record.getStandardQty().longValue() - Constant.INT_1;
            for (long num = Constant.INT_1; num <= record.getApplyQty().longValue(); num++) {
                if (detailResourceStart == null) {
                    detailResourceStart = resourceStartNum;
                }
                BigInteger detailResourceEnd = detailResourceStart.add(new BigInteger(String.valueOf(standardQty)));
                ResourceDetailEntityDTO detailEntityDTO = new ResourceDetailEntityDTO();
                detailEntityDTO.setDistributionId(UUID.randomUUID().toString());
                detailEntityDTO.setApplyId(record.getApplyId());
                detailEntityDTO.setCreateUser(record.getCreateUser());
                detailEntityDTO.setUpdateDate(record.getUpdateDate());
                detailEntityDTO.setEnableFlag(Constant.FLAG_Y);
                detailEntityDTO.setResourceId(resourceInfo.getResourceId());
                detailEntityDTO.setResourceStart(ResourceInfoServiceImpl.transformDecimal(resourceInfo.getResourceType(), detailResourceStart, resourceInfo.getResourceStart()));
                detailEntityDTO.setResourceSn(ResourceInfoServiceImpl.transformDecimal(resourceInfo.getResourceType(), detailResourceEnd, resourceInfo.getResourceEnd()));
                detailEntityDTO.setResourceStep(record.getStandardQty());
                detailEntityDTO.setResourceStatus(record.getResourceStatus());
                detailResourceStart = detailResourceEnd.add(BigInteger.ONE);
                detailEntityDTOList.add(detailEntityDTO);
                if (detailEntityDTOList.size() == Constant.INT_2000) {
                    resourceDetailService.batchInsert(detailEntityDTOList);
                    detailEntityDTOList.clear();
                }
            }
            if (detailEntityDTOList.size() > Constant.INT_0) {
                resourceDetailService.batchInsert(detailEntityDTOList);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            redisTemplate.delete(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, record.getResourceId()));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("个参申请信息")
    public void saveApplicationInfo(ResourceApplicationEntityDTO record) throws Exception {
        // this.isValidRegex(record.getResourceNoRule());
        ResourceInfoEntityDTO resourceInfo = this.validateRequestData(record,false);
        // 生成表基本信息
        this.convertResourceApplication(record, resourceInfo);
        resourceApplicationRepository.updateApplicationInfo(record);
        // 更新资源池信息
        this.updateResourceInfo(record, resourceInfo);
        // 写申请明细
        this.insertDetailInfo(record);
    }

    private void updateResourceInfo(ResourceApplicationEntityDTO record, ResourceInfoEntityDTO resourceInfo) {
        // 更新资源池数据可用数量
        resourceInfo.setAvailableQuantity(null);
        // 资源为MAC时，consumptionQty不为空
        if (record.getConsumptionQty() == null || record.getConsumptionQty() == 0) {
            resourceInfo.setAvailableQuantitySubtract((new BigInteger(record.getApplyAmount().toString())));
        } else {
            resourceInfo.setAvailableQuantitySubtract((new BigInteger(record.getConsumptionQty().toString())));
        }
        // 状态说明：新增时状态为“初始化”，已有申请记录状态为“使用中”
        if (ResourceStatusEnum.INIT.name().equals(resourceInfo.getResourceStatus())) {
            resourceInfo.setResourceStatus(ResourceStatusEnum.USING.name());
        }
        // 资源号剩余小部分，状态可直接设置为“已使用”
        if (StringUtils.equals(Constant.FLAG_N,record.getIsLastNo())) {
            resourceInfo.setResourceStatus(ResourceStatusEnum.USED.name());
        }
        resourceInfoRepository.batchUpdateSelectivity(Collections.singletonList(resourceInfo));
    }

    /**
     * 写入申请明细
     */
    public void insertDetailInfo(ResourceApplicationEntityDTO record) throws Exception {
        List<ResourceDetailEntityDTO> detailEntityDTOList = new ArrayList<>();
        switch (record.getResourceType()) {
            case Constant.ResourceType.MAC:
                this.macDetailInfo(record, detailEntityDTOList);
                break;
            case Constant.ResourceType.GPON_SN:
                this.gponSnDetailInfo(record, detailEntityDTOList);
                break;
            case Constant.ResourceType.CMEI:
            case Constant.ResourceType.CUEI:
            case Constant.ResourceType.CTEI:
            case Constant.ResourceType.IMEI:
            case Constant.ResourceType.SN:
            case Constant.ResourceType.STB_SN:
            case Constant.ResourceType.SO_NET_SN:
                this.commonDetailInfo(record, detailEntityDTOList);
                break;
            case Constant.ResourceType.NETWORK_ACCESS:
                this.networkAccessDetailInfo(record);
                break;
            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(detailEntityDTOList)) {
            resourceDetailService.batchInsertDetailInfo(detailEntityDTOList);
        }
    }

    private void networkAccessDetailInfo(ResourceApplicationEntityDTO record) throws Exception {
        if (StringUtils.isBlank(record.getResourceNo())) {
            return;
        }
        int resourceTotal = record.getRemainQty() == null ? record.getApplyQty().intValue() : record.getRemainQty().intValue();
        int pageSize = NumConstant.NUM_500;
        long totalPages = BigDecimal.valueOf(resourceTotal).divide(BigDecimal.valueOf(pageSize), RoundingMode.UP).longValue();
        pageSize = resourceTotal < NumConstant.LONG_500 ? resourceTotal : pageSize;
        ResourceInfoEntityDTO entityDTO = resourceInfoRepository.selectByResourceNo(record.getResourceNo());
        if (entityDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY);
        }
        List<ResourceDetailEntityDTO> detailEntityDTOList = new ArrayList<>();
        for (int i = NumConstant.NUM_ONE; i <= totalPages; i++) {
            Integer currentPage = i;
            Page page = new Page();
            page.setSearchCount(false);
            page.setPageSize(pageSize);
            ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
            resourceInfoDetailDTO.setResourceNo(record.getResourceNo());
            page.setParams(resourceInfoDetailDTO);
            page.setCurrent(currentPage);
            List<ResourceInfoDetailDTO> detailDTOList = resourceInfoDetailRepository.getInitResourceNumByNo(page);
            if (CollectionUtils.isEmpty(detailDTOList)) {
                break;
            }
            this.insertNalDetailInfo(record, detailDTOList, entityDTO, detailEntityDTOList);
        }
    }

    private void insertNalDetailInfo(ResourceApplicationEntityDTO record, List<ResourceInfoDetailDTO> detailDTOList, ResourceInfoEntityDTO entityDTO, List<ResourceDetailEntityDTO> detailEntityDTOList) throws Exception {
        for (ResourceInfoDetailDTO detailDTO : detailDTOList) {
            ResourceDetailEntityDTO detailEntityDTO = new ResourceDetailEntityDTO();
            detailEntityDTO.setDistributionId(UUID.randomUUID().toString());
            detailEntityDTO.setApplyId(record.getApplyId());
            detailEntityDTO.setCreateUser(record.getCreateUser());
            detailEntityDTO.setUpdateDate(record.getUpdateDate());
            detailEntityDTO.setEnableFlag(Constant.FLAG_Y);
            detailEntityDTO.setResourceId(record.getResourceId());
            detailEntityDTO.setResourceStart(detailDTO.getResourceNum());
            detailEntityDTO.setResourceSn(detailDTO.getResourceNum());
            detailEntityDTO.setResourceStep(record.getStandardQty());
            detailEntityDTO.setResourceStatus(record.getResourceStatus());
            detailEntityDTO.setScramblingCode(detailDTO.getScramblingCode());
            detailEntityDTO.setModelNumAndCode(entityDTO.getModelNumber() + detailDTO.getScramblingCode());
            detailEntityDTOList.add(detailEntityDTO);
        }
        resourceDetailService.batchInsertDetailInfo(detailEntityDTOList);
    }

    private void commonDetailInfo(ResourceApplicationEntityDTO entityDTO, List<ResourceDetailEntityDTO> detailEntityDTOList) throws Exception {
        String tempStart = entityDTO.getResourceStart();
        for (int j = 0; j < entityDTO.getApplyQty().intValue(); j++) {
            if (StringUtils.equals(entityDTO.getResourceEnd(), tempStart)) {
                break;
            }
            if (j != Constant.INT_0) {
                tempStart = this.decimalIncrement(entityDTO.getResourceType(), tempStart, 1);
            }
            this.insertDetailInfo(entityDTO, tempStart, tempStart, detailEntityDTOList);
        }
    }

    private void gponSnDetailInfo(ResourceApplicationEntityDTO entityDTO, List<ResourceDetailEntityDTO> detailEntityDTOList) throws Exception {
        String tempStart = entityDTO.getResourceStart();
        String mCode = StringUtils.equals(entityDTO.getMCode(), Constant.ZTEG) ? Constant.ZTEG : entityDTO.getMCode();
        tempStart = tempStart.replace(Constant.ZTEG, mCode);
        for (int j = 0; j < entityDTO.getApplyQty().intValue(); j++) {
            if (StringUtils.equals(entityDTO.getResourceEnd(), tempStart)) {
                break;
            }
            if (j != Constant.INT_0) {
                tempStart = this.incrementGponSn(tempStart, new BigInteger(Constant.STR_ONE));
                tempStart = tempStart.replace(Constant.ZTEG, mCode);
            }
            this.insertDetailInfo(entityDTO, tempStart, tempStart, detailEntityDTOList);
        }
    }

    private void macDetailInfo(ResourceApplicationEntityDTO entityDTO, List<ResourceDetailEntityDTO> detailEntityDTOList) throws Exception {
        String tempStart = this.getStart(entityDTO);
        String tempEnd = "";
        // 根据申请数量和步距  申请数量1*步距为一条记录
        for (int i = 0; i < entityDTO.getApplyQty().intValue(); i++) {
            if (StringUtils.equals(entityDTO.getResourceEnd(), tempEnd)) {
                break;
            }
            if (i != Constant.INT_0) {
                tempStart = this.incrementMacAddress(tempEnd, entityDTO.getSeparator(),1);
            }
            tempEnd = this.incrementMacAddress(tempStart, entityDTO.getSeparator(), entityDTO.getResourceStep() - 1);
            this.insertDetailInfo(entityDTO, tempStart, tempEnd, detailEntityDTOList);
        }
    }

    private String getStart(ResourceApplicationEntityDTO entityDTO) {
        String tempStart = entityDTO.getResourceStart();
        tempStart = tempStart.replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        if (StringUtils.isBlank(entityDTO.getSeparator())) {
            return tempStart;
        }
        StringBuilder tempStr = new StringBuilder();
        for (int i = 0; i < tempStart.length(); i++) {
            if (i > 0 && i % 2 == 0) {
                tempStr.append(entityDTO.getSeparator());
            }
            tempStr.append(tempStart.charAt(i));
        }
        tempStart = tempStr.toString();
        return tempStart;
    }

    private void insertDetailInfo(ResourceApplicationEntityDTO record, String tempStart, String tempEnd, List<ResourceDetailEntityDTO> detailEntityDTOList) throws Exception {
        ResourceDetailEntityDTO detailEntityDTO = new ResourceDetailEntityDTO();
        detailEntityDTO.setDistributionId(UUID.randomUUID().toString());
        detailEntityDTO.setApplyId(record.getApplyId());
        detailEntityDTO.setCreateUser(record.getCreateUser());
        detailEntityDTO.setUpdateDate(record.getUpdateDate());
        detailEntityDTO.setEnableFlag(Constant.FLAG_Y);
        detailEntityDTO.setResourceId(record.getResourceId());
        detailEntityDTO.setResourceStart(tempStart);
        // resourceEnd   resourceSn命名不规范
        detailEntityDTO.setResourceSn(tempEnd);
        detailEntityDTO.setResourceStep(record.getStandardQty());
        detailEntityDTO.setResourceStatus(record.getResourceStatus());
        detailEntityDTOList.add(detailEntityDTO);
        if (detailEntityDTOList.size() == Constant.INT_2000) {
            resourceDetailService.batchInsertDetailInfo(detailEntityDTOList);
            detailEntityDTOList.clear();
        }
    }

    /**
     * 转换表基本信息
     *
     * @param record       请求参数
     * @param resourceInfo 资源池
     */
    private void convertResourceApplication(ResourceApplicationEntityDTO record, ResourceInfoEntityDTO resourceInfo) {
        //申请单号
        // record.setApplyId(UUID.randomUUID().toString());
        String date = DateUtil.convertDateToString(new Date(), Constant.DATE_FORMATE_DAY);
        //需求单号
        Long serialNumber = redisTemplate.opsForValue().increment(String.format(Constant.APPLY_BILL_NO_REDIS_KEY, date));
        if (serialNumber == null || serialNumber == Constant.INT_1) {
            redisTemplate.opsForValue().set(String.format(Constant.APPLY_BILL_NO_REDIS_KEY, date), String.valueOf(Constant.INT_1001), Constant.INT_24, TimeUnit.HOURS);
            serialNumber = Constant.INT_1001;
        }
        String serialNumberStr = String.valueOf(serialNumber);
        serialNumberStr = serialNumberStr.substring(Constant.INT_1);
        //电信用户（特殊用户）申请单号以DXBC开头，其余格式为：产品大类+‘-’+年+月+日+流水号
        if (StringUtils.equals(record.getUserType(), Constant.SPECIAL_USER)) {
            record.setApplyBillNo(Constant.APPLY_BILL_NO_PREFIX + resourceInfo.getProductCategory() + '-' + date + serialNumberStr);
        } else {
            record.setApplyBillNo(record.getProductBigClass() + '-' + date + serialNumberStr);
        }
        //是否导出为“N”，是否分配为“Y”
        record.setIsExported(Constant.FLAG_N);
        record.setIsDistributed(Constant.FLAG_Y);
        //申请状态
        String resourceStatus = null;
        switch (record.getUserType()) {
            case Constant.SPECIAL_USER:
                resourceStatus = ResourceStatusEnum.LOCKING.name();
                record.setAllocatedQty(Constant.LONG_ZERO);
                break;
            case Constant.GENERAL_USER:
                resourceStatus = StringUtils.isBlank(record.getResourceStatus()) ? ResourceStatusEnum.INIT.name() : record.getResourceStatus();
                break;
            default:
                break;
        }
        record.setSourceSystem(resourceInfo.getSourceSystem());
        record.setCustInfo(resourceInfo.getCustInfo());
        record.setUsageScope(resourceInfo.getUsageScope());
        record.setResourceStatus(resourceStatus);
        record.setResourceStep(record.getStandardQty().intValue());
    }

    /**
     * 验证请求参数并获取资源池信息
     *
     * @param record 请求参数
     * @return 资源池信息
     * @throws MesBusinessException 异常
     */
    public ResourceInfoEntityDTO validateRequestData(ResourceApplicationEntityDTO record, boolean setLock) throws MesBusinessException {
        if (setLock) {
            Boolean lockResult = redisTemplate.opsForValue().setIfAbsent(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, record.getResourceId()), record.getResourceId(), Constant.INT_10, TimeUnit.MINUTES);
            if (lockResult == null || !lockResult) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_IS_APPLYING, new String[]{record.getResourceNo()});
            }
        }
        //判断用户是否是普通用户还是特殊用户
        if (!Constant.SPECIAL_USER.equals(record.getUserType()) && !Constant.GENERAL_USER.equals(record.getUserType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.USERTYPE_NOT_CORRENT);
        }
        //普通用户任务号必填，且必须唯一
//        if (Constant.GENERAL_USER.equals(record.getUserType())) {
//            List<ResourceApplicationEntityDTO> resourceTaskList = resourceApplicationRepository.taskList(record.getApplyTask());
//            if (!CollectionUtils.isEmpty(resourceTaskList) && resourceTaskList.stream().anyMatch(item -> item.getResourceType().equals(record.getResourceType()))) {
//                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ORDER_OR_TASK_HAS_BEEN_USED);
//            }
//        }
        //根据resourceId寻找资源池信息
        List<ResourceInfoEntityDTO> resourceInfoList = resourceInfoRepository.getList(new ResourceInfoEntityDTO() {{
            setResourceId(record.getResourceId());
        }});
        if (CollectionUtils.isEmpty(resourceInfoList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_INFO_NOT_EXIST, new String[]{record.getResourceNo()});
        }
        ResourceInfoEntityDTO resourceInfo = resourceInfoList.get(Constant.INT_0);
        if (resourceInfo.getAvailableQuantity() == null || resourceInfo.getAvailableQuantity().compareTo(BigInteger.ZERO) <= Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.AVAILABLE_QUANTITY_IS_ZERO, new String[]{record.getResourceNo()});
        }
        return resourceInfo;
    }

    @Override
    public long update(ResourceApplicationEntityDTO record) {
        return resourceApplicationRepository.updateResourceApplication(BeanUtil.copyProperties(record, ResourceApplicationDTO.class));
    }

    @Override
    public int retryInsert(List<String> recordIdList) throws Exception {
        //根据Id查询记录表
        List<ResourceApplicationRecordDTO> recordDTOList = resourceApplicationRepository.getRecordListByIds(recordIdList);
        if (CollectionUtils.isEmpty(recordDTOList)) {
            return Constant.INT_0;
        }
        for (ResourceApplicationRecordDTO recordDTO : recordDTOList) {
            if (StringUtils.isBlank(recordDTO.getResourceNo())) {
                continue;
            }
            ResourceApplicationEntityDTO entityDTO = resourceApplicationRepository.getResourceApplicationEntityById(recordDTO.getApplyId());

            BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(entityDTO.getResourceType(), recordDTO.getResourceStart());

            List<ResourceDetailEntityDTO> detailEntityDTOList = new ArrayList<>();
            BigInteger detailResourceStart = null;
            long standardQty = entityDTO.getStandardQty().longValue() - Constant.INT_1;
            for (long num = Constant.INT_1; num <= Integer.parseInt(recordDTO.getResourceNo()); num++) {
                if (detailResourceStart == null) {
                    detailResourceStart = resourceStartNum;
                }
                BigInteger detailResourceEnd = detailResourceStart.add(new BigInteger(String.valueOf(standardQty)));
                ResourceDetailEntityDTO detailEntityDTO = new ResourceDetailEntityDTO();
                detailEntityDTO.setDistributionId(UUID.randomUUID().toString());
                detailEntityDTO.setApplyId(entityDTO.getApplyId());
                detailEntityDTO.setCreateUser(entityDTO.getCreateUser());
                detailEntityDTO.setUpdateDate(entityDTO.getUpdateDate());
                detailEntityDTO.setEnableFlag(Constant.FLAG_Y);
                detailEntityDTO.setResourceId(entityDTO.getResourceId());
                detailEntityDTO.setResourceStart(ResourceInfoServiceImpl.transformDecimal(entityDTO.getResourceType(), detailResourceStart, entityDTO.getResourceStart()));
                detailEntityDTO.setResourceSn(ResourceInfoServiceImpl.transformDecimal(entityDTO.getResourceType(), detailResourceEnd, entityDTO.getResourceEnd()));
                detailEntityDTO.setResourceStep(entityDTO.getStandardQty());
                detailEntityDTO.setResourceStatus(entityDTO.getResourceStatus());
                detailResourceStart = detailResourceEnd.add(BigInteger.ONE);
                detailEntityDTOList.add(detailEntityDTO);
                if (detailEntityDTOList.size() == Constant.INT_2000) {
                    resourceDetailService.batchInsert(detailEntityDTOList);
                    detailEntityDTOList.clear();
                }
            }
            if (detailEntityDTOList.size() > Constant.INT_0) {
                resourceDetailService.batchInsert(detailEntityDTOList);
            }
            resourceApplicationRepository.deleteRecord(recordDTO);
        }
        return Constant.INT_1;
    }

    @Override
    public int deleteRetryRecord(List<String> recordIdList) {
        return resourceApplicationRepository.deleteRetryRecordById(recordIdList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void allocate(ResourceApplicationAllocateDTO param) throws Exception {
        try {
            Boolean lockResult = redisTemplate.opsForValue().setIfAbsent(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, param.getParentApplyId()), param.getParentApplyId(), Constant.INT_10, TimeUnit.MINUTES);
            if (lockResult == null || !lockResult) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_IS_APPLYING, new String[]{param.getParentApplyId()});
            }

            ResourceApplicationEntityDTO resourceApplicationEntityDTO = this.validateAllocate(param);

            // 更新父单的数据
            ResourceApplicationDTO resourceApplicationDTO = new ResourceApplicationDTO();
            resourceApplicationDTO.setApplyBillNo(resourceApplicationEntityDTO.getApplyBillNo());
            resourceApplicationDTO.setUpdateUser(param.getCreateUser());
            resourceApplicationDTO.setAllocatedQty(resourceApplicationEntityDTO.getAllocatedQty() == null ?
                    0L : resourceApplicationEntityDTO.getAllocatedQty() + param.getApplyQty().longValue());
            if (null != resourceApplicationEntityDTO.getApplyQty() && resourceApplicationEntityDTO.getApplyQty().longValue() == resourceApplicationDTO.getAllocatedQty()) {
                resourceApplicationDTO.setResourceStatus(ResourceStatusEnum.ALLOCATED.name());
            }
            resourceApplicationRepository.updateResourceApplication(resourceApplicationDTO);

            // 新增拆分单
            ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
            resourceDetailDTO.setApplyId(param.getParentApplyId());
            resourceDetailDTO.setPage(Constant.INT_1);
            resourceDetailDTO.setRows(param.getApplyQty().intValue());
            List<ResourceDetail> detailList = resourceDetailRepository.getFirstAndLast(resourceDetailDTO);
            resourceApplicationEntityDTO.setResourceStart(detailList.get(Constant.NUM_ZERO).getResourceStart());
            resourceApplicationEntityDTO.setResourceEnd(detailList.get(detailList.size() - NumConstant.NUM_ONE).getResourceSn());
            resourceApplicationEntityDTO.setApplyId(UUID.randomUUID().toString());
            resourceApplicationEntityDTO.setResourceStatus(ResourceStatusEnum.INIT.name());
            resourceApplicationEntityDTO.setParentApplyBillNo(resourceApplicationEntityDTO.getApplyBillNo());
            resourceApplicationEntityDTO.setApplyBillNo(this.getAllocateApplyBillNo(resourceApplicationEntityDTO.getApplyBillNo()));
            resourceApplicationEntityDTO.setApplyQty(param.getApplyQty());
            resourceApplicationEntityDTO.setAllocatedQty(null);
            resourceApplicationEntityDTO.setRemark(param.getRemark());
            resourceApplicationEntityDTO.setApplyTask(param.getApplyTask());
            resourceApplicationEntityDTO.setItemNo(param.getItemNo());
            resourceApplicationEntityDTO.setUpdateUser(param.getCreateUser());
            resourceApplicationEntityDTO.setCreateUser(param.getCreateUser());
            resourceApplicationRepository.batchInsert(Collections.singletonList(resourceApplicationEntityDTO));

            // 更新资源详情
            ResourceDetail resourceDetail = new ResourceDetail();
            resourceDetail.setApplyId(resourceApplicationEntityDTO.getApplyId());
            resourceDetail.setResourceStart(resourceApplicationEntityDTO.getResourceStart());
            resourceDetail.setResourceSn(resourceApplicationEntityDTO.getResourceEnd());
            resourceDetailRepository.updateApplyId(resourceDetail);
        } finally {
            //删除锁信息
            redisTemplate.delete(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, param.getParentApplyId()));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalid(ResourceApplicationInvalidDTO invalidDTO) {
        ResourceApplicationEntityDTO entityDTO = resourceApplicationRepository.getResourceApplicationEntityById(invalidDTO.getApplyId());
        if (null == entityDTO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_NOT_EXIST, new String[]{invalidDTO.getApplyId()});
        }

        //根据resourceId查出所有从资源池已申请资源的结束
        ResourceApplicationEntityDTO newestValid = resourceApplicationRepository.selectOneOfNewestValid2(entityDTO.getResourceId(), entityDTO.getResourceType(),entityDTO.getParentApplyBillNo());
        if (null != newestValid && !newestValid.getApplyId().equals(invalidDTO.getApplyId())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_NOT_NEWEST);
        }

        Boolean lockResult = redisTemplate.opsForValue().setIfAbsent(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, entityDTO.getResourceId()), entityDTO.getResourceId(), Constant.INT_10, TimeUnit.MINUTES);
        if (lockResult == null || !lockResult) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_IS_APPLYING, new String[]{entityDTO.getResourceNo()});
        }
        try {
            ResourceApplicationDTO resourceApplicationDTO = new ResourceApplicationDTO();
            resourceApplicationDTO.setApplyBillNo(entityDTO.getApplyBillNo());
            resourceApplicationDTO.setUpdateUser(invalidDTO.getCreateUser());
            resourceApplicationDTO.setResourceStatus(ResourceStatusEnum.INVALID.name());
            resourceApplicationRepository.updateResourceApplication(resourceApplicationDTO);

            if (StringUtils.isBlank(entityDTO.getParentApplyBillNo())) {
                //根据resourceId寻找资源池信息
                ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
                resourceInfoEntityDTO.setResourceId(entityDTO.getResourceId());
                List<ResourceInfoEntityDTO> resourceInfoList = resourceInfoRepository.getList(resourceInfoEntityDTO);
                if (CollectionUtils.isEmpty(resourceInfoList)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_INFO_NOT_EXIST, new String[]{entityDTO.getResourceNo()});
                }
                ResourceInfoEntityDTO resourceInfo = resourceInfoList.get(Constant.INT_0);
                BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(entityDTO.getResourceType(), null != newestValid ? newestValid.getResourceStart() : resourceInfo.getResourceStart());
                BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(resourceInfo.getResourceType(), resourceInfo.getResourceEnd());
                //更新资源池数据可用数量
                resourceInfo.setAvailableQuantity(resourceEndNum.subtract(resourceStartNum).add(BigInteger.ONE));
                if (resourceInfo.getAvailableQuantity().compareTo(resourceInfo.getResourceAmount()) == 0) {
                    resourceInfo.setResourceStatus(ResourceStatusEnum.INIT.name());
                }
                resourceInfoRepository.batchUpdateSelectivity(Collections.singletonList(resourceInfo));
                resourceDetailRepository.deleteByApplyId(entityDTO.getApplyId());
            } else {
                List<ResourceApplicationDTO> appliyList = resourceApplicationRepository.selectByApplyBillNo(entityDTO.getParentApplyBillNo());
                ResourceApplicationDTO parentEntityDTO = appliyList.get(NumConstant.NUM_ZERO);
                parentEntityDTO.setUpdateUser(invalidDTO.getCreateUser());
                parentEntityDTO.setAllocatedQty(parentEntityDTO.getAllocatedQty() - entityDTO.getApplyQty().longValue());
                if (parentEntityDTO.getResourceStatus().equals(ResourceStatusEnum.ALLOCATED.name())) {
                    parentEntityDTO.setResourceStatus(ResourceStatusEnum.IMPORTED.name());
                }
                resourceApplicationRepository.updateResourceApplication(parentEntityDTO);
                resourceDetailRepository.updateNewApplyId(parentEntityDTO.getApplyId(), entityDTO.getApplyId());
            }
        } finally {
            //删除锁信息
            redisTemplate.delete(String.format(Constant.APPLY_APPLICATION_LOCK_KEY, entityDTO.getResourceId()));
        }
    }

    private String getAllocateApplyBillNo(String parentApplyBillNo) {
        return parentApplyBillNo + Constant.LINE + (resourceApplicationRepository.countResourceApplicationByParentApplyBillNo(parentApplyBillNo) + Constant.LONG_1);

    }

    private ResourceApplicationEntityDTO validateAllocate(ResourceApplicationAllocateDTO param) throws Exception {
        // 检查资源申请是否存在
        ResourceApplicationEntityDTO resourceApplicationEntityDTO = resourceApplicationRepository.getResourceApplicationEntityById(param.getParentApplyId());
        if (null == resourceApplicationEntityDTO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RESOURCE_APPLICATION_NOT_EXIST, new String[]{param.getParentApplyId()});
        }

        // 资源详情是否还有没有分配的
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setApplyId(param.getParentApplyId());
        long num = resourceDetailRepository.getResourceDetailCount(resourceDetailDTO);
        if (num < param.getApplyQty().longValue()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APPLY_QTY_EXCEED_CAN_USE_QTY, new String[]{String.valueOf(num)});
        }

        // 订单/任务ID检查
        List<ResourceApplicationEntityDTO> resourceTaskList = resourceApplicationRepository.taskList(param.getApplyTask());
        if (CollectionUtils.isNotEmpty(resourceTaskList) && resourceTaskList.stream().anyMatch(item -> item.getResourceType().equals(resourceApplicationEntityDTO.getResourceType()))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ORDER_OR_TASK_HAS_BEEN_USED);
        }

        return resourceApplicationEntityDTO;
    }

    /**
     * 校验资源编号数量信息
     *
     */
    @Override
    public void validApplyQty(SpTemplateItemDTO itemDTO, SpSpecialityParam spSpecialityParam, List<ResourceForApplicationDTO> applicationDTOList) {
        if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.MAC)) {
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_START)) {
                this.validApplyQtyToMACStart(spSpecialityParam, applicationDTOList);
            } else if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.CUSTOMIZED_MAC_START)) {
                this.validApplyQtyToCustomizedMACStart(spSpecialityParam, itemDTO, applicationDTOList);
            }
        } else if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.GPON_SN)) {
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.GPON_SN)) {
                this.validApplyQtyToGponSn(spSpecialityParam, applicationDTOList);
            } else if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.TELMEX_GPON_SN)) {
                this.validApplyQtyToTelmexGponSn(spSpecialityParam);
            } else {
                this.validApplyQtyToCustomizedGponSn(spSpecialityParam, itemDTO, applicationDTOList);
            }
        } else if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.NASN)) {
            this.validApplyQtyToNASN(spSpecialityParam, itemDTO, applicationDTOList);
        } else if (StringUtils.equals(itemDTO.getParamType(), Constant.ParamType.ASSIGNMENT) &&
                StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.ASSIGNMENT_DB)) {
            this.validApplyQtyToDBParams(itemDTO, applicationDTOList, spSpecialityParam);
        }
    }


    private void validApplyQtyToTelmexGponSn(SpSpecialityParam spSpecialityParam) {
        // 申请总数
        long tempApplyQty = spSpecialityParam.getApplyQty();
        if (tempApplyQty <= 0) {
            return;
        }
        // 本周以及后续四周
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < Constant.INT_4; i++) {
            // 获取0-4周后的时间
            LocalDate localDate = currentDate.plusWeeks(i);
            // 获取年份的后两位(2位，不足2位补0)
            int currentYear = localDate.getYear();
            String yearPart = String.format("%02d", currentYear % 100);
            // 获取周数(2位，不足2位补0)
            WeekFields weekFields = WeekFields.of(Locale.getDefault());
            int weekOfYear = localDate.get(weekFields.weekOfYear());
            String weekPart = String.format("%02d", weekOfYear);
            // 根据 eigenValue 查询剩已使用的 累计值 4位随机数每周有65536个： 0-65535
            String eigenValue = Constant.GPON_SN_PREFIX + yearPart + weekPart;
            Long total = spSpecialityParamRepository.getTelmexEigenValueIndex(eigenValue);
            // 不存在则新增 数据库存的累计值 是待使用的序列号值
            // 累计值 默认位0（0000） 最大65535（FFFF），使用完了应该是存65536
            if (null == total) {
                total = Constant.LONG_ZERO;
                SpSpecialityParam telmexEigen = new SpSpecialityParam();
                telmexEigen.setTelmexEigenValue(eigenValue);
                telmexEigen.setTelmexTotal(total);
                telmexEigen.setLastUpdatedBy(spSpecialityParam.getLastUpdatedBy());
                spSpecialityParamRepository.insertTelmexEigenValue(telmexEigen);
            }
            if (total == Constant.LONG_65536) {
                continue;
            }
            tempApplyQty = tempApplyQty - (Constant.LONG_65536 - total);
            if (tempApplyQty <= 0) {
                return;
            }
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_GPON_SN_AVAILABLE_RESOURCE);
    }


    private void validApplyQtyToDBParams(SpTemplateItemDTO itemDTO, List<ResourceForApplicationDTO> applicationDTOList, SpSpecialityParam spSpecialityParam) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_BD_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        // 参数类型
        String paramType = tempStr.substring(0, tempStr.indexOf(','));
        // 前缀固定值
        String fixedValue = tempStr.substring(tempStr.indexOf(',') + 1);
        List<String> resourceNoList = new ArrayList<>();
        long tempApplyQty = spSpecialityParam.getApplyQty();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("resourceType", paramType);
        queryMap.put("usageScope", spSpecialityParam.getUsageScope());
        queryMap.put("fixedValue", fixedValue);
        queryMap.put("productBigClass", spSpecialityParam.getProductBigClass());
        queryMap.put("productSmallClass", spSpecialityParam.getProductSmallClass());
        do {
            ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
            dto.setResourceType(paramType);
            queryMap.put("resourceNoList", resourceNoList);
            ResourceApplicationEntityDTO entityDTO = resourceApplicationRepository.getResourceInfoByCondition(queryMap);
            if (null == entityDTO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_AVAILABLE_RESOURCE, new String[]{paramType});
            }
            dto.setResourceId(entityDTO.getResourceId());
            // 资源无申请记录
            if (StringUtils.isBlank(entityDTO.getResourceApplyEnd())) {
                dto.setResourceStart(entityDTO.getResourceStart());
            } else {
                dto.setResourceStart(this.decimalIncrement(paramType, entityDTO.getResourceApplyEnd(), 1));
            }
            // 申请数量大于资源可用数量
            if (tempApplyQty > entityDTO.getAvailableQuantity()) {
                dto.setResourceEnd(entityDTO.getResourceEnd());
                dto.setResourceQty(entityDTO.getAvailableQuantity());
                tempApplyQty -= entityDTO.getAvailableQuantity();
            } else {
                dto.setResourceEnd(this.decimalIncrement(paramType ,dto.getResourceStart(), tempApplyQty - 1));
                dto.setResourceQty(tempApplyQty);
                tempApplyQty = Constant.INT_0;
            }
            if (tempApplyQty <= 0) {
                dto.setIsLastNo(Constant.FLAG_Y);
            } else {
                dto.setIsLastNo(Constant.FLAG_N);
            }
            resourceNoList.add(entityDTO.getResourceNo());
            applicationDTOList.add(dto);
        } while (tempApplyQty > 0);
    }

    private void validApplyQtyToNASN(SpSpecialityParam spSpecialityParam, SpTemplateItemDTO itemDTO, List<ResourceForApplicationDTO> applicationDTOList) {
        // 批文
        String consent = itemDTO.getParamRule().replace(SpecialityParamConstant.NASN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("resourceNo", consent);
        paramMap.put("status", Constant.STR_0);
        int count = resourceInfoDetailRepository.countByResourceNo(paramMap);
        if (spSpecialityParam.getApplyQty() > count) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CONSENT_AVAILABLE_QUANTITY_IS_INSUFFICIENT);
        }
        ResourceInfoEntityDTO entityDTO = resourceInfoRepository.getResourceInfoByNo(consent);
        ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
        dto.setResourceId(entityDTO.getResourceId());
        dto.setResourceNo(entityDTO.getResourceNo());
        dto.setResourceType(Constant.ResourceType.NETWORK_ACCESS);
        dto.setResourceQty(spSpecialityParam.getApplyQty());
        dto.setIsLastNo(Constant.FLAG_Y);
        applicationDTOList.add(dto);
    }

    private void validApplyQtyToCustomizedGponSn(SpSpecialityParam spSpecialityParam, SpTemplateItemDTO itemDTO, List<ResourceForApplicationDTO> applicationDTOList) {
        List<String> resourceNoList = new ArrayList<>();
        // 前缀固定值
        String fixedValue = itemDTO.getParamRule().replace(SpecialityParamConstant.GPON_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        long tempApplyQty = spSpecialityParam.getApplyQty();
        Map<String, Object> cGponSnQueryMap = new HashMap<>();
        cGponSnQueryMap.put("resourceType", Constant.ResourceType.GPON_SN);
        cGponSnQueryMap.put("fixedValue", fixedValue);
        cGponSnQueryMap.put("usageScope", spSpecialityParam.getUsageScope());
        cGponSnQueryMap.put("productBigClass", spSpecialityParam.getProductBigClass());
        cGponSnQueryMap.put("productSmallClass", spSpecialityParam.getProductSmallClass());
        do {
            ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
            dto.setResourceType(Constant.ParamType.GPON_SN);
            cGponSnQueryMap.put("resourceNoList", resourceNoList);
            ResourceApplicationEntityDTO cGponSnEntityDTO = resourceApplicationRepository.getResourceInfoByCondition(cGponSnQueryMap);
            if (null == cGponSnEntityDTO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_GPON_SN_AVAILABLE_RESOURCE);
            }
            dto.setResourceId(cGponSnEntityDTO.getResourceId());
            // 资源无申请记录
            if (StringUtils.isBlank(cGponSnEntityDTO.getResourceApplyEnd())) {
                dto.setResourceStart(cGponSnEntityDTO.getResourceStart());
            } else {
                dto.setResourceStart(this.incrementGponSn(cGponSnEntityDTO.getResourceApplyEnd(), new BigInteger("1")));
            }
            // 申请数量大于资源可用数量
            if (tempApplyQty > cGponSnEntityDTO.getAvailableQuantity()) {
                dto.setResourceEnd(cGponSnEntityDTO.getResourceEnd());
                dto.setResourceQty(cGponSnEntityDTO.getAvailableQuantity());
                tempApplyQty -= cGponSnEntityDTO.getAvailableQuantity();
            } else {
                dto.setResourceEnd(this.incrementGponSn(dto.getResourceStart(), new BigInteger(String.valueOf(tempApplyQty - 1))));
                dto.setResourceQty(tempApplyQty);
                tempApplyQty = 0;
            }
            if (tempApplyQty <= 0) {
                dto.setIsLastNo(Constant.FLAG_Y);
            } else {
                dto.setIsLastNo(Constant.FLAG_N);
            }
            resourceNoList.add(cGponSnEntityDTO.getResourceNo());
            applicationDTOList.add(dto);
        } while (tempApplyQty > 0);
    }

    private void validApplyQtyToGponSn(SpSpecialityParam spSpecialityParam, List<ResourceForApplicationDTO> applicationDTOList) {
        List<String> resourceNoList = new ArrayList<>();
        long tempApplyQty = spSpecialityParam.getApplyQty();
        Map<String, Object> gponSnQueryMap = new HashMap<>();
        gponSnQueryMap.put("resourceType", Constant.ResourceType.GPON_SN);
        gponSnQueryMap.put("fixedValue", Constant.ZTEG);
        gponSnQueryMap.put("usageScope", spSpecialityParam.getUsageScope());
        gponSnQueryMap.put("productBigClass", spSpecialityParam.getProductBigClass());
        gponSnQueryMap.put("productSmallClass", spSpecialityParam.getProductSmallClass());
        do {
            ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
            dto.setResourceType(Constant.ParamType.GPON_SN);
            gponSnQueryMap.put("resourceNoList", resourceNoList);
            ResourceApplicationEntityDTO gponSnEntityDTO = resourceApplicationRepository.getResourceInfoByCondition(gponSnQueryMap);
            if (null == gponSnEntityDTO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_GPON_SN_AVAILABLE_RESOURCE);
            }
            dto.setResourceId(gponSnEntityDTO.getResourceId());
            // 资源无申请记录
            if (StringUtils.isBlank(gponSnEntityDTO.getResourceApplyEnd())) {
                dto.setResourceStart(gponSnEntityDTO.getResourceStart());
            } else {
                // 有申请记录
                String start = this.incrementGponSn(gponSnEntityDTO.getResourceApplyEnd(), new BigInteger("1"));
                dto.setResourceStart(start);
            }
            // 申请数量大于资源可用数量
            if (tempApplyQty > gponSnEntityDTO.getAvailableQuantity()) {
                dto.setResourceEnd(gponSnEntityDTO.getResourceEnd());
                dto.setResourceQty(gponSnEntityDTO.getAvailableQuantity());
                tempApplyQty -= gponSnEntityDTO.getAvailableQuantity();
            } else {
                dto.setResourceEnd(this.incrementGponSn(dto.getResourceStart(), new BigInteger(String.valueOf(tempApplyQty - 1))));
                dto.setResourceQty(tempApplyQty);
                tempApplyQty = Constant.INT_0;
            }
            if (tempApplyQty <= 0) {
                dto.setIsLastNo(Constant.FLAG_Y);
            } else {
                dto.setIsLastNo(Constant.FLAG_N);
            }
            resourceNoList.add(gponSnEntityDTO.getResourceNo());
            applicationDTOList.add(dto);
        } while (tempApplyQty > 0);
    }

    private void validApplyQtyToCustomizedMACStart(SpSpecialityParam spSpecialityParam, SpTemplateItemDTO itemDTO, List<ResourceForApplicationDTO> applicationDTOList) {
        List<String> resourceNoList = new ArrayList<>();
        String paramRule = itemDTO.getParamRule();
        String tempStr = paramRule.replace(Constant.GenerationMethod.MAC_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        // 分隔符
        String separator = tempStr.substring(0, tempStr.indexOf(','));
        // 特殊结尾
        String specialEnding = tempStr.substring(tempStr.indexOf(',') + 1);
        long tempApplyQty = spSpecialityParam.getApplyQty();
        Map<String, Object> cMacQueryMap = new HashMap<>();
        cMacQueryMap.put("resourceType", Constant.ResourceType.MAC);
        cMacQueryMap.put("usageScope", spSpecialityParam.getUsageScope());
        cMacQueryMap.put("productBigClass", spSpecialityParam.getProductBigClass());
        cMacQueryMap.put("productSmallClass", spSpecialityParam.getProductSmallClass());
        do {
            ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
            dto.setResourceType(Constant.ResourceType.MAC);
            cMacQueryMap.put("resourceNoList", resourceNoList);
            ResourceApplicationEntityDTO cMacEntityDTO = resourceApplicationRepository.getResourceInfoByCondition(cMacQueryMap);
            if (null == cMacEntityDTO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MAC_AVAILABLE_RESOURCE);
            }
            cMacEntityDTO.setSeparator(separator);
            dto.setResourceId(cMacEntityDTO.getResourceId());
            resourceNoList.add(cMacEntityDTO.getResourceNo());
            long stepL = Long.parseLong(spSpecialityParam.getStep());
            if (stepL < Constant.LONG_1) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            }
            // 资源无申请记录
            if (StringUtils.isBlank(cMacEntityDTO.getResourceApplyEnd())) {
                tempApplyQty = this.getQtyByNoApplication(cMacEntityDTO, specialEnding, tempApplyQty, stepL, dto);
            } else { // 资源有申请记录
                // 申请资源号开始
                String startNum = this.incrementMacAddress(cMacEntityDTO.getResourceApplyEnd(), cMacEntityDTO.getSeparator(), 1);
                resourceNoList.add(cMacEntityDTO.getResourceNo());
                // 资源号开头即符合特殊结尾
                if (StringUtils.equals(specialEnding, startNum.substring(startNum.length() - 1))) {
                    long availableQty = cMacEntityDTO.getAvailableQuantity();
                    tempApplyQty = this.calApplyQtyByNoApplicationToMACStart(stepL, availableQty, tempApplyQty, dto);
                    dto.setResourceStart(startNum);
                    String incrementedMac = this.incrementMacAddress(startNum, cMacEntityDTO.getSeparator(), dto.getResourceQty() - 1);
                    dto.setResourceEnd(incrementedMac);
                } else { // 资源号开头不符合特殊结尾，循环往下取到第一个值
                    tempApplyQty = this.getTempApplyQty(cMacEntityDTO, specialEnding, dto, tempApplyQty, stepL);
                }
            }
            if (tempApplyQty <= 0) {
                dto.setIsLastNo(Constant.FLAG_Y);
            } else {
                dto.setIsLastNo(Constant.FLAG_N);
            }
            applicationDTOList.add(dto);
        } while (tempApplyQty > 0);
    }


    /**
     * 定制MACStart情况下，资源无申请记录，计算资源可用数量是否满足申请数量
     *
     */
    private long getQtyByNoApplication(ResourceApplicationEntityDTO cMacEntityDTO, String specialEnding, long tempApplyQty, long stepL, ResourceForApplicationDTO dto) {
        String end = cMacEntityDTO.getResourceStart().substring(cMacEntityDTO.getResourceStart().length() - 1);
        // 资源号开头即符合特殊结尾
        if (StringUtils.equals(specialEnding, end)) {
            long availableQty = cMacEntityDTO.getAvailableQuantity();
            tempApplyQty = this.calApplyQtyByNoApplicationToMACStart(stepL, availableQty, tempApplyQty, dto);
            String incrementedMac = this.incrementMacAddress(cMacEntityDTO.getResourceStart(), cMacEntityDTO.getSeparator(), dto.getResourceQty() - 1);
            dto.setResourceStart(cMacEntityDTO.getResourceStart());
            dto.setResourceEnd(incrementedMac);
        } else { // 资源号开头不符合特殊结尾，循环往下取到第一个值
            tempApplyQty = this.getApplyQty(cMacEntityDTO, specialEnding, dto, tempApplyQty, stepL);
        }
        return tempApplyQty;
    }


    /**
     * 定制MACStart情况下，资源无申请记录，资源号开头不符合特殊结尾，循环往下取到第一个值
     *
     */
    private long getApplyQty(ResourceApplicationEntityDTO cMacEntityDTO, String specialEnding, ResourceForApplicationDTO dto, long tempApplyQty, long stepL) {
        int tempNum = 0;
        String incrementedMac = cMacEntityDTO.getResourceStart();
        do {
            tempNum++;
            incrementedMac = this.incrementMacAddress(incrementedMac, cMacEntityDTO.getSeparator(), 1);
        } while (!StringUtils.equals(specialEnding, incrementedMac.substring(incrementedMac.length() - 1)));
        dto.setResourceStart(incrementedMac);
        BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, incrementedMac);
        BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, cMacEntityDTO.getResourceEnd());
        BigInteger surplusNum = resourceEndNum.subtract(resourceStartNum);
        // 资源可用数量
        long availableQty = Long.parseLong(surplusNum.toString());
        tempApplyQty = this.calApplyQtyByToCusMACStartWithEnding(stepL, availableQty, tempApplyQty, dto, tempNum);
        String resourceEnd = this.incrementMacAddress(dto.getResourceStart(), cMacEntityDTO.getSeparator(), dto.getResourceQty() - 1);
        dto.setResourceEnd(resourceEnd);
        return tempApplyQty;
    }

    /**
     * 资源无申请记录，资源号开头不符合特殊结尾，循环往下取到第一个值，并计算资源可用数量是否满足申请数量
     *
     */
    private long getTempApplyQty(ResourceApplicationEntityDTO cMacEntityDTO, String specialEnding, ResourceForApplicationDTO dto, long tempApplyQty, long stepL) {
        int num = 0;
        String tempMac = this.incrementMacAddress(cMacEntityDTO.getResourceApplyEnd(), cMacEntityDTO.getSeparator(), 1);
        do {
            num++;
            tempMac = this.incrementMacAddress(tempMac, cMacEntityDTO.getSeparator(), 1);
        } while (!StringUtils.equals(specialEnding, tempMac.substring(tempMac.length() - 1)));
        dto.setResourceStart(tempMac);
        BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, tempMac);
        BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, cMacEntityDTO.getResourceEnd());
        BigInteger surplusQty = resourceEndNum.subtract(resourceStartNum);
        // 资源可用数量
        long availableQty = Long.parseLong(surplusQty.toString());
        tempApplyQty = this.calApplyQtyByToCusMACStartWithEnding(stepL, availableQty, tempApplyQty, dto, num);
        String resourceEnd = this.incrementMacAddress(dto.getResourceStart(), cMacEntityDTO.getSeparator(), dto.getResourceQty() - 1);
        dto.setResourceEnd(resourceEnd);
        return tempApplyQty;
    }

    private long calApplyQtyByToCusMACStartWithEnding(long stepL, long availableQty, long tempApplyQty, ResourceForApplicationDTO dto, int tempNum) {
        if (stepL == 1) {
            if (availableQty >= tempApplyQty) {
                dto.setResourceQty(tempApplyQty);
                dto.setConsumptionQty(tempApplyQty + tempNum);
                tempApplyQty = 0;
            } else {
                dto.setResourceQty(availableQty);
                dto.setConsumptionQty(availableQty + tempNum);
                tempApplyQty -= availableQty;
            }
        } else {
            // 向下取整
            long tempQty = availableQty / stepL;
            if (tempQty >= tempApplyQty) {
                dto.setResourceQty(tempApplyQty * stepL);
                // 实际消耗数量：特殊结尾，会有部分资源号不可用，这部分直接弃用，算入实际消耗数量
                dto.setConsumptionQty(tempApplyQty * stepL + tempNum);
                tempApplyQty = 0;
            } else {
                dto.setResourceQty(tempQty * stepL);
                dto.setConsumptionQty(tempQty * stepL + tempNum);
                tempApplyQty -= tempQty;
            }
        }
        return tempApplyQty;
    }

    /**
     * 校验Mac资源数量
     *
     */
    private void validApplyQtyToMACStart(SpSpecialityParam spSpecialityParam, List<ResourceForApplicationDTO> applicationDTOList) {
        List<String> resourceNoList = new ArrayList<>();
        long tempApplyQty = spSpecialityParam.getApplyQty();
        Map<String, Object> macQueryMap = new HashMap<>();
        macQueryMap.put("resourceType", Constant.ResourceType.MAC);
        macQueryMap.put("usageScope", spSpecialityParam.getUsageScope());
        macQueryMap.put("productBigClass", spSpecialityParam.getProductBigClass());
        macQueryMap.put("productSmallClass", spSpecialityParam.getProductSmallClass());
        do {
            ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
            dto.setResourceType(Constant.ResourceType.MAC);
            macQueryMap.put("resourceNoList", resourceNoList);
            ResourceApplicationEntityDTO macEntityDTO = resourceApplicationRepository.getResourceInfoByCondition(macQueryMap);
            if (null == macEntityDTO) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_MAC_AVAILABLE_RESOURCE);
            }
            dto.setResourceId(macEntityDTO.getResourceId());
            resourceNoList.add(macEntityDTO.getResourceNo());
            long stepL = Long.parseLong(spSpecialityParam.getStep());
            if (stepL < Constant.LONG_1) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
            }
            // 资源无申请记录
            if (StringUtils.isBlank(macEntityDTO.getResourceApplyEnd())) {
                // 资源可用数量
                long availableQty = macEntityDTO.getAvailableQuantity();
                dto.setResourceStart(macEntityDTO.getResourceStart());
                tempApplyQty = this.calApplyQtyByNoApplicationToMACStart(stepL, availableQty, tempApplyQty, dto);
                String resourceEnd = this.incrementMacAddress(dto.getResourceStart(), Constant.HORIZON, dto.getResourceQty() - 1);
                dto.setResourceEnd(resourceEnd);
            } else { // 资源有申请记录
                BigInteger resourceStartNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, macEntityDTO.getResourceApplyEnd());
                BigInteger resourceEndNum = ResourceInfoServiceImpl.convertDecimal(Constant.ResourceType.MAC, macEntityDTO.getResourceEnd());
                BigInteger surplusNum = resourceEndNum.subtract(resourceStartNum);
                // 资源可用数量
                long availableQty = Long.parseLong(surplusNum.toString());
                tempApplyQty = this.calApplyQtyByHaveApplicationToMACStart(availableQty, tempApplyQty, dto, stepL);
                // 申请资源号开始
                String startNum = this.incrementMacAddress(macEntityDTO.getResourceApplyEnd(), "-", 1);
                dto.setResourceStart(startNum);
                String resourceEnd = this.incrementMacAddress(startNum, "-", dto.getResourceQty() - 1);
                dto.setResourceEnd(resourceEnd);
            }
            if (tempApplyQty <= 0) {
                dto.setIsLastNo(Constant.FLAG_Y);
            } else {
                dto.setIsLastNo(Constant.FLAG_N);
            }
            applicationDTOList.add(dto);
        } while (tempApplyQty > 0);
    }


    /**
     * 资源已有申请记录，计算资源可用数量是否满足申请数量
     *
     */
    private long calApplyQtyByHaveApplicationToMACStart(long availableQty, long applyQty, ResourceForApplicationDTO dto, long stepL) {
        if (stepL == 1) {
            if (availableQty >= applyQty) {
                dto.setResourceQty(applyQty);
                applyQty = 0;
            } else {
                dto.setResourceQty(availableQty);
                dto.setConsumptionQty(availableQty);
                applyQty -= availableQty;
            }
        } else {
            // 向下取整
            long tempQty = availableQty / stepL;
            if (tempQty >= applyQty) {
                dto.setResourceQty(applyQty * stepL);
                applyQty = 0;
            } else {
                dto.setResourceQty(tempQty * stepL);
                applyQty -= tempQty;
            }
        }
        return applyQty;
    }

    /**
     * 资源无申请记录，计算资源可用数量是否满足申请数量，
     *
     */
    private long calApplyQtyByNoApplicationToMACStart(long stepL, long availableQty, long tempApplyQty, ResourceForApplicationDTO dto) {
        // 模板不包含MacEnd参数，步距即为1
        if (stepL == 1) {
            if (availableQty >= tempApplyQty) {
                dto.setResourceQty(tempApplyQty);
                tempApplyQty = 0;
            } else {
                dto.setResourceQty(availableQty);
                tempApplyQty -= availableQty;
            }
        } else {
            // 向下取整
            long tempQty = availableQty / stepL;
            if (tempQty >= tempApplyQty) {
                dto.setResourceQty(tempApplyQty * stepL);
                tempApplyQty = 0;
            } else {
                dto.setResourceQty(tempQty * stepL);
                tempApplyQty -= tempQty;
            }
        }
        return tempApplyQty;
    }

    /**
     * 十进制递增
     *
     */
    private String decimalIncrement(String paramType, String value, long plus) {
        String substring = "";
        String numStr = "";
        switch (paramType) {
            case Constant.DataBaseParams.SN:
                if (value.length() == Constant.INT_15) {
                    substring = value.substring(0, Constant.INT_5);
                    numStr = value.substring(Constant.INT_5);
                } else {
                    substring = value.substring(0, Constant.INT_17);
                    numStr = value.substring(Constant.INT_17);
                }
                break;
            case Constant.DataBaseParams.CMEI:
            case Constant.DataBaseParams.CUEI:
            case Constant.DataBaseParams.CTEI:
            case Constant.DataBaseParams.IMEI:
            case Constant.DataBaseParams.STB_SN:
                substring = value.substring(0, Constant.INT_7);
                numStr = value.substring(Constant.INT_7);
                break;
            case Constant.DataBaseParams.SO_NET_SN:
                substring = value.substring(0, Constant.INT_6);
                numStr = value.substring(Constant.INT_6);
                break;
            default:
                break;
        }
        long num = Long.parseLong(numStr) + plus;
        String strNum = String.format("%08d", num);
        if (StringUtils.equals(Constant.DataBaseParams.SO_NET_SN, paramType)) {
            strNum = String.format("%06d", num);
        } else if (StringUtils.equals(Constant.DataBaseParams.SN, paramType) && value.length() == Constant.INT_15) {
            strNum = String.format("%10d", num);
        }
        return substring + strNum;
    }


    /**
     * 递增GPON-SN
     *
     */
    public String incrementGponSn(String gponSn, BigInteger plus) {
        // 提取16进制部分
        String hexPart = gponSn.substring(Constant.INT_4);
        // 将16进制字符串转换为BigInteger
        BigInteger hexNumber = new BigInteger(hexPart, Constant.INT_16);
        // 累加1
        BigInteger incrementedHexNumber = hexNumber.add(plus);
        // 将累加后的BigInteger转换回16进制字符串，并确保长度至少为8位（必要时前面补0）
        String incrementedHexStr = String.format("%08X", incrementedHexNumber).toUpperCase();
        // 重新组合GPON-SN
        return Constant.GPON_SN_PREFIX + incrementedHexStr;
    }

    /**
     * 递增MAC
     *
     */
    public String incrementMacAddress(String mac, String separator, long plus) {
        mac = mac.replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        long macLong = Long.parseLong(mac, Constant.INT_16);
        macLong += plus;
        // 递增MAC地址的数值
        String tempMac = String.format("%012X", macLong).toUpperCase();
        // 将长整型转换回MAC地址字符串格式
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < tempMac.length(); i += Constant.INT_2) {
            str.append(tempMac, i, i + Constant.INT_2);
            if (i < tempMac.length() - Constant.INT_2) {
                str.append(separator);
            }
        }
        return str.toString();
    }
}
