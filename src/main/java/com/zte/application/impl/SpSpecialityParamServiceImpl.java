package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TtlRunnable;
import com.zte.application.*;
import com.zte.common.CommonUtils;
import com.zte.common.enums.ResourceStatusEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.common.utils.constant.RedisKeyConstant;
import com.zte.domain.model.DsnCUCCParams;
import com.zte.domain.model.ResourceApplicationRepository;
import com.zte.domain.model.ResourceDetail;
import com.zte.domain.model.ResourceDetailRepository;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.SpSpecialityParam;
import com.zte.domain.model.SpSpecialityParamItem;
import com.zte.domain.model.SpSpecialityParamRepository;
import com.zte.infrastructure.feign.DatawbFeignService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.GenerationMethod.STBID_MANUAL;
import static com.zte.common.utils.StringConstant.FORMATTER_YYYYMMDD;

/**
 * 个参生成表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-15 17:12:33
 */
@Slf4j
@Service("spSpecialityParamService")
public class SpSpecialityParamServiceImpl implements SpSpecialityParamService {

    @Autowired
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Autowired
    private SpSpecialityParamItemService spSpecialityParamItemService;
    @Autowired
    private ResourceApplicationService resourceApplicationService;
    @Autowired
    private SpTemplateService spTemplateService;
    @Autowired
    private ResourceInfoService resourceInfoService;
    @Autowired
    private ResourceDetailService resourceDetailService;
    @Autowired
    private DatawbFeignService datawbFeignService;
    @Autowired
    private BsItemInfoService bsItemInfoService;
    @Autowired
    private ResourceInfoServiceImpl resourceInfoServiceImpl;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ResourceInfoRepository resourceInfoRepository;
    @Autowired
    private ResourceDetailRepository resourceDetailRepository;
    @Autowired
    private ResourceApplicationRepository resourceApplicationRepository;
    @Autowired
    private ResourceInfoDetailRepository infoDetailRepository;
    @Value("${apply.qty.max:60000}")
    private Integer applyQtyMax;

    @Override
    public PageRows<SpSpecialityParam> queryPage(SpSpecialityParamPageQueryDTO query) {
        PageRows<SpSpecialityParam> pageRows = new PageRows<>();
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(spSpecialityParamRepository.countPage(query));
        if (pageRows.getTotal() > NumConstant.LONG_ZERO) {
            pageRows.setRows(spSpecialityParamRepository.queryPage(query));
        }
        return pageRows;
    }


    @Override
    public Long countByApplyTask(String applyTask) {
        SpSpecialityParamPageQueryDTO query = new SpSpecialityParamPageQueryDTO();
        query.setApplyTask(applyTask);
        return spSpecialityParamRepository.countPage(query);
    }

    @Override
    @TransmittableHeader
    @RecordLogAnnotation("个参生成")
    public void generate(SpSpecialityParam spSpecialityParam, boolean isJob) throws Exception {
        // 加锁 任务号，防止重复点击
        RedisLock lock = new RedisLock(String.format(RedisKeyConstant.SPECIALITY_PARAM,
                spSpecialityParam.getApplyTask()));
        boolean isLock = lock.lock();
        if (!isLock) {
            return;
        }
        try {
            // 校验入参信息
            this.checkParamsInfo(spSpecialityParam, isJob);
            SpTemplateDTO query = new SpTemplateDTO();
            query.setTemplateId(spSpecialityParam.getTemplateId());
            // 获取模板参数明细
            List<SpTemplateDTO> templateDetails = spTemplateService.getDetail(query);
            if (CollectionUtils.isEmpty(templateDetails) || CollectionUtils.isEmpty(templateDetails.get(NumConstant.NUM_ZERO).getItemList())) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_NOT_EXIST, new String[]{spSpecialityParam.getTemplateId()});
            }
            // 是否继续生成
            this.isContinueGen(spSpecialityParam, isJob, templateDetails.get(NumConstant.NUM_ZERO).getItemList());
            // 校验参数WholeDeviceCode特征值数量是否足够
            this.checkWholeDeviceCodeEigenValue(spSpecialityParam, templateDetails.get(NumConstant.NUM_ZERO).getItemList());
            if (!isJob) {
                // 写个参生成头表
                spSpecialityParam.setSpecialityParamId(generateSpecialtyId());
                spSpecialityParam.setProgress(0);
                spSpecialityParam.setItemNum(0L);
                spSpecialityParam.setTemplateItem(templateDetails.get(NumConstant.NUM_ZERO).getItemList().stream().map(SpTemplateItemDTO::getParamName).collect(Collectors.joining(Constant.COMMA)));
                spSpecialityParamRepository.insert(spSpecialityParam);
            }

            ThreadUtil.EXECUTOR.execute(Objects.requireNonNull(TtlRunnable.get(() -> {
                // 加锁 资源归属+产品大类+产品小类
                RedisLock genLock = new RedisLock(String.format(RedisKeyConstant.SPECIALITY_PARAM,
                        spSpecialityParam.getUsageScope() + spSpecialityParam.getProductBigClass() + spSpecialityParam.getProductSmallClass()),NumConstant.NUM_600);
                boolean isGenLock = genLock.lock();
                if (!isGenLock) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.TASK_IS_GENERATING);
                }
                try {
                    this.asyncGenerateInfo(spSpecialityParam, isJob, templateDetails);
                } catch (Exception e) {
                    this.deleteParamById(spSpecialityParam);
                    e.printStackTrace();
                    log.error("SP generate Item error :{}", e);
                    String msg = e.getMessage();
                    if (e instanceof MesBusinessException) {
                        MesBusinessException me = (MesBusinessException) e;
                        Object[] params = me.getParams();
                        msg = CommonUtils.getLmbMessage(me.getExMsgId(), new String[]{Arrays.toString(params)});
                    }
                    redisTemplate.opsForValue().set(String.format(RedisKeyConstant.SPECIALITY_PARAM_GENERATE, spSpecialityParam.getSpecialityParamId()), msg, 120L, TimeUnit.SECONDS);
                } finally {
                    genLock.unlock();
                }
            })));
        } finally {
            lock.unlock();
        }
    }

    private void checkWholeDeviceCodeEigenValue(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList) {
        String eigenValue = "";
        for (SpTemplateItemDTO spTemplateItemDTO : itemList) {
            if (StringUtils.equals(spTemplateItemDTO.getParamType(), Constant.ParamType.WHOLE_DEVICE_CODE)) {
                eigenValue = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.WHOLE_DEVICE_CODE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                break;
            }
        }
        if (StringUtils.isBlank(eigenValue)) {
            return;
        }
        Long num = spSpecialityParamRepository.getEigenValueIndex(eigenValue);
        num = num == null ? NumConstant.LONG_ZERO : num;
        if (num + spSpecialityParam.getApplyQty() >= NumConstant.LONG_1000000000) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EIGEN_VALUE_EXCEED_MAX, new Object[]{eigenValue, num});
        }
    }

    private void checkParamsInfo(SpSpecialityParam spSpecialityParam, boolean isJob) {
        if (spSpecialityParam.getApplyQty() > applyQtyMax) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.APPLY_QTY_IS_EXCEED_MAX, new Object[]{applyQtyMax});
        }
        // 检查任务号之前是否已经生成了
        if (!isJob && countByApplyTask(spSpecialityParam.getApplyTask()) > NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_APPLYTASK_GENERATE, new String[]{spSpecialityParam.getApplyTask()});
        }
        // 工号是否有任务正在生成中
        if (!isJob && this.countUndoneByCreator(spSpecialityParam.getCreateBy()) > NumConstant.NUM_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMP_NO_HAS_UNDONE_TASK);
        }
        // 校验物料代码是否有效
        boolean itemNoIsValid = bsItemInfoService.checkItemNoIsValid(spSpecialityParam.getItemCode());
        if (!itemNoIsValid) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_NO_IS_INVALID, new String[]{spSpecialityParam.getItemCode()});
        }
    }

    private void asyncGenerateInfo(SpSpecialityParam spSpecialityParam, boolean isJob, List<SpTemplateDTO> templateDetails) throws Exception {
        if (isJob) {
            this.paramsGenerate(spSpecialityParam, templateDetails);
            return;
        }
        // 生成申请单信息
        SpSpecialityParamService service = (SpSpecialityParamService) SpringContextUtil.getBean("spSpecialityParamService");
        service.generateApplicationInfo(spSpecialityParam, templateDetails.get(NumConstant.NUM_ZERO).getItemList());
        this.paramsGenerate(spSpecialityParam, templateDetails);
    }

    private void deleteParamById(SpSpecialityParam spSpecialityParam) {
        Integer num = resourceApplicationRepository.isHaveApplicationInfo(spSpecialityParam.getApplyTask());
        if (null == num) {
            SpSpecialityParam param = new SpSpecialityParam();
            param.setSpecialityParamId(spSpecialityParam.getSpecialityParamId());
            param.setEnabledFlag(Constant.FLAG_N);
            spSpecialityParamRepository.updateById(param);
        }
    }

    /**
     *  继续生成
     *
     */
    private void isContinueGen(SpSpecialityParam spSpecialityParam, boolean isJob, List<SpTemplateItemDTO> itemList) throws Exception {
        if (!isJob) {
            return;
        }
        // 获取申请明细
        List<ResourceApplicationEntityDTO> applyingList = resourceApplicationService.getApplyingList(spSpecialityParam.getApplyTask());
        if (CollectionUtils.isNotEmpty(applyingList)) {
            for (ResourceApplicationEntityDTO applicationEntityDTO : applyingList) {
                resourceApplicationService.continueGenerateApplicationDetailInfo(applicationEntityDTO, itemList);
            }
        }
    }

    @Override
    public void paramsGenerate(SpSpecialityParam spSpecialityParam, List<SpTemplateDTO> templateDetails) throws Exception {
        List<ResourceApplicationEntityDTO> applyList = resourceApplicationService.getList(spSpecialityParam.getApplyTask());
        if (CollectionUtils.isEmpty(applyList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_RESOURCE_ABNORMALITY);
        }
        // this.validateGenerate(spSpecialityParam, templateDetails, applyList);
        // 生成个参数据
        this.generateItem(spSpecialityParam, templateDetails.get(NumConstant.NUM_ZERO).getItemList(), applyList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGenerateData(List<SpSpecialityParamItem> spSpecialityParamItems, SpSpecialityParam updateParam,
                                 List<ResourceApplicationEntityDTO> applyList, HashMap<String, List<ResourceDetail>> resources) {
        // 写个参详情
        spSpecialityParamItemService.addBatch(spSpecialityParamItems);
        // 更新个参生成表
        spSpecialityParamRepository.updateById(updateParam);
        if (StringUtils.isNotBlank(updateParam.getWdcEigenValue())) {
            // WholeDeviceCode特征值首次使用，则新增
            if (updateParam.isFirstEigenValue()) {
                spSpecialityParamRepository.insertEigenValue(updateParam);
                updateParam.setFirstEigenValue(false);
            } else {
                // 更新WholeDeviceCode信息表
                spSpecialityParamRepository.updateEigenValueIndex(updateParam);
            }
        }

        for (ResourceApplicationEntityDTO apply : applyList) {
            List<ResourceDetail> resourceDetails = resources.get(apply.getResourceType());
            ResourceDetail resourceDetail = new ResourceDetail();
            resourceDetail.setApplyId(apply.getApplyId());
            resourceDetail.setResourceStart(resourceDetails.get(NumConstant.NUM_ZERO).getResourceStart());
            resourceDetail.setResourceSn(resourceDetails.get(resourceDetails.size() - NumConstant.NUM_ONE).getResourceSn());
            resourceDetail.setUpdateUser(updateParam.getLastUpdatedBy());
            resourceDetailService.updateBatchOfStatus(resourceDetail);
            if (StringUtils.equals(apply.getResourceType(), ResourceType.NETWORK_ACCESS)) {
                this.netWorkAccressOpts(updateParam, resourceDetails);
            }

            if (updateParam.getProgress().equals(NumConstant.NUM_100)) {
                apply.setResourceStatus(ResourceStatusEnum.USED.name());
                apply.setUpdateUser(updateParam.getLastUpdatedBy());
                resourceApplicationService.update(apply);
            }
        }
    }

    private void netWorkAccressOpts(SpSpecialityParam updateParam, List<ResourceDetail> resourceDetails) {
        List<String> numList = resourceDetails.stream().map(ResourceDetail::getResourceStart).collect(Collectors.toList());
        List<ResourceInfoDetailDTO> detailDTOList = new ArrayList<>();
        for (String num : numList) {
            ResourceInfoDetailDTO detailDTO = new ResourceInfoDetailDTO();
            detailDTO.setResourceNo(updateParam.getConsent());
            detailDTO.setResourceNum(num);
            detailDTOList.add(detailDTO);
        }
        ResourceInfoEntityDTO entityDTO = new ResourceInfoEntityDTO();
        entityDTO.setResourceType(ResourceType.NETWORK_ACCESS);
        // 写操作日志
        resourceInfoServiceImpl.batchInsertResourceOptLog(updateParam.getCreateBy(), entityDTO, detailDTOList, NumConstant.STRING_TWO);
        // 更新资源状态为已绑定
        for (List<String> splitList : CommonUtils.splitList(numList, Constant.BATCH_SIZE)) {
            infoDetailRepository.updateStatusByResourceNum(splitList, NumConstant.STRING_TWO, updateParam.getCreateBy());
        }
    }

    /**
     * 生成个参item数据
     *
     */
    @Override
    @RecordLogAnnotation("个参数据生成")
    public void generateItem(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList, List<ResourceApplicationEntityDTO> applyList) throws Exception {
        // 1、查询申请详情未使用数据的总数
        Long resourceTotal = this.getUnusedTotal(applyList);
        SpSpecialityParam params = spSpecialityParamRepository.selectIdByTask(spSpecialityParam.getApplyTask());
        if (params.getProgress() == NumConstant.NUM_100) {
            return;
        }
        spSpecialityParam.setSpecialityParamId(params.getSpecialityParamId());
        spSpecialityParam.setProgress(params.getProgress());

        if (resourceTotal == NumConstant.NUM_ZERO && spSpecialityParam.getProgress() != NumConstant.NUM_100) {
            // 未使用数未0，则认为生成未完成，更新状态
            SpSpecialityParam updateParam = new SpSpecialityParam();
            updateParam.setSpecialityParamId(spSpecialityParam.getSpecialityParamId());
            updateParam.setProgress(NumConstant.NUM_100);
            updateParam.setLastUpdatedDate(new Date());
            // 更新个参生成头表
            spSpecialityParamRepository.updateById(updateParam);
            return;
        }
        SpSpecialityParam updateParam = new SpSpecialityParam();
        updateParam.setCreateBy(spSpecialityParam.getCreateBy());
        // WholeDeviceCode特征值
        String eigenValue = this.getEigenValue(spSpecialityParam, itemList, updateParam);
        // 入网参数信息
        this.getNetWorkAccessInfo(spSpecialityParam, itemList, updateParam);
        this.getStbidPrefix(spSpecialityParam, itemList);

        // 2、循环生成数据
        int pageSize = NumConstant.NUM_500;
        long totalPages = BigDecimal.valueOf(resourceTotal).divide(BigDecimal.valueOf(pageSize), RoundingMode.UP).longValue();
        // long itemNum = spSpecialityParam.getItemNum();
        SpSpecialityParamService service = (SpSpecialityParamService) SpringContextUtil.getBean("spSpecialityParamService");
//        this.getNASNInfo(spSpecialityParam, consent, totalPages, resourceTotal);
        for (int page = NumConstant.NUM_ONE; page <= totalPages; page++) {
            HashMap<String, List<ResourceDetail>> resources = new HashMap<>();
            this.getResourceDetail(applyList, pageSize, resources);
            List<Long> numList = new ArrayList<>();
            numList.add(resourceTotal);
            numList.add((long) pageSize * (page - NumConstant.NUM_ONE));
            List<SpSpecialityParamItem> spSpecialityParamItems = this.generateSpItem(spSpecialityParam, itemList, applyList, resources, numList);

            updateParam.setSpecialityParamId(spSpecialityParam.getSpecialityParamId());
            updateParam.setProgress((int) (page * NumConstant.NUM_100 / totalPages));
            updateParam.setLastUpdatedDate(new Date());
            updateParam.setLastUpdatedBy(spSpecialityParam.getLastUpdatedBy());
            // itemNum += spSpecialityParamItems.size();
            updateParam.setItemNum((long) spSpecialityParamItems.size());
            if (StringUtils.isNotBlank(eigenValue)) {
                updateParam.setWdcEigenValue(eigenValue);
                updateParam.setWdcTotal((long) spSpecialityParamItems.size());
                spSpecialityParam.setWdcTotal(spSpecialityParam.getWdcTotal() + spSpecialityParamItems.size());
            }
            service.saveGenerateData(spSpecialityParamItems, updateParam, applyList, resources);
        }
    }

    private Long getUnusedTotal(List<ResourceApplicationEntityDTO> applyList) throws Exception {
        ResourceDetailDTO countDTO = new ResourceDetailDTO();
        Map<String, List<ResourceApplicationEntityDTO>> listMap = applyList.stream().collect(Collectors.groupingBy(ResourceApplicationEntityDTO::getResourceType));
        Map.Entry<String, List<ResourceApplicationEntityDTO>> entry = listMap.entrySet().iterator().next();
        List<ResourceApplicationEntityDTO> tempList = entry.getValue();
        List<String> applyIdList = tempList.stream().map(ResourceApplicationEntityDTO::getApplyId).collect(Collectors.toList());
        countDTO.setApplyIdList(applyIdList);
        countDTO.setNotResourceStatus(ResourceStatusEnum.USED.name());
        return resourceDetailService.getResourceDetailCount(countDTO);
    }

    private void getNetWorkAccessInfo(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList, SpSpecialityParam updateParam) {
        for (SpTemplateItemDTO spTemplateItemDTO : itemList) {
            if (StringUtils.equals(spTemplateItemDTO.getParamType(), Constant.ParamType.NASN)) {
                String consent = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.NASN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                spSpecialityParam.setNasnParamName(spTemplateItemDTO.getParamName());
                updateParam.setConsent(consent);
            } else if (StringUtils.equals(spTemplateItemDTO.getParamType(), Constant.ParamType.SCRAMBLING_CODE)) {
                spSpecialityParam.setScrambleCodeParamName(spTemplateItemDTO.getParamName());
            } else if (StringUtils.equals(spTemplateItemDTO.getParamType(), Constant.ParamType.NACC)) {
                spSpecialityParam.setNaccParamName(spTemplateItemDTO.getParamName());
            }
        }
    }

    private void getStbidPrefix(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList) {
        String stbidPrefix = StringUtils.EMPTY;
        String checkAa = StringUtils.EMPTY;
        List<SpTemplateItemDTO> stbidDtos = itemList.stream()
                .filter(p -> StringUtils.equals(p.getParamType(), Constant.ParamType.STBID))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stbidDtos)) {
            return;
        }
        if (stbidDtos.size() > 1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_NUM_OVERONE);
        }
        if (StringUtils.equals(stbidDtos.get(0).getGenerationMethod(), STBID_MANUAL)) {
            String[] variables = getVariables(stbidDtos);
            stbidPrefix = processString(variables[1], spSpecialityParam.getProductionUnit());
            checkAa = variables[2];
        } else {
            // todo:自动化平台接口异常，要拦截提示
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_NOAUTO);
        }
        if (StringUtils.isEmpty(stbidPrefix)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_NOSTBIDPREFIX);
        }
        spSpecialityParam.setStbidPrefix(stbidPrefix);
        spSpecialityParam.setStbidCheckAA(checkAa);
    }

    public static String processString(String input, String factoryName) {
        String[] parts = input.split(DELIMITER_SHORTBAR, -1);
        if (parts.length != STBIDPREFIX_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDCFG_ERROR);
        }
        String cPart = parts[1];
        if (StringUtils.length(cPart) != STBIDPREFIX_CPART_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDCFG_ERROR);
        }
        if (StringUtils.equals(StringUtils.substring(cPart, STBIDPREFIX_C3START_INDEX, STBIDPREFIX_C3END_INDEX), DELIMITER_STAR)) {
            cPart = cPart.replace(DELIMITER_STAR, factoryName);
        }
        parts[1] = cPart;
        String fPart = parts[4];
        if (StringUtils.equals(fPart, SPECIAL_FFFF)) {
            fPart = DateTimeFormatter.ofPattern("yyMM").format(LocalDate.now());
        }
        parts[4] = fPart;
        return String.join(StringUtils.EMPTY, parts);
    }

    @NotNull
    private static String[] getVariables(List<SpTemplateItemDTO> stbidDtos) {
        String paramRule = stbidDtos.get(0).getParamRule();
        String tempStr = paramRule.replace(SpecialityParamConstant.STBID_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] variables = tempStr.split(Constant.COMMA);
        if (variables.length != STBID_CFG_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.STBIDTYPE_STBIDTEMPLATE_ERROR);
        }
        return variables;
    }

    private void getResourceDetail(List<ResourceApplicationEntityDTO> applyList,
                                   int pageSize, HashMap<String, List<ResourceDetail>> resources) throws Exception {
        for (ResourceApplicationEntityDTO entityDTO : applyList) {
            // 查询资源
            ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
            resourceDetailDTO.setApplyId(entityDTO.getApplyId());
            resourceDetailDTO.setNotResourceStatus(ResourceStatusEnum.USED.name());
            resourceDetailDTO.setPage(NumConstant.NUM_ONE);
            resourceDetailDTO.setRows(pageSize);
            // 跨资源号段情况下，拼接Detail List
            if (resources.containsKey(entityDTO.getResourceType())) {
                List<ResourceDetail> list = resources.get(entityDTO.getResourceType());
                if (list.size() >= pageSize) {
                    continue;
                }
                resourceDetailDTO.setRows(pageSize - list.size());
                list.addAll(resourceDetailService.getPageList(resourceDetailDTO));
                resources.put(entityDTO.getResourceType(), list);
            } else {
                resources.put(entityDTO.getResourceType(), resourceDetailService.getPageList(resourceDetailDTO));
            }
        }
    }

    /**
     * 模板若包含WholeDeviceCode参数,获取特征值及累计值
     *
     */
    private String getEigenValue(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemDTOList, SpSpecialityParam updateParam) {
        String eigenValue = "";
        for (SpTemplateItemDTO spTemplateItemDTO : itemDTOList) {
            if (StringUtils.equals(spTemplateItemDTO.getParamType(), Constant.ParamType.WHOLE_DEVICE_CODE)) {
                eigenValue = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.WHOLE_DEVICE_CODE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            }
        }
        if (StringUtils.isNotBlank(eigenValue)) {
            Long index = spSpecialityParamRepository.getEigenValueIndex(eigenValue);
            if (index == null) {
                index = 0L;
                updateParam.setFirstEigenValue(true);
            }
            spSpecialityParam.setWdcEigenValue(eigenValue);
            spSpecialityParam.setWdcTotal(index);
        }
        return eigenValue;
    }

    /**
     * 生成申请单信息
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateApplicationInfo(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList) throws Exception {
        this.setSpParam(spSpecialityParam, itemList);
        List<ResourceForApplicationDTO> list = new ArrayList<>();
        for (SpTemplateItemDTO itemDTO : itemList) {
            // 获取资源池中资源编号等信息，校验资源编号数量是否满足
            resourceApplicationService.validApplyQty(itemDTO, spSpecialityParam, list);
        }

        List<ResourceApplicationEntityDTO> applicationList = new ArrayList<>();
        for (ResourceForApplicationDTO dto : list) {
            this.setApplicationInfo(spSpecialityParam, dto, applicationList);
        }
        this.saveApplicationInfo(applicationList);
    }

    private void setSpParam(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList) {
        // 分隔符
        String separator = "";
        // 步距  模板不包含MacEnd参数，步距即为1
        String step = Constant.STR_ONE;
        spSpecialityParam.setStep(step);
        // GPON-SN产商编码
        String mCode = "";
        for (SpTemplateItemDTO itemDTO : itemList) {
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_START)) {
                String tempStr = itemDTO.getParamRule().replace(Constant.GenerationMethod.MAC_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                if (tempStr.contains(Constant.STR_SPLIT)) {
                    separator = tempStr.substring(0, tempStr.indexOf(','));
                } else {
                    separator = tempStr;
                }
                spSpecialityParam.setSeparator(separator);
            }
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.MAC_END)) {
                step = itemDTO.getParamRule().replace(Constant.GenerationMethod.MAC_END, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                spSpecialityParam.setStep(step);
            }
            if (StringUtils.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.GPON_SN)) {
                mCode = itemDTO.getParamRule().replace(SpecialityParamConstant.GPON_SN_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                spSpecialityParam.setMCode(mCode);
            }
        }
    }

    private void saveApplicationInfo(List<ResourceApplicationEntityDTO> applicationList) throws Exception {
        // 全部资源写申请头表
        resourceApplicationRepository.batchInsert(applicationList);
        // 保存申请明细，并更新资源池信息
        for (ResourceApplicationEntityDTO entityDTO : applicationList) {
            resourceApplicationService.saveApplicationInfo(entityDTO);
        }
    }

    private void setApplicationInfo(SpSpecialityParam spSpecialityParam, ResourceForApplicationDTO dto, List<ResourceApplicationEntityDTO> applicationList) {
        ResourceApplicationEntityDTO record = new ResourceApplicationEntityDTO();
        record.setApplyId(UUID.randomUUID().toString());
        record.setResourceId(dto.getResourceId());
        record.setResourceNo(dto.getResourceNo());
        record.setUserType(Constant.GENERAL_USER);
        record.setProductBigClass(spSpecialityParam.getProductBigClass());
        record.setApplyTask(spSpecialityParam.getApplyTask());
        record.setResourceType(dto.getResourceType());
        record.setResourceStatus(ResourceStatusEnum.APPLYING.name());
        record.setResourceStart(dto.getResourceStart());
        record.setResourceEnd(dto.getResourceEnd());
        record.setIsLastNo(dto.getIsLastNo());
        if (StringUtils.equals(Constant.ResourceType.MAC, dto.getResourceType())) {
            record.setStandardQty(new BigDecimal(spSpecialityParam.getStep()));
            record.setResourceStep(Integer.valueOf(spSpecialityParam.getStep()));
        } else {
            record.setStandardQty(new BigDecimal(1));
            record.setResourceStep(1);
        }
        long applyQty = dto.getResourceQty() / record.getResourceStep();
        record.setApplyQty(new BigDecimal(applyQty));
        // BigDecimal applyTotalQty = record.getApplyQty().multiply(record.getStandardQty());
        record.setApplyAmount(new BigDecimal(dto.getResourceQty()));
        record.setConsumptionQty(dto.getConsumptionQty());
        record.setCreateUser(spSpecialityParam.getCreateBy());
        record.setUpdateUser(spSpecialityParam.getLastUpdatedBy());
        record.setSeparator(spSpecialityParam.getSeparator());
        record.setMCode(spSpecialityParam.getMCode());
        applicationList.add(record);
        dto.setApplyId(record.getApplyId());
    }


    /**
     * 生成个参item数据
     * @param spSpecialityParam 生成配置
     * @param itemList 模板列表
     * @param applyList 申请资源列表
     * @param resources 资源列表\
     * @return
     */
    private List<SpSpecialityParamItem> generateSpItem(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList,
                                                       List<ResourceApplicationEntityDTO> applyList, HashMap<String, List<ResourceDetail>> resources,
                                                       List<Long> numList) {
        List<SpTemplateItemDTO> telmexGponList = itemList.stream()
                .filter(item -> StringUtils.equals(item.getGenerationMethod(), Constant.GenerationMethod.TELMEX_GPON_SN))
                .collect(Collectors.toList());
        LinkedHashMap<String,Long> telmexEigenValMap = new LinkedHashMap<>();
        LinkedHashMap<String,Long> telmexOldEigenValMap = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(telmexGponList)){
            this.getTelmexEigenValMap(telmexEigenValMap, telmexOldEigenValMap, spSpecialityParam);
        }
        Long beforeRows = numList.get(NumConstant.NUM_ONE);
        // 模板参数下的特定参数值集合
        Map<String, String> paramsMap =  new HashMap<>();
        // 参数类型与参数名对应关系集合
        Map<String, String> paramTypeToNameMap =  new HashMap<>();
        this.setMapInfo(spSpecialityParam, itemList, paramTypeToNameMap, paramsMap);
        // 先生成参数List 减少循环次数
        List<SpTemplateItemDTO> preItemList = itemList.stream()
                .filter(item -> Constant.PARAM_TYPE_LIST.contains(item.getParamType()))
                .collect(Collectors.toList());
        // 后生成参数List
        List<SpTemplateItemDTO> postItemList = itemList.stream()
                .filter(item -> !Constant.PARAM_TYPE_LIST.contains(item.getParamType())
                        && !Constant.VARIABLE_PARAMS_LIST.contains(item.getGenerationMethod()))
                .collect(Collectors.toList());
        // 含“同变量”参数List
        List<SpTemplateItemDTO> variableItemList = itemList.stream()
                .filter(item -> Constant.VARIABLE_PARAMS_LIST.contains(item.getGenerationMethod()))
                .collect(Collectors.toList());
        List<SpTemplateItemDTO> stbidList = itemList.stream()
                .filter(item -> StringUtils.equals(item.getParamType(), Constant.ParamType.STBID))
                .collect(Collectors.toList());
        if (StringUtils.isNotEmpty(spSpecialityParam.getStbidPrefix()) && !CollectionUtils.isEmpty(stbidList)) {
            variableItemList.addAll(stbidList);
        }
        Map<String, String> fixedStrMap = this.getFixedStr(spSpecialityParam);
        List<SpSpecialityParamItem> spSpecialityParamItems = new ArrayList<>();
        List<ResourceDetail> resourceDetailList = resources.get(applyList.get(Constant.INT_0).getResourceType());
        paramsMap.put("haveCUCCParams", "false");
        for (int i = Constant.INT_0; i < resourceDetailList.size(); i++) {
            paramsMap.put("num", String.valueOf(i));
            // JSONObject additional = getAdditional(applyList, resources, i);
            SpSpecialityParamItem spSpecialityParamItem = new SpSpecialityParamItem();
            spSpecialityParamItem.setId(UUID.randomUUID().toString());
            spSpecialityParamItem.setSpecialityParamId(spSpecialityParam.getSpecialityParamId());
            spSpecialityParamItem.setCreateBy(spSpecialityParam.getCreateBy());
            spSpecialityParamItem.setLastUpdatedBy(spSpecialityParam.getCreateBy());
            spSpecialityParamItem.setGponStart(Constant.STRING_EMPTY);
            spSpecialityParamItem.setGponEnd(Constant.STRING_EMPTY);
            spSpecialityParamItem.setMacStart(Constant.STRING_EMPTY);
            spSpecialityParamItem.setMacEnd(Constant.STRING_EMPTY);
            spSpecialityParamItem.setIsUsed(0);
            JSONObject itemData = new JSONObject();
            // 开始生成数据
            this.preItemListGenData(resources, preItemList, itemData, paramsMap, telmexEigenValMap);
            for (SpTemplateItemDTO itemDTO : postItemList) {
                if (StringUtils.equals(Constant.ParamType.CUSTOMIZE, itemDTO.getParamType())) {
                    itemData.put(itemDTO.getParamName(), this.ruleHandle(itemDTO.getParamName(), itemDTO.getParamRule(), itemData, beforeRows + i));
                    continue;
                }
                this.getRandomType(itemDTO, itemData);
                this.genPostItemData(itemDTO, itemData, paramsMap, paramTypeToNameMap, fixedStrMap);
            }
            for (SpTemplateItemDTO itemDTO : variableItemList) {
                // 含“同变量”参数生成
                this.variableItemListGenData(itemDTO, itemData, paramsMap);
            }
            this.setCTCCSnInfo(itemData,itemList);
            spSpecialityParamItem.setItemData(itemData.toJSONString());
            String macStartStr = paramTypeToNameMap.get(Constant.GenerationMethod.MAC_START);
            spSpecialityParamItemExtracted(spSpecialityParamItem, itemData, macStartStr);
            spSpecialityParamItems.add(spSpecialityParamItem);
        }
        // 联通参数规则特殊，最后生成
        this.setCUCCData(spSpecialityParamItems, paramsMap);
        this.updateTelmexEigenValueIndex(telmexEigenValMap,telmexOldEigenValMap,spSpecialityParam);
        return spSpecialityParamItems;
    }

    private static void spSpecialityParamItemExtracted(SpSpecialityParamItem spSpecialityParamItem, JSONObject itemData, String macStartStr) {
        if (macStartStr != null) {
            Object itemDataOb = itemData.get(macStartStr);
            if (itemDataOb != null) {
                spSpecialityParamItem.setMacStart(itemDataOb.toString());
            }
        }
    }

    private void variableItemListGenData(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramsMap) {
        if (StringUtils.equals(Constant.ParamType.ASSIGNMENT, itemDTO.getParamType())) {
            this.setAssignmentParams(itemDTO, itemData);
        } else if (StringUtils.equals(Constant.ParamType.INTERVAL_VALUE, itemDTO.getParamType())){
            this.setIntervalValueParams(itemDTO, itemData);
        }
        if (StringUtils.equals(Constant.GenerationMethod.MAC_ADD, itemDTO.getGenerationMethod())) {
            this.setMacAddParam(itemDTO, itemData, paramsMap);
        }
        if (StringUtils.equals(Constant.ParamType.STBID, itemDTO.getParamType())) {
            this.setStbidParam(itemDTO, itemData, paramsMap);
        }
    }

    private void setStbidParam(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramsMap) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.STBID_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] variables = tempStr.split(Constant.COMMA);
        if (variables.length != STBID_CFG_LENGTH) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, "STBID参数模板配置错误");
        }
        String macVariableName = variables[0];
        Object mac = itemData.get(macVariableName);
        if (mac == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.VARIABLE_DOES_NOT_EXIST, new Object[]{itemDTO.getParamName(), macVariableName});
        }
        String macVariable = RegExUtils.removeAll(mac.toString(), Constant.SPECIAL_CHAR_PATTERN);
        String stbidPrefix = paramsMap.get(Constant.SPPARAM_KEY_STBID_PRRFIX);
        String stbidNoAA = stbidPrefix + macVariable;
        String stbid = getStbid(paramsMap, stbidNoAA);
        itemData.put(itemDTO.getParamName(), stbid);
    }

    private static String getStbid(Map<String, String> paramsMap, String barCode) {
        String stbidCheckAA = paramsMap.get(Constant.SPPARAM_KEY_STBID_CHECKAA);
        // 计算AA
        String strA2 = "";
        String stbID = "";
        if (StringUtils.equals(stbidCheckAA, STBID_CHECKAA_ON)) {
            int[] weight = {63, 47, 55, 79, 71, 7, 15, 23, 31, 39, 175, 183, 191, 2, 71, 79,
                    87, 95, 7, 15, 103, 111, 119, 127, 199, 207, 223, 215, 239, 254};
            int tmpWeight = 0;
            int checkSum = 0;
            int aa = 0;
            for (int i = 0; i < barCode.length(); i++) {
                tmpWeight = Integer.parseInt(barCode.substring(i, i + 1), 16) * weight[i];
                checkSum = checkSum + tmpWeight;
            }
            aa = checkSum % 256;
            aa = aa ^ ((aa & 0x07) << 5 | (aa & 0xF8) >> 3);
            strA2 = Integer.toHexString(aa).toUpperCase();
            stbID = String.format("%s%s", StringUtils.leftPad(strA2, 2, "0"), barCode);
        } else {
            stbID = String.format("00%s", barCode);
        }
        return stbID;
    }

    private void setMacAddParam(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramsMap) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.MAC_ADD_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String variableName = tempStr.substring(0, tempStr.indexOf(','));
        String step = tempStr.substring(tempStr.indexOf(',') + 1);
        Object mac = itemData.get(variableName);
        if (mac == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.VARIABLE_DOES_NOT_EXIST, new Object[]{itemDTO.getParamName(), variableName});
        }
        String macVariable = mac.toString();
        String separator = "";
        if (paramsMap.get(Constant.SEPARATOR) != null) {
            separator = paramsMap.get(Constant.SEPARATOR);
        }
        String macAdd = this.incrementMacAddress(macVariable, separator, Long.parseLong(step));
        itemData.put(itemDTO.getParamName(), macAdd);
    }

    private void setCTCCSnInfo(JSONObject itemData, List<SpTemplateItemDTO> itemList) {
        List<SpTemplateItemDTO> printItemList = itemList.stream()
                .filter(item -> Constant.ParamType.DEVICE_SERIAL_NUMBER.equals(item.getParamType()))
                .filter(item -> Constant.GenerationMethod.DSN_CTCC_SN_EMPTY.equals(item.getGenerationMethod()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(printItemList)) {
            return;
        }
        for (SpTemplateItemDTO itemDTO : printItemList) {
            // 取出打印类型的规则中的电信的参数名
            String paramName = itemDTO.getParamRule().replace(Constant.DSN_CTCC_SN, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            // 获取参数名对应的类型
            List<SpTemplateItemDTO> generationMethodList = itemList.stream()
                    .filter(item -> StringUtils.equals(paramName, item.getParamName()))
                    .filter(item -> Constant.ParamType.DEVICE_SERIAL_NUMBER.equals(item.getParamType()))
                    .filter(item -> Constant.GenerationMethod.DSN_CTCC_FTTO.equals(item.getGenerationMethod()) || Constant.GenerationMethod.DSN_CTCC.equals(item.getGenerationMethod()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(generationMethodList)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});

            }
            // 电信FTTO/电信智能 不为空
            String ctccSn = itemData.getString(paramName);
            if (StringUtils.isBlank(ctccSn)) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
            }
            String prefix = ctccSn.substring(0, ctccSn.indexOf(Constant.HORIZON) + Constant.INT_1);
            String suffix = ctccSn.substring(ctccSn.indexOf(Constant.HORIZON) + Constant.INT_1);

            // Insert spaces every 4 characters in the suffix
            StringBuilder formattedSuffix = new StringBuilder();
            for (int i = 0; i < suffix.length(); i += Constant.INT_4) {
                if (i > 0) {
                    formattedSuffix.append(" ");
                }
                formattedSuffix.append(suffix, i, Math.min(i + Constant.INT_4, suffix.length()));
            }
            itemData.put(itemDTO.getParamName(), prefix + formattedSuffix);
        }
    }

    private void updateTelmexEigenValueIndex(LinkedHashMap<String, Long> telmexEigenValMap,
                                             LinkedHashMap<String, Long> telmexOldEigenValMap, SpSpecialityParam spSpecialityParam) {
        if (MapUtils.isEmpty(telmexEigenValMap)) {
            return;
        }
        for (Map.Entry<String, Long> entry : telmexEigenValMap.entrySet()) {
            if (Objects.equals(entry.getValue(), telmexOldEigenValMap.get(entry.getKey()))) {
                continue;
            }
            spSpecialityParamRepository.updateTelmexEigenValueIndex(entry.getValue(), spSpecialityParam.getLastUpdatedBy(), entry.getKey());
        }
    }

    private void getRandomType(SpTemplateItemDTO itemDTO, JSONObject itemData) {
        if (!StringUtils.equals(Constant.ParamType.RANDOM_TYPE, itemDTO.getParamType())) {
            return;
        }
        if (StringUtils.isBlank(itemDTO.getParamRule())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }

        String result = this.getRandomData(itemDTO);
        itemData.put(itemDTO.getParamName(), result);
    }

    public String getRandomData(SpTemplateItemDTO itemDTO) {
        // 规则： Random(前缀,元素集合,特殊字符,随机位数,随机类型,后缀)
        String ruleTemp = itemDTO.getParamRule().replace(SpecialityParamConstant.RANDOM_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] split = ruleTemp.split(Constant.STR_SPLIT, Constant.INT_B1);
        if (split.length != Constant.INT_6) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        // 随机类型 0:纯随机，1：三选二，2：四选三
        String randomType = split[Constant.INT_4];
        String result = "";
        switch (randomType) {
            case Constant.STRING_ZERO:
                // 纯随机
                result = allRandom(split, itemDTO);
                break;
            case Constant.STRING_ONE:
                // 三选二
                result = minTwoTypeRandom(split, itemDTO);
                break;
            case Constant.STRING_TWO:
                // 四选三
                result = minThreeTypeRandom(split, itemDTO);
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        // 选项 0:前缀+随机,1:随机+后缀,2:前缀+随机+后缀,3:随机(随机类型)
        String generationMethod = itemDTO.getGenerationMethod();
        switch (generationMethod) {
            case Constant.GenerationMethod.RANDOM_PREFIX:
                // 0:前缀+随机
                result = split[Constant.INT_0] + result;
                break;
            case Constant.GenerationMethod.RANDOM_SUFFIX:
                // 1:随机+后缀
                result += split[Constant.INT_5];
                break;
            case Constant.GenerationMethod.RANDOM_PREFIX_SUFFIX:
                // 2:前缀+随机+后缀
                result = split[Constant.INT_0] + result +  split[Constant.INT_5];
                break;
            case Constant.GenerationMethod.RANDOM:
                // 3:随机(随机类型)
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                        MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        return result;
    }


    /**
     * 随机类型 四选三
     *
     * @param split 前缀,元素集合,特殊字符,随机位数,随机类型,后缀
     * @param itemDTO DTO
     * @return String
     */
    private String minThreeTypeRandom(String[] split, SpTemplateItemDTO itemDTO) {
        // 元素集合
        String elements = split[Constant.INT_1];
        elements = elements.replace("\u200B", "").replace(" ", "");
        if (StringUtils.isBlank(elements)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        // 元素集合
        List<String> elementsList = elements.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .collect(Collectors.toList());
        // 元素类型集合 用于分别收集数字、小写字母和大写字母 打乱顺序
        List<List<String>> elementsTypeList = new ArrayList<>();
        getElementsTypeList(elementsTypeList, elementsList);

        String specialChar = split[Constant.INT_2];
        specialChar = specialChar.replace("\u200B", "").replace(" ", "");
        // 如果输入的字符为四种，则可以选择纯随机、三选二、四选三 ---> 四选三字符类型必须为四种
        if (StringUtils.isEmpty(specialChar) || elementsTypeList.size() < Constant.INT_3) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        // 特殊字符集合
        List<String> specialCharList = specialChar.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .collect(Collectors.toList());
        Collections.shuffle(specialCharList);

        // % 后面能出现的字符集合
        List<String> appendList = new ArrayList<>();
        if (specialChar.contains(Constant.STR_PERCENT)) {
            // 如果包含 % 后面的第一位不能为0~9，a~f，A~F
            getAppendList(appendList, split, itemDTO);
        }

        // 包含所有元素的集合 0~9，a~z，A~Z +特殊字符
        List<String> allElementsList = new ArrayList<>();
        allElementsList.addAll(elementsList);
        allElementsList.addAll(specialCharList);
        // 随机位数
        int randomLength = getRandomLength(split, itemDTO);

        String temp = "";
        List<String> resultList = new ArrayList<>();
        // 四选三字符类型必须为四种 先取其中三种
        resultList.add(elementsTypeList.get(Constant.INT_0).get(Constant.INT_0));
        resultList.add(elementsTypeList.get(Constant.INT_1).get(Constant.INT_0));
        temp = specialCharList.get(Constant.INT_0);
        resultList.add(temp);
        randomLength = randomLength - Constant.INT_3;

        // 已完成最少包含3种，后面的随机取
        String resultStr = this.getResultStr(randomLength, temp, appendList, resultList, allElementsList);
        // 特殊字符不能出现在第一位 前面 randomLength 预留了一位用于拼接普通字符
        return getRandomElement(elementsList) + resultStr;
    }

    private String getResultStr(int randomLength, String temp, List<String> appendList, List<String> resultList, List<String> allElementsList) {
        for (int i = 0; i < randomLength - 1; i++) {
            // 上一个符号位 % 下一个符号接特殊字符
            if (StringUtils.equals(temp, Constant.STR_PERCENT)) {
                String randomElement = getRandomElement(appendList);
                resultList.set(resultList.size() - 1, temp + randomElement);
                temp = randomElement;
                continue;
            }
            temp = getRandomElement(allElementsList);
            resultList.add(temp);
        }
        // 集合最后一个字符串的元素是%结尾，则在集合最后一个字符串 截去第一个字符，补一个非0~9，a~f，A~F，%特殊字符
        checkAndUpdateLastChar(resultList, appendList);
        // 打乱顺序做拼接
        Collections.shuffle(resultList);
        String resultStr = String.join("", resultList);
        return resultStr;
    }

    /**
     * 随机类型 三选二
     *
     * @param split 前缀,元素集合,特殊字符,随机位数,随机类型,后缀
     * @param itemDTO DTO
     * @return String
     */
    private String minTwoTypeRandom(String[] split, SpTemplateItemDTO itemDTO) {
        // 元素集合
        String elements = split[Constant.INT_1];
        elements = elements.replace("\u200B", "").replace(" ", "");
        if (StringUtils.isBlank(elements)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        // 元素集合
        List<String> elementsList = elements.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .collect(Collectors.toList());
        // 元素类型集合 用于分别收集数字、小写字母和大写字母 打乱顺序
        List<List<String>> elementsTypeList = new ArrayList<>();
        getElementsTypeList(elementsTypeList, elementsList);

        // 特殊字符集合
        String specialChar = split[Constant.INT_2];
        specialChar = specialChar.replace("\u200B", "").replace(" ", "");
        List<String> specialCharList = new ArrayList<>();
        // % 后面能出现的字符集合
        List<String> appendList = new ArrayList<>();
        // 如果输入的字符为三种，可以选择纯随机、三选二
        if (StringUtils.isNotEmpty(specialChar)) {
            specialCharList = this.getSpecialCharList(split, itemDTO, specialChar, appendList, elementsTypeList);
        } else if (elementsTypeList.size() < Constant.INT_3) {
            // 三选二，特殊字符为空，元素集合校验：最少3种
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }

        // 包含所有元素的集合 0~9，a~z，A~Z +特殊字符
        List<String> allElementsList = new ArrayList<>();
        allElementsList.addAll(elementsList);
        allElementsList.addAll(specialCharList);
        // 随机位数
        int randomLength = getRandomLength(split, itemDTO);

        String temp = "";
        List<String> resultList = new ArrayList<>();
        // 三选二，最少2种，有特殊字符需包含特殊字符
        String str = elementsTypeList.get(Constant.INT_0).get(Constant.INT_0);
        resultList.add(str);
        randomLength--;
        if (CollectionUtils.isNotEmpty(specialCharList)) {
            temp = specialCharList.get(Constant.INT_0);
        } else {
            temp = elementsTypeList.get(Constant.INT_1).get(Constant.INT_0);
        }
        resultList.add(temp);
        randomLength--;

        // 已完成最少包含2种，后面的随机取
        String resultStr = getResultStr(randomLength, temp, appendList, resultList, allElementsList);
        // 特殊字符不能出现在第一位 前面 randomLength 预留了一位用于拼接普通字符
        return getRandomElement(elementsList) + resultStr;
    }

    private List<String> getSpecialCharList(String[] split, SpTemplateItemDTO itemDTO, String specialChar, List<String> appendList, List<List<String>> elementsTypeList) {
        List<String> specialCharList;
        specialCharList = specialChar.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .collect(Collectors.toList());
        Collections.shuffle(specialCharList);
        // 如果包含 % 后面的第一位不能为0~9，a~f，A~F
        if (specialChar.contains(Constant.STR_PERCENT)) {
            getAppendList(appendList, split, itemDTO);
        }
        // 三选二，特殊字符不为空，元素集合校验：最少2种
        if (elementsTypeList.size() < Constant.INT_2) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        return specialCharList;
    }

    /**
     * 随机类型 纯随机
     *
     * @param split 前缀,元素集合,特殊字符,随机位数,随机类型,后缀
     * @param itemDTO DTO
     * @return String
     */
    private String allRandom(String[] split, SpTemplateItemDTO itemDTO) {
        // 元素集合
        String elements = split[Constant.INT_1];
        elements = elements.replace("\u200B", "").replace(" ", "");
        if (StringUtils.isBlank(elements)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
        List<String> elementsList = elements.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .collect(Collectors.toList());

        // 特殊字符集合
        String specialChar = split[Constant.INT_2];
        specialChar = specialChar.replace("\u200B", "").replace(" ", "");
        List<String> specialCharList = new ArrayList<>();
        // % 后面能出现的字符集合
        List<String> appendList = new ArrayList<>();
        if (StringUtils.isNotEmpty(specialChar)) {
            specialCharList = specialChar.chars()
                    .mapToObj(c -> String.valueOf((char) c))
                    .collect(Collectors.toList());

            if (specialChar.contains(Constant.STR_PERCENT)) {
                // % 后面的第一位不能为0~9，a~f，A~F
                getAppendList(appendList, split, itemDTO);
            }
        }

        // 所有集合
        List<String> allElementsList = new ArrayList<>();
        allElementsList.addAll(elementsList);
        allElementsList.addAll(specialCharList);
        // 获取随机位数
        int randomLength = getRandomLength(split, itemDTO);

        String temp = "";
        List<String> resultList = new ArrayList<>();
        String resultStr = getResultStr(randomLength, temp, appendList, resultList, allElementsList);
        // 特殊字符不能出现在第一位 前面 randomLength 预留了一位用于拼接普通字符
        return getRandomElement(elementsList) + resultStr;
    }

    private void checkAndUpdateLastChar(List<String> resultList, List<String> appendList) {
        String lastTempStr = resultList.get(resultList.size() - 1);
        String lastChar = lastTempStr.substring(lastTempStr.length() - 1);
        if (StringUtils.equals(lastChar, Constant.STR_PERCENT)) {
            appendList.removeIf("%"::equals);
            resultList.set(resultList.size() - 1, lastTempStr.substring(1) + getRandomElement(appendList));
        }
    }


    /**
     * 获取数字/小写字母/大写字母的集合
     *
     * @param elementsTypeList 数字/小写字母/大写字母的集合
     * @param elementsList 原始集合
     */
    private void getElementsTypeList(List<List<String>> elementsTypeList, List<String> elementsList) {
        List<String> digits = elementsList.stream()
                .filter(c -> c.matches("\\d"))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(digits)) {
            Collections.shuffle(digits);
            elementsTypeList.add(digits);
        }
        List<String> lowerCaseLetters = elementsList.stream()
                .filter(c -> c.matches("[a-z]"))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lowerCaseLetters)) {
            Collections.shuffle(lowerCaseLetters);
            elementsTypeList.add(lowerCaseLetters);
        }
        List<String> upperCaseLetters = elementsList.stream()
                .filter(c -> c.matches("[A-Z]"))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(upperCaseLetters)) {
            Collections.shuffle(upperCaseLetters);
            elementsTypeList.add(upperCaseLetters);
        }
        Collections.shuffle(elementsTypeList);
    }

    /**
     * 随机位数
     *
     * @param split split
     * @param itemDTO itemDTO
     * @return int
     */
    private int getRandomLength( String[] split,SpTemplateItemDTO itemDTO) {
        try {
            String randomLengthStr = split[Constant.INT_3];
            return Integer.parseInt(randomLengthStr);
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
    }

    /**
     * 获取 0~9，a~F，A~F之外的元素
     *
     * @param appendList
     * @param split
     */
    private void getAppendList(List<String> appendList, String[] split, SpTemplateItemDTO itemDTO) {
        String allElements = split[Constant.INT_1] + split[Constant.INT_2];
        allElements = allElements.replace("\u200B", "").replace(" ", "");
        List<String> tempList = allElements.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .filter(item -> !item.matches("[0-9a-fA-F]"))
                .collect(Collectors.toList());
        appendList.addAll(tempList);
        if (appendList.size() < Constant.INT_2) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE,
                    MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{itemDTO.getParamName()});
        }
    }

    /**
     * 随机从集合中获取一个元素
     *
     * @param list
     * @return
     */
    private String getRandomElement(List<String> list) {
        Random random = new Random();
        int randomIndex = random.nextInt(list.size());
        return list.get(randomIndex);
    }

    private void getTelmexEigenValMap(LinkedHashMap<String, Long> telmexEigenValMap, LinkedHashMap<String, Long> telmexOldEigenValMap, SpSpecialityParam spSpecialityParam) {
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < Constant.INT_4; i++) {
            // 获取0-4周后的时间
            LocalDate localDate = currentDate.plusWeeks(i);
            // 获取年份的后两位(2位，不足2位补0)
            int currentYear = localDate.getYear();
            String yearPart = String.format("%02d", currentYear % 100);
            // 获取周数(2位，不足2位补0)
            WeekFields weekFields = WeekFields.of(Locale.getDefault());
            int weekOfYear = localDate.get(weekFields.weekOfYear());
            String weekPart = String.format("%02d", weekOfYear);
            // 根据 eigenValue 查询剩已使用的 累计值 4位随机数每周有65536个： 0-65535
            String telmexEigenValue = Constant.GPON_SN_PREFIX + yearPart + weekPart;
            Long total = spSpecialityParamRepository.getTelmexEigenValueIndex(telmexEigenValue);
            // 不存在则新增 数据库存的累计值 是待使用的序列号值
            // 累计值 默认位0（0000） 最大65535（FFFF），使用完了应该是存65536
            if (null == total) {
                total = Constant.LONG_ZERO;
                SpSpecialityParam telmexEigen = new SpSpecialityParam();
                telmexEigen.setTelmexEigenValue(telmexEigenValue);
                telmexEigen.setTelmexTotal(total);
                telmexEigen.setLastUpdatedBy(spSpecialityParam.getCreateBy());
                spSpecialityParamRepository.insertTelmexEigenValue(telmexEigen);
            }
            if (total != Constant.LONG_65536) {
                telmexEigenValMap.put(telmexEigenValue, total);
                telmexOldEigenValMap.put(telmexEigenValue, total);
            }
        }
    }


    private void setIntervalValueParams(SpTemplateItemDTO itemDTO, JSONObject itemData) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.INTERVAL_VALUE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] strings = tempStr.split(Constant.STR_SPLIT, Constant.INT_B1);
        String[] splits = strings[1].split(Constant.VIRGULE);
        String variable = this.getVariableValue(itemData, splits, itemDTO);
        // 大小写转换
        variable = this.toggleCase(strings[Constant.INT_2], variable);
        String value = strings[0] + variable + strings[Constant.INT_3];
        itemData.put(itemDTO.getParamName(), value);
    }

    // 获取变量值
    private String getVariableValue(JSONObject itemData, String[] splits, SpTemplateItemDTO itemDTO) {
        // Mac参数下，去掉分隔符
        String targetVariable = this.handleByMac(itemData, splits, itemDTO);
        int startIndex = Integer.parseInt(splits[Constant.INT_1]);
        int endIndex = Integer.parseInt(splits[Constant.INT_2]);
        return targetVariable.substring(startIndex - 1, startIndex + endIndex - 1);
    }

    // Mac参数下，去掉分隔符
    private String handleByMac(JSONObject itemData, String[] splits, SpTemplateItemDTO itemDTO) {
        Object object = itemData.get(splits[0]);
        if (object == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.VARIABLE_DOES_NOT_EXIST, new Object[]{itemDTO.getParamName(), splits[0]});
        }
        String targetVariable = object.toString();
        long count = targetVariable.chars().filter(c -> (c == '-' || c == ':')).count();
        if (count == Constant.INT_5) {
            targetVariable = targetVariable.replace(Constant.HORIZON, Constant.STRING_EMPTY)
                    .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        }
        return targetVariable;
    }

    private String toggleCase(String type, String variable) {
        if (StringUtils.isBlank(type)) {
            return variable;
        }
        if (StringUtils.equals(Constant.STR_0, type)) {
            variable = variable.toUpperCase();
        } else {
            variable = variable.toLowerCase();
        }
        return variable;
    }

    private void setAssignmentParams(SpTemplateItemDTO itemDTO, JSONObject itemData) {
        if (StringUtils.equals(Constant.GenerationMethod.ASSIGNMENT_TEXT, itemDTO.getGenerationMethod())) {
            String value = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_TEXT_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            itemData.put(itemDTO.getParamName(), value);
        } else if (StringUtils.equals(Constant.GenerationMethod.ASSIGNMENT_VARIABLE, itemDTO.getGenerationMethod())) {
            // 同变量值
            String variable = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_VARIABLE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            if (itemData.get(variable) == null) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.VARIABLE_DOES_NOT_EXIST, new Object[]{itemDTO.getParamName(), variable});
            }
            itemData.put(itemDTO.getParamName(), itemData.get(variable));
        } else if (StringUtils.equals(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE, itemDTO.getGenerationMethod())) {
            String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.ASSIGNMENT_TEXT_AND_RANGE_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                    .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
            String[] strings = tempStr.split(Constant.STR_SPLIT);
            String variable1 = "";
            String variable2 = "";
            if (StringUtils.isNotBlank(strings[1])) {
                String[] splits = strings[1].split(Constant.VIRGULE);
                variable1 = this.getVariableValue(itemData, splits, itemDTO);
            }
            if (StringUtils.isNotBlank(strings[Constant.INT_2])) {
                String[] splits = strings[Constant.INT_2].split(Constant.VIRGULE);
                variable2 = this.getVariableValue(itemData, splits, itemDTO);
            }
            String value = strings[0] + variable1 + variable2 + strings[3];
            itemData.put(itemDTO.getParamName(), value);
        }
    }

    private void genPostItemData(SpTemplateItemDTO itemDTO, JSONObject itemData,
                                 Map<String, String> paramsMap, Map<String, String> paramTypeToNameMap,
                                 Map<String, String> fixedStrMap) {
        if (StringUtils.equals(Constant.ParamType.DEVICE_SERIAL_NUMBER, itemDTO.getParamType())) {
            this.setDSNData(itemDTO, itemData, paramsMap, paramTypeToNameMap);
        } else if (itemDTO.getParamRule().contains(SpecialityParamConstant.FUNCTION_FIXEDSTR)) {
            itemData.put(itemDTO.getParamName(), fixedStrMap.get(itemDTO.getItemId()));
        }
    }

    private void setWholeDeviceCodeData(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramsMap) {
        long index = Long.parseLong(paramsMap.get("wdcTotal"));
        index++;
        String indexStr = String.format("%09d", index);
        paramsMap.put("wdcTotal", String.valueOf(index));
        itemData.put(itemDTO.getParamName(), paramsMap.get("wdcEigenValue") + indexStr);
    }

    private void setOUIData(SpTemplateItemDTO itemDTO, JSONObject itemData) {
        String tempStr = itemDTO.getParamRule().replace(SpecialityParamConstant.OUI_FUNCTION_PREFIX, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        String[] strings = tempStr.split(Constant.STR_SPLIT);
        if (!itemData.containsKey(strings[1])) {
            return;
        }
        if (itemData.get(strings[1]) == null) {
            return;
        }
        String tempVariableValue = itemData.get(strings[1]).toString().replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        int startIndex = Integer.parseInt(strings[Constant.INT_2].substring(0, strings[Constant.INT_2].indexOf(Constant.VIRGULE)));
        int endIndex = Integer.parseInt(strings[Constant.INT_2].substring(strings[Constant.INT_2].indexOf(Constant.VIRGULE) + 1));
        String cutVariableName = tempVariableValue.substring(startIndex - 1, startIndex + endIndex - 1);
        String tempValue = strings[0] + cutVariableName + strings[Constant.INT_3];
        String value = tempValue.replace(Constant.DOUBLE_QUOTE, Constant.STRING_EMPTY);
        itemData.put(itemDTO.getParamName(), value);
    }

    private void setDSNData(SpTemplateItemDTO spTemplateItemDTO, JSONObject itemData, Map<String, String> paramsMap,
                            Map<String, String> paramTypeToNameMap) {
        if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC_FTTO, spTemplateItemDTO.getGenerationMethod())) {
            // 电信FTTO
            this.setFTTOData(spTemplateItemDTO, itemData, paramTypeToNameMap, paramsMap.get("dsnFTTO"));
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CMCC, spTemplateItemDTO.getGenerationMethod())) {
            // 移动
            this.setCMCCData(spTemplateItemDTO, paramsMap.get("dsnCMCC"), itemData, paramTypeToNameMap);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC, spTemplateItemDTO.getGenerationMethod())) {
            // 电信
            this.setCTCCData(spTemplateItemDTO, itemData, paramTypeToNameMap, paramsMap.get("dsnCTCCProductType"), paramsMap.get("dsnCTCCProvinceCode"));
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CUCC, spTemplateItemDTO.getGenerationMethod())) {
            // 联通
            paramsMap.put("haveCUCCParams", "true");
            DsnCUCCParams dsnCUCCParams = new DsnCUCCParams();
            dsnCUCCParams.setDsnCUCCFixedValue(paramsMap.get("dsnCUCCFixedValue"));
            dsnCUCCParams.setDsnCUCCSn(paramsMap.get("dsnCUCCSn"));
            dsnCUCCParams.setDsnCUCCVariable(paramsMap.get("dsnCUCCVariable"));
            this.setTempCUCCData(spTemplateItemDTO, itemData, paramTypeToNameMap, dsnCUCCParams);
        }
    }


    private void setFTTOData(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramTypeToNameMap, String dsnFTTO) {
        // A：设备类型编码；BB：省份编码，如00、01、02、......、33；
        String macFirstSix = "";
        String macFirstTwelve = "";
        Object temp = itemData.get(paramTypeToNameMap.get(Constant.GenerationMethod.MAC_START));
        if (temp != null) {
            String replace = temp.toString().replace(Constant.HORIZON, Constant.STRING_EMPTY)
                    .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
            macFirstSix = replace.substring(0, Constant.INT_6);
            macFirstTwelve = replace.substring(0, Constant.INT_12);
        }
        String dsnCTCCValue = macFirstSix + Constant.HORIZON + Constant.STRING_ZERO_ONE + dsnFTTO + macFirstTwelve;
        itemData.put(itemDTO.getParamName(), dsnCTCCValue);
    }

    private void preItemListGenData(HashMap<String, List<ResourceDetail>> resources, List<SpTemplateItemDTO> preItemList,
                                    JSONObject itemData, Map<String, String> paramsMap, LinkedHashMap<String, Long> telmexEigenValMap) {
        int i = Integer.parseInt(paramsMap.get("num"));
        for (SpTemplateItemDTO spTemplateItemDTO : preItemList) {
            if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_MACSTART)) {
                ResourceDetail resourceDetail = resources.get(MpConstant.MAC).get(i);
                itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getResourceStart());
                paramsMap.put(Constant.MAC_START, resourceDetail.getResourceStart());
            } else if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_MACEND)) {
                ResourceDetail resourceDetail = resources.get(MpConstant.MAC).get(i);
                itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getResourceSn());
            } else if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_GPONSN)) {
                this.setGponSnData(resources, itemData, telmexEigenValMap, spTemplateItemDTO, i);
            } else if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_D_SN)) {
                itemData.put(spTemplateItemDTO.getParamName(), paramsMap.get("dsnStr"));
            } else if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_DEVICETYPE)) {
                itemData.put(spTemplateItemDTO.getParamName(), spTemplateItemDTO.getGenValue());
            } else if (spTemplateItemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_DB)) {
                String tempStr = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.RESOURCE_POOL_DB, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                String type = tempStr.substring(0, tempStr.indexOf(','));
                List<ResourceDetail> detailList = resources.get(type);
                if (detailList ==  null) {
                    return;
                }
                ResourceDetail resourceDetail = detailList.get(i);
                itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getResourceStart());
            } else if (StringUtils.equals(Constant.ParamType.WHOLE_DEVICE_CODE, spTemplateItemDTO.getParamType())) {
                this.setWholeDeviceCodeData(spTemplateItemDTO, itemData, paramsMap);
            }
            this.setNetWorkAccessInfo(resources, itemData, paramsMap, spTemplateItemDTO, i);
        }
    }


    /**
     * 赋值入网信息参数
     *
     */
    private void setNetWorkAccessInfo(HashMap<String, List<ResourceDetail>> resources, JSONObject itemData,
                                         Map<String, String> paramsMap, SpTemplateItemDTO spTemplateItemDTO, int i) {
        // 研发验证，不生成入网数据
        if (StringUtils.equals(STRING_FIVE, paramsMap.get(USAGE_SCOPE))) {
            return;
        }
        List<ResourceDetail> detailList = resources.get(MpConstant.NAL);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        ResourceDetail resourceDetail = detailList.get(i);
        if (StringUtils.equals(ParamType.NASN, spTemplateItemDTO.getParamType())) {
            itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getModelNumAndCode());
        }
        if (StringUtils.equals(ParamType.SCRAMBLING_CODE, spTemplateItemDTO.getParamType())) {
            itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getScramblingCode());
        }
        if (StringUtils.equals(ParamType.NACC, spTemplateItemDTO.getParamType())) {
            itemData.put(spTemplateItemDTO.getParamName(), resourceDetail.getResourceStart());
        }
    }

    private void setGponSnData(HashMap<String, List<ResourceDetail>> resources, JSONObject itemData, LinkedHashMap<String, Long> telmexEigenValMap, SpTemplateItemDTO itemDTO, int i) {
        if (Objects.equals(itemDTO.getGenerationMethod(), Constant.GenerationMethod.TELMEX_GPON_SN)) {
            for (Map.Entry<String, Long> entry : telmexEigenValMap.entrySet()) {
                // 找到累计值 < FFFF的
                if (entry.getValue() >= Constant.LONG_65536) {
                    continue;
                }
                // 使用 特征值 + 累计值的16进制
                Long value = entry.getValue();
                String key = entry.getKey();
                String incrementedHexStr = String.format("%04X", value).toUpperCase();
                itemData.put(itemDTO.getParamName(), key + incrementedHexStr);
                telmexEigenValMap.put(key, value + 1);
                break;
            }
        } else {
            ResourceDetail resourceDetail = resources.get(MpConstant.GPON_SN).get(i);
            itemData.put(itemDTO.getParamName(), resourceDetail.getResourceStart());
        }
    }

    private void setMapInfo(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList,
                            Map<String, String> paramTypeToNameMap, Map<String, String> paramsMap) {
        for (SpTemplateItemDTO spTemplateItemDTO : itemList) {
            if (StringUtils.equals(Constant.GenerationMethod.MAC_START, spTemplateItemDTO.getGenerationMethod())) {
                paramTypeToNameMap.put(Constant.GenerationMethod.MAC_START,spTemplateItemDTO.getParamName());
                String separator = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.RESOURCE_POOL_MACSTART, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                paramsMap.put(Constant.SEPARATOR, separator);
            } else if (StringUtils.equals(Constant.GenerationMethod.CUSTOMIZED_MAC_START, spTemplateItemDTO.getGenerationMethod())) {
                paramTypeToNameMap.put(Constant.GenerationMethod.MAC_START,spTemplateItemDTO.getParamName());
                String tempStr = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.RESOURCE_POOL_MACSTART, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                String separator = tempStr.substring(0, tempStr.indexOf(','));
                paramsMap.put(Constant.SEPARATOR, separator);
            } else if (StringUtils.equals(Constant.GenerationMethod.MAC_END, spTemplateItemDTO.getGenerationMethod())) {
                paramTypeToNameMap.put(Constant.GenerationMethod.MAC_END,spTemplateItemDTO.getParamName());
            } else if (StringUtils.equals(Constant.ParamType.GPON_SN, spTemplateItemDTO.getParamType())) {
                paramTypeToNameMap.put(Constant.ParamType.GPON_SN,spTemplateItemDTO.getParamName());
            } else if (StringUtils.equals(Constant.ParamType.D_SN,spTemplateItemDTO.getParamType())) {
                String tempStr = spTemplateItemDTO.getParamRule().replace(SpecialityParamConstant.RESOURCE_POOL_D_SN, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                        .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
                char[] charArray = tempStr.toCharArray();
                charArray[Constant.INT_3] = spSpecialityParam.getProductionUnit().charAt(0);
                // spTemplateItemDTO.setGenValue(new String(charArray));
                paramsMap.put("dsnStr", new String(charArray));
            } else if (StringUtils.equals(Constant.ParamType.DEVICE_SERIAL_NUMBER,spTemplateItemDTO.getParamType())) {
                this.setParamsMapByDSN(paramsMap, spTemplateItemDTO);
            } else if (StringUtils.equals(Constant.ParamType.WHOLE_DEVICE_CODE, spTemplateItemDTO.getParamType())) {
                paramsMap.put("wdcEigenValue", spSpecialityParam.getWdcEigenValue());
                paramsMap.put("wdcTotal", String.valueOf(spSpecialityParam.getWdcTotal()));
            } else if (StringUtils.equals(Constant.ParamType.STBID, spTemplateItemDTO.getParamType())) {
                paramsMap.put(SPPARAM_KEY_STBID_PRRFIX, spSpecialityParam.getStbidPrefix());
                paramsMap.put(SPPARAM_KEY_STBID_CHECKAA, spSpecialityParam.getStbidCheckAA());
            }
        }
        paramsMap.put(Constant.USAGE_SCOPE, spSpecialityParam.getUsageScope());
    }


    private void setParamsMapByDSN(Map<String, String> paramsMap, SpTemplateItemDTO itemDTO) {
        String substring = itemDTO.getParamRule().substring(itemDTO.getParamRule().indexOf('(') + 1);
        String tempStr = substring.replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY);
        if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC_FTTO, itemDTO.getGenerationMethod())) {
            String replace = tempStr.replace(Constant.COMMA, Constant.STRING_EMPTY);
            paramsMap.put("dsnFTTO", replace);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CMCC, itemDTO.getGenerationMethod())) {
            paramsMap.put("dsnCMCC", tempStr);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CTCC, itemDTO.getGenerationMethod())) {
            String dsnCTCCProductType = tempStr.substring(0, tempStr.indexOf(','));
            String dsnCTCCProvinceCode = tempStr.substring(tempStr.indexOf(',') + 1);
            paramsMap.put("dsnCTCCProductType", dsnCTCCProductType);
            paramsMap.put("dsnCTCCProvinceCode", dsnCTCCProvinceCode);
        } else if (StringUtils.equals(Constant.GenerationMethod.DSN_CUCC, itemDTO.getGenerationMethod())) {
            String[] strings = tempStr.split(Constant.STR_SPLIT);
            String cuccSn = "";
            cuccSn = strings[1].chars()
                    .filter(c -> !Character.isWhitespace(c))
                    .collect(StringBuilder::new,
                            StringBuilder::appendCodePoint,
                            StringBuilder::append)
                    .toString();
            paramsMap.put("dsnCUCCFixedValue", strings[0]);
            paramsMap.put("dsnCUCCSn", cuccSn);
            paramsMap.put("dsnCUCCVariable", strings[2]);
        }
    }


    private void setTempCUCCData(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramTypeToNameMap, DsnCUCCParams dsnCUCCParams) {
        Object tempMacStart = itemData.get(paramTypeToNameMap.get(Constant.GenerationMethod.MAC_START));
        String cutMacStart = "";
        if (tempMacStart == null) {
            return;
        }
        cutMacStart = tempMacStart.toString().replace(Constant.HORIZON, Constant.STRING_EMPTY)
                .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        if (StringUtils.isBlank(dsnCUCCParams.getDsnCUCCFixedValue())) {
            String macFirstSix = cutMacStart.substring(0, Constant.INT_6);
            dsnCUCCParams.setDsnCUCCFixedValue(macFirstSix);
        }
        Object variableValue = itemData.get(paramTypeToNameMap.get(dsnCUCCParams.getDsnCUCCVariable()));
        if (variableValue == null) {
            return;
        }
        variableValue = variableValue.toString().replace(Constant.HORIZON, Constant.STRING_EMPTY)
                .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        // setCUCCData()方法  YY：校验码为32位SerialNumber字符串前面0~29位的异或校验值，位宽8bit。
        String cuccPrefix = String.format(Constant.CUCC_PARAM_NAME, itemDTO.getParamName());
        String tempStr = cuccPrefix + dsnCUCCParams.getDsnCUCCFixedValue() + Constant.HORIZON
                + dsnCUCCParams.getDsnCUCCSn() + variableValue;
        itemData.put(Constant.CUCC_PREFIX, tempStr);
    }

    private void setCUCCData(List<SpSpecialityParamItem> spSpecialityParamItems, Map<String, String> paramsMap) {
        if (paramsMap.get(Constant.HAVE_CUCC_PARAMS) == null || StringUtils.equals(paramsMap.get(Constant.HAVE_CUCC_PARAMS), Constant.FALSE)) {
            return;
        }
        for (SpSpecialityParamItem spSpecialityParamItem : spSpecialityParamItems) {
            String itemData = spSpecialityParamItem.getItemData();
            JSONObject jsonObject = JSONObject.parseObject(itemData);
            Object object = jsonObject.get(Constant.CUCC_PREFIX);
            if (object == null) {
                continue;
            }
            String paramName = object.toString().substring(0, object.toString().indexOf(':'));
            // 32位SerialNumber，前30位
            String tempStr = object.toString().substring(object.toString().indexOf('-') + 1);
            char[] charArray = tempStr.toCharArray();
            int xorResult = 0;
            // 前30位计算异或校验值
            for (char c : charArray) {
                xorResult ^= c;
            }
            String xorResultStr = String.format("%02X", xorResult);
            int colonIndex = object.toString().indexOf(':');
            int dashIndex = object.toString().indexOf('-', colonIndex);
            String macFirstSix = object.toString().substring(colonIndex + 1, dashIndex);
            tempStr = macFirstSix + Constant.HORIZON + tempStr + xorResultStr;
            jsonObject.remove(Constant.CUCC_PREFIX);
            jsonObject.put(paramName, tempStr);
            spSpecialityParamItem.setItemData(jsonObject.toJSONString());
        }
    }

    private void setCTCCData(SpTemplateItemDTO itemDTO, JSONObject itemData, Map<String, String> paramTypeToNameMap, String dsnCTCCProductType, String dsnCTCCProvinceCode) {
        String macFirstSix = "";
        String macFirstTwelve = "";
        Object temp = itemData.get(paramTypeToNameMap.get(Constant.GenerationMethod.MAC_START));
        if (temp != null) {
            String replace = temp.toString().replace(Constant.HORIZON, Constant.STRING_EMPTY)
                    .replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
            macFirstSix = replace.substring(0, Constant.INT_6);
            macFirstTwelve = replace.substring(0, Constant.INT_12);
        }
        String randomNum = this.getRandom3or4();
        String randomStr = this.getRandomHexChar();
        String dsnCTCCValue = macFirstSix + Constant.HORIZON + randomNum + randomStr + dsnCTCCProductType + dsnCTCCProvinceCode + macFirstTwelve;
        itemData.put(itemDTO.getParamName(), dsnCTCCValue);
    }

    private void setCMCCData(SpTemplateItemDTO itemDTO, String dsnCMCC, JSONObject itemData, Map<String, String> paramTypeToNameMap) {
        String firstValue = "";
        if (StringUtils.isBlank(dsnCMCC)) {
            Object temp = itemData.get(paramTypeToNameMap.get(Constant.GenerationMethod.MAC_START));
            if (temp != null) {
                firstValue = temp.toString().replace(Constant.HORIZON,Constant.STRING_EMPTY)
                        .replace(Constant.SEMICOLON,Constant.STRING_EMPTY).substring(0, 6);
            }
        } else {
            firstValue = dsnCMCC;
        }
        Object temp = itemData.get(paramTypeToNameMap.get(Constant.ParamType.GPON_SN));
        if (temp != null) {
            String gponSn = temp.toString();
            String dsnCMCCValue = firstValue + Constant.HORIZON + gponSn;
            itemData.put(itemDTO.getParamName(), dsnCMCCValue);
        }
    }


    // 返回一个0-9或A-F之间的随机字符值
    public String getRandomHexChar() {
        Random random = new Random();
        // 生成一个0到15之间的随机数（包含0和15）
        int randomInt = random.nextInt(Constant.INT_16);
        // 使用三元运算符来决定返回哪个字符
        char numChar = randomInt < Constant.INT_10 ? (char) ('0' + randomInt) : (char) ('A' + randomInt - Constant.INT_10);
        return String.valueOf(numChar);
    }

    public String getRandom3or4() {
        Random random = new Random();
        // 生成一个0或1的随机数
        int randomInt = random.nextInt(Constant.INT_2);
        // 根据随机数返回'3'或'4'
        // 如果randomInt是0，返回'3'，否则返回'4'
        return randomInt == 0 ? Constant.STR_THREE : Constant.STRING_FOUR;
    }

    private String getMacSplitParam(String rule) {
        return rule.replace(SpecialityParamConstant.RESOURCE_POOL_GETSTARTMAC, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.RESOURCE_POOL_GETENDMAC, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.RESOURCE_POOL_GETSTARTGPONSN, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_START, Constant.STRING_EMPTY)
                .replace(SpecialityParamConstant.PARENTHESES_END, Constant.STRING_EMPTY)
                .replace(Constant.SINGLE_QUOTE, Constant.STRING_EMPTY);
    }

    private JSONObject getAdditional(List<ResourceApplicationEntityDTO> applyList, HashMap<String, List<ResourceDetail>> resources, int idx) {
        JSONObject additional = new JSONObject();
        if (applyList.stream().anyMatch(item -> item.getResourceType().equals(MpConstant.MAC))) {
            additional = JSON.parseObject(resources.get(MpConstant.MAC).get(idx).getAdditional());
        }
        return additional == null ? new JSONObject() : additional;
    }

    private Map<String, String> getFixedStr(SpSpecialityParam spSpecialityParam) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(spSpecialityParam.getFixedStr())) {
            String[] fixedstrs = spSpecialityParam.getFixedStr().split(Constant.COMMA);
            for (String fixedstr : fixedstrs) {
                if (StringUtils.isNotBlank(fixedstr)) {
                    String[] item = fixedstr.split(Constant.SEMICOLON);
                    map.put(item[NumConstant.NUM_ZERO], item[NumConstant.NUM_ONE]);
                }
            }
        }
        return map;
    }

    private Long getBarcode(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList, Long resourceTotal) {
        if (itemList.stream().noneMatch(itemDTO -> itemDTO.getParamRule().equals(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE + SpecialityParamConstant.PARENTHESES))) {
            return NumConstant.LONG_ZERO;
        }
        if (null == spSpecialityParam.getBarcode()) {
            // 申请条码
            BarcodeQueryDTO dto = new BarcodeQueryDTO();
            dto.setCreateBy(spSpecialityParam.getCreateBy());
            dto.setOrgId(635L);
            dto.setItemId(Long.parseLong(spSpecialityParam.getSpecialityParamId()));
            dto.setItemNo(spSpecialityParam.getItemCode());
            dto.setItemName(spSpecialityParam.getItemName());
            dto.setItemUnit(spSpecialityParam.getItemUnit());
            dto.setItemRevision(spSpecialityParam.getItemVersion());
            dto.setIsPiLiang(Constant.Y_STATUS);
            dto.setPrintNum(resourceTotal);
            ServiceData<Long> result = datawbFeignService.generateBarcode(dto);
            if (RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
                spSpecialityParam.setBarcode(result.getBo());
                spSpecialityParamRepository.updateById(spSpecialityParam);
                return spSpecialityParam.getBarcode();
            }
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        } else {
            Long barcode = spSpecialityParamItemService.getMaxBarcode(spSpecialityParam.getSpecialityParamId());
            if (barcode == null) {
                return spSpecialityParam.getBarcode();
            }
            return NumConstant.NUM_ONE + barcode;
        }
    }

    private String ruleHandle(String paramName, String rule, JSONObject itemData, long rowNum) {
        rule = rule.replace(SpecialityParamConstant.FUNCTION_ROWNUMBER + SpecialityParamConstant.PARENTHESES, rowNum + "");
        //变量替换
        for (String key : itemData.keySet()) {
            //存在变量，则替换
            if (rule.contains(key)) {
                if (itemData.get(key) == null) {
                    continue;
                }
                rule = rule.replace(SpecialityParamConstant.BRACKETS_START + key + SpecialityParamConstant.BRACKETS_END,
                        Constant.SINGLE_QUOTE + itemData.get(key).toString().replace(Constant.HORIZON, Constant.STRING_EMPTY) + Constant.SINGLE_QUOTE);
            }
        }
        if (rule.contains(SpecialityParamConstant.BRACKETS_START)) {
            // 判断参数名是否替换完
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_ERROR, new Object[]{paramName});
        }
        try {
            return new TemplateFunctionExpress(rule).caculate();
        } catch (Exception e) {
            log.error("###########模板规则执行错误{}", rule);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, new Object[]{rule});
        }
    }

    private void validateGenerate(SpSpecialityParam spSpecialityParam, List<SpTemplateDTO> templateDetails, List<ResourceApplicationEntityDTO> applyList) {
        // this.validateApplyResource(applyList);
        SpTemplateDTO spTemplate = templateDetails.get(NumConstant.NUM_ZERO);
        if (spTemplate.getItemList().stream().anyMatch(itemDTO -> itemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_GETSTARTMAC)
                || itemDTO.getParamRule().startsWith(SpecialityParamConstant.RESOURCE_POOL_GETENDMAC))
                && applyList.stream().noneMatch(resourceApplicationEntityDTO -> resourceApplicationEntityDTO.getResourceType().equals(MpConstant.MAC))) {
            // 无MAC资源
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_APPLYTASK_NOT_RESOURCE, new String[]{spSpecialityParam.getApplyTask(), MpConstant.MAC});
        }
        if (spTemplate.getItemList().stream().anyMatch(itemDTO -> itemDTO.getParamRule().equals(SpecialityParamConstant.RESOURCE_POOL_GETSTARTGPONSN)
                || itemDTO.getParamRule().equals(SpecialityParamConstant.RESOURCE_POOL_GETENDGPONSN))
                && applyList.stream().noneMatch(resourceApplicationEntityDTO -> resourceApplicationEntityDTO.getResourceType().equals(MpConstant.GPON_SN))) {
            // 无GPON_SN资源
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_APPLYTASK_NOT_RESOURCE, new String[]{spSpecialityParam.getApplyTask(), MpConstant.GPON_SN});
        }
    }

    private void validateApplyResource(List<ResourceApplicationEntityDTO> applyList) {
        List<ResourceApplicationEntityDTO> checkApplyList = applyList.stream().filter(item ->
                item.getResourceType().equals(MpConstant.GPON_SN) || MpConstant.MAC.equals(item.getResourceType())).collect(Collectors.toList());
        if (checkApplyList.size() > NumConstant.NUM_ONE) {
            for (int i = NumConstant.NUM_ONE; i < checkApplyList.size(); i++) {
                ResourceApplicationEntityDTO one = checkApplyList.get(i - NumConstant.NUM_ONE);
                ResourceApplicationEntityDTO two = checkApplyList.get(i);
                int oneNum = one.getDistributableQty() != null ? one.getDistributableQty() : one.getApplyQty().intValue();
                int twoNum = two.getDistributableQty() != null ? two.getDistributableQty() : two.getApplyQty().intValue();
                if (oneNum != twoNum) {
                    // 资源数量不相等
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SPECIALITY_PARAM_APPLYTASK_RESOURCE_NOT_MATCH,
                            new String[]{one.getResourceType(), one.getApplyBillNo(), two.getResourceType(), two.getApplyBillNo()});
                }
            }
        }
    }

    private String generateSpecialtyId() {
        //工厂ID
        StringBuilder sb = new StringBuilder();
        //8位日期
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FORMATTER_YYYYMMDD);
        String formatDate = simpleDateFormat.format(new Date());
        //4位流水码
        sb.append(formatDate).append(getRedisId(PROGRAM_KEY_VALUE_PREFIX_BILL + formatDate));
        return sb.toString();
    }

    //获取6位流水号
    private String getRedisId(String redisKey) {
        String lockKey = this.getClass().getSimpleName() + PROGRAM_KEY_VALUE_PREFIX_BILL;
        RedisLock redisLock = new RedisLock(lockKey);
        String redisId = null;
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
            }
            Integer maxCountValue = findMaxCount(redisKey);
            RedisCacheUtils.set(redisKey, maxCountValue + NumConstant.NUM_ONE, EXPIRE_TIME_DAY);
            StringBuilder serialCode = new StringBuilder("000000");
            serialCode = serialCode.append(maxCountValue);
            redisId = serialCode.substring(serialCode.length() - NumConstant.NUM_SIX, serialCode.length());
        } finally {
            redisLock.unlock();
        }
        return redisId;
    }

    //根据key获取最大值
    private Integer findMaxCount(String redisKey) {
        Integer maxCountValue = RedisCacheUtils.get(redisKey, Integer.class);
        if (maxCountValue == null) {
            return 0;
        }
        return maxCountValue;
    }


    @Override
    public SpSpecialityParam getById(String specialityParamId) {
        return spSpecialityParamRepository.selectById(specialityParamId);
    }

    @Override
    public List<SpSpecialityParam> queryList(SpSpecialityParamDTO query) {
        return spSpecialityParamRepository.selectList(query);
    }

    @Override
    public int getGenerateResult(String specialityParamId) {
        String result = redisTemplate.opsForValue().get(String.format(RedisKeyConstant.SPECIALITY_PARAM_GENERATE, specialityParamId));
        if (StringUtils.isNotBlank(result)) {
            redisTemplate.delete(String.format(RedisKeyConstant.SPECIALITY_PARAM_GENERATE, specialityParamId));
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, result, result);
        }
        SpSpecialityParam spSpecialityParam = getById(specialityParamId);
        if(null != spSpecialityParam) {
            return spSpecialityParam.getProgress();
        }
        return 0;
    }

    @Override
    public String generateTaskNo() {
        StringBuilder stringBuilder = new StringBuilder();
        //8位日期
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FORMATTER_YYYYMMDD);
        String formatDate = simpleDateFormat.format(new Date());
        //6位流水码
        stringBuilder.append(formatDate).append(this.getFlowId(Constant.GENERATE_TASK_NO_PREFIX + formatDate));
        return stringBuilder.toString();
    }

    private String getFlowId(String redisKey) {
        String lockKey = this.getClass().getSimpleName() + Constant.GENERATE_TASK_NO_PREFIX;
        RedisLock redisLock = new RedisLock(lockKey);
        String numStr;
        try {
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.SERVERERROR_CODE, MessageId.GENERATE_TASK_NO_REDIS_KEY_ERROR);
            }
            Integer maxCountValue = findMaxCount(redisKey);
            RedisCacheUtils.set(redisKey, maxCountValue + NumConstant.NUM_ONE, EXPIRE_TIME_DAY);
            StringBuilder flowId = new StringBuilder("000000");
            flowId.append(maxCountValue);
            numStr = flowId.substring(flowId.length() - NumConstant.NUM_SIX, flowId.length());
        } finally {
            redisLock.unlock();
        }
        return numStr;
    }

    /**
     * 递增MAC
     *
     */
    private String incrementMacAddress(String mac, String separator, long plus) {
        mac = mac.replace(Constant.HORIZON, Constant.STRING_EMPTY).replace(Constant.SEMICOLON, Constant.STRING_EMPTY);
        long macLong = Long.parseLong(mac, Constant.INT_16);
        macLong += plus;
        // 递增MAC地址的数值
        String formattedMac = String.format("%012X", macLong).toUpperCase();
        // 将长整型转换回MAC地址字符串格式
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < formattedMac.length(); i += Constant.INT_2) {
            sb.append(formattedMac, i, i + Constant.INT_2);
            if (i < formattedMac.length() - Constant.INT_2) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    private Long countUndoneByCreator(String creator) {
        if (StringUtils.isBlank(creator)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMP_NO_IS_NULL);
        }
        return spSpecialityParamRepository.countUndoneByCreator(creator);
    }
}
