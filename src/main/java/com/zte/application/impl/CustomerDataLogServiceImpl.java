package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.CustomerDataLogService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.bytedance.BoardRepairService;
import com.zte.common.CommonUtils;
import com.zte.common.DataPusher;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.InteractiveB2B;
import com.zte.common.utils.InteractiveTencentB2B;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.PushDataToB2B;
import com.zte.domain.model.CustomerDataLogRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.InforDatawbRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.ALiResultDTO;
import com.zte.interfaces.dto.AliBabaQualityDataDTO;
import com.zte.interfaces.dto.AliFailMsgDTO;
import com.zte.interfaces.dto.B2bCallBackDTO;
import com.zte.interfaces.dto.ByteDanceDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerQualityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.MeiTuanQualityDTO;
import com.zte.interfaces.dto.MeiTuanQualityDataDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TencentForwardInDTO;
import com.zte.interfaces.dto.TencentForwardOutDataDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.RUN;
import static com.zte.common.utils.Constant.SEMICOLON;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.Constant.STRING_ONE;
import static com.zte.common.utils.Constant.STR_DATA;
import static com.zte.common.utils.Constant.SUPPLIER_QUERY_COMPARE_RESULT;
import static com.zte.common.utils.Constant.SVR_SN;
import static com.zte.common.utils.Constant.ZTE;
import static com.zte.common.utils.Constant.ZTEIMES_ALIBABA_QUALITYCODE;
import static com.zte.common.utils.Constant.ZTEIMES_ALIBABA_TYPE;
import static com.zte.common.utils.Constant.ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE;
import static com.zte.common.utils.Constant.ZTEIMES_MEI_TUAN_QUALITYCODE;
import static com.zte.common.utils.Constant.ZTE_IMES_TENCENT_FORWARD_QUERY;
import static com.zte.common.utils.Constant.ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA;
import static com.zte.common.utils.NumConstant.NUM_ONE;

/**
 * 客户数据推送日志服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-06 16:44:08
 */
@Service
public class CustomerDataLogServiceImpl implements CustomerDataLogService {

    @Autowired
    private CustomerDataLogRepository customerDataLogRepository;
    @Autowired
    private TradeDataLogRepository tradeDataLogRepository;
    @Autowired
    private PushDataToB2B pushDataToB2B;
    @Autowired
    private InteractiveB2B interactiveB2B;
    @Autowired
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Autowired
    private ProductionmgmtRemoteService productionmgmtRemoteService;
    @Autowired
    private DatawbRemoteService datawbRemoteService;
    @Autowired
    private BoardRepairService boardRepairService;
    @Autowired
    private HrmUserCenterService hrmUserCenterService;
    @Autowired
    private InforDatawbRemoteService inforDatawbRemoteService;
    @Autowired
    private InteractiveTencentB2B interactiveTencentB2B;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<CustomerDataLogDTO> getPushErrorData(CustomerDataLogDTO customerDataLogDTO) throws Exception {
        Page page = new Page(NUM_ONE, NumConstant.NUM_500);
        // 默认同步方式，不支持指定其他参数
        page.setParams(customerDataLogDTO);
        return customerDataLogRepository.getPushErrorData(page);
    }

    /**
     * 查询客户数据上传日志信息
     *
     * <AUTHOR>
     */
    @Override
    public Page<CustomerDataLogDTO> queryLogData(CustomerDataLogDTO param) throws Exception {
        this.checkQueryLogDataParam(param);
        Page<CustomerDataLogDTO> page = new Page<>(param.getPage(), param.getRows());
        page.setParams(param);
        List<CustomerDataLogDTO> queryList = customerDataLogRepository.pageList(page);
        this.setEmpNoWithName(queryList);
        page.setRows(queryList);
        return page;
    }

    /**
     * 工号姓名组装
     *
     * <AUTHOR>
     */
    private void setEmpNoWithName(List<CustomerDataLogDTO> queryList) throws Exception {
        List<String> userIdList = queryList.stream().map(CustomerDataLogDTO::getLastUpdatedBy).collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = hrmUserCenterService.getHrmPersonInfo(userIdList);
        for (CustomerDataLogDTO customerDataLogDTO : queryList) {
            HrmPersonInfoDTO hrmPersonInfoDTO = hrmPersonInfoDTOMap.get(customerDataLogDTO.getLastUpdatedBy());
            if (hrmPersonInfoDTO != null) {
                customerDataLogDTO.setLastUpdatedBy(hrmPersonInfoDTO.getEmpName() + customerDataLogDTO.getLastUpdatedBy());
            }
        }
    }

    /**
     * 校验查询参数
     *
     * <AUTHOR>
     */
    private void checkQueryLogDataParam(CustomerDataLogDTO param) {
        // 参数不能全为空
        if (isEmptyCheck(param.getCustomerName(), param.getContractNo(), param.getTaskNo(), param.getMessageType(), param.getSn(), param.getStatus()) &&
                null == param.getLastUpdatedStartDate() && null == param.getLastUpdatedEndDate()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_QUERY_PARAM_NULL);
        }
        // 推送时间查询跨度不能超过180天
        if (null != param.getLastUpdatedStartDate() && null != param.getLastUpdatedEndDate()
                && DateUtil.caldaysByDate(param.getLastUpdatedEndDate(), param.getLastUpdatedStartDate()).compareTo(Constant.TIME_INTERVAL) > Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PUSH_TIME_WITHIN_180_DAYS);
        }
        // 合同号，条码，任务号可以单独查询
        if (!isEmptyCheck(param.getContractNo(), param.getTaskNo(), param.getSn())) {
            return;
        }
        // 客户名称，数据类别，推送状态须和时间一起查询
        if ((null == param.getLastUpdatedStartDate() || null == param.getLastUpdatedEndDate()) &&
                (!isEmptyCheck(param.getCustomerName(), param.getMessageType(), param.getStatus()))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME);
        }
    }

    /**
     * 批量校验字符串是否为空
     *
     * <AUTHOR>
     */
    private boolean isEmptyCheck(String... strings) {
        for (String str : strings) {
            if (StringUtils.isNotBlank(str)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 重推CN/PN状态数据
     *
     * <AUTHOR>
     */
    @Override
    public void repushCustomerLogDataMethod(List<CustomerDataLogDTO> param, String empNo) throws Exception {
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_3410);
        // 查询3410数据字典获取数据类型对应配置
        List<SysLookupTypesDTO> valuesList = sysLookupTypesRepository.getList(requestParam);
        if (CollectionUtils.isEmpty(valuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_LOOKUP_TYPE_ERROR, new String[]{Constant.LOOK_UP_CODE_3410});
        }
        Map<String, Object> urlParam = new HashMap<>(Constant.INT_2);
        urlParam.put("lookupType", Constant.LOOK_UP_CODE_1245);
        List<SysLookupTypesDTO> factoryUrlList = sysLookupTypesRepository.getList(urlParam);
        if (CollectionUtils.isEmpty(factoryUrlList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.LOG_DATA_LOOKUP_TYPE_ERROR, new String[]{Constant.LOOK_UP_CODE_1245});
        }
        // 处理数据，将有tid的数据重推时推送对应tid的数据（保证每次重推的都是第一条数据的id，后续重推的tid为第一条id）
        param.forEach(item -> {
            if (StringUtils.isNotBlank(item.getTid())) {
                item.setId(item.getTid());
                item.setTid(null);
            }
        });
        // 根据系统来源分组并调用对应的重推接口
        Map<String, List<CustomerDataLogDTO>> originMap = param.stream().filter(i -> StringUtils.isNotBlank(i.getOrigin())).collect(Collectors.groupingBy(CustomerDataLogDTO::getOrigin));
        repushIMESData(originMap, factoryUrlList, valuesList, empNo);
        repushMESData(originMap, valuesList, empNo);
        repushASMSData(originMap);
    }

    /**
     * 重推iMES系统数据
     *
     * <AUTHOR>
     */
    private void repushIMESData(Map<String, List<CustomerDataLogDTO>> originMap, List<SysLookupTypesDTO> factoryUrlList, List<SysLookupTypesDTO> valuesList, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(originMap.get(Constant.SOURCE_SYSTEM_IMES))) {
            return;
        }
        List<CustomerDataLogDTO> iMESDataList = originMap.get(Constant.SOURCE_SYSTEM_IMES);
        // 根据消息类型将重推数据分组
        Map<String, List<CustomerDataLogDTO>> groupMap = iMESDataList.stream().collect(Collectors.groupingBy(CustomerDataLogDTO::getMessageType));
        for (String messageType : groupMap.keySet()) {
            List<SysLookupTypesDTO> list = valuesList.stream().filter(i -> Objects.equals(i.getLookupMeaning(), messageType)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<CustomerDataLogDTO> groupList = groupMap.get(messageType);
            // 再根据工厂id进一步将重推数据分组
            Map<Integer, List<CustomerDataLogDTO>> groupByFactoryMap = groupList.stream().collect(Collectors.groupingBy(CustomerDataLogDTO::getFactoryId));
            for (Integer factoryId : groupByFactoryMap.keySet()) {
                List<CustomerDataLogDTO> dataLogListGroupByFactory = groupByFactoryMap.get(factoryId);
                List<SysLookupTypesDTO> urlList = factoryUrlList.stream().filter(i -> Objects.equals(i.getLookupMeaning(), factoryId.toString())).collect(Collectors.toList());
                SysLookupTypesDTO sysLookupTypesDTO = list.get(0);
                sysLookupTypesDTO.setAttribute1(urlList.get(0).getAttribute2() + sysLookupTypesDTO.getAttribute1());
                // 点对点调用生产管理对应方法重推接口
                productionmgmtRemoteService.repushCustomerLogDataMethod(dataLogListGroupByFactory, sysLookupTypesDTO, empNo);
            }
        }
    }

    /**
     * 重推MES系统数据
     *
     * <AUTHOR>
     */
    private void repushMESData(Map<String, List<CustomerDataLogDTO>> originMap, List<SysLookupTypesDTO> valuesList, String empNo) throws Exception {
        if (CollectionUtils.isEmpty(originMap.get(Constant.SOURCE_SYSTEM_MES))) {
            return;
        }
        List<CustomerDataLogDTO> dataList = originMap.get(Constant.SOURCE_SYSTEM_MES);
        // 根据消息类型将重推数据分组
        Map<String, List<CustomerDataLogDTO>> groupMap = dataList.stream().collect(Collectors.groupingBy(CustomerDataLogDTO::getMessageType));
        for (String messageType : groupMap.keySet()) {
            List<SysLookupTypesDTO> list = valuesList.stream().filter(i -> Objects.equals(i.getLookupMeaning(), messageType)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<CustomerDataLogDTO> groupList = groupMap.get(messageType);
            SysLookupTypesDTO sysLookupTypesDTO = list.get(0);
            String url = sysLookupTypesDTO.getAttribute4();
            // MES条码或样机数据上传，根据日志记录直接推送B2B
            List<String> projectPhaseMES = Arrays.asList(Constant.PROJECT_PHASE_MES);
            String projectPhase = groupList.get(0).getProjectPhase();
            if (StringUtils.isNotBlank(projectPhase) && projectPhaseMES.contains(projectPhase)) {
                repushMESDataForCSR(groupList, empNo);
            } else {
                datawbRemoteService.repushCustormerLogDataFromMES(groupList, url);
            }
        }
    }

    /**
     * MES数据CSR重推
     *
     * @param groupList
     * @param empNo
     * @throws Exception
     */
    private void repushMESDataForCSR(List<CustomerDataLogDTO> groupList, String empNo) throws Exception {
        List<CustomerDataLogDTO> temp = new ArrayList<>();
        for (CustomerDataLogDTO customerDataLogDTO : groupList) {
            CustomerDataLogDTO tempDto = new CustomerDataLogDTO();
            BeanUtils.copyProperties(customerDataLogDTO, tempDto);
            tempDto.setId(UUID.randomUUID().toString().trim().replaceAll("-", ""));
            tempDto.setTid(customerDataLogDTO.getId());
            tempDto.setStatus(null);
            tempDto.setCreateBy(empNo);
            tempDto.setLastUpdatedBy(empNo);
            temp.add(tempDto);
        }
        pushDataToB2B(temp);
    }

    /**
     * 重推返修中心数据
     *
     * <AUTHOR>
     */
    private void repushASMSData(Map<String, List<CustomerDataLogDTO>> originMap) throws Exception {
        if (CollectionUtils.isEmpty(originMap.get(Constant.SOURCE_SYSTEM_ASMS))) {
            return;
        }
        List<CustomerDataLogDTO> dataList = originMap.get(Constant.SOURCE_SYSTEM_ASMS);
        boardRepairService.boardRepairRetry(dataList);
    }

    /**
     * 新增日志信息
     *
     * <AUTHOR>
     */
    @Override
    public int insertCustomerDataLogFromMES(CustomerDataLogDTO param) {
        return customerDataLogRepository.insertCustomerDataLogFromMES(param);
    }

    @Override
    public void insertEntity(CustomerDataLogDTO dto) {
        customerDataLogRepository.insertEntity(dto);
    }

    @Override
    public void batchInsert(List<CustomerDataLogDTO> dtoList) {
        customerDataLogRepository.batchInsert(dtoList);
    }

    @Override
    public void updateAfterPushDataFail(String msg, String id) {
        customerDataLogRepository.updateStatusAndErrMsgById(msg, Constant.PUSH_B2B_STATUS.PN, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleResultOfPushDataToB2B(B2bCallBackDTO b2bCallBackDTO) {
        CustomerDataLogDTO customerDataLog = customerDataLogRepository.getPushedDataById(b2bCallBackDTO.getId());
        if(customerDataLog == null){
            return;
        }
        //阿里消息特殊处理
        this.alibabaMessageSpecialHandling(b2bCallBackDTO, customerDataLog);
        if (Constant.STR_SUCCESS.equals(b2bCallBackDTO.getResult())) {
            customerDataLogRepository.updateStatusAndErrMsgById
                    (Constant.STRING_EMPTY, Constant.PUSH_B2B_STATUS.CY, b2bCallBackDTO.getId());
            // 如果该条数据的TID不为空，则要将ID为该TID的最上层数据更新为CY，
            customerDataLogRepository.updateParentStatusById
                    (Constant.PUSH_B2B_STATUS.CY, b2bCallBackDTO.getId());
            customerDataLog.setStatus(Constant.PUSH_B2B_STATUS.CY);
        } else {
            AlarmHelper.alarm("imes_dependent_service_error", "1001", AlarmSeverityEnum.CRITICAL, Constant.B2B_ALARM_TITLE, JSON.toJSONString(b2bCallBackDTO.getFailMsg()));
            customerDataLogRepository.updateStatusAndErrMsgById
                    (b2bCallBackDTO.getFailMsg(), Constant.PUSH_B2B_STATUS.CN, b2bCallBackDTO.getId());
            customerDataLog.setStatus(Constant.PUSH_B2B_STATUS.CN);
        }
        // MES要求：校验是否条码或样机数据上传，如果是则更新MES系统中相关表中字段状态
        List<String> projectPhaseMES = Arrays.asList(Constant.PROJECT_PHASE_MES);
        String projectPhase = customerDataLog.getProjectPhase();
        if (StringUtils.isNotBlank(projectPhase) && projectPhaseMES.contains(projectPhase)) {
            if (StringUtils.isNotBlank(b2bCallBackDTO.getFailMsg())) {
                logger.error("CSR上传B2B回调返回错误信息：" + b2bCallBackDTO.getFailMsg());
            }
            // 更新MES系统中CSR状态
            datawbRemoteService.updateCSRStatusFromMES(customerDataLog);
        }
        customerDataLog.setFileData(getFileData(b2bCallBackDTO.getFailMsg()));
        // 更新业务系统日志表
        updateLog(customerDataLog);
    }

    private void alibabaMessageSpecialHandling(B2bCallBackDTO b2bCallBack, CustomerDataLogDTO customerDataLogDTO) {
        if(StringUtils.equals(customerDataLogDTO.getMessageType(),Constant.ZTEIMES_ALIBABA_MCTSERVERPRODUCTINFO)){
            AliFailMsgDTO aliFailMsg = JacksonJsonConverUtil.jsonToBean(b2bCallBack.getFailMsg(), AliFailMsgDTO.class);
            b2bCallBack.setResult(Constant.FAILED);
            this.setFailMsgAndResult(b2bCallBack, aliFailMsg);
        }
    }

    private void setFailMsgAndResult(B2bCallBackDTO b2bCallBack, AliFailMsgDTO aliFailMsg) {
        ALiResultDTO result = aliFailMsg == null || aliFailMsg.getResult() == null? null: aliFailMsg.getResult();
        if(result == null){
            b2bCallBack.setFailMsg(b2bCallBack.getFailMsg());
        }else if(result.isSuccess()){
            b2bCallBack.setResult(Constant.STR_SUCCESS);
        }else{
            b2bCallBack.setFailMsg(result.getErrMsg());
        }
    }

    /**
     * @return
     * <AUTHOR>
     * 获取腾讯文档标识
     * @Date 2025/2/24 13:42
     * @Param [java.lang.String]
     **/
    private String getFileData(String failMsg) {
        if (StringUtils.isEmpty(failMsg)) {
            return STRING_EMPTY;
        }
        try {
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(failMsg);
            JsonNode data = json.get(STR_DATA);
            return data == null ? STRING_EMPTY : data.toString().replaceAll("\"", "");
        } catch (Exception e) {
            return STRING_EMPTY;
        }
    }

    /* Started by AICoder, pid:2503cb5868c345f291c84bbcd3fbd83b */

    /**
     * 更新业务系统日志表
     */
    public void updateLog(CustomerDataLogDTO customerDataLogDTO) {

        if (StringUtils.equals(Constant.PUSH_B2B_STATUS.CY, customerDataLogDTO.getStatus())) {
            updateSuccessLog(customerDataLogDTO);
        } else {
            updateFailedLog(customerDataLogDTO);
        }
    }
    /* Ended by AICoder, pid:2503cb5868c345f291c84bbcd3fbd83b */

    public void updateSuccessLog(CustomerDataLogDTO customerDataLogDTO) {
        // 更新MES系统中美团整机生产质量数据回传记录表的推送状态，避免重复推送
        if (Constant.MESSAGE_TYPE_B2B_MEITUAN.equals(customerDataLogDTO.getMessageType())) {
            datawbRemoteService.updateOverAllUnitMeiTuanFromMES(customerDataLogDTO);
            return;
        }
        // 更新MES系统中美团测试文件上传日志表的推送状态，避免重复推送
        if (Constant.MEITUAN_TEST_FILE_BYTEDANCE.equals(customerDataLogDTO.getMessageType())) {
            datawbRemoteService.updateFileLogMeiTuanFromMES(customerDataLogDTO);
            return;
        }
        // 更新INFOR系统中B2B回传日志表的推送状态
        if (Constant.INFOR_WMS.equals(customerDataLogDTO.getOrigin())) {
            inforDatawbRemoteService.updateStockUploadLog(customerDataLogDTO);
            return;
        }
        // 更新MES系统中B2B回传日志表的推送状态
        if (Constant.MES.equals(customerDataLogDTO.getOrigin())) {
            SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
            sysLookupValuesDTO.setLookupType(Constant.LOOKUP_CODE_8240063);
            List<SysLookupValuesDTO> sysLookupValuesDTOList = datawbRemoteService.getSysLookupValuesList(sysLookupValuesDTO);
            if (CollectionUtils.isEmpty(sysLookupValuesDTOList)) {
                return;
            }
            List<SysLookupValuesDTO> sysLookupValuesDTOS = sysLookupValuesDTOList.stream().filter(i ->
                    customerDataLogDTO.getMessageType().equals(i.getDescription())
                            && i.getSortSeq().toString().equals(STRING_ONE)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sysLookupValuesDTOS)) {
                return;
            }
            datawbRemoteService.updateMesInfoUploadLog(customerDataLogDTO);
        }
    }

    public void updateFailedLog(CustomerDataLogDTO customerDataLogDTO) {
        // 更新MES系统中B2B回传日志表的推送状态
        if (Constant.MES.equals(customerDataLogDTO.getOrigin())) {
            SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
            sysLookupValuesDTO.setLookupType(Constant.LOOKUP_CODE_8240063);
            List<SysLookupValuesDTO> sysLookupValuesDTOList = datawbRemoteService.getSysLookupValuesList(sysLookupValuesDTO);
            if (CollectionUtils.isEmpty(sysLookupValuesDTOList)) {
                return;
            }
            List<SysLookupValuesDTO> sysLookupValuesDTOS = sysLookupValuesDTOList.stream().filter(i ->
                    customerDataLogDTO.getMessageType().equals(i.getDescription())
                            && i.getSortSeq().toString().equals(STRING_ONE)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sysLookupValuesDTOS)) {
                return;
            }
            datawbRemoteService.updateMesInfoUploadFailedLog(customerDataLogDTO);
        }
    }

    @Override
    public void pushDataToB2B(List<CustomerDataLogDTO> dataList) throws Exception {
        DataPusher retryUtil = new DataPusher(pushDataToB2B);
        List<List<CustomerDataLogDTO>> lists = CommonUtils.splitList(dataList, Constant.BATCH_SIZE);
        for (List<CustomerDataLogDTO> list : lists) {
            retryUtil.pushData(list);
        }
    }

    @Override
    public List<ByteDanceDTO> realTimeInteractiveB2B(CustomerQualityDTO dto) throws Exception {
        List<ByteDanceDTO> byteDanceDTOS = new ArrayList<>();
        // 字节质量码
        if (ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE.equals(dto.getMessageType())) {
            DataPusher retryUtil = new DataPusher(interactiveB2B);
            Map<String, List<String>> params = new HashMap<>(16);
            params.put(Constant.SERVER_SNS, dto.getServerSnList());
            dto.setJsonData(JSON.toJSONString(params));
            retryUtil.pushData(dto);
            String result = retryUtil.getResult();
            byteDanceDTOS = JSONArray.parseArray(result, ByteDanceDTO.class);
        }
        // 美团质量码
        if (ZTEIMES_MEI_TUAN_QUALITYCODE.equals(dto.getMessageType())) {
            DataPusher retryUtil = new DataPusher(interactiveB2B);
            List<String> serverSnList = dto.getServerSnList();
            for (String sn : serverSnList) {
                Map<String, Object> params = new HashMap<>(16);
                params.put(Constant.SN, sn);
                params.put(Constant.TYPE, NUM_ONE);
                dto.setJsonData(JSON.toJSONString(params));
                retryUtil.pushData(dto);
                String result = retryUtil.getResult();
                MeiTuanQualityDTO meiTuanQualityDTO = JSONObject.parseObject(result, MeiTuanQualityDTO.class);
                MeiTuanQualityDataDTO meiTuanQualityDataDTO = meiTuanQualityDTO.getData();
                if (null != meiTuanQualityDataDTO) {
                    ByteDanceDTO byteDanceDTO = new ByteDanceDTO();
                    byteDanceDTO.setServerSn(sn);
                    byteDanceDTO.setQualityCode(meiTuanQualityDataDTO.getQualityCode());
                    byteDanceDTO.setErrCause(meiTuanQualityDataDTO.getMsg());
                    byteDanceDTOS.add(byteDanceDTO);
                }
            }
        }

        // 腾讯正向数据和MPT测试日志比对
        interactiveTencent(dto, byteDanceDTOS);

        //阿里质量码
        interactiveAliBaba(dto, byteDanceDTOS);

        return byteDanceDTOS;
    }

    public void interactiveTencent(CustomerQualityDTO dto, List<ByteDanceDTO> byteDanceDTOS) throws Exception {
        // 腾讯正向数据比对
        if (ZTE_IMES_TENCENT_FORWARD_QUERY.equals(dto.getMessageType())) {
            DataPusher retryUtil = new DataPusher(interactiveTencentB2B);
            TencentForwardInDTO tencentForwardInDTO = new TencentForwardInDTO();
            List<Map<String, String>> snMapList = new ArrayList<>();
            for (String sn : dto.getServerSnList()) {
                Map<String, String> map = new HashMap<>(16);
                map.put(SVR_SN, sn);
                snMapList.add(map);
            }
            tencentForwardInDTO.setAction(SUPPLIER_QUERY_COMPARE_RESULT);
            tencentForwardInDTO.setMethod(RUN);
            tencentForwardInDTO.setStartCompany(ZTE);
            tencentForwardInDTO.setData(snMapList);
            dto.setJsonData(JSON.toJSONString(tencentForwardInDTO));
            retryUtil.pushData(dto);
            String result = retryUtil.getResult();
            getTencentData(byteDanceDTOS, result, dto.getMessageType());
        }
        // 腾讯MPT测试日志比对
        if (ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA.equals(dto.getMessageType())) {
            DataPusher retryUtil = new DataPusher(interactiveTencentB2B);
            Map<String, List<String>> params = new HashMap<>(16);
            params.put(Constant.SERVER_SN_LIST, dto.getServerSnList());
            dto.setJsonData(JSON.toJSONString(params));
            retryUtil.pushData(dto);
            String result = retryUtil.getResult();
            getTencentData(byteDanceDTOS, result, dto.getMessageType());
        }
    }

    public void getTencentData(List<ByteDanceDTO> byteDanceDTOS, String result, String messageType) {
        List<TencentForwardOutDataDTO> tencentForwardOutDataDTOList = JacksonJsonConverUtil.jsonToListBean(result, new TypeReference<List<TencentForwardOutDataDTO>>() {
        });
        for (TencentForwardOutDataDTO tencentForwardOutDataDTO : tencentForwardOutDataDTOList) {
            ByteDanceDTO byteDanceDTO = new ByteDanceDTO();
            if (ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA.equals(messageType)) {
                byteDanceDTO.setServerSn(tencentForwardOutDataDTO.getServerSN());
                byteDanceDTO.setQualityCode(tencentForwardOutDataDTO.getMptResult());
                byteDanceDTO.setErrCause(tencentForwardOutDataDTO.getMptResultDesc());
            }
            if (ZTE_IMES_TENCENT_FORWARD_QUERY.equals(messageType)) {
                byteDanceDTO.setServerSn(tencentForwardOutDataDTO.getSvrSN());
                byteDanceDTO.setQualityCode(tencentForwardOutDataDTO.getResult());
                byteDanceDTO.setErrCause(tencentForwardOutDataDTO.getMessage());
            }
            byteDanceDTOS.add(byteDanceDTO);
        }
    }

    public void interactiveAliBaba(CustomerQualityDTO dto, List<ByteDanceDTO> byteDanceDTOS) throws Exception {
        if (ZTEIMES_ALIBABA_QUALITYCODE.equals(dto.getMessageType())) {
            DataPusher retryUtil = new DataPusher(interactiveB2B);
            List<String> serverSnList = dto.getServerSnList();
            for (String sn : serverSnList) {
                Map<String, Object> params = new HashMap<>(16);
                params.put(Constant.SN, sn);
                params.put(Constant.TYPE, ZTEIMES_ALIBABA_TYPE);
                Map<String, Object> data = new HashMap<>(16);
                data.put(STR_DATA, params);
                dto.setJsonData(JSON.toJSONString(data));
                retryUtil.pushData(dto);
                String result = retryUtil.getResult();
                getAliBabaData(byteDanceDTOS, result, sn);
            }
        }
    }

    public void getAliBabaData(List<ByteDanceDTO> byteDanceDTOS, String result, String sn) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(result);
            JsonNode dataNode = rootNode.get("result");
            if (dataNode != null) {
                JsonNode resultData = dataNode.get("data");
                AliBabaQualityDataDTO aLiBabaQualityDTO = objectMapper.treeToValue(resultData, AliBabaQualityDataDTO.class);
                if(aLiBabaQualityDTO!=null) {
                    ByteDanceDTO byteDanceDTO = new ByteDanceDTO();
                    byteDanceDTO.setServerSn(sn);
                    byteDanceDTO.setQualityCode(aLiBabaQualityDTO.getQualityCode());
                    if (StringUtils.isNotEmpty(aLiBabaQualityDTO.getFailCode())) {
                        byteDanceDTO.setErrCause(aLiBabaQualityDTO.getFailCode() + SEMICOLON + aLiBabaQualityDTO.getMsg());
                    }
                    byteDanceDTOS.add(byteDanceDTO);
                }
            } else {
                ByteDanceDTO byteDanceDTO = new ByteDanceDTO();
                byteDanceDTO.setServerSn(sn);
                byteDanceDTO.setErrCause(rootNode.get("msg").asText());
                byteDanceDTOS.add(byteDanceDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<String> getPushedSnScanData(List<String> snList) {
        List<String> pushedSn = new ArrayList();
        if (CollectionUtils.isEmpty(snList)) {
            return pushedSn;
        }
        List<List<String>> lists = CommonUtils.splitList(snList, Constant.BATCH_SIZE);
        for (List<String> subList : lists) {
            pushedSn.addAll(customerDataLogRepository.getPushedSnScanData(subList));
        }
        return pushedSn;
    }

    @Override
    public List<CustomerDataLogDTO> getPushedTestProcessSnScanData(List<CustomerDataLogDTO> customerDataLogDTOList) {
        List<CustomerDataLogDTO> pushedSn = new ArrayList();
        if (CollectionUtils.isEmpty(customerDataLogDTOList)) {
            return pushedSn;
        }
        List<List<CustomerDataLogDTO>> lists = CommonUtils.splitList(customerDataLogDTOList, Constant.BATCH_SIZE);
        for (List<CustomerDataLogDTO> subList : lists) {
            pushedSn.addAll(customerDataLogRepository.getPushedTestProcessSnScanData(subList, Constant.MessageType.TEST_FEEDBACK));
        }
        return pushedSn;
    }

    @Override
    public List<String> getPushedSnScanDataOfL6(List<String> snList) {
        List<String> pushedSn = new ArrayList();
        if (CollectionUtils.isEmpty(snList)) {
            return pushedSn;
        }
        List<List<String>> lists = CommonUtils.splitList(snList, Constant.BATCH_SIZE);
        for (List<String> subList : lists) {
            pushedSn.addAll(customerDataLogRepository.getPushedSnScanDataOfL6(subList));
        }
        return pushedSn;
    }
}
