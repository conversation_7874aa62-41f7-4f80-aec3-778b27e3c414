package com.zte.application.impl;

import com.zte.application.AoiFileService;
import com.zte.application.IMESLogService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.AoiFile;
import com.zte.domain.model.AoiFileRepository;
import com.zte.interfaces.dto.LocRangeDTO;
import com.zte.interfaces.dto.SpcDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.km.udm.common.util.MD5Util;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.CommonUtils;
import com.zte.springbootframe.util.FileUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/* Ended by AICoder, pid:98cd031f33811a5144360b9fe027a40b27874224 */

/**
 * <AUTHOR>
 * @Date 2024/11/4 17:25
 */
@Service
public class AoiFileServiceImpl implements AoiFileService {

    @Autowired
    private AoiFileRepository repository;

    @Autowired
    private CloudDiskHelper cloudDiskHelper;

    @Autowired
    private IMESLogService imesLogService;

    @Override
    public AoiFile getAoiFileInfoByBomAndType(String bomNo, String type) {
        return repository.getAoiFileInfoByBomAndType(bomNo, type);
    }

    /**
     *<AUTHOR>
     * 单板位号文件上传
     *@Date 2024/11/4 17:29
     *@Param [java.lang.String, java.lang.String, java.lang.String, org.springframework.web.multipart.MultipartFile]
     *@return
     **/
    @Override
    public void uploadAoiFile(String bomNo, String type, String empNo, MultipartFile file) throws Exception {
        String fileName = this.checkUploadParam(bomNo, type, file);
        // 获取文件上传记录
        AoiFile aoiFile = repository.getAoiFileInfoByBomAndType(bomNo, type);
        // 获取上传文档md5值
        String hexMd5ByBytes = DigestUtils.sha256Hex(file.getBytes());
        // 未获取到时新增数据
        if (aoiFile == null) {
            aoiFile = new AoiFile();
            aoiFile.setFileName(fileName);
            aoiFile.setCreateBy(empNo);
            aoiFile.setLastUpdateBy(empNo);
            aoiFile.setBomNo(bomNo);
            aoiFile.setType(type);
            aoiFile.setMd5Code(hexMd5ByBytes);
            aoiFile.setFileSize(file.getSize());
            // 上传文档云
            String docId = cloudDiskHelper.fileUpload(file, empNo);
            aoiFile.setDocId(docId);
            repository.insertAoiFile(aoiFile);
            return;
        }
        // 文件无变动不处理
        if (StringUtils.equals(aoiFile.getMd5Code(), hexMd5ByBytes)) {
            return;
        }
        String sourceDocId = aoiFile.getDocId();
        // 上传文档云
        String docId = cloudDiskHelper.fileUpload(file, empNo);
        aoiFile.setFileName(fileName);
        aoiFile.setLastUpdateBy(empNo);
        aoiFile.setDocId(docId);
        aoiFile.setMd5Code(hexMd5ByBytes);
        aoiFile.setFileSize(file.getSize());
        // 更新上传记录
        repository.updateAoiFileById(aoiFile);
        try {
            // 根据docId删除文档
            cloudDiskHelper.deleteFile(sourceDocId, empNo);
        } catch (Exception e) {
            imesLogService.log(sourceDocId,"AoiFileDeleteFailed" + e.getMessage());
        }
    }

    /**
     *<AUTHOR>
     * 入参校验
     *@Date 2024/11/5 15:59
     *@Param [java.lang.String, java.lang.String, org.springframework.web.multipart.MultipartFile]
     *@return
     **/
    private String checkUploadParam(String bomNo, String type, MultipartFile file) {
        if (StringUtils.isEmpty(bomNo)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BOM_CODE_CAN_NOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(type)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMER_TYPE_NULL);
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NETWORK_LICENSE_FILE_NAME_ERROR);
        }
        String ext = fileName.substring(fileName.lastIndexOf('.') + MpConstant.INT_ONE);
        if (Constant.AOI_TYPE_LIST.contains(type) && !Constant.CSV.equalsIgnoreCase(ext)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FILE_FORMAT);
        }
        return fileName;
    }

    /**
     *<AUTHOR>
     * 获取位号范围
     *@Date 2024/11/6 9:15
     *@Param [java.lang.String, java.lang.String, java.util.List<java.lang.String>]
     *@return
     **/
    @Override
    public SpcDTO getTagCoordinates(String bomNo, String type, List<String> refNameList) throws Exception {
        // 未传入位号时不处理
        if (CollectionUtils.isEmpty(refNameList)){
            return new SpcDTO();
        }
        // 获取文件上传记录
        AoiFile aoiFile = repository.getAoiFileInfoByBomAndType(bomNo, type);
        if (aoiFile == null || !Constant.AOI_TYPE_LIST.contains(aoiFile.getType())) {
            return new SpcDTO();
        }
        // 文档云下载文档
        String filePath = FileUtils.createFilePathAndCheck(aoiFile.getFileName());
        cloudDiskHelper.downloadFile(filePath, aoiFile.getDocId(), aoiFile.getCreateBy());
        File file = new File(filePath);
        // 将File转换为MultipartFile
        MultipartFile multipartFile = convertMultipartFile(file);
        /* Started by AICoder, pid:v59b4e6667cbec01495e09e910c8b01e9e491d15 */
        // 将csv文件转换
        List<String[]> strings = this.transFile(multipartFile);
        SpcDTO spc = new SpcDTO();
        /* Ended by AICoder, pid:v59b4e6667cbec01495e09e910c8b01e9e491d15 */
        // 设置header数据
        this.setHeadData(spc, strings);
        LocRangeDTO locRangeDTO = new LocRangeDTO();
        // 获取设置个参数索引
        this.setDetailIndex(locRangeDTO, strings);
        // 设置位号坐标范围
        spc.setLocRangeList(this.getDetailData(locRangeDTO, strings, refNameList));
        FileUtils.deleteFileWithParentDic(filePath);
        return spc;
    }

    /* Started by AICoder, pid:b8cd0q1f33q11a5144360b9fe027a45b27874224 */
    private MultipartFile convertMultipartFile(File file) throws IOException {
        DiskFileItemFactory disk = new DiskFileItemFactory();
        String name = file.getName();
        FileItem fileItem = disk.createItem(Constant.FILE, Constant.CONTENT_TYPE, true, name);
        try (FileInputStream input = new FileInputStream(file);
             OutputStream os = fileItem.getOutputStream()) {
            IOUtils.copy(input, os);
        }
        return new CommonsMultipartFile(fileItem);
    }

    /**
     *<AUTHOR>
     * csv文件转换
     *@Date 2024/11/5 14:04
     *@Param [org.springframework.web.multipart.MultipartFile]
     *@return
     **/
    private List<String[]> transFile(MultipartFile file) throws Exception {
        /* Started by AICoder, pid:9d837c0b2c7edba14d3d083570fa0a0524e4b479 */
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            return reader.lines()
                    .map(line -> line.split(","))
                    .collect(Collectors.toList());
        }
        /* Ended by AICoder, pid:9d837c0b2c7edba14d3d083570fa0a0524e4b479 */
    }

    /**
     *<AUTHOR>
     * 获取头部表单起始行
     *@Date 2024/11/5 9:51
     *@Param [com.zte.interfaces.dto.SpcDTO, org.apache.poi.ss.usermodel.Sheet]
     *@return
     **/
    private void setHeadData(SpcDTO spc, List<String[]> strings) {
        for (int i = 0; i < strings.size(); i++) {
            List<String> list = Arrays.asList(strings.get(i));
            if (list.contains(Constant.BOARD_SIZE_X)) {
                int idx = list.indexOf(Constant.BOARD_SIZE_X);
                spc.setBoardSizeX(Float.parseFloat(Arrays.asList(strings.get(i + 1)).get(idx)));
            }
            if (list.contains(Constant.BOARD_SIZE_Y)) {
                int idx = list.indexOf(Constant.BOARD_SIZE_Y);
                spc.setBoardSizeY(Float.parseFloat(Arrays.asList(strings.get(i + 1)).get(idx)));
            }
            if (spc.getBoardSizeX() != null && spc.getBoardSizeY() != null)
            {
                return;
            }
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FILE_PARSING_ERROR);
    }

    /**
     *<AUTHOR>
     * 获取详情表单起始行
     *@Date 2024/11/5 9:50
     *@Param [com.zte.interfaces.dto.LocRangeDTO, org.apache.poi.ss.usermodel.Sheet]
     *@return
     **/
    private void setDetailIndex(LocRangeDTO loc, List<String[]> strings) {
        for (int i = 0; i < strings.size(); i++) {
            List<String> list = Arrays.asList(strings.get(i));
            if (list.contains(Constant.REF_NAME)) {
                loc.setRefNameIdx(list.indexOf(Constant.REF_NAME));
            }
            if (list.contains(Constant.STEP_CENTER_X)) {
                loc.setStepCenterXIdx(list.indexOf(Constant.STEP_CENTER_X));
            }
            if (list.contains(Constant.STEP_CENTER_Y)) {
                loc.setStepCenterYIdx(list.indexOf(Constant.STEP_CENTER_Y));
            }
            if (list.contains(Constant.STEP_SIZE_X)) {
                loc.setStepSizeXIdx(list.indexOf(Constant.STEP_SIZE_X));
            }
            if (list.contains(Constant.STEP_SIZE_Y)) {
                loc.setStepSizeYIdx(list.indexOf(Constant.STEP_SIZE_Y));
            }
            if (!locRangeEmptyCheck(loc)) {
                loc.setStartRowNum(i);
                return;
            }
        }
        throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FILE_PARSING_ERROR);
    }

    /**
     *<AUTHOR>
     * 判断详情表单需求字段是否为空
     *@Date 2024/11/5 9:52
     *@Param [com.zte.interfaces.dto.LocRangeDTO]
     *@return
     **/
    private boolean locRangeEmptyCheck(LocRangeDTO loc) {
        return loc.getRefNameIdx() == null || loc.getStepCenterXIdx() == null
                || loc.getStepCenterYIdx() == null || loc.getStepSizeXIdx() == null || loc.getStepSizeYIdx() == null;
    }

    /**
     *<AUTHOR>
     * 获取详情表单数据
     *@Date 2024/11/5 10:10
     *@Param [com.zte.interfaces.dto.LocRangeDTO, org.apache.poi.ss.usermodel.Sheet]
     *@return
     **/
    private List<LocRangeDTO> getDetailData(LocRangeDTO dto, List<String[]> strings, List<String> refNameList) {
        List<LocRangeDTO> list = new ArrayList<>();
        for (int i = dto.getStartRowNum() + 1; i < strings.size(); i++) {
            LocRangeDTO loc = new LocRangeDTO();
            List<String> list1 = Arrays.asList(strings.get(i));
            if (!refNameList.contains(list1.get(dto.getRefNameIdx()))){
                continue;
            }
            loc.setRefName(list1.get(dto.getRefNameIdx()));
            loc.setStepCenterX(Float.parseFloat(list1.get(dto.getStepCenterXIdx())));
            loc.setStepCenterY(Float.parseFloat(list1.get(dto.getStepCenterYIdx())));
            loc.setStepSizeX(Float.parseFloat(list1.get(dto.getStepSizeXIdx())));
            loc.setStepSizeY(Float.parseFloat(list1.get(dto.getStepSizeYIdx())));
            list.add(loc);
        }
        if (CollectionUtils.isEmpty(list)){
            return list;
        }
        /* Started by AICoder, pid:fd149bc673hde71140540b112098d313d6744704 */
        Map<String, LocRangeDTO> result = list.stream()
                .collect(Collectors.groupingBy(
                        // 分组依据
                        LocRangeDTO::getRefName,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list1 ->
                                        // 先根据stepSizeX降序排序
                                        list1.stream().sorted(Comparator.comparing(LocRangeDTO::getStepSizeX).reversed())
                                                // 取根据stepSizeY升序排序后的最后一个元素
                                                .max(Comparator.comparing(LocRangeDTO::getStepSizeY))
                                        // 如果没有找到任何元素，则返回null
                                        .orElse(null)
                        )
                ))
                .entrySet().stream()
                // 过滤掉没有找到任何元素的分组
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return new ArrayList<>(result.values());
    }
    /* Ended by AICoder, pid:b8cd0q1f33q11a5144360b9fe027a45b27874224 */
}
