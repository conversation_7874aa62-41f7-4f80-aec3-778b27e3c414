package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.CustomerDataLogService;
import com.zte.application.PushModelSnTestRecordService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushModelSnTestRecord;
import com.zte.domain.model.PushModelSnTestRecordRepository;
import com.zte.domain.model.PushStdModelSnData;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordPageQueryDTO;
import com.zte.interfaces.dto.PushStationAnalysisResultsToAlibabaDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.NUM_ONE;
import static com.zte.common.utils.NumConstant.NUM_ZERO;

/**
 * 标模任务条码推送测试记录表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-15 08:53:49
 */
@Service("pushModelSnTestRecordService")
public class PushModelSnTestRecordServiceImpl implements PushModelSnTestRecordService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private PushModelSnTestRecordRepository pushModelSnTestRecordRepository;
    @Autowired
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private MdsRemoteService mdsRemoteService;
    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private CustomerDataLogService customerDataLogService;

    @Autowired
    private IdGenerator idGenerator;


    @Value("${push.std.model.sn.data.pageSize:5000}")
    private Integer pageSize;

    @Value("${push.std.model.sn.data.preDays:180}")
    private Integer preDays;

    @Override
    public PushModelSnTestRecord getById(String requestId) {
        return pushModelSnTestRecordRepository.selectById(requestId);
    }

    @Override
    public void add(PushModelSnTestRecord pushModelSnTestRecord) {
        pushModelSnTestRecord.setRequestId(UUID.randomUUID().toString());

        pushModelSnTestRecord.setCreateBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        pushModelSnTestRecordRepository.insert(pushModelSnTestRecord);
    }

    @Override
    public void deleteByIds(List<String> requestIds) {
        pushModelSnTestRecordRepository.deleteByIds(requestIds);
    }

    @Override
    public void processingCompleteMachineTestData(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("processingCompleteMachineTestData,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        if (StringUtils.isBlank(b2bCallBackNewDTO.getKeywords())) {
            return;
        }
        PushModelSnTestRecord pushModelSnTestRecord = new PushModelSnTestRecord();
        pushModelSnTestRecord.setRequestId(b2bCallBackNewDTO.getKeywords());
        pushModelSnTestRecord.setUploadStatus(StringUtils.equals(b2bCallBackNewDTO.getCode(),RetCode.SUCCESS_CODE) ? NumConstant.STRING_ONE : NumConstant.STRING_TWO);
        pushModelSnTestRecord.setUploadMsg(this.getMsg(b2bCallBackNewDTO));
        pushModelSnTestRecordRepository.updateSelectiveById(pushModelSnTestRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processingStationAnalysisResults(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("processingStationAnalysisResults,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        String keywords = b2bCallBackNewDTO.getKeywords();
        if (StringUtils.isBlank(keywords)) {
            return;
        }
        PushModelSnTestRecord pushModelSnTestRecord = new PushModelSnTestRecord();
        pushModelSnTestRecord.setRequestId(keywords);
        pushModelSnTestRecord.setStationUploadStatus(StringUtils.equals(b2bCallBackNewDTO.getCode(),RetCode.SUCCESS_CODE) ? NumConstant.STRING_ONE : NumConstant.STRING_TWO);
        pushModelSnTestRecord.setStationUploadMsg(this.getMsg(b2bCallBackNewDTO));
        pushModelSnTestRecordRepository.updateSelectiveById(pushModelSnTestRecord);
        this.hasTheBarcodeTestBeenCompleted(keywords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOSSFileUpload(B2bCallBackNewDTO b2bCallBackNewDTO) {
        logger.info("processOSSFileUpload,data:{}", JSON.toJSON(b2bCallBackNewDTO));
        String keywords = b2bCallBackNewDTO.getKeywords();
        if (StringUtils.isBlank(keywords)) {
            return;
        }
        PushModelSnTestRecord pushModelSnTestRecord = new PushModelSnTestRecord();
        pushModelSnTestRecord.setRequestId(keywords);
        pushModelSnTestRecord.setFileUploadStatus(StringUtils.equals(b2bCallBackNewDTO.getCode(),RetCode.SUCCESS_CODE) ? NumConstant.STRING_ONE : NumConstant.STRING_TWO);
        pushModelSnTestRecord.setFileUploadMsg(this.getMsg(b2bCallBackNewDTO));
        pushModelSnTestRecordRepository.updateSelectiveById(pushModelSnTestRecord);
        this.hasTheBarcodeTestBeenCompleted(keywords);
    }

    /**
     * 条码关键节点测试完成并推送成功，则更新条码状态
     *
     * @param id
     */
    private void hasTheBarcodeTestBeenCompleted(String id) {
        PushModelSnTestRecord pushModelSnTestRecord = pushModelSnTestRecordRepository.selectById(id);
        if (pushModelSnTestRecord == null) {
            return;
        }
        Map<String, String> sysMap = this.getSysMap(Constant.LOOKUP_VALUE_6746);

        List<PushModelSnTestRecord> pushModelSnTestRecordList = pushModelSnTestRecordRepository.selectBySn(pushModelSnTestRecord.getSn());
        if(CollectionUtils.isEmpty(pushModelSnTestRecordList)){
            return;
        }

        Set<String> set = new HashSet<>(sysMap.values());

        Map<String, List<PushModelSnTestRecord>> pushStdMap = pushModelSnTestRecordList.stream().collect(Collectors.groupingBy(PushModelSnTestRecord::getStationName));
        boolean flag = checkIfBarcodeUploadIsComplete(set, pushStdMap);
        this.updateCurrProcessBySnAndTaskNo(flag, pushModelSnTestRecord);
    }

    private boolean checkIfBarcodeUploadIsComplete(Set<String> set, Map<String, List<PushModelSnTestRecord>> pushStdMap) {
        for (String name : set) {
            List<PushModelSnTestRecord> pushModelSnTestRecords = pushStdMap.get(name);
            if(CollectionUtils.isEmpty(pushModelSnTestRecords)){
                return false;
            }
            PushModelSnTestRecord uploadRecord = pushModelSnTestRecords.stream().filter(e ->
                    StringUtils.equals(NumConstant.STRING_ONE, e.getUploadStatus())
                    && StringUtils.equals(NumConstant.STRING_ONE, e.getFileUploadStatus())
                    && (StringUtils.equals(NumConstant.STRING_ONE, e.getStationUploadStatus()) || StringUtils.equals(e.getResult(), Constant.ALi.PASS))).findFirst().orElse(null);
            if (uploadRecord == null) {
                return false;
            }
        }
        return true;
    }


    private String getMsg(B2bCallBackNewDTO b2bCallBackNewDTO) {
        String msg = b2bCallBackNewDTO.getMsg();
        if (StringUtils.length(msg) > NumConstant.NUM_1000) {
            msg = msg.substring(NUM_ZERO, NumConstant.NUM_1000);
        }
        return msg;
    }

    @Override
    public Map<String, String> getSysMap(String lookUpType) {
        List<SysLookupValues> sysLookupValuesList = sysLookupValuesService.findByLookupType(lookUpType);
        if (CollectionUtils.isEmpty(sysLookupValuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{lookUpType});
        }
        Map<String, String> sysMap = sysLookupValuesList.stream().collect(
                Collectors.toMap(k -> k.getLookupMeaning(), v -> v.getAttribute1(), (oldValue, newValue) -> newValue));
        return sysMap;
    }

    @Override
    @RedisDistributedLockAnnotation(redisKey = "uploadTheResultsOfStationAnalysis", redisLockTime = 7200)
    public void uploadTheResultsOfStationAnalysis(List<String> list) throws Exception {
        PushModelSnTestRecordPageQueryDTO pushModelSnTestRecordPageQueryDTO = new PushModelSnTestRecordPageQueryDTO() {{
            setSnList(list);
            setPreDays(preDays);
            setResult(Constant.ALi.FAIL);
            setStationUploadStatus(Constant.STR_0);
        }};
        while (true) {
            List<PushModelSnTestRecord> pushModelSnTestRecordList = pushModelSnTestRecordRepository.queryUploadSuccessList(pushModelSnTestRecordPageQueryDTO);
            if (CollectionUtils.isEmpty(pushModelSnTestRecordList)) {
                break;
            }
            pushModelSnTestRecordPageQueryDTO.setLastUpdatedDate(pushModelSnTestRecordList.get(pushModelSnTestRecordList.size()- NUM_ONE).getLastUpdatedDate());
            pushModelSnTestRecordPageQueryDTO.setLastId(pushModelSnTestRecordList.get(pushModelSnTestRecordList.size()- NUM_ONE).getRequestId());
            List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>(pushModelSnTestRecordList.size());
            for (PushModelSnTestRecord pushModelSnTestRecord : pushModelSnTestRecordList) {
                PushStationAnalysisResultsToAlibabaDTO resultsToAlibabaDTO = new PushStationAnalysisResultsToAlibabaDTO();
                BeanUtils.copyProperties(pushModelSnTestRecord, resultsToAlibabaDTO);
                resultsToAlibabaDTO.setErrorCode(pushModelSnTestRecord.getActionCode());
                resultsToAlibabaDTO.setErrorMsg(pushModelSnTestRecord.getMessage());
                resultsToAlibabaDTO.setExternalSystemId(pushModelSnTestRecord.getWorkorderId());
                resultsToAlibabaDTO.setType(Constant.MCT_CAPTURE);
                resultsToAlibabaDTO.setSn(pushModelSnTestRecord.getNodeSn());

                CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
                customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
                customerDataLogDTO.setKeywords(pushModelSnTestRecord.getRequestId());
                customerDataLogDTO.setSn(pushModelSnTestRecord.getSn());
                customerDataLogDTO.setOrigin(Constant.IMES);
                customerDataLogDTO.setCustomerName(Constant.ALIBABA);
                customerDataLogDTO.setProjectName(Constant.ALIBABA);
                customerDataLogDTO.setProjectPhase(Constant.STATION_ANALYSIS_RESULTS);
                customerDataLogDTO.setPushType(Constant.PUSH_TYPE.KAFKA);
                customerDataLogDTO.setFactoryId(pushModelSnTestRecord.getFactoryId());
                customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_STATION_ANALYSIS_RESULTS);
                customerDataLogDTO.setTaskNo(pushModelSnTestRecord.getWorkorderId());
                customerDataLogDTO.setCreateBy(Constant.IMES);
                customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
                customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(resultsToAlibabaDTO));
                customerDataLogDTOList.add(customerDataLogDTO);
            }
            // 推送数据至B2B
            tradeDataLogService.pushDataOfExceptionRollback(customerDataLogDTOList);
        }
    }

    private void updateCurrProcessBySnAndTaskNo(boolean flag, PushModelSnTestRecord pushModelSnTestRecord) {
        if (flag) {
            PushStdModelSnData pushStdModelSnData = new PushStdModelSnData();
            pushStdModelSnData.setSn(pushModelSnTestRecord.getSn());
            pushStdModelSnData.setTaskNo(pushModelSnTestRecord.getWorkorderId());
            pushStdModelSnData.setCurrProcess(Constant.STR_25);
            pushStdModelSnData.setPushStatus(Constant.NUM_TWO);
            pushStdModelSnDataRepository.updateCurrProcessBySnAndTaskNo(pushStdModelSnData);
        }
    }

    @Override
    @RedisDistributedLockAnnotation(redisKey = "ossLogFileUpload", redisLockTime = 7200)
    public void ossLogFileUpload(List<String> list) throws Exception {
        PushModelSnTestRecordPageQueryDTO pushModelSnTestRecordPageQueryDTO = new PushModelSnTestRecordPageQueryDTO() {{
            setSnList(list);
            setPreDays(preDays);
            setFileUploadStatus(Constant.STR_0);
        }};
        while (true) {
            List<PushModelSnTestRecord> pushModelSnTestRecordList = pushModelSnTestRecordRepository.queryUploadSuccessList(pushModelSnTestRecordPageQueryDTO);
            if (CollectionUtils.isEmpty(pushModelSnTestRecordList)) {
                break;
            }
            pushModelSnTestRecordPageQueryDTO.setLastUpdatedDate(pushModelSnTestRecordList.get(pushModelSnTestRecordList.size()- NUM_ONE).getLastUpdatedDate());
            pushModelSnTestRecordPageQueryDTO.setLastId(pushModelSnTestRecordList.get(pushModelSnTestRecordList.size()- NUM_ONE).getRequestId());

            //按整机条码分组
            Map<String, List<PushModelSnTestRecord>> pushStdMap = pushModelSnTestRecordList.stream().collect(Collectors.groupingBy(PushModelSnTestRecord::getSn));
            List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = new ArrayList<>(pushModelSnTestRecordList.size());
            for (Map.Entry<String, List<PushModelSnTestRecord>> entry : pushStdMap.entrySet()) {
                List<String> stationIdList = entry.getValue().stream().filter(e -> StringUtils.isNotEmpty(e.getStationId())).map(e -> e.getStationId()).distinct().collect(Collectors.toList());
                List<MdsFeedbackProductionStationFileDTO> tempList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(entry.getKey(), stationIdList, true);
                stationFileDTOList.addAll(tempList);
            }
            Map<String, List<MdsFeedbackProductionStationFileDTO>> stationMap = stationFileDTOList.stream().collect(Collectors.groupingBy(MdsFeedbackProductionStationFileDTO::getStationId));

            this.pushData(pushModelSnTestRecordList, stationMap);
        }
    }

    private void pushData(List<PushModelSnTestRecord> pushModelSnTestRecordList, Map<String, List<MdsFeedbackProductionStationFileDTO>> stationMap) {
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>(pushModelSnTestRecordList.size());
        for (PushModelSnTestRecord pushModelSnTestRecord : pushModelSnTestRecordList) {
            String stationId = pushModelSnTestRecord.getStationId();
            List<MdsFeedbackProductionStationFileDTO> list = stationMap.get(stationId);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<MdsFeedbackProductionStationFileDTO> tempList = list.stream().filter(e->StringUtils.contains(e.getLogName(),pushModelSnTestRecord.getNodeSn())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempList)) {
                return;
            }
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
            customerDataLogDTO.setKeywords(pushModelSnTestRecord.getRequestId());
            customerDataLogDTO.setOrigin(Constant.IMES);
            customerDataLogDTO.setCustomerName(Constant.ALIBABA);
            customerDataLogDTO.setProjectName(Constant.ALIBABA);
            customerDataLogDTO.setProjectPhase(Constant.OSS_FILE_UPLOAD);
            customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_OSS_TEST_LOG_FILE);
            customerDataLogDTO.setPushType(Constant.PUSH_TYPE.KAFKA);
            customerDataLogDTO.setTaskNo(pushModelSnTestRecord.getWorkorderId());
            customerDataLogDTO.setSn(pushModelSnTestRecord.getSn());
            customerDataLogDTO.setFactoryId(pushModelSnTestRecord.getFactoryId());
            customerDataLogDTO.setCreateBy(Constant.IMES);
            customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
            customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(tempList.get(NUM_ZERO)));
            customerDataLogDTOList.add(customerDataLogDTO);
        }
        // 推送数据至B2B
        if(CollectionUtils.isNotEmpty(customerDataLogDTOList)){
            tradeDataLogService.pushData(customerDataLogDTOList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertOrUpdate(List<PushModelSnTestRecordDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (List<PushModelSnTestRecordDTO> tempList : CommonUtils.splitList(list, NumConstant.NUM_500)) {
            pushModelSnTestRecordRepository.batchInsertOrUpdate(tempList);
        }
    }

    @Override
    public List<PushModelSnTestRecord> getByIdList(List<String> stationIdList) {
        if(CollectionUtils.isEmpty(stationIdList)){
            return Collections.emptyList();
        }
        List<PushModelSnTestRecord> recordList = new ArrayList<>(stationIdList.size());
        for (List<String> tempIdList : CommonUtils.splitList(stationIdList, NumConstant.NUM_500)) {
            List<PushModelSnTestRecord> tempList = pushModelSnTestRecordRepository.selectByIdList(tempIdList);
            if(CollectionUtils.isNotEmpty(tempList)){
                recordList.addAll(tempList);
            }
        }
        return recordList;
    }
}
