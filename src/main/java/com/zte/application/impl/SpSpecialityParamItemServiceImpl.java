package com.zte.application.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TtlRunnable;
import com.zte.application.OfflineExportService;
import com.zte.application.SpSpecialityParamItemService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.common.utils.constant.RedisKeyConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.SpRecoverImportDTO;
import com.zte.interfaces.dto.SpRecoverResourceDTO;
import com.zte.interfaces.dto.SpSpecialityParamItemPageQueryDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.uac.ssoclient.util.MD5;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 个参详情表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-15 17:12:33
 */
@Slf4j
@Service("spSpecialityParamItemService")
public class SpSpecialityParamItemServiceImpl implements SpSpecialityParamItemService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private SpSpecialityParamItemRepository spSpecialityParamItemRepository;
    @Autowired
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Autowired
    private SpRecoveryRepository spRecoveryRepository;
    @Autowired
    private SpTemplateItemRepository spTemplateItemRepository;
    @Autowired
    private OfflineExportService offlineExportService;
    @Autowired
    private CloudDiskHelper cloudDiskHelper;
    @Autowired
    private MdsRemoteService mdsRemoteService;


    @Override
    public PageRows<SpSpecialityParamItem> queryPage(SpSpecialityParamItemPageQueryDTO query) {
        PageRows<SpSpecialityParamItem> pageRows = new PageRows<>();
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(spSpecialityParamItemRepository.countPage(query));
        if (pageRows.getTotal() > NumConstant.LONG_ZERO) {
            pageRows.setRows(spSpecialityParamItemRepository.queryPage(query));
        }
        return pageRows;
    }

    @Override
    public Long getMaxBarcode(String specialityParamId) {
        return spSpecialityParamItemRepository.selectMaxBarcode(specialityParamId);
    }

    @Override
    public void exportExcel(HttpServletResponse response, String specialityParamId) {
        SpSpecialityParamItemPageQueryDTO query = new SpSpecialityParamItemPageQueryDTO();
        query.setSpecialityParamId(specialityParamId);
        query.setRows(NumConstant.NUM_1000);
        long totalRows = spSpecialityParamItemRepository.countPage(query);
        if (totalRows <= NumConstant.LONG_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(Constant.X_REAL_EMP_NO.toLowerCase());
        RedisLock lock = new RedisLock(String.format(RedisKeyConstant.SPECIALITY_PARAM_EXPORT, empNo + specialityParamId), NumConstant.NUM_2000);
        boolean isLock = lock.lock();
        if (!isLock) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GENERATING_OFFLINE_EXPORT_EXCEL);
        }

        OfflineExport offlineExport = new OfflineExport();
        offlineExport.setId(UUID.randomUUID().toString());
        offlineExport.setFunctionName(Constant.FUNCTION_NAME);
        offlineExport.setStatus(NumConstant.STRING_ONE);
        offlineExport.setRecipients(empNo);
        offlineExport.setCreateBy(empNo);
        offlineExport.setLastUpdatedBy(empNo);
        offlineExportService.add(offlineExport);
        Map<String, String> map = new HashMap<>();
        map.put("specialityParamId", specialityParamId);
        map.put("empNo", empNo);
        ThreadUtil.EXECUTOR.execute(() -> {
            exportThread(map, query, totalRows, lock, offlineExport);
        });
    }

    private void exportThread(Map<String, String> map, SpSpecialityParamItemPageQueryDTO query, long totalRows, RedisLock lock, OfflineExport offlineExport) {
        String specialityParamId = map.get("specialityParamId");
        String empNo = map.get("empNo");
        ExcelWriter excelWriter = null;
        try {
            SpSpecialityParam spSpecialityParam = spSpecialityParamRepository.selectById(specialityParamId);
            String fileName = new StringBuilder(specialityParamId).append(spSpecialityParam.getTaskId())
                    .append(Constant.AND)
                    .append(spSpecialityParam.getItemCode()).append(spSpecialityParam.getOperator())
                    .append(Constant.AND)
                    .append(spSpecialityParam.getItemNum()).append(".xlsx").toString();
            List<List<String>> headList = null;
            if (StringUtils.isNotBlank(spSpecialityParam.getTemplateItem())) {
                List<List<String>> heads = new ArrayList<>();
                Arrays.stream(spSpecialityParam.getTemplateItem().split(Constant.COMMA)).forEach(item -> heads.add(Collections.singletonList(item)));
                headList = heads;
            } else {
                List<SpTemplateItem> templateItemList = spTemplateItemRepository.queryList(spSpecialityParam.getTemplateId());
                if (CollectionUtils.isNotEmpty(templateItemList)) {
                    List<List<String>> heads = new ArrayList<>();
                    templateItemList.forEach(item -> heads.add(Collections.singletonList(item.getParamName())));
                    headList = heads;
                }
            }
            String tempfilePath = FileUtils.createFilePathAndCheck(fileName);
            long pages = BigDecimal.valueOf(totalRows).divide(BigDecimal.valueOf(NumConstant.NUM_1000), RoundingMode.UP).longValue();
            long sheetNo = NumConstant.NUM_ONE;
            excelWriter = EasyExcel.write(tempfilePath).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(headList).build();
            WriteSheet writeSheet = EasyExcel.writerSheet((int) sheetNo, "Sheet" + sheetNo).build();
            for (int page = NumConstant.NUM_ONE; page <= pages; page++) {
                query.setPage(page);
                List<SpSpecialityParamItem> itemList = spSpecialityParamItemRepository.queryPage(query);
                long newSheetNo = pages / NumConstant.NUM_100 + NumConstant.NUM_ONE; // 100W
                if (newSheetNo > sheetNo) {
                    sheetNo = newSheetNo;
                    writeSheet = EasyExcel.writerSheet((int) sheetNo, "Sheet" + sheetNo).build();
                }
                excelWriter.write(getData(itemList, headList), writeSheet);
            }
            excelWriter.finish();
            // 上传文档云，并获取文档路径
            String fileKey = cloudDiskHelper.fileUpload(tempfilePath, empNo, CloudDiskHelper.MAX_RETRY_TIMES);
            FileUtils.deleteFile(tempfilePath);

            offlineExport.setStatus(NumConstant.STRING_TWO);
            offlineExport.setFileName(fileName);
            offlineExport.setFileKey(fileKey);
        } catch (Exception e) {

            offlineExport.setStatus(NumConstant.STRING_THREE);
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
        } finally {
            CommonUtils.closeExcelWriter(excelWriter);
            offlineExportService.updateById(offlineExport);
            lock.unlock();
        }
    }

    @Override
    @RecordLogAnnotation("个参文件同步mds")
    public void syncDataToMds(String specialityParamId) {
        if (StringUtils.isBlank(specialityParamId)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYNCMDS_SPECIALITYPARAMID_EMPTY_ERROR);
        }
        SpSpecialityParamItemPageQueryDTO query = new SpSpecialityParamItemPageQueryDTO();
        query.setSpecialityParamId(specialityParamId);
        query.setRows(NumConstant.NUM_1000);
        long totalRows = spSpecialityParamItemRepository.countPage(query);
        if (totalRows <= NumConstant.LONG_ZERO) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }
        SpSpecialityParam spSpecialityParam = spSpecialityParamRepository.selectById(specialityParamId);
        if (StringUtils.equals(spSpecialityParam.getSyncMdsStatus(), Constant.SYNC_MDS_SUCCESS)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYNCMDS_SPECIALITYPARAMID_REPEAT_ERROR);
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(Constant.X_REAL_EMP_NO.toLowerCase());
        RedisLock lock = new RedisLock(String.format(RedisKeyConstant.SPECIALITY_PARAM_EXPORT2MDS, empNo + specialityParamId), NumConstant.NUM_2000);
        boolean isLock = lock.lock();
        if (!isLock) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYNCMDS_FMTYPE_REPEATOPERATION_ERROR);
        }
        ThreadUtil.EXECUTOR.execute(() -> {
            try {
                syncDataThread(specialityParamId, totalRows, spSpecialityParam, query, lock);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void syncDataThread(String specialityParamId, long totalRows, SpSpecialityParam spSpecialityParam, SpSpecialityParamItemPageQueryDTO query, RedisLock lock) throws Exception {
        ExcelWriter excelWriter = null;
        String tempfilePath = StringUtils.EMPTY;
        try {
            String fileName = getFileName(specialityParamId, spSpecialityParam);
            List<List<String>> headList = getHeadList(spSpecialityParam);
            tempfilePath = FileUtils.createFilePathAndCheck(fileName);
            long pages = BigDecimal.valueOf(totalRows).divide(BigDecimal.valueOf(NumConstant.NUM_1000), RoundingMode.UP).longValue();
            excelWriter = EasyExcel.write(tempfilePath)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(headList)
                    .build();
            long sheetNo = NumConstant.NUM_ONE;
            WriteSheet writeSheet = EasyExcel.writerSheet((int) sheetNo, "Sheet" + sheetNo).build();
            for (int page = NumConstant.NUM_ONE; page <= pages; page++) {
                query.setPage(page);
                List<SpSpecialityParamItem> itemList = spSpecialityParamItemRepository.queryPage(query);
                // 100W
                long newSheetNo = pages / NumConstant.NUM_100 + NumConstant.NUM_ONE;
                if (newSheetNo > sheetNo) {
                    sheetNo = newSheetNo;
                    writeSheet = EasyExcel.writerSheet((int) sheetNo, "Sheet" + sheetNo).build();
                }
                excelWriter.write(getData(itemList, headList), writeSheet);
            }
            excelWriter.finish();
            // 转换为MultipartFile
            File excelFile = new File(tempfilePath);
            MultipartFile multipartFile = UploadFileUtils.convert(excelFile,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fmType = getFmType(spSpecialityParam);
            String result = mdsRemoteService.uploadFmParamMesFileInfo(multipartFile, fmType, spSpecialityParam.getApplyTask());
            // 未使用数未0，则认为生成未完成，更新状态
            SpSpecialityParam updateParam = new SpSpecialityParam();
            updateParam.setSpecialityParamId(spSpecialityParam.getSpecialityParamId());
            updateParam.setSyncMdsStatus(Constant.SYNC_MDS_SUCCESS);
            updateParam.setLastUpdatedDate(new Date());
            // 更新个参生成头表
            spSpecialityParamRepository.updateById(updateParam);
        } catch (Exception e) {
            String msg = e.getMessage();
            log.error("个参文件同步mds:" + "###" + msg);
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID + msg);
        } finally {
            CommonUtils.closeExcelWriter(excelWriter);
            if (lock != null) {
                lock.unlock();
            }
            // 确保删除临时文件
            FileUtils.deleteFile(tempfilePath);
        }
    }

    @NotNull
    private static String getFmType(SpSpecialityParam spSpecialityParam) {
        String fmType;
        switch (spSpecialityParam.getProductBigClass()) {
            case Constant.FMTYPE_CPE_IMESCODE: {
                fmType = Constant.FMTYPE_CPE_MDSNAME;
                break;
            }
            case Constant.FMTYPE_DHOME_IMESCODE:
                fmType = Constant.FMTYPE_DHOME_MDSNAME;
                break;
            default:
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYNCMDS_FMTYPE_ERROR, new Object[]{spSpecialityParam.getProductBigClass()});
        }
        return fmType;
    }


    @NotNull
    private static String getFileName(String specialityParamId, SpSpecialityParam spSpecialityParam) {
        return new StringBuilder(specialityParamId).append(spSpecialityParam.getTaskId())
                .append(Constant.AND)
                .append(spSpecialityParam.getItemCode()).append(spSpecialityParam.getOperator())
                .append(Constant.AND)
                .append(spSpecialityParam.getItemNum()).append(".xlsx").toString();
    }

    @Nullable
    private List<List<String>> getHeadList(SpSpecialityParam spSpecialityParam) {
        List<List<String>> headList = null;
        if (StringUtils.isNotBlank(spSpecialityParam.getTemplateItem())) {
            List<List<String>> heads = new ArrayList<>();
            Arrays.stream(spSpecialityParam.getTemplateItem().split(Constant.COMMA))
                    .forEach(item -> heads.add(Collections.singletonList(item)));
            headList = heads;
        } else {
            List<SpTemplateItem> templateItemList = spTemplateItemRepository.queryList(spSpecialityParam.getTemplateId());
            if (CollectionUtils.isNotEmpty(templateItemList)) {
                List<List<String>> heads = new ArrayList<>();
                templateItemList.forEach(item -> heads.add(Collections.singletonList(item.getParamName())));
                headList = heads;
            }
        }
        return headList;
    }

    @Override
    public void addBatch(List<SpSpecialityParamItem> list) {
        spSpecialityParamItemRepository.insertBatch(list);
    }

    private List<List<String>> getData(List<SpSpecialityParamItem> itemList, List<List<String>> headList) {
        if (CollectionUtils.isEmpty(itemList) || CollectionUtils.isEmpty(headList)) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
        }
        List<List<String>> datas = new ArrayList<>();
        itemList.forEach(item -> {
            JSONObject itemJSON = JSON.parseObject(item.getItemData());
            List<String> dataList = new ArrayList<>();
            headList.forEach(list -> dataList.add(itemJSON.getString(list.get(NumConstant.NUM_ZERO))));
            datas.add(dataList);
        });
        return datas;
    }

    @Override
    @TransmittableHeader
    public String importRecoverExcel(SpRecoverImportDTO spRecoverImportDTO) {
        if (spRecoverImportDTO.getFile() == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.IMPORT_DATA_IS_EMPTY);
        }

        if (Constant.STRING_EMPTY.equals(spRecoverImportDTO.getFile().getContentType())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
        }

        try {
            spRecoverImportDTO.setInputStream(spRecoverImportDTO.getFile().getInputStream());
        } catch (Exception e) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, MessageId.IMPORT_DATA_IS_EMPTY);
        }

        // 先写空值，超时时间5分钟
        String key = Constant.SPECIALITY_PARAM_RECOVER + MD5.getMD5Code(JSON.toJSONString(spRecoverImportDTO.getSpecialityParamId()));
        spRecoverImportDTO.setKey(key);
        redisTemplate.opsForValue().set(key, "", 300L, TimeUnit.SECONDS);
        ThreadUtil.EXECUTOR.execute(Objects.requireNonNull(TtlRunnable.get(() -> {
            try {
                importRecover(spRecoverImportDTO);
            } catch (Exception e) {
                redisTemplate.opsForValue().set(spRecoverImportDTO.getKey(), e.getMessage(), 120L, TimeUnit.SECONDS);
            }
        })));
        return key;
    }

    @Override
    public String getImportMessage(String importKey) {
        return redisTemplate.opsForValue().get(importKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commitRecover(String specialityParamId) {
        SpSpecialityParam specialityParam = spSpecialityParamRepository.selectById(specialityParamId);
        if (NumConstant.NUM_ONE == specialityParam.getIsRecovery()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.RECOVERY_FINISHED);
        }
        String empNo = MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase());
        // 修改回收状态
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setSpecialityParamId(specialityParamId);
        spSpecialityParam.setIsRecovery(NumConstant.NUM_ONE);
        spSpecialityParam.setLastUpdatedBy(empNo);
        spSpecialityParam.setLastUpdatedDate(new Date());
        spSpecialityParamRepository.updateById(spSpecialityParam);

        SpRecovery spRecovery = new SpRecovery();
        spRecovery.setSpecialityParamId(specialityParamId);
        spRecovery.setTaskId(specialityParam.getTaskId());
        spRecovery.setResourceType(MpConstant.MAC);
        spRecovery.setCreateBy(empNo);
        spRecovery.setLastUpdatedBy(empNo);
        spRecoveryRepository.recover(spRecovery);
        spRecovery.setResourceType(MpConstant.GPON_SN);
        spRecoveryRepository.recover(spRecovery);
    }

    @Transactional(rollbackFor = Exception.class)
    public void importRecover(SpRecoverImportDTO spRecoverImportDTO) throws Exception {
        log.info(MESHttpHelper.getHttpRequestHeader().toString());
        SpRecoverResourceDTO recoverResourceDTO = this.checkAndResetSpItem(spRecoverImportDTO);

        Workbook wb = null;
        // 2、添加分布式锁（防止同时上传导致数据重复）
        RedisLock redisLock = new RedisLock(String.format(RedisKeyConstant.IMPORT_RECOVER, spRecoverImportDTO.getKey()));
        try {
            // 锁超时时间20分钟
            boolean lock = redisLock.lock();
            if (!lock) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, CommonUtils.getLmbMessage(MessageId.IS_UPLOADING_PLEASE_WAIT));
            }

            // 3、解析Excel
            String fileName = spRecoverImportDTO.getFile().getOriginalFilename();
            String ext = fileName.substring(fileName.lastIndexOf('.') + 1);
            wb = ExcelUtil.initWorkbook(spRecoverImportDTO.getInputStream(), ext.toLowerCase());
            if (null == wb) {
                throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.WORKBOOK_INIT_FAIL));
            }
            Sheet sheet = wb.getSheetAt(IntegerConstant.INT_ZERO);
            setHeadIndex(recoverResourceDTO, sheet);

            handleData(spRecoverImportDTO, recoverResourceDTO, sheet);
            redisTemplate.opsForValue().set(spRecoverImportDTO.getKey(), RetCode.SUCCESS_CODE, 120L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("file data analysis error :{0}", e);
            if (e instanceof OfficeXmlFileException) {
                redisTemplate.opsForValue().set(spRecoverImportDTO.getKey(), CommonUtils.getLmbMessage(MessageId.EXCEL_CONTENT_FORMAT_NOT_MATCH), 120L, TimeUnit.SECONDS);
            } else {
                redisTemplate.opsForValue().set(spRecoverImportDTO.getKey(), e.getMessage(), 120L, TimeUnit.SECONDS);
            }
        } finally {
            redisLock.unlock();
            if (wb != null) {
                wb.close();
            }
        }

    }

    private SpRecoverResourceDTO checkAndResetSpItem(SpRecoverImportDTO spRecoverImportDTO) {
        // 检查回收
        SpSpecialityParamItem specialityParamItem = spSpecialityParamItemRepository.selectOneBySpecialityParamId(spRecoverImportDTO.getSpecialityParamId());
        if (null == specialityParamItem || (StringUtils.isBlank(specialityParamItem.getMacStart()) && StringUtils.isBlank(specialityParamItem.getMacEnd()) &&
                StringUtils.isBlank(specialityParamItem.getGponStart()) && StringUtils.isBlank(specialityParamItem.getGponEnd()))) {
            throw new MesBusinessException(RetCode.SERVERERROR_CODE, CommonUtils.getLmbMessage(MessageId.NO_RESOURCE_RECOVERY));
        }

        // 1、先将所有个参置为 未使用0
        SpSpecialityParamItem spSpecialityParamItem = new SpSpecialityParamItem();
        spSpecialityParamItem.setSpecialityParamId(spRecoverImportDTO.getSpecialityParamId());
        spSpecialityParamItem.setIsUsed(0);
        spSpecialityParamItemRepository.update(spSpecialityParamItem);

        // 获取需要回收的资源
        JSONObject itemJSON = JSON.parseObject(specialityParamItem.getItemData());
        SpRecoverResourceDTO spRecoverResourceDTO = new SpRecoverResourceDTO();
        spRecoverResourceDTO.setGponStart(Constant.STRING_EMPTY);
        spRecoverResourceDTO.setGponEnd(Constant.STRING_EMPTY);
        spRecoverResourceDTO.setMacStart(Constant.STRING_EMPTY);
        spRecoverResourceDTO.setMacEnd(Constant.STRING_EMPTY);
        itemJSON.keySet().forEach(key -> {
            if (StringUtils.isNotBlank(specialityParamItem.getMacStart()) && itemJSON.getString(key).equals(specialityParamItem.getMacStart())) {
                spRecoverResourceDTO.setMacStart(key);
            }
            if (StringUtils.isNotBlank(specialityParamItem.getMacEnd()) && itemJSON.getString(key).equals(specialityParamItem.getMacEnd())) {
                spRecoverResourceDTO.setMacEnd(key);
            }
            if (StringUtils.isNotBlank(specialityParamItem.getGponStart()) && itemJSON.getString(key).equals(specialityParamItem.getGponStart())) {
                spRecoverResourceDTO.setGponStart(key);
            }
            if (StringUtils.isNotBlank(specialityParamItem.getGponEnd()) && itemJSON.getString(key).equals(specialityParamItem.getGponEnd())) {
                spRecoverResourceDTO.setGponEnd(key);
            }
        });
        return spRecoverResourceDTO;
    }

    private void handleData(SpRecoverImportDTO spRecoverImportDTO, SpRecoverResourceDTO recoverResourceDTO, Sheet sheet) {
        List<SpSpecialityParamItem> itemTempList = new ArrayList<>();
        for (int i = IntegerConstant.INT_ONE; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);

            SpSpecialityParamItem spSpecialityParamItem = new SpSpecialityParamItem();
            spSpecialityParamItem.setId(UUID.randomUUID().toString());
            spSpecialityParamItem.setSpecialityParamId(spRecoverImportDTO.getSpecialityParamId());
            spSpecialityParamItem.setIsUsed(NumConstant.NUM_ONE);
            spSpecialityParamItem.setItemData(Constant.STRING_EMPTY);
            spSpecialityParamItem.setGponStart(Constant.STRING_EMPTY);
            spSpecialityParamItem.setGponEnd(Constant.STRING_EMPTY);
            spSpecialityParamItem.setMacStart(Constant.STRING_EMPTY);
            spSpecialityParamItem.setMacEnd(Constant.STRING_EMPTY);
            spSpecialityParamItem.setCreateBy(spRecoverImportDTO.getEmpNo());
            spSpecialityParamItem.setCreateDate(new Date());
            spSpecialityParamItem.setLastUpdatedBy(spRecoverImportDTO.getEmpNo());
            spSpecialityParamItem.setLastUpdatedDate(spSpecialityParamItem.getCreateDate());
            if (StringUtils.isNotBlank(recoverResourceDTO.getMacStart()) || StringUtils.isNotBlank(recoverResourceDTO.getMacEnd())) {
                spSpecialityParamItem.setMacStart(getCellValue(row, recoverResourceDTO.getMacStartIdx()));
                spSpecialityParamItem.setMacEnd(getCellValue(row, recoverResourceDTO.getMacStartIdx()));
            }

            if (StringUtils.isNotBlank(recoverResourceDTO.getGponStart()) || StringUtils.isNotBlank(recoverResourceDTO.getGponEnd())) {
                spSpecialityParamItem.setGponStart(getCellValue(row, recoverResourceDTO.getGponStartIdx()));
                spSpecialityParamItem.setGponEnd(getCellValue(row, recoverResourceDTO.getGponEndIdx()));
            }
            itemTempList.add(spSpecialityParamItem);

            if (i > 0 && i % NumConstant.NUM_100 == NumConstant.NUM_ZERO) {
                updateSpItem(itemTempList);
            }
        }
        if (CollectionUtils.isNotEmpty(itemTempList)) {
            updateSpItem(itemTempList);
        }
    }

    private void updateSpItem(List<SpSpecialityParamItem> itemTempList) {
        spSpecialityParamItemRepository.insertBatchTemp(itemTempList);
        spSpecialityParamItemRepository.updateTemp2Item();
        spSpecialityParamItemRepository.deleteTemp();
        itemTempList.clear();
    }

    private String getCellValue(Row row, int recoverResourceDTO) {
        Cell endCel = row.getCell(recoverResourceDTO);
        String endValue = endCel == null ? Constant.STRING_EMPTY : endCel.toString();
        return endValue;
    }


    private void setHeadIndex(SpRecoverResourceDTO recoverResourceDTO, Sheet sheet) {
        Row headRow = sheet.getRow(IntegerConstant.INT_ZERO);
        for (int i = IntegerConstant.INT_ZERO; i < headRow.getLastCellNum(); i++) {
            String val = getCellValue(headRow, i);
            if (recoverResourceDTO.getMacStart().equals(val)) {
                recoverResourceDTO.setMacStartIdx(i);
            }
            if (recoverResourceDTO.getMacEnd().equals(val)) {
                recoverResourceDTO.setMacEndIdx(i);
            }
            if (recoverResourceDTO.getGponStart().equals(val)) {
                recoverResourceDTO.setGponStartIdx(i);
            }
            if (recoverResourceDTO.getGponEnd().equals(val)) {
                recoverResourceDTO.setGponEndIdx(i);
            }
        }
    }
}
