package com.zte.application;

import com.zte.interfaces.dto.PushBoardDataProcessDTO;

import java.util.List;

/**
 * 单板数据推送进程明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-02 14:43:47
 */
public interface PushBoardDataProcessService {

    /**
     * 根据条码和业务类型查询待推送条码
     * 数据不存在则先新增
     * @param snList
     * @param businessType
     * @return
     */
    List<PushBoardDataProcessDTO> getAndInitNeedPushData(List<String> snList, String businessType);

    void update(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    void batchUpdate(List<PushBoardDataProcessDTO> list);

    /**
     * 根据B2B回调结果更新process推送状态
     *
     * @param pushBoardDataProcessDTO
     */
    void syncProcessPushStatus(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    int insertBoardProcessDataBatch(List<PushBoardDataProcessDTO> list);

    /**
     * 定时新增推送中间表
     *
     * @param pushBoardDataProcessDTO 参数
     * @return
     */
    Integer scheduledInsertPushProcessTask(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    /**
     * 批量更新数据
     * @param updateList 更细数据集
     */
    void updateBoardDataProcessBatch(List<PushBoardDataProcessDTO> updateList);

    /**
     * 查询推送进程统计数据
     * @param processDTO 查询参数
     * @return 请求
     */
    List<PushBoardDataProcessDTO> queryPushDataList(PushBoardDataProcessDTO processDTO);

    List<PushBoardDataProcessDTO> getDataBySnAndBusinessType(List<String> snList, List<String> businessTypeList);
}

