package com.zte.application.ems.impl;

import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmInfo;
import com.zte.application.ems.EmsService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforIwmsIscpRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.vo.JobLogHisVO;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_0;

/**
 * <AUTHOR>
 */
@Service
public class EmsServiceImpl implements EmsService {

    @Autowired
    private StepIscpRepository stepIscpRepository;
    @Autowired
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Value("${dtems.agent.applicationInsId}")
    private String applicationInsId;
    @Value("${dtems.agent.businessId}")
    private String businessId;
    @Value("${dtems.agent.alarmCode.step.job.monitor}")
    private String alarmCodeStepJob;

    @Override
    public void doMrpMinitor() {
        List<String> jobList = inforIwmsIscpRepository.getDicList(MRP_DICS);
        List<JobLogHisVO> list = stepIscpRepository.getMonitorListMrp(jobList);
        list.forEach(t-> {
            int count = stepIscpRepository.getCountJobUnknown(t.getInsId());
            if (count > INT_0) {
                AlarmInfo alarmInfo = new AlarmInfo();
                alarmInfo.setAlarmCode(alarmCodeStepJob);
                alarmInfo.setObjKey(t.getJobName());
                alarmInfo.setAlarmSeverity(NumConstant.INT_1);
                alarmInfo.setIsAutoResolve(STR_TRUE);
                alarmInfo.setAutoResolveExperied(NumConstant.INT_60);
                Map<String, String> alarmTitleMap = new HashMap<>();
                alarmTitleMap.put(LAN_EN, MRP + t.getJobName() + RUN_ERROR);
                alarmTitleMap.put(LAN_ZH, MRP + t.getJobName() + RUN_ERROR);
                alarmInfo.setAlarmTitle(alarmTitleMap);
                Map<String, String> additionalTextMap = new HashMap<>();
                additionalTextMap.put(MRP, Tools.isEmpty(t.getErrors()) ? UNKNOWN : StringUtils.substring(t.getErrors(), INT_0, NumConstant.INT_500));
                alarmInfo.setAdditionalText(additionalTextMap);
                AlarmHelper.alarm(applicationInsId, businessId, alarmInfo);
            }
        });
    }
    @Override
    public void concelAlarm(String jobName) {
        AlarmInfo alarmInfo = new AlarmInfo();
        alarmInfo.setAlarmCode(alarmCodeStepJob);
        alarmInfo.setObjKey(jobName);
        Map<String, String> alarmTitleMap = new HashMap<>();
        alarmTitleMap.put(LAN_EN, MRP + jobName + RUN_ERROR);
        alarmTitleMap.put(LAN_ZH, MRP + jobName + RUN_ERROR);
        alarmInfo.setAlarmTitle(alarmTitleMap);
        AlarmHelper.recover(applicationInsId, businessId, alarmInfo);
    }
}
