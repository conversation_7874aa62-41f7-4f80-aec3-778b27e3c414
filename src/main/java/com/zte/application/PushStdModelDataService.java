package com.zte.application;

import com.zte.common.enums.SceneCodeEnum;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;

import java.util.List;
import java.util.Map;

/**
 * 标模推送信息头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-02 14:43:47
 */
public interface PushStdModelDataService {
    int mergeStdModelTask(List<PushStdModelDataDTO> list, List<String> customerNoList);

    int pushStdModelData(List<String> customerNameList, int preDays);

    void dispatchManufactureOrderCallBack(B2bCallBackNewDTO data);

    void orderScheduleCallBack(B2bCallBackNewDTO b2bCallBackNewDTO);

    void batchSaveOrUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList);

    void ticketClosed() throws Exception;

    void ticketClosedCallBack(B2bCallBackNewDTO b2bCallBackNewDTO);

    /**
     * 分页查询标模推送信息头列表
     *
     * @param query 查询条件
     * @return 标模推送信息头列表
     */
    PageRows<PushStdModelDataExtDTO> queryPushStdModelDataExt(PushStdModelDataQueryDTO query);

    List<String> getExistTaskNo(List<String> taskNoList);

    void batchUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList);

    List<PushStdModelDataDTO> getListByTaskNo(List<String> taskNos);

    <T extends PushStdModelDTO> boolean pushSchedulingInfo(T pushStdModelDataDTO, Map<String, PsTaskExtendedDTO> taskExtendedMap) throws Exception;

    <T extends PushStdModelDTO> void sendAlarmEmail(List<T> needWarnList, String title);

    boolean pushSchedulingInfo(PushStdModelDTO pushStdModelDTO, PsTaskExtendedDTO psTaskExtendedDTO, SceneCodeEnum sceneCodeEnum) throws Exception;
}

