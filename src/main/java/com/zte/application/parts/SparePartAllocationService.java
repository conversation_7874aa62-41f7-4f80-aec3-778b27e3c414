package com.zte.application.parts;

import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.EmSmtStencil;
import com.zte.interfaces.dto.EmSmtStencilDTO;
import com.zte.interfaces.dto.FixtureInfoDetailDTO;
import com.zte.interfaces.dto.SolderInfoDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationItemDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO;
import com.zte.springbootframe.common.model.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-06-05 9:21
 */
public interface SparePartAllocationService {

    /**
     * 查询辅料调拨信息
     *
     * @param headDTO 查询参数
     * @return 辅料调拨单据信息
     */
    SparePartAllocationHeadDTO querySpareHeadAndDetails(SparePartAllocationHeadDTO headDTO);


    /**
     * 新增辅料调拨单据信息
     *
     * @param headDTO 辅料调拨信息
     */
    void savePartHeadAndDetails(SparePartAllocationHeadDTO headDTO) throws Exception;

    void saveProperties(SparePartAllocationHeadDTO headDTO);

    /**
     * 更新辅料调拨单据信息
     *
     * @param headDTO 辅料调拨单据
     */
    void updateSpareHeadAndDetails(SparePartAllocationHeadDTO headDTO);

    /**
     * 生产管理回调更新 单据状态
     *
     * @param headDTO 单据头信息
     */
    void updateBillHeadStatus(SparePartAllocationHeadDTO headDTO) throws Exception;

    /**
     * 下载辅料调拨单据明细
     *
     * @param response 响应
     */
    void downLoadExcelTemplate(HttpServletResponse response) throws Exception;

    /**
     * 解析单据明细
     *
     * @param file 文件
     * @return 单据明细信息
     * @throws Exception 流异常
     */
    List<SparePartAllocationDetailDTO> uploadExcel(MultipartFile file) throws Exception;


    List<String> getSpareBillNoList(SparePartAllocationHeadDTO headDTO);

    SparePartAllocationHeadDTO getSpareOutBoundList(SparePartAllocationHeadDTO headDTO) throws Exception;

    int newSparePartItemDetail(SparePartAllocationItemDetailDTO detailDTO) throws Exception;

    /**
     * 获取对应状态的单据号
     *
     * @param headDTO 辅料调拨单据
     */
    List<SparePartAllocationHeadDTO> getBillNos(SparePartAllocationHeadDTO headDTO);

    /**
     * 根据单据号获取详细信息
     *
     * @param dto
     */
    List<SparePartAllocationHeadDTO> getInfoByBillNo(SparePartAllocationHeadDTO dto);

    String checkItemExist(SparePartAllocationHeadDTO dto) throws Exception;

    /**
     * 辅料调拨入库--更新单据详表
     */
    int updateDetail(SparePartAllocationHeadDTO dto) throws Exception;

    /**
     * 辅料调拨入库--关闭单据
     */
    void updateHeadStatus(SparePartAllocationHeadDTO dto);

    /**
     * 辅料调拨查询--查询头表
     */
    Page<SparePartAllocationQueryDTO> querySpareHeadInfo(SparePartAllocationQueryDTO queryDTO) throws Exception;

    /**
     * 辅料调拨查询--查询调拨单详情表
     */
    Page<SparePartAllocationQueryDTO> queryBillDetailByBillNo(SparePartAllocationQueryDTO queryDTO);

    /**
     * 辅料调拨查询--查询调拨辅料详情表
     */
    Page<SparePartAllocationQueryDTO> queryItemDetailByBillNo(SparePartAllocationQueryDTO queryDTO) throws Exception;

    /**
     * 辅料调拨查询--异常关闭单据操作
     */
    Integer closeSparePartAllocation(String billNo, String empNo);

    /**
     * 辅料调拨查询--删除单据操作
     */
    Integer deleteSparePartAllocation(SparePartAllocationQueryDTO queryDTO, String empNo);

    /**
     * 辅料调拨查询--回退辅料操作
     */
    Integer rollbackSparePartAllocation(SparePartAllocationQueryDTO queryDTO, String empNo);

    /**
     * 辅料调拨查询--导出获取数据量
     */
    Integer countExportTotal(SparePartAllocationQueryDTO queryDTO);

    /**
     * 辅料调拨查询--导出获取数据
     */
    List<SparePartAllocationQueryDTO> queryExportData(SparePartAllocationQueryDTO queryDTO, int pageNo, int pageSize);

    /**
     * 辅料调拨查询--获取单据审批详情
     */
    List<ApprovalProcessInfoEntityDTO> getApprovalDetailByBillNo(String billNo);

    void deleteSolderOriginalInfo(SolderInfoDTO solderInfoDTO) throws Exception;

    void deleteFixtureOriginalInfo(FixtureInfoDetailDTO fixtureInfoDetailDTO) throws Exception;

    void deleteStencilsOriginalInfo(EmSmtStencil emSmtStencil) throws Exception;

    List<EmSmtStencilDTO> queryRelPcbInfo(EmSmtStencil emSmtStencil) throws Exception;

    /**
     *  撤销备件审批
     * @param billNo 单据号
     */
    void cancelTheApproval(String billNo);

    List<SparePartAllocationQueryDTO> querySpareInfoByPartCode(String partCode) throws Exception;
}
