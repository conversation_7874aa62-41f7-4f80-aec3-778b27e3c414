package com.zte.application.cbom;

import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.interfaces.dto.mbom.FixBomHeadDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-29 20:08
 */
public interface FixBomCommonService {

    /**
     * 定时任务计算fixBom
     */
    void refreshFixBomId();

    void createFixBomByTaskNo(String taskNo);

    void reCreateFixBomByTaskNo(String taskNo);

    /**
     * 批量删除FixBom头和明细数据
     * @param idList 头id List
     */
    void deleteFixBomHeadAndDetailById(List<String> idList);

    /**
     *
     * @param fixBomHeadDTO fixHead
     * @param list 明细
     * @param extendList 扩展表
     */
    void insertAndUpdate(FixBomHeadDTO fixBomHeadDTO, List<FixBomDetailDTO> list, List<PsTaskExtendedDTO> extendList);

    /**
     * 通过fixBomId 查询fixBom明细信息
     * @param fixBomId fixBomId
     * @return 明细
     */
    List<FixBomDetailDTO> queryFixBomDetailByFixBomId(String fixBomId);

    /**
     * 根据headId查询fixBomDetail
     *
     * @param headId
     * @return
     */
    List<FixBomDetailDTO> queryFixBomDetailByHeadId(String headId);

    List<FixBomDetailDTO> queryTreeNodeByFixBomId(String fixBomId);

    /**
     * 根据headId查询FixBomDetail树形结构
     *
     * @param headId
     * @return
     */
    List<FixBomDetailDTO> queryTreeNodeByHeadId(String headId);

    List<FixBomDetailDTO> getTreeByFixBomDetail(List<FixBomDetailDTO> fixBomDetailList);

    /**
     * 根据fixbom树调整物料用量
     * @param node fixBom树
     * @param multiple 当前倍数
     */
    void adjustItemNumber(List<FixBomDetailDTO> node, int multiple);

    List<FixBomDetailDTO> getFixBomByTaskNo(String taskNo);

    List<FixBomDetailDTO> queryFixBomByTaskNoOrFixBomId(String taskNo, String fixBomId);

    /**
     * 根据任务号或者headId查询fixBom
     *
     * @param taskNo
     * @param headId
     * @return
     */
    List<FixBomDetailDTO> queryFixBomByTaskNoOrHeadId(String taskNo, String headId);

    List<FixBomDetailDTO> adjustItemNumberByTaskNo(String taskNo);
}
