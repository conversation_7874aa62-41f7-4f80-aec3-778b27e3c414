package com.zte.application.step;

import com.zte.interfaces.step.dto.DocumentCallbackResultDTO;
import com.zte.interfaces.step.dto.EcssBillDTO;

/**
 * <AUTHOR>
 *
 */
public interface OmsEcssService {

    /**
     * 推送单据信息到ECSS
     * @param dto
     */
    void pushBillToEcss(EcssBillDTO dto);

    /**
     * ECSS新增单据回调接口
     * @param dto
     */
    void callBackEcssToOms(DocumentCallbackResultDTO dto);

}
