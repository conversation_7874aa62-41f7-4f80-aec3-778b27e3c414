/* Started by AICoder, pid:4a51dfb36b0d44c6b87d4e3b16c56316 */
package com.zte.application.step;

import com.zte.interfaces.step.dto.*;

/**
 * <AUTHOR>
 */
public interface OmsGtsService {

    /**
     * 推送单据信息到GTS
     */
    void pushBillToGts(GtsBillDTO dto);

    /**
     * GTS新增单据回调接口
     */
    GtsBackReturnDTO callBackGtsToOms(GtsBackParamDTO dto);
}
/* Ended by AICoder, pid:4a51dfb36b0d44c6b87d4e3b16c56316 */