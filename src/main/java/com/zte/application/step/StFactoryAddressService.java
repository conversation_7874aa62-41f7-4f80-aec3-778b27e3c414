/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-09-02
 * 修改历史 :
 *   1. [2019-09-02] 创建文件 by 6396000647
 **/
package com.zte.application.step;

import java.util.List;

import com.zte.domain.model.step.StFactoryAddress;
import com.zte.interfaces.step.dto.StAddressDTO;
import com.zte.interfaces.step.dto.StFactoryAddressDTO;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public interface StFactoryAddressService {

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    void insertStFactoryAddress(StFactoryAddress record);

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    void insertStFactoryAddressSelective(StFactoryAddress record);

    /**
     * 根据主键删除实体数据
     * 
     * @param record
     **/
    void deleteStFactoryAddressById(StFactoryAddress record);

    /**
     * 有选择性的更新实体数据
     * 
     * @param record
     **/
    void updateStFactoryAddressByIdSelective(StFactoryAddress record);

    /**
     * 根据主键更新实体数据
     * 
     * @param record
     **/
    void updateStFactoryAddressById(StFactoryAddress record);

    /**
     * get all record
     * @param record 查询条件
     * @return 所有的记录
     */
    List<StFactoryAddressDTO> selectStFactoryAddressAll(StAddressDTO record);

    /**
     * 根据主键查询实体信息
     * 
     * @param record
     * @return StFactoryAddress
     **/
    StFactoryAddress selectStFactoryAddressById(StFactoryAddress record);
}