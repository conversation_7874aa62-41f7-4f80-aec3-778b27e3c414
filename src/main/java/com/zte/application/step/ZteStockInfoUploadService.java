package com.zte.application.step;

import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.interfaces.step.dto.ZteStockInfoDTO;

/**
 * <AUTHOR>
 *
 */
public interface ZteStockInfoUploadService {

    /**
     * 库存数据上传
     */
    void stockInfoUpload(ZteStockInfoDTO dto);

    /**
     * 库存移动数据上传
     */
    void stockMoveInfoUpload(ZteStockInfoDTO dto);

    /**
     * 更新INFOR系统中B2B回传日志表的推送状态
     */
    void updateStockUploadLog(CustomerDataLogDTO dto);

    /**
     * 库存推送B2B日志监控
     */
    void stockUploadLogMonitor();

    /**
     * 采购订单收货数据上传
     */
    void purchaseOrderUpload(ZteStockInfoDTO dto);

    /**
     * 推送转代码单据信息给采购
     */
    void pushTransferSkuToIsrm(String xEmpNo);

    String getDataTransferBatchNo(String batchNoType);

}
