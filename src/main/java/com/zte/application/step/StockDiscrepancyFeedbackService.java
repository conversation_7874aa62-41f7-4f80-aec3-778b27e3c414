package com.zte.application.step;

import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackAddDTO;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackDTO;
import com.zte.interfaces.step.vo.StockDiscrepancyFeedbackListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/16 10:34
 */
public interface StockDiscrepancyFeedbackService {

    StockDiscrepancyFeedbackListVO query(StockDiscrepancyFeedbackDTO queryDTO);

    void add(StockDiscrepancyFeedbackAddDTO addDTO);

    List<SysLookupValuesDTO> getSysList(SysLookupValuesDTO dto);

    void deleteByKey(List<Long> keys);

    void export(StockDiscrepancyFeedbackDTO dto);

    void submit(StockDiscrepancyFeedbackDTO dto);

}
