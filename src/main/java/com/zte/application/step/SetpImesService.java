package com.zte.application.step;

import com.zte.interfaces.step.dto.*;

import java.util.List;

/**
 * imes退料单相关实现
 *
 * <AUTHOR>
 * @date 2024-01-23 10:11
 */
public interface SetpImesService {

    /**
     * 获取退料仓、拣选库位、存储区
     * @param dto
     * @return
     */
    List<BaItemDto> getStockInfo(BaItemQueryDto dto);

    /***
     * 获取项目
     * @return
     */
    List<ProjectDTO> getProjectInfo();

    /***
     * 获取走账别名
     * @return
     */
    List<StUtilityDTO> getStUtilityInfo();

    /***
     * 获取单板包装扫描推送INFO单据号
     * @return
     */
    String getInforBillNo();

    /***
     * 获取是否是ERP计划
     * @return
     */
    OpOrderplanHeadDto getIsErpPlan(String prodplanId);

    /***
     * 获取委托加工前信息
     * @return
     */
    BaBomHeadDto getEntrustBeforeInfo(String prodplanId);

    /***
     * 获取计划组信息
     * @return
     */
    DbProdclassPlangroupDto getPlangroupInfo(DbProdclassPlangroupDto dto);
}
