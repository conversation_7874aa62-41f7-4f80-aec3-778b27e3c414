package com.zte.application.step;

import com.zte.interfaces.step.dto.VmiDetailInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiSkuInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiUUIDInventoryQueryDTO;
import com.zte.itp.msa.core.model.ServiceData;

/**
 * VMI库存查询
 * <AUTHOR>
 *
 */
public interface VmiInventoryQueryService {
	/**
	 * 代码级库存查询
	 * @param inDTO
	 * @return
	 */
    ServiceData<?> vmiSkuInventoryQuery(VmiSkuInventoryQueryDTO inDTO);
    
	/**
	 * 拆分级库存查询
	 * @param inDTO
	 * @return
	 */
    ServiceData<?> vmiUUIDInventoryQuery(VmiUUIDInventoryQueryDTO inDTO);
    
    
	/**
	 * 明细级库存查询
	 * @param inDTO
	 * @return
	 */
    ServiceData<?> vmiDetailInventoryQuery(VmiDetailInventoryQueryDTO inDTO);
}
