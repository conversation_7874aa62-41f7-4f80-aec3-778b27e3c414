package com.zte.application.step;

import com.zte.domain.model.step.StepTransferBill;
import com.zte.interfaces.step.dto.CheckResultHeadDTO;
import com.zte.interfaces.step.dto.TransferDetailDTO;

/**
 * <AUTHOR>
 */
public interface OmsIqcService {

    /**
     * 推送源仓为研发打样库调拨单检验信息到IQC
     */
    void pushTransferCheckInfoToIqc(TransferDetailDTO dto) throws Exception;

    /**
     * IQC回传调拨单检验结果
     */
    void transferCheckResultToOms(CheckResultHeadDTO checkResultHeadDTO) throws Exception;
}
