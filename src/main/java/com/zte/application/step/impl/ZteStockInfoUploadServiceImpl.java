package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.step.ZteStockInfoUploadService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.ZteStockMoveInfoUploadRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.utils.CommonUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_12;

/**
 * <AUTHOR>
 *
 */
@Service
public class ZteStockInfoUploadServiceImpl implements ZteStockInfoUploadService {

	private static final Logger log = LoggerFactory.getLogger(ZteStockInfoUploadServiceImpl.class);
	@Autowired
	private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
	@Autowired
	private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;
	@Autowired
	private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
	@Autowired
	private StepTransferRepository stepTransferRepository;
	@Autowired
	private EmailUtil emailUtil;
	@Value("${in.one.url}")
	private String inoneUrl;
	@Value("${in.one.wms.app.code}")
	private String inoneAppcode;

	/* Started by AICoder, pid:db70ca1ce25b4451af1c5f0d86bc981d */
	@Override
	public void stockInfoUpload(ZteStockInfoDTO dto) {

		// 物料代码的个数不能超过500
		BusiAssertException.isTrue(Tools.isNotEmpty(dto.getItemNoList()) && dto.getItemNoList().size() > NumConstant.INT_500, MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED);

		//查询数据
		List<ZteStockInfoDTO> zteStockInfoDTOList = zteStockInfoUploadRepository.getStockInfo(dto);
		if (CommonUtils.isEmpty(zteStockInfoDTOList)) {
			return;
		}

		String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
		List<ZteStockInfoUploadDTO> zteStockInfoUploadDTOList = new ArrayList<>();
		for (ZteStockInfoDTO zteStockInfoDTO : zteStockInfoDTOList) {
			ZteStockInfoUploadDTO zteStockInfoUploadDTO = new ZteStockInfoUploadDTO();
			BeanUtils.copyProperties(zteStockInfoDTO, zteStockInfoUploadDTO);
			zteStockInfoUploadDTO.setDataTransferBatchNo(dataTransferBatchNo);
			zteStockInfoUploadDTO.setUuid(UUID.randomUUID().toString().trim().replaceAll(ONE_SPLIT, STRING_EMPTY));
			zteStockInfoUploadDTO.setLobCode(STRING_EMPTY);
			zteStockInfoUploadDTO.setConfigMaterialCode(STRING_EMPTY);
			zteStockInfoUploadDTO.setConfigMaterialDesc(STRING_EMPTY);
			zteStockInfoUploadDTOList.add(zteStockInfoUploadDTO);
		}

		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		for (List<ZteStockInfoUploadDTO> tempList : CommonUtils.splitList(zteStockInfoUploadDTOList, NumConstant.INT_500)) {
			//组装B2B对象
			CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
			customerDataLogDTO.setId(UUID.randomUUID().toString());
			customerDataLogDTO.setOrigin(INFOR_WMS);
			customerDataLogDTO.setCustomerName(BYTE_DANCE);
			customerDataLogDTO.setProjectName(STOCK_INFO_ZH);
			customerDataLogDTO.setProjectPhase(STRING_EMPTY);
			customerDataLogDTO.setCooperationMode(STRING_EMPTY);
			customerDataLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY);
			customerDataLogDTO.setContractNo(STRING_EMPTY);
			customerDataLogDTO.setTaskNo(dataTransferBatchNo);
			customerDataLogDTO.setItemNo(STRING_EMPTY);
			customerDataLogDTO.setSn(STRING_EMPTY);
			Map<String, Object> map = new HashMap<>();
			map.put(DATA, tempList);
			customerDataLogDTO.setJsonData(JSON.toJSONString(map));
			customerDataLogDTO.setFactoryId(INT_51);
			customerDataLogDTO.setCreateBy(dto.getEmpNo());
			customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
			customerDataLogDTOList.add(customerDataLogDTO);

			//组装日志对象
			ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
			BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
			zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
		}

		//保存库存快照
		for (List<ZteStockInfoDTO> tempZteStockInfoDTOList : CommonUtils.splitList(zteStockInfoDTOList, NumConstant.INT_500)) {
			zteStockInfoUploadRepository.insertStockUploadSnapshot(tempZteStockInfoDTOList);
		}

		//记录上传日志
		for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
			zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
		}

		//上传B2B
		for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
			imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, dto.getEmpNo());
		}

	}
	/* Ended by AICoder, pid:db70ca1ce25b4451af1c5f0d86bc981d */

	/* Started by AICoder, pid:ae8711fd7de44ab8b690691689540e97 */
	/**
	 * 生成传输批次号
	 */
	@Override
	public String getDataTransferBatchNo(String batchNoType) {
		// 获取递增的序列号，并将其转换为字符串
		String dataTransferBatchNo = RedisSerialNoUtil.getDateIncreaseId(batchNoType, INT_6);
		// 对序列号进行格式化，将前12位和后两位用分隔符连接起来
		return dataTransferBatchNo.substring(INT_0, INT_12) + STR_2 + dataTransferBatchNo.substring(INT_13);
	}
	/* Ended by AICoder, pid:ae8711fd7de44ab8b690691689540e97 */

	/* Started by AICoder, pid:a11b0424f1d848a6898f1e67870531d6 */
	@Override
	public void stockMoveInfoUpload(ZteStockInfoDTO dto) {

		// 物料代码的个数不能超过500
		BusiAssertException.isTrue(Tools.isNotEmpty(dto.getItemNoList()) && dto.getItemNoList().size() > NumConstant.INT_500, MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED);

		//查询数据
		List<ZteStockMoveInfoUploadDTO> zteStockMoveInfoUploadDTOList = zteStockMoveInfoUploadRepository.getStockMoveInfo(dto);
		if (CommonUtils.isEmpty(zteStockMoveInfoUploadDTOList)) {
			return;
		}

		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
		for (ZteStockMoveInfoUploadDTO zteStockMoveInfoUploadDTO : zteStockMoveInfoUploadDTOList) {
			zteStockMoveInfoUploadDTO.setDataTransferBatchNo(dataTransferBatchNo);
			zteStockMoveInfoUploadDTO.setUuid(UUID.randomUUID().toString().trim().replaceAll(ONE_SPLIT, STRING_EMPTY));
			zteStockMoveInfoUploadDTO.setBdPlantCode(STRING_EMPTY);
		}
		List<List<ZteStockMoveInfoUploadDTO>> list = CommonUtils.splitList(zteStockMoveInfoUploadDTOList, NumConstant.INT_500);
		int i = 1;
		for (List<ZteStockMoveInfoUploadDTO> tempList : list) {
			ZteStockMoveInfoUploadParamDTO zteStockMoveInfoUploadParamDTO = new ZteStockMoveInfoUploadParamDTO();
			zteStockMoveInfoUploadParamDTO.setRequestTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_SSS)));
			zteStockMoveInfoUploadParamDTO.setRequester(INFOR_WMS);
			zteStockMoveInfoUploadParamDTO.setDataTransferBatchNo(dataTransferBatchNo);
			zteStockMoveInfoUploadParamDTO.setRequestId(UUID.randomUUID().toString().trim().replaceAll(ONE_SPLIT, STRING_EMPTY));
			zteStockMoveInfoUploadParamDTO.setPageNo(i);
			zteStockMoveInfoUploadParamDTO.setIsLastPage(i==list.size()?T:F);
			zteStockMoveInfoUploadParamDTO.setPageSize(tempList.size());
			zteStockMoveInfoUploadParamDTO.setBody(tempList);
			//组装对象
			CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
			customerDataLogDTO.setId(UUID.randomUUID().toString());
			customerDataLogDTO.setOrigin(INFOR_WMS);
			customerDataLogDTO.setCustomerName(BYTE_DANCE);
			customerDataLogDTO.setProjectName(STOCK_MOVE_INFO_ZH);
			customerDataLogDTO.setProjectPhase(STRING_EMPTY);
			customerDataLogDTO.setCooperationMode(STRING_EMPTY);
			customerDataLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
			customerDataLogDTO.setContractNo(STRING_EMPTY);
			customerDataLogDTO.setTaskNo(dataTransferBatchNo);
			customerDataLogDTO.setItemNo(STRING_EMPTY);
			customerDataLogDTO.setSn(STRING_EMPTY);
			customerDataLogDTO.setJsonData(JSON.toJSONString(zteStockMoveInfoUploadParamDTO));
			customerDataLogDTO.setFactoryId(INT_51);
			customerDataLogDTO.setCreateBy(dto.getEmpNo());
			customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
			customerDataLogDTOList.add(customerDataLogDTO);

			//组装日志对象
			ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
			BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
			zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
			i++;
		}

		//记录上传日志
		for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
			zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
		}

		//上传B2B
		for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
			imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, dto.getEmpNo());
		}
	}
	/* Ended by AICoder, pid:a11b0424f1d848a6898f1e67870531d6 */

	/* Started by AICoder, pid:b248a17dfdb94c2593f5fbd2fd5033cf */
	@Override
	public void updateStockUploadLog(CustomerDataLogDTO dto) {

		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		zteStockInfoUploadLogDTO.setMessageType(dto.getMessageType());
		zteStockInfoUploadLogDTO.setId(dto.getId());
		zteStockInfoUploadRepository.updateStockUploadLog(zteStockInfoUploadLogDTO);
	}
	/* Ended by AICoder, pid:b248a17dfdb94c2593f5fbd2fd5033cf */

	/* Started by AICoder, pid:18c4696e17874414a58ef0db48f85922 */
	@Override
	public void stockUploadLogMonitor() {

		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = zteStockInfoUploadRepository.stockUploadLogMonitor();
		if (CommonUtils.isEmpty(zteStockInfoUploadLogDTOList)) {
			return;
		}
		StringBuilder content = new StringBuilder();
		for (ZteStockInfoUploadLogDTO dto : zteStockInfoUploadLogDTOList) {
			if (CommonUtils.equals(ZTE_IMES_BYTEDANCE_INVENTORY, dto.getMessageType())) {
				content.append(STOCK_INFO_UPLOAD_FAILED + Constant.STR_FEED).append(dto.getQty()).append(PIECE_TEXT).append(Constant.STR_FEED);
			}

			if (CommonUtils.equals(ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT, dto.getMessageType())) {
				content.append(STOCK_MOVE_INFO_UPLOAD_FAILED + Constant.STR_FEED).append(dto.getQty()).append(PIECE_TEXT).append(Constant.STR_FEED);
			}

			if (CommonUtils.equals(ZTE_IMES_BYTEDANCE_GOODS_RECEIPT_OF_PURCHASE_ORDER, dto.getMessageType())) {
				content.append(RECEIPT_AND_PURCHASE_ORDER_INFO_UPLOAD_FAILED + Constant.STR_FEED).append(dto.getQty()).append(PIECE_TEXT).append(Constant.STR_FEED);
			}
		}
		List<String> users = stepTransferRepository.getEmailUser(LOOKUP_TYPE_1000032);
		String receipts = StringUtils.join(users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
		emailUtil.sendMail(receipts, STOCK_INFO_UPLOAD_MONITOR, STOCK_INFO_UPLOAD_MONITOR, content.toString(), content.toString());
	}
	/* Ended by AICoder, pid:18c4696e17874414a58ef0db48f85922 */

	/* Started by AICoder, pid:57bb5c2804d34cabb6e0724d2e4f597a */
	@Override
	public void purchaseOrderUpload(ZteStockInfoDTO dto) {

		// 单据号的个数不能超过500
		BusiAssertException.isTrue(Tools.isNotEmpty(dto.getBillNoList()) && dto.getBillNoList().size() > NumConstant.INT_500, MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED);

		// 查询数据
		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOList = zteStockMoveInfoUploadRepository.getPurchaseOrder(dto);
		if (CommonUtils.isEmpty(ztePurchaseOrderDTOList)) {
			return;
		}

		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListNew = new ArrayList<>();
		// 获取送货单的采购订单行号、供应商名称、采购订单类型
		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListS = ztePurchaseOrderDTOList.stream().filter(i -> S.equals(i.getMovementCategory())).collect(Collectors.toList());
		if (CommonUtils.isNotEmpty(ztePurchaseOrderDTOListS)) {
			getIsrmReceiveOrder(ztePurchaseOrderDTOListS, dto);
			ztePurchaseOrderDTOListNew.addAll(ztePurchaseOrderDTOListS);
		}

		// 获取退货单的采购订单号、采购订单行号、数量、供应商名称、采购订单类型、源采购订单、源采购订单行
		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListH = ztePurchaseOrderDTOList.stream().filter(i -> H.equals(i.getMovementCategory())).collect(Collectors.toList());
		if (CommonUtils.isNotEmpty(ztePurchaseOrderDTOListH)) {
			getIsrmReturnOrder(ztePurchaseOrderDTOListH, dto);
			ztePurchaseOrderDTOListNew.addAll(ztePurchaseOrderDTOListH);
		}

		// 组装数据
		getPurchaseOrderData(ztePurchaseOrderDTOListNew, dto);
	}
	/* Ended by AICoder, pid:57bb5c2804d34cabb6e0724d2e4f597a */

	/* Started by AICoder, pid:eb00651dbf7842619a64b6f8cb15996f */
	/**
	 * 获取送货单的采购订单行号、供应商名称、采购订单类型
	 */
	public void getIsrmReceiveOrder(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListS, ZteStockInfoDTO dto) {

		List<IsrmReceiveOrderDTO> isrmReceiveOrderDTOList = new ArrayList<>();
		if (CommonUtils.isEmpty(ztePurchaseOrderDTOListS)) {
			return;
		}
		List<String> deliNoList = ztePurchaseOrderDTOListS.stream().map(ZtePurchaseOrderDTO::getMaterialDocNo).distinct().collect(Collectors.toList());
		boolean execFlag = true;
		Integer current = NumConstant.INT_1;
		Map<String, String> headerParamsMap = new HashMap<>(16);
		headerParamsMap.put(X_EMP_NO, dto.getEmpNo());
		headerParamsMap.put(INONE_APPCODE, inoneAppcode);
		headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
		headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
		for (List<String> list : CommonUtils.splitList(deliNoList, NumConstant.INT_10)) {
			// 调用采购的获取送货单订单号的分页方法
			while (execFlag) {
				Map<String, List<String>> jsonMap = new HashMap<>();
				jsonMap.put(DELI_NO_LIST, list);
				Map<String, Object> dataMap = new HashMap<>();
				dataMap.put(PAGE_NO, current);
				dataMap.put(PAGE_SIZE, NumConstant.INT_500);
				dataMap.put(SEARCH_MAP, jsonMap);
				Map<String, Object> paramMap = new HashMap<>();
				paramMap.put(DATA, dataMap);
				paramMap.put(REQUEST_TYPE, ORDER_QUERY_FOR_INFOR);
				try {
					String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + ISRM_ENTRY_INONE_URL,
							JacksonJsonConverUtil.beanToJson(paramMap), headerParamsMap);
					JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
					BusiAssertException.isEmpty(json, MessageId.GET_ORDER_QUERY_FOR_INFOR_FAILED);
					String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
					BusiAssertException.notEquals(RetCode.SUCCESS_CODE, retCode, MessageId.GET_ORDER_QUERY_FOR_INFOR_FAILED);
					String jsonStr = json.get(JSON_BO).get(LIST).toString();
					List<IsrmReceiveOrderDTO> receiveOrderDTOList = JacksonJsonConverUtil.jsonToListBean(jsonStr, new TypeReference<List<IsrmReceiveOrderDTO>>(){});
					if (CommonUtils.isNotEmpty(receiveOrderDTOList)) {
						isrmReceiveOrderDTOList.addAll(receiveOrderDTOList);
					}
					boolean isLastPage = json.get(JSON_BO).get(IS_LAST_PAGE).booleanValue();
					if (isLastPage) {
						execFlag = false;
					}
					current++;
				} catch (Exception e) {
                    execFlag = false;
					log.error("Post orderQueryForInfor exception:", e);
				}
			}
		}

		// 组装送货单的数据
		getReceivePurchaseOrderData(ztePurchaseOrderDTOListS, isrmReceiveOrderDTOList);
	}
	/* Ended by AICoder, pid:eb00651dbf7842619a64b6f8cb15996f */

	/* Started by AICoder, pid:af3fbb6b767f421babfdedb72dc4db70 */
	/**
	 * 获取退货单的采购订单号、采购订单行号、数量
	 */
	public void getIsrmReturnOrder(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListH, ZteStockInfoDTO dto) {

		List<IsrmReturnOrderDTO> isrmReturnOrderDTOList = new ArrayList<>();
		if (CommonUtils.isEmpty(ztePurchaseOrderDTOListH)) {
			return;
		}
		List<String> billNoList = ztePurchaseOrderDTOListH.stream().map(ZtePurchaseOrderDTO::getMaterialDocNo).distinct().collect(Collectors.toList());
		boolean execFlag = true;
		Integer current = NumConstant.INT_1;
		Map<String, String> headerParamsMap = new HashMap<>(16);
		headerParamsMap.put(X_EMP_NO, dto.getEmpNo());
		headerParamsMap.put(INONE_APPCODE, inoneAppcode);
		headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
		headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
		for (List<String> list : CommonUtils.splitList(billNoList, NumConstant.INT_10)) {
			// 调用采购的获取送货单订单号的分页方法
			while (execFlag) {
				Map<String, List<String>> jsonMap = new HashMap<>();
				jsonMap.put(BILL_NO_LIST, list);
				Map<String, Object> dataMap = new HashMap<>();
				dataMap.put(PAGE_NO, current);
				dataMap.put(PAGE_SIZE, NumConstant.INT_500);
				dataMap.put(SEARCH_MAP, jsonMap);
				Map<String, Object> paramMap = new HashMap<>();
				paramMap.put(DATA, dataMap);
				paramMap.put(REQUEST_TYPE, RETURN_ORDER_QUERY_FOR_INFOR);
				try {
					String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + ISRM_ENTRY_INONE_URL,
							JacksonJsonConverUtil.beanToJson(paramMap), headerParamsMap);
					JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
					BusiAssertException.isEmpty(json, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
					String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
					BusiAssertException.notEquals(RetCode.SUCCESS_CODE, retCode, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
					String jsonStr = json.get(JSON_BO).get(LIST).toString();
					List<IsrmReturnOrderDTO> returnOrderDTOList = JacksonJsonConverUtil.jsonToListBean(jsonStr, new TypeReference<List<IsrmReturnOrderDTO>>(){});
					if (CommonUtils.isNotEmpty(returnOrderDTOList)) {
						isrmReturnOrderDTOList.addAll(returnOrderDTOList);
					}
					boolean isLastPage = json.get(JSON_BO).get(IS_LAST_PAGE).booleanValue();
					if (isLastPage) {
						execFlag = false;
					}
					current++;
				} catch (Exception e) {
                    execFlag = false;
					log.error("Post returnOrderQueryForInfor exception:", e);
				}
			}
		}

		// 组装退库单
		getReturnPurchaseOrderData(ztePurchaseOrderDTOListH, isrmReturnOrderDTOList);
	}
	/* Ended by AICoder, pid:af3fbb6b767f421babfdedb72dc4db70 */

	/* Started by AICoder, pid:0d107b32ad82438b967793c2dd49e53e */
	/**
	 * 组装送货单
	 */
	public void getReceivePurchaseOrderData(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListS, List<IsrmReceiveOrderDTO> isrmReceiveOrderDTOList) {

		if (CommonUtils.isEmpty(isrmReceiveOrderDTOList)) {
			ztePurchaseOrderDTOListS.forEach(i -> i.setEnableFlag(FLAG_N));
			return;
		}
		for (ZtePurchaseOrderDTO ztePurchaseOrderDTO : ztePurchaseOrderDTOListS) {
			IsrmReceiveOrderDTO isrmReceiveOrderDTO = isrmReceiveOrderDTOList.stream().filter(i -> ztePurchaseOrderDTO.getMaterialDocNo().equals(i.getDeliNo())
					&& ztePurchaseOrderDTO.getMaterialDocLine().equals(i.getDeliRowNo().toString()) && ztePurchaseOrderDTO.getItemNo().equals(i.getItemNo())).findFirst().orElse(null);
			if (isrmReceiveOrderDTO == null) {
				ztePurchaseOrderDTO.setEnableFlag(FLAG_N);
			} else {
				ztePurchaseOrderDTO.setPoLineNo(isrmReceiveOrderDTO.getPoRowNo().toString());
				ztePurchaseOrderDTO.setVendorName(isrmReceiveOrderDTO.getSupplierName());
				ztePurchaseOrderDTO.setOriginalPurchaseOrderNo("");
				ztePurchaseOrderDTO.setOriginalPOLineNo("");
				ztePurchaseOrderDTO.setPurchaseOrderType(isrmReceiveOrderDTO.getCustSupType());
				ztePurchaseOrderDTO.setOrderDate(null==isrmReceiveOrderDTO.getPublishDate()?null:new SimpleDateFormat(STR_YYYY_MM_DD).format(isrmReceiveOrderDTO.getPublishDate()));
				if (Tools.equals(ztePurchaseOrderDTO.getHref11(), STR_461)) {
					ztePurchaseOrderDTO.setPurchaseOrderNo(isrmReceiveOrderDTO.getPoNo());
					ztePurchaseOrderDTO.setPurchaseNetPrice(isrmReceiveOrderDTO.getOrigPrice());
					ztePurchaseOrderDTO.setCurrencyCode(isrmReceiveOrderDTO.getOrigCurrency());
				}
			}
		}
	}
	/* Ended by AICoder, pid:0d107b32ad82438b967793c2dd49e53e */

	/* Started by AICoder, pid:7acd1a9e220548ddb0538b6d80507dac */
	/**
	 * 组装退库单
	 */
	public void getReturnPurchaseOrderData(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListH, List<IsrmReturnOrderDTO> isrmReturnOrderDTOList) {

		if (CommonUtils.isEmpty(isrmReturnOrderDTOList)) {
			ztePurchaseOrderDTOListH.forEach(i -> i.setEnableFlag(FLAG_N));
			return;
		}
		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListNew = new ArrayList<>();
		for (ZtePurchaseOrderDTO ztePurchaseOrderDTO : ztePurchaseOrderDTOListH) {
			List<IsrmReturnOrderDTO> isrmReturnOrderDTOS = isrmReturnOrderDTOList.stream().filter(i -> ztePurchaseOrderDTO.getMaterialDocNo().equals(i.getBillNo())
					&& ztePurchaseOrderDTO.getMaterialDocLine().equals(i.getBillRowNo()) && ztePurchaseOrderDTO.getItemNo().equals(i.getItemNo())).collect(Collectors.toList());
			if (CommonUtils.isEmpty(isrmReturnOrderDTOS)) {
				ztePurchaseOrderDTO.setEnableFlag(FLAG_N);
				ztePurchaseOrderDTOListNew.add(ztePurchaseOrderDTO);
				continue;
			}
			for (IsrmReturnOrderDTO isrmReturnOrderDTO : isrmReturnOrderDTOS) {
				ZtePurchaseOrderDTO ztePurchaseOrderDTONew = new ZtePurchaseOrderDTO();
				BeanUtils.copyProperties(ztePurchaseOrderDTO, ztePurchaseOrderDTONew);
				ztePurchaseOrderDTONew.setPurchaseOrderNo(isrmReturnOrderDTO.getPoNo());
				ztePurchaseOrderDTONew.setPoLineNo(isrmReturnOrderDTO.getPoRowNo());
				ztePurchaseOrderDTONew.setReceivingQuantity(isrmReturnOrderDTO.getRealOutNumber());
				ztePurchaseOrderDTONew.setVendorName(isrmReturnOrderDTO.getSupplierName());
				ztePurchaseOrderDTONew.setOriginalPurchaseOrderNo(isrmReturnOrderDTO.getPoNo());
				ztePurchaseOrderDTONew.setOriginalPOLineNo(isrmReturnOrderDTO.getPoRowNo());
				ztePurchaseOrderDTONew.setPurchaseOrderType(isrmReturnOrderDTO.getCustSupType());
				ztePurchaseOrderDTONew.setOrderDate(null==isrmReturnOrderDTO.getPublishDate()?null:new SimpleDateFormat(STR_YYYY_MM_DD).format(isrmReturnOrderDTO.getPublishDate()));
				if (Tools.equals(ztePurchaseOrderDTO.getHref11(), STR_471)) {
					ztePurchaseOrderDTONew.setPurchaseNetPrice(isrmReturnOrderDTO.getOrigPrice());
					ztePurchaseOrderDTONew.setCurrencyCode(isrmReturnOrderDTO.getOrigCurrency());
				}
				ztePurchaseOrderDTOListNew.add(ztePurchaseOrderDTONew);
			}
		}
		ztePurchaseOrderDTOListH.clear();
		ztePurchaseOrderDTOListH.addAll(ztePurchaseOrderDTOListNew);
	}
	/* Ended by AICoder, pid:7acd1a9e220548ddb0538b6d80507dac */

	/* Started by AICoder, pid:fc84a2f9407141da831cf7562721d39c */
	/**
	 * 组装数据
	 */
	public void getPurchaseOrderData(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListNew, ZteStockInfoDTO dto) {

		// 没有获取到送货单的采购订单行号，或退货单的采购订单号、采购订单行号、数量，则更新接口表的endtime，下次再推送B2B
		updatePoSoEndTime(ztePurchaseOrderDTOListNew);

		String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOUploadList = ztePurchaseOrderDTOListNew.stream().filter(i -> FLAG_Y.equals(i.getEnableFlag())).collect(Collectors.toList());
		if (CommonUtils.isEmpty(ztePurchaseOrderDTOUploadList)) {
			return;
		}
		List<String> billNoList = ztePurchaseOrderDTOUploadList.stream().map(ZtePurchaseOrderDTO::getMaterialDocNo).distinct().collect(Collectors.toList());
		List<ZtePurchaseOrderUploadHeadDTO> ztePurchaseOrderUploadHeadDTOList = new ArrayList<>();
		for (String billNo : billNoList) {
			List<ZtePurchaseOrderDTO> billNoDTOList = ztePurchaseOrderDTOUploadList.stream().filter(i -> billNo.equals(i.getMaterialDocNo())).collect(Collectors.toList());
			ZtePurchaseOrderUploadHeadDTO ztePurchaseOrderUploadHeadDTO = new ZtePurchaseOrderUploadHeadDTO();
			BeanUtils.copyProperties(billNoDTOList.get(INT_0), ztePurchaseOrderUploadHeadDTO);
			ztePurchaseOrderUploadHeadDTO.setDataTransferBatchNo(dataTransferBatchNo);
			ztePurchaseOrderUploadHeadDTO.setUuid(UUID.randomUUID().toString().trim().replaceAll(ONE_SPLIT, STRING_EMPTY));
			List<ZtePurchaseOrderUploadDetailDTO> ztePurchaseOrderUploadDetailDTOList = new ArrayList<>();
			for (ZtePurchaseOrderDTO ztePurchaseOrderDTO : billNoDTOList) {
				ZtePurchaseOrderUploadDetailDTO ztePurchaseOrderUploadDetailDTO = new ZtePurchaseOrderUploadDetailDTO();
				BeanUtils.copyProperties(ztePurchaseOrderDTO, ztePurchaseOrderUploadDetailDTO);
				ztePurchaseOrderUploadDetailDTOList.add(ztePurchaseOrderUploadDetailDTO);
			}
			ztePurchaseOrderUploadHeadDTO.setPurchaseOrderDeliveryItemParams(ztePurchaseOrderUploadDetailDTOList);
			ztePurchaseOrderUploadHeadDTOList.add(ztePurchaseOrderUploadHeadDTO);
		}

		// 采购订单收货数据推送B2B
		pushPurchaseOrderData(ztePurchaseOrderUploadHeadDTOList, dto);
	}
	/* Ended by AICoder, pid:fc84a2f9407141da831cf7562721d39c */

	/* Started by AICoder, pid:9587062079e7434cb6ace6e6907ca7dc */
	/**
	 * 更新接口表的endtime
	 */
	public void updatePoSoEndTime(List<ZtePurchaseOrderDTO> ztePurchaseOrderDTOListNew) {

		List<ZtePurchaseOrderDTO> ztePurchaseOrderDTONextList = ztePurchaseOrderDTOListNew.stream().filter(i -> FLAG_N.equals(i.getEnableFlag())).collect(Collectors.toList());
		if (CommonUtils.isEmpty(ztePurchaseOrderDTONextList)) {
			return;
		}
		// 没有获取到送货单的采购订单行号，或退货单的采购订单号、采购订单行号、数量，则下次再推送B2B
		// 入库单
		List<ZtePurchaseOrderDTO> zteEdiPoSList = ztePurchaseOrderDTONextList.stream().filter(i -> S.equals(i.getMovementCategory())).collect(Collectors.toList());
		if (CommonUtils.isNotEmpty(zteEdiPoSList)) {
			List<String> poSerialKeyList = zteEdiPoSList.stream().map(ZtePurchaseOrderDTO::getSerialkey).distinct().collect(Collectors.toList());
			for (List<String> list : CommonUtils.splitList(poSerialKeyList, NumConstant.INT_500)) {
				zteStockMoveInfoUploadRepository.updateEdiPoSBySerialKey(list);
			}
		}
		// 出库单
		List<ZtePurchaseOrderDTO> zteEdiSoSList = ztePurchaseOrderDTONextList.stream().filter(i -> H.equals(i.getMovementCategory())).collect(Collectors.toList());
		if (CommonUtils.isNotEmpty(zteEdiSoSList)) {
			List<String> soMaterialDocNoList = zteEdiSoSList.stream().map(ZtePurchaseOrderDTO::getMaterialDocNo).distinct().collect(Collectors.toList());
			ztePurchaseOrderDTOListNew.stream().filter(i -> H.equals(i.getMovementCategory())).forEach(j ->{
				if (soMaterialDocNoList.contains(j.getMaterialDocNo())) {
					j.setEnableFlag(FLAG_N);
				}
			});
			for (List<String> list : CommonUtils.splitList(soMaterialDocNoList, NumConstant.INT_500)) {
				zteStockMoveInfoUploadRepository.updateEdiSoSBySerialKey(list);
			}
		}
	}
	/* Ended by AICoder, pid:9587062079e7434cb6ace6e6907ca7dc */

	/* Started by AICoder, pid:73adbf83a6e7428389257a9db6a0f3a8 */
	/**
	 * 采购订单收货数据推送B2B
	 */
	public void pushPurchaseOrderData(List<ZtePurchaseOrderUploadHeadDTO> ztePurchaseOrderUploadHeadDTOList, ZteStockInfoDTO dto) {

		if (CommonUtils.isEmpty(ztePurchaseOrderUploadHeadDTOList)) {
			return;
		}
		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		for (List<ZtePurchaseOrderUploadHeadDTO> tempList : CommonUtils.splitList(ztePurchaseOrderUploadHeadDTOList, NumConstant.INT_500)) {
			//组装对象
			CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
			customerDataLogDTO.setId(UUID.randomUUID().toString());
			customerDataLogDTO.setOrigin(INFOR_WMS);
			customerDataLogDTO.setCustomerName(BYTE_DANCE);
			customerDataLogDTO.setProjectName(PURCHASE_ORDER_ZH);
			customerDataLogDTO.setProjectPhase(STRING_EMPTY);
			customerDataLogDTO.setCooperationMode(STRING_EMPTY);
			customerDataLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_GOODS_RECEIPT_OF_PURCHASE_ORDER);
			customerDataLogDTO.setContractNo(STRING_EMPTY);
			customerDataLogDTO.setTaskNo(tempList.get(INT_0).getDataTransferBatchNo());
			customerDataLogDTO.setItemNo(STRING_EMPTY);
			customerDataLogDTO.setSn(STRING_EMPTY);
			Map<String, Object> map = new HashMap<>();
			map.put(DATA, tempList);
			customerDataLogDTO.setJsonData(JSON.toJSONString(map));
			customerDataLogDTO.setFactoryId(INT_51);
			customerDataLogDTO.setCreateBy(dto.getEmpNo());
			customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
			customerDataLogDTOList.add(customerDataLogDTO);

			//组装日志对象
			ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
			BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
			zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
		}

		//记录上传日志
		for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
			zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
		}

		//上传B2B
		for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
			imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, dto.getEmpNo());
		}
	}
	/* Ended by AICoder, pid:73adbf83a6e7428389257a9db6a0f3a8 */

	/**
	 * 推送转代码单据信息给采购
	 */
	@Override
	public void pushTransferSkuToIsrm(String xEmpNo) {
		// 获取需要推送采购的转代码单据
		List<IscpEdiLog> iscpEdiLogList = zteStockMoveInfoUploadRepository.getTransferNo();
		if (Tools.isEmpty(iscpEdiLogList)) {
			return;
		}
		List<String> transferInNoList = iscpEdiLogList.stream().filter(i -> i.getSourceTable().equals(EDI_ZMD_PO_S))
				.map(IscpEdiLog::getExternkey).distinct().collect(Collectors.toList());
		List<String> transferOutNoList = iscpEdiLogList.stream().filter(i -> i.getSourceTable().equals(EDI_ZMD_SO_S))
				.map(IscpEdiLog::getExternkey).distinct().collect(Collectors.toList());
		// 获取需要推送给采购的数据
		if (Tools.isNotEmpty(transferInNoList)) {
			List<InforIsrmTransferDTO> transferInList = zteStockMoveInfoUploadRepository.getEdiPoSList(transferInNoList);
			pushTransfer(transferInList, xEmpNo, EDI_ZMD_PO_S);
		}
		if (Tools.isNotEmpty(transferOutNoList)) {
			List<InforIsrmTransferDTO> transferOutList = zteStockMoveInfoUploadRepository.getEdiSoSList(transferOutNoList);
			pushTransfer(transferOutList, xEmpNo, EDI_ZMD_SO_S);
		}
	}

	public void pushTransfer(List<InforIsrmTransferDTO> transferList, String xEmpNo, String sourceTable) {
		if (Tools.isEmpty(transferList)) {
			return;
		}
		Map<String, String> headerParamsMap = new HashMap<>(16);
		headerParamsMap.put(X_EMP_NO, xEmpNo);
		headerParamsMap.put(INONE_APPCODE, inoneAppcode);
		headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
		headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put(DATA, transferList);
		paramMap.put(REQUEST_TYPE, TRANSFER_STOCK_RECEIVE_FOR_INFOR);
		try {
			String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + ISRM_ENTRY_INONE_URL,
					JacksonJsonConverUtil.beanToJson(paramMap), headerParamsMap);
			JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
			BusiAssertException.isEmpty(json, MessageId.TRANSFER_STOCK_RECEIVE_FOR_INFOR_FAILED);
			String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
			List<String> transferNoList = transferList.stream().map(InforIsrmTransferDTO::getStockTransferNo).distinct().collect(Collectors.toList());
			if (Tools.equals(RetCode.SUCCESS_CODE, retCode)) {
				zteStockMoveInfoUploadRepository.updateIscpEdiLog(transferNoList, sourceTable, NumConstant.INT_0, null);
			} else {
				String errorStr = json.get(JSON_BO).toString();
				zteStockMoveInfoUploadRepository.updateIscpEdiLog(transferNoList, sourceTable, INT_B1,
						errorStr.length() > NumConstant.INT_2000 ? errorStr.substring(NumConstant.INT_0, NumConstant.INT_1999) : errorStr);
			}
		} catch (Exception e) {
			log.error("Post transferStockReceiveForInfor exception:", e);
		}
	}
}
