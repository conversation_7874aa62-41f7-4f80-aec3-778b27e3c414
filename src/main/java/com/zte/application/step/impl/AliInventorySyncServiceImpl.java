package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.step.AliInventorySyncService;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.interfaces.infor.dto.AliInventoryRequest;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.zte.common.model.MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class AliInventorySyncServiceImpl implements AliInventorySyncService {
    @Autowired
	private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private ZteAlibabaServiceImpl zteAlibabaService;

    @Override
    public void syncAliInventoryTransData(String empNo) {
        // 1. 获取时间参数
        String createEndStr = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        // 快码取消息类型
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100010);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        // 快码取最后更新时间
        SysLookupValuesDTO lookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100008800002);
        BusiAssertException.isEmpty(lookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        String createStartStr = lookupValuesDTO.getLookupMeaning();
        // 2. 构建请求参数
        AliInventoryRequest aliRequest = new AliInventoryRequest();
        aliRequest.setFactoryCode(ALIBABA_FACTORY_CODE);
        aliRequest.setCreateStartStr(createStartStr);
        aliRequest.setCreateEndStr(createEndStr);
        // 3. 调用阿里接口获取库存交易明细数据
        pushDataToB2B(aliRequest, sysLookupValuesDTO.getLookupMeaning());
    }

    /**
     * 调用b2b接口
     */
    public void pushDataToB2B(AliInventoryRequest aliRequest, String messageType){
        ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
        dto.setMessageType(messageType);
        dto.setEmpNo(SYSTEM);
        zteAlibabaService.pushDataToB2B(JSON.toJSONString(aliRequest), dto, ALI_INVENTORY_SYNC_ZH);
        //把日期更新到快码中
		inventoryholdRecordRepository.updateLookupMeaning(LOOKUP_CODE_100008800002,aliRequest.getCreateEndStr());
	}


}
