package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.step.ZteBaiduStockInfoUploadService;
import com.zte.application.step.ZteStockInfoUploadService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforIapsRepository;
import com.zte.domain.model.infor.SysWebserviceConfigRepository;
import com.zte.domain.model.infor.ZteBaiduStockInfoUploadRepository;
import com.zte.domain.model.infor.ZteStockMoveInfoUploadRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.interfaces.step.dto.CivControlInfoDTO;
import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.interfaces.step.dto.ErpSearchInfoDTO;
import com.zte.interfaces.step.dto.ErpStockResultDTO;
import com.zte.interfaces.step.dto.ZteBaiduInfoDTO;
import com.zte.interfaces.step.dto.ZteBaiduReturnDTO;
import com.zte.interfaces.step.dto.ZteStockInfoDTO;
import com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.ACCEPT;
import static com.zte.common.utils.Constant.APPLICATION_JSON;
import static com.zte.common.utils.Constant.BAIDU;
import static com.zte.common.utils.Constant.BAIDU_STOCK_UPLOAD_ERROR_DATA;
import static com.zte.common.utils.Constant.CONTENT_TYPE;
import static com.zte.common.utils.Constant.CUSTOMER_SUPPLY_TYPE;
import static com.zte.common.utils.Constant.DATA;
import static com.zte.common.utils.Constant.ECCN;
import static com.zte.common.utils.Constant.ERP_ITEM_INONE_URL;
import static com.zte.common.utils.Constant.EXTERNAL_ITEM_INONE_URL;
import static com.zte.common.utils.Constant.EXTRAFIELDS;
import static com.zte.common.utils.Constant.FIELD_NAME;
import static com.zte.common.utils.Constant.FIELD_TYPE;
import static com.zte.common.utils.Constant.FIELD_VALUE;
import static com.zte.common.utils.Constant.GET_ONHAND_QTY;
import static com.zte.common.utils.Constant.IN;
import static com.zte.common.utils.Constant.INFOR_WMS;
import static com.zte.common.utils.Constant.INONE_APPCODE;
import static com.zte.common.utils.Constant.ITEM_CODE;
import static com.zte.common.utils.Constant.ITEM_NO;
import static com.zte.common.utils.Constant.IWMS;
import static com.zte.common.utils.Constant.JSON_CODE;
import static com.zte.common.utils.Constant.MODULES;
import static com.zte.common.utils.Constant.ORGANIZATION_ID;
import static com.zte.common.utils.Constant.SPLIT;
import static com.zte.common.utils.Constant.STOCK_INFO_ZH;
import static com.zte.common.utils.Constant.STRING_EMPTY;
import static com.zte.common.utils.Constant.STR_B1;
import static com.zte.common.utils.Constant.SUBINVENTORYCODE;
import static com.zte.common.utils.Constant.X_EMP_NO;
import static com.zte.common.utils.Constant.X_LANG_ID;
import static com.zte.common.utils.Constant.X_LANG_ID_ZH;
import static com.zte.common.utils.Constant.ZTE1;
import static com.zte.common.utils.Constant.ZTEIMES_BAIDU_STOCK;
import static com.zte.common.utils.Constant.ZTE_IWMS_MANAGE;
import static com.zte.common.utils.NumConstant.INT_51;

/**
 * <AUTHOR>
 */
@Service
public class ZteBaiduStockInfoUploadServiceImpl implements ZteBaiduStockInfoUploadService {

    private static final Logger log = LoggerFactory.getLogger(ZteBaiduStockInfoUploadServiceImpl.class);
    @Autowired
    private ZteBaiduStockInfoUploadRepository zteBaiduStockInfoUploadRepository;
    @Autowired
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    @Autowired
    private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;
    @Autowired
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Autowired
    private StepTransferRepository stepTransferRepository;
    @Autowired
    private EmailUtil emailUtil;
    @Autowired
    private InforIapsRepository inforIapsRepository;
    @Autowired
    private SysWebserviceConfigRepository sysWebserviceConfigRepository;
    @Autowired
    private ZteStockInfoUploadService zteStockInfoUploadService;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;
    private static final String CONDITIONS = "conditions";

    @Override
    public void baiduStockInfoUpload(ZteBaiduInfoDTO dto) throws Exception {
        // 校验物料代码的个数不能超过500
        BusiAssertException.isTrue(Tools.isNotEmpty(dto.getItemNo()) && dto.getItemNo().size() > NumConstant.INT_500,
                MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED);
        //查询基础数据
        List<ZteBaiduReturnDTO> zteBaiduReturnDTOS = zteBaiduStockInfoUploadRepository.getBaiduStockInfoList(dto);
        if (CommonUtils.isEmpty(zteBaiduReturnDTOS)) {
            return;
        }
        // 调采购接口 获取 库存商务模式
        queryCivControlInfoList(zteBaiduReturnDTOS, dto.getEmpNo());
        // 调erp接口 获取 库存总数量和好件在库数量
        queryTotalQuantity(zteBaiduReturnDTOS, dto.getEmpNo());
        // 最长库龄
        queryMaxStockAge(zteBaiduReturnDTOS);

        //保存库存快照
        saveStockUploadSnapshot(zteBaiduReturnDTOS);
        //记录上传日志 以及B2B
        pushDataToB2BAndUploadLog(zteBaiduReturnDTOS, dto);

    }

    private void pushDataToB2BAndUploadLog(List<ZteBaiduReturnDTO> zteBaiduReturnDTOS, ZteBaiduInfoDTO dto) {
        String dataTransferBatchNo = zteStockInfoUploadService.getDataTransferBatchNo(ZTE1);
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        for (List<ZteBaiduReturnDTO> tempList : CommonUtils.splitList(zteBaiduReturnDTOS, NumConstant.INT_500)) {
            //组装B2B对象
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString());
            customerDataLogDTO.setOrigin(INFOR_WMS);
            customerDataLogDTO.setCustomerName(BAIDU);
            customerDataLogDTO.setProjectName(STOCK_INFO_ZH);
            customerDataLogDTO.setProjectPhase(STRING_EMPTY);
            customerDataLogDTO.setCooperationMode(STRING_EMPTY);
            customerDataLogDTO.setMessageType(ZTEIMES_BAIDU_STOCK);
            customerDataLogDTO.setContractNo(STRING_EMPTY);
            customerDataLogDTO.setTaskNo(dataTransferBatchNo);
            customerDataLogDTO.setItemNo(STRING_EMPTY);
            customerDataLogDTO.setSn(STRING_EMPTY);
            Map<String, Object> map = new HashMap<>();
            map.put(DATA, tempList);
            customerDataLogDTO.setJsonData(JSON.toJSONString(map, SerializerFeature.WriteDateUseDateFormat));
            customerDataLogDTO.setFactoryId(INT_51);
            customerDataLogDTO.setCreateBy(dto.getEmpNo());
            customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
            customerDataLogDTOList.add(customerDataLogDTO);
            //组装日志对象
            ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
            BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
            zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
        }
        List<ZteBaiduReturnDTO> list = zteBaiduReturnDTOS.stream()
                .filter(item -> StringUtils.isBlank(item.getStockType()) || item.getMaxStockAge() == null || item.getMaxStockAge().compareTo(-1) == 0)
                .collect(Collectors.toList());
        if (CommonUtils.isNotEmpty(list)) {
            // 缺少 库存商务模式/最⻓库龄 记录日志
            saveStockUploadErrorData(zteBaiduReturnDTOS, dto, dataTransferBatchNo);
        }
        //记录上传日志
        for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
            zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
        }
        //上传B2B
        for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
            imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, dto.getEmpNo());
        }
    }

    private void saveStockUploadErrorData(List<ZteBaiduReturnDTO> zteBaiduReturnDTOS, ZteBaiduInfoDTO dto, String dataTransferBatchNo) {
        List<String> stockTypeEmptyList = zteBaiduReturnDTOS.stream()
                .filter(item -> StringUtils.isBlank(item.getStockType()))
                .map(ZteBaiduReturnDTO::getOemPartName)
                .distinct()
                .collect(Collectors.toList());
        List<String> maxStockAgeEmptyList = zteBaiduReturnDTOS.stream()
                .filter(item -> item.getMaxStockAge() == null || item.getMaxStockAge().compareTo(-1) == 0)
                .map(ZteBaiduReturnDTO::getOemPartName)
                .distinct()
                .collect(Collectors.toList());
        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        zteStockInfoUploadLogDTO.setId(UUID.randomUUID().toString());
        zteStockInfoUploadLogDTO.setOrigin(INFOR_WMS);
        zteStockInfoUploadLogDTO.setCustomerName(BAIDU);
        zteStockInfoUploadLogDTO.setProjectName(STOCK_INFO_ZH);
        zteStockInfoUploadLogDTO.setProjectPhase(STRING_EMPTY);
        zteStockInfoUploadLogDTO.setCooperationMode(STRING_EMPTY);
        zteStockInfoUploadLogDTO.setMessageType(ZTEIMES_BAIDU_STOCK);
        zteStockInfoUploadLogDTO.setContractNo(STRING_EMPTY);
        zteStockInfoUploadLogDTO.setTaskNo(dataTransferBatchNo);
        zteStockInfoUploadLogDTO.setItemNo(STRING_EMPTY);
        zteStockInfoUploadLogDTO.setSn(STRING_EMPTY);
        Map<String, Object> map = new HashMap<>();
        map.put("stockTypeEmptyList", stockTypeEmptyList);
        map.put("maxStockAgeEmptyList", maxStockAgeEmptyList);
        zteStockInfoUploadLogDTO.setJsonData(JSON.toJSONString(map));
        zteStockInfoUploadLogDTO.setFactoryId(INT_51);
        zteStockInfoUploadLogDTO.setCreateBy(dto.getEmpNo());
        zteStockInfoUploadLogDTO.setLastUpdatedBy(dto.getEmpNo());
        zteStockInfoUploadLogDTO.setRemark(BAIDU_STOCK_UPLOAD_ERROR_DATA);
        zteStockInfoUploadRepository.insertStockUploadLog(Collections.singletonList(zteStockInfoUploadLogDTO));
    }

    /**
     * 保存库存快照
     *
     * @param zteBaiduReturnDTOS zteBaiduReturnDTOS
     */
    private void saveStockUploadSnapshot(List<ZteBaiduReturnDTO> zteBaiduReturnDTOS) {
        List<ZteStockInfoDTO> zteStockInfoDTOList = new ArrayList<>();
        for (ZteBaiduReturnDTO zteBaiduReturnDTO : zteBaiduReturnDTOS) {
            ZteStockInfoDTO zteStockInfoDTO = new ZteStockInfoDTO();
            zteStockInfoDTO.setItemNo(zteBaiduReturnDTO.getOemPartName());
            zteStockInfoDTO.setStockNo(STRING_EMPTY);
            zteStockInfoDTO.setStockQuantity(zteBaiduReturnDTO.getTotalStockAmount());
            zteStockInfoDTOList.add(zteStockInfoDTO);
        }
        for (List<ZteStockInfoDTO> tempZteStockInfoDTOList : CommonUtils.splitList(zteStockInfoDTOList, NumConstant.INT_500)) {
            zteStockInfoUploadRepository.insertStockUploadSnapshot(tempZteStockInfoDTOList);
        }
    }

    /**
     * 最长库龄
     *
     * @param zteStockInfoDTOList
     */
    private void queryMaxStockAge(List<ZteBaiduReturnDTO> zteStockInfoDTOList) {
        List<String> list = zteStockInfoDTOList.stream()
                .map(ZteBaiduReturnDTO::getOemPartName)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<AllocateExceptionMonitorVo> barcodeAgeList = stepTransferRepository.getMaxStockAgeByBarcode(list);
        Map<String, String> itemMap = barcodeAgeList.stream().collect(Collectors.toMap(AllocateExceptionMonitorVo::getItemNo,
                AllocateExceptionMonitorVo::getStoreAge, (v1, v2) -> v1));
        for (ZteBaiduReturnDTO zteBaiduReturnDTO : zteStockInfoDTOList) {
            String maxStockAge = itemMap.getOrDefault(zteBaiduReturnDTO.getOemPartName(), STR_B1);
            //最大库龄
            zteBaiduReturnDTO.setMaxStockAge(Integer.parseInt(maxStockAge));
        }
    }

    /**
     * 库存总数量和好件在库数量及采集时间
     *
     * @param zteStockInfoDTOList
     * @param empNo
     */
    private void queryTotalQuantity(List<ZteBaiduReturnDTO> zteStockInfoDTOList, String empNo) throws Exception {
        // 库存采集时间
        Date date = new Date();
        for (ZteBaiduReturnDTO zteBaiduReturnDTO : zteStockInfoDTOList) {
            zteBaiduReturnDTO.setStockTime(date);
        }
        // 物料代码集合
        List<String> itemNos = zteStockInfoDTOList.stream()
                .map(ZteBaiduReturnDTO::getOemPartName)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        // 库存组织ID
        List<String> organizationIdList = inforIapsRepository.getCodeByCodelkup();
        // 子库存编码
        List<SysLookupValuesDTO> webserviceInterface = sysWebserviceConfigRepository.getWebserviceInterface(Collections.singletonList("1000081"));
        List<String> subinventoryList =
                webserviceInterface.stream().map(SysLookupValuesDTO::getLookupMeaning).distinct().collect(Collectors.toList());
        // 请求路径
        String requestUrl = inoneUrl + ERP_ITEM_INONE_URL;
        // 请求头
        Map<String, String> httpRequestHeader = new HashMap<>();
        httpRequestHeader.put(X_EMP_NO, empNo);
        httpRequestHeader.put(X_LANG_ID, X_LANG_ID_ZH);
        httpRequestHeader.put(INONE_APPCODE, inoneAppcode);
        httpRequestHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        httpRequestHeader.put(ACCEPT, APPLICATION_JSON);
        // 请求体
        ErpSearchInfoDTO erpSearchInfoDTO = new ErpSearchInfoDTO();
        erpSearchInfoDTO.setApiCode(GET_ONHAND_QTY);
        erpSearchInfoDTO.setInvokeKey(NumConstant.STR_5);
        erpSearchInfoDTO.setProductType(IWMS);
        erpSearchInfoDTO.setServiceCode(ZTE_IWMS_MANAGE);
        Map<String, String> configParamMap = new HashMap<>(NumConstant.INT_16);
        String itemCode = String.join(SPLIT, itemNos);
        configParamMap.put(ITEM_CODE, itemCode);
        erpSearchInfoDTO.setConfigParamMap(configParamMap);
        String subinventory = String.join(SPLIT, subinventoryList);
        configParamMap.put(SUBINVENTORYCODE, subinventory);

        // 根据组织 循环调用inone-erp接口
        String organizationId = String.join(SPLIT, organizationIdList);
        configParamMap.put(ORGANIZATION_ID, organizationId);
        String result = HttpClientUtil.httpPostWithJSON(requestUrl, JSON.toJSONString(erpSearchInfoDTO),
                httpRequestHeader);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        BusiAssertException.isEmpty(json, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
        String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
        BusiAssertException.notEquals(RetCode.SUCCESS_CODE, retCode, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
        JsonNode dataArray = json.get(Constant.JSON_BO);
        String dataStr = Optional.ofNullable(dataArray)
                .map(JsonNode::toString)
                .orElse("");
        List<ErpStockResultDTO> dtoList = JacksonJsonConverUtil.jsonToListBean(dataStr,
                new TypeReference<ArrayList<ErpStockResultDTO>>() {
                });
        if (CommonUtils.isEmpty(dtoList)) {
            return;
        }
        Map<String, Integer> map = dtoList.stream()
                .collect(Collectors.toMap(ErpStockResultDTO::getSegment1,
                        item -> item.getQuantity() == null ? Constant.INT_0 : item.getQuantity(), Integer::sum));
        for (ZteBaiduReturnDTO zteBaiduReturnDTO : zteStockInfoDTOList) {
            zteBaiduReturnDTO.setTotalStockAmount(map.getOrDefault(zteBaiduReturnDTO.getOemPartName(), Constant.INT_0));
            zteBaiduReturnDTO.setInStockAmount(map.getOrDefault(zteBaiduReturnDTO.getOemPartName(), Constant.INT_0));
        }
    }

    public void queryCivControlInfoList(List<ZteBaiduReturnDTO> zteBaiduReturnDTOList, String empNo) throws Exception {
        List<CivControlInfoDTO> infoDTOList = new ArrayList();
        List<String> itemNos = zteBaiduReturnDTOList.stream()
                .map(ZteBaiduReturnDTO::getOemPartName)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        // 请求路径
        String requestUrl = inoneUrl + EXTERNAL_ITEM_INONE_URL;
        // 请求头
        Map<String, String> httpRequestHeader = new HashMap<>();
        httpRequestHeader.put(X_EMP_NO, empNo);
        httpRequestHeader.put(X_LANG_ID, X_LANG_ID_ZH);
        httpRequestHeader.put(INONE_APPCODE, inoneAppcode);
        httpRequestHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        httpRequestHeader.put(ACCEPT, APPLICATION_JSON);
        // 请求体
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_RED_4);
        requestParam.put(Constant.PAGE_SIZE, NumConstant.BATCH_SIZE);
        requestParam.put(EXTRAFIELDS, Arrays.asList(CUSTOMER_SUPPLY_TYPE));
        requestParam.put(MODULES, Arrays.asList(ECCN));
        Map<String, Object> conditionMap = new HashMap<>(Constant.INT_RED_4);
        conditionMap.put(FIELD_NAME, ITEM_NO);
        conditionMap.put(FIELD_TYPE, IN);

        // 调用inone-采购接口 分批查
        List<List<String>> items = CommonUtils.splitList(itemNos, NumConstant.BATCH_SIZE);
        for (List<String> itemp : items) {
            conditionMap.put(FIELD_VALUE, itemp.stream().collect(Collectors.joining(Constant.COMMA)));
            requestParam.put(CONDITIONS, Collections.singletonList(conditionMap));
            String result = HttpClientUtil.httpPostWithJSON(requestUrl, JSON.toJSONString(requestParam),
                    httpRequestHeader);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
            BusiAssertException.isEmpty(json, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
            String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
            BusiAssertException.notEquals(RetCode.SUCCESS_CODE, retCode, MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
            BusiAssertException.isEmpty(json.get(Constant.JSON_BO), MessageId.GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED);
            JsonNode dataArray = json.get(Constant.JSON_BO).get(Constant.LIST);
            String dataStr = Optional.ofNullable(dataArray)
                    .map(JsonNode::toString)
                    .orElse("");
            List<CivControlInfoDTO> dtoList = JacksonJsonConverUtil.jsonToListBean(dataStr,
                    new TypeReference<ArrayList<CivControlInfoDTO>>() {
                    });
            if (CommonUtils.isNotEmpty(dtoList)) {
                infoDTOList.addAll(dtoList);
            }
        }
        Map<String, String> itemMap =
                infoDTOList.stream().collect(Collectors.toMap(CivControlInfoDTO::getItemNo, item ->
                                StringUtils.defaultString(item.getCustomerSupplyType(), STRING_EMPTY),
                        (k1, k2) -> k1));
        for (ZteBaiduReturnDTO zteBaiduReturnDTO : zteBaiduReturnDTOList) {
            zteBaiduReturnDTO.setStockType(itemMap.getOrDefault(zteBaiduReturnDTO.getOemPartName(), STRING_EMPTY));
        }
    }
}
