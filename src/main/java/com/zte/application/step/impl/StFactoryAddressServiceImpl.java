/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-09-02
 * 修改历史 :
 *   1. [2019-09-02] 创建文件 by 6396000647
 **/
package com.zte.application.step.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.step.StFactoryAddressService;
import com.zte.domain.model.step.StFactoryAddress;
import com.zte.domain.model.step.StFactoryAddressRepository;
import com.zte.interfaces.step.dto.StAddressDTO;
import com.zte.interfaces.step.dto.StFactoryAddressDTO;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Service 
public class StFactoryAddressServiceImpl implements StFactoryAddressService {

    @Autowired
    private StFactoryAddressRepository stFactoryAddressRepository;

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertStFactoryAddress(StFactoryAddress record) {
        stFactoryAddressRepository.insertStFactoryAddress(record);
    }

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertStFactoryAddressSelective(StFactoryAddress record) {
        stFactoryAddressRepository.insertStFactoryAddressSelective(record);
    }

    /**
     * 根据主键删除实体数据
     * 
     * @param record
     **/
    @Override
    public void deleteStFactoryAddressById(StFactoryAddress record) {
        stFactoryAddressRepository.deleteStFactoryAddressById(record);
    }

    /**
     * 有选择性的更新实体数据
     * 
     * @param record
     **/
    @Override
    public void updateStFactoryAddressByIdSelective(StFactoryAddress record) {
        stFactoryAddressRepository.updateStFactoryAddressByIdSelective(record);
    }

    /**
     * 根据主键更新实体数据
     * 
     * @param record
     **/
    @Override
    public void updateStFactoryAddressById(StFactoryAddress record) {
        stFactoryAddressRepository.updateStFactoryAddressById(record);
    }

    @Override
    public List<StFactoryAddressDTO> selectStFactoryAddressAll(StAddressDTO record) {
        return stFactoryAddressRepository.selectStFactoryAddressAll(record);
    }

    /**
     * 根据主键查询实体信息
     * 
     * @param record
     * @return StFactoryAddress
     **/
    @Override
    public StFactoryAddress selectStFactoryAddressById(StFactoryAddress record) {
        return stFactoryAddressRepository.selectStFactoryAddressById(record);
    }
}