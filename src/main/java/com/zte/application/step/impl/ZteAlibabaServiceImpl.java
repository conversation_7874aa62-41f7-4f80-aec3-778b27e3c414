package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.impl.InventoryDiffQueryServiceImpl;
import com.zte.application.step.ZteAlibabaService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.ZteAliApprovalRepository;
import com.zte.domain.model.step.ZteAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zte.application.onlinefallback.ConstantApplyBillStatus.SUPPLIEER_DEALED;
import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_1;
import static com.zte.common.utils.NumConstant.INT_2;
import static com.zte.common.utils.NumConstant.INT_3;
import static com.zte.common.utils.NumConstant.INT_4;

/**
 * <AUTHOR>
 *
 */
@Service
public class ZteAlibabaServiceImpl implements ZteAlibabaService {

	private static final Logger log = LoggerFactory.getLogger(ZteAlibabaServiceImpl.class);

	@Autowired
	private ZteAlibabaRepository zteAlibabaRepository;
	@Autowired
	private ZteSnBoundPkgIdRepository zteSnBoundPkgIdRepository;
	@Autowired
	private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
	@Autowired
	private InventoryholdRecordRepository inventoryholdRecordRepository;
	@Autowired
	private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
	@Autowired
	private ZteAliApprovalRepository zteAliApprovalRepository;
	@Autowired
	private InforIwmsIscpRepository inforIwmsIscpRepository;
	@Autowired
	private ZteAlibabStockInfoUploadRepository zteAlibabStockInfoUploadRepository;
	@Autowired
	private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;
	@Autowired
	private ZteAlibabaInventoryRepository zteAlibabaInventoryRepository;
	@Autowired
	private TransferBoxRepository transferBoxRepository;
	@Autowired
	private StockDiscrepancyFeedbackRepository stockDiscrepancyFeedbackRepository;
	@Autowired
	private InventoryDiffQueryServiceImpl inventoryDiffQueryService;
	@Autowired
	private InventoryTransRepository inventoryTransRepository;
	@Autowired
	private EdiPoSRepository ediPosRepository;

	/**
	 * 部件出⼊库计划申请&执⾏&核销
	 */
	@Override
	public void deductionPlan(ZteDeductionPlanParamDTO dto) {

		BusiAssertException.isEmpty(dto.getSourceSystem(), SOURCE_SYSTEM_EMPTY);
		BusiAssertException.isEmpty(dto.getBusinessType(), BUSINESS_TYPE_EMPTY);
		BusiAssertException.isEmpty(dto.getBillType(), BILL_TYPE_EMPTY);

		List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList;
		if (Tools.equals(STR_1, dto.getSourceSystem())) {
			zteDeductionBillDTOList = zteAlibabaRepository.getBillInfo(dto);
		} else {
			zteDeductionBillDTOList = zteAliApprovalRepository.getBillInfo(dto);
			if (Tools.isNotEmpty(zteDeductionBillDTOList)) {
				List<ItemNoMpnDTO> controlItemNo = zteAlibabaRepository.getControlItemNo(Collections.singletonList(zteDeductionBillDTOList.get(INT_0).getMasterDataReferenceNumber()));
				BusiAssertException.isEmpty(controlItemNo, CUSTOMER_CONTROL_TYPE_NOT_ALIBABA);
				zteDeductionBillDTOList.forEach(item -> {
					item.setMpn(controlItemNo.get(INT_0).getMpn());
					item.setIsSn(controlItemNo.get(INT_0).getIsSn());
				});
			}
		}
		BusiAssertException.isEmpty(zteDeductionBillDTOList, BILL_NOT_EXISTS);

		// 校验业务数据
		checkBillInfo(dto, zteDeductionBillDTOList);

		// 组装部件出⼊库数据
		ZteDeductionPlanDTO zteDeductionPlanDTO = getDeductionPlanData(dto, zteDeductionBillDTOList);

		// 推送B2B
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100001);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		dto.setMessageType(sysLookupValuesDTO.getLookupMeaning());
		pushDataToB2B(JSON.toJSONString(zteDeductionPlanDTO), dto, DEDUCTION_PLAN_INFO_ZH);

		// 交易申请、核销申请，更新单据头状态，保存plan_id
		if (APPLY_TYPE_STR_LIST.contains(dto.getBusinessType())) {
			dto.setStatus(STR_APPROVALING);
			// oms系统
			if (Tools.equals(STR_1, dto.getSourceSystem())) {
				zteAlibabaRepository.updateBillHead(dto);
			}
			// iwms系统
			if (Tools.equals(STR_2, dto.getSourceSystem())) {
				zteAliApprovalRepository.updateBillHead(dto);
			}
		}
	}

	private void checkBillInfo(ZteDeductionPlanParamDTO dto, List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList) {

		ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = zteDeductionBillDTOList.get(INT_0);
		// 交易申请、核销申请
		if (APPLY_TYPE_STR_LIST.contains(dto.getBusinessType())) {
			// OMS系统单据状态必须为“已提交”
			if (Tools.equals(STR_1, dto.getSourceSystem())) {
				if (BILL_TYPE_STR_LIST.contains(dto.getBillType())) {
					BusiAssertException.notEquals(STR_SUBMITED,zteDeductionBillInfoDTO.getStatus(), BILL_STATUS_NOT_SUBMITED);
				}
				if (Tools.equals(STR_3, dto.getBillType())) {
					BusiAssertException.notEquals(CHECKING, zteDeductionBillInfoDTO.getStatus(), BILL_STATUS_NOT_RECHECK);
				}
				if (Tools.equals(STR_4, dto.getBillType())) {
					BusiAssertException.notEquals(RECHECK, zteDeductionBillInfoDTO.getStatus(), BILL_STATUS_NOT_RECHECK);
				}
			}
			//iwms系统单据类型必须为“ECSS已审核”
			if (Tools.equals(STR_2, dto.getSourceSystem())) {
				BusiAssertException.notEquals(ECSSAUDITED, zteDeductionBillInfoDTO.getStatus(), BILL_STATUS_NOT_ECSS_AUDITED);
			}
		}

		if (Tools.equals(STR_1, dto.getSourceSystem())) {
			// 客户管控类型不为“阿里管控”
			BusiAssertException.notEquals(INT_1, zteDeductionBillInfoDTO.getCustomerControlType(), CUSTOMER_CONTROL_TYPE_NOT_ALIBABA);
		}
	}

	/**
	 * 组装部件出⼊库数据
	 */
	private ZteDeductionPlanDTO getDeductionPlanData(ZteDeductionPlanParamDTO dto, List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList) {

		String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_SSS));
		String batchNo = CommonUtils.generate10DigitNumber();
		// deductionPlan
		ZteDeductionPlanDTO zteDeductionPlanDTO = new ZteDeductionPlanDTO();
		zteDeductionPlanDTO.setDeductionPlanDirective(STORERKEY_ZTE + HORIZON + time + HORIZON + batchNo);
		zteDeductionPlanDTO.setVersionSeqNo(String.valueOf(System.currentTimeMillis()));
		zteDeductionPlanDTO.setTranSeqNo(STR_0 + STR_0 + HORIZON + time + HORIZON + batchNo);
		zteDeductionPlanDTO.setFactoryCode(STORERKEY_ZTE + STR_101);
		zteDeductionPlanDTO.setDeductionPlanScene(STR_NORMAL);
		// deductionItems
		String customerPlanId = zteDeductionBillDTOList.get(INT_0).getCustomerPlanId(); // 交易执行和核销执行，传单据的planId
		String planId = customerPlanId != null ? customerPlanId : RedisSerialNoUtil.getDateIncreaseId(STORERKEY_ZTE + STR_ODMT + STR_1, INT_4);

		Map<Integer, List<ZteDeductionBillInfoDTO>> isSnMap = zteDeductionBillDTOList.stream().collect(Collectors.groupingBy(ZteDeductionBillInfoDTO::getIsSn));
		List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
		isSnMap.forEach((k, v) -> {
			ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = v.get(INT_0);
			// 阿里管控，一码通管控
			if (INT_3 == k) {
				// 根据交易类型查询管控颗粒度
				StepSysLookupValuesDTO sysLookupValues = new StepSysLookupValuesDTO();
				sysLookupValues.setLookupType(LOOKUP_TYPE_1000045);
				sysLookupValues.setLookupMeaning(zteDeductionBillInfoDTO.getTransType());
				List<StepSysLookupValuesDTO> sysLookupValuesDTOList = zteAlibabaRepository.getLookupValues(sysLookupValues);
				BusiAssertException.isEmpty(sysLookupValuesDTOList, SYS_LOOKUP_VALUES_NOT_EXISTS);
				dto.setControlGranularity(sysLookupValuesDTOList.get(INT_0).getAttribute1());

				// 根据单据号查询SN绑定记录
				List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList = zteSnBoundPkgIdRepository.getSnBoundPkgId(zteDeductionBillInfoDTO);
				// 交易执行、核销执行，校验出库单据是否绑定完成（出库绑定SN，infor系统出库未做拦截）
				checkBillSn(dto, zteDeductionBillInfoDTO, zteSnBoundPkgIdDTOList);
				getOneCodeDeductionItemDTO(dto, v, zteDeductionItemDTOList, planId, zteSnBoundPkgIdDTOList);
			} else {
				// 阿里管控，非一码通管控
				getNotOneCodeDeductionItemDTO(dto, v, zteDeductionItemDTOList, planId);
			}
			dto.setCustomerPlanId(planId);
		});
		zteDeductionPlanDTO.setDeductionItems(zteDeductionItemDTOList);
		return zteDeductionPlanDTO;
	}

	/**
	 * 交易执行、核销执行，校验出库单据是否绑定完成
	 */
	private void checkBillSn(ZteDeductionPlanParamDTO dto, ZteDeductionBillInfoDTO zteDeductionBillInfoDTO, List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList) {
		if (PENDING_TYPE_STR_LIST.contains(dto.getBusinessType())) {
			// OMS零星领料单,校验pickdetailkey维度
			if (Tools.equals(STR_2, dto.getBillType()) ||  Tools.equals(STR_4, dto.getBillType())) {
				String whseId = zteSnBoundPkgIdRepository.getWhseIdByBillNo(dto.getBillNo());
				BusiAssertException.isEmpty(whseId, QUERY_WHSEID_ERROR);
				int outQty = zteSnBoundPkgIdRepository.getOutQtyByBillNo(dto.getBillNo(), whseId, zteDeductionBillInfoDTO.getSourcekey());
				List<ZteSnBoundPkgIdDTO> snBoundPickList = zteSnBoundPkgIdDTOList.stream().filter(i ->
						Tools.equals(i.getPickdetailkey(), zteDeductionBillInfoDTO.getSourcekey())).collect(Collectors.toList());
				BusiAssertException.notEquals(snBoundPickList.size(), outQty, BILL_SN_NOT_BOUNDED);
			}
			// iWMS退货申请单，校验单据维度
			if (Tools.equals(STR_6, dto.getBillType()) ||
					(Tools.equals(STR_5, dto.getBillType()) && Tools.equals(STR_01, zteDeductionBillInfoDTO.getFallbackType()))) {
				String whseId = zteSnBoundPkgIdRepository.getWhseIdByBillNo(dto.getBillNo());
				BusiAssertException.isEmpty(whseId, QUERY_WHSEID_ERROR);
				int outQty = zteSnBoundPkgIdRepository.getOutQtyByBillNo(dto.getBillNo(), whseId, null);
				BusiAssertException.notEquals(zteSnBoundPkgIdDTOList.size(), outQty, BILL_SN_NOT_BOUNDED);
			}
		}
	}

	/**
	 * 阿里管控，一码通管控
	 */
	private void getOneCodeDeductionItemDTO(ZteDeductionPlanParamDTO dto, List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList,
											List<ZteDeductionItemDTO> zteDeductionItemDTOList, String planId, List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList) {

		for (ZteDeductionBillInfoDTO billInfoDTO : zteDeductionBillDTOList) {
			ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
			zteDeductionItemDTO.setPlanId(planId);
			zteDeductionItemDTO.setMpn(billInfoDTO.getMpn());
			zteDeductionItemDTO.setPlanConsumeDate(new SimpleDateFormat(DATE_FORMATER_YYYYMMDD).format(new Date()));
			zteDeductionItemDTO.setExecutedQuantity(INT_0);
			zteDeductionItemDTO.setApproveType(INT_1);
			zteDeductionItemDTO.setItemType(INT_0);
			zteDeductionItemDTO.setOperateType(INT_0);
			// OMS
			if (Tools.equals(STR_1, dto.getSourceSystem())) {
				zteDeductionItemDTO.setConsumeType(billInfoDTO.getApprovalType() == null ? billInfoDTO.getTransType() : billInfoDTO.getApprovalType());
				zteDeductionItemDTO.setRemark(billInfoDTO.getRemark());
			}
			// iWMS
			if (Tools.equals(STR_2, dto.getSourceSystem())) {
				zteDeductionItemDTO.setConsumeType(STR_101);
				zteDeductionItemDTO.setSubFactoryCode(ZTE101_NG01);
				zteDeductionItemDTO.setRemark(ALI_REMARK);
			}

			zteDeductionItemDTO.setDeductionItemLineNumber(billInfoDTO.getDetailId());
			zteDeductionItemDTO.setPlanConsumeQuantity(billInfoDTO.getQty());
			// 交易执行、核销执行，需要传executedQuantity
			if (PENDING_TYPE_STR_LIST.contains(dto.getBusinessType())){
				zteDeductionItemDTO.setExecutedQuantity(billInfoDTO.getOpenQty());
			}
			// 核销申请，需要传originPlanId+"-"+原单行号
			if (Tools.equals(STR_3, dto.getBusinessType())) {
				List<InforScatterSnDTO> inforScatterSnDTOList = zteAlibabaRepository.getInforScatterSnDTOList(billInfoDTO);
				if (Tools.isNotEmpty(inforScatterSnDTOList)) {
					String originPlanId = inforScatterSnDTOList.get(INT_0).getOriginPlanId();
					String originDetailId = inforScatterSnDTOList.get(INT_0).getOriginDetailId();
					zteDeductionItemDTO.setOriginPlanId(originPlanId + HORIZON + originDetailId);
				}
			}

			// 阿里管控，一码通管控，获取SN信息（交易执行、核销执行）
			getDeductionDetailList(dto, billInfoDTO, zteDeductionItemDTO, zteSnBoundPkgIdDTOList);
			zteDeductionItemDTOList.add(zteDeductionItemDTO);
		}
	}

	/**
	 * 阿里管控，一码通管控，获取SN信息（交易执行、核销执行）
	 */
	private void getDeductionDetailList(ZteDeductionPlanParamDTO dto, ZteDeductionBillInfoDTO zteDeductionBillInfoDTO,
        ZteDeductionItemDTO zteDeductionItemDTO, List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDTOList) {

		if (!PENDING_TYPE_STR_LIST.contains(dto.getBusinessType())) {
			return;
		}

		// 如果单据类型为零星领料单、零星领料单制作(整机)，根据pickdetailkey过滤绑定关系；其余单据类型，根据箱号过滤绑定关系
		List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIdDetailList;
		if (PENDING_TYPE_STR_LIST.contains(dto.getBillType())) {
			zteSnBoundPkgIdDetailList = zteSnBoundPkgIdDTOList.stream().filter(m -> Tools.equals(zteDeductionBillInfoDTO.getSourcekey(),m.getPickdetailkey())).collect(Collectors.toList());
		} else {
			zteSnBoundPkgIdDetailList = zteSnBoundPkgIdDTOList.stream().filter(m -> Tools.equals(zteDeductionBillInfoDTO.getPkgId(),m.getPkgId())).collect(Collectors.toList());
		}

		if (Tools.isEmpty(zteSnBoundPkgIdDetailList)) {
			return;
		}
		// 退料，全为混箱；领料时，需要判断是否原箱.0 原箱；1 混箱
		List<ZteDeductionDetailDTO> zteDeductionDetailDTOList = new ArrayList<>();
		// 原箱
		List<String> originBoxList = zteSnBoundPkgIdDetailList.stream().filter(i -> Tools.equals(STR_0, i.getPkgType())).map(ZteSnBoundPkgIdDTO::getPkgId).distinct().collect(Collectors.toList());
		for (String pkgId : originBoxList) {
			ZteDeductionDetailDTO zteDeductionDetailDTO = new ZteDeductionDetailDTO();
			List<ZteSnBoundPkgIdDTO> zteSnBoundPkgIds = zteSnBoundPkgIdDetailList.stream().filter(i -> Tools.equals(i.getPkgId(), pkgId)).collect(Collectors.toList());
			ZteSnBoundPkgIdDTO zteSnBoundPkgIdDTO = zteSnBoundPkgIds.get(INT_0);
			zteDeductionDetailDTO.setInventorySpace(STR_1);
			zteDeductionDetailDTO.setCartonId(zteSnBoundPkgIdDTO.getPkgId());
			zteDeductionDetailDTO.setQuantity(zteSnBoundPkgIdDTO.getQty());
			if (Tools.notEquals(SN, dto.getControlGranularity())) {
				zteDeductionDetailDTOList.add(zteDeductionDetailDTO);
				continue;
			}
			List<String> snList = zteSnBoundPkgIds.stream().map(ZteSnBoundPkgIdDTO::getSn).distinct().collect(Collectors.toList());
			// 交易执行时，需要传SnList
			if (Tools.equals(STR_2, dto.getBusinessType())) {
				zteDeductionDetailDTO.setSnList(snList);
			}
			// 核销执行时，需要传originSnList
			if (Tools.equals(STR_4, dto.getBusinessType())) {
				zteDeductionDetailDTO.setOriginSnList(snList);
			}
			zteDeductionDetailDTOList.add(zteDeductionDetailDTO);
		}

		// 混箱
		List<ZteSnBoundPkgIdDTO> mixBoxList = zteSnBoundPkgIdDetailList.stream().filter(i -> Tools.equals(STR_1, i.getPkgType())).collect(Collectors.toList());
		ZteDeductionDetailDTO zteDeductionDetailDTO = new ZteDeductionDetailDTO();
		zteDeductionDetailDTO.setInventorySpace(STR_2);
		List<String> snList = mixBoxList.stream().map(ZteSnBoundPkgIdDTO::getSn).distinct().collect(Collectors.toList());
		// 交易执行时，需要传SnList
		if (Tools.equals(STR_2, dto.getBusinessType())) {
			zteDeductionDetailDTO.setSnList(snList);
		}
		// 核销执行时，需要传originSnList
		if (Tools.equals(STR_4, dto.getBusinessType())) {
			zteDeductionDetailDTO.setOriginSnList(snList);
		}
		if (Tools.isNotEmpty(snList)) {
			zteDeductionDetailDTOList.add(zteDeductionDetailDTO);
		}

		// 核销执行，需要传originPlanId+"-"+原单行号
		if (Tools.equals(STR_4, dto.getBusinessType())) {
			String externlineno = zteSnBoundPkgIdDetailList.get(INT_0).getExternlineno();
			zteDeductionItemDTO.setOriginPlanId(zteDeductionBillInfoDTO.getOriginPlanId() + HORIZON + externlineno);
		}
		// 交易执行、核销执行，需要传deductionDetailItems
		zteDeductionItemDTO.setDeductionDetailItems(zteDeductionDetailDTOList);
	}

	/**
	 * 阿里管控，非一码通管控
	 */
	private void getNotOneCodeDeductionItemDTO(ZteDeductionPlanParamDTO dto, List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList,
											   List<ZteDeductionItemDTO> zteDeductionItemDTOList, String planId) {

		if (APPROVAL_TYPE_STR_LIST.contains(dto.getBusinessType())) {
			Map<String, List<ZteDeductionBillInfoDTO>> map = zteDeductionBillDTOList.stream().collect(Collectors.groupingBy(ZteDeductionBillInfoDTO::getItemBarcode));
			// 核销执行，根据单据号，查询核销数据
			List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList;
			if (Tools.equals(STR_4, dto.getBusinessType())) {
				inforApprovalAlibabaDTOList = zteAlibabaRepository.getInforApprovalAlibabaList(dto.getBillNo());
			} else {
				inforApprovalAlibabaDTOList = new ArrayList<>();
			}
			map.forEach((k, v) -> {
				List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList = new ArrayList<>();
				// 核销申请，根据220，查询可核销的出库单
				if (Tools.equals(STR_3, dto.getBusinessType())) {
					inforEdiSoAlibabaDTOList = zteAlibabaRepository.getInforEdiSoAlibabaList(k);
				}
				for (ZteDeductionBillInfoDTO billInfoDTO : v) {
					List<ZteDeductionItemDTO> zteDeductionItemDTOS = getNotOneCodeDeductionItemDTOLoop(dto, billInfoDTO, planId, inforApprovalAlibabaDTOList, inforEdiSoAlibabaDTOList);
					zteDeductionItemDTOList.addAll(zteDeductionItemDTOS);
				}
			});
		} else {
			for (ZteDeductionBillInfoDTO billInfoDTO : zteDeductionBillDTOList) {
				List<InforApprovalAlibabaDTO> defaultApprovalList = new ArrayList<>();
				List<InforEdiSoAlibabaDTO> defaultEdiSoList = new ArrayList<>();
				List<ZteDeductionItemDTO> zteDeductionItemDTOS = getNotOneCodeDeductionItemDTOLoop(dto, billInfoDTO, planId, defaultApprovalList, defaultEdiSoList);
				zteDeductionItemDTOList.addAll(zteDeductionItemDTOS);
			}
		}
	}

	private List<ZteDeductionItemDTO> getNotOneCodeDeductionItemDTOLoop(ZteDeductionPlanParamDTO dto, ZteDeductionBillInfoDTO billInfoDTO, String planId,
												   List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList,
												   List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList) {
		List<ZteDeductionItemDTO> zteDeductionItemDTOList = new ArrayList<>();
		ZteDeductionItemDTO zteDeductionItemDTO = new ZteDeductionItemDTO();
		zteDeductionItemDTO.setPlanId(planId);
		zteDeductionItemDTO.setMpn(billInfoDTO.getMpn());
		zteDeductionItemDTO.setPlanConsumeDate(new SimpleDateFormat(DATE_FORMATER_YYYYMMDD).format(new Date()));
		zteDeductionItemDTO.setExecutedQuantity(INT_0);
		zteDeductionItemDTO.setApproveType(INT_1);
		zteDeductionItemDTO.setItemType(INT_0);
		zteDeductionItemDTO.setOperateType(INT_0);
		// OMS
		if (Tools.equals(STR_1, dto.getSourceSystem())) {
			zteDeductionItemDTO.setConsumeType(billInfoDTO.getApprovalType() == null ? billInfoDTO.getTransType() : billInfoDTO.getApprovalType());
			zteDeductionItemDTO.setRemark(billInfoDTO.getRemark());
		}
		// iWMS
		if (Tools.equals(STR_2, dto.getSourceSystem())) {
			zteDeductionItemDTO.setConsumeType(STR_101);
			zteDeductionItemDTO.setSubFactoryCode(ZTE101_NG01);
			zteDeductionItemDTO.setRemark(ALI_REMARK);
		}

		zteDeductionItemDTO.setDeductionItemLineNumber(billInfoDTO.getDetailId());
		zteDeductionItemDTO.setPlanConsumeQuantity(billInfoDTO.getQty());
		// 交易执行，需要传executedQuantity
		if (Tools.equals(STR_2, dto.getBusinessType())){
			zteDeductionItemDTO.setExecutedQuantity(billInfoDTO.getOpenQty());
		}

		// 阿里管控，非一码通管控，核销申请
		if (Tools.equals(STR_3, dto.getBusinessType())) {
			return getNotOneCodeDetailIdApply(dto, billInfoDTO, zteDeductionItemDTO, inforEdiSoAlibabaDTOList);
		}
		// 阿里管控，非一码通管控，核销执行
		if (Tools.equals(STR_4, dto.getBusinessType())) {
			return getNotOneCodeDetailIdDo(billInfoDTO, zteDeductionItemDTO, inforApprovalAlibabaDTOList);
		}
		zteDeductionItemDTOList.add(zteDeductionItemDTO);
		return zteDeductionItemDTOList;
	}

	/**
	 * 阿里管控，非一码通管控，核销申请
	 */
	private List<ZteDeductionItemDTO> getNotOneCodeDetailIdApply(ZteDeductionPlanParamDTO dto, ZteDeductionBillInfoDTO billInfoDTO,
									   ZteDeductionItemDTO zteDeductionItemDTO,
									   List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList) {

		List<ZteDeductionItemDTO> zteDeductionItemDTOS = new ArrayList<>();
		List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = new ArrayList<>();
		int qty = billInfoDTO.getQty();
		int row = INT_1;
		if (Tools.isEmpty(inforEdiSoAlibabaDTOList)) {
			return zteDeductionItemDTOS;
		}
		for (InforEdiSoAlibabaDTO inforEdiSoAlibabaDTO : inforEdiSoAlibabaDTOList) {
			if (qty == INT_0) {
				break;
			}
			int canUseQty = inforEdiSoAlibabaDTO.getCanUseQty();
			if (canUseQty == INT_0) {
				continue;
			}
			ZteDeductionItemDTO zteDeductionItemDTOSplit = new ZteDeductionItemDTO();
			BeanUtils.copyProperties(zteDeductionItemDTO, zteDeductionItemDTOSplit);
			zteDeductionItemDTOSplit.setDeductionItemLineNumber(billInfoDTO.getDetailId() + HORIZON + row);
			zteDeductionItemDTOSplit.setOriginPlanId(inforEdiSoAlibabaDTO.getOriginPlanId() + HORIZON + inforEdiSoAlibabaDTO.getExternlineno());

			InforApprovalAlibabaDTO inforApprovalAlibabaDTO = InforApprovalAlibabaDTO.builder().build()
					.setBillNo(billInfoDTO.getBillNo())
					.setDetailId(billInfoDTO.getDetailId())
					.setItemNo(inforEdiSoAlibabaDTO.getSku())
					.setItemBarcode(inforEdiSoAlibabaDTO.getLottable02())
					.setInQty(billInfoDTO.getQty())
					.setPlanId(zteDeductionItemDTOSplit.getPlanId())
					.setDeductionItemLineNumber(zteDeductionItemDTOSplit.getDeductionItemLineNumber())
					.setOriginBillNo(inforEdiSoAlibabaDTO.getExternalorderkey2())
					.setOriginSerialkey(inforEdiSoAlibabaDTO.getSerialkey())
					.setOriginDetailId(inforEdiSoAlibabaDTO.getExternlineno())
					.setOriginPlanId(inforEdiSoAlibabaDTO.getOriginPlanId())
					.setCreatedBy(dto.getEmpNo());

			if (canUseQty >= qty) {
				zteDeductionItemDTOSplit.setPlanConsumeQuantity(qty);
				inforEdiSoAlibabaDTO.setCanUseQty(canUseQty - qty);
				inforApprovalAlibabaDTO.setApplyQty(qty);
				qty = INT_0;
			} else {
				zteDeductionItemDTOSplit.setPlanConsumeQuantity(canUseQty);
				inforEdiSoAlibabaDTO.setCanUseQty(INT_0);
				inforApprovalAlibabaDTO.setApplyQty(canUseQty);
				qty = qty - canUseQty;
			}
			zteDeductionItemDTOS.add(zteDeductionItemDTOSplit);
			inforApprovalAlibabaDTOList.add(inforApprovalAlibabaDTO);
			row++;
		}
		// 核销信息落表，预占
		if (Tools.isNotEmpty(inforApprovalAlibabaDTOList)) {
			zteAlibabaRepository.insertInforApprovalAlibaba(inforApprovalAlibabaDTOList);
		}
		return zteDeductionItemDTOS;
	}

	/**
	 * 阿里管控，非一码通管控，核销执行
	 */
	private List<ZteDeductionItemDTO> getNotOneCodeDetailIdDo(ZteDeductionBillInfoDTO billInfoDTO,
																 ZteDeductionItemDTO zteDeductionItemDTO,
																 List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList) {

		List<ZteDeductionItemDTO> zteDeductionItemDTOS = new ArrayList<>();
		List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOS = inforApprovalAlibabaDTOList.stream().filter(i ->
						Tools.equals(billInfoDTO.getBillNo(), i.getBillNo()) && Tools.equals(billInfoDTO.getDetailId(), i.getDetailId()))
				.collect(Collectors.toList());
		if (Tools.isEmpty(inforApprovalAlibabaDTOS)) {
			return zteDeductionItemDTOS;
		}
		for (InforApprovalAlibabaDTO inforApprovalAlibabaDTO : inforApprovalAlibabaDTOS) {
			ZteDeductionItemDTO zteDeductionItemDTOSplit = new ZteDeductionItemDTO();
			BeanUtils.copyProperties(zteDeductionItemDTO, zteDeductionItemDTOSplit);
			zteDeductionItemDTOSplit.setDeductionItemLineNumber(inforApprovalAlibabaDTO.getDeductionItemLineNumber());
			zteDeductionItemDTOSplit.setOriginPlanId(inforApprovalAlibabaDTO.getOriginPlanId() + HORIZON + inforApprovalAlibabaDTO.getOriginDetailId());
			zteDeductionItemDTOSplit.setPlanConsumeQuantity(inforApprovalAlibabaDTO.getApprovalQty());
			zteDeductionItemDTOSplit.setExecutedQuantity(inforApprovalAlibabaDTO.getApprovalQty());
			zteDeductionItemDTOS.add(zteDeductionItemDTOSplit);
		}
		return zteDeductionItemDTOS;
	}

	/**
	 * 根据infor日志定时推送信息给阿里
	 */
	@Override
	public void excutedUploadJob(ZteDeductionPlanParamDTO dto) {

		// 查询还未推送成功给B2B的单据
		dto.setSourcetable(ALIBABA_PUBLIC_CLOUD);
		List<IscpEdiLog> externkeyList = zteSnBoundPkgIdRepository.getAlibabaExternkey(dto);
		if (Tools.isEmpty(externkeyList)) {
			return;
		}
		Map<String, List<IscpEdiLog>> operateTypeMap = externkeyList.stream().collect(Collectors.groupingBy(IscpEdiLog::getOperateType));
		operateTypeMap.forEach((k, v) -> {
			// 部件出入库计划交易执行&核销执行
			if (Tools.equals(k, STR_10)) {
				ThreadUtil.ALIBABA_JOB_EXECUTOR.execute(() -> excutedDeductionPlan(v));
			}
			// 部件接收入库
			if (Tools.equals(k, STR_11)) {
				ThreadUtil.ALIBABA_JOB_EXECUTOR.execute(() -> excutedDeliveryDoCustomer(v));
			}
			// 工单实际扣料
			if (Tools.equals(k, STR_12)) {
				ThreadUtil.ALIBABA_JOB_EXECUTOR.execute(() -> excutedDistributeMaterial(v));
			}
			// 送货不良+调拨单（良品-不良品）,创建调拨单
			if (Tools.equals(k, STR_13)) {
				ThreadUtil.ALIBABA_JOB_EXECUTOR.execute(() -> excutedTransferInventoryMove(v));
			}
		});
	}

	public void excutedDeductionPlan(List<IscpEdiLog> iscpEdiLogs) {

		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100001);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);

		for (IscpEdiLog iscpEdiLog : iscpEdiLogs) {
			ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
			dto.setBillNo(iscpEdiLog.getExternkey());
			dto.setEmpNo(SYSTEM);
			getBillType(dto);
			// OMS的单据交易执行和核销执行，根据receiptkey和pickdetailkey查询detailId对应的单据信息上传阿里
			if (Tools.equals(STR_1, dto.getSourceSystem())) {
				List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = getBillInfoPending(dto, iscpEdiLog);
				if (Tools.isEmpty(zteDeductionBillDTOList)) {
					continue;
				}
				if (Tools.isNotEmpty(zteDeductionBillDTOList.get(INT_0).getApprovalType())) {
					// OMS单据的核销类型不为空，推送核销执行
					dto.setBusinessType(STR_4);
				} else {
					// 推送交易执行
					dto.setBusinessType(STR_2);
				}
				// 将receiptkey/pickdetailkey保存到日志表
				dto.setRemark(iscpEdiLog.getSourcekey());
				deductionPlanPending(dto, zteDeductionBillDTOList);
			} else {
				// 推送交易执行
				dto.setBusinessType(STR_2);
				deductionPlan(dto);
			}
		}
	}

	/**
	 * 交易执行/核销执行，按照receiptkey/pickdetailkey维度上传阿里
	 */
	private List<ZteDeductionBillInfoDTO> getBillInfoPending(ZteDeductionPlanParamDTO dto, IscpEdiLog iscpEdiLog) {
		// 根据externkey和sourcekey，查询INFOR数据
		List<ZteDeductionBillInfoDTO> poSoList = transferBoxRepository.getPoSoBySourceKey(iscpEdiLog);
		if (Tools.isEmpty(poSoList)) {
			return Collections.emptyList();
		}
		List<String> detailIdList = poSoList.stream().map(ZteDeductionBillInfoDTO::getDetailId).collect(Collectors.toList());
		dto.setDetailIdList(detailIdList);
		// 根据billNo和detailId，查询OMS数据
		List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = zteAlibabaRepository.getBillInfo(dto);
		if (Tools.isEmpty(zteDeductionBillDTOList)) {
			return Collections.emptyList();
		}
		for (ZteDeductionBillInfoDTO zteDeductionBillInfoDTO : zteDeductionBillDTOList) {
			for (ZteDeductionBillInfoDTO poSo : poSoList) {
				if (Tools.equals(zteDeductionBillInfoDTO.getBillNo(), poSo.getBillNo()) &&
						Tools.equals(zteDeductionBillInfoDTO.getDetailId(), poSo.getDetailId())) {
					int openQty = zteDeductionBillInfoDTO.getOpenQty() + poSo.getQty();
					zteDeductionBillInfoDTO.setOpeningQty(poSo.getQty());
					zteDeductionBillInfoDTO.setOpenQty(openQty);
					zteDeductionBillInfoDTO.setSourcekey(poSo.getSourcekey());
				}
			}
		}
		return zteDeductionBillDTOList;
	}

	private void deductionPlanPending(ZteDeductionPlanParamDTO dto, List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList) {

		BusiAssertException.isEmpty(dto.getSourceSystem(), SOURCE_SYSTEM_EMPTY);
		BusiAssertException.isEmpty(dto.getBusinessType(), BUSINESS_TYPE_EMPTY);
		BusiAssertException.isEmpty(dto.getBillType(), BILL_TYPE_EMPTY);
		BusiAssertException.isEmpty(zteDeductionBillDTOList, BILL_NOT_EXISTS);

		// 校验业务数据
		checkBillInfo(dto, zteDeductionBillDTOList);

		// 组装部件出⼊库数据
		ZteDeductionPlanDTO zteDeductionPlanDTO = getDeductionPlanData(dto, zteDeductionBillDTOList);

		// 推送B2B
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100001);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		dto.setMessageType(sysLookupValuesDTO.getLookupMeaning());
		pushDataToB2B(JSON.toJSONString(zteDeductionPlanDTO), dto, DEDUCTION_PLAN_INFO_ZH);

		// 交易执行、核销执行，OMS更新单据历史发料总数量openQty
		zteAlibabaRepository.updateBillDetailOpenQty(zteDeductionBillDTOList);
	}

	/**
	 * 部件接收入库
	 */
	public void excutedDeliveryDoCustomer(List<IscpEdiLog>  iscpEdiLogs) {
		doExecutedDeliveryDoCustomer(iscpEdiLogs);
	}

	/**
	 * 工单实际扣料
	 */
	public void excutedDistributeMaterial(List<IscpEdiLog> iscpEdiLogs){
		doExcutedDistributeMaterial(iscpEdiLogs);
	}

	/**
	 * 创建调拨单
	 */
	public void excutedTransferInventoryMove(List<IscpEdiLog> iscpEdiLogs) {
		doExecutedCreateMoveBill(iscpEdiLogs);
	}

	/**
	 * 获取来源系统和单据类型
	 */
	public void getBillType(ZteDeductionPlanParamDTO dto) {
		String billType = dto.getBillNo().substring(INT_0, INT_1);
		dto.setSourceSystem(STR_1);
		// OMS零星入库单
		if (Tools.equals(STR_A, billType)) {
			dto.setBillType(STR_1);
		}
		// OMS零星领料单
		if (Tools.equals(STR_B, billType)) {
			dto.setBillType(STR_2);
		}
		// OMS零星退库单制作(整机材料)
		if (Tools.equals(STR_M, billType)) {
			dto.setBillType(STR_3);
		}
		// OMS零星领料单制作(整机)
		if (Tools.equals(STR_P, billType)) {
			dto.setBillType(STR_4);
		}

		billType = dto.getBillNo().substring(INT_0, INT_3);
		// iWMS在线退
		if (Tools.equals(STRING_STH, billType)) {
			dto.setBillType(STR_5);
			dto.setSourceSystem(STR_2);
		}
		// iWMS来料退
		if (Tools.equals(STRING_LTH, billType)) {
			dto.setBillType(STR_6);
			dto.setSourceSystem(STR_2);
		}
	}

	/**
	 * 通知出⼊库审批结果
	 */
	@Override
	public void noticeApproveResult(List<ZteApproveResultDTO> list) {

		if (Tools.isEmpty(list)) {
			return;
		}
		// 记录阿里返回的出⼊库审批结果
		zteAlibabaRepository.insertZmsNoticeApproveResult(list);

		// 查询单据头信息
		List<String> applyIdList = list.stream().map(ZteApproveResultDTO::getPlanId).distinct().collect(Collectors.toList());
		// 查询OMS单据信息
		List<ZteDeductionBillInfoDTO> zteDeductionBillInfoDTOList = zteAlibabaRepository.getBillHeadByPlanId(applyIdList);
		List<ZteDeductionBillInfoDTO> billHeadByPlanId = zteAliApprovalRepository.getBillHeadByPlanId(applyIdList);
		zteDeductionBillInfoDTOList.addAll(Optional.ofNullable(billHeadByPlanId).orElse(Lists.newArrayList()));
		if (Tools.isEmpty(zteDeductionBillInfoDTOList)) {
			return;
		}
		for (ZteApproveResultDTO zteApproveResultDTO : list) {
			if (INT_1 == zteApproveResultDTO.getApproveStatus()) {
				return;
			}
			ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = zteDeductionBillInfoDTOList.stream().filter(i ->
					Tools.equals(i.getCustomerPlanId(), zteApproveResultDTO.getPlanId())).findFirst().orElse(null);
			if (zteDeductionBillInfoDTO != null) {
				zteApproveResultDTO.setDetailId(StringUtils.substringBefore(zteApproveResultDTO.getDeductionItemLineNumber(), HORIZON));
				zteApproveResultDTO.setBillId(zteDeductionBillInfoDTO.getBillId());
				zteApproveResultDTO.setBillNo(zteDeductionBillInfoDTO.getBillNo());
				zteApproveResultDTO.setBillType(zteDeductionBillInfoDTO.getBillType());
				if (INT_3 == zteApproveResultDTO.getApproveStatus()) {
					zteApproveResultDTO.setApproveAvailableQuantity(0L);
				}
			}
		}
		//如果是iwms单据，走这段逻辑
		if (isIwmsBill(list.get(0).getBillType())) {
			handleIwmsApprovalResult(list);
			return;
		}
		// 更新单据明细审批数量
		zteAlibabaRepository.updateBillDetailApprovalQty(list);
		// 如果所有单据明细的审批数量都不为空，更新单据头状态为”客户审批通过“
		int noApprovedQty = zteAlibabaRepository.getBillDetailNotApproved(list.get(INT_0));
		if (noApprovedQty == INT_0) {
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(list.get(INT_0).getBillNo());
			zteDeductionPlanParamDTO.setBillType(list.get(INT_0).getBillType());
			zteDeductionPlanParamDTO.setStatus(STR_APPROVED);
			zteAlibabaRepository.updateBillHead(zteDeductionPlanParamDTO);
		}
		// 如果核销类型不为空，更新核销表的核销数量，更新接口表(阿里)的核销数量
		ZteDeductionBillInfoDTO zteDeductionBillInfoDTO = zteDeductionBillInfoDTOList.get(INT_0);
		if (Tools.isNotEmpty(zteDeductionBillInfoDTO.getApprovalType())) {
			zteAlibabaRepository.updateInforApprovalAlibaba(list);
			List<InforApprovalAlibabaDTO> inforApprovalAlibabaDTOList = zteAlibabaRepository.getInforApprovalAlibabaList(zteDeductionBillInfoDTO.getBillNo());
			if (Tools.isEmpty(inforApprovalAlibabaDTOList)) {
				return;
			}
			Map<String, List<InforApprovalAlibabaDTO>> map = inforApprovalAlibabaDTOList.stream().collect(Collectors.groupingBy(InforApprovalAlibabaDTO::getOriginSerialkey));
			List<InforEdiSoAlibabaDTO> inforEdiSoAlibabaDTOList = new ArrayList<>();
			map.forEach((k, v) -> {
				InforEdiSoAlibabaDTO inforEdiSoAlibabaDTO = new InforEdiSoAlibabaDTO();
				inforEdiSoAlibabaDTO.setSerialkey(k);
				int approvalQty = v.stream().mapToInt(InforApprovalAlibabaDTO::getApprovalQty).sum();
				inforEdiSoAlibabaDTO.setApprovalQty(approvalQty);
				inforEdiSoAlibabaDTOList.add(inforEdiSoAlibabaDTO);
			});
			zteAlibabaRepository.updateInforEdiSoAlibaba(inforEdiSoAlibabaDTOList);
		}

	}

	private void handleIwmsApprovalResult(List<ZteApproveResultDTO> list) {
		//iWMS，更新明细
		zteAliApprovalRepository.updateBillDetailList(list);
		int noApprovedQtyWms = zteAliApprovalRepository.getBillDetailNotApproved(list.get(INT_0));
		if (noApprovedQtyWms == INT_0) {
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(list.get(INT_0).getBillNo());
			zteDeductionPlanParamDTO.setBillType(list.get(INT_0).getBillType());
			zteDeductionPlanParamDTO.setStatus(STR_APPROVED);
			zteAliApprovalRepository.updateBillHead(zteDeductionPlanParamDTO);
		}
	}

	private boolean isIwmsBill(String billType) {
		return Tools.equals(billType, STR_5) || Tools.equals(billType, STR_6);
	}

	/**
	 * 接收阿里的返回消息
	 */
	@Override
	public void zteB2BReturnInfo(B2BCallBackDTO dto) {

		// 查询消息类型
		SysLookupValuesDTO sysLookupValues = new SysLookupValuesDTO();
		sysLookupValues.setLookupType(LOOKUP_TYPE_1000091);
		List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValues);
		BusiAssertException.isEmpty(sysLookupValuesDTOList, SYS_LOOKUP_VALUES_NOT_EXISTS);

		for (SysLookupValuesDTO sysLookupValuesDTO : sysLookupValuesDTOList) {
			if (Tools.notEquals(sysLookupValuesDTO.getLookupMeaning(), dto.getMessageType())) {
				continue;
			}
			dealBillByMessage(sysLookupValuesDTO, dto);
		}
	}

	/**
	 * 定制格式化Json字符串，仅适用于阿里返回（非常定制化，慎用）
	 */
	private String reformatJson(String json) {
		if (StringUtils.isBlank(json)) {
			return json;
		}
		String result = json.replace("\\", "");
		result = result.replace("\"{", "{");
		result = result.replace("}\"", "}");
		return result;
	}

	private void dealBillByMessage(SysLookupValuesDTO sysLookupValuesDTO, B2BCallBackDTO dto) {

		//更新kafka日志表
		updateStockUploadLogJsonData(dto);

		// 阿里通知出入库审批结果
		if (Tools.equals(LOOKUP_CODE_100009100003, sysLookupValuesDTO.getLookupCode())) {
			dealNoticeApproveResult(dto);
			return;
		}

		// 根据messageId查询单据信息
		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		zteStockInfoUploadLogDTO.setId(dto.getKeywords());
		zteStockInfoUploadLogDTO.setMessageType(dto.getMessageType());
		zteStockInfoUploadLogDTO.setIsReturn(INT_1);
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = zteStockInfoUploadRepository.getStockUploadLog(zteStockInfoUploadLogDTO);
		if (Tools.isEmpty(zteStockInfoUploadLogDTOList)) {
			return;
		}

		// 部件出入库计划申请&执行&核销同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100001, sysLookupValuesDTO.getLookupCode())) {
			dealBillDeductionPlan(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 创建调拨单同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100002, sysLookupValuesDTO.getLookupCode())) {
			dealCreateMoveBillResult(dto, zteStockInfoUploadLogDTOList.get(INT_0), STR_13);
		}

		// 部件接收入库返回结果
		if (Tools.equals(LOOKUP_CODE_100009100004, sysLookupValuesDTO.getLookupCode())) {
			dealDeliveryDoResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 部件库存上传同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100005, sysLookupValuesDTO.getLookupCode())) {
			dealInventoryStaticsData(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 工单实际扣料同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100006, sysLookupValuesDTO.getLookupCode())) {
			dealCreateMoveBillResult(dto, zteStockInfoUploadLogDTOList.get(INT_0), STR_12);
		}

		// 混箱箱包与SN绑定同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100007, sysLookupValuesDTO.getLookupCode())) {
			dealPkgIdBoundSnResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 供应商库存Gap责任归属信息反馈同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100008, sysLookupValuesDTO.getLookupCode())) {
			dealInventoryDiffFeedbackResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 部件箱包&SN库存同步阿里
		if (Tools.equals(LOOKUP_CODE_100009100009, sysLookupValuesDTO.getLookupCode())) {
			dealSnInventoryData(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 阿里库存交易同步
		if (Tools.equals(LOOKUP_CODE_100009100010, sysLookupValuesDTO.getLookupCode())) {
			dealQueryDetailsResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 阿里库存现有量同步
		if (Tools.equals(LOOKUP_CODE_100009100011, sysLookupValuesDTO.getLookupCode())) {
			dealAlibabaInventoryResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// 原箱箱包与SN绑定
		if (Tools.equals(LOOKUP_CODE_100009100012, sysLookupValuesDTO.getLookupCode())) {
			dealPkgIdBoundSnResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}

		// OMS零星领、退料工单发料计划
		if (Tools.equals(LOOKUP_CODE_100009100014, sysLookupValuesDTO.getLookupCode())) {
			dealOmsBillsResult(dto, zteStockInfoUploadLogDTOList.get(INT_0));
		}
	}

	/**
	 * 更新kafka日志表jsonData
	 */
	public void updateStockUploadLogJsonData(B2BCallBackDTO dto) {
		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		zteStockInfoUploadLogDTO.setId(dto.getKeywords());
		zteStockInfoUploadLogDTO.setReturnData(JSON.toJSONString(dto));
		zteStockInfoUploadRepository.updateStockUploadLogJsonData(zteStockInfoUploadLogDTO);
	}

	/**
	 * 阿里通知出入库审批结果
	 */
	private void dealNoticeApproveResult(B2BCallBackDTO dto) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			return;
		}
		List<ZteApproveResultDTO> list = JacksonJsonConverUtil.jsonToBean(dto.getData(), new TypeReference<List<ZteApproveResultDTO>>(){});
		noticeApproveResult(list);
	}

	/**
	 * 部件出入库计划申请&执行&核销同步阿里
	 */
	private void dealBillDeductionPlan(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			rollBackOpenQty(zteStockInfoUploadLogDTO);
			return;
		}

		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean((dto.getData()), new TypeReference<B2BCallBackDataDTO>() {
		});
		if (b2bCallbackData == null) {
			rollBackOpenQty(zteStockInfoUploadLogDTO);
			return;
		}

		B2BCallBackResultDTO b2bCallBackResult = b2bCallbackData.getResult();
		if (b2bCallBackResult == null) {
			rollBackOpenQty(zteStockInfoUploadLogDTO);
			return;
		}
		String isSuccess = b2bCallBackResult.getIsSuccess();
		if (Tools.equals(STR_FALSE, isSuccess)) {
			// 如果失败，
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(zteStockInfoUploadLogDTO.getContractNo());
			zteDeductionPlanParamDTO.setBillType(zteStockInfoUploadLogDTO.getTaskNo());
			// 交易申请、核销申请，单据状态回退
			if (APPLY_TYPE_STR_LIST.contains(zteStockInfoUploadLogDTO.getItemNo())) {
				rollBackBillStatus(zteDeductionPlanParamDTO, zteStockInfoUploadLogDTO);
			}
			// 交易执行、核销执行，更新plugin.iscp_edi_log推送次数
			if (PENDING_TYPE_STR_LIST.contains(zteStockInfoUploadLogDTO.getItemNo())) {
				// 交易执行执行/核销执行，回滚OMS单据明细的openQty
				rollBackOpenQty(zteStockInfoUploadLogDTO);
				// 更新plugin.iscp_edi_log推送次数
				IscpEdiLog iscpEdiLog = new IscpEdiLog();
				iscpEdiLog.setExternkey(zteStockInfoUploadLogDTO.getContractNo());
				iscpEdiLog.setSourceTable(ALIBABA_PUBLIC_CLOUD);
				iscpEdiLog.setOperateType(STR_10);
				iscpEdiLog.setIsSend(INT_MINUS_1);
				iscpEdiLog.setSourcekey(zteStockInfoUploadLogDTO.getRemark());
				inforIwmsIscpRepository.updateIscpEdiLogTimes(iscpEdiLog);
			}
			return;
		}
		// 如果成功，更新kxstepiii.stock_upload_log的状态1，
		zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
		zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
		// 交易执行、核销执行，更新plugin.iscp_edi_log状态为0
		if (PENDING_TYPE_STR_LIST.contains(zteStockInfoUploadLogDTO.getItemNo())) {
			IscpEdiLog iscpEdiLog = new IscpEdiLog();
			iscpEdiLog.setExternkey(zteStockInfoUploadLogDTO.getContractNo());
			iscpEdiLog.setSourceTable(ALIBABA_PUBLIC_CLOUD);
			iscpEdiLog.setOperateType(STR_10);
			iscpEdiLog.setIsSend(INT_0);
			iscpEdiLog.setSourcekey(zteStockInfoUploadLogDTO.getRemark());
			inforIwmsIscpRepository.updateIscpEdiLogTimes(iscpEdiLog);
		}

	}

	/**
	 * 交易执行执行/核销执行，回滚OMS单据明细的openQty
	 */
	private void rollBackOpenQty(ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		if (Tools.notEquals(STR_1, zteStockInfoUploadLogDTO.getSn())) {
			return;
		}
		if (!PENDING_TYPE_STR_LIST.contains(zteStockInfoUploadLogDTO.getItemNo())) {
			return;
		}
		// 查询infor日志
		ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
		dto.setExternkey(zteStockInfoUploadLogDTO.getContractNo());
		dto.setSourcetable(ALIBABA_PUBLIC_CLOUD);
		dto.setOperatetype(STR_10);
		dto.setSourcekey(zteStockInfoUploadLogDTO.getRemark());
		List<IscpEdiLog> externkeyList = zteSnBoundPkgIdRepository.getAlibabaExternkey(dto);
		if (Tools.isEmpty(externkeyList)) {
			return;
		}
		List<ZteDeductionBillInfoDTO> poSoList = transferBoxRepository.getPoSoBySourceKey(externkeyList.get(INT_0));
		if (Tools.isEmpty(poSoList)) {
			return;
		}
		List<String> detailIdList = poSoList.stream().map(ZteDeductionBillInfoDTO::getDetailId).collect(Collectors.toList());
		dto.setBillNo(zteStockInfoUploadLogDTO.getContractNo());
		dto.setBillType(zteStockInfoUploadLogDTO.getTaskNo());
		dto.setBusinessType(zteStockInfoUploadLogDTO.getItemNo());
		dto.setDetailIdList(detailIdList);
		// 根据billNo和detailId，查询OMS数据
		List<ZteDeductionBillInfoDTO> zteDeductionBillDTOList = zteAlibabaRepository.getBillInfo(dto);
		if (Tools.isEmpty(zteDeductionBillDTOList)) {
			return;
		}
		for (ZteDeductionBillInfoDTO zteDeductionBillInfoDTO : zteDeductionBillDTOList) {
			for (ZteDeductionBillInfoDTO poSo : poSoList) {
				if (Tools.equals(zteDeductionBillInfoDTO.getBillNo(), poSo.getBillNo()) &&
						Tools.equals(zteDeductionBillInfoDTO.getDetailId(), poSo.getDetailId())) {
					zteDeductionBillInfoDTO.setOpeningQty(-poSo.getQty());
				}
			}
		}
		// 交易执行、核销执行，OMS更新单据历史发料总数量openQty
		zteAlibabaRepository.updateBillDetailOpenQty(zteDeductionBillDTOList);
	}

	private void rollBackBillStatus(ZteDeductionPlanParamDTO zteDeductionPlanParamDTO, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		// OMS,回退为”已提交“
		if (Tools.equals(STR_1, zteStockInfoUploadLogDTO.getSn())) {
			zteDeductionPlanParamDTO.setStatus(STR_SUBMITED);
			if (Tools.equals(STR_3, zteStockInfoUploadLogDTO.getTaskNo())) {
				zteDeductionPlanParamDTO.setStatus(CHECKING);
			}
			if (Tools.equals(STR_4, zteStockInfoUploadLogDTO.getTaskNo())) {
				zteDeductionPlanParamDTO.setStatus(RECHECK);
			}
			zteAlibabaRepository.updateBillHead(zteDeductionPlanParamDTO);
			// 如果是核销申请，失败时，需要删除核销信息（阿里非一码通管控）
			if (Tools.equals(STR_3, zteStockInfoUploadLogDTO.getItemNo())) {
				zteAlibabaRepository.deleteInforApprovalAlibaba(zteStockInfoUploadLogDTO.getContractNo());
			}
		}
		// iWMS,回退为“供方已处理”
		if (Tools.equals(STR_2, zteStockInfoUploadLogDTO.getSn())) {
			zteDeductionPlanParamDTO.setStatus(SUPPLIEER_DEALED);
			zteAliApprovalRepository.updateBillHead(zteDeductionPlanParamDTO);
		}
	}

	/**
	 * 创建调拨单同步阿里/工单实际扣料同步阿里
	 */
	private void dealCreateMoveBillResult(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO, String operateType) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>() {});
		if (b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null) {
			return;
		}
		IscpEdiLog iscpEdiLog = new IscpEdiLog();
		iscpEdiLog.setExternkey(zteStockInfoUploadLogDTO.getContractNo());
		iscpEdiLog.setSourcekey(zteStockInfoUploadLogDTO.getTaskNo());
		iscpEdiLog.setSourceTable(ALIBABA_PUBLIC_CLOUD);
		iscpEdiLog.setOperateType(operateType);
		// 失败更新推送次数
		AliCreateBillResDataDTO aliCreateBillResDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
		if (!aliCreateBillResDataDTO.isSuccess()) {
			iscpEdiLog.setIsSend(INT_MINUS_1);
			transferBoxRepository.updateSendStatus(dto.getKeywords(), STR_MINUS_1);
		} else {
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
			iscpEdiLog.setIsSend(INT_0);
			transferBoxRepository.updateSendStatus(dto.getKeywords(), STR_1);
		}
		// 更新infor推送状态
		inforIwmsIscpRepository.updateIscpEdiLogTimes(iscpEdiLog);
	}

	/**
	 * 部件接收入库回调
	 */
	private void dealDeliveryDoResult(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		String isB2BSuccess = dto.getSuccess();
		IscpEdiLog iscpEdiLog = new IscpEdiLog();
		iscpEdiLog.setExternkey(zteStockInfoUploadLogDTO.getContractNo());
		iscpEdiLog.setSourcekey(zteStockInfoUploadLogDTO.getTaskNo());
		iscpEdiLog.setSourceTable(ALIBABA_PUBLIC_CLOUD);
		iscpEdiLog.setOperateType(STR_11);
		// 默认返回失败
		iscpEdiLog.setIsSend(INT_M_1);
		if (Tools.equals(STR_TRUE, isB2BSuccess)) {
			B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>(){});
			if (b2bCallbackData != null && b2bCallbackData.getData() != null) {
				AliCreateBillResResultDTO resResultDto = b2bCallbackData.getData();
				if (resResultDto.isSuccess()) {
					AliCreateBillResDataDTO dataFrame = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
					if (dataFrame != null && dataFrame.isSuccess()) {
						iscpEdiLog.setIsSend(INT_0);
					}
				}
			}
		}
		if (Tools.equals(INT_0, iscpEdiLog.getIsSend())) {
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
		}
		// 更新infor推送状态
		inforIwmsIscpRepository.updateIscpEdiLogTimes(iscpEdiLog);
	}

	/**
	 * 部件库存上传同步阿里
	 */
	private void dealInventoryStaticsData(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		CustomerInventoryLinesDTO updateDTO = CustomerInventoryLinesDTO.builder().sendStatus(STR_1).messageId(dto.getKeywords()).build();
		if (Tools.equals(STR_FALSE, dto.getSuccess())) {
			updateDTO.setSendStatus(STR_MINUS_1);
		} else {
			B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(dto.getData(), new TypeReference<B2BCallBackDataDTO>(){});
			if (Tools.isEmpty(b2bCallbackData) || Tools.isEmpty(b2bCallbackData.getResult())) {
				updateDTO.setSendStatus(STR_MINUS_1);
			} else {
				String isSuccess = b2bCallbackData.getResult().getIsSuccess();
				if (Tools.equals(STR_FALSE, isSuccess)) {
					updateDTO.setSendStatus(STR_MINUS_1);
				} else {
					// 如果成功，更新kxstepiii.stock_upload_log的状态1，
					zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
					zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
				}
			}
		}
		zteAlibabStockInfoUploadRepository.updateInventoryMergedStatus(updateDTO);
	}

	/**
	 * 混箱箱包与SN绑定/原箱箱包与SN绑定
	 */
	private void dealPkgIdBoundSnResult(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		UpdateZtePkgIdBoundSnInfoDTO result = new UpdateZtePkgIdBoundSnInfoDTO();
		result.setSendStatus(STR_MINUS_1);
		result.setMessageId(dto.getKeywords());
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(result);
			return;
		}
		if (Tools.isEmpty(dto.getData())) {
			zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(result);
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>(){});
		if(Tools.isEmpty(b2bCallbackData) || Tools.isEmpty(b2bCallbackData.getData()) || Tools.isEmpty(b2bCallbackData.getData().getResult())) {
			zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(result);
			return;
		}
		AliCreateBillResDataDTO aliCreateBillResDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
		if (aliCreateBillResDataDTO.isSuccess()) {
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
			result.setSendStatus(STR_1);
		}
		zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfoByResult(result);
	}

	/**
	 * 供应商库存Gap责任归属信息反馈
	 */
	private void dealInventoryDiffFeedbackResult(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		StockDiscrepancyFeedbackDTO feedbackDTO = new  StockDiscrepancyFeedbackDTO();
		feedbackDTO.setSendStatus(STR_MINUS_1);
		feedbackDTO.setStatus(SAVED);
		feedbackDTO.setMessageId(dto.getKeywords());
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(feedbackDTO);
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>(){});
		if(b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null) {
			stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(feedbackDTO);
			return;
		}
		AliCreateBillResDataDTO aliCreateBillResDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
		if (aliCreateBillResDataDTO.isSuccess()) {
			// 成功，则更新状态，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
			feedbackDTO.setSendStatus(STR_1);
			feedbackDTO.setStatus(SUBMITED);
		}
		stockDiscrepancyFeedbackRepository.updateSendResultByMessageId(feedbackDTO);
	}

	/**
	 * 部件箱包&SN库存同步回调
	 */
	private void dealSnInventoryData(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO){
		CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
		customerInventoryLinesDto.setMessageType(dto.getMessageType());
		customerInventoryLinesDto.setMessageId(dto.getKeywords());
		customerInventoryLinesDto.setLastUpdatedBy(B2B);
		customerInventoryLinesDto.setSendStatus(STR_MINUS_1);
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			zteAlibabStockInfoUploadRepository.updateInventoryMergedStatusExt(customerInventoryLinesDto);
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>(){});
		if (b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null) {
			zteAlibabStockInfoUploadRepository.updateInventoryMergedStatusExt(customerInventoryLinesDto);
			return;
		}
		AliCreateBillResDataDTO aliCreateBillResDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
		if (aliCreateBillResDataDTO.isSuccess()) {
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
			customerInventoryLinesDto.setSendStatus(STR_1);
		}
		zteAlibabStockInfoUploadRepository.updateInventoryMergedStatusExt(customerInventoryLinesDto);
	}

	/**
	 * 阿里库存交易同步返回结果
	 */
	private void dealQueryDetailsResult(B2BCallBackDTO dto, ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>() {});
		if (b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null) {
			return;
		}
		AliCreateBillResListDataDTO aliCreateBillResListDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResListDataDTO>() {});
		if (aliCreateBillResListDataDTO.isSuccess()) {
			List<B2BCallBackInventoryResultDTO> data = aliCreateBillResListDataDTO.getData();
			if (Tools.isEmpty(data)) {
				return;
			}
			inventoryTransRepository.batchInsert(data);
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
		}
	}

	/**
	 * 阿里库存现有量同步
	 */
	public void dealAlibabaInventoryResult(B2BCallBackDTO dto,ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>() {});
		if(b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null){
			return;
		}
		AliCreateBillResListDataDTO aliCreateBillResListDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResListDataDTO>() {});
		if (aliCreateBillResListDataDTO.isSuccess()) {
			List<B2BCallBackInventoryResultDTO> data = aliCreateBillResListDataDTO.getData();
			if (Tools.isEmpty(data)) {
				return;
			}
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000097);
            List<SysLookupValuesDTO> lookupValues = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
            if (Tools.isNotEmpty(lookupValues)) {
                List<String> types  = lookupValues.stream().map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());
                data = data.stream().filter(item->types.contains(item.getInventoryCategoryName())).collect(Collectors.toList());
            }
			data.forEach(m -> m.setMessageId(dto.getMessageId()).setMessageType(dto.getMessageType()));
			if (zteStockInfoUploadLogDTO.getTaskNo().equals(STR_1)) {
				dealAlibabaInventoryData(dto, data);
			} else {
				inventoryDiffQueryService.dealAliInventoryTempData(dto,data);
			}
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
		}
	}

	public void dealAlibabaInventoryData(B2BCallBackDTO dto, List<B2BCallBackInventoryResultDTO> data) {
		//插入数据
		for (List<B2BCallBackInventoryResultDTO> temp : CommonUtils.splitList(data, NumConstant.INT_500)) {
			zteAlibabaInventoryRepository.insertAlibabaInventoryData(temp);
		}
		Map<String, List<B2BCallBackInventoryResultDTO>> mpnMap = data.stream()
				.collect(Collectors.groupingBy(B2BCallBackInventoryResultDTO::getMpn, LinkedHashMap::new, Collectors.toList()));
		List<CustomerInventoryPickUpDTO> customerInventoryPickUpDTOList = zteAlibabStockInfoUploadRepository.getInventoryDataByCurrent();
		Map<String, List<CustomerInventoryPickUpDTO>> cipMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(customerInventoryPickUpDTOList)) {
			cipMap = customerInventoryPickUpDTOList.stream()
					.collect(Collectors.groupingBy(CustomerInventoryPickUpDTO::getMpn, LinkedHashMap::new, Collectors.toList()));
		}
		List<ZmsInventoryDiffDTO> diffList = setDiffList(cipMap,mpnMap);
		zteAlibabaInventoryRepository.delInventoryDiffData();
		for (List<ZmsInventoryDiffDTO> temp : CommonUtils.splitList(diffList, NumConstant.INT_500)) {
			zteAlibabaInventoryRepository.insertInventoryDiffData(temp);
		}
	}

	public List<ZmsInventoryDiffDTO> setDiffList(Map<String, List<CustomerInventoryPickUpDTO>> cipMap,Map<String, List<B2BCallBackInventoryResultDTO>> mpnMap){
		//获取成品库类型
		List<String> categoryNameList =  getCategoryName();
		List<ZmsInventoryDiffDTO> diffList = new ArrayList<>();
		mpnMap.forEach((mpn, items) -> {
			//根据categoryName的值是否在成品库存数据字典区分原材料和成品
			String categoryName = items.stream().map(B2BCallBackInventoryResultDTO::getCategoryName).distinct().findFirst().orElse("");
			Integer inventoryType = categoryNameList.contains(categoryName) ? INT_1: INT_0;
			ZmsInventoryDiffDTO diff = new ZmsInventoryDiffDTO();
			diff.setMpn(mpn).setInventoryType(inventoryType);
			diff.setAvailableQuantity(items.stream().map(B2BCallBackInventoryResultDTO::getAvailableQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
			diff.setFreezeQuantity(items.stream().map(B2BCallBackInventoryResultDTO::getFreezeQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
			diff.setAlibabaStockQty(items.stream().map(B2BCallBackInventoryResultDTO::getInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
			if (!cipMap.containsKey(mpn)) {
				diff.setZteStockQty(BIG_0).setZteOnlineQty(BIG_0).setZteInstockQty(BIG_0);
			} else {
				List<CustomerInventoryPickUpDTO> finalCipList = cipMap.get(mpn);
				diff.setZteInstockQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(STR_1)).map(CustomerInventoryPickUpDTO::getVendorInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
				diff.setZteOnlineQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(STR_2)).map(CustomerInventoryPickUpDTO::getVendorInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
				diff.setZteStockQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(STR_3)).map(CustomerInventoryPickUpDTO::getVendorInventoryQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
				if(inventoryType == INT_0){
					diff.setZteStockQty(diff.getZteInstockQty().add(diff.getZteOnlineQty()));
				}
			}
			diff.setDiffStockQty(diff.getAlibabaStockQty().subtract(diff.getZteStockQty()).abs());
			diffList.add(diff);
		});
		return diffList;
	}

	/**
	 * 获取成品库类型
	 */
	public List<String> getCategoryName(){
		//获取数据字典项
		SysLookupValuesDTO sysLookupValues = new SysLookupValuesDTO();
		sysLookupValues.setLookupType(LOOKUP_TYPE_CATEGORY_NAME);
		List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValues);
		return sysLookupValuesDTOList.stream().map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());
	}

	/**
	 * OMS零星领、退料工单发料计划
	 */
	public void dealOmsBillsResult(B2BCallBackDTO dto,ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO) {
		if (Tools.notEquals(STR_TRUE, dto.getSuccess())) {
			return;
		}
		B2BCallBackDataDTO b2bCallbackData = JacksonJsonConverUtil.jsonToBean(reformatJson(dto.getData()), new TypeReference<B2BCallBackDataDTO>() {});
		if(b2bCallbackData == null || b2bCallbackData.getData() == null || b2bCallbackData.getData().getResult() == null){
			return;
		}
		AliCreateBillResDataDTO aliCreateBillResDataDTO = JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(b2bCallbackData.getData().getResult()), new TypeReference<AliCreateBillResDataDTO>() {});
		if (aliCreateBillResDataDTO.isSuccess()) {
			// 如果成功，更新kxstepiii.stock_upload_log的状态1，
			zteStockInfoUploadLogDTO.setUploadStatus(STR_1);
			zteStockInfoUploadRepository.updateStockUploadLogStatus(zteStockInfoUploadLogDTO);
		}
	}

	@Override
	public void doExcutedDistributeMaterial(List<IscpEdiLog> iscpEdiLogs){

		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100006);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		for (IscpEdiLog iscpEdiLog : iscpEdiLogs) {
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(iscpEdiLog.getExternkey());
			zteDeductionPlanParamDTO.setBillType(iscpEdiLog.getSourcekey());
			zteDeductionPlanParamDTO.setMessageType(sysLookupValuesDTO.getLookupMeaning());
			List<AliOrderDeductionBillDTO> orderDeductionInfoList = zteStockMoveInfoUploadRepository.getDistributeMaterialInfo(iscpEdiLog);
			if (Tools.isEmpty(orderDeductionInfoList)) {
				continue;
			}
			zteDeductionPlanParamDTO.setEmpNo(SYSTEM);
			doCreateDistributeMaterialAliBill(orderDeductionInfoList, zteDeductionPlanParamDTO);
		}
	}

	private void doCreateDistributeMaterialAliBill(List<AliOrderDeductionBillDTO> createInfo, ZteDeductionPlanParamDTO dto){
		//组装工单扣料数据
		OrderDeductionInfoDTO orderDeductionInfoDTO = new OrderDeductionInfoDTO();
		try {
			orderDeductionInfoDTO = getDistributeMaterialRequestDTO(createInfo, dto);
		}catch (Exception e){
			log.error("getDistributeMaterialRequestDTO exception:", e);
		}
		//推送B2B
		pushDataToB2B(JSON.toJSONString(orderDeductionInfoDTO), dto, DEDUCTION_INFO_ZH);
	}

	private OrderDeductionInfoDTO getDistributeMaterialRequestDTO(List<AliOrderDeductionBillDTO> orderDeductionInfoList, ZteDeductionPlanParamDTO dto) throws Exception{
		OrderDeductionInfoDTO orderDeductionInfoDTO =  new OrderDeductionInfoDTO();
		List<OrderDeductionInfoDTO.MaterialDTO> materialList = new ArrayList<>();
		// 按mpn分组
		Map<String, List<AliOrderDeductionBillDTO>> mpnMap = orderDeductionInfoList.stream().collect(Collectors.groupingBy(AliOrderDeductionBillDTO::getMpn));

		//获取业务分类
		List<String> taskNos = orderDeductionInfoList.stream().map(AliOrderDeductionBillDTO::getExternOrderkey).distinct().collect(Collectors.toList());
		List<BulkTaskDetailDTO>  bulkTaskDetailList = imesCenterfactoryRemoteService.getCategory(taskNos, dto.getEmpNo());

		mpnMap.forEach((mpn, items) -> {
			Map<String, List<AliOrderDeductionBillDTO>> boxMap = items.stream().collect(Collectors.groupingBy(AliOrderDeductionBillDTO::getBoxType));
			AliOrderDeductionBillDTO aliOrderDeductionBillDTO = items.get(INT_0);
			orderDeductionInfoDTO.setVoucherOrderNo(aliOrderDeductionBillDTO.getExternOrderNo() + aliOrderDeductionBillDTO.getSourcekey());
			orderDeductionInfoDTO.setMaterialOrderNo(aliOrderDeductionBillDTO.getExternOrderNo());
			orderDeductionInfoDTO.setCategory(bulkTaskDetailList.stream().filter(t -> t.getTaskNo().equals(aliOrderDeductionBillDTO.getExternOrderkey())).findFirst().orElse(new BulkTaskDetailDTO()).getCustomerPartType());
			boxMap.forEach((boxType, boxItems) -> {
				OrderDeductionInfoDTO.MaterialDTO materialDTO = new OrderDeductionInfoDTO.MaterialDTO();
				//发料记录
				materialDTO.setMaterialName(mpn);
				materialDTO.setQuantityIssue(boxItems.stream().mapToInt(AliOrderDeductionBillDTO::getQtyReceived).sum());
				materialDTO.setMaterialQuality(STR_NUMBER_ONE);
					if(NON_CONTROL.equals(boxType)){
						materialDTO.setDetailType(STR_NUMBER_FIVE);
					}else if(ORIGIN_BOX.equals(boxType)){
						materialDTO.setDetailType(STR_NUMBER_THREE);
					}else {
						materialDTO.setDetailType(STR_NUMBER_FOUR);
					}
					// 一码通管控时，需要传发料详情（领料混箱不传）
					if (Tools.notEquals(NON_CONTROL, boxType)){
						materialDTO.setBillList(createBillList(boxItems));
					}

				materialList.add(materialDTO);
			});
		});
		orderDeductionInfoDTO.setMaterialDetail(materialList);
		return orderDeductionInfoDTO;
	}

	//创建发料详情
	private List<OrderDeductionInfoDTO.billDTO> createBillList(List<AliOrderDeductionBillDTO> items){
		List<OrderDeductionInfoDTO.billDTO> list = new ArrayList<>();
		for (AliOrderDeductionBillDTO item : items) {
			// 领料混箱不传
			if (Tools.equals(INT_1, item.getOperateType()) && Tools.equals(MIX_BOX, item.getBoxType())) {
				continue;
			}
			OrderDeductionInfoDTO.billDTO billDTO = createBillDTO(item);
			list.add(billDTO);
		}
		return list;
	}

	private OrderDeductionInfoDTO.billDTO createBillDTO(AliOrderDeductionBillDTO aliOrderDeductionBillDTO){
		OrderDeductionInfoDTO.billDTO dto = new OrderDeductionInfoDTO.billDTO();
		dto.setSnNo(aliOrderDeductionBillDTO.getSn());
		dto.setMaterialBatch(aliOrderDeductionBillDTO.getToId());
		dto.setQuantityIssue(aliOrderDeductionBillDTO.getQtyReceived());
		return dto;
	}

	@Override
	public void doExecutedCreateMoveBill(List<IscpEdiLog> iscpEdiLogs) {

		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		for (IscpEdiLog iscpEdiLog : iscpEdiLogs) {
			ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
			zteDeductionPlanParamDTO.setBillNo(iscpEdiLog.getExternkey());
			zteDeductionPlanParamDTO.setBillType(iscpEdiLog.getSourcekey());
			zteDeductionPlanParamDTO.setMessageType(sysLookupValuesDTO.getLookupMeaning());
			List<AliCreateMoveBillDTO> aliCreateMoveBillList = zteStockMoveInfoUploadRepository.getCreateInfo(iscpEdiLog);
			if (Tools.isEmpty(aliCreateMoveBillList)) {
				continue;
			}
			zteDeductionPlanParamDTO.setEmpNo(SYSTEM);
			doCreateAliBill(aliCreateMoveBillList, zteDeductionPlanParamDTO);
		}
	}

	private void doCreateAliBill(List<AliCreateMoveBillDTO> createInfo, ZteDeductionPlanParamDTO dto) {
		//组装调拨单数据
		TransferRequestDTO transferRequestDTO = getTransferRequestDTO(createInfo);
		//推送B2B
		pushDataToB2B(JSON.toJSONString(transferRequestDTO), dto, CREATE_BILL);
	}

	private TransferRequestDTO getTransferRequestDTO(List<AliCreateMoveBillDTO> aliCreateMoveBillInfoList) {

		TransferRequestDTO transferRequestDTO = new TransferRequestDTO();
		AliCreateMoveBillDTO aliCreateMoveBillDTO = aliCreateMoveBillInfoList.get(0);
		// //ZTE101_Create_Transfer_+时间日期+流水码 例：ZTE101_Create_Transfer_20250507001
		transferRequestDTO.setTransactionId(RedisSerialNoUtil.getDateIncreaseId(ALIBABA_FACTORY_CODE+TWO_SPLIT+CREATE_TRANSFER, INT_3));
		//ZTE101_Move+时间日期+流水码 ZTE101_Move_20250507001
		transferRequestDTO.setSourceNumber(RedisSerialNoUtil.getDateIncreaseId(ALIBABA_FACTORY_CODE+TWO_SPLIT+MOVE, INT_3));
		transferRequestDTO.setBizScenes(BIZ_SCENES);
		transferRequestDTO.setTransferType(TRANSFER_TYPE);
		transferRequestDTO.setCreatorName(ALIBABA_FACTORY_CODE);
		transferRequestDTO.setExpectArriveDate(new Date());
		transferRequestDTO.setSourceOrganization(ALIBABA_FACTORY_CODE);
		transferRequestDTO.setSourceSubOrganization(aliCreateMoveBillDTO.getSourceSubOrganization());
		transferRequestDTO.setTargetOrganization(ALIBABA_FACTORY_CODE);

		//如果是来料为不合格，或者良品仓(以27仓为例)调拨到34仓，该字段传部件良品库编码：ZTE101_NG01；
		transferRequestDTO.setTargetSubOrganization(getTargetSubOrganization(aliCreateMoveBillDTO));

		// 按箱类型分组
		Map<String, List<AliCreateMoveBillDTO>> boxTypeMap = aliCreateMoveBillInfoList.stream()
				.collect(Collectors.groupingBy(AliCreateMoveBillDTO::getBoxType, LinkedHashMap::new, Collectors.toList()));

		List<TransferRequestDTO.LineDTO> lineDTOList = new ArrayList<>();
		AtomicInteger lineNo = new AtomicInteger(INT_1); // 行号递增

		boxTypeMap.forEach((boxType, items) -> {
			//行信息
			TransferRequestDTO.LineDTO lineDTO = new TransferRequestDTO.LineDTO();
			lineDTO.setLineNo(String.valueOf(lineNo.getAndIncrement())); // 行号递增
			lineDTO.setMpn(Optional.ofNullable(items.get(INT_0)).orElse(new AliCreateMoveBillDTO()).getMpn());
			//如果是非一码通管控，走这里的逻辑
			if (NON_CONTROL.equals(boxType)){
				lineDTO.setQuantity(items.stream().mapToInt(AliCreateMoveBillDTO::getQtyReceived).sum());
				lineDTOList.add(lineDTO);
				return;
			}

			lineDTO.setFromWarehouseLocation(Optional.ofNullable(items.get(INT_0)).orElse(new AliCreateMoveBillDTO()).getFromWarehouseLocation());
			lineDTO.setToWarehouseLocation(Optional.ofNullable(items.get(INT_0)).orElse(new AliCreateMoveBillDTO()).getToWarehouseLocation());
			// 调拨数量
			boolean isOriginBox = ORIGIN_BOX.equals(boxType);
			setQuantity(lineDTO, items, isOriginBox);
			//行下面的设备信息
			lineDTO.setShipGoodsList(createShipGoodsList(items, isOriginBox));

			lineDTOList.add(lineDTO);
		});

		transferRequestDTO.setLines(lineDTOList);
		return transferRequestDTO;
	}

	private String getTargetSubOrganization(AliCreateMoveBillDTO aliCreateMoveBillDTO) {
		return ZTE101_NG01.equals(aliCreateMoveBillDTO.getSourceSubOrganization()) ? ZTE101_CO01 : ZTE101_NG01;
	}

	private void setQuantity(TransferRequestDTO.LineDTO lineDTO, List<AliCreateMoveBillDTO> items, boolean isOriginBox) {
		if (isOriginBox) {
			lineDTO.setQuantity(items.stream().mapToInt(AliCreateMoveBillDTO::getQtyReceived).sum());
		} else {
			lineDTO.setQuantity(items.size());
		}
	}

	// 创建货物列表
	private List<TransferRequestDTO.ShipGoodsDTO> createShipGoodsList(List<AliCreateMoveBillDTO> items, boolean isOriginBox) {
		return items.stream()
				.map(item -> createShipGoodsDTO(item, isOriginBox))
				.collect(Collectors.toList());
	}

	// 创建单个ShipGoodsDTO
	private TransferRequestDTO.ShipGoodsDTO createShipGoodsDTO(AliCreateMoveBillDTO item, boolean isOriginBox) {
		TransferRequestDTO.ShipGoodsDTO dto = new TransferRequestDTO.ShipGoodsDTO();
		if (isOriginBox) {
			dto.setSkuCode(item.getToId());
			dto.setSpecification(item.getQtyReceived()); // 原箱传箱数量
		} else {
			dto.setSkuCode(item.getSn());
			dto.setSpecification(null); // 混箱不填
		}
		dto.setQuantity(INT_1); // 每个ShipGoods数量为1
		dto.setWarehouseLocation(item.getWarehouseLocation());
		dto.setSkuType(item.getSkuType());
		return dto;
	}

	/**
	 * 阿里推送B2B
	 * @param json     阿里交互DTO转成的Json字符串
	 * @param dto      通用dto，记录日志信息
	 * @param projName 接口名称常量，自己定义：例如：部件出入库计划申请&执行&核销、创建调拨单等等
	 */
	public Boolean pushDataToB2B(String json, ZteDeductionPlanParamDTO dto, String projName) {

		//查询该单据是否已经上传过日志，如果是则通过id更新
		ZteStockInfoUploadLogDTO logDto = new ZteStockInfoUploadLogDTO();
		logDto.setContractNo(dto.getBillNo());
		logDto.setTaskNo(dto.getBillType());
		logDto.setItemNo(dto.getBusinessType());
		logDto.setRemark(dto.getRemark());
		logDto.setMessageType(dto.getMessageType());
		logDto.setId(dto.getMessageId());
		List<ZteStockInfoUploadLogDTO> existLogInfo;
		//如果id和单号都为空。默认没有上传过日志
		if (StringUtils.isBlank(logDto.getId()) && StringUtils.isBlank(logDto.getContractNo())) {
			existLogInfo = Lists.newArrayList();
		} else {
			existLogInfo = zteStockInfoUploadRepository.getStockUploadLog(logDto);
		}
		//组装对象
		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
		if (CollectionUtils.isEmpty(existLogInfo)) {
			if(StringUtils.isNotEmpty(dto.getMessageId())) {
				customerDataLogDTO.setKeywords(dto.getMessageId());
			} else {
				customerDataLogDTO.setKeywords(UUID.randomUUID().toString());
			}
		} else {
			customerDataLogDTO.setKeywords(existLogInfo.get(INT_0).getId());
		}
		customerDataLogDTO.setOrigin(INFOR_WMS);
		customerDataLogDTO.setCustomerName(ALIBABA);
		customerDataLogDTO.setProjectName(projName);
		customerDataLogDTO.setProjectPhase(STRING_EMPTY);
		customerDataLogDTO.setCooperationMode(STRING_EMPTY);
		customerDataLogDTO.setMessageType(dto.getMessageType());
		customerDataLogDTO.setContractNo(dto.getBillNo());
		customerDataLogDTO.setTaskNo(dto.getBillType());
		customerDataLogDTO.setItemNo(dto.getBusinessType());
		customerDataLogDTO.setSn(dto.getSourceSystem());
		customerDataLogDTO.setJsonData(json);
		customerDataLogDTO.setFactoryId(INT_51);
		customerDataLogDTO.setCreateBy(dto.getEmpNo());
		customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
		customerDataLogDTO.setRemark(dto.getRemark());
		customerDataLogDTOList.add(customerDataLogDTO);

		// 组装日志对象
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
		zteStockInfoUploadLogDTO.setId(customerDataLogDTO.getKeywords());
		zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);

		// 插入或者更新上传日志
		for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
			zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(tempZteStockInfoUploadLogDTOList);
		}

		// 上传B2B
		for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
			imesCenterfactoryRemoteService.pushDataToB2BKafKa(tempCustomerDataLogDTOList, dto.getEmpNo());
		}
		return true;
	}

	@Override
	public void executeQueryInventoryJob(ZteAlibabaInventoryParamDTO dto) {

		//取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100011);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		Map<String, String> map = new HashMap<>();
		map.put("factory_code", ALIBABA_FACTORY_CODE);
		//组装对象
		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
		customerDataLogDTO.setKeywords(UUID.randomUUID().toString());
		customerDataLogDTO.setOrigin(INFOR_WMS);
		customerDataLogDTO.setCustomerName(ALIBABA);
		customerDataLogDTO.setProjectName(ALI_STOCK_ZH);
		customerDataLogDTO.setProjectPhase(STRING_EMPTY);
		customerDataLogDTO.setCooperationMode(STRING_EMPTY);
		customerDataLogDTO.setMessageType(sysLookupValuesDTO.getLookupMeaning());
		customerDataLogDTO.setContractNo(STRING_EMPTY);
		customerDataLogDTO.setTaskNo(dto.getTaskType());
		customerDataLogDTO.setItemNo(STRING_EMPTY);
		customerDataLogDTO.setSn(STRING_EMPTY);
		customerDataLogDTO.setJsonData(JSON.toJSONString(map));
		customerDataLogDTO.setFactoryId(INT_51);
		customerDataLogDTO.setCreateBy(dto.getEmpNo());
		customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
		customerDataLogDTOList.add(customerDataLogDTO);

		//组装日志对象
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
		zteStockInfoUploadLogDTO.setId(customerDataLogDTO.getKeywords());
		zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
		zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(zteStockInfoUploadLogDTOList);

		imesCenterfactoryRemoteService.pushDataToB2BKafKa(customerDataLogDTOList, dto.getEmpNo());
	}

	@Override
	public void executeSnInventoryUploadJob(ZteDeductionPlanParamDTO requestDto) {
		//先取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100009);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		requestDto.setMessageType(sysLookupValuesDTO.getLookupMeaning());

		//先取仓库信息
		List<ZteWarehouseInfoDTO> zteWarehouseList = zteAlibabStockInfoUploadRepository.getInforWarehouseList();
		if (CollectionUtils.isEmpty(zteWarehouseList)) {
			return;
		}

		//查询所有原箱和混箱库存
		List<ZteSnInventoryQueryDTO> allInventoryStockList = zteSnBoundPkgIdRepository.getAllInventory(zteWarehouseList);

		//查不到库存，直接返回
		if (CollectionUtils.isEmpty(allInventoryStockList)) {
			return;
		}

		int customerInventoryDetailLineNumber = 1;
		//组装阿里请求对象
		List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoList = new ArrayList<>();
		String inventoryDirective = getDirective();
		String versionSeqNo = String.valueOf(System.currentTimeMillis());
		for (ZteSnInventoryQueryDTO zteSnInventoryReqDto : allInventoryStockList) {
			zmsInventoryMergedDtoList.add(getZmsInventoryMergeDto(zteSnInventoryReqDto, requestDto,
					customerInventoryDetailLineNumber, inventoryDirective, versionSeqNo));
			customerInventoryDetailLineNumber++;
		}
		// 按InventoryType分组后每500条切割一个子数组
		List<List<CustomerInventoryLinesDTO>> splitZmsInventoryMergedDtoList = groupAndSplitList(zmsInventoryMergedDtoList);

		for (int i = 0; i < splitZmsInventoryMergedDtoList.size(); i++) {
			List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoOneBatchList = splitZmsInventoryMergedDtoList.get(i);
			//非最后一次循环
			if (i != splitZmsInventoryMergedDtoList.size() - 1) {
				generateAndPushB2B(requestDto, zmsInventoryMergedDtoOneBatchList, INT_0);
			} else {
				generateAndPushB2B(requestDto, zmsInventoryMergedDtoOneBatchList, INT_1);
			}
		}
	}

	private void generateAndPushB2B(ZteDeductionPlanParamDTO requestDto, List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoOneBatchList,int finishFlag) {
		//该批次的MessageId
		String messageId = UUID.randomUUID().toString().replace("-", STRING_EMPTY);
		ZteAliSnInventoryReqDTO zteAliSnInventoryReqDto = getAliSnInventoryDto(zmsInventoryMergedDtoOneBatchList, messageId, finishFlag);
		//先入本地记录表PLUGIN.ZMS_INVENTORY_MERGED,sendStatus=-1
		zteAlibabStockInfoUploadRepository.addInventoryMergedData(zmsInventoryMergedDtoOneBatchList);
		//非最后一批立刻推送B2B
		if (finishFlag == 0) {
			// 推送B2B
			requestDto.setMessageId(messageId);
			executePushB2B(requestDto, zteAliSnInventoryReqDto);
		}
	}

	private void executePushB2B(ZteDeductionPlanParamDTO requestDto, ZteAliSnInventoryReqDTO zteAliSnInventoryReqDto) {
		boolean pushB2BResult = false;
		try {
			pushB2BResult = pushDataToB2B(JSON.toJSONString(zteAliSnInventoryReqDto),requestDto,SN_INVENTORY_INFO_ZH);
		} catch (Exception ee) {
            log.error("SN库存推送B2B异常:",ee);
		}
		//推送B2B无异常
		if (pushB2BResult) {
			//本地记录表更新成上传中
			CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
			customerInventoryLinesDto.setMessageId(requestDto.getMessageId());
			customerInventoryLinesDto.setLastUpdatedBy(requestDto.getEmpNo());
			customerInventoryLinesDto.setSendStatus(STR_NUMBER_ZERO);
			zteAlibabStockInfoUploadRepository.updateInventoryMergedStatusExt(customerInventoryLinesDto);
		}
	}

	private ZteAliSnInventoryReqDTO getAliSnInventoryDto(List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoNonLastBatchList,String messageId,int finishFlag) {
		ZteAliSnInventoryReqDTO zteAliSnInventoryReqDto = new ZteAliSnInventoryReqDTO();
		List<ZteAliSnInventoryDetailLineDTO> zteAliSnInventoryDetailLineDtoList = new ArrayList<>();
		for(CustomerInventoryLinesDTO zmsInventoryMergedDTO : zmsInventoryMergedDtoNonLastBatchList) {
			zmsInventoryMergedDTO.setFinishFlag(finishFlag);
			zmsInventoryMergedDTO.setMessageId(messageId);
			zteAliSnInventoryReqDto.setInventoryType(zmsInventoryMergedDTO.getInventoryType());
			zteAliSnInventoryReqDto.setInventoryDirective(zmsInventoryMergedDTO.getInventoryDirective());
			zteAliSnInventoryReqDto.setFactoryCode(zmsInventoryMergedDTO.getFactoryCode());
			zteAliSnInventoryReqDto.setVersionSeqNo(zmsInventoryMergedDTO.getVersionSeqNo());
			zteAliSnInventoryReqDto.setFinishFlag(zmsInventoryMergedDTO.getFinishFlag());
			ZteAliSnInventoryDetailLineDTO zteAliSnInventoryDetailLineDto = getZteAliSnInventoryDetailLineDTO(zmsInventoryMergedDTO);
			zteAliSnInventoryDetailLineDtoList.add(zteAliSnInventoryDetailLineDto);
		}
		zteAliSnInventoryReqDto.setCustomerInventoryDetailLineList(zteAliSnInventoryDetailLineDtoList);
		return zteAliSnInventoryReqDto;
	}

	private ZteAliSnInventoryDetailLineDTO getZteAliSnInventoryDetailLineDTO(CustomerInventoryLinesDTO zmsInventoryMergedDTO) {
		ZteAliSnInventoryDetailLineDTO zteAliSnInventoryDetailLineDto = new ZteAliSnInventoryDetailLineDTO();
		BeanUtils.copyProperties(zmsInventoryMergedDTO,zteAliSnInventoryDetailLineDto);
		return zteAliSnInventoryDetailLineDto;
	}

	/**
	 * 分组
	 */
	public List<List<CustomerInventoryLinesDTO>> groupAndSplitList(List<CustomerInventoryLinesDTO> zmsInventoryMergedDtoList) {
		int limitNum = ediPosRepository.getTransCountLimit(Constant.LOOKUP_CODE_100008800005);
		return zmsInventoryMergedDtoList.stream()
				// 按inventoryType分组
				.collect(Collectors.groupingBy(CustomerInventoryLinesDTO::getInventoryType))
				// 处理每个分组
				.values().stream()
				// 对每个分组进行分割处理
				.flatMap(group -> {
					List<List<CustomerInventoryLinesDTO>> result = new ArrayList<>();
					for (int i = 0; i < group.size(); i += limitNum) {
						int end = Math.min(i + limitNum, group.size());
						result.add(group.subList(i, end));
					}
					return result.stream();
				})
				.collect(Collectors.toList());
	}

	private CustomerInventoryLinesDTO getZmsInventoryMergeDto(ZteSnInventoryQueryDTO zteSnInventoryReqDto,ZteDeductionPlanParamDTO requestDto,
															  int customerInventoryDetailLineNumber, String inventoryDirective, String versionSeqNo) {
		CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
		customerInventoryLinesDto.setInventoryType(zteSnInventoryReqDto.getInventoryType());
		customerInventoryLinesDto.setInventoryDirective(inventoryDirective);
		customerInventoryLinesDto.setFactoryCode(ALIBABA_FACTORY_CODE);
		customerInventoryLinesDto.setVersionSeqNo(versionSeqNo);
		customerInventoryLinesDto.setCustomerInventoryLineNumber(String.valueOf(customerInventoryDetailLineNumber));
		customerInventoryLinesDto.setMpn(zteSnInventoryReqDto.getMpn());
		customerInventoryLinesDto.setLocatorType(zteSnInventoryReqDto.getLocatorType());
		customerInventoryLinesDto.setInventoryBatch(zteSnInventoryReqDto.getInventoryBatch());
		customerInventoryLinesDto.setCartonId(zteSnInventoryReqDto.getCartonId());
		customerInventoryLinesDto.setVendorCartonQuantity(zteSnInventoryReqDto.getVendorCartonQuantity());
		customerInventoryLinesDto.setSn(zteSnInventoryReqDto.getSn());
		customerInventoryLinesDto.setItemType(zteSnInventoryReqDto.getItemType());
		customerInventoryLinesDto.setMessageType(requestDto.getMessageType());
		customerInventoryLinesDto.setCreatedBy(requestDto.getEmpNo());
		customerInventoryLinesDto.setLastUpdatedBy(requestDto.getEmpNo());
		return customerInventoryLinesDto;
	}

	@Override
	public void executeSnInventoryUploadLastBatchJob(ZteDeductionPlanParamDTO requestDto) {
		//先取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100009);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		requestDto.setMessageType(sysLookupValuesDTO.getLookupMeaning());

		//查询库存推送表中的近一天的数据
		CustomerInventoryLinesDTO customerInventoryLinesDto = new CustomerInventoryLinesDTO();
		customerInventoryLinesDto.setMessageId(requestDto.getMessageId());
		customerInventoryLinesDto.setMessageType(requestDto.getMessageType());
		List<CustomerInventoryLinesDTO> customerInventoryLinesDtoList = zteAlibabStockInfoUploadRepository.getAllUnsuccessfulInventoryMergedData(customerInventoryLinesDto);
		//找到非第一批且未发送成功的数据
		List<CustomerInventoryLinesDTO> customerInventoryLinesUnsuccessfulList = customerInventoryLinesDtoList.stream().filter(o ->
				o.getFinishFlag().equals(INT_0) && !Objects.equals(o.getSendStatus(), STR_NUMBER_ONE)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(customerInventoryLinesUnsuccessfulList)) {
			//发送最后一批数据
			List<CustomerInventoryLinesDTO> customerInventoryLinesLastBatchList = customerInventoryLinesDtoList.stream().filter(o ->
					o.getFinishFlag().equals(INT_1) && !Objects.equals(o.getSendStatus(), STR_NUMBER_ONE)).collect(Collectors.toList());
			//正常最后一批不会超过500条，保险起见也再按500分一次组
			List<List<CustomerInventoryLinesDTO>> splitZmsInventoryMergedDtoList = groupAndSplitList(customerInventoryLinesLastBatchList);
			for (List<CustomerInventoryLinesDTO> lastBatchZmsInventory : splitZmsInventoryMergedDtoList) {
				singleBatchPush(requestDto, lastBatchZmsInventory);
			}
		} else {
			//前面的批次还没推送完，补偿推送
			//避免超过500条，先分组
			List<List<CustomerInventoryLinesDTO>> splitZmsInventoryMergedDtoList = groupAndSplitList(customerInventoryLinesUnsuccessfulList);
			for (List<CustomerInventoryLinesDTO> unsuccessfulZmsInventory : splitZmsInventoryMergedDtoList) {
				singleBatchPush(requestDto, unsuccessfulZmsInventory);
			}
		}
	}

	private void singleBatchPush(ZteDeductionPlanParamDTO requestDto, List<CustomerInventoryLinesDTO> customerInventoryLinesLastBatchList) {
		if (CollectionUtils.isEmpty(customerInventoryLinesLastBatchList)) {
			return;
		}
		String messageId = customerInventoryLinesLastBatchList.get(0).getMessageId();
		Integer finishFlag = customerInventoryLinesLastBatchList.get(0).getFinishFlag();
		ZteAliSnInventoryReqDTO zteAliSnInventoryReqDto = getAliSnInventoryDto(customerInventoryLinesLastBatchList, messageId, finishFlag);
		//执行推送
		requestDto.setMessageId(messageId);
		executePushB2B(requestDto, zteAliSnInventoryReqDto);
	}

	/**
	 * 生成编码格式编码为时间格式（yyyyMMddHHmmssSSS）+⼗位随机数字字符串）
	 * ZTE-20180613202531469-801054668
	 * @return
	 */
	private String getDirective() {
		String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_SSS));
		String batchNo = CommonUtils.generate10DigitNumber();
		return STORERKEY_ZTE + HORIZON + time + HORIZON + batchNo;
	}

	private void doExecutedDeliveryDoCustomer(List<IscpEdiLog> iscpEdiLogs) {

		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100004);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		for (IscpEdiLog iscpEdiLog : iscpEdiLogs) {
			List<AliDeliveryReceiptQueryDTO> aliDeliveryReceiptQueryDTO;
			// 获取部件入库数据
			try {
				aliDeliveryReceiptQueryDTO = zteAlibabStockInfoUploadRepository.getPartsReceiptList(iscpEdiLog);
			} catch(Exception e) {
				log.error("获取部件接收入库数据异常:", e);
				BusiAssertException.result("获取部件接收入库数据异常: " + e.getMessage());
				break;
			}
			if (Tools.isEmpty(aliDeliveryReceiptQueryDTO)) {
				continue;
			}

			for (List<AliDeliveryReceiptQueryDTO> tempALiReceiptQueryDTO : CommonUtils.splitList(aliDeliveryReceiptQueryDTO, NumConstant.INT_500)) {
				// 组装对象
				AliPartsReceiptUploadDTO aliPartsReceiptUploadDTO = new AliPartsReceiptUploadDTO();
				AliDeliveryReceiptQueryDTO deliveryReceiptItem = tempALiReceiptQueryDTO.get(0);
				aliPartsReceiptUploadDTO.setActualDepartureNoteNumber(deliveryReceiptItem.getActualDepartureNoteNumber());
				aliPartsReceiptUploadDTO.setDeliveryNote(deliveryReceiptItem.getDeliveryNote());
				aliPartsReceiptUploadDTO.setCustomerRtNumber(STORERKEY_ZTE + iscpEdiLog.getExternkey() + iscpEdiLog.getSourcekey());
				aliPartsReceiptUploadDTO.setTicketType(TICKET_TYPE_ASN);
				if (Tools.isNotEmpty(deliveryReceiptItem.getActualDepartureNoteNumber())) {
					deliveryReceiptItem.setCustomerPoNumber(STRING_EMPTY);
					tempALiReceiptQueryDTO.stream().map(i -> i.setCustomerPoLineNumber(STRING_EMPTY)).collect(Collectors.toList());
				}
				aliPartsReceiptUploadDTO.setCustomerPoNumber(deliveryReceiptItem.getCustomerPoNumber());

				// 构造 AliPartsCustomerRtLineDTO 对象列表：
				List<AliPartsCustomerRtLineDTO> rtLineList = getAliPartsCustomerRtLineDTOS(tempALiReceiptQueryDTO);
				// 构造AliPartsAliPartsChildGrDTOGrDTO 对象列表
				createAliPartsChildGrDTOGrList(tempALiReceiptQueryDTO, rtLineList);

				aliPartsReceiptUploadDTO.setLines(rtLineList);
				try {
					pushDoDataToB2B(aliPartsReceiptUploadDTO, sysLookupValuesDTO.getLookupMeaning(), iscpEdiLog);
				} catch (Exception e) {
					log.error("上传数据异常:", e);
					BusiAssertException.result("上传数据异常: " + e.getMessage());
				}
			}
        }
	}

	private List<AliPartsCustomerRtLineDTO> getAliPartsCustomerRtLineDTOS(List<AliDeliveryReceiptQueryDTO> aliDeliveryReceiptQueryDTO) {
		Map<List<String>, Long> groupedCount = aliDeliveryReceiptQueryDTO.stream()
				.collect(Collectors.groupingBy(
						item -> Arrays.asList(
								item.getCustomerPoLineNumber(),
								item.getMpn(),
								item.getBrand(),
								item.getActualTimert(),
								item.getAsnLine()

						),
						Collectors.counting()
				));

		List<AliPartsCustomerRtLineDTO> rtLineList = new ArrayList<>();
		for (Map.Entry<List<String>, Long> entry : groupedCount.entrySet()) {
			List<String> key = entry.getKey();
			rtLineList.add(new AliPartsCustomerRtLineDTO(
					key.get(0),
					key.get(2),
					key.get(1),
					BigDecimal.ZERO,
					key.get(3),
					key.get(4),
					new ArrayList<>()
			));
		}
		return rtLineList;
	}

	private void createAliPartsChildGrDTOGrList(List<AliDeliveryReceiptQueryDTO> aliDeliveryReceiptQueryDTO,
								   List<AliPartsCustomerRtLineDTO> rtLineList) {
		for (AliDeliveryReceiptQueryDTO aliDeliveryItem : aliDeliveryReceiptQueryDTO) {

			if (Objects.equals(aliDeliveryItem.getTag(), STR_1)) {
				aliDeliveryItem.setSkuControl(BOX);
				aliDeliveryItem.setSkuType(BOX);
				aliDeliveryItem.setWarehouseLocation(ORIGINAL);
			} else if (Objects.equals(aliDeliveryItem.getTag(), STR_2)) {
				aliDeliveryItem.setSkuControl(BOX);
				aliDeliveryItem.setSkuType(SN);
				aliDeliveryItem.setWarehouseLocation(MIX);
			}
			if (Objects.isNull(aliDeliveryItem.getSpecifications())) {
				aliDeliveryItem.setSpecifications(BIG_0);
			}
		}
		Map<AliPartsCustomerOnlyGroup, List<AliPartsChildGrDTO>> groupedResult = aliDeliveryReceiptQueryDTO.stream()
				.collect(Collectors.groupingBy(
						item -> new AliPartsCustomerOnlyGroup(
								item.getBrand(),
								item.getMpn(),
								BigDecimal.ZERO,
								item.getActualTimert(),
								item.getAsnLine()
						),
						Collectors.mapping(item -> new AliPartsChildGrDTO(
								item.getSkuId(),
								item.getSkuType(),
								item.getSkuControl(),
								item.getSpecifications().toString(),
								INT_1,
								item.getCoo(),
								item.getWarehouseLocation(),
								item.getAliInvBatch()
						), Collectors.toList())
				))
				.entrySet().stream()
				.collect(Collectors.toMap(
						Map.Entry::getKey,
						entry -> {
							List<AliPartsChildGrDTO> children = entry.getValue();
							BigDecimal total = children.stream()
									.map(AliPartsChildGrDTO::getSpecifications)
									.map(BigDecimal::new)
									.reduce(BigDecimal.ZERO, BigDecimal::add);
							AliPartsCustomerOnlyGroup parent = entry.getKey();
							parent.setQuantity(total);
							return children;
						},
						(existing, replacement) -> existing
				));
		groupedResult.forEach((parent, children) ->{
			for(AliPartsCustomerRtLineDTO rtLine: rtLineList) {
				// TODO: 去掉tag =3 的孙节点数据（如果 Tag 不为3的 skuId 为空，可能被误删 ）
				children.removeIf(child -> child.getSkuId() == null);
				if (Objects.equals(rtLine.getBrand(), parent.getBrand()) && Objects.equals(rtLine.getMpn(), parent.getMpn()) &&
						Objects.equals(rtLine.getActualTimert(), parent.getActualTimert()) &&
						Objects.equals(rtLine.getAsnLine(), parent.getAsnLine())) {
					rtLine.setQuantity(parent.getQuantity());
					rtLine.setChildGrList(children);
				}
			}
		});
	}

	/**
	 * 部件接收入库上次B2B
     */
	private void pushDoDataToB2B(AliPartsReceiptUploadDTO aliPartsReceiptUploadDTO, String messageType, IscpEdiLog iscpEdiLog) {
		// 使用公共方法pushDataToB2B
		ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = new ZteDeductionPlanParamDTO();
		zteDeductionPlanParamDTO.setMessageType(messageType);
		zteDeductionPlanParamDTO.setBillNo(iscpEdiLog.getExternkey());
		zteDeductionPlanParamDTO.setBillType(iscpEdiLog.getSourcekey());
		zteDeductionPlanParamDTO.setEmpNo(SYSTEM);
		boolean pushB2BResult = false;
		try {
			pushB2BResult = pushDataToB2B(JSON.toJSONString(aliPartsReceiptUploadDTO), zteDeductionPlanParamDTO, PARTS_RECEIVED_WAREHOUSE);
		} catch (Exception e) {
			log.error("部件接收入库推送B2B异常:",e.getMessage());
		}
		if(pushB2BResult){
			// 更新数据上传状态为 0：上传中
			iscpEdiLog.setIsSend(INT_0);
			inforIwmsIscpRepository.updateIscpEdiLogTimes(iscpEdiLog);
		}
	}

	@Override
	public List<ZmsPicklistMainDTO> selectPicklistMain(ZmsPicklistMainInDTO zmsPicklistMainInDTO) {
		return this.zteAlibabaRepository.selectPicklistMain(zmsPicklistMainInDTO);
	}

	@Override
	public void executeInventoryDiffFeedback(List<List<CheckDifferenceIReqDTOS>> listList, List<Long> keyList, String empNo) {
		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(Constant.LOOKUP_CODE_100009100008);
		BusiAssertException.isEmpty(sysLookupValuesDTO, MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS);
		String messageId = UUID.randomUUID().toString().replace("-", STRING_EMPTY);
		ZteDeductionPlanParamDTO dto = new ZteDeductionPlanParamDTO();
		dto.setMessageType(sysLookupValuesDTO.getLookupMeaning());
		dto.setMessageId(messageId);
		dto.setEmpNo(empNo);
		Boolean pushB2BResult = this.pushDataToB2B(JSON.toJSONString(listList), dto, INVENTORY_DIFF_FEEDBACK);
		if (pushB2BResult) {
			stockDiscrepancyFeedbackRepository.updateByKeys(keyList, messageId, sysLookupValuesDTO.getLookupMeaning());
		}
	}

	@Override
	public void executedInventoryDiffFeedbackJob(InventoryDiffFeedbackJobDTO jobDTO) {
		//先取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100008);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);

		if (CollectionUtils.isNotEmpty(jobDTO.getMessageIds())) {
			List<StockDiscrepancyFeedback> infoList = stockDiscrepancyFeedbackRepository.getDataByMessageIds(jobDTO.getMessageIds());
			if (CollectionUtils.isEmpty(infoList)) {
				return;
			}
			this.assembleData(jobDTO, infoList, sysLookupValuesDTO);
			return;
		}

		// 查询推送表中的近一天推送失败的数据
		List<StockDiscrepancyFeedback> infoList = stockDiscrepancyFeedbackRepository.getAllUnsuccessfulData();
		List<String> messageIdList = infoList.stream().map(StockDiscrepancyFeedback::getMessageId)
				.filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (CollectionUtils.isEmpty(messageIdList)) {
			return;
		}
		jobDTO.setMessageIds(messageIdList);
		this.assembleData(jobDTO, infoList, sysLookupValuesDTO);
	}

	private void assembleData(InventoryDiffFeedbackJobDTO jobDTO, List<StockDiscrepancyFeedback> infoList, SysLookupValuesDTO sysLookupValuesDTO) {
		Map<String, List<StockDiscrepancyFeedback>> idToInfoListMap = infoList.stream()
				.collect(Collectors.groupingBy(StockDiscrepancyFeedback::getMessageId));
		// 根据id循环推送D
		for (String id : jobDTO.getMessageIds()) {
			ZteDeductionPlanParamDTO requestDto = new ZteDeductionPlanParamDTO();
			requestDto.setMessageType(sysLookupValuesDTO.getLookupMeaning());
			requestDto.setMessageId(id);
			requestDto.setEmpNo(jobDTO.getEmpNo());
			List<StockDiscrepancyFeedback> tempList = idToInfoListMap.get(id);
			if (CollectionUtils.isEmpty(tempList)) {
				continue;
			}
			List<CheckDifferenceIReqDTOS> reqDTOSList = new ArrayList<>();
			for (StockDiscrepancyFeedback entity : tempList) {
				CheckDifferenceIReqDTOS reqDTOS = new CheckDifferenceIReqDTOS();
				BeanUtils.copyProperties(entity, reqDTOS);
				reqDTOS.setItemId(entity.getItemType());
				reqDTOSList.add(reqDTOS);
			}
			List<List<CheckDifferenceIReqDTOS>> listList = new ArrayList<>();
			listList.add(reqDTOSList);
			Boolean pushB2BResult = this.pushDataToB2B(JSON.toJSONString(listList), requestDto, INVENTORY_DIFF_FEEDBACK);
			if (pushB2BResult) {
				List<Long> keyList = infoList.stream().map(StockDiscrepancyFeedback::getSerialkey).collect(Collectors.toList());
				stockDiscrepancyFeedbackRepository.updateByKeys(keyList, "", "");
			}
		}
	}
}

