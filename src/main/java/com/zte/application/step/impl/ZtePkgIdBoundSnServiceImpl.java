package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.step.ZtePkgIdBoundSnService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteSnBoundPkgIdRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.utils.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_51;

/**
 * <AUTHOR>
 *
 */
@Service
public class ZtePkgIdBoundSnServiceImpl implements ZtePkgIdBoundSnService {
	@Autowired
	private ZteSnBoundPkgIdRepository zteSnBoundPkgIdRepository;
	@Autowired
	private InventoryholdRecordRepository inventoryholdRecordRepository;
	@Autowired
	private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
	@Autowired
	private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

	/**
	 * 定时任务
	 */
	@Override
	public void excuteUploadMixedBoxPkgIdBoundSnJob(String empNo){
		List<String> list = zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnForExternalkey(STR_NUMBER_ONE);
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		// 取消息类型
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100007);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		for (String orderNo : list) {
			if (StringUtils.isBlank(orderNo)) {
				continue;
			}

			List<ZtePkgIdBoundSnInfoDTO> ztePkgIdBoundSnInfoDTOList = zteSnBoundPkgIdRepository.queryZtePkgIdBoundSnInfoList(orderNo, STR_NUMBER_ONE);
			if (CollectionUtils.isEmpty(ztePkgIdBoundSnInfoDTOList)) {
				continue;
			}
			List<ZtePkgIdBoundSnInfoDTO> uploadList = ztePkgIdBoundSnInfoDTOList.stream().filter(m->StringUtils.isBlank(m.getMessageId())).collect(Collectors.toList());
            if(CommonUtils.isNotEmpty(uploadList)){
				for (List<ZtePkgIdBoundSnInfoDTO> itemList : CommonUtils.splitList(uploadList, NumConstant.INT_500)) {
					pushDataToB2B(itemList, UUID.randomUUID().toString(), sysLookupValuesDTO.getLookupMeaning(), empNo);
				}
			}
			List<ZtePkgIdBoundSnInfoDTO> reUploadList = ztePkgIdBoundSnInfoDTOList.stream().filter(m->StringUtils.isNotBlank(m.getMessageId())).collect(Collectors.toList());
			if(CommonUtils.isNotEmpty(reUploadList)){
				Map<String, List<ZtePkgIdBoundSnInfoDTO>> groupList = reUploadList.stream().collect(Collectors.groupingBy(ZtePkgIdBoundSnInfoDTO::getMessageId));
				groupList.forEach((k, v) -> {
					pushDataToB2B(v, k, sysLookupValuesDTO.getLookupMeaning(), empNo);
				});
			}
        }
    }

	@Override
	public void excuteModifyOriginBoxPkgIdBoundSnJob(String empNo) {
		// 1. 直接查询所有待处理数据
		List<ZtePkgIdBoundSnInfoDTO> allBindings = zteSnBoundPkgIdRepository.queryPkgSnBindingsByExternalNo();
		if (CollectionUtils.isEmpty(allBindings)) {
			return;
		}

		// 2. 获取消息类型配置
		SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100012);
		BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
		String messageType = sysLookupValuesDTO.getLookupMeaning();

		// 3. 分离新数据和需重推数据
		Map<Boolean, List<ZtePkgIdBoundSnInfoDTO>> partitionedData = allBindings.stream()
			.collect(Collectors.partitioningBy(m -> StringUtils.isBlank(m.getMessageId())));

		List<ZtePkgIdBoundSnInfoDTO> newDataList = partitionedData.get(true);
		List<ZtePkgIdBoundSnInfoDTO> retryDataList = partitionedData.get(false);

		// 4. 处理新数据（按箱号分组+分批）
		if (CommonUtils.isNotEmpty(newDataList)) {
			processNewDataDirectly(newDataList, messageType, empNo);
		}

		// 5. 处理重推数据（按原messageId分组）
		if (CommonUtils.isNotEmpty(retryDataList)) {
			processRetryDataDirectly(retryDataList, messageType, empNo);
		}
	}

	// 直接处理新数据
	private void processNewDataDirectly(List<ZtePkgIdBoundSnInfoDTO> newDataList,
									  String messageType,
									  String empNo) {
		// 按箱号分组
		Map<String, List<ZtePkgIdBoundSnInfoDTO>> pkgGroupMap = newDataList.stream()
			.collect(Collectors.groupingBy(ZtePkgIdBoundSnInfoDTO::getPkgId));

		// 每组数据分批处理
		pkgGroupMap.forEach((pkgId, snList) -> {
			Lists.partition(snList, 500).forEach(batch -> {
				pushOriginBoxBatchDataToB2B(pkgId, batch, messageType, empNo);
			});
		});
	}

	// 直接处理重推数据
	private void processRetryDataDirectly(List<ZtePkgIdBoundSnInfoDTO> retryDataList,
										String messageType,
										String empNo) {
		// 按原messageId分组并重推
		retryDataList.stream()
			.collect(Collectors.groupingBy(ZtePkgIdBoundSnInfoDTO::getMessageId))
			.forEach((messageId, dataList) -> {
				pushDataToB2B(dataList, messageId, messageType, empNo);
			});
	}

	/**
	 * 500一次批量推送阿里接口数据
	 * @param pkgId
	 * @param batch
	 * @param messageType
	 * @param empNo
	 */
	private void pushOriginBoxBatchDataToB2B(String pkgId,
                                        List<ZtePkgIdBoundSnInfoDTO> batch,
                                        String messageType,
                                        String empNo) {
       // 1. 构建操作详情列表
    List<SyncCartonSnRelationOperateDTO.SnOperation> detailList = batch.stream()
        .map(sn -> new SyncCartonSnRelationOperateDTO.SnOperation()
            .setSn(sn.getSnCode())
            //.setOriginSn(sn.getOriginSn()) // 可选字段，根据需求决定是否保留
		).collect(Collectors.toList());

    // 2. 构建箱包操作数据
    SyncCartonSnRelationOperateDTO.CartonOperation cartonOp = new SyncCartonSnRelationOperateDTO.CartonOperation()
        .setCartonId(pkgId)
        .setMpn(batch.get(0).getMpn()) // 取第一个SN的MPN
        .setOperateDetailList(detailList);

    // 3. 构建主请求DTO
    SyncCartonSnRelationOperateDTO operateDTO = new SyncCartonSnRelationOperateDTO()
        .setOperateType(Integer.valueOf(INVENTORY_TYPE_GOOD)) // 0:新增绑定
        .setSource(ALIBABA_FACTORY_CODE) // 厂商代码
        .setOperateData(Collections.singletonList(cartonOp));

    // 4. 构建日志记录
    String messageId = UUID.randomUUID().toString();

    CustomerDataLogDTO logDTO = new CustomerDataLogDTO()
        .setKeywords(messageId)
        .setOrigin(INFOR_WMS)
        .setCustomerName(ALIBABA)
        .setProjectName(ORIGIN_BOX_BOUND_SN_UPDATE_ZH)
        .setMessageType(messageType)
        .setContractNo(batch.get(0).getExternalNo())
        .setJsonData(JSON.toJSONString(operateDTO)) // 直接序列化整个DTO对象
        .setFactoryId(51)
        .setCreateBy(empNo)
        .setLastUpdatedBy(empNo);

    // 5. 更新本地记录状态为"上传中"
    UpdateZtePkgIdBoundSnInfoDTO updateDTO = new UpdateZtePkgIdBoundSnInfoDTO()
        .setExternalkey(batch.get(0).getExternalNo())
        .setMessageId(messageId)
        .setEmpNo(empNo)
        .setMessageType(messageType)
        .setSnCodes(batch.stream().map(ZtePkgIdBoundSnInfoDTO::getSnCode).collect(Collectors.toList()));

    zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfo(updateDTO);

    // 6. 组装日志对象
    List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
    ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
    BeanUtils.copyProperties(logDTO, zteStockInfoUploadLogDTO);
    zteStockInfoUploadLogDTO.setId(messageId);
    zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);

    // 7. 记录日志
    zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(zteStockInfoUploadLogDTOList);

    // 8. 调用阿里云接口
    imesCenterfactoryRemoteService.pushDataToB2BKafKa(Collections.singletonList(logDTO), empNo);
}

	/**
	 * 推送数据
	 */
	private void pushDataToB2B(List<ZtePkgIdBoundSnInfoDTO> ztePkgIdBoundSnInfoDTOList,String messageId, String messageType,String empNo){
		Map<String,Object> data = new HashMap<String, Object>();
		data.put(STR_CATEGORY,STR_SERVER);
		data.put(STR_CREATE_CARTON_RELATION_LIST,ztePkgIdBoundSnInfoDTOList);
		//组装对象
		List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
		CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
		customerDataLogDTO.setId(UUID.randomUUID().toString());
        customerDataLogDTO.setKeywords(messageId);
		customerDataLogDTO.setOrigin(INFOR_WMS);
		customerDataLogDTO.setCustomerName(ALIBABA);
		customerDataLogDTO.setProjectName(MIXED_BOX_PKGID_BOUND_SN_ZH);
		customerDataLogDTO.setProjectPhase(STRING_EMPTY);
		customerDataLogDTO.setCooperationMode(STRING_EMPTY);
		customerDataLogDTO.setMessageType(messageType);
		customerDataLogDTO.setContractNo(ztePkgIdBoundSnInfoDTOList.get(Constant.INT_0).getExternalkey());
		customerDataLogDTO.setTaskNo(STRING_EMPTY);
		customerDataLogDTO.setItemNo(STRING_EMPTY);
		customerDataLogDTO.setSn(STRING_EMPTY);
		customerDataLogDTO.setJsonData(JSON.toJSONString(data));
		customerDataLogDTO.setFactoryId(INT_51);
		customerDataLogDTO.setCreateBy(empNo);
		customerDataLogDTO.setLastUpdatedBy(empNo);
		customerDataLogDTOList.add(customerDataLogDTO);

		//组装日志对象
		List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
		ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
		BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
		zteStockInfoUploadLogDTO.setId(messageId);
		zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);

		//更新数据
		UpdateZtePkgIdBoundSnInfoDTO dto = new UpdateZtePkgIdBoundSnInfoDTO();
		dto.setExternalkey(ztePkgIdBoundSnInfoDTOList.get(Constant.INT_0).getExternalkey());
		dto.setMessageId(messageId);
		dto.setEmpNo(empNo);
		dto.setMessageType(messageType);
		dto.setSerialkeys(ztePkgIdBoundSnInfoDTOList.stream().map(ZtePkgIdBoundSnInfoDTO::getSerialkey).collect(Collectors.toList()));
		//更新为同步中
		zteSnBoundPkgIdRepository.updateZtePkgIdBoundSnInfo(dto);
		//记录日志
		zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(zteStockInfoUploadLogDTOList);
		//上传B2B
		imesCenterfactoryRemoteService.pushDataToB2BKafKa(customerDataLogDTOList, empNo);
	}
}
