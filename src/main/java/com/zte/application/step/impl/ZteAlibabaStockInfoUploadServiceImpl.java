package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.action.iscpedi.GlobalVariable;
import com.zte.application.infor.impl.InventoryDiffQueryServiceImpl;
import com.zte.application.step.ZteAlibabaStockInfoUploadService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.EdiPoSRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_51;
import static com.zte.common.utils.NumConstant.STR_1;

/**
 * <AUTHOR>
 */
@Service
public class ZteAlibabaStockInfoUploadServiceImpl implements ZteAlibabaStockInfoUploadService {
    private static final Logger log = LoggerFactory.getLogger(ZteAlibabaStockInfoUploadServiceImpl.class);

    @Autowired
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    @Autowired
    private ZteAlibabStockInfoUploadRepository zteAlibabaStockInfoUploadRepository;
    @Autowired
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private EdiPoSRepository ediPoSRepository;
    @Autowired
    private InventoryDiffQueryServiceImpl inventoryDiffQueryService;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    private String messageType;

    @Override
    public void aliStockInfoUpload(String empNo) throws Exception {
        // 取消息类型
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100005);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        messageType = sysLookupValuesDTO.getLookupMeaning();
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO = zteAlibabaStockInfoUploadRepository.getInforWarehouseList();
        BusiAssertException.isEmpty(zteWarehouseInfoDTO, QUERY_WAREHOUSE_ERROR);
        List<CustomerInventoryPickUpDTO> allInventoryStaticsList = new ArrayList<>();
        // 获取Infor库存数据
        try {
            List<CustomerInventoryPickUpDTO> inforInventoryList = fetchInforInventory(zteWarehouseInfoDTO, empNo);
            allInventoryStaticsList.addAll(inforInventoryList);
        } catch (Exception e) {
            log.error("Infor库房查询异常:", e);
            BusiAssertException.result("Infor库房查询异常:" + e.getMessage());
        }
        // 获取标准仓的维修库库存数据
        try {
            List<CustomerInventoryPickUpDTO> repairInventoryList = fetchRepairInventory(empNo);
            allInventoryStaticsList.addAll(repairInventoryList);
        } catch (Exception e) {
            log.error("标准仓库存查询异常:", e);
            BusiAssertException.result("标准仓库存查询异常:" + e.getMessage());
        }
        // 获取成品库存数据
        try {
            List<CustomerInventoryPickUpDTO> productInventoryList = inventoryDiffQueryService.getProductInventory(empNo);
            allInventoryStaticsList.addAll(productInventoryList);
        } catch (Exception e) {
            log.error("成品库存查询异常:", e);
            BusiAssertException.result("成品库存查询异常:" + e.getMessage());
        }
        BusiAssertException.isEmpty(allInventoryStaticsList, NO_DATA_FOUND);

        try {
            saveInforInventoryToStatics(allInventoryStaticsList);
        } catch (Exception e) {
            log.error("保存查询数据异常: ", e);
            BusiAssertException.result("保存查询数据异常:: " + e.getMessage());
        }
        // 数据汇总
        List<CustomerInventoryLinesDTO> mergedData = new ArrayList<>();
        try {
            mergedData = zteAlibabaStockInfoUploadRepository.getInventoryStatisticsData();
        } catch (Exception e) {
            log.error("汇总数据异常: ", e);
            BusiAssertException.result("汇总数据异常: " + e.getMessage());
        }
        BusiAssertException.isEmpty(mergedData, MERGE_INVENTORY_NULL);
        try {
            pushDataAndUploadLog(mergedData, false);
        } catch (Exception e) {
            log.error("上传数据异常:", e);
            BusiAssertException.result("上传数据异常: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveInforInventoryToStatics(List<CustomerInventoryPickUpDTO> inforInventoryList) {
        zteAlibabaStockInfoUploadRepository.updateInventoryStaticsData();
        for (List<CustomerInventoryPickUpDTO> tempList : CommonUtils.splitList(inforInventoryList, NumConstant.INT_500)) {
            zteAlibabaStockInfoUploadRepository.addInventoryStaticsData(tempList);
        }
    }

    private List<CustomerInventoryPickUpDTO> fetchInforInventory(List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO, String empNo) {
        List<CustomerInventoryPickUpDTO> allInventoryStatics = new ArrayList<>();
        if (Tools.isNotEmpty(zteWarehouseInfoDTO)) {
            for (ZteWarehouseInfoDTO warehouseInfoDTO : zteWarehouseInfoDTO) {
                List<CustomerInventoryPickUpDTO> inventoryStatics = zteAlibabaStockInfoUploadRepository.getALiStockInfoStaticsList(warehouseInfoDTO);
                if (Tools.isNotEmpty(inventoryStatics)) {
                    for (CustomerInventoryPickUpDTO item : inventoryStatics) {
                        item.setSourceSystem(Constant.SOURCE_SYSTEM_FROM_INFOR);
                        item.setCreatedBy(empNo);
                        item.setLastUpdatedBy(empNo);
                    }
                    allInventoryStatics.addAll(inventoryStatics);
                }
            }
        }
        return allInventoryStatics;
    }

    public List<CustomerInventoryPickUpDTO> fetchRepairInventory(String empNo) {
        List<CustomerInventoryPickUpDTO> allRepairInventory = new ArrayList<>();
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(INONE_APPCODE, inoneAppcode);
        int pageIndex = INT_1;
        int pageSize = INT_100;
        int pageCount = INT_0;

        // 请求url
        String url = inoneUrl + "/" + ZTE_SCM_WMS_SSTOCK + STOCK_INFO_BY_WAREHOUSEIDS;
        CustomerInventoryRepairQueryDTO repairParam = CustomerInventoryRepairQueryDTO.builder()
                .pageIndex(pageIndex)
                .pageSize(pageSize)
                .build();

        CustomerInventoryRepairResultDTO resultDTO = getServiceData(url, repairParam, headers);
        if (null != resultDTO) {
            pageIndex = resultDTO.getCurrent();
            pageCount = resultDTO.getPages();
            for (CustomerInventoryPickUpDTO item : resultDTO.getRecords()) {
                item.setSourceSystem(SOURCE_SYSTEM_FROM_REPAIR);
                item.setCustomerControlType(NumConstant.STR_1);
                item.setCreatedBy(empNo);
                item.setLastUpdatedBy(empNo);
            }
            allRepairInventory.addAll(resultDTO.getRecords());
        }

        while (pageIndex < pageCount) {
            repairParam.setPageIndex(pageIndex++);
            resultDTO = getServiceData(url, repairParam, headers);
            if (null != resultDTO && Tools.isNotEmpty(resultDTO.getRecords())) {
                for (CustomerInventoryPickUpDTO item : resultDTO.getRecords()) {
                    item.setSourceSystem(SOURCE_SYSTEM_FROM_REPAIR);
                    item.setCustomerControlType(NumConstant.STR_1);
                    item.setCreatedBy(empNo);
                    item.setLastUpdatedBy(empNo);
                }
                allRepairInventory.addAll(resultDTO.getRecords());
            } else {
                break;
            }
        }
        return allRepairInventory;
    }

    private CustomerInventoryRepairResultDTO getServiceData(String url, CustomerInventoryRepairQueryDTO repairParam, Map<String, String> headers) {
        // 发送请求
        String res = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(repairParam), headers);
        BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
        ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});
        BusiAssertException.isEmpty(result, PUSH_B2B_FAILED);
        BusiAssertException.notEquals(SUSSESS, result.getCode().getCode(), result.getCode().getMsg());
        JSONObject bo = result.getBo();
        if (Tools.isNotEmpty(bo)) {
            return JsonUtil.parseObject(result.getBo().toJSONString(), CustomerInventoryRepairResultDTO.class);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSnapshotAndDisableOldData(List<CustomerInventoryLinesDTO> mergedData) {
        zteAlibabaStockInfoUploadRepository.updateInventoryMergedData();
        // 保存汇总后库存快照
        zteAlibabaStockInfoUploadRepository.addInventoryMergedData(mergedData);
        // 修改汇总前数据为无效数据
        zteAlibabaStockInfoUploadRepository.disableForInventoryStaticsData();
    }

    private void pushDataAndUploadLog(List<CustomerInventoryLinesDTO> customerInventoryLinesDTO, boolean isReTry) throws InterruptedException {
        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        List<CustomerDataListDTO> customerDataListDTOList = new ArrayList<>();
        if (isReTry) {
            // 按Keywords分组
            Map<String, List<CustomerInventoryLinesDTO>> groupedByKeywords = customerInventoryLinesDTO.stream()
                    .collect(Collectors.groupingBy(CustomerInventoryLinesDTO::getKeywords));
            groupedByKeywords.forEach((k, v) -> {
                ZmsALiStockInfoDTO zmsAliStockInfo = buildZmsAliStockInfo(v);

                String keywords = v.get(NumConstant.INT_0).getKeywords();
                CustomerDataLogDTO customerDataLogDTO = createCustomerDataLogDTO(zmsAliStockInfo, keywords);
                addCustomerDataLogDTOList(customerDataLogDTO, zmsAliStockInfo, customerDataListDTOList);

                ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
                BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
                zteStockInfoUploadLogDTO.setId(keywords);
                zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
            });
        } else {
            // 根据库存类型分组
            Map<String, List<CustomerInventoryLinesDTO>> groupedByInventoryType = customerInventoryLinesDTO.stream()
                    .collect(Collectors.groupingBy(CustomerInventoryLinesDTO::getInventoryType));
            List<CustomerInventoryLinesDTO> allInventoryMerged = new ArrayList<>();
            int limitNum = ediPoSRepository.getTransCountLimit(Constant.LOOKUP_CODE_100008800004);
            AtomicInteger customerInventoryLineNum = new AtomicInteger(INT_1);
            String inventoryDirective = STORERKEY_ZTE + CENTER_LINE + CommonUtils.generateTimestamp() + CENTER_LINE + CommonUtils.generate10DigitNumber();
            String timestamp = String.valueOf(System.currentTimeMillis());
            groupedByInventoryType.forEach((k, v) -> {
                for (List<CustomerInventoryLinesDTO> tempList : CommonUtils.splitList(v, limitNum)) {
                    String uuId = UUID.randomUUID().toString();
                    ZmsALiStockInfoDTO zmsAliStockInfo = createZmsAliStockInfo(tempList, uuId, inventoryDirective, timestamp, customerInventoryLineNum.get());

                    CustomerDataLogDTO customerDataLogDTO = createCustomerDataLogDTO(zmsAliStockInfo, uuId);
                    addCustomerDataLogDTOList(customerDataLogDTO, zmsAliStockInfo, customerDataListDTOList);

                    ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
                    BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
                    zteStockInfoUploadLogDTO.setId(uuId);
                    zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);

                    customerInventoryLineNum.addAndGet(tempList.size());
                }

                allInventoryMerged.addAll(v);
            });
            saveSnapshotAndDisableOldData(allInventoryMerged);
        }
        // 插入或者更新上传日志
        for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
            zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(tempZteStockInfoUploadLogDTOList);
        }
        Integer sleepTime = ediPoSRepository.getTransCountLimit(Constant.LOOKUP_CODE_100008800006);
        // 上传B2B
        for (CustomerDataListDTO customerDataListDTO : customerDataListDTOList) {
            List<CustomerDataLogDTO> customerDataLogDTOList = customerDataListDTO.getCustomerDataLogDTOList();
            imesCenterfactoryRemoteService.pushDataToB2BKafKa(customerDataLogDTOList, SYSTEM);
            // 更新汇总数据上传状态为 0：上传中
            for (CustomerDataLogDTO dto : customerDataLogDTOList) {
                CustomerInventoryLinesDTO mergedParams = CustomerInventoryLinesDTO.builder().messageId(dto.getKeywords())
                        .lastUpdatedBy(dto.getLastUpdatedBy()).build();
                zteAlibabaStockInfoUploadRepository.updateInventoryMergedStatusProcessing(mergedParams);
            }
            //整机库存,且设置了等待时间,则等待
            String inventoryType = customerDataListDTO.getInventoryType();
            if (StringUtils.isNotEmpty(inventoryType)
                    && STR_NUMBER_ONE.equals(inventoryType)
                    && Tools.isNotEmpty(sleepTime)) {
                Thread.sleep(sleepTime * (long) 1000);
            }
        }
    }

    private void addCustomerDataLogDTOList(CustomerDataLogDTO customerDataLogDTO,
                                           ZmsALiStockInfoDTO zmsAliStockInfo,
                                           List<CustomerDataListDTO> customerDataListDTOList) {
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        customerDataLogDTOList.add(customerDataLogDTO);
        CustomerDataListDTO customerDataListDTO = new CustomerDataListDTO();
        customerDataListDTO.setInventoryType(zmsAliStockInfo.getInventoryType());
        customerDataListDTO.setCustomerDataLogDTOList(customerDataLogDTOList);
        customerDataListDTOList.add(customerDataListDTO);
    }

    private ZmsALiStockInfoDTO buildZmsAliStockInfo(List<CustomerInventoryLinesDTO> inventoryList) {
        CustomerInventoryLinesDTO inventoryItem = inventoryList.get(0);
        ZmsALiStockInfoDTO zmsAliStockInfo = new ZmsALiStockInfoDTO();
        zmsAliStockInfo.setInventoryType(inventoryItem.getInventoryType());
        zmsAliStockInfo.setInventoryDirective(inventoryItem.getInventoryDirective());
        zmsAliStockInfo.setFactoryCode(ALIBABA_FACTORY_CODE);
        zmsAliStockInfo.setVersionSeqNo(inventoryItem.getVersionSeqNo());
        zmsAliStockInfo.setMachineBrand("");
        if (Tools.equals(STR_1, inventoryItem.getInventoryType())) {
            zmsAliStockInfo.setMachineBrand(STORERKEY_ZTE);
        }
        List<CustomerInventoryUpdateDTO> customerInventoryUpdateDTO = inventoryList.stream().
                map(item -> {
                    CustomerInventoryUpdateDTO customerInventory = new CustomerInventoryUpdateDTO();
                    BeanUtils.copyProperties(item, customerInventory);
                    return customerInventory;
                }).collect(Collectors.toList());
        zmsAliStockInfo.setCustomerInventoryList(customerInventoryUpdateDTO);
        return zmsAliStockInfo;
    }

    private ZmsALiStockInfoDTO createZmsAliStockInfo(List<CustomerInventoryLinesDTO> tempList, String uuId,
                                                        String inventoryDirective, String timestamp, int customerInventoryLineNum) {
        ZmsALiStockInfoDTO zmsAliStockInfo = new ZmsALiStockInfoDTO();
        zmsAliStockInfo.setInventoryType(tempList.get(INT_0).getInventoryType());
        zmsAliStockInfo.setInventoryDirective(inventoryDirective);
        zmsAliStockInfo.setFactoryCode(ALIBABA_FACTORY_CODE);
        zmsAliStockInfo.setVersionSeqNo(timestamp);
        zmsAliStockInfo.setMachineBrand("");
        if (Tools.equals(STR_1, tempList.get(INT_0).getInventoryType())) {
            zmsAliStockInfo.setMachineBrand(STORERKEY_ZTE);
        }
        for (CustomerInventoryLinesDTO item : tempList) {
            item.setCustomerInventoryLineNumber(String.valueOf(customerInventoryLineNum++));
            item.setMessageId(uuId);
            item.setMessageType(messageType);
            item.setFactoryCode(ALIBABA_FACTORY_CODE);
            item.setInventoryDirective(inventoryDirective);
            item.setVersionSeqNo(timestamp);
            item.setCreatedBy(SYSTEM);
            item.setLastUpdatedBy(SYSTEM);
        }
        List<CustomerInventoryUpdateDTO> customerInventoryUpdateDTO = tempList.stream().
                map(item -> {
                    CustomerInventoryUpdateDTO customerInventory = new CustomerInventoryUpdateDTO();
                    BeanUtils.copyProperties(item, customerInventory);
                    return customerInventory;
                }).collect(Collectors.toList());
        zmsAliStockInfo.setCustomerInventoryList(customerInventoryUpdateDTO);
        return zmsAliStockInfo;
    }

    private CustomerDataLogDTO createCustomerDataLogDTO(ZmsALiStockInfoDTO zmsAliStockInfo, String keywords) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setKeywords(keywords);
        customerDataLogDTO.setOrigin(INFOR_WMS);
        customerDataLogDTO.setCustomerName(ALIBABA);
        customerDataLogDTO.setProjectName(STOCK_INFO_ZH);
        customerDataLogDTO.setProjectPhase(STRING_EMPTY);
        customerDataLogDTO.setCooperationMode(STRING_EMPTY);
        customerDataLogDTO.setMessageType(messageType);
        customerDataLogDTO.setContractNo(STRING_EMPTY);
        customerDataLogDTO.setTaskNo(STRING_EMPTY);
        customerDataLogDTO.setItemNo(STRING_EMPTY);
        customerDataLogDTO.setSn(STRING_EMPTY);
        customerDataLogDTO.setJsonData(JSON.toJSONString(zmsAliStockInfo));
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setCreateBy(SYSTEM);
        customerDataLogDTO.setLastUpdatedBy(SYSTEM);
        return customerDataLogDTO;
    }

    @Override
    public void aliStockInfoUploadManual(CustomerInventoryLinesDTO dto) throws Exception {
        // 取消息类型
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100005);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        messageType = sysLookupValuesDTO.getLookupMeaning();
        dto.setMessageType(messageType);
        // 从汇总库存表中查败数据且次数小于5，日期做为入参（起始截止日期）
        List<CustomerInventoryLinesDTO> mergedData = zteAlibabaStockInfoUploadRepository.getInventoryMergedData(dto);
        if (Tools.isEmpty(mergedData)) {
            return;
        }
        pushDataAndUploadLog(mergedData, true);
    }
}

