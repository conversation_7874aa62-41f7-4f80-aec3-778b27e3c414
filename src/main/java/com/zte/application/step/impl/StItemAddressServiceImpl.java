/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-09-02
 * 修改历史 :
 *   1. [2019-09-02] 创建文件 by 6396000647
 **/
package com.zte.application.step.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.step.StItemAddressService;
import com.zte.domain.model.step.StItemAddress;
import com.zte.domain.model.step.StItemAddressRepository;
import com.zte.interfaces.step.dto.StAddressDTO;
import com.zte.interfaces.step.dto.StItemAddressDTO;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Service 
public class StItemAddressServiceImpl implements StItemAddressService {

    @Autowired
    private StItemAddressRepository stItemAddressRepository;

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertStItemAddress(StItemAddress record) {
        stItemAddressRepository.insertStItemAddress(record);
    }

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertStItemAddressSelective(StItemAddress record) {
        stItemAddressRepository.insertStItemAddressSelective(record);
    }

    /**
     * 根据主键删除实体数据
     * 
     * @param record
     **/
    @Override
    public void deleteStItemAddressById(StItemAddress record) {
        stItemAddressRepository.deleteStItemAddressById(record);
    }

    /**
     * 有选择性的更新实体数据
     * 
     * @param record
     **/
    @Override
    public void updateStItemAddressByIdSelective(StItemAddress record) {
        stItemAddressRepository.updateStItemAddressByIdSelective(record);
    }

    /**
     * 根据主键更新实体数据
     * 
     * @param record
     **/
    @Override
    public void updateStItemAddressById(StItemAddress record) {
        stItemAddressRepository.updateStItemAddressById(record);
    }

    @Override
    public List<StItemAddressDTO> selectStItemAddressAll(StAddressDTO record) {
        return stItemAddressRepository.selectStItemAddressAll(record);
    }

    /**
     * 根据主键查询实体信息
     * 
     * @param record
     * @return StItemAddress
     **/
    @Override
    public StItemAddress selectStItemAddressById(StItemAddress record) {
        return stItemAddressRepository.selectStItemAddressById(record);
    }
}