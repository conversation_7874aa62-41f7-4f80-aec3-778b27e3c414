package com.zte.application.step.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.zte.application.step.VmiInventoryQueryService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.step.VmiDetailInventory;
import com.zte.domain.model.step.VmiInventoryQueryRepository;
import com.zte.domain.model.step.VmiSkuInventory;
import com.zte.domain.model.step.VmiUUIDInventory;
import com.zte.interfaces.step.dto.VmiDetailInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiSkuInventoryQueryDTO;
import com.zte.interfaces.step.dto.VmiUUIDInventoryQueryDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;

@Service
public class VmiInventoryQueryServiceImpl implements VmiInventoryQueryService {

	@Autowired
	private VmiInventoryQueryRepository vmiInventoryQueryRepository;

	@Override
	public ServiceData<?> vmiSkuInventoryQuery(VmiSkuInventoryQueryDTO inDTO) {
		if (null == inDTO.getItemNo() || inDTO.getItemNo().size() <= 0) {
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SKU_INV_QUERY_VAL1));
		}
		int endRow = inDTO.getPageNo() * inDTO.getPageSize();
		int startRow = endRow - inDTO.getPageSize() + 1;
		inDTO.setEndRow(endRow);
		inDTO.setStartRow(startRow);
		List<VmiSkuInventory> list = vmiInventoryQueryRepository.selectVmiSkuInvQuery(inDTO);
		Long totals = vmiInventoryQueryRepository.selectVmiSkuInvCount(inDTO);
		PageInfo<VmiSkuInventory> pageInfo = new PageInfo<>(list);
		pageInfo.setPageNum(inDTO.getPageNo());
		pageInfo.setPageSize(inDTO.getPageSize());
		pageInfo.setSize(inDTO.getPageSize());
		pageInfo.setStartRow(startRow);
		pageInfo.setEndRow(endRow);
		pageInfo.setTotal(totals);
		int pages = (inDTO.getPageSize() > 0 ? (int) Math.ceil((float) totals /inDTO.getPageSize()) : 0);
		pageInfo.setPages(pages);
		return ServiceDataUtil.getSuccess(pageInfo);
	}

	@Override
	public ServiceData<?> vmiUUIDInventoryQuery(VmiUUIDInventoryQueryDTO inDTO) {
		if ((null == inDTO.getItemNo() || inDTO.getItemNo().size() <= 0)
				&& com.zte.utils.CommonUtils.isBlank(inDTO.getSupplyNo())) {
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_UUID_INV_QUERY_VAL1));
		}
		int endRow = inDTO.getPageNo() * inDTO.getPageSize();
		int startRow = endRow - inDTO.getPageSize() + 1;
		inDTO.setEndRow(endRow);
		inDTO.setStartRow(startRow);
		List<VmiUUIDInventory> list = vmiInventoryQueryRepository.selectVmiUUIDInvQuery(inDTO);
		Long totals = vmiInventoryQueryRepository.selectVmiUUIDInvCount(inDTO);
		PageInfo<VmiUUIDInventory> pageInfo = new PageInfo<>(list);
		pageInfo.setPageNum(inDTO.getPageNo());
		pageInfo.setPageSize(inDTO.getPageSize());
		pageInfo.setSize(inDTO.getPageSize());
		pageInfo.setStartRow(startRow);
		pageInfo.setEndRow(endRow);
		pageInfo.setTotal(totals);
		int pages = (inDTO.getPageSize() > 0 ? (int) Math.ceil((float) totals /inDTO.getPageSize()) : 0);
		pageInfo.setPages(pages);
		return ServiceDataUtil.getSuccess(pageInfo);
	}

	@Override
	public ServiceData<?> vmiDetailInventoryQuery(VmiDetailInventoryQueryDTO inDTO) {
		if ((null == inDTO.getItemNo() || inDTO.getItemNo().size() <= 0)
				&& com.zte.utils.CommonUtils.isBlank(inDTO.getSupplyNo())) {
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_UUID_INV_QUERY_VAL1));
		}
		int endRow = inDTO.getPageNo() * inDTO.getPageSize();
		int startRow = endRow - inDTO.getPageSize() + 1;
		inDTO.setEndRow(endRow);
		inDTO.setStartRow(startRow);
		List<VmiDetailInventory> list = vmiInventoryQueryRepository.selectVmiDetailInvQuery(inDTO);
		Long totals = vmiInventoryQueryRepository.selectVmiDetailInvCount(inDTO);
		PageInfo<VmiDetailInventory> pageInfo = new PageInfo<>(list);
		pageInfo.setPageNum(inDTO.getPageNo());
		pageInfo.setPageSize(inDTO.getPageSize());
		pageInfo.setSize(inDTO.getPageSize());
		pageInfo.setStartRow(startRow);
		pageInfo.setEndRow(endRow);
		pageInfo.setTotal(totals);
		int pages = (inDTO.getPageSize() > 0 ? (int) Math.ceil((float) totals /inDTO.getPageSize()) : 0);
		pageInfo.setPages(pages);
		return ServiceDataUtil.getSuccess(pageInfo);
	}
}
