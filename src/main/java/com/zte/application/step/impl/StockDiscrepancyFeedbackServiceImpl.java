package com.zte.application.step.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.zte.application.step.StockDiscrepancyFeedbackService;
import com.zte.application.step.ZteAlibabaService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.StockDiscrepancyFeedback;
import com.zte.domain.model.infor.StockDiscrepancyFeedbackRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.StockDiscrepancyFeedbackExportVO;
import com.zte.interfaces.step.dto.CheckDifferenceIReqDTOS;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackAddDTO;
import com.zte.interfaces.step.dto.StockDiscrepancyFeedbackDTO;
import com.zte.interfaces.step.vo.StockDiscrepancyFeedbackListVO;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_1;
import static com.zte.common.utils.Constant.INT_5000;
import static com.zte.common.utils.Constant.INVENTORY_DIFF_FEEDBACK_EX_NAME;
import static com.zte.common.utils.Constant.INVENTORY_HOLD_EX_NAME;
import static com.zte.common.utils.Constant.SHEET1;

/**
 * <AUTHOR>
 * @date 2025/5/16 13:53
 */
@Service
public class StockDiscrepancyFeedbackServiceImpl implements StockDiscrepancyFeedbackService {


    @Autowired
    private ZteAlibabaService zteAlibabaService;
    @Autowired
    private StockDiscrepancyFeedbackRepository stockDiscrepancyFeedbackRepository;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Override
    public StockDiscrepancyFeedbackListVO query(StockDiscrepancyFeedbackDTO queryDTO) {
        StockDiscrepancyFeedbackListVO listVo = new StockDiscrepancyFeedbackListVO();
        queryDTO.setStartRow((queryDTO.getPageIndex() - INT_1) * queryDTO.getPageSize() + INT_1)
                .setEndRow(queryDTO.getPageIndex() * queryDTO.getPageSize());
        listVo.setTotal(stockDiscrepancyFeedbackRepository.getTotalCount(queryDTO));
        List<StockDiscrepancyFeedback> infoList = stockDiscrepancyFeedbackRepository.selectByCondition(queryDTO);
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000095);
        List<SysLookupValuesDTO> sysList = this.getSysList(sysLookupValuesDTO);
        Map<String, SysLookupValuesDTO> tempMap = new HashMap<>();
        for (SysLookupValuesDTO lookupValuesDTO : sysList) {
            tempMap.put(lookupValuesDTO.getAttribute2(), lookupValuesDTO);
        }
        for (StockDiscrepancyFeedback stockDiscrepancyFeedback : infoList) {
            if (tempMap.containsKey(stockDiscrepancyFeedback.getSecondDiffCategory())) {
                stockDiscrepancyFeedback.setDiffCategory(tempMap.get(stockDiscrepancyFeedback.getSecondDiffCategory()).getDescription());
                stockDiscrepancyFeedback.setSecondDiffCategory(tempMap.get(stockDiscrepancyFeedback.getSecondDiffCategory()).getLookupMeaning());
            }
        }
        listVo.setResultList(infoList);
        return listVo;
    }

    @Override
    public void add(StockDiscrepancyFeedbackAddDTO addDTO) {
        int count = stockDiscrepancyFeedbackRepository.mpnIsSaved(addDTO);
        if (count > 0) {
            stockDiscrepancyFeedbackRepository.updateByMpn(addDTO);
        } else {
            stockDiscrepancyFeedbackRepository.add(addDTO);
        }
    }

    @Override
    public List<SysLookupValuesDTO> getSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }

    @Override
    public void deleteByKey(List<Long> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        // 已提交的数据，不可再删除
        long submittedCount = stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(keys);
        BusiAssertException.isTrue(submittedCount > 0, MessageId.DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_DELETE);
        stockDiscrepancyFeedbackRepository.deleteByKey(keys);
    }

    @Override
    public void export(StockDiscrepancyFeedbackDTO dto) {
        int total = stockDiscrepancyFeedbackRepository.getTotalCount(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            StockDiscrepancyFeedbackDTO param = ((StockDiscrepancyFeedbackDTO) params).setStartRow(statRow).setEndRow(endRow);
            List<StockDiscrepancyFeedbackExportVO> infoList = stockDiscrepancyFeedbackRepository.exportByCondition(param);
            this.transferStatus(infoList);
            this.transferTypeAndCategory(infoList);
            return new ArrayList<>(infoList);
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.INVENTORY_DIFF_FEEDBACK_EX_NAME).setFileName(Constant.INVENTORY_DIFF_FEEDBACK_EX_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(StockDiscrepancyFeedbackExportVO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    private void transferStatus(List<StockDiscrepancyFeedbackExportVO> infoList) {
        for (StockDiscrepancyFeedbackExportVO exportVO : infoList) {
            if (exportVO.getStatus().equals(Constant.SAVED)) {
                exportVO.setStatus(Constant.SAVED_CHINESE);
            } else if (exportVO.getStatus().equals(Constant.SUBMITED)) {
                exportVO.setStatus(Constant.SUBMITED_CHINESE);
            }
        }
    }

    private void transferTypeAndCategory(List<StockDiscrepancyFeedbackExportVO> infoList) {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000095);
        List<SysLookupValuesDTO> sysList = this.getSysList(sysLookupValuesDTO);
        Map<String, SysLookupValuesDTO> tempMap = new HashMap<>();
        for (SysLookupValuesDTO lookupValuesDTO : sysList) {
            tempMap.put(lookupValuesDTO.getAttribute2(), lookupValuesDTO);
        }
        for (StockDiscrepancyFeedbackExportVO exportVO : infoList) {
            if (tempMap.containsKey(exportVO.getSecondDiffCategory())) {
                exportVO.setDiffCategory(tempMap.get(exportVO.getSecondDiffCategory()).getDescription());
                exportVO.setSecondDiffCategory(tempMap.get(exportVO.getSecondDiffCategory()).getLookupMeaning());
            }
            if (exportVO.getInventoryType() == NumConstant.INT_0) {
                exportVO.setInventoryTypeStr(Constant.GOOD_QUALITY_COMPONENTS);
            } else if (exportVO.getInventoryType() == NumConstant.INT_1) {
                exportVO.setInventoryTypeStr(Constant.COMPLETE_MACHINE);
            } else if (exportVO.getInventoryType() == NumConstant.INT_4) {
                exportVO.setInventoryTypeStr(Constant.IN_PRODUCTION);
            } else if (exportVO.getInventoryType() == NumConstant.INT_8) {
                exportVO.setInventoryTypeStr(Constant.DEFECTIVE_PRODUCT);
            } else if (exportVO.getInventoryType() == NumConstant.INT_9) {
                exportVO.setInventoryTypeStr(Constant.BUFFER_STOCK);
            } else if (exportVO.getInventoryType() == NumConstant.INT_40) {
                exportVO.setInventoryTypeStr(Constant.COMPONENTS_STOCK);
            }
        }
    }

    @Override
    public void submit(StockDiscrepancyFeedbackDTO feedbackDTO) {
        if (CollectionUtils.isEmpty(feedbackDTO.getSerialkeyList())) {
            return;
        }
        // 已提交的数据，不可再提交
        long submittedCount = stockDiscrepancyFeedbackRepository.getHaveSubmittedCount(feedbackDTO.getSerialkeyList());
        BusiAssertException.isTrue(submittedCount > 0, MessageId.DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_SUBMIT);
        List<StockDiscrepancyFeedback> infoList = stockDiscrepancyFeedbackRepository.getInfoByKeys(feedbackDTO.getSerialkeyList());
        BusiAssertException.isTrue(CollectionUtils.isEmpty(infoList), MessageId.DETAILS_IS_NULL);
        List<CheckDifferenceIReqDTOS> reqDTOSList = new ArrayList<>();
        for (StockDiscrepancyFeedback entity : infoList) {
            CheckDifferenceIReqDTOS reqDTOS = new CheckDifferenceIReqDTOS();
            BeanUtils.copyProperties(entity, reqDTOS);
            reqDTOS.setItemId(entity.getItemType());
            reqDTOSList.add(reqDTOS);
        }
        // 无MessageId的List
        List<CheckDifferenceIReqDTOS> notIdList = reqDTOSList.stream().filter(item -> StringUtils.isBlank(item.getMessageId())).collect(Collectors.toList());
        List<Long> keyList = infoList.stream().filter(item -> StringUtils.isBlank(item.getMessageId())).map(StockDiscrepancyFeedback::getSerialkey).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notIdList)) {
            List<String> mpnList = infoList.stream().filter(item -> StringUtils.isNotBlank(item.getMessageId())).map(StockDiscrepancyFeedback::getMpn).collect(Collectors.toList());
            BusiAssertException.isTrue(true, MessageFormat.format(CommonUtils.getLmbMessage(MessageId.HAVE_SUBMITTED), mpnList.toString()));
        }
        List<List<CheckDifferenceIReqDTOS>> tempList = new ArrayList<>();
        tempList.add(notIdList);
        zteAlibabaService.executeInventoryDiffFeedback(tempList, keyList, feedbackDTO.getEmpNo());
    }
}
