package com.zte.application.step.impl;

import com.zte.application.step.SetpImesService;
import com.zte.domain.model.step.SetpImesRepository;
import com.zte.interfaces.step.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 退料单对接点料机相关实现类
 *
 * <AUTHOR>
 * @date 2024-01-23 10:13
 */
@Service
public class SetpImesServiceImpl implements SetpImesService {

    @Autowired
    private SetpImesRepository setpImesRepository;

    /**
     * 获取退料仓、拣选库位、存储区
     * @param dto
     * @return
     */
    @Override
    public List<BaItemDto> getStockInfo(BaItemQueryDto dto){
        return setpImesRepository.getStockInfo(dto);
    }

    /***
     * 获取项目
     * @return
     */
    @Override
    public List<ProjectDTO> getProjectInfo(){
        return setpImesRepository.getProjectInfo();
    }

    /***
     * 获取走账别名
     * @return
     */
    @Override
    public List<StUtilityDTO> getStUtilityInfo(){
        return setpImesRepository.getStUtilityInfo();
    }

    /***
     * 获取单板包装扫描推送INFO单据号
     * @return
     */
    @Override
    public String getInforBillNo(){
        return setpImesRepository.getInforBillNo();
    }

    /***
     * 获取是否是ERP计划
     * @return
     */
    @Override
    public OpOrderplanHeadDto getIsErpPlan(String prodplanId){
        return setpImesRepository.getIsErpPlan(prodplanId);
    }

    /***
     * 获取委托加工前信息
     * @return
     */
    @Override
    public BaBomHeadDto getEntrustBeforeInfo(String prodplanId) {
        return setpImesRepository.getEntrustBeforeInfo(prodplanId);
    }

    /***
     * 获取计划组信息
     * @return
     */
    @Override
    public DbProdclassPlangroupDto getPlangroupInfo(DbProdclassPlangroupDto dto) {
        return setpImesRepository.getPlangroupInfo(dto);
    }
}
