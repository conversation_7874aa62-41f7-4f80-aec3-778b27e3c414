package com.zte.application.step.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.EccnStockRemoteService;
import com.zte.application.step.OmsEcssItemService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.step.OmsEcssItemRepository;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.interfaces.infor.dto.EccnStockDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 * @since 2023年8月15日16:28
 */
@Service
@EnableAsync
public class OmsEcssItemServiceImpl implements OmsEcssItemService {

    @Autowired
    private OmsEcssItemRepository omsEcssItemRepository;

    @Autowired
    private OmsEcssRepository omsEcssRepository;

    @Autowired
    private EccnStockRemoteService eccnStockRemoteService;

    @Autowired
    private OmsEcssServiceImpl omsEcssServiceImpl;

    @Autowired
    private EmailUtil emailUtil;

    @Value("${ecss.url}")
    private String ecssUrl;

    /**
     * JOB调用注册及补偿方法
     */
    @Override
    public void registerStart() throws Exception {

        //查询待注册物料信息
        List<EcssItemDTO> ecssItemDTOList = this.omsEcssItemRepository.getUnregisteredItemList(ECSS_ITEM_ADDED);
        if (Tools.isEmpty(ecssItemDTOList)) {
            return;
        }
        //对于indFunc字段为空的物料查询ECCN接口获取数据
        List<EccnItemDTO> eccnItemListAll = ecssItemDTOList.stream()
                .filter(s -> Tools.isEmpty(s.getIndFunc()))
                .map(item -> {
                    EccnItemDTO eccnItemDTO = new EccnItemDTO();
                    eccnItemDTO.setRecordId(String.valueOf(item.getRecordId()));
                    eccnItemDTO.setItemNo(item.getItemCode());
                    eccnItemDTO.setVersion(item.getItemVersion());
                    return eccnItemDTO;
                }).collect(Collectors.toList());
        //ECCN接口上限1000，需拆分为多个List循环调用
        List<List<EccnItemDTO>> eccnItemLists = CommonUtils.splitList(eccnItemListAll, INT_1000);
        String paramsMap;
        for (List<EccnItemDTO> eccnItemList : eccnItemLists) {
            try {
                paramsMap = setEccnParams(1, "", "", eccnItemList);
                Map<String, Object> map = eccnStockRemoteService.getEccnStockInfo(paramsMap);
                List<EccnStockDTO> eccnStockDTOList = JsonUtil.parseObject(
                        JsonUtil.toJSONString(map.get("list")), new TypeReference<List<EccnStockDTO>>() {
                        }
                );
                if (CollectionUtils.isNotEmpty(eccnStockDTOList)) {
                    eccnStockDTOList.forEach(eccn -> ecssItemDTOList.stream()
                            .filter(ecss1 -> ecss1.getItemCode().equals(eccn.getItemNo()))
                            .forEach(ecss2 -> ecss2.setIndFunc(!eccn.getPurchasePartFlg().equals(STR_YES))));
                }
            } catch (Exception e) {
                eccnFail(eccnItemList, Arrays.stream(e.getStackTrace()).collect(Collectors.toList()).toString(), ecssItemDTOList);
                return;
            }
        }

        //按BATCH_ID(批次ID)分别组装ECSS调用List<DTO>
        List<FsBatchMaterialDTO> fsBatchMaterialDTOS = buildEcssParam(ecssItemDTOList);

        //按批次ID循环调用ECSS接口
        for (FsBatchMaterialDTO fsBatchMaterialDTO : fsBatchMaterialDTOS) {
            postEcssAsyncApi(fsBatchMaterialDTO);
        }
    }


    /**
     * 设置ECCN参数
     */
    public String setEccnParams(long currentPage, String startDate, String endDate, List<EccnItemDTO> itemList) {
        Map<String, Object> map = new HashMap<>(7);
        map.put("orgId", ORG_ID_0000);
        map.put("lastupDateFrom", startDate);
        map.put("lastupDateTo", endDate);
        map.put("itemList", itemList);
        map.put("pageSize", INT_1000);
        map.put("currentPage", currentPage);
        return JacksonJsonConverUtil.beanToJson(map);
    }

    /**
     * ECCN调用失败更新数据信息
     */
    public void eccnFail(List<EccnItemDTO> eccnItemList, String failReason, List<EcssItemDTO> ecssItemDTOList) {
        //失败原因截取，防止超过字符上限
        failReason = cutFailReason(failReason);

        List<String> itemCodeListAll = eccnItemList.stream()
                .map(EccnItemDTO::getRecordId).distinct()
                .collect(Collectors.toList());

        List<EcssItemDTO> itemCodeMailList = ecssItemDTOList.stream()
                .filter(ecss -> CollectionUtils.isNotEmpty(eccnItemList.stream().filter(
                                eccn -> Long.parseLong(eccn.getRecordId()) == ecss.getRecordId())
                        .collect(Collectors.toList())) && ecss.getFailNum() >= INT_3)
                .collect(Collectors.toList());
        updateFailItems(itemCodeListAll, failReason, itemCodeMailList);
    }

    /**
     * 调用失败更新数据信息
     */
    public void updateFailItems(List<String> itemCodeListAll, String failReason, List<EcssItemDTO> itemCodeMailList) {
        //200个主键每次更新失败原因及次数
        List<List<String>> itemCodeLists = CommonUtils.splitList(itemCodeListAll, INT_200);
        for (List<String> itemCodeList : itemCodeLists) {
            this.omsEcssItemRepository.updateFailList(ECSS_ITEM_ADDING, failReason, itemCodeList);
        }
        //超过3次失败的发邮件提醒
        sendMailCommon(itemCodeMailList);
    }

    /**
     * 发送邮件公共方法
     */
    public void sendMailCommon(List<EcssItemDTO> itemCodeMailList) {
        if (CollectionUtils.isEmpty(itemCodeMailList)) {
            return;
        }
        //获取收件人
        StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build()
                .setLookupType(LOOKUP_TYPE_1000022).setDescription(ECSS_BILL_FAILED);
        List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
        if (com.zte.utils.CommonUtils.isEmpty(stepSysLookupValuesDTOList)) {
            return;
        }
        List<String> users = stepSysLookupValuesDTOList.stream()
                .map(StepSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());

        //单号汇总去重
        String inGuidList = itemCodeMailList.stream()
                .map(EcssItemDTO::getInGuid).distinct().collect(Collectors.joining());

        //发送邮件
        String content = ECSS_SALE_BILL_FAILED_1 + "\r\n" + inGuidList + "\r\n" + ECSS_ITEM_ASYNC_FAILED;
        String receipts = StringUtils.join(
                users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
        emailUtil.sendMail(receipts, ECSS_ITEM_ASYNC_FAILED_WARN, BLANK, content, BLANK);
    }

    /**
     * 组装ECSS调用接口DTO
     */
    public List<FsBatchMaterialDTO> buildEcssParam(List<EcssItemDTO> ecssItemDTOList) {
        String batchIdHis = "";
        List<FsMaterialDTO> fsMaterialDTOsList = new ArrayList<>();
        FsBatchMaterialDTO fsBatchMaterialDTO = new FsBatchMaterialDTO();
        fsBatchMaterialDTO.setEccnFail(false);
        List<FsBatchMaterialDTO> fsBatchMaterialDTOList = new ArrayList<>();
        for (EcssItemDTO ecssItemDTO : ecssItemDTOList) {
            FsMaterialDTO fsMaterialDTOs = new FsMaterialDTO();
            FsGeneInfoDTO fsGeneInfoDTO = new FsGeneInfoDTO();
            MultilanStringDTO multilanStringDTO = new MultilanStringDTO();
            FsCorresDTO fsCorresDTO = new FsCorresDTO();
            Map<String, String> map = new HashMap<>(5);
            //不同的BATCH_ID(批次ID)按分页数量依次组装
            if (!batchIdHis.equals(ecssItemDTO.getBatchId()) || fsMaterialDTOsList.size() >= INT_5000) {
                buildFSBatchMaterialDTOList(fsBatchMaterialDTOList, fsBatchMaterialDTO);
                String uuid = UUID.randomUUID().toString().replace("-", "");
                FsPageDTO fsPageDTO = new FsPageDTO();
                fsPageDTO.setBatchId(uuid);
                fsPageDTO.setSubmitDate(ecssItemDTO.getCreationDate());
                fsPageDTO.setPageSize(INT_5000);
                fsPageDTO.setTotalPage(INT_1);
                fsPageDTO.setCurrentPage(INT_1);
                fsPageDTO.setTotalRecord(INT_5000);
                fsPageDTO.setInstanceId(uuid);
                fsMaterialDTOsList = new ArrayList<>();
                fsBatchMaterialDTO = new FsBatchMaterialDTO();
                fsBatchMaterialDTO.setEccnFail(false);
                fsBatchMaterialDTO.setFsPageDTO(fsPageDTO);
            }
            String itemVersion = StringUtils.isEmpty(ecssItemDTO.getItemVersion()) ? "" : ecssItemDTO.getItemVersion();
            fsMaterialDTOs.setRecordId(ecssItemDTO.getRecordId());
            fsMaterialDTOs.setFailNum(ecssItemDTO.getFailNum());
            fsMaterialDTOs.setInGuid(ecssItemDTO.getInGuid());
            fsMaterialDTOs.setRefNo(ecssItemDTO.getItemCode() + itemVersion);
            fsGeneInfoDTO.setCode(ecssItemDTO.getItemCode() + itemVersion);
            map.put(LAN_ZH, ecssItemDTO.getItemName());
            multilanStringDTO.setValues(map);
            fsGeneInfoDTO.setName(multilanStringDTO);
            if (Tools.isEmpty(ecssItemDTO.getIndFunc())) {
                fsBatchMaterialDTO.setEccnFail(true);
            } else {
                fsGeneInfoDTO.setIndFunc(ecssItemDTO.getIndFunc());
            }
            fsCorresDTO.setRefNo(ecssItemDTO.getItemCode() + itemVersion);
            fsCorresDTO.setOrgCode(ZTE_CORP);
            fsGeneInfoDTO.setFsCorresDTO(fsCorresDTO);
            fsGeneInfoDTO.setType(ecssItemDTO.getCodeType());
            fsGeneInfoDTO.setCategory(ecssItemDTO.getCodeCategory());
            fsMaterialDTOs.setFsGeneInfoDTO(fsGeneInfoDTO);

            fsMaterialDTOsList.add(fsMaterialDTOs);
            fsBatchMaterialDTO.setFsMaterialDTOs(fsMaterialDTOsList);
            FsPageDTO fsPgDTO = fsBatchMaterialDTO.getFsPageDTO();
            fsPgDTO.setTotalRecord(fsMaterialDTOsList.size());
            fsBatchMaterialDTO.setFsPageDTO(fsPgDTO);
            fsBatchMaterialDTO.setSaleOrderOrg(ecssItemDTO.getSaleOrderOrg());
            batchIdHis = ecssItemDTO.getBatchId();
        }
        //加入最后一组
        buildFSBatchMaterialDTOList(fsBatchMaterialDTOList, fsBatchMaterialDTO);
        return fsBatchMaterialDTOList;
    }

    /**
     * 加入ECSS调用入参LIST
     */
    public void buildFSBatchMaterialDTOList(List<FsBatchMaterialDTO> fsBatchMaterialDTOList, FsBatchMaterialDTO fsBatchMaterialDTO) {
        if (Tools.isNotEmpty(fsBatchMaterialDTO.getFsPageDTO())) {
            fsBatchMaterialDTOList.add(fsBatchMaterialDTO);
        }
    }

    /**
     * 调用ECSS注册接口方法
     */
    public void postEcssAsyncApi(FsBatchMaterialDTO fsBatchMaterialDTO) {
        // 测试环境：http://zecss.test.zte.com.cn/zte-grc-ecss-feedersystemapi/pub-api/v2/batchmaterial/async
        // 生产环境：https://zecss.zte.com.cn/zte-grc-ecss-feedersystemapi/pub-api/v2/batchmaterial/async

        if (fsBatchMaterialDTO.getEccnFail()) {
            ecssFail(fsBatchMaterialDTO, ECCN_INDFUNC_EMPTY);
            return;
        }
        if (Tools.isEmpty(fsBatchMaterialDTO.getFsPageDTO())) {
            return;
        }
        //拼接头参数
        Map<String, String> headerParamsMap = omsEcssServiceImpl.getEcssHeaderMap(fsBatchMaterialDTO.getSaleOrderOrg());
        headerParamsMap.put("Content-type", "application/json; charset=utf-8");
        headerParamsMap.put("Accept", "application/json; charset=utf-8");
        headerParamsMap.put("X-Lang-Id", "zh");
        fsBatchMaterialDTO.setSaleOrderOrg(null);

        try {
            String responseStr = HttpClientUtil.httpPostWithJSON(ecssUrl + ZTE_ECSS_BATCHMATERIAL_ASYNC,
                    JacksonJsonConverUtil.beanToJson(fsBatchMaterialDTO), headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
            if (null != json) {
                JsonNode boNode = json.get(JSON_BO);
                String boStr = "";
                if (Tools.isNotEmpty(boNode)) {
                    boStr = String.valueOf(boNode);
                }
                String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
                //根据CODE判断接口调用是否成功
                if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
                    String retMsg = json.get(JSON_CODE).get(JSON_MSG).asText();
                    ecssFail(fsBatchMaterialDTO, retCode + ":" + retMsg + ":" + boStr);
                } else {
                    ecssSuccess(fsBatchMaterialDTO);
                }
            }
        } catch (Exception e) {
            ecssFail(fsBatchMaterialDTO, Arrays.stream(e.getStackTrace()).collect(Collectors.toList()).toString());
        }
    }

    /**
     * ECSS注册接口调用失败方法
     */
    public void ecssFail(FsBatchMaterialDTO fsBatchMaterialDTO, String failReason) {
        List<FsMaterialDTO> fsMaterialDTOsList = fsBatchMaterialDTO.getFsMaterialDTOs();

        List<String> itemCodeListAll = fsMaterialDTOsList.stream()
                .map(s -> String.valueOf(s.getRecordId())).distinct()
                .collect(Collectors.toList());
        List<EcssItemDTO> itemCodeMailList = fsMaterialDTOsList.stream()
                .filter(e -> e.getFailNum() >= INT_3)
                .map(item -> {
                    EcssItemDTO ecssItemDTO = new EcssItemDTO();
                    ecssItemDTO.setRecordId(item.getRecordId());
                    ecssItemDTO.setItemCode(item.getRefNo());
                    ecssItemDTO.setInGuid(item.getInGuid());
                    return ecssItemDTO;
                })
                .collect(Collectors.toList());
        updateFailItems(itemCodeListAll, failReason, itemCodeMailList);
    }

    /**
     * ECSS注册接口调用成功方法
     */
    public void ecssSuccess(FsBatchMaterialDTO fsBatchMaterialDTO) {
        FsPageDTO fsPageDTO = fsBatchMaterialDTO.getFsPageDTO();
        String instanceId = fsPageDTO.getInstanceId();
        List<FsMaterialDTO> fsMaterialDTOsList = fsBatchMaterialDTO.getFsMaterialDTOs();

        List<String> itemCodeTrueList = fsMaterialDTOsList.stream()
                .filter(e -> e.getFsGeneInfoDTO().getIndFunc())
                .map(s -> String.valueOf(s.getRecordId())).distinct()
                .collect(Collectors.toList());
        List<String> itemCodeFalseList = fsMaterialDTOsList.stream()
                .filter(e -> !e.getFsGeneInfoDTO().getIndFunc())
                .map(s -> String.valueOf(s.getRecordId())).distinct()
                .collect(Collectors.toList());

        updateSuccessList(itemCodeTrueList, STR_TRUE, instanceId);
        updateSuccessList(itemCodeFalseList, STR_FALSE, instanceId);
    }

    /**
     * 更新ECSS调用成功状态
     */
    public void updateSuccessList(List<String> itemCodeListAll, String indFunc, String instanceId) {
        //200个主键每次更新成功相关参数
        List<List<String>> itemCodeLists = CommonUtils.splitList(itemCodeListAll, INT_200);
        for (List<String> itemCodeList : itemCodeLists) {
            this.omsEcssItemRepository.updateSuccessList(ECSS_ITEM_ADDING, indFunc, instanceId, itemCodeList);
        }
    }

    @Override
    public void registerBack(FsAsyncBatchDTO fsAsyncBatchDTO) throws Exception {
        FsPageDTO fsPageDTO = fsAsyncBatchDTO.getFsPageDTO();
        BusiAssertException.isEmpty(fsPageDTO, MessageId.NO_DATA_FOUND);
        String instanceId = fsPageDTO.getBatchId();
        String status = fsAsyncBatchDTO.getStatus();
        List<String> itemCodeListError = new ArrayList<>();
        //对注册失败的物料找出来批量更新
        if (status.equals(E)) {
            StringBuilder failReason = new StringBuilder();
            FsAsyncBatchProInfoListDTO fsAsyncBatchProInfoListDTO = fsAsyncBatchDTO.getProInfos();
            List<EcssItemDTO> itemCodeMailList = new ArrayList<>();
            List<FsAsyncBatchProInfoDTO> fsAsyncBatchProInfoDTOList = fsAsyncBatchProInfoListDTO.getFailedList();
            for (FsAsyncBatchProInfoDTO fsAsyncBatchProInfoDTO : fsAsyncBatchProInfoDTOList) {
                String itemCodeV = fsAsyncBatchProInfoDTO.getRefNo();
                EcssItemDTO ecssItemDTO = EcssItemDTO.builder()
                        .instanceId(instanceId).itemCode(itemCodeV.substring(INT_0, INT_12))
                        .itemVersion(itemCodeV.length() > INT_12 ? itemCodeV.substring(INT_12) : "").build();
                List<EcssItemDTO> ecssItemDTOList = this.omsEcssItemRepository.getItemList(ecssItemDTO);
                //对注册失败超过3次以上物料发送邮件
                itemCodeMailList.addAll(ecssItemDTOList.stream().filter(e -> e.getFailNum() >= INT_3).collect(Collectors.toList()));
                itemCodeListError.addAll(ecssItemDTOList.stream().map(e -> String.valueOf(e.getRecordId())).collect(Collectors.toList()));
                List<String> details = fsAsyncBatchProInfoDTO.getDetails();
                failReason.append(StringUtils.join(details, ";"));
            }
            failReason = new StringBuilder(cutFailReason(failReason.toString()));
            updateFailItems(itemCodeListError, failReason.toString(), itemCodeMailList);
        }

        //对注册成功的物料找出来批量更新
        this.omsEcssItemRepository.updateRegisterList(ECSS_ITEM_ADDED, instanceId, itemCodeListError);
    }

    /**
     * 失败原因截取防止超限
     */
    public String cutFailReason(String failReason) {
        if (failReason.length() > NumConstant.INT_2000) {
            failReason = failReason.substring(INT_0, NumConstant.INT_2000);
        }
        return failReason;
    }
}
