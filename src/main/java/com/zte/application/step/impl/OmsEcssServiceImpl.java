package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.step.OmsEcssService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.security.ZteSecurity;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.utils.CommonUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class OmsEcssServiceImpl implements OmsEcssService {

	@Autowired
	private OmsEcssRepository omsEcssRepository;
	@Autowired
	private EmailUtil emailUtil;
	@Value("${ecss.url}")
	private String ecssUrl;
	@Autowired
	ZteSecurity zteSecurity;

	@Override
	public void pushBillToEcss(EcssBillDTO dto) {

		// JOB，2分钟/次，获取满足条件的ECSSBill单据号
		List<EcssBillDTO> ecssBillList = omsEcssRepository.getAddingBillNo(dto);
		if (CommonUtils.isEmpty(ecssBillList)) {
			return;
		}

		// 循环处理
		for (EcssBillDTO ecssBillDTO : ecssBillList) {
			// 组装数据
			FsDocumentDTO fsDocumentDTO = getFsDocumentDTO(ecssBillDTO.getReferenceNumber());

			// 调用ECSS接口
			ServiceData<?> res = invokeBillToEcss(fsDocumentDTO, dto.getLastUpdatedBy(), ecssBillDTO.getSaleOrderOrg());

			// 处理ECSSBill信息
			dealEcssBill(ecssBillDTO, fsDocumentDTO, res, dto.getLastUpdatedBy());
		}

	}

	/**
	 * 组装数据
	 * @param billNo
	 * @return
	 */
	public FsDocumentDTO getFsDocumentDTO(String billNo) {

		FsDocumentDTO fsDocumentDTO = new FsDocumentDTO();
		fsDocumentDTO.setReferenceNumber(billNo);
		// ECSS单据编码
		fsDocumentDTO.setCode(getCode(billNo));
		// 销售领料单
		if (CommonUtils.equals(STING_C, billNo.substring(NumConstant.INT_0, NumConstant.INT_1))) {
			fsDocumentDTO.setName(BILL_NAME_C);
			fsDocumentDTO.setDocumentTypeCode(BILL_TYPE_CODE_C);
		}
		// 备料售卖领料单
		if (CommonUtils.equals(STING_ND, billNo.substring(NumConstant.INT_0, NumConstant.INT_2))) {
			fsDocumentDTO.setName(BILL_NAME_ND);
			fsDocumentDTO.setDocumentTypeCode(BILL_TYPE_CODE_ND);
		}

		// 路径
		List<FsCountryDTO> fsCountryDTOList = new ArrayList<>();
		FsCountryDTO fsCountryDTO = FsCountryDTO.builder().build().setCountryISOTwoDigitCode(COUNTRY_DIGIT_CODE);
		fsCountryDTOList.add(fsCountryDTO);
		FsCountryDTO fsCountryDTO1 = FsCountryDTO.builder().build().setCountryISOTwoDigitCode(COUNTRY_DIGIT_CODE);
		fsCountryDTOList.add(fsCountryDTO1);
		fsDocumentDTO.setRoutes(fsCountryDTOList);

		List<EcssBillDTO> materialInfoList = omsEcssRepository.getMaterialInfo(billNo);
		if (CommonUtils.isEmpty(materialInfoList)) {
			return fsDocumentDTO;
		}

		// 合作伙伴
		List<FsDocumentBusinessPartnerDTO> fsDocumentBusinessPartnerDTOList = new ArrayList<>();
		FsDocumentBusinessPartnerDTO fsDocumentBusinessPartnerDTO = FsDocumentBusinessPartnerDTO.
				builder().build().setMasterDataReferenceNumber(materialInfoList.get(NumConstant.INT_0).getCustomerNo())
				.setBusinessPartnerFunctionCode(BUSINESS_PARTNER_FUNCTION_CODE)
				.setBusinessPartnerTypeCode(BUSINESS_PARTNER_TYPE_CODE);
		fsDocumentBusinessPartnerDTOList.add(fsDocumentBusinessPartnerDTO);
		fsDocumentDTO.setFsDocumentBusinessPartnerDTOs(fsDocumentBusinessPartnerDTOList);

		// 物料
		List<FsDocumentMaterialDTO> fsDocumentMaterialDTOList = new ArrayList<>();
		for (EcssBillDTO ecssBillDTO : materialInfoList) {
			FsDocumentMaterialDTO fsDocumentMaterialDTO = new FsDocumentMaterialDTO();
			BeanUtils.copyProperties(ecssBillDTO, fsDocumentMaterialDTO);
			fsDocumentMaterialDTOList.add(fsDocumentMaterialDTO);
		}
		fsDocumentDTO.setFsDocumentMaterialDTOs(fsDocumentMaterialDTOList);

		return fsDocumentDTO;
	}

	public String getCode(String billNo) {

		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss"); // 定义日期格式
		String formattedDate = now.format(formatter);
		return billNo + ONE_SPLIT + formattedDate;

	}

	/**
	 * 调用ECSS推送单据的接口
	 * @param fsDocumentDTO
	 * @return
	 */
	public ServiceData<?> invokeBillToEcss(FsDocumentDTO fsDocumentDTO, String xEmpNo, Integer saleOrderOrg) {

		Map<String, String> header = getEcssHeaderMap(saleOrderOrg);
		header.put(X_EMP_NO, xEmpNo);
		header.put(X_LANG_ID, X_LANG_ID_ZH);
		header.put(CONTENT_TYPE, APPLICATION_JSON);
		String url = ecssUrl + ECSS_BILL_ASYNC;
		String result = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(fsDocumentDTO), header);
		return (ServiceData) JSON.parseObject(result, ServiceData.class);
	}

	/**
	 * 根据销售订单的组织，获取OMS售卖单据对接ECSS的密钥和token
	 */
	public Map<String, String> getEcssHeaderMap(Integer saleOrderOrg) {
		Map<String, String> header = new HashMap<>(INT_16);
		StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000041);
		List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
		if (Tools.isEmpty(stepSysLookupValuesDTOList)) {
			return header;
		}
		// 获取ECSS的密钥和token
		StepSysLookupValuesDTO ecssSysLookupValuesDTO = stepSysLookupValuesDTOList.stream().filter(i ->
                i.getSortSeq().compareTo(saleOrderOrg) == NumConstant.INT_0).findFirst().orElse(null);
		if (Tools.isNotEmpty(ecssSysLookupValuesDTO)) {
			header.put(X_AUTH_VALUE, zteSecurity.decrypt(ecssSysLookupValuesDTO.getLookupMeaning()));
			header.put(X_FEEDER_SYSTEM_TOKEN, zteSecurity.decrypt(ecssSysLookupValuesDTO.getDescription()));
		}
		return header;
	}

	/**
	 * 根据ECSS返回消息处理
	 * @param dto
	 * @param fsDocumentDTO
	 * @param res
	 * @param xEmpNo
	 */
	public void dealEcssBill(EcssBillDTO dto, FsDocumentDTO fsDocumentDTO, ServiceData<?> res, String xEmpNo) {

		EcssBillDTO ecssBillDTO = new EcssBillDTO();
		if (CommonUtils.equals(FLAG_N, dto.getEnabledFlag())) {
			BeanUtils.copyProperties(fsDocumentDTO, ecssBillDTO);
		}
		ecssBillDTO.setSendParam(StringUtils.substring(JSONObject.toJSONString(fsDocumentDTO), NumConstant.INT_0, NumConstant.INT_2000));
		ecssBillDTO.setReferenceNumber(dto.getReferenceNumber());
		ecssBillDTO.setBillStatus(ECSS_BILL_ADDING);
		ecssBillDTO.setCreatedBy(xEmpNo);
		ecssBillDTO.setLastUpdatedBy(xEmpNo);
		ecssBillDTO.setSaleOrderOrg(dto.getSaleOrderOrg());
		if (Tools.isNotEmpty(res) && CommonUtils.equals(SUCESS_CODE, res.getCode().getCode())) {
			// 如果成功，更新标识为成功
			ecssBillDTO.setInvokeFlag(NumConstant.INT_1);
			ecssBillDTO.setFailNum(dto.getFailNum());
		} else {
			// 如果失败，更新失败次数和失败原因
			ecssBillDTO.setInvokeFlag(NumConstant.INT_0);
			ecssBillDTO.setFailNum(dto.getFailNum() + NumConstant.INT_1);
			ecssBillDTO.setFailReason(Tools.isEmpty(res)?null:res.getCode().getMsg());
		}
		// 新增或更新单据信息
		omsEcssRepository.insertOrUpdateEcssBill(ecssBillDTO);
		// 如果失败次数达到4次，则发邮件通知相关人员
		if (ecssBillDTO.getFailNum() == INT_4) {
			sendFailMail(ecssBillDTO.getReferenceNumber());
		}
	}

	/**
	 * 失败次数达到4次，则发邮件通知相关人员
	 */
	public void sendFailMail(String referenceNumber) {

		StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build()
				.setLookupType(LOOKUP_TYPE_1000022).setDescription(ECSS_BILL_FAILED);
		List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
		if (CommonUtils.isEmpty(stepSysLookupValuesDTOList)) {
			return;
		}
		List<String> users = stepSysLookupValuesDTOList.stream().map(StepSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());

		//发送邮件
		String content = ECSS_SALE_BILL_FAILED_1 + "\r\n" + referenceNumber + "\r\n" + ECSS_SALE_BILL_FAILED_2;
		String receipts = StringUtils.join(
				users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
		emailUtil.sendMail(receipts, ECSS_SALE_BILL_FAILED_WARN, BLANK, content, BLANK);
	}

	/**
	 * ECSS回调接口
	 * @param dto
	 */
	@Override
	public void callBackEcssToOms(DocumentCallbackResultDTO dto) {

		BusiAssertException.isEmpty(dto, MessageId.CALL_BACK_DATA_EMPTY);
		BusiAssertException.isEmpty(dto.getRefNo(), MessageId.BILL_NO_EMPTY);

		// 获取ECSS实施阶段配置
		StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build().setLookupCode(LOOKUP_CODE_100002000001);
		List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
		BusiAssertException.isEmpty(stepSysLookupValuesDTOList, MessageId.OMS_SALE_ECSS_LOOKUP_NOT_EXISTS);
		int lookupMeaning = Integer.parseInt(stepSysLookupValuesDTOList.get(NumConstant.INT_0).getLookupMeaning());

		// 如果ECSS实施阶段为2，则ECSS的结果用于展示
		if (CommonUtils.equals(NumConstant.INT_2, lookupMeaning)) {
			updateEcssDTO(dto, null, lookupMeaning);
		} else {
			// 获取GTS扫描结果
			List<EcssBillDTO> materialInfoList = omsEcssRepository.getMaterialInfo(dto.getRefNo());
			BusiAssertException.isEmpty(materialInfoList, MessageId.OMS_SALE_BILL_NOT_EXISTS);
			// 如果单据状态是AUDITING “已审核”，表示单据已到INFOR，则不再处理
			if (CommonUtils.equals(AUDITING, materialInfoList.get(NumConstant.INT_0).getStatus())) {
				return;
			}
			String gtsResult = materialInfoList.get(NumConstant.INT_0).getGtsResult();
			updateEcssDTO(dto, gtsResult, lookupMeaning);
		}
	}

	/**
	 * 处理业务数据
	 * @param dto
	 * @param gtsResult
	 * @param lookupMeaning
	 */
	public void updateEcssDTO(DocumentCallbackResultDTO dto, String gtsResult, int lookupMeaning) {

		EcssBillDTO ecssBillDTO = new EcssBillDTO();
		ecssBillDTO.setReferenceNumber(dto.getRefNo());
		ecssBillDTO.setReturnParam(StringUtils.substring(JSONObject.toJSONString(dto), NumConstant.INT_0, NumConstant.INT_2000));
		ecssBillDTO.setBillStatus(ECSS_BILL_ADDED);
		ecssBillDTO.setLastUpdatedBy(dto.getLastUpdatedBy());

		// 判断ECSS的扫描结果和单据的扫描结果
		getEcssBillDTO(ecssBillDTO, dto, gtsResult, lookupMeaning);

		// 更新ecssBill表
		omsEcssRepository.updateEcssBill(ecssBillDTO);

		// 更新infor_omsales_bill表
		omsEcssRepository.updateInforOmSalesBill(ecssBillDTO);

		// 失效和新增ecss_outputcollection表信息
		EcssOutPutCollectionDTO ecssOutPutCollectionDTO = new EcssOutPutCollectionDTO();
		ecssOutPutCollectionDTO.setReferenceNumber(dto.getRefNo());
		BeanUtils.copyProperties(dto, ecssOutPutCollectionDTO);
		omsEcssRepository.failEcssOutputcollection(ecssOutPutCollectionDTO);
		omsEcssRepository.insertEcssOutputcollection(ecssOutPutCollectionDTO);

		// 失效和新增ecss_textretruncollection表信息
		omsEcssRepository.failEcssTextretruncollection(ecssOutPutCollectionDTO);
		List<EcssTextRetrunCollectionDTO> ecssTextRetrunCollectionDTOList = new ArrayList<>();
		if (CommonUtils.isNotEmpty(dto.getFsFReasonDTOs())) {
			for (FsfReasonDTO fsfReasonDTO : dto.getFsFReasonDTOs()) {
				EcssTextRetrunCollectionDTO ecssTextRetrunCollectionDTO = EcssTextRetrunCollectionDTO.builder().build()
						.setReferenceNumber(dto.getRefNo()).setCreatedBy(dto.getCreatedBy()).setItemNo(fsfReasonDTO.getRefNo())
						.setLaw(fsfReasonDTO.getLaw()).setFReason(fsfReasonDTO.getFReason()).setSType(fsfReasonDTO.getSType());
				ecssTextRetrunCollectionDTOList.add(ecssTextRetrunCollectionDTO);
			}
		}
		if (CommonUtils.isNotEmpty(dto.getFMessages())) {
			for (String str : dto.getFMessages()) {
				EcssTextRetrunCollectionDTO ecssTextRetrunCollectionDTO = EcssTextRetrunCollectionDTO.builder().build()
						.setReferenceNumber(dto.getRefNo()).setCreatedBy(dto.getCreatedBy()).setFMessages(str);
				ecssTextRetrunCollectionDTOList.add(ecssTextRetrunCollectionDTO);
			}
		}
		if (CommonUtils.isEmpty(ecssTextRetrunCollectionDTOList)) {
			return;
		}
		for (List<EcssTextRetrunCollectionDTO> tempList : CommonUtils.splitList(ecssTextRetrunCollectionDTOList, NumConstant.INT_500)) {
			omsEcssRepository.insertEcssTextretruncollection(tempList);
		}

	}

	/**
	 * 判断ECSS的扫描结果和单据的扫描结果
	 * @param ecssBillDTO
	 * @param dto
	 * @return
	 */
	public void getEcssBillDTO(EcssBillDTO ecssBillDTO, DocumentCallbackResultDTO dto, String gtsResult, int lookupMeaning) {

		// ECSS扫描结果
		boolean ecssResult = CommonUtils.equals(S, dto.getCStatus()) && ECSS_RELEASED_STATUS.contains(dto.getStatus());
		if (ecssResult) {
			ecssBillDTO.setEcssResult(RELEASED_ZH);
		} else {
			ecssBillDTO.setEcssResult(NOT_RELEASED_ZH);
		}
		if (CommonUtils.equals(NumConstant.INT_3, lookupMeaning)) {
			if (ecssResult && CommonUtils.equals(RELEASED_ZH, gtsResult)) {
				ecssBillDTO.setStatus(GTSAUDITING);
			}

		}
		if (CommonUtils.equals(NumConstant.INT_4, lookupMeaning)) {
			if (ecssResult) {
				ecssBillDTO.setStatus(GTSAUDITING);
			}
		}
	}

}
