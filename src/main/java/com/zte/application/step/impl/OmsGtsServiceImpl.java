package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.step.OmsGtsService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.step.OmsEcssRepository;
import com.zte.domain.model.step.OmsGtsRepository;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_4;

/**
 * <AUTHOR>
 *
 */
@Service
public class OmsGtsServiceImpl implements OmsGtsService {
	private static final Logger log = LoggerFactory.getLogger(OmsGtsServiceImpl.class);
	@Autowired
	private OmsGtsRepository omsGtsRepository;
	@Autowired
	private OmsEcssRepository omsEcssRepository;
	@Autowired
	private EmailUtil emailUtil;
	@Value("${in.one.url}")
	private String inoneUrl;
	@Value("${in.one.wms.app.code}")
	private String inoneAppcode;

	/* Started by AICoder, pid:ffe59135067948849ee43e541ab2a25d */
	@Override
	public void pushBillToGts(GtsBillDTO dto) {
		// JOB，2分钟/次，获取满足条件的GTSBill单据号
		List<GtsBillDTO> gtsBillDTOList = omsGtsRepository.getAddingBillNo(dto);

		if (Tools.isEmpty(gtsBillDTOList)) {
			return;
		}

		// 循环处理
		for (GtsBillDTO gtsBillDTO : gtsBillDTOList) {
			// 组装数据
			GtsPushParamDTO gtsPushParamDTO = getGtsPushParamDTO(gtsBillDTO.getReferenceNumber());

			// 调用GTS接口
			GtsPushReturnDTO gtsPushReturnDTO = invokeBillToGts(gtsPushParamDTO);

			// 处理GTSBill信息
			dealGtsBill(gtsBillDTO, gtsPushParamDTO, gtsPushReturnDTO, dto.getLastUpdatedBy());
		}
	}
	/* Ended by AICoder, pid:ffe59135067948849ee43e541ab2a25d */

	/**
	 * 组装数据
	 */
	/* Started by AICoder, pid:b2d2e9b96bdb485db9be55364382bbdf */
	public GtsPushParamDTO getGtsPushParamDTO(String billNo) {

		// GTS推送单据DTO
		GtsPushParamDTO gtsPushParamDTO = new GtsPushParamDTO();
		String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern(STR_YYYY_MM_DD_HH_MM_SS));
		// GTS服务消息头DTO
		GtsIsMsgHeaderDTO isMsgHeader = new GtsIsMsgHeaderDTO();
		isMsgHeader.setSourceSystemId(STR_OMS);
		isMsgHeader.setSourceSystemName(STR_OMS);
		isMsgHeader.setInstanceId(STR_OMS+TWO_SPLIT+billNo+TWO_SPLIT+time);
		isMsgHeader.setSubmitDate(time);
		gtsPushParamDTO.setIsMsgHeader(isMsgHeader);

		// 查询销售领料单的信息
		List<GtsBillDTO> gtsBillDTOList = omsGtsRepository.getOmsSalesInfo(billNo);
		BusiAssertException.isEmpty(gtsBillDTOList, MessageId.OMS_SALE_BILL_NOT_EXISTS);

		// GTS服务单据头DTO
		GtsIsDocHeaderDTO isDocHeader = new GtsIsDocHeaderDTO();
		isDocHeader.setLogSystem(STR_CNKX);
		isDocHeader.setBukrs(STR_CNKX);
		isDocHeader.setDocType(STR_ZDO);
		isDocHeader.setDocNo(billNo);
		isDocHeader.setAddData3(gtsBillDTOList.get(NumConstant.INT_0).getSalesContractNo());

		// GTS单据抬头合作伙伴DTO
		List<GtsHeaderParDTO> headerPar = new ArrayList<>();
		GtsHeaderParDTO gtsHeaderParDTO1 = new GtsHeaderParDTO();
		gtsHeaderParDTO1.setParFunc(STR_AG);
		gtsHeaderParDTO1.setParNo(STR_CNKX + TWO_SPLIT + gtsBillDTOList.get(NumConstant.INT_0).getCustomerNo());
		gtsHeaderParDTO1.setBptyp(NumConstant.STR_02);
		headerPar.add(gtsHeaderParDTO1);
		GtsHeaderParDTO gtsHeaderParDTO2 = new GtsHeaderParDTO();
		gtsHeaderParDTO2.setParFunc(STR_WE);
		gtsHeaderParDTO2.setParNo(STR_CNKX + TWO_SPLIT + gtsBillDTOList.get(NumConstant.INT_0).getCustomerNo());
		gtsHeaderParDTO2.setBptyp(NumConstant.STR_02);
		headerPar.add(gtsHeaderParDTO2);
		isDocHeader.setHeaderPar(headerPar);
		gtsPushParamDTO.setIsDocHeader(isDocHeader);

		// GTS服务数据实体DTO
		List<GtsItDocItemDTO> itDocItem = new ArrayList<>();
		for (GtsBillDTO gtsBillDTO : gtsBillDTOList) {
			GtsItDocItemDTO gtsItDocItemDTO = new GtsItDocItemDTO();
			gtsItDocItemDTO.setWerks(STR_KX01);
			gtsItDocItemDTO.setItemNo(gtsBillDTO.getDetailId());
			gtsItDocItemDTO.setItemCat(STR_TAN);
			gtsItDocItemDTO.setProdNo(gtsBillDTO.getItemNo());
			// 数量，3位小数
			gtsItDocItemDTO.setQuantity(gtsBillDTO.getQty().setScale(NumConstant.INT_3, RoundingMode.HALF_UP));
			gtsItDocItemDTO.setUom(STR_PCE);
			// 价格，2位小数
			gtsItDocItemDTO.setProdVal(getProdVal(gtsBillDTO));
			gtsItDocItemDTO.setCurrency(STR_CNY);
			itDocItem.add(gtsItDocItemDTO);
		}
		gtsPushParamDTO.setItDocItem(itDocItem);

		return gtsPushParamDTO;
	}
	/* Ended by AICoder, pid:b2d2e9b96bdb485db9be55364382bbdf */

	/**
	 * 计算价格
	 */
	/* Started by AICoder, pid:206098198b5440d08489359a8b5758c9 */
	public BigDecimal getProdVal(GtsBillDTO gtsBillDTO) {

		BigDecimal prodVal = null;
		BigDecimal salePrice = gtsBillDTO.getSalePrice();
		if (Tools.equals(NumConstant.INT_0, gtsBillDTO.getSalesType())) {
			// 材料
			prodVal = gtsBillDTO.getQty().multiply(salePrice);
		} else {
			// 单板
			// erp价格 与 oms价格比较，取两者最大值
			BigDecimal erpPrice = getErpPrice(gtsBillDTO.getItemNo());
			if (erpPrice.compareTo(salePrice) > NumConstant.INT_0) {
				salePrice = erpPrice;
			}
			prodVal = gtsBillDTO.getQty().multiply(salePrice);
		}
		if (prodVal.compareTo(NumConstant.BIG_001) < NumConstant.INT_0) {
			prodVal = NumConstant.BIG_001;
		}
		return prodVal.setScale(NumConstant.INT_2, RoundingMode.HALF_UP);
	}
	/* Ended by AICoder, pid:206098198b5440d08489359a8b5758c9 */

	/**
	 * 调用计划接口
	 */
	/* Started by AICoder, pid:44b4df0b686849e58ec03ebbb6df1f33 */
	public BigDecimal getErpPrice(String itemNo) {
		log.info("############ getErpPrice: {}" , itemNo);

		Map<String, String> headerParamsMap = new HashMap<>(NumConstant.INT_16);
		headerParamsMap.put(X_EMP_NO, STR_OMS);
		headerParamsMap.put(INONE_APPCODE, inoneAppcode);
		headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
		headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);

		ErpSearchInfoDTO erpSearchInfoDTO = new ErpSearchInfoDTO();
		erpSearchInfoDTO.setApiCode(ERP_API_CODE_GET_ITEM_COST);
		erpSearchInfoDTO.setInvokeKey(NumConstant.STR_13);
		erpSearchInfoDTO.setProductType(ERP_PRODUCT_TYPE_EPMS);
		erpSearchInfoDTO.setServiceCode(ERP_SERVICE_CODE_EPMS_MANAGE);
		Map<String, String> configParamMap = new HashMap<>(NumConstant.INT_16);
		configParamMap.put(STR_ORGANIZATION_ID, NumConstant.STR_395);
		configParamMap.put(ITEM_CODE, itemNo);
		erpSearchInfoDTO.setConfigParamMap(configParamMap);

		List<ErpItemCostDTO> erpItemCostDTOList = new ArrayList<>();
		try {
			String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + ERP_INONE_PUBLIC_QUERY_DATA,
					JacksonJsonConverUtil.beanToJson(erpSearchInfoDTO), headerParamsMap);
			JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
			if (null == json) {
				return BigDecimal.ZERO;
			}
			String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
			if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
				return BigDecimal.ZERO;
			}
			String jsonStr = json.get(JSON_BO).toString();
			erpItemCostDTOList = JacksonJsonConverUtil.jsonToListBean(jsonStr, new TypeReference<List<ErpItemCostDTO>>(){});
		} catch (Exception e) {
			log.error("getErpPrice exception: ", e);
		}

		if (Tools.isEmpty(erpItemCostDTOList)) {
			return BigDecimal.ZERO;
		}
		return erpItemCostDTOList.get(NumConstant.INT_0).getAvecost();
	}
	/* Ended by AICoder, pid:44b4df0b686849e58ec03ebbb6df1f33 */

	/**
	 * 调用GTS推送单据的接口
	 */
	/* Started by AICoder, pid:dfa6aa7df3394522b08d7e8bb7f692df */
	public GtsPushReturnDTO invokeBillToGts(GtsPushParamDTO gtsPushParamDTO) {
		log.info("############ invokeBillToGts: {}" , gtsPushParamDTO);

		GtsPushReturnDTO gtsPushReturnDTO = new GtsPushReturnDTO();
		try {
			String url = null;
			StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build().setLookupCode(LOOKUP_CODE_100003300001);
			List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
			if (Tools.isNotEmpty(stepSysLookupValuesDTOList)) {
				url = stepSysLookupValuesDTOList.get(NumConstant.INT_0).getLookupMeaning();
			}
			String result = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(gtsPushParamDTO), null);
			ObjectMapper objectMapper = new ObjectMapper();
			gtsPushReturnDTO = objectMapper.readValue(result, GtsPushReturnDTO.class);
		} catch (Exception e) {
			log.error("invokeBillToGts exception:", e);
		}
		return gtsPushReturnDTO;
	}
	/* Ended by AICoder, pid:dfa6aa7df3394522b08d7e8bb7f692df */

	/**
	 * 根据GTS返回消息处理
	 */
	/* Started by AICoder, pid:cbc4d785be6a438f8c4dff6ad7a7dba8 */
	public void dealGtsBill(GtsBillDTO dto, GtsPushParamDTO gtsPushParamDTO, GtsPushReturnDTO gtsPushReturnDTO, String xEmpNo) {

		GtsBillDTO gtsBillDTO = new GtsBillDTO();
		gtsBillDTO.setCode(gtsPushParamDTO.getIsMsgHeader().getInstanceId());
		// 销售领料单
		if (Tools.equals(STING_C, dto.getReferenceNumber().substring(NumConstant.INT_0, NumConstant.INT_1))) {
			gtsBillDTO.setName(BILL_NAME_C);
		}
		// 备料售卖领料单
		if (Tools.equals(STING_ND, dto.getReferenceNumber().substring(NumConstant.INT_0, NumConstant.INT_2))) {
			gtsBillDTO.setName(BILL_NAME_ND);
		}
		gtsBillDTO.setSendParam(StringUtils.substring(JSON.toJSONString(gtsPushParamDTO), NumConstant.INT_0, NumConstant.INT_2000));
		gtsBillDTO.setReferenceNumber(dto.getReferenceNumber());
		gtsBillDTO.setBillStatus(GTS_BILL_ADDING);
		gtsBillDTO.setCreatedBy(xEmpNo);
		gtsBillDTO.setLastUpdatedBy(xEmpNo);
		if (Tools.isNotEmpty(gtsPushReturnDTO) && Tools.equals(S, gtsPushReturnDTO.getProcessStatus())) {
			// 如果成功，更新标识为成功
			gtsBillDTO.setInvokeFlag(NumConstant.INT_1);
			gtsBillDTO.setFailNum(dto.getFailNum());
		} else {
			// 如果失败，更新失败次数和失败原因
			gtsBillDTO.setInvokeFlag(NumConstant.INT_0);
			gtsBillDTO.setFailNum(dto.getFailNum() + NumConstant.INT_1);
			gtsBillDTO.setFailReason(StringUtils.substring(JSON.toJSONString(gtsPushReturnDTO), NumConstant.INT_0, NumConstant.INT_2000));
		}
		// 新增或更新单据信息
		omsGtsRepository.insertOrUpdateGtsBill(gtsBillDTO);
		// 如果失败次数达到4次，则发邮件通知相关人员
		if (gtsBillDTO.getFailNum() == INT_4) {
			sendFailMail(gtsBillDTO.getReferenceNumber());
		}
	}
	/* Ended by AICoder, pid:cbc4d785be6a438f8c4dff6ad7a7dba8 */

	/**
	 * 失败次数达到4次，则发邮件通知相关人员
	 */
	/* Started by AICoder, pid:4fa150cb8eb0459ab4e42d5619dc3d44 */
	public void sendFailMail(String referenceNumber) {

		StepSysLookupValuesDTO stepSysLookupValuesDTO = StepSysLookupValuesDTO.builder().build()
				.setLookupType(LOOKUP_TYPE_1000022).setDescription(GTS_BILL_FAILED);
		List<StepSysLookupValuesDTO> stepSysLookupValuesDTOList = omsEcssRepository.getEcssLookupInfo(stepSysLookupValuesDTO);
		if (Tools.isEmpty(stepSysLookupValuesDTOList)) {
			return;
		}
		List<String> users = stepSysLookupValuesDTOList.stream().map(StepSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());

		//发送邮件
		String content = ECSS_SALE_BILL_FAILED_1 + "\r\n" + referenceNumber + "\r\n" + GTS_SALE_BILL_FAILED_2;
		String receipts = StringUtils.join(
				users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
		emailUtil.sendMail(receipts, GTS_SALE_BILL_FAILED_WARN, BLANK, content, BLANK);
	}
	/* Ended by AICoder, pid:4fa150cb8eb0459ab4e42d5619dc3d44 */

	/**
	 * ECSS回调接口
	 */
	/* Started by AICoder, pid:7b9eed09e9064f649b35037d222a7ad7 */
	@Override
	public GtsBackReturnDTO callBackGtsToOms(GtsBackParamDTO dto) {

		GtsBackReturnDTO gtsBackReturnDTO = new GtsBackReturnDTO();
		try {
			// 校验GTS回调的参数
			gtsBackReturnDTO.setSourceSystemId(STR_OMS);
			gtsBackReturnDTO.setLogSystem(STR_CNKX);
			gtsBackReturnDTO.setDocType(STR_ZDO);
			if (Tools.isEmpty(dto) || Tools.isEmpty(dto.getIsMsgHeader()) || Tools.isEmpty(dto.getItResult())) {
				gtsBackReturnDTO.setMsgTy(E);
				gtsBackReturnDTO.setMessage(CommonUtils.getLmbMessage(MessageId.CALL_BACK_DATA_EMPTY));
				return gtsBackReturnDTO;
			}
			gtsBackReturnDTO.setInstanceId(dto.getIsMsgHeader().getInstanceId());
			String docNo = dto.getIsMsgHeader().getDocNo();
			if (Tools.isEmpty(docNo)) {
				gtsBackReturnDTO.setMsgTy(E);
				gtsBackReturnDTO.setMessage(CommonUtils.getLmbMessage(MessageId.BILL_NO_EMPTY));
				return gtsBackReturnDTO;
			}
			gtsBackReturnDTO.setMsgTy(S);
			gtsBackReturnDTO.setMessage(TRANSFER_SUCCESS);
			gtsBackReturnDTO.setDocNo(docNo);

			// 获取ECSS扫描结果
			List<GtsBillDTO> gtsBillDTOList = omsGtsRepository.getOmsSalesInfo(docNo);
			if (Tools.isEmpty(gtsBillDTOList)) {
				gtsBackReturnDTO.setMsgTy(E);
				gtsBackReturnDTO.setMessage(CommonUtils.getLmbMessage(MessageId.OMS_SALE_BILL_NOT_EXISTS));
				return gtsBackReturnDTO;
			}
			// 如果单据状态是AUDITING “已审核”，表示单据已到INFOR，则不再处理
			if (Tools.equals(AUDITING, gtsBillDTOList.get(NumConstant.INT_0).getStatus())) {
				return gtsBackReturnDTO;
			}
			String ecssResult = gtsBillDTOList.get(NumConstant.INT_0).getEcssResult();

			// 处理业务数据
			updateGtsDTO(dto, ecssResult);
		} catch (Exception e) {
			gtsBackReturnDTO.setMsgTy(E);
			gtsBackReturnDTO.setMessage(StringUtils.substring(e.toString(), NumConstant.INT_0, NumConstant.BATCH_SIZE));
			return gtsBackReturnDTO;
		}
		return gtsBackReturnDTO;
	}
	/* Ended by AICoder, pid:7b9eed09e9064f649b35037d222a7ad7 */

	/**
	 * 处理业务数据
	 */
	/* Started by AICoder, pid:449a1439e89a492a8039d8f245f63bd2 */
	public void updateGtsDTO(GtsBackParamDTO dto, String ecssResult) {

		GtsBillDTO gtsBillDTO = new GtsBillDTO();
		gtsBillDTO.setReferenceNumber(dto.getIsMsgHeader().getDocNo());
		gtsBillDTO.setReturnParam(StringUtils.substring(JSON.toJSONString(dto), NumConstant.INT_0, NumConstant.INT_2000));
		gtsBillDTO.setBillStatus(GTS_BILL_ADDED);
		gtsBillDTO.setLastUpdatedBy(STR_OMS);

		// 判断GTS的扫描结果和单据的扫描结果
		getGtsBillDTO(gtsBillDTO, dto.getItResult(), ecssResult);

		// 更新gtsBill表
		omsGtsRepository.updateGtsBill(gtsBillDTO);

		// 更新infor_omsales_bill表
		omsGtsRepository.updateInforOmSalesBill(gtsBillDTO);

		// 失效和新增gts_outputcollection表信息
		updateGtsOutputcollection(gtsBillDTO, dto.getItResult());

		// 失效和新增gts_textretruncollection表信息
		updateGtsTextretruncollection(gtsBillDTO, dto.getItReturn());
	}
	/* Ended by AICoder, pid:449a1439e89a492a8039d8f245f63bd2 */

	/**
	 * 处理gts_outputcollection表
	 */
	/* Started by AICoder, pid:c9a07e0e599c4e1ca970014587df565c */
	public void updateGtsOutputcollection(GtsBillDTO gtsBillDTO, List<GtsItResultDTO> itResult) {

		if (Tools.isEmpty(itResult)) {
			return;
		}
		omsGtsRepository.failGtsOutputcollection(gtsBillDTO);
		List<GtsOutPutCollectionDTO> gtsOutPutCollectionDTOList = new ArrayList<>();
		for(GtsItResultDTO gtsItResultDTO : itResult) {
			GtsOutPutCollectionDTO gtsOutPutCollectionDTO = new GtsOutPutCollectionDTO();
			BeanUtils.copyProperties(gtsItResultDTO, gtsOutPutCollectionDTO);
			gtsOutPutCollectionDTO.setKeyId(gtsBillDTO.getReferenceNumber());
			gtsOutPutCollectionDTO.setGUuid(gtsItResultDTO.getGuidHeader());
			gtsOutPutCollectionDTOList.add(gtsOutPutCollectionDTO);
		}
		for (List<GtsOutPutCollectionDTO> tempList : CommonUtils.splitList(gtsOutPutCollectionDTOList, NumConstant.INT_500)) {
			omsGtsRepository.insertGtsOutputcollection(tempList);
		}
	}
	/* Ended by AICoder, pid:c9a07e0e599c4e1ca970014587df565c */

	/**
	 * 处理gts_textretruncollection表
	 */
	/* Started by AICoder, pid:844d598791024262b7ec2b7e44a53df5 */
	public void updateGtsTextretruncollection(GtsBillDTO gtsBillDTO, List<GtsItReturnDTO> itReturn) {

		omsGtsRepository.failGtsTextretruncollection(gtsBillDTO);
		if (Tools.isEmpty(itReturn)) {
			return;
		}
		List<GtsTextRetrunCollectionDTO> gtsTextRetrunCollectionDTOList = new ArrayList<>();
		for (GtsItReturnDTO gtsItReturnDTO : itReturn) {
			if (Tools.notEquals(S, gtsItReturnDTO.getType())) {
				GtsTextRetrunCollectionDTO gtsTextRetrunCollectionDTO = new GtsTextRetrunCollectionDTO();
				gtsTextRetrunCollectionDTO.setKeyId(gtsBillDTO.getReferenceNumber());
				gtsTextRetrunCollectionDTO.setType(gtsItReturnDTO.getType());
				gtsTextRetrunCollectionDTO.setMessage(gtsItReturnDTO.getMessage().replaceAll(SINGLE_QUOTE, ESCAPE_QUOTE));
				gtsTextRetrunCollectionDTO.setGUuid("");
				gtsTextRetrunCollectionDTOList.add(gtsTextRetrunCollectionDTO);
			}
		}
		if (Tools.isEmpty(gtsTextRetrunCollectionDTOList)) {
			return;
		}
		for (List<GtsTextRetrunCollectionDTO> tempList : CommonUtils.splitList(gtsTextRetrunCollectionDTOList, NumConstant.INT_500)) {
			omsGtsRepository.insertGtsTextretruncollection(tempList);
		}
	}
	/* Ended by AICoder, pid:844d598791024262b7ec2b7e44a53df5 */

	/**
	 * 判断GTS的扫描结果和单据的扫描结果
	 */
	/* Started by AICoder, pid:1e174e140341473abdbc6365355a369a */
	public void getGtsBillDTO(GtsBillDTO gtsBillDTO, List<GtsItResultDTO> itResult, String ecssResult) {

		// GTS扫描结果
		boolean gtsResult = true;
		for (GtsItResultDTO gtsItResultDTO : itResult) {
			if (Tools.notEquals(STR_A, gtsItResultDTO.getCheckInd())) {
				gtsResult = false;
			}
		}
		if (gtsResult) {
			gtsBillDTO.setGtsResult(RELEASED_ZH);
		} else {
			gtsBillDTO.setGtsResult(NOT_RELEASED_ZH);
		}
		if (gtsResult && Tools.equals(RELEASED_ZH, ecssResult)) {
			gtsBillDTO.setStatus(GTSAUDITING);
		}
	}
	/* Ended by AICoder, pid:1e174e140341473abdbc6365355a369a */

}
