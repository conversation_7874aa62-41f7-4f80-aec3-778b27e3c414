package com.zte.application.step.impl;

import com.zte.application.step.StepWarnService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.domain.model.step.StepWarnRepository;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@Service
public class StepWarnServiceImpl implements StepWarnService {
    private static final Logger logger = LoggerFactory.getLogger(StepWarnServiceImpl.class);

    @Autowired
    private StepWarnRepository stepWarnRepository;

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    private EmailUtil emailUtil;

    @Autowired
    private StepTransferRepository stepTransfer;

    /**
     * OMS售卖单对接合规扫描检查异常监控预警
     */
    @Override
    public void warnGtsEcssMonitorStep() {
        logger.info("warnGtsEcssMonitorStep");
        //获取ECSS控制开关
        List<String> ecssControl = stepTransfer.getEmailUser(LOOKUP_TYPE_1000020);
        if (Tools.isEmpty(ecssControl)) {
            return;
        }
        List<String> emailBill = getLeakBill(ecssControl);
        if (Tools.isEmpty(emailBill)) {
            return;
        }
        //获取收件人
        String receipts = getEmailUser();
        try {
            //发送邮件
            String content = LEAK_SCAN_GTS_ECSS_CONTENT + Constant.STR_FEED + StringUtils.join(emailBill, Constant.STR_FEED);
            emailUtil.sendMail(receipts, LEAK_SCAN_GTS_ECSS, LEAK_SCAN_GTS_ECSS, content, content);
        } catch (Exception e) {
            logger.error(" ecss/gts漏扫描监控失败 " + e.getMessage());
            emailUtil.sendMail(receipts, INFOR_ALLCATE_EXCEPTION_MONITOR, BLANK, LEAK_SCAN_GTS_ECSS_FAILED, BLANK);
        }
    }

    //获取漏传的数据
    public List<String> getLeakBill(List<String> ecssControl) {
        List<String> emailBill = Tools.newArrayList();
        List<String> getAllGts = stepWarnRepository.queryAllGtsList();
        List<String> getAllEcss = stepWarnRepository.queryAllEcssList();
        //GTS
        List<String> listLeakGts = new ArrayList<>();
        List<String> listLeakEcss = new ArrayList<>();
        if (!Tools.isEmpty(getAllGts)) {
            listLeakGts.addAll(getAllGts);
        }
        //ECSS
        if (!Tools.isEmpty(getAllEcss)) {
            listLeakEcss.addAll(getAllEcss);
        }
        switch (ecssControl.get(INT_0)) {
            case Constant.STR_NUMBER_ONE:
                emailBill.addAll(listLeakGts);
                break;
            case Constant.STR_NUMBER_TWO:
            case Constant.STR_NUMBER_THREE:
                List<String> getGtsAndEcss = Stream.concat(listLeakGts.stream(), listLeakEcss.stream())
                        .distinct()
                        .collect(Collectors.toList());
                emailBill.addAll(getGtsAndEcss);
                break;
            case Constant.STR_NUMBER_FOUR:
                emailBill.addAll(listLeakEcss);
                break;
            default:
                break;
        }
        return emailBill;
    }

    public String getEmailUser() {
        List<String> users = stepTransfer.getEmailUser(LOOKUP_TYPE_1000029);
        return StringUtils.join(
                users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).collect(Collectors.toList())
                , SPLIT_22);
    }


}
