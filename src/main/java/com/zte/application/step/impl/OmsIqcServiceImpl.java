package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.infor.impl.TransferToInforServiceImpl;
import com.zte.application.step.OmsIqcService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.IscpRemoteServiceDataUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.*;
import com.zte.interfaces.infor.dto.IscpResultDTO;
import com.zte.interfaces.infor.dto.QaExInspectionDetail;
import com.zte.interfaces.infor.dto.QaExInspectionHead;
import com.zte.interfaces.infor.dto.QaExInspectionPack;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.XstreamUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.CHECK_RESULT_DETAIL_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class OmsIqcServiceImpl implements OmsIqcService {
	private static final Logger log = LoggerFactory.getLogger(OmsIqcServiceImpl.class);
	@Autowired
	OmsTransferRepository omsTransferRepository;
	@Autowired
	OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;
	@Value("${in.one.url}")
	private String inoneUrl;
	@Value("${in.one.wms.app.code}")
	private String inoneAppcode;

	@Override
	public void pushTransferCheckInfoToIqc(TransferDetailDTO dto) throws Exception{

		List<TransferDetailDTO> transferDetailDTOList = omsTransferRepository.getTransferBillIqcInfo(dto);
		BusiAssertException.isEmpty(transferDetailDTOList, MessageId.TRANSFER_BILL_NOT_EXISTS);
		TransferDetailDTO transferDTO = transferDetailDTOList.get(NumConstant.INT_0);
		// 校验调拨单状态是否为已下达 02
		BusiAssertException.notEquals(STR_02, transferDTO.getStatus(), MessageId.TRANSFER_BILL_STATUS_NOT_MAKE);
		// 校验调拨单源仓是否为研发打样库
		BusiAssertException.notEquals(STR_WMWHSE11, transferDTO.getStockNo(), MessageId.TRANSFER_BILL_STOCK_NOT_PROOFING);
		// 校验调拨单是否已经推送infor


		List<QaExInspectionHead> qaExInspectionHeadList = new ArrayList<>();
		for (TransferDetailDTO transferDetailDTO : transferDetailDTOList) {
			// 组装参数
			QaExInspectionHead qaExInspectionHead = getQaExInspectionHead(transferDetailDTO);
			transferDetailDTO.setCheckParams(JSON.toJSONString(qaExInspectionHead));
			qaExInspectionHeadList.add(qaExInspectionHead);
		}
		omsTransferRepository.updateTransferDetailBatch(transferDetailDTOList);
		// 送检
		ServiceData<List<IscpResultDTO>> result = IscpRemoteServiceDataUtil.inspectionReq(qaExInspectionHeadList);
		boolean sendResult = Tools.isNotEmpty(result) && Tools.isNotEmpty(result.getCode()) &&
				SUCESS_CODE.equals(result.getCode().getCode()) &&
				Tools.isNotEmpty(result.getBo()) && S.equals(result.getBo().get(Constant.INT_0).getProcessStatus());
		BusiAssertException.isTrue(!sendResult, MessageId.TRANSFER_BILL_SEND_IQC_FAILED);
		if(sendResult){
			transferDetailDTOList.stream().map(i -> i.setCheckStatus(TESTING).setLastUpdatedBy(dto.getLastUpdatedBy())).collect(Collectors.toList());
			omsTransferRepository.updateTransferDetailBatch(transferDetailDTOList);
			dto.setStatus(STR_06);
			omsTransferRepository.updateTransferHead(dto);
		}
	}

	public QaExInspectionHead getQaExInspectionHead(TransferDetailDTO transferDetailDTO) {

		// 单头
		QaExInspectionHead qaExInspectionHead = new QaExInspectionHead();
		//qaExInspectionHead.setDataType(STR_OMS);
		qaExInspectionHead.setDataType(INFOR);
		qaExInspectionHead.setReceiveNo(transferDetailDTO.getBillNo());
		qaExInspectionHead.setReceiptKey(transferDetailDTO.getBillNo());
		qaExInspectionHead.setBillType(STR_05); // 退库复检
		qaExInspectionHead.setSupplierNo(transferDetailDTO.getSupplierNo());
		qaExInspectionHead.setSupplierName(transferDetailDTO.getSupplierName());
		qaExInspectionHead.setDeliveryType(SC);
		qaExInspectionHead.setShipmentMode(STR_NORMAL2);
		qaExInspectionHead.setStock(transferDetailDTO.getStockNo());

		//明细
		List<QaExInspectionDetail> details = new ArrayList<>();
		QaExInspectionDetail qaExInspectionDetail = new QaExInspectionDetail();
		BeanUtils.copyProperties(transferDetailDTO, qaExInspectionDetail);
		qaExInspectionDetail.setReceiveNo(transferDetailDTO.getBillNo());
		qaExInspectionDetail.setReceiptKey(transferDetailDTO.getBillNo());
		qaExInspectionDetail.setLocation(transferDetailDTO.getStockNo());
		qaExInspectionDetail.setTestMode(TEST_MODE_IN_INSPECTION);
		qaExInspectionDetail.setIsNeedSupReport(NumConstant.INT_0);

		//装箱信息
		List<QaExInspectionPack> packs = new ArrayList<>();
		QaExInspectionPack qaExInspectionPack = new QaExInspectionPack();
		BeanUtils.copyProperties(transferDetailDTO, qaExInspectionPack);
		qaExInspectionPack.setReceiveNo(transferDetailDTO.getBillNo());
		qaExInspectionPack.setReceiptKey(transferDetailDTO.getBillNo());
		qaExInspectionPack.setPackNo(transferDetailDTO.getItemBarcode());
		if (Tools.equals(STR_02, transferDetailDTO.getBarcodeControlType())) {
			qaExInspectionPack.setReel(transferDetailDTO.getItemBarcode());
		}
		if (Tools.equals(STR_01, transferDetailDTO.getBarcodeControlType())) {
			qaExInspectionPack.setSn(transferDetailDTO.getItemBarcode());
		}
		qaExInspectionPack.setCode22(transferDetailDTO.getItemBarcode());
		qaExInspectionPack.setQty(transferDetailDTO.getDeliveryQty());
		qaExInspectionPack.setPackLocation(transferDetailDTO.getStockNo());
		packs.add(qaExInspectionPack);
		qaExInspectionDetail.setPacks(packs);
		details.add(qaExInspectionDetail);
		qaExInspectionHead.setDetails(details);
		return qaExInspectionHead;
	}

	/**
	 * IQC回传调拨单检验结果
	 */
	@Override
	public void transferCheckResultToOms(CheckResultHeadDTO checkResultHeadDTO) throws Exception{

		List<CheckResultDetailDTO> checkResultDetailDTOList = checkResultHeadDTO.getCheckResultDetailDTOList();
		// 检验结果明细不存在
		BusiAssertException.isEmpty(checkResultDetailDTOList, CHECK_RESULT_DETAIL_NOT_EXISTS);
		// 判断调拨单是否是”检验中“
		TransferDetailDTO dto = TransferDetailDTO.builder().build().setBillNo(checkResultHeadDTO.getRecheckNo());
		List<TransferDetailDTO> transferDetailDTOList = omsTransferRepository.getTransferBillInfo(dto);
		BusiAssertException.isEmpty(transferDetailDTOList, MessageId.TRANSFER_BILL_NOT_EXISTS);
		TransferDetailDTO transferDTO = transferDetailDTOList.stream().filter(i ->
				Tools.equals(i.getLineId(), checkResultHeadDTO.getLineId())).findFirst().orElse(new TransferDetailDTO());
		// 校验调拨单状态是否为检验中 06
		BusiAssertException.notEquals(STR_06, transferDTO.getStatus(), MessageId.TRANSFER_BILL_STATUS_NOT_TESTING);
		// 校验调拨单明细状态是否为检验中 TESTING
		BusiAssertException.notEquals(TESTING, transferDTO.getCheckStatus(), MessageId.TRANSFER_DETAIL_STATUS_NOT_TESTING);

		// 记录IQC回传的检验结果
		dto.setLineId(checkResultHeadDTO.getLineId()).setCheckResult(JSON.toJSONString(checkResultHeadDTO))
				.setLastUpdatedBy(checkResultHeadDTO.getDealer()).setCheckStatus(STR_CLOSED).
				setRightQty(checkResultDetailDTOList.get(NumConstant.INT_0).getRightQty());
		omsTransferRepository.updateTransferDetail(dto);


		List<TransferDetailDTO> transferBillInfo = omsTransferRepository.getTransferBillInfo(dto);
		// 如果存在调拨单明细未检验完成，不推送INFOR
		if (Tools.isNotEmpty(transferBillInfo.stream().filter(i -> Tools.isEmpty(i.getRightQty())).collect(Collectors.toList()))) {
			return;
		}

		// 如果调拨单所有明细都检验完成，且都为检验不合格，更新单据状态为 05 异常关闭
		if (Tools.isEmpty(transferBillInfo.stream().filter(i -> Tools.isNotEmpty(i.getRightQty())
				&& i.getRightQty().compareTo(BIG_0) > NumConstant.INT_0).collect(Collectors.toList()))) {
			dto.setStatus(STR_05);
			omsTransferRepository.updateTransferHead(dto);
			return;
		}

		// 如果调拨单所有明细都检验完成，且存在检验合格的明细，将检验合格的明细推送INFOR
		dto.setStatus(STR_07);
		omsTransferRepository.updateTransferHead(dto);

		// 调用INFOR接口推送数据，成功则更新单据状态为 02 已下达；失败则更新单据状态为 07 检验完成
		boolean isSuccess = sendInfor(dto.getBillNo());
		if (isSuccess) {
			// 保存soHeader/soDetail的数据
			List<SoHeader> listSoHeader = omsTransferRepository.getTransferSoHead(dto.getBillNo());
			List<SoDetail> listSoDetail = omsTransferRepository.getTransferSoDetail(dto.getBillNo());
			listSoDetail.forEach(t -> t.setRef60(dto.getBillNo()));
			omsTransferRepository.insertTransferSoHead(listSoHeader);
			// 分批新增
			List<List<SoDetail>> listOfDeList = CommonUtils.splitList(listSoDetail, NumConstant.BATCH_SIZE);
			listOfDeList.forEach(k -> omsTransferRepository.insertTransferSoDetail(k));
			// 更新调拨单状态为 02 已下达
			dto.setStatus(STR_02);
			omsTransferRepository.updateTransferHead(dto);
		}

	}

	public boolean sendInfor(String billNo) {
		ServiceData<?> res = WebServiceClient.submitInfor(onlineFallBackApplyBillRepository.getWsdlUrl(), getXmlMessage(billNo));
        return Tools.equals(RetCode.SUCCESS_CODE, res.getCode().getCode());
	}

	public String getXmlMessage(String billNo) {
		String curTime = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(new Date());
		MsgHeader msgHeader = new MsgHeader();
		msgHeader.setSourceSystemId(TRANSFER_SOURCESYSTEMID);
		msgHeader.setSourceSystemName(TRANSFER_SOURCESYSTEMNAME);
		msgHeader.setUserId(TRANSFER_SOURCESYS_USER);
		msgHeader.setUserName(TRANSFER_SOURCESYS_USER);
		msgHeader.setSubmitDate(curTime);
		List<SoHeader> listSoHeader = omsTransferRepository.getTransferSoHead(billNo);
		List<SoDetail> listSoDetail = omsTransferRepository.getTransferSoDetail(billNo);
		if (Tools.isEmpty(listSoHeader) || Tools.isEmpty(listSoDetail)) {
			return BLANK;
		}
		SoHeader soHeader = listSoHeader.get(NumConstant.INT_0);
		soHeader.setExternalOrderKey2(billNo);
		soHeader.setRequestedShipDate(curTime);
		soHeader.setListSoDetail(listSoDetail);
		SoBizCont bizCont = new SoBizCont();
		bizCont.setSoHeader(soHeader);
		MsgBody<SoBizCont> msgBody = new MsgBody<>();
		msgBody.setServiceCode(TRANSFER_WEBSERVICE_CODE);
		msgBody.setMsgid(UUID.randomUUID().toString());
		msgBody.setBizCont(bizCont);
		MsgSend msgSend = new MsgSend();
		msgSend.setMsgHeader(msgHeader);
		msgSend.setMsgBody(msgBody);
		return XstreamUtil.toXML(msgSend);
	}

}
