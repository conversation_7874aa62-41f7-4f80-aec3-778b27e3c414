package com.zte.application.step.impl;

import com.zte.application.step.PdoOmsService;
import com.zte.common.constants.PdoConstants;
import com.zte.domain.model.step.PdoOmsRepository;
import com.zte.interfaces.step.dto.ErrorDto;
import com.zte.interfaces.step.dto.PdoDetailInputDto;
import com.zte.interfaces.step.dto.PdoDetailOutputDto;
import com.zte.interfaces.step.dto.PdoDetailResultDto;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.zte.common.constants.PdoConstants.MSG_INPUT_LIMIT_EXCEEDED;
import static com.zte.common.utils.Constant.INT_500;

/**
 * PDO OMS服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@Service
//@DataSource(DatabaseType.DB_STEP)
public class PdoOmsServiceImpl implements PdoOmsService {

    @Autowired
    private PdoOmsRepository pdoOmsRepository;

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public PdoDetailOutputDto createPdoDetail(List<PdoDetailInputDto> inputDtoList) {
        log.info(PdoConstants.MSG_START_PROCESSING, inputDtoList);
        
        // 校验输入参数
        if(inputDtoList.size() > INT_500) {
            BusiAssertException.result(MSG_INPUT_LIMIT_EXCEEDED);
        }

        PdoDetailOutputDto outputDto = new PdoDetailOutputDto();
        List<PdoDetailResultDto> resultList = new ArrayList<>();
        List<ErrorDto> errorList = new ArrayList<>();

        try {
            // 生成UUID
            inputDtoList.forEach(dto -> dto.setId(UUID.randomUUID().toString()));

            // 批量插入PDO明细
            int insertResult = pdoOmsRepository.batchInsertPdoDetail(inputDtoList);
            
            if (insertResult == inputDtoList.size()) {
                // 构建成功结果
                buildSuccessOutput(outputDto, resultList, inputDtoList);
                log.info(PdoConstants.MSG_BATCH_INSERT_SUCCESS, insertResult);
            } else {
                // 构建插入不匹配错误
                String errorMsg = String.format(PdoConstants.MSG_BATCH_INSERT_MISMATCH, 
                    inputDtoList.size(), insertResult);
                buildErrorOutput(outputDto, errorList, PdoConstants.ERROR_CODE_SYSTEM_ERROR, 
                    errorMsg, "batchInsert");
                log.error(errorMsg);
            }

        } catch (DuplicateKeyException e) {
            // 处理重复键异常
            log.error(PdoConstants.MSG_DUPLICATE_KEY_ERROR, e);
            buildErrorOutput(outputDto, errorList, PdoConstants.ERROR_CODE_DUPLICATE,
                PdoConstants.ERROR_MSG_DUPLICATE + PdoConstants.MSG_DUPLICATE_ROLLBACK, e.getMessage());

        } catch (Exception e) {
            // 处理系统异常
            log.error(PdoConstants.MSG_SYSTEM_ERROR, e);
            buildErrorOutput(outputDto, errorList, PdoConstants.ERROR_CODE_SYSTEM_ERROR,
                PdoConstants.MSG_SYSTEM_ERROR_PREFIX + e.getMessage() + PdoConstants.MSG_DUPLICATE_ROLLBACK, e.getMessage());
        }

        log.info(PdoConstants.MSG_PROCESSING_COMPLETE, outputDto);
        return outputDto;
    }

    /**
     * 构建成功输出结果
     */
    private void buildSuccessOutput(PdoDetailOutputDto outputDto, List<PdoDetailResultDto> resultList,
            List<PdoDetailInputDto> inputDtoList) {
        for (PdoDetailInputDto inputDto : inputDtoList) {
            PdoDetailResultDto resultDto = new PdoDetailResultDto();
            resultDto.setPdoBill(inputDto.getPdoBill());
            resultDto.setItemNo(inputDto.getItemNo());
            resultDto.setReqQty(inputDto.getReqQty());
            resultDto.setStatus(PdoConstants.PROCESS_STATUS_SUCCESS);
            resultDto.setMessage(PdoConstants.MSG_PDO_DETAIL_SUCCESS);
            resultList.add(resultDto);
        }
        
        outputDto.setProcessStatus(PdoConstants.PROCESS_STATUS_SUCCESS);
        outputDto.setProcessStatusDesc(PdoConstants.PROCESS_STATUS_SUCCESS_DESC);
        outputDto.setOutputCollection(resultList);
    }

    /**
     * 构建错误输出结果
     */
    private void buildErrorOutput(PdoDetailOutputDto outputDto, List<ErrorDto> errorList,
            String errorCode, String errorMsg, String detail) {
        ErrorDto error = new ErrorDto(errorCode, errorMsg, detail);
        errorList.add(error);
        
        outputDto.setProcessStatus(PdoConstants.PROCESS_STATUS_ERROR);
        outputDto.setProcessStatusDesc(PdoConstants.PROCESS_STATUS_ERROR_DESC);
        outputDto.setErrorCollection(errorList);
    }

    /**
     * 快速构建错误输出结果
     */
    private PdoDetailOutputDto buildErrorOutput(String errorCode, String errorMsg) {
        PdoDetailOutputDto outputDto = new PdoDetailOutputDto();
        List<ErrorDto> errorList = new ArrayList<>();
        buildErrorOutput(outputDto, errorList, errorCode, errorMsg, null);
        return outputDto;
    }
} 