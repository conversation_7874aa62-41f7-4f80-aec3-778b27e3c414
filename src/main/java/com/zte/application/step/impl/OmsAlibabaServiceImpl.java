package com.zte.application.step.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.step.OmsAlibabaService;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.OmsAlibabaRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.PUSH_B2B_FAILED;
import static com.zte.common.model.MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_1;
import static com.zte.common.utils.NumConstant.INT_3;
import static com.zte.common.utils.NumConstant.INT_2;


/**
 * <AUTHOR>
 */
@Service
public class OmsAlibabaServiceImpl implements OmsAlibabaService {
    private static final Logger log = LoggerFactory.getLogger(OmsAlibabaServiceImpl.class);
    @Autowired
    private OmsAlibabaRepository omsAlibabaRepository;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    @Autowired
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    /**
     * * 推送领料单数据到阿里
     */
    @Override
    public void pushRequisitionBillToAlibaba(OmsAlibabaBillDTO dto) throws Exception {
        List<OmsAlibabaBillDTO> requisitionBills = omsAlibabaRepository.getRequisitionBills(dto);
        if (CollectionUtils.isEmpty(requisitionBills)) {
            return;
        }
        pushBillsToAlibaba(OMS_REQUISITION_BILL_ZH, requisitionBills, dto);
    }

    /**
     * * 推送退料单数据到阿里
     */
    @Override
    public void pushReturnBillToAlibaba(OmsAlibabaBillDTO dto) throws Exception {
        List<OmsAlibabaBillDTO> returnBills = omsAlibabaRepository.getReturnBills(dto);
        if (CollectionUtils.isEmpty(returnBills)) {
            return;
        }
        pushBillsToAlibaba(OMS_RETURN_BILL_ZH, returnBills, dto);
    }

    public void pushBillsToAlibaba(String projName, List<OmsAlibabaBillDTO> list, OmsAlibabaBillDTO dto) throws Exception {
        List<String> taskNos = list.stream().map(OmsAlibabaBillDTO::getEntityName).distinct().collect(Collectors.toList());
        //取任务附加属性表中的业务分类字段
        List<BulkTaskDetailDTO> bulkTaskDetailDTOList = imesCenterfactoryRemoteService.getCategory(taskNos, dto.getEmpNo());
        if (CollectionUtils.isEmpty(bulkTaskDetailDTOList)) {
            return;
        }
        //取消息类型
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100014);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        dto.setMessageType(sysLookupValuesDTO.getLookupMeaning());

        //以单号维度推送数据
        Map<String, List<OmsAlibabaBillDTO>> groupMap = list.stream()
                .collect(Collectors.groupingBy(OmsAlibabaBillDTO::getBillNo));
        groupMap.forEach((billNo, groupList) -> {
            String taskNo = groupList.get(INT_0).getEntityName();
            Optional<BulkTaskDetailDTO> filterDto = bulkTaskDetailDTOList.stream().filter(m -> m.getTaskNo().equals(taskNo)).findFirst();
            if (!filterDto.isPresent()) {
                return;
            }
            List<String> itemCodeList = groupList.stream().map(OmsAlibabaBillDTO::getItemNo)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            List<CustomerItemsDTO>  customerItemsDTOS;
            try {
                customerItemsDTOS = getCustomerItemsInfo(itemCodeList, dto.getEmpNo(),ALIBABA);
            }
            catch (Exception ex){
                return;
            }
            if (CollectionUtils.isEmpty(customerItemsDTOS)) {
                return;
            }
            Map<String, Object> map = setHeaderData(billNo, projName, filterDto.get(), groupList, customerItemsDTOS);
            if (map.size() <= INT_0) {
                return;
            }
            pushDataToB2B(projName,billNo, taskNo, dto, map);
            //退料上传infor箱包SN数据
            if (OMS_RETURN_BILL_ZH.equals(projName)) {
                try {//报错不做处理 重推也会报错
                    pushAddSnBindData(billNo, dto.getEmpNo(),customerItemsDTOS, groupList);
                }
                catch (Exception ex){
                }
            }
        });
    }

    public Map<String, Object> setHeaderData(String billNo, String projName, BulkTaskDetailDTO filterDto,
                                             List<OmsAlibabaBillDTO> groupList, List<CustomerItemsDTO> customerItemsDTOList) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> detailMaps = setDetailData(groupList, customerItemsDTOList);
        if (CollectionUtils.isEmpty(detailMaps)) {
            return map;
        }
        map.put("material_order_no", billNo);
        map.put("material_order_type", INT_1);
        if (OMS_RETURN_BILL_ZH.equals(projName)) {
            map.put("material_order_type", INT_3);
        }
        map.put("material_use", INT_2);
        map.put("factoryCode", groupList.get(INT_0).getFactoryCode());
        map.put(STR_CATEGORY, filterDto.getCustomerPartType());
        map.put("material_bill_list", detailMaps);
        return map;
    }

    public List<Map<String, Object>> setDetailData(List<OmsAlibabaBillDTO> list, List<CustomerItemsDTO> customerItemsDTOS) {
        List<Map<String, Object>> detailMaps = new ArrayList<>();
        for (OmsAlibabaBillDTO detail : list) {
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("order_detail_no", detail.getBillNo() + detail.getDetailId());
            detailMap.put("quantity_issue", detail.getPlanQty());
            if (StringUtils.isBlank(detail.getItemNo())) {
                continue;
            }
            CustomerItemsDTO  customerItemsDTO = customerItemsDTOS.stream().filter(m -> detail.getItemNo().equals(m.getZteCode())).findFirst().orElse(null);
            if (customerItemsDTO != null) {
                if (StringUtils.isBlank(customerItemsDTO.getCustomerCode())) {
                    continue;
                }
                detailMap.put("material_name", customerItemsDTO.getCustomerCode());
                detailMaps.add(detailMap);
            }
        }
        return detailMaps;
    }

    public void pushDataToB2B(String projName,String billNo, String taskNo, OmsAlibabaBillDTO dto, Map<String, Object> map) {
        ZteStockInfoUploadLogDTO logQueryDto = new ZteStockInfoUploadLogDTO();
        logQueryDto.setContractNo(billNo);
        logQueryDto.setMessageType(dto.getMessageType());
        List<ZteStockInfoUploadLogDTO> existLogInfo = zteStockInfoUploadRepository.getStockUploadLog(logQueryDto);

        //组装对象
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        if (CollectionUtils.isEmpty(existLogInfo)) {
            customerDataLogDTO.setKeywords(UUID.randomUUID().toString());
        } else {
            customerDataLogDTO.setKeywords(existLogInfo.get(INT_0).getId());
        }
        customerDataLogDTO.setOrigin(STR_OMS);
        customerDataLogDTO.setCustomerName(ALIBABA);
        customerDataLogDTO.setProjectName(projName);
        customerDataLogDTO.setProjectPhase(STRING_EMPTY);
        customerDataLogDTO.setCooperationMode(STRING_EMPTY);
        customerDataLogDTO.setMessageType(dto.getMessageType());
        customerDataLogDTO.setContractNo(billNo);
        customerDataLogDTO.setTaskNo(taskNo);
        customerDataLogDTO.setItemNo(STRING_EMPTY);
        customerDataLogDTO.setSn(STRING_EMPTY);
        customerDataLogDTO.setJsonData(JSONObject.toJSONString(map));
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setCreateBy(dto.getEmpNo());
        customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
        customerDataLogDTOList.add(customerDataLogDTO);

        //组装日志对象
        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
        BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
        zteStockInfoUploadLogDTO.setId(customerDataLogDTO.getKeywords());
        zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);

        zteStockInfoUploadRepository.insertOrUpdateStockUploadLog(zteStockInfoUploadLogDTOList);

        imesCenterfactoryRemoteService.pushDataToB2BKafKa(customerDataLogDTOList, dto.getEmpNo());
    }

    public List<CustomerItemsDTO> getCustomerItemsInfo(List<String> itemCodeList, String empNo,String userAddress) throws Exception {
        if (StringUtils.isEmpty(userAddress)|| CollectionUtils.isEmpty(itemCodeList)) {
            return new ArrayList<>();
        }
        Map<String, String> headers = Tools.newHashMap();
        headers.put(STR_APP_CODE, inoneAppcode);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_FACTORY_ID, STR_51);

        // 请求url
        String url = inoneUrl+ZTE_MES_MANUFACTURESHARE_CENTERFACTORY+GET_CUSTOMER_ITEMS_INFO_URL;
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setItemNoList(itemCodeList);
        customerItemsDTO.setCustomerSubName(userAddress);
        // 发送请求
        String res = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(customerItemsDTO),headers);

        BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
        JsonNode jsonNode = JacksonJsonConverUtil.getMapperInstance().readTree(res);
        BusiAssertException.isEmpty(jsonNode, PUSH_B2B_FAILED);
        String jsonCode = jsonNode.get(JSON_CODE).get(JSON_CODE).asText();
        BusiAssertException.notEquals(SUSSESS, jsonCode,  jsonNode.get(JSON_CODE).get(JSON_MSG).asText());
        return JacksonJsonConverUtil.jsonToListBean(jsonNode.get(JSON_BO).toString(), new TypeReference<List<CustomerItemsDTO>>() {
        });
    }

    public Map<String, Object> setSnAndPkgIdHeadData(String billNo,List<CustomerItemsDTO>  customerItemsDtos,List<OmsAlibabaBillDTO> list) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> detailMap = setSnAndPkgIdDetailData(list,customerItemsDtos);
        if (CollectionUtils.isEmpty(detailMap)) {
            return map;
        }
        map.put("billDetail",detailMap);
        map.put("srcSystem",STR_OMS);
        map.put("billNo",billNo);
        map.put("businessType",STR_STORE_EN);
        map.put("billType",STR_350);
        Map<String,Object> headMap =new HashMap<>();
        headMap.put("billHead",map);
        return headMap;
    }

    public List<Map<String, Object>> setSnAndPkgIdDetailData(List<OmsAlibabaBillDTO> list,List<CustomerItemsDTO>  customerItemsDtos) {
        List<Map<String, Object>> detailMap = new ArrayList<>();
        for (OmsAlibabaBillDTO item:list){
            List<String> snList = omsAlibabaRepository.getReturnBillSnList(item.getBillNo(),item.getBoxId());
            if(CollectionUtils.isEmpty(snList)){
                continue;
            }
            List<Map<String,Object>> snMaps = new ArrayList<>();
            for (String sn:snList){
                Map<String,Object> snMap = new HashMap<>();
                snMap.put("sn",sn);
                snMaps.add(snMap);
            }
            Map<String, Object> detail = new HashMap<>();
            CustomerItemsDTO  customerItemsDTO = customerItemsDtos.stream().filter(m -> item.getItemNo().equals(m.getZteCode())).findFirst().orElse(null);
            if (customerItemsDTO != null) {
                if (StringUtils.isBlank(customerItemsDTO.getCustomerCode())) {
                    continue;
                }
                detail.put("mpn",customerItemsDTO.getCustomerCode());
                detail.put("snDetail",snMaps);
                detail.put("stockNo",item.getWarehourseId());
                detail.put("itemNo",item.getItemNo());
                detail.put("itemBarcode",item.getItemBarCode());
                detail.put("pkgId",item.getBoxId());
                detail.put("qty",item.getPlanQty());
                detail.put("returnBy",item.getReturnBy());
                detailMap.add(detail);
            }
        }
        return detailMap;
    }

    public void pushAddSnBindData(String billNo,String empNo,List<CustomerItemsDTO>  customerItemsDtos, List<OmsAlibabaBillDTO> groupList) {
        Map<String,Object> map = setSnAndPkgIdHeadData(billNo,customerItemsDtos,groupList);
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(STR_APP_CODE, inoneAppcode);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_FACTORY_ID, STR_51);
        // 请求url
        String url = inoneUrl + ZTE_MES_RESOURCEWAREHOUSE_BILL + INFOR_ADDSNBINDDATA_URL;

        // 发送请求
        HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(map), headers);
    }

}
