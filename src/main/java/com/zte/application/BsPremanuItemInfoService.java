/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application;

import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.interfaces.dto.AsyncBomDTO;
import com.zte.interfaces.dto.BsAsyncDataDTO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 前加工信息维护接口
 *
 * <AUTHOR>
 **/
public interface BsPremanuItemInfoService {

    /**
     * 增加实体数据
     *
     * @param record
     * @return
     **/
    int insertBsPremanuItemInfo(BsPremanuItemInfo record);

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    int insertBsPremanuItemInfoSelective(BsPremanuItemInfo record);

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    int deleteBsPremanuItemInfoById(BsPremanuItemInfo record) throws Exception;

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    int updateBsPremanuItemInfoByIdSelective(BsPremanuItemInfo record);

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    int updateBsPremanuItemInfoById(BsPremanuItemInfo record);

    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return BsPremanuItemInfo
     **/
    BsPremanuItemInfo selectBsPremanuItemInfoById(BsPremanuItemInfo record);

    /**
     * 获取数量
     *
     * @param map
     * @return
     */
    long getPremanuInfoCount(Map<String, Object> map);

    /**
     * list查询
     *
     * @param map
     * @return
     */
    List<BsPremanuItemInfo> getPremanuInfoList(Map<String, Object> map);

    Page<BsPremanuItemInfoDTO> getPreManuItemInfo(BsPremanuItemInfoDTO record);

    RetCode addPreManuItem(BsPremanuItemInfoDTO record);

    String addPreManuItemBatch(List<BsPremanuItemInfoDTO> list) throws Exception;

    RetCode updatePreManuItem(BsPremanuItemInfoDTO record) throws Exception;

    /**
     * bom分阶查询
     *
     * @param entity
     * @return
     */
    Page<BsPremanuItemInfoDTO> getSubLevelPremanuInfo(BsPremanuItemInfoDTO entity);

    /**
     * 导出
     *
     * @param fileName
     * @param strings
     * @param rows
     * @param strings2
     * @param response
     */
    void exportModel(String fileName, String[] strings, List rows, String[] strings2,
                     HttpServletResponse response);

    /**
     * 定时保存bom 分阶查询数据到 BS_BOM_HIERARCHICAL_HEAD
     *
     * @param psWorkOrderBasicDTOS psWorkOrderBasicDTOS
     */
    void cycleSaveSubLevelPremanuInfo(List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS);

    /**
     * 运行BOM 分阶 异步逻辑
     *
     * @param list bom 数据
     * @return 异步表数据
     */
    BsAsyncDataDTO runBomCodeAsync(List<AsyncBomDTO> list);

    void asyncCycleSaveSubLevelPremanuInfo(List<PsWorkOrderBasicDTO> psWorkOrderBasicDTOS);

    /**
     * 直接查询BS_BOM_HIERARCHICAL_HEAD以及DETAIL获取分阶信息
     */
    List<BsPremanuItemInfo> getBsBomInfo(BsPremanuItemInfo dto) throws Exception;

    /**
     * DIP调拨-直接查询BS_BOM_HIERARCHICAL_HEAD以及DETAIL获取分阶信息
     */
    List<BsPremanuItemInfo> getDipBomInfo(BsPremanuItemInfo dto);

    /**
     * 通过批次获取 DIP 料
     * @param prodPlanIdList 批次信息
     * @return DIP 物料清单
     */
    List<BsPremanuItemInfo> queryDipBomInfoByProdPlanIdBatch(List<String> prodPlanIdList);

    /**
     * 物料级前加工信息查询
     * @param dto
     * @return
     * @throws Exception
     */
    Page<BsPremanuItemInfo> getBsItemPreInfo(BsPremanuItemInfo dto) throws Exception;

    Integer countExportTotal(BsPremanuItemInfo dto) throws Exception;

    BsBomHierarchicalHead getAssembleBomInfo(BsPremanuItemInfo dto);
}
