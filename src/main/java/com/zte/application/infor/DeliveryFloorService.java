package com.zte.application.infor;

import com.zte.interfaces.infor.dto.DeliveryFloorDTO;
import com.zte.interfaces.infor.dto.DeliveryPrintInfoDTO;
import com.zte.interfaces.infor.vo.DeliveryFloorListVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeliveryFloorService {


    /**
     * 无人接收楼栋配置信息导入
     * @param list
     */
    void importFloorInfo(List<DeliveryFloorDTO> list, String xEmpNo);

    /**
     * 无人接收楼栋配置信息查询
     * @param dto
     * @return
     */
    DeliveryFloorListVo getFloorInfo(DeliveryFloorDTO dto);

    /**
     * 无人接收楼栋配置信息失效
     * @param dto
     */
    void loseFloorInfo(DeliveryFloorDTO dto,String xEmpNo);

    /**
     * 无人接收楼栋配置信息生效
     * @param dto
     * @param xEmpNo
     */
    void effectFloorInfo(DeliveryFloorDTO dto, String xEmpNo);

    /**
     * 呼叫帮助触发红点
     * @param dto
     */
    void sendRedDotInfo(DeliveryFloorDTO dto);

    /**
     * 获取回执打印信息
     * @param dto
     * @return
     */
    List<DeliveryPrintInfoDTO> getPrintInfo(DeliveryFloorDTO dto);

    /**
     * 无人接收楼栋配置删除
     * @param dto
     */
    void deleteFloorInfo(DeliveryFloorDTO dto, String xEmpNo);
}
