package com.zte.application.infor;

import com.zte.interfaces.infor.dto.OverdueMaterialsDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.OverDueMaterialsListVO;

import java.util.List;

/* Started by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */
// service接口类
public interface OverdueMaterialsService {
    /**
     * 查询超期发料信息
     *
     * @param overdueMaterialsDTO 查询条件
     * @return 超期发料信息列表
     */
    OverDueMaterialsListVO query(OverdueMaterialsDTO overdueMaterialsDTO);
    /* Ended by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */
    // 其他业务方法...

    /**
     * 导出超期发料需求
     *
     * @param dto
     */
    void exportOverdueMaterialsRecord(OverdueMaterialsDTO dto);

    /**
     * 查询数据字典维护的信息
     *
     * @param dto
     * @return
     */
    List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto);

    /**
     * 导入超期发料需求
     *
     * @param dtos
     */
    void importOverdueMaterials(List<OverdueMaterialsDTO> dtos,String xEmpNo);

    /**
     * 三次超期邮件监控预警
     */
    void overdueMaterialWarnEmail(String xEmpNo);

    /**
     * 三次超期物料計算
     */
    void overdueMaterialCalc(String xEmpNo);
}