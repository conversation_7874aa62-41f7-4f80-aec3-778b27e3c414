package com.zte.application.infor;

import com.zte.interfaces.infor.dto.IapsStockDto;
import com.zte.interfaces.infor.dto.XcItemInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface InforIapsService {

    void synXcItemInfo(List<XcItemInfoDTO> list);

    ServiceData getInforStockInfo(IapsStockDto dto) throws Exception;
}
