package com.zte.application.infor;

import com.zte.domain.model.infor.DeliveryDetailDTO;
import com.zte.domain.model.infor.DeliverySignDetailDTO;
import com.zte.domain.model.infor.PcbOverdueInfoDTO;
import com.zte.interfaces.infor.dto.DeliverySignDTO;
import com.zte.interfaces.infor.dto.PcbOverdueDTO;
import com.zte.interfaces.infor.vo.DeliverySignListVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeliveryNoReceivesService {
    /* Started by AICoder, pid:96b10e3005ed47cb89f2c7150b679985 */
    /**
     * 录入送货单接口
     */
    DeliverySignDetailDTO insertSignInfo(DeliverySignDTO dto);

    /**
     * 查询送货签到信息
     */
    DeliverySignListVo getDeliverySignInfo(DeliverySignDTO dto);

    /**
     * 查询空库位
     */
    List<DeliverySignDetailDTO> getEmptyLocByWhseId(DeliverySignDTO dto);

    List<DeliveryDetailDTO> getQualityByWhseId(DeliverySignDTO dto);

    PcbOverdueInfoDTO getPcbOverdueInfo(PcbOverdueDTO dto) throws Exception;
    /* Ended by AICoder, pid:96b10e3005ed47cb89f2c7150b679985 */

}
