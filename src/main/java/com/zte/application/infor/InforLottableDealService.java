package com.zte.application.infor;

import com.zte.domain.model.infor.LotattributeDealLog;
import com.zte.interfaces.infor.dto.InforLottablesDTO;
import com.zte.interfaces.infor.dto.LotattributeDealLogDTO;
import com.zte.interfaces.infor.vo.LotattributeDealLogListVO;

import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2021/12/8
 **/
public interface InforLottableDealService {
    /**
     * splitBarcodeList
     * @param dto
     */
    void splitBarcodeList(InforLottablesDTO dto);

    /**
     * 获取infor批属性变更的数据
     * @param dto
     * @return
     */
    LotattributeDealLogListVO getInforLottableLogList(LotattributeDealLogDTO dto);

    /**
     * 更新infor批属性变更数据
     * @param dto
     * @return
     */
    void updateInforLottableLog(LotattributeDealLogDTO dto);
    /**
     * dataArchiving
     * @param
     */
    void dataArchiving();

}
