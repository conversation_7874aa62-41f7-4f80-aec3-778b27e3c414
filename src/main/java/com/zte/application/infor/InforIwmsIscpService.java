package com.zte.application.infor;

import java.util.List;

import com.zte.interfaces.infor.dto.*;
import com.zte.itp.msa.core.model.ServiceData;

/**
 * [描述] <br>
 * 
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年3月24日 <br>
 * @see com.zte.application.infor <br>
 */
public interface InforIwmsIscpService {

    /**
     * [方法描述] <br>
     *
     * <AUTHOR>
     * @param oList
     * @return <br>
     */
    ServiceData<?> updateVmiSoPrice(List<VmiSoPriceDTO> oList);

    ServiceData<?> updateFallBackSoPrice(List<OnlineFallBackPriceHeadDTO> oList);

    ServiceData<?> iscpEdiEntry(String externkey, String sourceTable);

    List<ReelidInventoryOutDTO> getReelidInventory(ReelidInventoryInputDTO reelidInventoryInputDTO);

	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param boxLabelDTO
	 * @return <br>
	 */ 
	ServiceData<?> getBoxLabelInfor(BoxLabelDTO boxLabelDTO);

	void iscpReturnInfor(ISCPReturnDTO dto);

	void dealIscpReturnData(String serialKey,String sourceTable);

	void warnInfor();
}
