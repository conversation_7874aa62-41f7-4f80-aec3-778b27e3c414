package com.zte.application.infor;

import com.zte.interfaces.infor.dto.OutPickingTaskDto;
import com.zte.interfaces.infor.dto.PickingTaskDto;
import com.zte.interfaces.infor.vo.BraidPickingInfoListVo;

import java.util.List;

public interface BraidCutMaterialService {
    List<OutPickingTaskDto> queryPickingTask(List<PickingTaskDto> list);
    BraidPickingInfoListVo queryPickingInfo(PickingTaskDto queryParams);
    void exportPickingInfo(PickingTaskDto queryParams);
}
