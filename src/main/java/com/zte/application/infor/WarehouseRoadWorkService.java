package com.zte.application.infor;


import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO;
import com.zte.interfaces.infor.vo.WarehouseRoadWorkListVO;

import java.util.List;

/**
 * 10255258
 */
public interface WarehouseRoadWorkService {

    /**
     * 查询坐标类型列表
     */
    List<SysLookupValuesDTO> getCoordinateTypeList();
    /**
     * 查询库位标记列表
     */
    List<SysLookupValuesDTO> getLocMarkList();

    /**
     * 查询库房列表
     */
    List<SysLookupValuesDTO> getWarehouseList();

    /**
     * 查询库区列表
     */
    List<SysLookupValuesDTO> getWarehouseAreaList();

    /**
     * 查询路网数据
     */
    WarehouseRoadWorkListVO getWarehouseRoadWorkList(WarehouseRoadWorkDTO dto);

    /**
     * 保存路网数据
     */
    void saveWarehouseRoadWork(WarehouseRoadWorkDTO dto);

    /**
     * 更新路网数据
     */
    void updateWarehouseRoadWork(WarehouseRoadWorkDTO dto);

    /**
     * 导入路网数据
     */
    void importWarehouseRoadWork(List<WarehouseRoadWorkDTO> list);

    /**
     * 导出路网数据
     */
    void exportWarehouseRoadWork(WarehouseRoadWorkDTO dto);

}
