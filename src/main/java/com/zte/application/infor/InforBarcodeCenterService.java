/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor;

import com.zte.interfaces.infor.dto.RuleReqDTO;
import com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO;
import com.zte.itp.msa.core.model.ServiceData;

/** 
 * [描述] <br> 
 *  
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年06月15日 <br>
 * @see com.zte.application.infor <br>
 */

public interface InforBarcodeCenterService {

	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param xEmpNo
	 * @param xAuthValue
	 * @param dto
	 * @return <br>
	 */ 
	ServiceData<?> pushTransferReelidToBarcodeCenter(String xEmpNo, String xAuthValue, TransferReelidRelationshipDTO dto);

	/**
	 * 查询条码中心数据
	 * @param dto
	 * @return
	 */
	ServiceData<?> getShortSnCode(RuleReqDTO dto);
	
}
