package com.zte.application.infor;

import java.util.List;

import com.zte.interfaces.infor.dto.SkuDTO;
import com.zte.interfaces.infor.dto.SkuQueryDTO;
import com.zte.interfaces.infor.dto.SkuReelIdInputDTO;
import com.zte.interfaces.infor.dto.SkuReelIdOutputDTO;

public interface SkuService {
	 List<SkuDTO> selectSkuById(SkuQueryDTO record);

	List<SkuReelIdOutputDTO> selectReelId(SkuReelIdInputDTO record);
}
