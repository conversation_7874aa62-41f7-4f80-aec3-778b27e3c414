package com.zte.application.infor;

import com.zte.domain.model.infor.RedDotExecuteInfo;
import com.zte.interfaces.infor.vo.RedDotExecuteDetilInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoListVo;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2025/02/24
 **/
public interface RedDotExecuteService {
    void orderRedDotTriggerJob();

    RedDotExecuteInfoListVo queryRedDotInfo(RedDotExecuteInfo dto);

    void exportExcel(RedDotExecuteInfo dto);

    void updateRedDotInfo(RedDotExecuteInfo dto);

    RedDotExecuteDetilInfoListVo queryRedDotDetilInfo(RedDotExecuteInfo dto);
    void executeRedDot(RedDotExecuteInfo dto);
}
