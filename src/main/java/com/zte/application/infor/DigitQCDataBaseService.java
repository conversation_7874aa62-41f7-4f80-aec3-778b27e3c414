package com.zte.application.infor;

import com.zte.domain.model.infor.DigitQCDataBaseHeadDTO;
import com.zte.interfaces.infor.dto.DigitQCDataBaseDTO;
import com.zte.interfaces.infor.vo.DigitObjectValueListVo;
import com.zte.interfaces.infor.vo.DigitQCDataBaseListVo;

import java.util.List;


public interface DigitQCDataBaseService {

    /**
     * 拉取infor有库存的物料代码
     */
    void pullInforItemNoJob() throws Exception;

    /**
     * 查询物料基础数据信息
     * @param dto
     * @return
     */
    DigitQCDataBaseListVo getQCDataBaseList(DigitQCDataBaseDTO dto);

    /**
     * 导出物料基础数据信息
     * @param dto
     */
    void exportQCDataBaseList(DigitQCDataBaseDTO dto, String xEmpNo);

    /**
     * 物料基础数据信息失效
     * @param dto
     */
    void loseQCDataBase(DigitQCDataBaseDTO dto);

    /**
     * 物料基础数据信息生效
     * @param dto
     */
    void effectQCDataBase(DigitQCDataBaseDTO dto);

    /**
     * 智能QC基础数据维护
     * @param dto
     */
    void updateQCDataBase(DigitQCDataBaseDTO dto);

    /**
     * 智能QC基础数据导入
     * @param list
     */
    void importQCDataBase(List<DigitQCDataBaseDTO> list) throws Exception;

    /**
     * 智能QC对象值数据导入
     * @param list
     */
    void importObjectInfo(List<DigitQCDataBaseDTO> list);

    /**
     * 查询对象值数据
     * @param dto
     */
    DigitObjectValueListVo getObjectInfo(DigitQCDataBaseDTO dto);

    /**
     * 逻辑删除对象值数据
     * @param dto
     */
    void deleteObjectInfo(DigitQCDataBaseDTO dto);

    /**
     * 导出对象值数据
     * @param dto
     * @param xEmpNo
     */
    void exportObjectInfo(DigitQCDataBaseDTO dto, String xEmpNo);

    /**
     * 智能QC基础数据查询
     * @param dto
     * @return
     */
    List<DigitQCDataBaseHeadDTO> queryDataBaseInfoList(DigitQCDataBaseDTO dto);
}
