/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-08-30
 * 修改历史 :
 *   1. [2019-08-30] 创建文件 by 6396000647
 **/
package com.zte.application.infor;

import java.util.List;

import com.zte.domain.model.infor.EdiAsnqcS;
import com.zte.interfaces.infor.dto.IscpResultDTO;
import com.zte.interfaces.infor.dto.QaExInspectionDetail;
import com.zte.interfaces.infor.dto.VmiStoreDTO;
import com.zte.interfaces.infor.dto.VmiStoreParamDTO;
import com.zte.itp.msa.core.model.ServiceData;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
public interface EdiAsnqcsService {


    /**
     * get all record
     *
     * @return List<EdiAsnqcS>
     **/
    java.util.List<EdiAsnqcS> selectEdiAsnqcSAll();

    /**
     * get all selectQualityCheck
     *
     * @return AsnQcDTO
     * @throws Exception
     **/
    ServiceData<?> selectQualityCheck(String receiptKey,String wmwhseId) throws Exception;

    /**
     * 获取检验的代码
     * <AUTHOR>
     * @return List<AsnQcCheckDetailDTO>
     *
     * */
    List<QaExInspectionDetail> selectQualityCheckDetail(EdiAsnqcS record ,String sendingPerson,String receiveTime,String wmwhseId);

    void updateIscpResult(ServiceData<List<IscpResultDTO>> ret1);
    /**
     * update record
     *
     * @return AsnQcDTO
     **/
    void  updateAsnFlag(EdiAsnqcS record);

    /**
     * update record
     *
     **/
    void  insertEdiAsnqcS(EdiAsnqcS record);

    /**
     * 获取仓库数据
     **/
    List<VmiStoreDTO> getVmiStoreByWmwhse(VmiStoreParamDTO vmiStoreParamDTO);

    ServiceData<?> selectAsnQcData(int rn) throws Exception;
    /**
     * 延时处理日志里面的问题
     * */
    ServiceData<?> selectAsnQcDataDelay() throws Exception;

	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param recheckNo
	 * @return <br>
	 */ 
	ServiceData sendDelayRecheckToCheck(String recheckNo);
}