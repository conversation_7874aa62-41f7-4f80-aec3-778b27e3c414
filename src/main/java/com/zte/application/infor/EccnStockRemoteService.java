package com.zte.application.infor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.hash.Hashing;
import com.zte.common.model.MessageId;
import com.zte.interfaces.infor.dto.EccnStockDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.*;


/**
 * author 10292893
 **/

@Service
public class EccnStockRemoteService {
	private static String BO = "bo";
	private static String ROWS = "rows";
	private static String TOTAL = "total";

	@Value("${eccn.token}")
	private String eccnToken;
	@Value("${in.one.url}")
	private String inoneUrl;
	@Value("${in.one.wms.app.code}")
	private String inoneAppcode;

	/**
	 * 获得接口数据
	 * @param paramsMap
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getEccnStockInfo(String paramsMap) throws IOException {
		// 仿真环境：http://test.eccn.zte.com.cn/zte-grc-eccn-eccnsearch/eccnApi/versionEccn
		// 生产环境：https://eccn.zte.com.cn/zte-grc-eccn-eccnsearch/eccnApi/versionEccn
		Map<String, String> headerParamsMap = new HashMap<>(16);
		Long currentTime = System.currentTimeMillis();
		String signatureValue = String.format("X-System-Code=%s & X-Time-Stamp=%d", MES, currentTime);
		String encryptedKey = Hashing.hmacSha256(eccnToken.getBytes()).hashString(signatureValue, StandardCharsets.UTF_8).toString();
		headerParamsMap.put(X_TIME_STAMP, String.valueOf(currentTime));
		headerParamsMap.put(X_SYSTEM_CODE, MES);
		headerParamsMap.put(X_ECCN_TOKEN, encryptedKey);
		headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
		headerParamsMap.put(INONE_APPCODE, inoneAppcode);
		String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + ECCN_VERSION_ECCN_URL, paramsMap, headerParamsMap);
		JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
		BusiAssertException.isEmpty(json, MessageId.ECCN_VERSION_ECCN_FOR_IWMS_FAILED);
		String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
		long total = 0;
		List<EccnStockDTO> list = new ArrayList<>();
		if (Tools.equals(RetCode.SUCCESS_CODE, retCode)) {
			total = Long.parseLong(json.get(JSON_BO).get(TOATL).toString());
			String jsonStr = json.get(JSON_BO).get(ROWS).toString();
			list = JacksonJsonConverUtil.jsonToListBean(jsonStr, new TypeReference<List<EccnStockDTO>>() {});
		}
		Map<String, Object> map = new HashMap<>(16);
		map.put("list",list);
		map.put("total",total);
		return map;
	}
}
