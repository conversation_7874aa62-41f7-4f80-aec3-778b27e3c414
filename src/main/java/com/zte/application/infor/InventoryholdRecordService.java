package com.zte.application.infor;

import com.zte.interfaces.infor.dto.InventoryholdDTO;
import com.zte.interfaces.infor.dto.InventoryholdRecordDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.InventoryholdRecordListVO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2021/12/8
 **/
public interface InventoryholdRecordService {
    /**
     * @param list
     */
    void synInforInventoryholdRecord(List<InventoryholdDTO> list);
    void unfreezeInventoryhold(InventoryholdDTO dto);
    void deleteInventoryholdRecord(List<InventoryholdDTO> list);

    List<SysLookupValuesDTO> getInventoryholdCodeList(SysLookupValuesDTO dto);

    InventoryholdRecordListVO getInventoryholdRecordList(InventoryholdRecordDTO dto);

    void inventoryholdRecordSendMail(String xEmpNo);

    InventoryholdRecordDTO getItemNoByItemBarcode(InventoryholdRecordDTO dto);

    void saveInventoryholdRecord(InventoryholdRecordDTO dto);

    void importInventoryHold(List<InventoryholdRecordDTO> list);

    void effectiveInventoryholdRecord(List<InventoryholdRecordDTO> list);

    void expireInventoryholdRecord(List<InventoryholdRecordDTO> list);

    void exportInventoryholdRecord(InventoryholdRecordDTO dto);

    void exportInventoryholdException(InventoryholdRecordDTO dto);

    void batchUpdateInventoryholdRecord(InventoryholdRecordDTO dto);

    void inventoryholdRecordJob(InventoryholdRecordDTO dto);

}
