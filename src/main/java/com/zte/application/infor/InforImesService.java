package com.zte.application.infor;

import com.zte.interfaces.infor.dto.EdiSoSImesDTO;
import com.zte.interfaces.infor.dto.InforImesQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto;
import com.zte.interfaces.infor.vo.LocVo;
import com.zte.interfaces.infor.vo.ZteinboundserialVo;
import com.zte.interfaces.step.dto.TechItemStoreDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * imes料单接收相关 实现
 *
 * <AUTHOR>
 * @date 2021-12-08 13:35
 */
public interface InforImesService {

    /**
     * 通过单据号2 查询 infor 发料信息
     *
     * @param externalorderkey2 单据号2
     * @return 单据汇总信息
     */
    List<EdiSoSImesDTO> queryEdiSoSByKey2(String externalorderkey2);

    /**
     * 查询条码信息
     * @param externalorderkey2 外部单单号2
     * @param serialnumber 单板条码
     * @param billType 单据类型
     * @return 条码信息
     */
    EdiSoSImesDTO queryVeneerInfo(String externalorderkey2, String serialnumber,String billType);

    /**
     * 查询原材料信息信息
     * @param externalorderkey2 外部单单号2
     * @param serialnumber 原材料编码
     * @return 原材料编码 信息
     */
    EdiSoSImesDTO queryProcessedInfo(String externalorderkey2, String serialnumber);

    /**
     * 查询已退单未收料
     * @param dto
     * @return
     */
    Integer getIsReturnReceived(InforImesReturnWarehouseDto dto);

    /**
     * 查询已退、任务退数量
     * @param dto
     * @return
     */
    List<InforImesReturnQtyDto> getReturnQty(InforImesQtyDto dto);

    /**
     * 查询已发数量
     * @param dto
     * @return
     */
    List<InforImesReturnQtyDto> getIssuedQty(InforImesQtyDto dto);

    /**
     * 查询需求数量
     * @param dto
     * @return
     */
    List<InforImesReturnQtyDto> getReqQty(InforImesQtyDto dto);

    List<String> getSerialnumber(ZteinboundserialVo dto);

    ServiceData<?> checkInforLoc(LocVo dto);

    /***
     * 获取单板重量信息
     * @param prodplanId
     * @return
     */
    TechItemStoreDTO getTechItemStore(String prodplanId);

    /***
     * 新增或修改单板重量信息
     * @param dto
     */
    void updateTechItemStore(TechItemStoreDTO dto);

    /**
     * 查询央仓和班组
     * @param ediSoSImesDTO
     * @return
     */
    EdiSoSImesDTO queryEdiSoSByKey2AndSku(EdiSoSImesDTO ediSoSImesDTO);
}
