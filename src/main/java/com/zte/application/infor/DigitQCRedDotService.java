package com.zte.application.infor;

import com.zte.interfaces.infor.dto.DigitQCRedDotManageDTO;
import com.zte.interfaces.infor.vo.DigitQCRedDotListVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


public interface DigitQCRedDotService {

    /**
     * 定时拉取IDA巡检录入信息
     */
    void pullInspectionInfoJob(DigitQCRedDotManageDTO dto) throws Exception;

    /**
     * IWMS导入QC红点信息
     * @param dto
     * @param xEmpNo
     */
    void digitQCRedDotImport(List<DigitQCRedDotManageDTO> list, String xEmpNo) throws Exception;

    /**
     * QC红点信息查询
     * @param dto
     * @return
     */
    DigitQCRedDotListVo digitQCRedDotQuery(DigitQCRedDotManageDTO dto);

    /**
     * QC红点信息失效
     * @param dto
     */
    void digitQCRedDotLose(DigitQCRedDotManageDTO dto);

    /**
     * QC红点信息任务完成回调
     * @param dto
     */
    void digitQCRedDotCallback(DigitQCRedDotManageDTO dto);

    /**
     * QC红点信息任务导出
     *
     * @param dto
     * @param xEmpNo
     */
    void digitQCRedDotExport(DigitQCRedDotManageDTO dto, String xEmpNo);
}
