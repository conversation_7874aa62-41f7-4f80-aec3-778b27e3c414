/* Started by AICoder, pid:ea0c98f196fc4a6f9089529488bbe7c1 */
package com.zte.application.infor;

import com.zte.interfaces.infor.dto.IqcTestRequisitionHeadDTO;
import com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO;
import com.zte.interfaces.infor.vo.IqcTestRequisitionListVO;
import com.zte.itp.msa.core.model.ServiceData;

public interface IqcTestRequisitionService {
    /**
     * 新增检验领料接口数据
     */
    ServiceData<?> insertIqcTestRequisitionInfo(IqcTestRequisitionHeadDTO oList);

    /**
     * 定时拆分领料明细JOB
     */
    void splitIqcDetailsJob();
    /**
     * 定时推送领料明细到inforJOB
     */
    void pushIqcDetailToInfor();
    /**
     * 给ISCP提供IQC领料结果
     */
    IqcTestRequisitionListVO getIqcTestStatusToIscp(IqcTestRequisitionQueryDTO dto);
    /**
     * 查询IQC领料明细
     */
    IqcTestRequisitionListVO getIqcTestRequistionList(IqcTestRequisitionQueryDTO dto);
    /**
     * 批量更新领料单
     */
    void batchUpdateIqcBill(IqcTestRequisitionQueryDTO dto);
    /**
     * 批量更新领料单明细
     */
    void batchUpdateIqcDetail(IqcTestRequisitionQueryDTO dto);
    /**
     * 导出领料单明细
     */
    void exportIqcTestRequistion(IqcTestRequisitionQueryDTO dto);
    /**
     * 查询IQC领料单头
     */
    IqcTestRequisitionListVO getIqcTestRequisitionBill(IqcTestRequisitionQueryDTO dto);
    /**
     * IQC领料拆分和提交失败邮件通知及提醒
     */
    void iqcFailSendMail(String xEmpNo);
}
/* Ended by AICoder, pid:ea0c98f196fc4a6f9089529488bbe7c1 */
