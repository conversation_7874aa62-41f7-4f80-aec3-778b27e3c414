package com.zte.application.infor;

import com.zte.interfaces.infor.dto.InventoryDiffDTO;
import com.zte.interfaces.infor.vo.InventoryDiffVO;
import com.zte.interfaces.infor.vo.UnBindPkgSnJobVo;

import java.util.List;
/* Started by AICoder, pid:f82de697ff722aa14bea09917024b01ecc3450db */

public interface InventoryDiffQueryService {

    /**
     * 获取库存差异数据
     * @param dto
     * @return
     */
    InventoryDiffVO getInventoryDiffData(InventoryDiffDTO dto);

    /**
     * 导出库存差异数据
     * @param dto
     */
     void exportInventoryDiffData(InventoryDiffDTO dto);

    /**
     * 同步阿里库存
     */
    void synchronizeInventoryDiffData( String empNo);
    /**
     * 获取未及时绑定箱与sn关系数据，给提醒
     * @return
     */
   void unBindSnRelationAlarm();


    /**
     * 原箱区物料和混箱区物料一致性监控
     */
    void originAndMixedBoxMaterialConsistencyWarning();

    /**
     * ZTE&阿里库存一致性监控
     */
    void aliInventoryConsistencyWarning();
}

/* Ended by AICoder, pid:f82de697ff722aa14bea09917024b01ecc3450db */