package com.zte.application.infor;

import com.zte.domain.model.infor.BondedWarehouseInventoryInfo;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * @Deacription 保税仓service
 * <AUTHOR>
 * @Date 2020/8/18 15:56
 **/
public interface IBondedWarehouseService {

    /**
     * 保税务仓库存批量查询
     *
     * @param inventoryInfoList 查询条件
     * @return 整箱库存信息
     */
    ServiceData<List<BondedWarehouseInventoryInfo>> queryBondedWarehouseInventory(List<BondedWarehouseInventoryInfo> inventoryInfoList);
}