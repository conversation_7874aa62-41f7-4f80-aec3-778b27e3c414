package com.zte.application.infor.impl;
/* Started by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */
// serviceImpl实现类

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.OverdueMaterialsService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.OverdueMaterialsRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.dto.CarrierGpsReturnDTO;
import com.zte.interfaces.infor.dto.OverdueMaterialsDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

@Service
public class OverdueMaterialsServiceImpl implements OverdueMaterialsService {

    private static final Logger log = LoggerFactory.getLogger(OverdueMaterialsServiceImpl.class);

    @Autowired
    private OverdueMaterialsRepository overdueMaterialsRepository;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private StepIscpRepository stepIscpRepository;
    @Autowired
    private EmailUtil emailUtil;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    /**
     * 查询超期发料信息
     */
    @Override
    public OverDueMaterialsListVO query(OverdueMaterialsDTO overdueMaterialsDTO) {
        OverDueMaterialsListVO listVo = new OverDueMaterialsListVO();
        overdueMaterialsDTO.setStartRow((overdueMaterialsDTO.getPageIndex() - INT_1) * overdueMaterialsDTO.getPageSize() + INT_1)
                .setEndRow(overdueMaterialsDTO.getPageIndex() * overdueMaterialsDTO.getPageSize());
        listVo.setTotal(overdueMaterialsRepository.selectOverdueMaterialsListVOTotal(overdueMaterialsDTO));
        listVo.setOverdueMaterialsDTOList(overdueMaterialsRepository.selectOverdueMaterialsList(overdueMaterialsDTO));
        return listVo;
    }
    /* Ended by AICoder, pid:8e0c1966a80b446d8c85d14ffd554780 */

    // 其他业务方法的实现...

    /**
     * 导出超期发料需求
     */
    @Override
    public void exportOverdueMaterialsRecord(OverdueMaterialsDTO dto) {
        int total = overdueMaterialsRepository.selectOverdueMaterialsListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            OverdueMaterialsDTO param = ((OverdueMaterialsDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(overdueMaterialsRepository.selectOverdueMaterialsList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(OVERDUE_MATERIAL_NAME).setFileName(OVERDUE_MATERIAL_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(OverdueMaterialsDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /**
     * 查询数据字典维护的信息
     */
    @Override
    public List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);

    }

    /**
     * 导入超期发料需求
     *
     * @param dtos
     */
    @Override
    public void importOverdueMaterials(List<OverdueMaterialsDTO> dtos, String xEmpNo) {
        if (Tools.isEmpty(dtos)) {
            return;
        }
        this.checkOverdueData(dtos);
        //删除表数据
        overdueMaterialsRepository.deleteRecheckRequire();
        dtos.forEach(p -> {
            p.setLastUpdatedBy(xEmpNo);
            p.setFactory(p.getFactory().trim());
            p.setItemNo(p.getItemNo().trim());
        });
        //新增数据
        CommonUtils.splitList(dtos, NumConstant.INT_500).forEach(i ->
                overdueMaterialsRepository.insertOverdueMaterialsRequire(i));
    }

    /**
     * 校验数据
     *
     * @param list
     */
    public void checkOverdueData(List<OverdueMaterialsDTO> list) {
        List<String> allList = new ArrayList<>();
        //校验数据都不为空
        BusiAssertException.isTrue(list.parallelStream().anyMatch(t -> Tools.isEmpty(t.getFactory()) || Tools.isEmpty(t.getItemNo()) ||
                Tools.isEmpty(t.getQty())), MessageId.REQUIRE_FACTORY_ITEM_QTY_NOT_EXISTS);
        //校验工厂+代码是唯一值
        Map<String, String> uniqueMap = list.stream()
                .collect(Collectors.toMap(
                        myClass -> myClass.getFactory() + TWO_SPLIT + myClass.getItemNo(),
                        myClass -> myClass.getFactory() + TWO_SPLIT + myClass.getItemNo(),
                        (oldValue, newValue) -> oldValue));
        BusiAssertException.isTrue(list.size() != uniqueMap.size(), MessageId.REQUIRE_REPEAT_FACTORY_ITEM);
        /* Started by AICoder, pid:ffc259b3d76b477c841fb267564b6630 */
        List<OverdueMaterialsDTO> lists = list.stream()
                .filter(code -> code.getItemNo().trim().length() != INT_12)
                .collect(Collectors.toList());
        /* Ended by AICoder, pid:ffc259b3d76b477c841fb267564b6630 */
        //校验代码位数为12位
        BusiAssertException.isNotEmpty(lists, MessageId.REQUIRE_ITEM_LENGTH_FAIL);

    }

    /**
     * 三次超期物料計算
     */
    @Override
    public void overdueMaterialCalc(String xEmpNo) {
        //获取数据字典（过滤含铅等级和计划指令）
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_OVERDUE_MATERIAL_MONITOR);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getOverdueSysList(sysLookupValuesDTO);
        //获取超期库存明细信息(IJOB定时轮询调用，一次只查询20000行数据)
        int execCount = INT_20000;
        SysLookupValuesDTO execCountDt=sysLookupValuesDTOList.stream().filter(t -> Constant.LOOKUP_CODE_100005900002.equals(String.valueOf(t.getLookupCode()))).findFirst().orElse(null);
        if(Tools.isNotEmpty(execCountDt)){
            execCount = Integer.parseInt(execCountDt.getLookupMeaning());
        }
        List<OverDueMaterialsEmailListVO> overList = overdueMaterialsRepository.selectOverdueMaterialItemList(execCount);
        if (Tools.isEmpty(overList))
        {
            return;
        }
        //获取指令
        List<String> lookupMeanings = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000057.equals(t.getLookupType()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        //获取是否过滤含铅等级
        String isEnableLead;
        SysLookupValuesDTO dto=sysLookupValuesDTOList.stream().filter(t -> Constant.LOOKUP_CODE_100005900001.equals(String.valueOf(t.getLookupCode()))).findFirst().orElse(null);
        if(dto!=null){
            isEnableLead=dto.getLookupMeaning();
        } else {
            isEnableLead = Constant.STR_NUMBER_ZERO;
        }
        //异步调用
        ThreadUtil.OVERDUE_RECHECK_EXECUTOR.execute(() -> dealPlanData(overList, xEmpNo,lookupMeanings,isEnableLead));
    }

    /**
     * 调用计划接口,更新表
     */
    public void dealPlanData(List<OverDueMaterialsEmailListVO> overList, String xEmpNo,List<String> isProdplan,String isEnableLead) {
        log.info("############dealPlanData开始调用", JsonUtil.toJSONString(overList));
        //物料代码去重复
        List<String> list = overList.stream().map(OverDueMaterialsEmailListVO::getItemNo).distinct().collect(Collectors.toList());
        List<OverDueMaterialsEmailListVO> updateList = new ArrayList<>();
        //获取计划信息
        Map<String, String> planMap = new HashMap<>();
        planMap.put(X_EMP_NO,xEmpNo);
        planMap.put(IS_ENABLE_LEAD,isEnableLead);
        //定义复检数据集
        List<OverDueMaterialsEmailListVO> allRecheckList = new ArrayList<>();
        List<OverDueMaterialsEmailListVO> resList = postPlanAsyncApi(list,isProdplan,overList,planMap,allRecheckList);
        //计划对应条码数据需排除复检数据
        if(CollectionUtils.isNotEmpty(resList)){
            List<OverDueMaterialsEmailListVO> planResList = resList.stream()
                    .filter(t -> Tools.isEmpty(t.getQcStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(planResList)){
                updateList.addAll(planResList);
            }
        }
        //获取匹配到计划的代码
        List<String> planItemlist = resList.stream().map(OverDueMaterialsEmailListVO::getItemNo).distinct().collect(Collectors.toList());
        //获取未匹配计划数据物料代码
        List<String> itemNoList = list.stream()
                .filter(item -> !planItemlist.contains(item))
                .collect(Collectors.toList());
        //手动复检
        List<OverDueMaterialsEmailListVO> overdueRequireList=overdueRequireCalc(itemNoList,overList,allRecheckList);
        if(CollectionUtils.isNotEmpty(overdueRequireList)){
            updateList.addAll(overdueRequireList);
        }
        CommonUtils.splitList(list, NumConstant.INT_500).forEach(k -> overdueMaterialsRepository.updatePlanFlag(k));
        if(CollectionUtils.isNotEmpty(updateList)){
            CommonUtils.splitList(updateList, NumConstant.INT_500).forEach(i -> overdueMaterialsRepository.batchUpdatePlanFlag(i));
        }
        log.info("############dealPlanData结束调用", JsonUtil.toJSONString(overList));
    }

    /***
     * 计算手动计划复检单
     * @param itemNoList
     * @param overRequireList
     * @param allRecheckList
     * @return
     */
    public List<OverDueMaterialsEmailListVO> overdueRequireCalc(List<String> itemNoList,List<OverDueMaterialsEmailListVO> overRequireList,List<OverDueMaterialsEmailListVO> allRecheckList){
        List<OverDueMaterialsEmailListVO> list=new ArrayList<>();
        if(CollectionUtils.isEmpty(itemNoList)){
            return list;
        }

        //分组复检数据
        Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo =
                Optional.ofNullable(allRecheckList)
                        .map(items -> items.stream()
                                .collect(Collectors.groupingBy(OverDueMaterialsEmailListVO::getItemNo)))
                        .orElse(Collections.emptyMap());

        try {
            //获取手动复检物料需求量
            List<OverDueMaterialsEmailListVO> overdueRequireList = overdueMaterialsRepository.selectOverdueRequireList();

            //处理每个物料代码
            for (String item : itemNoList) {
                List<OverDueMaterialsEmailListVO> itemResult = processOverdueRequireByItem(
                        item, overdueRequireList, overRequireList, recheckListByItemNo);
                list.addAll(itemResult);
            }
        } catch (Exception e) {
            log.error("PostPlanAsyncApi-overdueRequireCalc take exception", e);
        }

        return list;
    }

    /**
     * 处理单个物料代码的超期需求计算
     * @param itemNo 物料代码
     * @param overdueRequireList 超期需求列表
     * @param overRequireList 库存列表
     * @param recheckListByItemNo 复检数据分组
     * @return 处理结果列表
     */
    private List<OverDueMaterialsEmailListVO> processOverdueRequireByItem(
            String itemNo,
            List<OverDueMaterialsEmailListVO> overdueRequireList,
            List<OverDueMaterialsEmailListVO> overRequireList,
            Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo) {

        List<OverDueMaterialsEmailListVO> resultList = new ArrayList<>();

        // 先查询当前代码的需求量(存在不同仓数据)
        List<OverDueMaterialsEmailListVO> currReqQtyList = overdueRequireList.stream()
                .filter(t -> t.getItemNo().equals(itemNo))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(currReqQtyList)){
            return resultList;
        }

        //处理每个需求项
        for (OverDueMaterialsEmailListVO currReqQty : currReqQtyList) {
            List<OverDueMaterialsEmailListVO> singleResult = processSingleRequireItem(
                    itemNo, currReqQty, overRequireList, recheckListByItemNo);
            resultList.addAll(singleResult);
        }

        return resultList;
    }

    /**
     * 处理单个需求项的计算
     * @param itemNo 物料代码
     * @param currReqQty 当前需求项
     * @param overRequireList 库存列表
     * @param recheckListByItemNo 复检数据分组
     * @return 处理结果列表
     */
    private List<OverDueMaterialsEmailListVO> processSingleRequireItem(
            String itemNo,
            OverDueMaterialsEmailListVO currReqQty,
            List<OverDueMaterialsEmailListVO> overRequireList,
            Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo) {

        List<OverDueMaterialsEmailListVO> resultList = new ArrayList<>();

        // 获取当前代码库存
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList = overRequireList.stream()
                .filter(t -> t.getItemNo().equals(itemNo) &&
                        (Tools.isEmpty(currReqQty.getWhseId()) || t.getWhseId().equals(currReqQty.getWhseId())))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(currOverDueMaterialsEmailList)){
            return resultList;
        }

        //处理复检数据
        Integer recheckQtyValue = INT_0;
        String recheckQty = recheckOverdueRequireCalc(recheckListByItemNo, currReqQty, currOverDueMaterialsEmailList);
        if(!FLAG_N.equals(recheckQty)){
            recheckQtyValue = Integer.parseInt(recheckQty);//复检库存数量
            if(recheckQtyValue >= currReqQty.getQty().intValue()){
                return resultList; //复检库存已满足需求，直接返回
            }
        }

        //过滤复检数据并分组排序库存量
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailNoRecheList = currOverDueMaterialsEmailList.stream()
                .filter(t -> !STR_NUMBER_TWO.equals(t.getEmailType()))
                .sorted((o1, o2) -> sortLogic(o1, o2))
                .collect(Collectors.toList());

        // 标记对应条码发送邮件
        Integer sumQty = INT_0;
        for (OverDueMaterialsEmailListVO emailListVO : currOverDueMaterialsEmailNoRecheList) {
            //标识已匹配过的条码
            OverDueMaterialsEmailListVO dto = new OverDueMaterialsEmailListVO();
            dto.setWhseId(emailListVO.getWhseId());
            dto.setItemNo(emailListVO.getItemNo());
            dto.setItemBarcode(emailListVO.getItemBarcode());
            dto.setEmailType(STR_NUMBER_TWO);
            dto.setRemark(Tools.isEmpty(currReqQty.getWhseId()) ? OVERDUE_REMARK : STRING_EMPTY);
            dto.setPlanOrgName(currReqQty.getPlanOrgName());
            dto.setReqQty(currReqQty.getQty().intValue());
            resultList.add(dto);

            //计算是否已经满足需求量
            sumQty += emailListVO.getQty().intValue();
            Integer diffQty = currReqQty.getQty().intValue() - recheckQtyValue - sumQty;//需求数量-复检库存数量-库存数量
            //已满足结束循环
            if (diffQty <= INT_0) {
                break;
            }
        }

        return resultList;
    }

    /**
     * 请求头
     * @param xEmpNo
     * @return
     */
    public Map<String, String> createHeaderParamsMap(String xEmpNo) {
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(X_EMP_NO, xEmpNo);
        headerParamsMap.put(INONE_APPCODE, inoneAppcode);
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        return headerParamsMap;
    }

    /**
     * 汇总所有计划数据
     * @param splitList 分页物料代码
     * @param headerParamsMap 请求头
     * @param isProdplan //指令类型
     * @return
     */
    public List<PlanKitItemListVo> fetchAllPlanList(List<List<String>> splitList, Map<String, String> headerParamsMap, List<String> isProdplan) {
        List<PlanKitItemListVo> allPlanList = new ArrayList<>();
        for (List<String> strList : splitList) {
            log.info("############postPlanAsyncApi:" + JsonUtil.toJSONString(strList));
            //获取计划分页数据
            PlanKitItemListPageVo pageVo = postPlanAsyncPage(headerParamsMap, strList, isProdplan, INT_1);
            if (Tools.isNotEmpty(pageVo)) {
                allPlanList.addAll(pageVo.getRows());
                //获取第二页数据及++1页数据
                int totalPages = (int) Math.ceil((double) pageVo.getTotal() / INT_1000);
                for (int page = INT_2; page <= totalPages; page++) {
                    PlanKitItemListPageVo pageVos = postPlanAsyncPage(headerParamsMap, strList, isProdplan, page);
                    if (Tools.isNotEmpty(pageVos)) {
                        allPlanList.addAll(pageVos.getRows());
                    }
                }
            }
        }
        return allPlanList;
    }

    /***
     * 获取复检信息
     * @param splitList 物料代码
     * @param allRecheckList 复检List对象
     */
    public void getRecheckMaterialItemList(List<List<String>> splitList,List<OverDueMaterialsEmailListVO> allRecheckList){
        for (List<String> strList : splitList) {
            List<OverDueMaterialsEmailListVO> list = overdueMaterialsRepository.selectRecheckMaterialItemList(strList);
            if (CollectionUtils.isNotEmpty(list)) {
                allRecheckList.addAll(list);
            }
        }
    }

    /***
     * 调用计划接口
     * @param itemNoList 物料代码
     * @param isProdplan 计划指令类型
     * @param overList 库存数据
     * @param planMap 计划相关参数
     * @param allRecheckList 复检数据
     * @return
     */
    public List<OverDueMaterialsEmailListVO> postPlanAsyncApi(List<String> itemNoList,List<String> isProdplan,List<OverDueMaterialsEmailListVO> overList,Map<String, String> planMap,List<OverDueMaterialsEmailListVO> allRecheckList) {
        List<OverDueMaterialsEmailListVO> allList=new ArrayList<>();
        List<List<String>> splitList = CommonUtils.splitList(itemNoList, INT_500);
        Map<String, String> headerParamsMap = createHeaderParamsMap(planMap.get(X_EMP_NO));
        //获取复检数据
        getRecheckMaterialItemList(splitList,allRecheckList);
        //获取计划数据
        List<PlanKitItemListVo> allPlanList = fetchAllPlanList(splitList, headerParamsMap, isProdplan);
        //提前对 recheckList 按 itemNo 进行分组
        Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo =
                Optional.ofNullable(allRecheckList)
                        .map(list -> list.stream()
                                .collect(Collectors.groupingBy(OverDueMaterialsEmailListVO::getItemNo)))
                        .orElse(Collections.emptyMap());

        // 提前对 allPlanList 按 itemNo 进行分组
        Map<String, List<PlanKitItemListVo>> planListByItemNo = allPlanList.stream()
                .collect(Collectors.groupingBy(PlanKitItemListVo::getItemNo));
        //循环物料代码匹配对应条码数据
        for (String item : itemNoList) {
            List<PlanKitItemListVo> planList = planListByItemNo.getOrDefault(item, Collections.emptyList());
            List<OverDueMaterialsEmailListVO> recheckList = recheckListByItemNo.getOrDefault(item, Collections.emptyList());
            if (CollectionUtils.isNotEmpty(planList)) {
                List<OverDueMaterialsEmailListVO> list = overdueMaterialMatchBarcode(overList, planMap.get(IS_ENABLE_LEAD), planList,recheckList);
                if (CollectionUtils.isNotEmpty(list)) {
                    allList.addAll(list);
                }
            }
        }
        return allList;
    }

    /**
     * 分页请求计划接口
     * @param headerParamsMap 请求头
     * @param itemNoList  分页的物料代码
     * @param isProdplan 计划指令
     * @param page 页码
     * @return
     */
    public PlanKitItemListPageVo postPlanAsyncPage(Map<String, String> headerParamsMap, List<String> itemNoList, List<String> isProdplan, Integer page){
        JSONObject jsonItems = new JSONObject();
        jsonItems.put(ITEM_LIST_PLAN, itemNoList);
        // 根据数据字典配置过滤指令
        if(CollectionUtils.isNotEmpty(isProdplan)){
            jsonItems.put(IS_PRODPLAN, isProdplan);
        }
        JSONObject params = new JSONObject();
        params.put(JSON_BO, jsonItems);
        params.put(PAGE, page);
        params.put(ROWS, INT_1000);
        try {
            String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + PLAN_INONE_URL,
                    JacksonJsonConverUtil.beanToJson(params), headerParamsMap);
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
            if (null == json) {
                return null;
            }
            String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
            if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
                return null;
            }
            JsonNode boNodeRow = json.get(JSON_BO).get(ROWS);
            if (!boNodeRow.isEmpty()) {
                JsonNode boNode = json.get(JSON_BO);
                //匹配库存对应条码数据
                return JacksonJsonConverUtil.jsonToListBean(String.valueOf(boNode), new TypeReference<PlanKitItemListPageVo>() {
                });
            }
        } catch (Exception e) {
            log.error("PostPlanAsyncApi take exception:", e);
        }
        return null;
    }

    /**
     * 三次超期库存匹配条码
     * @param overList 库存数据
     * @param isEnableLead 是否启用含铅过滤
     * @param planList 计划数据
     * @param recheckList 复检数据
     * @return
     */
    public List<OverDueMaterialsEmailListVO> overdueMaterialMatchBarcode(List<OverDueMaterialsEmailListVO> overList,String isEnableLead,List<PlanKitItemListVo> planList,List<OverDueMaterialsEmailListVO> recheckList){
        //1. 组织转换仓库ID
        String whseId = STRING_EMPTY;
        List<String> list = overdueMaterialsRepository.selectOverdueMaterialWhseId(planList.get(INT_0).getItemNo());
        if(CollectionUtils.isNotEmpty(list)){
            whseId = list.get(INT_0);
        }
        for (PlanKitItemListVo item : planList) {
            if(PLAN_ORG_NAME.equals(item.getPlanOrgName())){
                item.setWhseId(STR_WMWHSE26);
            }else{
                item.setWhseId(whseId);
            }
            if(StringUtils.isEmpty(item.getIsLead())){
                //赋值空值最小的含铅等级
                item.setIsLead(STR_NUMBER_TEN);
            }
        }
        // 2.获取含铅等级
        if(STR_NUMBER_ONE.equals(isEnableLead)){
            List<OverDueMaterialsLeadVO> isLeadList = overdueMaterialsRepository.selectOverdueMaterialIsLead(planList);
            //赋值含铅等级
            for (PlanKitItemListVo item : planList) {
                OverDueMaterialsLeadVO isLeadVo = isLeadList.stream().filter(t -> t.getDescription().equals(item.getIsLead())).findFirst().orElse(null);
                item.setIsLead(STR_NUMBER_TEN);
                if(isLeadVo != null){
                    item.setIsLead(isLeadVo.getCode());
                }
            }
        }
        //3.计划维度无需汇总，含铅等级-是否参与过滤
        if(STR_NUMBER_ONE.equals(isEnableLead)){
            planList.stream().sorted(Comparator.comparing(PlanKitItemListVo::getIsLead).reversed());
        }
        // 计算复检条码
        return overdueMaterialCalcBarcode(overList,isEnableLead,planList,recheckList);
    }

    private String overdueMaterialCalcReset(PlanKitItemListVo item,String upperWhseId){
        if(Tools.isNotEmpty(item.getWhseId()) && !item.getWhseId().equals(upperWhseId)){
            return FLAG_Y;
        }
        return FLAG_N;
    }

    private String overdueMaterialCalcInventoryReset(Integer qtyDifference,Integer isOccupation,Map<String, String> stockMap,PlanKitItemListVo item,List<PlanKitItemListVo> isMultiplePlan){
        if(qtyDifference<INT_0){
            if(isOccupation == INT_1){
                return FLAG_Y;
            }else {
                item.setUpperItemBarcode(stockMap.get(X_ITEM_BARCODE)); //存储条码
                item.setItemWhseId(stockMap.get(WM_WHSE_ID)); //存储仓库
                isMultiplePlan.add(item);
            }
        }
        return FLAG_N;
    }

    /**
     * 三次超期库存计算需匹配条码
     * @param overList 库存数据
     * @param isEnableLead 是否启用含铅过滤
     * @param sumPlanList 计划数据
     * @param recheckList 复检数据
     * @return
     */
    public List<OverDueMaterialsEmailListVO> overdueMaterialCalcBarcode(List<OverDueMaterialsEmailListVO> overList,String isEnableLead,List<PlanKitItemListVo> sumPlanList,List<OverDueMaterialsEmailListVO> recheckList){
        String itemNo = sumPlanList.get(INT_0).getItemNo();
        // 先查询当前代码的库存
        List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList = overList.stream()
                .filter(t -> t.getItemNo().equals(itemNo))
                .collect(Collectors.toList());
        // 循环匹配对应条码
        Integer qtyDifference = INT_0;//差值
        Integer sumQty=INT_0;//库存数量
        String upperItemBarcode = STRING_EMPTY;//上一个物料的220条码
        String upperWhseId = STRING_EMPTY;//上一个物料的仓库
        String itemWhseId = STRING_EMPTY;//条码对应的仓库
        Integer isOccupation=INT_0;//标识当前条码第一次被占用
        List<PlanKitItemListVo> isMultiplePlan=new ArrayList<>();
        for (PlanKitItemListVo item : sumPlanList) {
            if(this.overdueMaterialCalcReset(item,upperWhseId).equals(FLAG_Y)){
                qtyDifference = INT_0;// 不同仓剩余库存清零
                sumQty=INT_0;
            }
            //存在剩余库存记录计划信息
            Map<String, String> stockMap = new HashMap<>();
            stockMap.put(X_ITEM_BARCODE,upperItemBarcode);
            stockMap.put(WM_WHSE_ID,itemWhseId);
            String retStr = this.overdueMaterialCalcInventoryReset(qtyDifference,isOccupation,stockMap,item,isMultiplePlan);
            if(retStr.equals(FLAG_Y)){
                isOccupation=INT_0;
            }
            // 需求数量-上次条码剩余库存小于等于0结束本次循环，继续下一次循环
            Integer reqQty = item.getReqQty()+(qtyDifference);
            if(reqQty <= INT_0){
                qtyDifference = reqQty;   //上次条码足够库存-赋值剩余库存
                sumQty+=item.getReqQty();//上次条码足够库存直接累计需求量
                continue;
            }
            //处理复检数据
            String recheckQty = recheckMaterialCalc(isEnableLead,item,recheckList,currOverDueMaterialsEmailList);
            if(!FLAG_N.equals(recheckQty)){
                Integer recheckQtyValue = Integer.parseInt(recheckQty);
                qtyDifference = recheckQtyValue; //剩余库存（负数表示剩余库存，正数表示需求数量）
                if(recheckQtyValue <= INT_0){
                    upperWhseId = item.getWhseId();
                    continue;
                }
            }
            // 已排查标记发送邮件数据（匹配对应仓库或含铅等级库存数据）
            List<OverDueMaterialsEmailListVO> list = getOverDueMaterialsEmailListVOS(isEnableLead, item, currOverDueMaterialsEmailList);
            // 标记对应条码发送邮件
            Map<String, Integer> qtyMap = new HashMap<>();
            qtyMap.put(SUM_QTY,sumQty);
            qtyMap.put(REQ_QTY,reqQty);
            qtyMap.put(QTY_DIFFERENCE,qtyDifference);
            qtyMap.put(IS_OCCUPATION,isOccupation);
            Map<String, String> upperItemMap = new HashMap<>();
            upperItemMap.put(X_ITEM_BARCODE,upperItemBarcode);
            upperItemMap.put(UPPER_WHSE_ID,upperWhseId);
            upperItemMap.put(WM_WHSE_ID,itemWhseId);
            //计算条码匹配库存
            if(CollectionUtils.isNotEmpty(list)){
                Map<String, String> retMap = this.calcBarcode(list,currOverDueMaterialsEmailList,item,qtyMap,upperItemMap);
                sumQty = Integer.parseInt(retMap.get(SUM_QTY));
                qtyDifference = Integer.parseInt(retMap.get(QTY_DIFFERENCE));
                isOccupation = Integer.parseInt(retMap.get(IS_OCCUPATION));
                upperItemBarcode = retMap.get(X_ITEM_BARCODE);
                upperWhseId = retMap.get(UPPER_WHSE_ID);
                itemWhseId = retMap.get(WM_WHSE_ID);
            }
        }
        //插入同条码多个计划信息
        this.insertOverdueMaterialsPlan(isMultiplePlan);
        //返回需要发送邮件的条码
        return currOverDueMaterialsEmailList.stream()
                .filter(t -> STR_NUMBER_TWO.equals(t.getEmailType()))
                .collect(Collectors.toList());
    }

    private Map<String,String> calcBarcode(List<OverDueMaterialsEmailListVO> list,List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList,PlanKitItemListVo item,Map<String, Integer> qtyMap,Map<String, String> upperItemMap){
        Map<String, String> stockMap = new HashMap<>();
        Integer sumQty=qtyMap.get(SUM_QTY);//库存数量
        Integer reqQty=qtyMap.get(REQ_QTY);//需求数量
        Integer qtyDifference=qtyMap.get(QTY_DIFFERENCE);//差值
        Integer isOccupation=qtyMap.get(IS_OCCUPATION);//标识当前条码第一次被占用
        String upperItemBarcode = upperItemMap.get(X_ITEM_BARCODE);//上一个物料的220条码
        String upperWhseId = upperItemMap.get(UPPER_WHSE_ID);//上一个物料的仓库
        String itemWhseId = upperItemMap.get(WM_WHSE_ID);//条码对应的仓库
        for (OverDueMaterialsEmailListVO emailListVO : list) {
            //库存不满足才更新条码
            this.setCurrOverDueMaterialsEmailList(currOverDueMaterialsEmailList,emailListVO,item);
            sumQty+=emailListVO.getQty().intValue();
            Integer diffQty = reqQty-(sumQty);//需求数量-库存数量
            //已满足需求量
            if (diffQty <= INT_0) {
                qtyDifference=diffQty;
                if(!upperItemBarcode.equals(emailListVO.getItemBarcode())){
                    isOccupation=INT_1;
                }
                upperItemBarcode = emailListVO.getItemBarcode();
                upperWhseId = item.getWhseId();
                itemWhseId = emailListVO.getWhseId();
                stockMap.put(SUM_QTY,sumQty.toString());
                stockMap.put(QTY_DIFFERENCE,qtyDifference.toString());
                stockMap.put(IS_OCCUPATION,isOccupation.toString());
                stockMap.put(X_ITEM_BARCODE,upperItemBarcode);
                stockMap.put(UPPER_WHSE_ID,upperWhseId);
                stockMap.put(WM_WHSE_ID,itemWhseId);
                return stockMap;
            }
        }
        stockMap.put(SUM_QTY,sumQty.toString());
        stockMap.put(QTY_DIFFERENCE,qtyDifference.toString());
        stockMap.put(IS_OCCUPATION,isOccupation.toString());
        stockMap.put(X_ITEM_BARCODE,upperItemBarcode);
        stockMap.put(UPPER_WHSE_ID,upperWhseId);
        stockMap.put(WM_WHSE_ID,itemWhseId);
        return stockMap;
    }

    private void setCurrOverDueMaterialsEmailList(List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList,OverDueMaterialsEmailListVO emailListVO,PlanKitItemListVo item){
        currOverDueMaterialsEmailList.stream()
                .filter(t -> t.getItemBarcode().equals(emailListVO.getItemBarcode()) && t.getWhseId().equals(emailListVO.getWhseId()))
                .forEach(t -> {
                    t.setEmailType(STR_NUMBER_TWO);
                    t.setRemark(Tools.isEmpty(item.getWhseId())?OVERDUE_REMARK:STRING_EMPTY);
                    t.setPlanOrgName(item.getPlanOrgName());
                    t.setReqQty(item.getReqQty());
                    t.setBomNo(item.getBomNo());
                    t.setProdPlanNo(item.getProdPlanNo());
                    t.setPlanner(item.getPlanner());
                    t.setProductClassName(item.getProductClassName());
                });//标识匹配的条码发送邮件
    }

    private void insertOverdueMaterialsPlan(List<PlanKitItemListVo> isMultiplePlan){
        if(CollectionUtils.isNotEmpty(isMultiplePlan)) {
            CommonUtils.splitList(isMultiplePlan, NumConstant.INT_500).forEach(i ->
                    overdueMaterialsRepository.insertOverdueMaterialsPlan(i));//插入同条码多个计划信息
        }
    }

    /***
     * 根据复检单计算库存是否需要复检（手动复检）
     * @param recheckListByItemNo 复检代码分组数据
     * @param currReqQty 当前代码对应仓库数据
     * @param currOverDueMaterialsEmailList 当前代码库存数据
     * @return
     */
    public String recheckOverdueRequireCalc(Map<String, List<OverDueMaterialsEmailListVO>> recheckListByItemNo,
                                            OverDueMaterialsEmailListVO currReqQty,
                                            List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList) {
        //获取当前物料代码复检数据
        List<OverDueMaterialsEmailListVO> recheckList = recheckListByItemNo.getOrDefault(currReqQty.getItemNo(), Collections.emptyList());
        if (CollectionUtils.isEmpty(recheckList)) {
            return FLAG_N;
        }
        // 预处理过滤条件
        boolean found = false;
        for (OverDueMaterialsEmailListVO t : recheckList) {
            if (Tools.isEmpty(currReqQty.getWhseId()) || t.getWhseId().equals(currReqQty.getWhseId())) {
                t.setEmailType(Constant.STR_NUMBER_TWO);
                found = true;
            }
        }
        if (!found) {
            return FLAG_N;
        }
        // 第二阶段：更新当前库存条件已发送及复检状态
        Map<String, OverDueMaterialsEmailListVO> barcodeWhseMap = recheckList.stream()
                .filter(t -> Constant.STR_NUMBER_TWO.equals(t.getEmailType()))
                .collect(Collectors.toMap(
                        vo -> vo.getItemBarcode() + vo.getWhseId(),
                        Function.identity(),
                        (existing, replacement) -> existing // 保留第一个匹配项
                ));
        for (OverDueMaterialsEmailListVO t : currOverDueMaterialsEmailList) {
            if (!Constant.STR_NUMBER_TWO.equals(t.getEmailType())) {
                String key = t.getItemBarcode() + t.getWhseId();
                OverDueMaterialsEmailListVO dto = barcodeWhseMap.get(key);
                if (dto != null) {
                    t.setEmailType(dto.getEmailType());
                    t.setQcStatus(dto.getQcStatus());
                }
            }
        }
        // 第三阶段：计算复检库存量
        BigDecimal totalQty = recheckList.stream()
                .filter(t -> Tools.isEmpty(currReqQty.getWhseId()) || t.getWhseId().equals(currReqQty.getWhseId()))
                .map(OverDueMaterialsEmailListVO::getQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return String.valueOf(totalQty);
    }

    /***
     * 根据复检单计算库存是否需要复检
     * @param isEnableLead
     * @param item
     * @param recheckList
     * @param currOverDueMaterialsEmailList
     * @return
     */
    public String recheckMaterialCalc(String isEnableLead, PlanKitItemListVo item,
                                      List<OverDueMaterialsEmailListVO> recheckList,
                                      List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList) {
        if (CollectionUtils.isEmpty(recheckList)) {
            return FLAG_N;
        }
        // 预处理过滤条件
        boolean isEnableLeadZero = Constant.STR_NUMBER_ZERO.equals(isEnableLead);
        String whseId = item.getWhseId();
        boolean hasWarehouseFilter = Tools.isNotEmpty(whseId);
        // 第一阶段：标记符合条件的复检明细已发送
        boolean found = false;
        for (OverDueMaterialsEmailListVO t : recheckList) {
            if (shouldUpdateEmailType(t, isEnableLeadZero, item.getIsLead(), whseId, hasWarehouseFilter)) {
                t.setEmailType(Constant.STR_NUMBER_TWO);
                found = true;
            }
        }
        if (!found) {
            return FLAG_N;
        }
        // 第二阶段：更新当前库存条件已发送及复检状态
        Map<String, OverDueMaterialsEmailListVO> barcodeWhseMap = recheckList.stream()
                .filter(t -> Constant.STR_NUMBER_TWO.equals(t.getEmailType()))
                .collect(Collectors.toMap(
                        vo -> vo.getItemBarcode() + vo.getWhseId(),
                        Function.identity(),
                        (existing, replacement) -> existing // 保留第一个匹配项
                ));
        for (OverDueMaterialsEmailListVO t : currOverDueMaterialsEmailList) {
            if (!Constant.STR_NUMBER_TWO.equals(t.getEmailType())) {
                String key = t.getItemBarcode() + t.getWhseId();
                OverDueMaterialsEmailListVO dto = barcodeWhseMap.get(key);
                if (dto != null) {
                    t.setEmailType(dto.getEmailType());
                    t.setQcStatus(dto.getQcStatus());
                }
            }
        }
        // 第三阶段：计算剩余库存量
        BigDecimal totalQty = recheckList.stream()
                .filter(t -> shouldCountInTotal(t, isEnableLeadZero, item.getIsLead(), whseId, hasWarehouseFilter))
                .map(OverDueMaterialsEmailListVO::getQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return String.valueOf(item.getReqQty() - totalQty.intValue());
    }

    /***
     * 复检明细条件
     * @param t
     * @param isEnableLeadZero
     * @param itemIsLead
     * @param whseId
     * @param hasWarehouseFilter
     * @return
     */
    public boolean shouldUpdateEmailType(OverDueMaterialsEmailListVO t, boolean isEnableLeadZero,
                                          String itemIsLead, String whseId, boolean hasWarehouseFilter) {
        int lottable08 = Integer.parseInt(t.getLottable08());
        return (isEnableLeadZero || lottable08 >= Integer.parseInt(itemIsLead))
                && !Constant.STR_NUMBER_TWO.equals(t.getEmailType())
                && (!hasWarehouseFilter || Objects.equals(t.getWhseId(), whseId));
    }

    /***
     * 计算剩余库存复检条件
     * @param t
     * @param isEnableLeadZero
     * @param itemIsLead
     * @param whseId
     * @param hasWarehouseFilter
     * @return
     */
    public boolean shouldCountInTotal(OverDueMaterialsEmailListVO t, boolean isEnableLeadZero,
                                       String itemIsLead, String whseId, boolean hasWarehouseFilter) {
        int lottable08 = Integer.parseInt(t.getLottable08());
        return (isEnableLeadZero || lottable08 >= Integer.parseInt(itemIsLead))
                && (!hasWarehouseFilter || Objects.equals(t.getWhseId(), whseId));
    }

    /**
     * 按条件分组排序
     * @param isEnableLead
     * @param item
     * @param currOverDueMaterialsEmailList
     * @return
     */
    public List<OverDueMaterialsEmailListVO> getOverDueMaterialsEmailListVOS(String isEnableLead, PlanKitItemListVo item, List<OverDueMaterialsEmailListVO> currOverDueMaterialsEmailList) {
        // 过滤已匹配需发送邮件的条码
        List<OverDueMaterialsEmailListVO> list =new ArrayList<>();
        // 是否含铅过滤、匹配仓库（仓库为空不匹配）
        if (STR_NUMBER_ONE.equals(isEnableLead)) {
            list = filterAndSortByLead(currOverDueMaterialsEmailList, item);
        }else{
            list = currOverDueMaterialsEmailList.stream()
                    .filter(t -> !t.getEmailType().equals(STR_NUMBER_TWO) && (Tools.isEmpty(item.getWhseId()) || t.getWhseId().equals(item.getWhseId())))
                    .sorted((o1, o2) -> sortLogic(o1, o2))
                    .collect(Collectors.toList());
        }
        return list;
    }

    // 辅助方法：排序逻辑
    public int sortLogic(OverDueMaterialsEmailListVO o1, OverDueMaterialsEmailListVO o2) {
        if (STR_NUMBER_ONE.equals(o1.getFefo())) {
            // 如果都是到期先出，则先按时间排序，如果相同再按条码排序
            int lottableCompare = o1.getLottable12().compareTo(o2.getLottable12());
            if (lottableCompare != INT_0) {
                return lottableCompare;
            } else {
                return o1.getItemBarcode().compareTo(o2.getItemBarcode());
            }
        } else {
            // 否则按条码排序
            return o1.getItemBarcode().compareTo(o2.getItemBarcode());
        }
    }

    // 辅助方法：过滤并排序
    public List<OverDueMaterialsEmailListVO> filterAndSortByLead(List<OverDueMaterialsEmailListVO> list, PlanKitItemListVo item) {
        return list.stream()
                .filter(t -> Integer.valueOf(t.getLottable08()) >= Integer.valueOf(item.getIsLead()) && !t.getEmailType().equals(STR_NUMBER_TWO) && (Tools.isEmpty(item.getWhseId()) || t.getWhseId().equals(item.getWhseId())))
                .sorted(this::sortLogic)
                .collect(Collectors.toList());
    }

    /**
     * 三次超期邮件监控预警
     */
    @Override
    public void overdueMaterialWarnEmail(String xEmpNo) {
        //判断计划接口是否已调用完成，并且未发邮件
        //获取超期库存条码信息
        int total = overdueMaterialsRepository.selectOverdueEmailListCount();
        log.info("############OverdueMaterialWarnEmail开始调用");
        //异步调用
        ThreadUtil.OVERDUE_RECHECK_EXECUTOR.execute(() -> this.overDueSendEmail(total, xEmpNo));
    }

    /**
     * 发送邮件
     *
     * @param total
     */
    public void overDueSendEmail(int total, String xEmpNo) {
        log.info("############sendEmail开始调用");
        //删除邮件预警备份数据
        overdueMaterialsRepository.deleteDelayRecheckMaterialEmailBack();
        //把三次超期复检邮件预警信息表数据插入到备份表
        overdueMaterialsRepository.insertDelayRecheckMaterialEmailBack();
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000044);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getOverdueSysList(sysLookupValuesDTO);
        if (Tools.isEmpty(sysLookupValuesDTOList)) {
            return;
        }
        List<String> users = sysLookupValuesDTOList.stream().map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());
        String receipts = StringUtils.join(
                users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
        try {
            //如果为空，则发一则空邮件给接收人
            if (total == INT_0) {
                emailUtil.sendMail(receipts, INFOR_OVERDUE_MATERIAL_MONITOR, BLANK, INFOR_OVERDUE_MATERIAL_CONTANT, BLANK);
                return;
            } else {
                //上传文档云发邮件
                this.clodDiskSendEmail(receipts, xEmpNo);
            }
        } catch (Exception e) {
            log.error(" 三次超期邮件预警查询结果excel失败 " + e.getMessage());
            emailUtil.sendMail(receipts, INFOR_OVERDUE_MATERIAL_MONITOR, BLANK, INFOR_OVERDUE_MATERIAL_FAILED, BLANK);
        }
        log.info("############sendEmail結束调用");
    }

    /**
     * 上传文档云发邮件
     *
     * @param receipts
     * @param xEmpNo
     * @throws Exception
     */
    public void clodDiskSendEmail(String receipts, String xEmpNo) throws Exception {
        //上传文档云发邮件给接收人
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            OverDueMaterialsEmailListVO param = ((OverDueMaterialsEmailListVO) params).setStartRow(statRow).setEndRow(endRow).setEmailType(STR_NUMBER_TWO);
            return new ArrayList<>(overdueMaterialsRepository.selectOverdueMaterialsEmailList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(INFOR_OVERDUE_MATERIAL_MONITOR_NAME).setFileName(INFOR_OVERDUE_MATERIAL_MONITOR).setSheetName(SHEET1)
                .setQueryParams(new OverDueMaterialsEmailListVO()).setPojoClass(OverDueMaterialsEmailListVO.class).setTotal((long) INT_20001)
                .setReceipt(xEmpNo).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.makeBigExcelFileTemp(excelParams);
        if (!Tools.isEmpty(excelParams.getPath())) {
            CloudDiskHelper cloudDiskHelper = (CloudDiskHelper) SpringContextUtil.getBean(CLOUD_DISK);
            String fileKey = cloudDiskHelper.fileUpload(excelParams.getPath(), xEmpNo, CloudDiskHelper.MAX_RETRY_TIMES);
            FileUtil.del(excelParams.getPath());
            String downLoadUrl = ExcelUtil.getFileDownloadUrl(fileKey, (String) null, xEmpNo);
            emailUtil.sendMail(receipts, INFOR_OVERDUE_MATERIAL_MONITOR, STRING_EMPTY, INFOR_ALLCATE_EXCEPTION_MONITOR_DOWN + STR_BLANK + downLoadUrl, STRING_EMPTY);
        }
    }
}