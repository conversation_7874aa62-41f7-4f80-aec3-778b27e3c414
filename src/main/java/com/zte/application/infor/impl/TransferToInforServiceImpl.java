package com.zte.application.infor.impl;

import com.zte.application.infor.TransferToInforService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.XstreamUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


/**
 * <AUTHOR>
 */
@Service
public class
TransferToInforServiceImpl implements TransferToInforService {
    @Autowired
    StepTransferRepository stepTransfer;
    @Autowired
    OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;
    @Autowired
    private EmailUtil emailUtil;
    /**
     * 调拨单自动提交到infor
     */
    @Override
    public void transferToInfor() throws Exception {
        //查询上次任务是否结束
        int count = stepTransfer.getCountJobUnknown(JOB_AUTO_TRANSFER_BILL);
        if (count > INT_0) {
            return;
        }
        //查询待提交到infor的单据
        List<StepTransferBill> transferBillList = stepTransfer.getTransferBilToInfor();
        if (Tools.isEmpty(transferBillList)) {
            return;
        }
        //查询定时任务ID
        int jobId = stepTransfer.getJobId();
        //新增定时任务日志
        stepTransfer.insertJobHistory(JOB_AUTO_TRANSFER_BILL, jobId);
        List<String> getEmailBill = new ArrayList<>();
        List<String> getSuccessBill = new ArrayList<>();
        //提交到infor
        for (StepTransferBill stepTransferBil : transferBillList) {
            try {
                boolean isSuccess = sendInfor(stepTransferBil);
                if (isSuccess) {
                    //新增step接口数据
                    List<SoHeader> listSoHeader = stepTransfer.getTransferSoHead(stepTransferBil.getBillNo());
                    List<SoDetail> listSoDetail = stepTransfer.getTransferSoDe(stepTransferBil.getBillNo());
                    listSoDetail.stream().forEach(t -> t.setRef60(stepTransferBil.getBillNo()));
                    stepTransfer.insertTransferSoHead(listSoHeader);
                    //分批新增
                    List<List<SoDetail>> listOfDeList = CommonUtils.splitList(listSoDetail, NumConstant.BATCH_SIZE);
                    listOfDeList.forEach(k -> stepTransfer.insertTransferSoDetail(k));
                    getSuccessBill.add(stepTransferBil.getStockNo() + "&" +  stepTransferBil.getBillNo());
                } else {
                    //发送接口失败回滚数据
                    rollbackBill(stepTransferBil.getBillNo());
                    getEmailBill.add(stepTransferBil.getStockNo() + "&" +  stepTransferBil.getBillNo());
                }
                stepTransfer.updateJobHistory(jobId, TRANSFER_SUCCESS, "");
            } catch (Exception e) {
                stepTransfer.updateJobHistory(jobId, TRANSFER_FAILED, TRANSFER_MAKE_ERROR);
                rollbackBill(stepTransferBil.getBillNo());
                getEmailBill.add(stepTransferBil.getStockNo() + "&" +  stepTransferBil.getBillNo());
            }
        }
        //发送邮件
        String content = TRANSFER_SUCCESS_BILL + "\r\n" + StringUtils.join(getSuccessBill, "\r\n");
        content = content + "\r\n" + TRANSFER_FAILED_BILL + "\r\n" + StringUtils.join(getEmailBill, "\r\n");
        String receipts = getEmailUser();
        emailUtil.sendMail(receipts, TRANSFER_FAILED_WARN, TRANSFER_AUTO_BILL, content, TRANSFER_AUTO_BILL);

    }
    public String getEmailUser() {
        List<String> users = stepTransfer.getEmailUser(TRANSFER_EMAIL_USER);
        return StringUtils.join(
                users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList())
                ,SPLIT_22);
    }

    //回滚单据
    public void rollbackBill(String billNo) throws Exception {
        List<StepTransferDetail> transferDetailsList = stepTransfer.getTransferDetail(billNo);
        List<List<StepTransferDetail>> listOfList = CommonUtils.splitList(transferDetailsList, NumConstant.BATCH_SIZE);
        listOfList.forEach(k -> stepTransfer.updateDeliveryTransfer(k));
        stepTransfer.updateTransferStatus(billNo);
    }

    public boolean sendInfor(StepTransferBill stepTransferBil) throws Exception {
        ServiceData<?> res = WebServiceClient.submitInfor(onlineFallBackApplyBillRepository.getWsdlUrl(), getXmlMessage(stepTransferBil.getBillNo()));
        if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            return true;
        } else {
            return false;
        }
    }

    public String getXmlMessage(String billNo) throws Exception {
        String curTime = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(new Date());
        MsgHeader msgHeader = new MsgHeader();
        msgHeader.setSourceSystemId(TRANSFER_SOURCESYSTEMID);
        msgHeader.setSourceSystemName(TRANSFER_SOURCESYSTEMNAME);
        msgHeader.setUserId(TRANSFER_SOURCESYS_USER);
        msgHeader.setUserName(TRANSFER_SOURCESYS_USER);
        msgHeader.setSubmitDate(curTime);
        List<SoHeader> listSoHeader = stepTransfer.getTransferSoHead(billNo);
        List<SoDetail> listSoDetail = stepTransfer.getTransferSoDe(billNo);
        if (listSoHeader.size() != INT_1 || listSoDetail.isEmpty()) {
            return BLANK;
        }
        listSoHeader.get(INT_0).setExternalOrderKey2(billNo);
        listSoHeader.get(INT_0).setListSoDetail(listSoDetail);
        listSoHeader.get(INT_0).setRequestedShipDate(curTime);
        SoBizCont bizCont = new SoBizCont();
        bizCont.setSoHeader(listSoHeader.get(0));
        MsgBody<SoBizCont> msgBody = new MsgBody<>();
        msgBody.setServiceCode(TRANSFER_WEBSERVICE_CODE);
        msgBody.setMsgid(UUID.randomUUID().toString());
        msgBody.setBizCont(bizCont);
        MsgSend msgSend = new MsgSend();
        msgSend.setMsgHeader(msgHeader);
        msgSend.setMsgBody(msgBody);
        return XstreamUtil.toXML(msgSend);
    }


}
