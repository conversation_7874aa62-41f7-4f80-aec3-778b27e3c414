package com.zte.application.infor.impl;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.EccnStockRemoteService;
import com.zte.application.infor.SyncEccnStockDataService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.EccnStockRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.interfaces.infor.dto.EccnStock;
import com.zte.interfaces.infor.dto.EccnStockDTO;
import com.zte.interfaces.infor.dto.EccnStockLog;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.Normalizer;
import java.util.*;

import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.DateUtil.getTodayStartTime;

/**
 * <AUTHOR>
 */
@Service
public class SyncEccnStockDataServiceImpl implements SyncEccnStockDataService {
    @Autowired
    private EccnStockRepository eccnStockRepository;
    @Autowired
    private OnlineFallBackApplyBillRepository applyBillRepository;

    @Autowired
    private EccnStockRemoteService eccnStockRemoteService;
    @Override
    public void syncEccnStockData() {
        List<StSysLookupValuesDTO> lastUpdateValues = applyBillRepository.getEccnSysLookupValues(SYNC_ECCN_LOOKUP_TYPE,SYNC_ECCN_LOOKUP_CODE);
        // 数据字典值为空全量合，ECCN数据字典初始值设置为 2018-11-26 09:43:47。
        //判断数据字典是否设置初始值
        BusiAssertException.isEmpty(lastUpdateValues.get(INT_0).getLookupMeaning(),SYNC_ECCN_LOOKUP_CODE_NOT_CONFIG);
        // 设置startDate endDate
        String endDate = (Tools.isEmpty(lastUpdateValues.get(INT_0).getDescriptionEng())) ? getTodayStartTime() : lastUpdateValues.get(INT_0).getDescriptionEng();
        String startDate = lastUpdateValues.get(INT_0).getLookupMeaning();
        try {
            String paramsMap = setEccnParams(INT_1,startDate,endDate);
            long total = 0L;
            Map<String,Object> totalMap = eccnStockRemoteService.getEccnStockInfo(paramsMap);
            dealSynEccnData(endDate, startDate, total, totalMap);
        } catch (Exception e) {
            writeEccnRunErrorLog(e);
        }
    }

    public void writeEccnRunErrorLog(Exception e) {
        EccnStockLog eccnStockLog = new EccnStockLog();
        eccnStockLog.setSyncError(Constant.SYNCECCNSTOCKDATA+ e.getMessage());
        eccnStockRepository.insertEccnStockLog(eccnStockLog);
    }

    public void dealSynEccnData(String endDate, String startDate, long total, Map<String, Object> totalMap) throws IOException {
        String paramsMap;
        if(totalMap != null&& totalMap.get(TOATL)!=null){
            total = (long) totalMap.get(TOATL);
        }
        long pages = total % INT_1000 == INT_0 ? total / INT_1000 : total / INT_1000 + INT_1;
        // 循环页数
        for (long i = 1; i <= pages; i++) {
            paramsMap = setEccnParams(i, startDate, endDate);
            Map<String, Object> map = eccnStockRemoteService.getEccnStockInfo(paramsMap);
            List<EccnStockDTO> list = new ArrayList<>(INT_1);
            if(map !=null && map.get(LIST) !=null){
                 list = (List<EccnStockDTO>) map.get(LIST);
            }
            if(list != null && list.size()>0){
                saveEccnStock(list);
            }
        }
        applyBillRepository.updateLastUpdatedTime(endDate, SYNC_ECCN_LOOKUP_CODE);
        eccnSuccessLogs(endDate, startDate);
    }

    public void eccnSuccessLogs(String endDate, String startDate) {
        EccnStockLog eccnStockLog = new EccnStockLog();
        eccnStockLog.setSyncError(Constant.EXCUTE_SUCCESS + FROM + startDate + TO + endDate);
        eccnStockRepository.insertEccnStockLog(eccnStockLog);
    }

    public void saveEccnStock(List<EccnStockDTO> eccnStockDTOList) {
        List<EccnStock> eccnInsertList = new ArrayList<>();
        List<EccnStock> eccnUpdateList = new ArrayList<>();
        for (EccnStockDTO eccnStockDTO : eccnStockDTOList) {
            EccnStock entity = EccnStock.toEntity(eccnStockDTO);

            EccnStock eccnStock2 = new EccnStock();
            eccnStock2.setItemNo(eccnStockDTO.getItemNo());
            eccnStock2.setOrgId(eccnStockDTO.getOrgId());
            eccnStock2.setVersion(eccnStockDTO.getVersion());
            List<EccnStock> eccnStockList = eccnStockRepository.selectEccnStockAll(eccnStock2);

            entity.setVersion(StringUtils.substring(entity.getItemNo(),INT_0,INT_1).equals(STR_NUMBER_ONE) ? entity.getVersion() : " ");
            if (Tools.isNotEmpty(eccnStockList)) {
                entity.setRecordId(eccnStockList.get(INT_0).getRecordId());
                eccnUpdateList.add(entity);
            } else {
                eccnInsertList.add(entity);
            }
        }

        startSaveEccnStock(eccnInsertList, eccnUpdateList);
    }

    public void startSaveEccnStock(List<EccnStock> eccnInsertList, List<EccnStock> eccnUpdateList) {
        if (Tools.isNotEmpty(eccnUpdateList)) {
            this.updateEccnStocks(eccnUpdateList);
        }
        if (Tools.isNotEmpty(eccnInsertList)) {
            this.insertEccnStocks(eccnInsertList);
        }
    }

    /**
     * 更新eccnstock
     */
    public void updateEccnStocks(List<EccnStock> eccnList) {
        List<List<EccnStock>> listOfList = CommonUtils.splitList(eccnList, INT_500);
        for (List<EccnStock> paramsList : listOfList) {
            eccnStockRepository.updateEccnStocks(paramsList);
        }
    }

    /**
     * 插入
     */
    public void insertEccnStocks(List<EccnStock> eccnList) {
        List<List<EccnStock>> listOfList = CommonUtils.splitList(eccnList, INT_100);
        for (List<EccnStock> paramsList : listOfList) {
            eccnStockRepository.insertEccnStocks(paramsList);
        }
    }

    /**
     * 设置参数
     */
    public String setEccnParams(long currentPage, String startDate, String endDate) {

        EccnStockLog eccnStockLog = new EccnStockLog();
        try {
            List<String> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>(16);
            map.put("currentPage", currentPage);
            map.put("itemList", list);
            map.put("lastupDateFrom", startDate);
            map.put("lastupDateTo", endDate);
            map.put("orgId",ORG_ID_0000);
            map.put("pageSize", INT_1000);
            return JacksonJsonConverUtil.beanToJson(map);
        } catch (Exception e) {
            eccnStockLog.setSyncError(SETINITPARAMS + e.getMessage());
            eccnStockRepository.insertEccnStockLog(eccnStockLog);
        }
        return "";
    }

}
