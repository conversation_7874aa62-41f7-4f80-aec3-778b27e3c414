package com.zte.application.infor.impl;

import com.zte.application.infor.IBondedWarehouseService;
import com.zte.domain.model.infor.BondedWarehouseInventoryInfo;
import com.zte.domain.model.infor.EdiAsnqcSRepository;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Deacription 保税仓service实现
 * <AUTHOR>
 * @Date 2020/8/18 16:46
 **/
@Service
public class BondedWarehouseServiceImpl implements IBondedWarehouseService {

    @Autowired
    private EdiAsnqcSRepository ediAsnqcSRepository;

    @Override
    public ServiceData<List<BondedWarehouseInventoryInfo>> queryBondedWarehouseInventory(List<BondedWarehouseInventoryInfo> inventoryInfoList) {
        ServiceData<List<BondedWarehouseInventoryInfo>> serviceData = new ServiceData<>();
        if (CommonUtils.isEmpty(inventoryInfoList)) {
            serviceData.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
            return serviceData;
        }
        for (BondedWarehouseInventoryInfo inventoryInfo : inventoryInfoList) {
            if (CommonUtils.isBlankAny(inventoryInfo.getItemBarcode(), inventoryInfo.getStockNo())) {
                serviceData.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID));
                return serviceData;
            }
        }
        List<BondedWarehouseInventoryInfo> inventoryInfoListInDb = ediAsnqcSRepository.queryBondedWarehouseInventory(inventoryInfoList);
        // 先按照仓库编号升序排序，再按照22条码升序，再按照数量升序
        inventoryInfoListInDb = inventoryInfoListInDb.stream().sorted(Comparator.comparing(BondedWarehouseInventoryInfo::getStockNo)
                .thenComparing(BondedWarehouseInventoryInfo::getItemBarcode)
                .thenComparing(BondedWarehouseInventoryInfo::getQty))
                .collect(Collectors.toList());
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        serviceData.setBo(inventoryInfoListInDb);
        return serviceData;
    }
}