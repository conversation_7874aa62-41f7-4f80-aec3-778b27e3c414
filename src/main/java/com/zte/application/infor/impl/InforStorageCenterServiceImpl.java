/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.infor.InforStorageCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;

import static com.zte.common.model.MessageId.*;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年11月18日 <br>
 * @see com.zte.application.infor.impl <br>
 */

@Service
public class InforStorageCenterServiceImpl implements InforStorageCenterService {

	private static final Logger LOGGER = LoggerFactory.getLogger(InforStorageCenterService.class);

	@Autowired
	private InforStorageCenterRepository inforStorageCenterRepository;
	@Autowired
	private OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

	@Autowired
	private EdiPoSRepository ediPoSRepository;

	/**
	 * 定时捞取INFOR已经出库完成的HZ单写到日志表
	 */
	@Override
	public void getSoByBillTypeHz(String externalOrderkey2, String orderKey) {

		HZBillHead hzBillHead = new HZBillHead();
		if (Tools.isNotEmpty(orderKey)) {
			hzBillHead.setSrcBillNo(orderKey);
		}
		if (Tools.isNotEmpty(externalOrderkey2)) {
			hzBillHead.setRelatedBillNo(externalOrderkey2);
		}

		// 先捞取所有满足条件的单，然后逐条判断是否全部出库，如果是，则写入到日志表，否则不写
		List<HZBillHead> list = inforStorageCenterRepository.getAllHZBill(hzBillHead);
		if (Tools.isEmpty(list)) {
			return;
		}

		for (HZBillHead hzBillHead1 : list) {
			int isSo = inforStorageCenterRepository.checkIsSo(hzBillHead1.getWmwhseId(), hzBillHead1.getSrcBillNo());
			if (isSo > NumConstant.INT_0) {
				inforStorageCenterRepository.insertByBillTypeHZ(hzBillHead1);
			}
		}
	}

	/**
	 * 定时捞取终端备件的数据推送给仓储中心
	 */
	@Override
	public void pushHzBillToStorageCenter(String xEmpNo, String xAuthValue, String externalOrderkey2,
													String orderKey) {
		HZBillHead hzBillHead = new HZBillHead();
		if (Tools.isNotEmpty(orderKey)) {
			hzBillHead.setSrcBillNo(orderKey);
		}
		if (Tools.isNotEmpty(externalOrderkey2)) {
			hzBillHead.setRelatedBillNo(externalOrderkey2);
		}

		List<HZBillDTO> hzBillDTOList = getAndCombineParam(hzBillHead);

		if (Tools.isNotEmpty(hzBillDTOList)) {
			pushDataToStorageCenter(hzBillDTOList, xEmpNo, xAuthValue);
		}
	}

	/**
	 * 推送数据给仓储中心
	 */
	public void pushDataToStorageCenter(List<HZBillDTO> hzBillDTOList, String xEmpNo, String xAuthValue) {
		for (HZBillDTO hzBillDTO : hzBillDTOList) {
			Map<String, String> mapHeader = new HashMap<>(10);
			mapHeader.put(Constant.X_TENANT_ID, NumConstant.STR_10001);
			mapHeader.put(Constant.X_EMP_NO, xEmpNo);
			mapHeader.put(Constant.X_AUTH_VALUE, xAuthValue);

			ServiceData<?> invokeMsg = RemoteServiceDataUtil.invokeService(Constant.ZTE_ISS_STOCK_CENTER,
					MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, Constant.ZTE_ISS_STOCK_CENTER_ENTRY,
					JsonUtil.toJSONString(hzBillDTO), mapHeader);

			StorageCenterEdiLog storageCenterEdiLog = new StorageCenterEdiLog();

			String requestParam = JsonUtil.toJSONString(hzBillDTO);
			if (requestParam.length() > NumConstant.INT_2000) {
				requestParam = requestParam.substring(NumConstant.INT_0, NumConstant.INT_2000);
			}

			String responseParam = JsonUtil.toJSONString(invokeMsg);
			if (responseParam.length() > NumConstant.INT_2000) {
				responseParam = responseParam.substring(NumConstant.INT_0, NumConstant.INT_2000);
			}

			int isSend = NumConstant.INT_0;
			if (!Constant.SUCESS_CODE.equals(invokeMsg.getCode().getCode())) {
				isSend = NumConstant.INT_2;
			}

			storageCenterEdiLog.setOrderKey(hzBillDTO.getBillHead().getSrcBillNo());
			storageCenterEdiLog.setExternalOrderkey2(hzBillDTO.getBillHead().getRelatedBillNo());
			storageCenterEdiLog.setServiceCode(invokeMsg.getCode().getCode());
			storageCenterEdiLog.setIsSend(isSend);
			storageCenterEdiLog.setRequestParam(requestParam);
			storageCenterEdiLog.setResponseParam(responseParam);

			inforStorageCenterRepository.updateStorageCenterEdiLog(storageCenterEdiLog);
		}
	}

	/**
	 * 获取并组装好待推送的参数
	 */
	public List<HZBillDTO> getAndCombineParam(HZBillHead hzBillHead) {
		List<HZBillDTO> hzBillDTOList = new ArrayList<>();

		// 1、先从日志表捞取待推送的数据(orderKey和externalOrderkey2)
		List<HZBillHead> unPushedDataList = inforStorageCenterRepository.getUnPushedData(hzBillHead);
		if (Tools.isEmpty(unPushedDataList)) {
			return hzBillDTOList;
		}

		// 2、获取头信息
		List<HZBillHead> hzBillHeadList = inforStorageCenterRepository.getHZBillHead(unPushedDataList);

		// 3、获取明细信息
		List<HZBillDetail> hzBillDetailList = inforStorageCenterRepository.getHZBillDetail(unPushedDataList);

		// 4、获取erp缓存list
		List<EdiCacheList> ediCacheList = inforStorageCenterRepository.getEdiCacheList(unPushedDataList);

		return combineData(hzBillHeadList, hzBillDetailList, ediCacheList);
	}

	public List<HZBillDTO> combineData(List<HZBillHead> hzBillHeadList, List<HZBillDetail> hzBillDetailList,
									   List<EdiCacheList> ediCacheList) {
		List<HZBillDTO> hzBillDTOList = new ArrayList<HZBillDTO>();

		for (HZBillDetail hZBillDetail2 : hzBillDetailList) {
			if (NumConstant.STR_01.equals(hZBillDetail2.getType())) {
				List<EdiCacheList> ediCacheList2 = ediCacheList.stream()
						.filter(e -> ((hZBillDetail2.getRelatedBillNo() + hZBillDetail2.getSrcBillNo()
								+ hZBillDetail2.getReelid())
								.equals(e.getRelatedBillNo() + e.getSrcBillNo() + e.getReelid())))
						.collect(Collectors.toList());

				hZBillDetail2.setEdiCacheList(ediCacheList2);
			} else if (NumConstant.STR_02.equals(hZBillDetail2.getType())) {
				List<EdiCacheList> ediCacheList2 = ediCacheList.stream()
						.filter(e -> ((hZBillDetail2.getRelatedBillNo() + hZBillDetail2.getSrcBillNo()
								+ hZBillDetail2.getLineIndex())
								.equals(e.getRelatedBillNo() + e.getSrcBillNo() + e.getLineIndex())))
						.collect(Collectors.toList());

				hZBillDetail2.setEdiCacheList(ediCacheList2);
			} else {
				List<EdiCacheList> ediCacheList2 = ediCacheList.stream()
						.filter(e -> ((hZBillDetail2.getRelatedBillNo() + hZBillDetail2.getSrcBillNo()
								+ hZBillDetail2.getReelid())
								.equals(e.getRelatedBillNo() + e.getSrcBillNo() + e.getReelid())))
						.collect(Collectors.toList());

				hZBillDetail2.setEdiCacheList(ediCacheList2);
				hZBillDetail2.setReelid(null);
			}
		}

		HZBillDTO hzBillDTO = null;
		for (HZBillHead hzBillHead2 : hzBillHeadList) {
			List<HZBillDetail> listHZBillDetail = hzBillDetailList.stream()
					.filter(e -> ((hzBillHead2.getRelatedBillNo() + hzBillHead2.getSrcBillNo())
							.equals(e.getRelatedBillNo() + e.getSrcBillNo())))
					.collect(Collectors.toList());

			hzBillDTO = new HZBillDTO();
			hzBillDTO.setBillHead(hzBillHead2);
			hzBillDTO.setBillDetail(listHZBillDetail);

			hzBillDTOList.add(hzBillDTO);
		}

		return hzBillDTOList;
	}

	@Override
	public ServiceData<?> selectSoOutBoundInfo(SoOutBoundDTO boundDTO) {
		String beginDate = boundDTO.getBeginDate();
		String endDate = boundDTO.getEndDate();
		if (StringUtils.isEmpty(beginDate) && StringUtils.isEmpty(endDate)) {
			if (StringUtils.isEmpty(boundDTO.getBillNo()) && StringUtils.isEmpty(boundDTO.getItemNo())
			&& StringUtils.isEmpty(boundDTO.getProdplanId()) && StringUtils.isEmpty(boundDTO.getId())
			&& StringUtils.isEmpty(boundDTO.getReelId()) ) {
				return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_SOOUTDETAIL_QUERY_VAL1));
			}
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			if(!StringUtils.isEmpty(beginDate)) {
				dateFormat.parse(beginDate);
			}
			if(!StringUtils.isEmpty(endDate)) {
				dateFormat.parse(endDate);
			}

		} catch (ParseException e) {
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_SOOUTDETAIL_QUERY_VAL2));
		}
		List<EdiSosReelidDetail> list=new ArrayList<>();
		List<HeaderEdiSoS> ediSoS = inforStorageCenterRepository.selectEdiSoS(boundDTO);
		for (HeaderEdiSoS headerEdiSoS : ediSoS) {
			headerEdiSoS.setReelId(boundDTO.getReelId());
			if(!StringUtils.isEmpty(boundDTO.getReelId())){
				headerEdiSoS.setReelidProcess(Constant.INT_1);
			}else {
				long reelIdCount = inforStorageCenterRepository.selectSkuIsReelid(headerEdiSoS);
				headerEdiSoS.setReelidProcess((int)reelIdCount);
			}
			list.addAll(inforStorageCenterRepository.selectEdiSoSDetail(headerEdiSoS));
		}
		if(list.size()<=0) {
			return ServiceDataUtil.getBusinessError("No Data");
		}

		return ServiceDataUtil.getSuccess(list);
	}

	@Override
	public ServiceData<?> selectEdiSosInfo(SoOutBoundDTO boundDTO) {
		String billNo = boundDTO.getBillNo();
		if (StringUtils.isEmpty(billNo)) {
			return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_EDISOSDETAIL_QUERY_VAL1));
		}
		List<HeaderEdiSoS> ediSoS = inforStorageCenterRepository.selectEdiSoS(boundDTO);
		if (ediSoS.size()<=0){
			return ServiceDataUtil.getBusinessError("No Data");
		}
		List<EdiSoSInfoDTO> list = inforStorageCenterRepository.selectEdiSoSInfo(ediSoS.get(NumConstant.INT_0));
		if(list.size()<=0) {
			return ServiceDataUtil.getBusinessError("No Data");
		}

		return ServiceDataUtil.getSuccess(list);
	}

	/**
	 * 定时捞取INFOR已经出库完成的逆向报废单据并写到日志表
	 */
	@Override
	public void getSoByReverseScrap(String externalOrderkey2, String orderkey) {
		ReqHeadReqInstanceDTO dto = new ReqHeadReqInstanceDTO();
		if (Tools.isNotEmpty(orderkey)) {
			dto.setOrderkey(orderkey);
		}
		if (Tools.isNotEmpty(externalOrderkey2)) {
			dto.setExternalorderkey2(externalOrderkey2);
		}

		// 先捞取所有满足条件的单，然后逐条判断是否全部出库，如果是，则写入到日志表，否则不写
		List<ReqHeadReqInstanceDTO> list = inforStorageCenterRepository.getAllReverseScrap(dto);
		if (Tools.isEmpty(list)) {
			return;
		}

		for (ReqHeadReqInstanceDTO reqHeadReqInstanceDTO : list) {
			int isSo = inforStorageCenterRepository.checkIsSo(reqHeadReqInstanceDTO.getWmwhseId(), reqHeadReqInstanceDTO.getOrderkey());
			if (isSo > NumConstant.INT_0) {
				inforStorageCenterRepository.insertReverseScrap(reqHeadReqInstanceDTO);
			}
		}
	}

	/**
	 * 定时捞取逆向报废的数据推送给仓储中心
	 */
	@Override
	public void pushReverseScrapBillToStorageCenter(String xEmpNo, String externalOrderkey2, String orderKey) {

		ReqHeadReqInstanceDTO dto = new ReqHeadReqInstanceDTO();
		if (Tools.isNotEmpty(orderKey)) {
			dto.setOrderkey(orderKey);
		}
		if (Tools.isNotEmpty(externalOrderkey2)) {
			dto.setExternalorderkey2(externalOrderkey2);
		}

		List<ReqHeadAndDetailReqParamsDTO> list = this.getReqHeadAndDetailReqParams(dto);

		if (Tools.isNotEmpty(list)) {
			this.pushReverseScrapDataToStorageCenter(list, xEmpNo);
		}
	}

	/**
	 * 组装需要推送给仓储中心的逆向报废数据
	 * @param dto
	 * @return
	 */
	public List<ReqHeadAndDetailReqParamsDTO> getReqHeadAndDetailReqParams(ReqHeadReqInstanceDTO dto) {

		List<ReqHeadAndDetailReqParamsDTO> list = new ArrayList<>();

		// 从日志表捞取待推送的数据
		List<ReqHeadReqInstanceDTO> unPushedDataList = inforStorageCenterRepository.getUnPushedReverseScrapData(dto);
		if (Tools.isEmpty(unPushedDataList)) {
			return list;
		}

		// 获取头信息
		List<ReqHeadReqInstanceDTO> reqHeadReqInstanceDTOList = inforStorageCenterRepository.getReqHeadReqInstance(unPushedDataList);
		if (Tools.isEmpty(reqHeadReqInstanceDTOList)) {
			return list;
		}

		// 获取明细信息
		List<ReqDetailReqInstanceDTO> reqDetailReqInstanceDTOList = inforStorageCenterRepository.getReqDetailReqInstance(unPushedDataList);
		if (Tools.isEmpty(reqDetailReqInstanceDTOList)) {
			return list;
		}

		// 获取INFOR仓库与标准仓对应关系配置
		List<StSysLookupValuesDTO> stSysLookupValuesDTOList = onlineFallBackApplyBillRepository.getSysLookupValuesByOrder(NumConstant.STR_1000037);
		BusiAssertException.isEmpty(stSysLookupValuesDTOList, MessageId.SYS_LOOKUP_VALUES_1000037_NOT_EXIST);

		for (ReqHeadReqInstanceDTO reqHeadReqInstanceDTO : reqHeadReqInstanceDTOList) {
			List<StSysLookupValuesDTO> stSysLookupValues = stSysLookupValuesDTOList.stream().filter(t ->
					t.getLookupMeaning().equals(reqHeadReqInstanceDTO.getWmwhseId())).collect(Collectors.toList());
			if (Tools.isEmpty(stSysLookupValues)) {
				continue;
			}
			reqHeadReqInstanceDTO.setToWarehouseId(stSysLookupValues.get(NumConstant.INT_0).getDescriptionChin());
			List<ReqDetailReqInstanceDTO> detailReqInstanceDTOList = reqDetailReqInstanceDTOList.stream()
					.filter(e -> ((reqHeadReqInstanceDTO.getExternalorderkey2() + reqHeadReqInstanceDTO.getOrderkey())
							.equals(e.getExternalorderkey2() + e.getOrderkey()))).collect(Collectors.toList());

			ReqHeadAndDetailReqParamsDTO reqHeadAndDetailReqParamsDTO = ReqHeadAndDetailReqParamsDTO.builder().build()
					.setBillHead(reqHeadReqInstanceDTO).setBillDetail(detailReqInstanceDTOList);
			list.add(reqHeadAndDetailReqParamsDTO);
		}
		return list;
	}

	/**
	 * 推送数据给仓储中心
	 */
	public void pushReverseScrapDataToStorageCenter(List<ReqHeadAndDetailReqParamsDTO> reqHeadAndDetailReqParamsDTOList, String xEmpNo) {
		for (ReqHeadAndDetailReqParamsDTO reqHeadAndDetailReqParamsDTO : reqHeadAndDetailReqParamsDTOList) {
			Map<String, String> map = new HashMap<>(NumConstant.INT_16);
			map.put(Constant.X_TENANT_ID, NumConstant.STR_10001);
			map.put(Constant.X_EMP_NO, xEmpNo);

			StorageCenterEdiLog storageCenterEdiLog = new StorageCenterEdiLog();
			storageCenterEdiLog.setOrderKey(reqHeadAndDetailReqParamsDTO.getBillHead().getOrderkey());
			storageCenterEdiLog.setExternalOrderkey2(reqHeadAndDetailReqParamsDTO.getBillHead().getExternalorderkey2());

			reqHeadAndDetailReqParamsDTO.getBillHead().setExternalorderkey2(null).setOrderkey(null).setWmwhseId(null);
			reqHeadAndDetailReqParamsDTO.getBillDetail().forEach(i -> {
				i.setExternalorderkey2(null);
				i.setOrderkey(null);
			});

			ServiceData<?> invokeMsg = RemoteServiceDataUtil.invokeService(Constant.ZTE_SCM_WMS_SSTOCK,
					MicroServiceNameEum.VERSION, MicroServiceNameEum.SENDTYPEPOST, Constant.ZTE_SCM_WMS_SSTOCK_INBOUND,
					JsonUtil.toJSONString(reqHeadAndDetailReqParamsDTO), map);

			String requestParam = JsonUtil.toJSONString(reqHeadAndDetailReqParamsDTO);
			if (requestParam.length() > NumConstant.INT_2000) {
				requestParam = requestParam.substring(NumConstant.INT_0, NumConstant.INT_2000);
			}

			String responseParam = JsonUtil.toJSONString(invokeMsg);
			if (responseParam.length() > NumConstant.INT_2000) {
				responseParam = responseParam.substring(NumConstant.INT_0, NumConstant.INT_2000);
			}

			int isSend = NumConstant.INT_0;
			if (!Constant.SUCESS_CODE.equals(invokeMsg.getCode().getCode())) {
				isSend = NumConstant.INT_2;
			}

			storageCenterEdiLog.setServiceCode(invokeMsg.getCode().getCode());
			storageCenterEdiLog.setIsSend(isSend);
			storageCenterEdiLog.setRequestParam(requestParam);
			storageCenterEdiLog.setResponseParam(responseParam);

			inforStorageCenterRepository.updateStorageCenterEdiLog(storageCenterEdiLog);
		}
	}

	@Override
	public List<PoInBoundInfoDTO> selectEdiPosInfo(PoInBoundDTO inboundDTO) {
		// 入参校验
		List<String> itemNo = inboundDTO.getItemNo();
		String beginDate = inboundDTO.getInstoreBegindate();
		String endDate = inboundDTO.getInstoreEnddate();
		BusiAssertException.isTrue(
				Tools.isEmpty(itemNo) ||
						!hasNonEmptyItem(itemNo) ||
						StringUtils.isEmpty(beginDate) ||
						StringUtils.isEmpty(endDate),
				ITEMNO_BEGINDATE_ENDDATE_IS_NOT_EMPTY);
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			dateFormat.parse(beginDate);
			dateFormat.parse(endDate);
		} catch (ParseException e) {
			BusiAssertException.result(DATE_FORMAT);
		}
		int countLimit = ediPoSRepository.getTransCountLimit(Constant.LOOKUP_CODE_100008800001);
		BusiAssertException.isTrue(itemNo.size() > countLimit, QUANTITY_GREATER_LIMIT);

		List<PoInBoundInfoDTO> list = ediPoSRepository.getPoInboundInfo(inboundDTO);
		BusiAssertException.isTrue(Tools.isEmpty(list), NO_DATA_FOUND);

		return list;
	}

	private boolean hasNonEmptyItem(List<String> itemNo) {
		for (String item : itemNo) {
			if (item != null && !item.isEmpty()) {
				return true;
			}
		}
		return false;
	}
}
