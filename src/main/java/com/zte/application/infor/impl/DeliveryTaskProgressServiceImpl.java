package com.zte.application.infor.impl;

import com.zte.application.infor.DeliveryTaskProgressService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.DeliveryTaskProgressRepository;
import com.zte.interfaces.infor.dto.DeliverTaskProgressDTO;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.INT_0;
import static com.zte.common.utils.Constant.STRING_EMPTY;


/**
 * <AUTHOR>
 */
@Service
public class DeliveryTaskProgressServiceImpl implements DeliveryTaskProgressService {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    DeliveryTaskProgressRepository deliveryTaskProgressRepository;

    /* Started by AICoder, pid:eb1caf49ff4843c59c66207fc9c09160 */
    /**
     * 查询配送任务进展INFOR数据
     */
    @Override
    public List<DeliverTaskProgressDTO> deliveryTaskQuery(DeliverTaskProgressDTO deliveryTaskProgressDTO) {
        List<DeliverTaskProgressDTO> deliverTaskProgressDTOList = new ArrayList<>();
        List<DeliverTaskProgressDTO> orderList = deliveryTaskProgressRepository.queryOrderList(deliveryTaskProgressDTO.getExternoList());
        if (Tools.isEmpty(orderList)) {
            return deliverTaskProgressDTOList;
        }
        try {
            Map<String, List<DeliverTaskProgressDTO>> deliver = orderList.stream().collect(Collectors.groupingBy(DeliverTaskProgressDTO::getProdplanNo));
            deliver.forEach((k, v) -> {
                DeliverTaskProgressDTO dto = new DeliverTaskProgressDTO();
                DeliverTaskProgressDTO minTask = v.stream().min(Comparator.comparing(DeliverTaskProgressDTO::getDemandDate)).orElse(null);
                if (minTask != null) {
                    dto.setProdplanNo(k).setDemandDate(minTask.getDemandDate())
                            .setOutSupplier(Tools.transNullValue(minTask.getOutSupplier()))
                            .setInforLoc(Tools.transNullValue(minTask.getInforLoc()))
                            .setLineSideName(Tools.transNullValue(minTask.getLineSideName()))
                            .setIsComplete(Tools.transNullValue(minTask.getIsComplete()));
                } else {
                    dto.setProdplanNo(k).setDemandDate(null).setOutSupplier(STRING_EMPTY)
                            .setInforLoc(STRING_EMPTY).setLineSideName(STRING_EMPTY).setIsComplete(STRING_EMPTY);
                }

                //判断状态是否都是95，如果不是，取配送去向，如果是，赋值已完成，并且取最大的ActualShipDate
                boolean allStatus = v.stream().allMatch(obj -> obj.getStatus().equals(Constant.STR_95));
                if (!allStatus) {
                    String joinCompany = v.stream().filter(f -> Tools.isNotEmpty(f.getCompany())).map(DeliverTaskProgressDTO::getCompany).collect(Collectors.joining(","));
                    dto.setInstructionStatus(Arrays.asList(joinCompany.split(Constant.SPLIT)).stream().distinct().collect(Collectors.joining(",")));
                } else {
                    List<DeliverTaskProgressDTO> actualShipDatesList = v.stream().filter(t -> Tools.isNotEmpty(t.getActualShipDate())).collect(Collectors.toList());
                    DeliverTaskProgressDTO actual = actualShipDatesList.stream().max(Comparator.comparing(DeliverTaskProgressDTO::getActualShipDate)).orElse(null);
                    if (Tools.isNotEmpty(actual)) {
                        dto.setActualShipDate(actual.getActualShipDate());
                    }
                    dto.setInstructionStatus(Constant.STR_COMPLETE);
                }
                orderList.stream()
                        .filter(entity -> entity.getProdplanNo().equals(dto.getProdplanNo()))
                        .forEach(entity -> {
                            entity.setDemandDate(dto.getDemandDate())
                                    .setOutSupplier(Tools.transNullValue(dto.getOutSupplier()))
                                    .setInforLoc(Tools.transNullValue(dto.getInforLoc()))
                                    .setLineSideName(Tools.transNullValue(dto.getLineSideName()))
                                    .setIsComplete(Tools.transNullValue(dto.getIsComplete()))
                                    .setInstructionStatus(dto.getInstructionStatus())
                                    .setActualShipDate(dto.getActualShipDate());
                        });
                //获取仓库对应的订单状态
                getOrderStatusByWh(k, v, orderList);

            });
        } catch (Exception e) {
            logger.error("deliveryTaskQuery Exception : ", e);
            e.printStackTrace();
        }
        return orderList;
    }
    /* Ended by AICoder, pid:eb1caf49ff4843c59c66207fc9c09160 */

    private void getOrderStatusByWh(String exterNo, List<DeliverTaskProgressDTO> exterNoList, List<DeliverTaskProgressDTO> orderList) {
        exterNoList.forEach(e -> {
            boolean status02 = Arrays.stream(e.getStatus().split(Constant.SPLIT)).allMatch(s -> s.equals(NumConstant.STR_02));
            boolean status95 = Arrays.stream(e.getStatus().split(Constant.SPLIT)).allMatch(s -> s.equals(Constant.STR_95));
            Integer shortCount = INT_0;
            if (!status02 && !status95) {
                //查询是否缺料
                shortCount = deliveryTaskProgressRepository.queryShortCount(e.getWhseId(), exterNo);
            }
            String statusName = status02 ? Constant.STR_TOSTART : status95 ? Constant.STR_COMPLETED : (Constant.INT_0 == shortCount) ? Constant.STR_OUTBOUND : Constant.STR_SHORTBOUND;
            orderList.stream()
                    .filter(entity -> entity.getProdplanNo().equals(exterNo) && entity.getWhseId().equals(e.getWhseId()))
                    .forEach(entity -> {
                        entity.setStatus(Tools.transNullValue(statusName));
                    });
        });
    }
}
