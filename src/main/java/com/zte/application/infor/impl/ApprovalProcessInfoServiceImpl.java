package com.zte.application.infor.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.ApprovalProcessInfoService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.ApprovalProcessInforRepository;
import com.zte.domain.model.step.ApprovalProcessInfoRepository;
import com.zte.interfaces.step.dto.ApprovalProcessInfoDTO;
import com.zte.interfaces.step.dto.ApprovalProcessLogDTO;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.zte.common.utils.Constant.UNLOCK_URL;
import static com.zte.resourcewarehouse.common.constant.ResponseConstant.ApplicationName.ZTE_MES_RESOURCEWAREHOUSE_BILL;
import static com.zte.resourcewarehouse.common.constant.ResponseConstant.ApplicationName.ZTE_SCM_INFOR_MOVE;


/*
 * <AUTHOR>
 * 移动审批服务实现
 * */
@Service
public class ApprovalProcessInfoServiceImpl implements ApprovalProcessInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApprovalProcessInfoServiceImpl.class);
    @Autowired
    ApprovalProcessInfoRepository approvalProcessInfoRepository;
    @Autowired
    ApprovalProcessInforRepository inforRepository;

    @Value("${review.flow.temp.code}")
    private String revieflowTempCode;

    @Value("${review.flow.temp.code.detail}")
    private String revieflowTempCodeDetail;

    @Value("${review.appId}")
    private String reviewAppId;

    @Override
    public  void  dealScatterMachine(String key, JSONObject data, String appCode,String val)throws Exception{

        String flowCode = data.getString(Constant.FLOWCODE);
        String result = data.getString(Constant.RESULT);
        String businessId = data.getString(Constant.BUSINESSID);
        String approver = data.getString(Constant.APPROVER);

        if(StringUtils.isEmpty(result) || StringUtils.isEmpty(flowCode)
                || StringUtils.isEmpty(businessId) || StringUtils.isEmpty(approver)){
            return;
        }

        ApprovalProcessLogDTO dtoLog = new ApprovalProcessLogDTO();
        if(!StringUtils.isEmpty(val) && val.length()> Constant.INT_3000) {
            dtoLog.setResponse(val.substring(Constant.INT_0,Constant.INT_3000));
        }else {
            dtoLog.setResponse(val);
        }
        dtoLog.setBillNO(businessId);
        dtoLog.setInterfaceName(Constant.MOBILEAPPROVALKAFUKA);
        try {
            approvalProcessInfoRepository.insertMobileApprovalLog(dtoLog);
        }catch (Exception e){}

        ApprovalProcessInfoDTO dto =new ApprovalProcessInfoDTO();
        dto.setBillNO(businessId);
        dto.setLastUpdatedBy(approver);

        if(Constant.OMS_SCATTER_MACHINE_MATERIAL_IN.equals(flowCode)){
            this.updateInforMachineMaterialIn(businessId,result,dto);
        }else if(Constant.OMS_SCATTER_MACHINE_OUT.equals(flowCode)){
            this.updateInforMachineOut(businessId,result,dto);
        } else if(Constant.OMS_SCATTER_MACHINE_BOARD_IN.equals(flowCode)){
            this.updateInforMachineBoardIn(businessId,result,dto);
        }
        else {
            this.updateHoldRecordStatus(appCode,data,key,dto);
        }
    }

    @Override
    public  void  ReviewDealScatterMachine(String key, JSONObject data, String val)throws Exception{
        String flowCode = data.getString(Constant.FLOWCODE);
        String businessId = data.getString(Constant.BUSINESSID);
        ApprovalProcessLogDTO dtoLog = new ApprovalProcessLogDTO();
        if(!StringUtils.isEmpty(val) && val.length()> Constant.INT_3000) {
            dtoLog.setResponse(val.substring(Constant.INT_0,Constant.INT_3000));
        }else {
            dtoLog.setResponse(val);
        }
        dtoLog.setBillNO(businessId);
        dtoLog.setInterfaceName(Constant.REVIEW_TOPIC);
        try {
            approvalProcessInfoRepository.insertMobileApprovalLog(dtoLog);
        }catch (Exception e){}

        if (revieflowTempCode.equals(flowCode) || revieflowTempCodeDetail.equals(flowCode)){
            this.updateDelayRecheckBillByNo(businessId,key,flowCode);
        }
    }

    /***
     * 回调评审中心三次超期复检状态及提交复检
     * @param businessId
     * @param key
     * @param flowCode
     */
    private void updateDelayRecheckBillByNo(String businessId,String key,String flowCode){
        LOGGER.info("######三次超期复检审批kafka回调开始######"+key+businessId+flowCode);
        JSONObject jsonObj = new JSONObject();
        jsonObj.put(Constant.RECHECK_NO, businessId);
        jsonObj.put(Constant.TASK_STATUS, Constant.NEW);
        if(key.equals(reviewAppId+Constant.REVIEW_ENDED)){
            //审批结束
            jsonObj.put(Constant.IS_APPROVAL, Constant.STR_NUMBER_ZERO);//流程审批结束标识无需在审批（提交复检后会出现IQC失败）
            RemoteServiceDataUtil.invokeService(ZTE_MES_RESOURCEWAREHOUSE_BILL, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, Constant.UPDATE_DELAY_URL, JSONObject.toJSONString(jsonObj), Tools.newHashMap());

            JSONObject sendJsonObj = new JSONObject();
            sendJsonObj.put(Constant.RECHECK_NO, businessId);
            sendJsonObj.put(Constant.OPER_TYPE, Constant.MANUAL);
            if(flowCode.equals(revieflowTempCode)){
                RemoteServiceDataUtil.invokeService(ZTE_MES_RESOURCEWAREHOUSE_BILL, MicroServiceNameEum.VERSION,
                        MicroServiceNameEum.SENDTYPEPOST, Constant.SUBMIT_RECHECK_BILL_URL, JSONObject.toJSONString(sendJsonObj), Tools.newHashMap());
            }else{
                RemoteServiceDataUtil.invokeService(ZTE_MES_RESOURCEWAREHOUSE_BILL, MicroServiceNameEum.VERSION,
                        MicroServiceNameEum.SENDTYPEPOST, Constant.SUBMIT_DETAIL_URL, JSONObject.toJSONString(sendJsonObj), Tools.newHashMap());
            }
        }else if (key.equals(reviewAppId+Constant.REVIEW_TURN_DOWN)){
            //审批驳回
            RemoteServiceDataUtil.invokeService(ZTE_MES_RESOURCEWAREHOUSE_BILL, MicroServiceNameEum.VERSION,
                    MicroServiceNameEum.SENDTYPEPOST, Constant.UPDATE_DELAY_URL, JSONObject.toJSONString(jsonObj), Tools.newHashMap());
        }

        LOGGER.info("######三次超期复检审批kafka回调开始######"+key+businessId+flowCode);
    }

    private void updateInforMachineMaterialIn(String businessId,String result, ApprovalProcessInfoDTO dto){
        String status = approvalProcessInfoRepository.queryInforMachineMaterialIn(businessId);
        if(Constant.SUBMITTED.equals(status)){
            dto.setStatus(Constant.FLAG_Y.equals(result) ? Constant.TESTING : Constant.SAVED);
            approvalProcessInfoRepository.updateInforMachineMaterialIn(dto);
            approvalProcessInfoRepository.updateInforMachineMaterialInDetail(dto);
        }
    }
    private void updateInforMachineOut(String businessId,String result, ApprovalProcessInfoDTO dto){
        String status = approvalProcessInfoRepository.queryInforMachineOut(businessId);
        if(Constant.SUBMITTED.equals(status)){
            dto.setStatus(Constant.FLAG_Y.equals(result) ? Constant.RECHECK : Constant.SAVED);
            approvalProcessInfoRepository.updateInforMachineOut(dto);
            approvalProcessInfoRepository.updateInforMachineOutDetail(dto);
        }
    }
    private void updateInforMachineBoardIn(String businessId,String result, ApprovalProcessInfoDTO dto){
        String status = approvalProcessInfoRepository.queryInforMachineBoardIn(businessId);
        if (Constant.SUBMITTED.equals(status)) {
            dto.setStatus(Constant.FLAG_Y.equals(result) ? Constant.TESTING : Constant.SAVED);
            approvalProcessInfoRepository.updateInforMachineBoardIn(dto);
            approvalProcessInfoRepository.updateInforMachineBoardInDetail(dto);
        }
    }

    /**
     * 回调审批中心库存解冻和冻结
     * @param appCode
     * @param data
     * @param key
     * @param dto
     */
    /* Started by AICoder, pid:d8de2044afa9466caef85f0e34333ca3 */
    private void updateHoldRecordStatus(String appCode, JSONObject data, String key, ApprovalProcessInfoDTO dto) {
        LOGGER.info("######库存隔离kafka回调开始######");
        String holdFlowCode = data.getString(Constant.FLOWCODE);
        String holdresult = data.getString(Constant.RESULT);
        String finalResult;
        if (Tools.equals(key, appCode + Constant.HORIZON + Constant.TASK_COMPLETED) && Tools.equals(holdresult, Constant.FLAG_N)) {
            finalResult = Constant.FLAG_N;
        } else if (Tools.equals(key, appCode + Constant.HORIZON + Constant.TASK_COMPLETED) && data.getString(Constant.NODE_NAME).equals(Constant.APPROVAL_STQE) && Tools.equals(holdresult, Constant.FLAG_Y)) {
            finalResult = Constant.FLAG_Y;
        } else if (Tools.equals(key, appCode + Constant.HORIZON + Constant.TASK_COMPLETED) && !Constant.IWMS_INVENTORY_REMOVE_HOLD_QUALITY.equals(holdFlowCode) && Tools.equals(holdresult, Constant.FLAG_Y)) {
            finalResult = Constant.FLAG_Y;
        } else {
            return;
        }
        if (Constant.IWMS_INVENTORY_HOLD.equals(holdFlowCode)) {
            this.updateHoldInventoryRecord(finalResult, dto);
        } else if (Constant.IWMS_INVENTORY_REMOVE_HOLD_PLAN.equals(holdFlowCode) || Constant.IWMS_INVENTORY_REMOVE_HOLD_QUALITY.equals(holdFlowCode)) {
            this.updateRemoveHoldInventoryRecord(finalResult, dto);
        }
        LOGGER.info("######库存隔离kafka回调结束######");
    }
    /* Ended by AICoder, pid:d8de2044afa9466caef85f0e34333ca3 */

    /**
     * 回调成功更新库存隔离数据
     * @param result
     * @param dto
     */
    /* Started by AICoder, pid:4b011d7bc2f14a539e3371b68ecb2f83 */
    private void updateHoldInventoryRecord(String result, ApprovalProcessInfoDTO dto){
        LOGGER.info("######库存隔离回调开始######");
        if (Constant.FLAG_Y.equals(result)) {
            dto.setApprovedResult(Constant.FLAG_Y);
            inforRepository.updateHoldInventroyRecord(dto);
        } else {
            dto.setApprovedResult(Constant.FLAG_N).setHoldStatus(NumConstant.BIG_2);
            inforRepository.deleteHoldInventroyRecord(dto);
        }
        inforRepository.updateHoldInventroyRecordLog(dto);
    }
    /* Ended by AICoder, pid:4b011d7bc2f14a539e3371b68ecb2f83 */

    /**
     * 回调更新库存解除隔离
     * @param result
     * @param dto
     */
    /* Started by AICoder, pid:de5c77b92b7f454fac21f0a9f9820355 */
    private void updateRemoveHoldInventoryRecord(String result, ApprovalProcessInfoDTO dto){
        LOGGER.info("######库存解除隔离回调开始######");
        if (Constant.FLAG_Y.equals(result)) {
            dto.setApprovedResult(Constant.FLAG_Y).setHoldStatus(NumConstant.BIG_0);
        } else {
            dto.setApprovedResult(Constant.FLAG_N).setHoldStatus(NumConstant.BIG_1);
        }
        inforRepository.updateRemoveHoldInventroyRecord(dto);
        inforRepository.updateHoldInventroyRecordLog(dto);
    }
    /* Ended by AICoder, pid:de5c77b92b7f454fac21f0a9f9820355 */
}
