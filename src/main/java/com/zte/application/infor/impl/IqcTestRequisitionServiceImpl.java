package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.IqcTestRequisitionService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.IqcTestRequisitionListVO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.utils.inforclient.ConstantInfor;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import com.zte.resourcewarehouse.common.utils.inforclient.XstreamUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;


@Service
public class IqcTestRequisitionServiceImpl implements IqcTestRequisitionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IqcTestRequisitionServiceImpl.class);

    @Autowired
    private IqcTestRequisitionRepository iqcTestRequisitionRepository;

    @Autowired
    private StepTransferRepository stransferRepository;

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    OnlineFallBackApplyBillRepository onlineFallBackApplyBillRepository;

    @Autowired
    private EmailUtil emailUtil;

    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;

    @Value("${imes.centerFactory.url}")
    private String imesUrl;

    /**
     * <AUTHOR> 新增检验领料接口数据
     */
    @Override
    /* Started by AICoder, pid:1e4e7659e94348dc92ae01aa6d493fe9 */
    public ServiceData<?> insertIqcTestRequisitionInfo(IqcTestRequisitionHeadDTO oList) {
        if (null == oList) {
            return ServiceDataUtil.getBusinessError(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL14));
        }
        String error = checkIqcHeadDtoList(oList);
        if (StringUtils.isNotEmpty(error)) {
            return ServiceDataUtil.getBusinessError(error);
        }
        String errorPkg = checkIqcPkgDtoList(oList);
        if (StringUtils.isNotEmpty(errorPkg)) {
            return ServiceDataUtil.getBusinessError(errorPkg);
        }
        // 校验代码是箱管控还是reel管控
        List<String> itemCodeListAll = oList.getPackList().stream()
                .map(IqcTestRequisitionPkgDTO::getItemNo)
                .distinct()
                .collect(Collectors.toList());
        List<IqcTestRequisitionPkgDTO> getItemReelControlList = iqcTestRequisitionRepository.getItemReelControl(itemCodeListAll, oList.getStockNo());
        BusiAssertException.isTrue(getItemReelControlList.size() != itemCodeListAll.size(), MessageId.ITEM_NO_NOT_EXISTS);
        getItemReelControlList.forEach(e -> {
            oList.getPackList().stream()
                    .filter(k -> k.getItemNo().equals(e.getItemNo()))
                    .forEach(sur -> {
                        sur.setReelidProcess(e.getReelidProcess());
                    });
        });
        String errorDetail = checkIqcDetailDtoList(oList);
        if (StringUtils.isNotEmpty(errorDetail)) {
            return ServiceDataUtil.getBusinessError(errorDetail);
        }
        ThreadUtil.IQC_REQUISITION_EXECUTOR.execute(() -> {
            LOGGER.info("#######insertIqcTestRequisitionInfo begin" + JSONObject.toJSONString(oList.getDeliveryNo()));
            // 获取计划组和产品大类
            List<IqcTestRequisitionPkgDTO> getProductClass = iqcTestRequisitionRepository.getIqcProductClass(itemCodeListAll, oList.getDeliveryNo());
            if (Tools.isNotEmpty(getProductClass)) {
                List<String> planningGroupListAll = getProductClass.stream()
                        .map(IqcTestRequisitionPkgDTO::getProductClass)
                        .distinct()
                        .collect(Collectors.toList());
                List<IqcTestRequisitionPkgDTO> getPlanningGroups = stransferRepository.getIqcPlanningGroups(planningGroupListAll);
                getPlanningGroups.forEach(e -> {
                    getProductClass.stream()
                            .filter(k -> k.getProductClass().equals(e.getProductClass()))
                            .forEach(sur -> {
                                sur.setPlanningGroup(Tools.transNullValue(e.getPlanningGroup()));
                            });
                });
                getProductClass.forEach(e -> {
                    oList.getPackList().stream()
                            .filter(k -> k.getItemNo().equals(e.getItemNo()))
                            .forEach(sur -> {
                                sur.setProductClass(Tools.transNullValue(e.getProductClass()));
                                sur.setPlanningGroup(Tools.transNullValue(e.getPlanningGroup()));
                            });
                });
            }
            this.insertIqcTestRequisiton(oList);
            LOGGER.info("#######insertIqcTestRequisitionInfo end" + JSONObject.toJSONString(oList.getDeliveryNo()));
        });
        return ServiceDataUtil.getSuccess();
    }
    /* Ended by AICoder, pid:1e4e7659e94348dc92ae01aa6d493fe9 */

    /**
     * 校验头信息
     */
    /* Started by AICoder, pid:19705277121844e1ab58c15d3340e133 */
    public String checkIqcHeadDtoList(IqcTestRequisitionHeadDTO dto) {
        int existsFlag = Constant.INT_B1;
        StringBuilder stringBuilder = new StringBuilder();

        if (Tools.isEmpty(dto.getDeliveryNo()) || Tools.isEmpty(dto.getStockNo()) || Tools.isEmpty(dto.getMaterialPicker())
                || Tools.isEmpty(dto.getMaterialPickDept()) || Tools.isEmpty(dto.getReceiveOrg()) || Tools.isEmpty(dto.getUsageCode())
                || Tools.isEmpty(dto.getUsageName())) {
            stringBuilder.append(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL14));
            return stringBuilder.toString();
        }

        if (dto.getStockNo().toLowerCase().indexOf(Constant.WMWHSE) == existsFlag) {
            stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_STOCK_ERROR));
            return stringBuilder.toString();
        }

        return stringBuilder.toString();
    }
    /* Ended by AICoder, pid:19705277121844e1ab58c15d3340e133 */

    /**
     * 校验明细
     */
    /* Started by AICoder, pid:7df0ecce2cda4392a0a8e6b68dc82f12 */
    public String checkIqcPkgDtoList(IqcTestRequisitionHeadDTO dto) {
        StringBuilder stringBuilder = new StringBuilder();

        if (dto.getPackList() == null) {
            stringBuilder.append(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL14));
            return stringBuilder.toString();
        }

        List<String> itemBarcodeListAll = dto.getPackList().stream()
                .map(IqcTestRequisitionPkgDTO::getItemBarcode)
                .distinct()
                .collect(Collectors.toList());

        // 校验送货单号+物料条码是否是唯一值
        Integer repeatCount = iqcTestRequisitionRepository.isRepeatByBarcode(dto.getDeliveryNo(), itemBarcodeListAll);

        if (repeatCount > Constant.INT_0) {
            stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_DETAIL_NOT_VAL2));
            return stringBuilder.toString();
        }

        return stringBuilder.toString();
    }
    /* Ended by AICoder, pid:7df0ecce2cda4392a0a8e6b68dc82f12 */

    /**
     * 根据是否reel管控校验数据
     */
    /* Started by AICoder, pid:70ea6e780ea142f29205ac93d59e177f */
    public String checkIqcDetailDtoList(IqcTestRequisitionHeadDTO dto) {
        StringBuilder stringBuilder = new StringBuilder();

        // 校验箱明细数据
        for (IqcTestRequisitionPkgDTO pkgDTO : dto.getPackList()) {
            if (Tools.isEmpty(pkgDTO.getItemNo()) || Tools.isEmpty(pkgDTO.getItemBarcode())
                    || Tools.isEmpty(pkgDTO.getIsLtc()) || Tools.isEmpty(pkgDTO.getPkgId())
                    || Tools.isEmpty(pkgDTO.getChecktype())) {
                stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_DETAIL_NOT_VAL1));
                return stringBuilder.toString();
            }

            if (pkgDTO.getReelidProcess().equals(Constant.INT_1)) {
                String stringBuilder1 = checkIqcReelData(pkgDTO, stringBuilder);
                if (stringBuilder1 != null) {
                    return stringBuilder1;
                }
            } else {
                // 校验箱数据
                if (Tools.isEmpty(pkgDTO.getPkgGoodQty()) || Tools.isEmpty(pkgDTO.getPkgCheckResult())
                        || Tools.isEmpty(pkgDTO.getPkgPickQty()) || pkgDTO.getPkgPickQty().compareTo(BIG_0) <= Constant.INT_0) {
                    stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_DETAIL_NOT_VAL1));
                    return stringBuilder.toString();
                }
            }
        }

        return stringBuilder.toString();
    }
    /* Ended by AICoder, pid:70ea6e780ea142f29205ac93d59e177f */

    /**
     * 校验REEL数据
     */
    /* Started by AICoder, pid:3ab4c2766c4048caac36e246627b733e */
    public String checkIqcReelData(IqcTestRequisitionPkgDTO pkgDTO, StringBuilder stringBuilder) {
        // 校验reel数据
        if (pkgDTO.getReelList() == null) {
            stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_REEL_NOT_NULL));
            return stringBuilder.toString();
        }

        for (IqcTestRequisitionReelDTO reelDTO : pkgDTO.getReelList()) {
            // 校验REEL数据
            if (Tools.isEmpty(reelDTO.getPkgId()) || Tools.isEmpty(reelDTO.getReelId())
                    || Tools.isEmpty(reelDTO.getReelIdCheckResult()) || Tools.isEmpty(reelDTO.getReelGoodQty())
                    || Tools.isEmpty(reelDTO.getReelPickQty()) || reelDTO.getReelPickQty().compareTo(BIG_0) <= Constant.INT_0) {
                stringBuilder.append(CommonUtils.getLmbMessage(MessageId.IQC_TEST_REQUISITION_REEL_NOT_NULL));
                return stringBuilder.toString();
            }
        }

        return null;
    }
    /* Ended by AICoder, pid:3ab4c2766c4048caac36e246627b733e */

    /**
     * 操作数据
     */
    /* Started by AICoder, pid:4b83fa8bb99c4902bad775402ae418da */
    private void insertIqcTestRequisiton(IqcTestRequisitionHeadDTO oList) {
        LOGGER.info("#######insertIqcTestRequisiton begin" + JSONObject.toJSONString(oList.getDeliveryNo()));
        // 根据条码+送货单号唯一生成一个单号
        Map<String, List<IqcTestRequisitionPkgDTO>> barcodeGroupList = oList.getPackList().stream()
                .collect(Collectors.groupingBy(e -> e.getItemBarcode()));

        barcodeGroupList.forEach((k, v) -> {
            String strBIllNo = RedisSerialNoUtil.getDateIncreaseId(Constant.QCL, INT_5);
            IqcTestRequisitionBillDTO billDTO = new IqcTestRequisitionBillDTO();
            BeanUtils.copyProperties(oList, billDTO);
            billDTO.setBillNo(strBIllNo);

            List<IqcTestRequisitionDetailDTO> detailDTOS = Tools.newArrayList();

            for (IqcTestRequisitionPkgDTO psPkg : v) {
                if (psPkg.getReelidProcess().equals(Constant.INT_1)) {
                    for (IqcTestRequisitionReelDTO psReel : psPkg.getReelList()) {
                        String maxId = getMaxBillLineNumber(detailDTOS);
                        IqcTestRequisitionDetailDTO detailDTO = IqcTestRequisitionDetailDTO.builder()
                                .build()
                                .setBillNo(strBIllNo)
                                .setItemNo(psPkg.getItemNo())
                                .setItemBarcode(psPkg.getItemBarcode())
                                .setIsLtc(psPkg.getIsLtc())
                                .setCheckType(psPkg.getChecktype())
                                .setPkgId(psReel.getPkgId())
                                .setReelId(psReel.getReelId())
                                .setCheckResult(psReel.getReelIdCheckResult())
                                .setReelGoodQty(psReel.getReelGoodQty())
                                .setReelPickQty(psReel.getReelPickQty())
                                .setBillLineNumber(maxId)
                                .setProductClass(psPkg.getProductClass())
                                .setPlanningGroup(psPkg.getPlanningGroup());
                        detailDTOS.add(detailDTO);
                    }
                } else {
                    String maxId = getMaxBillLineNumber(detailDTOS);
                    IqcTestRequisitionDetailDTO detailDTO = IqcTestRequisitionDetailDTO.builder()
                            .build()
                            .setBillNo(strBIllNo)
                            .setItemNo(psPkg.getItemNo())
                            .setItemBarcode(psPkg.getItemBarcode())
                            .setIsLtc(psPkg.getIsLtc())
                            .setCheckType(psPkg.getChecktype())
                            .setPkgId(psPkg.getPkgId())
                            .setCheckResult(psPkg.getPkgCheckResult())
                            .setPkgGoodQty(psPkg.getPkgGoodQty())
                            .setPkgPickQty(psPkg.getPkgPickQty())
                            .setBillLineNumber(maxId)
                            .setProductClass(psPkg.getProductClass())
                            .setPlanningGroup(psPkg.getPlanningGroup());
                    detailDTOS.add(detailDTO);
                }
            }
            insertIqcData(detailDTOS, billDTO);
        });
        LOGGER.info("#######insertIqcTestRequisiton end" + JSONObject.toJSONString(oList.getDeliveryNo()));
    }
    /* Ended by AICoder, pid:4b83fa8bb99c4902bad775402ae418da */

    /**
     * 新增数据到表里
     */
    /* Started by AICoder, pid:3992b45df1a84923bf3c3f1c8c3d55df */
    private void insertIqcData(List<IqcTestRequisitionDetailDTO> detailDTOS, IqcTestRequisitionBillDTO billDTO) {
        detailDTOS.stream().filter(t -> t.getIsLtc().equals(Constant.FLAG_Y) && t.getCheckResult().equals(Constant.INT_0))
                .forEach(sur -> {
                    sur.setInforSplitStatus(Constant.INT_0);
                    sur.setDetailStatus(Constant.INT_0);
                });

        detailDTOS.stream().filter(t -> t.getIsLtc().equals(Constant.FLAG_Y) && t.getCheckResult().equals(Constant.INT_1))
                .forEach(sur -> {
                    sur.setInforSplitStatus(Constant.INT_0);
                    sur.setDetailStatus(Constant.INT_1);
                });

        detailDTOS.stream().filter(t -> t.getIsLtc().equals(Constant.FLAG_N) && t.getCheckResult().equals(Constant.INT_0))
                .forEach(sur -> {
                    sur.setInforSplitStatus(Constant.INT_0);
                    sur.setDetailStatus(Constant.INT_0);
                });

        detailDTOS.stream().filter(t -> t.getIsLtc().equals(Constant.FLAG_N) && t.getCheckResult().equals(Constant.INT_1))
                .forEach(sur -> {
                    sur.setInforSplitStatus(Constant.INT_1);
                    sur.setDetailStatus(Constant.INT_1);
                });
        // 如果单据明细中单据状态存在"已制单"，则更新单头的单据状态为"已制单"；
        boolean allStatus = detailDTOS.stream()
                .anyMatch(obj -> obj.getDetailStatus().equals(Constant.INT_1));
        if (allStatus) {
            billDTO.setBillStatus(Constant.INT_1);
        }

        // 如果单据明细中单据状态全部是"不制单"，则更新单头的单据状态为"不制单"；
        boolean allStatus1 = detailDTOS.stream()
                .allMatch(obj -> obj.getDetailStatus().equals(Constant.INT_0));
        if (allStatus1) {
            billDTO.setBillStatus(Constant.INT_0);
        }

        // 如果单据明细中Infor拆分状态存在"未拆分"，则更新单头的Infor拆分状态为"未拆分"；
        boolean allInforStatus = detailDTOS.stream()
                .anyMatch(obj -> obj.getInforSplitStatus().equals(Constant.INT_1));
        if (allInforStatus) {
            billDTO.setBillSplitStatus(Constant.INT_1);
        }

        // 如果单据明细中Infor拆分状态全部是"不拆分"，则更新单头的Infor拆分状态为"不拆分"；
        boolean allInforStatus1 = detailDTOS.stream()
                .allMatch(obj -> obj.getInforSplitStatus().equals(Constant.INT_0));
        if (allInforStatus1) {
            billDTO.setBillSplitStatus(Constant.INT_0);
        }

        //如果是立体仓的，明细状态有已制单，并且拆分状态全是不拆分，单据拆分状态直接改为已拆分，推送infor
        if (allStatus && allInforStatus1) {
            billDTO.setBillSplitStatus(Constant.INT_2);
        }

        // 新增数据到头表和明细表
        iqcTestRequisitionRepository.insertIqcTestRequisitionHead(billDTO);
        CommonUtils.splitList(detailDTOS, NumConstant.INT_500)
                .forEach(i -> iqcTestRequisitionRepository.insertIqcTestRequisitionDetail(i));
    }
    /* Ended by AICoder, pid:3992b45df1a84923bf3c3f1c8c3d55df */

    /**
     * 获取出库单行号
     */
    /* Started by AICoder, pid:03ba9df324324ac6ab6643e0ed22032b */
    public String getMaxBillLineNumber(List<IqcTestRequisitionDetailDTO> detailDTOS) {
        String maxId = Optional.ofNullable(detailDTOS)
                .map(list -> list.stream()
                        .max(Comparator.comparing(IqcTestRequisitionDetailDTO::getBillLineNumber))
                        .map(IqcTestRequisitionDetailDTO::getBillLineNumber)
                        .orElse(Constant.STR_NUMBER_ZERO))
                .orElse(Constant.STR_NUMBER_ZERO);

        int num = Integer.parseInt(maxId);
        num++;
        maxId = String.format(Constant.STR_LPAD, num);

        return maxId;
    }
    /* Ended by AICoder, pid:03ba9df324324ac6ab6643e0ed22032b */

    /**
     * 定时拆分JOB
     */
    @Override
    /* Started by AICoder, pid:c20b024ad79843e68ed7144ffbc18357 */
    public void splitIqcDetailsJob() {
        LOGGER.info("######调用拆分接口######");
        List<IqcTestRequisitionDetailDTO> detailDTOS = iqcTestRequisitionRepository.getIqcTestRequisitionSplitDetails();
        if (Tools.isEmpty(detailDTOS)) {
            return;
        }

        for (IqcTestRequisitionDetailDTO detail : detailDTOS) {
            // REEL管控
            if (Tools.isNotEmpty(detail.getReelId())) {
                if (detail.getReelGoodQty().compareTo(detail.getReelPickQty()) <= NumConstant.INT_0) {
                    detail.setInforSplitStatus(Constant.INT_0);
                }
            } else {
                if (detail.getPkgGoodQty().compareTo(detail.getPkgPickQty()) <= NumConstant.INT_0) {
                    detail.setInforSplitStatus(Constant.INT_0);
                }
            }
        }

        this.operateSplitData(detailDTOS);
    }
    /* Ended by AICoder, pid:c20b024ad79843e68ed7144ffbc18357 */

    /* Started by AICoder, pid:84a8d721fce04a8da6923f6ad94026f5 */
    private void operateSplitData(List<IqcTestRequisitionDetailDTO> detailDTOS) {
        // 获取不需要再次拆分的reel
        List<IqcTestRequisitionDetailDTO> haveReelList = detailDTOS.stream()
                .filter(i -> Tools.isNotEmpty(i.getReelId()) && i.getInforSplitStatus().equals(Constant.INT_1)
                 && Tools.isNotEmpty(i.getNewReelid()))
                .collect(Collectors.toList());
        if (Tools.isNotEmpty(haveReelList)) {
            for (IqcTestRequisitionDetailDTO dto : haveReelList) {
                // 已经生成了拆分reel，直接调用infor拆分REEL接口
                this.splitInforReel(dto, dto.getNewReelid());
            }
            iqcTestRequisitionRepository.updateIqcDetailBatch(haveReelList);
        }
        //获取需要拆分REEL的数据
        List<IqcTestRequisitionDetailDTO> noReelList = detailDTOS.stream()
                .filter(i -> Tools.isNotEmpty(i.getReelId()) && i.getInforSplitStatus().equals(Constant.INT_1)
                        && Tools.isEmpty(i.getNewReelid()))
                .collect(Collectors.toList());
        if (Tools.isNotEmpty(noReelList)) {
            this.splitReel(noReelList);
        }

        // 获取需要拆分箱码数据
        List<IqcTestRequisitionDetailDTO> pkgList = detailDTOS.stream()
                .filter(i -> Tools.isEmpty(i.getReelId()) && i.getInforSplitStatus().equals(Constant.INT_1))
                .collect(Collectors.toList());
        if (Tools.isNotEmpty(pkgList)) {
            this.splitPkg(pkgList);
        }

        // 更新不需要拆分的数据
        List<IqcTestRequisitionDetailDTO> noSplitList = detailDTOS.stream()
                .filter(i -> i.getInforSplitStatus().equals(Constant.INT_0))
                .collect(Collectors.toList());
        if (Tools.isNotEmpty(noSplitList)) {
            iqcTestRequisitionRepository.updateIqcDetailBatch(noSplitList);
        }

        List<String> billNos = detailDTOS.stream()
                .map(IqcTestRequisitionDetailDTO::getBillNo)
                .distinct()
                .collect(Collectors.toList());
        billNos.forEach(e -> {
            iqcTestRequisitionRepository.updateIqcBillSplited(e);
            iqcTestRequisitionRepository.updateIqcBillNoSplit(e);
        });
    }
    /* Ended by AICoder, pid:84a8d721fce04a8da6923f6ad94026f5 */

    //调用条码中心注册REEL
    /* Started by AICoder, pid:4db57a22b3e743aaa1886b78733ab278 */
    public void splitReel(List<IqcTestRequisitionDetailDTO> list) {
        for (IqcTestRequisitionDetailDTO dto : list) {
            try {
                Map<String, String> mapHeader = this.getIbarcodeHeaderMap();
                HashMap<String, Object> map = new HashMap<>(INT_16);
                map.put(SOURCE_SYSTEM, EWMS);
                map.put(ITEM_CODE, dto.getItemNo());
                map.put(PARENT_BARCODE, dto.getReelId());
                map.put(SOURCE_BATCH_NO, dto.getItemBarcode());

                List<BarcodeSplitDTO> paramBarcodeList = new ArrayList<>();
                BarcodeSplitDTO splitVo = new BarcodeSplitDTO();
                splitVo.setBarcode("");
                splitVo.setCount(dto.getReelPickQty());
                paramBarcodeList.add(splitVo);

                map.put(SPLIT_CHILD_DTOS, paramBarcodeList);

                ServiceData<?> result = RemoteServiceDataUtil.invokeService(Constant.ZTE_ISS_BARCODECENTER_BARCODE, MicroServiceNameEum.VERSION,
                        MicroServiceNameEum.SENDTYPEPOST, Constant.ZTE_ISS_BARCODECENTER_SPLIT_REEL, JSONObject.toJSONString(map),
                        mapHeader);

                if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
                    dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                    dto.setFailReason((result.getCode().getMsg().length() > Constant.INT_1000) ? result.getCode().getMsg().substring(0, Constant.INT_1000) : result.getCode().getMsg());
                    continue;
                }

                List<JSONObject> invokeMsg = (List<JSONObject>) result.getBo();
                if (Tools.isEmpty(invokeMsg)) {
                    dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                    continue;
                }

                // 只取第一条
                JSONObject json = invokeMsg.get(Constant.INT_0);
                Object newReelBarcode = json.get(Constant.BARCODE);
                if (Tools.isNotEmpty(newReelBarcode)) {
                    String newReel = newReelBarcode.toString();
                    dto.setNewReelid(newReel);
                    // 调用infor拆分REEL接口
                    this.splitInforReel(dto, newReel);
                } else {
                    dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                }
            } catch (Exception e) {
                LOGGER.error("IqcSplitReel exception: ", e);
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
            }
        }

        iqcTestRequisitionRepository.updateIqcDetailBatch(list);
    }
    /* Ended by AICoder, pid:4db57a22b3e743aaa1886b78733ab278 */

    //调用infor拆分REEL接口
    /* Started by AICoder, pid:5a1be29b7d444a3485aff312c6c411ff */
    public void splitInforReel(IqcTestRequisitionDetailDTO dto, String newReel) {
        try {
            ServiceData<?> result = getIbarcodePkg();
            if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                return;
            }
            List<JSONObject> invokeMsg = (List<JSONObject>) result.getBo();
            if (Tools.isEmpty(invokeMsg)) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                return;
            }
            Object newReelPkgBarcode = invokeMsg.get(Constant.INT_0);
            String newReelPkg = newReelPkgBarcode.toString();

            BarcodeSplitDTO splitDto = BarcodeSplitDTO.builder()
                    .loc(Constant.IQCCSZZ)
                    .newLpn(newReelPkg)
                    .newReelId(newReel)
                    .preLpn(dto.getPkgId())
                    .preReelId(dto.getReelId())
                    .qty(dto.getReelPickQty().toString())
                    .userid(Constant.ROOT)
                    .whseid(Constant.SCPRD + dto.getStockNo().toLowerCase())
                    .build();

            String responseStr = HttpClientUtil.httpPostWithJSON(
                    imesUrl + ZTE_INFOR_MOVE_SPLIT_REEL,
                    JacksonJsonConverUtil.beanToJson(splitDto),
                    Tools.newHashMap()
            );

            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
            if (json == null) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                return;
            }

            String retCode = json.get(JSON_CODE).asText();
            if (!Constant.INFOR_SUCCESS.equals(retCode)) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                dto.setFailReason((json.get(JSON_MESSAGE).asText().length() > Constant.INT_1000) ? json.get(JSON_MESSAGE).asText().substring(0, Constant.INT_1000) : json.get(JSON_MESSAGE).asText());
                return;
            }

            dto.setInforSplitStatus(Constant.INT_2);
        } catch (Exception e) {
            LOGGER.error("splitInforReel exception: ", e);
            dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
        }
    }
    /* Ended by AICoder, pid:5a1be29b7d444a3485aff312c6c411ff */

    //调用条码中心注册箱码
    /* Started by AICoder, pid:40858ebf875345f39797f05730e57856 */
    public void splitPkg(List<IqcTestRequisitionDetailDTO> list) {
        for (IqcTestRequisitionDetailDTO dto : list) {
            try {
                ServiceData<?> result = getIbarcodePkg();
                if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
                    dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                    continue;
                }
                List<JSONObject> invokeMsg = (List<JSONObject>) result.getBo();
                if (Tools.isEmpty(invokeMsg)) {
                    dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                    continue;
                }
                Object newPkgBarcode = invokeMsg.get(Constant.INT_0);
                String newPkg = newPkgBarcode.toString();
                // 调用infor拆分箱接口
                this.splitInforPkg(dto, newPkg);

            } catch (Exception e) {
                LOGGER.error("IqcSplitReel exception: ", e);
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
            }
        }
        iqcTestRequisitionRepository.updateIqcDetailBatch(list);
    }

    //调用条码中心注册箱码公用方法
    private ServiceData<?> getIbarcodePkg() {
        Map<String, String> mapHeader = this.getIbarcodeHeaderMap();
        HashMap<String, Object> map = new HashMap<>(INT_16);
        map.put(CATEGORYCODE, ZTEPKGPKGID);
        map.put(COUNT, Constant.INT_1);
        map.put(SOURCE_SYSTEM, EWMS_UPPER);
        ServiceData<?> result = RemoteServiceDataUtil.invokeService(Constant.ZTE_ISS_BARCODECENTER_BARCODE, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, Constant.ZTE_ISS_BARCODECENTER_BLANKGENERATE, JSONObject.toJSONString(map),
                mapHeader);
        return result;
    }
    /* Ended by AICoder, pid:40858ebf875345f39797f05730e57856 */

    //调用infor拆分pkg接口
    /* Started by AICoder, pid:0465474090e64dc7bf82153e2777ff14 */
    public void splitInforPkg(IqcTestRequisitionDetailDTO dto, String newPkg) {
        try {
            BarcodeSplitDTO splitDto = BarcodeSplitDTO.builder()
                    .toid(newPkg)
                    .uom(EA)
                    .packkey(dto.getItemNo())
                    .toloc(Constant.IQCCSZZ)
                    .qty(dto.getPkgPickQty().toString())
                    .storerKey(Constant.STORERKEY_ZTE)
                    .weight(NumConstant.STR_0)
                    .sku(dto.getItemNo())
                    .fromid(dto.getPkgId())
                    .userid(Constant.ROOT)
                    .whseid(dto.getStockNo().toUpperCase())
                    .build();
            String responseStr = HttpClientUtil.httpPostWithJSON(imesUrl + ZTE_INFOR_MOVE_SPLIT_PKG,
                    JacksonJsonConverUtil.beanToJson(splitDto), Tools.newHashMap());
            JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
            if (json == null) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                return;
            }
            String retCode = json.get(JSON_CODE).asText();
            if (!Constant.INFOR_SUCCESS.equals(retCode)) {
                dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
                return;
            }
            dto.setInforSplitStatus(Constant.INT_2);
            dto.setNewReelid(newPkg);
        } catch (Exception e) {
            LOGGER.error("IqcSplitPkg exception: ", e);
            dto.setSplitFailTimes(dto.getSplitFailTimes() + NumConstant.INT_1);
        }
    }
    /* Ended by AICoder, pid:0465474090e64dc7bf82153e2777ff14 */

    //设置条码中心头信息
    /* Started by AICoder, pid:a37a44ba9ef54a56bef0df2a4bb2db0e */
    public Map<String, String> getIbarcodeHeaderMap() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder()
                .lookupType(Constant.LOOKUP_TYPE_1000056)
                .build();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
        if (Tools.isEmpty(sysLookupValuesDTOList)) {
            return null;
        }
        SysLookupValuesDTO firstItem = sysLookupValuesDTOList.get(NumConstant.INT_0);
        String strLookupMeaning = firstItem.getLookupMeaning();
        String strXAuth = firstItem.getAttribute1();
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put(Constant.X_AUTH_VALUE, strXAuth);
        mapHeader.put(Constant.X_EMP_NO, strLookupMeaning);
        mapHeader.put(Constant.X_TENANT_ID, NumConstant.STR_10001);
        return mapHeader;
    }
    /* Ended by AICoder, pid:a37a44ba9ef54a56bef0df2a4bb2db0e */

    //定时推送领料明细到inforJOB
    /* Started by AICoder, pid:9beb15c5a194429d94a47e53b286a52d */
    @Override
    public void pushIqcDetailToInfor() {
        iqcTestRequisitionRepository.updateIqcBillByInfoSuccess();
        LOGGER.info("######推送iqc明细到infor--begin######");
        //INFOR接口反馈成功后，更新单据状态为"已出账"；
        List<IqcTestRequisitionBillDTO> billDTOS = iqcTestRequisitionRepository.getIqcBillToInfor();
        if (Tools.isEmpty(billDTOS)) {
            return;
        }
        // 提交到infor
        for (IqcTestRequisitionBillDTO bill : billDTOS) {
            IqcTestRequisitionBillDTO updateDto = IqcTestRequisitionBillDTO.builder()
                    .billNo(bill.getBillNo())
                    .build();
            try {
                boolean isSuccess = sendIqcInfor(bill);
                if (isSuccess) {
                    updateDto.setBillStatus(Constant.INT_3);
                } else {
                    updateDto.setBillFailTimes(bill.getBillFailTimes() + NumConstant.INT_1);
                }
            } catch (Exception e) {
                updateDto.setBillFailTimes(bill.getBillFailTimes() + NumConstant.INT_1);
            }
            iqcTestRequisitionRepository.updateIqcBill(updateDto);
        }
        LOGGER.info("######推送iqc明细到infor--end######");
    }
    /* Ended by AICoder, pid:9beb15c5a194429d94a47e53b286a52d */

    /* Started by AICoder, pid:2cb960fe6835434c9615523ffd8a3bac */
    public boolean sendIqcInfor(IqcTestRequisitionBillDTO dto) throws Exception {
        ServiceData<?> res = WebServiceClient.submitInfor(
                onlineFallBackApplyBillRepository.getWsdlUrl(),
                getXmlMessage(dto.getBillNo())
        );
        if (res.getCode().getCode().equals(RetCode.SUCCESS_CODE)) {
            return true;
        } else {
            return false;
        }
    }
    /* Ended by AICoder, pid:2cb960fe6835434c9615523ffd8a3bac */

    /* Started by AICoder, pid:840b652c8cb5438fb89fe1ec98c1e0fc */
    public String getXmlMessage(String billNo) throws Exception {
        String curTime = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(new Date());
        MsgHeader msgHeader = new MsgHeader();
        msgHeader.setSourceSystemId(ConstantInfor.IWMS_SOURCESYSTEMID);
        msgHeader.setSourceSystemName(ConstantInfor.IWMS_SOURCESYSTEMNAME);
        msgHeader.setUserId(ConstantInfor.IWMS_SOURCESYSTEMID);
        msgHeader.setUserName(ConstantInfor.IWMS_SOURCESYSTEMNAME);
        msgHeader.setSubmitDate(curTime);
        List<SoHeader> listSoHeader = iqcTestRequisitionRepository.getIqcSoHeader(billNo);
        List<SoDetail> listSoDetail = iqcTestRequisitionRepository.getIqcSoDetail(billNo);
        if (listSoHeader.size() != Constant.INT_1 || listSoDetail.isEmpty()) {
            return BLANK;
        }
        // 查询用途组织，名称，别名
        SoHeader utilityDto = stransferRepository.getUtilityInfo(listSoHeader.get(Constant.INT_0).getHref04());
        if (Tools.isNotEmpty(utilityDto)) {
            listSoHeader.get(Constant.INT_0).setHref01(Tools.transNullValue(utilityDto.getHref01()));
            listSoHeader.get(Constant.INT_0).setHref04(Tools.transNullValue(utilityDto.getHref04()));
            listSoHeader.get(Constant.INT_0).setHref05(Tools.transNullValue(utilityDto.getHref05()));
        }
        //查询配送去向名称
        String placeName = stransferRepository.getPlaceName(listSoHeader.get(Constant.INT_0).getConsigneeKey());
        if (Tools.isNotEmpty(placeName)) {
            listSoHeader.get(Constant.INT_0).setHref39(placeName);
        }
        // 查询领料人姓名
        String materialName = stransferRepository.getMaterialName(listSoHeader.get(Constant.INT_0).getHref33());
        if (Tools.isNotEmpty(materialName)) {
            listSoHeader.get(Constant.INT_0).setHref34(materialName);
        }
        listSoHeader.get(Constant.INT_0).setExternalOrderKey2(billNo);
        listSoHeader.get(Constant.INT_0).setListSoDetail(listSoDetail);
        listSoHeader.get(Constant.INT_0).setRequestedShipDate(curTime);
        listSoHeader.get(Constant.INT_0).setHref30(curTime);
        SoBizCont bizCont = new SoBizCont();
        bizCont.setSoHeader(listSoHeader.get(Constant.INT_0));
        MsgBody<SoBizCont> msgBody = new MsgBody<>();
        msgBody.setServiceCode(Constant.SCATTEROUT_WEBSERVICE_CODE);
        msgBody.setMsgid(UUID.randomUUID().toString());
        msgBody.setBizCont(bizCont);
        MsgSend msgSend = new MsgSend();
        msgSend.setMsgHeader(msgHeader);
        msgSend.setMsgBody(msgBody);
        return XstreamUtil.toXML(msgSend);
    }
    /* Ended by AICoder, pid:840b652c8cb5438fb89fe1ec98c1e0fc */

    /**
     * 查询
     */
    @Override
    /* Started by AICoder, pid:a41825f5cc49497ab8d497dab9983b2b */
    public IqcTestRequisitionListVO getIqcTestStatusToIscp(IqcTestRequisitionQueryDTO dto) {
        // 校验入参
        BusiAssertException.isEmpty(dto.getPageIndex(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getPageSize(), MessageId.NO_PARAMS);
        if (Tools.isEmpty(dto.getBillNo()) && Tools.isEmpty(dto.getDeliveryNo()) && Tools.isEmpty(dto.getItemNo()) && Tools.isEmpty(dto.getItemBarcode())) {
            this.compareTimeSpan(dto.getBeginDate(), dto.getEndDate());
        }
        IqcTestRequisitionListVO listVo = new IqcTestRequisitionListVO();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(iqcTestRequisitionRepository.getIqcTestStatusToIscpListVOTotal(dto));
        listVo.setIqcTestRequisitionDTOList(iqcTestRequisitionRepository.getIqcTestStatusToIscListVo(dto));
        return listVo;
    }
    /* Ended by AICoder, pid:a41825f5cc49497ab8d497dab9983b2b */

    /**
     * 比较时间跨度不能超过2年
     */
    /* Started by AICoder, pid:763c8922ae1549058ebd3e1749b77fef */
    private void compareTimeSpan(String startDate, String endDate) {
        try {
            if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
                BusiAssertException.result(MessageId.IQC_TEST_REQUISITION_QUERY_PARAM_IS_NULL);
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(Constant.YYYY_MM_DD_HH_MM_SS);
            LocalDate start = LocalDate.parse(startDate, dateTimeFormatter);
            LocalDate end = LocalDate.parse(endDate, dateTimeFormatter);
            LocalDate addedDate = start.plusDays(NumConstant.INT_740);
            if (addedDate.isBefore(end)) {
                BusiAssertException.result(MessageId.IQC_TEST_REQUISITION_QUERY_PARAM_IS_NULL);
            }
        } catch (Exception e) {
            LOGGER.error("日期格式转换发生异常", e);
            BusiAssertException.result(MessageId.IQC_TEST_REQUISITION_QUERY_PARAM_IS_NULL);
        }
    }
    /* Ended by AICoder, pid:763c8922ae1549058ebd3e1749b77fef */

    /**
     * 查询
     */
    @Override
    public IqcTestRequisitionListVO getIqcTestRequistionList(IqcTestRequisitionQueryDTO dto) {
        IqcTestRequisitionListVO listVo = new IqcTestRequisitionListVO();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(iqcTestRequisitionRepository.getIqcTestRequistionListVOTotal(dto));
        List<IqcTestRequisitionQueryDTO> list = iqcTestRequisitionRepository.getIqcTestRequistionListVo(dto);
        this.getProductName(list);
        listVo.setIqcTestRequisitionDTOList(list);
        return listVo;
    }

    /**
     * 批量更新领料单头
     */
    @Override
    public void batchUpdateIqcBill(IqcTestRequisitionQueryDTO dto) {
        iqcTestRequisitionRepository.batchUpdateIqcBill(dto);
    }

    /**
     * 批量更新领料单明细
     */
    @Override
    public void batchUpdateIqcDetail(IqcTestRequisitionQueryDTO dto) {
        iqcTestRequisitionRepository.batchUpdateIqcDetail(dto);
    }

    /**
     * 导出料单明细
     */
    @Override
    public void exportIqcTestRequistion(IqcTestRequisitionQueryDTO dto) {
        int total = iqcTestRequisitionRepository.getIqcTestRequistionListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            IqcTestRequisitionQueryDTO param = ((IqcTestRequisitionQueryDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(getProductName(iqcTestRequisitionRepository.getIqcTestRequistionListVo(param)));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(IQC_EXPORT_EX_NAME).setFileName(IQC_EX_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(IqcTestRequisitionQueryDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    //查询产品大类，计划组名称
    public List<IqcTestRequisitionQueryDTO> getProductName(List<IqcTestRequisitionQueryDTO> list) {
        //分批查询
        List<List<IqcTestRequisitionQueryDTO>> splitList = CommonUtils.splitList(list, NumConstant.INT_500);
        for (List<IqcTestRequisitionQueryDTO> allocateExceptions : splitList) {
            List<String> productClassList = allocateExceptions.stream()
                    .filter(iqcTestRequisitionQueryDTO -> Tools.isNotEmpty(iqcTestRequisitionQueryDTO.getProductClass()))
                    .map(IqcTestRequisitionQueryDTO::getProductClass)
                    .distinct()
                    .collect(Collectors.toList());
            if (Tools.isNotEmpty(productClassList)) {
                List<IqcTestRequisitionPkgDTO> getProductClassList = stransferRepository.getIqcPlanningGroups(productClassList);
                getProductClassList.forEach(e -> {
                    allocateExceptions.stream()
                            .filter(k -> Tools.isNotEmpty(k.getProductClass()) && Tools.isNotEmpty(k.getPlanningGroup()))
                            .filter(k -> k.getProductClass().equals(e.getProductClass()) && k.getPlanningGroup().equals(e.getPlanningGroup()))
                            .forEach(sur -> {
                                sur.setProductClassDesc(Tools.transNullValue(e.getProductClassDesc()));
                                sur.setPlanningGroupDesc(Tools.transNullValue(e.getPlanningGroupDesc()));
                            });
                });
            }
        }
        return list;
    }

    /**
     * 查询单头
     */
    @Override
    public IqcTestRequisitionListVO getIqcTestRequisitionBill(IqcTestRequisitionQueryDTO dto) {
        IqcTestRequisitionListVO listVo = new IqcTestRequisitionListVO();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(iqcTestRequisitionRepository.getIqcTestRequistionBillVOTotal(dto));
        listVo.setIqcTestRequisitionDTOList(iqcTestRequisitionRepository.getIqcTestRequistionBillVo(dto));
        return listVo;
    }

    /**
     * IQC领料拆分和提交失败邮件通知及提醒
     */
    /* Started by AICoder, pid:aa63a1490f5046738f686bc3ee069acd */
    @Override
    public void iqcFailSendMail(String xEmpNo) {
        LOGGER.info("############iqcFailSendMail开始调用");
        int total = iqcTestRequisitionRepository.selectIqcEmailListCount();
        if (Tools.equals(total, Constant.INT_0)) {
            return;
        }
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder()
                .lookupType(Constant.LOOKUP_TYPE_1000058)
                .build();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
        if (Tools.isEmpty(sysLookupValuesDTOList)) {
            return;
        }
        List<String> users = sysLookupValuesDTOList.stream()
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        String receipts = org.apache.commons.lang.StringUtils.join(
                users.parallelStream()
                        .map(t -> t + ZTE_EMAIL_SUFIIX)
                        .collect(Collectors.toList()), SPLIT_22);

        ThreadUtil.IQC_REQUISITION_EXECUTOR.execute(() -> {
            try {
                // 上传文档云发邮件给接收人
                IExcelExportServer server = (params, page) -> {
                    int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
                    int endRow = page * INT_5000;
                    IqcTestRequisitionEmailDTO param = ((IqcTestRequisitionEmailDTO) params).setStartRow(statRow).setEndRow(endRow);
                    return new ArrayList<>(iqcTestRequisitionRepository.selectIqcEmailListVo(param));
                };
                ExcelParams excelParams = ExcelParams.builder()
                        .title(INFOR_IQCFAIL_MATERIAL_MONITOR_NAME)
                        .fileName(INFOR_IQCFAIL_MATERIAL_MONITOR)
                        .sheetName(SHEET1)
                        .queryParams(new IqcTestRequisitionEmailDTO())
                        .pojoClass(IqcTestRequisitionEmailDTO.class)
                        .total((long) INT_20001)
                        .receipt(xEmpNo)
                        .createHeadRows(true)
                        .excelExportServer(server)
                        .build();
                ExcelUtil.makeBigExcelFileTemp(excelParams);
                if (!Tools.isEmpty(excelParams.getPath())) {
                    CloudDiskHelper cloudDiskHelper = (CloudDiskHelper) SpringContextUtil.getBean(CLOUD_DISK);
                    String fileKey = cloudDiskHelper.fileUpload(excelParams.getPath(), xEmpNo, CloudDiskHelper.MAX_RETRY_TIMES);
                    FileUtil.del(excelParams.getPath());
                    String downLoadUrl = ExcelUtil.getFileDownloadUrl(fileKey, (String) null, xEmpNo);
                    emailUtil.sendMail(receipts, INFOR_IQCFAIL_MATERIAL_MONITOR, STRING_EMPTY, INFOR_ALLCATE_EXCEPTION_MONITOR_DOWN + STR_BLANK + downLoadUrl, STRING_EMPTY);
                }
            } catch (Exception e) {
                LOGGER.error(" IQC质检超四次失败查询结果excel失败 " + e.getMessage());
                emailUtil.sendMail(receipts, INFOR_IQCFAIL_MATERIAL_MONITOR, BLANK, INFOR_IQCFAIL_MATERIAL_FAILED, BLANK);
            }
        });
        LOGGER.info("############iqcFailSendMail结束调用");
    }
    /* Ended by AICoder, pid:aa63a1490f5046738f686bc3ee069acd */
}

