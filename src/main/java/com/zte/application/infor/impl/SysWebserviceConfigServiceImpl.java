package com.zte.application.infor.impl;

import com.zte.application.infor.SysWebserviceConfigService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.SysWebserviceConfigRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.resourcewarehouse.common.utils.EmailUtil;
import com.zte.resourcewarehouse.common.utils.inforclient.WebServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

@Service
public class SysWebserviceConfigServiceImpl implements SysWebserviceConfigService {
    @Autowired
    private SysWebserviceConfigRepository repository;

    @Autowired
    private EmailUtil emailUtil;

    /**
     * Webservice接口超时监控
     */
    @Override
    public void webServiceInterfaceMonitoring(){
        List<String> lookupValue = Arrays.asList(WEBSERVICE_LOOKUP_TYPE.split(Constant.COMMA));
        List<SysLookupValuesDTO> lookupValuesDTOList=repository.getWebserviceInterface(lookupValue);//获取数据字典信息
        List<SysLookupValuesDTO> list=getlookupType(lookupValuesDTOList,LOOKUP_TYPE_1000042);//获取监控接口配置信息
        if(CollectionUtils.isNotEmpty(list)) {
            for (SysLookupValuesDTO item : list) {
                ThreadUtil.WEBSERVICE_INTERFACE_MONITORING.execute(() -> {
                    int overtime = Integer.valueOf(item.getAttribute1());
                    String interfaceUrl = item.getLookupMeaning();
                    LocalDateTime startTime = LocalDateTime.now(ZoneId.of(UTC));
                    try {
                        WebServiceClient.inforStore(item.getDescription(), interfaceUrl);
                        LocalDateTime endTime = LocalDateTime.now(ZoneId.of(UTC));
                        interfaceSendMail(lookupValuesDTOList,interfaceUrl, startTime,endTime,overtime);//发送邮件
                    } catch (Exception e) {
                        LocalDateTime endTimex = LocalDateTime.now(ZoneId.of(UTC));
                        interfaceSendMail(lookupValuesDTOList,interfaceUrl, startTime,endTimex,overtime);//发送邮件
                    }
                });
            }
        }
    }

    private List<SysLookupValuesDTO> getlookupType(List<SysLookupValuesDTO> list, String lookupType) {
        return list.stream().filter(t -> String.valueOf(t.getLookupType()).equals(lookupType)).collect(Collectors.toList());
    }

    public void interfaceSendMail(List<SysLookupValuesDTO> lookupValuesDTOList,String interfaceUrl,LocalDateTime startTime,LocalDateTime endTime,int overtime) {
        long secondsDiff = ChronoUnit.SECONDS.between(startTime, endTime);
        if (secondsDiff > overtime) {
            //发送邮件
            Random random = new Random();
            int intRandom = random.nextInt(INT_100) + INT_1;
            String subjectCN =WEBSERVICE_MONITORING+ONE_SPLIT+intRandom;
            String content = interfaceUrl+INTERFACE_TIME+secondsDiff+TIMED_OUT;
            List<String> users = getlookupType(lookupValuesDTOList,LOOKUP_TYPE_1000041).stream().map(SysLookupValuesDTO::getLookupMeaning).distinct().collect(Collectors.toList());
            String receipts=StringUtils.join(
                    users.parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList())
                    ,SPLIT_22);
            emailUtil.sendMail(receipts, subjectCN, STRING_EMPTY, content, STRING_EMPTY);
        }
    }
}