package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.DeliveryFloorService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.step.StepBaUserRepository;
import com.zte.interfaces.infor.dto.DeliveryFloorDTO;
import com.zte.interfaces.infor.dto.DeliveryPrintInfoDTO;
import com.zte.interfaces.infor.dto.RedDotTaskDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.DeliveryFloorListVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.Tools;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryFloorServiceImpl implements DeliveryFloorService {
    @Autowired
    DeliveryFloorRepository deliveryFloorRepository;

    @Autowired
    InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    DeliveryNoReceivesRepository deliveryNoReceivesRepository;

    @Autowired
    StepBaUserRepository stepBaUserRepository;

    @Value(("${redDot.task.token:}"))
    private String authToken;

    @Value("${redDot.task.url:}")
    private String redDotUrl;


    @Override
    public void importFloorInfo(List<DeliveryFloorDTO> list, String xEmpNo) {
        if (Tools.isEmpty(list)) {
            return;
        }
        List<DeliveryFloorDTO> deliveryFloorDTOS = new ArrayList<>();
        for (DeliveryFloorDTO dto : list) {
            String regex = Constant.EMPNO_FORMAT;
            //工号正则校验
            if (!dto.getDirector().matches(regex) || !dto.getLiability().matches(regex)) {
                BusiAssertException.result(Constant.FORMAT_ERROR);
            }
            int i = deliveryFloorRepository.queryFloorInfoExist(dto);
            if (i != NumConstant.INT_0) {
                deliveryFloorRepository.updateFloorInfo(dto, xEmpNo);
            }else {
                deliveryFloorDTOS.add(dto);
            }
        }
        if (Tools.isEmpty(deliveryFloorDTOS)) {
            return;
        }
        deliveryFloorRepository.insertFloorInfo(deliveryFloorDTOS, xEmpNo);
    }


    @Override
    public DeliveryFloorListVo getFloorInfo(DeliveryFloorDTO dto) {
        DeliveryFloorListVo listVo = new DeliveryFloorListVo();
        if (Tools.isNotEmpty(dto.getProductBase())) {
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000073);
            List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
            Map<String, String> baseMap = sysLookupValuesDTOList.stream()
                    .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescription));
            dto.setProductBase(baseMap.get(dto.getProductBase()));
        }
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(deliveryFloorRepository.getDeliveryFloorDetailTotal(dto));
        List<DeliveryFloorDetailDTO> list = deliveryFloorRepository.getDeliveryFloorDetailVo(dto);
        listVo.setDeliveryFloorDetailDTOS(list);
        return listVo;
    }


    @Override
    public void loseFloorInfo(DeliveryFloorDTO dto, String xEmpNo) {
        if (Tools.isEmpty(dto.getSerialKeys())) {
            return;
        }
        deliveryFloorRepository.loseFloorInfo(dto.getSerialKeys(), xEmpNo);
    }

    @Override
    public void effectFloorInfo(DeliveryFloorDTO dto, String xEmpNo) {
        if (Tools.isEmpty(dto.getSerialKeys())) {
            return;
        }
        deliveryFloorRepository.effectFloorInfo(dto.getSerialKeys(), xEmpNo);
    }


    /**
     * 查询数据字典维护的信息
     */
    public List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }


    @Override
    public void sendRedDotInfo(DeliveryFloorDTO dto) {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_FLOOR);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
        Map<String, String> empNoMap = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000072.equals(t.getLookupType()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupCode, SysLookupValuesDTO::getLookupMeaning));
        String empNo = empNoMap.get(LOOKUP_CODE_100007200001);
        List<SysLookupValuesDTO> baseList = sysLookupValuesDTOList.stream()
                .filter(t -> LOOKUP_TYPE_1000073.equals(t.getLookupType()) && dto.getProductBase().equals(t.getLookupMeaning()))
                .collect(Collectors.toList());
        SysLookupValuesDTO base = baseList.get(NumConstant.INT_0);
        dto.setProductBase(base.getDescription());
        List<DeliveryFloorDetailDTO> deliveryFloorDetailVo = deliveryFloorRepository.getDeliveryFloorDetailVo(dto);
        if (Tools.isEmpty(deliveryFloorDetailVo)) {
            return;
        }
        DeliveryFloorDetailDTO deliveryFloorDetailDTO = deliveryFloorDetailVo.get(NumConstant.INT_0);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put(X_FACTORY_ID, base.getAttribute1());
        mapHeader.put(X_EMP_NO, empNo);
        mapHeader.put(X_AUTH_VALUE, authToken);
        RedDotTaskDTO redDotTaskDTO = new RedDotTaskDTO();
        StringBuilder desc = new StringBuilder(PRODUCT_BASE).append(SEMICOLON).append(base.getDescription()).append(CHINESE_COMMA)
                .append(FLOOR).append(SEMICOLON).append(dto.getFloor()).append(CHINESE_COMMA)
                .append(HELP_TYPE).append(SEMICOLON).append(dto.getHelpType());
        redDotTaskDTO.setEqpCode(RED_DOT_FLOOR);
        redDotTaskDTO.setEqpName(RED_DOT_FLOOR);
        redDotTaskDTO.setFloor(dto.getFloor());
        redDotTaskDTO.setReddotCode(DISTR07);
        redDotTaskDTO.setDesc(desc.toString());
        if (FLAG_Y.equals(dto.getCallType())) {
            redDotTaskDTO.setSendTo(deliveryFloorDetailDTO.getLiability());
        }else {
            redDotTaskDTO.setSendTo(deliveryFloorDetailDTO.getDirector());
        }
        redDotTaskDTO.setSource(AUTOMATIC_ORDER);
        redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
        log.info("呼叫帮助红点：{}", JSONObject.toJSONString(redDotTaskDTO));
        HttpClientUtil.httpPostWithJSON(redDotUrl, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
    }


    @Override
    public List<DeliveryPrintInfoDTO> getPrintInfo(DeliveryFloorDTO dto) {
        if (Tools.isNotEmpty(dto.getProductBase())){
            //根据基地获取仓库
            List<String> whseidListByBase = deliveryNoReceivesRepository.queryWhseByBase(dto.getProductBase(),Constant.WHSEID_PRODUCTBASE);
            BusiAssertException.isEmpty(whseidListByBase, MessageId.PRODUCTBASE_NO_DATA_WHSE);
            dto.setWhseidList(whseidListByBase);
        }
        List<DeliverySignDetailDTO> deliveryList = deliveryFloorRepository.queryDeliveryInfo(dto);
        BusiAssertException.isEmpty(deliveryList, MessageId.DELIVERY_NO_RECEIVED_NOT_EXISTS);
        String asnStateString = deliveryList.stream()
                .map(DeliverySignDetailDTO::getAsnStatus)
                .collect(Collectors.joining(Constant.SPLIT));
        boolean completeStatus = Arrays.stream(asnStateString.split(Constant.SPLIT)).allMatch(s -> Integer.parseInt(s) >= NumConstant.INT_9);
        if (!completeStatus) {
            BusiAssertException.result(DELIVER_INFO_RECEIVING);
        }
        List<DeliveryPrintInfoDTO> deliveryPrintInfoDTOS = deliveryFloorRepository.getDeliveryPrintInfo(dto);
        if (Tools.isEmpty(deliveryPrintInfoDTOS)) {
            BusiAssertException.result(DELIVER_INFO_EMPTY);
        }
        for (DeliveryPrintInfoDTO printInfoDTO : deliveryPrintInfoDTOS) {
            String fullName = stepBaUserRepository.getFullName(printInfoDTO.getEditWho());
            printInfoDTO.setFullName(fullName);
        }
        return deliveryPrintInfoDTOS;
    }


    @Override
    public void deleteFloorInfo(DeliveryFloorDTO dto, String xEmpNo) {
        if (Tools.isEmpty(dto.getSerialKeys())) {
            return;
        }
        deliveryFloorRepository.deleteFloorInfo(dto.getSerialKeys(), xEmpNo);
    }
}
