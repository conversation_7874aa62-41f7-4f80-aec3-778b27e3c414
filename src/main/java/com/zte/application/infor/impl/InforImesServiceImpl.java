package com.zte.application.infor.impl;

import com.zte.application.infor.InforImesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InforImesRepository;
import com.zte.domain.model.step.TechItemStoreRepository;
import com.zte.interfaces.infor.dto.EdiSoSImesDTO;
import com.zte.interfaces.infor.dto.InforImesQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnQtyDto;
import com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto;
import com.zte.interfaces.infor.vo.LocVo;
import com.zte.interfaces.infor.vo.ZteinboundserialVo;
import com.zte.interfaces.step.dto.TechItemStoreDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 领料单接收相关实现类
 *
 * <AUTHOR>
 * @date 2021-12-08 13:43
 */
@Service
public class InforImesServiceImpl implements InforImesService {

    @Autowired
    private InforImesRepository inforImesRepository;

    @Autowired
    private TechItemStoreRepository techItemStoreRepository;

    /**
     * 通过单据号2 查询 infor 发料信息
     *
     * @param externalorderkey2 单据号2
     * @return 单据汇总信息
     */
    @Override
    public List<EdiSoSImesDTO> queryEdiSoSByKey2(String externalorderkey2) {
        if (StringUtils.isBlank(externalorderkey2)) {
            return new LinkedList<>();
        }
        // 1. 查询单据汇总信息 通过单据号2
        List<EdiSoSImesDTO> ediSoSImesList = inforImesRepository.queryEdiSoSByKey2(externalorderkey2);
        if (CollectionUtils.isEmpty(ediSoSImesList)) {
            return new LinkedList<>();
        }
        // 2. --获取单据对应的发料仓
        List<String> whseidList = inforImesRepository.queryWhseidByKey2(externalorderkey2);
        // 3. 获取发料状态
        if (CollectionUtils.isEmpty(whseidList)) {
            return ediSoSImesList;
        }
        Integer integer = inforImesRepository.queryAllStatus(whseidList, externalorderkey2);
        ediSoSImesList.forEach(item -> item.setStatus(integer));
        return ediSoSImesList;
    }

    /**
     * 查询单板信息
     *
     * @param externalorderkey2 外部单单号2
     * @param serialnumber      条码
     * @param billType      条码类型
     * @return 单板条码
     */
    @Override
    public EdiSoSImesDTO queryVeneerInfo(String externalorderkey2, String serialnumber,String billType) {
        EdiSoSImesDTO ediSoSImesDTO = null;
        if (StringUtils.isBlank(externalorderkey2) || StringUtils.isBlank(serialnumber)) {
            return ediSoSImesDTO;
        }
        if(Constant.VENEER.equals(billType)){
            // 1. 获取单板条码信息
            return inforImesRepository.queryVeneerInfo(externalorderkey2, serialnumber);
        }
        return this.queryProcessedInfo(externalorderkey2, serialnumber);
    }

    /**
     * 查询原材料信息信息
     *
     * @param externalorderkey2 外部单单号2
     * @param serialnumber      原材料编码
     * @return 原材料编码 信息
     */
    @Override
    public EdiSoSImesDTO queryProcessedInfo(String externalorderkey2, String serialnumber) {
        EdiSoSImesDTO ediSoSImesDTO = null;
        if (StringUtils.isBlank(externalorderkey2) || StringUtils.isBlank(serialnumber)) {
            return ediSoSImesDTO;
        }
        // 1. 查询发料仓
        List<String> whseidList = inforImesRepository.queryWhseidByKey2(externalorderkey2);
        if (CollectionUtils.isEmpty(whseidList)) {
            return ediSoSImesDTO;
        }
        // 查询 pkgId 信息
        return inforImesRepository.queryProcessedInfo(externalorderkey2, serialnumber, whseidList);
    }

    /**
     * 查询是否存在已退单未收料
     * @param dto
     * @return
     */
    @Override
    public Integer getIsReturnReceived(InforImesReturnWarehouseDto dto){
        //获取仓库
        List<String> list=inforImesRepository.getReturnWarehouse(dto);
        if (list.isEmpty()){
            return Constant.INT_0;
        }
        dto.setWhseidList(list);
        //查询是否存在已退单未收料
        return inforImesRepository.getIsReturnReceived(dto);
    }

    /**
     * 查询已退、任务退数量
     * @param dto
     * @return
     */
    @Override
    public List<InforImesReturnQtyDto> getReturnQty(InforImesQtyDto dto){
        //查询已退、任务退数量
        return inforImesRepository.getReturnQty(dto);
    }

    /**
     * 查询已发数量
     * @param dto
     * @return
     */
    @Override
    public List<InforImesReturnQtyDto> getIssuedQty(InforImesQtyDto dto){
        //查询已发数量
        return inforImesRepository.getIssuedQty(dto);
    }

    /**
     * 查询需求数量
     * @param dto
     * @return
     */
    @Override
    public List<InforImesReturnQtyDto> getReqQty(InforImesQtyDto dto){
        //查询需求数量
        return inforImesRepository.getReqQty(dto);
    }

    /**
     * 根据单据、仓库或条码查询已完成收货的条码
     * @param dto
     * @return
     */
    @Override
    public List<String> getSerialnumber(ZteinboundserialVo dto){
        BusiAssertException.isEmpty(dto.getExternreceiptkey(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.NO_PARAMS);
        return inforImesRepository.getSerialnumber(dto);
    }

    /**
     * infor库位码正确性校验
     * @param dto
     * @return
     */
    @Override
    public ServiceData<?> checkInforLoc(LocVo dto){
        BusiAssertException.isEmpty(dto.getLoc(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.NO_PARAMS);
        BusiAssertException.isTrue(inforImesRepository.checkInforLoc(dto)==0, MessageId.CHECK_INFORLOC);
        return ServiceDataUtil.getSuccess();
    }

    /***
     * 获取单板重量信息
     * @param prodplanId
     * @return
     */
    @Override
    public TechItemStoreDTO getTechItemStore(String prodplanId){
        return techItemStoreRepository.getTechItemStore(prodplanId);
    }

    /***
     * 更新单板重量信息
     * @param dto
     */
    @Override
    public void updateTechItemStore(TechItemStoreDTO dto){
        int count=techItemStoreRepository.checkTechItemStore(dto.getBomId());
        if(count==Constant.INT_0){
            //新增
            techItemStoreRepository.insertTechItemStore(dto);
        }else{
            //修改
            techItemStoreRepository.updateTechItemStore(dto);
        }
    }

    @Override
    public EdiSoSImesDTO queryEdiSoSByKey2AndSku(EdiSoSImesDTO ediSoSImesDTO) {
        // 1. 查询单据汇总信息 通过单据号2
        EdiSoSImesDTO respInfo = new EdiSoSImesDTO();
        List<String> whseidList = inforImesRepository.queryEdiSoSByKey2AndSku(ediSoSImesDTO);
        if (CollectionUtils.isEmpty(whseidList)) {
            return respInfo;
        }
        respInfo.setWhseid(whseidList.get(0));
        // 2. --获取央仓对应的班组
        List<String> teamGroup = inforImesRepository.queryEdiSoSByWhseidAndSku(whseidList.get(0), ediSoSImesDTO.getSku());
        if (!CollectionUtils.isEmpty(teamGroup)) {
            respInfo.setTeamGroup(teamGroup.get(0));
        }
        return respInfo;
    }
}
