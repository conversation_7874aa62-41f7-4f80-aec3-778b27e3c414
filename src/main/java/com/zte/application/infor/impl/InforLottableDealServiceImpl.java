package com.zte.application.infor.impl;

import com.zte.application.infor.InforLottableDealService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.infor.LotattributeDealLog;
import com.zte.domain.model.infor.LotattributeDealLogRepository;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.domain.model.material.InforLottableDealRepository;
import com.zte.domain.model.material.InforLottableDeal;
import com.zte.interfaces.infor.dto.InforLottablesDTO;
import com.zte.interfaces.infor.dto.LotattributeDealLogDTO;
import com.zte.interfaces.infor.vo.LotattributeDealLogListVO;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.resourcewarehouse.common.springbootframe.annotation.DataSource;
import com.zte.resourcewarehouse.common.springbootframe.datasource.DatabaseType;
import com.zte.resourcewarehouse.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.TASK_NUM_CODE_LOT;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_1;
import static com.zte.common.utils.NumConstant.INT_500;

import java.util.*;
import java.util.concurrent.*;


/**
 * <AUTHOR>
 */
@Service
@DataSource(DatabaseType.DB_1)
@Slf4j
public class InforLottableDealServiceImpl implements InforLottableDealService {

    @Autowired
    private InforLottableDealRepository inforLottableDealRepository;
    @Autowired
    private InforBarcodeCenterRepository barcodeCenterRepository;
    @Autowired
    private OnlineFallBackApplyBillRepository applyBillRepository;
    @Autowired
    private LotattributeDealLogRepository lotattributeDealLogRepository;
    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;



    @Override
    public void splitBarcodeList(InforLottablesDTO dto) {

        // 校验ISCP的入参
        BusiAssertException.isEmpty(dto.getItemBarcodeList(), MessageId.INPUT_DATA_CAN_NOT_NULL);
        BusiAssertException.isTrue(dto.getItemBarcodeList().size() > SPLITSIZE_FIVE_HUNDRED, MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED);

        // 保存ISCP的入参
        InforLottableDeal inforLottableDeal = new InforLottableDeal();
        BeanUtils.copyProperties(dto, inforLottableDeal);
        String serialNo = getSerialNo();
        inforLottableDeal.setSerialNo(serialNo);
        inforLottableDeal.setCreatedBy(dto.getUpdatedBy());
        inforLottableDeal.setLastUpdatedBy(dto.getUpdatedBy());
        inforLottableDeal.setSubmitBy(dto.getUpdatedBy());
        inforLottableDeal.setSubmitReason(dto.getUpdateReason());
        inforLottableDeal.setItemBarcodes(String.join(COMMA, dto.getItemBarcodeList()));
        inforLottableDeal.setSendParams(JsonUtil.toJSONString(dto));
        inforLottableDealRepository.insertInforLottableDeal(inforLottableDeal);

        // 保存批属性变更到infor
        List<LotattributeDealLog> lotattributeDealLogList = new ArrayList<>();
        List<String> wms = barcodeCenterRepository.getEnvWmwhids();
        BusiAssertException.isEmpty(wms, MessageId.WHSEID_CAN_NOT_BE_EMPTY);
        for (String whseid : wms) {
            for (String itemBarcode : dto.getItemBarcodeList()) {
                LotattributeDealLog lotattributeDealLog = new LotattributeDealLog();
                BeanUtils.copyProperties(dto, lotattributeDealLog);
                lotattributeDealLog.setWhseid(whseid);
                lotattributeDealLog.setSku(dto.getItemNo());
                lotattributeDealLog.setSymbol(BIG_9);
                lotattributeDealLog.setDealtimes(BIG_0);
                lotattributeDealLog.setSerialNo(serialNo);
                lotattributeDealLog.setAddwho(dto.getUpdatedBy());
                lotattributeDealLog.setEditwho(dto.getUpdatedBy());
                lotattributeDealLog.setLottable02(itemBarcode);
                lotattributeDealLogList.add(lotattributeDealLog);
            }
        }
        // 保存
        CommonUtils.splitList(lotattributeDealLogList, INT_500).forEach(i -> thplExecutor.execute(()-> lotattributeDealLogRepository.insertLotattributeDealLog(i)));

    }

    /**
     * 获取单据号
     */
    public String getSerialNo(){
        return RedisSerialNoUtil.getDateIncreaseIdWithDate(LOT, IWMS_DATAWB, DEAL_LOTTABLES, INT_1, INT_7);
    }

    @Override
    public LotattributeDealLogListVO getInforLottableLogList(LotattributeDealLogDTO dto) {
        LotattributeDealLogListVO listVo = new LotattributeDealLogListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize()+1).setEndRow(dto.getPageIndex()*dto.getPageSize());
        listVo.setTotal(lotattributeDealLogRepository.getLotattributeDealLogListVOTotal(dto));
        listVo.setLotattributeDealLogDTOList(lotattributeDealLogRepository.getLotattributeDealLogList(dto));
        return listVo;
    }

    @Override
    public void updateInforLottableLog(LotattributeDealLogDTO dto) {
        lotattributeDealLogRepository.updateInforLottableLog(dto);
    }

    /**
     * 数据归档
     */
    @Override
    public void dataArchiving(){
        List<StSysLookupValuesDTO> values = applyBillRepository.getSysLookupValues(TASK_NUM_CODE_LOT);
        int taskNum = Tools.isEmpty(values)?INT_5000:Integer.parseInt(values.get(0).getLookupMeaning());
        if(taskNum==INT_0){
            return;
        }
        List<String> serialNoList = inforLottableDealRepository.selectInforLottableDeal(taskNum);
        if (Tools.isEmpty(serialNoList)) {
            return;
        }
        List<List<String>> serialNoListSplitList = CommonUtils.splitList(serialNoList, INT_500);
        serialNoListSplitList.forEach(t->{
            //归档
            inforLottableDealRepository.dataArchiving(t);
            //删除
            inforLottableDealRepository.deleteInforLottableDeal(t);
        });
    }

}
