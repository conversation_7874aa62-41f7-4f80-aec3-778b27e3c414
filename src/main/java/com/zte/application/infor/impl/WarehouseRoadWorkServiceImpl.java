package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.zte.application.infor.WarehouseRoadWorkService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.WarehouseRoadWorkRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.WarehouseRoadWorkListVO;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_0;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarehouseRoadWorkServiceImpl implements WarehouseRoadWorkService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseRoadWorkServiceImpl.class);

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private WarehouseRoadWorkRepository warehouseRoadWorkRepository;

    /**
     * 查询坐标类型列表
     */
    @Override
    public List<SysLookupValuesDTO> getCoordinateTypeList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000062);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询库位标记列表
     */
    @Override
    public List<SysLookupValuesDTO> getLocMarkList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000063);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询库房列表
     */
    @Override
    public List<SysLookupValuesDTO> getWarehouseList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000064);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询库区列表
     */
    @Override
    public List<SysLookupValuesDTO> getWarehouseAreaList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000065);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询路网数据
     */
    @Override
    public WarehouseRoadWorkListVO getWarehouseRoadWorkList(WarehouseRoadWorkDTO dto) {
        WarehouseRoadWorkListVO listVo = new WarehouseRoadWorkListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(dto));
        listVo.setWarehouseRoadWorkDTOList(warehouseRoadWorkRepository.getWarehouseRoadWorkList(dto));
        return listVo;
    }

    /**
     * 保存路网数据
     */
    @Override
    public void saveWarehouseRoadWork(WarehouseRoadWorkDTO dto) {
        if (Tools.isEmpty(dto.getSerialkey())) {
            // 校验位置码是否在infor存在
            BusiAssertException.isTrue(warehouseRoadWorkRepository.getLocTotal(dto)==INT_0,
                    MessageId.INFOR_LOC_NOT_EXISTS);
            // 校验保存的路网数据是否已存在
            WarehouseRoadWorkDTO warehouseRoadWorkDTO = WarehouseRoadWorkDTO.builder().build()
                    .setWhseid(dto.getWhseid()).setLoc(dto.getLoc()).setEnabledFlag(FLAG_Y);
            BusiAssertException.isTrue(warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(warehouseRoadWorkDTO)>INT_0,
                    MessageId.WAREHOUSE_ROAD_WORK_EXISTS);
        }
        warehouseRoadWorkRepository.saveWarehouseRoadWork(dto);
    }

    /**
     * 更新路网数据
     */
    @Override
    public void updateWarehouseRoadWork(WarehouseRoadWorkDTO dto) {
        warehouseRoadWorkRepository.updateWarehouseRoadWork(dto);
    }

    /**
     * 导入路网数据
     */
    @Override
    public void importWarehouseRoadWork(List<WarehouseRoadWorkDTO> list) {
        if (Tools.isEmpty(list)) {
            return;
        }
        // 校验位置码是否在infor存在
        List<String> whseidList = list.stream().map(WarehouseRoadWorkDTO::getWhseid).distinct().collect(Collectors.toList());
        for (String whseid : whseidList) {
            List<String> locList = list.stream().filter(i -> whseid.equals(i.getWhseid())).map(WarehouseRoadWorkDTO::getLoc).distinct().collect(Collectors.toList());
            WarehouseRoadWorkDTO warehouseRoadWorkDTO = WarehouseRoadWorkDTO.builder().build().setWhseid(whseid).setLocList(locList);
            BusiAssertException.isTrue(locList.size() != warehouseRoadWorkRepository.getLocTotal(warehouseRoadWorkDTO),
                    MessageId.INFOR_LOC_NOT_EXISTS);
        }
        // 校验导入的路网数据是否重复
        List<String> whseidLocList = list.stream().map(i -> i.getWhseid()+i.getLoc()).distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(list.size() != whseidLocList.size(),
                MessageId.WAREHOUSE_ROAD_WORK_REPEATED);
        // 校验导入的路网数据是否已存在
        BusiAssertException.isTrue(warehouseRoadWorkRepository.getWarehouseRoadWorkCount(list)>INT_0,
                MessageId.WAREHOUSE_ROAD_WORK_EXISTS);
        warehouseRoadWorkRepository.saveWarehouseRoadWorkList(list);
    }

    /**
     * 导出路网数据
     */
    @Override
    public void exportWarehouseRoadWork(WarehouseRoadWorkDTO dto) {
        int total = warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            WarehouseRoadWorkDTO param = ((WarehouseRoadWorkDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(warehouseRoadWorkRepository.getWarehouseRoadWorkList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(WAREHOUSE_ROAD_WORK_NAME).setFileName(WAREHOUSE_ROAD_WORK_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(WarehouseRoadWorkDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

}
