package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.MslServiceLifeService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.MslServiceLifeInfo;
import com.zte.domain.model.infor.MslServiceLifeRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.MslServiceLifeInfoVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

@Service
public class MslServiceLifeServiceImpl implements MslServiceLifeService {
    private static final Logger log = LoggerFactory.getLogger(MslServiceLifeServiceImpl.class);
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    private MslServiceLifeRepository mslServiceLifeRepository;

    @Value(("${redDot.task.token:}"))
    private String authToken;
    @Value("${redDot.task.url:}")
    private String redDotUrl;
    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;
    /**
     * 插入潮敏车间寿命信息
     */
    @Override
    public void insertMslServiceLifeJob(){
        //获取数据字典项
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_MSL_SERVICE_LIFE);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getLookupValues(sysLookupValuesDTO);
        //获取仓库
        List<String> whseIdList = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000071.equals(t.getLookupType()) && Constant.BIG_DECIMAL_1.equals(t.getAttribute5()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        //获取潮敏等级配置
        List<String> wetlevelList = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000078.equals(t.getLookupType()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        //获取执行时间
        String executeDate = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_CODE_100007900001.equals(t.getLookupCode()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .findFirst()
                .orElse(null);
        //获取数据并插入
        MslServiceLifeInfo dto = MslServiceLifeInfo.builder().build().setExecuteDate(executeDate).setWhseIdList(whseIdList).setWetlevelList(wetlevelList);
        getMslServiceLifeData(dto);
    }

    /**
     * 获取潮敏车间寿命信息
     * @param dto
     */
    public void getMslServiceLifeData(MslServiceLifeInfo dto){
        log.info("############insertMslServiceLifeJob开始调用", JsonUtil.toJSONString(dto));
        //获取当前时间
        String currDate = new SimpleDateFormat(Constant.YYMMDDHHMMSS).format(new Date());
        mslServiceLifeRepository.insertMslServiceLife(dto);
        //获取待初始化订单信息的潮敏信息
        MslServiceLifeInfo mslServiceLifeInfo = MslServiceLifeInfo.builder().build().setDataStatus(Constant.INT_0);
        List<MslServiceLifeInfo> list = mslServiceLifeRepository.getMslServiceLifeList(mslServiceLifeInfo);
        //修改执行时间
        SysLookupValuesDTO valuesDTO=SysLookupValuesDTO.builder().build().setLookupCode(Constant.LOOKUP_CODE_100007900001).setLookupMeaning(currDate);
        mslServiceLifeRepository.updateLookupValues(valuesDTO);
        if(CollectionUtils.isNotEmpty(list)){
            log.info("############获取待初始化订单信息的潮敏信息", JsonUtil.toJSONString(list));
            //取仓库
            List<String> whseIdList = list.stream().map(MslServiceLifeInfo::getWhseid).distinct().collect(Collectors.toList());
            List<MslServiceLifeInfo> allList=new ArrayList<>();
            //按仓库计算
            whseIdList.forEach(e -> {
                //按仓库获取潮敏信息
                MslServiceLifeInfoVo lifeInfoVo=new MslServiceLifeInfoVo();
                lifeInfoVo.setWhseid(e);
                List<MslServiceLifeInfo> mslServiceLifeInfoList=list.stream().filter(t->t.getWhseid().equals(e)).collect(Collectors.toList());
                List<List<MslServiceLifeInfo>> splitList = CommonUtils.splitList(mslServiceLifeInfoList, INT_500);
                for (List<MslServiceLifeInfo> mslList : splitList) {
                    lifeInfoVo.setMslServiceLifeInfoList(mslList);
                    //获取订单相关信息
                    List<MslServiceLifeInfo> lifeInfoList = mslServiceLifeRepository.getMslServiceLifeByOrderList(lifeInfoVo);
                    if(CollectionUtils.isNotEmpty(lifeInfoList)){
                        allList.addAll(lifeInfoList);
                    }
                }
            });
            //所有潮敏信息（包含订单信息）不为空获取线边仓收料时间及执行修改
            if(CollectionUtils.isNotEmpty(allList)){
                getDeliveryDateByMslServiceLife(allList);
            }
        }
        log.info("############insertMslServiceLifeJob结束调用", JsonUtil.toJSONString(dto));
    }

    /**
     * 获取交易时间及修改潮敏信息
     * @param mslServiceLifeInfoList
     */
    public void getDeliveryDateByMslServiceLife(List<MslServiceLifeInfo> mslServiceLifeInfoList) {
        List<String> reelidList = mslServiceLifeInfoList.stream().map(MslServiceLifeInfo::getSerialnumber).collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(reelidList, INT_500);
        Map<String, String> headerParamsMap = new HashMap<>(16);
        headerParamsMap.put(INONE_APPCODE, inoneAppcode);
        headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
        headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
        try {
            for (List<String> spReelidList : splitList) {
                StockFlowDTO dto= StockFlowDTO.builder().build().setReelId(spReelidList);
                String responseStr = HttpClientUtil.httpPostWithJSON(inoneUrl + STOCK_FLOW_URL, JSONObject.toJSONString(dto), headerParamsMap);
                JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
                if (null != json) {
                    String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
                    if (RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
                        JsonNode boNode = json.get(JSON_BO);
                        if (boNode.size() > INT_0) {
                            List<StockFlowOutDTO> stockFlowOutDTOS = JacksonJsonConverUtil.jsonToListBean(String.valueOf(boNode), new TypeReference<List<StockFlowOutDTO>>() {
                            });
                            //赋值交易时间
                            getStockFlowList(mslServiceLifeInfoList,stockFlowOutDTOS);
                        }
                    }
                }
            }
        }catch (Exception e) {
            log.error("getDeliveryDateByMslServiceLife take exception:", e);
        }

        //分批处理
        List<List<MslServiceLifeInfo>> spMslList = CommonUtils.splitList(mslServiceLifeInfoList, INT_500);
        //修改潮敏信息数据
        for (List<MslServiceLifeInfo> upMslList : spMslList) {
            mslServiceLifeRepository.batchUpdateMslServiceLife(upMslList);
        }
    }

    /**
     * UTC时间格式转换为yyyy-MM-dd HH:mm:ss格式
     * @param isoDateTimeStr
     * @return
     */
    public String formattedDateTime(String isoDateTimeStr){
        try {
            Instant instant = Instant.parse(isoDateTimeStr);
            if (instant == null) {
                return STRING_EMPTY;
            }
            // 将Instant转换为默认时区的LocalDateTime
            LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            // 定义目标格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMATE_FULL);
            // 格式化LocalDateTime为字符串
            return localDateTime.format(formatter);
        } catch (DateTimeParseException e) {
            log.error("formattedDateTime take exception:", e);
            return STRING_EMPTY;
        }
    }

    /**
     * 赋值交易时间
     * @param mslServiceLifeInfoList 潮敏信息
     * @param stockFlowOutDTOS //仓储中心库存交易信息
     */
    public void getStockFlowList(List<MslServiceLifeInfo> mslServiceLifeInfoList,List<StockFlowOutDTO> stockFlowOutDTOS){
        Map<List<String>, String> dateMap = stockFlowOutDTOS.stream()
                .collect(Collectors.toMap(
                        m -> Arrays.asList(m.getReelid(), m.getSrcBillNo()),
                        m -> m.getJournalDate(),
                        (existing, replacement) -> existing  // 处理键冲突，保留第一个值
                ));
        // 赋值交易时间
        mslServiceLifeInfoList.forEach(m -> {
            List<String> key = Arrays.asList(m.getSerialnumber(), m.getExternalorderkey2());
            if (dateMap.containsKey(key)) {
                String deliveryDate = formattedDateTime(dateMap.get(key));
                if(!deliveryDate.equals(STRING_EMPTY)){
                    m.setDataStatus(INT_2);
                    m.setDeliveryDate(deliveryDate);
                }
            }
        });
    }

    /**
     * 获取数据字典值
     * @param dto
     * @return
     */
    public List<SysLookupValuesDTO> getLookupValues(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }

    /**
     * 获取库存交易时间
     */
    @Override
    public void getStockFlowByRidJob(){
        //获取待初始化交易信息的潮敏信息
        MslServiceLifeInfo mslServiceLifeInfo = MslServiceLifeInfo.builder().build().setDataStatus(Constant.INT_1);
        ThreadUtil.MSL_SERVICE_LIFE_EXECUTOR.execute(() -> {
            List<MslServiceLifeInfo> list = mslServiceLifeRepository.getMslServiceLifeList(mslServiceLifeInfo);
            log.info("############getStockFlowByRidJob开始调用", JsonUtil.toJSONString(list));
            getDeliveryDateByMslServiceLife(list);
            log.info("############getStockFlowByRidJob结束调用");
        });
    }

    /**
     * 潮敏物料车间寿命触发红点JOB
     */
    @Override
    public void mslServiceLifeRedDotJob(){
        //获取数据字典项
        SysLookupValuesDTO valuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000076);
        List<SysLookupValuesDTO> sysLookupValuesDTOS = this.getLookupValues(valuesDTO);
        String empNo = sysLookupValuesDTOS.stream()
                .filter(item -> Constant.LOOKUP_CODE_100007600004.equals(item.getLookupCode()))
                .findFirst().orElse(new SysLookupValuesDTO()).getLookupMeaning();
        //获取仓库
        List<SysLookupValuesDTO> sysLookupValuesDTOList = mslServiceLifeRepository.selectWhseId();
        log.info("############mslServiceLifeRedDotJob开始调用", JsonUtil.toJSONString(sysLookupValuesDTOList));
        ThreadUtil.MSL_SERVICE_LIFE_EXECUTOR.execute(() -> {
            List<MslServiceLifeInfo> allList=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(sysLookupValuesDTOList)){
                //按仓库计算
                sysLookupValuesDTOList.forEach(e -> {
                    //获取需触发红点的潮敏信息
                    MslServiceLifeInfo mslServiceLifeInfo = MslServiceLifeInfo.builder().build().setWhseid(e.getLookupMeaning()).setIsWarning(Constant.INT_0);
                    List<MslServiceLifeInfo> list = mslServiceLifeRepository.getMslServiceLifeRedDot(mslServiceLifeInfo);
                    if(CollectionUtils.isNotEmpty(list)){
                        //触发红点
                        sendRedDotTask(list,empNo,e);
                        allList.addAll(list);
                    }
                });
            }
            if(CollectionUtils.isNotEmpty(allList)){
                List<List<MslServiceLifeInfo>> splitList = CommonUtils.splitList(allList, INT_500);
                for (List<MslServiceLifeInfo> mslList : splitList) {
                    List<Integer> keyList=mslList.stream()
                            .map(MslServiceLifeInfo::getSerialkey)
                            .collect(Collectors.toList());
                    //修改预警状态
                    mslServiceLifeRepository.updateMslServiceLifeIsWarning(keyList);
                }
            }
            log.info("############mslServiceLifeRedDotJob结束调用", JsonUtil.toJSONString(allList));
        });
    }

    /**
     * 潮敏物料车间寿命预警红点
     * @param list 潮敏物料车间寿命预警信息
     */
    public void sendRedDotTask(List<MslServiceLifeInfo> list,String empNo,SysLookupValuesDTO dto) {
        try {
            Map<String, String> mapHeader = new HashMap<>();
            mapHeader.put(X_FACTORY_ID, dto.getAttribute3());
            mapHeader.put(X_EMP_NO,  empNo);
            mapHeader.put(X_AUTH_VALUE, authToken);
            list.forEach(e -> {
                RedDotTaskDTO redDotTaskDTO = new RedDotTaskDTO();
                Map<String, String> descMap = getRedDotDesc(dto,e);//仓库类型不同的红点描述拼接
                redDotTaskDTO.setEqpCode(descMap.get(EQP_NAME));
                redDotTaskDTO.setEqpName(descMap.get(EQP_NAME));
                redDotTaskDTO.setFloor(B25);
                redDotTaskDTO.setReddotCode(DISTR06);
                redDotTaskDTO.setDesc(descMap.get(DESC));
                redDotTaskDTO.setSource(AUTOMATIC_ORDER);
                redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
                HttpClientUtil.httpPostWithJSON(redDotUrl, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
            });
        }catch (Exception ex) {
            log.error("潮敏物料车间寿命预警红点发送失败:", ex);
        }
    }

    /**
     * 获取红点发送信息
     * @param dto
     * @param mslServiceLifeInfo
     * @return
     */
    public Map<String, String> getRedDotDesc(SysLookupValuesDTO dto,MslServiceLifeInfo mslServiceLifeInfo){
        StringBuilder desc = new StringBuilder();
        Map<String, String> descMap = new HashMap<>();
        //整机按仓库发送，部件按班组发送,班组为空按仓库发送
        String locationName = dto.getDescription();
        if (dto.getDbType() == INT_1 && Tools.isNotEmpty(mslServiceLifeInfo.getClassgroup())) {
            locationName = mslServiceLifeInfo.getClassgroup();
        }
        descMap.put(EQP_NAME, MSL_SERVICE_LIFE_RED + locationName);
        desc.append(WM_WHSE_NAME).append(SEMICOLON).append(dto.getDescription()).append(CHINESE_COMMA)
                .append(NO_NAME).append(SEMICOLON).append(mslServiceLifeInfo.getSku()).append(CHINESE_COMMA)
                .append(BARCODE_NAME).append(SEMICOLON).append(mslServiceLifeInfo.getLottable02()).append(CHINESE_COMMA)
                .append(REELID).append(SEMICOLON).append(mslServiceLifeInfo.getSerialnumber()).append(CHINESE_COMMA)
                .append(MSL_SERVICE_LIFE);
        descMap.put(DESC,  desc.toString());
        return descMap;
    }

    /**
     * 获取潮敏车间寿命信息
     * @param dto
     * @return
     */
    @Override
    public MslServiceLifeInfoVo queryMslServiceLife(MslServiceLifeInfo dto){
        dto.setStartRow((dto.getPageIndex()- Constant.INT_1) * dto.getPageSize()+Constant.INT_1);
        dto.setEndRow(dto.getPageIndex()*dto.getPageSize());
        MslServiceLifeInfoVo listVo=new MslServiceLifeInfoVo();
        listVo.setTotal(mslServiceLifeRepository.getMslServiceLifeAndOrderCount(dto));
        //获取潮敏信息记录
        List<MslServiceLifeInfo> list = mslServiceLifeRepository.getMslServiceLifeAndOrder(dto);
        listVo.setMslServiceLifeInfoList(list);
        return listVo;
    }

    /**
     * 潮敏物料车间寿命查询接口
     * @param dto
     * @return
     */
    @Override
    public List<MslServiceLifeInfo> mslServiceLifeQuery(MslServiceLifeInfo dto){
        BusiAssertException.isEmpty(dto.getSerialnumber(), MessageId.MSL_SERIALNUMBER_IS_NOT_EMPTY);
        BusiAssertException.isEmpty(dto.getSku(), MessageId.MSL_SKU_IS_NOT_EMPTY);
        BusiAssertException.isEmpty(dto.getLottable02(), MessageId.MSL_LOTTABLE02_IS_NOT_EMPTY);
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.MSL_WMWHSE_IS_NOT_EMPTY);
        //获取潮敏信息记录
        return mslServiceLifeRepository.getMslServiceLifeAndOrder(dto);
    }

    /**
     * 获取潮敏车间-仓库信息
     * @return
     */
    @Override
    public List<SysLookupValuesDTO> selectWhseId(){
       return mslServiceLifeRepository.selectWhseId();
    }

    /**
     * 导出记录
     * @param dto
     */
    @Override
    public void exportExcel(MslServiceLifeInfo dto){
        int total = mslServiceLifeRepository.getMslServiceLifeAndOrderCount(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            MslServiceLifeInfo param = ((MslServiceLifeInfo) params).setStartRow(statRow).setEndRow(endRow);
            //获取潮敏信息记录
            return new ArrayList<>(mslServiceLifeRepository.getMslServiceLifeAndOrder(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.MSL_SERVICE_LIFE_NAME).setFileName(Constant.MSL_SERVICE_LIFE_EXPORT).setSheetName(Constant.SHEET1)
                .setQueryParams(dto).setPojoClass(MslServiceLifeInfo.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }
}
