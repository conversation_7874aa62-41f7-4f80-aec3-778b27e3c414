package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.InventoryholdRecordService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.step.ApprovalProcessInfoRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.InventoryholdRecordEmailVO;
import com.zte.interfaces.infor.vo.InventoryholdRecordListVO;
import com.zte.interfaces.infor.vo.InventoryholdRecordVO;
import com.zte.interfaces.step.dto.ApprovalProcessInfoDTO;
import com.zte.interfaces.step.dto.ApprovalProcessLogDTO;
import com.zte.iss.approval.sdk.bean.FlowStartDTO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.LOWERCASE_SYSTEM;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_4000;
import static com.zte.resourcewarehouse.common.constant.ResponseConstant.ApplicationName.ZTE_SCM_INFOR_MOVE;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InventoryholdRecordServiceImpl implements InventoryholdRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryholdRecordServiceImpl.class);

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private InforBarcodeCenterRepository barcodeCenterRepository;
    @Autowired
    private StepIscpRepository stepIscpRepository;
    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;
    @Autowired
    private EmailUtil emailUtil;
    @Autowired
    private ApprovalProcessInfoRepository approvalProcessInfoRepository;

    @Value("${redDot.task.url:}")
    private String url;

    @Value(("${redDot.task.token:}"))
    private String authToken;

    @Override
    public void synInforInventoryholdRecord(List<InventoryholdDTO> list) {

        // 校验iSRM的入参
        checkInforInventoryholdRecord(list);

        // 保存iSRM的入参
        inventoryholdRecordRepository.insertInventoryholdRecordLog(list);

        // 组装数据
        List<InventoryholdRecord> inventoryholdRecordList = new ArrayList<>();
        List<String> wms = barcodeCenterRepository.getInventoryHoldWmwhids();
        BusiAssertException.isEmpty(wms, MessageId.WHSEID_CAN_NOT_BE_EMPTY);
        for (String whseid : wms) {
            for (InventoryholdDTO inventoryholdDTO : list) {
                InventoryholdRecord inventoryholdRecord = new InventoryholdRecord();
                BeanUtils.copyProperties(inventoryholdDTO, inventoryholdRecord);
                inventoryholdRecord.setWhseid(whseid);
                inventoryholdRecord.setItemBarcode(inventoryholdDTO.getItembarcode());
                inventoryholdRecord.setRemark(inventoryholdDTO.getHoldReasonDsc());
                inventoryholdRecord.setHoldStatus(BIG_1); //默认生效
                inventoryholdRecord.setInforStatus(BIG_0); //默认成功
                inventoryholdRecord.setInforFailTimes(BIG_0); //默认0次
                inventoryholdRecord.setAddwho(inventoryholdDTO.getApplyByName() + inventoryholdDTO.getApplyByNo()); //冻结申请人
                inventoryholdRecord.setEditwho(inventoryholdDTO.getApplyByName() + inventoryholdDTO.getApplyByNo()); //冻结申请人
                if(Tools.equals(SPOT_CHECK, inventoryholdDTO.getHoldReason())){
                    inventoryholdRecord.setEditDeptNo(inventoryholdDTO.getApplyDeptNo());
                    inventoryholdRecord.setEditDeptName(inventoryholdDTO.getApplyDeptName());
                }
                inventoryholdRecordList.add(inventoryholdRecord);
            }
        }

        // 保存采购同步的库存冻结记录
        CommonUtils.splitList(inventoryholdRecordList, NumConstant.INT_500).forEach(i ->
                ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> inventoryholdRecordRepository.insertInventoryholdRecord(i)));

    }

    @Override
    public void unfreezeInventoryhold(InventoryholdDTO dto) {
        // 校验iSRM的解冻入参
        checkUnfreezeInventoryhold(dto);
        //记录日志
        dto.setHoldStatus(BIG_0);//默认失效
        List<InventoryholdDTO> list=new ArrayList<>();
        list.add(dto);
        inventoryholdRecordRepository.insertInventoryholdRecordLog(list);
        //处理状态
        List<InventoryholdRecordDTO> inventoryholdRecordList=new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecord=new InventoryholdRecordDTO();
        inventoryholdRecord.setItemBarcode(dto.getItembarcode());
        inventoryholdRecord.setHoldReason(dto.getHoldReason());
        inventoryholdRecord.setHoldStatus(dto.getHoldStatus());
        inventoryholdRecord.setEditwho(dto.getApplyByName() + dto.getApplyByNo()); //冻结申请人
        inventoryholdRecord.setEditDeptNo(dto.getApplyDeptNo()); //冻结申请部门
        inventoryholdRecord.setEditDeptName(dto.getApplyDeptName()); //冻结申请部门名称
        inventoryholdRecord.setApprovedResult(FLAG_Y);//默认冻结审批通过
        inventoryholdRecordList.add(inventoryholdRecord);
        inventoryholdRecordRepository.updateBatchInventoryholdRecord(inventoryholdRecordList);
    }

    /**
     * 校验iSRM的解冻入参
     */
    public void checkUnfreezeInventoryhold(InventoryholdDTO dto) {
        // 1.校验非空
        BusiAssertException.isEmpty(dto, MessageId.INPUT_DATA_CAN_NOT_NULL);
        // 2.校验入参是否为空
        BusiAssertException.isEmpty(dto.getApplyByName(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getApplyByNo(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getApplyDeptNo(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getApplyDeptName(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getHoldReason(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getItembarcode(), MessageId.NO_PARAMS);
        // 3.冻结原因必须是“库存抽检”
        BusiAssertException.isTrue(Tools.notEquals(SPOT_CHECK, dto.getHoldReason()), MessageId.HOLD_REASON_NOT_SPOT_CHECK);
        // 4.校验库存冻结记录的状态：存在失效则提示失效
        List<String> itemBarcodeList =new ArrayList<>();
        itemBarcodeList.add(dto.getItembarcode());
        InventoryholdRecordDTO unDto = InventoryholdRecordDTO.builder().build()
                .setHoldReason(dto.getHoldReason()).setItemBarcodeList(itemBarcodeList).setHoldStatus(BIG_0);
        int count = inventoryholdRecordRepository.getInventoryholdRecordNum(unDto);
        BusiAssertException.isTrue(count > INT_0, MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED);
    }

    /**
     * 校验iSRM的入参
     */
    public void checkInforInventoryholdRecord(List<InventoryholdDTO> list) {

        // 1.校验非空
        BusiAssertException.isEmpty(list, MessageId.INPUT_DATA_CAN_NOT_NULL);

        // 2.校验每批不能超过50个
        BusiAssertException.isTrue(list.size() > NumConstant.INT_50, MessageId.CAN_NOT_BE_GREATER_THAN_FIFTY);

        // 3.冻结原因都需要是“试样超采或库存抽检”
        List<InventoryholdDTO> inventoryholdDTOList = list.stream().filter(i -> Tools.notEquals(SAMPLE_HOLD, i.getHoldReason()) && Tools.notEquals(SPOT_CHECK, i.getHoldReason())).collect(Collectors.toList());
        BusiAssertException.isNotEmpty(inventoryholdDTOList, MessageId.HOLD_REASON_NOT_SAMPLE_HOLD);

        // 4.备注字段超长
        List<InventoryholdDTO> inventoryholdDTOList1 = list.stream().filter(i -> Tools.isNotEmpty(i.getHoldReasonDsc()) && i.getHoldReasonDsc().length() > INT_60).collect(Collectors.toList());
        BusiAssertException.isNotEmpty(inventoryholdDTOList1, MessageId.REMARK_TOO_LONG);

        // 5.校验库存冻结记录是否在infor已存在
        List<String> itemBarcodeList = list.stream().map(InventoryholdDTO::getItembarcode).collect(Collectors.toList());
        InventoryholdRecordDTO inventoryholdRecordDTO = InventoryholdRecordDTO.builder().build()
                .setWhseid(STRING_EMPTY).setHoldReason(list.get(INT_0).getHoldReason()).setItemBarcodeList(itemBarcodeList);
        int count = inventoryholdRecordRepository.getInventoryholdRecordNum(inventoryholdRecordDTO);
        BusiAssertException.isTrue(count > INT_0, MessageId.INVENTORY_HOLD_RECORD_EXISTS);
    }

    @Override
    public void deleteInventoryholdRecord(List<InventoryholdDTO> list) {

        InventoryholdRecordDTO inventoryholdRecordDTO = InventoryholdRecordDTO.builder().build().setHoldReason(SAMPLE_HOLD);
        if (Tools.isEmpty(list)) {
            return;
        }
        List<String> itemBarcodeList = list.stream().distinct().map(InventoryholdDTO::getItembarcode).collect(Collectors.toList());
        itemBarcodeList.removeAll(Collections.singleton(null));
        if (Tools.isEmpty(itemBarcodeList)) {
            return;
        }
        inventoryholdRecordDTO.setItemBarcodeList(itemBarcodeList);
        inventoryholdRecordRepository.deleteInventoryholdRecord(inventoryholdRecordDTO);

    }

    /**
     * 获取冻结原因
     */
    @Override
    public List<SysLookupValuesDTO> getInventoryholdCodeList(SysLookupValuesDTO dto) {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000037);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);

    }

    /**
     * 查询
     */
    @Override
    public InventoryholdRecordListVO getInventoryholdRecordList(InventoryholdRecordDTO dto) {

        InventoryholdRecordListVO listVo = new InventoryholdRecordListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(dto));
        listVo.setInventoryholdRecordDTOList(inventoryholdRecordRepository.getInventoryholdRecordList(dto));
        return listVo;
    }

    /**
     * 获取代码信息
     */
    /* Started by AICoder, pid:3e45f21a93a8443ab3160c4a77c2710a */
    @Override
    public InventoryholdRecordDTO getItemNoByItemBarcode(InventoryholdRecordDTO dto) {

        InventoryholdRecordDTO inventoryholdRecordDTO = inventoryholdRecordRepository.getItemNoByItemBarcode(dto);
        BusiAssertException.isEmpty(inventoryholdRecordDTO, MessageId.ITEM_BARCODE_NOT_EXISTS);
        if (Tools.isNotEmpty(inventoryholdRecordDTO.getSupplyNo())) {
            // 材料条码
            InventoryholdRecordDTO inventoryholdRecordDTOC = stepIscpRepository.getItemNoByItemId(inventoryholdRecordDTO);
            BusiAssertException.isEmpty(inventoryholdRecordDTOC, MessageId.BA_ITEM_NOT_EXISTS);
            inventoryholdRecordDTO.setItemNo(inventoryholdRecordDTOC.getItemNo());
            return inventoryholdRecordDTO;
        }

        // 单板条码
        InventoryholdRecordDTO inventoryholdRecordDTOD = stepIscpRepository.getItemNoByBomId(inventoryholdRecordDTO);
        BusiAssertException.isEmpty(inventoryholdRecordDTOD, MessageId.BA_BOM_HEAD_NOT_EXISTS);
        inventoryholdRecordDTO.setItemNo(inventoryholdRecordDTOD.getItemNo());
        return inventoryholdRecordDTO;
    }
    /* Ended by AICoder, pid:3e45f21a93a8443ab3160c4a77c2710a */

    /**
     * 维护
     */
    @Override
    public void saveInventoryholdRecord(InventoryholdRecordDTO dto) {

        checkDTO(dto);
        if (Tools.isNotEmpty(dto.getSerialkey())) {
            InventoryholdRecordDTO inventoryholdRecordDTO = InventoryholdRecordDTO.builder().build()
                    .setWhseid(dto.getWhseid()).setItemBarcode(dto.getItemBarcode()).setHoldReason(dto.getHoldReason())
                    .setApplyByNo(dto.getApplyByNo()).setApplyByName(dto.getApplyByName()).setApplyDeptNo(dto.getApplyDeptNo())
                    .setApplyDeptName(dto.getApplyDeptName()).setResponsibleByNo(dto.getResponsibleByNo())
                    .setResponsibleByName(dto.getResponsibleByName()).setResponsibleDeptNo(dto.getResponsibleDeptNo())
                    .setResponsibleDeptName(dto.getResponsibleDeptName()).setRemark(dto.getRemark()).setEditwho(dto.getEditwho())
                    .setEditDeptNo(dto.getEditDeptNo()).setEditDeptName(dto.getEditDeptName());
            inventoryholdRecordRepository.updateInventoryholdRecord(inventoryholdRecordDTO);

        } else {
            // 校验库存冻结记录是否在infor已存在
            List<String> itemBarcodeList = new ArrayList<>();
            itemBarcodeList.add(dto.getItemBarcode());
            dto.setItemBarcodeList(itemBarcodeList);
            int count = inventoryholdRecordRepository.getInventoryholdRecordNum(dto);
            BusiAssertException.isTrue(count > INT_0, MessageId.INVENTORY_HOLD_RECORD_EXISTS);

            //调用审批中心接口，判断是否是计划冻结和计划原因冻结
            /* Started by AICoder, pid:d326c397773d4bc09ebd8c0f0e13a9d3 */
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000037)
                    .setAttribute2(HOLD_APPROVE).setLookupMeaning(dto.getHoldReason());
            List<SysLookupValuesDTO> approvedList = inventoryholdRecordRepository.getIsNeedApproved(sysLookupValuesDTO);
            BusiAssertException.isTrue(approvedList.size() > INT_0 && Tools.isEmpty(dto.getApprovedByNo()), MessageId.INVENTORY_HOLD_APPROVEDBY_EMPTY);
            /* Ended by AICoder, pid:d326c397773d4bc09ebd8c0f0e13a9d3 */

            //保存库存冻结记录
            List<InventoryholdRecord> inventoryholdRecordList = new ArrayList<>();
            List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
            if (Tools.isEmpty(dto.getWhseid())) {
                List<String> wms = barcodeCenterRepository.getInventoryHoldWmwhids();
                BusiAssertException.isEmpty(wms, MessageId.WHSEID_CAN_NOT_BE_EMPTY);
                for (String whseid : wms) {
                    InventoryholdRecord inventoryholdRecord = new InventoryholdRecord();
                    BeanUtils.copyProperties(dto, inventoryholdRecord);
                    inventoryholdRecord.setWhseid(whseid);
                    inventoryholdRecord.setHoldStatus(BIG_1); //默认生效
                    inventoryholdRecord.setInforStatus(BIG_0); //默认成功
                    inventoryholdRecord.setInforFailTimes(BIG_0); //默认0次
                    inventoryholdRecordList.add(inventoryholdRecord);

                    InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
                    BeanUtils.copyProperties(inventoryholdRecord, inventoryholdRecordDTO);
                    inventoryholdRecordDTO.setApplyBy(dto.getApplyByName() + dto.getApplyByNo());
                    inventoryholdRecordDTO.setResponsibleBy(dto.getResponsibleByName() + dto.getResponsibleByNo());
                    inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
                }
            } else {
                InventoryholdRecord inventoryholdRecord = new InventoryholdRecord();
                BeanUtils.copyProperties(dto, inventoryholdRecord);
                inventoryholdRecord.setHoldStatus(BIG_1); //默认生效
                inventoryholdRecord.setInforStatus(BIG_0); //默认成功
                inventoryholdRecord.setInforFailTimes(BIG_0); //默认0次
                inventoryholdRecordList.add(inventoryholdRecord);

                InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
                BeanUtils.copyProperties(inventoryholdRecord, inventoryholdRecordDTO);
                inventoryholdRecordDTO.setApplyBy(dto.getApplyByName() + dto.getApplyByNo());
                inventoryholdRecordDTO.setResponsibleBy(dto.getResponsibleByName() + dto.getResponsibleByNo());
                inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
            }
            if (approvedList.size() > INT_0) {
                //把记录新增到日志表
                inventoryholdRecordList.forEach(e -> {
                    e.setHoldStatus(BIG_2);//需要审批的状态默认已保存
                });
            }
            inventoryholdRecordRepository.insertInventoryholdRecord(inventoryholdRecordList);

            List<InventoryholdRecordDTO> toapproveList =new ArrayList<>();
            toapproveList.add(dto);
            this.holdInventoryToApprove(toapproveList, approvedList, inventoryholdRecordDTOList,inventoryholdRecordList);

        }
    }

    /**
     * 冻结调用审批中心接口
     */
    /* Started by AICoder, pid:e92296de96954f52902cc20ba2b03d1f */
    private void holdInventoryToApprove(List<InventoryholdRecordDTO> toapproveList, List<SysLookupValuesDTO> approvedList, List<InventoryholdRecordDTO> inventoryholdRecordDTOList,List<InventoryholdRecord> inventoryholdRecordList) {
        if (approvedList.size() > INT_0) {
            if (Tools.isEmpty(toapproveList)) {
                return;
            }
            //获取隔离原因具体描述
            SysLookupValuesDTO holdDescDTO = SysLookupValuesDTO.builder().build()
                    .setLookupType(LOOKUP_TYPE_1000037)
                    .setLookupMeaning(toapproveList.get(INT_0).getHoldReason());
            List<SysLookupValuesDTO> holdDescList = inventoryholdRecordRepository.getIsNeedApproved(holdDescDTO);
            if (Tools.isNotEmpty(holdDescList)) {
                toapproveList.forEach(e -> {
                    e.setHoldReasonDsc(holdDescList.get(INT_0).getDescription());//需要审批的状态默认已保存
                });
            }
            //根据条码不同，发送不同的审批邮件
            toApproveGroupByBarcode(toapproveList, inventoryholdRecordList);

        } else {
            // 冻结infor库存
            if (Tools.isEmpty(inventoryholdRecordDTOList)) {
                return;
            }
            inventoryholdRecordDTOList.forEach(i -> ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> holdInventory(i)));
        }
    }

    /**
     * /根据条码不同，发送不同的审批邮件
     * @param toapproveList
     * @param inventoryholdRecordList
     */
    private void toApproveGroupByBarcode(List<InventoryholdRecordDTO> toapproveList, List<InventoryholdRecord> inventoryholdRecordList) {
        Map<String, List<InventoryholdRecordDTO>> barcodeImportList = toapproveList.stream().collect(Collectors.groupingBy(e -> e.getItemBarcode()));
        barcodeImportList.forEach((k, v) -> {
            String strImportBusinessId = UUID.randomUUID().toString().replace("-", "");
            HoldFlowStartDTO flowStartDTO = HoldFlowStartDTO.builder().build()
                    .setApplyBy(v.get(INT_0).getApplyByName() + v.get(INT_0).getApplyByNo())
                    .setItemBarcode(v.get(INT_0).getItemNo() + HOLD_SPLIT + v.get(INT_0).getItemBarcode())
                    .setApproverId1(v.get(INT_0).getApprovedByNo())
                    .setEmailCc(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setUuid(strImportBusinessId)
                    .setEmpNo(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setRemark(v.get(INT_0).getRemark())
                    .setHoldReasonDsc(v.get(INT_0).getHoldReasonDsc());
            try {
                //查询仓库名称
                if (Tools.isNotEmpty(toapproveList.get(INT_0).getWhseid())) {
                    List<String> whseNameList = new ArrayList<>();
                    whseNameList.add(toapproveList.get(INT_0).getWhseid());
                    flowStartDTO.setWhseid(inventoryholdRecordRepository.getHoldWhseName(whseNameList));
                } else {
                    flowStartDTO.setWhseid(HOLD_ALL);
                }
                //调用接口日志
                ApprovalProcessLogDTO dtoLog = new ApprovalProcessLogDTO();
                dtoLog.setBillNO(strImportBusinessId)
                        .setInterfaceName(MOBILEAPPROVALSTART)
                        .setResponse(JSON.toJSONString(flowStartDTO).length() > INT_4000 ? JSON.toJSONString(flowStartDTO).substring(INT_0, INT_4000) : JSON.toJSONString(flowStartDTO));
                approvalProcessInfoRepository.insertMobileApprovalLog(dtoLog);
                List<InventoryholdRecord> addHoldLogList = inventoryholdRecordList.stream().filter(e -> (k.equals(e.getItemBarcode()))).collect(Collectors.toList());
                //把记录新增到日志表
                addHoldLogList.forEach(e -> {
                    e.setBusinessid(strImportBusinessId).setApproveType(INVENTORY_HOLD).setHoldStatus(BIG_2);//需要审批的状态默认已保存
                });
                inventoryholdRecordRepository.insertApproveHoldRecordLog(addHoldLogList);
                //调用审批中心接口
                this.holdFlowStart(flowStartDTO);
            } catch (Exception e) {
                LOGGER.error("####库存隔离失败###" + e.getMessage());
                //删除冻结记录表
                ApprovalProcessInfoDTO deleteLog = new ApprovalProcessInfoDTO();
                deleteLog.setBillNO(strImportBusinessId).setHoldStatus(NumConstant.BIG_2);
                inventoryholdRecordRepository.deleteBatchHoldInventroyRecord(deleteLog);
                e.printStackTrace();
            }
        });
    }
    /* Ended by AICoder, pid:e92296de96954f52902cc20ba2b03d1f */

    /**
     * 发送邮件（通过业务调度平台获取数据并发送邮件）
     */
    @Override
    public void inventoryholdRecordSendMail(String xEmpNo) {
        // 取发送的用户及排除通知部门
        List<String> lookupType = Arrays.asList(QUALITY_HOLD_LOOKUP_TYPE.split(Constant.COMMA));
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(lookupType);
        List<SysLookupValuesDTO> lookupList = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
        //发送用户数据字典
        List<SysLookupValuesDTO> sysLookupValuesDTOList = getlookupType(lookupList, LOOKUP_TYPE_1000038);
        if (Tools.isEmpty(sysLookupValuesDTOList)) {
            return;
        }
        //获取排除通知部门
        List<SysLookupValuesDTO> editDeptList = getlookupType(lookupList, LOOKUP_TYPE_1000045);
        //发送邮件
        ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> {
            try {
                //质量隔离通知DTO
                InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
                dto.setEmpNo(xEmpNo);
                dto.setHoldReasonType(STR_QUALITY_HOLD);
                dto.setInforStatus(BIG_0);
                dto.setSendMailType(INT_0);//单板
                if (Tools.isNotEmpty(editDeptList)) {
                    dto.setEditDeptNoList(editDeptList.stream().map(SysLookupValuesDTO::getLookupMeaning).distinct().collect(Collectors.toList()));
                }

                //冻结失败提醒DTO
                InventoryholdRecordDTO exDto = new InventoryholdRecordDTO();
                exDto.setEmpNo(xEmpNo);
                exDto.setInforStatus(BIG_1);
                exDto.setHoldStatus(BIG_1);
                exDto.setInforFailTimes(BIG_4);
                exDto.setSendMailType(INT_0);//单板

                //质量隔离单板
                this.getInventoryholdRecordSendMail(sysLookupValuesDTOList, dto, IWMS_DB_QUALITY_HOLD_HEAD, IWMS_DB_QUALITY_HOLD_NAME);
                //质量隔离材料
                dto.setSendMailType(Constant.INT_1);
                this.getInventoryholdRecordSendMail(sysLookupValuesDTOList, dto, IWMS_QUALITY_HOLD_HEAD, IWMS_QUALITY_HOLD_NAME);
                //质量隔离异常单板
                this.getInventoryholdRecordSendMail(sysLookupValuesDTOList, exDto, IWMS_DB_INVENTORY_HOLD_FAILED_HEAD, IWMS_DB_INVENTORY_HOLD_FAILED_NAME);
                //质量隔离异常材料
                exDto.setSendMailType(Constant.INT_1);
                this.getInventoryholdRecordSendMail(sysLookupValuesDTOList, exDto, IWMS_CL_INVENTORY_HOLD_FAILED_HEAD, IWMS_CL_INVENTORY_HOLD_FAILED_NAME);
                //其他隔离异常
                exDto.setSendMailType(Constant.INT_2);
                this.getInventoryholdRecordSendMail(sysLookupValuesDTOList, exDto, IWMS_INVENTORY_HOLD_FAILED_HEAD, IWMS_INVENTORY_HOLD_FAILED_NAME);

            } catch (Exception e) {
                LOGGER.error(" 库存冻结发送邮件生成查询结果excel失败 " + e.getMessage());
            }
        });
    }

    /**
     * @param sysLookupValuesDTOList 通知及提醒收件人List
     * @param inventoryholdRecordDTO 查询条件
     * @param fileName               邮件主题
     * @param title                  exec主题
     */
    public void getInventoryholdRecordSendMail(List<SysLookupValuesDTO> sysLookupValuesDTOList, InventoryholdRecordDTO inventoryholdRecordDTO, String fileName, String title) throws Exception {
        int count = inventoryholdRecordRepository.getInventoryholdRecordSendMailTotal(inventoryholdRecordDTO);
        if (count <= INT_0) {
            return;
        }

        int total = count > INT_20000 ? count : INT_20001;
        //上传文档云发邮件给接收人
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            InventoryholdRecordDTO param = ((InventoryholdRecordDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(inventoryholdRecordRepository.getInventoryholdRecordSendMail(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(title).setFileName(fileName).setSheetName(SHEET1)
                .setQueryParams(inventoryholdRecordDTO).setPojoClass(InventoryholdRecordEmailVO.class).setTotal((long) total)
                .setReceipt(inventoryholdRecordDTO.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.makeBigExcelFileTemp(excelParams);

        //获取配置邮件接收人
        List<String> users = sysLookupValuesDTOList.stream().filter(t -> String.valueOf(t.getAttribute1()).equals(fileName)).map(SysLookupValuesDTO::getLookupMeaning).distinct().collect(Collectors.toList());
        //隔离失败超4次,异常隔离获取申请人工号
        if (fileName.equals(IWMS_DB_INVENTORY_HOLD_FAILED_HEAD) || fileName.equals(IWMS_CL_INVENTORY_HOLD_FAILED_HEAD) || fileName.equals(IWMS_INVENTORY_HOLD_FAILED_HEAD)) {
            users.addAll(inventoryholdRecordRepository.getInventoryholdRecordSendApplyByNo(inventoryholdRecordDTO));
            log.info("隔离失败超4次提醒触发红点任务");
            sendRedDotTask(inventoryholdRecordDTO);//隔离失败超4次提醒触发红点任务
        }
        String receipts = StringUtils.join(
                users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).distinct().collect(Collectors.toList()), SPLIT_22);
        if (Tools.isNotEmpty(excelParams.getPath())) {
            CloudDiskHelper cloudDiskHelper = (CloudDiskHelper) SpringContextUtil.getBean(CLOUD_DISK);
            String fileKey = cloudDiskHelper.fileUpload(excelParams.getPath(), inventoryholdRecordDTO.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
            FileUtil.del(excelParams.getPath());
            String downLoadUrl = ExcelUtil.getFileDownloadUrl(fileKey, (String) null, inventoryholdRecordDTO.getEmpNo());
            emailUtil.sendMail(receipts, fileName, BLANK, INFOR_ALLCATE_EXCEPTION_MONITOR_DOWN + STR_BLANK + downLoadUrl, BLANK);
        }
    }

    /**
     * 隔离失败超4次提醒触发红点任务
     * @param param
     */
    public void sendRedDotTask(InventoryholdRecordDTO param) {
        try {
            param.setStartRow(null).setEndRow(null);
            List<InventoryholdRecordEmailVO> emailVOList = inventoryholdRecordRepository.getInventoryholdRecordSendMail(param);
            emailVOList = emailVOList.stream()
                    .filter(vo -> APPROVE_PSB.equals(vo.getEditDeptName()))
                    .collect(Collectors.toList());
            Map<String, String> mapHeader = null;
            RedDotTaskDTO redDotTaskDTO = null;
            StringBuilder desc = null;
            for (InventoryholdRecordEmailVO inventoryholdRecordEmailVO : emailVOList) {
                mapHeader = new HashMap<>();
                mapHeader.put(X_FACTORY_ID, STR_51);
                mapHeader.put(X_EMP_NO,  param.getEmpNo());
                mapHeader.put(X_AUTH_VALUE, authToken);
                desc = new StringBuilder(WM_WHSE_NAME).append(SEMICOLON).append(inventoryholdRecordEmailVO.getWhseid()).append(CHINESE_COMMA)
                        .append(MATERIAL_CODE).append(SEMICOLON).append(inventoryholdRecordEmailVO.getItemNo()).append(CHINESE_COMMA)
                        .append(MATERIAL_BARCODE).append(SEMICOLON).append(inventoryholdRecordEmailVO.getItemBarcode()).append(CHINESE_COMMA)
                        .append(QUARANTINE_REASON).append(SEMICOLON).append(inventoryholdRecordEmailVO.getHoldReasonDsc());
                redDotTaskDTO = new RedDotTaskDTO();
                redDotTaskDTO.setEqpCode(RED_DOT_TASK);
                redDotTaskDTO.setEqpName(RED_DOT_TASK);
                redDotTaskDTO.setFloor(B25);
                redDotTaskDTO.setReddotCode(DISTR01);
                redDotTaskDTO.setDesc(desc.toString());
                redDotTaskDTO.setSendTo(inventoryholdRecordEmailVO.getEditwho());
                redDotTaskDTO.setSendCc(inventoryholdRecordEmailVO.getEditwho());
                redDotTaskDTO.setSource(AUTOMATIC_ORDER);
                redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
                redDotTaskDTO.setAttr1(inventoryholdRecordEmailVO.getHoldReasonDsc());
                redDotTaskDTO.setAttr2(inventoryholdRecordEmailVO.getItemNo());
                redDotTaskDTO.setAttr3(inventoryholdRecordEmailVO.getItemBarcode());
                HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
            }
        }catch (Exception ex) {
            log.error("红点任务发送失败:", ex);
        }
    }

    /**
     * 获取对应的数据字典
     *
     * @param list
     * @param lookupType
     * @return
     */
    private List<SysLookupValuesDTO> getlookupType(List<SysLookupValuesDTO> list, String lookupType) {
        return list.stream().filter(t -> String.valueOf(t.getLookupType()).equals(lookupType)).collect(Collectors.toList());
    }

    /**
     * 导入库存冻结
     *
     * @param list
     */
    @Override
    public void importInventoryHold(List<InventoryholdRecordDTO> list) {
        //校验数据
        checkImportInventoryHold(list);
        //获取代码信息
        getBatchItemNoByItemBarcode(list);
        // 校验库存冻结记录是否在infor已存在
        List<String> itemBarcodeList = list.stream().map(InventoryholdRecordDTO::getItemBarcode).collect(Collectors.toList());
        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setItemBarcodeList(itemBarcodeList);
        dto.setHoldReason(list.get(INT_0).getHoldReason());
        int count = inventoryholdRecordRepository.getInventoryholdRecordNum(dto);
        BusiAssertException.isTrue(count > INT_0, MessageId.INVENTORY_HOLD_RECORD_EXISTS);
        List<String> wms = barcodeCenterRepository.getInventoryHoldWmwhids();
        BusiAssertException.isEmpty(wms, MessageId.WHSEID_CAN_NOT_BE_EMPTY);

        //调用审批中心接口，判断是否是计划冻结和计划原因冻结
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000037)
                .setAttribute2(HOLD_APPROVE).setLookupMeaning(list.get(INT_0).getHoldReason());
        List<SysLookupValuesDTO> approvedList = inventoryholdRecordRepository.getIsNeedApproved(sysLookupValuesDTO);
        BusiAssertException.isTrue(approvedList.size() > INT_0 && Tools.isEmpty(list.get(INT_0).getApprovedByNo()), MessageId.INVENTORY_HOLD_APPROVEDBY_EMPTY);

        // 保存库存冻结记录
        List<InventoryholdRecord> inventoryholdRecordList = new ArrayList<>();
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        for (InventoryholdRecordDTO item : list) {
            for (String whseid : wms) {
                InventoryholdRecord inventoryholdRecord = new InventoryholdRecord();
                BeanUtils.copyProperties(item, inventoryholdRecord);
                inventoryholdRecord.setWhseid(whseid);
                inventoryholdRecord.setHoldStatus(BIG_1); //默认生效
                inventoryholdRecord.setInforStatus(BIG_0); //默认成功
                inventoryholdRecord.setInforFailTimes(BIG_0); //默认0次
                inventoryholdRecordList.add(inventoryholdRecord);

                InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
                BeanUtils.copyProperties(inventoryholdRecord, inventoryholdRecordDTO);
                inventoryholdRecordDTO.setApplyBy(item.getApplyByName() + item.getApplyByNo());
                inventoryholdRecordDTO.setResponsibleBy(item.getResponsibleByName() + item.getResponsibleByNo());
                inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
            }
        }
        if (approvedList.size() > INT_0) {
            //把记录新增到日志表
            inventoryholdRecordList.forEach(e -> {
                e.setHoldStatus(BIG_2);//需要审批的状态默认已保存
            });
        }
        CommonUtils.splitList(inventoryholdRecordList, INT_200).forEach(i -> inventoryholdRecordRepository.insertInventoryholdRecord(i));
        //调用审批中心接口
        this.holdInventoryToApprove(list, approvedList, inventoryholdRecordDTOList,inventoryholdRecordList);
    }

    /**
     * 批量获取代码信息
     */
    /* Started by AICoder, pid:84b2653432dc4bd7b10b0b91bbbd34b4 */
    public void getBatchItemNoByItemBarcode(List<InventoryholdRecordDTO> list) {
        // 获取材料
        List<InventoryholdRecordDTO> itemNolist = inventoryholdRecordRepository.getBatchItemNoByItemBarcode(list);
        // 检查材料列表是否为空
        BusiAssertException.isEmpty(itemNolist, MessageId.ITEM_BARCODE_NOT_EXISTS);
        // 校验220条码的合法性
        BusiAssertException.isTrue(list.size() != itemNolist.size(), MessageId.ITEM_BARCODE_NOT_EXISTS);

        // 遍历原始列表，设置itemId、supplyNo和supplyName
        for (InventoryholdRecordDTO dto : list) {
            InventoryholdRecordDTO inventoryholdRecordDTO = itemNolist.stream().filter(t -> t.getItemBarcode().equals(dto.getItemBarcode())).findFirst().orElse(new InventoryholdRecordDTO());
            dto.setItemId(inventoryholdRecordDTO.getItemId());
            dto.setSupplyNo(inventoryholdRecordDTO.getSupplyNo());
            dto.setSupplyName(inventoryholdRecordDTO.getSupplyName());
        }

        // 有供应商编码，代表是材料代码
        List<InventoryholdRecordDTO> itemSupplylist = itemNolist.stream().filter(t -> Tools.isNotEmpty(t.getSupplyNo())).collect(Collectors.toList());
        if (Tools.isNotEmpty(itemSupplylist)) {
            List<InventoryholdRecordDTO> inventoryholdRecordDTOC = stepIscpRepository.getBatchItemNoByItemId(itemSupplylist);
            for (InventoryholdRecordDTO dto : list) {
                InventoryholdRecordDTO inventoryholdRecordDTO = inventoryholdRecordDTOC.stream().filter(t -> t.getItemId().equals(dto.getItemId())).findFirst().orElse(new InventoryholdRecordDTO());
                dto.setItemNo(inventoryholdRecordDTO.getItemNo());
            }
        }

        // 无供应商编码，代表是材料代码
        List<InventoryholdRecordDTO> itemNoSupplylist = itemNolist.stream().filter(t -> Tools.isEmpty(t.getSupplyNo())).collect(Collectors.toList());
        if (Tools.isNotEmpty(itemNoSupplylist)) {
            List<InventoryholdRecordDTO> inventoryholdRecordDTOD = stepIscpRepository.getBatchItemNoByBomId(itemNoSupplylist);
            for (InventoryholdRecordDTO dto : list) {
                InventoryholdRecordDTO inventoryholdRecordDTO = inventoryholdRecordDTOD.stream().filter(t -> t.getItemId().equals(dto.getItemId())).findFirst().orElse(new InventoryholdRecordDTO());
                dto.setItemNo(inventoryholdRecordDTO.getItemNo());
            }
        }
    }
    /* Ended by AICoder, pid:84b2653432dc4bd7b10b0b91bbbd34b4 */

    /**
     * 校验导入数据准确性
     *
     * @param list
     */
    public void checkImportInventoryHold(List<InventoryholdRecordDTO> list) {
        // 会话已失效，请先登录
        BusiAssertException.isTrue(Tools.isEmpty(list.get(0).getAddwho()) || Tools.isEmpty(list.get(0).getEditwho()), MessageId.PLEASE_LOGIN_FIRST);
        // 创建一个LinkedHashSet并添加列表元素
        LinkedHashSet<InventoryholdRecordDTO> linkedHashSet = new LinkedHashSet<>(list);
        //校验220条码是否重复
        BusiAssertException.isTrue(list.size() != linkedHashSet.size(), MessageId.ITEMBARCODE_REPEAT_CHECK);
    }

    /**
     * 校验字段
     */
    public void checkDTO(InventoryholdRecordDTO dto) {

        // 会话已失效，请先登录
        BusiAssertException.isTrue(Tools.isEmpty(dto.getAddwho()) || Tools.isEmpty(dto.getEditwho()) || Tools.isEmpty(dto.getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
        // 备注长度超过60
        BusiAssertException.isTrue(Tools.isNotEmpty(dto.getRemark()) && dto.getRemark().length() > INT_60, MessageId.REMARK_TOO_LONG);

    }

    /**
     * 生效
     */
    @Override
    public void effectiveInventoryholdRecord(List<InventoryholdRecordDTO> list) {

        if (Tools.isEmpty(list)) {
            return;
        }
        BusiAssertException.isTrue(Tools.isEmpty(list.get(list.size()-Constant.INT_1).getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
        // 校验库存冻结记录的状态：失效
        List<BigDecimal> serialkeyList = list.stream().map(InventoryholdRecordDTO::getSerialkey).collect(Collectors.toList());
        InventoryholdRecordDTO dto = InventoryholdRecordDTO.builder().build()
                .setSerialkeyList(serialkeyList).setHoldStatus(list.get(INT_0).getHoldStatus());
        int count = inventoryholdRecordRepository.getInventoryholdRecordNum(dto);
        BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EFFECTED);

        // 生效
        for (InventoryholdRecordDTO inventoryholdRecordDTO : list) {
            inventoryholdRecordDTO.setHoldStatus(BIG_1); //生效
            inventoryholdRecordRepository.updateInventoryholdRecord(inventoryholdRecordDTO);
        }

        // 冻结infor库存
        list.forEach(i -> ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> holdInventory(i)));
    }

    /**
     * 失效
     */
    @Override
    /* Started by AICoder, pid:fd08115ddf2a4e949c00f09c40582fa8 */
    public void expireInventoryholdRecord(List<InventoryholdRecordDTO> list) {
        if (Tools.isEmpty(list)) {
            return;
        }
        BusiAssertException.isTrue(Tools.isEmpty(list.get(list.size()-Constant.INT_1).getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
        // 校验库存冻结记录的状态：生效
        List<BigDecimal> serialkeyList = list.stream().map(InventoryholdRecordDTO::getSerialkey).collect(Collectors.toList());
        InventoryholdRecordDTO dto = InventoryholdRecordDTO.builder().build()
                .setSerialkeyList(serialkeyList).setHoldStatus(list.get(INT_0).getHoldStatus());
        int count = inventoryholdRecordRepository.getInventoryholdRecordNum(dto);
        BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED);

        //根据冻结类型分类
        Map<String, List<InventoryholdRecordDTO>> groupedList = list.stream()
                .collect(Collectors.groupingBy(InventoryholdRecordDTO::getHoldReasonType));

        List<InventoryholdRecordDTO> qualityHoldList = groupedList.getOrDefault(STR_QUALITY_HOLD, Collections.emptyList());
        List<InventoryholdRecordDTO> customsList = groupedList.getOrDefault(STR_SAMPLE_HOLD, Collections.emptyList());
        List<InventoryholdRecordDTO> combinedList = Stream.concat(qualityHoldList.stream(), customsList.stream())
                .collect(Collectors.toList());
        List<InventoryholdRecordDTO> planHoldList = groupedList.getOrDefault(STR_PLAN_HOLD, Collections.emptyList());
        List<InventoryholdRecordDTO> otherHoldList = groupedList.entrySet().stream()
                .filter(entry -> !STR_QUALITY_HOLD.equals(entry.getKey()) && !STR_PLAN_HOLD.equals(entry.getKey()) && !STR_SAMPLE_HOLD.equals(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList());
        List<InventoryholdRecordDTO> allList = new ArrayList<>();
        List<InventoryholdRecordDTO> planApproveHoldList = new ArrayList<>();
        List<InventoryholdRecordDTO> qualityApproveHoldList = new ArrayList<>();
        for (InventoryholdRecordDTO inventoryholdPlanDTO : planHoldList) {
            //如果冻结申请人和当前操作人一致，或者当前操作人是配送部，或者是单板，则不用审批
            if ((inventoryholdPlanDTO.getApplyBy()).equals(inventoryholdPlanDTO.getEditwho()) ||
                    inventoryholdPlanDTO.getEditDeptName().equals(APPROVE_PSB) || inventoryholdPlanDTO.getItemNo().startsWith(STR_1)) {
                otherHoldList.add(inventoryholdPlanDTO);
            } else {
                inventoryholdPlanDTO.setHoldStatus(BIG_3); //审批中
                planApproveHoldList.add(inventoryholdPlanDTO);
            }
        }
        for (InventoryholdRecordDTO inventoryCombinedDTO : combinedList) {
            //如果当前操作人是配送部，或者是单板，则不用审批
            if (inventoryCombinedDTO.getEditDeptName().equals(APPROVE_PSB) || inventoryCombinedDTO.getItemNo().startsWith(STR_1)) {
                otherHoldList.add(inventoryCombinedDTO);
            } else {
                inventoryCombinedDTO.setHoldStatus(BIG_3); //审批中
                qualityApproveHoldList.add(inventoryCombinedDTO);
            }
        }
        // 失效
        otherHoldList.forEach(e -> {
            e.setHoldStatus(BIG_0);
        });
        allList.addAll(qualityApproveHoldList);
        allList.addAll(planApproveHoldList);
        allList.addAll(otherHoldList);

        //校验质量类型的审批人是否为空
        if(Tools.isNotEmpty(qualityApproveHoldList)) {
            List<String> qualityApproveList = qualityApproveHoldList.stream().map(k -> k.getItemNo()).distinct().collect(Collectors.toList());
            List<HoldFlowStartDTO> getApprovedList = stepIscpRepository.getApprovedByItemNo(qualityApproveList);
            BusiAssertException.isTrue(getApprovedList.size() != qualityApproveList.size(), MessageId.ITEM_TQE_STQE_NOT_EXISTS);
            getApprovedList.forEach(e -> {
                qualityApproveHoldList.stream().filter(k -> k.getItemNo().equals(e.getItemNo())).forEach(sur -> {
                    sur.setApprovedByNo(e.getApproverId1());
                    sur.setApprovedByNo2(e.getApproverId2());
                });
            });
        }
        //处理数据
        handleExpireData(allList, planApproveHoldList, qualityApproveHoldList, otherHoldList);
    }
    /* Ended by AICoder, pid:fd08115ddf2a4e949c00f09c40582fa8 */
    //处理数据
    /* Started by AICoder, pid:8b7448f655c44621b2d26ef1cac25064 */
    private void handleExpireData(List<InventoryholdRecordDTO> allList, List<InventoryholdRecordDTO> planApproveHoldList, List<InventoryholdRecordDTO> qualityApproveHoldList, List<InventoryholdRecordDTO> otherHoldList) {
        try {
            // 批量更新数据
            inventoryholdRecordRepository.updateBatchInventoryholdRecord(allList);

            // 发送审批中心
            this.sendApprovedByPlan(planApproveHoldList);
            this.sendApprovedByQuality(qualityApproveHoldList);

            // 不需要调审批中心的数据进行解冻infor库存
            otherHoldList.forEach(i -> ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> holdInventory(i)));
        } catch (Exception e) {
            LOGGER.error("#### 库存质量解除隔离失败 #### " + e.getMessage(), e);

            // 状态回滚
            allList.forEach(s -> s.setHoldStatus(BIG_1));
            inventoryholdRecordRepository.updateBatchInventoryholdRecord(allList);

            // 打印堆栈跟踪，通常不建议在生产环境中这样做
            e.printStackTrace();
        }
    }
    /* Ended by AICoder, pid:8b7448f655c44621b2d26ef1cac25064 */

    /**
     * 解冻试样/质量类型发送审批中心
     */
    /* Started by AICoder, pid:5bee2167d3c946a79939c5ee327b9b7a */
    private void sendApprovedByQuality(List<InventoryholdRecordDTO> qualityHoldList) {
        if (Tools.isEmpty(qualityHoldList)) {
            return;
        }
        //根据条码不同，发送不同的审批邮件
        Map<String, List<InventoryholdRecordDTO>> barcodeGroupList = qualityHoldList.stream().collect(Collectors.groupingBy(e -> e.getItemBarcode()));
        barcodeGroupList.forEach((k, v) -> {
            String strExpireQuaBusinessId = UUID.randomUUID().toString().replace("-", "");
            //仓库去重
            String strWhName = v.stream()
                    .map(InventoryholdRecordDTO::getWhName).distinct().collect(Collectors.joining(SPLIT));
            //获取隔离原因
            String strHoldDesc = v.stream()
                    .map(InventoryholdRecordDTO::getHoldReasonDsc).distinct().collect(Collectors.joining(SPLIT));
            HoldFlowStartDTO flowStartDTO = HoldFlowStartDTO.builder().build()
                    .setApplyBy(v.get(INT_0).getEditwho())
                    .setItemBarcode(v.get(INT_0).getItemNo() + HOLD_SPLIT + v.get(INT_0).getItemBarcode())
                    .setApproverId1(v.get(INT_0).getApprovedByNo())
                    .setApproverId2(v.get(INT_0).getApprovedByNo2())
                    .setEmailCc(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setUuid(strExpireQuaBusinessId)
                    .setEmpNo(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setRemark(Tools.isEmpty(v.get(INT_0).getUnfreezeRemark()) ? INVENTORY_REMOVE_HOLD : v.get(INT_0).getUnfreezeRemark())
                    .setWhseid(strWhName)
                    .setHoldReasonDsc(strHoldDesc)
                    .setFlowCode(IWMS_INVENTORY_REMOVE_HOLD_QUALITY);
            this.insertRecordLog(v, strExpireQuaBusinessId, flowStartDTO);
            //调用审批中心接口
            this.removeHoldFlowStart(flowStartDTO);
        });
    }
    /* Ended by AICoder, pid:5bee2167d3c946a79939c5ee327b9b7a */

    /**
     * 插入调用审批中心日志
     */
    private void insertRecordLog(List<InventoryholdRecordDTO> v, String strExpireQuaBusinessId, HoldFlowStartDTO flowStartDTO) {
        //调用接口日志
        ApprovalProcessLogDTO dtoLog = new ApprovalProcessLogDTO();
        dtoLog.setBillNO(strExpireQuaBusinessId)
                .setInterfaceName(MOBILEAPPROVALSTART)
                .setResponse(JSON.toJSONString(flowStartDTO).length() > INT_4000 ? JSON.toJSONString(flowStartDTO).substring(INT_0, INT_4000) : JSON.toJSONString(flowStartDTO));
        approvalProcessInfoRepository.insertMobileApprovalLog(dtoLog);

        //保存库存冻结日志记录
        List<InventoryholdRecord> inventoryholdQuaList = new ArrayList<>();
        for (InventoryholdRecordDTO dto : v) {
            InventoryholdRecord inventoryholdRecord = new InventoryholdRecord();
            BeanUtils.copyProperties(dto, inventoryholdRecord);
            inventoryholdRecord.setHoldStatus(BIG_3); //审批中
            inventoryholdRecord.setInforStatus(BIG_0); //默认成功
            inventoryholdRecord.setInforFailTimes(BIG_0);
            inventoryholdRecord.setBusinessid(strExpireQuaBusinessId);
            inventoryholdRecord.setApproveType(INVENTORY_REMOVE_HOLD); //默认0次
            inventoryholdQuaList.add(inventoryholdRecord);
        }
        inventoryholdRecordRepository.insertApproveHoldRecordLog(inventoryholdQuaList);
    }

    /**
     * 解冻计划类型发送审批中心
     */
    /* Started by AICoder, pid:b805461d46174d3491d9138be21566ed */
    private void sendApprovedByPlan(List<InventoryholdRecordDTO> planHoldList) {
        if (Tools.isEmpty(planHoldList)) {
            return;
        }
        //根据条码不同，发送不同的审批邮件
        Map<String, List<InventoryholdRecordDTO>> barcodeGroupList = planHoldList.stream().collect(Collectors.groupingBy(e -> e.getItemBarcode()));
        barcodeGroupList.forEach((k, v) -> {
            String strExpireBusinessId = UUID.randomUUID().toString().replace("-", "");
            //仓库去重
            String strWhName = v.stream()
                    .map(InventoryholdRecordDTO::getWhName).distinct().collect(Collectors.joining(SPLIT));
            HoldFlowStartDTO flowStartDTO = HoldFlowStartDTO.builder().build()
                    .setApplyBy(v.get(INT_0).getEditwho())
                    .setItemBarcode(v.get(INT_0).getItemNo() + HOLD_SPLIT + v.get(INT_0).getItemBarcode())
                    .setApproverId1(v.get(INT_0).getApplyByNo())
                    .setEmailCc(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setUuid(strExpireBusinessId)
                    .setEmpNo(v.get(INT_0).getEditwho().substring(v.get(INT_0).getEditwho().length() - INT_8))
                    .setRemark(Tools.isEmpty(v.get(INT_0).getUnfreezeRemark()) ? INVENTORY_REMOVE_HOLD : v.get(INT_0).getUnfreezeRemark())
                    .setWhseid(strWhName)
                    .setHoldReasonDsc(v.get(INT_0).getHoldReasonDsc())
                    .setFlowCode(IWMS_INVENTORY_REMOVE_HOLD_PLAN);
            this.insertRecordLog(v, strExpireBusinessId, flowStartDTO);
            //调用审批中心接口
            this.removeHoldFlowStart(flowStartDTO);
        });

    }
    /* Ended by AICoder, pid:b805461d46174d3491d9138be21566ed */
    /**
     * 冻结解冻INFOR库存
     */
    public void holdInventory(InventoryholdRecordDTO dto) {

        InventoryholdRecordDTO inventoryholdRecordDTO = InventoryholdRecordDTO.builder().build()
                .setSerialkey(dto.getSerialkey()).setWhseid(dto.getWhseid()).setItemBarcode(dto.getItemBarcode())
                .setHoldReason(dto.getHoldReason()).setHoldStatus(dto.getHoldStatus()).setEditwho(dto.getEditwho())
                .setEditdate(dto.getEditdate())
                .setItemNo(dto.getItemNo());

        // 获取条码在当前仓库需要冻结或解冻的所有批号
        List<String> lotList = inventoryholdRecordRepository.getLotByItemBarcode(dto);
        if (Tools.isEmpty(lotList)) {
            inventoryholdRecordDTO.setInforStatus(BIG_0); // 成功
            inventoryholdRecordDTO.setInforFailTimes(BIG_0); // 失败次数清零
            // 更新库存冻结记录
            inventoryholdRecordRepository.updateInventoryholdRecord(inventoryholdRecordDTO);
            return;
        }

        ServiceData<Map<String, String>> result = new ServiceData<>();
        boolean resultApi = false;
        StringBuilder inforParamsResult = new StringBuilder();
        for (String lot : lotList) {
            InforLockDTO inforLockDTO = InforLockDTO.builder().build()
                    .setWhseId(dto.getWhseid())
                    .setLot(lot)
                    .setHldcmt(dto.getRemark()) // 备注
                    .setApplicant(dto.getApplyBy()) // 冻结申请人
                    .setApplicantDepart(dto.getApplyDeptName()) // 冻结申请部门名称
                    .setResponsible(dto.getResponsibleBy()) // 冻结责任人
                    .setResponsibleDepart(dto.getResponsibleDeptName()) // 冻结责任部门名称
                    .setHold(dto.getHoldStatus().toString()) // 冻结1/解冻0
                    .setStatus(dto.getHoldReason()) // 冻结原因
                    .setExecutive(dto.getEditwho())//最后更新人
                    .setExecutiveDepart(dto.getEditDeptName());//最后更新部门
            try {
                // 调用INFOR解冻或解冻API
                result = RemoteServiceDataUtil.invokeService(ZTE_SCM_INFOR_MOVE, MicroServiceNameEum.VERSION,
                        MicroServiceNameEum.SENDTYPEPOST, UNLOCK_URL, JSONObject.toJSONString(inforLockDTO), Tools.newHashMap());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Tools.isNotEmpty(result.getBo()) && Tools.isEmpty(result.getBo().get(lot))) {
                resultApi = true;
            } else {
                inforParamsResult.append(StringUtils.substring(result.getBo() == null ? "" : result.getBo().get(lot), INT_0, NumConstant.INT_2000));
                resultApi = false;
                break;
            }
        }

        if (resultApi) {
            inventoryholdRecordDTO.setInforStatus(BIG_0); // 成功
            inventoryholdRecordDTO.setInforParamsResult(StringUtils.substring(inforParamsResult.toString(), INT_0, NumConstant.INT_2000));// 失败日志
            inventoryholdRecordDTO.setInforFailTimes(BIG_0); // 失败次数清零
        } else {
            inventoryholdRecordDTO.setInforStatus(BIG_1); // 失败
            inventoryholdRecordDTO.setInforParamsResult(StringUtils.substring(inforParamsResult.toString(), INT_0, NumConstant.INT_2000));// 失败日志
            inventoryholdRecordDTO.setInforFailTimes(dto.getInforFailTimes().add(BIG_1)); // +1次失败次数
        }
        // 更新库存冻结记录
        inventoryholdRecordRepository.updateInventoryholdRecord(inventoryholdRecordDTO);
    }

    /**
     * 导出
     */
    @Override
    public void exportInventoryholdRecord(InventoryholdRecordDTO dto) {

        int total = inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            InventoryholdRecordDTO param = ((InventoryholdRecordDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(inventoryholdRecordRepository.getInventoryholdRecordList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(INVENTORY_HOLD_NAME).setFileName(INVENTORY_HOLD_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(InventoryholdRecordDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /**
     * 导出库存冻结异常记录
     */
    @Override
    public void exportInventoryholdException(InventoryholdRecordDTO dto) {
        int total = inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            InventoryholdRecordDTO param = ((InventoryholdRecordDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(inventoryholdRecordRepository.getInventoryholdRecordExeList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(INVENTORY_HOLD_EX_NAME).setFileName(INVENTORY_HOLD_EX_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(InventoryholdRecordVO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    @Override
    public void batchUpdateInventoryholdRecord(InventoryholdRecordDTO dto) {
        inventoryholdRecordRepository.batchUpdateInventoryholdRecord(dto);
    }

    /**
     * 定时JOB
     */
    @Override
    public void inventoryholdRecordJob(InventoryholdRecordDTO dto) {

        List<String> wms = barcodeCenterRepository.getInventoryHoldWmwhids();
        BusiAssertException.isEmpty(wms, MessageId.WHSEID_CAN_NOT_BE_EMPTY);
        for (String whseid : wms) {
            dto.setWhseid(whseid);
            List<InventoryholdRecordDTO> inventoryholdRecordDTOList = inventoryholdRecordRepository.getInventoryholdJobList(dto);
            if (Tools.isEmpty(inventoryholdRecordDTOList)) {
                continue;
            }
            // 冻结infor库存
            inventoryholdRecordDTOList.forEach(i -> ThreadUtil.INVENTORY_HOLD_RECORD_EXECUTOR.execute(() -> holdInventory(i)));
        }
    }
    /**
     * 库存隔离开始审批流程
     *
     * @param holdFlowStartDTO
     * @return 流程实例ID
     */
    public String holdFlowStart(HoldFlowStartDTO holdFlowStartDTO) {
        FlowStartDTO flowStartDTO = new FlowStartDTO();
        flowStartDTO.setHandler(holdFlowStartDTO.getEmpNo());
        flowStartDTO.setFlowCode(IWMS_INVENTORY_HOLD);
        flowStartDTO.setBusinessId(holdFlowStartDTO.getUuid());
        Map<String, Object> params = bean2Map(holdFlowStartDTO);
        params.put(APPROVE_TITLE, String.format(HOLD_TITLE, holdFlowStartDTO.getItemBarcode()));
        flowStartDTO.setParams(params);
        return ApprovalFlowClient.start(flowStartDTO);
    }
    /**
     * 库存解除隔离开始审批流程
     *
     * @param holdFlowStartDTO
     * @return 流程实例ID
     */
    public String removeHoldFlowStart(HoldFlowStartDTO holdFlowStartDTO) {
        FlowStartDTO flowStartDTO = new FlowStartDTO();
        flowStartDTO.setHandler(holdFlowStartDTO.getEmpNo());
        flowStartDTO.setFlowCode(holdFlowStartDTO.getFlowCode());
        flowStartDTO.setBusinessId(holdFlowStartDTO.getUuid());
        Map<String, Object> params = bean2Map(holdFlowStartDTO);
        params.put(APPROVE_TITLE, String.format(REMOVE_HOLD_TITLE, holdFlowStartDTO.getItemBarcode()));
        flowStartDTO.setParams(params);
        return ApprovalFlowClient.start(flowStartDTO);
    }

    public static Map<String, Object> bean2Map(Object bean) {
        Map<String, Object> map = new HashMap<>();
        BeanMap beanMap = BeanMap.create(bean);
        for (Object object : beanMap.entrySet()) {
            if (object instanceof Map.Entry) {
                Map.Entry<String, Object> entry = (Map.Entry<String, Object>) object;
                String key = entry.getKey();
                Object val = beanMap.get(key);
                if (null != val) {
                    map.put(key, beanMap.get(key));
                }
            }
        }
        return map;
    }

}
