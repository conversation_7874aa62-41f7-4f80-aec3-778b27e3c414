package com.zte.application.infor.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.zte.action.iscpedi.WriteBackIscpHandler;
import com.zte.application.kafka.IKafkaMessageProducer;
import com.zte.application.kafka.ReSendKafkaToIscpListener;
import com.zte.application.kafka.WriteBackIscpResultListener;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.*;
import com.zte.domain.model.material.OnlineFallBackApplyBillRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.zte.action.iscpedi.GlobalVariable;
import com.zte.action.iscpedi.model.EdiIscpData;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.InforIwmsIscpService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.springbootframe.annotation.LogAnnotation;

import static com.zte.action.iscpedi.GlobalVariable.*;
import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;

@Service
public class InforIwmsIscpServiceImpl implements InforIwmsIscpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InforIwmsIscpServiceImpl.class);
    private static final int RE_SENDING = 31;
    private static final int RESENDING_FAIL = 32;

    @Autowired
    private InforIwmsIscpRepository inforIwmsIscpRepository;
    @Autowired
    private OnlineFallBackApplyBillRepository applyBillRepository;
    @Autowired
    private StepIscpRepository stepIscpRepository;
    @Autowired
    private EdiSoSRepository ediSoSRepository;
    @Autowired
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;

    @Autowired
    private IKafkaMessageProducer kafkaMessageProducer;
    @Autowired
    private EmailUtil emailUtil;

   private  WriteBackIscpHandler writeBackIscpHandler = null;
    @Autowired
    private ReSendKafkaToIscpListener reSendKafkaToIscpListener;

    /**
     * <AUTHOR> 批量更新价格信息
     * @throws CloneNotSupportedException
     */
    @LogAnnotation(module = "infor模块", action = "刷新退库单价格")
    @Override
    public ServiceData<?> updateFallBackSoPrice(List<OnlineFallBackPriceHeadDTO> oList) {
        List<OnlineFallBackPriceResultDTO> listResult = new ArrayList<>();
        for (OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO : oList) {
            OnlineFallBackPriceResultDTO resultDto = new OnlineFallBackPriceResultDTO();
            resultDto.setBillNo(onlineFallBackPriceHeadDTO.getFallBackNo());
            try {
                this.updatePriceDone(resultDto, onlineFallBackPriceHeadDTO);
                listResult.add(resultDto);
            } catch (Exception e) {
                LOGGER.info(e.toString());
                resultDto.setResultFlag("Failed");
                resultDto.setMessage(e.getMessage());
            }
        }
        return ServiceDataUtil.getSuccess(listResult);
    }

    private void updatePriceDone(OnlineFallBackPriceResultDTO resultDto,
                                 OnlineFallBackPriceHeadDTO onlineFallBackPriceHeadDTO) throws CloneNotSupportedException {
        String failed = "Failed";
        String successed = "Successed";
        // 校验采购传入数据的完整性
        String checkError = checkOnlineDto(onlineFallBackPriceHeadDTO);
        if (Tools.isNotEmpty(checkError)) {
            resultDto.setResultFlag(failed);
            resultDto.setMessage(checkError);
            return;
        }
        // 查询infor出库数据
        EdiSoS ediSoS = new EdiSoS();
        ediSoS.setExternalorderkey2(onlineFallBackPriceHeadDTO.getFallBackNo());
        List<EdiSoS> ediSoSs = ediSoSRepository.selectEdiSoSAll(ediSoS);
        if (Tools.isEmpty(ediSoSs)) {
            resultDto.setResultFlag(failed);
            resultDto.setMessage(onlineFallBackPriceHeadDTO.getFallBackNo() + CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_NO_DATA));
            return;
        }
        // 按照220+外部行号组装infor数据
        List<EdiSoSParamDTO> ediSoSParamDTOs = buildSosLineNoData(ediSoSs);
        // 校验采购数据与本地数据是否匹配
        checkError = checkFallBackData(ediSoSParamDTOs, onlineFallBackPriceHeadDTO);
        if (StringUtils.isNotEmpty(checkError)) {
            resultDto.setResultFlag(failed);
            resultDto.setMessage(checkError);
            return;
        }
        // 批量插入 批量更新 1000条
        insertEdiSo(ediSoSs, ediSoSParamDTOs, onlineFallBackPriceHeadDTO.getDetails());
        resultDto.setResultFlag(successed);
    }
    
    public double add (double v1,double v2){
    	BigDecimal b1 = new BigDecimal(String.valueOf(v1));
    	BigDecimal b2 = new BigDecimal(String.valueOf(v2));
    	
		return b1.add(b2).doubleValue();
    }

    public List<EdiSoSParamDTO> buildSosLineNoData(List<EdiSoS> ediSoSs) {
        List<EdiSoSParamDTO> ediSoSParamDTOs = new ArrayList<>();
        for (EdiSoS eSoS : ediSoSs) {
            boolean exlineNoFlag = false;
            for (EdiSoSParamDTO ediSoSParamDTO : ediSoSParamDTOs) {
                if (ediSoSParamDTO.getLottable02().equals(eSoS.getLottable02())
                        && ediSoSParamDTO.getExLineNo().equals(eSoS.getExternlineno())) {
                    double qty = add(ediSoSParamDTO.getQty(),eSoS.getShippedqty().doubleValue());
                    ediSoSParamDTO.setQty(qty);
                    exlineNoFlag = true;
                }
            }
            if (!exlineNoFlag) {
                EdiSoSParamDTO e = new EdiSoSParamDTO();
                e.setLottable02(eSoS.getLottable02().toString());
                e.setExLineNo(eSoS.getExternlineno().toString());
                e.setQty(eSoS.getShippedqty().doubleValue());
                ediSoSParamDTOs.add(e);
            }
        }
        return ediSoSParamDTOs;
    }

    /**
     * 校验DTO
     *
     * <AUTHOR>
     */
    private String checkOnlineDto(OnlineFallBackPriceHeadDTO dto) {
        StringBuilder error = new StringBuilder();
        // 必填校验
        if (StringUtils.isEmpty(dto.getFallBackNo())) {
            error.append(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_FALLBACK_NO_EMPTY)).append(";");
        }
        if (null == dto.getDetails() || dto.getDetails().isEmpty()) {
            error.append(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_FALLBACK_DETAIL)).append(";");
            return error.toString();
        }
        int i = 1;
        // 大于0校验
        for (OnlineFallBackPriceDetailDTO detailDTO : dto.getDetails()) {
            if (StringUtils.isEmpty(detailDTO.getExlineNo())) {
                error.append(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL2) + ";", i));
            }
            if (StringUtils.isEmpty(detailDTO.getItemBarcode())) {
                error.append(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL3) + ";", i));
            }
            List<OnlineFallBackPriceDTO> onlineFallBackPriceDTOs = detailDTO.getItems();
            if (null == onlineFallBackPriceDTOs || onlineFallBackPriceDTOs.isEmpty()) {
                error.append(i).append(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_FALLBACK_DETAIL)).append(";");
            } else {
                error.append(checkItems(onlineFallBackPriceDTOs));
            }
            i++;
        }
        return error.toString();
    }

    /**
     * <AUTHOR>
     * @param onlineFallBackPriceDTOs
     * @return String
     */
    private String checkItems(List<OnlineFallBackPriceDTO> onlineFallBackPriceDTOs) {
        StringBuilder error = new StringBuilder();
        int i = 1;
        for (OnlineFallBackPriceDTO detailDTO : onlineFallBackPriceDTOs) {
            if (null == detailDTO.getQty() || detailDTO.getQty().doubleValue() <= 0) {
                error.append(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL4) + ";", i));
            }
            if (null == detailDTO.getPriceNoTax()) {
                error.append(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL4) + ";", i));
            } else if (detailDTO.getPriceNoTax().doubleValue() <= 0) {
                error.append(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL7) + ";", i));
            }
            i++;
        }
        return error.toString();
    }

    /**
     * 远程数据校验
     *
     * <AUTHOR>
     */
    private String checkEdiSoData(List<EdiSoS> ediSoSs) {
        String error = "";
        // 是否有价格校验
        for (EdiSoS ediSoS : ediSoSs) {
            if (ediSoS.getRef14() != null && StringUtils.isNotEmpty(ediSoS.getRef14().toString())) {
                return CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_IS_EXIST);
            }
        }
        return error;
    }

    /**
     * <AUTHOR> 校验录入数据和生产环境数量是否对的上
     */
    private String checkFallBackData(List<EdiSoSParamDTO> ediSoSParamDTOs, OnlineFallBackPriceHeadDTO dto) {
        StringBuilder error = new StringBuilder();
        // 校验行号的完整性
        int dbCount = ediSoSParamDTOs.size();
        int onlineCount = dto.getDetails().stream().collect(Collectors.groupingBy(OnlineFallBackPriceDetailDTO::getExlineNo)).size();
        if (dbCount != onlineCount) {
            return error.append(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_NO_SAMPLE_WITH_INFOR)).toString();
        }
        // 校验每个外部行号是否匹配
        for (EdiSoSParamDTO ediSoS : ediSoSParamDTOs) {
            String exLineNo = ediSoS.getExLineNo();
            List<OnlineFallBackPriceDetailDTO> detailDTOs = dto.getDetails().stream().filter(p -> p.getExlineNo().equals(exLineNo))
                    .collect(Collectors.toList());
            if (INT_0 == detailDTOs.size() || Tools.isEmpty(detailDTOs.get(INT_0).getItems())) {
                error.append(exLineNo).append(CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_QUERY_NO_DATA));
                break;
            }
            // 校验行号下的数量是否匹配
            error.append(checkFallBackDataSize(dto.getFallBackNo(), exLineNo, detailDTOs, ediSoS));
        }
        return error.toString();
    }

    /**
     * 检查数量
     */
    public String checkFallBackDataSize(String fallBackNo, String exLineNo, List<OnlineFallBackPriceDetailDTO> detailDTOs, EdiSoSParamDTO ediSoS) {
        String error = "";
        double shipTotalQty = 0;
        for (OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO : detailDTOs) {
            for (OnlineFallBackPriceDTO item : onlineFallBackPriceDetailDTO.getItems()) {
                shipTotalQty = add(shipTotalQty, item.getQty().doubleValue());
            }
        }
        if (INT_0 == shipTotalQty) {
            error += exLineNo + CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_QUERY_NO_DATA);
            return error;
        }
        if (ediSoS.getQty() != shipTotalQty) {
            // infor的出库数量向下取整与isrm的数量做比较
            double floorQty = Math.floor(ediSoS.getQty());
            if (floorQty != shipTotalQty) {
                error += exLineNo + CommonUtils.getLmbMessage(MessageId.INFOR_FALLBACK_PRICE_DATA_CAN_NOT_SAMPLE);
                return error;
            }
            // 针对infor有小数点，isrm无小数点，数量不相等的情况，保留日志
            OnlineFallBackPriceLogDTO onlineFallBackPriceLogDTO = OnlineFallBackPriceLogDTO.builder().build()
                    .setExternalorderkey2(fallBackNo).setExternlineno(exLineNo).setLottable02(ediSoS.getLottable02())
                    .setShippedqty(BigDecimal.valueOf(ediSoS.getQty())).setIsrmqty(BigDecimal.valueOf(shipTotalQty));
            ediSoSRepository.insertOnlineFallBackPriceLog(onlineFallBackPriceLogDTO);
        }
        return error;
    }

    /**
     * <AUTHOR> 将数据整合成批量插入和更新，只更新价格
     */
    private void insertEdiSo(List<EdiSoS> ediSoSs, List<EdiSoSParamDTO> ediSoSParamDTOs, List<OnlineFallBackPriceDetailDTO> detailDTOs) throws CloneNotSupportedException {
        List<EdiSoS> list = buildEdiSosData(ediSoSs, ediSoSParamDTOs, detailDTOs);
        List<EdiSoS> addList = new ArrayList<>();
        List<EdiSoS> updateList = new ArrayList<>();
        List<EdiSoS> deleteList = new ArrayList<>();
        for (EdiSoS ediSoS : list) {
            if (INT_0 == ediSoS.getSerialkey().intValue()) {
                addList.add(ediSoS);
            } else if (STR_B1.equals(ediSoS.getOrderkey())){
                deleteList.add(ediSoS);
            } else {
                updateList.add(ediSoS);
            }
        }
        List<List<EdiSoS>> pageAddEdiSo = buildPageEdiSo(addList);
        List<List<EdiSoS>> pageUpdateEdiSo = buildPageEdiSo(updateList);
        List<List<EdiSoS>> pageDeleteEdiSo = buildPageEdiSo(deleteList);
        insertOrUpdateEdiSo(pageAddEdiSo, pageUpdateEdiSo, pageDeleteEdiSo);
    }

    private List<List<EdiSoS>> buildPageEdiSo(List<EdiSoS> list) {
        int pageSize = 1000;
        List<List<EdiSoS>> pageEdiSo = new ArrayList<>();
        if (Tools.isNotEmpty(list)) {
            int total = list.size() % pageSize > 0 ? (list.size() / pageSize + 1) : list.size() / pageSize;
            for (int i = 0; i < total; i++) {
                pageEdiSo.add(list.stream().skip(i * pageSize).limit(pageSize).collect(Collectors.toList()));
            }
        }
        return pageEdiSo;
    }

    /**
     * 插入或者update
     */
    private void insertOrUpdateEdiSo(List<List<EdiSoS>> pageAddEdiSo, List<List<EdiSoS>> pageUpdateEdiSo, List<List<EdiSoS>> pageDeleteEdiSo) {
        for (List<EdiSoS> e : pageAddEdiSo) {
            // 批量插入
            ediSoSRepository.insertEdiSosBatch(e);
        }
        for (List<EdiSoS> e : pageDeleteEdiSo) {
            // 批量删除
            ediSoSRepository.deleteEdiSosBatch(e);
        }
        for (List<EdiSoS> e : pageUpdateEdiSo) {
            // 批量更新
            ediSoSRepository.updateEdiSosBatch(e);
        }

    }

    /**
     * <AUTHOR> 生成edi_so_s数据
     */
    public List<EdiSoS> buildEdiSosData(List<EdiSoS> ediSoSs, List<EdiSoSParamDTO> ediSoSParamDTOs, List<OnlineFallBackPriceDetailDTO> detailDTOs) throws CloneNotSupportedException {
        // 拆分策略 按照外部行号分组，找到第一个 把qty和价格改掉 减去剩下的 insert
        List<EdiSoS> allSoData = new ArrayList<>();
        for (EdiSoSParamDTO eSoSParamDTO : ediSoSParamDTOs) {
            List<EdiSoS> ediSoSs2 = ediSoSs.stream().filter(p -> p.getExternlineno().equals(eSoSParamDTO.getExLineNo()))
                    .collect(Collectors.toList());
            List<OnlineFallBackPriceDetailDTO> onlineFallBackPriceDetailDTOs = detailDTOs.stream().filter(p -> p.getExlineNo().equals(eSoSParamDTO.getExLineNo()))
                    .collect(Collectors.toList());
            List<OnlineFallBackPriceDTO> onlineFallBackPriceDTOs = new ArrayList<>();
            for (OnlineFallBackPriceDetailDTO onlineFallBackPriceDetailDTO : onlineFallBackPriceDetailDTOs) {
                onlineFallBackPriceDTOs.addAll(onlineFallBackPriceDetailDTO.getItems());
            }
            // 保持价格从低到高排序
            onlineFallBackPriceDTOs = onlineFallBackPriceDTOs.stream().sorted(Comparator.comparing(OnlineFallBackPriceDTO::getPriceNoTax)).collect(Collectors.toList());

            // 组装数据
            buildEdiSos(allSoData, ediSoSs2, onlineFallBackPriceDTOs);
        }
        return allSoData;
    }

    public void buildEdiSos(List<EdiSoS> allSoData, List<EdiSoS> ediSoSs2, List<OnlineFallBackPriceDTO> onlineFallBackPriceDTOs) throws CloneNotSupportedException {
        int soSize = ediSoSs2.size();
        // 取infor实际出库数量
        BigDecimal shippedqty = ediSoSs2.stream().map(EdiSoS::getShippedqty).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (int i = 0; i < onlineFallBackPriceDTOs.size(); i++) {
            EdiSoS eSoS;
            if (soSize <= i) {
                eSoS = (EdiSoS) ediSoSs2.get(soSize - 1).clone();
                eSoS.setSerialkey(BigDecimal.ZERO);
            } else {
                eSoS = (EdiSoS) ediSoSs2.get(i).clone();
            }
            eSoS.setRef14(onlineFallBackPriceDTOs.get(i).getPriceNoTax().toString());
            eSoS.setId("");
            eSoS.setRef13(onlineFallBackPriceDTOs.get(i).getCurrencyType());
            // 将小数点部分，放在价格最高的数量上
            if (i == onlineFallBackPriceDTOs.size()-1) {
                eSoS.setShippedqty(shippedqty);
                eSoS.setQtyshipedtotal(shippedqty);
            } else {
                eSoS.setShippedqty(onlineFallBackPriceDTOs.get(i).getQty());
                eSoS.setQtyshipedtotal(onlineFallBackPriceDTOs.get(i).getQty());
            }
            allSoData.add(eSoS);
            // 如果infor做了拆箱，则删除多余的行
            if (soSize > onlineFallBackPriceDTOs.size() && i == onlineFallBackPriceDTOs.size()-1) {
                List<EdiSoS> ediSoSs3 = ediSoSs2.subList(i+1, soSize).stream().map(es -> {es.setOrderkey(STR_B1);return es;}).collect(Collectors.toList());
                allSoData.addAll(ediSoSs3);
            }
            // infor实际出库数量 - 采购回传数量
            shippedqty = shippedqty.subtract(onlineFallBackPriceDTOs.get(i).getQty());
        }
    }

    /**
     * [VMI出库价格更新] <br>
     *
     * <AUTHOR>
     * @param oList
     * @return <br>
     */
    @LogAnnotation(module = "infor模块", action = "VMI出库价格刷新")
    @Override
    public ServiceData<?> updateVmiSoPrice(List<VmiSoPriceDTO> oList) {
        // 必填项校验
        ServiceData<?> checkMustError = checkMust(oList);
        if (!RetCode.SUCCESS_CODE.equals(checkMustError.getCode().getCode())) {
            return checkMustError;
        }

        // 查询数据
        List<EdiSoS> ediSoSs = ediSoSRepository.selectEdiSoSAllInfor(oList);
        // (1)如果没有查询到数据
        if (null == ediSoSs || ediSoSs.isEmpty()) {
            oList.get(0).setMsg(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL13), oList.get(0).getBillNo()));
            return ServiceDataUtil.getValidAttionError(oList.get(0));
        }

        // (2)如果有查询到数据
        // a.校验出库单号和条码是否匹配
        // b.校验价格字段是否刷新
        Map<String, EdiSoS> mapBillNoAndItemBarcode = new HashMap<>(128);
        ediSoSs.forEach(ret -> {
            String key = ObjectTransform.objectToStr(ret.getExternalorderkey2()) + ObjectTransform.objectToStr(ret.getLottable02());
            if (!mapBillNoAndItemBarcode.containsKey(key)) {
                mapBillNoAndItemBarcode.put(key, ret);
            }
        });

        ServiceData<?> checkIsExistedAndUpdatedError = checkIsExistedAndUpdated(oList, mapBillNoAndItemBarcode);
        if (!RetCode.SUCCESS_CODE.equals(checkIsExistedAndUpdatedError.getCode().getCode())) {
            return checkIsExistedAndUpdatedError;
        }
        // 执行批量更新
        return updateBatch(oList);
    }

    /**
     * [批量更新] <br>
     *
     * <AUTHOR>
     * @param oList
     * @return <br>
     */
    private ServiceData<?> updateBatch(List<VmiSoPriceDTO> oList) {
        try {
            List<VmiSoPriceDTO> list = new ArrayList<>();
            HashSet<String> billNoAndItemBarcode = new HashSet<>();
            VmiSoPriceDTO vmiSoPriceDTO = null;
            for (VmiSoPriceDTO obj : oList) {
                if (billNoAndItemBarcode.add(obj.getBillNo() + obj.getItemBarcode())) {
                    vmiSoPriceDTO = new VmiSoPriceDTO();

                    vmiSoPriceDTO.setBillNo(obj.getBillNo());
                    vmiSoPriceDTO.setItemBarcode(obj.getItemBarcode());
                    vmiSoPriceDTO.setUniPriceNoTax(obj.getUniPriceNoTax());

                    list.add(vmiSoPriceDTO);

                    if (list.size() == Constant.INT_100) {
                        ediSoSRepository.updateVmiSoPrice(list);
                        list.clear();
                    }
                }
            }
            if (Tools.isNotEmpty(list)) {
                ediSoSRepository.updateVmiSoPrice(list);
            }

            return ServiceDataUtil.getSuccess(new VmiSoPriceDTO());
        } catch (Exception e) {
            return ServiceDataUtil.getBusinessError(e.getMessage());
        }
    }

    /**
     * [校验单号+条码是否存在，校验价格是否更新] <br>
     *
     * <AUTHOR>
     * @param oList
     * @param mapBillNoAndItemBarcode
     * @return <br>
     */
    public ServiceData<?> checkIsExistedAndUpdated(List<VmiSoPriceDTO> oList, Map<String, EdiSoS> mapBillNoAndItemBarcode) {
        for (VmiSoPriceDTO vmiSoPriceDTO : oList) {
            if (!mapBillNoAndItemBarcode.containsKey(vmiSoPriceDTO.getBillNo() + vmiSoPriceDTO.getItemBarcode())) {
                vmiSoPriceDTO.setMsg(MessageFormat.format(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL13),
                        vmiSoPriceDTO.getBillNo() + "," + vmiSoPriceDTO.getItemBarcode()));
                return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
            }
        }
        return ServiceDataUtil.getSuccess();
    }

    /**
     * [校验必填项] <br>
     *
     * <AUTHOR>
     * @param oList
     * @return <br>
     */
    public ServiceData<?> checkMust(List<VmiSoPriceDTO> oList) {
        if (null == oList || oList.isEmpty()) {
            VmiSoPriceDTO vmiSoPriceDTO = new VmiSoPriceDTO();
            vmiSoPriceDTO.setMsg(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL14));
            return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
        }

        for (VmiSoPriceDTO vmiSoPriceDTO : oList) {
            vmiSoPriceDTO.setCode(RetCode.VALIDATIONERROR_CODE);
            if (StringUtils.isEmpty(vmiSoPriceDTO.getBillNo())) {
                vmiSoPriceDTO.setMsg(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL1));
                return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
            }
            if (StringUtils.isEmpty(vmiSoPriceDTO.getSerailKey())) {
                vmiSoPriceDTO.setMsg(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL2));
                return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
            }
            if (StringUtils.isEmpty(vmiSoPriceDTO.getItemBarcode())) {
                vmiSoPriceDTO.setMsg(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL4));
                return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
            }
            if (null == vmiSoPriceDTO.getUniPriceNoTax() || vmiSoPriceDTO.getUniPriceNoTax().doubleValue() <= 0) {
                vmiSoPriceDTO.setMsg(CommonUtils.getLmbMessage(MessageId.INFOR_VMI_SO_PRICE_NOT_VAL7));
                return ServiceDataUtil.getValidAttionError(vmiSoPriceDTO);
            }

        }
        return ServiceDataUtil.getSuccess();
    }

    @Override
    public ServiceData<?> iscpEdiEntry(String externkey, String sourceTable) {
        inforIwmsIscpRepository.updateExceptionIscpEdiLog();
        stepIscpRepository.updateExceptionIscpEdiLog();
        double inforRate=0.9;
        double stepRate=0.1;
        int avaiableSize = GlobalVariable.queueSize - GlobalVariable.ediQueue.size();
        if (avaiableSize < 1) {
            return ServiceDataUtil.getBusinessError("avaiable queue is full!");
        }
        try {
            List<IscpEdiLog> list = inforIwmsIscpRepository.getIscpEdiLog(externkey, sourceTable, (int)(avaiableSize*inforRate));
            // 分三个list

            List<IscpEdiLog> listStep = stepIscpRepository.getIscpEdiLog(externkey, sourceTable, (int)(avaiableSize*stepRate));
            //这里添加step签收的数据
            List<IscpEdiLog> listStepR = listStep.stream().filter(e -> STEP_R_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
            //这里添加step入库的数据
            List<IscpEdiLog> listStepS = listStep.stream().filter(e -> STEP_S_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
            getWaitSendDataToQueue(list,listStepR,listStepS);
            LOGGER.info("current GetEdiDataThread Queue size: {}", GlobalVariable.ediQueue.size());
        } catch (Exception e) {
            LOGGER.error("iscpEdiEntry Exception ", e);
            return ServiceDataUtil.getBusinessError(e);
        }
        return ServiceDataUtil.getSuccess();
    }

    public void getWaitSendDataToQueue(List<IscpEdiLog> listAll, List<IscpEdiLog> listStepR,List<IscpEdiLog> listStepS) {
        List<EdiIscpData> list = new ArrayList<>();
        List<IscpEdiLog> listPr = listAll.stream().filter(e -> GlobalVariable.EDI_PO_R_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
        List<IscpEdiLog> listPs = listAll.stream().filter(e -> GlobalVariable.EDI_PO_S_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
        List<IscpEdiLog> listSs = listAll.stream().filter(e -> GlobalVariable.EDI_SO_S_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
        List<IscpEdiLog> listSstransfer = listAll.stream().filter(e -> GlobalVariable.EDI_SO_S_TRANSFER_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());

        List<List<IscpEdiLog>> listPrret = PartitionList.splitList(listPr, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listPrret)) {
            listPrret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiPorData(e));
            });
        }
        combineData(list);
        list.clear();

        List<List<IscpEdiLog>> listPsret = PartitionList.splitList(listPs, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listPsret)) {
            listPsret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiPosData(e));
            });
        }
        combineData(list);
        list.clear();

        List<List<IscpEdiLog>> listSsret = PartitionList.splitList(listSs, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listSsret)) {
            listSsret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiSosData(e));
            });
        }
        combineData(list);
        list.clear();

        List<List<IscpEdiLog>> listSsTransferret = PartitionList.splitList(listSstransfer, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listSsTransferret)) {
            listSsTransferret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiSosTransferData(e));
            });
        }
        combineData(list);
        list.clear();

        List<List<IscpEdiLog>> listSteprret = PartitionList.splitList(listStepR, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listSteprret)) {
            listSteprret.forEach(e -> {
                stepIscpRepository.updateListSendState(e);
                list.addAll(stepIscpRepository.selectSteprData(e));
            });
        }
        combineData(list);
        list.clear();

        List<List<IscpEdiLog>> listStepsret = PartitionList.splitList(listStepS, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listStepsret)) {
            listStepsret.forEach(e -> {
                stepIscpRepository.updateListSendState(e);
                list.addAll(stepIscpRepository.selectStepsData(e));
            });
        }
        combineData(list);
        list.clear();
    }
    public final void directSendDataToKafka(List<IscpEdiLog> listPs,List<IscpEdiLog> listSs) {
        List<EdiIscpData> list = new ArrayList<>();
        List<List<IscpEdiLog>> listPsret = PartitionList.splitList(listPs, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listPsret)) {
            listPsret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiPosData(e));
            });
        }
        combineDataNew(list);
        list.clear();

        List<List<IscpEdiLog>> listSsret = PartitionList.splitList(listSs, 990);
        if (com.zte.utils.CommonUtils.isNotEmpty(listSsret)) {
            listSsret.forEach(e -> {
                inforIwmsIscpRepository.updateListSendState(e);
                list.addAll(inforIwmsIscpRepository.selectEdiSosData(e));
            });
        }
        combineDataNew(list);
        list.clear();
    }
    public void combineData(List<EdiIscpData> list){
        combineData(list,false);
    }
    public void combineData(List<EdiIscpData> list,boolean isDirectFlag) {
        if (isDirectFlag) {
            list.forEach(t -> {
                try {
                    // 子线程无法依赖注入，通过构造方法传进去
                    getWriteBackIscpHandler().doBiz(Tools.newArrayList(t), RE_SENDING, RESENDING_FAIL);
                } catch (Exception e) {
                    LOGGER.error("direct to kafka InterruptedException :{}", e.getMessage());
                }
            });
            return;
        }
        Map<String, List<EdiIscpData>> map = new HashMap<>();
        list.forEach(ret -> {
            String key = ret.getOperateType() + ret.getDeliBillNo() + ret.getRowNo();
            if (map.containsKey(key)) {
                map.get(key).add(ret);
            } else {
                List<EdiIscpData> tmp = new ArrayList<>();
                tmp.add(ret);
                map.put(key, tmp);
            }
        });
        map.forEach((key, value) -> {
            try {
                GlobalVariable.ediQueue.put(value);
            } catch (InterruptedException e) {
                LOGGER.error("getWaitSendDataToQueue InterruptedException ", e);
            }
        });
    }


    /**
     * 简单的单例模式，没必要double check，基本不会出现并发情况
     */
    public  WriteBackIscpHandler getWriteBackIscpHandler(){
        if(Tools.isNotEmpty(writeBackIscpHandler)){
            return writeBackIscpHandler;
        }
        writeBackIscpHandler = new WriteBackIscpHandler(kafkaMessageProducer, reSendKafkaToIscpListener, inforIwmsIscpRepository,stepIscpRepository);
        return writeBackIscpHandler;
    }
    public void combineDataNew(List<EdiIscpData> list) {
        combineData(list,true);
    }


    @Override
    public List<ReelidInventoryOutDTO> getReelidInventory(ReelidInventoryInputDTO reelidInventoryInputDTO) {
        //起止日期格式校验
        String beginDate = reelidInventoryInputDTO.getBeginTime();
        String endDate = reelidInventoryInputDTO.getEndTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if(Tools.isNotEmpty(beginDate)) {
                dateFormat.parse(beginDate);
            }
            if(Tools.isNotEmpty(endDate)) {
                dateFormat.parse(endDate);
            }
        } catch (ParseException e) {
            BusiAssertException.result(DATE_FORMAT);
        }
        //物料代码、物料条码、仓库、批号、箱号、库位、REELID及日期至少输入一个查询条件校验
        if(checkObjAllFieldsIsNull(reelidInventoryInputDTO)){
            BusiAssertException.result(AT_LEAST_ONE_PARAMS);
        }
        //获取数据
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO = inventoryDiffQueryRepository.getInforWarehouseList();
        BusiAssertException.isEmpty(zteWarehouseInfoDTO, QUERY_WAREHOUSE_ERROR);
        reelidInventoryInputDTO.setWhseIdList(zteWarehouseInfoDTO.stream().map(ZteWarehouseInfoDTO::getWarehouseId).collect(Collectors.toList()));
        return inforIwmsIscpRepository.selectReelIDInventoryList(reelidInventoryInputDTO);
    }

    private boolean checkObjAllFieldsIsNull(ReelidInventoryInputDTO reelidInventoryInputDTO) {
        ReelidInventoryInputDTO rid = reelidInventoryInputDTO;
        if(Tools.isNotEmpty(rid.getSku())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getLottable02())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getWhseId())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getLot()) && rid.getLot().size() > 0){
            return false;
        }
        if(Tools.isNotEmpty(rid.getId())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getLoc())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getSerialNumber())){
            return false;
        }
        if(Tools.isNotEmpty(rid.getBeginTime())&&Tools.isNotEmpty(rid.getEndTime())){
            return false;
        }
        return true;
    }

	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param boxLabelDTO
	 * @return <br>
	 */ 
	@Override
	public ServiceData<?> getBoxLabelInfor(BoxLabelDTO boxLabelDTO) {
		if(Tools.isEmpty(boxLabelDTO.getDropId())){
			return ServiceDataUtil.getValidAttionError(CommonUtils.getLmbMessage(MessageId.DROPID_AND_PRODUCENO_NOT_EMPTY));
		}
		return ServiceDataUtil.getSuccess(inforIwmsIscpRepository.getBoxLabelInfor(boxLabelDTO));
	}

    @Override
    public void iscpReturnInfor(ISCPReturnDTO dto) {
        BusiAssertException.isEmpty(dto.getAllSerialKeyList(),NO_DATA_FOUND);
        BusiAssertException.isEmpty(dto.getErrorSerialKeyList(),NO_DATA_FOUND);
        List<Long> allSerialKeys = dto.getAllSerialKeyList().stream().map(Long::parseLong).collect(Collectors.toList());
        List<Long> errorSerialKeys = dto.getErrorSerialKeyList().stream().map(Long::parseLong).collect(Collectors.toList());
        List<IscpEdiLog>  allList = inforIwmsIscpRepository.getIscpEdiLogBySerialKey(allSerialKeys,"",dto.getBillType());
        List<IscpEdiLog>  errorList = inforIwmsIscpRepository.getIscpEdiLogBySerialKey(errorSerialKeys,"",dto.getBillType());
        BusiAssertException.isEmpty(allList,NO_DATA_FOUND);
        BusiAssertException.isEmpty(errorList,NO_DATA_FOUND);
        if(Tools.equals(EDI_PO_S,dto.getBillType())){
            //入库先获取对应行的签收信息，如果存在有issend=0的签收行，则说明kafka签收信息已推，但是iscp没接收到，需要重推
            errorList.forEach(t->{
                List<IscpEdiLog> pList = inforIwmsIscpRepository.getIscpEdiPorsLog(t);
                if(Tools.isNotEmpty(pList)){
                    inforIwmsIscpRepository.updateIscpEdiLogBySerialKey(Tools.newArrayList(pList.get(INT_0).getSerialkey()),EDI_PO_R_TYPE,INT_M_1);
                }
            });
        }
        //TODO 出库时判断入库消息，比较麻烦，待确认 Tools.equals(EDI_SO_S,dto.getBillType())
        inforIwmsIscpRepository.updateIscpEdiLogBySerialKey(allSerialKeys,dto.getBillType(),INT_3);
    }

    @Override
    public void dealIscpReturnData(String serialKey,String sourceTable) {
        List<IscpEdiLog> list ;
        if(Tools.isEmpty(serialKey)){
             list = inforIwmsIscpRepository.getIscpEdiLogBySerialKey(null,"ISSEND","");
        }else{
            BusiAssertException.isEmpty(sourceTable,NO_DATA_FOUND);
            list = inforIwmsIscpRepository.getIscpEdiLogBySerialKey(Tools.newArrayList(Long.parseLong(serialKey)),"ISSEND",sourceTable);
        }
        if (Tools.isEmpty(list)) {
            return;
        }
        List<IscpEdiLog> listPO = list.stream().filter(e -> GlobalVariable.EDI_PO_S_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
        listPO.stream().parallel().forEach(t->t.setIsSend(RE_SENDING));
        List<IscpEdiLog> listSO = list.stream().filter(e -> GlobalVariable.EDI_SO_S_TYPE.equals(e.getSourceTable())).collect(Collectors.toList());
        listSO.stream().parallel().forEach(t->t.setIsSend(RE_SENDING));
        directSendDataToKafka(listPO,listSO);
    }

    /**
     * 出入库反馈告警
     */
    @Override
    public void warnInfor() {
        //获取默认的调数
        int iscpCount = inforIwmsIscpRepository.getBillToWarn(String.valueOf(INT_1));
        String content = BLANK;
        if(iscpCount>INT_0){
         String receipts = StringUtils.join(
                  applyBillRepository.getSysLookupValues(EMAIL_RECEIPTS)
                          .parallelStream().map(StSysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList()).parallelStream().map(t->t+ZTE_EMAIL_SUFIIX).collect(Collectors.toList())
                  ,SPLIT_22);
         if(iscpCount>INT_0){
             content += ISCP_KAFKA_TEXT+iscpCount+PIECE_TEXT;
         }
            emailUtil.sendMail(receipts,ISCP_WARN,BLANK,content,BLANK);
        }
    }
}
