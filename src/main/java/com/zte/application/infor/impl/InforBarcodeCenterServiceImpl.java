/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.InforBarcodeCenterService;
import com.zte.application.infor.InforStorageCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.interfaces.infor.dto.RuleReqDTO;
import com.zte.interfaces.infor.dto.SkuDTO;
import com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.*;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年06月15日 <br>
 * @see com.zte.application.infor.impl <br>
 */

@Service
public class InforBarcodeCenterServiceImpl implements InforBarcodeCenterService {

	private static final Logger log = LoggerFactory.getLogger(InforStorageCenterService.class);
	@Value("${in.one.url}")
	private String inoneUrl;
	@Value("${in.one.wms.app.code}")
	private String inoneAppcode;

	@Autowired
	private InforBarcodeCenterRepository inforBarcodeCenterRepository;

	/**
	 * [定时推送REELID转代码注册的数据推送给条码中心] <br>
	 *
	 * <AUTHOR>
	 * @param xEmpNo
	 * @param xAuthValue
	 * @param dto
	 * @return <br>
	 */
	@Override
	public ServiceData<?> pushTransferReelidToBarcodeCenter(String xEmpNo, String xAuthValue, TransferReelidRelationshipDTO dto) {
		try {
			List<TransferReelidRelationshipDTO> transferReelidRelationshipDTOList = inforBarcodeCenterRepository.getTransferReelidRelationship(dto);
			if (Tools.isEmpty(transferReelidRelationshipDTOList)) {
				return ServiceDataUtil.getSuccess();
			}
			List<List<TransferReelidRelationshipDTO>> splitList = CommonUtils.splitList(transferReelidRelationshipDTOList, INT_250);
			for (List<TransferReelidRelationshipDTO> transferList : splitList) {
				// 获取物料名称
				Map<String, String> skuNameMap = getSkuNameMap(transferList);
				List<Map<String, String>> list = Tools.newArrayList();
				for (TransferReelidRelationshipDTO transferDTO : transferList) {
					HashMap<String, String> map = new HashMap<>(INT_16);
					map.put(BARCODE, transferDTO.getNewSerialnumber());
					map.put(PARENT_CATEGORY_CODE, REEL_MP_CODE);
					map.put(ITEM_CODE, transferDTO.getNewSku());
					map.put(ITEM_NAME, skuNameMap.get(transferDTO.getNewSku()));
					map.put(SOURCE_SYSTEM, INFORWMS);
					map.put(SOURCE_BATCH_NO, transferDTO.getNewLottable02());
					HashMap<String, String> remarkMap = new HashMap<>(INT_16);
					remarkMap.put(OLD_SKU_ZH, transferDTO.getOldSku());
					remarkMap.put(OLD_LOTTABLE02_ZH, transferDTO.getOldLottable02());
					remarkMap.put(OLD_SERIALNUMBER_ZH, transferDTO.getOldSerialnumber());
					map.put(REMARK, JSONObject.toJSONString(remarkMap));
					list.add(map);
				}
				ServiceData<?> res = invokeTransferReelid(list, xEmpNo, xAuthValue);

				updateTransferReelids(transferList, res, xEmpNo);
			}
			return ServiceDataUtil.getSuccess();
		} catch (Exception e) {
			return ServiceDataUtil.getBusinessError(e.getMessage());
		}
	}

	/**
	 * 获取物料名称
	 */
	public Map<String, String> getSkuNameMap(List<TransferReelidRelationshipDTO> transferList) {
		Map<String, String> map = new HashMap<>(INT_16);
		if (Tools.isEmpty(transferList)) {
			return map;
		}
		List<String> list = new ArrayList<>();
		List<String> newSKuList = transferList.stream().map(TransferReelidRelationshipDTO :: getNewSku).distinct().collect(Collectors.toList());
		List<String> oldSKuList = transferList.stream().map(TransferReelidRelationshipDTO ::getOldSku).distinct().collect(Collectors.toList());
		list.addAll(newSKuList);
		list.addAll(oldSKuList);
		List<SkuDTO> skuDTOList = inforBarcodeCenterRepository.getSkuNameList(list);
		skuDTOList.forEach(i -> {
			map.put(i.getSku(), i.getDescr());
		});
		return map;
	}

	/**
	 * 更新推送状态
	 */
	public void updateTransferReelids(List<TransferReelidRelationshipDTO> transferList, ServiceData<?> res, String xEmpNo) {
		String serviceCode = res.getCode().getCode();
		String requestparam = JSONObject.toJSONString(res);
		requestparam = requestparam.length() > NumConstant.INT_2000 ? requestparam.substring(INT_0, NumConstant.INT_2000) : requestparam;
		for (TransferReelidRelationshipDTO dto : transferList) {
			dto.setIssend(SUCESS_CODE.equals(serviceCode) ? BIG_0 : BIG_2);
			dto.setRequestparam(requestparam);
			dto.setEditwho(xEmpNo);
		}
		inforBarcodeCenterRepository.updateTransferReelidRelationship(transferList);
	}

	/**
	 * 调用条码中心的接口
	 */
	public ServiceData<?> invokeTransferReelid(List<Map<String, String>> list, String xEmpNo, String xAuthValue) {
		//条码中心接口
		Map<String, String> mapHeader = new HashMap<>(INT_16);
		mapHeader.put(X_TENANT_ID, STR_10001);
		mapHeader.put(X_EMP_NO,  xEmpNo);
		mapHeader.put(X_AUTH_VALUE, xAuthValue);
		return RemoteServiceDataUtil.invokeService(ZTE_ISS_BARCODECENTER_BARCODE, MicroServiceNameEum.VERSION,
				MicroServiceNameEum.SENDTYPEPOST, ZTE_ISS_BARCODECENTER_BARCODE_UPDATE, JSONObject.toJSONString(list),
				mapHeader);
	}

	@Override
	public ServiceData<?> getShortSnCode(RuleReqDTO dto) {
		// 1. 参数校验
		if (StringUtils.isEmpty(dto.getRegisterBarcode()) ||
					(StringUtils.isEmpty(dto.getContainerBarcode()) && StringUtils.isEmpty(dto.getBatchBarcode()))) {
				return ServiceDataUtil.getValidAttionError(MessageId.BARDCODE_CENTER_VALIDATION_RESPONSE_FORMAT);
			}
			Map<String, String> headerParamsMap = new HashMap<>(16);
			headerParamsMap.put(INONE_APPCODE, inoneAppcode);
			headerParamsMap.put(CONTENT_TYPE, APPLICATION_JSON);
			headerParamsMap.put(X_LANG_ID, X_LANG_ID_ZH);
			try {
				String responseStr = com.zte.itp.msa.client.util.HttpClientUtil.httpPostWithJSON
						(inoneUrl + BARCODE_CENTER_INONE_URL,
					JacksonJsonConverUtil.beanToJson(dto), headerParamsMap);

				JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
				BusiAssertException.isEmpty(json, MessageId.BARDCODE_CENTER_VALIDATION_REGISTERBARCODE_EMPTY);
				String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
				if (Tools.equals(RetCode.SUCCESS_CODE, retCode)) {
					JsonNode boNode = json.get(JSON_BO);
					return ServiceDataUtil.getSuccess(boNode);
				} else {
					String errorStr = json.get(JSON_CODE).get(JSON_MSG).toString();
					return ServiceDataUtil.getBusinessError(errorStr);
				}
			} catch (Exception e) {
				log.error("Post getShortSnCode exception:", e);
			}
			return ServiceDataUtil.getValidationError(CommonUtils.getLmbMessage(MessageId.BARDCODE_CENTER_VALIDATION_RESPONSE_FORMAT));
	}

}
