package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.DeliveryNoReceivesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.*;
import com.zte.interfaces.infor.dto.DeliverySignDTO;
import com.zte.interfaces.infor.dto.PcbOverdueDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.DeliverySignListVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryNoReceivesServiceImpl implements DeliveryNoReceivesService {
    @Autowired
    DeliveryNoReceivesRepository deliveryNoReceivesRepository;

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Value("${in.one.url}")
    private String inOneUrl;

    @Value("${in.one.wms.app.code}")
    private String appCode;

    /**
     * 录入送货单接口
     */
    @Override
    public DeliverySignDetailDTO insertSignInfo(DeliverySignDTO dto) {
        //送货单和(基地或者仓库)必输
        BusiAssertException.isEmpty(dto.getDeliveryNumber(), MessageId.NO_PARAMS);
        BusiAssertException.isTrue(Tools.isEmpty(dto.getWhseidList()) && Tools.isEmpty(dto.getProductBase()), MessageId.NO_PARAMS);
        if (Tools.isNotEmpty(dto.getProductBase())){
           //根据基地获取仓库
            List<String> whseidListByBase = deliveryNoReceivesRepository.queryWhseByBase(dto.getProductBase(),Constant.WHSEID_PRODUCTBASE);
            BusiAssertException.isEmpty(whseidListByBase, MessageId.PRODUCTBASE_NO_DATA_WHSE);
            dto.setWhseidList(whseidListByBase);
        }
        //校验送货单是否存在，送货单是否录入重复
        Integer existsCount = deliveryNoReceivesRepository.queryExistsByDelivery(dto);
        BusiAssertException.isTrue(existsCount > NumConstant.INT_0, MessageId.DELIVERY_NO_RECEIVED_REPEAT);
        List<DeliverySignDetailDTO> deliveryList = deliveryNoReceivesRepository.queryDeliveryInfo(dto);
        BusiAssertException.isEmpty(deliveryList, MessageId.DELIVERY_NO_RECEIVED_NOT_EXISTS);
        deliveryList.forEach(e -> {
            e.setCreatedBy(dto.getCreatedBy());
            e.setLastUpdatedBy(dto.getCreatedBy());
        });
        //新增头和明细数据
        DeliverySignDetailDTO headDto = deliveryList.get(NumConstant.INT_0);
        deliveryNoReceivesRepository.insertDeliverySignHead(headDto);
        CommonUtils.splitList(deliveryList, NumConstant.INT_500)
                .forEach(i -> deliveryNoReceivesRepository.insertDeliverySignDetail(i));

        DeliverySignDetailDTO signList = deliveryNoReceivesRepository.queryDeliverySignInfo(dto);

        boolean newStatus = Arrays.stream(signList.getAsnStatus().split(Constant.SPLIT)).allMatch(s -> s.equals(NumConstant.STR_0));
        boolean completeStatus = Arrays.stream(signList.getAsnStatus().split(Constant.SPLIT)).allMatch(s -> Integer.parseInt(s) >= NumConstant.INT_11);
        boolean doingStatus = Arrays.stream(signList.getAsnStatus().split(Constant.SPLIT)).anyMatch(s -> Integer.parseInt(s) >= NumConstant.INT_5);
        if (doingStatus) {
            signList.setAsnStatus(Constant.STR_RECEIVED_DOING);
        }
        if (newStatus) {
            signList.setAsnStatus(Constant.STR_TOSTART);
        }
        if (completeStatus) {
            signList.setAsnStatus(Constant.STR_RECEIVED_COMPLETED);
        }
        return signList;
    }

    /**
     * 查询送货签到信息
     */
    @Override
    public DeliverySignListVo getDeliverySignInfo(DeliverySignDTO dto) {
        if (Tools.isNotEmpty(dto.getProductBase())){
            //根据基地获取仓库
            List<String> whseidListByBase = deliveryNoReceivesRepository.queryWhseByBase(dto.getProductBase(),Constant.WHSEID_PRODUCTBASE);
            BusiAssertException.isEmpty(whseidListByBase, MessageId.PRODUCTBASE_NO_DATA_WHSE);
            dto.setWhseidList(whseidListByBase);
        }
        if(Tools.isNotEmpty(dto.getWhseId()))
        {
            List<String> whseIds = new ArrayList<>();
            whseIds.add(dto.getWhseId());
            dto.setWhseidList(whseIds);
        }
        DeliverySignListVo listVo = new DeliverySignListVo();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(deliveryNoReceivesRepository.getDeliverySignDetailTotal(dto));
        List<DeliverySignDetailDTO> list = deliveryNoReceivesRepository.getDeliverySignDetailVo(dto);
        listVo.setDeliverySignDetailDTOS(list);
        return listVo;
    }

    /**
     * 查询空库位
     */    /* Started by AICoder, pid:3cfa8b539dc9428db717502c093cd0d1 */
    @Override
    public List<DeliverySignDetailDTO> getEmptyLocByWhseId(DeliverySignDTO dto) {
        // 确保仓库ID和库位列表不为空
        BusiAssertException.isEmpty(dto.getWhseidList(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getLocList(), MessageId.NO_PARAMS);

        // 查询空库位信息
        List<DeliverySignDetailDTO> emptyLocations = deliveryNoReceivesRepository.getEmptyLocByWhseId(dto);

        return emptyLocations;
    }
    /* Ended by AICoder, pid:3cfa8b539dc9428db717502c093cd0d1 */

    /**
     * 质检查询
     * @param dto
     * @return
     */
    @Override
    public List<DeliveryDetailDTO> getQualityByWhseId(DeliverySignDTO dto) {
        // 确保仓库ID和库位列表不为空
        BusiAssertException.isEmpty(dto.getWhseId(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getToIdList(), MessageId.NO_PARAMS);

        List<DeliveryDetailDTO> deliveryDetailDTOS = new ArrayList<>();
        // 查询质检信息
        List<DeliveryDetailDTO> notReceived = deliveryNoReceivesRepository.getNotReceivedInfo(dto);
        deliveryDetailDTOS.addAll(notReceived);
        List<DeliveryDetailDTO> underInspect = deliveryNoReceivesRepository.getUnderInspectInfo(dto);
        deliveryDetailDTOS.addAll(underInspect);
        List<DeliveryDetailDTO> unqualified = deliveryNoReceivesRepository.getUnqualifiedInfo(dto);
        deliveryDetailDTOS.addAll(unqualified);
        List<DeliveryDetailDTO> qualified = deliveryNoReceivesRepository.getQualifiedInfo(dto);
        deliveryDetailDTOS.addAll(qualified);
        List<String> toidList = dto.getToIdList();
        List<String> collect = deliveryDetailDTOS.stream().map(DeliveryDetailDTO::getToId).collect(Collectors.toList());
        toidList.removeAll(collect);
        getEmptyInfo(deliveryDetailDTOS, toidList, dto.getWhseId());
        return deliveryDetailDTOS;
    }

    private void getEmptyInfo(List<DeliveryDetailDTO> deliveryDetailDTOS, List<String> toIdList, String whseId) {
        for (String toid : toIdList) {
            DeliveryDetailDTO deliveryDetailDTO = new DeliveryDetailDTO();
            deliveryDetailDTO.setWhseId(whseId);
            deliveryDetailDTO.setToId(toid);
            deliveryDetailDTO.setQtyReceived(new BigDecimal(NumConstant.INT_0));
            deliveryDetailDTO.setStateType(Constant.NOT_RECEIVED);
            deliveryDetailDTOS.add(deliveryDetailDTO);
        }
    }


    /**
     * PCB超期情况查询
     * @param dto
     * @return
     */
    @Override
    public PcbOverdueInfoDTO getPcbOverdueInfo(PcbOverdueDTO dto) throws Exception{
        // 确保物料代码和物料条码不为空
        BusiAssertException.isEmpty(dto.getItemNo(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getItemBarcode(), MessageId.NO_PARAMS);

        PcbOverdueInfoDTO pcbOverdueInfoDTO = deliveryNoReceivesRepository.getPcbOverdueInfo(dto);
        if (Tools.isEmpty(pcbOverdueInfoDTO)) {
            BusiAssertException.result(DATA_INFO_IS_EMPTY);
        }
        if (Tools.isEmpty(pcbOverdueInfoDTO.getProductDate())) {
            BusiAssertException.result(PRODUCT_DATE_ISEMPTY);
        }
        pcbOverdueInfoDTO.setItemNo(dto.getItemNo());
        pcbOverdueInfoDTO.setItemBarcode(dto.getItemBarcode());
        long dateNum = getDateNum(pcbOverdueInfoDTO.getEffectiveDate());//计算天数
        String isOsp = getInoneOspState(dto);//调用INONE物料主数据查询接口
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000087);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
        String result = "";
        if (DATA_OSP.equals(isOsp)) {
            result = getResultInfo(STR_NUMBER_ZERO, dateNum, sysLookupValuesDTOList);
        }else {
            String shelfLife = String.valueOf(pcbOverdueInfoDTO.getShelfLife());
            result = getResultInfo(shelfLife, dateNum, sysLookupValuesDTOList);
        }
        pcbOverdueInfoDTO.setResult(result);
        return pcbOverdueInfoDTO;
    }


    public String getResultInfo(String type, long dateNum, List<SysLookupValuesDTO> sysLookupValuesDTOList) {
        Map<String, String> map = sysLookupValuesDTOList.stream()
                .filter(t -> type.equals(t.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescription));
        if (Tools.isEmpty(map)) {
            return STRING_EMPTY;
        }
        List<String> mins = sysLookupValuesDTOList.stream()
                .filter(t -> type.equals(t.getAttribute1()) && STR_NUMBER_ONE.equals(t.getAttribute2()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        String min = mins.get(NumConstant.INT_0);
        List<String> maxs = sysLookupValuesDTOList.stream()
                .filter(t -> type.equals(t.getAttribute1()) && STR_NUMBER_TWO.equals(t.getAttribute2()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        String max = maxs.get(NumConstant.INT_0);
        String mapKey = getMapKey(map, dateNum, min, max);
        return map.get(mapKey);
    }

    private String getMapKey(Map<String, String> map, long dateNum, String min, String max) {
        int intValue = (int) dateNum;
        if (intValue < Integer.parseInt(min)) {
            return min;
        }
        if (intValue >= Integer.parseInt(max)) {
            return max;
        }
        Iterator<String> iterator = map.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            String[] split = key.split(COMMA);
            if (split.length > NumConstant.INT_1) {
                int start = Integer.parseInt(split[NumConstant.INT_0]);
                int end = Integer.parseInt(split[NumConstant.INT_1]);
                if (intValue >= start  && intValue < end) {
                    return key;
                }
            }
        }
        return STRING_EMPTY;
    }

    /**
     * 获取物料osp状态
     * @param dto
     * @return
     * @throws Exception
     */
    private String getInoneOspState(PcbOverdueDTO dto) throws Exception{
        Map<String, String> mapHeader = new HashMap<>(NumConstant.INT_16);
        mapHeader.put(INONE_APPCODE, appCode);
        mapHeader.put(USER_CODE, DATA_IMES300);
        mapHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        mapHeader.put(PASSWARD, DATA_WARD);
        String url = inOneUrl + ESBMULE_URL;
        Map<String, Object> reqInfo = new HashMap<>();
        reqInfo.put(ESB, Collections.singletonMap(
                ESB_DATA, Collections.singletonMap(
                        DATA_INFOS, Collections.singletonMap(
                                DATA_INFO, Collections.singletonList(
                                        Collections.singletonMap(DATA_CODE, dto.getItemNo())
                                )
                        )
                )
        ));
        String result = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(reqInfo), mapHeader);
        log.info("SCM---result:{}", result);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String retCode = json.get(ESB).get(DATA_RESULT).asText();
        if (!S.equals(retCode)) {
            BusiAssertException.result(MessageId.INONE_OSP_STATE);
        }
        JsonNode propertyByCode = findPropertyByCode(json, DATA_CODE_005304, DATA_CODE_004000);
        if(propertyByCode == null){
            return STRING_EMPTY;
        }
        return propertyByCode.get(DATA_PROPERTYVALUE).asText();
    }

    /**
     * 解析返回报文
     * @param node
     * @param targetPropertyCode
     * @param targetSpecialityCode
     * @return
     */
    /* Started by AICoder, pid:8e4e2rae7660c0614a6f0927d03d53631d17bdf6 */
    public JsonNode findPropertyByCode(JsonNode node, String targetSpecialityCode, String targetPropertyCode) {
        JsonNode dataNode = node.path(ESB).path(ESB_DATA);
        if (dataNode.isMissingNode()) {
            return null;
        }
        JsonNode dataInfosNode = dataNode.path(DATA_INFOS);
        if (dataInfosNode.isMissingNode()) {
            return null;
        }
        JsonNode dataInfoArray = dataInfosNode.path(DATA_INFO);
        if (!dataInfoArray.isArray()) {
            return null;
        }
        for (JsonNode dataInfo : dataInfoArray) {
            JsonNode codeValueNode = dataInfo.path(DATA_CODEVALUE);
            if (codeValueNode.isMissingNode()) {
                continue;
            }
            JsonNode specialityArray = codeValueNode.path(DATA_SPECIALITY);
            if (!specialityArray.isArray()) {
                continue;
            }
            Optional<JsonNode> property = findPropertyInSpecialities(specialityArray, targetSpecialityCode, targetPropertyCode);
            if (property.isPresent()) {
                return property.get();
            }
        }
        return null;
    }

    private Optional<JsonNode> findPropertyInSpecialities(JsonNode specialityArray, String targetSpecialityCode, String targetPropertyCode) {
        for (JsonNode speciality : specialityArray) {
            String specialityCode = speciality.path(DATA_SPECIALITYCODE).asText();
            if (!targetPropertyCode.equals(specialityCode)) {
                continue;
            }
            JsonNode propertyArray = speciality.path(DATA_PROPERTY);
            if (!propertyArray.isArray()) {
                continue;
            }
            Optional<JsonNode> property = findPropertyInProperties(propertyArray, targetSpecialityCode);
            if (property.isPresent()) {
                return property;
            }
        }
        return Optional.empty();
    }

    private Optional<JsonNode> findPropertyInProperties(JsonNode propertyArray, String targetSpecialityCode) {
        for (JsonNode property : propertyArray) {
            String propertyCode = property.path(DATA_PROPERTYCODE).asText();
            if (targetSpecialityCode.equals(propertyCode)) {
                return Optional.of(property);
            }
        }
        return Optional.empty();
    }
    /* Ended by AICoder, pid:8e4e2rae7660c0614a6f0927d03d53631d17bdf6 */

    /**
     * 计算天数差
     * @param date
     * @return
     */
    private long getDateNum(Date date) {
        LocalDate givenDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算天数差
        long daysBetween = ChronoUnit.DAYS.between(givenDate, currentDate);
        return daysBetween;
    }

    /**
     * 查询数据字典维护的信息
     */
    public List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }

}
