package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.infor.WarehouseReplenishmentPathService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforWmsRepublishRepository;
import com.zte.domain.model.infor.WarehouseRoadWorkRepository;
import com.zte.interfaces.infor.dto.ReplenishOptDTO;
import com.zte.interfaces.infor.dto.WarehouseReplenishmentPathDTO;
import com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO;
import com.zte.interfaces.infor.dto.RepublishRouteDTO;
import com.zte.interfaces.infor.vo.WarehouseRepPathWorkListVO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.SHEET1;

/**
 * @Description 查询补货路径优化实现类
 * <AUTHOR>
 * @Date 17:25 2025/4/21
 * @Version 1.0
 **/
@Service
@Slf4j
public class WarehouseReplenishmentPathServiceImpl implements WarehouseReplenishmentPathService {
    @Autowired
    private WarehouseRoadWorkRepository warehouseRoadWorkRepository;

    @Autowired
    private InforWmsRepublishRepository inforWmsRepublishRepository;


    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseReplenishmentPathServiceImpl.class);

    @Override
    public WarehouseRepPathWorkListVO getReplenishmentPathList(WarehouseReplenishmentPathDTO dto) {

        //1.判断路网数据是否存在：路网查不到，点击查询按钮，提示用户：当前查询条件查不到对应的路网信息
        this.getWarehouseRoadWorkList(dto);
        //2查询路网数据及补货报表数据,3.调用算法的接口
        return this.getRouteDataList(dto);
    }

    /**
     * 生成数据
     * @param dto
     * @return
     */
    private WarehouseRepPathWorkListVO getRouteDataList(WarehouseReplenishmentPathDTO dto) {
        WarehouseRepPathWorkListVO listVo = new WarehouseRepPathWorkListVO();
        //2.查询路网数据及补货报表数据
        List<RepublishRouteDTO> inforWmsRepublishList = inforWmsRepublishRepository.getInforWmsRepublishList(dto);
        //调用算法生成数据
        ReplenishOptDTO routeDTOList = getRouteDTOList(inforWmsRepublishList);
        //总数
        listVo.setTotal(routeDTOList.getReplenishRouteDTOList().size());
         //分页
        listVo.setRouteDTOList(this.pageList(routeDTOList.getReplenishRouteDTOList(), dto.getPageIndex(), dto.getPageSize()));
        listVo.setReplenishResultDTO(routeDTOList.getReplenishResultDTO());
        return listVo;
    }

    /**
     * 对List进行分页处理
     * @param list 全部数据集合
     * @param pageIndex 当前页码(从1开始)
     * @param pageSize 每页条数
     * @return 分页后的子集合
     */
    public <T> List<T> pageList(List<T> list, int pageIndex, int pageSize) {
        if (list == null || list.isEmpty()) {
            return list;
        }
        // 计算分页起始位置
        int start = (pageIndex - 1) * pageSize;
        if (start >= list.size()) {
            return Collections.emptyList(); // 当前页无数据
        }

        // 计算分页结束位置（不超过列表大小）
        int end = Math.min(start + pageSize, list.size());
        return list.subList(start, end);
    }




    @Override
    public void exportReplenishmentPath(WarehouseReplenishmentPathDTO dto) {
        int total = inforWmsRepublishRepository.getInforWmsRepublishListTotal(dto);
        IExcelExportServer server = (params, page) -> {
            WarehouseReplenishmentPathDTO param = ((WarehouseReplenishmentPathDTO) params).setPageSize(INT_5000).setPageIndex(page);
            //2.查询路网数据及补货报表数据
            WarehouseRepPathWorkListVO routeDataList = this.getRouteDataList(param);
            return new ArrayList<>(routeDataList.getRouteDTOList());
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(WAREHOUSE_REPLENISHMENT_PATH_NAME).setFileName(WAREHOUSE_REPLENISHMENT_PATH_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(RepublishRouteDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);

    }


    /**
     * 调用算法生成数据
     * @param routeDTOList
     * @return
     */
    private ReplenishOptDTO getRouteDTOList(List<RepublishRouteDTO> routeDTOList) {
        //数据为空处理
        if(CollectionUtils.isEmpty(routeDTOList)){
            return new ReplenishOptDTO();
        }
        String  str = JSONObject.toJSONString(routeDTOList);
        log.info("routeDTOList入参:{}",str);
        ServiceData<?> res = RemoteServiceDataUtil.invokeService(Constant.ZTE_SCM_INFOR_ALGORITHM, MicroServiceNameEum.VERSION,
                MicroServiceNameEum.SENDTYPEPOST, Constant.CAL_SHORTE_STROUTE_URL, str, new HashMap<>());
        //判断接口是否返回成功
        BusiAssertException.isTrue(!res.getCode().getCode().equals(RetCode.SUCCESS_CODE), USING_ALGORITHM_ERROR);
        BusiAssertException.isEmpty(res.getBo(), USING_ALGORITHM_ERROR);
        Map<String, RepublishRouteDTO> dtoMap = routeDTOList.stream().collect(Collectors.toMap(x -> x.getFromLoc() + x.getId(), Function.identity(), (a, b) -> a));
        ReplenishOptDTO listBean = JacksonJsonConverUtil.jsonToListBean(res.getBo().toString(), new TypeReference<ReplenishOptDTO>() {
        });
        listBean.getReplenishRouteDTOList().forEach(x -> {
            RepublishRouteDTO orDefault = dtoMap.getOrDefault(x.getFromLoc() + x.getId(), new RepublishRouteDTO());
            x.setRepType(orDefault.getRepType());
            x.setWarehouseDesc(orDefault.getWarehouseDesc());
            x.setWarehouseAreaDesc(orDefault.getWarehouseAreaDesc());
            x.setWhName(orDefault.getWhName());
        });
        return listBean;
    }


    /**
     * 、1.路网查不到，点击查询按钮，提示用户：当前查询条件查不到对应的路网信息
     * @param dto
     * @param listVo
     */
    private void getWarehouseRoadWorkList(WarehouseReplenishmentPathDTO dto) {
        BusiAssertException.isEmpty(dto.getWhseid(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getWarehouseArea(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getWarehouse(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getRepType(), MessageId.NO_PARAMS);
        WarehouseRoadWorkDTO warehouseRoadWorkDTO = new WarehouseRoadWorkDTO();
        BeanUtils.copyProperties(dto,warehouseRoadWorkDTO);
        int warehouseRoadWorkListVOTotal = warehouseRoadWorkRepository.getWarehouseRoadWorkListVOTotal(warehouseRoadWorkDTO);
        BusiAssertException.isTrue(warehouseRoadWorkListVOTotal == 0, CAN_NOT_FIND_ROAD);
    }
}
