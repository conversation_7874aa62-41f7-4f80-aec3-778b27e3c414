package com.zte.application.infor.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.infor.PcbSerialDispense;
import com.zte.common.model.MessageId;
import com.zte.domain.model.infor.EdiPcbserial;
import com.zte.domain.model.infor.EdiPcbserialRepository;
import com.zte.interfaces.infor.dto.PcbserialDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.springbootframe.annotation.LogAnnotation;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;

@Service
public class PcbSerialDispenseImpl implements PcbSerialDispense {
 
	@Autowired
	EdiPcbserialRepository ediPcbserialRepository; 
	 
	@LogAnnotation(module = "infor模块", action = "ISCP盘条码录入")
	@Override
	public ServiceData<?> addPcbSerial(List<PcbserialDTO> list) throws Exception {
		if(null==list||list.isEmpty()){
			return ServiceDataUtil.getBusinessError("no data");
		}
		String error=checkSerialList(list);
		if(StringUtils.isNotEmpty(error)){
			return ServiceDataUtil.getBusinessError(error);
		}
		String whseId=list.get(0).getWhseid().toUpperCase();
		String externReceiptkey=list.get(0).getExternReceiptkey();
		EdiPcbserial query=new EdiPcbserial();
		query.setWhseid(whseId);
		query.setExternreceiptkey(externReceiptkey);
		//看仓库是否激活		
		int dbFlag=ediPcbserialRepository.selectPldb(query);
		if(0==dbFlag){
			return ServiceDataUtil.getBusinessError(whseId+CommonUtils.getLmbMessage(MessageId.INFOR_DISPENSE_SERIAL_WHSEID_VAL));
		}	 
		int receiptCount=ediPcbserialRepository.selectReceiptCount(query);
		//判断是否已经生成receipt表
		if(receiptCount>0){
			return ServiceDataUtil.getBusinessError(externReceiptkey+CommonUtils.getLmbMessage(MessageId.INFOR_DISPENSE_SERIAL_RECEIPT));
		}
		//如果pcbserial表有数据 清理掉
		int pcbDataCount=ediPcbserialRepository.selectPcbSerialCount(query);
		if(pcbDataCount>0){
			//删除
			ediPcbserialRepository.deleteEdiPcbserial(query);
		}
		//批量插入数据
		List<EdiPcbserial> ediPcbserials=changePcbserialDto(list);
		
		int pageSize=100;
		List<List<EdiPcbserial>> addBatchList=new ArrayList<List<EdiPcbserial>>();
		int total=ediPcbserials.size()%pageSize>0?(ediPcbserials.size()/pageSize+1):ediPcbserials.size()/pageSize;
        for(int i=0;i<total;i++){
        	addBatchList.add(ediPcbserials.stream().skip(i*pageSize).limit(pageSize).
    				collect(Collectors.toList()));
        }
        for (List<EdiPcbserial> listSingle : addBatchList) {
        	ediPcbserialRepository.insertEdiPcbserialBatch(listSingle);
		}				
		//判断ZTEINBOUNDSERIAL是否有数据
		int countInboundExt=ediPcbserialRepository.selectZteInboundSerial(query);
		//如果有 删除
		if(countInboundExt>0){
			//删除数据
			ediPcbserialRepository.deleteZteInboundSerial(query);
		}
		//判断ZTEINBOUNDSERIAL里面的箱号是否有在EDI_PCBSERIAL存在 如果有，把箱号清空
		int countInboundToid=ediPcbserialRepository.selectZteInboundSerialToId(query);		
		if(countInboundToid>0){
			ediPcbserialRepository.updatePcbSerial(query);
		} 
		//插入数据
		ediPcbserialRepository.insertZteInboundSerial(query);
		query.setSymbol(BigDecimal.ONE); 
		ediPcbserialRepository.updateSymbol(query);
		return ServiceDataUtil.getSuccess();
	}
	/**
	 * 减产录入盘条码仓库和数量的规则，其他的不校验
	 * */
	public String checkSerialList(List<PcbserialDTO> list){
		String preWhse="WMWHSE";
		int existsFlag=-1;
		int zero=0;
		int line=1;
		StringBuilder stringBuilder=new StringBuilder();
		for (PcbserialDTO pcbserialDTO : list) {
			if(StringUtils.isEmpty(pcbserialDTO.getWhseid())){
				stringBuilder.append("Warehouse IS NOT EMPTY IN LINE "+line+";");
			}
			else if(pcbserialDTO.getWhseid().toUpperCase().indexOf(preWhse)==existsFlag){
				stringBuilder.append("Warehouse IS NOT OK IN LINE "+line+";");
			}
			if(null==pcbserialDTO.getQty()||pcbserialDTO.getQty().intValue()<=zero){
				stringBuilder.append("Qty IS NOT OK IN LINE "+line+";");
			}
			if(StringUtils.isEmpty(pcbserialDTO.getExternReceiptkey())){
				stringBuilder.append("ExternReceiptkey IS NOT EMPTY IN LINE "+line+";");
			}
			line++;
		}
		return  stringBuilder.toString();
	}
	/**
	 * dto转成model
	 * */
	public List<EdiPcbserial> changePcbserialDto(List<PcbserialDTO> list){
		List<EdiPcbserial> ediPcbserials=new ArrayList<>();
		for (PcbserialDTO e : list) {
			EdiPcbserial ediPcbserial=new EdiPcbserial();
			ediPcbserial.setWhseid(e.getWhseid());
			ediPcbserial.setStorerkey(e.getStorerKey());
			ediPcbserial.setExternreceiptkey(e.getExternReceiptkey());
			ediPcbserial.setExternlineno(e.getExternLineno());
			ediPcbserial.setSku(e.getSku());
			ediPcbserial.setLottable02(e.getLottable02());
			ediPcbserial.setToid(e.getToid());
			ediPcbserial.setSerialnumber(e.getSerialNumber());
			ediPcbserial.setQty(e.getQty());
			ediPcbserial.setHref11(e.getHref11());
			ediPcbserial.setAsntype(e.getAsnType());
			//用-1的原因是担心出问题 被oracle 接口给分发了PKG_SYNC_PCBSERIAL是对Symbol=0的分发的
			ediPcbserial.setSymbol(BigDecimal.valueOf(-1));
			ediPcbserial.setReturner("0");
			ediPcbserial.setbScaned("0");
			ediPcbserial.setbReceived(BigDecimal.ZERO);
			ediPcbserial.setbEnabled(BigDecimal.ONE);
			ediPcbserial.setGoodDieQty(e.getGoodDie());
			ediPcbserials.add(ediPcbserial);
		}
		return ediPcbserials;
	}
    

}
