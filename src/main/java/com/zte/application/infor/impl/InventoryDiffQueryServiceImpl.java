package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.InventoryDiffQueryService;
import com.zte.application.step.impl.ZteAlibabaServiceImpl;
import com.zte.application.step.impl.ZteAlibabaStockInfoUploadServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.dto.RedDotTaskDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.AlibabaOnHandQtyVO;
import com.zte.interfaces.infor.vo.CustomerInventoryListVO;
import com.zte.interfaces.infor.vo.InventoryDiffVO;
import com.zte.interfaces.step.dto.*;
import com.zte.interfaces.infor.vo.UnBindPkgSnJobVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import com.zte.utils.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.*;

import static com.zte.common.model.MessageId.*;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.SHEET1;
import static com.zte.common.utils.NumConstant.*;

/* Started by AICoder, pid:u20930f59aa4e48149b9098052f8f36e1ef76cb6 */

@Service
public class InventoryDiffQueryServiceImpl implements InventoryDiffQueryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryDiffQueryServiceImpl.class);

    @Autowired
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;
    @Autowired
    private ZteAlibabStockInfoUploadRepository zteAlibabaStockInfoUploadRepository;
    @Autowired
    private ZteAlibabaStockInfoUploadServiceImpl zteAlibabaStockInfoUploadService;
    @Autowired
    private ZteAlibabaServiceImpl zteAlibabaService;

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Value("${redDot.task.url:}")
    private String url;

    @Value(("${redDot.task.token:}"))
    private String authToken;

    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    /**
     * 获取库存差异数据
     *
     * @param dto
     * @return
     */
    @Override
    public InventoryDiffVO getInventoryDiffData(InventoryDiffDTO dto) {
        int startRow = (dto.getPageIndex() - NumConstant.INT_1) * dto.getPageSize() + NumConstant.INT_1;
        int endRow = dto.getPageIndex() * dto.getPageSize();
        dto.setStartRow(startRow);
        dto.setEndRow(endRow);
        List<InventoryDiffDTO> list = dto.getCompareMode() ? inventoryDiffQueryRepository.getInventoryDiffData(dto) : inventoryDiffQueryRepository.getInventoryDiffTempData(dto);
        int total = dto.getCompareMode() ? inventoryDiffQueryRepository.getInventoryDiffDataCount(dto) : inventoryDiffQueryRepository.getInventoryDiffDataTempCount(dto);
        int pages = (dto.getPageSize() > 0 ? (int) Math.ceil((float) total / dto.getPageSize()) : 0);
        InventoryDiffVO listVo = new InventoryDiffVO();
        listVo.setTotal(total);
        listVo.setPageIndex(dto.getPageIndex());
        listVo.setPageCount(pages);
        listVo.setInventoryDiffList(list);
        return listVo;
    }

    @Override
    public void exportInventoryDiffData(InventoryDiffDTO dto) {
        int total = dto.getCompareMode() ? inventoryDiffQueryRepository.getInventoryDiffDataCount(dto) : inventoryDiffQueryRepository.getInventoryDiffDataTempCount(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            InventoryDiffDTO param = ((InventoryDiffDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(dto.getCompareMode() ? inventoryDiffQueryRepository.getInventoryDiffData(param) : inventoryDiffQueryRepository.getInventoryDiffTempData(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(INVENTORY_DIFF_DATA_NAME).setFileName(INVENTORY_DIFF_DATA_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(InventoryDiffDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /**
     * 同步阿里库存
     *
     * @param empNo
     */
    @Override
    public void synchronizeInventoryDiffData(String empNo) {
        //清空阿里库存临时表
        inventoryDiffQueryRepository.deleteInventoryAlibabaTemp();
        //清空ZTE库存临时表
        inventoryDiffQueryRepository.deleteInventoryTemp();
        //清空库存差异临时表
        inventoryDiffQueryRepository.deleteInventoryDiffTemp();
        //同步ZTE库存临时表
        dealInventoryTempData(empNo);
        //同步阿里库存临时表
        ZteAlibabaInventoryParamDTO dto = new ZteAlibabaInventoryParamDTO();
        dto.setTaskType(NumConstant.STR_2);
        dto.setEmpNo(empNo);
        zteAlibabaService.executeQueryInventoryJob(dto);
    }



    /* Started by AICoder, pid:40e5bv2ef1g2092146540897b1e1404a9a37f260 */
    /**
     * 同步ZTE库存临时表
     */
    private void dealInventoryTempData(String empNo) {
        List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO = inventoryDiffQueryRepository.getInforWarehouseList();
        BusiAssertException.isEmpty(zteWarehouseInfoDTO, QUERY_WAREHOUSE_ERROR);
        List<CustomerInventoryPickUpDTO> allInventoryStaticsList = new ArrayList<>();
        // 获取Infor数据
        try {
            List<CustomerInventoryPickUpDTO> inforInventoryList = fetchInforInventory(zteWarehouseInfoDTO, empNo);
            allInventoryStaticsList.addAll(inforInventoryList);
        } catch (Exception e) {
            LOGGER.error("Infor库房查询异常:", e);
            BusiAssertException.result("Infor库房查询异常:" + e.getMessage());
        }
        // 获取标准仓的维修库数据
        try {
            List<CustomerInventoryPickUpDTO> repairInventoryList = zteAlibabaStockInfoUploadService.fetchRepairInventory(empNo);
            //获取阿里的良品库
            List<CustomerInventoryPickUpDTO> aliRepairInventoryList = repairInventoryList.stream().filter(p -> NumConstant.STR_0.equals(p.getInventoryType()) && NumConstant.STR_1.equals(p.getCustomerControlType())).collect(Collectors.toList());
            allInventoryStaticsList.addAll(aliRepairInventoryList);
        } catch (Exception e) {
            LOGGER.error("标准仓库存查询异常:", e);
            BusiAssertException.result("标准仓库存查询异常:" + e.getMessage());
        }
        // 获取成品库存数据
        try {
            List<CustomerInventoryPickUpDTO> productInventoryList = getProductInventory(empNo);
            allInventoryStaticsList.addAll(productInventoryList);
        } catch (Exception e) {
            LOGGER.error("获取成品库存数据异常:", e);
            BusiAssertException.result("获取成品库存数据异常:" + e.getMessage());
        }
        List<CustomerInventoryPickUpDTO> filterInventoryStaticsList = allInventoryStaticsList.stream().filter(p-> StringUtils.isNotBlank(p.getMpn())).collect(Collectors.toList());
        BusiAssertException.isEmpty(filterInventoryStaticsList, NO_DATA_FOUND);
        try {
            saveInforInventoryToTemp(filterInventoryStaticsList);
        } catch (Exception e) {
            LOGGER.error("保存查询数据异常: ", e);
            BusiAssertException.result("保存查询数据异常:: " + e.getMessage());
        }
    }

    /**
     * 获取Infor数据
     *
     * @param zteWarehouseInfoDTO
     * @param empNo
     * @return
     */
    private List<CustomerInventoryPickUpDTO> fetchInforInventory(List<ZteWarehouseInfoDTO> zteWarehouseInfoDTO, String empNo) {
        List<CustomerInventoryPickUpDTO> allInventoryStatics = new ArrayList<>();
        if (Tools.isNotEmpty(zteWarehouseInfoDTO)) {
            for (List<ZteWarehouseInfoDTO> tempList : CommonUtils.splitList(zteWarehouseInfoDTO, NumConstant.INT_500)) {
                List<CustomerInventoryPickUpDTO> inventoryStatics = inventoryDiffQueryRepository.getALiStockInfoList(tempList);
                if (Tools.isNotEmpty(inventoryStatics)) {
                    for (CustomerInventoryPickUpDTO item : inventoryStatics) {
                        item.setSourceSystem(Constant.SOURCE_SYSTEM_FROM_INFOR);
                        item.setCreatedBy(empNo);
                        item.setLastUpdatedBy(empNo);
                    }
                    allInventoryStatics.addAll(inventoryStatics);
                }
            }
        }

        return allInventoryStatics;
    }

    /**
     * 同步阿里库存临时表
     *
     * @param dto
     * @param data
     */
    public void dealAliInventoryTempData(B2BCallBackDTO dto, List<B2BCallBackInventoryResultDTO> data) {
        //插入数据
        for (List<B2BCallBackInventoryResultDTO> temp : CommonUtils.splitList(data, NumConstant.INT_500)) {
            inventoryDiffQueryRepository.insertAliInventoryTempData(temp);
        }
        Map<String, List<B2BCallBackInventoryResultDTO>> mpnMap = data.stream()
                .collect(Collectors.groupingBy(B2BCallBackInventoryResultDTO::getMpn, LinkedHashMap::new, Collectors.toList()));
        List<CustomerInventoryPickUpDTO> inventoryTempDataList = inventoryDiffQueryRepository.getInventoryTempData();
        Map<String, List<CustomerInventoryPickUpDTO>> cipMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inventoryTempDataList)) {
            cipMap = inventoryTempDataList.stream()
                    .collect(Collectors.groupingBy(CustomerInventoryPickUpDTO::getMpn, LinkedHashMap::new, Collectors.toList()));
        }
        List<InventoryDiffTempDTO> diffList = setInventoryDiffTempData(cipMap, mpnMap);
        for (List<InventoryDiffTempDTO> temp : CommonUtils.splitList(diffList, NumConstant.INT_500)) {
            inventoryDiffQueryRepository.insertInventoryDiffTempData(temp);
        }
    }

    /**
     * 计算临时表差异数据
     *
     * @param cipMap
     * @param mpnMap
     * @return
     */
    public List<InventoryDiffTempDTO> setInventoryDiffTempData(Map<String, List<CustomerInventoryPickUpDTO>> cipMap, Map<String, List<B2BCallBackInventoryResultDTO>> mpnMap) {
        //获取成品库类型
        List<String> categoryNameList =  zteAlibabaService.getCategoryName();
        List<InventoryDiffTempDTO> diffList = new ArrayList<>();
        mpnMap.forEach((mpn, items) -> {
            //根据categoryName的值是否在成品库存数据字典区分原材料和成品
            String categoryName = items.stream().map(B2BCallBackInventoryResultDTO::getCategoryName).distinct().findFirst().orElse("");
            Integer inventoryType = categoryNameList.contains(categoryName) ? NumConstant.INT_1: NumConstant.INT_0;
            InventoryDiffTempDTO diff = new InventoryDiffTempDTO();
            diff.setMpn(mpn).setInventoryType(inventoryType);
            diff.setAvailableQuantity(items.stream().map(m -> m.getAvailableQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
            diff.setFreezeQuantity(items.stream().map(m -> m.getFreezeQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
            diff.setAlibabaStockQty(items.stream().map(m -> m.getInventoryQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
            if (!cipMap.containsKey(mpn)) {
                diff.setZteStockQty(NumConstant.BIG_0).setZteOnlineQty(NumConstant.BIG_0).setZteInstockQty(NumConstant.BIG_0);
            } else {
                List<CustomerInventoryPickUpDTO> finalCipList = cipMap.get(mpn);
                diff.setZteInstockQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(NumConstant.STR_1)).map(n -> n.getVendorInventoryQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
                diff.setZteOnlineQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(NumConstant.STR_2)).map(n -> n.getVendorInventoryQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
                diff.setZteStockQty(finalCipList.stream().filter(m -> m.getSourceSystem().equals(STR_3)).map(n -> n.getVendorInventoryQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add));
                if(inventoryType == NumConstant.INT_0){
                    diff.setZteStockQty(diff.getZteInstockQty().add(diff.getZteOnlineQty()));
                }
            }
            diff.setDiffStockQty(diff.getAlibabaStockQty().subtract(diff.getZteStockQty()).abs());
            diffList.add(diff);
        });
        return diffList;
    }
    /* Ended by AICoder, pid:40e5bv2ef1g2092146540897b1e1404a9a37f260 */

    /**
     * 保存差异数据临时表
     *
     * @param inforInventoryList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInforInventoryToTemp(List<CustomerInventoryPickUpDTO> inforInventoryList) {
        for (List<CustomerInventoryPickUpDTO> tempList : CommonUtils.splitList(inforInventoryList, NumConstant.INT_500)) {
            inventoryDiffQueryRepository.addInventoryTempData(tempList);
        }
    }

    /**
     * SN与箱ID未绑定任务监控预警
     *
     * @return
     */
    @Override
    public void unBindSnRelationAlarm() {
        List<UnBindPkgSnJobVo> data = inventoryDiffQueryRepository.getUnBindSnRelationData();
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        String empNo = getEmpNo();
        Map<String, List<UnBindPkgSnJobVo>> groupData = data.stream()
                .collect(Collectors.groupingBy(UnBindPkgSnJobVo::getWhseid));
        groupData.forEach((whseid, list) -> {
            String title = MessageFormat.format("{0}_{1}", RED_DOT_BOX_SN_BIND, whseid);
            processInventoryConsistency(list, this::buildUnBindSnBody, title, empNo);
        });
    }

    /**
     * 构建 “SN与箱ID未绑定任务监控预警” 描述内容
     *
     * @param o
     * @return
     */
    StringBuilder buildUnBindSnBody(Object o) {
        UnBindPkgSnJobVo vo = (UnBindPkgSnJobVo) o;
        StringBuilder body = new StringBuilder();
        //外部单号
        body.append(EXTERNAL_KEY).append(SEMICOLON).append(vo.getExternalkey()).append(CHINESE_COMMA);
        //ASN代表入库，SO代表出库
        //ASN/SO单号
        switch (vo.getTranType()) {
            case STR_STORE:
                body.append(ASN_SOURCE_KEY);
                break;
            case STR_OUT_STORE:
                body.append(SO_SOURCE_KEY);
                break;
            default:
                body.append(SOURCE_KEY);
                break;
        }
        body.append(SEMICOLON).append(vo.getSourcekey());
        return body;
    }

    @Override
    public void originAndMixedBoxMaterialConsistencyWarning() {

        //先取仓库信息
        List<ZteWarehouseInfoDTO> zteWarehouseList = zteAlibabaStockInfoUploadRepository.getInforWarehouseList();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(zteWarehouseList)) {
            return;
        }
        String empNo = getEmpNo();
        /* Started by AICoder, pid:gcddb14f3fw6c261409c0ad4006ddb0433d5efb0 */
        // 箱类别和库区一致性监控
        List<OriginMixedBoxInventoryDTO> originMixedBoxInventoryDtoList = inventoryDiffQueryRepository.getOriginAndMixedBoxInventory(zteWarehouseList);
        //按仓库分组
        if (CollectionUtils.isNotEmpty(originMixedBoxInventoryDtoList)) {
            Map<String, List<OriginMixedBoxInventoryDTO>> originMixedBoxInventoryMap = originMixedBoxInventoryDtoList.stream().collect(Collectors.groupingBy(OriginMixedBoxInventoryDTO::getWhseid));
            for (Map.Entry<String, List<OriginMixedBoxInventoryDTO>> entry : originMixedBoxInventoryMap.entrySet()) {
                processInventoryConsistency(entry.getValue(), this::buildOriginMixedBoxDesc, RED_DOT_BOX_TYPE + TWO_SPLIT + entry.getKey(), empNo);
            }
        }

        // 混箱区库存一致性监控
        List<MixedBoxInventoryDTO> mixedBoxInventoryDtoList = inventoryDiffQueryRepository.getMixedBoxAndSnInventory(zteWarehouseList);
        if (CollectionUtils.isNotEmpty(mixedBoxInventoryDtoList)) {
            Map<String, List<MixedBoxInventoryDTO>> mixedBoxInventoryMap = mixedBoxInventoryDtoList.stream().collect(Collectors.groupingBy(MixedBoxInventoryDTO::getWhseid));
            for (Map.Entry<String, List<MixedBoxInventoryDTO>> entry : mixedBoxInventoryMap.entrySet()) {
                processInventoryConsistency(entry.getValue(), this::buildMixedBoxSnDesc, RED_DOT_MIXED_BOX_INVENTORY + TWO_SPLIT + entry.getKey(), empNo);
            }
        }
        /* Ended by AICoder, pid:gcddb14f3fw6c261409c0ad4006ddb0433d5efb0 */
    }

    /* Started by AICoder, pid:zcddb14f3f56c261409c0ad4006ddb5433d0efb0 */
    private void processInventoryConsistency(List<?> inventoryList, DescBuilder descBuilder, String redEqpCode, String empNo) {
        if (CollectionUtils.isEmpty(inventoryList)) {
            return;
        }
        for (List<?> inventoryDtoList : CommonUtils.splitList(inventoryList, INT_10)) {
            Map<String, String> mapHeader = new HashMap<>(INT_16);
            mapHeader.put("X-Factory-Id", STR_51);
            mapHeader.put("X-Emp-No", empNo);
            mapHeader.put("X-Auth-Value", authToken);
            RedDotTaskDTO redDotTaskDTO = new RedDotTaskDTO();
            redDotTaskDTO.setEqpCode(redEqpCode);
            redDotTaskDTO.setEqpName(redEqpCode);
            redDotTaskDTO.setFloor(B25);
            redDotTaskDTO.setSendTo(STRING_EMPTY);
            redDotTaskDTO.setSendCc(STRING_EMPTY);
            redDotTaskDTO.setReddotCode(DISTR08);
            redDotTaskDTO.setSource(AUTOMATIC_ORDER);
            redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
            String desc = inventoryDtoList.stream().map(descBuilder::buildDesc).collect(Collectors.joining(SPLIT_22 + STR_FEED));
            redDotTaskDTO.setDesc(desc);
            String responseStr = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
            LOGGER.info(MessageFormat.format("{0}->{1}", redEqpCode, responseStr));
        }
    }

    @FunctionalInterface
    interface DescBuilder {
        /**
         * 构建desc参数
         * @param dto
         * @return
         */
        StringBuilder buildDesc(Object dto);
    }

    private StringBuilder buildOriginMixedBoxDesc(Object dto) {
        OriginMixedBoxInventoryDTO originMixedBoxDto = (OriginMixedBoxInventoryDTO) dto;
        return new StringBuilder(WM_WHSE_NAME).append(SEMICOLON).append(originMixedBoxDto.getWhseid()).append(CHINESE_COMMA)
                .append(ITEM_NO_NAME).append(SEMICOLON).append(originMixedBoxDto.getSku()).append(CHINESE_COMMA)
                .append(BOX_NO).append(SEMICOLON).append(originMixedBoxDto.getId()).append(CHINESE_COMMA)
                .append(BOX_TYPE).append(SEMICOLON).append(originMixedBoxDto.getPkgtype()).append(CHINESE_COMMA)
                .append(LOC).append(SEMICOLON).append(originMixedBoxDto.getLoc()).append(CHINESE_COMMA)
                .append(PUT_AWAY_ZONE).append(SEMICOLON).append(originMixedBoxDto.getPutawayzone()).append(CHINESE_COMMA)
                .append(ZONE_TYPE).append(SEMICOLON).append(originMixedBoxDto.getZonetype());
    }

    private StringBuilder buildMixedBoxSnDesc(Object dto) {
        MixedBoxInventoryDTO mixedBoxInventoryDto = (MixedBoxInventoryDTO) dto;
        return new StringBuilder(WM_WHSE_NAME).append(SEMICOLON).append(mixedBoxInventoryDto.getWhseid()).append(CHINESE_COMMA)
                .append(ITEM_NO_NAME).append(SEMICOLON).append(mixedBoxInventoryDto.getSku()).append(CHINESE_COMMA)
                .append(BOX_NO).append(SEMICOLON).append(mixedBoxInventoryDto.getId()).append(CHINESE_COMMA)
                .append(LOC).append(SEMICOLON).append(mixedBoxInventoryDto.getLoc()).append(CHINESE_COMMA)
                .append(BOX_QTY).append(SEMICOLON).append(mixedBoxInventoryDto.getQty()).append(CHINESE_COMMA)
                .append(SN_QTY).append(SEMICOLON).append(mixedBoxInventoryDto.getSnQty());
    }

    private StringBuilder buildZteAlibabaDiffDesc(Object dto) {
        ZteAliInventoryDTO zteAliInventoryDto = (ZteAliInventoryDTO) dto;
        return new StringBuilder(MPN).append(SEMICOLON).append(zteAliInventoryDto.getMpn()).append(CHINESE_COMMA)
                .append(ZTE_INSTOCK_QTY).append(SEMICOLON).append(zteAliInventoryDto.getZteInstockQty()).append(CHINESE_COMMA)
                .append(ZTE_ONLINE_QTY).append(SEMICOLON).append(zteAliInventoryDto.getZteOnlineQty()).append(CHINESE_COMMA)
                .append(ALIBABA_STOCK_QTY).append(SEMICOLON).append(zteAliInventoryDto.getAlibabaStockQty()).append(CHINESE_COMMA)
                .append(DIFF_STOCK_QTY).append(SEMICOLON).append(zteAliInventoryDto.getDiffStockQty());
    }
    /* Ended by AICoder, pid:zcddb14f3f56c261409c0ad4006ddb5433d0efb0 */

    @Override
    public void aliInventoryConsistencyWarning() {
        String empNo = getEmpNo();

        //先调用库存统计更新接口
        synchronizeInventoryDiffData(empNo);

        //开启异步线程，5分钟后再查询并告警
        ThreadUtil.ALIBABA_JOB_EXECUTOR.execute(() -> {
            try {
                aliInventoryWarning(empNo);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

    }

    private void aliInventoryWarning(String empNo) throws InterruptedException {
        //因阿里库存同步是异步动作，线程先等待5分钟
        Thread.sleep(TimeUnit.MINUTES.toMillis(INT_5));
        processInventoryConsistency(inventoryDiffQueryRepository.getZteAndAliInventoryDiff(), this::buildZteAlibabaDiffDesc, RED_DOT_ZTE_ALIBABA_INVENTORY,empNo);
    }

    private String getEmpNo() {
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100007600004);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        return sysLookupValuesDTO.getLookupMeaning();
    }

    /* Started by AICoder, pid:n79a1jb7f1r374914a310a4ea1ab19094df3b4ac */
    /**
     * 保存成品库存到zte库存快照表
     * @param empNo
     */
    public void dealProductInventoryData(String empNo) {
        try {
            List<CustomerInventoryPickUpDTO> productInventoryList = getProductInventory(empNo);
            saveProductInventoryToStatics(productInventoryList);
        } catch (Exception e) {
            LOGGER.error("定时任务获取成品库存数据异常:", e);
            BusiAssertException.result("定时任务获取成品库存数据异常:" + e.getMessage());
        }
    }
    /**
     * 保存成品库存到zte库存快照表
     * @param productInventoryList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveProductInventoryToStatics(List<CustomerInventoryPickUpDTO> productInventoryList) {
        /* Started by AICoder, pid:427a2ya6f1bdf3a144c30bcb8066b32d81b6ea60 */
        if(CommonUtils.isEmpty(productInventoryList)){
            return;
        }
        /* Ended by AICoder, pid:427a2ya6f1bdf3a144c30bcb8066b32d81b6ea60 */
        zteAlibabaStockInfoUploadRepository.updateInventoryStaticsData();
        for (List<CustomerInventoryPickUpDTO> tempList : CommonUtils.splitList(productInventoryList, NumConstant.INT_500)) {
            inventoryDiffQueryRepository.saveProductInventoryStaticsData(tempList);
        }
    }

    /**
     * 返回统计计算后的成品库存
     * @param empNo
     */
    public List<CustomerInventoryPickUpDTO>  getProductInventory(String empNo)throws Exception {
        List<CustomerInventoryPickUpDTO> allInventoryStaticsList = new ArrayList<>();
        //获取成品库存返回数据
        List<AlibabaOnHandQtyVO> productInventoryDataList = getProductInventoryData(empNo);
        for(AlibabaOnHandQtyVO productInventoryVo:productInventoryDataList){
            if(!productInventoryVo.getInventoryType().equals(Constant.INT_1)){
                continue;
            }
            List<CustomerInventoryListVO> customerInventoryList = productInventoryVo.getCustomerInventoryList();
            if(CommonUtils.isEmpty(customerInventoryList)){
                continue;
            }
            //统计计算
            Map<String, List<CustomerInventoryListVO>> mpnMap = customerInventoryList.stream()
                    .collect(Collectors.groupingBy(CustomerInventoryListVO::getMpn, LinkedHashMap::new, Collectors.toList()));
            mpnMap.forEach((mpn, items) -> {
                CustomerInventoryPickUpDTO productInventory = new CustomerInventoryPickUpDTO();
                productInventory.setMpn(mpn).setInventoryType(Constant.STR_NUMBER_ONE).setSourceSystem(Constant.STR_NUMBER_THREE).setWhseid(Constant.STR_NUMBER_ONE);
                productInventory.setCustomerControlType(Constant.STR_NUMBER_ONE).setItemType(NumConstant.INT_1);
                productInventory.setCreatedBy(empNo).setLastUpdatedBy(empNo);
                productInventory.setConfigModel(items.get(NumConstant.INT_0).getConfigModel());
                productInventory.setVendorInventoryQuantity(items.stream().map(m -> BigDecimal.valueOf(m.getVendorInventoryQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add));
                productInventory.setDeliveredQuantity(items.stream().map(m -> BigDecimal.valueOf(m.getDeliveredQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add));
                allInventoryStaticsList.add(productInventory);
            });
        }

        return allInventoryStaticsList;
    }
    /**
     * 获取成品库存返回数据
     * @param empNo
     * @return
     */
    public List<AlibabaOnHandQtyVO> getProductInventoryData(String empNo) throws Exception{
        List<AlibabaOnHandQtyVO> productInventoryDataList = new ArrayList<>();
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(X_EMP_NO, empNo);
        headers.put(INONE_APPCODE, inoneAppcode);
        //请求体
        Map<String, Object> params = new HashMap<>();
        params.put("isGetData", Constant.FLAG_Y);
        // 请求url
        String url = inoneUrl + PRODUCT_INVENTORY_URL;
        // 发送请求
        String result = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(params), headers);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
        if (ORG_ID_0000.equals(retCode)) {
            String bo = json.get(Constant.JSON_BO).toString();
            productInventoryDataList = JacksonJsonConverUtil.jsonToListBean(bo,new TypeReference<List<AlibabaOnHandQtyVO>>() {});
        }

        return productInventoryDataList;
    }
    /* Ended by AICoder, pid:n79a1jb7f1r374914a310a4ea1ab19094df3b4ac */


}
/* Ended by AICoder, pid:u20930f59aa4e48149b9098052f8f36e1ef76cb6 */
