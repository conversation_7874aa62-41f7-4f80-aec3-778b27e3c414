package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.infor.RedDotExecuteService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.RedDotExecuteInfo;
import com.zte.domain.model.infor.RedDotExecuteRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.RedDotExecuteDetilInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoListVo;
import com.zte.interfaces.infor.vo.RedDotExecuteInfoVo;
import com.zte.interfaces.material.dto.OverTimeBarcodeDTO;
import com.zte.interfaces.material.dto.OverTimeInDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * 红点执行实现类
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class RedDotExecuteServiceImpl implements RedDotExecuteService {
    private static final Logger log = LoggerFactory.getLogger(RedDotExecuteServiceImpl.class);
    @Value(("${redDot.task.token:}"))
    private String authToken;

    @Value("${redDot.task.url:}")
    private String redDotUrl;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private RedDotExecuteRepository redDotExecuteRepository;

    @Autowired
    StepTransferRepository stepIscpRepository;

    /**
     * 触发订单红点JOB
     */
    @Override
    public void orderRedDotTriggerJob(){
        //获取数据字典
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_RED_DOT);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getRedDotSysList(sysLookupValuesDTO);
        //获取仓库排除清单
        List<String> whseNotList = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000077.equals(t.getLookupType()))
                .map(SysLookupValuesDTO::getLookupMeaning)
                .collect(Collectors.toList());
        //获取仓库
        List<PlDbDTO> whsetList = redDotExecuteRepository.getInforWarehouseList(whseNotList);
        //获取分配订单超时时间
        String allocationTimeout= getLookupCode(Constant.LOOKUP_CODE_100007600001,sysLookupValuesDTOList).getLookupMeaning();
        //获取订单行超时时间
        String timeout= getLookupCode(Constant.LOOKUP_CODE_100007600002,sysLookupValuesDTOList).getLookupMeaning();
        //获取执行人
        String empNo= getLookupCode(Constant.LOOKUP_CODE_100007600004,sysLookupValuesDTOList).getLookupMeaning();
        //获取复检预警天数
        String warningDays= getLookupCode(Constant.LOOKUP_CODE_100005900004,sysLookupValuesDTOList).getLookupMeaning();
        //获取红点触发记录
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(INT_1);
        List<RedDotExecuteInfo> redList=redDotExecuteRepository.getRedDotExecuteInfo(redDotExecuteInfo);
        log.info("############获取已执行红点记录", JsonUtil.toJSONString(redList));
        Map<String, String> strMap = new HashMap<>();
        strMap.put(X_EMP_NO,  empNo);
        strMap.put(ALLOCATION_TIMEOUT,  allocationTimeout);
        strMap.put(TIMEOUT,  timeout);
        strMap.put(WARNING_DAYS,  warningDays);
        //异步调用
        ThreadUtil.RED_DOT_EXECUTOR.execute(() -> getOrderData(whsetList,redList,strMap,sysLookupValuesDTOList));
    }

    /**
     * 获取订单信息
     * @param whsetList 仓库List
     * @param strMap 红点执行数据字典值
     * @param redList 订单执行记录
     * @param sysLookupValuesDTOList 数据字典
     */
    public void getOrderData(List<PlDbDTO> whsetList,List<RedDotExecuteInfo> redList,Map<String, String> strMap,List<SysLookupValuesDTO> sysLookupValuesDTOList){
        log.info("############orderRedDotTriggerJob开始调用", JsonUtil.toJSONString(whsetList));
        Integer timeout = Integer.parseInt(strMap.get(ALLOCATION_TIMEOUT));//订单分配超时时长
        //获取超期复检白名单
        List<OrdersDto> delayWhit = redDotExecuteRepository.getDelayWhiteLists(OrdersDto.builder().build().setOrderKey(STRING_EMPTY).setWhseId(STRING_EMPTY));
        log.info("############获取超期复检白名单", JsonUtil.toJSONString(delayWhit));
        List<RedDotExecuteInfo> allList=new ArrayList<>();
        //按仓库计算
        whsetList.forEach(e -> {
            //删除订单状态>=95的红点记录
            redDotExecuteRepository.deleteRedDot(e.getWarehouseId());
            OrdersDto ordersDto=new OrdersDto();
            ordersDto.setWhseId(e.getWarehouseId());
            ordersDto.setIsTimeout(timeout);
            //获取订单状态<95的所有订单
            List<OrdersDto> list = redDotExecuteRepository.getOrderData(ordersDto);
            if(CollectionUtils.isNotEmpty(list)){
                log.info("############"+e.getWarehouseId()+"仓获取订单信息", JsonUtil.toJSONString(list));
                //获取工厂配置
                SysLookupValuesDTO dto = sysLookupValuesDTOList.stream()
                        .filter(t -> Constant.LOOKUP_TYPE_1000071.equals(t.getLookupType()) && t.getLookupMeaning().equals(e.getWarehouseId())).findFirst().orElse(new SysLookupValuesDTO());
                e.setXFactoryId(dto.getAttribute3());
                e.setWmwhseName(dto.getDescription());
                //红点计算
                List<RedDotExecuteInfo> redDotExecuteInfoList = calculationRedDot(list,redList,strMap,e,delayWhit);
                if(CollectionUtils.isNotEmpty(redDotExecuteInfoList)){
                    allList.addAll(redDotExecuteInfoList);
                }
            }
        });
        //处理红点执行记录
        if(CollectionUtils.isNotEmpty(allList)){
            // 创建一个 Set 来存储 redList 中的唯一组合
            Set<List<String>> redSet = redList.stream()
                    .map(m2 -> Arrays.asList(m2.getWhseId(), m2.getExternreceiptkey(), m2.getExternreceiptnumber()))
                    .collect(Collectors.toSet());
            List<RedDotExecuteInfo> retursRed = allList.stream().map(m -> {
                List<String> key = Arrays.asList(m.getWhseId(), m.getExternreceiptkey(), m.getExternreceiptnumber());
                if (redSet.contains(key)) {
                    m.setIsAdd(INT_0);
                }
                return m;
            }).collect(Collectors.toList());
            //插入数据或修改数据
            batchUpdateRedDotInfo(retursRed);
        }
        log.info("############orderRedDotTriggerJob结束调用", JsonUtil.toJSONString(whsetList));
    }

    /**
     * 处理红点记录
     * @param list 执行红点记录
     */
    public void batchUpdateRedDotInfo(List<RedDotExecuteInfo> list){
        //三次超期会出现重复场景（但红点需多次发送）
        List<RedDotExecuteInfo> distinctList = new ArrayList<>(new LinkedHashSet<>(list));
        //获取超期复检（超期复检与其他红点场景会重复），超期复检不存在已执行的新增，其他修改
        List<RedDotExecuteInfo> timeOutRedList=distinctList.stream().filter(t -> t.getOverTimeCheckRed()==INT_1 && t.getIsAdd()==INT_1).collect(Collectors.toList());
        // 创建一个 Set 来存储 redList 中的唯一组合
        Set<List<String>> redSet = timeOutRedList.stream()
                .map(m2 -> Arrays.asList(m2.getWhseId(), m2.getExternreceiptkey(), m2.getExternreceiptnumber()))
                .collect(Collectors.toSet());

        List<RedDotExecuteInfo> retursRed = distinctList.stream().map(m -> {
            List<String> key = Arrays.asList(m.getWhseId(), m.getExternreceiptkey(), m.getExternreceiptnumber());
            if (redSet.contains(key) && m.getOverTimeCheckRed()==INT_0) {
                m.setIsAdd(INT_0);
                m.setOverTimeCheckRed(INT_1);
            }
            return m;
        }).collect(Collectors.toList());
        //获取新增数据
        List<RedDotExecuteInfo> addRedList=retursRed.stream().filter(t -> t.getIsAdd()==INT_1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(addRedList)){
            List<List<RedDotExecuteInfo>> splitAddList = CommonUtils.splitList(addRedList, INT_500);
            for (List<RedDotExecuteInfo> saList : splitAddList) {
                redDotExecuteRepository.batchInsertRedDotInfo(saList);
            }
        }

        //获取修改数据，并且排个序避免之前步骤红点覆盖之后红点
        List<RedDotExecuteInfo> updateRedList=retursRed.stream().filter(t -> t.getIsAdd()==INT_0).sorted(Comparator.comparing(RedDotExecuteInfo::getRedDotSubclass)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(updateRedList)){
            List<List<RedDotExecuteInfo>> splitUpList = CommonUtils.splitList(updateRedList, INT_500);
            for (List<RedDotExecuteInfo> suList : splitUpList) {
                redDotExecuteRepository.batchUpdateRedDotInfo(suList);
            }
        }
    }

    /**
     * 红点触发计算
     * @param list 订单List
     * @param strMap 红点执行数据字典值
     * @param redList 订单执行记录
     * @param plDbDTO 仓库信息
     * @param delayWhit 三次超期白名单
     */
    public List<RedDotExecuteInfo> calculationRedDot(List<OrdersDto> list,List<RedDotExecuteInfo> redList,Map<String, String> strMap,PlDbDTO plDbDTO,List<OrdersDto> delayWhit){
        List<RedDotExecuteInfo> redDotExecuteInfoList=new ArrayList<>();
        //获取订单分配超时(订单状态小于10，订单时间超两小时，按订单发送红点)
        List<OrdersDto> timeoutList = list.stream()
                .filter(dto -> dto.getIsTimeout() == INT_1)  // 过滤出超时的订单
                .collect(Collectors.toMap(
                        OrdersDto::getOrderKey,                  // 使用订单号作为键
                        dto -> dto,                              // 值为OrdersDto对象本身
                        (existing, replacement) -> existing      // 如果有重复，保留第一个
                ))
                .values()
                .stream()
                .peek(dto -> dto.setOrderLineNumber(null))       // 将订单行号置为空
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(timeoutList)){
            //获取当前仓库的订单分配超时红点记录
            List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getRedDotSubclass()==INT_1 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
            // 创建一个 Set 来存储 redList 中的唯一 externreceiptkey
            Set<String> redSet = whseIdRedList.stream()
                    .map(RedDotExecuteInfo::getExternreceiptkey)
                    .collect(Collectors.toSet());
            // 过滤掉在 redSet 中存在的订单（在红点执行记录不存在，才触发红点）
            List<OrdersDto> result = timeoutList.stream()
                    .filter(t -> !redSet.contains(t.getOrderKey()))
                    .collect(Collectors.toList());
            List<RedDotExecuteInfo> allTimeoutRedList = sendRedDotTask(result,strMap,plDbDTO,INT_1);
            if(CollectionUtils.isNotEmpty(allTimeoutRedList)){
                redDotExecuteInfoList.addAll(allTimeoutRedList);
            }
        }
        //获取订单已分配
        List<OrdersDto> allList=list.stream().filter(t -> t.getIsTimeout()==INT_0).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(allList)){
            //处理订单已分配红点场景
            List<RedDotExecuteInfo> allNotTimeoutRedList = allocationNotTimeoutRedDot(redList,allList,strMap,plDbDTO,delayWhit);
            if(CollectionUtils.isNotEmpty(allNotTimeoutRedList)) {
                redDotExecuteInfoList.addAll(allNotTimeoutRedList);
            }
        }
        return redDotExecuteInfoList;
    }

    /**
     * 订单已分配红点业务处理
     * @param redList 红点执行记录
     * @param ordersDtos 订单
     * @param strMap 红点执行数据字典值
     * @param plDbDTO 仓库信息
     * @param delayWhit 三次超期白名单
     * @return
     */
    public List<RedDotExecuteInfo> allocationNotTimeoutRedDot(List<RedDotExecuteInfo> redList,List<OrdersDto> ordersDtos,Map<String, String> strMap,PlDbDTO plDbDTO,List<OrdersDto> delayWhit){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        //获取当前仓库的三次超期复检红点
        List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getOverTimeCheckRed() == INT_1 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
        //在红点执行记录不存在，才触发红点
        List<OrdersDto> result = getRedDotSet(whseIdRedList,ordersDtos);
        //获取超期复检红点
        if(CollectionUtils.isNotEmpty(result)){
            List<RedDotExecuteInfo> redDotExecuteInfoList=getOrderLot02ByWhiteList(result,plDbDTO,delayWhit,strMap);
            if(CollectionUtils.isNotEmpty(redDotExecuteInfoList)){
                redExecuteInfoList.addAll(redDotExecuteInfoList);
            }
        }
        //订单是否齐套业务逻辑
        List<RedDotExecuteInfo> kittingRedDotExecuteInfoList=orderKittingRedDot(redList,ordersDtos,strMap,plDbDTO);
        if(CollectionUtils.isNotEmpty(kittingRedDotExecuteInfoList)){
            redExecuteInfoList.addAll(kittingRedDotExecuteInfoList);
        }
        return redExecuteInfoList;
    }

    /**
     * 处理齐套和不齐套业务
     * @param redList 红点执行记录
     * @param ordersDtos 订单
     * @param strMap 红点执行数据字典值
     * @param plDbDTO 仓库信息
     * @return
     */
    public List<RedDotExecuteInfo> orderKittingRedDot(List<RedDotExecuteInfo> redList,List<OrdersDto> ordersDtos,Map<String, String> strMap,PlDbDTO plDbDTO){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        //获取齐套数据
        List<OrdersDto> kittingOrdersDtos = ordersDtos.stream().filter(t -> t.getIsKitting() == INT_1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(kittingOrdersDtos)){
            List<RedDotExecuteInfo> allocationTimeout = getOrderAllocationTimeout(redList,kittingOrdersDtos,strMap,plDbDTO);
            if(CollectionUtils.isNotEmpty(allocationTimeout)){
                redExecuteInfoList.addAll(allocationTimeout);
            }
        }

        //获取不齐套数据
        List<OrdersDto> notKittingOrdersDtos = ordersDtos.stream().filter(t -> t.getIsKitting() == INT_0).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notKittingOrdersDtos)){
            List<RedDotExecuteInfo> lackMaterialsRed = getOrderQty(redList,notKittingOrdersDtos,plDbDTO,strMap);
            if(CollectionUtils.isNotEmpty(lackMaterialsRed)){
                redExecuteInfoList.addAll(lackMaterialsRed);
            }
        }
        return redExecuteInfoList;
    }

    /**
     * 获取库存数据
     * @param redList 红点执行记录
     * @param notKittingOrdersDtos 不齐套订单数据
     * @param plDbDTO 仓库信息
     * @param strMap  红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getOrderQty(List<RedDotExecuteInfo> redList,List<OrdersDto> notKittingOrdersDtos,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        //查询库存数据
        List<List<OrdersDto>> qtySplitList = CommonUtils.splitList(notKittingOrdersDtos, INT_500);
        RedDotOverTimeDTO overTimeDTO=new RedDotOverTimeDTO();
        overTimeDTO.setWhseId(plDbDTO.getWarehouseId());
        List<OrdersDto> qtyOrderDt=new ArrayList<>();
        for (List<OrdersDto> dtoList : qtySplitList) {
            overTimeDTO.setOrdersList(dtoList);
            List<OrdersDto> list = redDotExecuteRepository.getOrderQty(overTimeDTO);
            if(CollectionUtils.isNotEmpty(list)){
                qtyOrderDt.addAll(list);
            }
        }
        if(CollectionUtils.isNotEmpty(qtyOrderDt)){
            //获取不齐套红点
            redExecuteInfoList = getNotKittingRed(qtyOrderDt,redList,plDbDTO,strMap);
        }
        return redExecuteInfoList;
    }

    /**
     * 获取不齐套红点
     * @param qtyOrderDt 订单信息
     * @param redList 红点执行记录
     * @param plDbDTO 仓库信息
     * @param strMap  红字执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getNotKittingRed(List<OrdersDto> qtyOrderDt,List<RedDotExecuteInfo> redList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        List<OrdersDto> availableQty = qtyOrderDt.stream().filter(t -> t.getAvailableQty() > INT_0).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(availableQty)){
            //获取当前仓库的待补货红点
            List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getRedDotSubclass() == INT_RED_4 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
            log.info("############获取待补货记录", JsonUtil.toJSONString(availableQty));
            //在红点执行记录不存在，才触发红点
            List<OrdersDto> result = getRedDotSet(whseIdRedList,availableQty);
            if(CollectionUtils.isNotEmpty(result)){
                //触发红点
                List<RedDotExecuteInfo> redExecuteInfoList = sendRedDotTask(result,strMap,plDbDTO,INT_RED_4);//执行红点
                if(CollectionUtils.isNotEmpty(redExecuteInfoList)){
                    allRedList.addAll(redExecuteInfoList);
                }
            }
        }
        //获取缺料和库存冻结红点
        List<RedDotExecuteInfo> LmFeRedList = getLackMaterialsAndFreezeRed(qtyOrderDt,redList,plDbDTO,strMap);
        if(CollectionUtils.isNotEmpty(LmFeRedList)){
            allRedList.addAll(LmFeRedList);
        }
        return allRedList;
    }

    /**
     * 获取缺料和库存冻结红点
     * @param qtyOrderDt 库存订单
     * @param redList 红点执行记录
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getLackMaterialsAndFreezeRed(List<OrdersDto> qtyOrderDt,List<RedDotExecuteInfo> redList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        //获取当前仓库的缺料红点
        List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getRedDotSubclass() == INT_3 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
        //在红点执行记录不存在，才触发红点
        List<OrdersDto> result = getRedDotSet(whseIdRedList,qtyOrderDt);
        if(CollectionUtils.isNotEmpty(result)){
            //获取缺料订单(非100\110\120\130\131\140\432\430\420\461单据类型”)
            List<OrdersDto> lackMaterialsNotList = result.stream().filter(t -> t.getQty() == INT_0 && !ORDER_TYPE_RED_DOT.contains(t.getRef11())).collect(Collectors.toList());
            log.info("############获取缺料订单非100单据类型”)", JsonUtil.toJSONString(lackMaterialsNotList));
            if(CollectionUtils.isNotEmpty(lackMaterialsNotList)){
                //触发红点
                plDbDTO.setRedDotThreeClass(INT_1);//属于需计调核实缺料红点
                List<RedDotExecuteInfo> redExecuteInfoList = sendRedDotTask(lackMaterialsNotList,strMap,plDbDTO,INT_3);//执行红点
                if(CollectionUtils.isNotEmpty(redExecuteInfoList)){
                    allRedList.addAll(redExecuteInfoList);
                }
            }

            //获取缺料订单(100\110\120\130\131\140\432\430\420\461单据类型”)
            List<OrdersDto> lackMaterialsList = result.stream().filter(t -> t.getQty() == INT_0 && ORDER_TYPE_RED_DOT.contains(t.getRef11())).collect(Collectors.toList());
            log.info("############获取缺料订单100单据类型”)", JsonUtil.toJSONString(lackMaterialsList));
            //获取供应商送货与待调拨收货
            List<RedDotExecuteInfo> receiptRedList = getIsSupplierDeliveryRed(lackMaterialsList,plDbDTO,strMap);
            if(CollectionUtils.isNotEmpty(receiptRedList)){
                allRedList.addAll(receiptRedList);
            }
        }
        //获取冻结数据
        List<OrdersDto> holdOrderList = qtyOrderDt.stream().filter(t -> t.getQty() > INT_0 && t.getAvailableQty()<= INT_0).collect(Collectors.toList());
        log.info("############获取冻结记录", JsonUtil.toJSONString(holdOrderList));
        List<RedDotExecuteInfo> holdRedList = getHoldRedList(redList,holdOrderList,plDbDTO,strMap);
        if(CollectionUtils.isNotEmpty(holdRedList)){
            allRedList.addAll(holdRedList);
        }
        return allRedList;
    }

    /**
     * 获取冻结红点信息
     * @param redList 红点执行记录
     * @param holdOrderList 冻结订单
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getHoldRedList(List<RedDotExecuteInfo> redList,List<OrdersDto> holdOrderList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        //根据库存量>0且可用量<0获取冻结信息
        if(CollectionUtils.isNotEmpty(holdOrderList)){
            //获取当前仓库的库存冻结红点
            List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getRedDotSubclass() == INT_RED_5 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
            //在红点执行记录不存在，才触发红点
            List<OrdersDto> result = getRedDotSet(whseIdRedList,holdOrderList);
            if(CollectionUtils.isNotEmpty(result)) {
                //查询冻结信息
                List<String> skuList = result.stream().map(OrdersDto::getSku).collect(Collectors.toList());
                List<List<String>> holdSplitList = CommonUtils.splitList(skuList, INT_500);
                RedDotOverTimeDTO overTimeDTO = new RedDotOverTimeDTO();
                overTimeDTO.setWhseId(plDbDTO.getWarehouseId());
                List<OrdersDto> holdLotList = new ArrayList<>();
                for (List<String> dtoList : holdSplitList) {
                    overTimeDTO.setSkuList(dtoList);
                    List<OrdersDto> list = redDotExecuteRepository.getOrderHoldInfo(overTimeDTO);
                    if (CollectionUtils.isNotEmpty(list)) {
                        holdLotList.addAll(list);
                    }
                }
                return getHoldDescInfo(result, holdLotList,plDbDTO,strMap);
            }
        }
        return allRedList;
    }

    /**
     * 获取冻结原因
     * @param holdOrderList 冻结订单
     * @param holdLotList 冻结信息
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getHoldDescInfo(List<OrdersDto> holdOrderList,List<OrdersDto> holdLotList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(holdOrderList)) {
            //按sku分组拼接冻结原因
            Map<String, String> groupedHoldDesc = holdLotList.stream()
                    .collect(Collectors.groupingBy(
                            OrdersDto::getSku,
                            Collectors.mapping(OrdersDto::getHoldDesc,
                                    Collectors.joining(HOLD_SPLIT))
                    ));
            holdOrderList.forEach(e -> {
                String holdDesc = groupedHoldDesc.getOrDefault(e.getSku(), STRING_EMPTY);
                e.setHoldDesc(holdDesc);
            });

            //获取存在冻结原因的数据
            List<OrdersDto> allHoldOrderList= holdOrderList.stream().filter(t -> !t.getHoldDesc().equals(STRING_EMPTY)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(allHoldOrderList)){
                //查询QCFAILED、RECHOLD冻结数据，赋值属于检验/入库冻结数据
                List<String> skuList = holdLotList.stream().filter(t -> HOLD_CHECK_TYPE.contains(t.getHoldCode())).map(OrdersDto::getSku).distinct().collect(Collectors.toList());
                return getHoldOrderInfo(allHoldOrderList,skuList,plDbDTO,strMap);
            }
        }
        return allRedList;
    }

    /**
     * 标记订单属于检验/入库冻结信息
     * @param holdOrderList 冻结订单
     * @param skuList 冻结信息
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getHoldOrderInfo(List<OrdersDto> holdOrderList,List<String> skuList,PlDbDTO plDbDTO,Map<String, String> strMap){
        if(CollectionUtils.isNotEmpty(skuList)){
            holdOrderList.forEach(e -> {
                if (skuList.contains(e.getSku())) {
                    e.setIsHoldCheck(INT_1);
                }
            });
        }
        return sendRedDotTask(holdOrderList,strMap,plDbDTO,INT_RED_5);//执行红点
    }

    /**
     * 获取供应商送货与待调拨收货相关红点
     * @param lackMaterialsList 缺料订单
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getIsSupplierDeliveryRed(List<OrdersDto> lackMaterialsList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(lackMaterialsList)){
            //获取物料代码
            List<String> skuList = lackMaterialsList.stream().map(k -> k.getSku()).collect(Collectors.toList());
            List<List<String>> splitList = CommonUtils.splitList(skuList, INT_500);
            List<OrdersDto> allOrderList=new ArrayList<>();
            RedDotOverTimeDTO overTimeDTO=new RedDotOverTimeDTO();
            overTimeDTO.setWhseId(plDbDTO.getWarehouseId());
            for (List<String> strList : splitList) {
                overTimeDTO.setSkuList(strList);
                List<OrdersDto> list = redDotExecuteRepository.getReceiptBarcode(overTimeDTO);
                if(CollectionUtils.isNotEmpty(list)){
                    allOrderList.addAll(list);
                }
            }
            return getReceiptRedList(lackMaterialsList,allOrderList,plDbDTO,strMap);
        }
        return allRedList;
    }

    /**
     * 获取接收相关红点信息
     * @param lackMaterialsList 缺料订单
     * @param allOrderList 接收-订单
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getReceiptRedList(List<OrdersDto> lackMaterialsList,List<OrdersDto> allOrderList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        //不存在接收明细
        if(CollectionUtils.isEmpty(allOrderList)){
            //触发红点
            plDbDTO.setRedDotThreeClass(INT_1);//属于需计调核实缺料红点
            return sendRedDotTask(lackMaterialsList,strMap,plDbDTO,INT_3);//执行红点
        }else{
            List<String> allSkuList=new ArrayList<>();
            //获取供应商送货
            List<String> supplierDeliveryList = allOrderList.stream().filter(t-> t.getIsSupplierDelivery()==INT_1).map(OrdersDto::getSku).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(supplierDeliveryList)){
                allSkuList.addAll(supplierDeliveryList);
                //触发红点
                plDbDTO.setRedDotThreeClass(INT_2);//属于供应商缺料红点
                List<OrdersDto> supplierDeliveryOrderList = lackMaterialsList.stream().filter(t -> supplierDeliveryList.contains(t.getSku())).collect(Collectors.toList());
                List<RedDotExecuteInfo> redExecuteInfoList = sendRedDotTask(supplierDeliveryOrderList,strMap,plDbDTO,INT_3);//执行红点
                if(CollectionUtils.isNotEmpty(redExecuteInfoList)){
                    allRedList.addAll(redExecuteInfoList);
                }
            }
            //获取调拨有接收明细及调拨和供应商没有接收明细红点
            List<RedDotExecuteInfo> allotRedList = getAllotRedList(lackMaterialsList,allOrderList,allSkuList,plDbDTO,strMap);
            if(CollectionUtils.isNotEmpty(allotRedList)){
                allRedList.addAll(allotRedList);
            }
        }
        return allRedList;
    }

    /**
     * 获取调拨有接收明细及调拨和供应商没有接收明细红点
     * @param lackMaterialsList 缺料订单
     * @param allOrderList 接收明细订单
     * @param allSkuList 物料代码List
     * @param plDbDTO 仓库信息
     * @param strMap 红点执行数据字典
     * @return
     */
    public List<RedDotExecuteInfo> getAllotRedList(List<OrdersDto> lackMaterialsList,List<OrdersDto> allOrderList,List<String> allSkuList,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> allRedList=new ArrayList<>();
        //获取调拨收货
        List<String> allotList = allOrderList.stream().filter(t-> t.getIsSupplierDelivery()==INT_0).map(OrdersDto::getSku).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(allotList)){
            allSkuList.addAll(allotList);
            //触发红点
            plDbDTO.setRedDotThreeClass(INT_3);//属于调拨缺料红点
            List<OrdersDto> allotOrderList = lackMaterialsList.stream().filter(t -> allotList.contains(t.getSku())).collect(Collectors.toList());
            List<RedDotExecuteInfo> redExecuteInfoList = sendRedDotTask(allotOrderList,strMap,plDbDTO,INT_3);//执行红点
            if(CollectionUtils.isNotEmpty(redExecuteInfoList)){
                allRedList.addAll(redExecuteInfoList);
            }
        }
        //获取排除接收明细的缺料红点
        plDbDTO.setRedDotThreeClass(INT_1);//属于需计调核实缺料红点
        List<OrdersDto> notReceiptOrderList = lackMaterialsList.stream().filter(t -> !allSkuList.contains(t.getSku())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notReceiptOrderList)){
            List<RedDotExecuteInfo> redExecuteInfoList = sendRedDotTask(notReceiptOrderList,strMap,plDbDTO,INT_3);//执行红点
            if(CollectionUtils.isNotEmpty(redExecuteInfoList)){
                allRedList.addAll(redExecuteInfoList);
            }
        }
        return allRedList;
    }

    /***
     * 获取订单行超时
     * @param redList 红点已执行记录
     * @param kittingOrdersDtos 齐套订单信息
     * @param strMap 红点执行数据字典值
     * @param plDbDTO 仓库信息
     * @return
     */
    public List<RedDotExecuteInfo> getOrderAllocationTimeout(List<RedDotExecuteInfo> redList,List<OrdersDto> kittingOrdersDtos,Map<String, String> strMap,PlDbDTO plDbDTO){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        //获取当前仓库的订单行超时红点
        List<RedDotExecuteInfo> whseIdRedList = redList.stream().filter(t -> t.getRedDotSubclass() == INT_2 && t.getWhseId().equals(plDbDTO.getWarehouseId())).collect(Collectors.toList());
        //在红点执行记录不存在，才触发红点
        List<OrdersDto> result = getRedDotSet(whseIdRedList,kittingOrdersDtos);
        if(CollectionUtils.isNotEmpty(result)){
            Integer timeout = Integer.parseInt(strMap.get(TIMEOUT));//订单行超时时长
            //获取订单行超时
            RedDotOverTimeDTO overTimeDTO=new RedDotOverTimeDTO();
            overTimeDTO.setWhseId(plDbDTO.getWarehouseId());
            overTimeDTO.setAllocationTimeout(timeout);
            List<OrdersDto> allOrderDt=new ArrayList<>();
            List<List<OrdersDto>> splitList = CommonUtils.splitList(result, INT_500);
            for (List<OrdersDto> dtoList : splitList) {
                overTimeDTO.setOrdersList(dtoList);
                List<OrdersDto> list = redDotExecuteRepository.getOrderAllocationTimeout(overTimeDTO);
                if(CollectionUtils.isNotEmpty(list)){
                    allOrderDt.addAll(list);
                }
            }
            if(CollectionUtils.isNotEmpty(allOrderDt)){
                redExecuteInfoList = sendRedDotTask(allOrderDt,strMap,plDbDTO,INT_2);//执行红点
            }
        }
        return redExecuteInfoList;
    }

    /**
     * 获取220条码及排除白名单订单
     * @param result
     * @param plDbDTO
     * @param delayWhit
     * @param strMap
     * @return
     */
    public List<RedDotExecuteInfo> getOrderLot02ByWhiteList(List<OrdersDto> result,PlDbDTO plDbDTO,List<OrdersDto> delayWhit,Map<String, String> strMap){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        List<List<OrdersDto>> splitList = CommonUtils.splitList(result, INT_500);
        //获取批属性02
        RedDotOverTimeDTO overTimeDTO=new RedDotOverTimeDTO();
        overTimeDTO.setWhseId(plDbDTO.getWarehouseId());
        List<OrdersDto> lotOrderDt=new ArrayList<>();
        for (List<OrdersDto> dtoList : splitList) {
            overTimeDTO.setOrdersList(dtoList);
            List<OrdersDto> list = redDotExecuteRepository.getOrderLot02(overTimeDTO);
            if(CollectionUtils.isNotEmpty(list)){
                lotOrderDt.addAll(list);
            }
        }
        //超期复检红点业务处理
        if(CollectionUtils.isNotEmpty(lotOrderDt)){
            log.info("############获取超期复检批属性02", JsonUtil.toJSONString(lotOrderDt));
            //创建一个 Set 来存储 redList 中的唯一组合
            Set<List<String>> redSet = delayWhit.stream().filter(t -> t.getWhseId().equals(plDbDTO.getWarehouseId()))
                    .map(t -> Arrays.asList(t.getOrderKey(), t.getLottable02()))
                    .collect(Collectors.toSet());
            //排除白名单数据
            List<OrdersDto> overTimeList = lotOrderDt.stream()
                    .filter(m -> !redSet.contains(Arrays.asList(m.getOrderKey(), m.getLottable02())))
                    .collect(Collectors.toList());
            //获取三次超期红点
            if(CollectionUtils.isNotEmpty(overTimeList)){
                redExecuteInfoList = checkOverTime(overTimeList,plDbDTO,strMap);
            }
        }
        return redExecuteInfoList;
    }

    /**
     * 校验条码是否超期复检，返回未复检的红点执行记录
     * @param lotOrderDt
     * @param plDbDTO
     * @param strMap
     * @return
     */
    public List<RedDotExecuteInfo> checkOverTime(List<OrdersDto> lotOrderDt,PlDbDTO plDbDTO,Map<String, String> strMap){
        List<RedDotExecuteInfo> redExecuteInfoList=new ArrayList<>();
        List<String> itemCodeList = lotOrderDt.stream().map(OrdersDto::getLottable02).distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(itemCodeList, INT_500);
        List<OverTimeBarcodeDTO> allOverTimeInDTOList=new ArrayList<>();
        //获取是超二次继续存储期的条码
        for (List<String> strList : splitList) {
            OverTimeInDTO dto=new OverTimeInDTO();
            dto.setItemBarcode(strList);
            List<OverTimeBarcodeDTO> overTimeInDTOList = stepIscpRepository.getValidateBarcode(dto);
            if(CollectionUtils.isNotEmpty(overTimeInDTOList)){
                allOverTimeInDTOList.addAll(overTimeInDTOList);
            }
        }
        if(CollectionUtils.isEmpty(allOverTimeInDTOList)){
            return redExecuteInfoList;
        }
        List<OrdersDto> list = getOverTimeBarcodeList(lotOrderDt,allOverTimeInDTOList,plDbDTO,strMap);
        if(CollectionUtils.isNotEmpty(list)){
            redExecuteInfoList = sendRedDotTask(list,strMap,plDbDTO,INT_0);//执行红点
        }
        return redExecuteInfoList;
    }

    /**
     * 获取超期未复检订单
     * @param lotOrderDt
     * @param allOverTimeInDTOList
     * @param plDbDTO
     * @param strMap
     * @return
     */
    public List<OrdersDto> getOverTimeBarcodeList(List<OrdersDto> lotOrderDt,List<OverTimeBarcodeDTO> allOverTimeInDTOList,PlDbDTO plDbDTO,Map<String, String> strMap){
        //超二次继续存储期的条码转换成List<String>
        List<String> barcodeList = allOverTimeInDTOList.stream().map(k -> k.getItemBarcodes()).collect(Collectors.toList());
        //查询超期条码的检验记录
        List<String> allOverTimeList=new ArrayList<>();
        OverTimeInDTO overTimeInNewDTO = new OverTimeInDTO();
        overTimeInNewDTO.setWhseid(plDbDTO.getWarehouseId());
        overTimeInNewDTO.setWarningDays(Integer.parseInt(strMap.get(WARNING_DAYS)));//复检预警天数
        List<List<String>> splitBarcodeList = CommonUtils.splitList(barcodeList, INT_500);
        for (List<String> strList : splitBarcodeList) {
            overTimeInNewDTO.setItemBarcode(strList);
            List<String> overTimeList = redDotExecuteRepository.getDelayCheckInfo(overTimeInNewDTO);
            if(CollectionUtils.isNotEmpty(overTimeList)){
                allOverTimeList.addAll(overTimeList);
            }
        }
        //获取等于超期的订单
        List<OrdersDto> orderDt = lotOrderDt.stream().filter(t -> barcodeList.contains(t.getLottable02()))
                .collect(Collectors.toList());
        //排除已检验的订单
        if(CollectionUtils.isNotEmpty(allOverTimeList)){
            return orderDt.stream()
                    .filter(t -> !allOverTimeList.contains(t.getLottable02()))
                    .collect(Collectors.toList());
        }
        return orderDt;
    }

    /**
     * 获取执行红点记录
     * @param whseIdRedList
     * @param ordersDtos
     * @return
     */
    public List<OrdersDto> getRedDotSet(List<RedDotExecuteInfo> whseIdRedList,List<OrdersDto> ordersDtos){
        //创建一个 Set 来存储 redList 中的唯一组合
        Set<List<String>> redSet = whseIdRedList.stream()
                .map(m2 -> Arrays.asList(m2.getExternreceiptkey(), m2.getExternreceiptnumber()))
                .collect(Collectors.toSet());
        //在红点执行记录不存在，才触发红点
        return ordersDtos.stream()
                .filter(t -> !redSet.contains(Arrays.asList(t.getOrderKey(), t.getOrderLineNumber())))
                .collect(Collectors.toList());
    }

    /**
     * 获取红点发送群组名称
     * @param plDbDTO
     * @param dto
     * @return
     */
    public String getLocationName(PlDbDTO plDbDTO,OrdersDto dto){
        //整机按仓库发送，部件按班组发送,班组为空按仓库发送
        String locationName = plDbDTO.getWmwhseName();
        // 如果是部件仓，则使用班组名称isNotEmpty
        if (plDbDTO.getDbType() == INT_1 && Tools.isNotEmpty(dto.getClassgroup())) {
            locationName = dto.getClassgroup();
        }
        return locationName;
    }

    /**
     * 获取仓库、订单、订单行号红点描述信息
     * @param plDbDTO
     * @param dto
     * @return
     */
    public String getOrderDesc(PlDbDTO plDbDTO,OrdersDto dto){
        StringBuilder desc = new StringBuilder(WM_WHSE_NAME).append(SEMICOLON).append(plDbDTO.getWmwhseName()).append(CHINESE_COMMA)
                .append(ORDER).append(SEMICOLON).append(dto.getOrderKey()).append(CHINESE_COMMA)
                .append(EXTERNALORDERKEY2).append(SEMICOLON).append(dto.getExternalorderkey2()).append(CHINESE_COMMA)
                .append(LINENUMBER).append(SEMICOLON).append(dto.getOrderLineNumber()).append(CHINESE_COMMA);
        return desc.toString();
    }

    /***
     * 动态拼接红点信息
     * @param dto
     * @param redDotSubclass
     * @param plDbDTO
     * @return
     */
    public Map<String, String> getRedDotDesc(OrdersDto dto,Integer redDotSubclass,PlDbDTO plDbDTO){
        StringBuilder desc = new StringBuilder();
        Map<String, String> descMap = new HashMap<>();
        if(redDotSubclass == INT_0){
            descMap.put(EQP_NAME, OVER_TIME_RED + getLocationName(plDbDTO,dto));
            //超期未复检EQP信息及不同红点场景描述信息
            desc.append(getOrderDesc(plDbDTO,dto))
                    .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                    .append(BARCODE_NAME).append(SEMICOLON).append(dto.getLottable02())
                    .append(OVER_TIME_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,STRING_EMPTY);
        }
        else if(redDotSubclass == INT_1){
            //订单分配超时EQP信息及不同红点场景描述信息
            descMap.put(EQP_NAME,  ALLOCATION_RED+plDbDTO.getWmwhseName());
            desc.append(WM_WHSE_NAME).append(SEMICOLON).append(plDbDTO.getWmwhseName()).append(CHINESE_COMMA)
                    .append(ORDER).append(SEMICOLON).append(dto.getOrderKey()).append(CHINESE_COMMA)
                    .append(EXTERNALORDERKEY2).append(SEMICOLON).append(dto.getExternalorderkey2()).append(CHINESE_COMMA)
                    .append(ALLOCATION_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,ORDER_TIMEOUT_ALLOT);
        }
        else if(redDotSubclass == INT_2){
            //订单行超时EQP信息及不同红点场景描述信息
            descMap.put(EQP_NAME, PICKING_TIMEOUT + getLocationName(plDbDTO,dto));
            desc.append(getOrderDesc(plDbDTO,dto))
                    .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                    .append(PICKING_TIMEOUT_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,STRING_EMPTY);
        }
        else if(redDotSubclass == INT_RED_4){
            //待补货EQP信息及不同红点场景描述信息
            descMap.put(EQP_NAME, REPLENISH + getLocationName(plDbDTO,dto));
            desc.append(getOrderDesc(plDbDTO,dto))
                    .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                    .append(REPLENISH_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,REPLENISH_TITLE);
        }else{
            //其他红点
            return getLackMaterialsAndFreezeRedDesc(dto,redDotSubclass,plDbDTO);
        }
        return descMap;
    }

    /***
     * 获取缺料、冻结红点
     * @param dto
     * @param redDotSubclass
     * @param plDbDTO
     * @return
     */
    public Map<String, String> getLackMaterialsAndFreezeRedDesc(OrdersDto dto,Integer redDotSubclass,PlDbDTO plDbDTO){
        StringBuilder desc = new StringBuilder();
        Map<String, String> descMap = new HashMap<>();
        //缺料EQP信息及描述信息
        if(redDotSubclass == INT_3){
            if(plDbDTO.getRedDotThreeClass()==INT_1){
                //计调需核实缺料红点
                descMap.put(EQP_NAME,  MATERIAL_SHORTAGE+plDbDTO.getWmwhseName());
                desc.append(getOrderDesc(plDbDTO,dto))
                        .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                        .append(MATERIAL_SHORTAGE_ERROR);
                descMap.put(DESC,  desc.toString());
                descMap.put(ERROR_REASON,MATERIAL_SHORTAGE_TITLE);
            } else if (plDbDTO.getRedDotThreeClass()==INT_2) {
                //供应商待送红点
                descMap.put(EQP_NAME,  MATERIAL_SHORTAGE_SUPPLIER+plDbDTO.getWmwhseName());
                desc.append(getOrderDesc(plDbDTO,dto))
                        .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                        .append(MATERIAL_SHORTAGE_SUPPLIER_ERROR);
                descMap.put(DESC,  desc.toString());
                descMap.put(ERROR_REASON,TAKE_OVER);
            }else{
                //调拨待收红点
                descMap.put(EQP_NAME,  MATERIAL_SHORTAGE_ALLOT + getLocationName(plDbDTO,dto));
                desc.append(getOrderDesc(plDbDTO,dto))
                        .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                        .append(MATERIAL_SHORTAGE_ALLOT_ERROR);
                descMap.put(DESC,  desc.toString());
                descMap.put(ERROR_REASON,TAKE_OVER);
            }
        }else {
            //库存冻结红点
            return getFreezeRedDesc(dto,plDbDTO);
        }
        return descMap;
    }

    /**
     * 获取冻结红点描述
     * @param dto 订单信息
     * @param plDbDTO 仓库信息
     * @return
     */
    public Map<String, String> getFreezeRedDesc(OrdersDto dto,PlDbDTO plDbDTO){
        StringBuilder desc = new StringBuilder();
        Map<String, String> descMap = new HashMap<>();
        if(dto.getIsHoldCheck()==INT_0){
            //库存冻结红点
            descMap.put(EQP_NAME,  FREEZE + getLocationName(plDbDTO,dto));
            desc.append(getOrderDesc(plDbDTO,dto))
                    .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                    .append(QTY_HOLD).append(SEMICOLON).append(dto.getHoldDesc()).append(CHINESE_COMMA)
                    .append(FREEZE_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,dto.getHoldDesc());
        }else{
            //库存冻结-检验红点
            descMap.put(CHECKOUT,  FREEZE + plDbDTO.getWmwhseName());
            desc.append(getOrderDesc(plDbDTO,dto))
                    .append(ITEM_NO_NAME).append(SEMICOLON).append(dto.getSku()).append(CHINESE_COMMA)
                    .append(CHECKOUT_ERROR);
            descMap.put(DESC,  desc.toString());
            descMap.put(ERROR_REASON,dto.getHoldDesc());
        }
        return descMap;
    }

    /**
     * 触发红点任务
     * @param list 订单信息
     * @param strMap 红点执行数据字典值
     * @param plDbDTO 仓库信息
     * @param redDotSubclass 红点场景小类
     */
    public List<RedDotExecuteInfo> sendRedDotTask(List<OrdersDto> list,Map<String, String> strMap,PlDbDTO plDbDTO,Integer redDotSubclass) {
        List<RedDotExecuteInfo> redDotExecuteInfoList=new ArrayList<>();
        //获取仓库与红点群编码关系
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000098);
        List<SysLookupValuesDTO> sysLookupValuesDtoS= this.getRedDotSysList(sysLookupValuesDTO);
        try {
            Map<String, String> mapHeader = new HashMap<>();
            mapHeader.put(X_FACTORY_ID, plDbDTO.getXFactoryId());
            mapHeader.put(X_EMP_NO,  strMap.get(X_EMP_NO));
            mapHeader.put(X_AUTH_VALUE, authToken);
            list.forEach(e -> {
                RedDotTaskDTO redDotTaskDTO = new RedDotTaskDTO();
                Map<String, String> descMap = getRedDotDesc(e,redDotSubclass,plDbDTO);//红点不同场景描述拼接
                redDotTaskDTO.setEqpCode(descMap.get(EQP_NAME));
                redDotTaskDTO.setEqpName(descMap.get(EQP_NAME));
                redDotTaskDTO.setFloor(B25);
                redDotTaskDTO.setReddotCode(DISTR04);
                redDotTaskDTO.setDesc(descMap.get(DESC));
                redDotTaskDTO.setSource(AUTOMATIC_ORDER);
                redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
                HttpClientUtil.httpPostWithJSON(redDotUrl, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
                //根据需单独执行红点的仓库再次发送信息
                this.getRedDotCodeBySend(sysLookupValuesDtoS,e.getWhseId(),redDotTaskDTO,mapHeader);
                //增加执行记录
                RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
                redDotExecuteInfo.setIsAdd(INT_1);//用来区别新增还是修改
                redDotExecuteInfo.setRedDotType(INT_1);
                redDotExecuteInfo.setRedDotSubclass(redDotSubclass);//0标识不属于任何场景
                redDotExecuteInfo.setOverTimeCheckRed(INT_0);
                if(redDotSubclass==INT_0){
                    redDotExecuteInfo.setOverTimeCheckRed(INT_1);//超期复检红点场景
                }
                redDotExecuteInfo.setWhseId(e.getWhseId());
                redDotExecuteInfo.setExternreceiptkey(e.getOrderKey());
                redDotExecuteInfo.setExternreceiptnumber(e.getOrderLineNumber());
                redDotExecuteInfo.setRedDotStatus(INT_1);
                redDotExecuteInfo.setCreatedBy(LOWERCASE_SYSTEM);
                redDotExecuteInfo.setLastUpdatedBy(LOWERCASE_SYSTEM);
                redDotExecuteInfo.setBillType(e.getRef11());
                redDotExecuteInfo.setErrorReason(descMap.get(ERROR_REASON));
                redDotExecuteInfo.setExternalorderkey2(e.getExternalorderkey2());
                redDotExecuteInfoList.add(redDotExecuteInfo);
            });
        }catch (Exception ex) {
            log.error("计调任务红点发送失败:", ex);
        }
        return redDotExecuteInfoList;
    }

    /***
     * 获取仓库与群编码关系及发送红点
     * @param sysLookupValuesDtoS 仓库与群编码关系数据字典
     * @param whseId 仓库号
     * @param redDotTaskDTO 红点实体
     * @param mapHeader 请求头信息
     */
    public void getRedDotCodeBySend (List<SysLookupValuesDTO> sysLookupValuesDtoS,String whseId,RedDotTaskDTO redDotTaskDTO,Map<String, String> mapHeader){
        //获取单独发送红点群需再次执行
        if(CollectionUtils.isNotEmpty(sysLookupValuesDtoS)){
            SysLookupValuesDTO valuesDto = sysLookupValuesDtoS.stream()
                    .filter(item -> whseId.equals(item.getLookupMeaning()))
                    .findFirst().orElse(new SysLookupValuesDTO());
            if(Tools.isNotEmpty(valuesDto)){
                redDotTaskDTO.setReddotCode(valuesDto.getDescription());
                HttpClientUtil.httpPostWithJSON(redDotUrl, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
            }
        }
    }

    /***
     * 根据Code获取数据字典信息
     * @param lookupCode
     * @param sysLookupValuesDTOList
     * @return
     */
    public SysLookupValuesDTO getLookupCode(String lookupCode,List<SysLookupValuesDTO> sysLookupValuesDTOList) {
        return sysLookupValuesDTOList.stream()
                .filter(item -> lookupCode.equals(item.getLookupCode()))
                .findFirst().orElse(new SysLookupValuesDTO());
    }

    /**
     * 获取数据字典
     * @param dto
     * @return
     */
    public List<SysLookupValuesDTO> getRedDotSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }

    /**
     * 查询执行记录
     * @param dto
     * @return
     */
    @Override
    public RedDotExecuteInfoListVo queryRedDotInfo(RedDotExecuteInfo dto){
        RedDotExecuteInfoListVo listVo=new RedDotExecuteInfoListVo();
        // 查询
        dto.setStartRow((dto.getPageIndex()- Constant.INT_1) * dto.getPageSize()+Constant.INT_1);
        dto.setEndRow(dto.getPageIndex()*dto.getPageSize());
        //如果业务类型查询条件不为空需转换成对应的单据类型List
        if(Tools.isNotEmpty(dto.getBillType())){
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000074);
            //获取所有单据类型
            List<SysLookupValuesDTO> sysLookupValuesDTOS= this.getRedDotSysList(sysLookupValuesDTO);
            if(QT.equals(dto.getBillType())){
                dto.setQtBillTypeList(sysLookupValuesDTOS.stream().map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList()));
            }else{
                dto.setBillTypeList(sysLookupValuesDTOS.stream().filter(t-> dto.getBillType().equals(t.getAttribute1())).map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList()));
            }
        }
        listVo.setTotal(redDotExecuteRepository.queryRedDotInfoCount(dto));
        //获取红点记录
        List<RedDotExecuteInfoVo> list = redDotExecuteRepository.queryRedDotInfo(dto);
        //赋值数据字典项值
        coverRowsProperties(list);
        listVo.setRedDotList(list);
        return listVo;
    }

    /**
     * 获取数据字典Map值
     * @param list
     * @param productBaseMap
     * @param billTypeMap
     * @param bussTypeMap
     */
    public void getPropertiesMap(List<SysLookupValuesDTO> list,Map<String, SysLookupValuesDTO> productBaseMap,Map<String, String> billTypeMap,Map<String, String> bussTypeMap){
        for (SysLookupValuesDTO valueByTypeCode : list) {
            if (Tools.isEmpty(valueByTypeCode.getLookupType())) {
                continue;
            }
            if (LOOKUP_TYPE_1000071.equals(valueByTypeCode.getLookupType())) {
                productBaseMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode);
            }
            if (LOOKUP_TYPE_1000074.equals(valueByTypeCode.getLookupType())) {
                billTypeMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getAttribute1());
            }
            if (LOOKUP_TYPE_1000075.equals(valueByTypeCode.getLookupType())) {
                bussTypeMap.put(valueByTypeCode.getLookupMeaning(), valueByTypeCode.getDescription());
            }
        }
    }

    /**
     * 赋值多次取数据字典值
     * @param rows
     */
    public void coverRowsProperties(List<RedDotExecuteInfoVo> rows) {
        //获取数据字典
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_RED_DOT_REPORT);
        List<SysLookupValuesDTO> list = this.getRedDotSysList(sysLookupValuesDTO);
        // 1 生产基地、仓库名称map
        Map<String, SysLookupValuesDTO> productBaseMap = new HashMap<>();
        //2 单据类型
        Map<String, String> billTypeMap = new HashMap<>();
        //3 业务类型
        Map<String, String> bussTypeMap = new HashMap<>();
        //获取Map数据字典
        getPropertiesMap(list,productBaseMap,billTypeMap,bussTypeMap);
        // 创建业务类型映射
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String> entry : billTypeMap.entrySet()) {
            String billTypeValue = entry.getValue();
            String businessType = STRING_EMPTY;
            if (bussTypeMap.get(billTypeValue) != null) {
                businessType = bussTypeMap.get(billTypeValue);
            }
            result.put(entry.getKey(), businessType);
        }
        for(RedDotExecuteInfoVo dto: rows){
            // 仓库名称
            dto.setWhseName(productBaseMap.get(dto.getWhseId()).getDescription());
            // 生产基地
            dto.setProductBase(productBaseMap.get(dto.getWhseId()).getAttribute1());
            // 业务类型
            String mappedBillType = result.get(dto.getBillType());
            dto.setBillType(mappedBillType);
            if (Tools.isEmpty(mappedBillType)) {
                dto.setBillType(QT_NAME);
            }
        }
    }

    /**
     * 导出看板记录
     * @param dto
     */
    @Override
    public void exportExcel(RedDotExecuteInfo dto){
        //如果业务类型查询条件不为空需转换成对应的单据类型List
        if(Tools.isNotEmpty(dto.getBillType())){
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000074).setAttribute1(dto.getBillType());
            dto.setBillTypeList(this.getRedDotSysList(sysLookupValuesDTO).stream().map(k -> k.getLookupMeaning()).collect(Collectors.toList()));
        }
        int total = redDotExecuteRepository.queryRedDotInfoCount(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            RedDotExecuteInfo param = ((RedDotExecuteInfo) params).setStartRow(statRow).setEndRow(endRow);
            //获取红点记录
            List<RedDotExecuteInfoVo> list = redDotExecuteRepository.queryRedDotInfo(param);
            //赋值数据字典项值
            coverRowsProperties(list);
            return new ArrayList<>(list);
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.RED_DOT_NAME).setFileName(Constant.RED_DOT_EXPORT).setSheetName(Constant.SHEET1)
                .setQueryParams(dto).setPojoClass(RedDotExecuteInfoVo.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /**
     * 计调任务红点-状态修改
     * @param dto
     */
    @Override
    public void updateRedDotInfo(RedDotExecuteInfo dto){
        redDotExecuteRepository.updateRedDotInfo(dto);
    }

    /**
     * 获取红点详情
     * @param dto
     * @return
     */
    @Override
    public RedDotExecuteDetilInfoListVo queryRedDotDetilInfo(RedDotExecuteInfo dto){
        RedDotExecuteDetilInfoListVo list=new RedDotExecuteDetilInfoListVo();
        list.setDetilType(dto.getDetilType());
        List<OrdersDto> ordersDtoList =new ArrayList<>();
        //按详情类型获取数据
        if(OVER_TIME_CHECK.equals(dto.getDetilType())){
            //超期复检
            ordersDtoList = queryRedDotDetilLot02(dto);
        }else if (IS_TIME_OUT.equals(dto.getDetilType())) {
            //是否超时
            ordersDtoList = redDotExecuteRepository.queryRedDotTimeOutDetilInfo(dto);
        }else{
            //分配异常
            ordersDtoList = redDotExecuteRepository.queryRedDotDetilInfo(dto);
        }
        //为空刷新状态
        if(CollectionUtils.isEmpty(ordersDtoList)){
            dto.setRedDotType(INT_1);
            dto.setLastUpdatedBy(LOWERCASE_SYSTEM);
            if(OVER_TIME_CHECK.equals(dto.getDetilType())){
                //超期复检
                dto.setOverTimeCheckRed(INT_0);
            }else{
                //非超期复检
                dto.setRedDotSubclass(INT_0);
            }
            redDotExecuteRepository.updateRedDotInfo(dto);
        }
        list.setRedDotDetilList(ordersDtoList);
        return list;
    }

    /**
     * 获取复检条码
     * @param dto
     * @return
     */
    public List<OrdersDto> queryRedDotDetilLot02(RedDotExecuteInfo dto){
        List<OrdersDto> list=new ArrayList<>();
        //获取超期复检条码数据
        List<OrdersDto> barcodeList = redDotExecuteRepository.queryRedDotDetilLot02(dto);
        //获取超期复检白名单
        OrdersDto ordersDto=new OrdersDto();
        ordersDto.setOrderKey(dto.getExternreceiptkey());
        ordersDto.setWhseId(dto.getWhseId());
        List<OrdersDto> delayWhitList = redDotExecuteRepository.getDelayWhiteLists(ordersDto);
        List<String> delayWhit = delayWhitList.stream().map(OrdersDto::getLottable02).collect(Collectors.toList());
        //排除白名单数据
        List<OrdersDto> overTimeList = barcodeList.stream()
                .filter(m -> !delayWhit.contains(m.getLottable02()))
                .collect(Collectors.toList());
        //获取三次超期红点
        if(CollectionUtils.isNotEmpty(overTimeList)){
            List<String> itemCodeList = overTimeList.stream().map(OrdersDto::getLottable02).distinct().collect(Collectors.toList());
            List<List<String>> splitList = CommonUtils.splitList(itemCodeList, INT_500);
            List<OverTimeBarcodeDTO> allOverTimeInDTOList=new ArrayList<>();
            //获取是超二次继续存储期的条码
            OverTimeInDTO overTimeInDTO=new OverTimeInDTO();
            for (List<String> strList : splitList) {
                overTimeInDTO.setItemBarcode(strList);
                List<OverTimeBarcodeDTO> overTimeInDTOList = stepIscpRepository.getValidateBarcode(overTimeInDTO);
                if(CollectionUtils.isNotEmpty(overTimeInDTOList)){
                    allOverTimeInDTOList.addAll(overTimeInDTOList);
                }
            }
            if(CollectionUtils.isEmpty(allOverTimeInDTOList)){
                return overTimeList;
            }
            PlDbDTO plDbDTO=new PlDbDTO();
            plDbDTO.setWarehouseId(dto.getWhseId());
            //获取数据字典
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(Constant.LOOKUP_TYPE_1000059);
            List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getRedDotSysList(sysLookupValuesDTO);
            //获取复检预警天数
            String warningDays = getLookupCode(Constant.LOOKUP_CODE_100005900004,sysLookupValuesDTOList).getLookupMeaning();
            Map<String, String> strMap=new HashMap<>();
            strMap.put(WARNING_DAYS,warningDays);
            list = getOverTimeBarcodeList(overTimeList,allOverTimeInDTOList,plDbDTO,strMap);
        }
        return list;
    }

    /**
     * 计调任务红点-触发红点
     * @param dto
     */
    @Override
    public void executeRedDot(RedDotExecuteInfo dto){
        //根据不同场景触发
        Integer redDotSubclass =dto.getRedDotSubclass();
        //获取数据字典
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_EXECUTE_RED_DOT);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = this.getRedDotSysList(sysLookupValuesDTO);
        //判断是否失效及执行时间间隔
        RedDotExecuteInfo redDotExecuteInfo=new RedDotExecuteInfo();
        redDotExecuteInfo.setRedDotType(INT_1);
        redDotExecuteInfo.setRedDotStatus(INT_1);
        redDotExecuteInfo.setWhseId(dto.getWhseId());
        redDotExecuteInfo.setExternreceiptkey(dto.getExternreceiptkey());
        if(redDotSubclass != INT_0){
            redDotExecuteInfo.setExternreceiptnumber(dto.getExternreceiptnumber());
        }
        List<RedDotExecuteInfo> redList=redDotExecuteRepository.getRedDotExecuteInfo(redDotExecuteInfo);
        //判断是否失效
        BusiAssertException.isEmpty(redList, MessageId.RED_DOT_STATUS_CHECK);
        Integer executeMin = Integer.parseInt(getLookupCode(Constant.LOOKUP_CODE_100007600003,sysLookupValuesDTOList).getLookupMeaning());
        Integer executeJgMin = redList.get(INT_0).getExecuteMin();
        if(redDotSubclass == INT_0){
            //时间间隔
            executeJgMin = redList.get(INT_0).getExecuteOverMin();
        }
        BusiAssertException.isTrue(executeMin>executeJgMin,MessageFormat.format(CommonUtils.getLmbMessage(MessageId.RED_DOT_EXECUTE_DATE_CHECK), executeMin.toString()));
        List<OrdersDto> ordersDtoList =new ArrayList<>();
        if(redDotSubclass == INT_0){
            //超期复检
            dto.setOverTimeCheckRed(INT_1);
            ordersDtoList = queryRedDotDetilLot02(dto);
        }else
        {
            //非超期复检场景
            ordersDtoList = executeNotOverTimeCheckRedDot(dto);
        }
        executeRedDotUpdateStatus(dto,ordersDtoList,sysLookupValuesDTOList);
    }

    /***
     * 非超期复检场景执行红点
     * @param dto
     * @return
     */
    public List<OrdersDto> executeNotOverTimeCheckRedDot(RedDotExecuteInfo dto){
        List<OrdersDto> ordersDtoList =new ArrayList<>();
        Integer redDotSubclass =dto.getRedDotSubclass();
        //1.订单行超时场景
        if(redDotSubclass == INT_2){
            ordersDtoList = redDotExecuteRepository.queryRedDotTimeOutDetilInfo(dto);
        }else if(redDotSubclass == INT_1){
            //1.订单分配超时场景
            ordersDtoList = redDotExecuteRepository.queryRedDotAllocDetilInfo(dto);
        }else{
            //补货、缺货、冻结场景取数
            ordersDtoList = redDotExecuteRepository.queryRedDotLackMateDetilInfo(dto);
            if(redDotSubclass == INT_3){
                //缺料场景处理
                ordersDtoList = executeLackMateRedDot(ordersDtoList,dto);
            } else if (redDotSubclass == INT_RED_5) {
                //库存冻结场景处理
                ordersDtoList = executeHoldRedDot(ordersDtoList,dto);
            }
        }
        return ordersDtoList;
    }

    /***
     * 执行冻结红点
     * @param ordersDtoList 订单(实际只存在单个)
     * @param dto 红点信息
     * @return
     */
    public List<OrdersDto> executeHoldRedDot(List<OrdersDto> ordersDtoList,RedDotExecuteInfo dto){
        //为空直接返回
        if(CollectionUtils.isEmpty(ordersDtoList)){
            return ordersDtoList;
        }
        //查询冻结信息
        List<String> skuList = ordersDtoList.stream().map(OrdersDto::getSku).collect(Collectors.toList());
        RedDotOverTimeDTO overTimeDTO = new RedDotOverTimeDTO();
        overTimeDTO.setWhseId(dto.getWhseId());
        overTimeDTO.setSkuList(skuList);
        List<OrdersDto> holdOrderList = redDotExecuteRepository.getOrderHoldInfo(overTimeDTO);
        if (CollectionUtils.isNotEmpty(holdOrderList)) {
            // 拼接冻结原因（集合只有单个SKU）
            String allHoldDesc = holdOrderList.stream()
                    .map(OrdersDto::getHoldDesc)
                    .filter(Objects::nonNull)  // 确保过滤掉可能的null值
                    .collect(Collectors.joining(HOLD_SPLIT));
            //将拼接后的冻结原因设置到所有订单中
            ordersDtoList.forEach(e -> e.setHoldDesc(allHoldDesc));
            // 查询QCFAILED、RECHOLD冻结数据，并赋值属于检验/入库冻结数据
            boolean hasQcHold = holdOrderList.stream()
                    .anyMatch(t -> HOLD_CHECK_TYPE.contains(t.getHoldCode()));
            if (hasQcHold) {
                ordersDtoList.forEach(e -> e.setIsHoldCheck(INT_1));
            }
            return ordersDtoList;
        }
        return null;
    }

    /***
     * 执行缺料红点
     * @param ordersDtoList 订单(实际只存在单个)
     * @param dto 红点信息
     * @return
     */
    public List<OrdersDto> executeLackMateRedDot(List<OrdersDto> ordersDtoList,RedDotExecuteInfo dto){
        //为空直接返回
        if(CollectionUtils.isEmpty(ordersDtoList)){
            return ordersDtoList;
        }
        //获取缺料订单(非100\110\120\130\131\140\432\430\420\461单据类型”)
        if(!ORDER_TYPE_RED_DOT.contains(ordersDtoList.get(INT_0).getRef11())){
            return ordersDtoList;
        }
        //由于前面时不等于直接返回，默认是(100\110\120\130\131\140\432\430\420\461单据类型”)
        List<String> skuList = ordersDtoList.stream().map(OrdersDto::getSku).collect(Collectors.toList());
        RedDotOverTimeDTO overTimeDTO=new RedDotOverTimeDTO();
        overTimeDTO.setWhseId(dto.getWhseId());
        overTimeDTO.setSkuList(skuList);
        List<OrdersDto> receiptOrder = redDotExecuteRepository.getReceiptBarcode(overTimeDTO);
        //不存在接收明细
        if(CollectionUtils.isNotEmpty(receiptOrder)){
            //获取存在接收明细订单
            ordersDtoList.forEach(e -> e.setRedDotThreeClass(INT_3));
            if(receiptOrder.get(INT_0).getIsSupplierDelivery()==INT_1){
                //供应商送货
                ordersDtoList.forEach(e -> e.setRedDotThreeClass(INT_2));
            }
            return ordersDtoList;
        }
        return null;
    }

    /**
     * 执行红点-刷新状态
     * @param dto 需执行红点DTO
     * @param ordersDtoList 触发红点信息
     * @param sysLookupValuesDTOList 数据字典信息
     */
    public void executeRedDotUpdateStatus(RedDotExecuteInfo dto,List<OrdersDto> ordersDtoList,List<SysLookupValuesDTO> sysLookupValuesDTOList){
        Integer redDotSubclass =dto.getRedDotSubclass();
        //为空刷新状态
        dto.setRedDotType(INT_1);
        if(CollectionUtils.isEmpty(ordersDtoList)){
            if(redDotSubclass == INT_0){
                //超期复检
                dto.setOverTimeCheckRed(INT_0);
            }else{
                //非超期复检
                dto.setRedDotSubclass(INT_0);
            }
        }else {
            //获取执行人
            String empNo= getLookupCode(Constant.LOOKUP_CODE_100007600004,sysLookupValuesDTOList).getLookupMeaning();
            //获取红点执行数据字典
            Map<String, String> strMap = new HashMap<>();
            strMap.put(X_EMP_NO,  empNo);
            //获取工厂配置
            List<PlDbDTO> whsetList = redDotExecuteRepository.getInforWarehouseList(null);
            PlDbDTO plDbDTO = whsetList.stream().filter(t -> dto.getWhseId().equals(t.getWarehouseId())).findFirst().orElse(null);
            SysLookupValuesDTO lookupValuesDTO = sysLookupValuesDTOList.stream()
                    .filter(t -> Constant.LOOKUP_TYPE_1000071.equals(t.getLookupType()) && dto.getWhseId().equals(t.getLookupMeaning())).findFirst().orElse(new SysLookupValuesDTO());
            if (plDbDTO != null) {
                if (Tools.isNotEmpty(lookupValuesDTO)) {
                    plDbDTO.setXFactoryId(lookupValuesDTO.getAttribute3());
                    plDbDTO.setWmwhseName(lookupValuesDTO.getDescription());
                }

                if(redDotSubclass == INT_3){
                    plDbDTO.setRedDotThreeClass(ordersDtoList.get(INT_0).getRedDotThreeClass());
                }
                //触发红点
                sendRedDotTask(ordersDtoList,strMap,plDbDTO,redDotSubclass);
                dto.setExecuteCount(INT_1);
            }
        }
        redDotExecuteRepository.updateRedDotInfo(dto);
    }
}
