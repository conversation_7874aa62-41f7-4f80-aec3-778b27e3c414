package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.google.common.collect.Lists;
import com.zte.application.infor.BraidCutMaterialService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.BraidCutMaterialRepository;
import com.zte.interfaces.infor.dto.OutPickingTaskDto;
import com.zte.interfaces.infor.dto.PickingTaskDto;
import com.zte.interfaces.infor.vo.BraidPickingInfoListVo;
import com.zte.interfaces.infor.vo.BraidPickingInfoVo;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BraidCutMaterialServiceImpl implements BraidCutMaterialService {
    @Autowired
    private BraidCutMaterialRepository braidCutMaterialRepository;
    @Override
    public List<OutPickingTaskDto> queryPickingTask(List<PickingTaskDto> list){
        // 1.校验非空
        BusiAssertException.isEmpty(list, MessageId.INPUT_DATA_CAN_NOT_NULL);
        // 查找订单或仓库为空数据
        List<PickingTaskDto> pickingTaskDtoList=list.stream().filter(t -> StringUtils.isEmpty(t.getWhseid())
                || StringUtils.isEmpty(t.getOrderkey())).collect(Collectors.toList());
        // 校验是否存在订单或仓库为空
        BusiAssertException.isNotEmpty(pickingTaskDtoList, MessageId.INPUT_DATA_CAN_NOT_NULL);
        // 拆分集合查询
        List<List<PickingTaskDto>> lists = Lists.partition(list, Constant.INT_500);
        List<OutPickingTaskDto> allList = new ArrayList<>();
        lists.forEach(e -> {
            List<OutPickingTaskDto> outPickingTaskDtos = braidCutMaterialRepository.queryPickingTask(e);
            if(CollectionUtils.isNotEmpty(outPickingTaskDtos)){
                allList.addAll(outPickingTaskDtos);
            }
        });
        return allList;
    }

    /**
     * 查询拆零拣货清单
     * @param queryParams
     * @return
     */
    @Override
    public BraidPickingInfoListVo queryPickingInfo(PickingTaskDto queryParams) {
        Integer strPageIndex = queryParams.getPageIndex();
        queryParams.setStartRow((strPageIndex - Constant.INT_1) * queryParams.getPageSize()+Constant.INT_1);
        queryParams.setEndRow(strPageIndex*queryParams.getPageSize());
        BraidPickingInfoListVo braidPickingInfoListVo=new  BraidPickingInfoListVo();
        braidPickingInfoListVo.setTotal(braidCutMaterialRepository.queryPickingInfoTotal(queryParams));
        braidPickingInfoListVo.setPickingInfoVoList(braidCutMaterialRepository.queryPickingInfo(queryParams));
        return braidPickingInfoListVo;
    }

    /**
     * 导出拆零拣货清单
     * @param queryParams
     */
    @Override
    public void exportPickingInfo(PickingTaskDto queryParams) {
        //设置total
        Integer total = braidCutMaterialRepository.queryPickingInfoTotal(queryParams);
        //构造ExcelExportServer
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            PickingTaskDto paramDto =  ((PickingTaskDto) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(braidCutMaterialRepository.queryPickingInfo(paramDto));
        };
        //构造参数
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.PICKING_INFO).setFileName(Constant.PICKING_INFO_EXPORT).setSheetName(Constant.SHEET1)
                .setQueryParams(queryParams).setPojoClass(BraidPickingInfoVo.class).setTotal((long) total)
                .setReceipt(queryParams.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }
}
