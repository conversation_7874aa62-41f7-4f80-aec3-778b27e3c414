package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.infor.TransferBoxService;
import com.zte.application.step.ZteAlibabaService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.TransferBoxRepository;
import com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.SnBoundDetail;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.dto.TransferReceiptDetailDTO;
import com.zte.interfaces.infor.dto.ZmsTransferBox;
import com.zte.interfaces.step.dto.TransferRequestDTO;
import com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.model.MessageId.SYS_LOOKUP_VALUES_NOT_EXISTS;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_3;
import static com.zte.common.utils.NumConstant.INT_500;
import static com.zte.common.utils.NumConstant.*;

@Service
public class TransferBoxServiceImpl implements TransferBoxService {

    @Autowired
    private ZteAlibabaService zteAlibabaService;
    @Autowired
    private TransferBoxRepository transferBoxRepository;
    @Autowired
    private ZteAlibabStockInfoUploadRepository zteAlibabStockInfoUploadRepository;
    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepositor;

    @Override
    public void syncTransferBoxDataJob() {
        List<ZteWarehouseInfoDTO> inforWarehouseList = zteAlibabStockInfoUploadRepository.getInforWarehouseList();

        if (CollectionUtils.isEmpty(inforWarehouseList)) {
            return;
        }
        for (ZteWarehouseInfoDTO zteWarehouseInfoDTO : inforWarehouseList) {

            List<TransferReceiptDetailDTO> syncData = transferBoxRepository.selectSyncData(zteWarehouseInfoDTO.getWarehouseId());
            if (CollectionUtils.isEmpty(syncData)) {
                continue;
            }
            List<ZmsTransferBox> resultList = Lists.newArrayList();
            SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepositor.getLookupValue(LOOKUP_CODE_100009100002);
            BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
            Map<String, List<TransferReceiptDetailDTO>> groupMap = syncData.stream()
                    .collect(Collectors.groupingBy(TransferReceiptDetailDTO::getExternalKey));
            //根据单号分组后，对单下的明细赋予相同的messageId
            groupMap.forEach((sourceKey, boxes) -> {
                String messageId = UUID.randomUUID().toString();
                List<ZmsTransferBox> transferBoxes = boxes.stream()
                        .map(item -> convertToEntity(item, sysLookupValuesDTO, messageId))
                        .collect(Collectors.toList());
                resultList.addAll(transferBoxes);
            });

            for (List<ZmsTransferBox> transferBox : CommonUtils.splitList(resultList, INT_500)) {
                transferBoxRepository.batchInsertTransferBox(transferBox);
            }
        }
    }

    private ZmsTransferBox convertToEntity(TransferReceiptDetailDTO dto, SysLookupValuesDTO sysLookupValuesDTO, String messageId) {
        ZmsTransferBox entity = new ZmsTransferBox();
        // 字段映射
        entity.setExternalKey(dto.getExternalKey());
        entity.setWhseId(dto.getWhseid());
        entity.setSourceKey(dto.getSourceKey());
        entity.setOldPkgId(dto.getFromId());
        entity.setLoc(dto.getToLoc());
        entity.setLot(dto.getToLot());
        entity.setQty(dto.getQty());
        entity.setSku(dto.getSku());
        entity.setMpn(dto.getCustomerItemNo());
        entity.setItemBarcode(dto.getLottable02());
        entity.setReceiptDate(dto.getDatereceived());

        // 固定值设置
        entity.setStatus(STR_0);
        entity.setCreatedBy(SYSTEM);
        entity.setSendStatus(INT_MINUS_1);
        entity.setSendTimes(INT_0);
        entity.setMessageId(messageId);
        entity.setMessageType(sysLookupValuesDTO.getLookupMeaning());
        if (Tools.equals(NumConstant.INT_1, dto.getUnPack())) {
            entity.setNewPkgId(dto.getToId());
            entity.setStatus(STR_1);
        }
        return entity;
    }

    @Override
    public void processTransfersDataJob() {
        // 1. 查询待处理数据
        List<ZmsTransferBox> pendingList = transferBoxRepository.selectPendingTransfer();
        if (CollectionUtils.isEmpty(pendingList)) {
            return;
        }
        SysLookupValuesDTO sysLookupValuesDTO = inventoryholdRecordRepositor.getLookupValue(LOOKUP_CODE_100009100002);
        BusiAssertException.isEmpty(sysLookupValuesDTO, SYS_LOOKUP_VALUES_NOT_EXISTS);
        // 2. 按sourceKey分组
        Map<String, List<ZmsTransferBox>> groupMap = pendingList.stream()
                .collect(Collectors.groupingBy(ZmsTransferBox::getExternalKey));

        // 3. 处理每个分组
        groupMap.forEach((sourceKey, boxes) -> {
            if (boxes.stream().anyMatch(item -> STR_0.equals(item.getStatus()))) {
                return;
            }
            // 3.1 提取所有旧箱号
            List<String> oldPkgIds = boxes.stream()
                    .map(ZmsTransferBox::getOldPkgId)
                    .distinct()
                    .collect(Collectors.toList());

            // 3.2 查询SN绑定信息
            List<SnBoundDetail> snDetails = transferBoxRepository.selectSnByExternalKeyAndPkgs(
                    sourceKey, oldPkgIds);
           if(CollectionUtils.isEmpty(snDetails)){
               return;
           }
            // 3.3 构建请求DTO
            TransferRequestDTO request = buildTransferRequest(boxes, snDetails);

            String messageId = boxes.stream().map(ZmsTransferBox::getMessageId).findFirst().get();
            ZteDeductionPlanParamDTO zteDeductionPlanParamDTO = buildZteDeductionPlanParamDTO(messageId, sourceKey, sysLookupValuesDTO);
            // 3.4 推送B2B调用阿里接口
            boolean success = zteAlibabaService.pushDataToB2B(JSON.toJSONString(request), zteDeductionPlanParamDTO, CREATE_BILL);

            // 3.5 更新发送状态
            updateTransferStatus(boxes, success);
        });
    }

    private ZteDeductionPlanParamDTO buildZteDeductionPlanParamDTO(String messageId, String sourceKey, SysLookupValuesDTO sysLookupValuesDTO) {

        return ZteDeductionPlanParamDTO
                .builder()
                .messageId(messageId)
                .billNo(sourceKey)
                .empNo(SYSTEM)
                .messageType(sysLookupValuesDTO.getLookupMeaning())
                .build();
    }

    private TransferRequestDTO buildTransferRequest(List<ZmsTransferBox> boxes,
                                                    List<SnBoundDetail> snDetails) {
        // 按MPN分组汇总数量
        Map<String, Integer> mpnQtyMap = boxes.stream()
                .collect(Collectors.groupingBy(ZmsTransferBox::getMpn,
                        Collectors.summingInt(b -> b.getQty().intValue())));

        // 构建行数据
        List<TransferRequestDTO.LineDTO> lines = new ArrayList<>();
        int lineNo = NumConstant.INT_1;

        for (Map.Entry<String, Integer> entry : mpnQtyMap.entrySet()) {
            String mpn = entry.getKey();
            Integer totalQty = entry.getValue();

            // 构建箱数据
            List<TransferRequestDTO.ShipGoodsDTO> shipGoodsList = boxes.stream()
                    .filter(b -> mpn.equals(b.getMpn()))
                    .map(b -> buildShipGoods(b, snDetails))
                    .collect(Collectors.toList());

            lines.add(TransferRequestDTO.LineDTO.builder()
                    .fromWarehouseLocation(ORIGINAL)
                    .lineNo(String.valueOf(lineNo++))
                    .mpn(mpn)
                    .quantity(totalQty)
                    .toWarehouseLocation(MIX)
                    .shipGoodsList(shipGoodsList)
                    .build());
        }

        return TransferRequestDTO.builder()
                .bizScenes(BIZ_SCENES)
                .creatorName(ALIBABA_FACTORY_CODE)
                .expectArriveDate(new Date())
                .sourceNumber(RedisSerialNoUtil.getDateIncreaseId(ALIBABA_FACTORY_CODE+ TWO_SPLIT + MOVE, INT_3))
                .sourceOrganization(ALIBABA_FACTORY_CODE)
                .sourceSubOrganization(ZTE101_CO01)
                .targetOrganization(ALIBABA_FACTORY_CODE)
                .targetSubOrganization(ZTE101_CO01)
                .transactionId(RedisSerialNoUtil.getDateIncreaseId(ALIBABA_FACTORY_CODE + TWO_SPLIT + CREATE_TRANSFER, INT_3))
                .transferType(TRANS_TYPE)
                .lines(lines)
                .build();
    }

    private TransferRequestDTO.ShipGoodsDTO buildShipGoods(ZmsTransferBox box,
                                                           List<SnBoundDetail> snDetails) {
        // 查找该箱对应的SN
        List<SnBoundDetail> boxSn = snDetails.stream()
                .filter(sn -> sn.getPkgId().equals(box.getOldPkgId()))
                .collect(Collectors.toList());

        // 构建子SN数据
        List<TransferRequestDTO.SnChildDTO> children = boxSn.stream()
                .map(sn -> TransferRequestDTO.SnChildDTO.builder()
                        .quantity(1)
                        .skuCode(sn.getSn())
                        .skuType(SN)
                        .warehouseLocation(MIX)
                        .build())
                .collect(Collectors.toList());

        return TransferRequestDTO.ShipGoodsDTO.builder()
                .quantity(NumConstant.INT_1)
                .skuCode(box.getOldPkgId())
                .skuType(BOX)
                .specification(box.getQty().intValue())
                .warehouseLocation(ORIGINAL)
                .children(children)
                .build();
    }


    private void updateTransferStatus(List<ZmsTransferBox> boxes, boolean success) {
        List<String> serialKeys = boxes.stream()
                .map(ZmsTransferBox::getSerialKey)
                .collect(Collectors.toList());
        int status = success ? INT_0 : INT_MINUS_1;
        transferBoxRepository.batchUpdateSendStatus(serialKeys, status);
    }

}