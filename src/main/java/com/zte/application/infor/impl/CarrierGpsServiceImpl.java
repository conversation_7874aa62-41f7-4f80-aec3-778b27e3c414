package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.CarrierGpsService;
import com.zte.common.utils.*;
import com.zte.interfaces.infor.dto.CarrierGpsDTO;
import com.zte.interfaces.infor.dto.CarrierGpsReturnDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.*;

/**
 * b2b调用外部接口方法实现类
 *
 * <AUTHOR>
 */
@Service
public class CarrierGpsServiceImpl implements CarrierGpsService {

    private static final Logger log = LoggerFactory.getLogger(CarrierGpsServiceImpl.class);
    @Autowired
    InteractiveB2B pushDataHandler;

    @Value("${b2b.url}")
    String b2bUrl;
    @Value("${b2b.authorization.appSecret}")
    public String inoneAppcode;


    public List<CarrierGpsDTO> realTimeInteractiveB2B(Object jsonMsgContent) throws Exception {
        Map<String, String> headParams = new HashMap<>();

        try {
            headParams = pushDataHandler.getHeadParams();
        } catch (Exception e) {
            BusiAssertException.isEmpty(BLANK, BLANK);

        }
        List<CarrierGpsDTO> carrierGPSDTOS = new ArrayList<>();

        String responseStr = HttpClientUtil.httpPostWithJSON(b2bUrl, JacksonJsonConverUtil.beanToJson(jsonMsgContent), headParams);
        if (StringUtils.isBlank(responseStr)) {
            BusiAssertException.isEmpty(carrierGPSDTOS, BLANK);
            return null;
        }
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(responseStr);
        if (null == json) {
            BusiAssertException.isEmpty(carrierGPSDTOS, BLANK);
            return null;
        }
        String jsonStr = json.get(JSON_BO).asText();
        CarrierGpsReturnDTO carrierGPSReturnDTO = new CarrierGpsReturnDTO();
        try {
            carrierGPSReturnDTO = JSON.parseObject(jsonStr, CarrierGpsReturnDTO.class);
        } catch (Exception e) {
        }
        if (null == carrierGPSReturnDTO.getObj()) {
            BusiAssertException.isEmpty(carrierGPSDTOS, BLANK);
            return null;
        }
        carrierGPSDTOS = carrierGPSReturnDTO.getObj().getRealCoordinates();
        BusiAssertException.isEmpty(carrierGPSDTOS, BLANK);
        return carrierGPSDTOS;
    }
    @Override
    public List<CarrierGpsDTO> carrierGpsB2B(String waybillNo) throws Exception {
        Map<String, Object> dataMap = new HashMap<>(NUMBER_16);
        dataMap.put(WAYBILL_NO, waybillNo);
        Map<String, Object> jsonMsgContentMap = new HashMap<>(NUMBER_16);
        jsonMsgContentMap.put(METHOD, FACE_TO_CUSTOMER_TRAJECTORY_QUERY);
        jsonMsgContentMap.put(DATA, dataMap);

        Object requestBody = pushDataHandler.getRequestBody(jsonMsgContentMap);
        return realTimeInteractiveB2B(requestBody);
    }
}