package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.DigitQCRedDotService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.DigitQCRedDotDetailDTO;
import com.zte.domain.model.infor.DigitQCRedDotRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.interfaces.infor.dto.DigitQCIdaInfoDTO;
import com.zte.interfaces.infor.dto.DigitQCRedDotManageDTO;
import com.zte.interfaces.infor.dto.RedDotTaskDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.DigitQCRedDotListVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.STR_51;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/4/2 10:05
 */
@Slf4j
@Service
public class DigitQCRedDotServiceImpl implements DigitQCRedDotService {

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    private DigitQCRedDotRepository digitQCRedDotRepository;

    @Value(("${redDot.task.token:}"))
    private String authToken;

    @Value("${redDot.task.url:}")
    private String redDotUrl;

    @Value("${in.one.url}")
    private String inOneUrl;

    @Value("${in.one.wms.app.code}")
    private String appCode;

    SimpleDateFormat sim = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);

    @Override
    public void pullInspectionInfoJob(DigitQCRedDotManageDTO digitQCRedDotManageDTO) throws Exception{
        String creationDate = digitQCRedDotManageDTO.getCreationDate();
        String xEmpNo = digitQCRedDotManageDTO.getCreatedBy();
        List<String> idList = digitQCRedDotRepository.selectIdaInfo();
        Map<String, String> reqInfo = new HashMap<>();
        for (String id : idList) {
            reqInfo.put(IDA_ID, id);
            List<DigitQCIdaInfoDTO> idaInfo = getIdaInfo(reqInfo, xEmpNo);
            for (DigitQCIdaInfoDTO digitQCIdaInfoDTO : idaInfo) {
                if (IDA_MODIFYED.equals(digitQCIdaInfoDTO.getRadioField_71klyzoo())) {
                    digitQCRedDotRepository.updateIDATaskId(digitQCIdaInfoDTO);
                }
            }
        }
        Calendar calendar = Calendar.getInstance();
        // 当前减去n个小时
        calendar.add(Calendar.HOUR_OF_DAY, Integer.parseInt(creationDate));
        reqInfo = new HashMap<>();
        reqInfo.put(CREATE_TIME, sim.format(calendar.getTime()));
        List<DigitQCIdaInfoDTO> dtoList = getIdaInfo(reqInfo, xEmpNo);
        if (Tools.isEmpty(dtoList)) {
            return;
        }
        //获取数据字典
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_QC);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
        Map<String, String> baseMap = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000083.equals(t.getLookupType()) && STR_BM.equals(t.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescription));
        Map<String, String> map = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000080.equals(t.getLookupType()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getDescription, SysLookupValuesDTO::getLookupMeaning));
        Map<String, String> qcState = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000082.equals(t.getLookupType()) && STR_NUMBER_ONE.equals(t.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getDescription, SysLookupValuesDTO::getLookupMeaning));
        Map<String, String> problemState = sysLookupValuesDTOList.stream()
                .filter(t -> Constant.LOOKUP_TYPE_1000082.equals(t.getLookupType()) && STR_NUMBER_TWO.equals(t.getAttribute1()))
                .collect(Collectors.toMap(SysLookupValuesDTO::getDescription, SysLookupValuesDTO::getLookupMeaning));
        List<DigitQCRedDotDetailDTO> digitQCRedDotDetailDTOS = new ArrayList<>();
        DigitQCRedDotDetailDTO digitQCRedDotDetailDTO = null;
        for (DigitQCIdaInfoDTO digitQCIdaInfoDTO : dtoList) {
            digitQCRedDotDetailDTO = new DigitQCRedDotDetailDTO();
            digitQCRedDotDetailDTO.setProductBase(digitQCIdaInfoDTO.getTextField_hs7ptpfg());
            digitQCRedDotDetailDTO.setSourceType(SOURECE_IDA);
            digitQCRedDotDetailDTO.setQcProject(qcState.get(digitQCIdaInfoDTO.getSelectField_d96f32bp()));
            digitQCRedDotDetailDTO.setDepartment(Tools.isEmpty(digitQCIdaInfoDTO.getDepartmentSelectField_29f7llku()) ? "" : baseMap.get(String.join(COMMA, digitQCIdaInfoDTO.getDepartmentSelectField_29f7llku())));
            digitQCRedDotDetailDTO.setSection(digitQCIdaInfoDTO.getTextField_9pamv02v());
            digitQCRedDotDetailDTO.setGroups(digitQCIdaInfoDTO.getSelectField_q73qlbkw());
            if (Tools.isNotEmpty(digitQCIdaInfoDTO.getEmployeeField_du4vdnzf())) {
                List<String> collect = digitQCIdaInfoDTO.getEmployeeField_du4vdnzf().stream()
                        .map(dto -> dto.getEmpName() + dto.getEmpUIID())
                        .collect(Collectors.toList());//责任人
                digitQCRedDotDetailDTO.setLiability(String.join(COMMA, collect));
            }
            if (Tools.isNotEmpty(digitQCIdaInfoDTO.getEmployeeField_y66mk6nl())) {
                List<String> list = digitQCIdaInfoDTO.getEmployeeField_y66mk6nl().stream()
                        .map(dto -> dto.getEmpName() + dto.getEmpUIID())
                        .collect(Collectors.toList());//提交人
                digitQCRedDotDetailDTO.setCreatedBy(String.join(COMMA, list));
            }
            digitQCRedDotDetailDTO.setCreationDate(digitQCIdaInfoDTO.getCreate_time());
            digitQCRedDotDetailDTO.setLastUpdateDate(digitQCIdaInfoDTO.getLast_modified_time());
            digitQCRedDotDetailDTO.setProblemDesc(digitQCIdaInfoDTO.getTextareaField_uu6w7151());
            if (Tools.isNotEmpty(digitQCIdaInfoDTO.getAttachmentField_wzaaw6gl())) {
                List<String> collected = digitQCIdaInfoDTO.getAttachmentField_wzaaw6gl().stream()
                        .map(dto -> dto.getDownloadUrl())
                        .collect(Collectors.toList());
                digitQCRedDotDetailDTO.setAttached(String.join(COMMA, collected));
            }
            digitQCRedDotDetailDTO.setTaskOrderNo(digitQCIdaInfoDTO.getId());
            digitQCRedDotDetailDTO.setState(map.get(digitQCIdaInfoDTO.getRadioField_71klyzoo()));
            digitQCRedDotDetailDTO.setProblemType(problemState.get(digitQCIdaInfoDTO.getSelectField_uu62uoo8()));
            digitQCRedDotDetailDTO.setSolveMeasure(digitQCIdaInfoDTO.getTextareaField());
            digitQCRedDotDetailDTOS.add(digitQCRedDotDetailDTO);
        }
        digitQCRedDotRepository.insertQCRedDotInfo(digitQCRedDotDetailDTOS);
    }

    /**
     * 获取IDA的巡检数据
     * @param reqInfo
     * @param xEmpNo
     */
    public List<DigitQCIdaInfoDTO> getIdaInfo(Map<String, String> reqInfo, String xEmpNo) throws Exception{
        Map<String, String> mapHeader = new HashMap<>(NumConstant.INT_16);
        mapHeader.put(INONE_APPCODE, appCode);
        mapHeader.put(X_EMP_NO, xEmpNo);
        mapHeader.put(INONE_X_ACCOUNT_ID, xEmpNo);
        mapHeader.put(X_LANG_ID, X_LANG_ID_ZH);
        mapHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        String url = inOneUrl + GET_IDA_INFOURL;
        String result = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(reqInfo), mapHeader);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
        if (!ORG_ID_0000.equals(retCode)) {
            return new ArrayList<>();
        }
        String dataArray = json.get(Constant.JSON_BO).toString();
        List<DigitQCIdaInfoDTO> dtoList = JacksonJsonConverUtil.jsonToListBean(dataArray,
                new TypeReference<ArrayList<DigitQCIdaInfoDTO>>() {
                });
        return dtoList;
    }


    @Override
    public void digitQCRedDotImport(List<DigitQCRedDotManageDTO> list, String xEmpNo) throws Exception {
        if (Tools.isEmpty(list)) {
            return;
        }
        List<DigitQCRedDotDetailDTO> digitQCRedDotDetailDTOS = new ArrayList<>();
        DigitQCRedDotDetailDTO digitQCRedDotDetailDTO = null;
        for (DigitQCRedDotManageDTO digitQCRedDotManageDTO : list) {
            digitQCRedDotDetailDTO = new DigitQCRedDotDetailDTO();
            digitQCRedDotManageDTO.setSourceType(SOURECE_IWMS);
            digitQCRedDotManageDTO.setCreationDate(sim.format(new Date()));
            digitQCRedDotManageDTO.setLastUpdateDate(sim.format(new Date()));
            BeanUtils.copyProperties(digitQCRedDotManageDTO, digitQCRedDotDetailDTO);
            digitQCRedDotDetailDTOS.add(digitQCRedDotDetailDTO);
        }
        //异步处理
        ThreadUtil.RED_DOT_DIGITQC.execute(() -> {
            log.info("RED_DOT_DIGITQC---begin");
            //获取数据字典
            SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupTypeList(Constant.LOOKUP_TYPE_QC);
            List<SysLookupValuesDTO> sysLookupValuesDTOList = getOverdueSysList(sysLookupValuesDTO);
            Map<String, String> empnoMap = sysLookupValuesDTOList.stream()
                    .filter(t -> Constant.LOOKUP_TYPE_1000072.equals(t.getLookupType()))
                    .collect(Collectors.toMap(SysLookupValuesDTO::getLookupCode, SysLookupValuesDTO::getLookupMeaning));
            String empNo = empnoMap.get(LOOKUP_CODE_100007200001);
            Map<String, String> baseMap = sysLookupValuesDTOList.stream()
                    .filter(t -> Constant.LOOKUP_TYPE_1000082.equals(t.getLookupType()))
                    .collect(Collectors.toMap(SysLookupValuesDTO::getLookupMeaning, SysLookupValuesDTO::getDescription));
            //判断是否需要发送红点预警并处理状态
            isSendRedDotInfo(digitQCRedDotDetailDTOS, empNo, baseMap);
            digitQCRedDotRepository.insertQCRedDotInfo(digitQCRedDotDetailDTOS);
            log.info("RED_DOT_DIGITQC---end");
        });
    }

    /**
     * 判断红点并处理任务状态
     * @param digitQCRedDotDetailDTOS
     * @param empNo
     * @param baseMap
     */
    public void isSendRedDotInfo(List<DigitQCRedDotDetailDTO> digitQCRedDotDetailDTOS,String empNo, Map<String, String> baseMap) {
        for (DigitQCRedDotDetailDTO qcRedDotDetailDTO : digitQCRedDotDetailDTOS) {
            if (FLAG_Y.equals(qcRedDotDetailDTO.getIsWarning())) {
                String taskId = sendRedDotInfo(qcRedDotDetailDTO, empNo, baseMap);
                qcRedDotDetailDTO.setTaskOrderNo(taskId);
                qcRedDotDetailDTO.setState(ONGOING);//进行中
            }else {
                qcRedDotDetailDTO.setState(IMPORTED);//已导入
            }
        }
    }

    /**
     * 发送红点预警信息
     * @param dto
     * @param empNo
     */
    public String sendRedDotInfo(DigitQCRedDotDetailDTO dto, String empNo, Map<String, String> baseMap) {
        log.info("sendRedDotInfo---DigitQCInfo---begin");
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put(X_FACTORY_ID, STR_51);
        mapHeader.put(X_EMP_NO, empNo);
        mapHeader.put(X_AUTH_VALUE, authToken);
        RedDotTaskDTO redDotTaskDTO = new RedDotTaskDTO();
        StringBuilder depart = new StringBuilder(dto.getDepartment())
                .append(StringUtils.isEmpty(dto.getSection()) ? CENTER_LINE : HOLD_SPLIT + dto.getSection())
                .append(StringUtils.isEmpty(dto.getGroups()) ? CENTER_LINE : HOLD_SPLIT + dto.getGroups());
        StringBuilder desc = new StringBuilder(PRODUCT_BASE).append(SEMICOLON).append(dto.getProductBase()).append(CHINESE_COMMA)
                .append(WM_WHSE_NAME).append(SEMICOLON).append(dto.getInforStock()).append(CHINESE_COMMA)
                .append(QC_PROJECT).append(SEMICOLON).append(baseMap.get(dto.getQcProject())).append(CHINESE_COMMA)
                .append(RESPONSIBLE_DEPARTMENT).append(SEMICOLON).append(depart).append(CHINESE_COMMA)
                .append(LIABILITY).append(SEMICOLON).append(dto.getLiability()).append(CHINESE_COMMA)
                .append(PROBLEM_DESC).append(SEMICOLON).append(dto.getProblemDesc()).append(CHINESE_COMMA)
                .append(PROBLEM_TYPE).append(SEMICOLON).append(baseMap.get(dto.getProblemType()));
        redDotTaskDTO.setEqpCode(RED_DOT_INSPECTION);
        redDotTaskDTO.setEqpName(RED_DOT_INSPECTION);
        redDotTaskDTO.setFloor(B25);
        redDotTaskDTO.setReddotCode(DISTR05);
        redDotTaskDTO.setDesc(desc.toString());
        redDotTaskDTO.setSendTo(dto.getLiability());
        redDotTaskDTO.setSendCc(dto.getCreatedBy());
        redDotTaskDTO.setSource(AUTOMATIC_ORDER);
        redDotTaskDTO.setCreateBy(LOWERCASE_SYSTEM);
        String result = HttpClientUtil.httpPostWithJSON(redDotUrl, JSONObject.toJSONString(redDotTaskDTO), mapHeader);
        JSONObject jsonObject = JSONObject.parseObject(result);
        log.info("sendRedDotInfo---result：{}", jsonObject);
        if (ORG_ID_0000.equals(jsonObject.getJSONObject(JSON_CODE).getString(JSON_CODE))) {
            return jsonObject.getString(JSON_BO);
        }
        return STRING_EMPTY;
    }

    /**
     * 查询数据字典维护的信息
     */
    public List<SysLookupValuesDTO> getOverdueSysList(SysLookupValuesDTO dto) {
        return inventoryholdRecordRepository.getLookupValues(dto);
    }



    @Override
    public DigitQCRedDotListVo digitQCRedDotQuery(DigitQCRedDotManageDTO dto) {
        DigitQCRedDotListVo listVo = new DigitQCRedDotListVo();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(digitQCRedDotRepository.getDigitQCRedDotDetailTotal(dto));
        List<DigitQCRedDotDetailDTO> list = digitQCRedDotRepository.getDigitQCRedDotDetailVo(dto);
        listVo.setDigitQCRedDotDetailDTOS(list);
        return listVo;
    }


    @Override
    public void digitQCRedDotLose(DigitQCRedDotManageDTO dto) {
        List<String> serialKeys = dto.getSerialKeys();
        if (Tools.isEmpty(serialKeys)) {
            return;
        }
        digitQCRedDotRepository.digitQCRedDotInfoLose(serialKeys, dto.getLastUpdatedBy());
    }


    @Override
    public void digitQCRedDotCallback(DigitQCRedDotManageDTO dto) {
        BusiAssertException.isEmpty(dto.getTaskOrderNo(), MessageId.NO_PARAMS);
        digitQCRedDotRepository.digitQCRedDotInfoCallback(dto);
    }

    @Override
    public void digitQCRedDotExport(DigitQCRedDotManageDTO dto, String xEmpNo) {
        int count = digitQCRedDotRepository.getDigitQCRedDotDetailTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            DigitQCRedDotManageDTO param = ((DigitQCRedDotManageDTO) params).setStartRow(statRow).setEndRow(endRow);
            List<DigitQCRedDotDetailDTO> list = digitQCRedDotRepository.getDigitQCRedDotDetailVo(param);
            return new ArrayList<>(list);
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.QUALITY_INSPECTION).setFileName(Constant.QUALITY_INSPECTION).setSheetName(Constant.SHEET1)
                .setQueryParams(dto).setPojoClass(DigitQCRedDotDetailDTO.class).setTotal((long) count)
                .setReceipt(xEmpNo).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

}
