/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-11-19
 * 修改历史 :
 *   1. [2019-11-19] 创建文件 by 6396000647
 **/
package com.zte.application.infor.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.infor.IscpEsbLogService;
import com.zte.domain.model.infor.IscpEsbLog;
import com.zte.domain.model.infor.IscpEsbLogRepository;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Service
public class IscpEsbLogServiceImpl implements IscpEsbLogService {

    @Autowired
    private IscpEsbLogRepository iscpEsbLogRepository;

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertIscpEsbLog(IscpEsbLog record) {
        iscpEsbLogRepository.insertIscpEsbLog(record);
    }

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void updateIscpEsbLog(IscpEsbLog record) {
        iscpEsbLogRepository.updateIscpEsbLog(record);
    }

    /**
     * get all record
     * 
     * @param record
     * @return List<IscpEsbLog>
     **/
    @Override
    public List<IscpEsbLog> selectIscpEsbLog(IscpEsbLog record) {
        return iscpEsbLogRepository.selectIscpEsbLog(record);
    }
}