package com.zte.application.infor.impl;


import com.zte.application.infor.UpdateInforStageService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.UpdateInforStageRepository;
import com.zte.interfaces.infor.dto.CodelKup;
import com.zte.interfaces.infor.dto.Orders;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


@Service
public class UpdateInforStageServiceImpl implements UpdateInforStageService {
    @Autowired
    UpdateInforStageRepository updateInforStage;

    /**
     * 自动波更新待运库位
     */
    @Override
    public void updateInforStage() {
        //设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String tim = df.format(new Date());
        List<String> getWhse = updateInforStage.getAllWhse();
        List<CodelKup> codeList = Tools.newArrayList();
        getWhse.forEach(t -> {
            CodelKup code = updateInforStage.getCodelKup(t);
            if (Tools.isNotEmpty(code)) {
                //更新PIKTO
                String strPik = "";
                if (tim.equals(code.getUdf1())) {
                    strPik = code.getUdf2();
                } else {
                    strPik = String.valueOf(NumConstant.INT_0);
                }
                int strNum = Integer.parseInt(strPik) + NumConstant.INT_1;
                //获取需要更新的数据
                List<Orders> getList = updateInforStage.getOrdersList(t);
                strPik = getStrPik(strPik, strNum, getList);
                //更新orders表stage
                if (Tools.isNotEmpty(getList)) {
                    List<List<Orders>> listOfList = CommonUtils.splitList(getList, NumConstant.BATCH_SIZE);
                    listOfList.forEach(k -> updateInforStage.updateStageByOrders(k));
                    //更新配置表，最新日期和最大PIKTO
                    updateInforStage.updateCodeListByWhse(t, strPik, tim);
                }
            }
        });
    }

    private String getStrPik(String strPik, int strNum, List<Orders> getList) {
        int count = NumConstant.INT_0;
        while (count < getList.size()) {
            strPik = String.format("%2d", strNum).replace(" ", String.valueOf(NumConstant.INT_0));
            strNum += NumConstant.INT_1;
            count += NumConstant.INT_1;
            if (strNum >= NumConstant.INT_601) {
                strNum = NumConstant.INT_1;
            }
            String strPickTO = "PICKTO" + strPik;

            getList.stream().filter(f -> Tools.isEmpty(f.getStage()))
                    .findFirst()
                    .map(k -> {
                        k.setStage(strPickTO);
                        return k;
                    });
        }
        return strPik;
    }


}
