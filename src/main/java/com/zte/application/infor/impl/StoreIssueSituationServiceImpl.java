package com.zte.application.infor.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.infor.StoreIssueSituationService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.StoreIssueParam;
import com.zte.domain.model.infor.StoreIssueSituationRepository;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.AesUtils;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

@Slf4j
@Service
public class StoreIssueSituationServiceImpl implements StoreIssueSituationService {

    @Autowired
    private StoreIssueSituationRepository situationRepository;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;

    @Value("${imes.url}")
    private String imesUrl;
    @Value("${imes.access.key}")
    private String accessKey;
    @Value("${imes.access.secret}")
    private String accessSecret;
    @Value("${imes.secret}")
    private String secret;

    public StoreIssueSituationServiceImpl() {
    }

    @Override
    public void updateProdRecDate(String externKey, String xEmpNo) {
        try{
            int page = INT_1;
            List<String> externList = situationRepository.getStoreIssueList(INT_1,INT_1000,externKey);
            while(Tools.isNotEmpty(externList)){
                List<List<String>> pExternList = CommonUtils.splitList(externList,INT_100);
                pExternList.forEach(t->{
                        List<StoreIssueParam> resultsList = getRemoteList(t, xEmpNo);
                        if(resultsList!=null && resultsList.size()>INT_0){
                            resultsList.forEach(j->situationRepository.updateProdRecDate(j));
                        }
                });
                page++;
                externList = situationRepository.getStoreIssueList((page-INT_1)*INT_1000,page*INT_1000,externKey);
            }
        }catch (Exception e){
            log.info("updateProdRecDate Exception:{}",e.getMessage());
        }
    }


    @Override
    public List<StoreIssueParam> getRemoteList(List<String> taskNoList, String xEmpNo){
        List<StoreIssueParam> resultsList = new CopyOnWriteArrayList<>();
        try {
            Map<String, String> headers = Tools.newHashMap();
            headers.put(Constant.X_TENANT_ID, NumConstant.STR_10001);
            headers.put(Constant.X_EMP_NO, xEmpNo);
            headers.put(Constant.X_AUTH_VALUE, INT_1+BLANK);
            headers.put(X_SOURCE_SYS, STR_R_WMS);
            headers.put(STR_APP_CODE, accessKey);
            headers.put(STR_SECRET_KEY, AesUtils.aesGcmEncrypt(accessSecret+SPLIT_22 + new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(new Date()), secret));
            Map<String, String> map = Tools.newHashMap();
            map.put(TASKNO, StringUtils.join(taskNoList,","));
            map.put(ROWS,INT_100+BLANK);
            map.put(PAGE,INT_1+BLANK);

            String res = HttpClientUtil.httpGet(imesUrl+ZTE_MES_MANUFACTURESHARE_CENTERFACTORY+SPECIFIED_PS_TASKLIST,map,headers);
            ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});
            if(SUSSESS.equals(result.getCode().getCode())&&Tools.isNotEmpty(result)&&Tools.isNotEmpty(result.getBo())&&Tools.isNotEmpty(result.getBo().get(ROWS))){
                List<StoreIssueParam> storeIssueParamList = JsonUtil.parseObject(JsonUtil.toJSONString(result.getBo().get(ROWS)), new TypeReference<List<StoreIssueParam>>(){});
                storeIssueParamList = storeIssueParamList.stream().map(t-> {
                    try {
                        if(Tools.isNotEmpty(t.getGetDate())){
                            return t.setUpdateToDate(new SimpleDateFormat(YYMMDDHHMMSS).parse(t.getGetDate()));
                        }
                    } catch (ParseException ex) {
                    }
                    return null;
                }).filter(h->Tools.isNotEmpty(h)).collect(Collectors.toList());

                Map<String,List<StoreIssueParam>> reMap = storeIssueParamList.stream().collect(Collectors.groupingBy(StoreIssueParam::getTaskNo));
                reMap.forEach((k,v)->{
                    if(Tools.isNotEmpty(v)){
                        resultsList.add(v.get(INT_0));
                    }
                });
            }
        } catch (Exception ex) {
            log.info("getRemoteList Exception:{}",ex.getMessage());
        }
        return resultsList;
    }
}
