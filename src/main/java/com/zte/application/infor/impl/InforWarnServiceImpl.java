package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.io.FileUtil;
import com.zte.application.infor.InforWarnService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforWarnRepository;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.StepTransferRepository;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.utils.*;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@Service
public class InforWarnServiceImpl implements InforWarnService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InforWarnServiceImpl.class);

    @Autowired
    private InforWarnRepository inforWarnRepository;

    @Autowired
    private StepTransferRepository stransferRepository;

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Autowired
    private EmailUtil emailUtil;

    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;


    /**
     * 先进先出邮件预警
     */
    @Override
    public void warnAllocateMonitorInfor(AllocateExceptionMonitorVo dto) {
        //获取接收人
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000040);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
        if (Tools.isEmpty(sysLookupValuesDTOList)) {
            return;
        }
        List<String> users = sysLookupValuesDTOList.stream().map(SysLookupValuesDTO::getLookupMeaning).collect(Collectors.toList());
        String receipts = StringUtils.join(
                users.parallelStream().map(t -> t + ZTE_EMAIL_SUFIIX).collect(Collectors.toList()), SPLIT_22);
        thplExecutor.execute(() -> {
            fifoEmail(dto, receipts);
            fefoEmail(dto, receipts);
        });
    }

    /**
     * 先进先出发邮件
     *
     * @param dto
     * @param receipts
     */
    private void fifoEmail(AllocateExceptionMonitorVo dto, String receipts) {
        try {
            LOGGER.error("########先进先出发邮件开始########");
            //查询违反先进先出策略条码信息
            int count = inforWarnRepository.getAllocateExceptionMonitorTotal(INT_1);
            //如果违反先进先出策略条码信息为空，则发一则空邮件给接收人
            if (count == INT_0) {
                emailUtil.sendMail(receipts, INFOR_ALLCATE_EXCEPTION_MONITOR, BLANK, INFOR_ALLCATE_EXCEPTION_CONTANT, BLANK);
            } else {
                //上传文档云发邮件
                this.getAllocateSendEmail(receipts, INT_1, dto);
            }
            LOGGER.error("########先进先出发邮件结束########");
        } catch (Exception e) {
            LOGGER.error("生成先进先出查询结果excel失败" + e.getMessage());
            emailUtil.sendMail(receipts, INFOR_ALLCATE_EXCEPTION_MONITOR, BLANK, GENERATE_QUERY_RESULT_EXCEL_FAILED, BLANK);
        }
    }

    /**
     * 到期先出发邮件
     *
     * @param dto
     * @param receipts
     */
    private void fefoEmail(AllocateExceptionMonitorVo dto, String receipts) {
        try {
            LOGGER.error("########到期先出发邮件开始########");
            //查询违反到期先出策略条码信息
            int count = inforWarnRepository.getAllocateExceptionMonitorTotal(INT_2);
            //如果违反到期先出策略条码信息为空，则发一则空邮件给接收人
            if (count == INT_0) {
                emailUtil.sendMail(receipts, INFOR_FEFO_EXCEPTION_MONITOR, BLANK, INFOR_ALLCATE_EXCEPTION_CONTANT, BLANK);
            } else {
                //上传文档云发邮件
                this.getAllocateSendEmail(receipts, INT_2, dto);
            }
            LOGGER.error("########到期先出发邮件结束########");
        } catch (Exception e) {
            LOGGER.error("生成到期先出查询结果excel失败" + e.getMessage());
            emailUtil.sendMail(receipts, INFOR_FEFO_EXCEPTION_MONITOR, BLANK, FEFO_QUERY_RESULT_EXCEL_FAILED, BLANK);
        }
    }

    public void getAllocateSendEmail(String receipts, int type, AllocateExceptionMonitorVo dto) throws Exception {
        //上传文档云发邮件给接收人
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            AllocateExceptionMonitorVo param = ((AllocateExceptionMonitorVo) params).setStartRow(statRow).setEndRow(endRow).setExceptionType(type);
            return new ArrayList<>(getStockAge(inforWarnRepository.getAllocateExceptionMonitor(param)));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(type == INT_1 ? INFOR_ALLCATE_EXCEPTION_MONITOR_NAME : INFOR_FEFO_EXCEPTION_MONITOR_NAME)
                .setFileName(type == INT_1 ? INFOR_ALLCATE_EXCEPTION_MONITOR : INFOR_FEFO_EXCEPTION_MONITOR).setSheetName(SHEET1)
                .setQueryParams(new AllocateExceptionMonitorVo()).setPojoClass(AllocateExceptionMonitorVo.class).setTotal((long) INT_20001)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.makeBigExcelFileTemp(excelParams);
        if (!Tools.isEmpty(excelParams.getPath())) {
            CloudDiskHelper cloudDiskHelper = (CloudDiskHelper) SpringContextUtil.getBean(CLOUD_DISK);
            String fileKey = cloudDiskHelper.fileUpload(excelParams.getPath(), dto.getEmpNo(), CloudDiskHelper.MAX_RETRY_TIMES);
            FileUtil.del(excelParams.getPath());
            String downLoadUrl = ExcelUtil.getFileDownloadUrl(fileKey, (String) null, dto.getEmpNo());
            emailUtil.sendMail(receipts, type == INT_1 ? INFOR_ALLCATE_EXCEPTION_MONITOR : INFOR_FEFO_EXCEPTION_MONITOR, "", INFOR_ALLCATE_EXCEPTION_MONITOR_DOWN + "\t\n" + downLoadUrl, "");
        }
    }

    public List<AllocateExceptionMonitorVo> getStockAge(List<AllocateExceptionMonitorVo> listException) {
        //分批查询条码库龄
        List<List<AllocateExceptionMonitorVo>> splitList = CommonUtils.splitList(listException, INT_500);
        for (List<AllocateExceptionMonitorVo> allocateExceptions : splitList) {
            List<String> barcodeList = allocateExceptions.stream().map(k -> k.getItemBarcode()).collect(Collectors.toList());
            List<AllocateExceptionMonitorVo> barcodeAgeList = stransferRepository.getStockAgeByBarcode(barcodeList);
            //更新条码库龄
            allocateExceptions.stream()
                    .map(sur -> barcodeAgeList.stream()
                            .filter(tar -> tar.getItemBarcode().equals(sur.getItemBarcode()))
                            .findFirst()
                            .map(tar -> {
                                sur.setStoreAge(Tools.transNullValue(tar.getStoreAge()));
                                return sur;
                            }).orElse(null))
                    .collect(Collectors.toList());
        }
        return listException;
    }
}
