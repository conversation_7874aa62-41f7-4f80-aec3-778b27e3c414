package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.infor.DigitQCDataBaseService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.DigitObjectValueDTO;
import com.zte.domain.model.infor.DigitQCDataBaseHeadDTO;
import com.zte.domain.model.infor.DigitQCDataBaseRepository;
import com.zte.interfaces.infor.dto.DigitQCDataBaseDTO;
import com.zte.interfaces.infor.dto.ItemSplitReqDTO;
import com.zte.interfaces.infor.dto.ItemSplitRespDTO;
import com.zte.interfaces.infor.vo.DigitObjectValueListVo;
import com.zte.interfaces.infor.vo.DigitQCDataBaseListVo;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * Description:
 *
 * <AUTHOR>
 * date: 2025/5/20 10:05
 */
@Slf4j
@Service
public class DigitQCDataBaseServiceImpl implements DigitQCDataBaseService {

    @Autowired
    DigitQCDataBaseRepository digitQCDataBaseRepository;

    @Value("${in.one.url}")
    private String inOneUrl;

    @Value("${in.one.wms.app.code}")
    private String appCode;

    @Override
    public void pullInforItemNoJob() throws Exception{
        List<String> whseidList = digitQCDataBaseRepository.selectWmwhseId();
        if (Tools.isEmpty(whseidList)) {
            return;
        }
        int count = digitQCDataBaseRepository.selectQCDataBase();
        if(count == NumConstant.INT_0 ) {
            getAllItemInfo(whseidList);
        }else {
            LocalDate today = LocalDate.now();
            LocalDate previousDay = today.minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMATER_YYYYMMDD);
            String date = previousDay.format(formatter);
            List<String> items = digitQCDataBaseRepository.queryAddItemInfo(whseidList, date);
            if (Tools.isEmpty(items)) {
                return;
            }
            CommonUtils.splitList(items, NumConstant.INT_250)
                    .forEach(i -> {
                        try {
                            updateItemNoInfo(i);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
        }
    }

    /**
     * 更新增量信息
     * @param items
     * @throws Exception
     */
    private void updateItemNoInfo(List<String> items) throws Exception{
        List<ItemSplitRespDTO> addItemSplitInfos = new ArrayList<>();
        for (String itemNo : items) {
            List<ItemSplitRespDTO> itemSplitInfos = getItemSplitInfo(itemNo);
            List<ItemSplitRespDTO> itemMasterInfos = getItemMasterInfo(itemNo);
            String master = null;
            if (Tools.isNotEmpty(itemMasterInfos)) {
                master = itemMasterInfos.get(NumConstant.INT_0).getMaster();
            }
            List<ItemSplitRespDTO> uniqueItems = itemSplitInfos.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    ItemSplitRespDTO::getSupplierNo,
                                    item -> item,
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            for (ItemSplitRespDTO itemSplitInfo : uniqueItems) {
                itemSplitInfo.setMaster(master);
                int isExist = digitQCDataBaseRepository.queryItemNoIsExist(itemSplitInfo);
                if (isExist == NumConstant.INT_0) {
                    addItemSplitInfos.add(itemSplitInfo);
                }else {
                    digitQCDataBaseRepository.updateItemNoInfo(itemSplitInfo);
                }
            }
        }
        if (Tools.isNotEmpty(addItemSplitInfos)) {
            CommonUtils.splitList(addItemSplitInfos, NumConstant.INT_500)
                    .forEach(i -> digitQCDataBaseRepository.insertItemInfos(i));
        }
    }

    /**
     * 第一次获取全量物料代码
     */
    private void getAllItemInfo(List<String> whseidList) {
        int totalItemCount = digitQCDataBaseRepository.queryItemNum(whseidList);
        int pageSize = NumConstant.INT_250;
        int totalPages = (int) Math.ceil((double) totalItemCount / pageSize);
        // 分页查询并处理数据
        for (int page = NumConstant.INT_0; page < totalPages; page++) {
            int offset = page * pageSize;
            List<String> items = digitQCDataBaseRepository.queryItemsByPage(whseidList, offset, pageSize);
            try {
                updateItemNoInfo(items);
            }catch (Exception ex) {
                log.error("获取物料快查失败, page: {}, offset: {}, pageSize: {}", page, offset, pageSize, ex);
            }
        }
    }


    /**
     * 调用iSRM物料快查接口
     *
     * @param itemNo
     * @return
     */
    private List<ItemSplitRespDTO> getItemSplitInfo(String itemNo) throws Exception{
        ItemSplitReqDTO itemSplitReqDTO = new ItemSplitReqDTO();
        itemSplitReqDTO.setItemNo(itemNo);
        itemSplitReqDTO.setModules(ISRM_MODULES);
        itemSplitReqDTO.setPageNo(NumConstant.INT_1);
        itemSplitReqDTO.setPageSize(NumConstant.INT_500);
        Map<String, String> mapHeader = new HashMap<>(NumConstant.INT_16);
        mapHeader.put(INONE_APPCODE, appCode);
        mapHeader.put(X_LANG_ID, X_LANG_ID_ZH);
        mapHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        String url = inOneUrl + EXTERNAL_ITEM_SPLIT;
        String result = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(itemSplitReqDTO), mapHeader);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
        if (!ORG_ID_0000.equals(retCode)) {
            return new ArrayList<>();
        }
        String dataArray = json.get(JSON_BO).get(LIST).toString();
        return JacksonJsonConverUtil.jsonToListBean(dataArray,
                new TypeReference<ArrayList<ItemSplitRespDTO>>() {
                });
    }

    /**
     * 调用iSRM物料快查接口
     *
     * @param itemNo
     * @return
     */
    public List<ItemSplitRespDTO> getItemMasterInfo(String itemNo) throws Exception{
        ItemSplitReqDTO itemSplitReqDTO = new ItemSplitReqDTO();
        itemSplitReqDTO.setItemNo(itemNo);
        itemSplitReqDTO.setModules(ISRM_MODULES);
        itemSplitReqDTO.setPageNo(NumConstant.INT_1);
        itemSplitReqDTO.setPageSize(NumConstant.INT_500);
        Map<String, String> mapHeader = new HashMap<>(NumConstant.INT_16);
        mapHeader.put(INONE_APPCODE, appCode);
        mapHeader.put(X_LANG_ID, X_LANG_ID_ZH);
        mapHeader.put(CONTENT_TYPE, APPLICATION_JSON);
        String url = inOneUrl + EXTERNAL_ITEM_INFO;
        String result = HttpClientUtil.httpPostWithJSON(url, JSONObject.toJSONString(itemSplitReqDTO), mapHeader);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result);
        String retCode = json.get(JSON_CODE).get(JSON_CODE).asText();
        if (!ORG_ID_0000.equals(retCode)) {
            return new ArrayList<>();
        }
        String dataArray = json.get(JSON_BO).get(LIST).toString();
        return JacksonJsonConverUtil.jsonToListBean(dataArray,
                new TypeReference<ArrayList<ItemSplitRespDTO>>() {
                });
    }

    @Override
    public DigitQCDataBaseListVo getQCDataBaseList(DigitQCDataBaseDTO dto) {
        DigitQCDataBaseListVo listVo = new DigitQCDataBaseListVo();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(digitQCDataBaseRepository.getDigitQCDataBaseTotal(dto));
        List<DigitQCDataBaseHeadDTO> list = digitQCDataBaseRepository.getDigitQCDataBaseVo(dto);
        listVo.setDigitQCDataBaseHeadDTOS(list);
        return listVo;
    }

    @Override
    public void exportQCDataBaseList(DigitQCDataBaseDTO dto, String xEmpNo) {
        int count = digitQCDataBaseRepository.getDigitQCDataBaseTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            DigitQCDataBaseDTO param = ((DigitQCDataBaseDTO) params).setStartRow(statRow).setEndRow(endRow);
            List<DigitQCDataBaseHeadDTO> list = digitQCDataBaseRepository.getDigitQCDataBaseVo(param);
            return new ArrayList<>(list);
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.DATABASE_NAME).setFileName(Constant.DATABASE_NAME).setSheetName(Constant.SHEET1)
                .setQueryParams(dto).setPojoClass(DigitQCDataBaseHeadDTO.class).setTotal((long) count)
                .setReceipt(xEmpNo).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    @Override
    public void loseQCDataBase(DigitQCDataBaseDTO dto) {
        List<String> serialKeys = dto.getSerialKeys();
        if (Tools.isEmpty(serialKeys)) {
            return;
        }
        digitQCDataBaseRepository.digitQCDataBaseInfoLose(serialKeys, dto.getLastUpdatedBy());
    }

    @Override
    public void effectQCDataBase(DigitQCDataBaseDTO dto) {
        List<String> serialKeys = dto.getSerialKeys();
        if (Tools.isEmpty(serialKeys)) {
            return;
        }
        digitQCDataBaseRepository.digitQCDataBaseInfoEffect(serialKeys, dto.getLastUpdatedBy());
    }

    @Override
    public void updateQCDataBase(DigitQCDataBaseDTO dto) {
        digitQCDataBaseRepository.updateQCDataBase(dto);
    }

    @Override
    public void importQCDataBase(List<DigitQCDataBaseDTO> list) throws Exception{
        if (Tools.isEmpty(list)) {
            return;
        }
        for (DigitQCDataBaseDTO digitQCDataBaseDTO : list) {
            int isExist = digitQCDataBaseRepository.queryIsExist(digitQCDataBaseDTO);
            if (isExist == 0) {
                saveDataBaseInfo(digitQCDataBaseDTO);
            }else {
                digitQCDataBaseRepository.updateDataBase(digitQCDataBaseDTO);
            }
        }
    }

    private void saveDataBaseInfo(DigitQCDataBaseDTO digitQCDataBaseDTO) {
        try {
            List<ItemSplitRespDTO> itemSplitInfo = getItemSplitInfo(digitQCDataBaseDTO.getItemNo());
            List<ItemSplitRespDTO> itemMasterInfos = getItemMasterInfo(digitQCDataBaseDTO.getItemNo());
            String master = null;
            if (Tools.isNotEmpty(itemMasterInfos)) {
                master = itemMasterInfos.get(NumConstant.INT_0).getMaster();
            }
            for (ItemSplitRespDTO itemSplitRespDTO : itemSplitInfo) {
                if (digitQCDataBaseDTO.getSupplierNo().equals(itemSplitRespDTO.getSupplierNo())) {
                    digitQCDataBaseDTO.setSupplierName(itemSplitRespDTO.getSupplierName());
                    digitQCDataBaseDTO.setPackType(itemSplitRespDTO.getPackTypeName());
                    digitQCDataBaseDTO.setEsdProtectionLevel(itemSplitRespDTO.getAntiElectricLevel());
                    digitQCDataBaseDTO.setMaterialCategory(master);
                    digitQCDataBaseRepository.insertDataBase(digitQCDataBaseDTO);
                    break;
                }
            }
        }catch (Exception ex) {
            log.error("物料{}查询失败", digitQCDataBaseDTO.getItemNo());
        }
    }

    @Override
    public void importObjectInfo(List<DigitQCDataBaseDTO> list) {
        if (Tools.isEmpty(list)) {
            return;
        }
        List<DigitQCDataBaseDTO> digitQCDataBaseDTOList = new ArrayList<>();
        for (DigitQCDataBaseDTO digitQCDataBaseDTO : list) {
            int isExist = digitQCDataBaseRepository.queryObjectExist(digitQCDataBaseDTO);
            if (isExist == 0) {
                digitQCDataBaseDTOList.add(digitQCDataBaseDTO);
            }else {
                digitQCDataBaseRepository.updateObjectInfo(digitQCDataBaseDTO);
            }
        }
        if (Tools.isNotEmpty(digitQCDataBaseDTOList)) {
            digitQCDataBaseRepository.insertObjectInfo(digitQCDataBaseDTOList);
        }
    }

    @Override
    public DigitObjectValueListVo getObjectInfo(DigitQCDataBaseDTO dto) {
        DigitObjectValueListVo listVo = new DigitObjectValueListVo();
        dto.setStartRow((dto.getPageIndex() - Constant.INT_1) * dto.getPageSize() + Constant.INT_1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(digitQCDataBaseRepository.getObjectValueTotal(dto));
        List<DigitObjectValueDTO> list = digitQCDataBaseRepository.getObjectValueVo(dto);
        listVo.setDigitObjectValueDTOS(list);
        return listVo;
    }

    @Override
    public void deleteObjectInfo(DigitQCDataBaseDTO dto) {
        if (Tools.isEmpty(dto.getSerialKeys())) {
            return;
        }
        digitQCDataBaseRepository.deleteObjectInfo(dto.getSerialKeys(), dto.getLastUpdatedBy());
    }

    @Override
    public void exportObjectInfo(DigitQCDataBaseDTO dto, String xEmpNo) {
        int count = digitQCDataBaseRepository.getObjectValueTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - Constant.INT_1) * Constant.INT_5000 + Constant.INT_1;
            int endRow = page * Constant.INT_5000;
            DigitQCDataBaseDTO param = ((DigitQCDataBaseDTO) params).setStartRow(statRow).setEndRow(endRow);
            List<DigitObjectValueDTO> list = digitQCDataBaseRepository.getObjectValueVo(param);
            return new ArrayList<>(list);
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(Constant.OBJECT_VALUE_NAME).setFileName(Constant.OBJECT_VALUE_NAME).setSheetName(Constant.SHEET1)
                .setQueryParams(dto).setPojoClass(DigitObjectValueDTO.class).setTotal((long) count)
                .setReceipt(xEmpNo).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    @Override
    public List<DigitQCDataBaseHeadDTO> queryDataBaseInfoList(DigitQCDataBaseDTO dto) {
        BusiAssertException.isEmpty(dto.getItemNo(), MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(dto.getSupplierNo(), MessageId.NO_PARAMS);
        return digitQCDataBaseRepository.queryDataBaseInfoList(dto);
    }
}
