package com.zte.application.infor.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.infor.SkuService;
import com.zte.domain.model.infor.SkuRepository;
import com.zte.interfaces.infor.dto.SkuDTO;
import com.zte.interfaces.infor.dto.SkuQueryDTO;
import com.zte.interfaces.infor.dto.SkuReelIdInputDTO;
import com.zte.interfaces.infor.dto.SkuReelIdOutputDTO;

@Service
public class SkuServiceImpl implements SkuService {

	@Autowired
	private SkuRepository skuRepository;
	
	@Override
	public List<SkuDTO> selectSkuById(SkuQueryDTO record) {
		// TODO Auto-generated method stub
		return skuRepository.selectSkuById(record);
	}
	
	@Override
	public List<SkuReelIdOutputDTO> selectReelId(SkuReelIdInputDTO record){
		return skuRepository.selectReelId(record);
	}

}
