/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-08-30
 * 修改历史 :
 *   1. [2019-08-30] 创建文件 by 6396000647
 **/
package com.zte.application.infor.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.zte.domain.model.infor.DelayBillRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.vo.ItemUUIDInforVo;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.application.infor.EdiAsnqcsService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.IscpRemoteServiceDataUtil;
import com.zte.domain.model.infor.EdiAsnqcS;
import com.zte.domain.model.infor.EdiAsnqcSRepository;
import com.zte.domain.model.infor.IscpEsbLog;
import com.zte.domain.model.infor.IscpEsbLogRepository;
import com.zte.domain.model.infor.LocationModel;
import com.zte.domain.model.infor.VmiQtyReelIdMode;
import com.zte.interfaces.infor.dto.AsnQcCheckBoxDTO;
import com.zte.interfaces.infor.dto.AsnQcCheckDTO;
import com.zte.interfaces.infor.dto.AsnQcCheckDetailDTO;
import com.zte.interfaces.infor.dto.AsnQcResultDTO;
import com.zte.interfaces.infor.dto.AsnqcTransDTO;
import com.zte.interfaces.infor.dto.IscpResultDTO;
import com.zte.interfaces.infor.dto.QaExInspectionDetail;
import com.zte.interfaces.infor.dto.QaExInspectionHead;
import com.zte.interfaces.infor.dto.QaExInspectionPack;
import com.zte.interfaces.infor.dto.VmiStoreDTO;
import com.zte.interfaces.infor.dto.VmiStoreParamDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.CommonUtils;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.Tools;

import static com.zte.common.utils.Constant.NJLTC;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
@Slf4j
public class EdiAsnqcsServiceImpl implements EdiAsnqcsService {

	private static final String LTC_LOCATIONCATEGORY="NJLTC";
	/**
	 * 服务调用成功返回值
	 * */
	private String serviceSuccessResult="0000";

	private String iscpServiceSuccessResult="S";

	@Autowired
	private EdiAsnqcSRepository ediAsnqcSRepository;

	@Autowired
	private IscpEsbLogRepository iscpEsbLogRepository;
	@Autowired
	private DelayBillRepository delayBillRepository;
	@Autowired
	private StepIscpRepository stepIscpRepository;



	/**
	 * get all record
	 *
	 * @return List<EdiAsnqcS>
	 **/
	@Override
	public java.util.List<EdiAsnqcS> selectEdiAsnqcSAll() {
		return ediAsnqcSRepository.selectEdiAsnqcSAll();
	}
	@Override
	public ServiceData<?> selectAsnQcData(int rn) throws Exception{
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		AsnqcTransDTO asnDto=new AsnqcTransDTO();
		asnDto.setRn(BigDecimal.valueOf(rn));
		List<AsnqcTransDTO> transDTOs=ediAsnqcSRepository.selectAsnQcData(asnDto);
		for (AsnqcTransDTO asnqcTransDTO : transDTOs) {
			selectQualityCheck(asnqcTransDTO.getReceiptKey(),asnqcTransDTO.getWhseId());
		}
		ret.setBo("success");
		return ret;
	}


	/**
	 * get all selectQualityCheck
	 *
	 * @return AsnQcDTO
	 * @throws Exception
	 **/
	@Override
	public ServiceData<?> selectQualityCheck(String receiptKey,String wmwhseId) throws Exception{
		EdiAsnqcS eAsnqcS=new EdiAsnqcS();
		if(!StringUtils.isEmpty(receiptKey)){
			eAsnqcS.setReceiptkey(receiptKey);
		}
		if(!StringUtils.isEmpty(wmwhseId)){
			eAsnqcS.setWhseid(wmwhseId.toUpperCase());
		}
		List<AsnQcCheckDTO> list= ediAsnqcSRepository.selectQualityCheck(eAsnqcS);
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		List<QaExInspectionHead> dto=new ArrayList<>();
		for (AsnQcCheckDTO asnQcCheckDTO : list) {
			log.info("selectQualityCheck receiptKey {}, wmwhseId:{}",asnQcCheckDTO.getReceiptKey(),asnQcCheckDTO.getStock());
			QaExInspectionHead qaExInspectionHead=new QaExInspectionHead();
			qaExInspectionHead.setReceiveNo(asnQcCheckDTO.getReceiveNo());

			qaExInspectionHead.setDataType(Constant.INFOR);
			qaExInspectionHead.setBillType("03");
			qaExInspectionHead.setPurchaseOrg(asnQcCheckDTO.getPurchaseOrg());
			qaExInspectionHead.setReceiptOrg(asnQcCheckDTO.getReceiptOrg());
			qaExInspectionHead.setSupplierName(asnQcCheckDTO.getSupplierName());
			qaExInspectionHead.setSupplierNo(asnQcCheckDTO.getSupplierNo());
			qaExInspectionHead.setReceiptKey(asnQcCheckDTO.getReceiptKey());
			qaExInspectionHead.setReceiveTime(asnQcCheckDTO.getReceiveTime());
			qaExInspectionHead.setDeliveryType(Constant.SC);

			qaExInspectionHead.setSendingPerson(asnQcCheckDTO.getSendingPerson());

			qaExInspectionHead.setShipmentMode(asnQcCheckDTO.getShipmentMode());

			qaExInspectionHead.setStockAddress(asnQcCheckDTO.getStockAddress());

			qaExInspectionHead.setDeliveryAddress(asnQcCheckDTO.getDeliveryAddress());

			qaExInspectionHead.setStock(asnQcCheckDTO.getStock());

			qaExInspectionHead.setDeliveryCountryNo(asnQcCheckDTO.getDeliveryCountryNo());

			qaExInspectionHead.setDeliveryCountryName(asnQcCheckDTO.getDeliveryCountry());

			qaExInspectionHead.setReceiptId(asnQcCheckDTO.getReceiptId());

			//循环
			EdiAsnqcS record=new EdiAsnqcS();
			record.setReceiptkey(asnQcCheckDTO.getReceiptKey());
			record.setWhseid(asnQcCheckDTO.getStock());
			List<QaExInspectionDetail> details =selectQualityCheckDetail(record, asnQcCheckDTO.getSendingPerson(), asnQcCheckDTO.getReceiveTime(),asnQcCheckDTO.getStock());

			qaExInspectionHead.setDetails(details);
			dto.add(qaExInspectionHead);
		}
		if(Tools.isNotEmpty(dto)){
			ServiceData<List<IscpResultDTO>> ret1 = IscpRemoteServiceDataUtil.inspectionReq(dto);
			if(null!=ret1){
				ret.setBo(ret1.getBo());
				updateIscpResult(ret1);
			}
		}
		else{
			AsnQcResultDTO asnQcResultDTO=new AsnQcResultDTO();
			asnQcResultDTO.setProcessMessage(Constant.NO_DATA);
			asnQcResultDTO.setProcessStatus("0001");
			ret.setBo(asnQcResultDTO);
		}
		return ret;
	}

	/**
	 * 获取检验的代码
	 * <AUTHOR>
	 * @return List<AsnQcCheckDetailDTO>
	 *
	 * */
	@Override
	public List<QaExInspectionDetail> selectQualityCheckDetail(EdiAsnqcS record ,String sendingPerson,String receiveTime,String wmwhseId){
		List<AsnQcCheckDetailDTO> list=ediAsnqcSRepository.selectQualityCheckDetail(record);
		return dealList(list,sendingPerson,receiveTime,wmwhseId);
	}

	public List<QaExInspectionDetail> dealList(List<AsnQcCheckDetailDTO> list,String sendingPerson,String receiveTime,String wmwhseId){
		List<QaExInspectionDetail> details = Tools.newArrayList();
		for (AsnQcCheckDetailDTO asnQcCheckDetailDTO : list) {
			QaExInspectionDetail qaExInspectionDetail=new QaExInspectionDetail();
			String receiptKey=asnQcCheckDetailDTO.getReceiptKey();
			qaExInspectionDetail.setReceiptKey(receiptKey);
			qaExInspectionDetail.setItemNo(asnQcCheckDetailDTO.getItemNo());
			qaExInspectionDetail.setItemUuid(Integer.parseInt(asnQcCheckDetailDTO.getUuid()));
			qaExInspectionDetail.setItemName(asnQcCheckDetailDTO.getItemName());
			qaExInspectionDetail.setBrandName(asnQcCheckDetailDTO.getBrandName());
			qaExInspectionDetail.setBrandNo(asnQcCheckDetailDTO.getBrandNo());
			qaExInspectionDetail.setBrandStyle(asnQcCheckDetailDTO.getBrandStyle());
			qaExInspectionDetail.setPackageRequire(asnQcCheckDetailDTO.getPackageRequire());
			qaExInspectionDetail.setLineId(asnQcCheckDetailDTO.getLineId());
			qaExInspectionDetail.setDeliveryQty(asnQcCheckDetailDTO.getDeliveryQty());
			qaExInspectionDetail.setSendingPerson(sendingPerson);
			qaExInspectionDetail.setTransferBillNo(asnQcCheckDetailDTO.getTransferBillNo());
			qaExInspectionDetail.setTestMode(asnQcCheckDetailDTO.getTestMode());
			qaExInspectionDetail.setReceiveNo(asnQcCheckDetailDTO.getReceiveNo());
			this.setLocation(qaExInspectionDetail, wmwhseId, asnQcCheckDetailDTO.getStock());
			//qaExInspectionDetail.setLocation(asnQcCheckDetailDTO.getStock());
			qaExInspectionDetail.setEnvProperty(asnQcCheckDetailDTO.getEnvProperty());
			qaExInspectionDetail.setIsNeedSupReport(asnQcCheckDetailDTO.getIsNeedSupReport().intValue());
			qaExInspectionDetail.setReceiptLineNumber(asnQcCheckDetailDTO.getReceiptlinenumber());
			qaExInspectionDetail.setReceiveTime(receiveTime);
			String controleTypeFinal="1";
			if(controleTypeFinal.equals(asnQcCheckDetailDTO.getBarcodeControlType())){
				qaExInspectionDetail.setBarcodeControlType("02");
			}
			else{
				qaExInspectionDetail.setBarcodeControlType("01");
			}


			qaExInspectionDetail.setQtyExpected(asnQcCheckDetailDTO.getQtyExpected());

			EdiAsnqcS record1=new EdiAsnqcS();
			record1.setReceiptkey(asnQcCheckDetailDTO.getReceiptKey());
			record1.setReceiptlinenumber(asnQcCheckDetailDTO.getReceiptlinenumber());
			record1.setWhseid(wmwhseId);
			record1.setExternreceiptkey(asnQcCheckDetailDTO.getReceiveNo());
			List<AsnQcCheckBoxDTO> list2=ediAsnqcSRepository.selectQualityCheckDetailBox(record1);
			List<QaExInspectionPack> packs=new ArrayList();
			for (AsnQcCheckBoxDTO  m: list2) {
				QaExInspectionPack qaExInspectionPack=new QaExInspectionPack();
				qaExInspectionPack.setArrivalDate(m.getExpectArrivalDate());
				qaExInspectionPack.setBatchNo(m.getBatchNo());
				qaExInspectionPack.setCode22(m.getBarcode());
				qaExInspectionPack.setCountryOfOrigin(m.getCountryOfOrigin());
				qaExInspectionPack.setItemUuid(Integer.parseInt(m.getUuid()));
				qaExInspectionPack.setLineId(m.getLineId());
				qaExInspectionPack.setMinPackCode(m.getMinPackCode());
				qaExInspectionPack.setPackNo(m.getToid());
				qaExInspectionPack.setProductionDate(m.getProductionDate());
				qaExInspectionPack.setQty(m.getQty());
				qaExInspectionPack.setReceiveNo(m.getReceiveNo());
				qaExInspectionPack.setReceiptKey(receiptKey);
				qaExInspectionPack.setRecordNo("");//检验记录号
				qaExInspectionPack.setReel(m.getReelid());
				qaExInspectionPack.setSn("");
				qaExInspectionPack.setPackLocation(m.getPackLocation());
				packs.add(qaExInspectionPack);
			}
			qaExInspectionDetail.setPacks(packs);
			details.add(qaExInspectionDetail);
		}
		return details;
	}

	@Override
	public void updateIscpResult(ServiceData<List<IscpResultDTO>> ret1) {
		if (null==ret1) {
			return;
		}
		if(null==ret1.getCode()){
			return;
		}
		if(serviceSuccessResult.equals(ret1.getCode().getCode())){
			List<IscpResultDTO> iscpResultDTOs=ret1.getBo();
			if(Tools.isNotEmpty(iscpResultDTOs)){
				for (IscpResultDTO iscpResultDTO : iscpResultDTOs){
					addIscpStatus(iscpResultDTO);
				}
			}
		}
	}
	/**
	 * 更加iscp检验状态
	 * <AUTHOR>
	 * */
	private void addIscpStatus(IscpResultDTO iscpResultDTO){
		//插入到itcp系统
		String processStatus= iscpResultDTO.getProcessStatus();
		updateIscpLog(iscpResultDTO);
		//update 表
		if(iscpServiceSuccessResult.equals(iscpResultDTO.getProcessStatus()))
		{
			EdiAsnqcS record=new EdiAsnqcS();
			record.setReceiptkey(iscpResultDTO.getReceiptKey());
			record.setExternreceiptkey(iscpResultDTO.getSerialId());
			ediAsnqcSRepository.updateAsnFlag(record);
		}
	}
	/**
	 * 更新或者插入日志
	 * <AUTHOR>
	 * */
	public void updateIscpLog(IscpResultDTO iscpResultDTO) {
		int finalLen = 2000;
		IscpEsbLog iscpEsbLog = new IscpEsbLog();
		iscpEsbLog.setSourcesystemid(iscpResultDTO.getReceiptKey());
		iscpEsbLog.setExternreceiptkey(iscpResultDTO.getSerialId());
		iscpEsbLog.setMessagepriority(Short.valueOf("2"));
		List<IscpEsbLog> listmodel = iscpEsbLogRepository.selectIscpEsbLog(iscpEsbLog);
		if (null != listmodel && !listmodel.isEmpty()) {
			IscpEsbLog model = listmodel.get(0);
			model.setResultmessage(iscpResultDTO.getProcessStatus() + "-" + iscpResultDTO.getErrorMsgStr());
			String details = "";
			if (iscpResultDTO.getDetails() == null) {
				details = Constant.NONE;
			}
			if (JSONObject.toJSONString(iscpResultDTO.getDetails()).length() > finalLen) {
				details = JSONObject.toJSONString(iscpResultDTO.getDetails()).substring(0, 2000);
			}
			model.setResultmessage2(details);
			iscpEsbLogRepository.updateIscpEsbLog(model);
		} else {
			iscpEsbLog.setSourcesystemname(Constant.ISCP);
			iscpEsbLog.setUserid(Constant.SYSTEM);
			iscpEsbLog.setServicecode("0000");
			iscpEsbLog.setResultmessage(iscpResultDTO.getProcessStatus() + "-" + iscpResultDTO.getErrorMsgStr());
			String details = "";
			if (iscpResultDTO.getDetails() == null) {
				details = Constant.NONE;
			}
			if (JSONObject.toJSONString(iscpResultDTO.getDetails()).length() > finalLen) {
				details = JSONObject.toJSONString(iscpResultDTO.getDetails()).substring(0, 2000);
			}
			iscpEsbLog.setResultmessage2(details);
			iscpEsbLogRepository.insertIscpEsbLog(iscpEsbLog);
		}
	}
	/**
	 * update updateAsnFlag
	 *
	 * @return AsnQcDTO
	 **/
	@Override
	public void  updateAsnFlag(EdiAsnqcS record){
		ediAsnqcSRepository.updateAsnFlag(record);
	}

	/**
	 * update record
	 *
	 **/
	@Override
	public void  insertEdiAsnqcS(EdiAsnqcS record){
		ediAsnqcSRepository.insertEdiAsnqcS(record);
	}

	/**
	 * 获取仓库数据
	 **/
	@Override
	public List<VmiStoreDTO> getVmiStoreByWmwhse(VmiStoreParamDTO vmiStoreParamDTO){

		//vmi现存量集合
		List<VmiStoreDTO> vmiStorelist = ediAsnqcSRepository.findVmiStoreByWmwhse(vmiStoreParamDTO);
		if(CollectionUtils.isEmpty(vmiStorelist)){
			return vmiStorelist;
		}
		List<VmiStoreDTO> list = new ArrayList<>(10);
		//拆分集合
		List<List<VmiStoreDTO>> partition = Lists.partition(vmiStorelist, 1000);
		for (List<VmiStoreDTO> storeDTOList : partition) {
			storeDTOList=getVmiStoreDto(storeDTOList,vmiStoreParamDTO);
			list.addAll(storeDTOList);
		}
		return list;
	}
	/**
	 * 返回数据
	 * */
	public List<VmiStoreDTO> getVmiStoreDto(List<VmiStoreDTO> storeDTOList,VmiStoreParamDTO vmiStoreParamDTO){
		//组合查询条件
		List<String> serialKeyList = storeDTOList.stream().map(VmiStoreDTO::getSerialKey).collect(Collectors.toList());
		Map<String, Object> vmiQtyReelIdMap = new HashMap<>(16);
		vmiQtyReelIdMap.put("whseid",vmiStoreParamDTO.getWhseid());
		vmiQtyReelIdMap.put("serialKeyList",serialKeyList);
		//vmi 数量 reelID集合
		List<VmiQtyReelIdMode> qtyReelIdList = ediAsnqcSRepository.findQtyReelId(vmiQtyReelIdMap);
		if(!CollectionUtils.isEmpty(qtyReelIdList)){
			//循环组合数据
			for (VmiStoreDTO vmiStoreDTO : storeDTOList) {
				for (VmiQtyReelIdMode qtyReelIdMode : qtyReelIdList) {
					if(vmiStoreDTO.getSerialKey().equals(qtyReelIdMode.getSerialKey())){
						vmiStoreDTO.setQty(qtyReelIdMode.getQty());
						vmiStoreDTO.setReelId(qtyReelIdMode.getReelId());
					}
				}
			}
		}
		return storeDTOList;
	}
	@Override
	public ServiceData<?> selectAsnQcDataDelay() throws Exception {
		ServiceData ret = new ServiceData();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
		AsnqcTransDTO asnDto=new AsnqcTransDTO();
		List<AsnqcTransDTO> transDTOs=ediAsnqcSRepository.selectAsnQcDataDelay(asnDto);
		for (AsnqcTransDTO asnqcTransDTO : transDTOs) {
			selectQualityCheck(asnqcTransDTO.getReceiptKey(),asnqcTransDTO.getWhseId());
		}
		ret.setBo("success");
		return ret;
	}

	public void setLocation(QaExInspectionDetail qaExInspectionDetail,String whseid,String loc)  {
		//IQC质检传立体仓标记
		LocationModel locationModel=ediAsnqcSRepository.selectLocationDetail(whseid.toUpperCase(), loc);
		if(LTC_LOCATIONCATEGORY.equals(locationModel.getLocationcategory())) {
			qaExInspectionDetail.setLocation(LTC_LOCATIONCATEGORY);
		}else {
			qaExInspectionDetail.setLocation(loc);
		}
	}
	/**
	 * [超期复检单送检接口] <br> 
	 *  
	 * <AUTHOR>
	 * @param recheckNo
	 * @return <br>
	 */ 
	@Override
	public ServiceData<?> sendDelayRecheckToCheck(String recheckNo) {
		if(Tools.isEmpty(recheckNo)){
			return ServiceDataUtil.getValidationError(CommonUtils.getLmbMessage(MessageId.RECHECKNO_CAN_NOT_BE_EMPTY));
		}
		
		Integer recheckNoNum = delayBillRepository.getRecheckNoNum(recheckNo);
		if(Constant.INT_0 == recheckNoNum){
			return ServiceDataUtil.getValidationError(CommonUtils.getLmbMessage(MessageId.RECHECKNO_NOT_EXISTS));
		}
		
		return sendBillToCheck(recheckNo);
	}
	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param recheckNo
	 * @return <br>
	 */ 
	public ServiceData<?> sendBillToCheck(String recheckNo) {
		//1、组装参数
		QaExInspectionHead qaExInspectionHead = combineParam(recheckNo);
		String param = JSONObject.toJSONString(qaExInspectionHead);
		delayBillRepository.updateSendCheckParam(recheckNo,param);
		
		//2、送检
		List<QaExInspectionHead> dto = new ArrayList<>();
		dto.add(qaExInspectionHead);
		ServiceData<?> ret1 = null;
		try {
			log.info("sendBillToCheck dto:{}", JsonUtil.toJSONString(dto));
			ret1 = IscpRemoteServiceDataUtil.inspectionReq(dto);
		} catch (Exception e) {
			return ServiceDataUtil.getBusinessError(e.getMessage());
		}
		
		return ret1;
	}
	/**
	 * [方法描述] <br> 
	 *  
	 * <AUTHOR>
	 * @param recheckNo <br>
	 */ 
	public QaExInspectionHead combineParam(String recheckNo) {
		//1、获取单头信息
		QaExInspectionHead qaExInspectionHead = delayBillRepository.getQaExInspectionHead(recheckNo);
		List<ItemUUIDInforVo> codeList = stepIscpRepository.getitemUuidAndEnv(qaExInspectionHead.getItemBarcode());
		ItemUUIDInforVo uuidInforVo = codeList.get(0);
		qaExInspectionHead.setSupplierNo(uuidInforVo.getSupplierNo());
		//2、获取单明细信息
		List<QaExInspectionDetail> details = delayBillRepository.getQaExInspectionDetail(recheckNo,qaExInspectionHead.getStock());
		//2.1 获取库位类型字典
		Map<String, String> locationCategoryMap = getLocationCategoryMap(qaExInspectionHead, details);
		//3、获取装箱信息
		for(QaExInspectionDetail qaExInspectionDetail : details){
			//判断库存的库位类型是否是立体仓库,是则调用iSRM送检接口明细中的库位（location）传值NJLTC;
			if(!NJLTC.equals(qaExInspectionDetail.getLocation())&&NJLTC.equals(locationCategoryMap.get(qaExInspectionDetail.getLocation()))){
				qaExInspectionDetail.setLocation(NJLTC);
			}
			qaExInspectionDetail.setItemUuid(uuidInforVo.getItemUuid());
			qaExInspectionDetail.setEnvProperty(uuidInforVo.getEnvProperty());
			qaExInspectionDetail.setBrandNo(uuidInforVo.getBrandNo());
			qaExInspectionDetail.setBrandName(uuidInforVo.getBrandName());
			qaExInspectionDetail.setBrandStyle(uuidInforVo.getBrandStyle());
			qaExInspectionDetail.setBarcodeControlType(ediAsnqcSRepository.getBarcodeControlType(qaExInspectionDetail));
			qaExInspectionDetail.setItemBarcode(qaExInspectionHead.getItemBarcode());
			qaExInspectionDetail.setProductionDate(uuidInforVo.getProductionDate());
			List<QaExInspectionPack> packs = new ArrayList<QaExInspectionPack>();
			//2022-05-30  mxw说：有箱码则传箱码，没箱码则传REEL（不管有没有REEL）
			//有箱码
			if(Tools.isNotEmpty(qaExInspectionDetail.getPackNo())){
				packs = delayBillRepository.getQaExInspectionPackWithPackNo(qaExInspectionDetail);
			}
			//无箱码
			if(Tools.isEmpty(qaExInspectionDetail.getPackNo())) {
				qaExInspectionDetail.setPackNo(qaExInspectionHead.getItemBarcode());
				packs = iscpEsbLogRepository.getQaExInspectionPack(qaExInspectionDetail);
			}
			packs = packs.parallelStream().map(t->{
				t.setBatchNo(uuidInforVo.getBatchNo());
				t.setItemUuid(uuidInforVo.getItemUuid());
				return t;
			}).collect(Collectors.toList());
			qaExInspectionDetail.setPacks(packs);
		}
		
		qaExInspectionHead.setDetails(details);
		
		return qaExInspectionHead;
	}

	public Map<String, String> getLocationCategoryMap(QaExInspectionHead qaExInspectionHead, List<QaExInspectionDetail> details) {

		List<String> locationList = details.stream().map(QaExInspectionDetail::getLocation).distinct().collect(Collectors.toList());
		List<List<String>> locationGrouplist = CommonUtils.splitList(locationList, Constant.INT_500);
		List<QaExInspectionDetail> locationCategoryList = new ArrayList<>();
		for (List<String> locationGroup : locationGrouplist) {
			List<QaExInspectionDetail> qaExInspectionDetailList = iscpEsbLogRepository.getWmwhseXLocationMapList(locationGroup, qaExInspectionHead.getStock());
			if (Tools.isNotEmpty(qaExInspectionDetailList)) {
				locationCategoryList.addAll(qaExInspectionDetailList);
			}
		}
		if (Tools.isEmpty(locationCategoryList)) {
			return new HashMap<>();
		}
		return locationCategoryList.stream().collect(Collectors.toMap(QaExInspectionDetail::getLocation, QaExInspectionDetail::getLocationcategory));
	}
}

