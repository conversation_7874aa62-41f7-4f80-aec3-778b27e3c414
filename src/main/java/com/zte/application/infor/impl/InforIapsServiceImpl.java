package com.zte.application.infor.impl;

import com.zte.application.infor.InforIapsService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforIapsRepository;
import com.zte.domain.model.step.InforStepErpStock;
import com.zte.domain.model.step.InforStepErpStockRepository;
import com.zte.interfaces.infor.dto.IapsInforStock;
import com.zte.interfaces.infor.dto.IapsInforStockDto;
import com.zte.interfaces.infor.dto.IapsStockDto;
import com.zte.interfaces.infor.dto.XcItemInfoDTO;
import com.zte.interfaces.infor.vo.IapsInforStockVo;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InforIapsServiceImpl implements InforIapsService {

    @Autowired
    private InforIapsRepository inforIapsRepository;
    @Autowired
    private InforStepErpStockRepository inforStepErpStockRepository;
    @Autowired
    @Qualifier("thplExecutor")
    private Executor thplExecutor;

    @Override
    public void synXcItemInfo(List<XcItemInfoDTO> list) {

        // 校验计划的入参
        // 1.校验非空
        BusiAssertException.isEmpty(list, MessageId.INPUT_DATA_CAN_NOT_NULL);

        // 2.校验每批不能超过5000个
        BusiAssertException.isTrue(list.size() > INT_5000, MessageId.CAN_NOT_BE_GREATER_THAN_FIVE_THOUSAND);

        // 3.物料代码/境内后进先出/境外后进先出/是否有效/更新人字段不能为空
        BusiAssertException.isTrue(list.parallelStream().anyMatch(t -> Tools.isEmpty(t.getSku()) || Tools.isEmpty(t.getDomesticFirst()) ||
                Tools.isEmpty(t.getInternalFirst()) || Tools.isEmpty(t.getEnableFlag()) || Tools.isEmpty(t.getUpdateBy())), MessageId.SKU_DOMESTIC_INTERNAL_ENABLED_UPDATE_BY_EMPTY);

        // 4.校验是否有重复物料代码
        List<String> skuList = list.stream().map(XcItemInfoDTO::getSku).distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(list.size() != skuList.size(), MessageId.SKU_CAN_NOT_REPEAT);

        // 保存
        CommonUtils.splitList(list, NumConstant.INT_500).forEach(i ->
                thplExecutor.execute(()-> inforIapsRepository.saveXcItemInfo(i)));

    }

    @Override
    public ServiceData getInforStockInfo(IapsStockDto dto) throws Exception{
        //校验参数
        checkRequestParameters(dto);
        List<IapsInforStock> stockItemList=dto.getStockItemList();
        //获取STEP库位
        List<String> stepStockList=stockItemList.stream().map(IapsInforStock::getStockNo).distinct().collect(Collectors.toList());
        //根据STEP库位获取库位信息
        List<InforStepErpStock> stockList=inforStepErpStockRepository.getInforStepErpStock(stepStockList);
        //判断INFOR仓库是否为空
        BusiAssertException.isEmpty(stockList,MessageId.STOCKNO_CHECK);
        //按STEP库位汇总条码
        Map<String, List<String>> result = stockItemList.stream()
                .collect(Collectors.groupingBy(IapsInforStock::getStockNo,
                        Collectors.mapping(IapsInforStock::getItemBarcode, Collectors.toList())));
        //获取我司货主信息
        dto.setCodelKupVOList(inforIapsRepository.getCodelkup());
        //赋值STEP库位的List条码
        for (InforStepErpStock item:stockList) {
            item.setItemBarcodeList(result.get(item.getStepStock()));
        }
        //赋值入参
        dto.setInfoStockItemList(stockList);

        //计算起始行与结束行
        int strPageIndex = dto.getPageIndex();
        dto.setPageIndex((strPageIndex - INT_1) * dto.getPageSize()+INT_1);
        dto.setPageSize(strPageIndex*dto.getPageSize());

        int inforStockCount=inforIapsRepository.selectInforStockCount(dto);
        List<IapsInforStockDto> list=inforIapsRepository.selectInforStockPage(dto);

        IapsInforStockVo stockVo=new IapsInforStockVo();
        stockVo.setTotal(inforStockCount);
        stockVo.setInforStockList(list);
        return ServiceDataUtil.getSuccess(stockVo);
    }

    private void checkRequestParameters(IapsStockDto dto) throws Exception{
        int pageSize=dto.getPageSize();
        int pageIndex=dto.getPageIndex();
        BusiAssertException.isEmpty(pageSize,MessageId.NO_PARAMS);
        BusiAssertException.isEmpty(pageIndex,MessageId.NO_PARAMS);
        BusiAssertException.isTrue(pageSize<=INT_0,MessageId.NO_PARAMS);
        BusiAssertException.isTrue(pageIndex<=INT_0,MessageId.NO_PARAMS);
        //校验List是否为空
        List<IapsInforStock>  stockList=dto.getStockItemList();
        BusiAssertException.isTrue(CollectionUtils.isEmpty(stockList) || stockList.size()<=INT_0,MessageId.NO_PARAMS);
        //校验参数是否超500个
        BusiAssertException.isTrue(stockList.size()>NumConstant.INT_500,MessageId.STOCKNO_ITEMBARCODE_CHECK_COUNT);

        for (IapsInforStock item : stockList) {
            if(StringUtils.isEmpty(item.getItemBarcode()) || StringUtils.isEmpty(item.getStockNo())){
                BusiAssertException.result(MessageId.NO_PARAMS);
            }
        }
    }
}
