package com.zte.application.infor;


import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.AlgorithmExecuteLogListVO;
import com.zte.interfaces.infor.vo.AlgorithmResultDetailListVO;
import com.zte.interfaces.infor.vo.WarehouseAlgorithmListVO;
import com.zte.interfaces.infor.vo.AlgorithmResultHeadListVO;

import java.util.List;

/**
 * 10255258
 */
public interface WarehouseAlgorithmService {

    /**
     * 查询方案类型列表
     */
    List<SysLookupValuesDTO> getSchemeTypeList();

    /**
     * 查询整库策略列表
     */
    List<SysLookupValuesDTO> getWholeWarehouseStrategyList();

    /**
     * 查询算法方案
     */
    WarehouseAlgorithmListVO getWarehouseAlgorithmList(WarehouseAlgorithmDTO dto);

    /**
     * 保存算法方案
     */
    void saveWarehouseAlgorithm(WarehouseAlgorithmDTO dto);

    /**
     * 更新算法方案
     */
    void updateWarehouseAlgorithm(WarehouseAlgorithmDTO dto);

    /**
     * 导入算法方案
     */
    void importWarehouseAlgorithm(List<WarehouseAlgorithmDTO> list);

    /**
     * 导出算法方案
     */
    void exportWarehouseAlgorithm(WarehouseAlgorithmDTO dto);

    /**
     * 查询算法计算结果
     */
    AlgorithmResultHeadListVO getAlgorithmResultHeadList(AlgorithmResultDetailDTO dto);

    /**
     * 查询算法计算结果明细
     */
    AlgorithmResultDetailListVO getAlgorithmResultDetailList(AlgorithmResultDetailDTO dto);
    /**
     * 导出算法计算结果
     */
    void exportAlgorithmResult(AlgorithmResultDetailDTO dto);
    /**
     * 查询算法执行日志
     */
    AlgorithmExecuteLogListVO getAlgorithmExecuteLogList(AlgorithmExecuteLogDTO dto);

}
