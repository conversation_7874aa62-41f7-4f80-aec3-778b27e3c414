package com.zte.application.infor;

import com.zte.interfaces.infor.dto.WarehouseReplenishmentPathDTO;
import com.zte.interfaces.infor.vo.WarehouseRepPathWorkListVO;

/**
 * @Description 查询补货路径优化Service
 * <AUTHOR>
 * @Date 17:24 2025/4/21
 * @Version 1.0
 **/
public interface WarehouseReplenishmentPathService {

    /**
     * 查询补货路径优化
     * @param dto
     * @return
     */
    WarehouseRepPathWorkListVO getReplenishmentPathList(WarehouseReplenishmentPathDTO dto);


    /**
     * 导出补货路径优化
     * @param dto
     */
    void exportReplenishmentPath(WarehouseReplenishmentPathDTO dto);
}
