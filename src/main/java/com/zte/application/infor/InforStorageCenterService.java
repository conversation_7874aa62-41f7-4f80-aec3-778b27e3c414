/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.application.infor;

import com.zte.interfaces.infor.dto.PoInBoundDTO;
import com.zte.interfaces.infor.dto.PoInBoundInfoDTO;
import com.zte.interfaces.infor.dto.SoOutBoundDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/** 
 * [描述] <br> 
 *  
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年11月18日 <br>
 * @see com.zte.application.infor <br>
 */

public interface InforStorageCenterService {

	/**
	 * 定时捞取INFOR已经出库完成的HZ单写到日志表
	 * @param externalOrderkey2
	 * @param orderKey
	 */
	void getSoByBillTypeHz(String externalOrderkey2, String orderKey);

	/**
	 * 定时捞取终端备件的数据推送给仓储中心
	 * @param xEmpNo
	 * @param xAuthValue
	 * @param externalOrderkey2
	 * @param orderKey
	 */
	void pushHzBillToStorageCenter(String xEmpNo, String xAuthValue, String externalOrderkey2, String orderKey);
	
	
	/**
	 * 查询出库信息
	 * @param boundDTO
	 * @return <br>
	 */
	ServiceData<?> selectSoOutBoundInfo(SoOutBoundDTO boundDTO);

	/**
	 * 查询出库信息
	 * @param boundDTO
	 * @return <br>
	 */
	ServiceData<?> selectEdiSosInfo(SoOutBoundDTO boundDTO);

	/**
	 * 定时捞取INFOR已经出库完成的逆向报废单据并写到日志表
	 * @param externalOrderkey2
	 * @param orderkey
	 */
	void getSoByReverseScrap(String externalOrderkey2,String orderkey);

	/**
	 * 定时捞取逆向报废的数据推送给仓储中心
	 * @param xEmpNo
	 * @param externalOrderkey2
	 * @param orderKey
	 */
	void pushReverseScrapBillToStorageCenter(String xEmpNo, String externalOrderkey2, String orderKey);

	/**
	 * 查询入库信息
	 * @param inboundDTO
	 * @return <br>
	 */
	List<PoInBoundInfoDTO> selectEdiPosInfo(PoInBoundDTO inboundDTO);
}
