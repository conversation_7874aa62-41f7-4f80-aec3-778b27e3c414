package com.zte.application.infor;

import com.zte.domain.model.infor.MslServiceLifeInfo;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.MslServiceLifeInfoVo;

import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2025/03/28
 **/
public interface MslServiceLifeService {
    void insertMslServiceLifeJob();

    void getStockFlowByRidJob();

    void mslServiceLifeRedDotJob();

    MslServiceLifeInfoVo queryMslServiceLife(MslServiceLifeInfo dto);
    List<MslServiceLifeInfo> mslServiceLifeQuery(MslServiceLifeInfo dto);
    List<SysLookupValuesDTO> selectWhseId();
    void exportExcel(MslServiceLifeInfo dto);
}
