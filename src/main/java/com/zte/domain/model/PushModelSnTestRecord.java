package com.zte.domain.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**
 * 标模任务条码推送测试记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-15 08:53:49
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushModelSnTestRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ExcelProperty(value = "requestId")
    private String requestId;
	@ExcelProperty(value = "stationId")
	private String stationId;
	/**
	 * 设备类型
	 */
	@ExcelProperty(value = "设备类型")
    private String type;
	/**
	 * 生产指令
	 */
	@ExcelProperty(value = "生产指令")
    private String directiveNumber;
	/**
	 * 中兴任务号
	 */
	@ExcelProperty(value = "中兴任务号")
    private String workorderId;
	/**
	 * 整机条码
	 */
	@ExcelProperty(value = "整机条码")
    private String sn;
	@ExcelProperty(value = "设备条码")
	private String nodeSn;
	/**
	 * 制造品牌名
	 */
	@ExcelProperty(value = "制造品牌名")
    private String brand;
	/**
	 * 主板条码，存在多个时，使用,分隔
	 */
	@ExcelProperty(value = "主板条码，存在多个时，使用,分隔")
    private String boardSn;
	/**
	 * 任务附加属性表-三段机型
	 */
	@ExcelProperty(value = "任务附加属性表-三段机型")
    private String model;
	/**
	 * 站位名称
	 */
	@ExcelProperty(value = "站位名称")
    private String stationName;
	/**
	 * 检测的开始时间
	 */
	@ExcelProperty(value = "检测的开始时间")
    private String startedTime;
	/**
	 * 检测的结束时间
	 */
	@ExcelProperty(value = "检测的结束时间")
    private String finishedTime;
	/**
	 * 检测结果,取值范围：Pass/Fail
	 */
	@ExcelProperty(value = "检测结果,取值范围：Pass/Fail")
    private String result;
	/**
	 * 工序站位检查结果失败时的原因说明
	 */
	@ExcelProperty(value = "工序站位检查结果失败时的原因说明")
    private String message;
	/**
	 * oss测试文件
	 */
	@ExcelProperty(value = "oss测试文件")
    private String ossFileKey;
	@ExcelProperty(value = "oss测试文件地址")
	private String ossFileUrl;
	/**
	 * 工厂名称
	 */
	@ExcelProperty(value = "工厂名称")
    private String manufacturerName;
	@ExcelProperty(value = "维修类型")
	private String actionCode;
	@ExcelProperty(value = "维修详情")
	private String actionMsg;
	@ExcelProperty(value = "维修时间")
	private String finishReworkTime;
	@ExcelProperty(value = "送修时间")
	private String reworkTime;
	/**
	 * 
	 */
	@ExcelProperty(value = "enabledFlag")
    private String enabledFlag;
	/**
	 * 
	 */
	@ExcelProperty(value = "createDate")
    private Date createDate;
	/**
	 * 
	 */
	@ExcelProperty(value = "lastUpdatedDate")
    private Date lastUpdatedDate;
	/**
	 * 
	 */
	@ExcelProperty(value = "createBy")
    private String createBy;
	/**
	 * 
	 */
	@ExcelProperty(value = "lastUpdatedBy")
    private String lastUpdatedBy;
	@ApiModelProperty(value = "状态 0 待推送 1 整机测试数据推送成功  2 整机测试数据推送失败")
	private String uploadStatus;
	@ApiModelProperty(value = "整机测试数据推送错误信息")
	private String uploadMsg;
	@ApiModelProperty(value = "状态 0 待推送 1 站位分析结果推送成功  2 站位分析结果推送失败")
	private String stationUploadStatus;
	@ApiModelProperty(value = "站位分析结果推送结果")
	private String stationUploadMsg;
	@ApiModelProperty(value = "状态 0 待推送 1 oss日志文件推送成功  2 oss日志文件推送失败")
	private String fileUploadStatus;
	@ApiModelProperty(value = "oss日志文件推送结果")
	private String fileUploadMsg;
	@ApiModelProperty(value = "工厂id")
	private Integer factoryId;

}
