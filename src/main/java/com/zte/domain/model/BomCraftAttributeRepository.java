package com.zte.domain.model;

import com.zte.interfaces.dto.BomCraftAttributeDTO;
import com.zte.interfaces.dto.WorkOrderCraftAttributeDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BomCraftAttributeRepository {

    List<BomCraftAttribute> getBomCraftAttributePage(Page<BomCraftAttributeDTO> relDTOPage);

    List<BomCraftAttribute> getBomCraftAttributeList(BomCraftAttributeDTO dto);

    int updateBomCraftAttributeById(BomCraftAttributeDTO record);

    int insertBomCraftAttributeBatch(List<BomCraftAttributeDTO> list);

    int deleteCraftAttribute(BomCraftAttributeDTO record);

    void deleteCraftAttributeSPM();

    List<WorkOrderCraftAttributeDTO> getCraftAttributeForWorkOrder(@Param("list") List<WorkOrderCraftAttributeDTO> list);

    int getExportCount(BomCraftAttributeDTO dto);

    List<String> getItemNoList(String likeItemNo);

    List<BomCraftAttributeDTO> getBomInfoList (Page<BomCraftAttributeDTO> pageInfo);
}
