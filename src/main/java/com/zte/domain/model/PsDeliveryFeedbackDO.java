package com.zte.domain.model;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/9 下午2:47
 */
/* Started by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PsDeliveryFeedbackDO  implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 单据类型(1指令、2工单)
     */
    @NotNull(message = "单据类型不能为空")
    private String orderType;
    /**
     * 工单类型
     */
    @NotBlank(message = "工单类型不能为空")
    private String businessScene;

    /**
     * 单据编号(指令编号/工单编号)
     */
    @NotBlank(message = "单据编号不能为空")
    private String orderNo;

    /**
     * 业务分类
     */
    @NotBlank(message = "业务分类不能为空")
    private String category;

    /**
     * 反馈数量
     */
    @NotNull(message = "反馈数量不能为空")
    private BigDecimal quantity;

    /**
     * 预计完工日期
     */
    @NotNull(message = "预计完工日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateEstimatedCompletion;

    /**
     * 期望完工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateExpectedCompletion;

    /**
     * 厂商自供料预计齐套日期
     */
    @NotNull(message = "厂商自供料预计齐套日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateMaterialEstimatedPrepared;

    /**
     * 全部物料预计齐套日期
     */
    @NotNull(message = "全部物料预计齐套日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialEstimatedPrepared;

    /**
     * 预计投产日期
     */
    @NotNull(message = "预计投产日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateScheduledProduction;

    /**
     * 厂商自供料实际齐套日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeMaterialPrepared;

    /**
     * 全部物料实际齐套日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateAllMaterialPrepared;

    /**
     * 实际投产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timeProduction;

    /**
     * 首次领料日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstPickingDate;

    /**
     * 责任（1阿里责任，2厂商责任）
     */
    @Size(max = 1,message = "责任不能超过1字符")
    private String liability;

    /**
     * 一级原因分类
     */
    @Size(max = 64,message = "一级原因分类不能超过64字符")
    private String abnormalCategoryFirst;

    /**
     * 二级原因分类
     */
    @Size(max = 64,message = "二级原因分类不能超过64字符")
    private String abnormalCategorySecond;

    /**
     * 三级原因分类
     */
    @Size(max = 64,message = "三级原因分类不能超过64字符")
    private String abnormalCategoryThird;

    /**
     * 延期原因
     */
    @Size(max = 2000,message = "延期原因不能超过2000字符")
    private String remark;

    /**
     * 延期编号(不能重复)
     */
    @Size(max = 64,message = "延期编号不能超过64字符")
    private String abnormalNo;

    /**
     * 操作类型，1-人工，2-定时任务
     */
    private String operateType;

    /**
     * 推送状态
     */
    private String pushStatus;

    /**
     * 推送报错消息
     */
    private String pushErrorMsg;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    /**
     * 有效标识，Y-有效，N-无效
     */
    private String enabledFlag;

    /**
     * 定时任务调度表示，Y-已调度，N-未调度
     */
    private String scheduleFlag;

    /**
     * 备注
     */
    private String notes;

    /**
     * 原因分类（级联模式）
     */
    private List<String> abnormalCategory;
}

/* Ended by AICoder, pid:l446fxd852w995514e0109d3019dbe69c594923c */