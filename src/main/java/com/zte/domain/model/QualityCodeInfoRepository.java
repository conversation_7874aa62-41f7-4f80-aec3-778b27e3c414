package com.zte.domain.model;

import com.zte.interfaces.dto.QualityCodeInfoDTO;
import com.zte.interfaces.dto.SaveQualityCodeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/* Started by AICoder, pid:318e0h035dcc8ec14d0d0b7500fdb814b2c8c932 */

@Mapper
public interface QualityCodeInfoRepository {
    List<String> getSnExistQualityCode(@Param("qualityCodeInfoList") List<QualityCodeInfoDTO> qualityCodeInfoList);


    List<QualityCodeInfoDTO> getQualityCodeNotGenerated(SaveQualityCodeDTO qualityCodeDTO);
    List<QualityCodeInfoDTO> getQualityCodeBySnAndTaskNo(@Param("qualityCodeInfoList") List<QualityCodeInfoDTO> qualityCodeInfoList);

    void saveQualityCode(@Param("qualityCodeInfoList") List<QualityCodeInfoDTO> qualityCodeInfoList);

    Integer updateQualityCode(QualityCodeInfoDTO qualityCodeInfoDTO);

    /**
     * 增量查询表中条码数据
     * @param lastUpdatedDate
     * @param lastSn
     * @return
     */
    List<QualityCodeInfoDTO> getSnInc(@Param("lastUpdatedDate") String lastUpdatedDate, @Param("lastSn") String lastSn, @Param("limit") Integer limit);

    QualityCodeInfoDTO getQualityCode(QualityCodeInfoDTO qualityCodeInfoDTO);
}

/* Ended by AICoder, pid:318e0h035dcc8ec14d0d0b7500fdb814b2c8c932 */
