package com.zte.domain.model;

import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.TaskExtendedDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 标模绑定关系手工推送PDM记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-29 20:31:23
 */
public interface PsTaskExtendedRepository {
    List<TaskExtendedDTO> bulkQueriesByTaskNos(@Param("taskNos") List<String> taskNos);



    /**
     * 获取所有的 fixbomIdList
     * @return fixbomIdList
     */
    List<String> queryAllFixBomId();

    PsTaskExtendedDTO queryExtendedByTaskNo(@Param("taskNo") String taskNo);

    List<PsTaskExtendedDTO> queryByTaskNos(@Param("taskNoList") List<String> taskNoList);

    void updatePsExtendedBatch(List<PsTaskExtendedDTO> list);

    List<PsTaskExtendedDTO> queryTaskExtendedList(@Param("refreshFixBomBeforeHour") Integer refreshFixBomBeforeHour);

    /**
     * 根据客户编码筛选任务号
     * @param taskNoList
     * @param customerNoList
     * @return
     */
    List<PsTaskExtendedDTO> filterListByCustomerNo(@Param("taskNoList") List<String> taskNoList, @Param("customerNoList") List<String> customerNoList);

    void batchSave(List<PsTaskExtendedDTO> psTaskExtendedDTOList);

    List<PsTaskExtendedDTO> listByTaskNos(Collection<String> taskNos);

    String getReconfigurationOrSpecificFlag(@Param("taskNo") String taskNo, @Param("entityClass") String entityClass);

    List<PsTaskExtendedDTO> getSpecificTaskExtended(@Param("taskNo") String taskNo, @Param("taskNoList") List<String> taskNoList, @Param("customerNoList") List<String> customerNoList);


    List<PsTaskExtendedDTO> pageList(Page<PsTaskExtendedDTO> pageInfo);
    void updateCustomPartType(@Param("customPartType") String customPartType, @Param("taskNo") String taskNo);
    /* Started by AICoder, pid:z800dpdd96zb3e51433509d0c0d6fb1e42e4badd */
    /**
     * 获取阿里标模任务号
     * @param customerNo
     * @param taskNoList
     * @param entityClassList
     * @return
     */
    List<String> getTaskNoFromWmes(@Param("customerNo") String customerNo, @Param("taskNoList") List<String> taskNoList, @Param("entityClassList") List<String> entityClassList);

    /* Ended by AICoder, pid:z800dpdd96zb3e51433509d0c0d6fb1e42e4badd */

    /**
     * 筛选属于特定客户的任务号
     * @param taskNoList
     * @param customerNoList
     * @return
     */
    List<String> filterSpecificTaskNo(@Param("taskNoList") List<String> taskNoList, @Param("customerNoList") List<String> customerNoList);

    void batchUpdateByTaskNo(List<PsTaskExtendedDTO> psTaskExtendedDTOS);

    PsTaskExtendedDTO queryExtendedByTaskNoAndFixBomId(@Param("taskNo") String taskNo, @Param("fixBomId") String fixBomId);

    /**
     * 根据任务号或者headid查询任务扩展信息
     *
     * @param taskNo
     * @param fixBomHeadId
     * @return
     */
    PsTaskExtendedDTO queryExtendedByTaskNoAndFixBomHeadId(@Param("taskNo") String taskNo, @Param("fixBomHeadId") String fixBomHeadId);
}
