package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 个参生成表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-15 17:12:33
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpSpecialityParam implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 个参ID
	 */
    private String specialityParamId;
	/**
	 * 模板ID
	 */
    private String templateId;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 模板规则
	 */
	private String templateItem;
	/**
	 * 物料代码
	 */
    private String itemCode;
	/**
	 * 物料版本
	 */
    private String itemVersion;
	/**
	 * 单位
	 */
	private String itemUnit;
	/**
	 * 名称
	 */
	private String itemName;
	/**
	 * 任务号
	 */
    private String taskId;
	/**
	 * 资源归属
	 */
    private String usageScope;
	/**
	 * 运营商
	 */
    private String operator;
	/**
	 * 发往地区
	 */
    private String destinedArea;
	/**
	 * 订单号
	 */
    private String applyTask;
	/**
	 * 动态参数
	 */
    private String fixedStr;
	/**
	 * 进度
	 */
    private Integer progress;

    private String progressStr;

	private String status;
	/**
	 * 条码
	 */
	private Long barcode;
	/**
	 * 创建人
	 */
    private String createBy;
	/**
	 * 创建日期 默认为当前时间
	 */
    private Date createDate;
	/**
	 * 更新人
	 */
    private String lastUpdatedBy;
	/**
	 * 更新日期 默认为当前时间
	 */
    private Date lastUpdatedDate;
	/**
	 * 是否有效 选项：Y=正常 N=已删除
	 */
    private String enabledFlag;
	/**
	 * 是否回收 0未回收 1已回收
	 */
	private Integer isRecovery;
	/**
	 * 资源数量
	 */
	private Long itemNum;
	/**
	 * 产品大类
	 */
	private String productBigClass;
	/**
	 * 产品小类
	 */
	private String productSmallClass;

	private String syncMdsStatus;

	private long applyQty;

	private String productionUnit;

	private String wdcEigenValue;

	private Long wdcTotal;

	private boolean isFirstEigenValue;

	private String nasnParamName;
	private String scrambleCodeParamName;
	private String naccParamName;
	private String consent;
	private List<String> nasnList;

	private List<String> resourceNumList;

	private List<String> scramblingCodeList;
	// 分隔符
	private String separator;
	// 步距
	private String step;
	// GPON-SN产商编码
	private String mCode;
	private String telmexEigenValue;

	private Long telmexTotal;
	// stbid前缀-数字化平台获取或从配置获取
	private String stbidPrefix;
	private String stbidCheckAA;
}
