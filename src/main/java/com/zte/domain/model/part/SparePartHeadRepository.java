package com.zte.domain.model.part;


import com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationItemDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import java.util.List;

/**
 * 告警表数据库层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-31 11:23:24
 */
public interface SparePartHeadRepository {

    /**
     * 新增头信息
     *
     * @param headDTO 头信息
     */
    void insertHead(SparePartAllocationHeadDTO headDTO);

    /**
     * 查询单据头信息
     *
     * @param headDTO 头信息
     * @return 单据头信息
     */
    SparePartAllocationHeadDTO selectHeadByObject(SparePartAllocationHeadDTO headDTO);


    /**
     * 查询单据头加明细信息
     *
     * @param spare 查询参数
     * @return 单据信息
     */
    SparePartAllocationHeadDTO querySpareHeadAndDetails(SparePartAllocationHeadDTO spare);

    /**
     * 更新单据头信息
     *
     * @param spare 单据信息
     */
    void updateHead(SparePartAllocationHeadDTO spare);

    /**
     * 辅料调拨回调更新状态和remark
     * @param spare 请求参数
     */
    void updateBillHeadStatus(SparePartAllocationHeadDTO spare);

    List<String> getSpareBillNoList(SparePartAllocationHeadDTO headDTO);

    SparePartAllocationHeadDTO getSpareOutBoundList(@Param("billNo") String billNo);

    List<SparePartAllocationQueryDTO> querySpareInfoByPartCode(@Param("partCode") String partCode);

	List<SparePartAllocationHeadDTO> getBillNos(SparePartAllocationHeadDTO dto);

	List<SparePartAllocationHeadDTO> getInfoByBillNo(SparePartAllocationHeadDTO dto);

	Integer checkItemExist(SparePartAllocationHeadDTO dto);

	String checkDetailId(SparePartAllocationHeadDTO dto);

	int updateDetailStatus(SparePartAllocationHeadDTO dto);

	int updateHeadStatus(SparePartAllocationHeadDTO dto);

	Integer getCountByBillNoAndStatus(SparePartAllocationHeadDTO dto);

	List<SparePartAllocationHeadDTO> getCountByBillNo(SparePartAllocationHeadDTO dto);

    List<SparePartAllocationQueryDTO> queryExprotSpareInfo(Page<SparePartAllocationQueryDTO> page);

    List<SparePartAllocationQueryDTO> queryItemDetailByBillNo(Page<SparePartAllocationQueryDTO> page);

    Integer closeSparePartAllocation(@Param("billNo") String billNo, @Param("empNo") String empNo);

    Integer deleteSparePartAllocation(@Param("billNo") String billNo, @Param("empNo") String empNo);

    Integer rollbackSparePartAllocation(@Param("partCode")String partCode, @Param("empNo") String empNo);

    Integer checkAllocationItemDetailClose(String billNo);

    int countSparePartAllocationQuery(SparePartAllocationQueryDTO queryDTO);

    List<SparePartAllocationQueryDTO> pageList(Page<SparePartAllocationQueryDTO> page);

}
