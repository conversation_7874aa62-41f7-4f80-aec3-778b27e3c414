/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.domain.model;

import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 前加工信息维护DAO
 *
 * <AUTHOR>
 **/
@Mapper
public interface BsPremanuItemInfoRepository {

    /**
     * 增加实体数据
     *
     * @param record
     * @return
     **/
    int insertBsPremanuItemInfo(BsPremanuItemInfo record);

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    int insertBsPremanuItemInfoSelective(BsPremanuItemInfo record);

    /**
     * 根据主键删除实体数据
     *
     * @param record
     **/
    int deleteBsPremanuItemInfoById(BsPremanuItemInfo record);

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     **/
    int updateBsPremanuItemInfoByIdSelective(BsPremanuItemInfo record);

    /**
     * 根据主键更新实体数据
     *
     * @param record
     **/
    int updateBsPremanuItemInfoById(BsPremanuItemInfo record);

    /**
     * 根据主键查询实体信息
     *
     * @param record
     * @return BsPremanuItemInfo
     **/
    BsPremanuItemInfo selectBsPremanuItemInfoById(BsPremanuItemInfo record);

    /**
     * 查询总数
     */
    long getPremanuInfoCount(Map<String, Object> map);

    /**
     * list查询
     */
    List<BsPremanuItemInfo> getPremanuInfoList(Map<String, Object> map);

    /**
     * 查询物料级前加数据获取
     *
     * @param dto
     * @return
     */
    List<BsPremanuItemInfo> queryPreManuItemInfoNoPage(BsPremanuItemInfo dto);

    List<BsPremanuItemInfo> getPreManuItemInfo(Page<BsPremanuItemInfoDTO> record);

    int updatePreItemBatch(List<BsPremanuItemInfo> list);

    /**
     * 直接查询BS_BOM_HIERARCHICAL_HEAD以及DETAIL获取分阶信息
     */
    List<BsPremanuItemInfo> getBsBomInfo(@Param("bomCodeList") List<String> bomCodeList);

    List<BsPremanuItemInfo> getDipBomInfo(@Param("bomCodeList") List<String> bomCodeList);

    /**
     * list
     *
     * @param list 分页参数
     * @return 分页数据
     */
    List<BsPremanuItemInfo> selectPremanuInfo(List<String> list);

    /**
     * 获取维护成型、烘烤、写片 如前加工类型为写片
     * @return 维护了前加工的料单
     */
    List<String> selectItemList();

    List<BsPremanuItemInfo> queryBsItemPreInfoList(Page<BsPremanuItemInfo> pageInfo);

    Integer countBsItemPreExportTotal(BsPremanuItemInfo dto);

}
