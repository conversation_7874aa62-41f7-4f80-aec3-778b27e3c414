/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2020-05-15
 * 修改历史 :
 * 1. [2020-05-15] 创建文件 by 10241694
 **/
package com.zte.domain.model;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
public interface BsBomHierarchicalHeadRepository {

    /**
     * 批量新增数据
     * @param bsBomHierarchicalHeadList
     * @return
     */
    int batchInsertBsBomHead(List<BsBomHierarchicalHead> bsBomHierarchicalHeadList);

    /**
     * 批量更新数据
     * @param bomCodeList
     * @return
     */
    int updateBatchByBomCode(List<String> bomCodeList);
    /**
     * 批量删除数据
     * @param bomCodeList
     * @return
     */
    int deleteBatchByBomCode(List<String> bomCodeList);

    /**
     * 获取bom 分阶数据
     * @param productCodeList 料单代码
     * @return BOM 分阶数据
     */
    List<BsBomHierarchicalHead> getListByProductList(@Param("productCodeList") List<String> productCodeList);

    BsBomHierarchicalHead getAssembleBomInfo(@Param("bomCodeList")List<String> bomCodeList);
}