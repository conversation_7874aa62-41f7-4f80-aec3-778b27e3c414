/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BBomHeader implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * // TODO remarks
     **/
    private String bomHeaderId;

    /**
     * // TODO remarks
     **/
    private String productCode;

    /**
     * 文档云ID
     */
    private String cloudDiskId;

    /**
     * 文档云文件名
     */
    private String fileName;

    /**
     * // TODO remarks
     **/
    private String productType;

    /**
     * // TODO remarks
     **/
    private String engDesc;

    /**
     * // TODO remarks
     **/
    private String chiDesc;

    /**
     * // TODO remarks
     **/
    private String verNo;

    /**
     * // TODO remarks
     **/
    private String remark;

    /**
     * // TODO remarks
     **/
    private String sourceSystem;

    /**
     * // TODO remarks
     **/
    private String sourceItemId;

    /**
     * // TODO remarks
     **/
    private String createBy;

    /**
     * // TODO remarks
     **/
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    private Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    private String importedCadFlag;

    private String zjSubcardFlag;
    @ApiModelProperty("明细信息")
    private List<BBomDetail> details;

    /**
     * 解析状态；0：待处理  1成功，  5失败
     */
    private String parseStatus;
    private String confirmedPcbFpc;
    private String confirmedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date confirmedDate;
    @ApiModelProperty("0不包含，1包含")
    private Integer includePcbFpc;
	//价格
	private String lastPrice;

	private String standardCost;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getCloudDiskId() {
        return cloudDiskId;
    }

    public void setCloudDiskId(String cloudDiskId) {
        this.cloudDiskId = cloudDiskId;
    }

    public String getStandardCost() {
		return standardCost;
	}

	public void setStandardCost(String standardCost) {
		this.standardCost = standardCost;
	}
	public Integer getIncludePcbFpc() {
        return includePcbFpc;
    }

    public void setIncludePcbFpc(Integer includePcbFpc) {
        this.includePcbFpc = includePcbFpc;
    }

    public String getConfirmedPcbFpc() {
        return confirmedPcbFpc;
    }

    public void setConfirmedPcbFpc(String confirmedPcbFpc) {
        this.confirmedPcbFpc = confirmedPcbFpc;
    }

    public String getConfirmedBy() {
        return confirmedBy;
    }

    public void setConfirmedBy(String confirmedBy) {
        this.confirmedBy = confirmedBy;
    }

    public Date getConfirmedDate() {
        return confirmedDate;
    }

    public void setConfirmedDate(Date confirmedDate) {
        this.confirmedDate = confirmedDate;
    }

    public String getParseStatus() {
        return parseStatus;
    }

    public void setParseStatus(String parseStatus) {
        this.parseStatus = parseStatus;
    }

    public List<BBomDetail> getDetails() {
        return details;
    }

    public void setDetails(List<BBomDetail> details) {
        this.details = details;
    }

    public void setBomHeaderId(String bomHeaderId) {
        this.bomHeaderId = bomHeaderId;
    }

    public String getBomHeaderId() {
        return bomHeaderId;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductType() {
        return productType;
    }

    public void setEngDesc(String engDesc) {
        this.engDesc = engDesc;
    }

    public String getEngDesc() {
        return engDesc;
    }

    public void setChiDesc(String chiDesc) {
        this.chiDesc = chiDesc;
    }

    public String getChiDesc() {
        return chiDesc;
    }

    public void setVerNo(String verNo) {
        this.verNo = verNo;
    }

    public String getVerNo() {
        return verNo;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceItemId(String sourceItemId) {
        this.sourceItemId = sourceItemId;
    }

    public String getSourceItemId() {
        return sourceItemId;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

    public String getImportedCadFlag() {
        return importedCadFlag;
    }

    public void setImportedCadFlag(String importedCadFlag) {
        this.importedCadFlag = importedCadFlag;
    }

    public String getZjSubcardFlag() {
        return zjSubcardFlag;
    }

    public void setZjSubcardFlag(String zjSubcardFlag) {
        this.zjSubcardFlag = zjSubcardFlag;
    }

	public String getLastPrice() {
		return lastPrice;
	}

	public void setLastPrice(String lastPrice) {
		this.lastPrice = lastPrice;
	}
}
