package com.zte.domain.model.cbom;

import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-30 16:34
 */
@Mapper
public interface FixBomDetailRepository {

    /**
     * 批量删除数据
     * @param idList 头id
     */
    void deleteByHeadIdList(@Param("idList") List<String> idList,@Param("lastUpdatedBy") String lastUpdatedBy);

    /**
     * 批量新增记录
     * @param list 头表记录
     */
    void insertBatch(@Param("list") List<FixBomDetailDTO> list);

    /**
     *
     * @param headId headId
     * @return 头信息
     */
    List<FixBomDetailDTO> selectIdByHeadId(@Param("headId") String headId);

    /**
     *
     * @param fixBomId fixBomId
     * @return 头信息
     */
    List<FixBomDetailDTO> selectIdByFixBomId(@Param("fixBomId") String fixBomId);

    List<FixBomDetailDTO> getFixBomByTaskNo(@Param("taskNo") String taskNo);

    List<FixBomDetailDTO> getFixBomDetailByTaskNo(@Param("taskNo") String taskNo);
}
