package com.zte.domain.model;

import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 单板推送信息明细表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-24 15:30:55
 */
public interface PushBoardDataDetailRepository {
    /**
     * 查询已存在条码
     * @param snList
     * @return
     */
    List<String> getExistSn(@Param("snList") List<String> snList);

    /**
     * 批量插入
     * @param tempList
     * @return
     */
    int batchInsert(@Param("list") List<PushBoardDataDetailDTO> tempList);

    /**
     * 增量分批查询未推送条码
     * @param startTime 开始时间
     * @param sn 上一批数据最后一个sn 首次查询传空
     * @param limit 限制条数
     * @return
     */
    List<PushBoardDataDetailDTO> getNeedPushSn(@Param("startTime") Date startTime, @Param("sn") String sn, @Param("limit") int limit);

    /**
     * 更新数据
     * 仅更新状态,失败次数,错误信息,最后更新时间等字段
     * @param pushBoardDataDetailDTO
     */
    void update(PushBoardDataDetailDTO pushBoardDataDetailDTO);

    List<PushBoardDataDetailDTO> queryNeedInsertProcessList(PushBoardDataProcessDTO dto);

    List<PushBoardDataDetailDTO> getNotPushedList(@Param("startTime") Date startTime, @Param("sn") String sn, @Param("limit") int limit);

    void updateStatusBySn(@Param("snList") List<String> snList, @Param("pushStatus") int pushStatus);

    List<String> getPushDoneProdplanId(@Param("headList") List<PushBoardDataHeadDTO> headList);
}
