package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 料单与炉温关系
 *
 * <AUTHOR>
 * @date 2023-02-16 08:42:56
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BomCraftAttribute implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(value = "料单代码")
    private String bomNo;

    @ApiModelProperty(value = "料单名称")
    private String bomName;

    @ApiModelProperty(value = "PCB版本")
    private String verNo;

    @ApiModelProperty(value = "面别")
    private String surface;

    @ApiModelProperty(value = "炉温名称")
    private String furnaceTempName;

    @ApiModelProperty(value = "环保属性")
    private String leadFlag;

    @ApiModelProperty(value = "喷码机模板")
    private String inkJetPrinterTemplate;

    @ApiModelProperty(value = "喷码机后缀")
    private String inkJetPrinterSuffix;

    @ApiModelProperty(value = "工艺段")
    private String craftSection;

    @ApiModelProperty(value = "工厂id")
    private Integer factoryId;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "最后修改人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedDate;

    private String createByName;

    private String lastUpdatedByName;

    private String leadFlagMean;

    @ApiModelProperty(value = "批次")
    private String prodplanId;
}
