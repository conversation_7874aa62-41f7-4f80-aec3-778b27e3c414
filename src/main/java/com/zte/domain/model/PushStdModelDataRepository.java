package com.zte.domain.model;

import com.zte.interfaces.dto.PushStdDataTaskAndSnNumDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.PushStdModelDataExtDTO;
import com.zte.interfaces.dto.PushStdModelDataQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 标模推送信息头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-02 14:43:47
 */
public interface PushStdModelDataRepository {
    int batchInsert(@Param("list") List<PushStdModelDataDTO> list);

    int update(PushStdModelDataDTO dto);

    /**
     * 根据查询条件获取标模推送信息头扩展信息
     *
     * @param query 查询条件
     * @return 标模推送信息头扩展信息
     */
    List<PushStdModelDataExtDTO> selectExtByQuery(PushStdModelDataQueryDTO query);

    /**
     * 筛选已存在任务号
     * @param taskNoList
     * @return
     */
    List<String> getExistTaskNo(@Param("taskNoList") List<String> taskNoList);

    List<PushStdModelDataDTO> getNeedPushDataInc(@Param("startTime") Date startTime, @Param("customerName") String customerName, @Param("taskNo") String taskNo, @Param("limit") int limit);

    /**
     * 定时任务分批查询任务数据
     *
     * @param taskNo    上一个任务号
     * @param startTime
     * @return 查询出来的数据
     */
    List<PushStdModelDataDTO> selectDataByTaskNoLimit(@Param("taskNo")String taskNo,@Param("customerNameList") List<String> customerNameList,@Param("startTime") Date startTime);

    /**
     * 查询任务下sn的数量
     * @param taskNos 任务号
     * @return 推送数据
     */
    PushStdDataTaskAndSnNumDTO getPushSnNumByTaskNos(@Param("taskNo")String taskNos);

    /**
     * 查询失败记录
     *
     * @param taskNo    任务号
     * @param startTime
     * @return 推送数据
     */
    List<PushStdDataTaskAndSnNumDTO> selectPushFailData(@Param("taskNo")String taskNo,@Param("startTime") Date startTime);

    /**
     * 通过任务号查询单个数据
     * @param taskNo 任务号
     * @return 返回数据
     */
    PushStdModelDataDTO getDataByTaskNo(@Param("taskNo")String taskNo);
    List<PushStdModelDataDTO> getNeedPushDataInc(@Param("startTime") Date startTime, @Param("customerNameList") List<String> customerNameList, @Param("taskNo") String taskNo, @Param("limit") int limit);

    void batchSaveOrUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList);

    void batchUpdate(List<PushStdModelDataDTO> pushStdModelDataDTOList);

    List<PushStdModelDataDTO> getListByTaskNo(@Param("taskNos") List<String> taskNos);
}
