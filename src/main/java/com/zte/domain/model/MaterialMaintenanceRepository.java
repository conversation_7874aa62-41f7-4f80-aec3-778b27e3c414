package com.zte.domain.model;

import com.zte.interfaces.dto.MaterialMaintenanceDTO;
import com.zte.interfaces.dto.SolderInfo;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface MaterialMaintenanceRepository {

    List<MaterialMaintenanceDTO> queryMaterial(Page<MaterialMaintenanceDTO> page);
    List<String> queryMaterialByGlue();
    List<String> queryItemCodeByItemName(@Param("itemName") String itemName);
    List<String> querySupplierByItemNo(@Param("itemNo") String itemNo);

    void updateMaterial(MaterialMaintenanceDTO page);

    void addMaterial(MaterialMaintenanceDTO page);

    int checkExist(MaterialMaintenanceDTO dto);

    int delMaterial(MaterialMaintenanceDTO dto);

    List<MaterialMaintenanceDTO> queryMaterialByItemNoOrBarcode(@Param("itemNo") String itemNo, @Param("barcode") String barcode);

    List<MaterialMaintenanceDTO> queryMaterialsByBarcodes(@Param("barcodes") List<String> barcodes);

    List<MaterialMaintenanceDTO> queryMaterialsByItemNos(@Param("itemNos") List<String> itemNos);

}
