package com.zte.domain.model;


import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataPageQueryDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标模任务条码推送信息表数据库层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-14 09:58:57
 */
public interface PushStdModelSnDataRepository {


    /**
     * 查询数量
     *
     * @param query
     * @return
     */
    Integer selectCount(PushStdModelSnDataPageQueryDTO query);

    /**
     * 分页查询列表
     *
     * @param query
     * @return
     */
    List<PushStdModelSnData> selectPage(Page<PushStdModelSnDataPageQueryDTO> query);

    /**
     * 查询详情
     *
     * @param id 主键ID
     * @return
     */
    PushStdModelSnData selectById(@Param("id") String id);

    /**
     * 新增数据
     *
     * @param pushStdModelSnData 实体类
     * @return
     */
    long insert(PushStdModelSnData pushStdModelSnData);

    void batchInsertOrUpdate(List<PushStdModelSnData> pushStdModelSnDataList);


    /**
     * 按主键修改数据
     *
     * @param pushStdModelSnData
     * @return
     */
    long updateById(PushStdModelSnData pushStdModelSnData);

    long updateCurrProcessBySnAndTaskNo(PushStdModelSnData pushStdModelSnData);

    /**
     * 根据主键删除数据
     *
     * @param ids
     * @return
     */
    long deleteByIds(@Param("ids") List<String> ids);

    List<PushStdModelSnDataDTO> getNeedPushDataPage(PushStdModelSnDataDTO query);

    /**
     * 查询传入列表中已经存在的数据
     *
     * @param queryPushStdModelSnDataList queryPushStdModelSnDataList
     * @param factoryId                   工厂id
     * @return 已经存在的数据
     */
    List<PushStdModelSnDataDTO> selectExists(
            @Param("queryPushStdModelSnDataList") List<? extends PushStdModelSnDataDTO> queryPushStdModelSnDataList,
            @Param("factoryId") Integer factoryId
    );

    PushStdModelSnDataExtDTO selectExtByPrimaryKey(String id);

    List<PushStdModelSnDataExtDTO> selectExtByPrimaryKeys(List<String> ids);

    /**
     * 根据条件查询
     *
     * @param query query
     * @return List<PushStdModelSnDataExtDTO>
     */
    List<PushStdModelSnDataDTO> selectByQuery(PushStdModelSnDataQueryDTO query);

    /**
     * 批量插入标模推送信息SN明细
     *
     * @param snDataList snDataList
     * @return 成功条数
     */
    int batchInsert(List<PushStdModelSnDataDTO> snDataList);

    /**
     * 更新
     *
     * @param pushStdModelSnData pushStdModelSnData
     * @return 成功数
     */
    int update(PushStdModelSnDataDTO pushStdModelSnData);

    /**
     * 批量更新
     *
     * @param pushStdModelSnDataList pushStdModelSnDataList
     * @return 成功数
     */
    int batchUpdate(List<PushStdModelSnDataDTO> pushStdModelSnDataList);

    List<PushStdModelSnDataDTO> reportRecords(PushStdModelSnDataDTO b2bCallBackNew);

    /**
     * 获取推送条码信息
     *
     * @param sn        条码
     * @param taskNo    任务
     * @param factoryId 工厂id
     * @return 推送条码信息
     */
    PushStdModelSnDataDTO queryPushStdModelSn(@Param("sn") String sn, @Param("taskNo") String taskNo,
                                              @Param("factoryId") Integer factoryId);

    /**
     * 条码提换
     */
    void replaceSnByCondition(PushStdModelSnData pushStdModelSnData);

    /**
     * 批量查询条码细腻
     *
     * @param list list
     * @return List<PushStdModelSnDataDTO>
     */
    List<PushStdModelSnDataDTO> queryPushStdModelSnList(@Param("list") List<PushStdModelSnData> list);

    /**
     * 更新条码推送表虚拟条码
     * @param list list
     */
    void updateVirtualSnBatch(@Param("list") List<PushStdModelSnData> list);

    /**
     * 获取虚拟条码
     *
     * @param sn
     * @return
     */
    String getVirtualSn(String sn);
}
