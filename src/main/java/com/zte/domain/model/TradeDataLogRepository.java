package com.zte.domain.model;

import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/2 14:34
 */
public interface TradeDataLogRepository {

    int insert(CustomerDataLogDTO dto);
    void batchInsert(@Param("dtoList") List<CustomerDataLogDTO> dtoList);

    void updateStatusAndErrMsgById(@Param("msg") String msg, @Param("status")String status, @Param("id") String id);

    void updateStatusAndRemarkById(@Param("remark") String remark, @Param("status")String status,@Param("id") String id);

    void updateParentStatusById(@Param("status") String status, @Param("id") String id);

    List<CustomerDataLogDTO> getPushErrorData(Page page);

    CustomerDataLogDTO getTradeDataLogById(String id);

    List<CustomerDataLogDTO> getDataBySn(@Param("snList") List<String> snList, @Param("projectPhaseList") List<String> projectPhaseList);

    String getPushData(@Param("uuid")String uuid);

    List<CustomerDataLogDTO> pageList(Page<CustomerDataLogDTO> page);

    /**
     * 查询每个合同号的最早推送成功时间
     * @param messageType 消息类型
     * @return List<CustomerDataLogDTO>
     */
    List<CustomerDataLogDTO> selectFirstDateOfTaskNo(@Param("params") List<CustomerDataLogDTO> params, @Param("messageType") String messageType);

    List<CustomerDataLogDTO> selectPushFailList(@Param("idList") List<String> idList, @Param("createDate") Date createDate);
}
