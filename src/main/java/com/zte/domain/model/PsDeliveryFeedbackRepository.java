package com.zte.domain.model;

import com.zte.interfaces.VO.PsDeliveryFeedbackDownImportVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackVO;
import com.zte.interfaces.dto.PsDeliveryFeedbackSearchDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/9 下午2:49
 */
/* Started by AICoder, pid:0446fbd852j995514e0109d3009dbe19c598923c */

public interface PsDeliveryFeedbackRepository {
    /**
     * 批量新增，可写入空字段
     * @param list
     */
    int batchInsert(List<PsDeliveryFeedbackDO> list);

    /**
     * 单个更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(PsDeliveryFeedbackDO record);

    /**
     * 批量更新
     * @param list
     */
    int batchUpdateByPrimaryKeySelective(List<PsDeliveryFeedbackDO> list);
    /**
     * 批量更新，可更新空字段
     * @param list
     */
    int batchUpdateByPrimaryKey(List<PsDeliveryFeedbackDO> list);

    /**
     * 分页查询
     *
     * @param pageInfo
     * @return
     */
    List<PsDeliveryFeedbackVO> listByPage(Page<PsDeliveryFeedbackVO> pageInfo);


    /**
     * 通过任务号查询反馈数据
     *
     * @param taskNos
     * @return
     */
    List<PsDeliveryFeedbackVO> listByTask(List<String> taskNos);

    /**
     * 导出组件导出数量方法
     * @param psDeliveryFeedbackSearchDTO
     * @return
     */
    Integer countSparePartAllocationQuery(PsDeliveryFeedbackSearchDTO psDeliveryFeedbackSearchDTO);

    /**
     * 获取定时任务需要排除的任务号
     * @param taskNos
     * @return
     */
    List<String> listTaskNosOfScheduleExclude(List<String> taskNos);

    /**
     * 通过延期编号查询
     * @param importVO
     * @return
     */
    List<String> listByAbnormalNo(PsDeliveryFeedbackDownImportVO importVO);
}

/* Ended by AICoder, pid:0446fbd852j995514e0109d3009dbe19c598923c */
