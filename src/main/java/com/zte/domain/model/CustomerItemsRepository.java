package com.zte.domain.model;

import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CustomerItemsRepository {
    int newCustomerItems(CustomerItemsDTO dto);

    void updateCustomerItems(CustomerItemsDTO dto);

    Integer checkItemsExist(CustomerItemsDTO dto);

    List<CustomerItemsDTO> pageCustomerItemsInfo(Page<CustomerItemsDTO> page);

    void deleteCustomerItems(@Param("id") String id, @Param("empNo") String empNo);

    List<CustomerItemsDTO> getSubCustomerItemsInfo(CustomerItemsDTO dto);

    List<CustomerItemsDTO> getCustomerItemsInfoSelfDevelopedByItemNoList(CustomerItemsDTO customerItemsDTO);

    /**
     * 根据主键查数据id
     *
     * @param id id
     * @return
     */
    CustomerItemsDTO queryCustomerItemsById(@Param("id") String id);

    /**
     * 根据客户名称获取所有客户代码
     * @param dto 参数
     * @return
     */
    List<CustomerItemsDTO> queryListByCustomerList(CustomerItemsDTO dto);

    List<CustomerItemsDTO> checkItemsExistBatch(List<CustomerItemsDTO> customerItemsDTOs);

    void batchInsertCustomerItems(@Param("list") List<CustomerItemsDTO> customerItemsBatch);

    Integer deleteCustomerItemsByZteCodes(@Param("zteCodes") List<String> zteCodes);

    List<CustomerItemsDTO> queryListByZteCodes(@Param("zteCodes") List<String> zteCodes);

    List<CustomerItemsDTO> getZteCodeByCustomerName(@Param("customerNameList") List<String> customerNameList, @Param("rows") int rows, @Param("page") int page);

    int batchUpdateByZteCode(List<CustomerItemsDTO> customerItemsDTOList);

    List<CustomerItemsDTO> getByCustomerCode(@Param("customerCodeList") List<String> customerCodeList);

    List<CustomerItemsDTO> getSameItemOfZteCode(@Param("zteCodeList") List<String> zteCodeList, @Param("customerNumberList") List<String> customerNumberList);
    List<CustomerItemsDTO> queryListByZteCodAndCustomerNumber(@Param("zteCodes") List<String> zteCodes,
                                                          @Param("customerNumber") String customerNumber);
    List<CustomerItemsDTO> getZteCodeOfSameItem(@Param("itemSupplierNoList") List<String> itemSupplierNoList, @Param("customerNumberList") List<String> customerNumberList);
}
