package com.zte.domain.model;

import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 单板数据推送进程明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-02 14:43:47
 */
public interface PushBoardDataProcessRepository {

    /**
     * 查询条码在指定业务类型下是否存在数据
     *
     * @param snList
     * @param businessType
     * @return
     */
    List<String> getExistSn(@Param("snList") List<String> snList, @Param("businessType") String businessType);

    void batchInsert(@Param("list") List<PushBoardDataProcessDTO> list);

    void batchUpdate(@Param("list") List<PushBoardDataProcessDTO> list);

    void update(PushBoardDataProcessDTO pushBoardDataProcessDTO);

    List<PushBoardDataProcessDTO> getNeedPushSn(@Param("snList") List<String> snList, @Param("businessType") String businessType);

    List<PushBoardDataProcessDTO> getNotCallBackList(@Param("startTime") Date startTime, @Param("sn") String sn,
                                                     @Param("limit") int limit,@Param("businessTypeList") List<String> businessTypeList);

    /**
     * 不存在则新增对应数据
     *
     * @param list 参数
     */
    void batchInsertIfNotExists(List<PushBoardDataProcessDTO> list);

    /**
     * 查询需要推送的数据集合
     * @param param 请求参数
     * @return 条码集合
     */
    List<PushBoardDataProcessDTO> queryPushDataList(PushBoardDataProcessDTO param);

    /**
     * 根据条码和业务类类型获取已回调成功数据
     * @param snList
     * @param businessTypeList
     */
    List<PushBoardDataProcessDTO> getDataBySnAndBusinessType(@Param("snList") List<String> snList, @Param("businessTypeList") List<String> businessTypeList);
}
