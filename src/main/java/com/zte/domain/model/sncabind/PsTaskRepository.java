/**
 * 项目名称 : SnCaBinding
 * 创建日期 : 2019-07-11
 * 修改历史 :
 * 1. [2019-07-11] 创建文件 by 10243397
 **/
package com.zte.domain.model.sncabind;

import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackAliDTO;
import com.zte.interfaces.dto.StandardModelTaskDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import com.zte.interfaces.dto.aps.TaskCancelableDTO;
import com.zte.interfaces.dto.task.PsTaskTreeDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.interfaces.sncabind.dto.TaskBrandChangeStatusDTO;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 批次
 *
 * <AUTHOR>
 **/
@Mapper
public interface PsTaskRepository {

    /**
     * 有选择性的更新实体数据
     *
     * @param record
     * @return
     */
    long updatePsTaskByIdSelective(PsTask record);

    /**
     * getCount方法
     *
     * @param map 参数集
     * @return long
     */
    long getSpecifiedPsTaskCount(Map<String, Object> map);

    /**
     * getCount方法
     *
     * @param map 参数集
     * @return long
     */
    long getPsTaskCount(Map<String, Object> map);

    /**
     * 获取工厂数据 getlist方法
     *
     * @param map 参数集
     * @return list
     */
    List<PsTask> getPsTaskList(Map<String, Object> map);

    long getPsTaskCountWithRouteDetail(Map<String, Object> map);
    List<PsTask> getPsTaskListWithRouteDetail(Map<String, Object> map);

    /**
     * 分页查询
     * @param page 参数集
     * @return list
     */
    List<SpecifiedPsTaskVO> getSpecifiedPsTaskList(Page<SpecifiedPsTaskVO> page);

    List<PsTask> searchForProdPlan(List<String> transferList);

    List<String> searchByTaskNoList(List<String> taskNoList);

    /**
     * 更新任务生产单位
     *
     * @param psTask
     * @return
     */
    Integer updatePsTaskForFactory(PsTask psTask);

    /**
     * 根据任务号查询任务
     *
     * @param taskNo
     * @return
     */
    PsTask selectPsTaskByTaskNo(@Param("taskNo") String taskNo, @Param("prodplanId") String prodplanId);

    /**
     * @param entity
     * @return
     */
    int updateTaskMaintainInfo(PsTask entity);

    /**
     * 根据任务号以及
     *
     * @param taskNo
     * @param sourceSys
     * @return
     */
    List<PsTask> selectPsTaskList(@Param("taskNo") String taskNo, @Param("sourceSys") String sourceSys);

    /**
     * 插入
     *
     * @param psTask
     * @return
     */
    int insertSelective(PsTask psTask);

    /**
     * 更新
     *
     * @param psTask
     * @return
     */
    int updateByPrimaryKeySelective(PsTask psTask);

    /**
     * @param psTask
     * @return
     */
    int updateByPrimaryKey(PsTask psTask);

    /**
     * 根据批次查询所派发工厂
     *
     * @param List<String>
     * @return java.lang.String
     * @Author: 10307315陈俊熙
     * @date 2021/9/9 下午2:09
     */
    List<PsTaskDTO> getFactoryIdByProdplanId(@Param("prodplanIds") List<String> prodplanId);

    List<PsTaskDTO> getPsTaskByItemNoList(@Param("itemNoList") List<String> itemNoList);

    List<PsTaskDTO> getPsTaskByProdplanIdList(@Param("prodplanIdList") List<String> prodplanIdList);


    List<StandardModelTaskDTO> getStandardModelTaskStatus(Page<StandardModelTaskDTO> pageInfo);

    /**
     * 根据任务和批次号查询信息
     *
     * @param psTask
     * @return
     */
    List<PsTask> queryByTaskNoAndProdplanId(@Param("params") PsTask psTask);

    /**
     * 批量更新
     *
     * @param batchList
     * @return the int
     */
    int batchUpdate(@Param("list") List<PsTask> batchList);


    List<String> getExistingPlanId(@Param("list") List<String> planIds);

    int insertPsTask(@Param("list") List<PsTask> list);

    /**
     * 查询ps_task 是否存在任务
     *
     * @param list 任务号集合
     * @return 已经存在的任务号
     */
    List<String> getExistingTaskNo(@Param("list") List<String> list);

    /**
     * 生成下一个批次序列
     *
     * @return 批次序列
     */
    String getNextProdplanId();

    /**
     * 批量新增数据
     *
     * @param list 批次信息
     */
    void insertDataBatch(@Param("list") List<PsTaskDTO> list);

    /**
     * 查询批次信息
     *
     * @param sourceSys  来源系统
     * @param taskNoList 任务信息
     * @return 任务
     */
    List<PsTask> queryTaskByTaskNoList(@Param("sourceSys") String sourceSys, @Param("taskNoList") List<String> taskNoList);

    /**
     * 查询料单 子卡信息
     *
     * @param productList 料单代码
     */
    List<PsTask> querySiMeItemNo(@Param("productList") List<String> productList);

    void updateBatchPsTask(@Param("psTaskList") List<PsTask> psTaskList);

    /**
     * 批量获取批次信息，少量字段
     *
     * @param prodPlanIdList 批次信息列表
     * @return
     */
    List<PsTask> queryPsTaskBatch(@Param("prodPlanIdList") List<String> prodPlanIdList);

    String getBomNoByProdplanId(@Param("prodplanId") String prodplanId);

    String getLeadFlagByProdplanId(@Param("prodplanId") String prodplanId);

    List<BoardInstructionCycleDataCreateDTO> selectGetDateByProdplanNo(@Param("planNos") List<String> planNos);

    List<PsTaskDTO> selectErpStatusByProdplanId(@Param("prodplanList") List<String> prodplanList);

    List<PsTask> getTaskQtyByProdplanId(@Param("prodPlanIdList") List<String> prodPlanIdList);

    List<PsTask> queryFactoryIdByProdIdList(@Param("prodPlanIdList") List<String> prodPlanIdList);
    List<PsTask> queryFactoryIdByTaskNoList(@Param("taskNoList") List<String> prodPlanIdList);


    List<PsTask> selectPsTaskByProdIdSet(@Param("prodIdSet") Set<String> prodIdSet);

    PsTaskDTO getPsTaskByTaskNo(@Param("taskNo") String taskNo);

    List<PsTask> getFactoryIdByProdId(@Param("prodPlanIdList") List<String> prodPlanIdList);

    List<PsTask> selectListByPartFirstWarehouseDate(@Param("partFirstWarehouseDate") String partFirstWarehouseDate);

    PsTask selectOneByProdplanNo(@Param("prodplanNo") String prodplanNo);

    TaskBrandChangeStatusDTO selectBrandChangeStatusByTaskNo(@Param("taskNo")String taskNo);


    /**
     * 更新调拨标识
     *
     * @param sourceTaskList 批次
     * @param inforExe       标识
     */
    void batchUpdateInforExe(@Param("sourceTaskList") List<String> sourceTaskList, @Param("inforExe") String inforExe
            , @Param("lastUpdatedBy") String lastUpdatedBy);

    List<PsTask> selectItemCodeByProd(@Param("prods") List<String> prods);

    /**
     * 获取所有子卡信息
     *
     * @param taskNos 任务号
     * @return 子卡批次信息
     */
    List<PsTaskTreeDTO> getSubTaskByTaskNoList(@Param("taskNos") List<String> taskNos);

    /**
     * 查询标模任务号 source_sys为WMES的任务
     *
     * @param list 任务号集合
     * @return 标模任务号
     */
    List<String> getWmesTaskNo(@Param("list") List<String> list);
    /**
     * 处理未发放到本地工厂的批次
     *
     * @param map 参数集
     * @return list
     */
    List<PsTask> getPsTaskListByCondition(Map<String, Object> map);
    void updateBatchPsTaskByCondition(@Param("psTaskList") List<PsTask> psTaskList);

    PsTask getTaskInfoByTaskNo(@Param("taskNo") String taskNo);

    List<PsTask> selectProdPlanIDWitnSpare(@Param("prodPlanIdList") List<String> prodPlanIdList,@Param("inforList") List<String> inforList);

    List<PsTask> selectProdplanNoByProdplanId(@Param("prodPlanIdList") List<String> prodPlanIdList);

    com.github.pagehelper.Page<PsTask> pageBySourceSysAndTaskStatus(@Param("sourceSys") String sourceSys, @Param("taskStatus") String taskStatus, @Param("beginDate") Date beginDate);

    List<PsTask> queryTaskList(@Param("sourceSys") String sourceSys, @Param("taskCancelableDTOList") List<TaskCancelableDTO> taskCancelableDTOList);

    void updateConfirmationStatus(@Param("confirmationStatus") String confirmationStatus, @Param("taskNo") String taskNo);

    void batchUpdateByPK(List<PsTask> psTaskList);

    /**
     * 查询阿里任务工单数据
     *
     * @param feedbackScheduleDTO
     * @return
     */
    List<OrderTaskInfoVO> listAlibabaTask(PsDeliveryFeedbackAliDTO feedbackScheduleDTO);

    List<EntityQueryDTO> queryTaskQty(@Param("orgIdTaskNoPairs") List<EntityQueryDTO> entityQueryDTOS, @Param("beginProcess") Integer beginProcess, @Param("endProcess") Integer endProcess);
}
