package com.zte.domain.model.sncabind;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PsTask implements Serializable {

	/**
	 * 上传文件
	 **/
	@ApiModelProperty(value = "上传文件")
	private MultipartFile file;

    private String taskId;


    private String taskNo;


    private String contractNo;


    private String itemNo;


    private String isFinishGood;


    private String productMode;

    private String mBom;


    private String itemName;


    private String isLead;


    private BigDecimal taskQty;


    private BigDecimal completeQty;


    private String taskStatus;


    private String internalType;


    private String externalType;


    private String moNo;


    private String type;


    private String softwareVersion;


    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date releaseDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date plannedFinishDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date finalFinishDate;


    private String sourceSys;


    private String productType;


    private String remark;


    private String createBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createDate;


    private String lastUpdatedBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date lastUpdatedDate;


    private String enabledFlag;


    private BigDecimal orgId;


    private BigDecimal factoryId;


    private BigDecimal entityId;


    private String attribute1;


    private BigDecimal attribute2;


    private String attribute3;


    private String attribute4;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date attribute5;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date attribute6;


    private String attribute7;


    private String attribute8;


    private BigDecimal attribute9;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date attribute10;


    private String prodplanNo;


    private String prodplanId;


    private String bomId;


    private String planId;


    private String planUuid;


    private String isParts;


    private String partsPlanno;


    private BigDecimal planSequence;


    private String isFinishen;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date priceDate;


    private String softwareVer;


    private String leadFlag;


    private String outFlag;


    private String planner;


    private String bjBomId;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date demanEndDate;


    private String generateFlag;


    private String codeDesc;


    private String stoveType;


    private String isDiscave;


    private String prodAddress;


    private String pdmWorkGroup3Name;


    private String sourceType;


    private String prodStatus;


    private String pdmWorkGroup1Name;


    private String pdmWorkGroup2Name;


    private String erpStatus;


    private String entpNo;


    private String hasGenerateSn;

    private String factoryName;

    private String attribute11;

    private String attribute12;

    private String attribute13;

    private String attribute14;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date toGrantDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date grantTime;

    private String isCompleted;

    private String originalTask;

    private String erpModel;

    private String reworkSource;

    //首次预计交期
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date firstExpectedDeliveryDate;

    //首件入库日期
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date firstWarehouseDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date firstWarehouse90Date;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date partFirstWarehouseDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date partFirstWarehouse90Date;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date getDate;

    //最新交期
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date lastDeliveryDate;

    //批次收发料是否完成标识（Y完成，N未完成）
    private String receiveSendMaterialsFlag;

    private String splitFlag;

    private String operateType;

    private BigDecimal selectedFactoryId;

	private String assemblyRemark;

	private List<String> taskNoList;

	private List<String> prodplanIdList;

	private String validResp; // excel校验结果

    private String inforExe; // 备料状态
    // 委托加工单号
    private String consignplanNo;

    // 是否委托加工
    private Integer isConsign;

    // 委托加工单号
    private String consignBomId;

    // 委托加工料单
    private String consignBomNo;

    private String orgBomId;

    // 外协厂编码
    private String outSourceFactoryCode;

    /**
     * 交货地
     */
    private String stock;

    private String routeDetail;
    /**
     * 工艺员
     */
    private String technologist;

    private String verNo;

    private String plannerName;

    private String grantByName;

    /**
     * 是否需要转移技改 Y N
     */
    private String needMoveTenicAndLock;
    /**
     * 任务数量是否有变化 Y N
     */
    private String qtyChangeFlag;
    private String delSns;

    /**
     * 转正状态
     */
    private String confirmationStatus;

    @ApiModelProperty("变更版本")
    private String changeVersion;

    @ApiModelProperty("处理结果 Y成功 N失败")
    private String dealResult;

    /**
     * 存在入库条码
     */
    private boolean existInboundBarcode;

    private String materialSign;

    private String materialSignName;

    public String getDealResult() {
        return dealResult;
    }

    public void setDealResult(String dealResult) {
        this.dealResult = dealResult;
    }

    public String getChangeVersion() {
        return changeVersion;
    }

    public void setChangeVersion(String changeVersion) {
        this.changeVersion = changeVersion;
    }

    public String getDelSns() {
        return delSns;
    }

    public void setDelSns(String delSns) {
        this.delSns = delSns;
    }

    public String getQtyChangeFlag() {
        return qtyChangeFlag;
    }

    public void setQtyChangeFlag(String qtyChangeFlag) {
        this.qtyChangeFlag = qtyChangeFlag;
    }

    public String getNeedMoveTenicAndLock() {
        return needMoveTenicAndLock;
    }

    public void setNeedMoveTenicAndLock(String needMoveTenicAndLock) {
        this.needMoveTenicAndLock = needMoveTenicAndLock;
    }

    public String getOutSourceFactoryCode() {
        return outSourceFactoryCode;
    }

    public void setOutSourceFactoryCode(String outSourceFactoryCode) {
        this.outSourceFactoryCode = outSourceFactoryCode;
    }

    public String getPlannerName() {
        return plannerName;
    }

    public void setPlannerName(String plannerName) {
        this.plannerName = plannerName;
    }

    public String getGrantByName() {
        return grantByName;
    }

    public void setGrantByName(String grantByName) {
        this.grantByName = grantByName;
    }

    public String getVerNo() {
        return verNo;
    }

    public void setVerNo(String verNo) {
        this.verNo = verNo;
    }

    private String grantBy;

    public String getGrantBy() {
        return grantBy;
    }

    public void setGrantBy(String grantBy) {
        this.grantBy = grantBy;
    }

    public String getStock() {
        return stock;
    }

    public void setStock(String stock) {
        this.stock = stock;
    }

    public String getOrgBomId() {
        return orgBomId;
    }

    public void setOrgBomId(String orgBomId) {
        this.orgBomId = orgBomId;
    }

    public String getConsignplanNo() {
        return consignplanNo;
    }

    public void setConsignplanNo(String consignplanNo) {
        this.consignplanNo = consignplanNo;
    }

    public Integer getIsConsign() {
        return isConsign;
    }

    public void setIsConsign(Integer isConsign) {
        this.isConsign = isConsign;
    }

    public String getConsignBomId() {
        return consignBomId;
    }

    public void setConsignBomId(String consignBomId) {
        this.consignBomId = consignBomId;
    }

    public String getConsignBomNo() {
        return consignBomNo;
    }

    public void setConsignBomNo(String consignBomNo) {
        this.consignBomNo = consignBomNo;
    }

    public String getInforExe() {
        return inforExe;
    }

    public void setInforExe(String inforExe) {
        this.inforExe = inforExe;
    }

    public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}


	public BigDecimal getSelectedFactoryId() {
        return selectedFactoryId;
    }

    public void setSelectedFactoryId(BigDecimal selectedFactoryId) {
        this.selectedFactoryId = selectedFactoryId;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    private static final long serialVersionUID = 1L;

    public String getSplitFlag() {
        return splitFlag;
    }

    public void setSplitFlag(String splitFlag) {
        this.splitFlag = splitFlag;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getIsFinishGood() {
        return isFinishGood;
    }

    public void setIsFinishGood(String isFinishGood) {
        this.isFinishGood = isFinishGood;
    }

    public String getProductMode() {
        return productMode;
    }

    public void setProductMode(String productMode) {
        this.productMode = productMode;
    }

    public String getmBom() {
        return mBom;
    }

    public void setmBom(String mBom) {
        this.mBom = mBom;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getIsLead() {
        return isLead;
    }

    public void setIsLead(String isLead) {
        this.isLead = isLead;
    }

    public BigDecimal getTaskQty() {
        return taskQty;
    }

    public void setTaskQty(BigDecimal taskQty) {
        this.taskQty = taskQty;
    }

    public BigDecimal getCompleteQty() {
        return completeQty;
    }

    public void setCompleteQty(BigDecimal completeQty) {
        this.completeQty = completeQty;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getInternalType() {
        return internalType;
    }

    public void setInternalType(String internalType) {
        this.internalType = internalType;
    }

    public String getExternalType() {
        return externalType;
    }

    public void setExternalType(String externalType) {
        this.externalType = externalType;
    }

    public String getMoNo() {
        return moNo;
    }

    public void setMoNo(String moNo) {
        this.moNo = moNo;
    }

    public String getType() {
        return type;
    }

    public String getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(String isCompleted) {
        this.isCompleted = isCompleted;
    }

    public String getOriginalTask() {
        return originalTask;
    }

    public void setOriginalTask(String originalTask) {
        this.originalTask = originalTask;
    }

    public String getErpModel() {
        return erpModel;
    }

    public void setErpModel(String erpModel) {
        this.erpModel = erpModel;
    }

    public String getReworkSource() {
        return reworkSource;
    }

    public void setReworkSource(String reworkSource) {
        this.reworkSource = reworkSource;
    }

    public Date getFirstExpectedDeliveryDate() {
        return firstExpectedDeliveryDate;
    }

    public void setFirstExpectedDeliveryDate(Date firstExpectedDeliveryDate) {
        this.firstExpectedDeliveryDate = firstExpectedDeliveryDate;
    }

    public Date getFirstWarehouseDate() {
        return firstWarehouseDate;
    }

    public void setFirstWarehouseDate(Date firstWarehouseDate) {
        this.firstWarehouseDate = firstWarehouseDate;
    }

    public Date getLastDeliveryDate() {
        return lastDeliveryDate;
    }

    public void setLastDeliveryDate(Date lastDeliveryDate) {
        this.lastDeliveryDate = lastDeliveryDate;
    }

    public String getReceiveSendMaterialsFlag() {
        return receiveSendMaterialsFlag;
    }

    public void setReceiveSendMaterialsFlag(String receiveSendMaterialsFlag) {
        this.receiveSendMaterialsFlag = receiveSendMaterialsFlag;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public Date getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    public Date getPlannedFinishDate() {
        return plannedFinishDate;
    }

    public void setPlannedFinishDate(Date plannedFinishDate) {
        this.plannedFinishDate = plannedFinishDate;
    }

    public Date getFinalFinishDate() {
        return finalFinishDate;
    }

    public void setFinalFinishDate(Date finalFinishDate) {
        this.finalFinishDate = finalFinishDate;
    }

    public String getSourceSys() {
        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {
        this.sourceSys = sourceSys;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public BigDecimal getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(BigDecimal attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public Date getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(Date attribute5) {
        this.attribute5 = attribute5;
    }

    public Date getAttribute6() {
        return attribute6;
    }

    public void setAttribute6(Date attribute6) {
        this.attribute6 = attribute6;
    }

    public String getAttribute7() {
        return attribute7;
    }

    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7;
    }

    public String getAttribute8() {
        return attribute8;
    }

    public void setAttribute8(String attribute8) {
        this.attribute8 = attribute8;
    }

    public BigDecimal getAttribute9() {
        return attribute9;
    }

    public void setAttribute9(BigDecimal attribute9) {
        this.attribute9 = attribute9;
    }

    public Date getAttribute10() {
        return attribute10;
    }

    public void setAttribute10(Date attribute10) {
        this.attribute10 = attribute10;
    }

    public String getProdplanNo() {
        return prodplanNo;
    }

    public void setProdplanNo(String prodplanNo) {
        this.prodplanNo = prodplanNo;
    }

    public String getProdplanId() {
        return prodplanId;
    }

    public void setProdplanId(String prodplanId) {
        this.prodplanId = prodplanId;
    }

    public String getBomId() {
        return bomId;
    }

    public void setBomId(String bomId) {
        this.bomId = bomId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getPlanUuid() {
        return planUuid;
    }

    public void setPlanUuid(String planUuid) {
        this.planUuid = planUuid;
    }

    public String getIsParts() {
        return isParts;
    }

    public void setIsParts(String isParts) {
        this.isParts = isParts;
    }

    public String getPartsPlanno() {
        return partsPlanno;
    }

    public void setPartsPlanno(String partsPlanno) {
        this.partsPlanno = partsPlanno;
    }

    public BigDecimal getPlanSequence() {
        return planSequence;
    }

    public void setPlanSequence(BigDecimal planSequence) {
        this.planSequence = planSequence;
    }

    public String getIsFinishen() {
        return isFinishen;
    }

    public void setIsFinishen(String isFinishen) {
        this.isFinishen = isFinishen;
    }

    public Date getPriceDate() {
        return priceDate;
    }

    public void setPriceDate(Date priceDate) {
        this.priceDate = priceDate;
    }

    public String getSoftwareVer() {
        return softwareVer;
    }

    public void setSoftwareVer(String softwareVer) {
        this.softwareVer = softwareVer;
    }

    public String getLeadFlag() {
        return leadFlag;
    }

    public void setLeadFlag(String leadFlag) {
        this.leadFlag = leadFlag;
    }

    public String getOutFlag() {
        return outFlag;
    }

    public void setOutFlag(String outFlag) {
        this.outFlag = outFlag;
    }

    public String getPlanner() {
        return planner;
    }

    public void setPlanner(String planner) {
        this.planner = planner;
    }

    public String getBjBomId() {
        return bjBomId;
    }

    public void setBjBomId(String bjBomId) {
        this.bjBomId = bjBomId;
    }

    public Date getDemanEndDate() {
        return demanEndDate;
    }

    public void setDemanEndDate(Date demanEndDate) {
        this.demanEndDate = demanEndDate;
    }

    public String getGenerateFlag() {
        return generateFlag;
    }

    public void setGenerateFlag(String generateFlag) {
        this.generateFlag = generateFlag;
    }

    public String getCodeDesc() {
        return codeDesc;
    }

    public void setCodeDesc(String codeDesc) {
        this.codeDesc = codeDesc;
    }

    public String getStoveType() {
        return stoveType;
    }

    public void setStoveType(String stoveType) {
        this.stoveType = stoveType;
    }

    public String getIsDiscave() {
        return isDiscave;
    }

    public void setIsDiscave(String isDiscave) {
        this.isDiscave = isDiscave;
    }

    public String getProdAddress() {
        return prodAddress;
    }

    public void setProdAddress(String prodAddress) {
        this.prodAddress = prodAddress;
    }

    public String getPdmWorkGroup3Name() {
        return pdmWorkGroup3Name;
    }

    public void setPdmWorkGroup3Name(String pdmWorkGroup3Name) {
        this.pdmWorkGroup3Name = pdmWorkGroup3Name;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getProdStatus() {
        return prodStatus;
    }

    public void setProdStatus(String prodStatus) {
        this.prodStatus = prodStatus;
    }

    public String getPdmWorkGroup1Name() {
        return pdmWorkGroup1Name;
    }

    public void setPdmWorkGroup1Name(String pdmWorkGroup1Name) {
        this.pdmWorkGroup1Name = pdmWorkGroup1Name;
    }

    public String getPdmWorkGroup2Name() {
        return pdmWorkGroup2Name;
    }

    public void setPdmWorkGroup2Name(String pdmWorkGroup2Name) {
        this.pdmWorkGroup2Name = pdmWorkGroup2Name;
    }

    public String getErpStatus() {
        return erpStatus;
    }

    public void setErpStatus(String erpStatus) {
        this.erpStatus = erpStatus;
    }

    public String getEntpNo() {
        return entpNo;
    }

    public void setEntpNo(String entpNo) {
        this.entpNo = entpNo;
    }

    public String getHasGenerateSn() {
        return hasGenerateSn;
    }

    public void setHasGenerateSn(String hasGenerateSn) {
        this.hasGenerateSn = hasGenerateSn;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getAttribute11() {
        return attribute11;
    }

    public void setAttribute11(String attribute11) {
        this.attribute11 = attribute11;
    }

    public String getAttribute12() {
        return attribute12;
    }

    public void setAttribute12(String attribute12) {
        this.attribute12 = attribute12;
    }

    public String getAttribute13() {
        return attribute13;
    }

    public void setAttribute13(String attribute13) {
        this.attribute13 = attribute13;
    }

    public String getAttribute14() {
        return attribute14;
    }

    public void setAttribute14(String attribute14) {
        this.attribute14 = attribute14;
    }

    public Date getToGrantDate() {
        return toGrantDate;
    }

    public void setToGrantDate(Date toGrantDate) {
        this.toGrantDate = toGrantDate;
    }

    public Date getGrantTime() {
        return grantTime;
    }

    public void setGrantTime(Date grantTime) {
        this.grantTime = grantTime;
    }

	public String getAssemblyRemark() {
		return assemblyRemark;
	}

	public void setAssemblyRemark(String assemblyRemark) {
		this.assemblyRemark = assemblyRemark;
	}

	public List<String> getTaskNoList() {
		return taskNoList;
	}

	public void setTaskNoList(List<String> taskNoList) {
		this.taskNoList = taskNoList;
	}

	public List<String> getProdplanIdList() {
		return prodplanIdList;
	}

	public void setProdplanIdList(List<String> prodplanIdList) {
		this.prodplanIdList = prodplanIdList;
	}

	public MultipartFile getFile() {
		return file;
	}

	public void setFile(MultipartFile file) {
		this.file = file;
	}

    private String productClassName;
    private String productSmlclassName;
    private String prodUnitname;

    /**
     * 上层指令号
     */
    private String zbjprodplanNo;

    public String getProdUnitname() {
        return prodUnitname;
    }

    public void setProdUnitname(String prodUnitname) {
        this.prodUnitname = prodUnitname;
    }

    public String getProductClassName() {
        return productClassName;
    }

    public void setProductClassName(String productClassName) {
        this.productClassName = productClassName;
    }

    public String getProductSmlclassName() {
        return productSmlclassName;
    }

    public void setProductSmlclassName(String productSmlclassName) {
        this.productSmlclassName = productSmlclassName;
    }


    public String getRouteDetail() {
        return routeDetail;
    }

    public void setRouteDetail(String routeDetail) {
        this.routeDetail = routeDetail;
    }

    public Date getFirstWarehouse90Date() {
        return firstWarehouse90Date;
    }

    public void setFirstWarehouse90Date(Date firstWarehouse90Date) {
        this.firstWarehouse90Date = firstWarehouse90Date;
    }

    public Date getPartFirstWarehouseDate() {
        return partFirstWarehouseDate;
    }

    public void setPartFirstWarehouseDate(Date partFirstWarehouseDate) {
        this.partFirstWarehouseDate = partFirstWarehouseDate;
    }

    public Date getPartFirstWarehouse90Date() {
        return partFirstWarehouse90Date;
    }

    public void setPartFirstWarehouse90Date(Date partFirstWarehouse90Date) {
        this.partFirstWarehouse90Date = partFirstWarehouse90Date;
    }

    public Date getGetDate() {
        return getDate;
    }

    public void setGetDate(Date getDate) {
        this.getDate = getDate;
    }

    public String getZbjprodplanNo() {
        return zbjprodplanNo;
    }

    public void setZbjprodplanNo(String zbjprodplanNo) {
        this.zbjprodplanNo = zbjprodplanNo;
    }

    public String getTechnologist() {
        return technologist;
    }

    public void setTechnologist(String technologist) {
        this.technologist = technologist;
    }

    public String getConfirmationStatus() {
        return confirmationStatus;
    }

    public void setConfirmationStatus(String confirmationStatus) {
        this.confirmationStatus = confirmationStatus;
    }

    public boolean isExistInboundBarcode() {
        return existInboundBarcode;
    }

    public void setExistInboundBarcode(boolean existInboundBarcode) {
        this.existInboundBarcode = existInboundBarcode;
    }

    private String customerNo;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getMaterialSign() {
        return materialSign;
    }

    public void setMaterialSign(String materialSign) {
        this.materialSign = materialSign;
    }

    public String getMaterialSignName() {
        return materialSignName;
    }

    public void setMaterialSignName(String materialSignName) {
        this.materialSignName = materialSignName;
    }
}
