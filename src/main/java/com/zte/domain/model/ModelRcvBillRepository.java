package com.zte.domain.model;

import com.zte.interfaces.dto.ModelChangedCartonDTO;
import com.zte.interfaces.dto.ModelRcvBillDTO;
import com.zte.interfaces.dto.ModelRcvBillDetailDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface ModelRcvBillRepository {

    /**
     * 保存MES领用单
     *
     * @param list
     * @return
     */
    int batchInsertIgnoreExisted(@Param("list") List<ModelRcvBillDTO> list);

    /**
     * 条件查询
     *
     * @param modelRcvBillDTO
     */
    List<ModelRcvBillDTO> getByCondition(ModelRcvBillDTO modelRcvBillDTO);

    /**
     * 修改推送状态
     *
     * @param pushStdModelDataDTO
     * @return
     */
    int updateStatus(@Param("list") List<PushStdModelDataDTO> pushStdModelDataDTO);

    /**
     * 批量插入
     * @param modelRcvBillDetailList
     */
    void batchInsert(@Param("list") List<ModelRcvBillDetailDTO> modelRcvBillDetailList);

    /**
     * 根据条件查询变化的箱单SN关系
     * @param modelRcvBillDTO
     * @return
     */
    List<ModelChangedCartonDTO> getChangeSNByCondition(ModelRcvBillDTO modelRcvBillDTO);

    /**
     * 根据箱号查标模领料信息推送明细
     * @param cartonNoList
     * @return
     */
    List<ModelRcvBillDetailDTO> getModelRcvBillDetailByCartonNo(@Param("cartonNoList") List<String> cartonNoList);

    /**
     * 修改推送状态
     *
     * @param pushStdModelDataDTO
     * @return
     */
    int updateStatusByCartonNo(@Param("list") List<PushStdModelDataDTO> pushStdModelDataDTO);

    /**
     * 批量插入变化的箱单SN关系
     * @param modelChangedCartonList
     * @return
     */
    int batchInsertChangeCartonNo(List<ModelChangedCartonDTO> modelChangedCartonList);

    void deleteExistedSn(@Param("rcvNos") List<String> rcvNos);

    void updateErrorMsg(@Param("list") List<ModelRcvBillDTO> updateRcvBills);

}
