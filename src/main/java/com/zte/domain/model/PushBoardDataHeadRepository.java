package com.zte.domain.model;

import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 单板推送信息头表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-24 15:30:54
 */
public interface PushBoardDataHeadRepository {

    /**
     * 查询已存在批次
     * @param prodplanIdList
     * @return
     */
    List<String> getExistProdplanId(@Param("prodplanIdList") List<String> prodplanIdList);

    /**
     * 批量插入
     * @param list
     * @return
     */
    Integer batchInsert(@Param("list") List<PushBoardDataHeadDTO> list);

    /**
     * 根据客户名称分页查询未完成推送批次
     * @param customerNameList 客户名称
     * @param rows 分页数
     * @param page 页码
     * @return
     */
    List<String> getNotPushDoneProdplanId(@Param("customerNameList") List<String> customerNameList, @Param("rows") int rows, @Param("page") int page);

    List<PushBoardDataHeadDTO> getNotPushedList(@Param("startTime") Date startTime, @Param("lastProdplanId") String lastProdplanId, @Param("limit") int limit);

    void updateStatusByProdplanId(@Param("prodplanIdList") List<String> prodplanIdList, @Param("pushStatus") int pushStatus);
}
