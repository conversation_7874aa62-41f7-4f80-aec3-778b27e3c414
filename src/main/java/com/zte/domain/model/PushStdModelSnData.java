package com.zte.domain.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;


/**
 * 标模任务条码推送信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-14 09:58:57
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushStdModelSnData implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ExcelProperty(value = "id")
    private String id;
	/**
	 * 条码
	 */
	@ExcelProperty(value = "条码")
    private String sn;
	/**
	 * 任务号
	 */
	@NotBlank
	@ExcelProperty(value = "任务号")
    private String taskNo;
	/**
	 * 工厂id
	 */
	@ExcelProperty(value = "工厂id")
    private Integer factoryId;
	/**
	 * 当前推送进程 25:整机测试数据上报 30:产品SN上报 40:成品入库上报
	 */
	@ExcelProperty(value = "当前推送进程 25:整机测试数据上报 30:产品SN上报 40:成品入库上报")
    private String currProcess;
	/**
	 * 当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常
	 */
	@ExcelProperty(value = "当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常")
    private Integer pushStatus;
	/**
	 * 当前进程数据推送时间
	 */
	@ExcelProperty(value = "当前进程数据推送时间")
    private Date pushDate;
	/**
	 * 异常信息
	 */
	@ExcelProperty(value = "异常信息")
    private String errorMsg;
	/**
	 * 当前进程推送失败次数
	 */
	@ExcelProperty(value = "当前进程推送失败次数")
    private Integer pushFailCount;
	/**
	 * 
	 */
	@ExcelProperty(value = "createBy")
    private String createBy;
	/**
	 * 
	 */
	@ExcelProperty(value = "createDate")
    private Date createDate;
	/**
	 * 
	 */
	@ExcelProperty(value = "lastUpdatedBy")
    private String lastUpdatedBy;
	/**
	 * 
	 */
	@ExcelProperty(value = "lastUpdatedDate")
    private Date lastUpdatedDate;
	/**
	 * 
	 */
	@ExcelProperty(value = "enabledFlag")
    private String enabledFlag;
	@ExcelProperty(value = "itemNo")
	private String itemNo;

	/**
	 * 原始Sn
	 */
	@NotBlank
	private String originalSn;
	/**
	 * 虚拟mock条码
	 */
	private String virtualSn;
	/**
	 * 替换Sn
	 */
	@NotBlank
	private String replaceSn;

}
