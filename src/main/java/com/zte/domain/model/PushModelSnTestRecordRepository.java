package com.zte.domain.model;


import com.zte.interfaces.dto.PushModelSnTestRecordDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordPageQueryDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标模任务条码推送测试记录表数据库层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-15 08:53:49
 */
public interface PushModelSnTestRecordRepository{
    List<PushModelSnTestRecord> queryUploadSuccessList(PushModelSnTestRecordPageQueryDTO query);

    /**
     * 查询详情
     *
     * @param requestId 主键ID
     * @return
     */
    PushModelSnTestRecord selectById(@Param("requestId") String requestId);

    List<PushModelSnTestRecord> selectByIdList(@Param("stationIdList") List<String> requestIdList);

    List<PushModelSnTestRecord> selectBySn(@Param("sn") String sn);

    /**
     * 新增数据
     *
     * @param pushModelSnTestRecord 实体类
     * @return
     */
    long insert(PushModelSnTestRecord pushModelSnTestRecord);


    /**
     * 新增数据
     *
     * @param pushModelSnTestRecord 实体类
     * @return
     */
    long batchInsertOrUpdate(List<PushModelSnTestRecordDTO> list);

    /**
     * 按主键修改数据
     *
     * @param pushModelSnTestRecord
     * @return
     */
    long updateSelectiveById(PushModelSnTestRecord pushModelSnTestRecord);

    /**
     * 根据主键删除数据
     *
     * @param requestIds
     * @return
     */
    long deleteByIds(@Param("requestIds") List<String> requestIds);

}
