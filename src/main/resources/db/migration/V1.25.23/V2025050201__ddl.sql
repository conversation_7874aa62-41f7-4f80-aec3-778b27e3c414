CREATE TABLE if not exists trade_data_log (
	id varchar(64) NOT NULL, -- 主键ID
	tid varchar(64) NULL, -- 原始ID
	keywords varchar(255) NULL DEFAULT ''::character varying, -- 关键字
	origin varchar(32) NOT NULL DEFAULT ''::character varying, -- 来源 iMES、MES、INFOR
	customer_name varchar(255) NOT NULL DEFAULT ''::character varying, -- 客户名称
	project_name varchar(255) NOT NULL DEFAULT ''::character varying, -- 项目名称
	project_phase varchar(32) NULL DEFAULT ''::character varying, -- 项目阶段
	cooperation_mode varchar(32) NULL DEFAULT ''::character varying, -- 合作模式
	message_type varchar(255) NOT NULL DEFAULT ''::character varying, -- 消息类型
	push_type varchar(255) NOT NULL DEFAULT ''::character varying, -- 推送方式
	craft_section varchar(255) NULL DEFAULT ''::character varying, -- 主工序
	contract_no varchar(125) NULL DEFAULT ''::character varying, -- 合同号
	task_no varchar(125) NULL DEFAULT ''::character varying, -- 任务号
	item_no varchar(64) NULL DEFAULT ''::character varying, -- 物料代码
	sn varchar(255) NULL DEFAULT ''::character varying, -- 条码
	json_data varchar NOT NULL DEFAULT ''::character varying, -- 数据
	err_msg varchar NULL DEFAULT ''::character varying, -- 错误数据
	begin_time timestamp NULL, -- 开始时间
	end_time timestamp NULL, -- 结束时间
	factory_id int4 NOT NULL DEFAULT 0, -- 工厂ID
	status varchar(10) NOT NULL DEFAULT ''::character varying, -- 状态 PN 推B2B失败¶PY 推B2B成功/未回调  ¶CY回调返回成功¶ CN回调返回失败
	remark varchar(4000) NULL DEFAULT ''::character varying, -- 备注
	create_by varchar(20) NOT NULL DEFAULT ''::character varying, -- 创建人
	create_date timestamp NOT NULL DEFAULT now(), -- 创建日期 默认为当前时间
	last_updated_by varchar(20) NOT NULL DEFAULT ''::character varying, -- 更新人
	last_updated_date timestamp NOT NULL DEFAULT now(), -- 更新日期 默认为当前时间
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying, -- 是否有效 选项：Y=正常 N=已删除
	CONSTRAINT trade_data_log_pk PRIMARY KEY (create_date, id)
)
PARTITION BY RANGE (create_date);
CREATE INDEX if not exists idx_tdl_id ON ONLY trade_data_log USING btree (id);
CREATE INDEX if not exists idx_tdl_create_date ON ONLY trade_data_log USING btree (create_date);
CREATE INDEX if not exists idx_tdl_message_type ON ONLY trade_data_log USING btree (message_type, end_time);
CREATE INDEX if not exists idx_tdl_keywords ON ONLY trade_data_log USING btree (keywords);
CREATE INDEX if not exists idx_tdl_contract_no ON ONLY trade_data_log USING btree (contract_no);
CREATE INDEX if not exists idx_tdl_task_no ON ONLY trade_data_log USING btree (task_no);
CREATE INDEX if not exists idx_tdl_sn_project_phase ON ONLY trade_data_log USING btree (sn,project_phase);
COMMENT ON TABLE trade_data_log IS '交易数据推送日志';

-- Column comments

COMMENT ON COLUMN trade_data_log.id IS '主键ID';
COMMENT ON COLUMN trade_data_log.tid IS '原始ID';
COMMENT ON COLUMN trade_data_log.keywords IS '关键字';
COMMENT ON COLUMN trade_data_log.origin IS '来源 iMES、MES、INFOR';
COMMENT ON COLUMN trade_data_log.customer_name IS '客户名称';
COMMENT ON COLUMN trade_data_log.project_name IS '项目名称';
COMMENT ON COLUMN trade_data_log.project_phase IS '项目阶段';
COMMENT ON COLUMN trade_data_log.cooperation_mode IS '合作模式';
COMMENT ON COLUMN trade_data_log.message_type IS '消息类型';
COMMENT ON COLUMN trade_data_log.push_type IS '推送方式';
COMMENT ON COLUMN trade_data_log.craft_section IS '主工序';
COMMENT ON COLUMN trade_data_log.contract_no IS '合同号';
COMMENT ON COLUMN trade_data_log.task_no IS '任务号';
COMMENT ON COLUMN trade_data_log.item_no IS '物料代码';
COMMENT ON COLUMN trade_data_log.sn IS '条码';
COMMENT ON COLUMN trade_data_log.json_data IS '数据';
COMMENT ON COLUMN trade_data_log.err_msg IS '错误数据';
COMMENT ON COLUMN trade_data_log.begin_time IS '开始时间';
COMMENT ON COLUMN trade_data_log.end_time IS '结束时间';
COMMENT ON COLUMN trade_data_log.factory_id IS '工厂ID';
COMMENT ON COLUMN trade_data_log.status IS '状态 PN推B2B失败 PY推B2B成功/未回调  CY回调返回成功 CN回调返回失败';
COMMENT ON COLUMN trade_data_log.remark IS '备注';
COMMENT ON COLUMN trade_data_log.create_by IS '创建人';
COMMENT ON COLUMN trade_data_log.create_date IS '创建日期 默认为当前时间';
COMMENT ON COLUMN trade_data_log.last_updated_by IS '更新人';
COMMENT ON COLUMN trade_data_log.last_updated_date IS '更新日期 默认为当前时间';
COMMENT ON COLUMN trade_data_log.enabled_flag IS '是否有效 选项：Y=正常 N=已删除';

-- 创建分区表
CREATE TABLE if not exists trade_data_log_20251 PARTITION OF trade_data_log FOR VALUES FROM ('2025-01-01') TO ('2025-06-30');
CREATE TABLE if not exists trade_data_log_20252 PARTITION OF trade_data_log FOR VALUES FROM ('2025-07-01') TO ('2025-12-31');
CREATE TABLE if not exists trade_data_log_20261 PARTITION OF trade_data_log FOR VALUES FROM ('2026-01-01') TO ('2026-06-30');
CREATE TABLE if not exists trade_data_log_20262 PARTITION OF trade_data_log FOR VALUES FROM ('2026-07-01') TO ('2026-12-31');
CREATE TABLE if not exists trade_data_log_20271 PARTITION OF trade_data_log FOR VALUES FROM ('2027-01-01') TO ('2027-06-30');
CREATE TABLE if not exists trade_data_log_20272 PARTITION OF trade_data_log FOR VALUES FROM ('2027-07-01') TO ('2027-12-31');