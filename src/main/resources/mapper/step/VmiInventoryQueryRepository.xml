<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.VmiInventoryQueryRepository">
	<resultMap id="VmiSkuInventoryMap" type="com.zte.domain.model.step.VmiSkuInventory">
		<result column="item_no"  jdbcType="VARCHAR" property="itemNo" />
		<result column="item_name"  jdbcType="VARCHAR" property="itemName" />
		<result column="unit"  jdbcType="VARCHAR" property="unit" />
		<result column="stock_no"  jdbcType="VARCHAR" property="warehouseNo" />
		<result column="stock_name"  jdbcType="VARCHAR" property="warehouseName" />
		<result column="org_id"  jdbcType="VARCHAR" property="orgnizationCode" />
		<result column="customer_simple_name"  jdbcType="VARCHAR" property="orgnizationName" />
		<result column="balance_qty"  jdbcType="DECIMAL" property="qty" />
	</resultMap>
	
	
	<select id="selectVmiSkuInvQuery"  resultMap="VmiSkuInventoryMap" parameterType="com.zte.interfaces.step.dto.VmiSkuInventoryQueryDTO">
		SELECT * FROM (select a.*,rownum rn from (SELECT bi.item_no,
       		bi.item_name,
            bi.unit,
       		st.stock_no,
       		sk.stock_name,
       		bc.org_id,
       		bc.customer_simple_name,
            st.balance_qty,
            decode(st.stock_no,'KXHUB_PACK','SUP','HUBZT_NJ','SUP',
            (select max(goods_type) from kxstepiii.infor_step_erp_stock es where st.stock_no=es.step_stock)) storerkey
  		FROM kxstepiii.st_summary st	
  		join kxstepiii.ba_item bi
    	on bi.item_id = st.item_id
 		join kxstepiii.st_stock sk
    	on sk.stock_no = st.stock_no
  		join kxstepiii.ba_customer bc
    	on bc.org_id = sk.organization_id
		where 1=1 and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=ST.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	) a where 1=1
    	<if test=" storerkey !=null and storerkey !='' ">
       		and  a.storerkey=#{storerkey}
    	</if>
    	<if test=" endRow !=null">
       		and rownum &lt;= #{endRow}
    	</if>
    	) b 
    	<if test=" startRow !=null  ">
       		 where b.rn>=#{startRow}
    	</if>
	</select>
	
		
	<select id="selectVmiSkuInvCount"  resultType="java.lang.Long" parameterType="com.zte.interfaces.step.dto.VmiSkuInventoryQueryDTO">
		select count(1) from (SELECT bi.item_no,
       		bi.item_name,
            bi.unit,
       		st.stock_no,
       		sk.stock_name,
       		bc.org_id,
       		bc.customer_simple_name,
            st.balance_qty,
            decode(st.stock_no,'KXHUB_PACK','SUP','HUBZT_NJ','SUP',
            (select max(goods_type) from kxstepiii.infor_step_erp_stock es where st.stock_no=es.step_stock)) storerkey
  		FROM kxstepiii.st_summary st	
  		join kxstepiii.ba_item bi
    	on bi.item_id = st.item_id
 		join kxstepiii.st_stock sk
    	on sk.stock_no = st.stock_no
  		join kxstepiii.ba_customer bc
    	on bc.org_id = sk.organization_id
		where 1=1 and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=ST.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	) a where 1=1
    	<if test=" storerkey !=null and storerkey !='' ">
       		and  a.storerkey=#{storerkey}
    	</if>
	</select>
	
	<resultMap id="VmiUUIDInventoryMap" type="com.zte.domain.model.step.VmiUUIDInventory">
		<result column="supplier_no"  jdbcType="VARCHAR" property="supplyNo" />
		<result column="supplier_name"  jdbcType="VARCHAR" property="supplyName" />
		<result column="brand_style"  jdbcType="VARCHAR" property="brandStyle" />
		<result column="brand_name"  jdbcType="VARCHAR" property="brandName" />
		<result column="item_uuid"  jdbcType="VARCHAR" property="uuid" />
		<result column="item_no"  jdbcType="VARCHAR" property="itemNo" />
		<result column="item_name"  jdbcType="VARCHAR" property="itemName" />
		<result column="unit"  jdbcType="VARCHAR" property="unit" />
		<result column="stock_no"  jdbcType="VARCHAR" property="warehouseNo" />
		<result column="stock_name"  jdbcType="VARCHAR" property="warehouseName" />
		<result column="org_id"  jdbcType="VARCHAR" property="orgnizationCode" />
		<result column="customer_simple_name"  jdbcType="VARCHAR" property="orgnizationName" />
		<result column="balance_qty"  jdbcType="DECIMAL" property="qty" />
	</resultMap>
	
	
	<select id="selectVmiUUIDInvQuery"  resultMap="VmiUUIDInventoryMap" parameterType="com.zte.interfaces.step.dto.VmiUUIDInventoryQueryDTO">
	   select  * from (select a.*,rownum rn from (SELECT sis.stock_no,
                       max(st.stock_name) stock_name,
                       sib.item_uuid,
                       max(bi.unit) unit,
                       bi.item_no item_no,
                       max(bi.item_name) item_name,
                       max(sib.supplier_no) supplier_no,
                       max(bs.supplier_name) supplier_name,
                       (select max(bib.brand_no)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_no,
                       (select max(bb.brand_name)
                          from kxstepiii.ba_brand bb
                         where bb.brand_no =
                               (select bib.brand_no
                                  from kxstepiii.ba_item_brandstyle bib
                                 where bib.item_uuid = sib.item_uuid)) brand_name,
                       (select max(bib.brand_style)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_style,
                       max(bc.org_id) org_id,
                       max(bc.customer_simple_name) customer_simple_name,
                       sum(sis.balance_qty) balance_qty,
                       decode(sis.stock_no,
                              'KXHUB_PACK',
                              'SUP',
                              'HUBZT_NJ',
                              'SUP',
                              (select max(goods_type)
                                 from kxstepiii.infor_step_erp_stock es
                                where sis.stock_no = es.step_stock)) storerkey
                  FROM kxstepiii.st_itembarcode_stock sis
                  join kxstepiii.st_stock st
                    on sis.stock_no = st.stock_no
                  join kxstepiii.ba_customer bc
                    on bc.org_id = st.organization_id
                  join kxstepiii.st_item_barcode sib
                    on sib.item_barcode = sis.item_barcode
                  join kxstepiii.ba_item bi
                    on bi.item_id = sib.item_id
                  left join kxstepiii.ba_supplier bs
                    on bs.supplier_no = sib.supplier_no
                 where 1 = 1  and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=SIS.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" supplyNo !=null and supplyNo !='' ">
       		and bs.supplier_no=#{supplyNo}
    	</if>
        
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	group by sis.stock_no,bi.item_no,sib.item_uuid ) a
    	where  1=1
    	<if test=" storerkey !=null and storerkey !='' ">
       		and   a.storerkey=#{storerkey}
    	</if>
    	<if test=" endRow !=null  ">
       		 and rownum &lt;= #{endRow}
    	</if>
    	) b 
    	<if test=" startRow !=null  ">
       		 where b.rn>=#{ startRow }
    	</if>
	</select>
		
	<select id="selectVmiUUIDInvCount"  resultType="java.lang.Long" parameterType="com.zte.interfaces.step.dto.VmiUUIDInventoryQueryDTO">
	   select count(1) from (SELECT sis.stock_no,
                       max(st.stock_name) stock_name,
                       sib.item_uuid,
                       max(bi.unit) unit,
                       bi.item_no item_no,
                       max(bi.item_name) item_name,
                       max(sib.supplier_no) supplier_no,
                       max(bs.supplier_name) supplier_name,
                       (select max(bib.brand_no)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_no,
                       (select max(bb.brand_name)
                          from kxstepiii.ba_brand bb
                         where bb.brand_no =
                               (select bib.brand_no
                                  from kxstepiii.ba_item_brandstyle bib
                                 where bib.item_uuid = sib.item_uuid)) brand_name,
                       (select max(bib.brand_style)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_style,
                       max(bc.org_id) org_id,
                       max(bc.customer_simple_name) customer_simple_name,
                       sum(sis.balance_qty) balance_qty,
                       decode(sis.stock_no,
                              'KXHUB_PACK',
                              'SUP',
                              'HUBZT_NJ',
                              'SUP',
                              (select max(goods_type)
                                 from kxstepiii.infor_step_erp_stock es
                                where sis.stock_no = es.step_stock)) storerkey
                  FROM kxstepiii.st_itembarcode_stock sis
                  join kxstepiii.st_stock st
                    on sis.stock_no = st.stock_no
                  join kxstepiii.ba_customer bc
                    on bc.org_id = st.organization_id
                  join kxstepiii.st_item_barcode sib
                    on sib.item_barcode = sis.item_barcode
                  join kxstepiii.ba_item bi
                    on bi.item_id = sib.item_id
                  left join kxstepiii.ba_supplier bs
                    on bs.supplier_no = sib.supplier_no
                 where 1 = 1  and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=SIS.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" supplyNo !=null and supplyNo !='' ">
       		and bs.supplier_no=#{supplyNo}
    	</if>
        
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	group by sis.stock_no,bi.item_no,sib.item_uuid ) a
    	where  1=1
    	<if test=" storerkey !=null and storerkey !='' ">
       		and   a.storerkey=#{storerkey}
    	</if>
	</select>
	
	<resultMap id="VmiDetailInventoryMap" type="com.zte.domain.model.step.VmiDetailInventory">
		<result column="supplier_no"  jdbcType="VARCHAR" property="supplyNo" />
		<result column="supplier_name"  jdbcType="VARCHAR" property="supplyName" />
		<result column="brand_style"  jdbcType="VARCHAR" property="brandStyle" />
		<result column="brand_name"  jdbcType="VARCHAR" property="brandName" />
		<result column="item_uuid"  jdbcType="VARCHAR" property="uuid" />
		<result column="item_no"  jdbcType="VARCHAR" property="itemNo" />
		<result column="item_name"  jdbcType="VARCHAR" property="itemName" />
		<result column="unit"  jdbcType="VARCHAR" property="unit" />
		<result column="stock_no"  jdbcType="VARCHAR" property="warehouseNo" />
		<result column="stock_name"  jdbcType="VARCHAR" property="warehouseName" />
		<result column="org_id"  jdbcType="VARCHAR" property="orgnizationCode" />
		<result column="customer_simple_name"  jdbcType="VARCHAR" property="orgnizationName" />
		<result column="balance_qty"  jdbcType="DECIMAL" property="qty" />
		<result column="item_barcode"  jdbcType="VARCHAR" property="itembarcode" />
		<result column="product_no"  jdbcType="VARCHAR" property="productNo" />
		<result column="come_date"  jdbcType="VARCHAR" property="instoreDate" />
	</resultMap>	
	
	<select id="selectVmiDetailInvQuery"  resultMap="VmiDetailInventoryMap"
            parameterType="com.zte.interfaces.step.dto.VmiDetailInventoryQueryDTO">
	   select  * from (select a.*,rownum rn from (SELECT sis.ITEM_BARCODE,
       sis.stock_no,
       
       st.stock_name stock_name,
       
       sib.item_uuid,
       
       bi.unit unit,
       
       bi.item_no item_no,
       
       bi.item_name item_name,
       
       sib.supplier_no supplier_no,
       
       sib.product_no,
       
       bs.supplier_name supplier_name,
       
       (select max(bib.brand_no)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_no,
                         
       (select max(bb.brand_name)
                          from kxstepiii.ba_brand bb
                         where bb.brand_no =
                               (select bib.brand_no
                                  from kxstepiii.ba_item_brandstyle bib
                                 where bib.item_uuid = sib.item_uuid)) brand_name,
                                 
       (select max(bib.brand_style) from kxstepiii.ba_item_brandstyle bib where bib.item_uuid = sib.item_uuid ) brand_style,
       
       bc.org_id org_id,
       
       bc.customer_simple_name customer_simple_name,
       
       sis.balance_qty balance_qty,
       
       decode(sis.stock_no,
              'KXHUB_PACK',
              'SUP',
              'HUBZT_NJ',
              'SUP',
              (select max(goods_type)
                 from kxstepiii.infor_step_erp_stock es
                where sis.stock_no = es.step_stock)) storerkey,
       
       to_char(sib.come_date, 'yyyy-mm-dd HH24:mi:ss') come_date

  FROM kxstepiii.st_itembarcode_stock sis

  join kxstepiii.st_stock st

    on sis.stock_no = st.stock_no

  join kxstepiii.ba_customer bc

    on bc.org_id = st.organization_id

  join kxstepiii.st_item_barcode sib

    on sib.item_barcode = sis.item_barcode

  join kxstepiii.ba_item bi

    on bi.item_id = sib.item_id

  left join kxstepiii.ba_supplier bs

    on bs.supplier_no = sib.supplier_no

 where 1 = 1   and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=SIS.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        
        <if test=" supplyNo !=null and supplyNo !='' ">
       		and bs.supplier_no=#{supplyNo}
    	</if>
        <if test=" uuid !=null and uuid !='' ">
       		and sib.item_uuid=#{uuid}
    	</if>
    	<if test=" itembarcode !=null and itembarcode !='' ">
       		and sis.ITEM_BARCODE=#{itembarcode}
    	</if>
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	) a where 1=1
    	
    	<if test=" storerkey !=null and storerkey !='' ">
       		 and  a.storerkey=#{storerkey}
    	</if>
    	<if test=" endRow !=null  ">
       		 and rownum &lt;= #{endRow}
    	</if>
    	) b 
    	<if test=" startRow !=null  ">
       		 where b.rn>=#{ startRow }
    	</if>
	</select>
	
	
		
	<select id="selectVmiDetailInvCount"  resultType="java.lang.Long" parameterType="com.zte.interfaces.step.dto.VmiDetailInventoryQueryDTO">
	   select count(1) from (SELECT sis.ITEM_BARCODE,
       sis.stock_no,
       
       st.stock_name stock_name,
       
       sib.item_uuid,
       
       bi.unit unit,
       
       bi.item_no item_no,
       
       bi.item_name item_name,
       
       sib.supplier_no supplier_no,
       
       sib.product_no,
       
       bs.supplier_name supplier_name,
       
       (select max(bib.brand_no)
                          from kxstepiii.ba_item_brandstyle bib
                         where bib.item_uuid = sib.item_uuid) brand_no,
                         
       (select max(bb.brand_name)
                          from kxstepiii.ba_brand bb
                         where bb.brand_no =
                               (select bib.brand_no
                                  from kxstepiii.ba_item_brandstyle bib
                                 where bib.item_uuid = sib.item_uuid)) brand_name,
                                 
       (select max(bib.brand_style) from kxstepiii.ba_item_brandstyle bib where bib.item_uuid = sib.item_uuid ) brand_style,
       
       bc.org_id org_id,
       
       bc.customer_simple_name customer_simple_name,
       
       sis.balance_qty balance_qty,
       
       decode(sis.stock_no,
              'KXHUB_PACK',
              'SUP',
              'HUBZT_NJ',
              'SUP',
              (select max(goods_type)
                 from kxstepiii.infor_step_erp_stock es
                where sis.stock_no = es.step_stock)) storerkey,
       
       to_char(sib.come_date, 'yyyy-mm-dd HH24:mi:ss') come_date

  FROM kxstepiii.st_itembarcode_stock sis

  join kxstepiii.st_stock st

    on sis.stock_no = st.stock_no

  join kxstepiii.ba_customer bc

    on bc.org_id = st.organization_id

  join kxstepiii.st_item_barcode sib

    on sib.item_barcode = sis.item_barcode

  join kxstepiii.ba_item bi

    on bi.item_id = sib.item_id

  left join kxstepiii.ba_supplier bs

    on bs.supplier_no = sib.supplier_no

 where 1 = 1   and  EXISTS (SELECT 1  FROM kxstepiii.ba_period  BP WHERE BP.STATUS=1 AND BP.PERIOD_NUMBER=SIS.PERIOD_NUMBER )
        <if test=" itemNo.size>0 ">
            and bi.item_no in
            <foreach collection="itemNo" item="tmp" index="index" open="(" close=")" separator=",">
                #{tmp, jdbcType=VARCHAR}
            </foreach>
        </if>
        
        <if test=" supplyNo !=null and supplyNo !='' ">
       		and bs.supplier_no=#{supplyNo}
    	</if>
        <if test=" uuid !=null and uuid !='' ">
       		and sib.item_uuid=#{uuid}
    	</if>
    	<if test=" itembarcode !=null and itembarcode !='' ">
       		and sis.ITEM_BARCODE=#{itembarcode}
    	</if>
        <if test=" warehouseNo !=null and warehouseNo !='' ">
       		and st.stock_no=#{warehouseNo}
    	</if>
        <if test=" orgnizationCode !=null and orgnizationCode !='' ">
       		and bc.org_id=#{orgnizationCode}
    	</if>
    	) a where 1=1
    	
    	<if test=" storerkey !=null and storerkey !='' ">
       		 and  a.storerkey=#{storerkey}
    	</if>
	</select>
		
</mapper>