<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.PdoOmsRepository">

    <!-- 批量插入PDO明细 -->
    <insert id="batchInsertPdoDetail">
        INSERT INTO kxstepiii.PDO_ORDERPLAN_DETAIL (
            ID,
            PDO_NO,
            ITEM_NO,
            REQ_QTY,
            DELIONROAD_QTY,
            ISSUED_QTY,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATED_DATE,
            ENABLED
        )
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.id},
                #{item.pdoBill},
                #{item.itemNo},
                #{item.reqQty},
                0,
                0,
                'SYSTEM',
                SYSDATE,
                'SYSTEM',
                SYSDATE,
                1
            FROM DUAL
        </foreach>
    </insert>

</mapper> 