<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OmsTransferRepository">

    <select id="getTransferBillIqcInfo" parameterType="com.zte.interfaces.step.dto.TransferDetailDTO" resultType="com.zte.interfaces.step.dto.TransferDetailDTO">
        select b.bill_no billNo, b.status, b.stock_no stockNo, sib.supplier_no supplierNo, bs.supplier_name supplierName,
        d.detail_id lineId, decode(bi.barcode_control_type, '30', '02', '01') barcodeControlType, sib.item_uuid itemUuid,
        d.item_no itemNo,bi.item_name itemName,d.item_barcode itemBarcode, bib.brand_no brandNo, bb.brand_name brandName,
        bib.brand_style brandStyle, sib.is_lead envProperty, sib.product_no batchNo,to_char(sib.product_date,'yyyy-mm-dd') productionDate,
        d.plan_qty deliveryQty
        from kxstepiii.infor_transfer_detail d
        join kxstepiii.infor_transfer_bill b on d.bill_id = b.bill_id
        left join kxstepiii.st_item_barcode sib on d.item_barcode = sib.item_barcode
        left join kxstepiii.ba_supplier bs on bs.supplier_no = sib.supplier_no
        left join kxstepiii.ba_item bi on bi.item_no = d.item_no
        left join KXSTEPIII.BA_ITEM_BRANDSTYLE bib on bib.item_uuid = sib.item_uuid
        left join KXSTEPIII.Ba_Brand bb on bb.brand_no = bib.brand_no
        where
        b.bill_no = #{billNo, jdbcType=VARCHAR}
        and b.enabled = 1
        and d.enabled = 1
    </select>

    <update id="updateTransferDetailBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" separator=";" close="; end;">
            update kxstepiii.infor_transfer_detail
            set
            <if test="item.checkParams != null and item.checkParams != ''">
                check_params = #{item.checkParams, jdbcType=CLOB},
            </if>
            <if test="item.checkParams != null and item.checkParams != ''">
                check_result = #{item.checkResult, jdbcType=CLOB},
            </if>
            <if test="item.checkStatus != null and item.checkStatus != ''">
                check_status = #{item.checkStatus, jdbcType=VARCHAR},
            </if>
            <if test="item.rightQty != null ">
                right_qty = #{item.rightQty, jdbcType=DECIMAL},
            </if>
            <if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">
                last_updated_by = #{item.lastUpdatedBy, jdbcType=VARCHAR},
            </if>
            last_updated_date = sysdate
            where detail_id = #{item.lineId, jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateTransferHead" parameterType="com.zte.interfaces.step.dto.TransferDetailDTO">
        update kxstepiii.infor_transfer_bill
        set status = #{status, jdbcType=VARCHAR},
        last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
        last_updated_date = sysdate
        where bill_no = #{billNo, jdbcType=VARCHAR}
    </update>

    <update id="updateTransferDetail" parameterType="com.zte.interfaces.step.dto.TransferDetailDTO">
        update kxstepiii.infor_transfer_detail
        set
        <if test="checkParams != null and checkParams != ''">
            check_params = #{checkParams, jdbcType=CLOB},
        </if>
        <if test="checkParams != null and checkParams != ''">
            check_result = #{checkResult, jdbcType=CLOB},
        </if>
        <if test="checkStatus != null and checkStatus != ''">
            check_status = #{checkStatus, jdbcType=VARCHAR},
        </if>
        <if test="rightQty != null ">
            right_qty = #{rightQty, jdbcType=DECIMAL},
        </if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
            last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
        </if>
        last_updated_date = sysdate
        where detail_id = #{lineId, jdbcType=VARCHAR}
    </update>

    <select id="getTransferBillInfo" parameterType="com.zte.interfaces.step.dto.TransferDetailDTO" resultType="com.zte.interfaces.step.dto.TransferDetailDTO">
        select b.bill_no billNo, b.status, b.stock_no stockNo, d.detail_id lineId, d.item_no itemNo, d.item_barcode itemBarcode,
        d.plan_qty deliveryQty, d.check_status checkStatus, d.right_qty rightQty
        from kxstepiii.infor_transfer_detail d
        join kxstepiii.infor_transfer_bill b on d.bill_id = b.bill_id
        where
        b.bill_no = #{billNo, jdbcType=VARCHAR}
        and b.enabled = 1
        and d.enabled = 1
    </select>

    <select id="getTransferSoHead" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader" parameterType="java.lang.String">
        SELECT '' AS ORDERKEY,
        '' AS EXTERNORDERKEY,
        '11' AS TYPE,
        'ZTE' AS STORERKEY,
        T.MAKED_DATE AS REQUESTEDSHIPDATE,
        T.BILL_NO AS EXTERNALORDERKEY2,
        '' AS CONSIGNEEKEY,
        '' AS ORDERSID,
        '' AS PROCESS_FLAG,
        '' AS ERROR_MESSAGE,
        '' AS HSUSR1,
        '' AS HSUSR2,
        '' AS HSUSR3,
        '' AS HSUSR4,
        '' AS HSUSR5,
        '' AS HREF01,
        '' AS HREF02,
        '' AS HREF03,
        '' AS HREF04,
        '' AS HREF05,
        '' AS HREF06,
        '' AS HREF07,
        '' AS HREF08,
        '' AS HREF09,
        '' AS HREF10,
        T.INFOR_TYPE AS HREF11,
        '' AS HREF12,
        '' AS HREF13,
        T.STOCK_NO AS HREF14,
        DECODE(T.DEST_STOCK_NO ,'-1',NULL,T.DEST_STOCK_NO ) AS HREF15,
        '1' AS HREF16,
        T.MAKED_BY AS HREF17,
        '' AS HREF18,
        '' AS HREF19,
        '' AS HREF20,
        '' AS HREF21,
        '' AS HREF22,
        '' AS HREF23,
        '' AS HREF24,
        T.REMARK AS HREF25,
        '' AS HREF26,
        '' AS HREF27,
        '' AS HREF28,
        '' AS HREF29,
        T.MAKED_DATE AS HREF30,
        T.MAKED_BY AS HREF31,
        '' AS HREF32,
        '' AS HREF33,
        '' AS HREF34,
        '' AS HREF35,
        '' AS HREF36,
        '' AS HREF37,
        '' AS HREF38,
        T.DELIVERYTO AS HREF39,
        '' AS HREF40,
        '' AS HREF41,
        '' AS HREF42,
        '' AS HREF43,
        '' AS HREF44,
        '' AS HREF45,
        '' AS HREF46,
        '' AS HREF47,
        '' AS HREF48,
        '' AS HREF49,
        NVL(T.ACCOUNT_TYPE,'N') AS HREF50,
        '' AS HREF51,
        '' AS HREF52,
        (SELECT T.CODE_DESC FROM KXSTEPIII.OP_BASIC_CODEINFO T WHERE T.CODE = T.DEST_DELI_ADDRESS AND T.CODE_TYPE = '交货地点') AS HREF53,
        '' AS HREF54,
        '' AS HREF55,
        '' AS HREF56,
        '' AS HREF57,
        '' AS HREF58,
        T.ISTEXT AS HREF59,
        '' HREF60,
        '' AS LAST_UPDATED_DATE
        FROM KXSTEPIII.INFOR_TRANSFER_BILL T
        WHERE T.ENABLED = 1
        AND T.BILL_NO = #{billNo,jdbcType=VARCHAR}
    </select>

    <select id="getTransferSoDetail" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail" parameterType="java.lang.String">
        select itb.BILL_NO AS EXTERNALORDERKEY2,
        itb.STOCK_NO AS WHSEID,
        '' ORDERLINENUMBER,
        to_char(itd.DETAIL_ID) EXTERNLINENO,
        itd.ITEM_NO SKU,
        'SKU' PACKKEY,
        itd.RIGHT_QTY ORIGINALQTY,
        0 SHIPPEDQTY,
        '' UOM,
        0 ALLOWOVERPICK,
        '' PREALLOCATESTRATEGYKEY,
        '' ALLOCATESTRATEGYKEY,
        '' ALLOCATESTRATEGYTYPE,
        0 SHELFLIFE,
        '' ROTATION,
        '' SKUROTATION,
        '' LOTTABLE01,
        itd.ITEM_BARCODE LOTTABLE02,
        '' LOTTABLE03,
        '' LOTTABLE04,
        '' LOTTABLE05,
        DECODE(ITB.BILL_TYPE, '03', ITD.OWNER_CLASS_NO, '02', ITD.DST_OWNER_CLASS,
        DECODE(ITB.INS_TYPE, '01', 'KX', '02', SIB.SUPPLIER_NO, '03', DECODE(INSTR(ITB.STOCK_NO || ITB.DEST_STOCK_NO, 'HUB', 1, 1), 0, 'KX', SIB.SUPPLIER_NO))) LOTTABLE06,
        (select to_char(ITEM_UUID) from kxstepiii.st_item_barcode where ITEM_BARCODE = itd.ITEM_BARCODE) LOTTABLE07,
        '' LOTTABLE08,
        '' LOTTABLE09,
        '' LOTTABLE10,
        '' LOTTABLE11,
        '' LOTTABLE12,
        '' SUSR1,
        (select to_char(ITEM_ID) from kxstepiii.ba_item where ITEM_NO = itd.ITEM_NO) SUSR2,
        '' SUSR3,
        '' SUSR4,
        '' SUSR5,
        '' NOTES2,
        '' NOTES,
        to_char(itd.PLAN_QTY) REF01,
        '' REF02,
        '' REF03,
        '' REF04,
        '' REF05,
        '' REF06,
        '' REF07,
        '' REF08,
        (select to_char(SUPPLIER_NO) from kxstepiii.st_item_barcode where ITEM_BARCODE = itd.ITEM_BARCODE) REF09,
        '' REF10,
        '' REF11,
        '' REF12,
        '' REF13,
        '' REF14,
        '' REF15,
        decode(itb.INS_TYPE, '01', '调拨指令', '02', '配送指令', '03', '无') REF16,
        itb.INS_TYPE REF17,
        (SELECT OP.PRODUCT_CLASS_NAME FROM KXSTEPIII.OP_PRODUCTINFO OP WHERE itd.PURPOSE_BIGCLASS = OP.PRODUCT_CLASS AND op.entp_no = 'S' AND ROWNUM = 1) REF18,
        itd.PURPOSE_BIGCLASS REF19,
        (select DPP.PLANNING_GROUP_DESC from LMS.DB_PRODCLASS_PLANGROUP DPP WHERE itd.PURPOSE_PLAN = DPP.PLANNING_GROUP AND ROWNUM = 1) REF20,
        itd.PURPOSE_PLAN REF21,
        '' REF22,
        '' REF23,
        '' REF24,
        TO_CHAR(itd.DELIVERY_ID) REF25,
        '' REF26,
        '' REF27,
        '' REF28,
        '' REF29,
        '' REF30,
        (SELECT DISTINCT op.product_class_name FROM kxstepiii.op_productinfo op WHERE op.product_class = itd.source_bigclass AND op.entp_no = 'S' AND rownum = 1) REF31,
        itd.SOURCE_BIGCLASS REF32,
        (SELECT DISTINCT dpp.planning_group_desc FROM lms.db_prodclass_plangroup dpp WHERE dpp.planning_group = itd.source_plan AND dpp.entp_no = 'S' AND rownum = 1) REF33,
        itd.SOURCE_PLAN REF34,
        '' REF35,
        (select to_char(max(t.project_id)) From lms.db_prodclass_plangroup t where t.product_class = itd.source_bigclass and t.entp_no = 'S') REF36,
        '' REF37,
        (select to_char(max(t.project_id)) From lms.db_prodclass_plangroup t where t.product_class = itd.purpose_bigclass and t.entp_no = 'S') REF38,
        '' REF39,
        '' REF40,
        '' REF41,
        '' REF42,
        '' REF43,
        '' REF44,
        '' REF45,
        DECODE(ITB.SRC_AREA, NULL, NULL, (SELECT CODE_DESC FROM KXSTEPIII.ST_CODEINFO SCI WHERE SCI.CODE_TYPE = 'INFOR区域' AND SCI.CODE = ITB.SRC_AREA)) REF46,
        '' REF47,
        '' REF48,
        '' REF49,
        '' REF50,
        DECODE(ITB.DEST_AREA, NULL, NULL, (SELECT CODE_DESC FROM KXSTEPIII.ST_CODEINFO SCI WHERE SCI.CODE_TYPE = 'INFOR区域' AND SCI.CODE = ITB.DEST_AREA)) REF51,
        '' REF52,
        '' REF53,
        '' REF54,
        '' REF55,
        '' REF56,
        '' REF57,
        '' REF58,
        '' REF59,
        '' REF60
        from KXSTEPIII.INFOR_TRANSFER_DETAIL itd
        JOIN kxstepiii.infor_transfer_bill itb ON itd.bill_id = itb.bill_id
        LEFT JOIN kxstepiii.st_item_barcode sib ON itd.item_barcode =
        sib.item_barcode
        WHERE itd.enabled = 1
        and itb.BILL_NO = #{billNo,jdbcType=VARCHAR}
        and itd.RIGHT_QTY > 0
    </select>

    <insert id="insertTransferSoHead" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            insert into KXSTEPIII.INFOR_WMS_SOHEAD
            (ORDERKEY,EXTERNORDERKEY,STORERKEY,REQUESTEDSHIPDATE,EXTERNALORDERKEY2,CONSIGNEEKEY,ORDERSID,HSUSR1,HSUSR2,HSUSR3,HSUSR4,HSUSR5,HREF01,HREF02,HREF03,HREF04,HREF05,HREF06,HREF07,HREF08,HREF09,HREF10,HREF11,HREF12,HREF13,HREF14,HREF15,HREF16,HREF17,HREF18,HREF19,HREF20,HREF21,HREF22,HREF23,HREF24,HREF25,HREF26,HREF27,HREF28,HREF29,HREF30,HREF31,HREF32,HREF33,HREF34,HREF35,HREF36,HREF37,HREF38,HREF39,HREF40,HREF41,HREF42,HREF43,HREF44,HREF45,HREF46,HREF47,HREF48,HREF49,HREF50,HREF51,HREF52,HREF53,HREF54,HREF55,HREF56,HREF57,HREF58,HREF59,HREF60,PROCESS_FLAG,TYPE,ERROR_MESSAGE)
            values
            (#{item.orderKey,jdbcType=VARCHAR}, #{item.externOrderKey,jdbcType=VARCHAR},
            #{item.storerKey,jdbcType=VARCHAR},TO_DATE(#{item.requestedShipDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'),
            #{item.externalOrderKey2,jdbcType=VARCHAR},#{item.consigneeKey,jdbcType=VARCHAR},
            #{item.ordersId,jdbcType=VARCHAR},#{item.hsusr1,jdbcType=VARCHAR},
            #{item.hsusr2,jdbcType=VARCHAR},#{item.hsusr3,jdbcType=VARCHAR},
            #{item.hsusr4,jdbcType=VARCHAR},#{item.hsusr5,jdbcType=VARCHAR},
            #{item.href01,jdbcType=VARCHAR},#{item.href02,jdbcType=VARCHAR},
            #{item.href03,jdbcType=VARCHAR},#{item.href04,jdbcType=VARCHAR},
            #{item.href05,jdbcType=VARCHAR},#{item.href06,jdbcType=VARCHAR},
            #{item.href07,jdbcType=VARCHAR},#{item.href08,jdbcType=VARCHAR},
            #{item.href09,jdbcType=VARCHAR},#{item.href10,jdbcType=VARCHAR},
            #{item.href11,jdbcType=VARCHAR},#{item.href12,jdbcType=VARCHAR},
            #{item.href13,jdbcType=VARCHAR},#{item.href14,jdbcType=VARCHAR},
            #{item.href15,jdbcType=VARCHAR},#{item.href16,jdbcType=VARCHAR},
            #{item.href17,jdbcType=VARCHAR},#{item.href18,jdbcType=VARCHAR},
            #{item.href19,jdbcType=VARCHAR},#{item.href20,jdbcType=VARCHAR},
            #{item.href21,jdbcType=VARCHAR},#{item.href22,jdbcType=VARCHAR},
            #{item.href23,jdbcType=VARCHAR},#{item.href24,jdbcType=VARCHAR},
            #{item.href25,jdbcType=VARCHAR},TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE(#{item.href30, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'),
            #{item.href31,jdbcType=VARCHAR},#{item.href32,jdbcType=VARCHAR},
            #{item.href33,jdbcType=VARCHAR},#{item.href34,jdbcType=VARCHAR},
            #{item.href35,jdbcType=VARCHAR},#{item.href36,jdbcType=VARCHAR},
            #{item.href37,jdbcType=VARCHAR},#{item.href38,jdbcType=VARCHAR},
            #{item.href39,jdbcType=VARCHAR},#{item.href40,jdbcType=VARCHAR},
            #{item.href41,jdbcType=VARCHAR},#{item.href42,jdbcType=VARCHAR},
            #{item.href43,jdbcType=VARCHAR},#{item.href44,jdbcType=VARCHAR},
            #{item.href45,jdbcType=VARCHAR},#{item.href46,jdbcType=VARCHAR},
            #{item.href47,jdbcType=VARCHAR},#{item.href48,jdbcType=VARCHAR},
            #{item.href49,jdbcType=VARCHAR},#{item.href50,jdbcType=VARCHAR},
            #{item.href51,jdbcType=VARCHAR},#{item.href52,jdbcType=VARCHAR},
            #{item.href53,jdbcType=VARCHAR},#{item.href54,jdbcType=VARCHAR},
            #{item.href55,jdbcType=VARCHAR},#{item.href56,jdbcType=VARCHAR},
            #{item.href57,jdbcType=VARCHAR},#{item.href58,jdbcType=VARCHAR},
            #{item.href59,jdbcType=VARCHAR},#{item.href60,jdbcType=VARCHAR},3,
            #{item.type,jdbcType=VARCHAR},'')
        </foreach>
    </insert>

    <insert id="insertTransferSoDetail" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            insert into KXSTEPIII.INFOR_WMS_SODETAIL
            (EXTERNLINENO,SKU,ORIGINALQTY,LOTTABLE01,LOTTABLE02,LOTTABLE03,LOTTABLE04,LOTTABLE05,LOTTABLE06,LOTTABLE07,LOTTABLE08,LOTTABLE09,LOTTABLE10,LOTTABLE11,LOTTABLE12,SUSR1,SUSR2,SUSR3,SUSR4,SUSR5,NOTES2,NOTES,REF01,REF02,REF03,REF04,REF05,REF06,REF07,REF08,REF09,REF10,REF11,REF12,REF13,REF14,REF15,REF16,REF17,REF18,REF19,REF20,REF21,REF22,REF23,REF24,REF25,REF26,REF27,REF28,REF29,REF30,REF31,REF32,REF33,REF34,REF35,REF36,REF37,REF38,REF39,REF40,REF41,REF42,REF43,REF44,REF45,REF46,REF47,REF48,REF49,REF50,REF51,REF52,REF53,REF54,REF55,REF56,REF57,REF58,REF59,REF60,EXTERNALORDERKEY2,ORDERLINENUMBER,WHSEID,PACKKEY,SHIPPEDQTY,UOM,ALLOWOVERPICK,PREALLOCATESTRATEGYKEY,ALLOCATESTRATEGYKEY,ALLOCATESTRATEGYTYPE,SHELFLIFE,ROTATION,SKUROTATION)
            values
            (#{item.externLineNo,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR},
            #{item.originalQty,jdbcType=VARCHAR}, #{item.lottable01,jdbcType=VARCHAR},
            #{item.lottable02,jdbcType=VARCHAR}, #{item.lottable03,jdbcType=VARCHAR},
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            #{item.lottable06,jdbcType=VARCHAR}, #{item.lottable07,jdbcType=VARCHAR},
            #{item.lottable08,jdbcType=VARCHAR}, #{item.lottable09,jdbcType=VARCHAR},
            #{item.lottable10,jdbcType=VARCHAR},  TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), #{item.susr1,jdbcType=VARCHAR},
            #{item.susr2,jdbcType=VARCHAR}, #{item.susr3,jdbcType=VARCHAR},
            #{item.susr4,jdbcType=VARCHAR}, #{item.susr5,jdbcType=VARCHAR},
            #{item.notes2,jdbcType=VARCHAR}, #{item.notes,jdbcType=VARCHAR},
            #{item.ref01,jdbcType=VARCHAR}, #{item.ref02,jdbcType=VARCHAR},
            #{item.ref03,jdbcType=VARCHAR}, #{item.ref04,jdbcType=VARCHAR},
            #{item.ref05,jdbcType=VARCHAR}, #{item.ref06,jdbcType=VARCHAR},
            #{item.ref07,jdbcType=VARCHAR}, #{item.ref08,jdbcType=VARCHAR},
            #{item.ref09,jdbcType=VARCHAR}, #{item.ref10,jdbcType=VARCHAR},
            #{item.ref11,jdbcType=VARCHAR}, #{item.ref12,jdbcType=VARCHAR},
            #{item.ref13,jdbcType=VARCHAR}, #{item.ref14,jdbcType=VARCHAR},
            #{item.ref15,jdbcType=VARCHAR}, #{item.ref16,jdbcType=VARCHAR},
            #{item.ref17,jdbcType=VARCHAR}, #{item.ref18,jdbcType=VARCHAR},
            #{item.ref19,jdbcType=VARCHAR}, #{item.ref20,jdbcType=VARCHAR},
            #{item.ref21,jdbcType=VARCHAR}, #{item.ref22,jdbcType=VARCHAR},
            #{item.ref23,jdbcType=VARCHAR}, #{item.ref24,jdbcType=VARCHAR},
            #{item.ref25,jdbcType=VARCHAR},  TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            #{item.ref31,jdbcType=VARCHAR}, #{item.ref32,jdbcType=VARCHAR},
            #{item.ref33,jdbcType=VARCHAR}, #{item.ref34,jdbcType=VARCHAR},
            #{item.ref35,jdbcType=VARCHAR}, #{item.ref36,jdbcType=VARCHAR},
            #{item.ref37,jdbcType=VARCHAR}, #{item.ref38,jdbcType=VARCHAR},
            #{item.ref39,jdbcType=VARCHAR}, #{item.ref40,jdbcType=VARCHAR},
            #{item.ref41,jdbcType=VARCHAR}, #{item.ref42,jdbcType=VARCHAR},
            #{item.ref43,jdbcType=VARCHAR}, #{item.ref44,jdbcType=VARCHAR},
            #{item.ref45,jdbcType=VARCHAR}, #{item.ref46,jdbcType=VARCHAR},
            #{item.ref47,jdbcType=VARCHAR}, #{item.ref48,jdbcType=VARCHAR},
            #{item.ref49,jdbcType=VARCHAR}, #{item.ref50,jdbcType=VARCHAR},
            #{item.ref51,jdbcType=VARCHAR}, #{item.ref52,jdbcType=VARCHAR},
            #{item.ref53,jdbcType=VARCHAR}, #{item.ref54,jdbcType=VARCHAR},
            #{item.ref55,jdbcType=VARCHAR}, #{item.ref56,jdbcType=VARCHAR},
            #{item.ref57,jdbcType=VARCHAR}, #{item.ref58,jdbcType=VARCHAR},
            #{item.ref59,jdbcType=VARCHAR}, #{item.ref60,jdbcType=VARCHAR},
            #{item.ref60,jdbcType=VARCHAR}, '',
            #{item.whseid,jdbcType=VARCHAR}, #{item.packKey,jdbcType=VARCHAR},
            #{item.shippedQty,jdbcType=VARCHAR}, #{item.uom,jdbcType=VARCHAR},
            #{item.allowOverPick,jdbcType=VARCHAR}, #{item.preAllocateStrategyKey,jdbcType=VARCHAR},
            #{item.allocateStrategyKey,jdbcType=VARCHAR}, #{item.allocateStrategyType,jdbcType=VARCHAR},
            #{item.shelfLife,jdbcType=VARCHAR}, #{item.rotation,jdbcType=VARCHAR}, #{item.skuRotation,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>