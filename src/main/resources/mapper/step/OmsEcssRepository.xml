<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OmsEcssRepository">

	<select id="getAddingBillNo" parameterType="com.zte.interfaces.step.dto.EcssBillDTO" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select distinct ei.in_guid referenceNumber, 0 failNum, 'N' enabledFlag, ei.sale_order_org saleOrderOrg
        from kxstepiii.ecss_items ei
        where
        not exists (
        select 1 from kxstepiii.ecss_items t where ei.in_guid = t.in_guid and t.code_status != 'ECSSMaterialAdded'
        )
        and not exists (
        select 1 from  kxstepiii.ecss_bill eb where ei.in_guid = eb.reference_number
        )
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and ei.in_guid=#{referenceNumber}
        </if>
        union all
        select distinct eb.reference_number referenceNumber, eb.fail_num failNum, eb.enabled_flag enabledFlag, eb.sale_order_org saleOrderOrg
        from kxstepiii.ecss_bill eb
        where
        not exists (
        select 1 from kxstepiii.ecss_items ei where ei.in_guid = eb.reference_number and ei.code_status != 'ECSSMaterialAdded'
        )
        and eb.enabled_flag = 'Y'
        and eb.invoke_flag = 0
        and eb.fail_num <![CDATA[<]]> 4
        and eb.bill_status = 'ECSSBillAdding'
        and eb.send_date <![CDATA[<]]> sysdate - 1/24
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and ei.in_guid=#{referenceNumber}
        </if>
	</select>

	<select id="getMaterialInfo" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select d.item_no || d.version masterDataReferenceNumber,d.qty quantity, d.qty*d.sale_price cost,
        'CNY' currencyCode, h.customer_no customerNo, h.gts_result gtsResult, h.ecss_result ecssResult, h.status
        from kxstepiii.infor_omsales_detail d,
        kxstepiii.infor_omsales_bill h
        where d.bill_id = h.bill_id
        and h.bill_no = #{billNo}
	</select>

    <insert id="insertOrUpdateEcssBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        merge into kxstepiii.ecss_bill a
        using (select #{code,jdbcType=VARCHAR} code, #{name,jdbcType=VARCHAR} name, #{referenceNumber,jdbcType=VARCHAR} reference_number,
        #{documentTypeCode,jdbcType=VARCHAR} document_type_code, #{sendParam,jdbcType=VARCHAR} send_param, #{billStatus,jdbcType=VARCHAR} bill_status,
        #{invokeFlag,jdbcType=INTEGER} invoke_flag, #{failNum,jdbcType=INTEGER} fail_num,#{failReason,jdbcType=VARCHAR} fail_reason,
        #{createdBy,jdbcType=VARCHAR} created_by, #{lastUpdatedBy,jdbcType=VARCHAR} last_updated_by,
        #{saleOrderOrg,jdbcType=INTEGER} sale_order_org from dual) b
        on (a.reference_number = b.reference_number)
        when matched then
        update set a.send_param=b.send_param, a.send_date=sysdate, a.bill_status=b.bill_status, a.invoke_flag=b.invoke_flag,
        a.fail_num=b.fail_num, a.fail_reason=b.fail_reason, a.last_updated_by=b.last_updated_by, a.last_update_date=sysdate,
        a.sale_order_org=b.sale_order_org
        when not matched then
        insert (a.record_id, a.code, a.name, a.reference_number, a.document_type_code, a.send_param, a.send_date, a.bill_status,
        a.invoke_flag, a.fail_num, a.fail_reason, a.creation_date, a.created_by, a.sale_order_org)
        values (kxstepiii.ecss_bill_s.nextval, b.code, b.name, b.reference_number, b.document_type_code,b.send_param,
        sysdate, b.bill_status, b.invoke_flag, b.fail_num, b.fail_reason, sysdate, b.created_by, b.sale_order_org)
    </insert>

    <select id="getEcssLookupInfo" parameterType="com.zte.interfaces.step.dto.StepSysLookupValuesDTO" resultType="com.zte.interfaces.step.dto.StepSysLookupValuesDTO">
        select lookup_type lookupType,lookup_code lookupCode,lookup_meaning lookupMeaning,description,sort_seq sortSeq
        from kxstepiii.sys_lookup_values
        where enabled_flag = 'Y'
        <if test="lookupCode != null and lookupCode !='' ">
            and lookup_code = #{lookupCode,jdbcType=VARCHAR}
        </if>
        <if test="lookupType != null and lookupType !='' ">
            and lookup_type = #{lookupType,jdbcType=VARCHAR}
        </if>
        <if test="description != null and description !='' ">
            and description = #{description,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateEcssBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        update kxstepiii.ecss_bill
        set return_param = #{returnParam,jdbcType=VARCHAR},
            bill_status = #{billStatus,jdbcType=VARCHAR},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="updateInforOmSalesBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        update kxstepiii.infor_omsales_bill
        <set>
            <if test="ecssResult != null">
                ecss_result = #{ecssResult,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            last_updated_date = sysdate
        </set>
        where bill_no = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="failEcssOutputcollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        update kxstepiii.ecss_outputcollection
        set enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <insert id="insertEcssOutputcollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        insert into kxstepiii.ecss_outputcollection
        (record_id, code, reference_number, c_status, s_time, status, creation_date, created_by)
        values
        (kxstepiii.ecss_outputcollection_s.nextval, #{code,jdbcType=VARCHAR}, #{referenceNumber,jdbcType=VARCHAR},
        #{cStatus,jdbcType=VARCHAR}, #{sTime,jdbcType=DATE}, #{status,jdbcType=VARCHAR},
        sysdate, #{createdBy,jdbcType=VARCHAR})
    </insert>

    <update id="failEcssTextretruncollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        update kxstepiii.ecss_textretruncollection
        set enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <insert id="insertEcssTextretruncollection" parameterType="java.util.List">
        insert into kxstepiii.ecss_textretruncollection
        (record_id, reference_number, item_no, law, s_type, f_reason, f_messages, creation_date, created_by)
        select kxstepiii.ecss_textretruncollection_s.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
        select #{item.referenceNumber,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR},#{item.law,jdbcType=VARCHAR},
            #{item.sType,jdbcType=VARCHAR}, #{item.fReason,jdbcType=VARCHAR}, #{item.fMessages,jdbcType=VARCHAR},
            sysdate, #{item.createdBy,jdbcType=VARCHAR} from dual
        </foreach>
        ) temp
    </insert>

</mapper>