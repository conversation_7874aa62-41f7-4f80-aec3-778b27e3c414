<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OmsEcssItemRepository">

    <resultMap id="EcssItemDataMap" type="com.zte.interfaces.step.dto.EcssItemDTO">
        <result column="RECORD_ID" jdbcType="NUMERIC" property="recordId"/>
        <result column="BATCH_ID" jdbcType="VARCHAR" property="batchId"/>
        <result column="INSTANCE_ID" jdbcType="VARCHAR" property="instanceId"/>
        <result column="IN_GUID" jdbcType="VARCHAR" property="inGuid"/>
        <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode"/>
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName"/>
        <result column="ITEM_VERSION" jdbcType="VARCHAR" property="itemVersion"/>
        <result column="IND_FUNC" jdbcType="VARCHAR" javaType="Boolean" typeHandler="com.zte.handler.BooleanTypeHandler" property="indFunc"/>
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode"/>
        <result column="CODE_TYPE" jdbcType="VARCHAR" property="codeType"/>
        <result column="CODE_CATEGORY" jdbcType="VARCHAR" property="codeCategory"/>
        <result column="CODE_STATUS" jdbcType="VARCHAR" property="codeStatus"/>
        <result column="INVOKE_FLAG" jdbcType="INTEGER" property="invokeFlag"/>
        <result column="FAIL_NUM" jdbcType="INTEGER" property="failNum"/>
        <result column="FAIL_REASON" jdbcType="VARCHAR" property="failReason"/>
        <result column="REG_DATE" jdbcType="DATE" property="regDate"/>
        <result column="CREATION_DATE" jdbcType="DATE" property="creationDate"/>
        <result column="CREATED_BY" jdbcType="NUMERIC" property="createdBy"/>
        <result column="LAST_UPDATE_DATE" jdbcType="DATE" property="lastUpdateDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="NUMERIC" property="lastUpdatedBy"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="SALE_ORDER_ORG" jdbcType="INTEGER" property="saleOrderOrg"/>
    </resultMap>

    <select id="getUnregisteredItemList" resultMap="EcssItemDataMap" parameterType="string">
        SELECT *
          FROM KXSTEPIII.ECSS_ITEMS E
         WHERE E.CODE_STATUS != #{codeStatus,jdbcType=VARCHAR}
           AND INVOKE_FLAG = 0
           AND E.FAIL_NUM &lt; 4
           AND (E.REG_DATE IS NULL OR E.REG_DATE &lt; SYSDATE - 1 / 48)
           AND E.ENABLED_FLAG = 'Y'
         ORDER BY E.BATCH_ID
    </select>

    <select id="getItemList" resultMap="EcssItemDataMap" parameterType="com.zte.interfaces.step.dto.EcssItemDTO">
        SELECT *
          FROM KXSTEPIII.ECSS_ITEMS E
        <where>
            INSTANCE_ID = #{instanceId,jdbcType=VARCHAR}
            AND E.ENABLED_FLAG = 'Y'
            <if test="itemCode != null and itemCode != ''">
                AND ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
            </if>
            <if test="itemVersion != null and itemVersion != ''">
                AND ITEM_VERSION = #{itemVersion,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updateFailList" parameterType="string">
        UPDATE KXSTEPIII.ECSS_ITEMS E
           SET E.CODE_STATUS      = #{codeStatus,jdbcType=VARCHAR},
               E.FAIL_NUM         = E.FAIL_NUM + 1,
               E.FAIL_REASON      = #{failReason,jdbcType=VARCHAR},
               E.REG_DATE         = SYSDATE,
               E.LAST_UPDATE_DATE = SYSDATE
         WHERE E.RECORD_ID IN
        <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateRegisterList" parameterType="string">
        UPDATE KXSTEPIII.ECSS_ITEMS E
        <set>
            E.CODE_STATUS = #{codeStatus,jdbcType=VARCHAR},
            E.INVOKE_FLAG = '1',
            E.LAST_UPDATE_DATE = SYSDATE
        </set>
        <where>
            INSTANCE_ID = #{instanceId,jdbcType=VARCHAR}
            <if test="recordIdList != null and recordIdList.size > 0">
                AND E.RECORD_ID NOT IN
                <foreach collection="recordIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </update>

    <update id="updateSuccessList" parameterType="string">
        UPDATE KXSTEPIII.ECSS_ITEMS E
           SET E.CODE_STATUS      = #{codeStatus,jdbcType=VARCHAR},
               E.REG_DATE         = SYSDATE,
               E.IND_FUNC         = #{indFunc,jdbcType=VARCHAR},
               E.INSTANCE_ID      = #{instanceId,jdbcType=VARCHAR},
               E.LAST_UPDATE_DATE = SYSDATE
         WHERE INVOKE_FLAG = 0 AND E.RECORD_ID IN
        <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>