<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StFactoryAddressRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.step.StFactoryAddress">
    <id column="FACTORY_ADDRESS_ID" jdbcType="DECIMAL" property="factoryAddressId" />
    <result column="ORGANIZATION_ID" jdbcType="VARCHAR" property="organizationId" />
    <result column="STOCK_NAME" jdbcType="VARCHAR" property="stockName" />
    <result column="IS_DEFAULT" jdbcType="DECIMAL" property="isDefault" />
    <result column="FAC_COUNTRY_CODE" jdbcType="VARCHAR" property="facCountryCode" />
    <result column="FAC_COUNTRY_NAME" jdbcType="VARCHAR" property="facCountryName" />
    <result column="FAC_PROVINCE_CODE" jdbcType="VARCHAR" property="facProvinceCode" />
    <result column="FAC_PROVINCE_NAME" jdbcType="VARCHAR" property="facProvinceName" />
    <result column="FAC_CITY_CODE" jdbcType="VARCHAR" property="facCityCode" />
    <result column="FAC_CITY_NAME" jdbcType="VARCHAR" property="facCityName" />
    <result column="FAC_AREA_CODE" jdbcType="VARCHAR" property="facAreaCode" />
    <result column="FAC_AREA_NAME" jdbcType="VARCHAR" property="facAreaName" />
    <result column="FAC_ADDRESS_DETAIL" jdbcType="VARCHAR" property="facAddressDetail" />
    <result column="CONTACT_USER" jdbcType="VARCHAR" property="contactUser" />
    <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="INFOR_STOCK" jdbcType="VARCHAR" property="inforStock" />
    <result column="FACTORY_CODE" jdbcType="DECIMAL" property="factoryCode" />
  </resultMap>
  
  <resultMap id="FactoryResultMap" type="com.zte.interfaces.step.dto.StFactoryAddressDTO"> 
    <result column="FACTORY_CODE" jdbcType="DECIMAL" property="factory" />
    <result column="ORGANIZATION_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="STOCK_NAME" jdbcType="VARCHAR" property="stepStock" /> 
    <result column="INFOR_STOCK" jdbcType="VARCHAR" property="inforStock" />
    <result column="FAC_COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode" /> 
    <result column="FAC_COUNTRY_NAME" jdbcType="VARCHAR" property="country" />
    <result column="FAC_PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
    <result column="FAC_PROVINCE_NAME" jdbcType="VARCHAR" property="province" />
    <result column="FAC_CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="FAC_CITY_NAME" jdbcType="VARCHAR" property="city" />
    <result column="FAC_AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="FAC_AREA_NAME" jdbcType="VARCHAR" property="area" />
    <result column="FAC_ADDRESS_DETAIL" jdbcType="VARCHAR" property="address" />
    <result column="CONTACT_USER" jdbcType="VARCHAR" property="connectBy" />
    <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="connectPhone" />
    <result column="IS_DEFAULT" jdbcType="DECIMAL" property="flag" />
    <result column="IS_ERP" jdbcType="DECIMAL" property="isErp" />
    <result column="IS_VMI" jdbcType="DECIMAL" property="isVmi" />
    <result column="IS_PROOFING" jdbcType="DECIMAL" property="isProofing" />
    <result column="IS_LOW_CONSUMABLE" jdbcType="DECIMAL" property="isLowConsumable" />
    <result column="PRODUCTION_BASE" jdbcType="VARCHAR" property="productionBase" />
    <result column="IS_SPECIAL_STORAGE" jdbcType="DECIMAL" property="isSpecialStorage" />
    <result column="FACTORY_ADDRESS_ID" jdbcType="DECIMAL" property="factoryAddressId" />
  </resultMap>

  <sql id="Base_Column_List">
    FACTORY_ADDRESS_ID, ORGANIZATION_ID, STOCK_NAME, IS_DEFAULT, FAC_COUNTRY_CODE, FAC_COUNTRY_NAME, 
    FAC_PROVINCE_CODE, FAC_PROVINCE_NAME, FAC_CITY_CODE, FAC_CITY_NAME, FAC_AREA_CODE, 
    FAC_AREA_NAME, FAC_ADDRESS_DETAIL, CONTACT_USER, CONTACT_MOBILE, CREATION_DATE, CREATED_BY, 
    LAST_UPDATE_DATE, LAST_UPDATED_BY, ENABLED_FLAG, INFOR_STOCK, FACTORY_CODE
  </sql>

  <select id="selectStFactoryAddressAll" parameterType="com.zte.interfaces.step.dto.StAddressDTO" resultMap="FactoryResultMap">
    select 
    FACTORY_ADDRESS_ID, 
    ORGANIZATION_ID, 
    STOCK_NAME, 
    IS_DEFAULT,
    FAC_COUNTRY_CODE,  
    FAC_COUNTRY_NAME,
    FAC_PROVINCE_CODE,
    FAC_PROVINCE_NAME,
    FAC_CITY_CODE,
    FAC_CITY_NAME,
    FAC_AREA_CODE,
    FAC_AREA_NAME,
    FAC_ADDRESS_DETAIL,
    CONTACT_USER, 
    CONTACT_MOBILE,
    INFOR_STOCK, 
    FACTORY_CODE,
    IS_ERP,
    IS_VMI,
    IS_PROOFING,
    IS_LOW_CONSUMABLE,
    PRODUCTION_BASE,
    IS_SPECIAL_STORAGE
    from KXSTEPIII.ST_FACTORY_ADDRESS S
    WHERE S.ENABLED_FLAG='Y'
    <if test="updateTime != null and updateTime != '' ">
      and LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{updateTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
    </if>
    <if test="factory != null and factory !='' ">
       and FACTORY_CODE=#{factory,jdbcType=VARCHAR}
    </if>
    <if test="orgId != null and orgId !='' ">
       and ORGANIZATION_ID=#{orgId,jdbcType=VARCHAR}
    </if>
    <if test="isLowConsumable != null">
      and IS_LOW_CONSUMABLE = #{isLowConsumable,jdbcType=DECIMAL}
    </if>
    <if test="productionBase != null and productionBase !='' ">
      and PRODUCTION_BASE = #{productionBase,jdbcType=VARCHAR}
    </if>
    <if test="isProofing != null">
      and IS_PROOFING = #{isProofing,jdbcType=DECIMAL}
    </if>
  </select> 
  <select id="selectStFactoryAddressById" parameterType="com.zte.domain.model.step.StFactoryAddress" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from KXSTEPIII.ST_FACTORY_ADDRESS
    where FACTORY_ADDRESS_ID = #{factoryAddressId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteStFactoryAddressById" parameterType="com.zte.domain.model.step.StFactoryAddress">
    delete from ST_FACTORY_ADDRESS
    where FACTORY_ADDRESS_ID = #{factoryAddressId,jdbcType=DECIMAL}
  </delete>

  <insert id="insertStFactoryAddress" parameterType="com.zte.domain.model.step.StFactoryAddress">
    insert into KXSTEPIII.ST_FACTORY_ADDRESS (FACTORY_ADDRESS_ID, ORGANIZATION_ID, 
      STOCK_NAME, IS_DEFAULT, FAC_COUNTRY_CODE, 
      FAC_COUNTRY_NAME, FAC_PROVINCE_CODE, FAC_PROVINCE_NAME, 
      FAC_CITY_CODE, FAC_CITY_NAME, FAC_AREA_CODE, 
      FAC_AREA_NAME, FAC_ADDRESS_DETAIL, CONTACT_USER, 
      CONTACT_MOBILE, CREATION_DATE, CREATED_BY, 
      LAST_UPDATE_DATE, LAST_UPDATED_BY, ENABLED_FLAG, 
      INFOR_STOCK, FACTORY_CODE)
    values (#{factoryAddressId,jdbcType=DECIMAL}, #{organizationId,jdbcType=VARCHAR}, 
      #{stockName,jdbcType=VARCHAR}, #{isDefault,jdbcType=DECIMAL}, #{facCountryCode,jdbcType=VARCHAR}, 
      #{facCountryName,jdbcType=VARCHAR}, #{facProvinceCode,jdbcType=VARCHAR}, #{facProvinceName,jdbcType=VARCHAR}, 
      #{facCityCode,jdbcType=VARCHAR}, #{facCityName,jdbcType=VARCHAR}, #{facAreaCode,jdbcType=VARCHAR}, 
      #{facAreaName,jdbcType=VARCHAR}, #{facAddressDetail,jdbcType=VARCHAR}, #{contactUser,jdbcType=VARCHAR}, 
      #{contactMobile,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, #{enabledFlag,jdbcType=VARCHAR}, 
      #{inforStock,jdbcType=VARCHAR}, #{factoryCode,jdbcType=DECIMAL})
  </insert>

  <insert id="insertStFactoryAddressSelective" parameterType="com.zte.domain.model.step.StFactoryAddress">
    insert into KXSTEPIII.ST_FACTORY_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="factoryAddressId != null">
        FACTORY_ADDRESS_ID,
      </if>

      <if test="organizationId != null">
        ORGANIZATION_ID,
      </if>

      <if test="stockName != null">
        STOCK_NAME,
      </if>

      <if test="isDefault != null">
        IS_DEFAULT,
      </if>

      <if test="facCountryCode != null">
        FAC_COUNTRY_CODE,
      </if>

      <if test="facCountryName != null">
        FAC_COUNTRY_NAME,
      </if>

      <if test="facProvinceCode != null">
        FAC_PROVINCE_CODE,
      </if>

      <if test="facProvinceName != null">
        FAC_PROVINCE_NAME,
      </if>

      <if test="facCityCode != null">
        FAC_CITY_CODE,
      </if>

      <if test="facCityName != null">
        FAC_CITY_NAME,
      </if>

      <if test="facAreaCode != null">
        FAC_AREA_CODE,
      </if>

      <if test="facAreaName != null">
        FAC_AREA_NAME,
      </if>

      <if test="facAddressDetail != null">
        FAC_ADDRESS_DETAIL,
      </if>

      <if test="contactUser != null">
        CONTACT_USER,
      </if>

      <if test="contactMobile != null">
        CONTACT_MOBILE,
      </if>

      <if test="creationDate != null">
        CREATION_DATE,
      </if>

      <if test="createdBy != null">
        CREATED_BY,
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG,
      </if>

      <if test="inforStock != null">
        INFOR_STOCK,
      </if>

      <if test="factoryCode != null">
        FACTORY_CODE,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="factoryAddressId != null">
        #{factoryAddressId,jdbcType=DECIMAL},
      </if>

      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>

      <if test="stockName != null">
        #{stockName,jdbcType=VARCHAR},
      </if>

      <if test="isDefault != null">
        #{isDefault,jdbcType=DECIMAL},
      </if>

      <if test="facCountryCode != null">
        #{facCountryCode,jdbcType=VARCHAR},
      </if>

      <if test="facCountryName != null">
        #{facCountryName,jdbcType=VARCHAR},
      </if>

      <if test="facProvinceCode != null">
        #{facProvinceCode,jdbcType=VARCHAR},
      </if>

      <if test="facProvinceName != null">
        #{facProvinceName,jdbcType=VARCHAR},
      </if>

      <if test="facCityCode != null">
        #{facCityCode,jdbcType=VARCHAR},
      </if>

      <if test="facCityName != null">
        #{facCityName,jdbcType=VARCHAR},
      </if>

      <if test="facAreaCode != null">
        #{facAreaCode,jdbcType=VARCHAR},
      </if>

      <if test="facAreaName != null">
        #{facAreaName,jdbcType=VARCHAR},
      </if>

      <if test="facAddressDetail != null">
        #{facAddressDetail,jdbcType=VARCHAR},
      </if>

      <if test="contactUser != null">
        #{contactUser,jdbcType=VARCHAR},
      </if>

      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>

      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="inforStock != null">
        #{inforStock,jdbcType=VARCHAR},
      </if>

      <if test="factoryCode != null">
        #{factoryCode,jdbcType=DECIMAL},
      </if>

    </trim>

  </insert>

  <update id="updateStFactoryAddressByIdSelective" parameterType="com.zte.domain.model.step.StFactoryAddress">
    update KXSTEPIII.ST_FACTORY_ADDRESS
    <set>
      <if test="organizationId != null">
        ORGANIZATION_ID = #{organizationId,jdbcType=VARCHAR},
      </if>

      <if test="stockName != null">
        STOCK_NAME = #{stockName,jdbcType=VARCHAR},
      </if>

      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=DECIMAL},
      </if>

      <if test="facCountryCode != null">
        FAC_COUNTRY_CODE = #{facCountryCode,jdbcType=VARCHAR},
      </if>

      <if test="facCountryName != null">
        FAC_COUNTRY_NAME = #{facCountryName,jdbcType=VARCHAR},
      </if>

      <if test="facProvinceCode != null">
        FAC_PROVINCE_CODE = #{facProvinceCode,jdbcType=VARCHAR},
      </if>

      <if test="facProvinceName != null">
        FAC_PROVINCE_NAME = #{facProvinceName,jdbcType=VARCHAR},
      </if>

      <if test="facCityCode != null">
        FAC_CITY_CODE = #{facCityCode,jdbcType=VARCHAR},
      </if>

      <if test="facCityName != null">
        FAC_CITY_NAME = #{facCityName,jdbcType=VARCHAR},
      </if>

      <if test="facAreaCode != null">
        FAC_AREA_CODE = #{facAreaCode,jdbcType=VARCHAR},
      </if>

      <if test="facAreaName != null">
        FAC_AREA_NAME = #{facAreaName,jdbcType=VARCHAR},
      </if>

      <if test="facAddressDetail != null">
        FAC_ADDRESS_DETAIL = #{facAddressDetail,jdbcType=VARCHAR},
      </if>

      <if test="contactUser != null">
        CONTACT_USER = #{contactUser,jdbcType=VARCHAR},
      </if>

      <if test="contactMobile != null">
        CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
      </if>

      <if test="creationDate != null">
        CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="inforStock != null">
        INFOR_STOCK = #{inforStock,jdbcType=VARCHAR},
      </if>

      <if test="factoryCode != null">
        FACTORY_CODE = #{factoryCode,jdbcType=DECIMAL},
      </if>

    </set>

    where FACTORY_ADDRESS_ID = #{factoryAddressId,jdbcType=DECIMAL}
  </update>

  <update id="updateStFactoryAddressById" parameterType="com.zte.domain.model.step.StFactoryAddress">
    update KXSTEPIII.ST_FACTORY_ADDRESS
    set ORGANIZATION_ID = #{organizationId,jdbcType=VARCHAR},
      STOCK_NAME = #{stockName,jdbcType=VARCHAR},
      IS_DEFAULT = #{isDefault,jdbcType=DECIMAL},
      FAC_COUNTRY_CODE = #{facCountryCode,jdbcType=VARCHAR},
      FAC_COUNTRY_NAME = #{facCountryName,jdbcType=VARCHAR},
      FAC_PROVINCE_CODE = #{facProvinceCode,jdbcType=VARCHAR},
      FAC_PROVINCE_NAME = #{facProvinceName,jdbcType=VARCHAR},
      FAC_CITY_CODE = #{facCityCode,jdbcType=VARCHAR},
      FAC_CITY_NAME = #{facCityName,jdbcType=VARCHAR},
      FAC_AREA_CODE = #{facAreaCode,jdbcType=VARCHAR},
      FAC_AREA_NAME = #{facAreaName,jdbcType=VARCHAR},
      FAC_ADDRESS_DETAIL = #{facAddressDetail,jdbcType=VARCHAR},
      CONTACT_USER = #{contactUser,jdbcType=VARCHAR},
      CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
      CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      INFOR_STOCK = #{inforStock,jdbcType=VARCHAR},
      FACTORY_CODE = #{factoryCode,jdbcType=DECIMAL}
    where FACTORY_ADDRESS_ID = #{factoryAddressId,jdbcType=DECIMAL}
  </update>

</mapper>
