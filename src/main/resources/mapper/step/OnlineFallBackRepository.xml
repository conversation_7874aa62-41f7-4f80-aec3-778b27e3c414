<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OnlineFallBackRepository">
    <resultMap id="NonconformingResultMap" type="com.zte.interfaces.onlinefallback.dto.NonconformingBillOutputDTO">
        <result column="nonconformingProductNo" jdbcType="VARCHAR" property="nonconformingProductNo" />
        <result column="itemBarcode" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
        <result column="itemName" jdbcType="VARCHAR" property="itemName" />
        <result column="supplierNo" jdbcType="VARCHAR" property="supplierNo" />
        <result column="supplierName" jdbcType="VARCHAR" property="supplierName" />
        <result column="qty" jdbcType="DECIMAL" property="qty" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="prodplanId" jdbcType="VARCHAR" property="prodplanId" />
        <result column="importBatchId" jdbcType="VARCHAR" property="importBatchId" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="type" jdbcType="VARCHAR" property="type" />
    </resultMap>
  <select id="getNonconformingBill" parameterType="com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO" resultMap="NonconformingResultMap">
  	select DISTINCT a.sheet_no nonconformingProductNo,a.status,'01' type 
      from kxstepiii.QA_GEN_BADSHEET a
     where a.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
     UNION ALL
    select DISTINCT ikr.sheet_no nonconformingProductNo,nvl(e.code,'05') status,'02' type 
      from kxstepiii.qa_kxonline_badsheet ikr 
      left join kxstepiii.st_codeinfo e
	    on ikr.status = e.code_desc
	   and e.code_type = 'qakxonlinebadsheet'
	 where ikr.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
	 UNION ALL
	 select DISTINCT qui.sheet_no nonconformingProductNo,to_char(qui.prod_status) status,'03' type
       from kxstepiii.qa_userqa_info qui
      where qui.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
  </select>
  <select id="queryProcessedBySkuManager" parameterType="com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO" resultMap="NonconformingResultMap">
    select qa.sheet_no nonconformingProductNo,
           '02' type
      from kxstepiii.qa_gen_badsheetprocess qa
     where qa.itembarcodebz is not null
       and rownum = 1
       and qa.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
    union all
    select qa.sheet_no nonconformingProductNo, '01' type
      from kxstepiii.qa_kxonline_badsheet qa
     where qa.item_barcode is not null
       and rownum = 1
       and qa.sheet_no =#{nonconformingProductNo,jdbcType=VARCHAR}
       and exists (
       	   select 1 from kxstepiii.qa_kxonline_badproc b where b.sheet_no=#{nonconformingProductNo,jdbcType=VARCHAR} and b.process='物料技术经理处理'
       )
  </select>
  <select id="queryNonconformingBill" parameterType="com.zte.interfaces.onlinefallback.dto.NonconformingBillInputDTO" resultMap="NonconformingResultMap">
    select DISTINCT a.sheet_no nonconformingProductNo,
           b.itemBarcode,
           nvl(i.item_no,bbh.bom_no) itemNo,
           nvl(i.item_name,bbh.bom_name) itemName,
           a.supplier_no supplierNo,
           s.supplier_name supplierName,
           batch_bad_qty qty,
           a.status,
           a.bom_barcode prodplanId,
           '' importBatchId,
           to_char(a.bad_desc) remark,
           '02' type
      from kxstepiii.QA_GEN_BADSHEET a
      left join kxstepiii.ba_supplier s
        on (a.supplier_no = s.supplier_no)
      left join kxstepiii.ba_item i
        on a.item_id = i.item_id
      left join kxstepiii.ba_bom_head bbh on a.item_id=bbh.bom_id
      join (select distinct regexp_substr(aa.itembarcodebz, '[^,]+', 1, level) itembarcode,aa.sheet_no
  from (select qa.sheet_no, qa.itembarcodebz
          from kxstepiii.qa_gen_badsheetprocess qa
         where qa.itembarcodebz is not null
           and rownum = 1
           and qa.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}) aa
connect by regexp_substr(aa.itembarcodebz, '[^,]+', 1, level) is not null) b
		on a.sheet_no = b.sheet_no
     where a.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
       and a.status in ('10','11','12')
       and exists (
       	   select 1 from kxscmiii.pu_deli_iscp_package pdip where pdip.item_barcode = b.itemBarcode and b.itemBarcode != 'NO'
           union select 1 from dual where b.itemBarcode = 'NO'
       )
    <if test="itemBarcode != null and itemBarcode != ''">
      and a.item_barcode = #{itemBarcode,jdbcType=VARCHAR}
    </if>
    <if test="itemNo != null and itemNo != ''">
      and i.item_no = #{itemNo,jdbcType=VARCHAR}
    </if>
    <if test="status != null and status != ''">
      and a.status = #{status,jdbcType=VARCHAR}
    </if>
    <if test="supplierNo != null and supplierNo != ''">
      and a.supplier_no = #{supplierNo,jdbcType=VARCHAR}
    </if>   
    UNION ALL
    SELECT DISTINCT ikr.sheet_no nonconformingProductNo,
                 b.itembarcode itemBarcode,
                ikr.item_no itemNo,
                bi.item_name itemName,
                 ikr.supplier_no supplierNo,
                 ikr.supplier_name supplierName,
                 ikr.bad_num qty,
                 ikr.status,
                 lpad(to_char(ikr.prodplan_id), 7,'0') prodplanId,
                 '' importBatchId,
                 to_char(ikr.problem_desc) remark,
                 '01' type
	  FROM kxstepiii.qa_kxonline_badsheet ikr 
	  LEFT JOIN kxstepiii.ba_item bi ON bi.item_no = ikr.item_no
	  join kxstepiii.st_codeinfo e
	  on ikr.status = e.code_desc
	  and e.code_type = 'qakxonlinebadsheet'
	  and e.code in ('01','02','03','04')
	  join (select distinct regexp_substr(aa.item_barcode, '[^,]+', 1, level) itembarcode,aa.sheet_no
	  from (select qa.sheet_no, qa.item_barcode
	          from kxstepiii.qa_kxonline_badsheet qa
	         where qa.item_barcode is not null
	           and rownum = 1
	           and qa.sheet_no =#{nonconformingProductNo,jdbcType=VARCHAR}) aa
	connect by regexp_substr(aa.item_barcode, '[^,]+', 1, level) is not null) b
			on ikr.sheet_no = b.sheet_no
     where ikr.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
     and exists (
       	   select 1 from kxscmiii.pu_deli_iscp_package pdip where pdip.item_barcode = b.itemBarcode and b.itemBarcode != 'NO'
           union select 1 from dual where b.itemBarcode = 'NO'
       )
    <if test="itemBarcode != null and itemBarcode != ''">
      and ikr.item_barcode = #{itemBarcode,jdbcType=VARCHAR}
    </if>
    <if test="itemNo != null and itemNo != ''">
      and ikr.item_no = #{itemNo,jdbcType=VARCHAR}
    </if>
    <if test="status != null and status != ''">
      and ikr.status = #{status,jdbcType=VARCHAR}
    </if>
    <if test="supplierNo != null and supplierNo != ''">
      and ikr.supplier_no = #{supplierNo,jdbcType=VARCHAR}
    </if>
    UNION ALL
      select DISTINCT qui.sheet_no nonconformingProductNo,
      qui.item_barcode itemBarcode,
      qui.item_no itemNo,
      bi.item_name itemName,
      qui.supplier_no supplierNo,
      bs.supplier_name supplierName,
      qui.bad_rate qty,
      to_char(qui.prod_status) status,
      qui.product_no prodplanId,
      '' importBatchId,
      to_char(qui.bad_desc) remark,
      '03' type
      from kxstepiii.qa_userqa_info qui, kxstepiii.ba_supplier bs,kxstepiii.ba_item bi
      where qui.supplier_no = bs.supplier_no(+) and qui.item_no = bi.item_no
      and qui.sheet_no = #{nonconformingProductNo,jdbcType=VARCHAR}
      and exists (
       	   select 1 from kxscmiii.pu_deli_iscp_package pdip where pdip.item_barcode = qui.item_barcode
       )
      <if test="itemBarcode != null and itemBarcode != ''">
          and qui.item_barcode = #{itemBarcode,jdbcType=VARCHAR}
      </if>
      <if test="itemNo != null and itemNo != ''">
          and qui.item_no = #{itemNo,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
          and to_char(qui.prod_status) = #{status,jdbcType=VARCHAR}
      </if>
      <if test="supplierNo != null and supplierNo != ''">
          and qui.supplier_no = #{supplierNo,jdbcType=VARCHAR}
      </if>
  </select> 
</mapper>
