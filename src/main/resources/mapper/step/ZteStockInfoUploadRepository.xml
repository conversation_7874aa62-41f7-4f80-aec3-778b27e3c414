<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.ZteStockInfoUploadRepository">

	<select id="getStockInfo" parameterType="com.zte.interfaces.step.dto.ZteStockInfoDTO" resultType="com.zte.interfaces.step.dto.ZteStockInfoDTO">
        select
        'ZTE1' odmPlantCode,
        'BU02' buCode,
        to_char(sysdate,'YYYYMMDD') dataDate,
        bi.item_no itemNo,
        ci.customer_code materialCode,
        ci.customer_item_name materialDesc,
        'N001' odmStorageLoc,
        st.balance_qty stockQuantity,
        'EA' unit,
        st.stock_no stockNo,
        case when instr(ses.stock_name, '在途') > 0 then '20'
        when instr(ses.stock_name, '待处理') > 0 then '11'
        else '00' end as stockStatus
        from kxstepiii.st_summary st
        join kxstepiii.ba_item bi
        on bi.item_id = st.item_id
        join kxstepiii.st_stock ses
        on ses.stock_no = st.stock_no
        join kxstepiii.customer_items ci
        on ci.zte_code = bi.item_no
        where st.balance_qty > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        <if test="itemNoList !=null and itemNoList.size()>0">
            and bi.item_no in
            <foreach collection="itemNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
	</select>

    <insert id="insertStockUploadSnapshot" parameterType="java.util.List">
        insert into kxstepiii.stock_upload_snapshot
        (serialkey, item_no, stock_no, qty, create_by, last_updated_by)
        select kxstepiii.seq_stock_upload_snapshot.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{item.itemNo,jdbcType=VARCHAR}, #{item.stockNo,jdbcType=VARCHAR}, #{item.stockQuantity,jdbcType=INTEGER},
            'system' createBy, 'system' lastUpdatedBy from dual
        </foreach>
        ) temp
    </insert>

    <insert id="insertStockUploadLog" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close="end;">
            insert into kxstepiii.stock_upload_log
            (id, origin, customer_name, project_name, project_phase, cooperation_mode, message_type, craft_section, contract_no,
            task_no, item_no, sn, json_data, factory_id, status, remark, create_by, last_updated_by)
            values (#{item.id,jdbcType=VARCHAR}, #{item.origin,jdbcType=VARCHAR}, #{item.customerName,jdbcType=VARCHAR},
            #{item.projectName,jdbcType=VARCHAR}, #{item.projectPhase,jdbcType=VARCHAR}, #{item.cooperationMode,jdbcType=VARCHAR},
            #{item.messageType,jdbcType=VARCHAR}, #{item.craftSection,jdbcType=VARCHAR}, #{item.contractNo,jdbcType=VARCHAR},
            #{item.taskNo,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR},
            empty_clob(), #{item.factoryId,jdbcType=INTEGER}, #{item.status,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, 'system', 'system');
            <if test="item.jsonData != null">
                update kxstepiii.stock_upload_log set json_data = #{item.jsonData,jdbcType=CLOB} where id = #{item.id,jdbcType=VARCHAR};
            </if>
        </foreach>
    </insert>

    <insert id="insertOrUpdateStockUploadLog" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close="end;">
            MERGE INTO kxstepiii.stock_upload_log t
            USING (
            SELECT
            #{item.id,jdbcType=VARCHAR} AS id,
            #{item.origin,jdbcType=VARCHAR} AS origin,
            #{item.customerName,jdbcType=VARCHAR} AS customer_name,
            #{item.projectName,jdbcType=VARCHAR} AS project_name,
            #{item.projectPhase,jdbcType=VARCHAR} AS project_phase,
            #{item.cooperationMode,jdbcType=VARCHAR} AS cooperation_mode,
            #{item.messageType,jdbcType=VARCHAR} AS message_type,
            #{item.craftSection,jdbcType=VARCHAR} AS craft_section,
            #{item.contractNo,jdbcType=VARCHAR} AS contract_no,
            #{item.taskNo,jdbcType=VARCHAR} AS task_no,
            #{item.itemNo,jdbcType=VARCHAR} AS item_no,
            #{item.sn,jdbcType=VARCHAR} AS sn,
            #{item.factoryId,jdbcType=INTEGER} AS factory_id,
            #{item.status,jdbcType=VARCHAR} AS status,
            #{item.remark,jdbcType=VARCHAR} AS remark
            FROM DUAL
            ) s
            ON (t.id = s.id)
            WHEN MATCHED THEN
            UPDATE SET
            t.origin = s.origin,
            t.customer_name = s.customer_name,
            t.project_name = s.project_name,
            t.project_phase = s.project_phase,
            t.cooperation_mode = s.cooperation_mode,
            t.message_type = s.message_type,
            t.craft_section = s.craft_section,
            t.contract_no = s.contract_no,
            t.task_no = s.task_no,
            t.item_no = s.item_no,
            t.sn = s.sn,
            t.factory_id = s.factory_id,
            t.status = s.status,
            t.remark = s.remark,
            t.last_updated_by = 'system',
            t.last_updated_date = SYSDATE
            WHEN NOT MATCHED THEN
            INSERT (
            id, origin, customer_name, project_name, project_phase, cooperation_mode,
            message_type, craft_section, contract_no, task_no, item_no, sn,
            json_data, factory_id, status, remark, create_by, last_updated_by
            ) VALUES (
            s.id, s.origin, s.customer_name, s.project_name, s.project_phase, s.cooperation_mode,
            s.message_type, s.craft_section, s.contract_no, s.task_no, s.item_no, s.sn,
            empty_clob(), s.factory_id, s.status, s.remark, 'system', 'system'
            );
            <if test="item.jsonData != null">
                update kxstepiii.stock_upload_log set json_data = #{item.jsonData,jdbcType=CLOB} where id = #{item.id,jdbcType=VARCHAR};
            </if>
        </foreach>
    </insert>

    <update id="updateStockUploadLog" parameterType="com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO">
        update kxstepiii.stock_upload_log
        set last_updated_by = 'B2B',
            last_updated_date = sysdate,
            upload_status = 1
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateStockUploadLogStatus" parameterType="com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO">
        update kxstepiii.stock_upload_log
        set last_updated_by = 'B2B',
        last_updated_date = sysdate,
        upload_status = #{uploadStatus,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR} and message_type = #{messageType,jdbcType=VARCHAR}
    </update>

    <select id="getStockUploadLog" resultType="com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO">
        select t.message_type messageType,t.id,t.contract_no contractNo,t.task_no taskNo,t.item_no itemNo,t.sn,t.remark
        from kxstepiii.stock_upload_log t
        where 1=1
        <if test="contractNoList != null and contractNoList.size()>0">
            and t.contract_no in
            <foreach collection="contractNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            and t.item_no in ('2','4')
        </if>
        <if test="contractNo != null and contractNo != ''">
            and t.contract_no  = #{contractNo,jdbcType=VARCHAR}
        </if>
        <if test="taskNo != null and taskNo != ''">
            and t.task_no  = #{taskNo,jdbcType=VARCHAR}
        </if>
        <if test="itemNo != null and itemNo != ''">
            and t.item_no  = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            and t.remark  = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="messageType != null and messageType != '' ">
            and t.message_type = #{messageType,jdbcType=VARCHAR}
        </if>
        <if test="id != null and id != '' ">
            and t.id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="isReturn == 1 ">
            and t.upload_status != -1
        </if>
    </select>

    <select id="stockUploadLogMonitor" resultType="com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO">
        select * from (
            select t.message_type messageType, count(1) qty
            from kxstepiii.stock_upload_log t
            where t.upload_status = 0
            group by t.message_type
        ) s where s.qty > 0
    </select>

    <update id="updateStockUploadLogJsonData" parameterType="com.zte.interfaces.step.dto.ZteStockInfoUploadLogDTO">
        update kxstepiii.stock_upload_log
        set return_data =  #{returnData,jdbcType=VARCHAR},
        last_updated_by = 'B2B',
        last_updated_date = sysdate
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>