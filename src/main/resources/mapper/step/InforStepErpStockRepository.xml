<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.InforStepErpStockRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.step.InforStepErpStock">
        <result column="INFOR_STOCK" jdbcType="VARCHAR" property="inforStock" />
        <result column="STEP_STOCK" jdbcType="VARCHAR" property="stepStock" />
        <result column="ORGANIZATION_ID" jdbcType="VARCHAR" property="organizationId" />
        <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType" />
    </resultMap>
    <select id="getInforStepErpStock" parameterType="java.util.List" resultMap="BaseResultMap">
        select ses.INFOR_STOCK,ses.STEP_STOCK,st.ORGANIZATION_ID,ses.GOODS_TYPE
        from kxstepiii.infor_step_erp_stock ses
        join kxstepiii.st_stock st on st.stock_no = ses.step_stock
        where ses.step_stock in (
        <foreach collection="list" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
    </select>
</mapper>
