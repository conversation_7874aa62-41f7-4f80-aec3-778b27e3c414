<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.ApprovalProcessInfoRepository">
    
    <select id="queryInforMachineBoardIn" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT  s.status
        FROM KXSTEPIII.INFOR_BOM_RETURN_BILL s
        WHERE s.bill_no =  #{billNO,jdbcType=VARCHAR}
         and rownum =1
    </select>

    <update id="updateInforMachineBoardIn"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE KXSTEPIII.INFOR_BOM_RETURN_BILL s
        SET s.status =  #{status,jdbcType=VARCHAR},
        s.audit_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        s.audit_date = sysdate,
        s.last_updated_by   = #{lastUpdatedBy,jdbcType=VARCHAR},
        s.last_updated_date = sysdate
        WHERE s.bill_no = #{billNO,jdbcType=VARCHAR}
    </update>

    <update id="updateInforMachineBoardInDetail"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE KXSTEPIII.INFOR_BOM_RETURN_DETAIL
        SET last_updated_by   =  #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = sysdate,
            allow_qty         = PLAN_QTY
        WHERE BILL_ID in
            (SELECT BILL_ID
             FROM kxstepiii.infor_bom_return_bill
             WHERE BILL_NO = #{billNO,jdbcType = VARCHAR}
            )
    </update>

    <select id="queryInforMachineOut" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT  s.status
        FROM KXSTEPIII.INFOR_CM_REQUISITION_BILL s
        WHERE s.bill_no = #{billNO,jdbcType=VARCHAR}
        and rownum =1
    </select>


    <update id="updateInforMachineOut"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE KXSTEPIII.INFOR_CM_REQUISITION_BILL s
        SET s.status =  #{status,jdbcType=VARCHAR},
        s.audit_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        s.audit_date = sysdate,
        s.last_updated_by   =  #{lastUpdatedBy,jdbcType=VARCHAR},
        s.last_updated_date = sysdate
        WHERE s.bill_no =  #{billNO,jdbcType=VARCHAR}
    </update>
    <update id="updateInforMachineOutDetail"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE kxstepiii.infor_cm_requisition_detail
        SET last_updated_by   =  #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = sysdate,
            allow_qty         = PLAN_QTY
        WHERE BILL_ID in
             (SELECT BILL_ID
              FROM KXSTEPIII.INFOR_CM_REQUISITION_BILL
              where bill_NO = #{billNO, jdbcType = VARCHAR}
             )
    </update>

    <select id="queryInforMachineMaterialIn" parameterType="java.lang.String"  resultType="java.lang.String">
        SELECT  s.status
        FROM KXSTEPIII.INFOR_CM_RETURN_BILL s
        WHERE s.bill_no = #{billNO,jdbcType=VARCHAR}
        and rownum =1
    </select>


    <update id="updateInforMachineMaterialIn"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE KXSTEPIII.INFOR_CM_RETURN_BILL s
        SET s.status =  #{status,jdbcType=VARCHAR},
        s.audit_by =  #{lastUpdatedBy,jdbcType=VARCHAR},
        s.audit_date = sysdate,
        s.last_updated_by   = #{lastUpdatedBy,jdbcType=VARCHAR},
        s.last_updated_date = sysdate
        WHERE s.bill_no =  #{billNO,jdbcType=VARCHAR}
    </update>
    <update id="updateInforMachineMaterialInDetail"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
        UPDATE KXSTEPIII.INFOR_CM_RETURN_DETAIL
        SET last_updated_by   = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = sysdate,
            allow_qty         = PLAN_QTY
        WHERE BILL_ID in
             (SELECT BILL_ID
              FROM KXSTEPIII.Infor_Cm_Return_Bill
              where BILL_NO = #{billNO, jdbcType = VARCHAR}
             )
    </update>

    <insert id ="insertMobileApprovalLog" parameterType="com.zte.interfaces.step.dto.ApprovalProcessLogDTO">
        insert into kxstepiii.MOBILEAPPROVAL_LOG(bill_no,interface_name,response)
        values(#{billNO,jdbcType=VARCHAR},
               #{interfaceName,jdbcType=VARCHAR},
               #{response,jdbcType=VARCHAR})
    </insert>

</mapper>