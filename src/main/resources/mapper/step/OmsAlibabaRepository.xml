<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OmsAlibabaRepository">

	<select id="getRequisitionBills" parameterType="com.zte.interfaces.step.dto.OmsAlibabaBillDTO" resultType="com.zte.interfaces.step.dto.OmsAlibabaBillDTO">
        SELECT B.BILL_NO billNO,
        B.FACTORY_CODE factoryCode,
        B.ENTITY_NAME entityName,
        D.DETAIL_ID detailId,
        D.PLAN_QTY planQty,
        D.ITEM_NO itemNo
        FROM KXSTEPIII.INFOR_CM_REQUISITION_BILL   B,
        KXSTEPIII.INFOR_CM_REQUISITION_DETAIL D
        WHERE B.BILL_ID = D.BILL_ID
        AND B.ENTITY_NAME IS NOT NULL
        AND B.FACTORY_CODE IS NOT NULL
        AND B.STATUS = 'CHECKED'
        <if test="billNos !=null and billNos.size()>0">
            AND B.BILL_NO IN
            <foreach collection="billNos" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND NOT EXISTS (SELECT 1
        FROM KXSTEPIII.STOCK_UPLOAD_LOG L
        WHERE L.CONTRACT_NO = B.BILL_NO
        AND L.ENABLED_FLAG = 'Y'
        AND L.UPLOAD_STATUS = 1)
	</select>
    <select id="getReturnBills" parameterType="com.zte.interfaces.step.dto.OmsAlibabaBillDTO" resultType="com.zte.interfaces.step.dto.OmsAlibabaBillDTO">
        SELECT B.BILL_NO billNO,B.Warehourse_Id warehourseId,D.Item_Barcode itemBarCode,D.Toid boxId,B.Created_By returnBy,
               B.FACTORY_CODE factoryCode,
               B.ENTITY_NAME entityName,
               D.DETAIL_ID detailId,
               D.PLAN_QTY planQty,
               BI.ITEM_NO itemNo
        FROM KXSTEPIII.INFOR_CM_RETURN_BILL   B,
             KXSTEPIII.INFOR_CM_RETURN_DETAIL D,
             KXSTEPIII.BA_ITEM                BI
        WHERE B.BILL_ID = D.BILL_ID
          AND B.ENTITY_NAME IS NOT NULL
          AND B.FACTORY_CODE IS NOT NULL
          AND B.STATUS = 'CHECKED'
          AND D.ITEM_ID = BI.ITEM_ID
        <if test="billNos !=null and billNos.size()>0">
            AND B.BILL_NO IN
            <foreach collection="billNos" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
          AND NOT EXISTS (SELECT 1
                          FROM KXSTEPIII.STOCK_UPLOAD_LOG L
                          WHERE L.CONTRACT_NO = B.BILL_NO
                            AND L.ENABLED_FLAG = 'Y'
                            AND L.UPLOAD_STATUS = 1)
    </select>
    <select id="getReturnBillSnList" resultType="java.lang.String">
        SELECT R.SERVER_SN
        FROM KXSTEPIII.INFOR_CM_RETURN_BOX_SN R
        WHERE R.BILL_NO = #{billNo,jdbcType=VARCHAR}
          AND R.pkg_id = #{pkgId,jdbcType=VARCHAR}
          AND R.ENABLED_FLAG = 'Y'
    </select>
</mapper>