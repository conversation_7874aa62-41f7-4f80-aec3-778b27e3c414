<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StepBaUserRepository">
    <select id="getFullName" resultType="java.lang.String">
        SELECT MAX(b.full_name || b.user_name)
        FROM kxstepiii.ba_users b
        where b.user_name = #{editWho,jdbcType=VARCHAR}
    </select>
</mapper>