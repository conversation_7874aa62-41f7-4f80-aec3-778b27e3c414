<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StItemAddressRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.step.StItemAddress">
    <id column="ITEM_ADDRESS_ID" jdbcType="DECIMAL" property="itemAddressId" />
    <result column="ITEM_ORGANIZATION_ID" jdbcType="VARCHAR" property="itemOrganizationId" />
    <result column="STOCK_NAME" jdbcType="VARCHAR" property="stockName" />
    <result column="INFOR_STOCK" jdbcType="VARCHAR" property="inforStock" />
    <result column="FACTORY_ADDRESS_GUID" jdbcType="DECIMAL" property="factoryAddressGuid" />
    <result column="CONTACT_USER" jdbcType="VARCHAR" property="contactUser" />
    <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="FACTORY_CODE" jdbcType="DECIMAL" property="factoryCode" />
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
  </resultMap>
  
  <resultMap id="ItemResultMap" type="com.zte.interfaces.step.dto.StItemAddressDTO">
    <result column="FACTORY_CODE" jdbcType="DECIMAL" property="factory" />
    <result column="ITEM_ORGANIZATION_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="STOCK_NAME" jdbcType="VARCHAR" property="stepStock" /> 
    <result column="INFOR_STOCK" jdbcType="VARCHAR" property="inforStock" /> 
    <result column="ITEM_CODE" jdbcType="VARCHAR" property="itemNo" /> 
    <result column="FAC_COUNTRY_CODE" jdbcType="VARCHAR" property="countryCode" />
    <result column="FAC_COUNTRY_NAME" jdbcType="VARCHAR" property="country" />
    <result column="FAC_PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
    <result column="FAC_PROVINCE_NAME" jdbcType="VARCHAR" property="province" />
    <result column="FAC_CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="FAC_CITY_NAME" jdbcType="VARCHAR" property="city" />
    <result column="FAC_AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="FAC_AREA_NAME" jdbcType="VARCHAR" property="area" />
    <result column="FAC_ADDRESS_DETAIL" jdbcType="VARCHAR" property="address" />
    <result column="CONTACT_USER" jdbcType="VARCHAR" property="connectBy" />
    <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="connectPhone" />
    <result column="IS_DEFAULT" jdbcType="DECIMAL" property="flag" />
    <result column="IS_PROOFING" jdbcType="DECIMAL" property="isProofing" />
  </resultMap>

  <sql id="Base_Column_List">
    ITEM_ADDRESS_ID, ITEM_ORGANIZATION_ID, STOCK_NAME, INFOR_STOCK, FACTORY_ADDRESS_GUID, 
    CONTACT_USER, CONTACT_MOBILE, CREATION_DATE, CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
    ENABLED_FLAG, FACTORY_CODE, ITEM_CODE
  </sql>

  <select id="selectStItemAddressAll" parameterType="com.zte.interfaces.step.dto.StAddressDTO" resultMap="ItemResultMap">
    select S.ITEM_ADDRESS_ID,
    S.ITEM_ORGANIZATION_ID,
    S.STOCK_NAME,
    S.INFOR_STOCK,
    S.FACTORY_ADDRESS_GUID,
    F.IS_DEFAULT,
    F.FAC_COUNTRY_CODE,
    F.FAC_COUNTRY_NAME,
    F.FAC_PROVINCE_CODE,
    F.FAC_PROVINCE_NAME,
    F.FAC_CITY_CODE,
    F.FAC_CITY_NAME,
    F.FAC_AREA_CODE,
    F.FAC_AREA_NAME,
    F.FAC_ADDRESS_DETAIL,
    F.IS_PROOFING,
    S.CONTACT_USER,
    S.CONTACT_MOBILE,
    S.FACTORY_CODE,
    S.ITEM_CODE
    from KXSTEPIII.ST_ITEM_ADDRESS S
    JOIN KXSTEPIII.ST_FACTORY_ADDRESS F
    ON F.FACTORY_ADDRESS_ID = S.FACTORY_ADDRESS_GUID
    AND F.ENABLED_FLAG = 'Y'
    WHERE S.ENABLED_FLAG = 'Y'
    <if test="updateTime != null">
      and s.LAST_UPDATE_DATE <![CDATA[>=]]>#{updateTime,jdbcType=TIMESTAMP}
    </if>
    <if test="itemNo != null and itemNo.size > 0 ">
      and s.ITEM_CODE IN
      <foreach item="item" index="index" collection="itemNo" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="factory != null and factory !='' ">
      and s.FACTORY_CODE=#{factory,jdbcType=VARCHAR}
    </if>
    <if test="orgId != null and orgId !='' ">
      and s.ITEM_ORGANIZATION_ID=#{orgId,jdbcType=VARCHAR}
    </if>
    <if test="isProofing != null">
      and F.IS_PROOFING = #{isProofing,jdbcType=DECIMAL}
    </if>
  </select>

  <select id="selectStItemAddressById" parameterType="com.zte.domain.model.step.StItemAddress" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from KXSTEPIII.ST_ITEM_ADDRESS
    where ITEM_ADDRESS_ID = #{itemAddressId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteStItemAddressById" parameterType="com.zte.domain.model.step.StItemAddress">
    delete from KXSTEPIII.ST_ITEM_ADDRESS
    where ITEM_ADDRESS_ID = #{itemAddressId,jdbcType=DECIMAL}
  </delete>

  <insert id="insertStItemAddress" parameterType="com.zte.domain.model.step.StItemAddress">
    insert into KXSTEPIII.ST_ITEM_ADDRESS (ITEM_ADDRESS_ID, ITEM_ORGANIZATION_ID, 
      STOCK_NAME, INFOR_STOCK, FACTORY_ADDRESS_GUID, 
      CONTACT_USER, CONTACT_MOBILE, CREATION_DATE, 
      CREATED_BY, LAST_UPDATE_DATE, LAST_UPDATED_BY, 
      ENABLED_FLAG, FACTORY_CODE, ITEM_CODE
      )
    values (#{itemAddressId,jdbcType=DECIMAL}, #{itemOrganizationId,jdbcType=VARCHAR}, 
      #{stockName,jdbcType=VARCHAR}, #{inforStock,jdbcType=VARCHAR}, #{factoryAddressGuid,jdbcType=DECIMAL}, 
      #{contactUser,jdbcType=VARCHAR}, #{contactMobile,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{enabledFlag,jdbcType=VARCHAR}, #{factoryCode,jdbcType=DECIMAL}, #{itemCode,jdbcType=VARCHAR}
      )
  </insert>

  <insert id="insertStItemAddressSelective" parameterType="com.zte.domain.model.step.StItemAddress">
    insert into KXSTEPIII.ST_ITEM_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="itemAddressId != null">
        ITEM_ADDRESS_ID,
      </if>

      <if test="itemOrganizationId != null">
        ITEM_ORGANIZATION_ID,
      </if>

      <if test="wmsStock != null">
        STOCK_NAME,
      </if>

      <if test="inforStock != null">
        INFOR_STOCK,
      </if>

      <if test="factoryAddressGuid != null">
        FACTORY_ADDRESS_GUID,
      </if>

      <if test="contactUser != null">
        CONTACT_USER,
      </if>

      <if test="contactMobile != null">
        CONTACT_MOBILE,
      </if>

      <if test="creationDate != null">
        CREATION_DATE,
      </if>

      <if test="createdBy != null">
        CREATED_BY,
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG,
      </if>

      <if test="factoryCode != null">
        FACTORY_CODE,
      </if>

      <if test="itemCode != null">
        ITEM_CODE,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="itemAddressId != null">
        #{itemAddressId,jdbcType=DECIMAL},
      </if>

      <if test="itemOrganizationId != null">
        #{itemOrganizationId,jdbcType=VARCHAR},
      </if>

      <if test="wmsStock != null">
        #{stockName,jdbcType=VARCHAR},
      </if>

      <if test="inforStock != null">
        #{inforStock,jdbcType=VARCHAR},
      </if>

      <if test="factoryAddressGuid != null">
        #{factoryAddressGuid,jdbcType=DECIMAL},
      </if>

      <if test="contactUser != null">
        #{contactUser,jdbcType=VARCHAR},
      </if>

      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>

      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="factoryCode != null">
        #{factoryCode,jdbcType=DECIMAL},
      </if>

      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>

    </trim>

  </insert>

  <update id="updateStItemAddressByIdSelective" parameterType="com.zte.domain.model.step.StItemAddress">
    update KXSTEPIII.ST_ITEM_ADDRESS
    <set>
      <if test="itemOrganizationId != null">
        ITEM_ORGANIZATION_ID = #{itemOrganizationId,jdbcType=VARCHAR},
      </if>

      <if test="wmsStock != null">
        STOCK_NAME = #{stockName,jdbcType=VARCHAR},
      </if>

      <if test="inforStock != null">
        INFOR_STOCK = #{inforStock,jdbcType=VARCHAR},
      </if>

      <if test="factoryAddressGuid != null">
        FACTORY_ADDRESS_GUID = #{factoryAddressGuid,jdbcType=DECIMAL},
      </if>

      <if test="contactUser != null">
        CONTACT_USER = #{contactUser,jdbcType=VARCHAR},
      </if>

      <if test="contactMobile != null">
        CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
      </if>

      <if test="creationDate != null">
        CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      </if>

      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="enabledFlag != null">
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="factoryCode != null">
        FACTORY_CODE = #{factoryCode,jdbcType=DECIMAL},
      </if>

      <if test="itemCode != null">
        ITEM_CODE = #{itemCode,jdbcType=VARCHAR},
      </if>

    </set>

    where ITEM_ADDRESS_ID = #{itemAddressId,jdbcType=DECIMAL}
  </update>

  <update id="updateStItemAddressById" parameterType="com.zte.domain.model.step.StItemAddress">
    update KXSTEPIII.ST_ITEM_ADDRESS
    set ITEM_ORGANIZATION_ID = #{itemOrganizationId,jdbcType=VARCHAR},
      STOCK_NAME = #{stockName,jdbcType=VARCHAR},
      INFOR_STOCK = #{inforStock,jdbcType=VARCHAR},
      FACTORY_ADDRESS_GUID = #{factoryAddressGuid,jdbcType=DECIMAL},
      CONTACT_USER = #{contactUser,jdbcType=VARCHAR},
      CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
      CREATION_DATE = #{creationDate,jdbcType=TIMESTAMP},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
      ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
      FACTORY_CODE = #{factoryCode,jdbcType=DECIMAL},
      ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
    where ITEM_ADDRESS_ID = #{itemAddressId,jdbcType=DECIMAL}
  </update>

</mapper>
