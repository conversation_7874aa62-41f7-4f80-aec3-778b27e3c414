<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.TechItemStoreRepository">
    <!-- 获取单板重量信息 -->
    <select id="getTechItemStore" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.TechItemStoreDTO" >
        SELECT p.stock,p.OUT_AREA outArea,bbh.bom_id bomId,tis.item_id itemId,nvl(tis.WEIGHT,0) weight,bbh.bom_no bomNo,nvl(tis.VOLUME,0) volume,
        bbh.bom_name bomName,'中兴通讯' supplierName,nvl(tis.BOX_AMOUNT,0) boxAmount,nvl(tis.BOX_WEIGHT,0) boxWeight,
        nvl(tis.BOX_LENGTH,0) boxLength,nvl(tis.BOX_WIDTH,0) boxWidth,nvl(tis.BOX_HEIGHT,0) boxHeight,nvl(tis.SCALAGE,0) scalage
        FROM KXSTEPIII.OP_PRODPLAN P
        inner join kxstepiii.ba_bom_head bbh on p.BOM_ID=bbh.bom_id
        left join kxstepiii.tech_item_store tis on bbh.bom_id=tis.item_id
        WHERE P.PRODPLAN_ID = #{prodplanId, jdbcType=VARCHAR}
    </select>
    <!-- 校验是否维护单板重量信息 -->
    <select id="checkTechItemStore" parameterType="java.lang.String" resultType="java.lang.Integer" >
        SELECT count(1) FROM kxstepiii.tech_item_store
        where item_id = #{bomId,jdbcType=VARCHAR}
    </select>
    <!-- 新增单板重量信息 -->
    <insert id="insertTechItemStore" parameterType="com.zte.interfaces.step.dto.TechItemStoreDTO">
        insert into KXSTEPIII.tech_item_store (item_id,
        TYPE,
        box_amount,
        box_weight,
        box_length,
        box_width,
        box_height,
        volume,
        weight,
        scalage)
        values (#{bomId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
        #{boxAmount,jdbcType=DECIMAL}, #{boxWeight,jdbcType=DECIMAL}, #{boxLength,jdbcType=DECIMAL},
        #{boxWidth,jdbcType=DECIMAL}, #{boxHeight,jdbcType=DECIMAL}, #{volume,jdbcType=DECIMAL},
        #{weight,jdbcType=DECIMAL}, #{scalage,jdbcType=DECIMAL}
        )
    </insert>
    <!-- 修改单板重量信息 -->
    <update id="updateTechItemStore" parameterType="com.zte.interfaces.step.dto.TechItemStoreDTO">
        update KXSTEPIII.tech_item_store
        SET box_amount = #{boxAmount,jdbcType=DECIMAL},
            box_weight = #{boxWeight,jdbcType=DECIMAL},
            box_length = #{boxLength,jdbcType=DECIMAL},
            box_width = #{boxWidth,jdbcType=DECIMAL},
            box_height = #{boxHeight,jdbcType=DECIMAL},
            volume = #{volume,jdbcType=DECIMAL},
            weight = #{weight,jdbcType=DECIMAL},
            scalage = #{scalage,jdbcType=DECIMAL}
        where item_id = #{bomId,jdbcType=VARCHAR}
    </update>
</mapper>