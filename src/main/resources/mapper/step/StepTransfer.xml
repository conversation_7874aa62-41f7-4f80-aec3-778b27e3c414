<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StepTransferRepository">
    <resultMap id="AllocateBarcodeResultMap" type="com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo">
        <result column="item_barcode" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="diff" jdbcType="VARCHAR" property="storeAge" />
        <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    </resultMap>

    <select id="getCountJobUnknown" resultType="java.lang.Integer">
        SELECT count(1)
        FROM kxstepiii.sys_job_history h
        where h.job_name=#{jobName,jdbcType=VARCHAR}
        and h.status = 'UNKNOWN'
    </select>
    <insert id="insertJobHistory">
        INSERT INTO kxstepiii.sys_job_history
        (ins_id, job_owner, job_name, begin_date, status, host_name,ip_address)
        values
        (
        #{jobId,jdbcType=INTEGER},
        'KXSTEPIII',
        #{jobName,jdbcType=VARCHAR},
        SYSDATE,
        'UNKNOWN',
        sys_context('USERENV', 'HOST'),
        sys_context('USERENV', 'IP_ADDRESS')
        )
    </insert>
    <select id="getTransferBilToInfor" resultType="com.zte.domain.model.step.StepTransferBill">
        select distinct b.bill_no,b.stock_no
        from kxstepiii.infor_transfer_bill b
        where b.remark = '自动制作'
        and b.status = '02'
        and not exists (select 1
        from KXSTEPIII.INFOR_WMS_SOHEAD s
        where s.externalorderkey2 = b.bill_no)
        order by b.stock_no
    </select>
    <select id="getTransferSoHead" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader" parameterType="java.lang.String">
        SELECT '' AS ORDERKEY,
        '' AS EXTERNORDERKEY,
        '11' AS TYPE,
        'ZTE' AS STORERKEY,
        T.MAKED_DATE AS REQUESTEDSHIPDATE,
        T.BILL_NO AS EXTERNALORDERKEY2,
        '' AS CONSIGNEEKEY,
        '' AS ORDERSID,
        '' AS PROCESS_FLAG,
        '' AS ERROR_MESSAGE,
        '' AS HSUSR1,
        '' AS HSUSR2,
        '' AS HSUSR3,
        '' AS HSUSR4,
        '' AS HSUSR5,
        '' AS HREF01,
        '' AS HREF02,
        '' AS HREF03,
        '' AS HREF04,
        '' AS HREF05,
        '' AS HREF06,
        '' AS HREF07,
        '' AS HREF08,
        '' AS HREF09,
        '' AS HREF10,
        T.INFOR_TYPE AS HREF11,
        '' AS HREF12,
        '' AS HREF13,
        T.STOCK_NO AS HREF14,
        DECODE(T.DEST_STOCK_NO ,'-1',NULL,T.DEST_STOCK_NO ) AS HREF15,
        '1' AS HREF16,
        T.MAKED_BY AS HREF17,
        '' AS HREF18,
        '' AS HREF19,
        '' AS HREF20,
        '' AS HREF21,
        '' AS HREF22,
        '' AS HREF23,
        '' AS HREF24,
        T.REMARK AS HREF25,
        '' AS HREF26,
        '' AS HREF27,
        '' AS HREF28,
        '' AS HREF29,
        T.MAKED_DATE AS HREF30,
        T.MAKED_BY AS HREF31,
        '' AS HREF32,
        '' AS HREF33,
        '' AS HREF34,
        '' AS HREF35,
        '' AS HREF36,
        '' AS HREF37,
        '' AS HREF38,
        T.DELIVERYTO AS HREF39,
        '' AS HREF40,
        '' AS HREF41,
        '' AS HREF42,
        '' AS HREF43,
        '' AS HREF44,
        '' AS HREF45,
        '' AS HREF46,
        '' AS HREF47,
        '' AS HREF48,
        '' AS HREF49,
        NVL(T.ACCOUNT_TYPE,'N') AS HREF50,
        '' AS HREF51,
        '' AS HREF52,
        (SELECT T.CODE_DESC
        FROM KXSTEPIII.OP_BASIC_CODEINFO T
        WHERE T.CODE = T.DEST_DELI_ADDRESS
        AND T.CODE_TYPE = '交货地点') AS HREF53,
        '' AS HREF54,
        '' AS HREF55,
        '' AS HREF56,
        '' AS HREF57,
        '' AS HREF58,
        T.ISTEXT AS HREF59,
        '' HREF60,
        '' AS LAST_UPDATED_DATE
        FROM KXSTEPIII.INFOR_TRANSFER_BILL T WHERE T.ENABLED = 1 AND T.BILL_NO = #{billNo,jdbcType=VARCHAR}
    </select>

    <select id="getTransferSoDe" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail" parameterType="java.lang.String">
        select itb.BILL_NO AS EXTERNALORDERKEY2,
        itb.STOCK_NO AS WHSEID,
        '' ORDERLINENUMBER,
        to_char(itd.DETAIL_ID) EXTERNLINENO,
        itd.ITEM_NO SKU,
        'SKU' PACKKEY,
        itd.PLAN_QTY ORIGINALQTY,
        0 SHIPPEDQTY,
        '' UOM,
        0 ALLOWOVERPICK,
        '' PREALLOCATESTRATEGYKEY,
        '' ALLOCATESTRATEGYKEY,
        '' ALLOCATESTRATEGYTYPE,
        0 SHELFLIFE,
        '' ROTATION,
        '' SKUROTATION,
        '' LOTTABLE01,
        itd.ITEM_BARCODE LOTTABLE02,
        '' LOTTABLE03,
        '' LOTTABLE04,
        '' LOTTABLE05,

        DECODE(ITB.BILL_TYPE,
        '03',
        ITD.OWNER_CLASS_NO,
        '02',
        ITD.DST_OWNER_CLASS,

        DECODE(ITB.INS_TYPE,
        '01',
        'KX',
        '02',
        SIB.SUPPLIER_NO,
        '03',
        DECODE(INSTR(ITB.STOCK_NO || ITB.DEST_STOCK_NO,
        'HUB',
        1,
        1),
        0,
        'KX',
        SIB.SUPPLIER_NO))) LOTTABLE06,

        (select to_char(ITEM_UUID)
        from kxstepiii.st_item_barcode
        where ITEM_BARCODE = itd.ITEM_BARCODE) LOTTABLE07,
        '' LOTTABLE08,
        '' LOTTABLE09,
        '' LOTTABLE10,
        '' LOTTABLE11,
        '' LOTTABLE12,
        '' SUSR1,
        (select to_char(ITEM_ID)
        from kxstepiii.ba_item
        where ITEM_NO = itd.ITEM_NO) SUSR2,
        '' SUSR3,
        '' SUSR4,
        '' SUSR5,
        '' NOTES2,
        '' NOTES,
        to_char(itd.PLAN_QTY) REF01,
        '' REF02,
        '' REF03,
        '' REF04,
        '' REF05,
        '' REF06,
        '' REF07,
        '' REF08,
        (select to_char(SUPPLIER_NO)
        from kxstepiii.st_item_barcode
        where ITEM_BARCODE = itd.ITEM_BARCODE) REF09,
        '' REF10,
        '' REF11,
        '' REF12,
        '' REF13,
        '' REF14,
        '' REF15,
        decode(itb.INS_TYPE, '01', '调拨指令', '02', '配送指令', '03', '无') REF16,
        itb.INS_TYPE REF17,
        (SELECT OP.PRODUCT_CLASS_NAME
        FROM KXSTEPIII.OP_PRODUCTINFO OP
        WHERE itd.PURPOSE_BIGCLASS = OP.PRODUCT_CLASS
        AND op.entp_no = 'S'
        AND ROWNUM = 1) REF18,
        itd.PURPOSE_BIGCLASS REF19,
        (select DPP.PLANNING_GROUP_DESC
        from LMS.DB_PRODCLASS_PLANGROUP DPP
        WHERE itd.PURPOSE_PLAN = DPP.PLANNING_GROUP
        AND ROWNUM = 1) REF20,
        itd.PURPOSE_PLAN REF21,
        '' REF22,
        '' REF23,
        '' REF24,
        TO_CHAR(itd.DELIVERY_ID) REF25,
        '' REF26,
        '' REF27,
        '' REF28,
        '' REF29,
        '' REF30,
        (SELECT DISTINCT op.product_class_name
        FROM kxstepiii.op_productinfo op
        WHERE op.product_class = itd.source_bigclass
        AND op.entp_no = 'S'
        AND rownum = 1) REF31,
        itd.SOURCE_BIGCLASS REF32,
        (SELECT DISTINCT dpp.planning_group_desc
        FROM lms.db_prodclass_plangroup dpp
        WHERE dpp.planning_group = itd.source_plan
        AND dpp.entp_no = 'S'
        AND rownum = 1) REF33,
        itd.SOURCE_PLAN REF34,
        '' REF35,
        (select to_char(max(t.project_id))
        From lms.db_prodclass_plangroup t
        where t.product_class = itd.source_bigclass
        and t.entp_no = 'S') REF36,
        '' REF37,
        (select to_char(max(t.project_id))
        From lms.db_prodclass_plangroup t
        where t.product_class = itd.purpose_bigclass
        and t.entp_no = 'S') REF38,
        '' REF39,
        '' REF40,
        '' REF41,
        '' REF42,
        '' REF43,
        '' REF44,
        '' REF45,
        DECODE(ITB.SRC_AREA,
        NULL,
        NULL,
        (SELECT CODE_DESC
        FROM KXSTEPIII.ST_CODEINFO SCI
        WHERE SCI.CODE_TYPE = 'INFOR区域'
        AND SCI.CODE = ITB.SRC_AREA)) REF46,
        '' REF47,
        '' REF48,
        '' REF49,
        '' REF50,
        DECODE(ITB.DEST_AREA,
        NULL,
        NULL,
        (SELECT CODE_DESC
        FROM KXSTEPIII.ST_CODEINFO SCI
        WHERE SCI.CODE_TYPE = 'INFOR区域'
        AND SCI.CODE = ITB.DEST_AREA)) REF51,
        '' REF52,
        '' REF53,
        '' REF54,
        '' REF55,
        '' REF56,
        '' REF57,
        '' REF58,
        '' REF59,
        '' REF60
        from KXSTEPIII.INFOR_TRANSFER_DETAIL itd
        JOIN kxstepiii.infor_transfer_bill itb ON itd.bill_id = itb.bill_id
        LEFT JOIN kxstepiii.st_item_barcode sib ON itd.item_barcode =
        sib.item_barcode
        WHERE itd.enabled = 1
        and itb.BILL_NO = #{billNo,jdbcType=VARCHAR}
    </select>
    <insert id="insertTransferSoHead" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
        insert into KXSTEPIII.INFOR_WMS_SOHEAD
        (ORDERKEY,EXTERNORDERKEY,STORERKEY,REQUESTEDSHIPDATE,EXTERNALORDERKEY2,CONSIGNEEKEY,ORDERSID,HSUSR1,HSUSR2,HSUSR3,HSUSR4,HSUSR5,HREF01,HREF02,HREF03,HREF04,HREF05,HREF06,HREF07,HREF08,HREF09,HREF10,HREF11,HREF12,HREF13,HREF14,HREF15,HREF16,HREF17,HREF18,HREF19,HREF20,HREF21,HREF22,HREF23,HREF24,HREF25,HREF26,HREF27,HREF28,HREF29,HREF30,HREF31,HREF32,HREF33,HREF34,HREF35,HREF36,HREF37,HREF38,HREF39,HREF40,HREF41,HREF42,HREF43,HREF44,HREF45,HREF46,HREF47,HREF48,HREF49,HREF50,HREF51,HREF52,HREF53,HREF54,HREF55,HREF56,HREF57,HREF58,HREF59,HREF60,PROCESS_FLAG,TYPE,ERROR_MESSAGE)
        values
        (#{item.orderKey,jdbcType=VARCHAR}, #{item.externOrderKey,jdbcType=VARCHAR},
            #{item.storerKey,jdbcType=VARCHAR},TO_DATE(#{item.requestedShipDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'),
            #{item.externalOrderKey2,jdbcType=VARCHAR},#{item.consigneeKey,jdbcType=VARCHAR},
            #{item.ordersId,jdbcType=VARCHAR},#{item.hsusr1,jdbcType=VARCHAR},
            #{item.hsusr2,jdbcType=VARCHAR},#{item.hsusr3,jdbcType=VARCHAR},
            #{item.hsusr4,jdbcType=VARCHAR},#{item.hsusr5,jdbcType=VARCHAR},
            #{item.href01,jdbcType=VARCHAR},#{item.href02,jdbcType=VARCHAR},
            #{item.href03,jdbcType=VARCHAR},#{item.href04,jdbcType=VARCHAR},
            #{item.href05,jdbcType=VARCHAR},#{item.href06,jdbcType=VARCHAR},
            #{item.href07,jdbcType=VARCHAR},#{item.href08,jdbcType=VARCHAR},
            #{item.href09,jdbcType=VARCHAR},#{item.href10,jdbcType=VARCHAR},
            #{item.href11,jdbcType=VARCHAR},#{item.href12,jdbcType=VARCHAR},
            #{item.href13,jdbcType=VARCHAR},#{item.href14,jdbcType=VARCHAR},
            #{item.href15,jdbcType=VARCHAR},#{item.href16,jdbcType=VARCHAR},
            #{item.href17,jdbcType=VARCHAR},#{item.href18,jdbcType=VARCHAR},
            #{item.href19,jdbcType=VARCHAR},#{item.href20,jdbcType=VARCHAR},
            #{item.href21,jdbcType=VARCHAR},#{item.href22,jdbcType=VARCHAR},
            #{item.href23,jdbcType=VARCHAR},#{item.href24,jdbcType=VARCHAR},
            #{item.href25,jdbcType=VARCHAR},TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE(#{item.href30, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS'),
            #{item.href31,jdbcType=VARCHAR},#{item.href32,jdbcType=VARCHAR},
            #{item.href33,jdbcType=VARCHAR},#{item.href34,jdbcType=VARCHAR},
            #{item.href35,jdbcType=VARCHAR},#{item.href36,jdbcType=VARCHAR},
            #{item.href37,jdbcType=VARCHAR},#{item.href38,jdbcType=VARCHAR},
            #{item.href39,jdbcType=VARCHAR},#{item.href40,jdbcType=VARCHAR},
            #{item.href41,jdbcType=VARCHAR},#{item.href42,jdbcType=VARCHAR},
            #{item.href43,jdbcType=VARCHAR},#{item.href44,jdbcType=VARCHAR},
            #{item.href45,jdbcType=VARCHAR},#{item.href46,jdbcType=VARCHAR},
            #{item.href47,jdbcType=VARCHAR},#{item.href48,jdbcType=VARCHAR},
            #{item.href49,jdbcType=VARCHAR},#{item.href50,jdbcType=VARCHAR},
            #{item.href51,jdbcType=VARCHAR},#{item.href52,jdbcType=VARCHAR},
            #{item.href53,jdbcType=VARCHAR},#{item.href54,jdbcType=VARCHAR},
            #{item.href55,jdbcType=VARCHAR},#{item.href56,jdbcType=VARCHAR},
            #{item.href57,jdbcType=VARCHAR},#{item.href58,jdbcType=VARCHAR},
            #{item.href59,jdbcType=VARCHAR},#{item.href60,jdbcType=VARCHAR},3,
            #{item.type,jdbcType=VARCHAR},'')
        </foreach>
    </insert>
    <insert id="insertTransferSoDetail" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            insert into KXSTEPIII.INFOR_WMS_SODETAIL
            (EXTERNLINENO,SKU,ORIGINALQTY,LOTTABLE01,LOTTABLE02,LOTTABLE03,LOTTABLE04,LOTTABLE05,LOTTABLE06,LOTTABLE07,LOTTABLE08,LOTTABLE09,LOTTABLE10,LOTTABLE11,LOTTABLE12,SUSR1,SUSR2,SUSR3,SUSR4,SUSR5,NOTES2,NOTES,REF01,REF02,REF03,REF04,REF05,REF06,REF07,REF08,REF09,REF10,REF11,REF12,REF13,REF14,REF15,REF16,REF17,REF18,REF19,REF20,REF21,REF22,REF23,REF24,REF25,REF26,REF27,REF28,REF29,REF30,REF31,REF32,REF33,REF34,REF35,REF36,REF37,REF38,REF39,REF40,REF41,REF42,REF43,REF44,REF45,REF46,REF47,REF48,REF49,REF50,REF51,REF52,REF53,REF54,REF55,REF56,REF57,REF58,REF59,REF60,EXTERNALORDERKEY2,ORDERLINENUMBER,WHSEID,PACKKEY,SHIPPEDQTY,UOM,ALLOWOVERPICK,PREALLOCATESTRATEGYKEY,ALLOCATESTRATEGYKEY,ALLOCATESTRATEGYTYPE,SHELFLIFE,ROTATION,SKUROTATION)
            values
            (#{item.externLineNo,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR},
            #{item.originalQty,jdbcType=VARCHAR}, #{item.lottable01,jdbcType=VARCHAR},
            #{item.lottable02,jdbcType=VARCHAR}, #{item.lottable03,jdbcType=VARCHAR},
            TO_DATE('1900/1/1', 'YYYY-MM-DD'),TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            #{item.lottable06,jdbcType=VARCHAR}, #{item.lottable07,jdbcType=VARCHAR},
            #{item.lottable08,jdbcType=VARCHAR}, #{item.lottable09,jdbcType=VARCHAR},
            #{item.lottable10,jdbcType=VARCHAR},  TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), #{item.susr1,jdbcType=VARCHAR},
            #{item.susr2,jdbcType=VARCHAR}, #{item.susr3,jdbcType=VARCHAR},
            #{item.susr4,jdbcType=VARCHAR}, #{item.susr5,jdbcType=VARCHAR},
            #{item.notes2,jdbcType=VARCHAR}, #{item.notes,jdbcType=VARCHAR},
            #{item.ref01,jdbcType=VARCHAR}, #{item.ref02,jdbcType=VARCHAR},
            #{item.ref03,jdbcType=VARCHAR}, #{item.ref04,jdbcType=VARCHAR},
            #{item.ref05,jdbcType=VARCHAR}, #{item.ref06,jdbcType=VARCHAR},
            #{item.ref07,jdbcType=VARCHAR}, #{item.ref08,jdbcType=VARCHAR},
            #{item.ref09,jdbcType=VARCHAR}, #{item.ref10,jdbcType=VARCHAR},
            #{item.ref11,jdbcType=VARCHAR}, #{item.ref12,jdbcType=VARCHAR},
            #{item.ref13,jdbcType=VARCHAR}, #{item.ref14,jdbcType=VARCHAR},
            #{item.ref15,jdbcType=VARCHAR}, #{item.ref16,jdbcType=VARCHAR},
            #{item.ref17,jdbcType=VARCHAR}, #{item.ref18,jdbcType=VARCHAR},
            #{item.ref19,jdbcType=VARCHAR}, #{item.ref20,jdbcType=VARCHAR},
            #{item.ref21,jdbcType=VARCHAR}, #{item.ref22,jdbcType=VARCHAR},
            #{item.ref23,jdbcType=VARCHAR}, #{item.ref24,jdbcType=VARCHAR},
            #{item.ref25,jdbcType=VARCHAR},  TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            TO_DATE('1900/1/1', 'YYYY-MM-DD'), TO_DATE('1900/1/1', 'YYYY-MM-DD'),
            #{item.ref31,jdbcType=VARCHAR}, #{item.ref32,jdbcType=VARCHAR},
            #{item.ref33,jdbcType=VARCHAR}, #{item.ref34,jdbcType=VARCHAR},
            #{item.ref35,jdbcType=VARCHAR}, #{item.ref36,jdbcType=VARCHAR},
            #{item.ref37,jdbcType=VARCHAR}, #{item.ref38,jdbcType=VARCHAR},
            #{item.ref39,jdbcType=VARCHAR}, #{item.ref40,jdbcType=VARCHAR},
            #{item.ref41,jdbcType=VARCHAR}, #{item.ref42,jdbcType=VARCHAR},
            #{item.ref43,jdbcType=VARCHAR}, #{item.ref44,jdbcType=VARCHAR},
            #{item.ref45,jdbcType=VARCHAR}, #{item.ref46,jdbcType=VARCHAR},
            #{item.ref47,jdbcType=VARCHAR}, #{item.ref48,jdbcType=VARCHAR},
            #{item.ref49,jdbcType=VARCHAR}, #{item.ref50,jdbcType=VARCHAR},
            #{item.ref51,jdbcType=VARCHAR}, #{item.ref52,jdbcType=VARCHAR},
            #{item.ref53,jdbcType=VARCHAR}, #{item.ref54,jdbcType=VARCHAR},
            #{item.ref55,jdbcType=VARCHAR}, #{item.ref56,jdbcType=VARCHAR},
            #{item.ref57,jdbcType=VARCHAR}, #{item.ref58,jdbcType=VARCHAR},
            #{item.ref59,jdbcType=VARCHAR}, #{item.ref60,jdbcType=VARCHAR},
            #{item.ref60,jdbcType=VARCHAR}, '',
            #{item.whseid,jdbcType=VARCHAR}, #{item.packKey,jdbcType=VARCHAR},
            #{item.shippedQty,jdbcType=VARCHAR}, #{item.uom,jdbcType=VARCHAR},
            #{item.allowOverPick,jdbcType=VARCHAR}, #{item.preAllocateStrategyKey,jdbcType=VARCHAR},
            #{item.allocateStrategyKey,jdbcType=VARCHAR}, #{item.allocateStrategyType,jdbcType=VARCHAR},
            #{item.shelfLife,jdbcType=VARCHAR}, #{item.rotation,jdbcType=VARCHAR}, #{item.skuRotation,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="getTransferDetail" resultType="com.zte.domain.model.step.StepTransferDetail">
        SELECT NVL(D.PLAN_QTY, 0) AS PLAN_QTY, D.DELIVERY_ID, D.ITEM_BARCODE
        FROM KXSTEPIII.INFOR_TRANSFER_DETAIL D
        WHERE D.ENABLED = 1
        AND D.BILL_ID = (SELECT B.BILL_ID
        FROM KXSTEPIII.INFOR_TRANSFER_BILL B
        WHERE B.BILL_NO = #{billNo,jdbcType=VARCHAR})
    </select>
    <update id="updateDeliveryTransfer" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE KXSTEPIII.OP_DELIVERY_TRANSFER T
            <set>
                T.SHEET_QTY = NVL(T.SHEET_QTY, 0) - #{item.planQty,jdbcType=DECIMAL},
                T.STATUS = 'A'
            </set>
            <where>
                 T.TRANSFER_ID = #{item.deliveryId,jdbcType=VARCHAR}
            </where>
        </foreach>
    </update>
    <update id="updateTransferStatus" parameterType="java.lang.String">
        UPDATE KXSTEPIII.INFOR_TRANSFER_BILL
        SET STATUS        = '01',
        MAKED_BY          = '',
        MAKED_DATE        = TO_DATE('1900/1/1', 'YYYY-MM-DD'),
        LAST_UPDATED_DATE = SYSDATE
        WHERE BILL_NO = #{billNo,jdbcType=VARCHAR}
    </update>
    <select id="getJobId" resultType="java.lang.Integer">
        SELECT  kxstepiii.s_sys_job_history.nextval  FROM DUAL
    </select>
    <update id="updateJobHistory">
        UPDATE kxstepiii.sys_job_history sjh
        SET sjh.end_date = SYSDATE,
        sjh.status   = #{status,jdbcType=VARCHAR},
        sjh.errors   = #{errors,jdbcType=VARCHAR}
        WHERE sjh.ins_id = #{jobId,jdbcType=INTEGER}
    </update>
    <select id="getEmailUser" parameterType="java.lang.String" resultType="java.lang.String">
        select distinct lookup_meaning
        from kxstepiii.sys_lookup_values
        where enabled_flag = 'Y'
        and lookup_type = #{lookUpType,jdbcType=VARCHAR}
    </select>
	
	<resultMap id="OverTimeResultMap" type="com.zte.interfaces.material.dto.OverTimeBarcodeDTO">
        <result column="itemBarcodes" jdbcType="VARCHAR" property="itemBarcodes" />

    </resultMap>

    <select id="getValidateBarcode" parameterType="com.zte.interfaces.material.dto.OverTimeInDTO" resultMap="OverTimeResultMap">
        select distinct B.ITEM_BARCODE as itemBarcodes
        FROM KXSTEPIII.ST_ITEM_BARCODE B,KXSTEPIII.BA_ITEM_SUPPLIER F, KXSTEPIII.TECH_ITEM_STORE TIS
        WHERE B.ITEM_BARCODE IN
        <trim suffixOverrides=" OR B.ITEM_BARCODE IN()">    <!-- 表示删除最后一个条件 -->
            <foreach collection="itemBarcode" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR B.ITEM_BARCODE IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>

        and B.ITEM_UUID = F.ITEM_UUID
        AND B.SUPPLIER_NO = F.SUPPLIER_NO
        and B.ITEM_ID = TIS.ITEM_ID(+)
        and (trunc(B.product_date -sysdate) + NVL(F.quality_Time,TIS.QUALITY_SUP_TIME)*30+F.quality_new_time*60)<![CDATA[<0]]>

    </select>

    <select id="getStockAgeByBarcode" parameterType="java.util.List"  resultMap="AllocateBarcodeResultMap">
        select distinct bi.item_barcode, trunc(sysdate - bi.come_date) as diff
        from kxstepiii.st_item_barcode bi
        where bi.item_barcode IN
        <foreach collection="list" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp}
        </foreach>
        and bi.come_date is not null

    </select>

    <select id="getIqcPlanningGroups" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionPkgDTO">
        select distinct op.planning_group planningGroup,op.product_class productClass,
        op.planning_group_desc planningGroupDesc,op.project_name productClassDesc
        from lms.db_prodclass_plangroup op
        where op.product_class in
        <foreach collection="productClasss" index="index" item="productClass" open="(" separator="," close=")">
            #{productClass}
        </foreach>
        and op.entp_no = 'S'
        and op.planning_group is not null
    </select>

    <select id="getUtilityInfo" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader" parameterType="java.lang.String">
        select to_char(su.organization_id) href01,su.utility_name href04,su.subject href05
        from kxstepiii.st_utility su
        where su.utility_no = #{utilityNo,jdbcType=VARCHAR}
        and rownum = 1
    </select>
    <select id="getMaterialName" parameterType="java.lang.String" resultType="java.lang.String">
        select bi.full_name from kxstepiii.ba_person_info bi where bi.card_no =  #{cardNo,jdbcType=VARCHAR} and rownum =1
    </select>
    <select id="getPlaceName" parameterType="java.lang.String" resultType="java.lang.String">
        select sc.code_desc
        from kxstepiii.st_codeinfo sc
        where sc.code_type = '配送类型'
        and sc.code =  #{placeNo,jdbcType=VARCHAR}
        and rownum =1
    </select>

    <select id="getMaxStockAgeByBarcode" parameterType="java.util.List"  resultMap="AllocateBarcodeResultMap">
        SELECT bi.item_no, MAX(trunc(sysdate) - trunc(sib.come_date)) AS diff
        FROM kxstepiii.st_item_barcode sib
        JOIN kxstepiii.ba_item bi ON bi.item_id = sib.item_id
        WHERE bi.item_no IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and sib.come_date IS NOT NULL
        GROUP BY bi.item_no
        ORDER BY bi.item_no
    </select>
</mapper>