<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StepWarnRepository">
	<select id="getAllBillNo" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT B.BILL_NO BILLNO
		FROM KXSTEPIII.INFOR_OMSALES_BILL B
		WHERE B.BILL_TYPE = 0
		AND B.STATUS IN ('GTSAUDITING', 'AUDITING')
		AND B.LAST_UPDATED_DATE >= ADD_MONTHS(SYSDATE, -3)
	</select>
	<select id="queryAllGtsList" resultType="java.lang.String">
		SELECT B.BILL_NO
		FROM KXSTEPIII.INFOR_OMSALES_BILL B
		WHERE B.BILL_TYPE = 0
		AND B.STATUS IN ('GTSAUDITING', 'AUDITING')
		AND B.LAST_UPDATED_DATE >= ADD_MONTHS(SYSDATE, -3)
		and (B.factory_org_id != '31' or B.sales_type != '1')
		AND NOT EXISTS (SELECT 1
		FROM KXSTEPIII.GTS_ITEMS G
		WHERE G.STATUS = 1
		AND G.IN_GUID = B.BILL_NO)
	</select>
	<select id="queryAllEcssList" resultType="java.lang.String">
		SELECT B.BILL_NO
		FROM KXSTEPIII.INFOR_OMSALES_BILL B
		WHERE B.BILL_TYPE = 0
		AND B.STATUS IN ('GTSAUDITING', 'AUDITING')
		AND B.LAST_UPDATED_DATE >= ADD_MONTHS(SYSDATE, -3)
		and (B.factory_org_id != '31' or B.sales_type != '1')
		AND NOT EXISTS (SELECT 1
		FROM KXSTEPIII.ECSS_ITEMS S
		WHERE S.ENABLED_FLAG = 'Y'
		AND S.IN_GUID = B.BILL_NO)
	</select>
</mapper>