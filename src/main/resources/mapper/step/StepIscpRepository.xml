<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.StepIscpRepository">
	<resultMap id="EdiIscpDataMap" type="com.zte.action.iscpedi.model.EdiIscpData">
		<result column="serialKey" jdbcType="BIGINT" property="serialKey" />
		<result column="deliBillNo" jdbcType="VARCHAR" property="deliBillNo" />
		<result column="rowNo" jdbcType="VARCHAR" property="rowNo" />
		<result column="operateType" jdbcType="VARCHAR" property="operateType" />
		<result column="sourceTable" jdbcType="VARCHAR" property="sourceTable" />
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
		<result column="actualQty" jdbcType="DECIMAL" property="actualQty" />
		<result column="anticipatedQty" jdbcType="DECIMAL" property="anticipatedQty" />
		<result column="boxNo" jdbcType="VARCHAR" property="boxNo" />
		<result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
		<result column="sku" jdbcType="VARCHAR" property="sku" />
		<result column="operateTime" jdbcType="VARCHAR" property="operateTime" />
		<result column="batchNo" jdbcType="VARCHAR" property="batchNo" />
	</resultMap>


	<select id="getitemUuidAndEnv" resultType="com.zte.interfaces.infor.vo.ItemUUIDInforVo">
		SELECT
		BIB.BRAND_NO brandNo,
		BB.BRAND_NAME brandName,
		SIB.ITEM_UUID itemUuid,
		SIB.SUPPLIER_NO supplierNo,
		SIB.PRODUCT_NO batchNo,
		SIB.IS_LEAD envProperty,
		to_char(SIB.PRODUCT_DATE , 'yyyy-mm-dd') productionDate,
		SIB.item_barcode itemBarcode,
		BIB.BRAND_STYLE brandStyle
		FROM KXSTEPIII.ST_ITEM_BARCODE SIB
		LEFT JOIN KXSTEPIII.BA_ITEM_BRANDSTYLE BIB ON BIB.ITEM_UUID =
		SIB.ITEM_UUID
		LEFT JOIN KXSTEPIII.BA_BRAND BB ON BB.BRAND_NO = BIB.BRAND_NO
		where   SIB.item_barcode=#{itemBarcode}
	</select>
	<select id="getTransferSoHeader" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader" parameterType="java.lang.String">
		SELECT '' AS ORDERKEY,
		'' AS EXTERNORDERKEY,
		'11' AS TYPE,
		'ZTE' AS STORERKEY,
		'' AS REQUESTEDSHIPDATE,
		'' AS EXTERNALORDERKEY2,
		'' AS CONSIGNEEKEY,
		'' AS ORDERSID,
		'' AS PROCESS_FLAG,
		'' AS ERROR_MESSAGE,
		'' AS HSUSR1,
		'' AS HSUSR2,
		'' AS HSUSR3,
		'' AS HSUSR4,
		'' AS HSUSR5,
		-- 来源库存组织
		'' AS HREF01,
		-- 来源子库存
		'' AS HREF02,
		-- 来源货位
		'' AS HREF03,
		'' AS HREF04,
		'' AS HREF05,
		'' AS HREF06,
		-- 目标库存组织
		'' AS HREF07,
		-- 目标子库存
		'' AS HREF08,
		-- 目标货位
		'' AS HREF09,
		'' AS HREF10,
		'472' AS HREF11,
		'' AS HREF12,
		'' AS HREF13,
		OST.INFOR_STOCK_NO AS HREF14,
		OST.INFOR_STOCK_NO AS HREF15,
		'0' AS HREF16,
		'' AS HREF17,
		'' AS HREF18,
		'' AS HREF19,
		'' AS HREF20,
		'' AS HREF21,
		'' AS HREF22,
		'' AS HREF23,
		'' AS HREF24,
		'' AS HREF25,
		'' AS HREF26,
		'' AS HREF27,
		'' AS HREF28,
		'' AS HREF29,
		'' AS HREF30,
		'' AS HREF31,
		'' AS HREF32,
		'' AS HREF33,
		'' AS HREF34,
		'' AS HREF35,
		'' AS HREF36,
		'' AS HREF37,
		'' AS HREF38,
		'' AS HREF39,
		'' AS HREF40,
		'' AS HREF41,
		'' AS HREF42,
		'' AS HREF43,
		'' AS HREF44,
		'' AS HREF45,
		'' AS HREF46,
		'' AS HREF47,
		'' AS HREF48,
		'' AS HREF49,
		'' AS HREF50,
		'' AS HREF51,
		'' AS HREF52,
		'' AS HREF53,
		'' AS HREF54,
		'' AS HREF55,
		'' AS HREF56,
		'' AS HREF57,
		'' AS HREF58,
		'' AS HREF59,
		'' AS HREF60
		FROM KXSTEPIII.OP_STOCKORG_TRANSFER  OST
		WHERE OST.ENABLED = 1
		AND OST.TRANSFER_ID = #{transfer_id}
	</select>
	<select id="getTransferSoDetail" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail" parameterType="java.lang.String">
		SELECT '' AS EXTERNALORDERKEY2,
		OST.INFOR_STOCK_NO AS WHSEID,
		'' ORDERLINENUMBER,
		'' EXTERNLINENO,
		OST.ITEM_NO SKU,
		'SKU' PACKKEY,
		OST.TRANSFER_QTY ORIGINALQTY,
		0 SHIPPEDQTY,
		'' UOM,
		0 ALLOWOVERPICK,
		'' PREALLOCATESTRATEGYKEY,
		'' ALLOCATESTRATEGYKEY,
		'' ALLOCATESTRATEGYTYPE,
		0 SHELFLIFE,
		'' ROTATION,
		'' SKUROTATION,
		'' LOTTABLE01,
		-- 条码
		OST.ITEM_BARCODE LOTTABLE02,
		OST.SRC_STOCK_ORG_ID LOTTABLE03,
		'' LOTTABLE04,
		'' LOTTABLE05,
		-- 货主分类
		'' LOTTABLE06,
		'' LOTTABLE07,
		'' LOTTABLE08,
		'' LOTTABLE09,
		'' LOTTABLE10,
		'' LOTTABLE11,
		'' LOTTABLE12,
		'' SUSR1,
		'' SUSR2,
		'' SUSR3,
		'' SUSR4,
		'' SUSR5,
		'' NOTES2,
		'' NOTES,
		'' REF01,
		'' REF02,
		'' REF03,
		'' REF04,
		'' REF05,
		'' REF06,
		'' REF07,
		'' REF08,
		'' REF09,
		'' REF10,
		'' REF11,
		'' REF12,
		'' REF13,
		'' REF14,
		'' REF15,
		'库存转组织指令' REF16,
		-- 指令类型
		OST.TRANSFER_TYPE REF17,
		'' REF19,
		'' REF20,
		'' REF21,
		'' REF22,
		'' REF23,
		'' REF24,
		-- 转货主指令号
		OST.TRANSFER_ID REF25,
		'' REF26,
		'' REF27,
		'' REF28,
		'' REF29,
		'' REF30,
		'' REF31,
		'' REF32,
		'' REF33,
		'' REF34,
		'' REF35,
		'' REF36,
		'' REF37,
		'' REF38,
		-- 目的货主分类
		'' REF39,
		'' REF40,
		OST.DST_STOCK_ORG_ID REF41,
		'' REF42,
		'' REF43,
		'' REF44,
		'' REF45,
		'' REF46,
		'' REF47,
		'' REF48,
		'' REF49,
		'' REF50,
		'' REF51,
		'' REF52,
		'' REF53,
		'' REF54,
		'' REF55,
		'' REF56,
		'' REF57,
		'' REF58,
		'' REF59,
		'' REF60
		FROM KXSTEPIII.OP_STOCKORG_TRANSFER  OST
		WHERE OST.ENABLED = 1
		AND OST.transfer_id = #{transferId, jdbcType=VARCHAR}
	</select>


	<!--增加单据-->
	<insert id="insertTransferBill"  parameterType="com.zte.domain.model.step.InforTransferBill" >
		<selectKey keyProperty="billId" order="BEFORE" resultType="String">
			SELECT KXSTEPIII.infor_transfer_s.nextval  FROM DUAL
		</selectKey>
		INSERT INTO KXSTEPIII.INFOR_TRANSFER_BILL
		(CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED,BILL_ID,BILL_NO,TRANSFER_TYPE,IS_INFOR,INFOR_TYPE,STOCK_NO,DEST_STOCK_NO,SOURCE_CLASS,PURPOSE_CLASS,MAKED_BY,MAKED_DATE,REMARK)
		values
		(
		#{createdBy,jdbcType=VARCHAR},
		to_date(#{creationDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
		#{lastUpdatedBy,jdbcType=VARCHAR},
		to_date(#{lastUpdatedDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
		#{enabled,jdbcType=INTEGER},
		#{billId,jdbcType=VARCHAR},
		#{billNo,jdbcType=VARCHAR},
		#{transferType,jdbcType=VARCHAR},
		#{inforFlag,jdbcType=INTEGER},
		#{inforType,jdbcType=VARCHAR},
		#{stockNo,jdbcType=VARCHAR},
		#{destStockNo,jdbcType=VARCHAR},
		#{sourceClass,jdbcType=VARCHAR},
		#{purposeClass,jdbcType=VARCHAR},
		#{makedBy,jdbcType=VARCHAR},
		to_date(#{makedDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
		#{remark,jdbcType=VARCHAR}
		)
	</insert>

	<select id="selectBillId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT BILL_ID FROM KXSTEPIII.INFOR_TRANSFER_BILL where bill_no=#{billNo,jdbcType=VARCHAR}
	</select>
	<!--新增单据明细-->
	<insert id="insertTransferDetail" parameterType="com.zte.domain.model.step.InforTransferDetail" >
		<selectKey keyProperty="detailId" order="BEFORE" resultType="int">
			SELECT KXSTEPIII.infor_transfer_detail_s.nextval  FROM DUAL
		</selectKey>
		insert into KXSTEPIII.INFOR_TRANSFER_DETAIL
		(CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATED_DATE,ENABLED,DETAIL_ID,BILL_ID,DELIVERY_ID,ITEM_NO,ITEM_BARCODE,PLAN_QTY,OWNER_CLASS_NO)
		values
		(
		#{createdBy,jdbcType=VARCHAR},
		to_date(#{creationDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
		#{lastUpdatedBy,jdbcType=VARCHAR},
		to_date(#{lastUpdatedDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
		#{enabled,jdbcType=INTEGER},
		to_number(#{detailId,jdbcType=VARCHAR}),
		#{billId,jdbcType=VARCHAR},
		#{deliveryId,jdbcType=VARCHAR},
		#{itemNo,jdbcType=VARCHAR},
		#{itemBarcode,jdbcType=VARCHAR},
		to_number(#{planQty,jdbcType=DECIMAL}),
		#{ownerClassNo,jdbcType=VARCHAR})
	</insert>

	<select id="selectSteprData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select max(pdb.batch_id) serialkey,
		D.DELI_NO deliBillNo,
		D.ROWNO rowNo,
		'08' operateType,
		'ST_RECEIVECHECK' sourceTable,
		s.STOCK_NO whseid,
		0 actualQty,
		0 anticipatedQty,
		'' boxNo,
		'' lottable02,
		'' sku,
		to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') operateTime,
		'' batchNo
		from KXSTEPIII.ST_RECEIVECHECK s, kxscmiii.pu_deli_detail D,kxscmiii.pu_deli_batch pdb
		where s.deli_no = d.deli_no and d.delidetail_id = pdb.delidetail_id
		and
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator="or">
			(pdb.batch_id = #{tmp.serialkey, jdbcType=VARCHAR} and d.ROWNO = #{tmp.externlineno, jdbcType=VARCHAR})
		</foreach>
		group by d.delidetail_id,d.ROWNO, D.DELI_NO, s.STOCK_NO,pdb.item_barcode

	</select>

	<select id="selectStepsData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select max(pdb.batch_id) serialkey,
		D.DELI_NO deliBillNo,
		D.ROWNO rowNo,
		'01' operateType,
		'ST_RECEIVECHECK_S' sourceTable,
		s.STOCK_NO whseid,
		0 actualQty,
		0 anticipatedQty,
		'' boxNo,
		'' lottable02,
		'' sku,
		to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') operateTime,
		'' batchNo
		from KXSTEPIII.ST_RECEIVECHECK s, kxscmiii.pu_deli_detail D,kxscmiii.pu_deli_batch pdb
		where s.deli_no = d.deli_no and d.delidetail_id = pdb.delidetail_id
		and
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator="or">
			(pdb.batch_id = #{tmp.serialkey, jdbcType=VARCHAR} and d.ROWNO = #{tmp.externlineno, jdbcType=VARCHAR})
		</foreach>
		group by d.delidetail_id,d.ROWNO, D.DELI_NO, s.STOCK_NO,pdb.item_barcode

	</select>

	<resultMap id="barcodeMap"  type="com.zte.action.iscpedi.model.BarCodeInfo">
		<result column="serialkey" jdbcType="BIGINT" property="serialkey" />
		<result column="actualQty" jdbcType="VARCHAR" property="actualQty" />
		<result column="anticipatedQty" jdbcType="VARCHAR" property="anticipatedQty" />
		<result column="boxNo" jdbcType="VARCHAR" property="boxNo" />
		<result column="barCode" jdbcType="VARCHAR" property="barCode" />
		<result column="operateTime" jdbcType="VARCHAR" property="operateTime" />
		<result column="batchNo" jdbcType="VARCHAR" property="batchNo" />
	</resultMap>
	<select id="selectBatchData" parameterType="com.zte.action.iscpedi.model.EdiIscpData" resultMap="barcodeMap">
		select max(pdb.batch_id) serialkey,
		sum(pdb.batch_qty) actualQty,
		sum(pdb.batch_qty) anticipatedQty,
		'' boxNo,
		pdb.item_barcode barCode,
		to_char(SYSDATE, 'yyyy-MM-dd HH24:mi:ss') operateTime,
		'' batchNo
		from kxscmiii.pu_deli_detail pdd
		join kxscmiii.pu_deli_batch pdb
		on pdb.delidetail_id = pdd.delidetail_id
		where pdd.deli_no =  #{deliBillNo, jdbcType=VARCHAR}
		and pdd.rowno = #{rowNo, jdbcType=VARCHAR}
		group by pdd.delidetail_id, pdb.item_barcode
	</select>

	<select id="selectBatchSData" parameterType="com.zte.action.iscpedi.model.EdiIscpData" resultMap="barcodeMap">
		select max(pdb.batch_id) serialkey,
		sum(pdb.batch_qty) actualQty,
		sum(pdb.batch_qty) anticipatedQty,
		'' boxNo,
		pdb.item_barcode barCode,
		to_char(SYSDATE, 'yyyy-MM-dd HH24:mi:ss') operateTime,
		'' batchNo
		from kxscmiii.pu_deli_detail pdd
		join kxscmiii.pu_deli_batch pdb
		on pdb.delidetail_id = pdd.delidetail_id
		where pdd.deli_no =  #{deliBillNo, jdbcType=VARCHAR}
		and pdd.rowno = #{rowNo, jdbcType=VARCHAR}
		group by pdd.delidetail_id, pdb.item_barcode
	</select>

	<resultMap id="IscpEdiLogMap" type="com.zte.action.iscpedi.model.IscpEdiLog">
		<result column="serialkey" jdbcType="BIGINT" property="serialkey" />
		<result column="externkey" jdbcType="VARCHAR" property="externkey" />
		<result column="externlineno" jdbcType="VARCHAR" property="externlineno" />
		<result column="operateType" jdbcType="VARCHAR" property="operateType" />
		<result column="sourceTable" jdbcType="VARCHAR" property="sourceTable" />
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
		<result column="isSend" jdbcType="INTEGER" property="isSend" />
		<result column="sendTimes" jdbcType="INTEGER" property="sendTimes" />
	</resultMap>


	<select id="getIscpEdiLog" resultMap="IscpEdiLogMap">
		<if test="externkey == null or externkey == ''  or sourceTable == null  or sourceTable == ''">
			select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
			from (select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
			from KXSTEPIII.ISCP_EDI_LOG where isSend in (-1, 2) and <![CDATA[sendTimes < 4]]>
			order by sourceTable,externkey,externlineno )
			WHERE <![CDATA[rownum < #{avaiableSize, jdbcType=INTEGER}]]>
		</if>
		<if test="externkey != null and externkey != '' and sourceTable != null  and sourceTable != ''">
			select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
			from KXSTEPIII.ISCP_EDI_LOG
			where externkey = #{externkey, jdbcType=VARCHAR} AND sourceTable = #{sourceTable, jdbcType=VARCHAR}
		</if>
	</select>
	<select id="getMonitorListMrp" resultType="com.zte.interfaces.infor.vo.JobLogHisVO" parameterType="java.util.List">
		select ins_id insId,JOB_NAME jobName,errors errors,STATUS status ,
		to_char(BEGIN_DATE,'yyyy/mm/dd hh24:mi:ss') beginDate,to_char(END_DATE, 'yyyy/mm/dd hh24:mi:ss') endDate,JOB_OWNER jobOwner from kxstepiii.sys_job_history
		where ins_id in(
		select max(h.ins_id) ins_id
		from kxstepiii.sys_job_history h
		where h.job_name in
		<foreach collection="jobNameList" index="index" item="jobName" open="(" separator="," close=")">
			#{jobName}
		</foreach>
		group by h.job_name)
	</select>
	<select id="getCountJobUnknown" resultType="java.lang.Integer">
		SELECT count(1)
		FROM kxstepiii.sys_job_history h
		where h.ins_id = #{insId, jdbcType=BIGINT}
		and h.status = 'UNKNOWN'
		and (sysdate - h.begin_date) > 2 / 24
	</select>
	<update id="updateIscpEdiLog" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update KXSTEPIII.iscp_edi_log set
			isSend = #{info.isSend, jdbcType=INTEGER},
			sendTimes = sendTimes + 1,
			requestParam = #{info.requestParam, jdbcType=VARCHAR},
			updateDate = TO_DATE(#{info.updateDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
			WHERE serialkey = #{info.serialkey, jdbcType=BIGINT}
			AND EXTERNLINENO = #{info.externlineno, jdbcType=VARCHAR}
			AND sourceTable = #{info.sourceTable, jdbcType=VARCHAR}
			AND isSend <![CDATA[<>]]> 0
		</foreach>
	</update>

	<update id="updateIscpEdiLogResult" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update KXSTEPIII.iscp_edi_log set
			isSend = #{info.isSend, jdbcType=INTEGER},
			updateDate = TO_DATE(#{info.updateDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
			WHERE serialkey = #{info.serialkey, jdbcType=BIGINT}
			AND EXTERNLINENO = #{info.externlineno, jdbcType=VARCHAR}
			AND sourceTable = #{info.stepSourceTable, jdbcType=VARCHAR}
		</foreach>
	</update>

	<select id="getBillToWarn" resultType="java.lang.Integer">
		select count(1)
		from KXSTEPIII.iscp_edi_log
		where 1=1
		and issend in (1,2) and sendtimes >=4
	</select>


	<update id="updateListSendState" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update KXSTEPIII.iscp_edi_log
			<set>
				isSend = 1,
				updateDate = SYSDATE
			</set>
			<where>
				serialkey = #{info.serialkey, jdbcType=BIGINT}
				AND EXTERNLINENO = #{info.externlineno, jdbcType=VARCHAR}
				AND sourceTable = #{info.sourceTable, jdbcType=VARCHAR}
			</where>
		</foreach>
	</update>

	<update id="updateExceptionIscpEdiLog" >
		UPDATE  KXSTEPIII.ISCP_EDI_LOG T SET T.ISSEND = -1,T.UPDATEDATE = SYSDATE WHERE T.ISSEND = 1 AND <![CDATA[T.UPDATEDATE  > SYSDATE - 1 ]]>
	</update>

	<select id="getItemNoByItemId" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.item_no itemNo,t.item_id itemId
		from kxstepiii.ba_item t
		where t.item_id = #{itemId,jdbcType=VARCHAR}
		and rownum = 1
	</select>
	<select id="getBatchItemNoByItemId" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.item_no itemNo,t.item_id itemId
		from kxstepiii.ba_item t
		where t.item_id in (
		<foreach collection="list" item="item" index="index" separator=",">
			#{item.itemId,jdbcType=VARCHAR}
		</foreach>
		)
	</select>
	<select id="getItemNoByBomId" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.new_bom_no itemNo,t.bom_id itemId
		from kxstepiii.ba_bom_head t
		where t.bom_id = #{itemId,jdbcType=VARCHAR}
		and rownum = 1
	</select>
	<select id="getBatchItemNoByBomId" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.new_bom_no itemNo,t.bom_id itemId
		from kxstepiii.ba_bom_head t
		where t.bom_id in (
		<foreach collection="list" item="item" index="index" separator=",">
			#{item.itemId,jdbcType=VARCHAR}
		</foreach>
		)
	</select>
	<select id="getApprovedByItemNo" resultType="com.zte.interfaces.infor.dto.HoldFlowStartDTO" parameterType="java.util.List">
		select bi.item_no itemNo, bic.station1 as approverId1, bic.station4 as approverId2
		from kxstepiii.ba_item bi
		left join kxstepiii.ba_item_class bic on bic.class_no = bi.class_no
		where bic.station1 is not null
		and bic.station4 is not null
		and bi.item_no in
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator=",">
			#{tmp}
		</foreach>
	</select>
</mapper>
