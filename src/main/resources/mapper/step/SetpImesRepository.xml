<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.SetpImesRepository">
    <resultMap id="BaItemMap" type="com.zte.interfaces.step.dto.BaItemDto">
        <result column="stock_no" jdbcType="VARCHAR" property="stockNo"/>
        <result column="stock_loc" jdbcType="VARCHAR" property="stockLoc"/>
        <result column="stock_area" jdbcType="VARCHAR" property="stockArea"/>
        <result column="item_barcode" jdbcType="VARCHAR" property="itemBarcode"/>
        <result column="item_uuid" jdbcType="VARCHAR" property="itemUuid"/>
        <result column="is_lead" jdbcType="VARCHAR" property="isLead"/>
        <result column="is_xp" jdbcType="VARCHAR" property="isXp"/>
        <result column="brand_no" jdbcType="VARCHAR" property="brandNo"/>
    </resultMap>
    <!-- 获取退料仓、拣选库位、存储区 -->
    <select id="getStockInfo" parameterType="com.zte.interfaces.step.dto.BaItemQueryDto" resultMap="BaItemMap">
        SELECT sib.item_barcode,bi.stock_no, bi.stock_loc, bi.stock_area,
        sib.item_uuid, sib.is_lead,CASE WHEN sib.is_xp = 1 THEN 30 ELSE 10 END as is_xp,t.brand_no
        FROM kxstepiii.ba_item bi
        join kxstepiii.st_item_barcode sib on sib.item_id = bi.item_id
        left join kxstepiii.ba_item_brandstyle t on sib.item_uuid=t.item_uuid
        where sib.item_barcode in (
        <foreach collection="itemBarcodeList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
        <if test = "stockNo != null and stockNo !='' " >
            and bi.stock_no = #{stockNo, jdbcType=VARCHAR}
        </if>
    </select>
    <!-- 获取项目 -->
    <select id="getProjectInfo" resultType="com.zte.interfaces.step.dto.ProjectDTO">
        SELECT PROJECT_ID projectId,PROJECT_NUMBER projectName FROM LMS.DB_PRODCLASS_PLANGROUP
        WHERE ENTP_NO = 'S'
    </select>
    <!-- 获取走账别名 -->
    <select id="getStUtilityInfo" resultType="com.zte.interfaces.step.dto.StUtilityDTO">
        SELECT DISTINCT SU.UTILITY_NO utilityNo, SU.UTILITY_NAME utilityName, SU.IS_SALEORDER isSaleorder
        FROM KXSTEPIII.ST_UTILITY SU
        JOIN KXSTEPIII.MTL_GENERIC_DISPOSITIONS MGD ON SU.SUBJECT = MGD.SEGMENT1
        WHERE SU.UTILITY_ATTRIBUTE = 0
        AND SU.UTILITY_TYPE IN (1, 2)
        ORDER BY SU.UTILITY_NAME
    </select>
    <!-- 获取单板包装扫描推送INFO单据号 -->
    <select id="getInforBillNo" resultType="java.lang.String">
        SELECT 'J' || to_char(sysdate,'yyyymmdd') ||
        CASE WHEN LENGTH(LMS.S_INFOR_RECEBILL_NO.NEXTVAL) <![CDATA[<]]> 5 THEN LPAD(LMS.S_INFOR_RECEBILL_NO.NEXTVAL||'', 5, '0')
        ELSE SUBSTR(LMS.S_INFOR_RECEBILL_NO.NEXTVAL||'', -5) END from dual
    </select>
    <!-- 获取是否是ERP计划 -->
    <select id="getIsErpPlan" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.OpOrderplanHeadDto" >
        SELECT nvl(h.is_erp_plan,0) isErpPlan,h.PLAN_NO planNo
        FROM kxstepiii.OP_PRODPLAN p
        left join kxstepiii.OP_ORDERPLAN_HEAD h on p.PLAN_NO = h.PLAN_NO
        WHERE p.PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR}
    </select>
    <!-- 获取委托加工前信息 -->
    <select id="getEntrustBeforeInfo" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.BaBomHeadDto" >
        SELECT BBH1.NEW_BOM_NO newBomNo,BBH1.version
        FROM KXSTEPIII.OP_PRODPLAN OP
        LEFT JOIN KXSTEPIII.BA_BOM_HEAD BBH1 ON OP.BOM_ID = BBH1.BOM_ID
        LEFT JOIN KXSTEPIII.BA_BOM_HEAD BBH2 ON OP.CONSIGNBOM_ID = BBH2.BOM_ID
        WHERE OP.PRODPLAN_ID = #{prodplanId,jdbcType=VARCHAR} AND BBH2.NEW_BOM_NO IS NOT NULL
        AND BBH1.NEW_BOM_NO <![CDATA[<>]]> BBH2.NEW_BOM_NO
    </select>
    <!-- 获取计划组信息 -->
    <select id="getPlangroupInfo" parameterType="com.zte.interfaces.step.dto.DbProdclassPlangroupDto" resultType="com.zte.interfaces.step.dto.DbProdclassPlangroupDto" >
        select PLANNING_GROUP planningGroup,PLANNING_GROUP_DESC planningGroupDesc,PROJECT_ID projectId
        from LMS.DB_PRODCLASS_PLANGROUP
        where PRODUCT_CLASS = #{productClass, jdbcType=VARCHAR}
        and entp_no = #{entpNo, jdbcType=VARCHAR}
    </select>
</mapper>