<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.OmsGtsRepository">

	<select id="getAddingBillNo" parameterType="com.zte.interfaces.step.dto.GtsBillDTO" resultType="com.zte.interfaces.step.dto.GtsBillDTO">
        select distinct ei.in_guid referenceNumber, 0 failNum, 'N' enabledFlag
        from kxstepiii.gts_items ei
        where
        ei.status = 1
        and ei.code_status = 'GTSMaterialAdded'
        and not exists (
        select 1 from  kxstepiii.gts_bill eb where ei.in_guid = eb.reference_number
        )
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and ei.in_guid=#{referenceNumber}
        </if>
        union all
        select distinct eb.reference_number referenceNumber, eb.fail_num failNum, eb.enabled_flag enabledFlag
        from kxstepiii.gts_bill eb
        where
        eb.enabled_flag = 'Y'
        and eb.invoke_flag = 0
        and eb.fail_num <![CDATA[<]]> 4
        and eb.bill_status = 'GTSBillAdding'
        and eb.send_date <![CDATA[<]]> sysdate - 1/24
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and ei.in_guid=#{referenceNumber}
        </if>
	</select>

	<select id="getOmsSalesInfo" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.GtsBillDTO">
        select d.detail_id detailId, d.item_no itemNo, d.qty, d.sale_price salePrice, h.sales_contract_no salesContractNo,
        h.customer_no customerNo, h.sales_type salesType, h.gts_result gtsResult, h.ecss_result ecssResult, h.status
        from kxstepiii.infor_omsales_detail d,
        kxstepiii.infor_omsales_bill h
        where d.bill_id = h.bill_id
        and h.bill_no = #{billNo}
	</select>

    <insert id="insertOrUpdateGtsBill" parameterType="com.zte.interfaces.step.dto.GtsBillDTO">
        merge into kxstepiii.gts_bill a
        using (select #{code,jdbcType=VARCHAR} code, #{name,jdbcType=VARCHAR} name, #{referenceNumber,jdbcType=VARCHAR} reference_number,
        #{documentTypeCode,jdbcType=VARCHAR} document_type_code, #{sendParam,jdbcType=VARCHAR} send_param, #{billStatus,jdbcType=VARCHAR} bill_status,
        #{invokeFlag,jdbcType=INTEGER} invoke_flag, #{failNum,jdbcType=INTEGER} fail_num,#{failReason,jdbcType=VARCHAR} fail_reason,
        #{createdBy,jdbcType=VARCHAR} created_by, #{lastUpdatedBy,jdbcType=VARCHAR} last_updated_by from dual) b
        on (a.reference_number = b.reference_number)
        when matched then
        update set a.send_param=b.send_param, a.send_date=sysdate, a.bill_status=b.bill_status, a.invoke_flag=b.invoke_flag,
        a.fail_num=b.fail_num, a.fail_reason=b.fail_reason, a.last_updated_by=b.last_updated_by, a.last_update_date=sysdate
        when not matched then
        insert (a.record_id, a.code, a.name, a.reference_number, a.document_type_code, a.send_param, a.send_date, a.bill_status,
        a.invoke_flag, a.fail_num, a.fail_reason, a.creation_date, a.created_by)
        values (kxstepiii.gts_bill_s.nextval, b.code, b.name, b.reference_number, b.document_type_code,b.send_param,
        sysdate, b.bill_status, b.invoke_flag, b.fail_num, b.fail_reason, sysdate, b.created_by)
    </insert>

    <update id="updateGtsBill" parameterType="com.zte.interfaces.step.dto.GtsBillDTO">
        update kxstepiii.gts_bill
        set return_param = #{returnParam,jdbcType=VARCHAR},
            bill_status = #{billStatus,jdbcType=VARCHAR},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="updateInforOmSalesBill" parameterType="com.zte.interfaces.step.dto.GtsBillDTO">
        update kxstepiii.infor_omsales_bill
        <set>
            <if test="gtsResult != null">
                gts_result = #{gtsResult,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            last_updated_date = sysdate
        </set>
        where bill_no = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="failGtsOutputcollection" parameterType="com.zte.interfaces.step.dto.GtsBillDTO">
        update kxstepiii.gts_outputcollection
        set status = 0
        where keyid = #{referenceNumber,jdbcType=VARCHAR}
        and status = 1
    </update>

    <insert id="insertGtsOutputcollection" parameterType="com.zte.interfaces.step.dto.GtsOutPutCollectionDTO">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
        insert into kxstepiii.gts_outputcollection
        (keyid, document_number, serv_stah, item_number, serv_stai, qual_stai, status_text, check_ind, guuid)
        values
        (#{item.keyId,jdbcType=VARCHAR}, #{item.documentNumber,jdbcType=VARCHAR}, #{item.servStah,jdbcType=VARCHAR},
        #{item.itemNumber,jdbcType=VARCHAR}, #{item.servStai,jdbcType=VARCHAR}, #{item.qualStai,jdbcType=VARCHAR},
        #{item.statusText,jdbcType=VARCHAR}, #{item.checkInd,jdbcType=VARCHAR}, #{item.gUuid,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="failGtsTextretruncollection" parameterType="com.zte.interfaces.step.dto.GtsBillDTO">
        update kxstepiii.gts_textretruncollection
        set status = 0
        where keyid = #{referenceNumber,jdbcType=VARCHAR}
        and status = 1
    </update>

    <insert id="insertGtsTextretruncollection" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
        insert into kxstepiii.gts_textretruncollection (keyid, type, message, guuid) values
        (#{item.keyId,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR},#{item.message,jdbcType=VARCHAR}, #{item.gUuid,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>