<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.OrgTransferLogRepository">


    <select id="getLastOne" resultType="com.zte.domain.model.material.StStockOrgTransferLog">
        select transfer_id transferId, bill_no billNo from st_stockorg_transfer_log order by last_update_time desc limit 1
    </select>

    <select id="getTransferIdList" resultType="java.lang.String" parameterType="java.lang.String">
        select transfer_id transferId from st_stockorg_transfer sst
        where sst.status =#{status}
        and  not exists
        (select 1  from st_stockorg_transfer_log slt where slt.transfer_id = sst.transfer_id)
        order by sst.transfer_id
    </select>
</mapper>