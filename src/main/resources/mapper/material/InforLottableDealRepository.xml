<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.InforLottableDealRepository">

	<insert id="insertInforLottableDeal" parameterType="com.zte.domain.model.material.InforLottableDeal">

		insert into infor_lottable_deal (serial_no, item_no, item_uuid, supply_no, item_barcodes,
		src_lottable01, dst_lottable01, src_lottable02, dst_lottable02, src_lottable03, dst_lottable03, src_lottable04, dst_lottable04,
		src_lottable05, dst_lottable05, src_lottable06, dst_lottable06, src_lottable07, dst_lottable07, src_lottable08, dst_lottable08,
		src_lottable09, dst_lottable09, src_lottable10, dst_lottable10, src_lottable11, dst_lottable11, src_lottable12, dst_lottable12,
		submit_by, submit_date, submit_reason, created_by, created_date, last_updated_by, last_updated_date, send_params)
		values (#{serialNo,jdbcType=VARCHAR}, #{itemNo,jdbcType=VARCHAR}, #{itemUuid,jdbcType=VARCHAR}, #{supplyNo,jdbcType=VARCHAR},
		#{itemBarcodes,jdbcType=VARCHAR}, #{srcLottable01,jdbcType=VARCHAR}, #{dstLottable01,jdbcType=VARCHAR},
		#{srcLottable02,jdbcType=VARCHAR}, #{dstLottable02,jdbcType=VARCHAR}, #{srcLottable03,jdbcType=VARCHAR}, #{dstLottable03,jdbcType=VARCHAR},
		#{srcLottable04,jdbcType=DATE}, #{dstLottable04,jdbcType=DATE}, #{srcLottable05,jdbcType=DATE}, #{dstLottable05,jdbcType=DATE},
		#{srcLottable06,jdbcType=VARCHAR}, #{dstLottable06,jdbcType=VARCHAR}, #{srcLottable07,jdbcType=VARCHAR}, #{dstLottable07,jdbcType=VARCHAR},
		#{srcLottable08,jdbcType=VARCHAR}, #{dstLottable08,jdbcType=VARCHAR}, #{srcLottable09,jdbcType=VARCHAR}, #{dstLottable09,jdbcType=VARCHAR},
		#{srcLottable10,jdbcType=VARCHAR}, #{dstLottable10,jdbcType=VARCHAR}, #{srcLottable11,jdbcType=DATE}, #{dstLottable11,jdbcType=DATE},
		#{srcLottable12,jdbcType=DATE}, #{dstLottable12,jdbcType=DATE}, #{submitBy,jdbcType=VARCHAR}, sysdate, #{submitReason,jdbcType=VARCHAR},
		#{createdBy,jdbcType=VARCHAR}, sysdate, #{lastUpdatedBy,jdbcType=VARCHAR}, sysdate, #{sendParams,jdbcType=VARCHAR})

	</insert>

	<select id="selectInforLottableDeal"  resultType="String">
		select serial_no from infor_lottable_deal where TO_DAYS( NOW( ) ) - TO_DAYS(submit_date) >= 90
		limit #{split}
	</select>

	<insert id="dataArchiving" parameterType="java.lang.String">
		INSERT INTO infor_lottable_deal_back
		(select * from infor_lottable_deal where serial_no in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		)
	</insert>

	<delete id="deleteInforLottableDeal" parameterType="java.lang.String" >
		delete from infor_lottable_deal
		where serial_no in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

</mapper>