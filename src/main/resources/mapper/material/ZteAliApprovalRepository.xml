<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.ZteAliApprovalRepository">

    <select id="getBillInfo" parameterType="com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO"
            resultType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        <if test='billType == "5" '>
            select
            sfad.row_no detailId,
            sfd.item_no masterDataReferenceNumber,
            sfad.qty qty,
            sfah.return_remark remark,
            sfah.status status,
            sfah.fallback_no billNo,
            sfad.approval_qty openQty,
            sfah.customer_plan_id customerPlanId,
            sfad.to_id pkgId,
            sfh.fallback_type fallbackType
            from
            st_fallback_applybill_head sfah
            join st_fallback_applybill_detail sfad on
            sfah.applybill_no = sfad.applybill_no
            join st_fallback_detail sfd on
            sfd.fallback_detail_guid = sfad.fallback_detail_guid
            join st_fallback_head sfh on
            sfd.fallback_head_guid = sfh.fallback_head_guid
            where
            sfah.enabled_flag = 'Y'
            and sfad.enabled_flag = 'Y'
            and sfd.enabled_flag = 'Y'
            and sfah.applybill_no = #{billNo,jdbcType=VARCHAR}
            <if test='businessType == "2" or  businessType == "4" '>
                and sfad.approval_qty > 0
            </if>
        </if>
        <if test='billType == "6" '>
            select
            h.remark remark,
            b.item_no masterDataReferenceNumber,
            d.row_no detailId,
            d.qty_check qty,
            h.cur_flag status,
            h.returnapply_no billNo,
            d.approval_qty openQty,
            h.customer_plan_id customerPlanId,
            d.box_no pkgId
            from
            st_returnapply_head h
            join st_edi_qcbad_bill b on
            h.edi_qcbad_bill_guid = b.edi_qcbad_bill_guid
            join st_edi_qcbad_bill_detail d on
            b.edi_qcbad_bill_guid = d.edi_qcbad_bill_guid
            where
            h.enabled_flag = 'Y'
            and b.enabled_flag = 'Y'
            and d.enabled_flag = 'Y'
            and h.returnapply_no = #{billNo,jdbcType=VARCHAR}
            <if test='businessType == "2" or  businessType == "4" '>
                and d.approval_qty > 0
            </if>
        </if>
    </select>

    <update id="updateBillHead" parameterType="com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO">
        <if test='billType == "5" '>
            update rwms_material.st_fallback_applybill_head b
            set b.status = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.applybill_no = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test='billType == "6" '>
            update rwms_material.st_returnapply_head b
            set b.cur_flag = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.returnapply_no = #{billNo,jdbcType=VARCHAR}
        </if>
    </update>

    <select id="getBillHeadByPlanId" parameterType="java.util.List"
            resultType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        select b.applybill_no billId, b.applybill_no billNo, b.customer_plan_id customerPlanId, '5' billType
        from rwms_material.st_fallback_applybill_head b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.status = 'APPROVALING'
        union all
        select b.returnapply_head_guid billId, b.returnapply_no billNo, b.customer_plan_id customerPlanId, '6' billType
        from rwms_material.st_returnapply_head b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.cur_flag = 'APPROVALING'
    </select>

    <update id="updateBillDetailList" parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            <if test='item.billType == "5" '>
                update rwms_material.st_fallback_applybill_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.applybill_no = #{item.billId,jdbcType=VARCHAR}
                and d.row_no = #{item.deductionItemLineNumber,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
            <if test='item.billType == "6" '>
                update rwms_material.st_edi_qcbad_bill_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.edi_qcbad_bill_guid = #{item.billId,jdbcType=INTEGER}
                and d.row_no = #{item.deductionItemLineNumber,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
        </foreach>
    </update>

    <select id="getBillDetailNotApproved" parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO"
            resultType="java.lang.Integer">
        <if test='billType == "5" '>
            select count(1) from rwms_material.st_fallback_applybill_detail d
            where d.applybill_no = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
        <if test='billType == "6" '>
            select count(1) from rwms_material.st_edi_qcbad_bill_detail d
            where d.edi_qcbad_bill_guid = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
    </select>


</mapper>