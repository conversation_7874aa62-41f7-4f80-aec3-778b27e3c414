<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StTallyPackingHeadRepository"> 
  
  <resultMap id="ResponseResultMap" type="com.zte.interfaces.tally.dto.TallyHeadDTO"> 
    <result column="detail_address_code" jdbcType="VARCHAR" property="shippingFromPlaceNo" />
    <result column="target_address_code" jdbcType="VARCHAR" property="shippingToPlaceNo" />
    <result column="target_detail_address" jdbcType="VARCHAR" property="shippingToPlaceText" />
    <result column="freight_number" jdbcType="VARCHAR" property="sourceNo" />
    <result column="shipping_type" jdbcType="VARCHAR" property="shippingType" />
    <result column="expense_department" jdbcType="VARCHAR" property="feeBelongDepartment" /> 
    <result column="package_number" jdbcType="INTEGER" property="boxNum" />
    <result column="item_code_qty" jdbcType="INTEGER" property="pieceNum" />
    <result column="volume" jdbcType="DECIMAL" property="volume" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="createdOrderBy" />
    <result column="submited_name" jdbcType="VARCHAR" property="createdOrderByName" />
    <result column="last_updated_date" jdbcType="VARCHAR" property="createdOrderDate" />
    <result column="quantity_unit" jdbcType="INTEGER" property="packageTypeName" />     
    <result column="contact_person" jdbcType="VARCHAR" property="consignorName" /> 
    <result column="contact_phone" jdbcType="VARCHAR" property="consignorContact" /> 
    <result column="target_contact_person" jdbcType="VARCHAR" property="consigneeName" /> 
    <result column="target_contact_company" jdbcType="VARCHAR" property="consigneeCompany" /> 
    <result column="target_contact_phone" jdbcType="VARCHAR" property="consigneeContact" /> 
    <result column="source_repository" jdbcType="VARCHAR" property="sourceRepository" /> 
  </resultMap>
    <resultMap id="BaseResultMap" type="com.zte.domain.model.material.StTallyPackingHead">
        <result column="tally_packing_head_guid" jdbcType="VARCHAR" property="tallyPackingHeadGuid" />
        <result column="freight_number" jdbcType="VARCHAR" property="freightNumber" />
        <result column="fork_plate_number" jdbcType="VARCHAR" property="forkPlateNumber" />
        <result column="package_number" jdbcType="INTEGER" property="packageNumber" />
        <result column="source_repository" jdbcType="VARCHAR" property="sourceRepository" />
        <result column="target_warehouse" jdbcType="VARCHAR" property="targetWarehouse" />
        <result column="is_freight" jdbcType="VARCHAR" property="isFreight" />
        <result column="shipping_type" jdbcType="VARCHAR" property="shippingType" />
        <result column="expense_department" jdbcType="VARCHAR" property="expenseDepartment" />
        <result column="distribution_direction" jdbcType="VARCHAR" property="distributionDirection" />
        <result column="receipt_address" jdbcType="VARCHAR" property="receiptAddress" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_date" jdbcType="VARCHAR" property="createDate" />
        <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="last_updated_date" jdbcType="VARCHAR" property="lastUpdatedDate" />
        <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="w_volume" jdbcType="VARCHAR" property="wVolume" />
        <result column="w_weight" jdbcType="VARCHAR" property="wWeight" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="submited_name" jdbcType="VARCHAR" property="submitedName" />
        <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
        <result column="node_update_time" jdbcType="VARCHAR" property="nodeUpdateTime" />
        <result column="node_place_address" jdbcType="VARCHAR" property="nodePlaceAddress" />
        <result column="way_bill_id" jdbcType="VARCHAR" property="wayBillId" />
    </resultMap>
  <select id="selectStTallyPackingHeadList" parameterType="java.lang.String" resultMap="ResponseResultMap">
    select 
	h.source_repository,
	h.target_warehouse,
	h.freight_number,
	h.expense_department,
	h.shipping_type,
	sum(d.package_number) package_number,
	IFNULL(SUM(de.qty),0) item_code_qty,
	IFNULL(h.w_volume,0) volume,
	IFNULL(h.w_weight,0) weight,
	h.last_updated_by,
	h.submited_name,
	DATE_FORMAT(h.last_updated_date, '%Y-%m-%d %H:%i:%s') last_updated_date,
	GROUP_CONCAT(d.quantity_unit)  quantity_unit,
	sw.detail_address_code,
    sw.contact_person,
    sw.contact_phone, 
    sw1.detail_address target_detail_address,
    sw1.detail_address_code target_address_code,
    sw1.contact_company target_contact_company,
    sw1.contact_person target_contact_person,
    sw1.contact_phone target_contact_phone
	from st_tally_packing_head h
	join st_tally_packing_detail d
	on d.tally_packing_head_guid=h.tally_packing_head_guid
	and d.enabled_flag='Y'
	left join st_delivery de
	on de.bill_no=d.packing_number
      and de.enabled_flag='Y'
	left join st_warehouse sw
    on sw.team_name=h.source_repository
    and sw.enabled_flag='Y'
    left join st_warehouse sw1
    on sw1.team_name=h.target_warehouse
    and sw1.enabled_flag='Y'
	where  h.enabled_flag='Y'
	and  h.freight_number in 
	<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
	</foreach>
	GROUP BY h.source_repository,h.target_warehouse,h.freight_number
  </select>
    <select id="getStTallyPackingHead" resultMap="BaseResultMap">
        select tally_packing_head_guid, freight_number, fork_plate_number, package_number, source_repository,
        target_warehouse, is_freight, shipping_type, expense_department, distribution_direction,
        receipt_address, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        w_volume, w_weight, status, submited_name, node_name, node_update_time, node_place_address,
        way_bill_id
        from st_tally_packing_head
        where last_updated_date<![CDATA[<]]>sysdate-180 and status in ('已确认签收','已签收')
        order by last_updated_date limit 0,10000
    </select>
    <delete id="deleteStTallyPackingHead" parameterType="com.zte.domain.model.material.StTallyPackingHead">
        delete from st_tally_packing_head
        where tally_packing_head_guid in (
        <foreach collection="list" separator="," item="item">
            #{item.tallyPackingHeadGuid,jdbcType=VARCHAR}
        </foreach>
        )
    </delete>
</mapper>
