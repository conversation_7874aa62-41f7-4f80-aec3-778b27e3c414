<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StWarehouseRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.material.StWarehouse">
    <id column="warehouse_guid" jdbcType="VARCHAR" property="warehouseGuid" />
    <result column="team_name" jdbcType="VARCHAR" property="teamName" />
    <result column="team_id" jdbcType="VARCHAR" property="teamId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_person" jdbcType="VARCHAR" property="contactPerson" />
    <result column="contact_type" jdbcType="VARCHAR" property="contactType" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="country_id" jdbcType="VARCHAR" property="countryId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_id" jdbcType="VARCHAR" property="districtId" />
    <result column="detail_address" jdbcType="VARCHAR" property="detailAddress" />
    <result column="detail_address_code" jdbcType="VARCHAR" property="detailAddressCode" />
    <result column="contact_company" jdbcType="VARCHAR" property="contactCompany" />
    <result column="warehouse_type" jdbcType="VARCHAR" property="warehouseType" />
    <result column="primary_contact" jdbcType="VARCHAR" property="primaryContact" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="last_updated_date" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
    <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="mobile_type" jdbcType="VARCHAR" property="mobileType" />
    <result column="attr1" jdbcType="VARCHAR" property="attr1" />
  </resultMap>
  
  <resultMap id="StockMap" type="com.zte.interfaces.tally.dto.StockDTO">
    <result column="team_name" jdbcType="VARCHAR" property="areaName" /> 
    <result column="warehouse_id" jdbcType="VARCHAR" property="stockName" /> 
    <result column="contact_phone" jdbcType="VARCHAR" property="telephone" />
    <result column="contact_person" jdbcType="VARCHAR" property="receiptBy" />
    <result column="contact_type" jdbcType="VARCHAR" property="receiptClass" /> 
    <result column="detail_address" jdbcType="VARCHAR" property="address" /> 
    <result column="primary_contact" jdbcType="VARCHAR" property="mainReceiptBy" /> 
    <result column="enabled_flag" jdbcType="VARCHAR" property="enableFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    warehouse_guid, team_name, team_id, warehouse_name, warehouse_id, contact_phone, 
    contact_person, contact_type, country_name, country_id, province_name, province_id, 
    city_name, city_id, district_name, district_id, detail_address, detail_address_code, 
    contact_company, warehouse_type, primary_contact, create_by, create_date, last_updated_by, 
    last_updated_date, enabled_flag, mobile_type, attr1
  </sql>
  <select id="selectStWarehouse" parameterType="com.zte.interfaces.tally.dto.StockQueryDTO" resultMap="StockMap">
    select team_name, warehouse_id, contact_person, contact_phone,
    contact_type,detail_address, primary_contact, enabled_flag
    from st_warehouse
    where  enabled_flag='Y'
    <if test=" stockName !=null and stockName!='' ">
      and warehouse_name= #{stockName,jdbcType=VARCHAR}
    </if>
    <if test=" areaName !=null and areaName!='' ">
      and team_name= #{areaName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectStWarehouseAll" resultMap="BaseResultMap">
    select warehouse_guid, team_name, team_id, warehouse_name, warehouse_id, contact_phone, 
    contact_person, contact_type, country_name, country_id, province_name, province_id, 
    city_name, city_id, district_name, district_id, detail_address, detail_address_code, 
    contact_company, warehouse_type, primary_contact, create_by, create_date, last_updated_by, 
    last_updated_date, enabled_flag, mobile_type, attr1
    from st_warehouse
  </select>

  <select id="selectStWarehouseById" parameterType="com.zte.domain.model.material.StWarehouse" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from st_warehouse
    where warehouse_guid = #{warehouseGuid,jdbcType=VARCHAR}
  </select>

  <delete id="deleteStWarehouseById" parameterType="com.zte.domain.model.material.StWarehouse">
    delete from st_warehouse
    where warehouse_guid = #{warehouseGuid,jdbcType=VARCHAR}
  </delete>

  <insert id="insertStWarehouse" parameterType="com.zte.domain.model.material.StWarehouse">
    insert into st_warehouse (warehouse_guid, team_name, team_id, 
      warehouse_name, warehouse_id, contact_phone, 
      contact_person, contact_type, country_name, 
      country_id, province_name, province_id, 
      city_name, city_id, district_name, 
      district_id, detail_address, detail_address_code, 
      contact_company, warehouse_type, primary_contact, 
      create_by, create_date, last_updated_by, 
      last_updated_date, enabled_flag, mobile_type, 
      attr1)
    values (#{warehouseGuid,jdbcType=VARCHAR}, #{teamName,jdbcType=VARCHAR}, #{teamId,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{warehouseId,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{contactPerson,jdbcType=VARCHAR}, #{contactType,jdbcType=VARCHAR}, #{countryName,jdbcType=VARCHAR}, 
      #{countryId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{provinceId,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{cityId,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR}, 
      #{districtId,jdbcType=VARCHAR}, #{detailAddress,jdbcType=VARCHAR}, #{detailAddressCode,jdbcType=VARCHAR}, 
      #{contactCompany,jdbcType=VARCHAR}, #{warehouseType,jdbcType=VARCHAR}, #{primaryContact,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{lastUpdatedDate,jdbcType=TIMESTAMP}, #{enabledFlag,jdbcType=VARCHAR}, #{mobileType,jdbcType=VARCHAR}, 
      #{attr1,jdbcType=VARCHAR})
  </insert>

  <insert id="insertStWarehouseSelective" parameterType="com.zte.domain.model.material.StWarehouse">
    insert into st_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="warehouseGuid != null">
        warehouse_guid,
      </if>

      <if test="teamName != null">
        team_name,
      </if>

      <if test="teamId != null">
        team_id,
      </if>

      <if test="warehouseName != null">
        warehouse_name,
      </if>

      <if test="warehouseId != null">
        warehouse_id,
      </if>

      <if test="contactPhone != null">
        contact_phone,
      </if>

      <if test="contactPerson != null">
        contact_person,
      </if>

      <if test="contactType != null">
        contact_type,
      </if>

      <if test="countryName != null">
        country_name,
      </if>

      <if test="countryId != null">
        country_id,
      </if>

      <if test="provinceName != null">
        province_name,
      </if>

      <if test="provinceId != null">
        province_id,
      </if>

      <if test="cityName != null">
        city_name,
      </if>

      <if test="cityId != null">
        city_id,
      </if>

      <if test="districtName != null">
        district_name,
      </if>

      <if test="districtId != null">
        district_id,
      </if>

      <if test="detailAddress != null">
        detail_address,
      </if>

      <if test="detailAddressCode != null">
        detail_address_code,
      </if>

      <if test="contactCompany != null">
        contact_company,
      </if>

      <if test="warehouseType != null">
        warehouse_type,
      </if>

      <if test="primaryContact != null">
        primary_contact,
      </if>

      <if test="createBy != null">
        create_by,
      </if>

      <if test="createDate != null">
        create_date,
      </if>

      <if test="lastUpdatedBy != null">
        last_updated_by,
      </if>

      <if test="lastUpdatedDate != null">
        last_updated_date,
      </if>

      <if test="enabledFlag != null">
        enabled_flag,
      </if>

      <if test="mobileType != null">
        mobile_type,
      </if>

      <if test="attr1 != null">
        attr1,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="warehouseGuid != null">
        #{warehouseGuid,jdbcType=VARCHAR},
      </if>

      <if test="teamName != null">
        #{teamName,jdbcType=VARCHAR},
      </if>

      <if test="teamId != null">
        #{teamId,jdbcType=VARCHAR},
      </if>

      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>

      <if test="warehouseId != null">
        #{warehouseId,jdbcType=VARCHAR},
      </if>

      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>

      <if test="contactPerson != null">
        #{contactPerson,jdbcType=VARCHAR},
      </if>

      <if test="contactType != null">
        #{contactType,jdbcType=VARCHAR},
      </if>

      <if test="countryName != null">
        #{countryName,jdbcType=VARCHAR},
      </if>

      <if test="countryId != null">
        #{countryId,jdbcType=VARCHAR},
      </if>

      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>

      <if test="provinceId != null">
        #{provinceId,jdbcType=VARCHAR},
      </if>

      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>

      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>

      <if test="districtName != null">
        #{districtName,jdbcType=VARCHAR},
      </if>

      <if test="districtId != null">
        #{districtId,jdbcType=VARCHAR},
      </if>

      <if test="detailAddress != null">
        #{detailAddress,jdbcType=VARCHAR},
      </if>

      <if test="detailAddressCode != null">
        #{detailAddressCode,jdbcType=VARCHAR},
      </if>

      <if test="contactCompany != null">
        #{contactCompany,jdbcType=VARCHAR},
      </if>

      <if test="warehouseType != null">
        #{warehouseType,jdbcType=VARCHAR},
      </if>

      <if test="primaryContact != null">
        #{primaryContact,jdbcType=VARCHAR},
      </if>

      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedDate != null">
        #{lastUpdatedDate,jdbcType=TIMESTAMP},
      </if>

      <if test="enabledFlag != null">
        #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="mobileType != null">
        #{mobileType,jdbcType=VARCHAR},
      </if>

      <if test="attr1 != null">
        #{attr1,jdbcType=VARCHAR},
      </if>

    </trim>

  </insert>

  <update id="updateStWarehouseByIdSelective" parameterType="com.zte.domain.model.material.StWarehouse">
    update st_warehouse
    <set>
      <if test="teamName != null">
        team_name = #{teamName,jdbcType=VARCHAR},
      </if>

      <if test="teamId != null">
        team_id = #{teamId,jdbcType=VARCHAR},
      </if>

      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>

      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=VARCHAR},
      </if>

      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>

      <if test="contactPerson != null">
        contact_person = #{contactPerson,jdbcType=VARCHAR},
      </if>

      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=VARCHAR},
      </if>

      <if test="countryName != null">
        country_name = #{countryName,jdbcType=VARCHAR},
      </if>

      <if test="countryId != null">
        country_id = #{countryId,jdbcType=VARCHAR},
      </if>

      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>

      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=VARCHAR},
      </if>

      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>

      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>

      <if test="districtName != null">
        district_name = #{districtName,jdbcType=VARCHAR},
      </if>

      <if test="districtId != null">
        district_id = #{districtId,jdbcType=VARCHAR},
      </if>

      <if test="detailAddress != null">
        detail_address = #{detailAddress,jdbcType=VARCHAR},
      </if>

      <if test="detailAddressCode != null">
        detail_address_code = #{detailAddressCode,jdbcType=VARCHAR},
      </if>

      <if test="contactCompany != null">
        contact_company = #{contactCompany,jdbcType=VARCHAR},
      </if>

      <if test="warehouseType != null">
        warehouse_type = #{warehouseType,jdbcType=VARCHAR},
      </if>

      <if test="primaryContact != null">
        primary_contact = #{primaryContact,jdbcType=VARCHAR},
      </if>

      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>

      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>

      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>

      <if test="lastUpdatedDate != null">
        last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
      </if>

      <if test="enabledFlag != null">
        enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      </if>

      <if test="mobileType != null">
        mobile_type = #{mobileType,jdbcType=VARCHAR},
      </if>

      <if test="attr1 != null">
        attr1 = #{attr1,jdbcType=VARCHAR},
      </if>

    </set>

    where warehouse_guid = #{warehouseGuid,jdbcType=VARCHAR}
  </update>

  <update id="updateStWarehouseById" parameterType="com.zte.domain.model.material.StWarehouse">
    update st_warehouse
    set team_name = #{teamName,jdbcType=VARCHAR},
      team_id = #{teamId,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      warehouse_id = #{warehouseId,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      contact_person = #{contactPerson,jdbcType=VARCHAR},
      contact_type = #{contactType,jdbcType=VARCHAR},
      country_name = #{countryName,jdbcType=VARCHAR},
      country_id = #{countryId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      province_id = #{provinceId,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=VARCHAR},
      district_name = #{districtName,jdbcType=VARCHAR},
      district_id = #{districtId,jdbcType=VARCHAR},
      detail_address = #{detailAddress,jdbcType=VARCHAR},
      detail_address_code = #{detailAddressCode,jdbcType=VARCHAR},
      contact_company = #{contactCompany,jdbcType=VARCHAR},
      warehouse_type = #{warehouseType,jdbcType=VARCHAR},
      primary_contact = #{primaryContact,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
      enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
      mobile_type = #{mobileType,jdbcType=VARCHAR},
      attr1 = #{attr1,jdbcType=VARCHAR}
    where warehouse_guid = #{warehouseGuid,jdbcType=VARCHAR}
  </update>

</mapper>
