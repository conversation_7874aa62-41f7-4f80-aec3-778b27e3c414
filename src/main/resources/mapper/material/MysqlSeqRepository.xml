<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.zte.domain.model.material.MysqlSeqRepository">

	<select id="getSeqCurval" parameterType="java.lang.String" resultType="java.util.HashMap">
		select current_value, increment from sequence where name = #{name,jdbcType=VARCHAR}
	</select>

	<insert id="insert" >
		insert into sequence(name, current_value, increment) values (#{name,jdbcType=VARCHAR},
		#{val,jdbcType=BIGINT}, #{increment,jdbcType=INTEGER})
	</insert>

	<update id="setSeq" >
		update sequence set
		current_value = #{val,jdbcType=BIGINT} where name = #{name,jdbcType=VARCHAR};
	</update>
</mapper>
