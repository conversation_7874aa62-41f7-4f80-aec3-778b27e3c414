<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.IwmsEcssRepository">

	<select id="getAddingBillNo" parameterType="com.zte.interfaces.step.dto.EcssBillDTO" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select distinct srh.returnapply_no referenceNumber, 0 failNum, 'N' enabledFlag
        from st_returnapply_head srh
        where not exists (
        select 1 from ecss_bill eb where srh.returnapply_no = eb.reference_number
        )
        and srh.enabled_flag = 'Y'
        and srh.cur_flag  = 'SUPPLIEERDEALED'
        and srh.returnapply_no=#{referenceNumber,jdbcType=VARCHAR}
        union all
        select distinct sah.applybill_no referenceNumber, 0 failNum, 'N' enabledFlag
        from st_fallback_applybill_head sah
        where not exists (
        select 1 from ecss_bill eb where sah.applybill_no = eb.reference_number
        )
        and sah.handling_opinions  = '同意'
        and sah.iwms_status = 'NEW'
        and sah.status not in (select sslv2.lookup_meaning
        from st_sys_lookup_values sslv2 where
        sslv2.lookup_meaning =sah.status
        and sslv2.lookup_type='1543131'
        and sslv2.enabled_flag='Y')
        and sah.applybill_no=#{referenceNumber,jdbcType=VARCHAR}
        union all
        select distinct eb.reference_number referenceNumber, eb.fail_num failNum, eb.enabled_flag enabledFlag
        from ecss_bill eb
        where eb.enabled_flag = 'Y'
        and eb.invoke_flag = 0
        and eb.fail_num &lt; 4
        and eb.bill_status = 'ECSSBillAdding'
        and eb.send_date &lt; sysdate - 1/24
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and eb.reference_number=#{referenceNumber,jdbcType=VARCHAR}
        </if>
	</select>

	<select id="getReturnApplyMaterialInfo" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select b.item_no masterDataReferenceNumber,sum(d.qty_rejects) quantity,0 cost,'CNY' currencyCode,
        b.supply_no customerNo,h.ecss_result ecssResult,h.cur_flag status
        from st_returnapply_head h
        join st_edi_qcbad_bill b on h.edi_qcbad_bill_guid = b.edi_qcbad_bill_guid
        join st_edi_qcbad_bill_detail d on b.edi_qcbad_bill_guid = d.edi_qcbad_bill_guid
        where
        h.enabled_flag = 'Y'
        and b.enabled_flag = 'Y'
        and d.enabled_flag = 'Y'
        and h.returnapply_no = #{billNo}
        group by b.item_no,b.supply_no,h.ecss_result,h.cur_flag
	</select>

    <select id="getFallbackApplyMaterialInfo" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select sfd.item_no masterDataReferenceNumber,sum(sfad.qty) quantity,0 cost,'CNY' currencyCode,
        sfah.supplier_no customerNo,sfah.ecss_result ecssResult,sfah.status
        from st_fallback_applybill_head sfah
        join st_fallback_applybill_detail sfad on sfah.applybill_no = sfad.applybill_no
        join st_fallback_detail sfd on sfd.fallback_detail_guid = sfad.fallback_detail_guid
        where sfah.enabled_flag = 'Y'
        and sfad.enabled_flag = 'Y'
        and sfd.enabled_flag = 'Y'
        and sfah.applybill_no = #{billNo}
        group by sfd.item_no,sfah.supplier_no,sfah.ecss_result,sfah.status
    </select>

    <insert id="insertOrUpdateEcssBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        merge into ecss_bill a
        using (select #{code,jdbcType=VARCHAR} code, #{name,jdbcType=VARCHAR} name, #{referenceNumber,jdbcType=VARCHAR} reference_number,
        #{documentTypeCode,jdbcType=VARCHAR} document_type_code, #{sendParam,jdbcType=VARCHAR} send_param, #{billStatus,jdbcType=VARCHAR} bill_status,
        #{invokeFlag,jdbcType=INTEGER} invoke_flag, #{failNum,jdbcType=INTEGER} fail_num,#{failReason,jdbcType=VARCHAR} fail_reason,
        #{createdBy,jdbcType=VARCHAR} created_by, #{lastUpdatedBy,jdbcType=VARCHAR} last_updated_by from dual) b
        on (a.reference_number = b.reference_number)
        when matched then
        update set a.send_param=b.send_param, a.send_date=sysdate, a.bill_status=b.bill_status, a.invoke_flag=b.invoke_flag,
        a.fail_num=b.fail_num, a.fail_reason=b.fail_reason, a.last_updated_by=b.last_updated_by, a.last_update_date=sysdate
        when not matched then
        insert (a.code, a.name, a.reference_number, a.document_type_code, a.send_param, a.send_date, a.bill_status,
        a.invoke_flag, a.fail_num, a.fail_reason, a.creation_date, a.created_by)
        values (b.code, b.name, b.reference_number, b.document_type_code,b.send_param,
        sysdate, b.bill_status, b.invoke_flag, b.fail_num, b.fail_reason, sysdate, b.created_by)
    </insert>

    <update id="updateEcssBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        update ecss_bill
        set return_param = #{returnParam,jdbcType=VARCHAR},
            bill_status = #{billStatus,jdbcType=VARCHAR},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="updateReturnApplyBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        update st_returnapply_head
        <set>
            <if test="ecssResult != null and ecssResult !=''">
                ecss_result = #{ecssResult,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status !=''">
                cur_flag = #{status,jdbcType=VARCHAR},
            </if>
            <if test="submitTimes != null and submitTimes != 0">
                submit_times = submit_times + 1,
            </if>
            last_updated_date = sysdate
        </set>
        where returnapply_no = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="updateFallbackApplyBill" parameterType="com.zte.interfaces.step.dto.EcssBillDTO">
        update st_fallback_applybill_head
        <set>
            <if test="ecssResult != null and ecssResult !=''">
                ecss_result = #{ecssResult,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status !=''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="iwmsStatus != null and iwmsStatus !=''">
                iwms_status = #{iwmsStatus,jdbcType=VARCHAR},
            </if>
            <if test="submitTimes != null and submitTimes != 0">
                submit_times = submit_times + 1,
            </if>
            last_updated_date = sysdate
        </set>
        where applybill_no = #{referenceNumber,jdbcType=VARCHAR}
    </update>

    <update id="failEcssOutputcollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        update ecss_outputcollection
        set enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <insert id="insertEcssOutputcollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        insert into ecss_outputcollection
        (code, reference_number, c_status, s_time, status, creation_date, created_by)
        values
        (#{code,jdbcType=VARCHAR}, #{referenceNumber,jdbcType=VARCHAR},
        #{cStatus,jdbcType=VARCHAR}, #{sTime,jdbcType=DATE}, #{status,jdbcType=VARCHAR},
        sysdate, #{createdBy,jdbcType=VARCHAR})
    </insert>

    <update id="failEcssTextretruncollection" parameterType="com.zte.interfaces.step.dto.EcssOutPutCollectionDTO">
        update ecss_textretruncollection
        set enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_update_date = sysdate
        where reference_number = #{referenceNumber,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <insert id="insertEcssTextretruncollection" parameterType="java.util.List">
        insert into ecss_textretruncollection
        (reference_number, item_no, law, s_type, f_reason, f_messages, creation_date, created_by)
        <foreach collection="list" item="item" index="index" separator="union all">
        select #{item.referenceNumber,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR},#{item.law,jdbcType=VARCHAR},
            #{item.sType,jdbcType=VARCHAR}, #{item.fReason,jdbcType=VARCHAR}, #{item.fMessages,jdbcType=VARCHAR},
            sysdate, #{item.createdBy,jdbcType=VARCHAR} from dual
        </foreach>
    </insert>

    <select id="getApplyBillToInfor" parameterType="com.zte.interfaces.step.dto.EcssBillDTO" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select returnapply_no referenceNumber,cur_flag status
        from st_returnapply_head
        where enabled_flag = 'Y'
        and cur_flag in ('ECSSAUDITED','APPROVED')
        and ecss_result = '通过'
        <if test="submitTimes != null and submitTimes == 0">
            and submit_times &lt; 4
        </if>
        <if test="submitTimes != null and submitTimes == 4">
            and submit_times >= 4
        </if>
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and returnapply_no = #{referenceNumber,jdbcType=VARCHAR}
        </if>
        union all
        select applybill_no referenceNumber,status
        from st_fallback_applybill_head sfah
        where enabled_flag = 'Y'
        and status in ('ECSSAUDITED','APPROVED')
        and ecss_result = '通过'
        <if test="submitTimes != null and submitTimes == 0">
            and submit_times &lt; 4
        </if>
        <if test="submitTimes != null and submitTimes == 4">
            and submit_times >= 4
        </if>
        <if test=" referenceNumber !=null and referenceNumber !='' ">
            and applybill_no = #{referenceNumber,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryReturnApplyInfo" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select
            h.returnapply_no referenceNumber,
            b.item_no itemNo,
            h.create_by createdBy,
            b.stock_no warehouseNo
        from
            st_returnapply_head h
        join st_edi_qcbad_bill b on
            h.edi_qcbad_bill_guid = b.edi_qcbad_bill_guid
            and b.enabled_flag = 'Y'
        where
            h.returnapply_no = #{referenceNumber,jdbcType=VARCHAR}
    </select>
    <select id="queryFallbackApplyInfo" resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select
        sfah.applybill_no referenceNumber,
        sfh.item_no itemNo,
        sfh.create_by createdBy,
        sfh.source_location warehouseNo
        from
        st_fallback_applybill_head sfah
        join st_fallback_head sfh on
        sfah.fallback_no = sfh.fallback_no
        and sfh.enabled_flag='Y'
        where
        sfah.applybill_no = #{referenceNumber,jdbcType=VARCHAR}
    </select>

    <select id="selectItemNoByBillNo" parameterType="java.util.List"
            resultType="com.zte.interfaces.step.dto.EcssBillDTO">
        select
        sfd.item_no masterDataReferenceNumber,
        sfah.applybill_no referenceNumber
        from
        st_fallback_applybill_head sfah
        join st_fallback_applybill_detail sfad on
        sfah.applybill_no = sfad.applybill_no
        join st_fallback_detail sfd on
        sfd.fallback_detail_guid = sfad.fallback_detail_guid
        where
        sfah.enabled_flag = 'Y'
        and sfad.enabled_flag = 'Y'
        and sfd.enabled_flag = 'Y'
        <if test="billNos != null and billNos.size() > 0">
            and sfah.applybill_no in
            <foreach collection="billNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="billNos == null or billNos.size() == 0">
            and 1=2
        </if>
        union all
        select b.item_no masterDataReferenceNumber,
        h.returnapply_no referenceNumber
        from st_returnapply_head h
        join st_edi_qcbad_bill b on h.edi_qcbad_bill_guid = b.edi_qcbad_bill_guid
        join st_edi_qcbad_bill_detail d on b.edi_qcbad_bill_guid = d.edi_qcbad_bill_guid
        where
        h.enabled_flag = 'Y'
        and b.enabled_flag = 'Y'
        and d.enabled_flag = 'Y'
        <if test="billNos != null and billNos.size() > 0">
            and h.returnapply_no in
            <foreach collection="billNos" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="billNos == null or billNos.size() == 0">
            and 1=2
        </if>
    </select>


</mapper>