<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StDeliveryBackRepository">
    <insert id="insertStDeliveryBack" parameterType="com.zte.domain.model.material.StTallyPackingHead">
        insert into st_delivery_back
        (delivery_guid, bill_no, batchno, itemcode, start_no, qty, loc, create_by, create_date,
        last_updated_by, last_updated_date, enabled_flag, item_name
        )
        select distinct de.delivery_guid, de.bill_no, de.batchno, de.itemcode, de.start_no, de.qty, de.loc,
        de.create_by, de.create_date, de.last_updated_by, de.last_updated_date, de.enabled_flag,
        de.item_name
        from st_tally_packing_detail td
        join st_delivery de on td. packing_number=de.bill_no
        where td.tally_packing_head_guid in (
        <foreach collection="list" separator="," item="item">
            #{item.tallyPackingHeadGuid,jdbcType=VARCHAR}
        </foreach>
        )
    </insert>
</mapper>
