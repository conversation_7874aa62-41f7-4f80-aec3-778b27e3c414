<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StTallyPackingDetailRepository">

  <resultMap id="ResponseResultMap" type="com.zte.interfaces.tally.dto.TallyDetailDTO"> 
    <result column="packing_number" jdbcType="VARCHAR" property="containerNo" />
    <result column="freight_number" jdbcType="VARCHAR" property="sourceNo" />
    <result column="fork_plate_number" jdbcType="VARCHAR" property="parentContainerNo" />     
    <result column="w_volume" jdbcType="DECIMAL" property="bulkVolume" />
    <result column="w_weight" jdbcType="DECIMAL" property="bulkWeight" />
    <result column="start_no" jdbcType="VARCHAR" property="startNo" />  
  </resultMap>
 

  <select id="selectStTallyPackingDetailList" parameterType="java.lang.String" resultMap="ResponseResultMap">
    select d.packing_number,d.freight_number,d.w_volume,d.w_weight,d.fork_plate_number, start_no
    from st_tally_packing_detail d
    where  d.freight_number in 
	<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
	</foreach>
  </select>
  <delete id="deleteStTallyPackingDetail" parameterType="com.zte.domain.model.material.StTallyPackingHead">
    delete from st_tally_packing_detail
    where tally_packing_head_guid in (
    <foreach collection="list" separator="," item="item">
      #{item.tallyPackingHeadGuid,jdbcType=VARCHAR}
    </foreach>
    )
  </delete>

</mapper>
