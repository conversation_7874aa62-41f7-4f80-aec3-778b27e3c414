<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.SequenceRepository">
    <select id="selectByPrimaryKey"  parameterType="java.lang.String" resultType="com.zte.domain.model.material.Sequence">
        select
        <include refid="Base_Column_List" />
        from st_sequence st_sequence
        where st_sequence.sequence_id = #{sequenceId,jdbcType=VARCHAR}
    </select>


    <sql id="Base_Column_List" >
        st_sequence.sequence_id sequenceId,
        st_sequence.sequence_code sequenceCode,
        st_sequence.project_code projectCode,
        st_sequence.sequence_length sequenceLength,
        st_sequence.sequence_prefix sequencePrefix,
        st_sequence.sequence_suffix sequenceSuffix,
        st_sequence.sequence_curr_no sequenceCurrNo,
        st_sequence.sequence_step sequenceStep,
        st_sequence.year year,
        st_sequence.month month,
        st_sequence.day day,
        st_sequence.remark remark,
        st_sequence.version version
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String"  >
        delete from st_sequence
        where sequence_id = #{sequenceId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert"  parameterType="com.zte.domain.model.material.Sequence" >
        insert into st_sequence (sequence_id, sequence_code, remark,
        project_code, sequence_length, sequence_prefix,
        sequence_suffix, sequence_curr_no, sequence_step,
        year,month,day, version)
        values (#{sequenceId,jdbcType=VARCHAR}, #{sequenceCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{projectCode,jdbcType=VARCHAR},  #{sequenceLength,jdbcType=INTEGER}, #{sequencePrefix,jdbcType=VARCHAR},
        #{sequenceSuffix,jdbcType=VARCHAR}, #{sequenceCurrNo,jdbcType=INTEGER}, #{sequenceStep,jdbcType=INTEGER},
        #{year,jdbcType=INTEGER}, #{month,jdbcType=INTEGER}, #{day,jdbcType=INTEGER},
        #{version,jdbcType=INTEGER})
    </insert>

    <update id="updateByPrimaryKeyAndVersion" parameterType="com.zte.domain.model.material.Sequence" >
        update st_sequence
        <set >
            <if test="version != null">
                version = version + 1,
            </if>
            <if test="sequenceCode != null" >
                sequence_code = #{sequenceCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="projectCode != null" >
                project_code = #{projectCode,jdbcType=VARCHAR},
            </if>
            <if test="sequenceLength != null" >
                sequence_length = #{sequenceLength,jdbcType=INTEGER},
            </if>
            <if test="sequencePrefix != null" >
                sequence_prefix = #{sequencePrefix,jdbcType=VARCHAR},
            </if>
            <if test="sequenceSuffix != null" >
                sequence_suffix = #{sequenceSuffix,jdbcType=VARCHAR},
            </if>
            <if test="sequenceCurrNo != null" >
                sequence_curr_no = #{sequenceCurrNo,jdbcType=INTEGER},
            </if>
            <if test="sequenceStep != null" >
                sequence_step = #{sequenceStep,jdbcType=INTEGER},
            </if>
            <if test="year != null" >
                year = #{year,jdbcType=INTEGER},
            </if>
            <if test="month != null" >
                month = #{month,jdbcType=INTEGER},
            </if>
            <if test="day != null" >
                day = #{day,jdbcType=INTEGER},
            </if>
        </set>
        where sequence_id = #{sequenceId,jdbcType=VARCHAR} AND version = #{version,jdbcType=VARCHAR}
    </update>


</mapper>