<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StDeliveryRepository">
  <resultMap id="ResponseResultMap" type="com.zte.interfaces.tally.dto.BoxDetailDTO"> 
    <result column="bill_no" jdbcType="VARCHAR" property="containerNo" />
    <result column="itemcode" jdbcType="VARCHAR" property="materialCode" />
    <result column="item_name" jdbcType="VARCHAR" property="materialName" />
    <result column="qty" jdbcType="INTEGER" property="piece" />     
  </resultMap> 

  

  <select id="selectStDeliveryList" parameterType="java.lang.String" resultMap="ResponseResultMap">
    select d.bill_no,d.itemcode,d.item_name,d.qty 
    from st_delivery d
    where d.enabled_flag='Y'
    and d.bill_no in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
	</foreach>
  </select>

  <delete id="deleteStDelivery" parameterType="com.zte.domain.model.material.StTallyPackingHead">
    delete de
    from st_tally_packing_detail td
    join st_delivery de on td. packing_number=de.bill_no
    where td.tally_packing_head_guid in (
    <foreach collection="list" separator="," item="item">
      #{item.tallyPackingHeadGuid,jdbcType=VARCHAR}
    </foreach>
    )
  </delete>

</mapper>
