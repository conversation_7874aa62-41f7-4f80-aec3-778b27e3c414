<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StTallyPackingDetailBackRepository">
    <insert id="insertStTallyPackingDetailBack" parameterType="com.zte.domain.model.material.StTallyPackingHead">
        insert into st_tally_packing_detail_back
        (tally_packing_detail_guid, tally_packing_head_guid, freight_number, fork_plate_number,
        packing_type, packing_number, package_number, quantity_unit, source_repository, target_warehouse,
        distribution_direction, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        w_volume, w_weight, start_no, attr1
        )
        select tally_packing_detail_guid, tally_packing_head_guid, freight_number, fork_plate_number,
        packing_type, packing_number, package_number, quantity_unit, source_repository, target_warehouse,
        distribution_direction, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        w_volume, w_weight, start_no, attr1
        from st_tally_packing_detail
        where tally_packing_head_guid in (
        <foreach collection="list" separator="," item="item">
            #{item.tallyPackingHeadGuid,jdbcType=VARCHAR}
        </foreach>
        )
    </insert>
</mapper>
