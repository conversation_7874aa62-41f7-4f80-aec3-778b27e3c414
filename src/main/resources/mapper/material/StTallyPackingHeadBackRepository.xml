<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.StTallyPackingHeadBackRepository">
    <insert id="insertStTallyPackingHeadBack" parameterType="com.zte.domain.model.material.StTallyPackingHead">
        insert into st_tally_packing_head_back
        (tally_packing_head_guid, freight_number, fork_plate_number, package_number, source_repository,
        target_warehouse, is_freight, shipping_type, expense_department, distribution_direction,
        receipt_address, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        w_volume, w_weight, status, submited_name, node_name, node_update_time, node_place_address,
        way_bill_id
        )
        select tally_packing_head_guid, freight_number, fork_plate_number, package_number, source_repository,
        target_warehouse, is_freight, shipping_type, expense_department, distribution_direction,
        receipt_address, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        w_volume, w_weight, status, submited_name, node_name, node_update_time, node_place_address,
        way_bill_id from (
        <foreach collection="list" separator="union all" item="item">
            select #{item.tallyPackingHeadGuid,jdbcType=VARCHAR} tally_packing_head_guid,
            #{item.freightNumber,jdbcType=VARCHAR} freight_number,
            #{item.forkPlateNumber,jdbcType=VARCHAR} fork_plate_number,
            #{item.packageNumber,jdbcType=VARCHAR} package_number,
            #{item.sourceRepository,jdbcType=VARCHAR} source_repository,
            #{item.targetWarehouse,jdbcType=VARCHAR} target_warehouse,
            #{item.isFreight,jdbcType=VARCHAR} is_freight, #{item.shippingType,jdbcType=VARCHAR} shipping_type,
            #{item.expenseDepartment,jdbcType=VARCHAR} expense_department,
            #{item.distributionDirection,jdbcType=VARCHAR} distribution_direction,
            #{item.receiptAddress,jdbcType=VARCHAR} receipt_address,
            #{item.createBy,jdbcType=VARCHAR} create_by, #{item.createDate,jdbcType=VARCHAR} create_date,
            #{item.lastUpdatedBy,jdbcType=VARCHAR} last_updated_by,
            #{item.lastUpdatedDate,jdbcType=VARCHAR} last_updated_date,
            #{item.enabledFlag,jdbcType=VARCHAR} enabled_flag, #{item.wVolume,jdbcType=VARCHAR} w_volume,
            #{item.wWeight,jdbcType=VARCHAR} w_weight, #{item.status,jdbcType=VARCHAR} status,
            #{item.submitedName,jdbcType=VARCHAR} submited_name, #{item.nodeName,jdbcType=VARCHAR} node_name,
            #{item.nodeUpdateTime,jdbcType=VARCHAR} node_update_time,
            #{item.nodePlaceAddress,jdbcType=VARCHAR} node_place_address,
            #{item.wayBillId,jdbcType=VARCHAR} way_bill_id
            from dual
        </foreach>
        ) t
    </insert>
</mapper>
