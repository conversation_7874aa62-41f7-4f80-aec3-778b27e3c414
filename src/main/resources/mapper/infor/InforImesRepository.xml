<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforImesRepository">
    <resultMap id="EdiSoSImesMap" type="com.zte.interfaces.infor.dto.EdiSoSImesDTO">
        <result column="lottable02" jdbcType="VARCHAR" property="lottable02"/>
        <result column="lottable08" jdbcType="VARCHAR" property="lottable08"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="shippedqty" jdbcType="DECIMAL" property="shippedqty"/>
        <result column="ref45" jdbcType="VARCHAR" property="ref45"/>
        <result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2"/>
        <result column="descr" jdbcType="VARCHAR" property="descr"/>
        <result column="qty" jdbcType="DECIMAL" property="qty"/>
    </resultMap>
    <resultMap id="InfoQtyMap" type="com.zte.interfaces.infor.dto.InforImesReturnQtyDto">
        <result column="issuedQty" jdbcType="INTEGER" property="issuedQty"/>
        <result column="returnQty" jdbcType="INTEGER" property="returnQty"/>
        <result column="taskReqQty" jdbcType="INTEGER" property="taskReqQty"/>
        <result column="reqQty" jdbcType="INTEGER" property="reqQty"/>
        <result column="sku" jdbcType="VARCHAR" property="itemNo"/>
    </resultMap>

    <select id="queryEdiSoSByKey2" resultMap="EdiSoSImesMap">
        SELECT
        ess.externalorderkey2, ess.ref45, ess.sku, sk.descr, ess.lottable02,
        sum(ess.shippedqty) as shippedqty
        FROM plugin.edi_so_s ess
        JOIN enterprise.sku sk ON
        sk.sku = ess.sku AND sk.storerkey = 'ZTE'
        WHERE
        ess.externalorderkey2 = #{externalorderkey2}
        GROUP BY
        ess.externalorderkey2,
        ess.orderkey,
        ess.ref45,
        ess.sku,
        sk.descr,
        ess.lottable02
    </select>

    <select id="queryWhseidByKey2" resultType="java.lang.String">
        select distinct(ess.whseid) FROM plugin.edi_so_s ess
        where
        ess.externalorderkey2 = #{externalorderkey2}
    </select>

    <select id="queryAllStatus" statementType="STATEMENT" resultType="java.lang.Integer">
        select sum(num) from
        (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            SELECT count(1) as num
            FROM ${item}.orders oo
            where oo.externalorderkey2 = '${externalorderkey2}'
            and
            <![CDATA[
            oo.status < '95'
            ]]>
        </foreach>
        )
    </select>

    <!-- 单板条码 复核查询 -->
    <select id="queryVeneerInfo" resultMap="EdiSoSImesMap">
        SELECT epo.sku,epo.lottable02,epo.qty
        FROM plugin.edi_pcbserial_out epo
        where
        epo.externalorderkey2 = #{externalorderkey2}
        and epo.serialnumber = #{serialnumber}
    </select>

    <!-- 查询原材料信息 -->
    <select id="queryProcessedInfo" resultMap="EdiSoSImesMap" statementType="STATEMENT">
        select sku,lottable02,sum(qty) qty from (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            SELECT p.sku as sku ,it.lottable02 as lottable02 ,p.qty as qty
            FROM ${item}.pickdetail p
            join ${item}.itrn it
            on p.pickdetailkey = it.sourcekey
            and p.lot = it.lot
            where it.trantype = 'MV'
            and it.sourcetype in ('PICKING')
            and p.orderkey in
            (
            SELECT oo.orderkey
            FROM ${item}.orders oo
            where oo.externalorderkey2 = '${externalorderkey2}'
            )
            and it.fromid = '${serialnumber}'
            and it.fromloc <![CDATA[
                <>
            ]]> it.toloc
        </foreach> )
        group by sku,lottable02
    </select>

    <!-- 查询退料仓 -->
    <select id="getReturnWarehouse" parameterType="com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto" resultType="java.lang.String">
        SELECT distinct whseid FROM plugin.edi_po
        where externalreceiptkey2 = #{prodplanNo,jdbcType=VARCHAR}
        and href11 = '200' and sku = #{itemNo,jdbcType=VARCHAR}
    </select>
    <!-- 查询已退单未收料 -->
    <select id="getIsReturnReceived" parameterType="com.zte.interfaces.infor.dto.InforImesReturnWarehouseDto" resultType="java.lang.Integer">
        select sum(num) from
        (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            select count(1) as num
            from ${item}.receipt rr
            join ${item}.receiptdetail rd on rr.receiptkey = rd.receiptkey
            where rr.ref11 = '200'
            and rr.externalreceiptkey2 = #{prodplanNo,jdbcType=VARCHAR}
            and rd.sku = #{itemNo,jdbcType=VARCHAR}
            and rr.po_s = 'N'
        </foreach>
        )
    </select>
    <!-- 查询已退数量、任务退数量 -->
    <select id="getReturnQty" parameterType="com.zte.interfaces.infor.dto.InforImesQtyDto" resultMap="InfoQtyMap">
        SELECT sku,SUM(qtystore) returnQty,sum(ref39) taskReqQty
        FROM plugin.edi_po_s
        where href11 = '200'
        and externalreceiptkey2 = #{prodplanNo,jdbcType=VARCHAR}
        and sku
        in (
        <foreach collection="itemNoList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) group by sku
    </select>
    <!-- 查询已发数量 -->
    <select id="getIssuedQty" parameterType="com.zte.interfaces.infor.dto.InforImesQtyDto" resultMap="InfoQtyMap">
        SELECT sku,SUM(shippedqty) issuedQty
        FROM plugin.edi_so_s
        where href11 in ('140','130','120','100')
        and externalorderkey2 = #{prodplanNo,jdbcType=VARCHAR}
        and sku
        in (
        <foreach collection="itemNoList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) group by sku
    </select>
    <!-- 查询需求数量 -->
    <select id="getReqQty" parameterType="com.zte.interfaces.infor.dto.InforImesQtyDto" resultMap="InfoQtyMap">
        SELECT sku,SUM(originalqty) reqQty
        FROM plugin.edi_so
        where href11 in ('140','130','120','100')
        and externalorderkey2 = #{prodplanNo,jdbcType=VARCHAR}
        and sku
        in (
        <foreach collection="itemNoList" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) group by sku
    </select>
    <!-- 根据单据、仓库或条码查询已完成收货的条码 -->
    <select id="getSerialnumber" parameterType="com.zte.interfaces.infor.vo.ZteinboundserialVo" resultType="java.lang.String">
        select zz.serialnumber
        from ${whseid}.zteinboundserial zz
        where 1=1
        <if test = "externreceiptkey != null and externreceiptkey !='' " >
            and zz.externreceiptkey = #{externreceiptkey, jdbcType=VARCHAR}
        </if>
        <if test = "serialnumber != null and serialnumber !='' " >
            and zz.serialnumber = #{serialnumber, jdbcType=VARCHAR}
        </if>
        and exists (select 1 from ${whseid}.serialinventory st where st.serialnumber = zz.serialnumber)
    </select>
    <!-- infor库位码正确性校验 -->
    <select id="checkInforLoc" parameterType="com.zte.interfaces.infor.vo.LocVo" resultType="java.lang.Integer">
        select count(1) from ${whseid}.loc where loc = #{loc, jdbcType=VARCHAR}
    </select>
    <select id="queryEdiSoSByKey2AndSku" parameterType="com.zte.interfaces.infor.dto.EdiSoSImesDTO" resultType="java.lang.String">
        SELECT
        es.whseid
        FROM plugin.edi_so es
        WHERE
        es.externalorderkey2 = #{externalorderkey2, jdbcType=VARCHAR}
        and es.sku = #{sku, jdbcType=VARCHAR}
    </select>
    <select id="queryEdiSoSByWhseidAndSku" parameterType="java.lang.String" resultType="java.lang.String">
        select vv.classgroup
        from ${whseid}.v_putawayzone_classgroup vv
        where vv.PUTAWAYZONE in (select sk.PUTAWAYZONE
        from ${whseid}.sku sk
        where sk.storerkey = 'ZTE'
        and sk.sku = #{sku, jdbcType=VARCHAR})
    </select>
</mapper>