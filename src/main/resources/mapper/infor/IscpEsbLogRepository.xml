<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.IscpEsbLogRepository"> 
  <resultMap id="BaseResult1Map" type="com.zte.domain.model.infor.IscpEsbLog">
    <result column="EXTERNRECEIPTKEY" jdbcType="OTHER" property="externreceiptkey" />
    <result column="SOURCESYSTEMID" jdbcType="OTHER" property="sourcesystemid" />
    <result column="SOURCESYSTEMNAME" jdbcType="OTHER" property="sourcesystemname" />
    <result column="USERID" jdbcType="OTHER" property="userid" />
    <result column="USERNAME" jdbcType="DECIMAL" property="username" />
    <result column="SUBMITDATE" jdbcType="TIMESTAMP" property="submitdate" />
    <result column="SERVICECODE" jdbcType="OTHER" property="servicecode" />
    <result column="MSGID" jdbcType="OTHER" property="msgid" />
    <result column="MESSAGEPRIORITY" jdbcType="DECIMAL" property="messagepriority" />
    <result column="MESSAGETYPE" jdbcType="OTHER" property="messagetype" />
    <result column="RELSERVICECODE" jdbcType="OTHER" property="relservicecode" />
    <result column="RELMSGID" jdbcType="OTHER" property="relmsgid" />
    <result column="ADDDATE" jdbcType="TIMESTAMP" property="adddate" />
    <result column="EDITDATE" jdbcType="TIMESTAMP" property="editdate" />
    <result column="PROCESSFLAG" jdbcType="DECIMAL" property="processflag" />
    <result column="PROCESSCOUNT" jdbcType="DECIMAL" property="processcount" />
    <result column="FEEDBACKDATE" jdbcType="TIMESTAMP" property="feedbackdate" />
    <result column="RESULTMESSAGE" jdbcType="OTHER" property="resultmessage" /> 
  </resultMap>

  <select id="selectIscpEsbLog" parameterType="com.zte.domain.model.infor.IscpEsbLog" resultMap="BaseResult1Map">
    select EXTERNRECEIPTKEY, SOURCESYSTEMID, SOURCESYSTEMNAME, USERID, USERNAME, SUBMITDATE, 
    SERVICECODE, MSGID, MESSAGEPRIORITY, MESSAGETYPE, RELSERVICECODE, RELMSGID, ADDDATE, 
    EDITDATE, PROCESSFLAG, NVL(PROCESSCOUNT,0) PROCESSCOUNT, FEEDBACKDATE, RESULTMESSAGE
    from ISCP_ESB_LOG
    WHERE 1=1
    <if test=" externreceiptkey !=null and externreceiptkey !='' ">
       and EXTERNRECEIPTKEY=#{externreceiptkey,jdbcType=OTHER}
    </if>
    <if test=" sourcesystemid !=null and sourcesystemid !='' ">
       and SOURCESYSTEMID=#{sourcesystemid,jdbcType=OTHER}
    </if>
    <if test=" sourcesystemname !=null and sourcesystemname !='' ">
       and SOURCESYSTEMNAME=#{sourcesystemname,jdbcType=OTHER}
    </if>
     <if test=" messagepriority !=null and messagepriority > 0 ">
       and MESSAGEPRIORITY=#{messagepriority}
    </if>
     <if test=" resultmessage !=null and resultmessage != '' ">
       and RESULTMESSAGE=#{resultmessage}
    </if>
    
  </select>

  <insert id="insertIscpEsbLog" parameterType="com.zte.domain.model.infor.IscpEsbLog">
    insert into ISCP_ESB_LOG (EXTERNRECEIPTKEY, SOURCESYSTEMID, SOURCESYSTEMNAME, 
      USERID, SUBMITDATE, SERVICECODE,ADDDATE, EDITDATE, 
      PROCESSCOUNT, FEEDBACKDATE, RESULTMESSAGE, RESULTMESSAGE2,MESSAGEPRIORITY)
    values (#{externreceiptkey,jdbcType=OTHER}, #{sourcesystemid,jdbcType=OTHER}, #{sourcesystemname,jdbcType=OTHER}, 
      #{userid,jdbcType=OTHER}, SYSDATE, #{servicecode,jdbcType=OTHER},      
      sysdate, sysdate,1, sysdate, #{resultmessage,jdbcType=OTHER}, #{resultmessage2,jdbcType=OTHER},#{messagepriority})
  </insert>

  <select id="queryListApplybillNo" parameterType="java.lang.String" resultType="java.lang.String">
    select EXTERNRECEIPTKEY from plugin.iscp_esb_log iel where iel.servicecode != '0000' and
    iel.processcount <![CDATA[ < ]]> 4
      <if test=" applybillNo !=null and applybillNo != '' ">
          and EXTERNRECEIPTKEY = #{applybillNo,jdbcType=VARCHAR}
      </if>
  </select>

    <update  id="updateFailReelIDLog" parameterType="com.zte.domain.model.infor.IscpEsbLog">
    UPDATE plugin.iscp_esb_log iel SET
    iel.servicecode = #{servicecode,jdbcType=VARCHAR},
    iel.editdate = sysdate,
    iel.processcount = processcount + 1,
    iel.resultmessage = #{resultmessage,jdbcType=VARCHAR},
    iel.resultmessage2 = #{resultmessage2,jdbcType=VARCHAR}
    WHERE iel.externreceiptkey = #{externreceiptkey,jdbcType=VARCHAR}
  </update>

    <insert id="insertListApplybillNo" parameterType="java.lang.String">
    insert into PLUGIN.ISCP_ESB_LOG(EXTERNRECEIPTKEY,SOURCESYSTEMID,SOURCESYSTEMNAME,MESSAGEPRIORITY,ADDDATE,PROCESSCOUNT,SERVICECODE)
    select #{singleApplybillNo,jdbcType=VARCHAR},'ISCP','ISCP','4',sysdate,0,'0001'
    from DUAL where not exists(select * from PLUGIN.ISCP_ESB_LOG
    where PLUGIN.ISCP_ESB_LOG.EXTERNRECEIPTKEY=#{singleApplybillNo,jdbcType=VARCHAR})
  </insert>

  <update  id="updateIscpEsbLog" parameterType="com.zte.domain.model.infor.IscpEsbLog">
     update  ISCP_ESB_LOG set      
     PROCESSCOUNT=PROCESSCOUNT+1,
     EDITDATE=sysdate,     
     RESULTMESSAGE=#{resultmessage,jdbcType=OTHER},
     RESULTMESSAGE2=#{resultmessage2,jdbcType=OTHER}
     <if test="servicecode !=null and servicecode !=''">
       , SERVICECODE=#{servicecode}
    </if>
     where EXTERNRECEIPTKEY=#{externreceiptkey,jdbcType=OTHER}
     <if test=" messagepriority !=null and messagepriority > 0 ">
       and MESSAGEPRIORITY=#{messagepriority}
    </if>
      <if test=" sourcesystemid !=null and sourcesystemid != '' ">
       and SOURCESYSTEMID=#{sourcesystemid}
    </if>
    <if test=" sourcesystemname !=null and sourcesystemname !='' ">
       and SOURCESYSTEMNAME=#{sourcesystemname,jdbcType=OTHER}
    </if>
  </update>

	<select id="getQaExInspectionPack" resultType="com.zte.interfaces.infor.dto.QaExInspectionPack" parameterType="com.zte.interfaces.infor.dto.QaExInspectionDetail" >
		select #{receiveNo,jdbcType = VARCHAR} receiveNo,
		       #{receiptKey,jdbcType = VARCHAR} receiptKey,
		       #{lineId,jdbcType = VARCHAR} lineId,
		       #{itemBarcode,jdbcType = VARCHAR} packNo,
		       #{itemBarcode,jdbcType = VARCHAR} code22,
		       #{productionDate,jdbcType = VARCHAR} productionDate,
		       t.serialnumber reel,
		       t.qty qty,
               t.loc packLocation
		  from ${stock}.serialinventory t
		  join ${stock}.lotattribute lt
		    on t.lot = lt.lot
		 where lt.lottable02 = #{itemBarcode, jdbcType = VARCHAR}
		   and t.qty > 0
		   and lt.storerkey = 'ZTE'
	</select>
    <select id="getWmwhseXLocationMapList" resultType="com.zte.interfaces.infor.dto.QaExInspectionDetail">
      SELECT ll.loc location,ll.locationcategory locationcategory FROM ${stock}.loc ll where ll.loc in
      <foreach collection="locationList" item="location" index="index" open="(" close=")" separator=",">
        #{location}
      </foreach>
    </select>
</mapper>
