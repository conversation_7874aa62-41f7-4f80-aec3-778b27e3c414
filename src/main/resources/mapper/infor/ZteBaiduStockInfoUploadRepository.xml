<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ZteBaiduStockInfoUploadRepository">

    <select id="getBaiduStockInfoList" parameterType="com.zte.interfaces.step.dto.ZteBaiduInfoDTO"
            resultType="com.zte.interfaces.step.dto.ZteBaiduReturnDTO">
        select  lower(ci.customer_component_type) resTypeName,
                ci.customer_specification resSpecName,
                ci.customer_material_type resPartName,
                '' originalManName,
                '' originalPartName,
                ci.zte_code oemPartName
        from plugin.customer_items ci
        where upper(ci.customer_name) = 'BAIDU'
        and ci.zte_code not like '1%'
        <if test="itemNo !=null and itemNo.size()>0">
            and ci.zte_code in
            <foreach collection="itemNo" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

</mapper>