<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.WarehouseRoadWorkRepository">

	<select id="getLocTotal" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO" resultType="java.lang.Integer">
		select count(1) from ${whseid}.loc
		where 1=1
		<if test="locList != null and locList.size() > 0">
			and loc in
			<foreach collection="locList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="loc != null and loc !='' ">
			and loc = #{loc,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getWarehouseRoadWorkList" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO" resultType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO">
		select s.* from (
		select t.serialkey,
		t.whseid,
		f.DESCRIPTION as whName,
		t.loc,
		t.warehouse,
		slv1.description warehouseDesc,
		t.warehouse_area as warehouseArea,
		slv2.description warehouseAreaDesc,
		t.loc_mark as locMark,
		t.coordinate_type as coordinateType,
		t.average_capacity as averageCapacity,
		t.loc_coordinate_x as locCoordinateX,
		t.loc_coordinate_y as locCoordinateY,
		t.loc_coordinate_z as locCoordinateZ,
		t.loc_long as locLong,
		t.loc_width as locWidth,
		t.loc_high as locHigh,
		t.inf_point_coordinate_x1 as infPointCoordinateX1,
		t.inf_point_coordinate_y1 as infPointCoordinateY1,
		t.inf_point_coordinate_x2 as infPointCoordinateX2,
		t.inf_point_coordinate_y2 as infPointCoordinateY2,
		t.enabled_flag as enabledFlag,
		decode(t.enabled_flag,'Y','有效','N','无效','') as enabledFlagDesc,
		t.created_by as createdBy,
		t.creation_date as creationDate,
		t.last_updated_by as lastUpdatedBy,
		t.last_update_date as lastUpdateDate,
		t.remark,
		rownum rn
		from plugin.warehouse_road_work t
		left join ENTERPRISE.FACILITYNEST f
		on t.whseid = upper(substr(f.name, 7))
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000064' and slv1.lookup_meaning = t.warehouse
		left join plugin.sys_lookup_values slv2
		on slv2.lookup_type = '1000065' and slv2.lookup_meaning = t.warehouse_area
		where slv1.enabled_flag = 'Y'
		and slv2.enabled_flag = 'Y'
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and t.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="loc != null and loc !='' ">
			and t.loc  = #{loc,jdbcType=VARCHAR}
		</if>
		<if test="coordinateType != null and coordinateType !='' ">
			and t.coordinate_type = #{coordinateType,jdbcType=VARCHAR}
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			and t.enabled_flag  = #{enabledFlag,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and t.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and t.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and t.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<select id="getWarehouseRoadWorkListVOTotal" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.warehouse_road_work t
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and t.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="loc != null and loc !='' ">
			and t.loc  = #{loc,jdbcType=VARCHAR}
		</if>
		<if test="coordinateType != null and coordinateType !='' ">
			and t.coordinate_type = #{coordinateType,jdbcType=VARCHAR}
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			and t.enabled_flag  = #{enabledFlag,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and t.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and t.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and t.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<insert id="saveWarehouseRoadWork" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO">
		merge into plugin.warehouse_road_work a
		using (select nvl(#{serialkey,jdbcType=VARCHAR},-1) serialkey,
		upper(#{whseid,jdbcType=VARCHAR}) whseid, #{loc,jdbcType=VARCHAR} loc, #{warehouse,jdbcType=VARCHAR} warehouse,
		#{warehouseArea,jdbcType=VARCHAR} warehouse_area, #{locMark,jdbcType=VARCHAR} loc_mark,
		#{coordinateType,jdbcType=VARCHAR} coordinate_type, #{averageCapacity,jdbcType=VARCHAR} average_capacity,
		#{locCoordinateX,jdbcType=DECIMAL} loc_coordinate_x, #{locCoordinateY,jdbcType=DECIMAL} loc_coordinate_y,
		#{locCoordinateZ,jdbcType=DECIMAL} loc_coordinate_z, #{locLong,jdbcType=DECIMAL} loc_long,
		#{locWidth,jdbcType=DECIMAL} loc_width, #{locHigh,jdbcType=DECIMAL} loc_high,
		#{infPointCoordinateX1,jdbcType=DECIMAL} inf_point_coordinate_x1, #{infPointCoordinateY1,jdbcType=DECIMAL} inf_point_coordinate_y1,
		#{infPointCoordinateX2,jdbcType=DECIMAL} inf_point_coordinate_x2, #{infPointCoordinateY2,jdbcType=DECIMAL} inf_point_coordinate_y2,
		#{createdBy,jdbcType=VARCHAR} created_by, #{lastUpdatedBy,jdbcType=VARCHAR} last_updated_by, #{remark,jdbcType=VARCHAR} remark
		from dual) b
		on (a.serialkey = b.serialkey)
		when matched then
		update set a.whseid=b.whseid, a.loc=b.loc, a.warehouse=b.warehouse, a.warehouse_area=b.warehouse_area, a.loc_mark=b.loc_mark,
		a.coordinate_type=b.coordinate_type, a.average_capacity=b.average_capacity, a.loc_coordinate_x=b.loc_coordinate_x,
		a.loc_coordinate_y=b.loc_coordinate_y, a.loc_coordinate_z=b.loc_coordinate_z, a.loc_long=b.loc_long,
		a.loc_width=b.loc_width, a.loc_high=b.loc_high, a.inf_point_coordinate_x1=b.inf_point_coordinate_x1,
		a.inf_point_coordinate_y1=b.inf_point_coordinate_y1, a.inf_point_coordinate_x2=b.inf_point_coordinate_x2,
		a.inf_point_coordinate_y2=b.inf_point_coordinate_y2, a.last_updated_by=b.last_updated_by,
		a.last_update_date = sysdate, a.remark = b.remark
		when not matched then
		insert (a.serialkey, a.whseid, a.loc, a.warehouse, a.warehouse_area, a.loc_mark,
		a.coordinate_type, a.average_capacity, a.loc_coordinate_x, a.loc_coordinate_y, a.loc_coordinate_z,
		a.loc_long, a.loc_width, a.loc_high, a.inf_point_coordinate_x1, a.inf_point_coordinate_y1,
		a.inf_point_coordinate_x2, a.inf_point_coordinate_y2, a.enabled_flag, a.created_by, a.creation_date,
		a.last_updated_by, a.last_update_date, a.remark)
		values (plugin.warehouse_road_work_s.nextval, b.whseid, b.loc, b.warehouse, b.warehouse_area,
		b.loc_mark, b.coordinate_type, b.average_capacity, b.loc_coordinate_x, b.loc_coordinate_y,
		b.loc_coordinate_z, b.loc_long, b.loc_width, b.loc_high, b.inf_point_coordinate_x1, b.inf_point_coordinate_y1,
		b.inf_point_coordinate_x2, b.inf_point_coordinate_y2, 'Y', b.created_by, sysdate, b.last_updated_by, sysdate, b.remark)
	</insert>

	<insert id="saveWarehouseRoadWorkList" parameterType="java.util.List">
		insert into plugin.warehouse_road_work (serialkey, whseid, loc, warehouse, warehouse_area,
		loc_mark, coordinate_type, average_capacity, loc_coordinate_x, loc_coordinate_y,
		loc_coordinate_z, loc_long, loc_width, loc_high, inf_point_coordinate_x1, inf_point_coordinate_y1,
		inf_point_coordinate_x2, inf_point_coordinate_y2, enabled_flag, created_by, creation_date, last_updated_by,
		last_update_date, remark)
		select plugin.warehouse_road_work_s.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			upper(#{item.whseid,jdbcType=VARCHAR}), #{item.loc,jdbcType=VARCHAR}, #{item.warehouse,jdbcType=VARCHAR}, #{item.warehouseArea,jdbcType=VARCHAR},
			#{item.locMark,jdbcType=VARCHAR}, #{item.coordinateType,jdbcType=VARCHAR}, #{item.averageCapacity,jdbcType=VARCHAR},
			#{item.locCoordinateX,jdbcType=DECIMAL}, #{item.locCoordinateY,jdbcType=DECIMAL}, #{item.locCoordinateZ,jdbcType=DECIMAL},
			#{item.locLong,jdbcType=DECIMAL}, #{item.locWidth,jdbcType=DECIMAL}, #{item.locHigh,jdbcType=DECIMAL},
			#{item.infPointCoordinateX1,jdbcType=DECIMAL}, #{item.infPointCoordinateY1,jdbcType=DECIMAL},
			#{item.infPointCoordinateX2,jdbcType=DECIMAL}, #{item.infPointCoordinateY2,jdbcType=DECIMAL}, 'Y', #{item.createdBy,jdbcType=VARCHAR},
			sysdate as creationDate, #{item.lastUpdatedBy,jdbcType=VARCHAR}, sysdate as lastUpdateDate, #{item.remark,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>

	<update id="updateWarehouseRoadWork" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO">
		update plugin.warehouse_road_work
		set
		<if test="whseid != null and applyByNo !='' ">
			whseid = #{whseid, whseid=VARCHAR},
		</if>
		<if test="loc != null and loc !='' ">
			loc = #{loc, jdbcType=VARCHAR},
		</if>
		<if test="warehouse != null and warehouse !='' ">
			warehouse = #{warehouse, jdbcType=VARCHAR},
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			warehouse_area = #{warehouseArea, jdbcType=VARCHAR},
		</if>
		<if test="locMark != null and locMark !='' ">
			loc_mark = #{locMark, jdbcType=VARCHAR},
		</if>
		<if test="coordinateType != null and coordinateType !='' ">
			coordinate_type = #{coordinateType, jdbcType=VARCHAR},
		</if>
		<if test="averageCapacity != null and averageCapacity !='' ">
			average_capacity = #{averageCapacity, jdbcType=VARCHAR},
		</if>
		<if test="locCoordinateX != null">
			loc_coordinate_x = #{locCoordinateX, jdbcType=DECIMAL},
		</if>
		<if test="locCoordinateY != null">
			loc_coordinate_y = #{locCoordinateY, jdbcType=DECIMAL},
		</if>
		<if test="locCoordinateZ != null">
			loc_coordinate_z = #{locCoordinateZ, jdbcType=DECIMAL},
		</if>
		<if test="locLong != null">
			loc_long = #{locLong, jdbcType=DECIMAL},
		</if>
		<if test="locWidth != null">
			loc_width = #{locWidth, jdbcType=DECIMAL},
		</if>
		<if test="locHigh != null">
			loc_high = #{locHigh, jdbcType=DECIMAL},
		</if>
		<if test="infPointCoordinateX1 != null">
			inf_point_coordinate_x1 = #{infPointCoordinateX1, jdbcType=DECIMAL},
		</if>
		<if test="infPointCoordinateY1 != null">
			inf_point_coordinate_y1 = #{infPointCoordinateY1, jdbcType=DECIMAL},
		</if>
		<if test="infPointCoordinateX2 != null">
			inf_point_coordinate_x2 = #{infPointCoordinateX2, jdbcType=DECIMAL},
		</if>
		<if test="infPointCoordinateY2 != null">
			inf_point_coordinate_y2 = #{infPointCoordinateY2, jdbcType=DECIMAL},
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			enabled_flag = #{enabledFlag, jdbcType=VARCHAR},
		</if>
		<if test="lastUpdatedBy != null and lastUpdatedBy !='' ">
			last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
		</if>
		<if test="remark != null and remark !='' ">
			remark = #{remark, jdbcType=VARCHAR},
		</if>
		last_update_date = sysdate
		where 1=1
		<if test="serialkey != null">
			and serialkey = #{serialkey,jdbcType=VARCHAR}
		</if>
		<if test="serialkey == null">
			and 1=2
		</if>
	</update>

	<select id="getWarehouseRoadWorkCount" parameterType="com.zte.interfaces.infor.dto.WarehouseRoadWorkDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.warehouse_road_work t
		where 1 = 1
		<if test="list != null and list.size() > 0">
			and (whseid,loc) in
			<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
				(#{item.whseid,jdbcType=VARCHAR},#{item.loc,jdbcType=VARCHAR})
			</foreach>
		</if>
		and t.enabled_flag  = 'Y'
	</select>
</mapper>