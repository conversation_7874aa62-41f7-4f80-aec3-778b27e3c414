<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforWarnRepository">
	<resultMap id="AllocateMonitorResultMap" type="com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo">
		<result column="whseid" jdbcType="VARCHAR" property="whseId" />
		<result column="putawayzone" jdbcType="VARCHAR" property="putawayZone" />
		<result column="sku" jdbcType="VARCHAR" property="sku" />
		<result column="itemBarcode" jdbcType="VARCHAR" property="itemBarcode" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="description" jdbcType="VARCHAR" property="description" />
		<result column="qty" jdbcType="VARCHAR" property="qty" />
		<result column="store_age" jdbcType="VARCHAR" property="storeAge" />
	</resultMap>
	<select id="getAllocateExceptionMonitor" parameterType="com.zte.interfaces.infor.vo.AllocateExceptionMonitorVo" resultMap="AllocateMonitorResultMap">
		select s.* from (select t.whseid,
		t.putawayzone,
		t.sku,
		t.lottable02 itemBarcode,
		t.status,
		t.description,
		t.qty,
		t.store_age,rownum rn
		from plugin.allocate_exception_monitor t
		where  t.exception_type = #{exceptionType,jdbcType=INTEGER}
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<select id="getAllocateExceptionMonitorTotal"  resultType="java.lang.Integer">
	select count(1)
	from plugin.allocate_exception_monitor t
	where t.exception_type = #{exceptionType,jdbcType=INTEGER}
	</select>
</mapper>