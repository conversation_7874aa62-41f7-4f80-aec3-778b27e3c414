<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.EdiAsnqcSRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.infor.EdiAsnqcS">
        <result column="SERIALKEY" jdbcType="DECIMAL" property="serialkey" />
        <result column="LOGID" jdbcType="OTHER" property="logid" />
        <result column="EDI_ADDDATE" jdbcType="TIMESTAMP" property="ediAdddate" />
        <result column="EDI_EDITDATE" jdbcType="TIMESTAMP" property="ediEditdate" />
        <result column="SYMBOL" jdbcType="DECIMAL" property="symbol" />
        <result column="INVSYMBOL" jdbcType="DECIMAL" property="invsymbol" />
        <result column="INVFAILDSYMBOL" jdbcType="DECIMAL" property="invfaildsymbol" />
        <result column="FEEDBACKFLAG" jdbcType="DECIMAL" property="feedbackflag" />
        <result column="FEEDBACKDATE" jdbcType="TIMESTAMP" property="feedbackdate" />
        <result column="RECEIPTKEY" jdbcType="OTHER" property="receiptkey" />
        <result column="EXPECTEDRECEIPTDATE" jdbcType="TIMESTAMP" property="expectedreceiptdate" />
        <result column="EXTERNRECEIPTKEY" jdbcType="OTHER" property="externreceiptkey" />
        <result column="EXTERNALRECEIPTKEY2" jdbcType="OTHER" property="externalreceiptkey2" />
        <result column="WHSEID" jdbcType="OTHER" property="whseid" />
        <result column="HSUSR1" jdbcType="OTHER" property="hsusr1" />
        <result column="HSUSR2" jdbcType="OTHER" property="hsusr2" />
        <result column="HSUSR3" jdbcType="OTHER" property="hsusr3" />
        <result column="HSUSR4" jdbcType="OTHER" property="hsusr4" />
        <result column="HSUSR5" jdbcType="OTHER" property="hsusr5" />
        <result column="RECEIPTID" jdbcType="OTHER" property="receiptid" />
        <result column="HREF01" jdbcType="OTHER" property="href01" />
        <result column="HREF02" jdbcType="OTHER" property="href02" />
        <result column="HREF03" jdbcType="OTHER" property="href03" />
        <result column="HREF04" jdbcType="OTHER" property="href04" />
        <result column="HREF05" jdbcType="OTHER" property="href05" />
        <result column="HREF06" jdbcType="OTHER" property="href06" />
        <result column="HREF11" jdbcType="OTHER" property="href11" />
        <result column="HREF12" jdbcType="OTHER" property="href12" />
        <result column="HREF13" jdbcType="OTHER" property="href13" />
        <result column="HREF14" jdbcType="OTHER" property="href14" />
        <result column="HREF15" jdbcType="OTHER" property="href15" />
        <result column="RECEIPTDATE" jdbcType="TIMESTAMP" property="receiptdate" />
        <result column="RECEIPTLINENUMBER" jdbcType="OTHER" property="receiptlinenumber" />
        <result column="EXTERNLINENO" jdbcType="OTHER" property="externlineno" />
        <result column="STORERKEY" jdbcType="OTHER" property="storerkey" />
        <result column="SKU" jdbcType="OTHER" property="sku" />
        <result column="QTYEXPECTED" jdbcType="DECIMAL" property="qtyexpected" />
        <result column="QTYRECEIVED" jdbcType="DECIMAL" property="qtyreceived" />
        <result column="QCREQUIRED" jdbcType="OTHER" property="qcrequired" />
        <result column="SUSR1" jdbcType="OTHER" property="susr1" />
        <result column="SUSR2" jdbcType="OTHER" property="susr2" />
        <result column="SUSR3" jdbcType="OTHER" property="susr3" />
        <result column="SUSR4" jdbcType="OTHER" property="susr4" />
        <result column="SUSR5" jdbcType="OTHER" property="susr5" />
        <result column="LOT" jdbcType="OTHER" property="lot" />
        <result column="LOTTABLE01" jdbcType="OTHER" property="lottable01" />
        <result column="LOTTABLE02" jdbcType="OTHER" property="lottable02" />
        <result column="LOTTABLE03" jdbcType="OTHER" property="lottable03" />
        <result column="LOTTABLE04" jdbcType="TIMESTAMP" property="lottable04" />
        <result column="LOTTABLE05" jdbcType="TIMESTAMP" property="lottable05" />
        <result column="LOTTABLE06" jdbcType="OTHER" property="lottable06" />
        <result column="LOTTABLE07" jdbcType="OTHER" property="lottable07" />
        <result column="LOTTABLE08" jdbcType="OTHER" property="lottable08" />
        <result column="LOTTABLE09" jdbcType="OTHER" property="lottable09" />
        <result column="LOTTABLE10" jdbcType="OTHER" property="lottable10" />
        <result column="LOTTABLE11" jdbcType="TIMESTAMP" property="lottable11" />
        <result column="LOTTABLE12" jdbcType="TIMESTAMP" property="lottable12" />
        <result column="REF01" jdbcType="OTHER" property="ref01" />
        <result column="REF02" jdbcType="OTHER" property="ref02" />
        <result column="REF03" jdbcType="OTHER" property="ref03" />
        <result column="REF04" jdbcType="OTHER" property="ref04" />
        <result column="REF05" jdbcType="OTHER" property="ref05" />
        <result column="REF06" jdbcType="OTHER" property="ref06" />
        <result column="REF07" jdbcType="OTHER" property="ref07" />
        <result column="REF08" jdbcType="OTHER" property="ref08" />
        <result column="REF09" jdbcType="OTHER" property="ref09" />
        <result column="REF10" jdbcType="OTHER" property="ref10" />
        <result column="REF11" jdbcType="OTHER" property="ref11" />
        <result column="TOID" jdbcType="OTHER" property="toid" />
        <result column="QTY" jdbcType="DECIMAL" property="qty" />
        <result column="LOC" jdbcType="OTHER" property="loc" />
        <result column="ADDDATE" jdbcType="TIMESTAMP" property="adddate" />
        <result column="ADDWHO" jdbcType="OTHER" property="addwho" />
        <result column="EDITDATE" jdbcType="TIMESTAMP" property="editdate" />
        <result column="EDITWHO" jdbcType="OTHER" property="editwho" />
        <result column="AREA" jdbcType="VARCHAR" property="area" />
    </resultMap>

    <resultMap id="QualityCheckMap" type="com.zte.interfaces.infor.dto.AsnQcCheckDTO">
        <result column="receiveNo" jdbcType="VARCHAR" property="receiveNo" />
        <result column="billType" jdbcType="VARCHAR" property="billType" />
        <result column="purchaseOrg" jdbcType="VARCHAR" property="purchaseOrg" />
        <result column="receiptOrg" jdbcType="VARCHAR" property="receiptOrg" />
        <result column="supplierName" jdbcType="VARCHAR" property="supplierName" />
        <result column="supplierNo" jdbcType="VARCHAR" property="supplierNo" />
        <result column="receiptKey" jdbcType="VARCHAR" property="receiptKey" />
        <result column="receiveTime" jdbcType="VARCHAR" property="receiveTime" />
        <result column="sendingPerson" jdbcType="VARCHAR" property="sendingPerson" />
        <result column="shipmentMode" jdbcType="VARCHAR" property="shipmentMode" />
        <result column="stockAddress" jdbcType="VARCHAR" property="stockAddress" />
        <result column="deliveryAddress" jdbcType="VARCHAR" property="deliveryAddress" />
        <result column="stock" jdbcType="VARCHAR" property="stock" />
        <result column="deliveryCountry" jdbcType="VARCHAR" property="deliveryCountry" />
        <result column="deliveryCountryNo" jdbcType="VARCHAR" property="deliveryCountryNo" />
        <result column="expectArrivalDate" jdbcType="VARCHAR" property="expectArrivalDate" />
        <result column="receiptId" jdbcType="VARCHAR" property="receiptId" />
    </resultMap>
    <resultMap id="QualityCheckDetailMap" type="com.zte.interfaces.infor.dto.AsnQcCheckDetailDTO">
        <result column="receiptKey" jdbcType="VARCHAR" property="receiptKey" />
        <result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
        <result column="uuid" jdbcType="VARCHAR" property="uuid" />
        <result column="storerkey" jdbcType="VARCHAR" property="storerkey" />
        <result column="packageRequire" jdbcType="VARCHAR" property="packageRequire" />
        <result column="itemName" jdbcType="VARCHAR" property="itemName" />
        <result column="brandNo" jdbcType="VARCHAR" property="brandNo" />
        <result column="brandName" jdbcType="VARCHAR" property="brandName" />
        <result column="brandStyle" jdbcType="VARCHAR" property="brandStyle" />
        <result column="qtyReceived" jdbcType="DECIMAL" property="qtyReceived" />
        <result column="receiptlinenumber" jdbcType="VARCHAR" property="receiptlinenumber" />
        <result column="lineId" jdbcType="VARCHAR" property="lineId" />
        <result column="deliveryQty" jdbcType="DECIMAL" property="deliveryQty" />
        <result column="receiveNo" jdbcType="VARCHAR" property="receiveNo" />
        <result column="stock" jdbcType="VARCHAR" property="stock" />
        <result column="testMode" jdbcType="VARCHAR" property="testMode" />
        <result column="envProperty" jdbcType="VARCHAR" property="envProperty" />
        <result column="isNeedSupReport" jdbcType="DECIMAL" property="isNeedSupReport" />
        <result column="qtyExpected" jdbcType="DECIMAL" property="qtyExpected" />
        <result column="barcodeControlType" jdbcType="VARCHAR" property="barcodeControlType" />
        <result column="transferBillNo" jdbcType="VARCHAR" property="transferBillNo" />
    </resultMap>

    <resultMap id="QualityCheckDetailBoxMap" type="com.zte.interfaces.infor.dto.AsnQcCheckBoxDTO">
        <result column="receiveNo" jdbcType="VARCHAR" property="receiveNo" />
        <result column="barcode" jdbcType="VARCHAR" property="barcode" />
        <result column="uuid" jdbcType="VARCHAR" property="uuid" />
        <result column="lineId" jdbcType="VARCHAR" property="lineId" />
        <result column="toid" jdbcType="VARCHAR" property="toid" />
        <result column="packLocation" jdbcType="VARCHAR" property="packLocation" />
        <result column="productionDate" jdbcType="VARCHAR" property="productionDate" />
        <result column="batchNo" jdbcType="VARCHAR" property="batchNo" />
        <result column="countryOfOrigin" jdbcType="VARCHAR" property="countryOfOrigin" />
        <result column="minPackCode" jdbcType="VARCHAR" property="minPackCode" />
        <result column="expectArrivalDate" jdbcType="VARCHAR" property="expectArrivalDate" />
        <result column="qty" jdbcType="DECIMAL" property="qty" />
    </resultMap>

    <resultMap id="VmiStoreWmwhseMap" type="com.zte.interfaces.infor.dto.VmiStoreDTO">
        <result column="supplyNo" jdbcType="VARCHAR" property="supplyNo" />
        <result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
        <result column="itemName" jdbcType="VARCHAR" property="itemName" />
        <result column="uuid" jdbcType="VARCHAR" property="uuid" />
        <result column="brandNo" jdbcType="VARCHAR" property="brandNo" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="whseName" jdbcType="VARCHAR" property="whseName" />
        <result column="whseCode" jdbcType="VARCHAR" property="whseCode" />
        <result column="receiveOrg" jdbcType="VARCHAR" property="receiveOrg" />
        <result column="prodPlanId" jdbcType="VARCHAR" property="prodPlanId" />
        <result column="boxNo" jdbcType="VARCHAR" property="boxNo" />
        <result column="reelId" jdbcType="VARCHAR" property="reelId" />
        <result column="itemBarcode" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="instoreDate" jdbcType="VARCHAR" property="instoreDate" />
        <result column="qty" jdbcType="DECIMAL" property="qty" />
        <result column="brandstyle" jdbcType="VARCHAR" property="brandstyle" />
        <result column="serialkey" jdbcType="VARCHAR" property="serialKey"></result>
        <result column="whseid" jdbcType="VARCHAR" property="whseid"></result>
    </resultMap>
     <resultMap id="VmiQtyReelIdMap" type="com.zte.domain.model.infor.VmiQtyReelIdMode">
         <result column="qty" jdbcType="DECIMAL" property="qty"></result>
         <result column="reelId" jdbcType="VARCHAR" property="reelId"></result>
         <result column="serialkey" jdbcType="VARCHAR" property="serialKey"></result>
     </resultMap>
    <resultMap id="AsnQcDataMap" type="com.zte.interfaces.infor.dto.AsnqcTransDTO">
        <result column="RECEIPTKEY" jdbcType="VARCHAR" property="receiptKey" />
        <result column="WHSEID" jdbcType="VARCHAR" property="whseId" />
    </resultMap>

    <resultMap id="BondedWarehouseInventoryInfoMap" type="com.zte.domain.model.infor.BondedWarehouseInventoryInfo">
        <result column="whseid" jdbcType="VARCHAR" property="stockNo" />
        <result column="sku" jdbcType="VARCHAR" property="itemCode" />
        <result column="lottable02" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="lottable06" jdbcType="VARCHAR" property="storeKey" />
        <result column="id" jdbcType="VARCHAR" property="boxNo" />
        <result column="qty" jdbcType="DECIMAL" property="qty" />
    </resultMap>

    <select id="selectAsnQcData"  parameterType="com.zte.interfaces.infor.dto.AsnqcTransDTO" resultMap="AsnQcDataMap">
        SELECT T.RECEIPTKEY,T.WHSEID
          FROM (SELECT EAS.RECEIPTKEY, EAS.WHSEID
                  FROM PLUGIN.EDI_ASNQC_S EAS
                 WHERE EAS.REF05 = 'ISCP'
                   AND EAS.REF10 IS NULL
                   AND EAS.EDI_ADDDATE > SYSDATE-30
                   AND NOT EXISTS
                 (SELECT 1
                          FROM PLUGIN.ISCP_ESB_LOG L
                         WHERE L.EXTERNRECEIPTKEY = EAS.EXTERNRECEIPTKEY
                           AND L.SOURCESYSTEMID = EAS.RECEIPTKEY
                           AND L.PROCESSCOUNT > 4
                           AND L.ADDDATE > SYSDATE - 30
                           AND L.MESSAGEPRIORITY = 2)
                 GROUP BY EAS.RECEIPTKEY, EAS.WHSEID) T
        WHERE  ROWNUM<![CDATA[<=]]> #{rn,jdbcType=DECIMAL}
  </select>
  <select id="selectAsnQcDataDelay"  parameterType="com.zte.interfaces.infor.dto.AsnqcTransDTO" resultMap="AsnQcDataMap">
     SELECT EAS.RECEIPTKEY, EAS.WHSEID
	  FROM PLUGIN.ISCP_ESB_LOG L, PLUGIN.EDI_ASNQC_S EAS
	 WHERE L.MESSAGEPRIORITY = 2
	   AND L.RESULTMESSAGE <![CDATA[<>]]> 'S-'
	   AND L.PROCESSCOUNT <![CDATA[>]]> 4
	   AND L.PROCESSCOUNT <![CDATA[<]]> 11
	   AND L.EXTERNRECEIPTKEY = EAS.EXTERNRECEIPTKEY
	   AND L.SOURCESYSTEMID = EAS.RECEIPTKEY
	 GROUP BY EAS.RECEIPTKEY, EAS.WHSEID         

  </select>

    <select id="selectEdiAsnqcSAll" resultMap="BaseResultMap">
    select SERIALKEY, LOGID, EDI_ADDDATE, EDI_EDITDATE, SYMBOL, INVSYMBOL, INVFAILDSYMBOL, 
    FEEDBACKFLAG, FEEDBACKDATE, RECEIPTKEY, EXPECTEDRECEIPTDATE, EXTERNRECEIPTKEY, EXTERNALRECEIPTKEY2, 
    WHSEID, HSUSR1, HSUSR2, HSUSR3, HSUSR4, HSUSR5, RECEIPTID, HREF01, HREF02, HREF03, 
    HREF04, HREF05, HREF06, HREF11, HREF12, HREF13, HREF14, HREF15, RECEIPTDATE, RECEIPTLINENUMBER, 
    EXTERNLINENO, STORERKEY, SKU, QTYEXPECTED, QTYRECEIVED, QCREQUIRED, SUSR1, SUSR2, 
    SUSR3, SUSR4, SUSR5, LOT, LOTTABLE01, LOTTABLE02, LOTTABLE03, LOTTABLE04, LOTTABLE05, 
    LOTTABLE06, LOTTABLE07, LOTTABLE08, LOTTABLE09, LOTTABLE10, LOTTABLE11, LOTTABLE12, 
    REF01, REF02, REF03, REF04, REF05, REF06, REF07, REF08, REF09, REF10, REF11, TOID, 
    QTY, LOC, ADDDATE, ADDWHO, EDITDATE, EDITWHO, AREA
    from EDI_ASNQC_S
  </select>

    <select id="selectQualityCheck" parameterType="com.zte.domain.model.infor.EdiAsnqcS" resultMap="QualityCheckMap">
        SELECT eas.externreceiptkey receiveNo,
        eas.href11 billType,
        p.HREF43 purchaseOrg,
        eas.href01 receiptOrg,
        p.HREF44 supplierName,
        eas.HSUSR5 supplierNo,
        eas.RECEIPTKEY receiptKey,
        to_char(sysdate, 'yyyy-mm-dd HH24:mi:ss') receiveTime,
        eas.editwho sendingPerson,
        p.HREF45 shipmentMode,
        p.ref44 stockAddress,
        p.HREF47 deliveryAddress,
        eas.WHSEID stock,
        p.HREF49 deliveryCountry,
        p.HREF48 deliveryCountryNo,
        to_char(p.HREF56, 'yyyy-mm-dd HH24:mi:ss') expectArrivalDate,
        eas.receiptid receiptId
        FROM plugin.edi_asnqc_s eas, plugin.edi_po p
        WHERE p.externreceiptkey = eas.externreceiptkey
        and p.externreceiptkey = eas.externreceiptkey
        and p.externlineno = eas.externlineno
        and eas.ref05 = 'ISCP'
        and p.symbol=1
        <if test="receiptkey != null and receiptkey !='' ">
            and eas.receiptkey=#{receiptkey,jdbcType=OTHER}
        </if>
        <if test=" whseid !=null and whseid !='' ">
            and eas.WHSEID = #{whseid,jdbcType=OTHER}
        </if>
        <if test="receiptkey == null">
            and eas.ref10 is null
            and not exists (
            select 1 from plugin.iscp_esb_log l
            where l.externreceiptkey=eas.externreceiptkey
            and l.sourcesystemid=eas.receiptkey
            and l.processcount>4
            and l.messagepriority =2
            )
        </if>
        and rownum  <![CDATA[<=]]>100
        GROUP BY eas.externreceiptkey,
        eas.href11,
        p.HREF43,
        eas.href01,
        p.HREF44,
        eas.HSUSR5,
        eas.RECEIPTKEY,
        eas.editdate,
        eas.editwho,
        p.HREF45,
        p.ref44,
        p.HREF47,
        eas.WHSEID,
        p.HREF49,
        p.HREF48,
        p.HREF56,
        eas.receiptid
    </select>

    <select id="selectQualityCheckDetail" parameterType="com.zte.domain.model.infor.EdiAsnqcS" resultMap="QualityCheckDetailMap">
   SELECT eas.RECEIPTKEY receiptKey, 
       eas.sku        itemNo, 
       eas.lottable07 uuid, 
       eas.storerkey  storerkey,
       ''       packageRequire,
       item.descr     itemName, 
       p.lottable10 brandNo,
       u.brand_name brandName,
       u.brand_style brandStyle,
       eas.receiptlinenumber receiptlinenumber,
       eas.QTYRECEIVED qtyReceived,
       eas.externlineno lineId,
       sum(eas.qty)  deliveryQty ,
       eas.externreceiptkey receiveNo,
       eas.loc stock, 
       eas.REF11 testMode,
       eas.LOTTABLE08 envProperty,
       nvl((select REF47
             from plugin.edi_po po
            where po.externreceiptkey = eas.externreceiptkey
              and po.sku = eas.sku
              and po.symbol=1
              and po.lottable07 = eas.lottable07
              and rownum = 1),
           0) isNeedSupReport,
       eas.QTYEXPECTED qtyExpected,
       nvl(item.reelidprocess,0) barcodeControlType,
       (select REF52
             from plugin.edi_po po
            where po.externreceiptkey = eas.externreceiptkey
              and po.sku = eas.sku
              and po.symbol=1
              and po.lottable07 = eas.lottable07
              and po.externlineno=eas.externlineno
              and rownum = 1) transferBillNo
  FROM plugin.edi_asnqc_s eas
  left join enterprise.sku item
    on item.sku = eas.sku
    and item.STORERKEY=eas.STORERKEY
  left join ENTERPRISE.UUID u
    on u.item_uuid = eas.lottable07
    JOIN (SELECT DISTINCT  
                         p.lottable10,
                         p.externlineno,
                         p.externreceiptkey,
                         p.receiptkey
           FROM plugin.edi_po p 
           where  p.symbol=1 
         ) P
     ON p.externreceiptkey = eas.externreceiptkey
    AND p.externlineno = eas.externlineno     
 WHERE eas.receiptkey = #{receiptkey,jdbcType=OTHER}
     AND eas.whseid= #{whseid,jdbcType=OTHER}     
    
 GROUP BY eas.RECEIPTKEY,
          eas.sku,
          eas.lottable07,
          eas.storerkey,
          item.descr,
          p.lottable10,
          u.brand_name,
          u.brand_style, 
          eas.QTYRECEIVED,
          eas.receiptlinenumber, 
          eas.externlineno,
          eas.externreceiptkey,
          eas.loc , 
          eas.REF11,
          eas.LOTTABLE08,
          eas.QTYEXPECTED,
          item.reelidprocess
  </select>

    <select id="selectQualityCheckDetailBox" parameterType="com.zte.domain.model.infor.EdiAsnqcS" resultMap="QualityCheckDetailBoxMap">
   select distinct eas.externreceiptkey receiveNo,
         eas.lottable02 barcode,
         eas.lottable07 uuid,
         eas.externlineno lineId, 
         eas.toid,
         eas.loc packLocation,
         to_char(eas.lottable04, 'yyyy-mm-dd HH24:mi:ss') productionDate,
         nvl(sr.qty,eas.qty) qty,
         p.REF54 batchNo,
         (select REF49
             from plugin.edi_po po
            where po.externreceiptkey = eas.externreceiptkey
              and po.lottable02 = eas.lottable02
              and po.lottable07 = eas.lottable07
              and po.symbol=1 
              and rownum = 1 ) countryOfOrigin,
         '' minPackCode,
         to_char(p.HREF56, 'yyyy-mm-dd HH24:mi:ss') expectArrivalDate,
         sr.reelid reelid
    from plugin.edi_asnqc_s eas         
         left join plugin.edi_asnqc_s_detail sr
	     on sr.lottable02 = eas.LOTTABLE02
	     AND sr.WHSEID = eas.WHSEID
	     AND sr.TOID = eas.TOID
	     AND sr.RECEIPTKEY = eas.RECEIPTKEY      
	     and exists (select 1
          from enterprise.sku sku
         where sku.sku = eas.sku
           and sku.reelidprocess = 1
           and sku.Storerkey=EAS.STORERKEY)
     JOIN (SELECT DISTINCT p.REF54, 
                         p.externlineno,
                         p.externreceiptkey,
                         p.HREF56,
                         P.LOTTABLE02
           FROM plugin.edi_po p 
           where  p.symbol=1
         ) P 
     on p.externreceiptkey = eas.externreceiptkey 
     and p.externlineno = eas.externlineno 
     and p.LOTTABLE02=eas.lottable02
     
   where        
     EAS.RECEIPTKEY = #{receiptkey,jdbcType=OTHER}
     AND eas.whseid= #{whseid,jdbcType=OTHER}     
     and EAS.receiptlinenumber = #{receiptlinenumber,jdbcType=OTHER}
  </select>

    <insert id="insertEdiAsnqcS" parameterType="com.zte.domain.model.infor.EdiAsnqcS">
    insert into EDI_ASNQC_S (SERIALKEY, LOGID, EDI_ADDDATE, 
      EDI_EDITDATE, SYMBOL, INVSYMBOL, 
      INVFAILDSYMBOL, FEEDBACKFLAG, FEEDBACKDATE, 
      RECEIPTKEY, EXPECTEDRECEIPTDATE, EXTERNRECEIPTKEY, 
      EXTERNALRECEIPTKEY2, WHSEID, HSUSR1, 
      HSUSR2, HSUSR3, HSUSR4, HSUSR5, 
      RECEIPTID, HREF01, HREF02, 
      HREF03, HREF04, HREF05, HREF06, 
      HREF11, HREF12, HREF13, HREF14, 
      HREF15, RECEIPTDATE, RECEIPTLINENUMBER, 
      EXTERNLINENO, STORERKEY, SKU, 
      QTYEXPECTED, QTYRECEIVED, QCREQUIRED, 
      SUSR1, SUSR2, SUSR3, SUSR4, 
      SUSR5, LOT, LOTTABLE01, LOTTABLE02, 
      LOTTABLE03, LOTTABLE04, LOTTABLE05, 
      LOTTABLE06, LOTTABLE07, LOTTABLE08, 
      LOTTABLE09, LOTTABLE10, LOTTABLE11, 
      LOTTABLE12, REF01, REF02, 
      REF03, REF04, REF05, REF06, 
      REF07, REF08, REF09, REF10, 
      REF11, TOID, QTY, LOC, 
      ADDDATE, ADDWHO, EDITDATE, 
      EDITWHO, AREA)
    values (#{serialkey,jdbcType=DECIMAL}, #{logid,jdbcType=OTHER}, #{ediAdddate,jdbcType=TIMESTAMP}, 
      #{ediEditdate,jdbcType=TIMESTAMP}, #{symbol,jdbcType=DECIMAL}, #{invsymbol,jdbcType=DECIMAL}, 
      #{invfaildsymbol,jdbcType=DECIMAL}, #{feedbackflag,jdbcType=DECIMAL}, #{feedbackdate,jdbcType=TIMESTAMP}, 
      #{receiptkey,jdbcType=OTHER}, #{expectedreceiptdate,jdbcType=TIMESTAMP}, #{externreceiptkey,jdbcType=OTHER}, 
      #{externalreceiptkey2,jdbcType=OTHER}, #{whseid,jdbcType=OTHER}, #{hsusr1,jdbcType=OTHER}, 
      #{hsusr2,jdbcType=OTHER}, #{hsusr3,jdbcType=OTHER}, #{hsusr4,jdbcType=OTHER}, #{hsusr5,jdbcType=OTHER}, 
      #{receiptid,jdbcType=OTHER}, #{href01,jdbcType=OTHER}, #{href02,jdbcType=OTHER}, 
      #{href03,jdbcType=OTHER}, #{href04,jdbcType=OTHER}, #{href05,jdbcType=OTHER}, #{href06,jdbcType=OTHER}, 
      #{href11,jdbcType=OTHER}, #{href12,jdbcType=OTHER}, #{href13,jdbcType=OTHER}, #{href14,jdbcType=OTHER}, 
      #{href15,jdbcType=OTHER}, #{receiptdate,jdbcType=TIMESTAMP}, #{receiptlinenumber,jdbcType=OTHER}, 
      #{externlineno,jdbcType=OTHER}, #{storerkey,jdbcType=OTHER}, #{sku,jdbcType=OTHER}, 
      #{qtyexpected,jdbcType=DECIMAL}, #{qtyreceived,jdbcType=DECIMAL}, #{qcrequired,jdbcType=OTHER}, 
      #{susr1,jdbcType=OTHER}, #{susr2,jdbcType=OTHER}, #{susr3,jdbcType=OTHER}, #{susr4,jdbcType=OTHER}, 
      #{susr5,jdbcType=OTHER}, #{lot,jdbcType=OTHER}, #{lottable01,jdbcType=OTHER}, #{lottable02,jdbcType=OTHER}, 
      #{lottable03,jdbcType=OTHER}, #{lottable04,jdbcType=TIMESTAMP}, #{lottable05,jdbcType=TIMESTAMP}, 
      #{lottable06,jdbcType=OTHER}, #{lottable07,jdbcType=OTHER}, #{lottable08,jdbcType=OTHER}, 
      #{lottable09,jdbcType=OTHER}, #{lottable10,jdbcType=OTHER}, #{lottable11,jdbcType=TIMESTAMP}, 
      #{lottable12,jdbcType=TIMESTAMP}, #{ref01,jdbcType=OTHER}, #{ref02,jdbcType=OTHER}, 
      #{ref03,jdbcType=OTHER}, #{ref04,jdbcType=OTHER}, #{ref05,jdbcType=OTHER}, #{ref06,jdbcType=OTHER}, 
      #{ref07,jdbcType=OTHER}, #{ref08,jdbcType=OTHER}, #{ref09,jdbcType=OTHER}, #{ref10,jdbcType=OTHER}, 
      #{ref11,jdbcType=OTHER}, #{toid,jdbcType=OTHER}, #{qty,jdbcType=DECIMAL}, #{loc,jdbcType=OTHER}, 
      #{adddate,jdbcType=TIMESTAMP}, #{addwho,jdbcType=OTHER}, #{editdate,jdbcType=TIMESTAMP}, 
      #{editwho,jdbcType=OTHER}, #{area,jdbcType=VARCHAR})
  </insert>

    <insert id="insertEdiAsnqcSSelective" parameterType="com.zte.domain.model.infor.EdiAsnqcS">
        insert into EDI_ASNQC_S
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialkey != null">
                SERIALKEY,
            </if>

            <if test="logid != null">
                LOGID,
            </if>

            <if test="ediAdddate != null">
                EDI_ADDDATE,
            </if>

            <if test="ediEditdate != null">
                EDI_EDITDATE,
            </if>

            <if test="symbol != null">
                SYMBOL,
            </if>

            <if test="invsymbol != null">
                INVSYMBOL,
            </if>

            <if test="invfaildsymbol != null">
                INVFAILDSYMBOL,
            </if>

            <if test="feedbackflag != null">
                FEEDBACKFLAG,
            </if>

            <if test="feedbackdate != null">
                FEEDBACKDATE,
            </if>

            <if test="receiptkey != null">
                RECEIPTKEY,
            </if>

            <if test="expectedreceiptdate != null">
                EXPECTEDRECEIPTDATE,
            </if>

            <if test="externreceiptkey != null">
                EXTERNRECEIPTKEY,
            </if>

            <if test="externalreceiptkey2 != null">
                EXTERNALRECEIPTKEY2,
            </if>

            <if test="whseid != null">
                WHSEID,
            </if>

            <if test="hsusr1 != null">
                HSUSR1,
            </if>

            <if test="hsusr2 != null">
                HSUSR2,
            </if>

            <if test="hsusr3 != null">
                HSUSR3,
            </if>

            <if test="hsusr4 != null">
                HSUSR4,
            </if>

            <if test="hsusr5 != null">
                HSUSR5,
            </if>

            <if test="receiptid != null">
                RECEIPTID,
            </if>

            <if test="href01 != null">
                HREF01,
            </if>

            <if test="href02 != null">
                HREF02,
            </if>

            <if test="href03 != null">
                HREF03,
            </if>

            <if test="href04 != null">
                HREF04,
            </if>

            <if test="href05 != null">
                HREF05,
            </if>

            <if test="href06 != null">
                HREF06,
            </if>

            <if test="href11 != null">
                HREF11,
            </if>

            <if test="href12 != null">
                HREF12,
            </if>

            <if test="href13 != null">
                HREF13,
            </if>

            <if test="href14 != null">
                HREF14,
            </if>

            <if test="href15 != null">
                HREF15,
            </if>

            <if test="receiptdate != null">
                RECEIPTDATE,
            </if>

            <if test="receiptlinenumber != null">
                RECEIPTLINENUMBER,
            </if>

            <if test="externlineno != null">
                EXTERNLINENO,
            </if>

            <if test="storerkey != null">
                STORERKEY,
            </if>

            <if test="sku != null">
                SKU,
            </if>

            <if test="qtyexpected != null">
                QTYEXPECTED,
            </if>

            <if test="qtyreceived != null">
                QTYRECEIVED,
            </if>

            <if test="qcrequired != null">
                QCREQUIRED,
            </if>

            <if test="susr1 != null">
                SUSR1,
            </if>

            <if test="susr2 != null">
                SUSR2,
            </if>

            <if test="susr3 != null">
                SUSR3,
            </if>

            <if test="susr4 != null">
                SUSR4,
            </if>

            <if test="susr5 != null">
                SUSR5,
            </if>

            <if test="lot != null">
                LOT,
            </if>

            <if test="lottable01 != null">
                LOTTABLE01,
            </if>

            <if test="lottable02 != null">
                LOTTABLE02,
            </if>

            <if test="lottable03 != null">
                LOTTABLE03,
            </if>

            <if test="lottable04 != null">
                LOTTABLE04,
            </if>

            <if test="lottable05 != null">
                LOTTABLE05,
            </if>

            <if test="lottable06 != null">
                LOTTABLE06,
            </if>

            <if test="lottable07 != null">
                LOTTABLE07,
            </if>

            <if test="lottable08 != null">
                LOTTABLE08,
            </if>

            <if test="lottable09 != null">
                LOTTABLE09,
            </if>

            <if test="lottable10 != null">
                LOTTABLE10,
            </if>

            <if test="lottable11 != null">
                LOTTABLE11,
            </if>

            <if test="lottable12 != null">
                LOTTABLE12,
            </if>

            <if test="ref01 != null">
                REF01,
            </if>

            <if test="ref02 != null">
                REF02,
            </if>

            <if test="ref03 != null">
                REF03,
            </if>

            <if test="ref04 != null">
                REF04,
            </if>

            <if test="ref05 != null">
                REF05,
            </if>

            <if test="ref06 != null">
                REF06,
            </if>

            <if test="ref07 != null">
                REF07,
            </if>

            <if test="ref08 != null">
                REF08,
            </if>

            <if test="ref09 != null">
                REF09,
            </if>

            <if test="ref10 != null">
                REF10,
            </if>

            <if test="ref11 != null">
                REF11,
            </if>

            <if test="toid != null">
                TOID,
            </if>

            <if test="qty != null">
                QTY,
            </if>

            <if test="loc != null">
                LOC,
            </if>

            <if test="adddate != null">
                ADDDATE,
            </if>

            <if test="addwho != null">
                ADDWHO,
            </if>

            <if test="editdate != null">
                EDITDATE,
            </if>

            <if test="editwho != null">
                EDITWHO,
            </if>

            <if test="area != null">
                AREA,
            </if>

        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialkey != null">
                #{serialkey,jdbcType=DECIMAL},
            </if>

            <if test="logid != null">
                #{logid,jdbcType=OTHER},
            </if>

            <if test="ediAdddate != null">
                #{ediAdddate,jdbcType=TIMESTAMP},
            </if>

            <if test="ediEditdate != null">
                #{ediEditdate,jdbcType=TIMESTAMP},
            </if>

            <if test="symbol != null">
                #{symbol,jdbcType=DECIMAL},
            </if>

            <if test="invsymbol != null">
                #{invsymbol,jdbcType=DECIMAL},
            </if>

            <if test="invfaildsymbol != null">
                #{invfaildsymbol,jdbcType=DECIMAL},
            </if>

            <if test="feedbackflag != null">
                #{feedbackflag,jdbcType=DECIMAL},
            </if>

            <if test="feedbackdate != null">
                #{feedbackdate,jdbcType=TIMESTAMP},
            </if>

            <if test="receiptkey != null">
                #{receiptkey,jdbcType=OTHER},
            </if>

            <if test="expectedreceiptdate != null">
                #{expectedreceiptdate,jdbcType=TIMESTAMP},
            </if>

            <if test="externreceiptkey != null">
                #{externreceiptkey,jdbcType=OTHER},
            </if>

            <if test="externalreceiptkey2 != null">
                #{externalreceiptkey2,jdbcType=OTHER},
            </if>

            <if test="whseid != null">
                #{whseid,jdbcType=OTHER},
            </if>

            <if test="hsusr1 != null">
                #{hsusr1,jdbcType=OTHER},
            </if>

            <if test="hsusr2 != null">
                #{hsusr2,jdbcType=OTHER},
            </if>

            <if test="hsusr3 != null">
                #{hsusr3,jdbcType=OTHER},
            </if>

            <if test="hsusr4 != null">
                #{hsusr4,jdbcType=OTHER},
            </if>

            <if test="hsusr5 != null">
                #{hsusr5,jdbcType=OTHER},
            </if>

            <if test="receiptid != null">
                #{receiptid,jdbcType=OTHER},
            </if>

            <if test="href01 != null">
                #{href01,jdbcType=OTHER},
            </if>

            <if test="href02 != null">
                #{href02,jdbcType=OTHER},
            </if>

            <if test="href03 != null">
                #{href03,jdbcType=OTHER},
            </if>

            <if test="href04 != null">
                #{href04,jdbcType=OTHER},
            </if>

            <if test="href05 != null">
                #{href05,jdbcType=OTHER},
            </if>

            <if test="href06 != null">
                #{href06,jdbcType=OTHER},
            </if>

            <if test="href11 != null">
                #{href11,jdbcType=OTHER},
            </if>

            <if test="href12 != null">
                #{href12,jdbcType=OTHER},
            </if>

            <if test="href13 != null">
                #{href13,jdbcType=OTHER},
            </if>

            <if test="href14 != null">
                #{href14,jdbcType=OTHER},
            </if>

            <if test="href15 != null">
                #{href15,jdbcType=OTHER},
            </if>

            <if test="receiptdate != null">
                #{receiptdate,jdbcType=TIMESTAMP},
            </if>

            <if test="receiptlinenumber != null">
                #{receiptlinenumber,jdbcType=OTHER},
            </if>

            <if test="externlineno != null">
                #{externlineno,jdbcType=OTHER},
            </if>

            <if test="storerkey != null">
                #{storerkey,jdbcType=OTHER},
            </if>

            <if test="sku != null">
                #{sku,jdbcType=OTHER},
            </if>

            <if test="qtyexpected != null">
                #{qtyexpected,jdbcType=DECIMAL},
            </if>

            <if test="qtyreceived != null">
                #{qtyreceived,jdbcType=DECIMAL},
            </if>

            <if test="qcrequired != null">
                #{qcrequired,jdbcType=OTHER},
            </if>

            <if test="susr1 != null">
                #{susr1,jdbcType=OTHER},
            </if>

            <if test="susr2 != null">
                #{susr2,jdbcType=OTHER},
            </if>

            <if test="susr3 != null">
                #{susr3,jdbcType=OTHER},
            </if>

            <if test="susr4 != null">
                #{susr4,jdbcType=OTHER},
            </if>

            <if test="susr5 != null">
                #{susr5,jdbcType=OTHER},
            </if>

            <if test="lot != null">
                #{lot,jdbcType=OTHER},
            </if>

            <if test="lottable01 != null">
                #{lottable01,jdbcType=OTHER},
            </if>

            <if test="lottable02 != null">
                #{lottable02,jdbcType=OTHER},
            </if>

            <if test="lottable03 != null">
                #{lottable03,jdbcType=OTHER},
            </if>

            <if test="lottable04 != null">
                #{lottable04,jdbcType=TIMESTAMP},
            </if>

            <if test="lottable05 != null">
                #{lottable05,jdbcType=TIMESTAMP},
            </if>

            <if test="lottable06 != null">
                #{lottable06,jdbcType=OTHER},
            </if>

            <if test="lottable07 != null">
                #{lottable07,jdbcType=OTHER},
            </if>

            <if test="lottable08 != null">
                #{lottable08,jdbcType=OTHER},
            </if>

            <if test="lottable09 != null">
                #{lottable09,jdbcType=OTHER},
            </if>

            <if test="lottable10 != null">
                #{lottable10,jdbcType=OTHER},
            </if>

            <if test="lottable11 != null">
                #{lottable11,jdbcType=TIMESTAMP},
            </if>

            <if test="lottable12 != null">
                #{lottable12,jdbcType=TIMESTAMP},
            </if>

            <if test="ref01 != null">
                #{ref01,jdbcType=OTHER},
            </if>

            <if test="ref02 != null">
                #{ref02,jdbcType=OTHER},
            </if>

            <if test="ref03 != null">
                #{ref03,jdbcType=OTHER},
            </if>

            <if test="ref04 != null">
                #{ref04,jdbcType=OTHER},
            </if>

            <if test="ref05 != null">
                #{ref05,jdbcType=OTHER},
            </if>

            <if test="ref06 != null">
                #{ref06,jdbcType=OTHER},
            </if>

            <if test="ref07 != null">
                #{ref07,jdbcType=OTHER},
            </if>

            <if test="ref08 != null">
                #{ref08,jdbcType=OTHER},
            </if>

            <if test="ref09 != null">
                #{ref09,jdbcType=OTHER},
            </if>

            <if test="ref10 != null">
                #{ref10,jdbcType=OTHER},
            </if>

            <if test="ref11 != null">
                #{ref11,jdbcType=OTHER},
            </if>

            <if test="toid != null">
                #{toid,jdbcType=OTHER},
            </if>

            <if test="qty != null">
                #{qty,jdbcType=DECIMAL},
            </if>

            <if test="loc != null">
                #{loc,jdbcType=OTHER},
            </if>

            <if test="adddate != null">
                #{adddate,jdbcType=TIMESTAMP},
            </if>

            <if test="addwho != null">
                #{addwho,jdbcType=OTHER},
            </if>

            <if test="editdate != null">
                #{editdate,jdbcType=TIMESTAMP},
            </if>

            <if test="editwho != null">
                #{editwho,jdbcType=OTHER},
            </if>

            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>

        </trim>

    </insert>

    <update id="updateAsnFlag" parameterType="com.zte.domain.model.infor.EdiAsnqcS">
        update EDI_ASNQC_S set ref10 = 1
        where externreceiptkey = #{externreceiptkey,jdbcType=VARCHAR}
        <if test=" receiptkey!=null and  receiptkey!='' ">
            and RECEIPTKEY=  #{receiptkey,jdbcType=OTHER}
        </if>
    </update>

    <select id="findVmiStoreByWmwhse" parameterType="com.zte.interfaces.infor.dto.VmiStoreParamDTO"  resultMap="VmiStoreWmwhseMap">
        SELECT * FROM (
        SELECT skbs.*,ROWNUM as rowNo FROM (
        SELECT  bas.*,
        uu.brand_no brandNo,
        C.UDF2 receiveOrg
        FROM (SELECT
        zb.item_barcode itemBarcode,
        zb.supplier_no supplyNo,
        zb.supplier_model brandstyle,
        zb.item_id,
        zb.item_uuid,
        p.db_alias whseName,
        LPAD(replace(upper(lx.whseid),'WMWHSE',''),4,'0') whseCode,
        lx.sku itemNo,
        nvl(lx.id,'') boxNo,
        lx.serialkey,
        lx.whseid,
        lx.storerkey,
        lt.lottable07 uuid,
        lt.lottable11 instoreDate,
        sk.descr itemName,
        zb.product_no prodPlanId,
        sk.busr2 unit
        FROM
        enterprise.ztebarcode zb,
        ${whseid}.lotxlocxid lx,
        wmsadmin.pl_db p,
        ${whseid}.lotattribute lt,
        ${whseid}.sku sk
        WHERE lt.lot = lx.lot
        AND lt.sku = lx.sku
        AND lt.lottable02 = zb.item_barcode
        AND sk.sku = lt.sku
        AND upper(p.db_logid)=upper(lx.whseid)
        <![CDATA[
        AND lt.lottable06 <> 'KX' AND lt.lottable06 <> 'ME' AND lt.lottable06 <> ' '
        ]]>
        AND LT.STORERKEY=SK.STORERKEY
        AND lx.status = 'OK'
        AND lx.storerkey='ZTE' ) bas
        LEFT JOIN ${whseid}.serialinventory si
        ON bas.itemNo = si.sku
        AND si.storerkey = 'ZTE'
        LEFT JOIN ENTERPRISE.CODELKUP  C
        ON upper(C.SHORT)=upper(bas.whseid)
        AND C.LISTNAME = 'ZWHLIST'
        LEFT JOIN enterprise.uuid uu
        ON uu.item_id = bas.item_id
        AND uu.item_uuid = bas.item_uuid
        ) skbs
        WHERE 1=1
        <if test="supplyNo != null and supplyNo !='' ">
            and skbs.supplyNo =  #{supplyNo,jdbcType=VARCHAR}
        </if>
        <if test="itemNo != null  and itemNo !='' ">
            and skbs.itemNo = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="brandstyle != null  and brandstyle !='' ">
            and skbs.brandstyle = #{brandstyle,jdbcType=VARCHAR}
        </if>
        <if test="brandNo != null  and brandNo !='' ">
            and skbs.brandNo = #{brandNo,jdbcType=VARCHAR}
        </if>
        <if test="prodPlanId != null  and prodPlanId !='' ">
            and skbs.prodPlanId = #{prodPlanId,jdbcType=VARCHAR}
        </if>
        AND ROWNUM <![CDATA[ < ]]> #{pageEnd,jdbcType=DECIMAL} ) where rowNo >= #{pageStart,jdbcType=DECIMAL}
    </select>

    <select id="findQtyReelId" parameterType="java.util.Map"  resultMap="VmiQtyReelIdMap">
        SELECT nvl(sks.qty,qtys1.qtys) qty ,
        sks.serialnumber reelId,
        qtys1.serialkey
        FROM (
            SELECT nvl(sum(zbts.qty),0) qtys,
            skus,
            serialkey
            FROM (
                SELECT
                lxs.qty,
                lxs.sku skus,
                lxs.serialkey
                FROM
                ${whseid}.lotxlocxid lxs ,
                (SELECT
                lt.sku,
                lt.lot
                FROM enterprise.ztebarcode zb, ${whseid}.lotattribute lt
                WHERE lt.lottable02 = zb.item_barcode
                AND lt.lottable06 <![CDATA[<>]]> 'KX'
                AND lt.lottable06 <![CDATA[<>]]> 'ME'
                AND lt.lottable06 <![CDATA[<>]]> ' '
                )zbt
                WHERE lxs.status = 'OK'
                AND lxs.storerkey='ZTE'
                AND zbt.lot = lxs.lot
                AND zbt.sku = lxs.sku
            )zbts
            WHERE 1=1
            AND zbts.serialkey
            <foreach item="item" index="index" collection="serialKeyList" open="in(" separator="," close=")">
                #{item}
            </foreach>
            group by zbts.qty,zbts.skus,zbts.serialkey
        ) qtys1
        LEFT JOIN (
            SELECT
            si.serialnumber,
            si.qty,
            sk.sku
            FROM ${whseid}.serialinventory si
            INNER JOIN ${whseid}.sku sk
            ON  sk.sku = si.sku
            AND sk.storerkey='ZTE'
            AND sk.reelidprocess = 1
        ) sks ON qtys1.skus = sks.sku
    </select>

    <select id="queryBondedWarehouseInventory" resultMap="BondedWarehouseInventoryInfoMap" resultType="com.zte.domain.model.infor.BondedWarehouseInventoryInfo">
        SELECT lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id, sum(lx.qty) as qty
            FROM wmwhse25.lotxlocxid lx
                join wmwhse25.lotattribute lt
                on lt.lot = lx.lot
                and lx.sku = lt.sku
                and lt.storerkey = 'ZTE'
        where lx.status = 'OK'
            and lx.qty > 0
            and
            <foreach collection="inventoryInfoDOList" item="iterm" index="index" separator="or" open="(" close=")">
                (lt.lottable02 = #{iterm.itemBarcode} and lx.whseid = #{iterm.stockNo})
            </foreach>
        group by lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id
        union all
        SELECT lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id, sum(lx.qty) as qty
            FROM wmwhse31.lotxlocxid lx
                join wmwhse31.lotattribute lt
                on lt.lot = lx.lot
                and lx.sku = lt.sku
                and lt.storerkey = 'ZTE'
        where lx.status = 'OK'
            and lx.qty > 0
            and
            <foreach collection="inventoryInfoDOList" item="iterm" index="index" separator="or" open="(" close=")">
                (lt.lottable02 = #{iterm.itemBarcode} and lx.whseid = #{iterm.stockNo})
            </foreach>
        group by lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id
        union all
        SELECT lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id, sum(lx.qty) as qty
            FROM wmwhse40.lotxlocxid lx
            join wmwhse40.lotattribute lt
            on lt.lot = lx.lot
            and lx.sku = lt.sku
            and lt.storerkey = 'ZTE'
        where lx.status = 'OK'
            and lx.qty > 0
            and
            <foreach collection="inventoryInfoDOList" item="iterm" index="index" separator="or" open="(" close=")">
                (lt.lottable02 = #{iterm.itemBarcode} and lx.whseid = #{iterm.stockNo})
            </foreach>
        group by lx.whseid, lx.sku, lt.lottable02, lt.lottable06, lx.id
    </select>
    	
    	
   <resultMap id="locationDetailMap" type="com.zte.domain.model.infor.LocationModel">
         <result column="whseid" jdbcType="VARCHAR" property="whseid"></result>
         <result column="loc" jdbcType="VARCHAR" property="loc"></result>
         <result column="locationcategory" jdbcType="VARCHAR" property="locationcategory"></result>
   </resultMap>
    
      <select id="selectLocationDetail"  resultType="java.lang.String" resultMap="locationDetailMap" >
       SELECT WHSEID,LOCATIONCATEGORY,LOC FROM 
          ${whseid}.LOC R
      WHERE R.LOC=#{loc,jdbcType=OTHER}
  </select>

    <select id="getBarcodeControlType"  parameterType="com.zte.interfaces.infor.dto.QaExInspectionDetail" resultType="java.lang.String">
	    	select case nvl(item.reelidprocess, 0)
			         when 0 then
			          '01'
			         else
			          '02'
			       end
			  from ${stock}.sku item
			 where item.sku = #{itemNo,jdbcType=VARCHAR}
			   and item.storerkey = 'ZTE'
			   and exists (select 1
			          from ${stock}.nsqlconfig t
			         where t.configkey = 'EnablingReelid')
		 union all
			select '01' from dual
			 where not exists (select 1
			          from ${stock}.nsqlconfig t
			         where t.configkey = 'EnablingReelid')
    </select>
    




</mapper>
