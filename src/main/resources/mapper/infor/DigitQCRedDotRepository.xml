<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DigitQCRedDotRepository">

	<insert id="insertQCRedDotInfo" parameterType="com.zte.domain.model.infor.DigitQCRedDotDetailDTO">
		insert into plugin.QC_REDDOT_MANAGE
		(serialkey,SOURCE_TYPE,PRODUCT_BASE,INFOR_STOCK,QC_PROJECT,DEPARTMENT,SECTION,GROUPS,
		LIABILITY,PROBLEM_DESC,CREATED_BY,IS_WARNING,TASK_ORDERNO,STATE,ATTACHED,PROBLEM_TYPE,CREATION_DATE,LAST_UPDATE_DATE,SOLVE_MEASURE)
		select PLUGIN.SEQ_QC_REDDOT_MANAGE.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.sourceType,jdbcType=VARCHAR},#{item.productBase,jdbcType=VARCHAR},#{item.inforStock,jdbcType=VARCHAR},#{item.qcProject,jdbcType=VARCHAR},
			#{item.department,jdbcType=VARCHAR}, #{item.section,jdbcType=VARCHAR},#{item.groups,jdbcType=VARCHAR},#{item.liability,jdbcType=VARCHAR},
			#{item.problemDesc,jdbcType=VARCHAR},#{item.createdBy,jdbcType=VARCHAR},#{item.isWarning,jdbcType=VARCHAR},#{item.taskOrderNo,jdbcType=VARCHAR},
			#{item.state,jdbcType=VARCHAR},#{item.attached,jdbcType=VARCHAR},#{item.problemType,jdbcType=VARCHAR},
			to_date(#{item.creationDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),to_date(#{item.lastUpdateDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
			#{item.solveMeasure,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>
	<select id="queryQCRedDotInfo" resultType="com.zte.domain.model.infor.DigitQCRedDotDetailDTO">
		select serialKey,SOURCE_TYPE,PRODUCT_BASE,INFOR_STOCK,QC_PROJECT,DEPARTMENT,SECTION,GROUPS,
			LIABILITY,PROBLEM_DESC,STATE,CREATED_BY
		from plugin.QC_REDDOT_MANAGE
		where IS_WARNING = 'Y' and CREATION_DATE >= #{date} and enabled_flag = 'Y'
	</select>
	<update id="updateQCRedDotTaskId">
		update plugin.QC_REDDOT_MANAGE
		set state = 'ongoing', TASK_ORDERNO = #{taskId,jdbcType=VARCHAR}
		where serialKey = #{dto.serialKey,jdbcType=BIGINT} and enabled_flag = 'Y'
	</update>
	<select id="getDigitQCRedDotDetailTotal" resultType="java.lang.Integer">
		select count(1)
		from plugin.QC_REDDOT_MANAGE
		where enabled_flag = 'Y'
		<if test="productBase != null and productBase !='' ">
			and PRODUCT_BASE  = #{productBase,jdbcType=VARCHAR}
		</if>
		<if test="sourceType != null and sourceType !='' ">
			and SOURCE_TYPE  = #{sourceType,jdbcType=VARCHAR}
		</if>
		<if test="state != null and state !='' ">
			and state  = #{state,jdbcType=VARCHAR}
		</if>
		<if test="qcProject != null and qcProject !='' ">
			and QC_PROJECT  = #{qcProject,jdbcType=VARCHAR}
		</if>
		<if test="department != null and department !='' ">
			and DEPARTMENT  = #{department,jdbcType=VARCHAR}
		</if>
		<if test="section != null and section !='' ">
			and SECTION  = #{section,jdbcType=VARCHAR}
		</if>
		<if test="groups != null and groups !='' ">
			and GROUPS  = #{groups,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and CREATED_BY  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="liability != null and liability !='' ">
			and LIABILITY  = #{liability,jdbcType=VARCHAR}
		</if>
		<if test="createDateBegin != null and createDateBegin !='' ">
			and CREATION_DATE <![CDATA[>=]]> to_date(#{createDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="createDateEnd != null and createDateEnd !='' ">
			and CREATION_DATE <![CDATA[<=]]> to_date(#{createDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
			and COMPLETED_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
			and COMPLETED_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>
	<select id="getDigitQCRedDotDetailVo" resultType="com.zte.domain.model.infor.DigitQCRedDotDetailDTO">
		select B.* from (
		select A.*,rownum rn from(
		select t.serialKey,
		t.PRODUCT_BASE,
		t.SOURCE_TYPE sourceType,
		t.INFOR_STOCK inforStock,
		slv1.description qcProject,
		t.department,
		t.section,
		t.groups,
		t.TASK_ORDERNO taskOrderNo,
		t.liability,
		t.CREATED_BY createdBy,
		t.CREATION_DATE creationDate,
		t.PROBLEM_DESC problemDesc,
		slv2.description state,
		t.COMPLETED_DATE completedDate,
		t.LAST_UPDATE_DATE lastUpdateDate,
		t.LAST_UPDATED_BY lastUpdatedBy,
		decode(t.IS_WARNING,'Y','是','否') isWarning,
		slv3.description problemType,
		t.SOLVE_MEASURE solveMeasure
		from plugin.QC_REDDOT_MANAGE t
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000082' and slv1.lookup_meaning = t.QC_PROJECT and slv1.attribute1 = '1'
		left join plugin.sys_lookup_values slv2
		on slv2.lookup_type = '1000080' and slv2.lookup_meaning = t.state
		left join plugin.sys_lookup_values slv3
		on slv3.lookup_type = '1000082' and slv3.lookup_meaning = t.PROBLEM_TYPE and slv3.attribute1 = '2'
		where t.enabled_flag = 'Y'
		<if test="productBase != null and productBase !='' ">
			and t.PRODUCT_BASE  = #{productBase,jdbcType=VARCHAR}
		</if>
		<if test="sourceType != null and sourceType !='' ">
			and t.SOURCE_TYPE  = #{sourceType,jdbcType=VARCHAR}
		</if>
		<if test="state != null and state !='' ">
			and t.state  = #{state,jdbcType=VARCHAR}
		</if>
		<if test="qcProject != null and qcProject !='' ">
			and t.QC_PROJECT  = #{qcProject,jdbcType=VARCHAR}
		</if>
		<if test="department != null and department !='' ">
			and t.DEPARTMENT  = #{department,jdbcType=VARCHAR}
		</if>
		<if test="section != null and section !='' ">
			and t.SECTION  = #{section,jdbcType=VARCHAR}
		</if>
		<if test="groups != null and groups !='' ">
			and t.GROUPS  = #{groups,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and t.CREATED_BY  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="liability != null and liability !='' ">
			and t.LIABILITY  = #{liability,jdbcType=VARCHAR}
		</if>
		<if test="createDateBegin != null and createDateBegin !='' ">
			and t.CREATION_DATE <![CDATA[>=]]> to_date(#{createDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="createDateEnd != null and createDateEnd !='' ">
			and t.CREATION_DATE <![CDATA[<=]]> to_date(#{createDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
			and t.COMPLETED_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
			and t.COMPLETED_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		order by t.CREATION_DATE desc
		)A )B
		where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  b.rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  b.rn &lt;= 500000
		</if>
	</select>
	<update id="digitQCRedDotInfoLose">
		update plugin.QC_REDDOT_MANAGE
		set state = 'closed',
		LAST_UPDATE_DATE = sysdate,
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
		COMPLETED_DATE = sysdate
		where enabled_flag = 'Y'
		and serialkey in
		<foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
			#{item, jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="digitQCRedDotInfoCallback">
		update plugin.QC_REDDOT_MANAGE
		set state = 'completed',
		LAST_UPDATE_DATE = sysdate,
		COMPLETED_DATE = sysdate,
		SOLVE_MEASURE = #{solveMeasure, jdbcType=VARCHAR}
		where enabled_flag = 'Y'
		and TASK_ORDERNO = #{taskOrderNo, jdbcType=VARCHAR}
	</update>
	<update id="updateIDATaskId">
		update plugin.QC_REDDOT_MANAGE
		set state = 'modifyed',
		LAST_UPDATE_DATE = to_date(#{last_modified_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		where enabled_flag = 'Y'
		and TASK_ORDERNO = #{id,jdbcType=VARCHAR}
	</update>
	<select id="selectIdaInfo" resultType="java.lang.String">
		select t.TASK_ORDERNO from plugin.QC_REDDOT_MANAGE t where t.state = 'modify'
	</select>
</mapper>