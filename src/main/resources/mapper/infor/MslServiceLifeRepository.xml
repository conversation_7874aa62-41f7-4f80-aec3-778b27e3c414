<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.MslServiceLifeRepository">
    <resultMap id="BaseMap" type="com.zte.domain.model.infor.MslServiceLifeInfo">
        <result column="serialkey" jdbcType="INTEGER" property="serialkey"/>
        <result column="whseid" jdbcType="VARCHAR" property="whseid"/>
        <result column="whseName" jdbcType="VARCHAR" property="whseName"/>
        <result column="classgroup" jdbcType="VARCHAR" property="classgroup"/>
        <result column="orderkey" jdbcType="VARCHAR" property="orderkey"/>
        <result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="lottable02" jdbcType="VARCHAR" property="lottable02"/>
        <result column="wetlevel" jdbcType="VARCHAR" property="wetlevel"/>
        <result column="serialnumber" jdbcType="VARCHAR" property="serialnumber"/>
        <result column="ref20" jdbcType="VARCHAR" property="ref20"/>
        <result column="susr1" jdbcType="VARCHAR" property="susr1"/>
        <result column="c_company" jdbcType="VARCHAR" property="company"/>
        <result column="is_warning" jdbcType="VARCHAR" property="isWarning"/>
        <result column="CREATED_DATE" jdbcType="VARCHAR" property="createdDate"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="VARCHAR" property="lastUpdatedDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="delivery_date" jdbcType="VARCHAR" property="deliveryDate"/>
        <result column="data_status" jdbcType="INTEGER" property="dataStatus"/>
        <result column="pickingDate" jdbcType="VARCHAR" property="pickingDate"/>
        <result column="shippingDate" jdbcType="VARCHAR" property="shippingDate"/>
        <result column="exposureTime" jdbcType="VARCHAR" property="exposureTime"/>
        <result column="floorLife" jdbcType="VARCHAR" property="floorLife"/>
        <result column="warning" jdbcType="VARCHAR" property="warning"/>
    </resultMap>
    <insert id="insertMslServiceLife" parameterType="com.zte.domain.model.infor.MslServiceLifeInfo">
        insert into PLUGIN.MSL_SERVICE_LIFE_INFO (SERIALKEY,
        whseid,
        orderkey,
        externalorderkey2,
        sku,
        lottable02,
        wetlevel,
        serialnumber)
        SELECT PLUGIN.SEQ_MSL_SERVICE_LIFE_INFO.NEXTVAL SERIALKEY,p.whseid,
        p.orderkey,
        p.externalorderkey2,
        p.sku,
        p.lottable02,
        REPLACE(z.wetlevel, ' ', '') wetlevel,
        p.serialnumber
        FROM plugin.edi_pcbserial_out p
        inner join enterprise.ztebarcode z on p.LOTTABLE02=z.item_barcode
        where p.sku not like '1%'
        AND NOT EXISTS (SELECT 1 FROM PLUGIN.MSL_SERVICE_LIFE_INFO
            WHERE externalorderkey2 = p.externalorderkey2 AND serialnumber = p.serialnumber
        )
        <if test="executeDate == null and executeDate == ''">
            and 1=2
        </if>
        <if test="executeDate != null and executeDate != ''">
            and p.EDITDATE <![CDATA[>=]]> to_date(#{executeDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') - (5/24/60)
        </if>
        <if test="whseIdList !=null and whseIdList.size()>0">
            and p.WHSEID in
            <foreach collection="whseIdList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="wetlevelList !=null and wetlevelList.size()>0">
            and REPLACE(z.WETLEVEL, ' ', '') in
            <foreach collection="wetlevelList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </insert>
    <select id="getMslServiceLifeList" parameterType="com.zte.domain.model.infor.MslServiceLifeInfo" resultMap="BaseMap">
        select serialkey,
        whseid,
        classgroup,
        orderkey,
        externalorderkey2,
        sku,
        lottable02,
        wetlevel,
        serialnumber,
        ref20,
        susr1,
        c_company,
        is_warning,
        created_by,
        created_date,
        last_updated_by,
        last_updated_date,
        delivery_date,
        data_status from PLUGIN.MSL_SERVICE_LIFE_INFO
        where 1=1
        <if test="dataStatus != null ">
            and data_status = #{dataStatus, jdbcType=INTEGER}
        </if>
    </select>
    <select id="getMslServiceLifeByOrderList" parameterType="com.zte.interfaces.infor.vo.MslServiceLifeInfoVo" resultMap="BaseMap">
        select t.serialkey,t.serialnumber,t.externalorderkey2,vpc.classgroup,o.SUSR1,o.ref20,o.c_company,1 data_status from (
        <foreach collection="mslServiceLifeInfoList" item="item" index="index" separator="union all">
            select
            #{item.serialkey,jdbcType=INTEGER} AS serialkey,#{item.serialnumber,jdbcType=VARCHAR} AS serialnumber,
            #{item.externalorderkey2,jdbcType=VARCHAR} externalorderkey2,#{item.orderkey,jdbcType=VARCHAR} orderkey,
            #{item.sku,jdbcType=VARCHAR} sku
            from dual
        </foreach>
        ) t
        inner join ${whseid}.Orders o on t.orderKey = o.orderKey
        inner join ${whseid}.SKU sk ON t.sku = sk.sku AND sk.STORERKEY = 'ZTE'
        left join ${whseid}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
    </select>
    <update id="updateLookupValues" parameterType="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
        update plugin.sys_lookup_values set lookup_meaning = #{lookupMeaning, jdbcType=VARCHAR}
        where LOOKUP_CODE = #{lookupCode, jdbcType=VARCHAR}
    </update>
    <update id="batchUpdateMslServiceLife" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator =";" >
            update plugin.MSL_SERVICE_LIFE_INFO
            set data_status = #{item.dataStatus,jdbcType=INTEGER},
            <if test="item.classgroup != null and item.classgroup != ''">
                classgroup = #{item.classgroup,jdbcType=VARCHAR},
            </if>
            <if test="item.susr1 != null and item.susr1 != ''">
                SUSR1 = #{item.susr1,jdbcType=VARCHAR},
            </if>
            <if test="item.ref20 != null and item.ref20 != ''">
                ref20 = #{item.ref20,jdbcType=VARCHAR},
            </if>
            <if test="item.company != null and item.company != ''">
                c_company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryDate != null and item.deliveryDate != ''">
                delivery_date = to_date(#{item.deliveryDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
            </if>
            LAST_UPDATED_DATE = sysdate
            where serialkey = #{item.serialkey, jdbcType=INTEGER}
        </foreach>
    </update>
    <select id="getMslServiceLifeAndOrder" parameterType="com.zte.domain.model.infor.MslServiceLifeInfo" resultMap="BaseMap">
        select t2.* from (select t1.whseid,
        t1.whseName,
        t1.classgroup,
        t1.orderkey,
        t1.externalorderkey2,
        t1.sku,
        t1.lottable02,
        t1.wetlevel,
        t1.serialnumber,
        t1.ref20,
        t1.susr1,
        t1.c_company,
        t1.is_warning,
        t1.warning,
        t1.created_by,
        t1.created_date,
        t1.last_updated_by,
        t1.last_updated_date,
        t1.delivery_date,
        t1.pickingDate,
        t1.floorLife,
        case when t1.pickingDate > t1.shippingDate then t1.pickingDate else t1.shippingDate end shippingDate,
        t1.data_status,
        CASE WHEN t1.attribute4!=t1.ref20 THEN
        (case when t1.pickingDate > t1.shippingDate then '0.0000' else TO_CHAR((t1.shippingDate-t1.pickingDate)*24,'FM999999990.0000') end)
        ELSE (CASE WHEN t1.c_company = 'SMT配送' THEN (case when t1.delivery_date is null THEN '' else TO_CHAR((t1.delivery_date-t1.pickingDate)*24,'FM999999990.0000') end) ELSE TO_CHAR(t1.attribute3,'FM999999990.0000') END)
        END exposureTime,
        rownum rn
        from (select t.serialkey,
        t.whseid,
        s.description whseName,
        t.classgroup,
        t.orderkey,
        t.externalorderkey2,
        t.sku,
        t.lottable02,
        t.wetlevel,
        t.serialnumber,
        t.ref20,
        t.susr1,
        t.c_company,
        t.is_warning,
        case when t.is_warning=1 then '是' else '否' end warning,
        t.created_by,
        t.created_date,
        t.last_updated_by,
        t.last_updated_date,
        t.delivery_date,
        s.attribute4,
        sl.attribute3,
        (SELECT MAX(ADDDATE)+8/24 FROM ${whseid}.itrnserial
          WHERE SERIALNUMBER = t.serialnumber and DATA5 = 'TMPKP' GROUP BY SERIALNUMBER
        ) pickingDate,
        sl.attribute1 floorLife,
        (SELECT MAX(ADDDATE)+8/24 FROM ${whseid}.itrnserial
          WHERE SERIALNUMBER = t.serialnumber and TRANTYPE = 'WD' GROUP BY SERIALNUMBER
        ) shippingDate,
        t.data_status
        from PLUGIN.MSL_SERVICE_LIFE_INFO t
        left join plugin.sys_lookup_values s on s.lookup_type = '1000071' and s.enabled_flag = 'Y' and t.whseid = s.lookup_meaning
        left join plugin.sys_lookup_values sl on sl.lookup_type = '1000078' and sl.enabled_flag = 'Y' and t.wetlevel = sl.lookup_meaning
        where t.whseid = #{whseid,jdbcType=VARCHAR}
        <if test="classgroup != null and classgroup != ''">
            and t.classgroup = #{classgroup,jdbcType=VARCHAR}
        </if>
        <if test="sku != null and sku != ''">
            and t.sku = #{sku,jdbcType=VARCHAR}
        </if>
        <if test="lottable02 != null and lottable02 != ''">
            and t.lottable02 = #{lottable02,jdbcType=VARCHAR}
        </if>
        <if test="wetlevel != null and wetlevel != ''">
            and t.wetlevel = #{wetlevel,jdbcType=VARCHAR}
        </if>
        <if test="serialnumber != null and serialnumber != ''">
            and t.serialnumber = #{serialnumber,jdbcType=VARCHAR}
        </if>
        <if test="ref20 != null and ref20 != ''">
            and t.ref20 like CONCAT(CONCAT('%',#{ref20,jdbcType=VARCHAR}),'%')
        </if>
        <if test="susr1 != null and susr1 != ''">
            and t.susr1 = #{susr1,jdbcType=VARCHAR}
        </if>
        <if test="isWarning != null ">
            and t.is_warning = #{isWarning,jdbcType=INTEGER}
        </if>
        <if test="createdDateBegin != null and createdDateBegin !='' ">
            and t.created_date <![CDATA[>=]]> to_date(#{createdDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="createdDateEnd != null and createdDateEnd !='' ">
            and t.created_date <![CDATA[<=]]> to_date(#{createdDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) t1 where 1=1
        <if test="pickingDateBegin != null and pickingDateBegin !='' ">
            and t1.pickingDate <![CDATA[>=]]> to_date(#{pickingDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="pickingDateEnd != null and pickingDateEnd !='' ">
            and t1.pickingDate <![CDATA[<=]]> to_date(#{pickingDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) t2 where 1=1
        <if test="startRow != null and endRow != null ">
            and  t2.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or endRow == null ">
            and  t2.rn &lt;= 1000000
        </if>
    </select>
    <select id="getMslServiceLifeAndOrderCount" parameterType="com.zte.domain.model.infor.MslServiceLifeInfo" resultType="java.lang.Integer">
        select count(1)
        from (select
        (SELECT MAX(ADDDATE)+8/24 FROM ${whseid}.itrnserial
        WHERE SERIALNUMBER = t.serialnumber and DATA5 = 'TMPKP' GROUP BY SERIALNUMBER
        ) pickingDate
        from PLUGIN.MSL_SERVICE_LIFE_INFO t
        where t.whseid = #{whseid,jdbcType=VARCHAR}
        <if test="classgroup != null and classgroup != ''">
            and t.classgroup = #{classgroup,jdbcType=VARCHAR}
        </if>
        <if test="sku != null and sku != ''">
            and t.sku = #{sku,jdbcType=VARCHAR}
        </if>
        <if test="lottable02 != null and lottable02 != ''">
            and t.lottable02 = #{lottable02,jdbcType=VARCHAR}
        </if>
        <if test="wetlevel != null and wetlevel != ''">
            and t.wetlevel = #{wetlevel,jdbcType=VARCHAR}
        </if>
        <if test="serialnumber != null and serialnumber != ''">
            and t.serialnumber = #{serialnumber,jdbcType=VARCHAR}
        </if>
        <if test="ref20 != null and ref20 != ''">
            and t.ref20 like CONCAT(CONCAT('%',#{ref20,jdbcType=VARCHAR}),'%')
        </if>
        <if test="susr1 != null and susr1 != ''">
            and t.susr1 = #{susr1,jdbcType=VARCHAR}
        </if>
        <if test="isWarning != null ">
            and t.is_warning = #{isWarning,jdbcType=INTEGER}
        </if>
        <if test="createdDateBegin != null and createdDateBegin !='' ">
            and t.created_date <![CDATA[>=]]> to_date(#{createdDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="createdDateEnd != null and createdDateEnd !='' ">
            and t.created_date <![CDATA[<=]]> to_date(#{createdDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) t1 where 1=1
        <if test="pickingDateBegin != null and pickingDateBegin !='' ">
            and t1.pickingDate <![CDATA[>=]]> to_date(#{pickingDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="pickingDateEnd != null and pickingDateEnd !='' ">
            and t1.pickingDate <![CDATA[<=]]> to_date(#{pickingDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
    <select id="selectWhseId" resultType="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
        select s.lookup_meaning lookupMeaning,
        s.description,
        s.attribute3,
        PB.DB_TYPE dbType
        from plugin.sys_lookup_values s
        inner join WMSADMIN.PL_DB PB on s.lookup_meaning = UPPER(PB.DB_LOGID) and PB.ISACTIVE = 1 AND PB.DB_ENTERPRISE = 0
        where s.enabled_flag = 'Y' and s.lookup_type='1000071' and s.attribute5=1
    </select>
    <select id="getMslServiceLifeRedDot" parameterType="com.zte.domain.model.infor.MslServiceLifeInfo" resultMap="BaseMap">
        select t1.serialkey,
        t1.whseid,
        t1.classgroup,
        t1.sku,
        t1.lottable02,
        t1.serialnumber
        from (select t.serialkey,
        t.whseid,
        t.classgroup,
        t.sku,
        t.lottable02,
        t.serialnumber,
        (SELECT MAX(ADDDATE)+8/24 FROM ${whseid}.itrnserial
        WHERE SERIALNUMBER = t.serialnumber and DATA5 = 'TMPKP' GROUP BY SERIALNUMBER
        ) pickingDate,
        (SELECT MAX(ADDDATE)+8/24 FROM ${whseid}.itrnserial
        WHERE SERIALNUMBER = t.serialnumber and TRANTYPE = 'WD' GROUP BY SERIALNUMBER
        ) shippingDate,
        s.attribute4,
        sl.attribute3,
        t.c_company,
        t.ref20,
        t.delivery_date,
        sl.attribute2
        from PLUGIN.MSL_SERVICE_LIFE_INFO t
        INNER join plugin.sys_lookup_values s on s.lookup_type = '1000071' and s.enabled_flag = 'Y' and t.whseid = s.lookup_meaning
        INNER join plugin.sys_lookup_values sl on sl.lookup_type = '1000078' and sl.enabled_flag = 'Y' and t.wetlevel = sl.lookup_meaning
        where t.whseid = #{whseid,jdbcType=VARCHAR}
        and (t.data_status <![CDATA[>]]> 0 or (t.c_company = 'SMT配送' and (s.attribute4=t.ref20 or t.ref20 is null)))
        <if test="isWarning != null ">
            and t.is_warning = #{isWarning,jdbcType=INTEGER}
        </if>
        ) t1 where TRUNC(CASE WHEN t1.attribute4!=t1.ref20 THEN (case when t1.pickingDate > t1.shippingDate then 0.0 else (t1.shippingDate-t1.pickingDate)*24 end)
        ELSE (CASE WHEN t1.c_company = 'SMT配送' THEN (t1.delivery_date-t1.pickingDate)*24 ELSE TO_NUMBER(t1.attribute3) END)
        END,4)>=TO_NUMBER(t1.attribute2)
    </select>
    <update id="updateMslServiceLifeIsWarning" parameterType="java.lang.Integer">
        update plugin.MSL_SERVICE_LIFE_INFO
        set is_warning = 1,
        LAST_UPDATED_DATE = sysdate
        where serialkey in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>