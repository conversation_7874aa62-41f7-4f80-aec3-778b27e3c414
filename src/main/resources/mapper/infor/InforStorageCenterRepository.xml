<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforStorageCenterRepository">
	<resultMap id="HZBillHeadMap" type="com.zte.domain.model.infor.HZBillHead">
		<result column="billInfoType" jdbcType="VARCHAR" property="billInfoType" />
        <result column="billTypeCode" jdbcType="VARCHAR" property="billTypeCode" />
        <result column="actionCode" jdbcType="DECIMAL" property="actionCode" />
        <result column="statusCode" jdbcType="VARCHAR" property="statusCode" />
        <result column="srcBillNo" jdbcType="VARCHAR" property="srcBillNo" />
        <result column="applyBy" jdbcType="VARCHAR" property="applyBy" />
        <result column="countryCode" jdbcType="VARCHAR" property="countryCode" />
        <result column="submitedBy" jdbcType="VARCHAR" property="submitedBy" />
        <result column="submitedDate" jdbcType="VARCHAR" property="submitedDate" />
        <result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
        <result column="createdDate" jdbcType="VARCHAR" property="createdDate" />
        <result column="accessSystem" jdbcType="DECIMAL" property="accessSystem" />
        <result column="sourceMode" jdbcType="VARCHAR" property="sourceMode" />
        <result column="relatedBillNo" jdbcType="VARCHAR" property="relatedBillNo" />
        <result column="toWarehouseId" jdbcType="VARCHAR" property="toWarehouseId" />
        <result column="wmwhseId" jdbcType="VARCHAR" property="wmwhseId" />
	</resultMap>
	<resultMap id="HZBillDetailMap" type="com.zte.domain.model.infor.HZBillDetail">
		<result column="stockTypeCode" jdbcType="VARCHAR" property="stockTypeCode" />
        <result column="stockType" jdbcType="VARCHAR" property="stockType" />
        <result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
        <result column="lineIndex" jdbcType="DECIMAL" property="lineIndex" />
        <result column="reqQty" jdbcType="DECIMAL" property="reqQty" />
        <result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
        <result column="reelid" jdbcType="VARCHAR" property="reelid" />
        <result column="createdDate" jdbcType="VARCHAR" property="createdDate" />
        <result column="accessSystem" jdbcType="DECIMAL" property="accessSystem" />
        <result column="srcBillNo" jdbcType="VARCHAR" property="srcBillNo" />
        <result column="relatedBillNo" jdbcType="VARCHAR" property="relatedBillNo" />
        <result column="boxNo" jdbcType="VARCHAR" property="boxNo" />
        <result column="itemBarcode" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="type" jdbcType="VARCHAR" property="type" />
	</resultMap>
	<resultMap id="EdiCacheListMap" type="com.zte.domain.model.infor.EdiCacheList">
		<result column="transactionReference" jdbcType="VARCHAR" property="transactionReference" />
        <result column="organizationId" jdbcType="VARCHAR" property="organizationId" />
        <result column="subinventoryCode" jdbcType="VARCHAR" property="subinventoryCode" />
        <result column="transactionDate" jdbcType="VARCHAR" property="transactionDate" />
        <result column="transactionQuantity" jdbcType="DECIMAL" property="transactionQuantity" />
        <result column="renewPriceFlag" jdbcType="VARCHAR" property="renewPriceFlag" />
        <result column="reelid" jdbcType="VARCHAR" property="reelid" />
        <result column="processStatus" jdbcType="VARCHAR" property="processStatus" />
        <result column="lastUpdateDate" jdbcType="VARCHAR" property="lastUpdateDate" />
        <result column="creationDate" jdbcType="VARCHAR" property="creationDate" />
        <result column="contractEntityFlag" jdbcType="VARCHAR" property="contractEntityFlag" />
        <result column="relatedBillNo" jdbcType="VARCHAR" property="relatedBillNo" />
        <result column="srcBillNo" jdbcType="VARCHAR" property="srcBillNo" />
        <result column="lineIndex" jdbcType="DECIMAL" property="lineIndex" />
	</resultMap>
	<resultMap id="EdiSoSInfoMap" type="com.zte.interfaces.infor.dto.EdiSoSInfoDTO">
		<result column="orderkey" jdbcType="VARCHAR" property="orderkey" />
		<result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
		<result column="sku" jdbcType="VARCHAR" property="sku" />
		<result column="originalqty" jdbcType="DECIMAL" property="originalqty" />
		<result column="qtyshipedtotal" jdbcType="DECIMAL" property="qtyshipedtotal" />
		<result column="shippedqty" jdbcType="DECIMAL" property="shippedqty" />
		<result column="ref01" jdbcType="VARCHAR" property="ref01" />
		<result column="ref02" jdbcType="VARCHAR" property="ref02" />
		<result column="ref03" jdbcType="VARCHAR" property="ref03" />
		<result column="externorderkey" jdbcType="VARCHAR" property="externorderkey" />
		<result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2" />
		<result column="hsusr1" jdbcType="VARCHAR" property="hsusr1" />
		<result column="hsusr2" jdbcType="VARCHAR" property="hsusr2" />
		<result column="hsusr3" jdbcType="VARCHAR" property="hsusr3" />
		<result column="href04" jdbcType="VARCHAR" property="href04" />
		<result column="fromId" jdbcType="VARCHAR" property="fromId" />
	</resultMap>
	<resultMap id="ReqHeadReqInstanceDTOMap" type="com.zte.interfaces.infor.dto.ReqHeadReqInstanceDTO">
		<result column="billInfoType" jdbcType="VARCHAR" property="billInfoType" />
		<result column="billTypeCode" jdbcType="VARCHAR" property="billTypeCode" />
		<result column="actionCode" jdbcType="INTEGER" property="actionCode" />
		<result column="srcBillNo" jdbcType="VARCHAR" property="srcBillNo" />
		<result column="relatedBillNo" jdbcType="VARCHAR" property="relatedBillNo" />
		<result column="organization" jdbcType="VARCHAR" property="organization" />
		<result column="toWarehouseId" jdbcType="VARCHAR" property="toWarehouseId" />
		<result column="applyBy" jdbcType="VARCHAR" property="applyBy" />
		<result column="countryCode" jdbcType="VARCHAR" property="countryCode" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="submitedBy" jdbcType="VARCHAR" property="submitedBy" />
		<result column="submitedDate" jdbcType="DATE" property="submitedDate" />
		<result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
		<result column="createdDate" jdbcType="VARCHAR" property="createdDate" />
		<result column="accessSystem" jdbcType="INTEGER" property="accessSystem" />
		<result column="sourceMode" jdbcType="VARCHAR" property="sourceMode" />
		<result column="wmwhseId" jdbcType="VARCHAR" property="wmwhseId" />
		<result column="orderkey" jdbcType="VARCHAR" property="orderkey" />
		<result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2" />
	</resultMap>
	<resultMap id="ReqDetailReqInstanceDTOMap" type="com.zte.interfaces.infor.dto.ReqDetailReqInstanceDTO">
		<result column="stockType" jdbcType="VARCHAR" property="stockType" />
		<result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
		<result column="itemBarcode" jdbcType="VARCHAR" property="itemBarcode" />
		<result column="leadProperties" jdbcType="VARCHAR" property="leadProperties" />
		<result column="reqQty" jdbcType="DECIMAL" property="reqQty" />
		<result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
		<result column="createdDate" jdbcType="DATE" property="createdDate" />
		<result column="accessSystem" jdbcType="INTEGER" property="accessSystem" />
		<result column="itemDestination" jdbcType="VARCHAR" property="itemDestination" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="brand" jdbcType="VARCHAR" property="brand" />
		<result column="orderkey" jdbcType="VARCHAR" property="orderkey" />
		<result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2" />
	</resultMap>
	<select id="getAllHZBill" parameterType="com.zte.domain.model.infor.HZBillHead" resultMap="HZBillHeadMap">
		select distinct ess.orderkey srcBillNo,ess.externalorderkey2 relatedBillNo,ess.whseid wmwhseId
		  from plugin.edi_so_s ess
		 where ess.externalorderkey2 like 'HZ%'
		   and ess.href08 in ('35CP', '61_印度')
		   and ess.href11 in ('650', '653')
		   and <![CDATA[ess.edi_adddate > (select slt.creation_date from plugin.sys_lookup_types slt where slt.lookup_type = '1000013')]]>
		   <if test = "srcBillNo != null and srcBillNo !='' " >
		   		and ess.orderkey = #{srcBillNo,jdbcType=VARCHAR}
		   </if> 
		   <if test = "relatedBillNo != null and relatedBillNo != '' " >
	   			and ess.externalorderkey2 = #{relatedBillNo,jdbcType=VARCHAR}
		   </if>
		   and <![CDATA[ess.shippedqty > 0]]>
	       and not exists (select 1
          from plugin.storagecenter_edi_log sel
         where sel.orderkey = ess.orderkey
           and sel.externalorderkey2 = ess.externalorderkey2)
	</select>
	<select id="checkIsSo" resultType="java.lang.Integer">
		select count(1)
			from (select o.externalorderkey2,o.orderkey,
						sum(od.shippedqty) pickqty,
						(select sum(ess.shippedqty)
						from plugin.edi_so_s ess
						where ess.externalorderkey2 = o.externalorderkey2
						and ess.orderkey = o.orderkey) shippedqty
					from ${wmwhseId}.orderdetail od
					join ${wmwhseId}.orders o
					on od.orderkey = o.orderkey
					and o.orderkey = #{srcBillNo}
					and o.status = '95'
					and od.status = '95'
			group by o.externalorderkey2,o.orderkey) a
		where a.pickqty = a.shippedqty
	</select>
	<insert id="insertByBillTypeHZ" parameterType="com.zte.domain.model.infor.HZBillHead">
		insert into plugin.storagecenter_edi_log
		  (externalorderkey2, orderkey, issend, sendtimes, adddate, type)
		  select a.externalorderkey2,
		         a.orderkey,
		         a.issend,
		         a.sendtimes,
		         a.adddate,
				 '01'
		    from (select distinct
		                 ess.externalorderkey2,
		                 ess.orderkey,
		                 -1 issend,
		                 0 sendtimes,
		                 sysdate adddate
		            from plugin.edi_so_s ess
		           where ess.externalorderkey2 like 'HZ%'
		             and ess.href08 in ('35CP', '61_印度')
		             and ess.href11 in ('650', '653')
		             and <![CDATA[ess.edi_adddate > (select slt.creation_date from plugin.sys_lookup_types slt where slt.lookup_type = '1000013')]]>
		      	<if test = "srcBillNo != null and srcBillNo !='' " >
		   			 and ess.orderkey = #{srcBillNo,jdbcType=VARCHAR}
				</if> 
				<if test = "relatedBillNo != null and relatedBillNo != '' " >
		   			 and ess.externalorderkey2 = #{relatedBillNo,jdbcType=VARCHAR}
				</if>) a
		   where not exists (select 1
            from plugin.storagecenter_edi_log sel
           where sel.orderkey = a.orderkey
             and sel.externalorderkey2 = a.externalorderkey2) 
	</insert>
	
	<select id="getUnPushedData" parameterType="com.zte.domain.model.infor.HZBillHead" resultMap="HZBillHeadMap">
		select distinct sel.orderkey srcBillNo,sel.externalorderkey2 relatedBillNo
		  from plugin.storagecenter_edi_log sel
		 where sel.isSend in (-1, 2) and <![CDATA[sel.sendTimes < 4]]>
		   and sel.type = '01'
		<if test = "srcBillNo != null and srcBillNo !='' " >
		   	 and sel.orderkey = #{srcBillNo,jdbcType=VARCHAR}
		 </if> 
		 <if test = "relatedBillNo != null and relatedBillNo != '' " >
   			 and sel.externalorderkey2 = #{relatedBillNo,jdbcType=VARCHAR}
		 </if>
	</select>
	
	<select id="getHZBillHead" parameterType="java.util.List" resultMap="HZBillHeadMap">
		select distinct 'LXRKST' billInfoType,
		       'TSRK_YC' billTypeCode,
		       0 actionCode,
		       '0020' statusCode,
		       ess.orderkey srcBillNo,
		       ess.editwho applyBy,
		       'CN' countryCode,
		       ess.editwho submitedBy,
		       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') submitedDate,
		       ess.editwho createdBy,
		       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') createdDate,
		       104 accessSystem,
		       'Infor' sourceMode,
		       ess.externalorderkey2 relatedBillNo,
		       case to_char(ess.href08) when '35CP' then '1317029475103760386' when '61_印度' then '1317029317339209730' end toWarehouseId
		  from plugin.edi_so_s ess
		 where 1=1 and
		<foreach collection="list" item="hzBillHead" open="" separator="or" close="">
			(ess.orderkey = #{hzBillHead.srcBillNo,jdbcType=VARCHAR} and ess.externalorderkey2 = #{hzBillHead.relatedBillNo,jdbcType=VARCHAR})
		</foreach>
	</select>
	
	<select id="getHZBillDetail" parameterType="java.util.List" resultMap="HZBillDetailMap">
       <foreach collection="list" item="item" open="" separator="union" close="">
			select epo.orderkey srcBillNo,
			       epo.externalorderkey2 relatedBillNo,
			       'box' stockTypeCode,
			       '1' stockType,
			       epo.sku itemNo,
			       epo.externlineno lineIndex,
			       epo.qty reqQty,
			       epo.lottable02 itemBarcode,
			       epo.toid boxNo,
			       to_char(epo.serialnumber) reelid,
			       104 accessSystem,
			       to_char(epo.editdate, 'yyyy-MM-dd HH24:mi:ss') createdDate,
			       'admin' createdBy,
			       '01' type
			  from plugin.edi_pcbserial_out epo
			 where epo.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			   and epo.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			   and <![CDATA[epo.qty > 0]]>
			   and exists (
			   	   select 1 from plugin.edi_so_s ess 
			   	    where ess.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			   	      and ess.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			   	      and ess.ref45 = '10'
			   )
			union
			select ess.orderkey srcBillNo,
			       ess.externalorderkey2 relatedBillNo,
			       'box' stockTypeCode,
			       '1' stockType,
			       ess.sku itemNo,
			       ess.orderlinenumber lineIndex,
			       ess.shippedqty reqQty,
			       ess.lottable02 itemBarcode,
			       ess.id boxNo,
			       '' reelid,
			       104 accessSystem,
			       to_char(ess.editdate, 'yyyy-MM-dd HH24:mi:ss') createdDate,
			       'admin' createdBy,
			       '02' type
			  from plugin.edi_so_s ess
			 where ess.externalorderkey2 = #{item.relatedBillNo,jdbcType=VARCHAR}
			   and ess.orderkey = #{item.srcBillNo,jdbcType=VARCHAR}
			   and ess.ref45 = '10'
			   and <![CDATA[ess.shippedqty > 0]]>
			   and not exists (select 1
			          from plugin.edi_pcbserial_out epo
			         where epo.externalorderkey2 = #{item.relatedBillNo,jdbcType=VARCHAR}
			           and epo.orderkey = #{item.srcBillNo,jdbcType=VARCHAR}
			           and epo.sku = ess.sku)
			union
			select epo.orderkey srcBillNo,
			       epo.externalorderkey2 relatedBillNo,
			       'box' stockTypeCode,
			       '1' stockType,
			       epo.sku itemNo,
			       epo.externlineno lineIndex,
			       epo.qty reqQty,
			       epo.serialnumber itemBarcode,
			       epo.toid boxNo,
			       to_char(epo.serialnumber) reelid,
			       104 accessSystem,
			       to_char(epo.editdate, 'yyyy-MM-dd HH24:mi:ss') createdDate,
			       'admin' createdBy,
			       '03' type
			  from plugin.edi_pcbserial_out epo
			 where epo.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			   and epo.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			   and <![CDATA[epo.qty > 0]]>
			   and exists (
			   	   select 1 from plugin.edi_so_s ess 
			   	    where ess.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			   	      and ess.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			   	      and ess.ref45 = '20'
			   )
		</foreach>
	</select>
	<select id="getEdiCacheList" parameterType="java.util.List" resultMap="EdiCacheListMap">
	    <foreach collection="list" item="item" open="" separator="union" close="">
			select epo.orderkey transactionReference,
			       b.href07 organizationId,
			       b.href08 subinventoryCode,
			       epo.externlineno lineIndex,
			       epo.qty transactionQuantity,
			       to_char(epo.serialnumber) reelid,
			       'N' renewPriceFlag,
			       'N' processStatus,
			       'N' contractEntityFlag,
			       epo.externalorderkey2 relatedBillNo,
			       epo.orderkey srcBillNo,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') transactionDate,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') lastUpdateDate,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') creationDate
			  from plugin.edi_pcbserial_out epo
			  join (select ess.href07, ess.href08, ess.externalorderkey2, ess.orderkey
			          from plugin.edi_so_s ess
			         where ess.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			           and ess.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			           and rownum = 1) b
			    on epo.externalorderkey2 = b.externalorderkey2
			   and epo.orderkey = b.orderkey
			 where epo.externalorderkey2 = #{item.relatedBillNo,jdbcType = VARCHAR}
			   and epo.orderkey = #{item.srcBillNo, jdbcType = VARCHAR}
			   and <![CDATA[epo.qty > 0]]>
			union
			select ess.orderkey transactionReference,
			       ess.href07 organizationId,
			       ess.href08 subinventoryCode,
			       ess.orderlinenumber lineIndex,
			       ess.shippedqty transactionQuantity,
			       '' reelid,
			       'N' renewPriceFlag,
			       'N' processStatus,
			       'N' contractEntityFlag,
			       ess.externalorderkey2 relatedBillNo,
			       ess.orderkey srcBillNo,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') transactionDate,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') lastUpdateDate,
			       to_char(sysdate, 'yyyy-MM-dd HH24:mi:ss') creationDate
			  from plugin.edi_so_s ess
			 where ess.externalorderkey2 = #{item.relatedBillNo,jdbcType=VARCHAR}
			   and ess.orderkey = #{item.srcBillNo,jdbcType=VARCHAR}
			   and <![CDATA[ess.shippedqty > 0]]>
			   and not exists (select 1
			          from plugin.edi_pcbserial_out epo
			         where epo.externalorderkey2 = #{item.relatedBillNo,jdbcType=VARCHAR}
			           and epo.orderkey = #{item.srcBillNo,jdbcType=VARCHAR}
			           and epo.sku = ess.sku)
		</foreach>
	</select>
	<update id="updateStorageCenterEdiLog" parameterType="com.zte.domain.model.infor.StorageCenterEdiLog">
		update plugin.storagecenter_edi_log sel
		   set sel.issend        = #{isSend,jdbcType=DECIMAL},
		       sel.requestparam  = #{requestParam,jdbcType=VARCHAR},
		       sel.responseparam = #{responseParam,jdbcType=VARCHAR},
		       sel.servicecode   = #{serviceCode,jdbcType=VARCHAR},
		       sel.sendtimes     = sel.sendtimes + 1,
		       sel.updatedate    = sysdate
		 where sel.orderkey = #{orderKey,jdbcType=VARCHAR}
		   and sel.externalorderkey2 = #{externalOrderkey2,jdbcType=VARCHAR}
	</update>
	
	
	<resultMap id="HeaderEdiSoSListMap" type="com.zte.domain.model.infor.HeaderEdiSoS">
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
        <result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2" />
        <result column="orderkey" jdbcType="VARCHAR" property="orderkey" />
        <result column="orderlinenumber" jdbcType="VARCHAR" property="orderlinenumber" />
        <result column="storerkey" jdbcType="VARCHAR" property="storerkey" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="lot" jdbcType="VARCHAR" property="lot" />
        <result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
        <result column="dropid" jdbcType="VARCHAR" property="dropid" />
        <result column="pickdetailkey" jdbcType="VARCHAR" property="pickdetailkey" />
		<result column="prodplanId" jdbcType="VARCHAR" property="prodplanId" />
	</resultMap>
	
	<select id="selectEdiSoS" parameterType="com.zte.interfaces.infor.dto.SoOutBoundDTO" resultMap="HeaderEdiSoSListMap">
		select distinct s.whseid ,s.externalorderkey2,s.orderkey,s.orderlinenumber,s.storerkey,s.sku,s.lot,s.lottable02,
			s.id as dropid,s.REF60 pickdetailkey,s.hsusr1 as prodplanId
		  	from plugin.edi_so_s  s
		<if test = "reelId != null and reelId != '' " >
			left join PLUGIN.EDI_PCBSERIAL_OUT EO
			on s.externalorderkey2 = EO.externalorderkey2
			and s.externlineno = EO.externlineno
		</if>
		WHERE 1=1
		<if test = "reelId != null and reelId != '' " >
			 and EO.SERIALNUMBER=#{reelId,jdbcType=VARCHAR}
		</if>
		<if test = "billNo != null and billNo !='' " >
		   	 and s.externalorderkey2 = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test = "itemNo != null and itemNo != '' " >
   			 and s.sku = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test = "prodplanId != null and prodplanId != '' " >
			and s.hsusr1 = #{prodplanId,jdbcType=VARCHAR}
		</if>
		<if test = "id != null and id != '' " >
			and s.id = #{id,jdbcType=VARCHAR}
		</if>
		<if test = "beginDate != null and beginDate != '' " >
   			 and s.EDI_EDITDATE >= TO_DATE(#{beginDate},'YYYY-MM-DD HH24:MI:SS')
		</if>
		<if test = "endDate != null and endDate != '' " >
   			 and s.EDI_EDITDATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
		</if>
	</select>
	
		
	<resultMap id="EdiSoSReelidDetailListMap" type="com.zte.domain.model.infor.EdiSosReelidDetail">
		<result column="SERIALKEY" jdbcType="VARCHAR" property="serialkey" />
        <result column="BILLNO" jdbcType="VARCHAR" property="billNo" />
        <result column="ITEMNO" jdbcType="VARCHAR" property="itemNo" />
        <result column="ITEMBARCODE" jdbcType="VARCHAR" property="itembarcode" />
        <result column="DROPID" jdbcType="VARCHAR" property="dropId" />
        <result column="BOXNO" jdbcType="VARCHAR" property="boxNo" />
        <result column="PRODPLANID" jdbcType="VARCHAR" property="prodplanId" />
        <result column="SENDTYPE" jdbcType="VARCHAR" property="sendType" />
        <result column="LEADFLAG" jdbcType="VARCHAR" property="leadFlag" />
        <result column="brandNo" jdbcType="VARCHAR" property="brandNo" />
        <result column="supplyNo" jdbcType="VARCHAR" property="supplyNo" />
        <result column="wetLevel" jdbcType="VARCHAR" property="wetLevel" />
        <result column="REELID" jdbcType="VARCHAR" property="reelId" />
        <result column="QTY" jdbcType="VARCHAR" property="qty" />
        <result column="ADDDATE" jdbcType="VARCHAR" property="addDate" />
        <result column="EDITDATE" jdbcType="VARCHAR" property="editDate" /> 
        <result column="WMWHSEID" jdbcType="VARCHAR" property="wmwhseid" />
	</resultMap>
	
	<select id="selectEdiSoSDetail" parameterType="com.zte.domain.model.infor.HeaderEdiSoS" resultMap="EdiSoSReelidDetailListMap">
		<if test = "reelidProcess == 1 " >
			SELECT  'SR'||EO.WHSEID||EO.SERIALKEY  AS SERIALKEY,
			EO.EXTERNALORDERKEY2 AS BILLNO,
			EO.SKU AS ITEMNO,
			EO.LOTTABLE02  AS ITEMBARCODE,
			EO.TOID AS DROPID,
			O.SUSR1 AS PRODPLANID,
			O.C_COMPANY AS SENDTYPE,
			ZB.IS_LEAD AS LEADFLAG,
			ZB.PRODUCT_NO AS brandNo,
			ZB.SUPPLIER_NO AS supplyNo,
			ZB.WETLEVEL AS wetLevel,
			EO.SERIALNUMBER AS REELID,
			EO.QTY,
			TO_CHAR(EO.ADDDATE,'YYYY-MM-DD HH24:MI:SS') AS  ADDDATE,
			TO_CHAR(EO.EDITDATE,'YYYY-MM-DD HH24:MI:SS')  AS EDITDATE,
			(SELECT  MAX(FROMID) FROM  ${whseid}.ITRN I WHERE   1=1
			<if test = "pickdetailkey != null and pickdetailkey != '' " >
				and  I.SOURCEKEY=#{pickdetailkey,jdbcType=VARCHAR}
			</if>
			AND i.toid=EO.TOID AND i.STORERKEY=EO.STORERKEY AND i.SKU=EO.SKU and i.trantype = 'MV' and i.sourcetype='PICKING' AND EO.LOTTABLE02=I.lottable02)
			AS boxNo,
			O.WHSEID WMWHSEID
			FROM   PLUGIN.EDI_PCBSERIAL_OUT   EO
		    INNER JOIN  ${whseid}.ORDERS O
		    ON EO.ORDERKEY=O.ORDERKEY
		    INNER JOIN ENTERPRISE.ZTEBARCODE ZB
		    ON ZB.ITEM_BARCODE=EO.LOTTABLE02
			WHERE  EO.EXTERNALORDERKEY2=#{externalorderkey2,jdbcType=VARCHAR}
		    AND  EO.TOID=#{dropid,jdbcType=VARCHAR}
		    AND  EO.STORERKEY=#{storerkey,jdbcType=VARCHAR}
		    AND  EO.SKU=#{sku,jdbcType=VARCHAR}
		    AND  EO.LOTTABLE02=#{lottable02,jdbcType=VARCHAR}
		    AND  EO.orderkey=#{orderkey,jdbcType=VARCHAR}
			<if test = "reelId != null and reelId != '' " >
				and EO.SERIALNUMBER=#{reelId,jdbcType=VARCHAR}
			</if>
		    AND EXISTS(SELECT 1  FROM  ${whseid}.lotxiddetail l where eo.serialnumber=l.serialnumberlong
       		and eo.orderkey=l.sourcekey and eo.toid=l.id and l.ioflag='O'
			<if test = "pickdetailkey != null and pickdetailkey != '' " >
				and l.pickdetailkey=#{pickdetailkey,jdbcType=VARCHAR}
			</if>)
		</if> 
		<if test = "reelidProcess == 0 " >
			SELECT  'SO'||EO.WHSEID||EO.SERIALKEY  AS SERIALKEY,
			O.EXTERNALORDERKEY2 AS BILLNO,
			EO.SKU AS ITEMNO,
			LT.LOTTABLE02  AS ITEMBARCODE,
			EO.DROPID AS DROPID,
			O.SUSR1 AS PRODPLANID,
			O.C_COMPANY AS SENDTYPE,
			ZB.IS_LEAD AS LEADFLAG,
			ZB.PRODUCT_NO AS brandNo,
			ZB.SUPPLIER_NO AS supplyNo,
			ZB.WETLEVEL AS wetLevel,
			' ' AS REELID,
			EO.QTY,
			TO_CHAR(EO.ADDDATE + 8/24,'YYYY-MM-DD HH24:MI:SS') AS  ADDDATE,
			TO_CHAR(EO.EDITDATE + 8/24,'YYYY-MM-DD HH24:MI:SS')  AS EDITDATE,
			(SELECT  MAX(FROMID) FROM  ${whseid}.ITRN I WHERE  I.SOURCEKEY=EO.PICKDETAILKEY AND I.LOT=EO.LOT
			AND i.toid=EO.DROPID AND i.STORERKEY=EO.STORERKEY AND i.SKU=EO.SKU and i.trantype = 'MV' and i.sourcetype='PICKING' )
			AS boxNo,
			O.WHSEID WMWHSEID
			FROM   ${whseid}.PICKDETAIL   EO
		    INNER JOIN  ${whseid}.ORDERS O
		    ON EO.ORDERKEY=O.ORDERKEY
		    INNER JOIN  ${whseid}.Lotattribute  lt
		    on eo.lot=lt.lot and eo.storerkey=lt.storerkey and eo.sku=lt.sku
		    INNER JOIN ENTERPRISE.ZTEBARCODE ZB
		    ON ZB.ITEM_BARCODE=lt.LOTTABLE02
		    WHERE  eo.orderkey=#{orderkey,jdbcType=VARCHAR}
		    AND  EO.DROPID=#{dropid,jdbcType=VARCHAR}
		    AND  EO.STORERKEY=#{storerkey,jdbcType=VARCHAR}
		    AND  EO.SKU=#{sku,jdbcType=VARCHAR}
		    and  lt.lottable02=#{lottable02,jdbcType=VARCHAR}
		    <if test = "lot != null and lot != '' " >
		    	AND  EO.lot=#{lot,jdbcType=VARCHAR}
		    </if>
		    <if test = "pickdetailkey != null and pickdetailkey != '' " >
				and  eo.pickdetailkey=#{pickdetailkey,jdbcType=VARCHAR}
		    </if>
		</if>
	</select>
	
	<select id="selectSkuIsReelid" parameterType="com.zte.domain.model.infor.HeaderEdiSoS" resultType="java.lang.Long">
		select count(1) from  ${whseid}.sku where storerkey=#{storerkey,jdbcType=VARCHAR} and sku=#{sku,jdbcType=VARCHAR} and exists(select 1 from ${whseid}.nsqlconfig where configkey='EnablingReelid') and reelidprocess='1'
	</select>

	<select id="selectEdiSoSInfo" parameterType="com.zte.domain.model.infor.HeaderEdiSoS" resultMap="EdiSoSInfoMap">
		select
		t.ORDERKEY,
		t.LOTTABLE02,
		'' ITEMNAME,
		t.SKU,
		t.ORIGINALQTY,
		t.QTYSHIPEDTOTAL,
		t.SHIPPEDQTY,
		t.REF01,
		t.REF02,
		t.REF03,
		t.EXTERNORDERKEY,
		t.EXTERNALORDERKEY2,
		t.HSUSR1,
		t.HSUSR2,
		t.HSUSR3,
		t.HREF04,
		(SELECT  MAX(FROMID) FROM  ${whseid}.ITRN i WHERE 1=1
		<if test = "pickdetailkey != null and pickdetailkey != '' " >
			and  I.SOURCEKEY=#{pickdetailkey,jdbcType=VARCHAR}
		</if>
		AND i.toid=t.ID AND i.STORERKEY=t.STORERKEY AND i.SKU=t.SKU and i.trantype = 'MV' and i.sourcetype='PICKING' AND t.LOTTABLE02=i.lottable02)
		AS fromId
		from plugin.EDI_SO_S t where 1=1
		<if test = "externalorderkey2 != null and externalorderkey2 != '' " >
			and  t.EXTERNALORDERKEY2=#{externalorderkey2,jdbcType=VARCHAR}
		</if>
		<if test = "externalorderkey2 == null or externalorderkey2 == '' " >
			and 1=2
		</if>
	</select>

	<select id="getAllReverseScrap" parameterType="com.zte.interfaces.infor.dto.ReqHeadReqInstanceDTO" resultMap="ReqHeadReqInstanceDTOMap">
		select distinct ess.orderkey, ess.externalorderkey2, ess.whseid wmwhseId
		from plugin.edi_so_s ess
		where ess.href11 in ('320', '330', '310', '650', '651', '652', '654', '655')
		and ess.href39 = '报废交逆向'
		and ess.href05 like '%待处理%'
		<if test = "orderkey != null and orderkey !='' " >
			and ess.orderkey = #{orderkey, jdbcType=VARCHAR}
		</if>
		<if test = "externalorderkey2 != null and externalorderkey2 != '' " >
			and ess.externalorderkey2 = #{externalorderkey2, jdbcType=VARCHAR}
		</if>
		and <![CDATA[ess.shippedqty > 0]]>
		and not exists (select 1
		from plugin.storagecenter_edi_log sel
		where sel.orderkey = ess.orderkey
		and sel.externalorderkey2 = ess.externalorderkey2)
	</select>

	<insert id="insertReverseScrap" parameterType="com.zte.interfaces.infor.dto.ReqHeadReqInstanceDTO">
		insert into plugin.storagecenter_edi_log
		(externalorderkey2, orderkey, issend, sendtimes, adddate, type)
		select a.externalorderkey2,
		a.orderkey,
		a.issend,
		a.sendtimes,
		a.adddate,
		'02'
		from (select distinct
		ess.externalorderkey2,
		ess.orderkey,
		-1 issend,
		0 sendtimes,
		sysdate adddate
		from plugin.edi_so_s ess
		where ess.href11 in ('320', '330', '310', '650', '651', '652', '654', '655')
		and ess.href39 = '报废交逆向'
		and ess.href05 like '%待处理%'
		<if test = "orderkey != null and orderkey !='' " >
			and ess.orderkey = #{orderkey, jdbcType=VARCHAR}
		</if>
		<if test = "externalorderkey2 != null and externalorderkey2 != '' " >
			and ess.externalorderkey2 = #{externalorderkey2, jdbcType=VARCHAR}
		</if>) a
		where not exists (select 1
		from plugin.storagecenter_edi_log sel
		where sel.orderkey = a.orderkey
		and sel.externalorderkey2 = a.externalorderkey2)
	</insert>

	<select id="getUnPushedReverseScrapData" parameterType="com.zte.interfaces.infor.dto.ReqHeadReqInstanceDTO" resultMap="ReqHeadReqInstanceDTOMap">
		select distinct sel.orderkey,sel.externalorderkey2
		from plugin.storagecenter_edi_log sel
		where sel.isSend in (-1, 2) and <![CDATA[sel.sendTimes < 4]]>
		and sel.type = '02'
		<if test = "orderkey != null and orderkey !='' " >
			and sel.orderkey = #{orderkey, jdbcType=VARCHAR}
		</if>
		<if test = "externalorderkey2 != null and externalorderkey2 != '' " >
			and sel.externalorderkey2 = #{externalorderkey2, jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getReqHeadReqInstance" parameterType="java.util.List" resultMap="ReqHeadReqInstanceDTOMap">
		select distinct 'LXRKST' billInfoType,
		'LXRK_ZBF_NERP' billTypeCode,
		0 actionCode,
		ess.externalorderkey2 relatedBillNo,
		ess.href25 srcBillNo,
		ess.whseId wmwhseId,
		ess.href31 applyBy,
		'CN' countryCode,
		'INFOR' sourceMode,
		ess.href31 submitedBy,
		sysdate submitedDate,
		101 accessSystem,
		ess.externalorderkey2 remark,
		ess.orderkey,
		ess.externalorderkey2
		from plugin.edi_so_s ess
		where 1=1 and
		<foreach collection="list" item="ReqHeadReqInstanceDTO" open="" separator="or" close="">
			(ess.orderkey = #{ReqHeadReqInstanceDTO.orderkey, jdbcType=VARCHAR} and ess.externalorderkey2 = #{ReqHeadReqInstanceDTO.externalorderkey2, jdbcType=VARCHAR})
		</foreach>
	</select>

	<select id="getReqDetailReqInstance" parameterType="java.util.List" resultMap="ReqDetailReqInstanceDTOMap">
		select '2' stockType,
		ess.sku itemNo,
		sum(ess.shippedqty) reqQty,
		ess.orderkey,
		ess.externalorderkey2,
		101 accessSystem
		from plugin.edi_so_s ess
		where 1=1
		and <![CDATA[ess.shippedqty > 0]]>
		and
		<foreach collection="list" item="ReqHeadReqInstanceDTO" open="" separator="or" close="">
			(ess.orderkey = #{ReqHeadReqInstanceDTO.orderkey, jdbcType=VARCHAR} and ess.externalorderkey2 = #{ReqHeadReqInstanceDTO.externalorderkey2, jdbcType=VARCHAR})
		</foreach>
		group by
		ess.sku,
		ess.orderkey,
		ess.externalorderkey2
	</select>

	
</mapper>