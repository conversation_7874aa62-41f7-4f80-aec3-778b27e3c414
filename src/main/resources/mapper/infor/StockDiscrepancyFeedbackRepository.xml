<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.StockDiscrepancyFeedbackRepository">

    <resultMap id="BaseResultMap" type="com.zte.domain.model.infor.StockDiscrepancyFeedback">
        <id column="SERIALKEY" jdbcType="DECIMAL" property="serialkey" />
        <result column="PROBLEM_CATEGORY" jdbcType="VARCHAR" property="problemCategory" />
        <result column="INVENTORY_TYPE" jdbcType="NUMERIC" property="inventoryType" />
        <result column="FACTORY_CODE" jdbcType="VARCHAR" property="factoryCode" />
        <result column="MPN" jdbcType="DECIMAL" property="mpn" />
        <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType" />
        <result column="DIFF_CATEGORY" jdbcType="VARCHAR" property="diffCategory" />
        <result column="SECOND_DIFF_CATEGORY" jdbcType="TIMESTAMP" property="secondDiffCategory" />
        <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark" />
        <result column="STATUS" jdbcType="TIMESTAMP" property="status" />
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="CREATION_DATE" jdbcType="VARCHAR" property="creationDate" />
        <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
        <result column="LAST_UPDATE_DATE" jdbcType="VARCHAR" property="lastUpdateDate" />
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="MESSAGE_ID" jdbcType="VARCHAR" property="messageId" />
    </resultMap>

    <select id="getTotalCount" resultType="java.lang.Integer">
        select count(1) from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where enabled_flag = 'Y'
        <if test="problemCategory != null and problemCategory !='' ">
            and problem_category  = #{problemCategory,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType != null">
            and inventory_type  = #{inventoryType,jdbcType=INTEGER}
        </if>
        <if test="status != null and status !='' ">
            and status  = #{status,jdbcType=VARCHAR}
        </if>
        <if test="feedbackPerson != null and feedbackPerson !='' ">
            and created_by  = #{feedbackPerson,jdbcType=VARCHAR}
        </if>
        <if test="feedbackStartTime != null and feedbackStartTime !='' ">
            and creation_date <![CDATA[>=]]> to_date(#{feedbackStartTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="feedbackEndTime != null and feedbackEndTime !='' ">
            and creation_date <![CDATA[<=]]> to_date(#{feedbackEndTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>

    <select id="selectByCondition" parameterType="com.zte.interfaces.step.dto.StockDiscrepancyFeedbackDTO"
            resultMap="BaseResultMap">
        select s.* from (
        select r.serialkey,r.problem_category,r.inventory_type,r.factory_code,r.mpn,r.item_type,r.diff_category,r.second_diff_category
        ,r.problem_remark,r.status,r.created_by,r.creation_date,rownum rn
        from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK r
        WHERE r.enabled_flag = 'Y'
        <if test="problemCategory != null and problemCategory !=''">
            and r.problem_category  = #{problemCategory,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType != null">
            and r.inventory_type  = #{inventoryType,jdbcType=NUMERIC}
        </if>
        <if test="status != null and status !=''">
            and r.status  = #{status,jdbcType=VARCHAR}
        </if>
        <if test="feedbackPerson != null and feedbackPerson !=''">
            and r.created_by  = #{feedbackPerson,jdbcType=VARCHAR}
        </if>
        <if test="feedbackStartTime != null and feedbackStartTime !=''">
            and r.creation_date <![CDATA[>=]]> to_date(#{feedbackStartTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="feedbackEndTime != null and feedbackEndTime !=''">
            and r.creation_date <![CDATA[<=]]> to_date(#{feedbackEndTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) s where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and s.rn &lt;= 500000
        </if>
    </select>

    <select id="exportByCondition" resultType="com.zte.interfaces.infor.vo.StockDiscrepancyFeedbackExportVO">
        select s.* from (
        select r.serialkey,r.problem_category,r.inventory_type,r.factory_code,r.mpn,r.item_type,r.diff_category,r.second_diff_category
        ,r.problem_remark,r.status,r.created_by,r.creation_date,rownum rn
        from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK r
        WHERE r.enabled_flag = 'Y'
        <if test="problemCategory != null and problemCategory !=''">
            and r.problem_category  = #{problemCategory,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType != null">
            and r.inventory_type  = #{inventoryType,jdbcType=NUMERIC}
        </if>
        <if test="status != null and status !=''">
            and r.status  = #{status,jdbcType=VARCHAR}
        </if>
        <if test="feedbackPerson != null and feedbackPerson !=''">
            and r.created_by  = #{feedbackPerson,jdbcType=VARCHAR}
        </if>
        <if test="feedbackStartTime != null and feedbackStartTime !=''">
            and r.creation_date <![CDATA[>=]]> to_date(#{feedbackStartTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="feedbackEndTime != null and feedbackEndTime !=''">
            and r.creation_date <![CDATA[<=]]> to_date(#{feedbackEndTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) s where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and s.rn &lt;= 500000
        </if>
    </select>


    <select id="mpnIsSaved" resultType="java.lang.Integer">
        select count(1) from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where mpn = #{mpn,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
        and status = 'SAVED'
    </select>

    <update id="updateByMpn" parameterType="com.zte.interfaces.step.dto.StockDiscrepancyFeedbackAddDTO">
        update PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        set
        problem_category = #{problemCategory,jdbcType=VARCHAR},
        inventory_type = #{inventoryType,jdbcType=NUMERIC},
        factory_code = #{factoryCode,jdbcType=VARCHAR},
        item_type = #{itemType,jdbcType=VARCHAR},
        diff_category = #{diffCategory,jdbcType=VARCHAR},
        second_diff_category = #{secondDiffCategory,jdbcType=TIMESTAMP},
        problem_remark = #{problemRemark,jdbcType=VARCHAR},
        last_updated_by = #{empNo,jdbcType=VARCHAR},
        last_update_date = sysdate
        where mpn = #{mpn,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
        and status = 'SAVED'
    </update>

    <insert id="add" parameterType="com.zte.interfaces.step.dto.StockDiscrepancyFeedbackAddDTO">
        insert into PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        (serialkey,problem_category,inventory_type,factory_code,mpn,item_type,diff_category,second_diff_category
        ,problem_remark,status,created_by,creation_date,last_updated_by,last_update_date,enabled_flag)
        values (
        plugin.zms_inventory_diff_feedback_s.nextval,
        #{problemCategory,jdbcType=VARCHAR},
        #{inventoryType,jdbcType=NUMERIC},
        #{factoryCode,jdbcType=VARCHAR},
        #{mpn,jdbcType=VARCHAR},
        #{itemType,jdbcType=VARCHAR},
        #{diffCategory,jdbcType=VARCHAR},
        #{secondDiffCategory,jdbcType=TIMESTAMP},
        #{problemRemark,jdbcType=VARCHAR},
        'SAVED',
        #{empNo,jdbcType=VARCHAR},
        sysdate,
        #{empNo,jdbcType=VARCHAR},
        sysdate,
        'Y'
        )
    </insert>

    <delete id="deleteByKey">
        update PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        set enabled_flag = 'N'
        where serialkey in
        <foreach collection="keys" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getHaveSubmittedCount" resultType="java.lang.Long">
        select count(1) from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where enabled_flag = 'Y'
        and status = 'SUBMITED'
        and serialkey in
        <foreach collection="keys" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getInfoByKeys" resultMap="BaseResultMap">
        select serialkey,item_type,inventory_type,mpn,diff_category,problem_category,problem_remark,factory_code,second_diff_category,message_id
        from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where enabled_flag = 'Y'
        and serialkey in
        <foreach collection="keys" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateSendResultByMessageId" parameterType="com.zte.interfaces.step.dto.StockDiscrepancyFeedbackDTO">
        update PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        set
        status = #{status,jdbcType=VARCHAR},
        send_status = #{sendStatus,jdbcType=VARCHAR},
        last_update_date = sysdate
        where message_id = #{messageId,jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <update id="updateByKeys">
        update PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        set
        status = 'SUBMITED',
        SEND_STATUS = '0',
        <if test="messageId != null and messageId !=''">
            message_id = #{messageId,jdbcType=VARCHAR},
        </if>
        <if test="messageType != null and messageType !=''">
            message_type = #{messageType,jdbcType=VARCHAR},
        </if>
        send_times = send_times + 1,
        last_update_date = sysdate
        where serialkey in
        <foreach collection="keys" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getAllUnsuccessfulData" resultMap="BaseResultMap">
        select serialkey,item_type,inventory_type,mpn,diff_category,problem_category,problem_remark,factory_code,second_diff_category,message_id
        from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where enabled_flag = 'Y'
        and status = 'SAVED'
        and send_times  <![CDATA[<]]> 4
        and creation_date <![CDATA[>=]]> TRUNC(SYSDATE)
    </select>

    <select id="getDataByMessageIds" resultMap="BaseResultMap">
        select serialkey,item_type,inventory_type,mpn,diff_category,problem_category,problem_remark,factory_code,second_diff_category,message_id
        from PLUGIN.ZMS_INVENTORY_DIFF_FEEDBACK
        where enabled_flag = 'Y'
        and message_id in
        <foreach collection="messageIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>