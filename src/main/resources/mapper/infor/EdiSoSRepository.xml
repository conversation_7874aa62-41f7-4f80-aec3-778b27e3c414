<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.EdiSoSRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.infor.EdiSoS">
    <result column="SERIALKEY" jdbcType="DECIMAL" property="serialkey" />
    <result column="LOGID" jdbcType="OTHER" property="logid" />
    <result column="EDI_ADDDATE" jdbcType="TIMESTAMP" property="ediAdddate" />
    <result column="EDI_EDITDATE" jdbcType="TIMESTAMP" property="ediEditdate" />
    <result column="SYMBOL" jdbcType="DECIMAL" property="symbol" />
    <result column="INVSYMBOL" jdbcType="DECIMAL" property="invsymbol" />
    <result column="INVFAILDSYMBOL" jdbcType="DECIMAL" property="invfaildsymbol" />
    <result column="FEEDBACKFLAG" jdbcType="DECIMAL" property="feedbackflag" />
    <result column="FEEDBACKDATE" jdbcType="TIMESTAMP" property="feedbackdate" />
    <result column="ORDERKEY" jdbcType="OTHER" property="orderkey" />
    <result column="EXTERNORDERKEY" jdbcType="OTHER" property="externorderkey" />
    <result column="TYPE" jdbcType="OTHER" property="type" />
    <result column="STORERKEY" jdbcType="OTHER" property="storerkey" />
    <result column="REQUESTEDSHIPDATE" jdbcType="TIMESTAMP" property="requestedshipdate" />
    <result column="EXTERNALORDERKEY2" jdbcType="OTHER" property="externalorderkey2" />
    <result column="CONSIGNEEKEY" jdbcType="OTHER" property="consigneekey" />
    <result column="ORDERSID" jdbcType="OTHER" property="ordersid" />
    <result column="HSUSR1" jdbcType="OTHER" property="hsusr1" />
    <result column="HSUSR2" jdbcType="OTHER" property="hsusr2" />
    <result column="HSUSR3" jdbcType="OTHER" property="hsusr3" />
    <result column="HSUSR4" jdbcType="OTHER" property="hsusr4" />
    <result column="HSUSR5" jdbcType="OTHER" property="hsusr5" />
    <result column="HREF01" jdbcType="OTHER" property="href01" />
    <result column="HREF02" jdbcType="OTHER" property="href02" />
    <result column="HREF03" jdbcType="OTHER" property="href03" />
    <result column="HREF04" jdbcType="OTHER" property="href04" />
    <result column="HREF05" jdbcType="OTHER" property="href05" />
    <result column="HREF06" jdbcType="OTHER" property="href06" />
    <result column="HREF07" jdbcType="OTHER" property="href07" />
    <result column="HREF08" jdbcType="OTHER" property="href08" />
    <result column="HREF09" jdbcType="OTHER" property="href09" />
    <result column="HREF10" jdbcType="OTHER" property="href10" />
    <result column="HREF11" jdbcType="OTHER" property="href11" />
    <result column="HREF12" jdbcType="OTHER" property="href12" />
    <result column="HREF13" jdbcType="OTHER" property="href13" />
    <result column="HREF14" jdbcType="OTHER" property="href14" />
    <result column="HREF15" jdbcType="OTHER" property="href15" />
    <result column="HREF16" jdbcType="OTHER" property="href16" />
    <result column="HREF17" jdbcType="OTHER" property="href17" />
    <result column="HREF18" jdbcType="OTHER" property="href18" />
    <result column="HREF19" jdbcType="OTHER" property="href19" />
    <result column="HREF20" jdbcType="OTHER" property="href20" />
    <result column="HREF21" jdbcType="OTHER" property="href21" />
    <result column="HREF22" jdbcType="OTHER" property="href22" />
    <result column="HREF23" jdbcType="OTHER" property="href23" />
    <result column="HREF24" jdbcType="OTHER" property="href24" />
    <result column="HREF25" jdbcType="OTHER" property="href25" />
    <result column="HREF26" jdbcType="TIMESTAMP" property="href26" />
    <result column="HREF27" jdbcType="TIMESTAMP" property="href27" />
    <result column="HREF28" jdbcType="TIMESTAMP" property="href28" />
    <result column="HREF29" jdbcType="TIMESTAMP" property="href29" />
    <result column="HREF30" jdbcType="TIMESTAMP" property="href30" />
    <result column="HREF31" jdbcType="OTHER" property="href31" />
    <result column="HREF32" jdbcType="OTHER" property="href32" />
    <result column="HREF33" jdbcType="OTHER" property="href33" />
    <result column="HREF34" jdbcType="OTHER" property="href34" />
    <result column="HREF35" jdbcType="OTHER" property="href35" />
    <result column="HREF36" jdbcType="OTHER" property="href36" />
    <result column="HREF37" jdbcType="OTHER" property="href37" />
    <result column="HREF38" jdbcType="OTHER" property="href38" />
    <result column="HREF39" jdbcType="OTHER" property="href39" />
    <result column="HREF40" jdbcType="OTHER" property="href40" />
    <result column="HREF41" jdbcType="OTHER" property="href41" />
    <result column="HREF42" jdbcType="OTHER" property="href42" />
    <result column="HREF43" jdbcType="OTHER" property="href43" />
    <result column="HREF44" jdbcType="OTHER" property="href44" />
    <result column="HREF45" jdbcType="OTHER" property="href45" />
    <result column="HREF46" jdbcType="OTHER" property="href46" />
    <result column="HREF47" jdbcType="OTHER" property="href47" />
    <result column="HREF48" jdbcType="OTHER" property="href48" />
    <result column="HREF49" jdbcType="OTHER" property="href49" />
    <result column="HREF50" jdbcType="OTHER" property="href50" />
    <result column="HREF51" jdbcType="OTHER" property="href51" />
    <result column="HREF52" jdbcType="OTHER" property="href52" />
    <result column="HREF53" jdbcType="OTHER" property="href53" />
    <result column="HREF54" jdbcType="OTHER" property="href54" />
    <result column="HREF55" jdbcType="OTHER" property="href55" />
    <result column="HREF56" jdbcType="OTHER" property="href56" />
    <result column="HREF57" jdbcType="OTHER" property="href57" />
    <result column="HREF58" jdbcType="OTHER" property="href58" />
    <result column="HREF59" jdbcType="OTHER" property="href59" />
    <result column="HREF60" jdbcType="OTHER" property="href60" />
    <result column="ORDERLINENUMBER" jdbcType="OTHER" property="orderlinenumber" />
    <result column="EXTERNLINENO" jdbcType="OTHER" property="externlineno" />
    <result column="SKU" jdbcType="OTHER" property="sku" />
    <result column="PACKKEY" jdbcType="OTHER" property="packkey" />
    <result column="ORIGINALQTY" jdbcType="DECIMAL" property="originalqty" />
    <result column="QTYSHIPEDTOTAL" jdbcType="DECIMAL" property="qtyshipedtotal" />
    <result column="SHIPPEDQTY" jdbcType="DECIMAL" property="shippedqty" />
    <result column="UOM" jdbcType="OTHER" property="uom" />
    <result column="ALLOWOVERPICK" jdbcType="DECIMAL" property="allowoverpick" />
    <result column="PREALLOCATESTRATEGYKEY" jdbcType="OTHER" property="preallocatestrategykey" />
    <result column="ALLOCATESTRATEGYKEY" jdbcType="OTHER" property="allocatestrategykey" />
    <result column="ALLOCATESTRATEGYTYPE" jdbcType="OTHER" property="allocatestrategytype" />
    <result column="SHELFLIFE" jdbcType="DECIMAL" property="shelflife" />
    <result column="ROTATION" jdbcType="OTHER" property="rotation" />
    <result column="SKUROTATION" jdbcType="OTHER" property="skurotation" />
    <result column="LOT" jdbcType="OTHER" property="lot" />
    <result column="LOTTABLE01" jdbcType="OTHER" property="lottable01" />
    <result column="LOTTABLE02" jdbcType="OTHER" property="lottable02" />
    <result column="LOTTABLE03" jdbcType="OTHER" property="lottable03" />
    <result column="LOTTABLE04" jdbcType="TIMESTAMP" property="lottable04" />
    <result column="LOTTABLE05" jdbcType="TIMESTAMP" property="lottable05" />
    <result column="LOTTABLE06" jdbcType="OTHER" property="lottable06" />
    <result column="LOTTABLE07" jdbcType="OTHER" property="lottable07" />
    <result column="LOTTABLE08" jdbcType="OTHER" property="lottable08" />
    <result column="LOTTABLE09" jdbcType="OTHER" property="lottable09" />
    <result column="LOTTABLE10" jdbcType="OTHER" property="lottable10" />
    <result column="LOTTABLE11" jdbcType="TIMESTAMP" property="lottable11" />
    <result column="LOTTABLE12" jdbcType="TIMESTAMP" property="lottable12" />
    <result column="SUSR1" jdbcType="OTHER" property="susr1" />
    <result column="SUSR2" jdbcType="OTHER" property="susr2" />
    <result column="SUSR3" jdbcType="OTHER" property="susr3" />
    <result column="SUSR4" jdbcType="OTHER" property="susr4" />
    <result column="SUSR5" jdbcType="OTHER" property="susr5" />
    <result column="NOTES2" jdbcType="OTHER" property="notes2" />
    <result column="NOTES" jdbcType="OTHER" property="notes" />
    <result column="REF01" jdbcType="OTHER" property="ref01" />
    <result column="REF02" jdbcType="OTHER" property="ref02" />
    <result column="REF03" jdbcType="OTHER" property="ref03" />
    <result column="REF04" jdbcType="OTHER" property="ref04" />
    <result column="REF05" jdbcType="OTHER" property="ref05" />
    <result column="REF06" jdbcType="OTHER" property="ref06" />
    <result column="REF07" jdbcType="OTHER" property="ref07" />
    <result column="REF08" jdbcType="OTHER" property="ref08" />
    <result column="REF09" jdbcType="OTHER" property="ref09" />
    <result column="REF10" jdbcType="OTHER" property="ref10" />
    <result column="REF11" jdbcType="OTHER" property="ref11" />
    <result column="REF12" jdbcType="OTHER" property="ref12" />
    <result column="REF13" jdbcType="OTHER" property="ref13" />
    <result column="REF14" jdbcType="OTHER" property="ref14" />
    <result column="REF15" jdbcType="OTHER" property="ref15" />
    <result column="REF16" jdbcType="OTHER" property="ref16" />
    <result column="REF17" jdbcType="OTHER" property="ref17" />
    <result column="REF18" jdbcType="OTHER" property="ref18" />
    <result column="REF19" jdbcType="OTHER" property="ref19" />
    <result column="REF20" jdbcType="OTHER" property="ref20" />
    <result column="REF21" jdbcType="OTHER" property="ref21" />
    <result column="REF22" jdbcType="OTHER" property="ref22" />
    <result column="REF23" jdbcType="OTHER" property="ref23" />
    <result column="REF24" jdbcType="OTHER" property="ref24" />
    <result column="REF25" jdbcType="OTHER" property="ref25" />
    <result column="REF26" jdbcType="TIMESTAMP" property="ref26" />
    <result column="REF27" jdbcType="TIMESTAMP" property="ref27" />
    <result column="REF28" jdbcType="TIMESTAMP" property="ref28" />
    <result column="REF29" jdbcType="TIMESTAMP" property="ref29" />
    <result column="REF30" jdbcType="TIMESTAMP" property="ref30" />
    <result column="REF31" jdbcType="OTHER" property="ref31" />
    <result column="REF32" jdbcType="OTHER" property="ref32" />
    <result column="REF33" jdbcType="OTHER" property="ref33" />
    <result column="REF34" jdbcType="OTHER" property="ref34" />
    <result column="REF35" jdbcType="OTHER" property="ref35" />
    <result column="REF36" jdbcType="OTHER" property="ref36" />
    <result column="REF37" jdbcType="OTHER" property="ref37" />
    <result column="REF38" jdbcType="OTHER" property="ref38" />
    <result column="REF39" jdbcType="OTHER" property="ref39" />
    <result column="REF40" jdbcType="OTHER" property="ref40" />
    <result column="REF41" jdbcType="OTHER" property="ref41" />
    <result column="REF42" jdbcType="OTHER" property="ref42" />
    <result column="REF43" jdbcType="OTHER" property="ref43" />
    <result column="REF44" jdbcType="OTHER" property="ref44" />
    <result column="REF45" jdbcType="OTHER" property="ref45" />
    <result column="REF46" jdbcType="OTHER" property="ref46" />
    <result column="REF47" jdbcType="OTHER" property="ref47" />
    <result column="REF48" jdbcType="OTHER" property="ref48" />
    <result column="REF49" jdbcType="OTHER" property="ref49" />
    <result column="REF50" jdbcType="OTHER" property="ref50" />
    <result column="REF51" jdbcType="OTHER" property="ref51" />
    <result column="REF52" jdbcType="OTHER" property="ref52" />
    <result column="REF53" jdbcType="OTHER" property="ref53" />
    <result column="REF54" jdbcType="OTHER" property="ref54" />
    <result column="REF55" jdbcType="OTHER" property="ref55" />
    <result column="REF56" jdbcType="OTHER" property="ref56" />
    <result column="REF57" jdbcType="OTHER" property="ref57" />
    <result column="REF58" jdbcType="OTHER" property="ref58" />
    <result column="REF59" jdbcType="OTHER" property="ref59" />
    <result column="REF60" jdbcType="OTHER" property="ref60" />
    <result column="ADDDATE" jdbcType="TIMESTAMP" property="adddate" />
    <result column="EDITDATE" jdbcType="TIMESTAMP" property="editdate" />
    <result column="EDITWHO" jdbcType="OTHER" property="editwho" />
    <result column="WHSEID" jdbcType="OTHER" property="whseid" />
    <result column="ID" jdbcType="OTHER" property="id" />
    <result column="SMALLAMOUNTFLAG" jdbcType="DECIMAL" property="smallamountflag" />
    <result column="REMARK" jdbcType="OTHER" property="remark" />
    <result column="ENDTIME" jdbcType="TIMESTAMP" property="endtime" />
    <result column="HREF61" jdbcType="OTHER" property="href61" />
    <result column="HREF62" jdbcType="OTHER" property="href62" />
    <result column="HREF63" jdbcType="OTHER" property="href63" />
    <result column="HREF64" jdbcType="OTHER" property="href64" />
  </resultMap>

   <sql id="Base_Column_List">
    SERIALKEY, LOGID, EDI_ADDDATE, EDI_EDITDATE, SYMBOL, INVSYMBOL, INVFAILDSYMBOL, 
    FEEDBACKFLAG, FEEDBACKDATE, ORDERKEY, EXTERNORDERKEY, TYPE, STORERKEY, REQUESTEDSHIPDATE, 
    EXTERNALORDERKEY2, CONSIGNEEKEY, ORDERSID, HSUSR1, HSUSR2, HSUSR3, HSUSR4, HSUSR5, 
    HREF01, HREF02, HREF03, HREF04, HREF05, HREF06, HREF07, HREF08, HREF09, HREF10, HREF11, 
    HREF12, HREF13, HREF14, HREF15, HREF16, HREF17, HREF18, HREF19, HREF20, HREF21, HREF22, 
    HREF23, HREF24, HREF25, HREF26, HREF27, HREF28, HREF29, HREF30, HREF31, HREF32, HREF33, 
    HREF34, HREF35, HREF36, HREF37, HREF38, HREF39, HREF40, HREF41, HREF42, HREF43, HREF44, 
    HREF45, HREF46, HREF47, HREF48, HREF49, HREF50, HREF51, HREF52, HREF53, HREF54, HREF55, 
    HREF56, HREF57, HREF58, HREF59, HREF60, ORDERLINENUMBER, EXTERNLINENO, SKU, PACKKEY, 
    ORIGINALQTY, QTYSHIPEDTOTAL, SHIPPEDQTY, UOM, ALLOWOVERPICK, PREALLOCATESTRATEGYKEY, 
    ALLOCATESTRATEGYKEY, ALLOCATESTRATEGYTYPE, SHELFLIFE, ROTATION, SKUROTATION, LOT, 
    LOTTABLE01, LOTTABLE02, LOTTABLE03, LOTTABLE04, LOTTABLE05, LOTTABLE06, LOTTABLE07, 
    LOTTABLE08, LOTTABLE09, LOTTABLE10, LOTTABLE11, LOTTABLE12, SUSR1, SUSR2, SUSR3, 
    SUSR4, SUSR5, NOTES2, NOTES, REF01, REF02, REF03, REF04, REF05, REF06, REF07, REF08, 
    REF09, REF10, REF11, REF12, REF13, REF14, REF15, REF16, REF17, REF18, REF19, REF20, 
    REF21, REF22, REF23, REF24, REF25, REF26, REF27, REF28, REF29, REF30, REF31, REF32, 
    REF33, REF34, REF35, REF36, REF37, REF38, REF39, REF40, REF41, REF42, REF43, REF44, 
    REF45, REF46, REF47, REF48, REF49, REF50, REF51, REF52, REF53, REF54, REF55, REF56, 
    REF57, REF58, REF59, REF60, ADDDATE, EDITDATE, EDITWHO, WHSEID, ID, SMALLAMOUNTFLAG, 
    REMARK, ENDTIME, HREF61, HREF62, HREF63, HREF64
  </sql>

  <select id="selectEdiSoSAll"  parameterType="com.zte.domain.model.infor.EdiSoS" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from plugin.EDI_SO_S
    where externalorderkey2 like 'STH%'
    and WHSEID in (select UPPER(DB_LOGID) FROM WMSADMIN.PL_DB WHERE DB_TYPE=4 and ISACTIVE = 1)
    AND HREF11 in (351,352)
    <if test=" externalorderkey2 !=null and externalorderkey2 !='' ">
      and EXTERNALORDERKEY2=#{externalorderkey2,jdbcType=OTHER}
    </if>
    <if test=" serialkey !=null and serialkey !=0 ">
      and SERIALKEY=#{serialkey,jdbcType=DECIMAL}
    </if>
  </select>
  
  <select id="selectEdiSoSAllInfor"  parameterType="java.util.List" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from plugin.EDI_SO_S
    where EXTERNALORDERKEY2 in 
    <foreach item="item" index="index" collection="list" open="(" separator=" union all " close=")">
        select #{item.billNo} from dual
    </foreach>
  </select>

  <insert id="insertEdiSoS" parameterType="com.zte.domain.model.infor.EdiSoS">
    insert into plugin.EDI_SO_S ( LOGID, EDI_ADDDATE, 
      EDI_EDITDATE, SYMBOL, INVSYMBOL, 
      INVFAILDSYMBOL, FEEDBACKFLAG, FEEDBACKDATE, 
      ORDERKEY, EXTERNORDERKEY, TYPE, 
      STORERKEY, REQUESTEDSHIPDATE, EXTERNALORDERKEY2, 
      CONSIGNEEKEY, ORDERSID, HSUSR1, 
      HSUSR2, HSUSR3, HSUSR4, HSUSR5, 
      HREF01, HREF02, HREF03, HREF04, 
      HREF05, HREF06, HREF07, HREF08, 
      HREF09, HREF10, HREF11, HREF12, 
      HREF13, HREF14, HREF15, HREF16, 
      HREF17, HREF18, HREF19, HREF20, 
      HREF21, HREF22, HREF23, HREF24, 
      HREF25, HREF26, HREF27, 
      HREF28, HREF29, HREF30, 
      HREF31, HREF32, HREF33, HREF34, 
      HREF35, HREF36, HREF37, HREF38, 
      HREF39, HREF40, HREF41, HREF42, 
      HREF43, HREF44, HREF45, HREF46, 
      HREF47, HREF48, HREF49, HREF50, 
      HREF51, HREF52, HREF53, HREF54, 
      HREF55, HREF56, HREF57, HREF58, 
      HREF59, HREF60, ORDERLINENUMBER, 
      EXTERNLINENO, SKU, PACKKEY, 
      ORIGINALQTY, QTYSHIPEDTOTAL, SHIPPEDQTY, 
      UOM, ALLOWOVERPICK, PREALLOCATESTRATEGYKEY, 
      ALLOCATESTRATEGYKEY, ALLOCATESTRATEGYTYPE, 
      SHELFLIFE, ROTATION, SKUROTATION, 
      LOT, LOTTABLE01, LOTTABLE02, 
      LOTTABLE03, LOTTABLE04, LOTTABLE05, 
      LOTTABLE06, LOTTABLE07, LOTTABLE08, 
      LOTTABLE09, LOTTABLE10, LOTTABLE11, 
      LOTTABLE12, SUSR1, SUSR2, 
      SUSR3, SUSR4, SUSR5, NOTES2, 
      NOTES, REF01, REF02, REF03, 
      REF04, REF05, REF06, REF07, 
      REF08, REF09, REF10, REF11, 
      REF12, REF13, REF14, REF15, 
      REF16, REF17, REF18, REF19, 
      REF20, REF21, REF22, REF23, 
      REF24, REF25, REF26, 
      REF27, REF28, REF29, 
      REF30, REF31, REF32, 
      REF33, REF34, REF35, REF36, 
      REF37, REF38, REF39, REF40, 
      REF41, REF42, REF43, REF44, 
      REF45, REF46, REF47, REF48, 
      REF49, REF50, REF51, REF52, 
      REF53, REF54, REF55, REF56, 
      REF57, REF58, REF59, REF60, 
      ADDDATE, EDITDATE, EDITWHO, 
      WHSEID, ID, SMALLAMOUNTFLAG, 
      REMARK, ENDTIME, HREF61, 
      HREF62, HREF63, HREF64)
    values (#{logid,jdbcType=OTHER}, sysdate, 
      sysdate, #{symbol,jdbcType=DECIMAL}, #{invsymbol,jdbcType=DECIMAL}, 
      #{invfaildsymbol,jdbcType=DECIMAL}, #{feedbackflag,jdbcType=DECIMAL}, #{feedbackdate,jdbcType=TIMESTAMP}, 
      #{orderkey,jdbcType=OTHER}, #{externorderkey,jdbcType=OTHER}, #{type,jdbcType=OTHER}, 
      #{storerkey,jdbcType=OTHER}, #{requestedshipdate,jdbcType=TIMESTAMP}, #{externalorderkey2,jdbcType=OTHER}, 
      #{consigneekey,jdbcType=OTHER}, #{ordersid,jdbcType=OTHER}, #{hsusr1,jdbcType=OTHER}, 
      #{hsusr2,jdbcType=OTHER}, #{hsusr3,jdbcType=OTHER}, #{hsusr4,jdbcType=OTHER}, #{hsusr5,jdbcType=OTHER}, 
      #{href01,jdbcType=OTHER}, #{href02,jdbcType=OTHER}, #{href03,jdbcType=OTHER}, #{href04,jdbcType=OTHER}, 
      #{href05,jdbcType=OTHER}, #{href06,jdbcType=OTHER}, #{href07,jdbcType=OTHER}, #{href08,jdbcType=OTHER}, 
      #{href09,jdbcType=OTHER}, #{href10,jdbcType=OTHER}, #{href11,jdbcType=OTHER}, #{href12,jdbcType=OTHER}, 
      #{href13,jdbcType=OTHER}, #{href14,jdbcType=OTHER}, #{href15,jdbcType=OTHER}, #{href16,jdbcType=OTHER}, 
      #{href17,jdbcType=OTHER}, #{href18,jdbcType=OTHER}, #{href19,jdbcType=OTHER}, #{href20,jdbcType=OTHER}, 
      #{href21,jdbcType=OTHER}, #{href22,jdbcType=OTHER}, #{href23,jdbcType=OTHER}, #{href24,jdbcType=OTHER}, 
      #{href25,jdbcType=OTHER}, #{href26,jdbcType=TIMESTAMP}, #{href27,jdbcType=TIMESTAMP}, 
      #{href28,jdbcType=TIMESTAMP}, #{href29,jdbcType=TIMESTAMP}, #{href30,jdbcType=TIMESTAMP}, 
      #{href31,jdbcType=OTHER}, #{href32,jdbcType=OTHER}, #{href33,jdbcType=OTHER}, #{href34,jdbcType=OTHER}, 
      #{href35,jdbcType=OTHER}, #{href36,jdbcType=OTHER}, #{href37,jdbcType=OTHER}, #{href38,jdbcType=OTHER}, 
      #{href39,jdbcType=OTHER}, #{href40,jdbcType=OTHER}, #{href41,jdbcType=OTHER}, #{href42,jdbcType=OTHER}, 
      #{href43,jdbcType=OTHER}, #{href44,jdbcType=OTHER}, #{href45,jdbcType=OTHER}, #{href46,jdbcType=OTHER}, 
      #{href47,jdbcType=OTHER}, #{href48,jdbcType=OTHER}, #{href49,jdbcType=OTHER}, #{href50,jdbcType=OTHER}, 
      #{href51,jdbcType=OTHER}, #{href52,jdbcType=OTHER}, #{href53,jdbcType=OTHER}, #{href54,jdbcType=OTHER}, 
      #{href55,jdbcType=OTHER}, #{href56,jdbcType=OTHER}, #{href57,jdbcType=OTHER}, #{href58,jdbcType=OTHER}, 
      #{href59,jdbcType=OTHER}, #{href60,jdbcType=OTHER}, #{orderlinenumber,jdbcType=OTHER}, 
      #{externlineno,jdbcType=OTHER}, #{sku,jdbcType=OTHER}, #{packkey,jdbcType=OTHER}, 
      #{originalqty,jdbcType=DECIMAL}, #{qtyshipedtotal,jdbcType=DECIMAL}, #{shippedqty,jdbcType=DECIMAL}, 
      #{uom,jdbcType=OTHER}, #{allowoverpick,jdbcType=DECIMAL}, #{preallocatestrategykey,jdbcType=OTHER}, 
      #{allocatestrategykey,jdbcType=OTHER}, #{allocatestrategytype,jdbcType=OTHER}, 
      #{shelflife,jdbcType=DECIMAL}, #{rotation,jdbcType=OTHER}, #{skurotation,jdbcType=OTHER}, 
      #{lot,jdbcType=OTHER}, #{lottable01,jdbcType=OTHER}, #{lottable02,jdbcType=OTHER}, 
      #{lottable03,jdbcType=OTHER}, #{lottable04,jdbcType=TIMESTAMP}, #{lottable05,jdbcType=TIMESTAMP}, 
      #{lottable06,jdbcType=OTHER}, #{lottable07,jdbcType=OTHER}, #{lottable08,jdbcType=OTHER}, 
      #{lottable09,jdbcType=OTHER}, #{lottable10,jdbcType=OTHER}, #{lottable11,jdbcType=TIMESTAMP}, 
      #{lottable12,jdbcType=TIMESTAMP}, #{susr1,jdbcType=OTHER}, #{susr2,jdbcType=OTHER}, 
      #{susr3,jdbcType=OTHER}, #{susr4,jdbcType=OTHER}, #{susr5,jdbcType=OTHER}, #{notes2,jdbcType=OTHER}, 
      #{notes,jdbcType=OTHER}, #{ref01,jdbcType=OTHER}, #{ref02,jdbcType=OTHER}, #{ref03,jdbcType=OTHER}, 
      #{ref04,jdbcType=OTHER}, #{ref05,jdbcType=OTHER}, #{ref06,jdbcType=OTHER}, #{ref07,jdbcType=OTHER}, 
      #{ref08,jdbcType=OTHER}, #{ref09,jdbcType=OTHER}, #{ref10,jdbcType=OTHER}, #{ref11,jdbcType=OTHER}, 
      #{ref12,jdbcType=OTHER}, #{ref13,jdbcType=OTHER}, #{ref14,jdbcType=OTHER}, #{ref15,jdbcType=OTHER}, 
      #{ref16,jdbcType=OTHER}, #{ref17,jdbcType=OTHER}, #{ref18,jdbcType=OTHER}, #{ref19,jdbcType=OTHER}, 
      #{ref20,jdbcType=OTHER}, #{ref21,jdbcType=OTHER}, #{ref22,jdbcType=OTHER}, #{ref23,jdbcType=OTHER}, 
      #{ref24,jdbcType=OTHER}, #{ref25,jdbcType=OTHER}, #{ref26,jdbcType=TIMESTAMP}, 
      #{ref27,jdbcType=TIMESTAMP}, #{ref28,jdbcType=TIMESTAMP}, #{ref29,jdbcType=TIMESTAMP}, 
      #{ref30,jdbcType=TIMESTAMP}, #{ref31,jdbcType=OTHER}, #{ref32,jdbcType=OTHER}, 
      #{ref33,jdbcType=OTHER}, #{ref34,jdbcType=OTHER}, #{ref35,jdbcType=OTHER}, #{ref36,jdbcType=OTHER}, 
      #{ref37,jdbcType=OTHER}, #{ref38,jdbcType=OTHER}, #{ref39,jdbcType=OTHER}, #{ref40,jdbcType=OTHER}, 
      #{ref41,jdbcType=OTHER}, #{ref42,jdbcType=OTHER}, #{ref43,jdbcType=OTHER}, #{ref44,jdbcType=OTHER}, 
      #{ref45,jdbcType=OTHER}, #{ref46,jdbcType=OTHER}, #{ref47,jdbcType=OTHER}, #{ref48,jdbcType=OTHER}, 
      #{ref49,jdbcType=OTHER}, #{ref50,jdbcType=OTHER}, #{ref51,jdbcType=OTHER}, #{ref52,jdbcType=OTHER}, 
      #{ref53,jdbcType=OTHER}, #{ref54,jdbcType=OTHER}, #{ref55,jdbcType=OTHER}, #{ref56,jdbcType=OTHER}, 
      #{ref57,jdbcType=OTHER}, #{ref58,jdbcType=OTHER}, #{ref59,jdbcType=OTHER}, #{ref60,jdbcType=OTHER}, 
      #{adddate,jdbcType=TIMESTAMP}, #{editdate,jdbcType=TIMESTAMP}, #{editwho,jdbcType=OTHER}, 
      #{whseid,jdbcType=OTHER}, #{id,jdbcType=OTHER}, #{smallamountflag,jdbcType=DECIMAL}, 
      #{remark,jdbcType=OTHER}, #{endtime,jdbcType=TIMESTAMP}, #{href61,jdbcType=OTHER}, 
      #{href62,jdbcType=OTHER}, #{href63,jdbcType=OTHER}, #{href64,jdbcType=OTHER})
  </insert>
  
  <insert id="insertEdiSosBatch" parameterType="com.zte.domain.model.infor.EdiSoS">
     <foreach collection="list"  item="item" separator=";" open="begin" close=";end;">
     insert into plugin.EDI_SO_S ( LOGID, EDI_ADDDATE, 
      EDI_EDITDATE, SYMBOL, INVSYMBOL, 
      INVFAILDSYMBOL, FEEDBACKFLAG, FEEDBACKDATE, 
      ORDERKEY, EXTERNORDERKEY, TYPE, 
      STORERKEY, REQUESTEDSHIPDATE, EXTERNALORDERKEY2, 
      CONSIGNEEKEY, ORDERSID, HSUSR1, 
      HSUSR2, HSUSR3, HSUSR4, HSUSR5, 
      HREF01, HREF02, HREF03, HREF04, 
      HREF05, HREF06, HREF07, HREF08, 
      HREF09, HREF10, HREF11, HREF12, 
      HREF13, HREF14, HREF15, HREF16, 
      HREF17, HREF18, HREF19, HREF20, 
      HREF21, HREF22, HREF23, HREF24, 
      HREF25, HREF26, HREF27, 
      HREF28, HREF29, HREF30, 
      HREF31, HREF32, HREF33, HREF34, 
      HREF35, HREF36, HREF37, HREF38, 
      HREF39, HREF40, HREF41, HREF42, 
      HREF43, HREF44, HREF45, HREF46, 
      HREF47, HREF48, HREF49, HREF50, 
      HREF51, HREF52, HREF53, HREF54, 
      HREF55, HREF56, HREF57, HREF58, 
      HREF59, HREF60, ORDERLINENUMBER, 
      EXTERNLINENO, SKU, PACKKEY, 
      ORIGINALQTY, QTYSHIPEDTOTAL, SHIPPEDQTY, 
      UOM, ALLOWOVERPICK, PREALLOCATESTRATEGYKEY, 
      ALLOCATESTRATEGYKEY, ALLOCATESTRATEGYTYPE, 
      SHELFLIFE, ROTATION, SKUROTATION, 
      LOT, LOTTABLE01, LOTTABLE02, 
      LOTTABLE03, LOTTABLE04, LOTTABLE05, 
      LOTTABLE06, LOTTABLE07, LOTTABLE08, 
      LOTTABLE09, LOTTABLE10, LOTTABLE11, 
      LOTTABLE12, SUSR1, SUSR2, 
      SUSR3, SUSR4, SUSR5, NOTES2, 
      NOTES, REF01, REF02, REF03, 
      REF04, REF05, REF06, REF07, 
      REF08, REF09, REF10, REF11, 
      REF12, REF13, REF14, REF15, 
      REF16, REF17, REF18, REF19, 
      REF20, REF21, REF22, REF23, 
      REF24, REF25, REF26, 
      REF27, REF28, REF29, 
      REF30, REF31, REF32, 
      REF33, REF34, REF35, REF36, 
      REF37, REF38, REF39, REF40, 
      REF41, REF42, REF43, REF44, 
      REF45, REF46, REF47, REF48, 
      REF49, REF50, REF51, REF52, 
      REF53, REF54, REF55, REF56, 
      REF57, REF58, REF59, REF60, 
      ADDDATE, EDITDATE, EDITWHO, 
      WHSEID, ID, SMALLAMOUNTFLAG, 
      REMARK, ENDTIME, HREF61, 
      HREF62, HREF63, HREF64)
    values
    
     (#{item.logid,jdbcType=VARCHAR}, sysdate, 
      sysdate, #{item.symbol,jdbcType=DECIMAL}, #{item.invsymbol,jdbcType=DECIMAL}, 
      #{item.invfaildsymbol,jdbcType=DECIMAL}, #{item.feedbackflag,jdbcType=DECIMAL}, #{item.feedbackdate,jdbcType=TIMESTAMP}, 
      #{item.orderkey,jdbcType=VARCHAR}, #{item.externorderkey,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
      #{item.storerkey,jdbcType=VARCHAR}, #{item.requestedshipdate,jdbcType=TIMESTAMP}, #{item.externalorderkey2,jdbcType=VARCHAR}, 
      #{item.consigneekey,jdbcType=VARCHAR}, #{item.ordersid,jdbcType=VARCHAR}, #{item.hsusr1,jdbcType=VARCHAR}, 
      #{item.hsusr2,jdbcType=VARCHAR}, #{item.hsusr3,jdbcType=VARCHAR}, #{item.hsusr4,jdbcType=VARCHAR}, #{item.hsusr5,jdbcType=VARCHAR}, 
      #{item.href01,jdbcType=VARCHAR}, #{item.href02,jdbcType=VARCHAR}, #{item.href03,jdbcType=VARCHAR}, #{item.href04,jdbcType=VARCHAR}, 
      #{item.href05,jdbcType=VARCHAR}, #{item.href06,jdbcType=VARCHAR}, #{item.href07,jdbcType=VARCHAR}, #{item.href08,jdbcType=VARCHAR}, 
      #{item.href09,jdbcType=VARCHAR}, #{item.href10,jdbcType=VARCHAR}, #{item.href11,jdbcType=VARCHAR}, #{item.href12,jdbcType=VARCHAR}, 
      #{item.href13,jdbcType=VARCHAR}, #{item.href14,jdbcType=VARCHAR}, #{item.href15,jdbcType=VARCHAR}, #{item.href16,jdbcType=VARCHAR}, 
      #{item.href17,jdbcType=VARCHAR}, #{item.href18,jdbcType=VARCHAR}, #{item.href19,jdbcType=VARCHAR}, #{item.href20,jdbcType=VARCHAR}, 
      #{item.href21,jdbcType=VARCHAR}, #{item.href22,jdbcType=VARCHAR}, #{item.href23,jdbcType=VARCHAR}, #{item.href24,jdbcType=VARCHAR}, 
      #{item.href25,jdbcType=VARCHAR}, #{item.href26,jdbcType=TIMESTAMP}, #{item.href27,jdbcType=TIMESTAMP}, 
      #{item.href28,jdbcType=TIMESTAMP}, #{item.href29,jdbcType=TIMESTAMP}, #{item.href30,jdbcType=TIMESTAMP}, 
      #{item.href31,jdbcType=VARCHAR}, #{item.href32,jdbcType=VARCHAR}, #{item.href33,jdbcType=VARCHAR}, #{item.href34,jdbcType=VARCHAR}, 
      #{item.href35,jdbcType=VARCHAR}, #{item.href36,jdbcType=VARCHAR}, #{item.href37,jdbcType=VARCHAR}, #{item.href38,jdbcType=VARCHAR}, 
      #{item.href39,jdbcType=VARCHAR}, #{item.href40,jdbcType=VARCHAR}, #{item.href41,jdbcType=VARCHAR}, #{item.href42,jdbcType=VARCHAR}, 
      #{item.href43,jdbcType=VARCHAR}, #{item.href44,jdbcType=VARCHAR}, #{item.href45,jdbcType=VARCHAR}, #{item.href46,jdbcType=VARCHAR}, 
      #{item.href47,jdbcType=VARCHAR}, #{item.href48,jdbcType=VARCHAR}, #{item.href49,jdbcType=VARCHAR}, #{item.href50,jdbcType=VARCHAR}, 
      #{item.href51,jdbcType=VARCHAR}, #{item.href52,jdbcType=VARCHAR}, #{item.href53,jdbcType=VARCHAR}, #{item.href54,jdbcType=VARCHAR}, 
      #{item.href55,jdbcType=VARCHAR}, #{item.href56,jdbcType=VARCHAR}, #{item.href57,jdbcType=VARCHAR}, #{item.href58,jdbcType=VARCHAR}, 
      #{item.href59,jdbcType=VARCHAR}, #{item.href60,jdbcType=VARCHAR}, #{item.orderlinenumber,jdbcType=VARCHAR}, 
      #{item.externlineno,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.packkey,jdbcType=VARCHAR}, 
      #{item.originalqty,jdbcType=DECIMAL}, #{item.qtyshipedtotal,jdbcType=DECIMAL}, #{item.shippedqty,jdbcType=DECIMAL}, 
      #{item.uom,jdbcType=VARCHAR}, #{item.allowoverpick,jdbcType=DECIMAL}, #{item.preallocatestrategykey,jdbcType=VARCHAR}, 
      #{item.allocatestrategykey,jdbcType=VARCHAR}, #{item.allocatestrategytype,jdbcType=VARCHAR}, 
      #{item.shelflife,jdbcType=DECIMAL}, #{item.rotation,jdbcType=VARCHAR}, #{item.skurotation,jdbcType=VARCHAR}, 
      #{item.lot,jdbcType=VARCHAR}, #{item.lottable01,jdbcType=VARCHAR}, #{item.lottable02,jdbcType=VARCHAR}, 
      #{item.lottable03,jdbcType=VARCHAR}, #{item.lottable04,jdbcType=TIMESTAMP}, #{item.lottable05,jdbcType=TIMESTAMP}, 
      #{item.lottable06,jdbcType=VARCHAR}, #{item.lottable07,jdbcType=VARCHAR}, #{item.lottable08,jdbcType=VARCHAR}, 
      #{item.lottable09,jdbcType=VARCHAR}, #{item.lottable10,jdbcType=VARCHAR}, #{item.lottable11,jdbcType=TIMESTAMP}, 
      #{item.lottable12,jdbcType=TIMESTAMP}, #{item.susr1,jdbcType=VARCHAR}, #{item.susr2,jdbcType=VARCHAR}, 
      #{item.susr3,jdbcType=VARCHAR}, #{item.susr4,jdbcType=VARCHAR}, #{item.susr5,jdbcType=VARCHAR}, #{item.notes2,jdbcType=VARCHAR}, 
      #{item.notes,jdbcType=VARCHAR}, #{item.ref01,jdbcType=VARCHAR}, #{item.ref02,jdbcType=VARCHAR}, #{item.ref03,jdbcType=VARCHAR}, 
      #{item.ref04,jdbcType=VARCHAR}, #{item.ref05,jdbcType=VARCHAR}, #{item.ref06,jdbcType=VARCHAR}, #{item.ref07,jdbcType=VARCHAR}, 
      #{item.ref08,jdbcType=VARCHAR}, #{item.ref09,jdbcType=VARCHAR}, #{item.ref10,jdbcType=VARCHAR}, #{item.ref11,jdbcType=VARCHAR}, 
      #{item.ref12,jdbcType=VARCHAR}, #{item.ref13,jdbcType=VARCHAR}, #{item.ref14,jdbcType=VARCHAR}, #{item.ref15,jdbcType=VARCHAR}, 
      #{item.ref16,jdbcType=VARCHAR}, #{item.ref17,jdbcType=VARCHAR}, #{item.ref18,jdbcType=VARCHAR}, #{item.ref19,jdbcType=VARCHAR}, 
      #{item.ref20,jdbcType=VARCHAR}, #{item.ref21,jdbcType=VARCHAR}, #{item.ref22,jdbcType=VARCHAR}, #{item.ref23,jdbcType=VARCHAR}, 
      #{item.ref24,jdbcType=VARCHAR}, #{item.ref25,jdbcType=VARCHAR}, #{item.ref26,jdbcType=TIMESTAMP}, 
      #{item.ref27,jdbcType=TIMESTAMP}, #{item.ref28,jdbcType=TIMESTAMP}, #{item.ref29,jdbcType=TIMESTAMP}, 
      #{item.ref30,jdbcType=TIMESTAMP}, #{item.ref31,jdbcType=VARCHAR}, #{item.ref32,jdbcType=VARCHAR}, 
      #{item.ref33,jdbcType=VARCHAR}, #{item.ref34,jdbcType=VARCHAR}, #{item.ref35,jdbcType=VARCHAR}, #{item.ref36,jdbcType=VARCHAR}, 
      #{item.ref37,jdbcType=VARCHAR}, #{item.ref38,jdbcType=VARCHAR}, #{item.ref39,jdbcType=VARCHAR}, #{item.ref40,jdbcType=VARCHAR}, 
      #{item.ref41,jdbcType=VARCHAR}, #{item.ref42,jdbcType=VARCHAR}, #{item.ref43,jdbcType=VARCHAR}, #{item.ref44,jdbcType=VARCHAR}, 
      #{item.ref45,jdbcType=VARCHAR}, #{item.ref46,jdbcType=VARCHAR}, #{item.ref47,jdbcType=VARCHAR}, #{item.ref48,jdbcType=VARCHAR}, 
      #{item.ref49,jdbcType=VARCHAR}, #{item.ref50,jdbcType=VARCHAR}, #{item.ref51,jdbcType=VARCHAR}, #{item.ref52,jdbcType=VARCHAR}, 
      #{item.ref53,jdbcType=VARCHAR}, #{item.ref54,jdbcType=VARCHAR}, #{item.ref55,jdbcType=VARCHAR}, #{item.ref56,jdbcType=VARCHAR}, 
      #{item.ref57,jdbcType=VARCHAR}, #{item.ref58,jdbcType=VARCHAR}, #{item.ref59,jdbcType=VARCHAR}, #{item.ref60,jdbcType=VARCHAR}, 
      #{item.adddate,jdbcType=TIMESTAMP}, #{item.editdate,jdbcType=TIMESTAMP}, #{item.editwho,jdbcType=VARCHAR}, 
      #{item.whseid,jdbcType=VARCHAR}, #{item.id,jdbcType=VARCHAR}, #{item.smallamountflag,jdbcType=DECIMAL}, 
      #{item.remark,jdbcType=VARCHAR}, #{item.endtime,jdbcType=TIMESTAMP}, #{item.href61,jdbcType=VARCHAR}, 
      #{item.href62,jdbcType=VARCHAR}, #{item.href63,jdbcType=VARCHAR}, #{item.href64,jdbcType=VARCHAR})
      </foreach>
  </insert>

  <insert id="insertEdiSoSSelective" parameterType="com.zte.domain.model.infor.EdiSoS">
    insert into EDI_SO_S
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialkey != null">
        SERIALKEY,
      </if>

      <if test="logid != null">
        LOGID,
      </if>

      <if test="ediAdddate != null">
        EDI_ADDDATE,
      </if>

      <if test="ediEditdate != null">
        EDI_EDITDATE,
      </if>

      <if test="symbol != null">
        SYMBOL,
      </if>

      <if test="invsymbol != null">
        INVSYMBOL,
      </if>

      <if test="invfaildsymbol != null">
        INVFAILDSYMBOL,
      </if>

      <if test="feedbackflag != null">
        FEEDBACKFLAG,
      </if>

      <if test="feedbackdate != null">
        FEEDBACKDATE,
      </if>

      <if test="orderkey != null">
        ORDERKEY,
      </if>

      <if test="externorderkey != null">
        EXTERNORDERKEY,
      </if>

      <if test="type != null">
        TYPE,
      </if>

      <if test="storerkey != null">
        STORERKEY,
      </if>

      <if test="requestedshipdate != null">
        REQUESTEDSHIPDATE,
      </if>

      <if test="externalorderkey2 != null">
        EXTERNALORDERKEY2,
      </if>

      <if test="consigneekey != null">
        CONSIGNEEKEY,
      </if>

      <if test="ordersid != null">
        ORDERSID,
      </if>

      <if test="hsusr1 != null">
        HSUSR1,
      </if>

      <if test="hsusr2 != null">
        HSUSR2,
      </if>

      <if test="hsusr3 != null">
        HSUSR3,
      </if>

      <if test="hsusr4 != null">
        HSUSR4,
      </if>

      <if test="hsusr5 != null">
        HSUSR5,
      </if>

      <if test="href01 != null">
        HREF01,
      </if>

      <if test="href02 != null">
        HREF02,
      </if>

      <if test="href03 != null">
        HREF03,
      </if>

      <if test="href04 != null">
        HREF04,
      </if>

      <if test="href05 != null">
        HREF05,
      </if>

      <if test="href06 != null">
        HREF06,
      </if>

      <if test="href07 != null">
        HREF07,
      </if>

      <if test="href08 != null">
        HREF08,
      </if>

      <if test="href09 != null">
        HREF09,
      </if>

      <if test="href10 != null">
        HREF10,
      </if>

      <if test="href11 != null">
        HREF11,
      </if>

      <if test="href12 != null">
        HREF12,
      </if>

      <if test="href13 != null">
        HREF13,
      </if>

      <if test="href14 != null">
        HREF14,
      </if>

      <if test="href15 != null">
        HREF15,
      </if>

      <if test="href16 != null">
        HREF16,
      </if>

      <if test="href17 != null">
        HREF17,
      </if>

      <if test="href18 != null">
        HREF18,
      </if>

      <if test="href19 != null">
        HREF19,
      </if>

      <if test="href20 != null">
        HREF20,
      </if>

      <if test="href21 != null">
        HREF21,
      </if>

      <if test="href22 != null">
        HREF22,
      </if>

      <if test="href23 != null">
        HREF23,
      </if>

      <if test="href24 != null">
        HREF24,
      </if>

      <if test="href25 != null">
        HREF25,
      </if>

      <if test="href26 != null">
        HREF26,
      </if>

      <if test="href27 != null">
        HREF27,
      </if>

      <if test="href28 != null">
        HREF28,
      </if>

      <if test="href29 != null">
        HREF29,
      </if>

      <if test="href30 != null">
        HREF30,
      </if>

      <if test="href31 != null">
        HREF31,
      </if>

      <if test="href32 != null">
        HREF32,
      </if>

      <if test="href33 != null">
        HREF33,
      </if>

      <if test="href34 != null">
        HREF34,
      </if>

      <if test="href35 != null">
        HREF35,
      </if>

      <if test="href36 != null">
        HREF36,
      </if>

      <if test="href37 != null">
        HREF37,
      </if>

      <if test="href38 != null">
        HREF38,
      </if>

      <if test="href39 != null">
        HREF39,
      </if>

      <if test="href40 != null">
        HREF40,
      </if>

      <if test="href41 != null">
        HREF41,
      </if>

      <if test="href42 != null">
        HREF42,
      </if>

      <if test="href43 != null">
        HREF43,
      </if>

      <if test="href44 != null">
        HREF44,
      </if>

      <if test="href45 != null">
        HREF45,
      </if>

      <if test="href46 != null">
        HREF46,
      </if>

      <if test="href47 != null">
        HREF47,
      </if>

      <if test="href48 != null">
        HREF48,
      </if>

      <if test="href49 != null">
        HREF49,
      </if>

      <if test="href50 != null">
        HREF50,
      </if>

      <if test="href51 != null">
        HREF51,
      </if>

      <if test="href52 != null">
        HREF52,
      </if>

      <if test="href53 != null">
        HREF53,
      </if>

      <if test="href54 != null">
        HREF54,
      </if>

      <if test="href55 != null">
        HREF55,
      </if>

      <if test="href56 != null">
        HREF56,
      </if>

      <if test="href57 != null">
        HREF57,
      </if>

      <if test="href58 != null">
        HREF58,
      </if>

      <if test="href59 != null">
        HREF59,
      </if>

      <if test="href60 != null">
        HREF60,
      </if>

      <if test="orderlinenumber != null">
        ORDERLINENUMBER,
      </if>

      <if test="externlineno != null">
        EXTERNLINENO,
      </if>

      <if test="sku != null">
        SKU,
      </if>

      <if test="packkey != null">
        PACKKEY,
      </if>

      <if test="originalqty != null">
        ORIGINALQTY,
      </if>

      <if test="qtyshipedtotal != null">
        QTYSHIPEDTOTAL,
      </if>

      <if test="shippedqty != null">
        SHIPPEDQTY,
      </if>

      <if test="uom != null">
        UOM,
      </if>

      <if test="allowoverpick != null">
        ALLOWOVERPICK,
      </if>

      <if test="preallocatestrategykey != null">
        PREALLOCATESTRATEGYKEY,
      </if>

      <if test="allocatestrategykey != null">
        ALLOCATESTRATEGYKEY,
      </if>

      <if test="allocatestrategytype != null">
        ALLOCATESTRATEGYTYPE,
      </if>

      <if test="shelflife != null">
        SHELFLIFE,
      </if>

      <if test="rotation != null">
        ROTATION,
      </if>

      <if test="skurotation != null">
        SKUROTATION,
      </if>

      <if test="lot != null">
        LOT,
      </if>

      <if test="lottable01 != null">
        LOTTABLE01,
      </if>

      <if test="lottable02 != null">
        LOTTABLE02,
      </if>

      <if test="lottable03 != null">
        LOTTABLE03,
      </if>

      <if test="lottable04 != null">
        LOTTABLE04,
      </if>

      <if test="lottable05 != null">
        LOTTABLE05,
      </if>

      <if test="lottable06 != null">
        LOTTABLE06,
      </if>

      <if test="lottable07 != null">
        LOTTABLE07,
      </if>

      <if test="lottable08 != null">
        LOTTABLE08,
      </if>

      <if test="lottable09 != null">
        LOTTABLE09,
      </if>

      <if test="lottable10 != null">
        LOTTABLE10,
      </if>

      <if test="lottable11 != null">
        LOTTABLE11,
      </if>

      <if test="lottable12 != null">
        LOTTABLE12,
      </if>

      <if test="susr1 != null">
        SUSR1,
      </if>

      <if test="susr2 != null">
        SUSR2,
      </if>

      <if test="susr3 != null">
        SUSR3,
      </if>

      <if test="susr4 != null">
        SUSR4,
      </if>

      <if test="susr5 != null">
        SUSR5,
      </if>

      <if test="notes2 != null">
        NOTES2,
      </if>

      <if test="notes != null">
        NOTES,
      </if>

      <if test="ref01 != null">
        REF01,
      </if>

      <if test="ref02 != null">
        REF02,
      </if>

      <if test="ref03 != null">
        REF03,
      </if>

      <if test="ref04 != null">
        REF04,
      </if>

      <if test="ref05 != null">
        REF05,
      </if>

      <if test="ref06 != null">
        REF06,
      </if>

      <if test="ref07 != null">
        REF07,
      </if>

      <if test="ref08 != null">
        REF08,
      </if>

      <if test="ref09 != null">
        REF09,
      </if>

      <if test="ref10 != null">
        REF10,
      </if>

      <if test="ref11 != null">
        REF11,
      </if>

      <if test="ref12 != null">
        REF12,
      </if>

      <if test="ref13 != null">
        REF13,
      </if>

      <if test="ref14 != null">
        REF14,
      </if>

      <if test="ref15 != null">
        REF15,
      </if>

      <if test="ref16 != null">
        REF16,
      </if>

      <if test="ref17 != null">
        REF17,
      </if>

      <if test="ref18 != null">
        REF18,
      </if>

      <if test="ref19 != null">
        REF19,
      </if>

      <if test="ref20 != null">
        REF20,
      </if>

      <if test="ref21 != null">
        REF21,
      </if>

      <if test="ref22 != null">
        REF22,
      </if>

      <if test="ref23 != null">
        REF23,
      </if>

      <if test="ref24 != null">
        REF24,
      </if>

      <if test="ref25 != null">
        REF25,
      </if>

      <if test="ref26 != null">
        REF26,
      </if>

      <if test="ref27 != null">
        REF27,
      </if>

      <if test="ref28 != null">
        REF28,
      </if>

      <if test="ref29 != null">
        REF29,
      </if>

      <if test="ref30 != null">
        REF30,
      </if>

      <if test="ref31 != null">
        REF31,
      </if>

      <if test="ref32 != null">
        REF32,
      </if>

      <if test="ref33 != null">
        REF33,
      </if>

      <if test="ref34 != null">
        REF34,
      </if>

      <if test="ref35 != null">
        REF35,
      </if>

      <if test="ref36 != null">
        REF36,
      </if>

      <if test="ref37 != null">
        REF37,
      </if>

      <if test="ref38 != null">
        REF38,
      </if>

      <if test="ref39 != null">
        REF39,
      </if>

      <if test="ref40 != null">
        REF40,
      </if>

      <if test="ref41 != null">
        REF41,
      </if>

      <if test="ref42 != null">
        REF42,
      </if>

      <if test="ref43 != null">
        REF43,
      </if>

      <if test="ref44 != null">
        REF44,
      </if>

      <if test="ref45 != null">
        REF45,
      </if>

      <if test="ref46 != null">
        REF46,
      </if>

      <if test="ref47 != null">
        REF47,
      </if>

      <if test="ref48 != null">
        REF48,
      </if>

      <if test="ref49 != null">
        REF49,
      </if>

      <if test="ref50 != null">
        REF50,
      </if>

      <if test="ref51 != null">
        REF51,
      </if>

      <if test="ref52 != null">
        REF52,
      </if>

      <if test="ref53 != null">
        REF53,
      </if>

      <if test="ref54 != null">
        REF54,
      </if>

      <if test="ref55 != null">
        REF55,
      </if>

      <if test="ref56 != null">
        REF56,
      </if>

      <if test="ref57 != null">
        REF57,
      </if>

      <if test="ref58 != null">
        REF58,
      </if>

      <if test="ref59 != null">
        REF59,
      </if>

      <if test="ref60 != null">
        REF60,
      </if>

      <if test="adddate != null">
        ADDDATE,
      </if>

      <if test="editdate != null">
        EDITDATE,
      </if>

      <if test="editwho != null">
        EDITWHO,
      </if>

      <if test="whseid != null">
        WHSEID,
      </if>

      <if test="id != null">
        ID,
      </if>

      <if test="smallamountflag != null">
        SMALLAMOUNTFLAG,
      </if>

      <if test="remark != null">
        REMARK,
      </if>

      <if test="endtime != null">
        ENDTIME,
      </if>

      <if test="href61 != null">
        HREF61,
      </if>

      <if test="href62 != null">
        HREF62,
      </if>

      <if test="href63 != null">
        HREF63,
      </if>

      <if test="href64 != null">
        HREF64,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialkey != null">
        #{serialkey,jdbcType=DECIMAL},
      </if>

      <if test="logid != null">
        #{logid,jdbcType=OTHER},
      </if>

      <if test="ediAdddate != null">
        #{ediAdddate,jdbcType=TIMESTAMP},
      </if>

      <if test="ediEditdate != null">
        #{ediEditdate,jdbcType=TIMESTAMP},
      </if>

      <if test="symbol != null">
        #{symbol,jdbcType=DECIMAL},
      </if>

      <if test="invsymbol != null">
        #{invsymbol,jdbcType=DECIMAL},
      </if>

      <if test="invfaildsymbol != null">
        #{invfaildsymbol,jdbcType=DECIMAL},
      </if>

      <if test="feedbackflag != null">
        #{feedbackflag,jdbcType=DECIMAL},
      </if>

      <if test="feedbackdate != null">
        #{feedbackdate,jdbcType=TIMESTAMP},
      </if>

      <if test="orderkey != null">
        #{orderkey,jdbcType=OTHER},
      </if>

      <if test="externorderkey != null">
        #{externorderkey,jdbcType=OTHER},
      </if>

      <if test="type != null">
        #{type,jdbcType=OTHER},
      </if>

      <if test="storerkey != null">
        #{storerkey,jdbcType=OTHER},
      </if>

      <if test="requestedshipdate != null">
        #{requestedshipdate,jdbcType=TIMESTAMP},
      </if>

      <if test="externalorderkey2 != null">
        #{externalorderkey2,jdbcType=OTHER},
      </if>

      <if test="consigneekey != null">
        #{consigneekey,jdbcType=OTHER},
      </if>

      <if test="ordersid != null">
        #{ordersid,jdbcType=OTHER},
      </if>

      <if test="hsusr1 != null">
        #{hsusr1,jdbcType=OTHER},
      </if>

      <if test="hsusr2 != null">
        #{hsusr2,jdbcType=OTHER},
      </if>

      <if test="hsusr3 != null">
        #{hsusr3,jdbcType=OTHER},
      </if>

      <if test="hsusr4 != null">
        #{hsusr4,jdbcType=OTHER},
      </if>

      <if test="hsusr5 != null">
        #{hsusr5,jdbcType=OTHER},
      </if>

      <if test="href01 != null">
        #{href01,jdbcType=OTHER},
      </if>

      <if test="href02 != null">
        #{href02,jdbcType=OTHER},
      </if>

      <if test="href03 != null">
        #{href03,jdbcType=OTHER},
      </if>

      <if test="href04 != null">
        #{href04,jdbcType=OTHER},
      </if>

      <if test="href05 != null">
        #{href05,jdbcType=OTHER},
      </if>

      <if test="href06 != null">
        #{href06,jdbcType=OTHER},
      </if>

      <if test="href07 != null">
        #{href07,jdbcType=OTHER},
      </if>

      <if test="href08 != null">
        #{href08,jdbcType=OTHER},
      </if>

      <if test="href09 != null">
        #{href09,jdbcType=OTHER},
      </if>

      <if test="href10 != null">
        #{href10,jdbcType=OTHER},
      </if>

      <if test="href11 != null">
        #{href11,jdbcType=OTHER},
      </if>

      <if test="href12 != null">
        #{href12,jdbcType=OTHER},
      </if>

      <if test="href13 != null">
        #{href13,jdbcType=OTHER},
      </if>

      <if test="href14 != null">
        #{href14,jdbcType=OTHER},
      </if>

      <if test="href15 != null">
        #{href15,jdbcType=OTHER},
      </if>

      <if test="href16 != null">
        #{href16,jdbcType=OTHER},
      </if>

      <if test="href17 != null">
        #{href17,jdbcType=OTHER},
      </if>

      <if test="href18 != null">
        #{href18,jdbcType=OTHER},
      </if>

      <if test="href19 != null">
        #{href19,jdbcType=OTHER},
      </if>

      <if test="href20 != null">
        #{href20,jdbcType=OTHER},
      </if>

      <if test="href21 != null">
        #{href21,jdbcType=OTHER},
      </if>

      <if test="href22 != null">
        #{href22,jdbcType=OTHER},
      </if>

      <if test="href23 != null">
        #{href23,jdbcType=OTHER},
      </if>

      <if test="href24 != null">
        #{href24,jdbcType=OTHER},
      </if>

      <if test="href25 != null">
        #{href25,jdbcType=OTHER},
      </if>

      <if test="href26 != null">
        #{href26,jdbcType=TIMESTAMP},
      </if>

      <if test="href27 != null">
        #{href27,jdbcType=TIMESTAMP},
      </if>

      <if test="href28 != null">
        #{href28,jdbcType=TIMESTAMP},
      </if>

      <if test="href29 != null">
        #{href29,jdbcType=TIMESTAMP},
      </if>

      <if test="href30 != null">
        #{href30,jdbcType=TIMESTAMP},
      </if>

      <if test="href31 != null">
        #{href31,jdbcType=OTHER},
      </if>

      <if test="href32 != null">
        #{href32,jdbcType=OTHER},
      </if>

      <if test="href33 != null">
        #{href33,jdbcType=OTHER},
      </if>

      <if test="href34 != null">
        #{href34,jdbcType=OTHER},
      </if>

      <if test="href35 != null">
        #{href35,jdbcType=OTHER},
      </if>

      <if test="href36 != null">
        #{href36,jdbcType=OTHER},
      </if>

      <if test="href37 != null">
        #{href37,jdbcType=OTHER},
      </if>

      <if test="href38 != null">
        #{href38,jdbcType=OTHER},
      </if>

      <if test="href39 != null">
        #{href39,jdbcType=OTHER},
      </if>

      <if test="href40 != null">
        #{href40,jdbcType=OTHER},
      </if>

      <if test="href41 != null">
        #{href41,jdbcType=OTHER},
      </if>

      <if test="href42 != null">
        #{href42,jdbcType=OTHER},
      </if>

      <if test="href43 != null">
        #{href43,jdbcType=OTHER},
      </if>

      <if test="href44 != null">
        #{href44,jdbcType=OTHER},
      </if>

      <if test="href45 != null">
        #{href45,jdbcType=OTHER},
      </if>

      <if test="href46 != null">
        #{href46,jdbcType=OTHER},
      </if>

      <if test="href47 != null">
        #{href47,jdbcType=OTHER},
      </if>

      <if test="href48 != null">
        #{href48,jdbcType=OTHER},
      </if>

      <if test="href49 != null">
        #{href49,jdbcType=OTHER},
      </if>

      <if test="href50 != null">
        #{href50,jdbcType=OTHER},
      </if>

      <if test="href51 != null">
        #{href51,jdbcType=OTHER},
      </if>

      <if test="href52 != null">
        #{href52,jdbcType=OTHER},
      </if>

      <if test="href53 != null">
        #{href53,jdbcType=OTHER},
      </if>

      <if test="href54 != null">
        #{href54,jdbcType=OTHER},
      </if>

      <if test="href55 != null">
        #{href55,jdbcType=OTHER},
      </if>

      <if test="href56 != null">
        #{href56,jdbcType=OTHER},
      </if>

      <if test="href57 != null">
        #{href57,jdbcType=OTHER},
      </if>

      <if test="href58 != null">
        #{href58,jdbcType=OTHER},
      </if>

      <if test="href59 != null">
        #{href59,jdbcType=OTHER},
      </if>

      <if test="href60 != null">
        #{href60,jdbcType=OTHER},
      </if>

      <if test="orderlinenumber != null">
        #{orderlinenumber,jdbcType=OTHER},
      </if>

      <if test="externlineno != null">
        #{externlineno,jdbcType=OTHER},
      </if>

      <if test="sku != null">
        #{sku,jdbcType=OTHER},
      </if>

      <if test="packkey != null">
        #{packkey,jdbcType=OTHER},
      </if>

      <if test="originalqty != null">
        #{originalqty,jdbcType=DECIMAL},
      </if>

      <if test="qtyshipedtotal != null">
        #{qtyshipedtotal,jdbcType=DECIMAL},
      </if>

      <if test="shippedqty != null">
        #{shippedqty,jdbcType=DECIMAL},
      </if>

      <if test="uom != null">
        #{uom,jdbcType=OTHER},
      </if>

      <if test="allowoverpick != null">
        #{allowoverpick,jdbcType=DECIMAL},
      </if>

      <if test="preallocatestrategykey != null">
        #{preallocatestrategykey,jdbcType=OTHER},
      </if>

      <if test="allocatestrategykey != null">
        #{allocatestrategykey,jdbcType=OTHER},
      </if>

      <if test="allocatestrategytype != null">
        #{allocatestrategytype,jdbcType=OTHER},
      </if>

      <if test="shelflife != null">
        #{shelflife,jdbcType=DECIMAL},
      </if>

      <if test="rotation != null">
        #{rotation,jdbcType=OTHER},
      </if>

      <if test="skurotation != null">
        #{skurotation,jdbcType=OTHER},
      </if>

      <if test="lot != null">
        #{lot,jdbcType=OTHER},
      </if>

      <if test="lottable01 != null">
        #{lottable01,jdbcType=OTHER},
      </if>

      <if test="lottable02 != null">
        #{lottable02,jdbcType=OTHER},
      </if>

      <if test="lottable03 != null">
        #{lottable03,jdbcType=OTHER},
      </if>

      <if test="lottable04 != null">
        #{lottable04,jdbcType=TIMESTAMP},
      </if>

      <if test="lottable05 != null">
        #{lottable05,jdbcType=TIMESTAMP},
      </if>

      <if test="lottable06 != null">
        #{lottable06,jdbcType=OTHER},
      </if>

      <if test="lottable07 != null">
        #{lottable07,jdbcType=OTHER},
      </if>

      <if test="lottable08 != null">
        #{lottable08,jdbcType=OTHER},
      </if>

      <if test="lottable09 != null">
        #{lottable09,jdbcType=OTHER},
      </if>

      <if test="lottable10 != null">
        #{lottable10,jdbcType=OTHER},
      </if>

      <if test="lottable11 != null">
        #{lottable11,jdbcType=TIMESTAMP},
      </if>

      <if test="lottable12 != null">
        #{lottable12,jdbcType=TIMESTAMP},
      </if>

      <if test="susr1 != null">
        #{susr1,jdbcType=OTHER},
      </if>

      <if test="susr2 != null">
        #{susr2,jdbcType=OTHER},
      </if>

      <if test="susr3 != null">
        #{susr3,jdbcType=OTHER},
      </if>

      <if test="susr4 != null">
        #{susr4,jdbcType=OTHER},
      </if>

      <if test="susr5 != null">
        #{susr5,jdbcType=OTHER},
      </if>

      <if test="notes2 != null">
        #{notes2,jdbcType=OTHER},
      </if>

      <if test="notes != null">
        #{notes,jdbcType=OTHER},
      </if>

      <if test="ref01 != null">
        #{ref01,jdbcType=OTHER},
      </if>

      <if test="ref02 != null">
        #{ref02,jdbcType=OTHER},
      </if>

      <if test="ref03 != null">
        #{ref03,jdbcType=OTHER},
      </if>

      <if test="ref04 != null">
        #{ref04,jdbcType=OTHER},
      </if>

      <if test="ref05 != null">
        #{ref05,jdbcType=OTHER},
      </if>

      <if test="ref06 != null">
        #{ref06,jdbcType=OTHER},
      </if>

      <if test="ref07 != null">
        #{ref07,jdbcType=OTHER},
      </if>

      <if test="ref08 != null">
        #{ref08,jdbcType=OTHER},
      </if>

      <if test="ref09 != null">
        #{ref09,jdbcType=OTHER},
      </if>

      <if test="ref10 != null">
        #{ref10,jdbcType=OTHER},
      </if>

      <if test="ref11 != null">
        #{ref11,jdbcType=OTHER},
      </if>

      <if test="ref12 != null">
        #{ref12,jdbcType=OTHER},
      </if>

      <if test="ref13 != null">
        #{ref13,jdbcType=OTHER},
      </if>

      <if test="ref14 != null">
        #{ref14,jdbcType=OTHER},
      </if>

      <if test="ref15 != null">
        #{ref15,jdbcType=OTHER},
      </if>

      <if test="ref16 != null">
        #{ref16,jdbcType=OTHER},
      </if>

      <if test="ref17 != null">
        #{ref17,jdbcType=OTHER},
      </if>

      <if test="ref18 != null">
        #{ref18,jdbcType=OTHER},
      </if>

      <if test="ref19 != null">
        #{ref19,jdbcType=OTHER},
      </if>

      <if test="ref20 != null">
        #{ref20,jdbcType=OTHER},
      </if>

      <if test="ref21 != null">
        #{ref21,jdbcType=OTHER},
      </if>

      <if test="ref22 != null">
        #{ref22,jdbcType=OTHER},
      </if>

      <if test="ref23 != null">
        #{ref23,jdbcType=OTHER},
      </if>

      <if test="ref24 != null">
        #{ref24,jdbcType=OTHER},
      </if>

      <if test="ref25 != null">
        #{ref25,jdbcType=OTHER},
      </if>

      <if test="ref26 != null">
        #{ref26,jdbcType=TIMESTAMP},
      </if>

      <if test="ref27 != null">
        #{ref27,jdbcType=TIMESTAMP},
      </if>

      <if test="ref28 != null">
        #{ref28,jdbcType=TIMESTAMP},
      </if>

      <if test="ref29 != null">
        #{ref29,jdbcType=TIMESTAMP},
      </if>

      <if test="ref30 != null">
        #{ref30,jdbcType=TIMESTAMP},
      </if>

      <if test="ref31 != null">
        #{ref31,jdbcType=OTHER},
      </if>

      <if test="ref32 != null">
        #{ref32,jdbcType=OTHER},
      </if>

      <if test="ref33 != null">
        #{ref33,jdbcType=OTHER},
      </if>

      <if test="ref34 != null">
        #{ref34,jdbcType=OTHER},
      </if>

      <if test="ref35 != null">
        #{ref35,jdbcType=OTHER},
      </if>

      <if test="ref36 != null">
        #{ref36,jdbcType=OTHER},
      </if>

      <if test="ref37 != null">
        #{ref37,jdbcType=OTHER},
      </if>

      <if test="ref38 != null">
        #{ref38,jdbcType=OTHER},
      </if>

      <if test="ref39 != null">
        #{ref39,jdbcType=OTHER},
      </if>

      <if test="ref40 != null">
        #{ref40,jdbcType=OTHER},
      </if>

      <if test="ref41 != null">
        #{ref41,jdbcType=OTHER},
      </if>

      <if test="ref42 != null">
        #{ref42,jdbcType=OTHER},
      </if>

      <if test="ref43 != null">
        #{ref43,jdbcType=OTHER},
      </if>

      <if test="ref44 != null">
        #{ref44,jdbcType=OTHER},
      </if>

      <if test="ref45 != null">
        #{ref45,jdbcType=OTHER},
      </if>

      <if test="ref46 != null">
        #{ref46,jdbcType=OTHER},
      </if>

      <if test="ref47 != null">
        #{ref47,jdbcType=OTHER},
      </if>

      <if test="ref48 != null">
        #{ref48,jdbcType=OTHER},
      </if>

      <if test="ref49 != null">
        #{ref49,jdbcType=OTHER},
      </if>

      <if test="ref50 != null">
        #{ref50,jdbcType=OTHER},
      </if>

      <if test="ref51 != null">
        #{ref51,jdbcType=OTHER},
      </if>

      <if test="ref52 != null">
        #{ref52,jdbcType=OTHER},
      </if>

      <if test="ref53 != null">
        #{ref53,jdbcType=OTHER},
      </if>

      <if test="ref54 != null">
        #{ref54,jdbcType=OTHER},
      </if>

      <if test="ref55 != null">
        #{ref55,jdbcType=OTHER},
      </if>

      <if test="ref56 != null">
        #{ref56,jdbcType=OTHER},
      </if>

      <if test="ref57 != null">
        #{ref57,jdbcType=OTHER},
      </if>

      <if test="ref58 != null">
        #{ref58,jdbcType=OTHER},
      </if>

      <if test="ref59 != null">
        #{ref59,jdbcType=OTHER},
      </if>

      <if test="ref60 != null">
        #{ref60,jdbcType=OTHER},
      </if>

      <if test="adddate != null">
        #{adddate,jdbcType=TIMESTAMP},
      </if>

      <if test="editdate != null">
        #{editdate,jdbcType=TIMESTAMP},
      </if>

      <if test="editwho != null">
        #{editwho,jdbcType=OTHER},
      </if>

      <if test="whseid != null">
        #{whseid,jdbcType=OTHER},
      </if>

      <if test="id != null">
        #{id,jdbcType=OTHER},
      </if>

      <if test="smallamountflag != null">
        #{smallamountflag,jdbcType=DECIMAL},
      </if>

      <if test="remark != null">
        #{remark,jdbcType=OTHER},
      </if>

      <if test="endtime != null">
        #{endtime,jdbcType=TIMESTAMP},
      </if>

      <if test="href61 != null">
        #{href61,jdbcType=OTHER},
      </if>

      <if test="href62 != null">
        #{href62,jdbcType=OTHER},
      </if>

      <if test="href63 != null">
        #{href63,jdbcType=OTHER},
      </if>

      <if test="href64 != null">
        #{href64,jdbcType=OTHER},
      </if>

    </trim>

  </insert>
  
  <update id="updateEdiSoS" parameterType="com.zte.domain.model.infor.EdiSoS">
     update  plugin.EDI_SO_S set
     REF14=#{ref14,jdbcType=OTHER},
     SHIPPEDQTY=#{shippedqty,jdbcType=DECIMAL}
     where SERIALKEY=#{serialkey,jdbcType=DECIMAL}
     and ref14 is null
  </update>
  <update id="updateEdiSosBatch" parameterType="com.zte.domain.model.infor.EdiSoS">
    <foreach collection="list" item="item" index="index"   separator=";" open="begin" close=";end;">
            update plugin.EDI_SO_S
            <set >
                edi_editdate = sysdate,
                edi_adddate = sysdate,
                SHIPPEDQTY=#{item.shippedqty,jdbcType=DECIMAL},
                REF14=#{item.ref14,jdbcType=OTHER},
                REF13=#{item.ref13,jdbcType=OTHER},
                ID=#{item.id,jdbcType=OTHER},
                QTYSHIPEDTOTAL=#{item.qtyshipedtotal,jdbcType=DECIMAL}
            </set>
            where SERIALKEY = #{item.serialkey,jdbcType=DECIMAL}
            and ref14 is null
        </foreach> 
  </update>

  <delete id="deleteEdiSosBatch" parameterType="com.zte.domain.model.infor.EdiSoS">
    delete plugin.EDI_SO_S
    where SERIALKEY in
    <foreach collection="list" open="(" separator="," close=")" item="item">
        #{item.serialkey,jdbcType=VARCHAR}
    </foreach>
  </delete>
  
  <update id="updateVmiSoPrice" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
        update plugin.edi_so_s
        <set>
            ref14 = to_char(#{item.uniPriceNoTax}),edi_editdate = sysdate,edi_adddate = sysdate
        </set>
        where externalorderkey2 = #{item.billNo,jdbcType=VARCHAR}
        and lottable02 = #{item.itemBarcode,jdbcType=VARCHAR}
        and ref14 is null
    </foreach>   
  </update>

    <update id="insertOnlineFallBackPriceLog" parameterType="com.zte.interfaces.infor.dto.OnlineFallBackPriceLogDTO">
        insert into plugin.online_fall_back_price_log(serialkey, externalorderkey2, externlineno, shippedqty, isrmqty,
        lottable02, adddate, addwho, editdate, editwho) values(plugin.seq_online_fall_back_price_log.nextval,
        #{externalorderkey2,jdbcType=VARCHAR}, #{externlineno,jdbcType=VARCHAR}, #{shippedqty,jdbcType=DECIMAL},
        #{isrmqty,jdbcType=DECIMAL}, #{lottable02,jdbcType=VARCHAR}, sysdate, 'iSRM', sysdate, 'iSRM')
    </update>

</mapper>
