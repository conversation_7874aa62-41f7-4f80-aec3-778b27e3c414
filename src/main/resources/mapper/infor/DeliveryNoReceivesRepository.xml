<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DeliveryNoReceivesRepository">

	<select id="queryDeliveryInfo" parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		select * from
		(
		<foreach collection="whseidList" item="item" index="index" separator="union all">
			select distinct rd.whseid whseId,
			rd.externreceiptkey deliveryNumber,
			r.receiptkey receiptKey,
			rd.toid pkgId,
			zb.supplier_no supplierNo,
			st.company supplierName,
			rz.ref44 deliveryAddress,
			rd.sku itemNo,
			rd.lottable02 itemBarcode,
			r.status asnStatus,
			rd.status detailStatus,
			rd.qtyexpected qtyReceived
			from ${item}.receipt r
			join ${item}.receiptdetail rd on r.receiptkey = rd.receiptkey
			join ${item}.receiptdetail_ref_zte rz on rz.receiptkey = r.receiptkey
			join enterprise.ztebarcode zb on zb.item_barcode = rd.lottable02
			left join enterprise.storer st on zb.supplier_no = st.storerkey
			and st.type = 5
			where rd.qtyexpected > 0
			and rd.externreceiptkey = #{deliveryNumber, jdbcType = VARCHAR}
		</foreach>
		)
	</select>

	<select id="queryExistsByDelivery"  parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="java.lang.Integer">
		select count(*)
		from plugin.delivery_sign_head sh
		join plugin.delivery_sign_detail de on sh.delivery_number = de.delivery_number
		where sh.enabled_flag = 'Y'
		and de.enabled_flag = 'Y'
		and sh.delivery_number = #{deliveryNumber,jdbcType=VARCHAR}
	</select>

	<insert id="insertDeliverySignHead" parameterType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		insert into plugin.delivery_sign_head
		(serialkey,delivery_number,product_base,infor_stock,delivery_address,supplier_no,supplier_name,sign_date,sign_method, created_by,last_updated_by)
		select plugin.seq_delivery_sign_head.nextval,#{deliveryNumber,jdbcType=VARCHAR},
		(select b.attribute1
		from plugin.sys_lookup_types a
		join plugin.sys_lookup_values b on a.lookup_type = b.lookup_type
		and b.enabled_flag = 'Y'
		where a.enabled_flag = 'Y'
		and b.lookup_type = '1000071'
		and b.lookup_meaning = #{whseId,jdbcType=VARCHAR}
		and rownum = 1 ),#{whseId,jdbcType=VARCHAR},#{deliveryAddress,jdbcType=VARCHAR},#{supplierNo,jdbcType=VARCHAR},#{supplierName,jdbcType=VARCHAR},
		sysdate,'现场补录',#{createdBy,jdbcType=VARCHAR},#{createdBy,jdbcType=VARCHAR}
		from dual
	</insert>

	<insert id="insertDeliverySignDetail" parameterType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		insert into plugin.delivery_sign_detail
		(serialkey,delivery_number,receiptkey,item_no,item_barcode,pkg_id,qtyreceived,asn_status,detail_status,
		created_by,last_updated_by)
		select plugin.seq_delivery_sign_detail.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.deliveryNumber,jdbcType=VARCHAR},#{item.receiptKey,jdbcType=VARCHAR},#{item.itemNo,jdbcType=VARCHAR},#{item.itemBarcode,jdbcType=VARCHAR},
			#{item.pkgId,jdbcType=VARCHAR}, #{item.qtyReceived,jdbcType=DECIMAL},#{item.asnStatus,jdbcType=VARCHAR},#{item.detailStatus,jdbcType=VARCHAR}，
			#{item.createdBy,jdbcType=VARCHAR},#{item.createdBy,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>

	<select id="queryDeliverySignInfo" parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		select distinct sh.delivery_number deliveryNumber,
		sh.infor_stock whseId,
		sh.product_base productBase,
		decode(sh.is_archive,'Y','是','否') isArchive,
		sh.supplier_no supplierNo,
		sh.supplier_name supplierName,
		sh.sign_date signDate,
		decode(sh.is_reservation,'Y','有','无') isReservation,
		sh.sign_method signMethod,
		to_char(wm_concat(distinct to_char(de.asn_status))) asnStatus
		from plugin.delivery_sign_head sh
		join plugin.delivery_sign_detail de on sh.delivery_number =
		de.delivery_number
		where sh.enabled_flag = 'Y'
		and de.enabled_flag = 'Y'
		and sh.delivery_number = #{deliveryNumber, jdbcType = VARCHAR}
		group by sh.delivery_number,
		sh.infor_stock,
		sh.product_base,
		sh.is_archive,
		sh.supplier_no,
		sh.supplier_name,
		sh.sign_date,
		sh.sign_method,
		sh.is_reservation
	</select>

	<select id="getDeliverySignDetailTotal" parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.delivery_sign_head sh
		join plugin.delivery_sign_detail de on sh.delivery_number =
		de.delivery_number
		where sh.enabled_flag = 'Y'
		and de.enabled_flag = 'Y'
		<if test="deliveryNumber != null and deliveryNumber !='' ">
			and sh.delivery_number  = #{deliveryNumber,jdbcType=VARCHAR}
		</if>
		<if test="pkgId != null and pkgId !='' ">
			and de.pkg_id  = #{pkgId,jdbcType=VARCHAR}
		</if>
		<if test="whseidList != null and whseidList.size > 0">
			and sh.infor_stock in (
			<foreach collection="whseidList" item="whseid" index="index" separator=",">
				#{whseid,jdbcType=VARCHAR}
			</foreach>
			)
		</if>
		<if test="supplierName != null and supplierName !='' ">
			and sh.supplier_name like concat(#{supplierName,jdbcType=VARCHAR},'%')
		</if>
		<if test="isCompleted != null and isCompleted !='' ">
			and de.is_completed  = #{isCompleted,jdbcType=VARCHAR}
		</if>
		<if test="isReservation != null and isReservation !='' ">
			and sh.is_reservation  = #{isReservation,jdbcType=VARCHAR}
		</if>
		<if test="isArchive != null and isArchive !='' ">
			and sh.is_archive  = #{isArchive,jdbcType=VARCHAR}
		</if>
		<if test="signStart != null and signStart !='' ">
			and sh.sign_date <![CDATA[>=]]> to_date(#{signStart,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="signEnd != null and signEnd !='' ">
			and sh.sign_date <![CDATA[<=]]> to_date(#{signEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<select id="getDeliverySignDetailVo" parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		select B.* from (
		select A.*,rownum rn from(
		select sh.delivery_number deliveryNumber,
		sh.infor_stock whseId,
		sh.product_base productBase,
		de.receiptkey receiptKey,
		de.item_no itemNo,
		de.item_barcode itemBarcode,
		de.pkg_id pkgId,
		de.qtyreceived qtyReceived,
		sh.supplier_no supplierNo,
		sh.supplier_name supplierName,
		sh.delivery_address deliveryAddress,
		sh.sign_date signDate,
		(select m.description
		from enterprise.codelkup m
		where m.listname = 'RECSTATUS'
		and m.code = de.asn_status
		and rownum = 1) asnStatus,
		(select m.description
		from enterprise.codelkup m
		where m.listname = 'RECSTATUS'
		and m.code = de.detail_status
		and rownum = 1) detailStatus,
		sh.sign_method signMethod,
		decode(sh.is_reservation,'Y','有','无') isReservation,
		decode(de.is_completed,'Y','是','否') isCompleted,
		de.asn_status asnCode
		from plugin.delivery_sign_head sh
		join plugin.delivery_sign_detail de on sh.delivery_number =
		de.delivery_number
		where sh.enabled_flag = 'Y'
		and de.enabled_flag = 'Y'
		<if test="deliveryNumber != null and deliveryNumber !='' ">
			and sh.delivery_number  = #{deliveryNumber,jdbcType=VARCHAR}
		</if>
		<if test="pkgId != null and pkgId !='' ">
			and de.pkg_id  = #{pkgId,jdbcType=VARCHAR}
		</if>
		<if test="whseidList != null and whseidList.size > 0">
			and sh.infor_stock in (
			<foreach collection="whseidList" item="whseid" index="index" separator=",">
				#{whseid,jdbcType=VARCHAR}
			</foreach>
			)
		</if>
		<if test="supplierName != null and supplierName !='' ">
			and sh.supplier_name like concat(#{supplierName,jdbcType=VARCHAR},'%')
		</if>
		<if test="isCompleted != null and isCompleted !='' ">
			and de.is_completed  = #{isCompleted,jdbcType=VARCHAR}
		</if>
		<if test="isReservation != null and isReservation !='' ">
			and sh.is_reservation  = #{isReservation,jdbcType=VARCHAR}
		</if>
		<if test="isArchive != null and isArchive !='' ">
			and sh.is_archive  = #{isArchive,jdbcType=VARCHAR}
		</if>
		<if test="signStart != null and signStart !='' ">
			and sh.sign_date <![CDATA[>=]]> to_date(#{signStart,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="signEnd != null and signEnd !='' ">
			and sh.sign_date <![CDATA[<=]]> to_date(#{signEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		order by sh.sign_date desc
		)A )B
		where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  b.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  b.rn &lt;= 500000
		</if>

	</select>
	<select id="getEmptyLocByWhseId" parameterType="com.zte.interfaces.infor.dto.DeliverySignDTO" resultType="com.zte.domain.model.infor.DeliverySignDetailDTO">
		select * from
		(
		<foreach collection="whseidList" item="item" index="index" separator="union all">
			select distinct l.whseid whseId, l.loc
			from ${item}.loc l
			where not exists
			(select 1
			from ${item}.lotxlocxid lt
			where lt.qty > 0
			and lt.loc = l.loc)
			and l.loc in
			<foreach collection="locList" open="(" separator="," close=")" item="item">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</foreach>
		)
	</select>

	<select id="queryWhseByBase" resultType="java.lang.String">
		select distinct b.lookup_meaning
		from plugin.sys_lookup_types a
		join plugin.sys_lookup_values b on a.lookup_type = b.lookup_type
		and b.enabled_flag = 'Y'
		where a.enabled_flag = 'Y'
		and a.lookup_type = #{lookupType,jdbcType=VARCHAR}
		and b.attribute2 = #{productBase,jdbcType=VARCHAR}
	</select>
	<select id="getNotReceivedInfo" resultType="com.zte.domain.model.infor.DeliveryDetailDTO">
		select #{whseId} whseId,
			s.toid toId,
			s.TOLOC loc,
			'未收货' stateType,
			s.QTYRECEIVED qtyReceived
		from ${whseId}.RECEIPTDETAIL s
		where
			s.CONDITIONCODE = #{action,jdbcType=VARCHAR}
			and s.STATUS &lt; '12'
			and s.toid IN
		<foreach collection="toIdList" open="(" separator="," close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
	<select id="getUnderInspectInfo" resultType="com.zte.domain.model.infor.DeliveryDetailDTO">
		select #{whseId} whseId,
			s.toid toId,
			s.TOLOC loc,
			'检验中' stateType,
			s.QTYRECEIVED qtyReceived
		from ${whseId}.RECEIPTDETAIL s
		where
			s.CONDITIONCODE = #{action,jdbcType=VARCHAR}
			and s.STATUS = '12'
			and s.toid IN
		<foreach collection="toIdList" open="(" separator="," close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
	<select id="getUnqualifiedInfo" resultType="com.zte.domain.model.infor.DeliveryDetailDTO">
		select #{whseId} whseId,
			s.toid toId,
			s.TOLOC loc,
			'不合格' stateType,
			s.QTYRECEIVED qtyReceived
		from ${whseId}.RECEIPTDETAIL s
		where
			s.CONDITIONCODE = #{action,jdbcType=VARCHAR}
		and s.STATUS > '12'
		and s.QCQTYREJECTED > 0
		and s.toid IN
		<foreach collection="toIdList" open="(" separator="," close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
	<select id="getQualifiedInfo" resultType="com.zte.domain.model.infor.DeliveryDetailDTO">
		select #{whseId} whseId,
			s.toid toId,
			s.TOLOC loc,
			'已合格' stateType,
			s.QTYRECEIVED qtyReceived
		from ${whseId}.RECEIPTDETAIL s
		where
			s.CONDITIONCODE = #{action,jdbcType=VARCHAR}
		and s.STATUS > '12'
		and s.QCQTYREJECTED = 0
		and s.QCQTYINSPECTED > 0
		and s.toid IN
		<foreach collection="toIdList" open="(" separator="," close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
	<select id="getPcbOverdueInfo" resultType="com.zte.domain.model.infor.PcbOverdueInfoDTO">
		SELECT
			sku itemNo,
			item_barcode itemBarcode,
			PRODUCT_DATE productDate,
			WETLEVEL wetLevel,
			SHELFLIFE shelfLife,
			PRODUCT_DATE + (SHELFLIFE * 30) AS effectiveDate
		FROM
			enterprise.ZTEBARCODE
		where
			item_barcode = #{itemBarcode,jdbcType=VARCHAR}
	</select>
</mapper>