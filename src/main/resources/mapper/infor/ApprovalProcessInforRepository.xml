<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ApprovalProcessInforRepository">

    <update id="updateHoldInventroyRecord"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
      update plugin.inventoryhold_record d
        set d.hold_status = 1,
        d.approved_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        d.approved_result = #{approvedResult,jdbcType=VARCHAR},
        d.approved_time = sysdate
        where exists (select 1
        from plugin.hold_mobileapproval_log l
        where l.whseid = d.whseid
        and l.item_barcode = d.item_barcode
        and l.hold_reason = d.hold_reason
        and l.businessid = #{billNO,jdbcType=VARCHAR})
      and d.hold_status = 2
    </update>

  <update id="updateHoldInventroyRecordLog"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
    update plugin.hold_mobileapproval_log d
    set d.approved_by = #{lastUpdatedBy,jdbcType=VARCHAR},
    d.approved_result = #{approvedResult,jdbcType=VARCHAR},
    d.approved_time = sysdate
    where d.businessid = #{billNO,jdbcType=VARCHAR}
  </update>

  <delete id="deleteHoldInventroyRecord" parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
    delete from plugin.inventoryhold_record d
    where exists (select 1
    from plugin.hold_mobileapproval_log l
    where l.whseid = d.whseid
    and l.item_barcode = d.item_barcode
    and l.hold_reason = d.hold_reason
    and l.businessid = #{billNO,jdbcType=VARCHAR})
    and d.hold_status = #{holdStatus,jdbcType=DECIMAL}
  </delete>

  <update id="updateRemoveHoldInventroyRecord"  parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
    update plugin.inventoryhold_record d
    set d.hold_status = #{holdStatus,jdbcType=DECIMAL},
    d.approved_by = #{lastUpdatedBy,jdbcType=VARCHAR},
    d.approved_result = #{approvedResult,jdbcType=VARCHAR},
    d.approved_time = sysdate
    where exists (select 1
    from plugin.hold_mobileapproval_log l
    where l.whseid = d.whseid
    and l.item_barcode = d.item_barcode
    and l.hold_reason = d.hold_reason
    and l.businessid = #{billNO,jdbcType=VARCHAR})
    and d.hold_status = 3
  </update>

</mapper>