<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InventoryTransRepository">
    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO PLUGIN.ZMS_INVENTORY_TRANS (
            SERIALKEY, tran_order_no, order_line_no, inventory_category_name,
            brand_name, category_name, item_name, mpn, batch,
            inventory_space, tran_quantity, tran_category_name,
            tran_date, crea_date, quantity_type, created_by,
            creation_date, last_updated_by, last_update_date, enabled_flag
        )
        SELECT PLUGIN.ZMS_INVENTORY_TRANS_S.NEXTVAL, A.* FROM (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT
            #{item.tranOrderNo} AS tran_order_no,
            #{item.orderLineNo} AS order_line_no,
            #{item.inventoryCategoryName} AS inventory_category_name,
            #{item.brandName} AS brand_name,
            #{item.categoryName} AS category_name,
            #{item.itemName} AS item_name,
            #{item.mpn} AS mpn,
            #{item.batch} AS batch,
            #{item.inventorySpace} AS inventory_space,
            #{item.tranQuantity} AS tran_quantity,
            #{item.tranCategoryName} AS tran_category_name,
            to_date(#{item.tranDate,jdbcType=VARCHAR}, 'yyyy-mm-dd hh24:mi:ss') AS tran_date,
            to_date(#{item.creaDate,jdbcType=VARCHAR}, 'yyyy-mm-dd hh24:mi:ss') AS crea_date,
            #{item.quantityType} AS quantity_type,
            'B2B' AS created_by,
            SYSDATE AS creation_date,
            'B2B' AS last_updated_by,
            SYSDATE AS last_update_date,
            'Y' AS enabled_flag
            FROM DUAL
        </foreach>
        ) A
    </insert>

</mapper>