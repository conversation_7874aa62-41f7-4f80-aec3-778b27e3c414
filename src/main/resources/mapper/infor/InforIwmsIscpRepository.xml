<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforIwmsIscpRepository">
	<resultMap id="EdiIscpDataMap" type="com.zte.action.iscpedi.model.EdiIscpData">
		<result column="serialKey" jdbcType="BIGINT" property="serialKey" />
		<result column="deliBillNo" jdbcType="VARCHAR" property="deliBillNo" />
		<result column="rowNo" jdbcType="VARCHAR" property="rowNo" />
		<result column="operateType" jdbcType="VARCHAR" property="operateType" />
		<result column="sourceTable" jdbcType="VARCHAR" property="sourceTable" />
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
		<result column="actualQty" jdbcType="DECIMAL" property="actualQty" />
		<result column="anticipatedQty" jdbcType="DECIMAL" property="anticipatedQty" />
		<result column="boxNo" jdbcType="VARCHAR" property="boxNo" />
		<result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
		<result column="sku" jdbcType="VARCHAR" property="sku" />
		<result column="operateTime" jdbcType="VARCHAR" property="operateTime" />
		<result column="batchNo" jdbcType="VARCHAR" property="batchNo" />

	</resultMap>
	
	<resultMap id="BoxLabelDTODataMap" type="com.zte.interfaces.infor.dto.BoxLabelDTO">
		<result column="deliverTo" jdbcType="VARCHAR" property="deliverTo" />
		<result column="externalSupplierNo" jdbcType="VARCHAR" property="externalSupplierNo" />
		<result column="classGroup" jdbcType="VARCHAR" property="classGroup" />
		<result column="produceNo" jdbcType="VARCHAR" property="produceNo" />
		<result column="dropId" jdbcType="VARCHAR" property="dropId" />
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
		<result column="externalorderkey2" jdbcType="VARCHAR" property="externalorderkey2" />
	</resultMap>
	<select id="selectEdiPorData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select serialkey, externreceiptkey deliBillNo, externlineno rowNo,
      	'08' operateType, 'EDI_PO_R' sourceTable, whseid, qtyreceived actualQty, qtyexpected anticipatedQty, '' boxNo, lottable02,sku,
      	to_char(edi_editdate,'yyyy-MM-dd HH24:mi:ss') operateTime, '' batchNo
      	from PLUGIN.EDI_PO_R WHERE
		SERIALKEY IN
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp.serialkey}
        </foreach>
	</select>

	
	<select id="selectEdiPosData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select serialkey, externreceiptkey deliBillNo, externlineno rowNo,
      	'01' operateType, 'EDI_PO_S' sourceTable, whseid, qtystore actualQty, qtyexpected anticipatedQty, '' boxNo, lottable02,sku,
      	to_char(edi_editdate,'yyyy-MM-dd HH24:mi:ss') operatetime, '' batchNo
      	from PLUGIN.EDI_PO_S WHERE SERIALKEY IN 
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp.serialkey}
        </foreach> 
	</select>	
	
	<select id="selectEdiSosData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select serialkey, externalorderkey2 deliBillNo, externlineno rowNo,
      	'03' operateType, 'EDI_SO_S' sourceTable, whseid, shippedqty actualQty, originalqty anticipatedQty, '' boxNo, lottable02,sku,
      	to_char(edi_editdate,'yyyy-MM-dd HH24:mi:ss') operatetime, '' batchNo
      	from PLUGIN.EDI_SO_S WHERE SERIALKEY IN 
		<foreach collection="list" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp.serialkey}
        </foreach> 
	</select>
	
	<select id="selectEdiSosTransferData" parameterType="java.util.List" resultMap="EdiIscpDataMap">
		select A.serialkey serialkey, A.EXTERNALORDERKEY2 deliBillNo, '1' rowNo,
      	'03' operateType, 'EDI_SO_S_TRANSFER' sourceTable, A.WHSEID whseid, A.QTY actualQty, A.QTY anticipatedQty, '' boxNo, lottable02,sku,
      	to_char(editdate,'yyyy-MM-dd HH24:mi:ss') operatetime, '' batchNo
      	from PLUGIN.V_PU_INFOR_TRANSFERSTOCK_ISCP A where 1=1 and
      	<foreach collection="list" item="tmp" open="(" separator="or" close=")">
			(A.SERIALKEY = #{tmp.serialkey} and A.WHSEID = #{tmp.whseid})
		</foreach>
	</select>	
	
	<resultMap id="IscpEdiLogMap" type="com.zte.action.iscpedi.model.IscpEdiLog">
		<result column="serialkey" jdbcType="BIGINT" property="serialkey" />
		<result column="externkey" jdbcType="VARCHAR" property="externkey" />
		<result column="externlineno" jdbcType="VARCHAR" property="externlineno" />
		<result column="operateType" jdbcType="VARCHAR" property="operateType" />
		<result column="sourceTable" jdbcType="VARCHAR" property="sourceTable" />
		<result column="whseid" jdbcType="VARCHAR" property="whseid" />
		<result column="isSend" jdbcType="INTEGER" property="isSend" />
		<result column="sendTimes" jdbcType="INTEGER" property="sendTimes" />
	</resultMap>
	
			
	<select id="getIscpEdiLog" resultMap="IscpEdiLogMap">
      	<if test="externkey == null or externkey == ''  or sourceTable == null  or sourceTable == ''">
			select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
 			from (select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
      		from PLUGIN.ISCP_EDI_LOG where isSend in (-1, 2) and <![CDATA[sendTimes < 4]]>
      		 order by sourceTable,externkey,externlineno )
			WHERE <![CDATA[rownum < #{avaiableSize, jdbcType=INTEGER}]]>
        </if>	
      	<if test="externkey != null and externkey != '' and sourceTable != null  and sourceTable != ''">
      		select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
      		from PLUGIN.ISCP_EDI_LOG
			where externkey = #{externkey, jdbcType=VARCHAR} AND sourceTable = #{sourceTable, jdbcType=VARCHAR}
        </if>  
	</select>

	<update id="updateIscpEdiLogTimes" parameterType="com.zte.action.iscpedi.model.IscpEdiLog">
		update plugin.iscp_edi_log set
		<if test="isSend != null">
			isSend = #{isSend, jdbcType=BIGINT},
		</if>
		sendTimes = sendTimes + 1,
		updateDate = SYSDATE
		WHERE externkey = #{externkey, jdbcType=VARCHAR}
		AND sourceTable = #{sourceTable, jdbcType=VARCHAR}
		and operatetype = #{operateType, jdbcType=VARCHAR}
		<if test="sourcekey != null and sourcekey != '' ">
			and sourcekey = #{sourcekey, jdbcType=VARCHAR}
		</if>
	</update>

	<update id="updateIscpEdiLog" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update plugin.iscp_edi_log set
			isSend = #{info.isSend, jdbcType=INTEGER},
			sendTimes = sendTimes + 1,
			requestParam = #{info.requestParam, jdbcType=VARCHAR},
			updateDate = TO_DATE(#{info.updateDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
			WHERE serialkey = #{info.serialkey, jdbcType=BIGINT} AND sourceTable = #{info.sourceTable, jdbcType=VARCHAR}
			and isSend <![CDATA[<>]]> 0
		</foreach>
	</update>

	<update id="updateIscpEdiLogResult" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update plugin.iscp_edi_log set
			isSend = #{info.isSend, jdbcType=INTEGER},
			updateDate = TO_DATE(#{info.updateDate, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
			WHERE serialkey = #{info.serialkey, jdbcType=BIGINT} AND sourceTable = #{info.sourceTable, jdbcType=VARCHAR}
		</foreach>
	</update>

		
	<update id="updateListSendState" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update plugin.iscp_edi_log 
			<set>
				<if test="info.issend!=31">
					isSend = 1,
				</if>
				<if test="info.issend==31">
					isSend = 31,
				</if>
			updateDate = SYSDATE
			</set>
			<where>
			serialkey = #{info.serialkey, jdbcType=BIGINT} AND (
				sourceTable = #{info.sourceTable, jdbcType=VARCHAR}
				OR sourceTable = #{info.transferSourceTable, jdbcType=VARCHAR}
			)
			and whseid = #{info.whseid, jdbcType=VARCHAR}
			</where>
		</foreach>
	</update> 	
			
	<select id="getIscpEdiEntryArgs" resultType="java.lang.String">
		select b.lookup_meaning from plugin.sys_lookup_types a 
           join plugin.sys_lookup_values b on a.lookup_type = b.lookup_type and b.enabled_flag = 'Y'
   			where a.enabled_flag = 'Y' and a.lookup_type = '1000011'
  	</select>	
	<select id="getDicList" resultType="java.lang.String">
		select b.lookup_meaning from plugin.sys_lookup_types a
           join plugin.sys_lookup_values b on a.lookup_type = b.lookup_type and b.enabled_flag = 'Y'
   			where a.enabled_flag = 'Y' and a.lookup_type = #{lookupType}
  	</select>

	<update id="updateExceptionIscpEdiLog">
		UPDATE  PLUGIN.ISCP_EDI_LOG T SET T.ISSEND = -1,T.UPDATEDATE = SYSDATE WHERE T.ISSEND = 1 AND <![CDATA[T.UPDATEDATE  < SYSDATE - 1 ]]> 
	</update>

	<select id="selectReelIDInventoryList" parameterType="com.zte.interfaces.infor.dto.ReelidInventoryInputDTO" resultType="com.zte.interfaces.infor.dto.ReelidInventoryOutDTO">
		select a.SKU sku,a.lottable02 lottable02,a.LOT lot,a.ID id,a.WHSEID whseId,a.LOC loc,a.LOTTABLE06 lottable06,a.SERIALNUMBER serialNumber,a.QTY qty,a.ADDDATE addDate,a.addWho,a.EDITDATE editDate,a.EDITWHO editWho
		from (
		<foreach collection="whseIdList" item="item" index="index" separator="union all">
		SELECT st.SKU sku,lt.lottable02 lottable02,st.LOT lot,st.ID id,st.WHSEID whseId,st.LOC loc,lt.LOTTABLE06 lottable06,st.SERIALNUMBER serialNumber,st.QTY qty,lt.ADDDATE addDate,lt.addWho,lt.EDITDATE editDate,lt.EDITWHO editWho
		FROM ${item}.serialinventory st
		join ${item}.lotattribute lt
		on lt.lot = st.lot
		and st.sku = lt.sku
		and lt.storerkey = 'ZTE'
		</foreach>
		) a where 1 = 1
		<if test = "sku != null and sku != '' " >
			and  a.sku=#{sku,jdbcType=VARCHAR}
		</if>
		<if test = "lottable02 != null and lottable02 != '' " >
			and  a.LOTTABLE02=#{lottable02,jdbcType=VARCHAR}
		</if>
		<if test = "whseId != null and whseId != '' " >
			and  a.WHSEID=#{whseId,jdbcType=VARCHAR}
		</if>
		<if test = "lot != null and lot.size()>0"  >
			and  a.LOT in
			<foreach item="item" index="index" collection="lot" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test = "id != null and id != '' " >
			and  a.ID=#{id,jdbcType=VARCHAR}
		</if>
		<if test = "loc != null and loc != '' " >
			and  a.LOC=#{loc,jdbcType=VARCHAR}
		</if>
		<if test = "serialNumber != null and serialNumber != '' " >
			and  a.SERIALNUMBER=#{serialNumber,jdbcType=VARCHAR}
		</if>
		<if test = "beginTime != null and beginTime != '' " >
			and  a.editdate &gt;= TO_DATE(#{beginTime},'YYYY-MM-DD HH24:MI:SS')
		</if>
		<if test = "endTime != null and endTime != '' " >
			and  a.editdate &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
		</if>
	</select>
	
	<select id="getBoxLabelInfor" parameterType="com.zte.interfaces.infor.dto.BoxLabelDTO" resultMap="BoxLabelDTODataMap">
      	select distinct 
      			c.c_company         as deliverTo,
                c.ref20             as externalSupplierNo,
                c.classgroup        as classGroup,
                c.susr1             as produceNo,
                c.dropid            as dropId,
                c.externalorderkey2 as externalorderkey2
		  from plugin.v_pu_infor_dropid c
		 where 1=1
		   and c.dropid = #{dropId,jdbcType=VARCHAR} 
		   <if test = "produceNo != null and produceNo != '' " >
			and c.susr1 = #{produceNo,jdbcType=VARCHAR}
		   </if>
		   <if test = "deliverTo != null and deliverTo != '' " >
			and c.c_company = #{deliverTo,jdbcType=VARCHAR}
		   </if>
		   <if test = "externalorderkey2 != null and externalorderkey2 != '' " >
			and c.externalorderkey2 = #{externalorderkey2,jdbcType=VARCHAR}
		   </if>
		   <if test = "classGroup != null and classGroup != '' " >
			and c.classgroup = #{classGroup,jdbcType=VARCHAR}
		   </if>
	</select>
    <select id="getIscpEdiLogBySerialKey" resultType="com.zte.action.iscpedi.model.IscpEdiLog">
		select serialkey, externkey, externlineno, operateType, sourceTable, whseid, isSend, sendTimes
		from PLUGIN.ISCP_EDI_LOG
		where
		1=1
		<if test="serialKeyList!=null and  serialKeyList.size!=0">
			and serialkey in
			<foreach item="serialKey" index="index" collection="serialKeyList" open="(" separator="," close=")">
				#{serialKey}
			</foreach>

		</if>
		<if test="isSend!=null and isSend!='' ">
			and isSend in (31,32,3)
		</if>
		<if test="sourceTable!=null and sourceTable!=''">
			and sourceTable = #{sourceTable}
		</if>
	</select>
    <select id="getIscpEdiPorsLog" resultType="com.zte.action.iscpedi.model.IscpEdiLog">
		select isl.serialkey, isl.externkey, isl.externlineno, isl.operateType, isl.sourceTable, isl.whseid, isl.isSend, isl.sendTimes
		from     PLUGIN.ISCP_EDI_LOG  isl
		left join plugin.edi_po_r epr on epr.serialkey = isl.serialkey
		left join plugin.edi_po_s eps
		on epr.externreceiptkey = eps.externreceiptkey and epr.externlineno = eps.externlineno
		and epr.receiptkey = eps.receiptkey
		where eps.serialkey = #{serialkey} and isl.sourcetable ='EDI_PO_R'
	</select>


	<update id="updateIscpEdiLogBySerialKey" >
		update PLUGIN.ISCP_EDI_LOG set
		isSend = #{isSend},
		<choose>
			<when test="isSend==3">sendTimes = sendTimes+1,</when>
			<otherwise>sendTimes = 0,</otherwise>
		</choose>
		 updateDate = SYSDATE
		where
		 serialkey  in
		<foreach item="serialKey" index="index" collection="serialKeyList" open="(" separator="," close=")">
			#{serialKey}
		</foreach>
		<if test="sourceTable!=null and sourceTable!=''">
			and sourceTable = #{sourceTable}
		</if>
	</update>
	<select id="getBillToWarn" resultType="java.lang.Integer">
		select count(1)
		from PLUGIN.ISCP_EDI_LOG
		where 1=1
		<if test='isIscpReturn =="0"'>
			and (issend in (1,2) and sendtimes >=4 )
		</if>
		<if test='isIscpReturn !="0"'>
			and (issend in (3,31,32)  and adddate <![CDATA[<]]> sysdate-1)
		</if>
	</select>
</mapper>