<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InventoryholdRecordRepository">

	<insert id="insertInventoryholdRecordLog" parameterType="java.util.List">
		insert into plugin.inventoryhold_record_log (serialkey, item_no, item_barcode, supply_no, supply_name, apply_by_no, apply_by_name,
		apply_dept_no, apply_dept_name, responsible_by_no, responsible_by_name, responsible_dept_no, responsible_dept_name, hold_reason,
		hold_reason_desc, addwho, adddate, editwho, editdate,HOLD_STATUS)
		select plugin.seq_inventoryhold_record_log.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.itemNo,jdbcType=VARCHAR}, #{item.itembarcode,jdbcType=VARCHAR}, #{item.supplyNo,jdbcType=VARCHAR},
			#{item.supplyName,jdbcType=VARCHAR}, #{item.applyByNo,jdbcType=VARCHAR}, #{item.applyByName,jdbcType=VARCHAR}, #{item.applyDeptNo,jdbcType=VARCHAR},
			#{item.applyDeptName,jdbcType=VARCHAR}, #{item.responsibleByNo,jdbcType=VARCHAR}, #{item.responsibleByName,jdbcType=VARCHAR},
			#{item.responsibleDeptNo,jdbcType=VARCHAR}, #{item.responsibleDeptName,jdbcType=VARCHAR}, #{item.holdReason,jdbcType=VARCHAR},
			#{item.holdReasonDsc,jdbcType=VARCHAR}, 'ISCP' addwho, sysdate as adddate, 'ISCP' editwho, sysdate as editdate,
			#{item.holdStatus,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>

	<select id="getInventoryholdRecordNum" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.inventoryhold_record
		where 1=1
		<if test="whseid != null and whseid !='' ">
			and whseid = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcodeList != null and itemBarcodeList.size() > 0">
			and item_barcode in
			<foreach collection="itemBarcodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and hold_reason = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="serialkeyList != null and serialkeyList.size() > 0">
			and serialkey in
			<foreach collection="serialkeyList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="holdStatus != null">
			and hold_status = #{holdStatus,jdbcType=DECIMAL}
		</if>
	</select>

	<insert id="insertInventoryholdRecord" parameterType="java.util.List">
		insert into plugin.inventoryhold_record (serialkey, whseid, item_no, item_barcode, supply_no, supply_name, apply_by_no, apply_by_name,
		apply_dept_no, apply_dept_name, responsible_by_no, responsible_by_name, responsible_dept_no, responsible_dept_name, hold_status,
		hold_reason, remark, infor_status, infor_fail_times, addwho, adddate, editwho, editdate, edit_dept_no, edit_dept_name)
		select plugin.seq_inventoryhold_record.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			upper(#{item.whseid,jdbcType=VARCHAR}), #{item.itemNo,jdbcType=VARCHAR}, #{item.itemBarcode,jdbcType=VARCHAR}, #{item.supplyNo,jdbcType=VARCHAR},
			#{item.supplyName,jdbcType=VARCHAR}, #{item.applyByNo,jdbcType=VARCHAR}, #{item.applyByName,jdbcType=VARCHAR}, #{item.applyDeptNo,jdbcType=VARCHAR},
			#{item.applyDeptName,jdbcType=VARCHAR}, #{item.responsibleByNo,jdbcType=VARCHAR}, #{item.responsibleByName,jdbcType=VARCHAR},
			#{item.responsibleDeptNo,jdbcType=VARCHAR}, #{item.responsibleDeptName,jdbcType=VARCHAR}, #{item.holdStatus,jdbcType=DECIMAL},
			#{item.holdReason,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.inforStatus,jdbcType=DECIMAL}, #{item.inforFailTimes,jdbcType=DECIMAL},
			#{item.addwho,jdbcType=VARCHAR}, sysdate as adddate, #{item.editwho,jdbcType=VARCHAR}, sysdate as editdate,
			#{item.editDeptNo,jdbcType=VARCHAR},#{item.editDeptName,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>

	<delete id="deleteInventoryholdRecord" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		delete from plugin.inventoryhold_record
		where 1=1
		<if test="itemBarcodeList != null and itemBarcodeList.size() > 0">
			and item_barcode in
			<foreach collection="itemBarcodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and hold_reason = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcodeList == null and (holdReason == null or holdReason =='') ">
			and 1=2
		</if>
	</delete>

	<select id="getInventoryholdRecordList" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select s.* from (
		select t.serialkey,t.whseid,f.DESCRIPTION whName,t.item_no as itemNo,t.item_barcode as itemBarcode,t.supply_no as supplyNo,t.supply_name as supplyName,
		t.apply_by_no as applyByNo,t.apply_by_name as applyByName,t.apply_by_name||t.apply_by_no as applyBy,t.apply_dept_no as applyDeptNo,t.apply_dept_name as applyDeptName,
		t.responsible_by_no as responsibleByNo,t.responsible_by_name as responsibleByName,t.responsible_by_name||t.responsible_by_no as responsibleBy,t.responsible_dept_no as responsibleDeptNo,
		t.responsible_dept_name as responsibleDeptName, t.hold_status as holdStatus,case when t.hold_status = 0 then '失效' when t.hold_status = 1 then '生效'
		when t.hold_status = 2 then '已保存' when t.hold_status = 3 then '审批中' else '' end as holdStatusDsc,
		t.hold_reason as holdReason,b.description as holdReasonDsc,b.attribute1 as holdReasonType,t.remark, t.infor_status as inforStatus,t.infor_fail_times as inforFailTimes,
		t.adddate,t.addwho,t.editdate,t.editwho,t.edit_dept_no as editDeptNo,t.edit_dept_name as editDeptName,bs.description || bs.lookup_meaning approvedByFullName,
		decode(t.approved_result,'Y','通过','N','不通过','') approvedResultDesc, b.attribute3, rownum rn
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		left join plugin.sys_lookup_values bs on bs.lookup_meaning = t.approved_by and bs.lookup_type='1000050' and bs.enabled_flag = 'Y'
		left join ENTERPRISE.FACILITYNEST f on t.whseid = upper(substr(f.name,7))
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and t.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and t.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and t.hold_reason  = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="applyByNo != null and applyByNo !='' ">
			and t.apply_by_no  = #{applyByNo,jdbcType=VARCHAR}
		</if>
		<if test="applyDeptNo != null and applyDeptNo !='' ">
			and t.apply_dept_no  = #{applyDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleByNo != null and responsibleByNo !='' ">
			and t.responsible_by_no  = #{responsibleByNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleDeptNo != null and responsibleDeptNo !='' ">
			and t.responsible_dept_no  = #{responsibleDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="holdStatus != null and holdStatus !=-1 ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="editDeptNo != null and editDeptNo !='' ">
			and t.edit_dept_no  = #{editDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="approvedByNo != null and approvedByNo !='' ">
			and t.approved_by  = #{approvedByNo,jdbcType=VARCHAR}
		</if>
		<if test="approvedResult != null and approvedResult !='' ">
			and t.approved_result  = #{approvedResult,jdbcType=VARCHAR}
		</if>
		<if test="adddateBegin != null and adddateBegin !='' ">
			and t.adddate <![CDATA[>=]]> to_date(#{adddateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="adddateEnd != null and adddateEnd !='' ">
			and t.adddate <![CDATA[<=]]> to_date(#{adddateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateBegin != null and editdateBegin !='' ">
			and t.editdate <![CDATA[>=]]> to_date(#{editdateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateEnd != null and editdateEnd !='' ">
			and t.editdate <![CDATA[<=]]> to_date(#{editdateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>
	<select id="getInventoryholdRecordExeList" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.vo.InventoryholdRecordVO">
		select s.* from (
		select t.serialkey,t.whseid,f.DESCRIPTION whName,t.item_no as itemNo,t.item_barcode as itemBarcode,t.supply_no as supplyNo,t.supply_name as supplyName,
		t.apply_by_no as applyByNo,t.apply_by_name as applyByName,t.apply_by_name||t.apply_by_no as applyBy,t.apply_dept_no as applyDeptNo,t.apply_dept_name as applyDeptName,
		t.responsible_by_no as responsibleByNo,t.responsible_by_name as responsibleByName,t.responsible_by_name||t.responsible_by_no as responsibleBy,t.responsible_dept_no as responsibleDeptNo,
		t.responsible_dept_name as responsibleDeptName, t.hold_status as holdStatus,case when t.hold_status = 0 then '失效' when t.hold_status = 1 then '生效'
		when t.hold_status = 2 then '已保存' when t.hold_status = 3 then '审批中' else '' end as holdStatusDsc,
		t.hold_reason as holdReason,b.description as holdReasonDsc,b.attribute1 as holdReasonType,t.remark, t.infor_status as inforStatus,
		case when t.infor_status = 0 then '成功' else '失败' end as inforStatusDsc,
		t.infor_fail_times as inforFailTimes,
		t.adddate,t.addwho,t.editdate,t.editwho,t.edit_dept_no as editDeptNo,t.edit_dept_name as editDeptName,rownum rn
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		left join ENTERPRISE.FACILITYNEST f on t.whseid = upper(substr(f.name,7))
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and t.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and t.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and t.hold_reason  = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="applyByNo != null and applyByNo !='' ">
			and t.apply_by_no  = #{applyByNo,jdbcType=VARCHAR}
		</if>
		<if test="applyDeptNo != null and applyDeptNo !='' ">
			and t.apply_dept_no  = #{applyDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleByNo != null and responsibleByNo !='' ">
			and t.responsible_by_no  = #{responsibleByNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleDeptNo != null and responsibleDeptNo !='' ">
			and t.responsible_dept_no  = #{responsibleDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="holdStatus != null and holdStatus !=-1 ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="editDeptNo != null and editDeptNo !='' ">
			and t.edit_dept_no  = #{editDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="adddateBegin != null and adddateBegin !='' ">
			and t.adddate <![CDATA[>=]]> to_date(#{adddateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="adddateEnd != null and adddateEnd !='' ">
			and t.adddate <![CDATA[<=]]> to_date(#{adddateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateBegin != null and editdateBegin !='' ">
			and t.editdate <![CDATA[>=]]> to_date(#{editdateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateEnd != null and editdateEnd !='' ">
			and t.editdate <![CDATA[<=]]> to_date(#{editdateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>
	<select id="getInventoryholdRecordSendMail" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.vo.InventoryholdRecordEmailVO">
		select s.* from (select t.whseid,t.item_no as itemNo,t.item_barcode as itemBarcode,t.apply_by_name||t.apply_by_no as applyBy,
		t.apply_dept_name as applyDeptName,case when t.hold_status = 0 then '失效' when t.hold_status = 1 then '生效'
		when t.hold_status = 2 then '已保存' when t.hold_status = 3 then '审批中' else '' end as holdStatusDsc,
		b.description as holdReasonDsc,
		t.editwho,t.edit_dept_name as editDeptName,rownum rn
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		where (t.editdate <![CDATA[>=]]> SYSDATE - 2/24 or (t.approved_result='Y' and t.approved_time <![CDATA[>=]]> SYSDATE - 2/24))
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="holdStatus != null ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforFailTimes != null ">
			and t.infor_fail_times <![CDATA[>=]]> #{inforFailTimes,jdbcType=DECIMAL}
		</if>
		<if test="editDeptNoList != null and editDeptNoList.size() > 0">
			and t.edit_dept_no not in
			<foreach collection="editDeptNoList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and (case when b.attribute1='质量冻结' then DECODE(SUBSTR(t.item_no, 1, 1), '1', 0, 1) else 2 end)=#{sendMailType,jdbcType=INTEGER}
		and t.hold_status not in ('2','3')
		and (t.approved_result is null or t.approved_result = 'Y')
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>
	<select id="getInventoryholdRecordSendApplyByNo" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="java.lang.String">
		select distinct t.apply_by_no
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		where (t.editdate <![CDATA[>=]]> SYSDATE - 2/24 or (t.approved_result='Y' and t.approved_time <![CDATA[>=]]> SYSDATE - 2/24))
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="holdStatus != null ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforFailTimes != null ">
			and t.infor_fail_times <![CDATA[>=]]> #{inforFailTimes,jdbcType=DECIMAL}
		</if>
		and (case when b.attribute1='质量冻结' then DECODE(SUBSTR(t.item_no, 1, 1), '1', 0, 1) else 2 end)=#{sendMailType,jdbcType=INTEGER}
		and t.hold_status not in ('2','3')
		and (t.approved_result is null or t.approved_result = 'Y')
	</select>
	<select id="getInventoryholdRecordSendMailTotal" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		where (t.editdate <![CDATA[>=]]> SYSDATE - 2/24 or (t.approved_result='Y' and t.approved_time <![CDATA[>=]]> SYSDATE - 2/24))
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="holdStatus != null ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="inforFailTimes != null ">
			and t.infor_fail_times <![CDATA[>=]]> #{inforFailTimes,jdbcType=DECIMAL}
		</if>
		<if test="editDeptNoList != null and editDeptNoList.size() > 0">
			and t.edit_dept_no not in
			<foreach collection="editDeptNoList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and (case when b.attribute1='质量冻结' then DECODE(SUBSTR(t.item_no, 1, 1), '1', 0, 1) else 2 end)=#{sendMailType,jdbcType=INTEGER}
		and t.hold_status not in ('2','3')
		and (t.approved_result is null or t.approved_result = 'Y')
	</select>
	<select id="getInventoryholdRecordListVOTotal" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.inventoryhold_record t
		left join plugin.sys_lookup_values b on b.lookup_meaning = t.hold_reason and b.lookup_type='1000037' and b.enabled_flag = 'Y'
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and t.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and t.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and t.hold_reason  = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="holdReasonType != null and holdReasonType !='' ">
			and b.attribute1 = #{holdReasonType,jdbcType=VARCHAR}
		</if>
		<if test="applyByNo != null and applyByNo !='' ">
			and t.apply_by_no  = #{applyByNo,jdbcType=VARCHAR}
		</if>
		<if test="applyDeptNo != null and applyDeptNo !='' ">
			and t.apply_dept_no  = #{applyDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleByNo != null and responsibleByNo !='' ">
			and t.responsible_by_no  = #{responsibleByNo,jdbcType=VARCHAR}
		</if>
		<if test="responsibleDeptNo != null and responsibleDeptNo !='' ">
			and t.responsible_dept_no  = #{responsibleDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="inforStatus != null ">
			and t.infor_status  = #{inforStatus,jdbcType=DECIMAL}
		</if>
		<if test="holdStatus != null and holdStatus !=-1 ">
			and t.hold_status  = #{holdStatus,jdbcType=DECIMAL}
		</if>
		<if test="editDeptNo != null and editDeptNo !='' ">
			and t.edit_dept_no  = #{editDeptNo,jdbcType=VARCHAR}
		</if>
		<if test="approvedByNo != null and approvedByNo !='' ">
			and t.approved_by  = #{approvedByNo,jdbcType=VARCHAR}
		</if>
		<if test="approvedResult != null and approvedResult !='' ">
			and t.approved_result  = #{approvedResult,jdbcType=VARCHAR}
		</if>
		<if test="adddateBegin != null and adddateBegin !='' ">
			and t.adddate <![CDATA[>=]]> to_date(#{adddateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="adddateEnd != null and adddateEnd !='' ">
			and t.adddate <![CDATA[<=]]> to_date(#{adddateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateBegin != null and editdateBegin !='' ">
			and t.editdate <![CDATA[>=]]> to_date(#{editdateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="editdateEnd != null and editdateEnd !='' ">
			and t.editdate <![CDATA[<=]]> to_date(#{editdateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<select id="getItemNoByItemBarcode" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.item_barcode itemBarcode,t.item_id itemId,t.supplier_no supplyNo,storer.company supplyName
		from enterprise.ztebarcode t
		left join enterprise.storer on storer.storerkey = t.supplier_no and storer.type = '5'
		where t.item_barcode = #{itemBarcode,jdbcType=VARCHAR}
		and rownum = 1
	</select>

	<select id="getLotByItemBarcode" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="java.lang.String">
		select t.lot from ${whseid}.lot t
		join ${whseid}.lotattribute h
		on t.lot = h.lot
		where h.lottable02= #{itemBarcode,jdbcType=VARCHAR}
		and t.storerkey='ZTE'
		and t.qty > 0
		and not exists (
			select 1 from ${whseid}.inventoryhold i
			where i.lot = t.lot
			and i.status = #{holdReason,jdbcType=VARCHAR}
			and i.hold = #{holdStatus}
		)
	</select>

	<update id="updateInventoryholdRecord" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		update plugin.inventoryhold_record
		set
		<if test="applyByNo != null and applyByNo !='' ">
			apply_by_no = #{applyByNo, jdbcType=VARCHAR},
		</if>
		<if test="applyByName != null and applyByName !='' ">
			apply_by_name = #{applyByName, jdbcType=VARCHAR},
		</if>
		<if test="applyDeptNo != null and applyDeptNo !='' ">
			apply_dept_no = #{applyDeptNo, jdbcType=VARCHAR},
		</if>
		<if test="applyDeptName != null and applyDeptName !='' ">
			apply_dept_name = #{applyDeptName, jdbcType=VARCHAR},
		</if>
		<if test="responsibleByNo != null and responsibleByNo !='' ">
			responsible_by_no = #{responsibleByNo, jdbcType=VARCHAR},
		</if>
		<if test="responsibleByName != null and responsibleByName !='' ">
			responsible_by_name = #{responsibleByName, jdbcType=VARCHAR},
		</if>
		<if test="responsibleDeptNo != null and responsibleDeptNo !='' ">
			responsible_dept_no = #{responsibleDeptNo, jdbcType=VARCHAR},
		</if>
		<if test="responsibleDeptName != null and responsibleDeptName !='' ">
			responsible_dept_name = #{responsibleDeptName, jdbcType=VARCHAR},
		</if>
		<if test="remark != null and remark !='' ">
			remark = #{remark, jdbcType=VARCHAR},
		</if>
		<if test="holdStatus != null">
			hold_status = #{holdStatus, jdbcType=DECIMAL},
		</if>
		<if test="inforStatus != null">
			infor_status = #{inforStatus, jdbcType=DECIMAL},
		</if>
		<if test="inforFailTimes != null">
			infor_fail_times = #{inforFailTimes, jdbcType=DECIMAL},
		</if>
		<if test="inforParamsResult != null and inforParamsResult !='' ">
			infor_params_result = #{inforParamsResult, jdbcType=VARCHAR},
		</if>
		<if test="editwho != null and editwho !='' ">
			editwho = #{editwho, jdbcType=VARCHAR},
		</if>
		<if test="editDeptNo != null and editDeptNo !='' ">
			edit_dept_no = #{editDeptNo, jdbcType=VARCHAR},
		</if>
		<if test="editDeptName != null and editDeptName !='' ">
			edit_dept_name = #{editDeptName, jdbcType=VARCHAR},
		</if>
		<if test="isEditDate == null or isEditDate =='' ">
			editdate = sysdate
		</if>
		<if test="isEditDate =='1' ">
			editdate = to_date(#{editdate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		where 1=1
		<if test="serialkey != null">
			and serialkey = #{serialkey,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and whseid = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and item_barcode = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="holdReason != null and holdReason !='' ">
			and hold_reason = #{holdReason,jdbcType=VARCHAR}
		</if>
		<if test="serialkey == null and (whseid == null or whseid =='') and (itemBarcode == null or itemBarcode =='') and (holdReason == null or holdReason =='')">
			and 1=2
		</if>
	</update>

	<update id="batchUpdateInventoryholdRecord" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		update plugin.inventoryhold_record
		set infor_fail_times = 0
		where serialkey in
		<foreach collection="serialkeyList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<select id="getLookupValues" parameterType="com.zte.interfaces.infor.dto.SysLookupValuesDTO" resultType="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
		select b.*
		from plugin.sys_lookup_types a
		join plugin.sys_lookup_values b
		on a.lookup_type = b.lookup_type
		and b.enabled_flag = 'Y'
		where a.enabled_flag = 'Y'
		<if test="lookupType != null and lookupType !='' ">
			and a.lookup_type = #{lookupType, jdbcType=VARCHAR}
		</if>
		<if test="attribute1 != null and attribute1 !='' ">
			and b.attribute1 = #{attribute1, jdbcType=VARCHAR}
		</if>
		<if test="lookupMeaning != null and lookupMeaning !='' ">
			and b.lookup_meaning = #{lookupMeaning, jdbcType=VARCHAR}
		</if>
		<if test="lookupTypeList != null and lookupTypeList.size() > 0">
			and a.lookup_type in
			<foreach collection="lookupTypeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getLookupValue" parameterType="java.lang.String" resultType="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
		select b.*
		from plugin.sys_lookup_types a
		join plugin.sys_lookup_values b
		on a.lookup_type = b.lookup_type
		and b.enabled_flag = 'Y'
		where a.enabled_flag = 'Y'
		and b.lookup_code = #{lookupCode, jdbcType=VARCHAR}
	</select>

	<!-- 更新 lookup_meaning -->
	<update id="updateLookupMeaning">
		UPDATE plugin.sys_lookup_values b
		SET
			b.lookup_meaning = #{lookupMeaning, jdbcType=VARCHAR},
			b.last_update_date = SYSDATE
		WHERE b.lookup_code = #{lookupCode, jdbcType=VARCHAR}
		AND b.enabled_flag = 'Y'
		AND EXISTS (
			SELECT 1
			FROM plugin.sys_lookup_types a
			WHERE a.lookup_type = b.lookup_type
			AND a.enabled_flag = 'Y'
		)
	</update>


	<select id="getInventoryholdJobList" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select distinct t.serialkey,t.whseid,t.item_no as itemNo,t.item_barcode as itemBarcode,t.supply_no as supplyNo,t.supply_name as supplyName,
		t.apply_by_no as applyByNo,t.apply_by_name as applyByName,t.apply_by_name||t.apply_by_no as applyBy,t.apply_dept_no as applyDeptNo,t.apply_dept_name as applyDeptName,
		t.responsible_by_no as responsibleByNo,t.responsible_by_name as responsibleByName,t.responsible_by_name||t.responsible_by_no as responsibleBy,t.responsible_dept_no as responsibleDeptNo,
		t.responsible_dept_name as responsibleDeptName, t.hold_status as holdStatus, t.hold_reason as holdReason,t.remark, t.infor_status as inforStatus,t.infor_fail_times as inforFailTimes,
		t.adddate,t.addwho,t.editdate,t.editwho,t.edit_dept_name as editDeptName,'1' isEditDate
		from plugin.inventoryhold_record t
		join ${whseid}.lotattribute lo on lo.lottable02 = t.item_barcode  and lo.storerkey='ZTE'
		join ${whseid}.lot l on l.lot = lo.lot and l.storerkey = lo.storerkey
		where t.whseid = lo.whseid
		and t.hold_status = 1
		and t.infor_fail_times &lt; 4
		and l.qty > 0
		and not exists (
			select 1 from ${whseid}.inventoryhold i
			where i.lot = l.lot
			and i.status = t.hold_reason
			and i.hold = '1'
		)
		union all
		select distinct t.serialkey,t.whseid,t.item_no as itemNo,t.item_barcode as itemBarcode,t.supply_no as supplyNo,t.supply_name as supplyName,
		t.apply_by_no as applyByNo,t.apply_by_name as applyByName,t.apply_by_name||t.apply_by_no as applyBy,t.apply_dept_no as applyDeptNo,t.apply_dept_name as applyDeptName,
		t.responsible_by_no as responsibleByNo,t.responsible_by_name as responsibleByName,t.responsible_by_name||t.responsible_by_no as responsibleBy,t.responsible_dept_no as responsibleDeptNo,
		t.responsible_dept_name as responsibleDeptName, t.hold_status as holdStatus, t.hold_reason as holdReason,t.remark, t.infor_status as inforStatus,t.infor_fail_times as inforFailTimes,
		t.adddate,t.addwho,t.editdate,t.editwho,t.edit_dept_name as editDeptName,'1' isEditDate
		from plugin.inventoryhold_record t
		join ${whseid}.lotattribute lo on lo.lottable02 = t.item_barcode  and lo.storerkey='ZTE'
		join ${whseid}.lot l on l.lot = lo.lot and l.storerkey = lo.storerkey
		where t.whseid = lo.whseid
		and t.hold_status = 0
		and t.infor_fail_times &lt; 4
		and l.qty > 0
		and t.approved_result = 'Y'
		and not exists (
		select 1 from ${whseid}.inventoryhold i
		where i.lot = l.lot
		and i.status = t.hold_reason
		and i.hold = '0'
		)
	</select>
	<select id="getBatchItemNoByItemBarcode" parameterType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO" resultType="com.zte.interfaces.infor.dto.InventoryholdRecordDTO">
		select t.item_barcode itemBarcode,t.item_id itemId,t.supplier_no supplyNo,s.company supplyName
		from enterprise.ztebarcode t
		left join enterprise.storer s on s.storerkey = t.supplier_no and s.type = '5'
		where t.item_barcode in (
		<foreach collection="list" item="item" index="index" separator=",">
			#{item.itemBarcode,jdbcType=VARCHAR}
		</foreach>
		)
	</select>

	<select id="getIsNeedApproved" parameterType="com.zte.interfaces.infor.dto.SysLookupValuesDTO" resultType="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
		select b.*
		from plugin.sys_lookup_types a
		join plugin.sys_lookup_values b
		on a.lookup_type = b.lookup_type
		and b.enabled_flag = 'Y'
		where a.enabled_flag = 'Y'
		<if test="lookupType != null and lookupType !='' ">
			and a.lookup_type = #{lookupType, jdbcType=VARCHAR}
		</if>
		<if test="attribute1 != null and attribute1 !='' ">
			and b.attribute1 = #{attribute1, jdbcType=VARCHAR}
		</if>
		<if test="attribute2 != null and attribute2 !='' ">
			and b.attribute2 = #{attribute2, jdbcType=VARCHAR}
		</if>
		<if test="attribute3 != null and attribute3 !='' ">
			and b.attribute3 = #{attribute3, jdbcType=VARCHAR}
		</if>
		<if test="lookupMeaning != null and lookupMeaning !='' ">
			and b.lookup_meaning = #{lookupMeaning, jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getHoldWhseName" parameterType="java.util.List" resultType="java.lang.String">
		select TO_CHAR(WM_CONCAT(TO_CHAR(F.DESCRIPTION))) AS WHSEID
		from WMSADMIN.PL_DB PD
		JOIN ENTERPRISE.FACILITYNEST F ON F.NAME = PD.DB_NAME
		where UPPER(PD.DB_LOGID)  in (
		<foreach collection = "list" item = "tmp" index = "index" open = "(" close = ")" separator = "," >
			#{tmp}
		</foreach >
		)
	</select>

	<insert id="insertApproveHoldRecordLog" parameterType="java.util.List">
		insert into plugin.hold_mobileapproval_log(serail_id, businessid, whseid, item_no, item_barcode, approve_type, hold_reason, adddate,addwho,editdate,editwho)
		select plugin.seq_hold_mobileapproval_log.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.businessid,jdbcType=VARCHAR}, upper(#{item.whseid,jdbcType=VARCHAR}), #{item.itemNo,jdbcType=VARCHAR}, #{item.itemBarcode,jdbcType=VARCHAR},
			#{item.approveType,jdbcType=VARCHAR}, #{item.holdReason,jdbcType=VARCHAR},
			sysdate as adddate, #{item.editwho,jdbcType=VARCHAR}, sysdate as editdate, #{item.editwho,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>

	<update id="updateBatchInventoryholdRecord" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update plugin.inventoryhold_record
			set
			<if test="info.remark != null and info.remark !='' ">
				remark = #{info.remark, jdbcType=VARCHAR},
			</if>
			<if test="info.unfreezeRemark != null and info.unfreezeRemark !='' ">
				unfreeze_remark = #{info.unfreezeRemark, jdbcType=VARCHAR},
			</if>
			<if test="info.holdStatus != null">
				hold_status = #{info.holdStatus, jdbcType=DECIMAL},
			</if>
			<if test="info.holdStatus != null and info.holdStatus == 3 and info.approvedResult == null and info.approvedResult ==''">
				APPROVED_RESULT = '',
			</if>
			<if test="info.inforStatus != null">
				infor_status = #{info.inforStatus, jdbcType=DECIMAL},
			</if>
			<if test="info.inforFailTimes != null">
				infor_fail_times = #{info.inforFailTimes, jdbcType=DECIMAL},
			</if>
			<if test="info.inforParamsResult != null and iinfo.nforParamsResult !='' ">
				infor_params_result = #{info.inforParamsResult, jdbcType=VARCHAR},
			</if>
			<if test="info.editwho != null and info.editwho !='' ">
				editwho = #{info.editwho, jdbcType=VARCHAR},
			</if>
			<if test="info.editDeptNo != null and info.editDeptNo !='' ">
				edit_dept_no = #{info.editDeptNo, jdbcType=VARCHAR},
			</if>
			<if test="info.editDeptName != null and info.editDeptName !='' ">
				edit_dept_name = #{info.editDeptName, jdbcType=VARCHAR},
			</if>
			<if test="info.approvedResult != null and info.approvedResult !='' ">
				APPROVED_RESULT = #{info.approvedResult,jdbcType=VARCHAR},
			</if>
			<if test="info.isEditDate == null or info.isEditDate =='' ">
				editdate = sysdate
			</if>
			<if test="info.isEditDate =='1' ">
				editdate = to_date(#{info.editdate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
			</if>
			where 1=1
			<if test="info.serialkey != null">
				and serialkey = #{info.serialkey,jdbcType=VARCHAR}
			</if>
			<if test="info.whseid != null and info.whseid !='' ">
				and whseid = #{info.whseid,jdbcType=VARCHAR}
			</if>
			<if test="info.itemBarcode != null and info.itemBarcode !='' ">
				and item_barcode = #{info.itemBarcode,jdbcType=VARCHAR}
			</if>
			<if test="info.holdReason != null and info.holdReason !='' ">
				and hold_reason = #{info.holdReason,jdbcType=VARCHAR}
			</if>
			<if test="info.serialkey == null and (info.whseid == null or info.whseid =='') and (info.itemBarcode == null or info.itemBarcode =='') and (info.holdReason == null or info.holdReason =='')">
				and 1=2
			</if>
		</foreach>
	</update>
	<delete id="deleteBatchHoldInventroyRecord" parameterType="com.zte.interfaces.step.dto.ApprovalProcessInfoDTO">
		delete from plugin.inventoryhold_record d
		where exists (select 1
		from plugin.hold_mobileapproval_log l
		where l.whseid = d.whseid
		and l.item_barcode = d.item_barcode
		and l.hold_reason = d.hold_reason
		and l.businessid = #{billNO,jdbcType=VARCHAR})
		and d.hold_status = #{holdStatus,jdbcType=DECIMAL}
	</delete>

</mapper>