<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.LotattributeDealLogRepository">

	<insert id="insertLotattributeDealLog" parameterType="java.util.List">
		insert into plugin.lotattribute_deal_log (serialkey, whseid, sku, item_uuid, supply_no, lottable02, symbol, dealtimes, serial_no,
		src_lottable01, dst_lottable01, src_lottable02, dst_lottable02, src_lottable03, dst_lottable03, src_lottable04, dst_lottable04,
		src_lottable05, dst_lottable05, src_lottable06, dst_lottable06, src_lottable07, dst_lottable07, src_lottable08, dst_lottable08,
		src_lottable09, dst_lottable09, src_lottable10, dst_lottable10, src_lottable11, dst_lottable11, src_lottable12, dst_lottable12,
		addwho, adddate, editwho, editdate)
		select PLUGIN.SEQ_LOTATTRIBUTE_DEAL_LOG.NEXTVAL, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.whseid,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.itemUuid,jdbcType=VARCHAR}, #{item.supplyNo,jdbcType=VARCHAR},
			#{item.lottable02,jdbcType=VARCHAR}, #{item.symbol,jdbcType=DECIMAL}, #{item.dealtimes,jdbcType=DECIMAL}, #{item.serialNo,jdbcType=VARCHAR},
			#{item.srcLottable01,jdbcType=VARCHAR}, #{item.dstLottable01,jdbcType=VARCHAR}, #{item.srcLottable02,jdbcType=VARCHAR}, #{item.dstLottable02,jdbcType=VARCHAR},
			#{item.srcLottable03,jdbcType=VARCHAR}, #{item.dstLottable03,jdbcType=VARCHAR}, #{item.srcLottable04,jdbcType=DATE}, #{item.dstLottable04,jdbcType=DATE},
			#{item.srcLottable05,jdbcType=DATE}, #{item.dstLottable05,jdbcType=DATE}, #{item.srcLottable06,jdbcType=VARCHAR}, #{item.dstLottable06,jdbcType=VARCHAR},
			#{item.srcLottable07,jdbcType=VARCHAR}, #{item.dstLottable07,jdbcType=VARCHAR}, #{item.srcLottable08,jdbcType=VARCHAR}, #{item.dstLottable08,jdbcType=VARCHAR},
			#{item.srcLottable09,jdbcType=VARCHAR}, #{item.dstLottable09,jdbcType=VARCHAR}, #{item.srcLottable10,jdbcType=VARCHAR}, #{item.dstLottable10,jdbcType=VARCHAR},
			#{item.srcLottable11,jdbcType=DATE}, #{item.dstLottable11,jdbcType=DATE}, #{item.srcLottable12,jdbcType=DATE}, #{item.dstLottable12,jdbcType=DATE},
			#{item.addwho,jdbcType=VARCHAR}, sysdate as adddate, #{item.editwho,jdbcType=VARCHAR}, sysdate as editdate
			from dual
		</foreach>
		) temp
	</insert>

	<select id="getLotattributeDealLogList" parameterType="com.zte.interfaces.infor.dto.LotattributeDealLogDTO" resultType="com.zte.interfaces.infor.dto.LotattributeDealLogDTO">
		select B.* from (
		select A.*,rownum rn from(select serialkey, whseid, sku, item_uuid as itemUuid, supply_no as supplyNo, lottable02, symbol, dealtimes, serial_no as serialNo,
		src_lottable01 as srcLottable01, dst_lottable01 as dstLottable01, src_lottable02 as srcLottable02, dst_lottable02 as dstLottable02,
		src_lottable03 as srcLottable03, dst_lottable03 as dstLottable03, to_char(src_lottable04, 'yyyy-mm-dd hh24:mi:ss') as srcLottable04,
		to_char(dst_lottable04, 'yyyy-mm-dd hh24:mi:ss') as dstLottable04, to_char(src_lottable05, 'yyyy-mm-dd hh24:mi:ss') as srcLottable05,
		to_char(dst_lottable05, 'yyyy-mm-dd hh24:mi:ss') as dstLottable05, src_lottable06 as srcLottable06, dst_lottable06 as dstLottable06,
		src_lottable07 as srcLottable07, dst_lottable07 as dstLottable07, src_lottable08 as srcLottable08, dst_lottable08 as dstLottable08,
		src_lottable09 as srcLottable09, dst_lottable09 as dstLottable09, src_lottable10 as srcLottable10, dst_lottable10 as dstLottable10,
		src_lottable11 as srcLottable11, dst_lottable11 as dstLottable11, src_lottable12 as srcLottable12, dst_lottable12 as dstLottable12,
		addwho, to_char(adddate, 'yyyy-mm-dd hh24:mi:ss') as adddate, editwho, to_char(editdate, 'yyyy-mm-dd hh24:mi:ss') as editdate, remark
		from plugin.lotattribute_deal_log
		where 1=1
		<if test="lottable02 != null and lottable02 !='' ">
			and lottable02 = #{lottable02,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and whseid = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="symbol != null">
			and symbol = #{symbol,jdbcType=DECIMAL}
		</if>
		order by adddate
		)A )B
		where 1=1
		<if test="pageSize!=null">
			and B.rn <![CDATA[>=]]> #{startRow} and B.rn <![CDATA[<=]]>#{endRow}
		</if>
	</select>

	<select id="getLotattributeDealLogListVOTotal" parameterType="com.zte.interfaces.infor.dto.LotattributeDealLogDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.lotattribute_deal_log
		where 1=1
		<if test="lottable02 != null and lottable02 !='' ">
			and lottable02 = #{lottable02,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and whseid = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="symbol != null">
			and symbol = #{symbol,jdbcType=DECIMAL}
		</if>
	</select>

	<update id="updateInforLottableLog" parameterType="com.zte.interfaces.infor.dto.LotattributeDealLogDTO">
		update plugin.lotattribute_deal_log
		set dealtimes = 0, editdate = sysdate, editwho = #{editwho, jdbcType=VARCHAR}
		where serialkey in
		<foreach collection="serialkeyList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

</mapper>