<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.EdiPoSRepository">
  <resultMap id="PoInboundInfoMap" type="com.zte.interfaces.infor.dto.PoInBoundInfoDTO">
    <result column="SKU" jdbcType="OTHER" property="itemNo" />
    <result column="LOTTABLE03" jdbcType="OTHER" property="orgId" />
    <result column="REF32" jdbcType="OTHER" property="productClass" />
    <result column="SUM(EPS.QTYSTORE)" jdbcType="DECIMAL" property="qty" />
  </resultMap>

  <select id="getTransCountLimit" resultType="java.lang.Integer" parameterType="java.lang.String">
    select b.lookup_meaning from plugin.sys_lookup_types a
    join plugin.sys_lookup_values b on a.lookup_type = b.lookup_type and b.enabled_flag = 'Y'
    where a.enabled_flag = 'Y' and b.lookup_code = #{lookupCode}
  </select>

  <select id="getPoInboundInfo"  parameterType="com.zte.interfaces.infor.dto.PoInBoundDTO"
        resultMap="PoInboundInfoMap">
    select eps.sku, eps.lottable03, eps.ref32, sum(eps.qtystore)
    from plugin.edi_po_s eps
    where
    eps.href11 in ('100', '110', '120', '130', '140')
    and eps.sku in
    <!-- Started by AICoder, pid:n2e8448d594e3bd14736089e00c75e0792b5f29c -->
    <if test="itemNo != null and itemNo.size() > 0">
        <foreach item="item" index="index" collection="itemNo" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <!-- Ended by AICoder, pid:n2e8448d594e3bd14736089e00c75e0792b5f29c -->
    <if test="instoreBegindate != null and instoreBegindate !='' ">
        and eps.edi_adddate between TO_DATE(#{instoreBegindate}, 'YYYY-MM-DD HH24:MI:SS')
    </if>
    <if test="instoreEnddate != null and instoreEnddate !='' ">
        and TO_DATE(#{instoreEnddate}, 'YYYY-MM-DD HH24:MI:SS')
    </if>
    <!-- 条件全部为空时禁止查询 -->
    <if test="itemNo == null or itemNo.size() == 0
        and (instoreBegindate == null or instoreBegindate == '')
        and (instoreEnddate == null or instoreEnddate)
    ">
        and 1=2
    </if>
    group by eps.lottable03, eps.sku, eps.ref32
  </select>
</mapper>
