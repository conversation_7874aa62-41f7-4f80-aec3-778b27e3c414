<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.IqcTestRequisitionRepository">

	<select id="isRepeatByBarcode" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(*)
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		and b.delivery_no = #{deliverNo,jdbcType=VARCHAR}
		and d.item_barcode in
		<foreach collection="itemBarcodes" index="index" item="itemBarcode" open="(" separator="," close=")">
		#{itemBarcode}
	</foreach>
	</select>

	<select id="getItemReelControl" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionPkgDTO">
		select distinct lt.sku itemNo ,nvl(lt.reelidprocess, 0)  reelidProcess
		from ${wmwhseId}.sku lt
		where lt.storerkey = 'ZTE'
		and lt.sku  in
		<foreach collection="itemNos" index="index" item="itemNo" open="(" separator="," close=")">
			#{itemNo}
		</foreach>
	</select>

	<insert id="insertIqcTestRequisitionHead" parameterType="com.zte.domain.model.infor.IqcTestRequisitionBillDTO">
		insert into plugin.iqc_test_requisition_bill
		(serialkey, bill_no, delivery_no, stock_no, receive_org, usage_code, usage_name, material_picker,
		material_pickdept, bill_status, bill_split_status, addwho, editwho)
		values (plugin.seq_iqc_test_requisition_bill.nextval,#{billNo,jdbcType=VARCHAR}, #{deliveryNo,jdbcType=VARCHAR},
		upper(#{stockNo,jdbcType=VARCHAR}), #{receiveOrg,jdbcType=VARCHAR}, #{usageCode,jdbcType=VARCHAR},
		#{usageName,jdbcType=VARCHAR}, #{materialPicker,jdbcType=VARCHAR}, #{materialPickDept,jdbcType=VARCHAR},
		#{billStatus,jdbcType=DECIMAL}, #{billSplitStatus,jdbcType=DECIMAL}, 'ISCP','ISCP')
	</insert>

	<insert id="insertIqcTestRequisitionDetail" parameterType="com.zte.domain.model.infor.IqcTestRequisitionDetailDTO">
		insert into plugin.iqc_test_requisition_detail
		(serialkey, bill_no, bill_linenumber, item_no, item_barcode, planning_group, product_class, is_ltc, check_type,
		pkgid, reelid, pkg_good_qty, pkg_pick_qty, reel_good_qty, reel_pick_qty, check_result, detail_status,
		infor_split_status, addwho, editwho)
		select plugin.seq_iqc_test_requi_detail.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.billNo,jdbcType=VARCHAR},#{item.billLineNumber,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR},
			#{item.itemBarcode,jdbcType=VARCHAR}, #{item.planningGroup,jdbcType=VARCHAR}, #{item.productClass,jdbcType=VARCHAR},
			#{item.isLtc,jdbcType=VARCHAR}, #{item.checkType,jdbcType=VARCHAR}, #{item.pkgId,jdbcType=VARCHAR},
			#{item.reelId,jdbcType=VARCHAR}, #{item.pkgGoodQty,jdbcType=DECIMAL}, #{item.pkgPickQty,jdbcType=DECIMAL},
			#{item.reelGoodQty,jdbcType=DECIMAL}, #{item.reelPickQty,jdbcType=DECIMAL}, #{item.checkResult,jdbcType=DECIMAL},
			#{item.detailStatus,jdbcType=DECIMAL}, #{item.inforSplitStatus,jdbcType=DECIMAL}, 'ISCP' addwho,'ISCP' editwho
			from dual
		</foreach>
		) temp
	</insert>

	<select id="getIqcProductClass" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionPkgDTO">
		select  distinct eps.sku itemNo,eps.ref32 productClass
		from plugin.edi_po eps
		where eps.externreceiptkey = #{deliveryNo,jdbcType=VARCHAR}
		and trim(eps.ref32) is not null
		and eps.sku in
		<foreach collection="itemNos" index="index" item="itemNo" open="(" separator="," close=")">
			#{itemNo}
		</foreach>
	</select>

	<select id="getIqcTestRequisitionSplitDetails" resultType="com.zte.domain.model.infor.IqcTestRequisitionDetailDTO">
		select wh.billNo,
		wh.billLineNumber,
		wh.itemNo,
		wh.itemBarcode,
		wh.pkgId,
		wh.reelId,
		wh.pkgGoodQty,
		wh.pkgPickQty,
		wh.reelGoodQty,
		wh.reelPickQty,
		wh.inforSplitStatus,
		wh.splitFailTimes,
		wh.stockNo,
		wh.newReelid
		from (select d.bill_no billNo,
		d.bill_linenumber billLineNumber,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		d.pkg_good_qty pkgGoodQty,
		d.pkg_pick_qty pkgPickQty,
		d.reel_good_qty reelGoodQty,
		d.reel_pick_qty reelPickQty,
		d.infor_split_status inforSplitStatus,
		d.split_fail_times splitFailTimes,
		b.stock_no stockNo,
		d.new_reelid newReelid
		from plugin.iqc_test_requisition_detail d
		join plugin.iqc_test_requisition_bill b on d.bill_no = b.bill_no
		where d.enable_flag = 'Y'
		and b.enable_flag = 'Y'
		and d.infor_split_status = 1
		and d.split_fail_times &lt; 4
		and d.check_type = '来料检验'
		and exists (select 1
		from plugin.edi_asnqc_r ear
		where ear.lottable02 = d.item_barcode
		and ear.symbol in (1,3))
		union all
		select d.bill_no billNo,
		d.bill_linenumber billLineNumber,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		d.pkg_good_qty pkgGoodQty,
		d.pkg_pick_qty pkgPickQty,
		d.reel_good_qty reelGoodQty,
		d.reel_pick_qty reelPickQty,
		d.infor_split_status inforSplitStatus,
		d.split_fail_times splitFailTimes,
		b.stock_no stockNo,
		d.new_reelid newReelid
		from plugin.iqc_test_requisition_detail d
		join plugin.iqc_test_requisition_bill b on d.bill_no = b.bill_no
		join plugin.delay_recheck_bill_head h on h.recheck_no =
		b.delivery_no
		and h.check_result_returned = 1
		where d.enable_flag = 'Y'
		and b.enable_flag = 'Y'
		and d.infor_split_status = 1
		and d.split_fail_times &lt; 4
		and d.check_type = '超期复检') wh
		order by wh.billNo

	</select>


	<update id="updateIqcDetailBatch" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
			update plugin.iqc_test_requisition_detail
			<set>
				<if test="info.inforSplitStatus != null" >
					infor_split_status = #{info.inforSplitStatus,jdbcType=INTEGER},
				</if>
				<if test="info.splitFailTimes != null" >
					split_fail_times = #{info.splitFailTimes,jdbcType=INTEGER},
				</if>
				<if test="info.newReelid != null and info.newReelid !='' " >
					new_reelid = #{info.newReelid,jdbcType=VARCHAR},
				</if>
				<if test="info.failReason != null and info.failReason !='' " >
					fail_reason = #{info.failReason,jdbcType=VARCHAR},
				</if>
				editdate = SYSDATE
			</set>
			<where>
				bill_no = #{info.billNo, jdbcType=VARCHAR}
				and bill_linenumber = #{info.billLineNumber, jdbcType=VARCHAR}
			</where>
		</foreach>
	</update>

	<update id="updateIqcBillSplited">
		update plugin.iqc_test_requisition_bill b
		set b.bill_split_status = 2,b.editdate = sysdate
		where (b.bill_no = (select d.bill_no
		from plugin.iqc_test_requisition_detail d
		where d.enable_flag = 'Y'
		and d.bill_no = #{billNo,jdbcType=VARCHAR}
		and exists (select 1
		from plugin.iqc_test_requisition_detail d1
		where d1.enable_flag = 'Y'
		and d1.infor_split_status = 0
		and d1.bill_no = d.bill_no)
		and exists (select 1
		from plugin.iqc_test_requisition_detail d2
		where d2.enable_flag = 'Y'
		and d2.infor_split_status = 2
		and d2.bill_no = d.bill_no)
		and not exists (select 1
		from plugin.iqc_test_requisition_detail d3
		where d3.enable_flag = 'Y'
		and d3.infor_split_status = 1
		and d3.bill_no = d.bill_no)
		and rownum = 1)
		or
		b.bill_no = (select d.bill_no
		from plugin.iqc_test_requisition_detail d
		where d.enable_flag = 'Y'
		and d.bill_no = #{billNo,jdbcType=VARCHAR}
		and not exists (select 1
		from plugin.iqc_test_requisition_detail d1
		where d1.enable_flag = 'Y'
		and d1.infor_split_status = 0
		and d1.bill_no = d.bill_no)
		and exists (select 1
		from plugin.iqc_test_requisition_detail d2
		where d2.enable_flag = 'Y'
		and d2.infor_split_status = 2
		and d2.bill_no = d.bill_no)
		and not exists (select 1
		from plugin.iqc_test_requisition_detail d3
		where d3.enable_flag = 'Y'
		and d3.infor_split_status = 1
		and d3.bill_no = d.bill_no)
		and rownum = 1)
		)
	</update>

	<update id="updateIqcBillNoSplit">
		update plugin.iqc_test_requisition_bill b
		set b.bill_split_status = 0,b.editdate = sysdate
		where b.bill_no = (select d.bill_no
		from plugin.iqc_test_requisition_detail d
		where d.enable_flag = 'Y'
		and d.bill_no = #{billNo,jdbcType=VARCHAR}
		and exists (select 1
		from plugin.iqc_test_requisition_detail d1
		where d1.enable_flag = 'Y'
		and d1.infor_split_status = 0
		and d1.bill_no = d.bill_no)
		and not exists (select 1
		from plugin.iqc_test_requisition_detail d2
		where d2.enable_flag = 'Y'
		and d2.infor_split_status = 2
		and d2.bill_no = d.bill_no)
		and not exists (select 1
		from plugin.iqc_test_requisition_detail d3
		where d3.enable_flag = 'Y'
		and d3.infor_split_status = 1
		and d3.bill_no = d.bill_no)
		and rownum = 1)
	</update>

	<select id="getIqcBillToInfor" resultType="com.zte.domain.model.infor.IqcTestRequisitionBillDTO">
		select b.bill_no,b.bill_fail_times from plugin.iqc_test_requisition_bill b
		where b.bill_status = 1
		and b.bill_split_status = 2
		and b.bill_fail_times &lt; 4
		and not exists (select 1
			from plugin.edi_interface ei
			where ei.externkey = b.bill_no
			and ei.symbol = 1)
		order by b.bill_no
	</select>

	<select id="getIqcTestStatusToIscpListVOTotal" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		left join plugin.sys_lookup_values v on v.lookup_meaning = b.bill_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		left join plugin.sys_lookup_values v1 on v1.lookup_meaning =
		b.bill_split_status
		and v1.lookup_type = '1000052'
		and v1.enabled_flag = 'Y'
		and v1.attribute1 = '2'
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		<if test="deliveryNo != null and deliveryNo !='' ">
			and b.delivery_no  = #{deliveryNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and d.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and d.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status !='' ">
			and b.bill_status = (select v.lookup_meaning from plugin.sys_lookup_values v where v.lookup_type = '1000052'
			and v.enabled_flag = 'Y'
			and v.attribute1 = '1'
			and v.description = #{status,jdbcType=VARCHAR})
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and d.editdate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and d.editdate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<select id="getIqcTestStatusToIscListVo" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO">
		select s.* from (
		select b.bill_no billNo,
		b.stock_no stockNo,
		b.delivery_no deliveryNo,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		nvl(d.reel_pick_qty, d.pkg_pick_qty) qty,
		b.receive_org receiveOrg,
		d.planning_group planningGroup,
		d.product_class productClass,
		b.usage_name usageName,
		b.material_picker materialPicker,
		b.material_pickdept materialPickDept,
		v.description billStatus,
		v1.description splitStatus,
		v2.description splitInforStatus,
		d.adddate createdDate,
		d.editdate updateDate,
		d.new_reelid newReelid, rownum rn
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		left join plugin.sys_lookup_values v on v.lookup_meaning = b.bill_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		left join plugin.sys_lookup_values v1 on v1.lookup_meaning =
		b.bill_split_status
		and v1.lookup_type = '1000052'
		and v1.enabled_flag = 'Y'
		and v1.attribute1 = '2'
		left join plugin.sys_lookup_values v2 on v2.lookup_meaning = d.infor_split_status
		and v2.lookup_type = '1000052'
		and v2.enabled_flag = 'Y'
		and v2.attribute1 = '2'
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		<if test="deliveryNo != null and deliveryNo !='' ">
			and b.delivery_no  = #{deliveryNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and d.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and d.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="status != null and status !='' ">
			and b.bill_status = (select v.lookup_meaning from plugin.sys_lookup_values v where v.lookup_type = '1000052'
			and v.enabled_flag = 'Y'
			and v.attribute1 = '1'
			and v.description = #{status,jdbcType=VARCHAR})
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and d.editdate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and d.editdate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<select id="getIqcTestRequistionListVOTotal" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		left join plugin.sys_lookup_values v on v.lookup_meaning = d.detail_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		left join plugin.sys_lookup_values v1 on v1.lookup_meaning = d.infor_split_status
		and v1.lookup_type = '1000052'
		and v1.enabled_flag = 'Y'
		and v1.attribute1 = '2'
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		<if test="stockNo != null and stockNo !='' ">
			and b.stock_no  = #{stockNo,jdbcType=VARCHAR}
		</if>
		<if test="deliveryNo != null and deliveryNo !='' ">
			and b.delivery_no  = #{deliveryNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and d.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and d.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="pkgId != null and pkgId !='' ">
			and d.pkgid  = #{pkgId,jdbcType=VARCHAR}
		</if>
		<if test="reelId != null and reelId !='' ">
			and d.reelid  = #{reelId,jdbcType=VARCHAR}
		</if>
		<if test="detailStatus != null ">
			and d.detail_status = #{detailStatus,jdbcType=INTEGER}
		</if>
		<if test="inforSplitStatus != null ">
			and d.infor_split_status = #{inforSplitStatus,jdbcType=INTEGER}
		</if>
		<if test="materialPicker != null and materialPicker !='' ">
			and b.material_picker like concat(#{materialPicker,jdbcType=VARCHAR},'%')
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and b.adddate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and b.adddate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<select id="getIqcTestRequistionListVo" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO">
		select s.* from (
		select b.bill_no billNo,
		b.stock_no stockNo,
		b.delivery_no deliveryNo,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		nvl(d.reel_pick_qty, d.pkg_pick_qty) qty,
		b.receive_org receiveOrg,
		d.planning_group planningGroup,
		d.product_class productClass,
		b.usage_name usageName,
		b.material_picker materialPicker,
		b.material_pickdept materialPickDept,
		v.description billStatus,
		v1.description splitStatus,
		d.adddate createdDate,
		d.editdate updateDate,
		d.split_fail_times splitFailTimes,
		d.new_reelid newReelid,
		d.editwho editWho,
		decode(d.infor_split_status,1,d.fail_reason,'') failReason, rownum rn
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		left join plugin.sys_lookup_values v on v.lookup_meaning = d.detail_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		left join plugin.sys_lookup_values v1 on v1.lookup_meaning = d.infor_split_status
		and v1.lookup_type = '1000052'
		and v1.enabled_flag = 'Y'
		and v1.attribute1 = '2'
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		<if test="stockNo != null and stockNo !='' ">
			and b.stock_no  = #{stockNo,jdbcType=VARCHAR}
		</if>
		<if test="deliveryNo != null and deliveryNo !='' ">
			and b.delivery_no  = #{deliveryNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="itemBarcode != null and itemBarcode !='' ">
			and d.item_barcode  = #{itemBarcode,jdbcType=VARCHAR}
		</if>
		<if test="itemNo != null and itemNo !='' ">
			and d.item_no  = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="pkgId != null and pkgId !='' ">
			and d.pkgid  = #{pkgId,jdbcType=VARCHAR}
		</if>
		<if test="reelId != null and reelId !='' ">
			and d.reelid  = #{reelId,jdbcType=VARCHAR}
		</if>
		<if test="detailStatus != null ">
			and d.detail_status = #{detailStatus,jdbcType=INTEGER}
		</if>
		<if test="inforSplitStatus != null ">
			and d.infor_split_status = #{inforSplitStatus,jdbcType=INTEGER}
		</if>
		<if test="materialPicker != null and materialPicker !='' ">
			and b.material_picker like concat(#{materialPicker,jdbcType=VARCHAR},'%')
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and b.adddate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and b.adddate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<update id="batchUpdateIqcBill" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO">
		update plugin.iqc_test_requisition_bill
		set bill_fail_times = 0,
		editwho = #{editWho,jdbcType=VARCHAR},
		editdate = sysdate
		where bill_no in
		<foreach collection="billNoList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>
	<update id="batchUpdateIqcDetail" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO">
		update plugin.iqc_test_requisition_detail
		set split_fail_times = 0,
		editwho = #{editWho,jdbcType=VARCHAR},
		editdate = sysdate
		where infor_split_status = 1
		and bill_no in
		<foreach collection="billNoList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<select id="getIqcTestRequistionBillVOTotal" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.iqc_test_requisition_bill b
		left join plugin.sys_lookup_values v on v.lookup_meaning = b.bill_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		where b.enable_flag = 'Y'
		and b.bill_status = 1
		and (b.bill_fail_times <![CDATA[>=]]> 4 or exists
		(select 1
		from plugin.iqc_test_requisition_detail d
		where d.infor_split_status = 1
		and d.split_fail_times <![CDATA[>=]]> 4
		and d.bill_no = b.bill_no))
		<if test="stockNo != null and stockNo !='' ">
			and b.stock_no  = #{stockNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="materialPicker != null and materialPicker !='' ">
			and b.material_picker like concat(#{materialPicker,jdbcType=VARCHAR},'%')
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and b.adddate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and b.adddate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>
	<select id="getIqcTestRequistionBillVo" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO" resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionQueryDTO">
		select s.* from (
		select b.bill_no billNo,
		b.stock_no stockNo,
		b.delivery_no deliveryNo,
		b.material_picker materialPicker,
		b.material_pickdept materialPickDept,
		v.description billStatus, rownum rn
		from plugin.iqc_test_requisition_bill b
		left join plugin.sys_lookup_values v on v.lookup_meaning = b.bill_status
		and v.lookup_type = '1000052'
		and v.enabled_flag = 'Y'
		and v.attribute1 = '1'
		where b.enable_flag = 'Y'
		and b.bill_status = 1
		and (b.bill_fail_times <![CDATA[>=]]> 4 or exists
		(select 1
		from plugin.iqc_test_requisition_detail d
		where d.infor_split_status = 1
		and d.split_fail_times <![CDATA[>=]]> 4
		and d.bill_no = b.bill_no))
		<if test="stockNo != null and stockNo !='' ">
			and b.stock_no  = #{stockNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo != null and billNo !='' ">
			and b.bill_no  = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="materialPicker != null and materialPicker !='' ">
			and b.material_picker like concat(#{materialPicker,jdbcType=VARCHAR},'%')
		</if>
		<if test="beginDate != null and beginDate !='' ">
			and b.adddate <![CDATA[>=]]> to_date(#{beginDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="endDate != null and endDate !='' ">
			and b.adddate <![CDATA[<=]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>
	<select id="getIqcSoHeader" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader" parameterType="java.lang.String">
		SELECT
		b.bill_no externalOrderKey2,
		#{params.curTime, jdbcType=VARCHAR} requestedShipDate,
		b.usage_code href04,
		b.receive_org href07,
		decode(p.db_type,1,'320','654') href11,
		#{params.curTime, jdbcType=VARCHAR} href30,
		SUBSTR(b.material_picker, -8) href31,
		SUBSTR(b.material_picker, -8) href33,
		'材料' href35,
		'10' href36,
		'其他' href37,
		'0' href38,
		'0' href48,
		'0' href49,
		'NBZZ' href50,
		b.material_pickdept href54,
		'21' consigneeKey
		FROM plugin.iqc_test_requisition_bill b
		join wmsadmin.pl_db p on  UPPER(b.stock_no) = UPPER(P.DB_LOGID)
		WHERE enable_flag='Y'
		and p.isactive =1
		and bill_no = #{billNo, jdbcType=VARCHAR}
	</select>

	<select id="getIqcSoDetail" resultType="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail" parameterType="java.lang.String">
		select a.*, rownum externLineNo
		from (select b.stock_no whseid,
		d.item_no sku,
		0 shippedQty,
		d.item_barcode lottable02,
		' ' lottable06,
		' ' lottable07,
		decode(p.db_type, 2, '10', '') lottable09,
		sum(nvl(d.reel_pick_qty, d.pkg_pick_qty)) REF01,
		'21' ref02,
		b.bill_no externalOrderKey2,
		decode(p.db_type, 2, d.product_class, '') ref32,
		decode(p.db_type, 2, d.planning_group, '') ref34,
		'0' ref42,
		'0' ref45,
		sum(nvl(d.reel_pick_qty, d.pkg_pick_qty)) originalQty
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		join wmsadmin.pl_db p on UPPER(b.stock_no) = UPPER(P.DB_LOGID)
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		and p.isactive = 1
		and d.check_result=1
		and b.bill_no =  #{billNo, jdbcType=VARCHAR}
		group by b.stock_no,
		d.item_no,
		d.item_barcode,
		b.bill_no,
		p.db_type,
		d.product_class,
		d.planning_group) a
	</select>

	<update id="updateIqcBill" parameterType="com.zte.domain.model.infor.IqcTestRequisitionBillDTO">
		update plugin.iqc_test_requisition_bill
		set
		<if test="billFailTimes != null ">
			bill_fail_times = #{billFailTimes, jdbcType=INTEGER},
		</if>
		<if test="billStatus != null ">
			bill_status = #{billStatus, jdbcType=INTEGER},
		</if>
			editdate = sysdate
		where 1=1
		<if test="billNo != null and billNo !='' ">
			and bill_no = #{billNo,jdbcType=VARCHAR}
		</if>
		<if test="billNo == null or billNo =='' ">
			and 1=2
		</if>
	</update>

	<select id="selectIqcEmailListCount"  resultType="java.lang.Integer">
		select count(*)
		from (select '明细拆分失败' failType,
		b.bill_no billNo,
		b.delivery_no deliveryNo,
		b.stock_no stockNo,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		nvl(d.reel_pick_qty, d.pkg_pick_qty) qty
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		and d.split_fail_times  <![CDATA[>=]]> 4
		and d.infor_split_status = 1
		union all
		select '单据提交失败' failType,
		b.bill_no billNo,
		b.delivery_no deliveryNo,
		b.stock_no stockNo,
		null itemNo,
		null itemBarcode,
		null pkgId,
		null reelId,
		0 qty
		from plugin.iqc_test_requisition_bill b
		where b.enable_flag = 'Y'
		and b.bill_fail_times  <![CDATA[>=]]> 4
		and b.bill_status = 1
		and b.bill_split_status = 2)
	</select>

	<select id="selectIqcEmailListVo" parameterType="com.zte.interfaces.infor.dto.IqcTestRequisitionEmailDTO"
			resultType="com.zte.interfaces.infor.dto.IqcTestRequisitionEmailDTO">
		select s.* from (select '明细拆分失败' failType,
		b.bill_no billNo,
		b.delivery_no deliveryNo,
		b.stock_no stockNo,
		d.item_no itemNo,
		d.item_barcode itemBarcode,
		d.pkgid pkgId,
		d.reelid reelId,
		nvl(d.reel_pick_qty, d.pkg_pick_qty) qty,
		d.fail_reason failReason,rownum rn
		from plugin.iqc_test_requisition_bill b
		join plugin.iqc_test_requisition_detail d on b.bill_no = d.bill_no
		where b.enable_flag = 'Y'
		and d.enable_flag = 'Y'
		and d.split_fail_times  <![CDATA[>=]]> 4
		and d.infor_split_status = 1
		union all
		select '单据提交失败' failType,
		b.bill_no billNo,
		b.delivery_no deliveryNo,
		b.stock_no stockNo,
		null itemNo,
		null itemBarcode,
		null pkgId,
		null reelId,
		0 qty,
		null failReason,rownum rn
		from plugin.iqc_test_requisition_bill b
		where b.enable_flag = 'Y'
		and b.bill_fail_times  <![CDATA[>=]]> 4
		and b.bill_status = 1
		and b.bill_split_status = 2) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<update id="updateIqcBillByInfoSuccess">
		update plugin.iqc_test_requisition_bill b
		set b.bill_status = 2, b.editdate = sysdate
		where b.bill_no in (select s.externalorderkey2
		from plugin.edi_so_s s
		where s.invsymbol = 1
		and s.externalorderkey2 = b.bill_no)
		and b.bill_status = 3
	</update>
</mapper>