<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DigitQCDataBaseRepository">

    <select id="selectWmwhseId" resultType="java.lang.String">
        select DISTINCT UPPER(db_logid)
        from wmsadmin.pl_db
        where isactive = '1'
            and db_type in ('1','2', '5')
    </select>
    <select id="selectQCDataBase" resultType="java.lang.Integer">
        select count(1) from PLUGIN.QC_DATABASE_HEDA where ENABLED_FLAG = 'Y'
    </select>
    <select id="queryItemNum" resultType="java.lang.Integer">
        select count(DISTINCT sku) from (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            SELECT sl.sku
            FROM ${item}.skuxloc sl
            where sl.qty > 0
        </foreach>
        )
    </select>
    <select id="queryItemsByPage" resultType="java.lang.String">
        SELECT DISTINCT sku
        FROM (
        SELECT inner_query.sku, ROWNUM rn
        FROM (
        <foreach collection="whseidList" item="item" index="index" separator="UNION ALL">
            SELECT sl.sku
            FROM ${item}.skuxloc sl
            WHERE sl.qty > 0
        </foreach>
        ) inner_query
        WHERE ROWNUM <![CDATA[<=]]> #{pageSize} + #{offset}
        ) outer_query
        WHERE rn > #{offset}
        ORDER BY sku
    </select>
    <insert id="insertItemInfos">
        insert into plugin.QC_DATABASE_HEDA
        (serialkey,ITEM_NO,SUPPLIER_NO,SUPPLIER_NAME,PACKAGE_TYPE,state,MATERIAL_CATEGORY,ESD_PROTECTION_LEVEL)
        select PLUGIN.SEQ_QC_DATABASE_HEDA.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.itemNo,jdbcType=VARCHAR},#{item.supplierNo,jdbcType=VARCHAR},#{item.supplierName,jdbcType=VARCHAR},
            #{item.packTypeName,jdbcType=VARCHAR},'1',#{item.master,jdbcType=VARCHAR},#{item.antiElectricLevel,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) temp
    </insert>
    <select id="queryAddItemInfo" resultType="java.lang.String">
        SELECT DISTINCT sku
        FROM (
        <foreach collection="whseidList" item="item" index="index" separator="UNION ALL">
            SELECT sl.sku
            FROM ${item}.skuxloc sl
            WHERE sl.qty > 0
            AND sl.editdate <![CDATA[>=]]> to_date(#{date,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </foreach>
        ) skuquery
    </select>
    <select id="queryItemNoIsExist" resultType="java.lang.Integer">
        select count(1) from plugin.QC_DATABASE_HEDA
        where ENABLED_FLAG = 'Y'
        and state = '1'
        and ITEM_NO = #{itemNo,jdbcType=VARCHAR}
        and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
    </select>
    <update id="updateItemNoInfo">
        update plugin.QC_DATABASE_HEDA
        set SUPPLIER_NAME = #{itemName,jdbcType=VARCHAR},
        PACKAGE_TYPE = #{packTypeName,jdbcType=VARCHAR},
        MATERIAL_CATEGORY = #{master,jdbcType=VARCHAR},
        ESD_PROTECTION_LEVEL = #{antiElectricLevel,jdbcType=VARCHAR}
        where ENABLED_FLAG = 'Y'
        and state = '1'
        and ITEM_NO = #{itemNo,jdbcType=VARCHAR}
        and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
    </update>
    <select id="getDigitQCDataBaseTotal" resultType="java.lang.Integer">
        select count(1)
        from plugin.QC_DATABASE_HEDA
        where enabled_flag = 'Y'
        <if test="itemNo != null and itemNo !='' ">
            and ITEM_NO  = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="supplierNo != null and supplierNo !='' ">
            and SUPPLIER_NO  = #{supplierNo,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state !='' ">
            and state  = #{state,jdbcType=VARCHAR}
        </if>
        <if test="packType != null and packType !='' ">
            and PACKAGE_TYPE  = #{packType,jdbcType=VARCHAR}
        </if>
        <if test="customInfo != null and customInfo !='' ">
            <choose>
                <when test="custom == 'custom1'">
                    AND custom1 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom2'">
                    AND custom2 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom3'">
                    AND custom3 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom4'">
                    AND custom4 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
            </choose>
        </if>
        <if test="esdProtectionLevel != null and esdProtectionLevel !='' ">
            and ESD_PROTECTION_LEVEL  = #{esdProtectionLevel,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdatedBy != null and lastUpdatedBy !='' ">
            and LAST_UPDATED_BY  = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
    <select id="getDigitQCDataBaseVo" resultType="com.zte.domain.model.infor.DigitQCDataBaseHeadDTO">
        select B.* from (
        select A.*,rownum rn from(
        select t.serialKey,
        t.ITEM_NO itemNo,
        t.SUPPLIER_NO supplierNo,
        t.SUPPLIER_NAME supplierName,
        t.MATERIAL_CATEGORY materialCategory,
        t.ESD_PROTECTION_LEVEL esdProtectionLevel,
        t.PACKAGE_TYPE packageType,
        decode(t.state,'1','有效','失效') state,
        t.custom1,
        t.custom2,
        t.custom3,
        t.custom4,
        t.SPECIAL_DESC specialDesc,
        t.LAST_UPDATED_BY lastUpdatedBy,
        t.LAST_UPDATE_DATE lastUpdateDate
        from plugin.QC_DATABASE_HEDA t
        where t.enabled_flag = 'Y'
        <if test="itemNo != null and itemNo !='' ">
            and ITEM_NO  = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="supplierNo != null and supplierNo !='' ">
            and SUPPLIER_NO  = #{supplierNo,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state !='' ">
            and state  = #{state,jdbcType=VARCHAR}
        </if>
        <if test="packType != null and packType !='' ">
            and PACKAGE_TYPE  = #{packType,jdbcType=VARCHAR}
        </if>
        <if test="customInfo != null and customInfo !='' ">
            <choose>
                <when test="custom == 'custom1'">
                    AND custom1 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom2'">
                    AND custom2 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom3'">
                    AND custom3 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
                <when test="custom == 'custom4'">
                    AND custom4 LIKE '%' || #{customInfo,jdbcType=VARCHAR} || '%'
                </when>
            </choose>
        </if>
        <if test="esdProtectionLevel != null and esdProtectionLevel !='' ">
            and ESD_PROTECTION_LEVEL  = #{esdProtectionLevel,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdatedBy != null and lastUpdatedBy !='' ">
            and LAST_UPDATED_BY  = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by t.LAST_UPDATE_DATE desc
        )A )B
        where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  b.rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  b.rn &lt;= 500000
        </if>
    </select>
    <update id="digitQCDataBaseInfoLose">
        update plugin.QC_DATABASE_HEDA
        set state = '2',
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and serialkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="digitQCDataBaseInfoEffect">
        update plugin.QC_DATABASE_HEDA
        set state = '1',
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and serialkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateQCDataBase">
        update plugin.QC_DATABASE_HEDA
        <set>
            LAST_UPDATE_DATE = sysdate,
            LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
            <if test="custom1 != null and custom1 != ''">
                custom1 = #{custom1,jdbcType=VARCHAR},
            </if>
            <if test="custom2 != null and custom2 != ''">
                custom2 = #{custom2,jdbcType=VARCHAR},
            </if>
            <if test="custom3 != null and custom3 != ''">
                custom3 = #{custom3,jdbcType=VARCHAR},
            </if>
            <if test="custom4 != null and custom4 != ''">
                custom4 = #{custom4,jdbcType=VARCHAR},
            </if>
            <if test="specialDesc != null and specialDesc != ''">
                SPECIAL_DESC = #{specialDesc,jdbcType=VARCHAR},
            </if>
        </set>
        where enabled_flag = 'Y'
        and state = '1'
        and ITEM_NO  = #{itemNo,jdbcType=VARCHAR}
        <if test="supplierNo != null and supplierNo != ''">
            and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
        </if>
    </update>
    <select id="queryIsExist" resultType="java.lang.Integer">
        select count(1) from plugin.QC_DATABASE_HEDA
        where ENABLED_FLAG = 'Y'
        and state = '1'
        and ITEM_NO = #{itemNo,jdbcType=VARCHAR}
        and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
    </select>
    <insert id="insertDataBase">
        insert into plugin.QC_DATABASE_HEDA
        (serialKey,ITEM_NO,SUPPLIER_NO,SUPPLIER_NAME,MATERIAL_CATEGORY,ESD_PROTECTION_LEVEL,PACKAGE_TYPE,STATE,
        CUSTOM1,CUSTOM2,CUSTOM3,CUSTOM4,SPECIAL_DESC,CREATED_BY,LAST_UPDATED_BY) values
        (PLUGIN.SEQ_QC_DATABASE_HEDA.nextval,
        #{itemNo,jdbcType=VARCHAR},
        #{supplierNo,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{materialCategory,jdbcType=VARCHAR},
        #{esdProtectionLevel,jdbcType=VARCHAR},
        #{packType,jdbcType=VARCHAR},
        '1',
        #{custom1,jdbcType=VARCHAR},
        #{custom2,jdbcType=VARCHAR},
        #{custom3,jdbcType=VARCHAR},
        #{custom4,jdbcType=VARCHAR},
        #{specialDesc,jdbcType=VARCHAR},
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{lastUpdatedBy,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateDataBase">
        update plugin.QC_DATABASE_HEDA
        set
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        CUSTOM1 = #{custom1,jdbcType=VARCHAR},
        CUSTOM2 = #{custom2,jdbcType=VARCHAR},
        CUSTOM3 = #{custom3,jdbcType=VARCHAR},
        CUSTOM4 = #{custom4,jdbcType=VARCHAR},
        SPECIAL_DESC = #{specialDesc,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and state = '1'
        and ITEM_NO  = #{itemNo,jdbcType=VARCHAR}
        and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
    </update>
    <select id="queryObjectExist" resultType="java.lang.Integer">
        select count(1) from plugin.QC_DATABASE_DETAIL
        where ENABLED_FLAG = 'Y'
        and type = #{type,jdbcType=VARCHAR}
        and CONDITION = #{condition,jdbcType=VARCHAR}
    </select>
    <insert id="insertObjectInfo">
        insert into plugin.QC_DATABASE_DETAIL
        (serialkey,TYPE,CONDITION,WARN_VALUE,CREATED_BY,LAST_UPDATED_BY)
        select PLUGIN.SEQ_QC_DATABASE_DETAIL.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.type,jdbcType=VARCHAR},#{item.condition,jdbcType=VARCHAR},#{item.warnValue,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},#{item.lastUpdatedBy,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) temp
    </insert>
    <update id="updateObjectInfo">
        update plugin.QC_DATABASE_DETAIL
        set WARN_VALUE = #{warnValue,jdbcType=VARCHAR},
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATE_DATE = sysdate
        where ENABLED_FLAG = 'Y'
        and type = #{type,jdbcType=VARCHAR}
        and CONDITION = #{condition,jdbcType=VARCHAR}
    </update>
    <select id="getObjectValueTotal" resultType="java.lang.Integer">
        select count(1)
        from plugin.QC_DATABASE_DETAIL
        where enabled_flag = 'Y'
        <if test="type != null and type !='' ">
            and TYPE  = #{type,jdbcType=VARCHAR}
        </if>
        <if test="condition != null and condition !='' ">
            and CONDITION  = #{condition,jdbcType=VARCHAR}
        </if>
        <if test="warnValue != null and warnValue !='' ">
            and WARN_VALUE  = #{warnValue,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
    <select id="getObjectValueVo" resultType="com.zte.domain.model.infor.DigitObjectValueDTO">
        select B.* from (
        select A.*,rownum rn from(
        select t.serialKey,
        t.type,
        t.condition,
        t.WARN_VALUE warnValue,
        t.LAST_UPDATED_BY lastUpdatedBy,
        t.LAST_UPDATE_DATE lastUpdateDate
        from plugin.QC_DATABASE_DETAIL t
        where t.enabled_flag = 'Y'
        <if test="type != null and type !='' ">
            and t.TYPE  = #{type,jdbcType=VARCHAR}
        </if>
        <if test="condition != null and condition !='' ">
            and t.CONDITION  = #{condition,jdbcType=VARCHAR}
        </if>
        <if test="warnValue != null and warnValue !='' ">
            and t.WARN_VALUE  = #{warnValue,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and t.LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and t.LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by t.LAST_UPDATE_DATE desc
        )A )B
        where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  b.rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  b.rn &lt;= 500000
        </if>
    </select>
    <update id="deleteObjectInfo">
        update plugin.QC_DATABASE_DETAIL
        set enabled_flag = 'N',
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        LAST_UPDATE_DATE = sysdate
        where serialkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="queryDataBaseInfoList" resultType="com.zte.domain.model.infor.DigitQCDataBaseHeadDTO">
        select t.serialKey,
        t.ITEM_NO itemNo,
        t.SUPPLIER_NO supplierNo,
        t.SUPPLIER_NAME supplierName,
        t.MATERIAL_CATEGORY materialCategory,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '物料大类' and qdd.condition = t.MATERIAL_CATEGORY) materialCategoryWarnValue,
        t.ESD_PROTECTION_LEVEL esdProtectionLevel,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '防静电等级' and qdd.condition = t.ESD_PROTECTION_LEVEL) esdProtectionLevelWarnValue,
        t.PACKAGE_TYPE packageType,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '包装形式' and qdd.condition = t.PACKAGE_TYPE) packageTypeWarnValue,
        decode(t.state,'1','有效','失效') state,
        t.custom1,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '自定义1' and qdd.condition = t.custom1) custom1WarnValue,
        t.custom2,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '自定义2' and qdd.condition = t.custom2) custom2WarnValue,
        t.custom3,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '自定义3' and qdd.condition = t.custom3) custom3WarnValue,
        t.custom4,
        (select qdd.WARN_VALUE from plugin.QC_DATABASE_DETAIL qdd
        where qdd.enabled_flag = 'Y' and qdd.type = '自定义4' and qdd.condition = t.custom4) custom4WarnValue,
        t.SPECIAL_DESC specialDesc,
        t.LAST_UPDATED_BY lastUpdatedBy,
        t.LAST_UPDATE_DATE lastUpdateDate
        from plugin.QC_DATABASE_HEDA t
        where t.enabled_flag = 'Y'
        and state = '1'
        and ITEM_NO  = #{itemNo,jdbcType=VARCHAR}
        and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
    </select>
</mapper>