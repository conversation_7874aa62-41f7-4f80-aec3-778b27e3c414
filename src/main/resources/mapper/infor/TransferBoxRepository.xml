<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.TransferBoxRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.infor.dto.ZmsTransferBox">
        <!-- Started by AICoder, pid:qcb5396c9c505c0149bc09b3e07d3623ee226fc9 -->
        <id column="SERIALKEY" property="serialKey" jdbcType="NUMERIC"/>
        <result column="EXTERNALKEY" property="externalKey" jdbcType="NVARCHAR"/>
        <result column="WHSEID" property="whseId" jdbcType="NVARCHAR"/>
        <result column="SOURCEKEY" property="sourceKey" jdbcType="NVARCHAR"/>
        <result column="OLD_PKG_ID" property="oldPkgId" jdbcType="NVARCHAR"/>
        <result column="NEW_PKG_ID" property="newPkgId" jdbcType="NVARCHAR"/>
        <result column="LOC" property="loc" jdbcType="NVARCHAR"/>
        <result column="LOT" property="lot" jdbcType="NVARCHAR"/>
        <result column="QTY" property="qty" jdbcType="DECIMAL"/>
        <result column="MPN" property="mpn" jdbcType="NVARCHAR"/>
        <result column="SKU" property="sku" jdbcType="NVARCHAR"/>
        <result column="ITEM_BARCODE" property="itemBarcode" jdbcType="NVARCHAR"/>
        <result column="STATUS" property="status" jdbcType="NVARCHAR"/>
        <result column="RECEIPT_DATE" property="receiptDate" jdbcType="DATE"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="NVARCHAR"/>
        <result column="CREATION_DATE" property="creationDate" jdbcType="DATE"/>
        <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="NVARCHAR"/>
        <result column="LAST_UPDATE_DATE" property="lastUpdateDate" jdbcType="DATE"/>
        <result column="ENABLED_FLAG" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="MESSAGE_ID" property="messageId" jdbcType="NVARCHAR"/>
        <result column="MESSAGE_TYPE" property="messageType" jdbcType="NVARCHAR"/>
        <result column="SEND_STATUS" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="SEND_TIMES" property="sendTimes" jdbcType="NUMERIC"/>
        <!-- Ended by AICoder, pid:qcb5396c9c505c0149bc09b3e07d3623ee226fc9 -->
    </resultMap>

    <select id="selectSyncData" resultType="com.zte.interfaces.infor.dto.TransferReceiptDetailDTO">
        SELECT
        rd.externreceiptkey externalKey,
        rd.receiptkey sourceKey,
        rd.whseid,
        rd.toid fromId,
        null toId,
        rd.toloc toLoc,
        rd.tolot toLot,
        rd.qtyreceived qty,
        s.customer_item_no AS customerItemNo,
        rd.sku,
        rd.lottable02,
        rd.datereceived,
        0 unPack
        FROM ${whseId}.receiptdetail rd
        JOIN ${whseId}.sku s ON s.sku = rd.sku
        WHERE rd.qtyreceived > 0
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        AND EXISTS (
        SELECT 1 FROM plugin.edi_so_s so
        WHERE rd.externreceiptkey = so.externalorderkey2
        AND so.href11 IN ('430', '432')
        AND EXISTS (
        SELECT 1 FROM wmsadmin.pl_db pd
        WHERE pd.db_type IN (1, 2, 4)
        AND pd.db_enterprise = 0
        AND UPPER(pd.db_logid) = so.href14
        )
        AND NOT EXISTS (
        SELECT 1 FROM wmsadmin.pl_db pd
        WHERE pd.db_type = 4
        AND pd.db_enterprise = 0
        AND UPPER(pd.db_logid) = so.href15
        )
        )
        AND NOT EXISTS (
        SELECT 1 FROM ${whseId}.receipt t
        WHERE t.externreceiptkey = rd.externreceiptkey
        AND t.status <![CDATA[<]]> 11
        )
        AND EXISTS (
        SELECT 1 FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        AND NOT EXISTS (
        SELECT 1 FROM PLUGIN.ZMS_TRANSFER_BOX zt
        WHERE zt.externalkey = rd.externreceiptkey
        )

        union all

        select o.externalorderkey2 externalKey,
        pd.orderkey sourceKey,
        pd.whseid,
        it.fromid fromId,
        it.toid toId,
        pd.toloc toLoc,
        pd.lot toLot,
        pd.qty,
        s.customer_item_no AS customerItemNo,
        pd.sku,
        l.lottable02,
        null datereceived,
        1 unPack
        from ${whseId}.pickdetail pd
        JOIN ${whseId}.orders o
        ON pd.orderkey = o.orderkey
        JOIN ${whseId}.itrn it
        ON pd.pickdetailkey = it.sourcekey
        AND pd.lot = it.lot
        AND it.trantype = 'MV'
        AND it.sourcetype = 'PICKING'
        AND it.fromid <![CDATA[<>]]> it.toid
        JOIN ${whseId}.lotattribute l
        ON pd.lot = l.lot
        JOIN ${whseId}.sku s
        ON s.sku = pd.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        WHERE pd.qty > 0
        AND pd.status = '9'
        AND o.status = '95'
        AND o.ref11 IN ('430', '432')
        AND EXISTS (SELECT 1
        FROM wmsadmin.pl_db pd
        WHERE pd.db_type IN (1, 2, 4)
        AND pd.db_enterprise = 0
        AND UPPER(pd.db_logid) = o.ref14)
        AND NOT EXISTS (SELECT 1
        FROM wmsadmin.pl_db pd
        WHERE pd.db_type = 4
        AND pd.db_enterprise = 0
        AND UPPER(pd.db_logid) = o.ref15)
        AND EXISTS (SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = it.fromid)
        AND NOT EXISTS (SELECT 1
        FROM PLUGIN.ZMS_TRANSFER_BOX zt
        WHERE zt.externalkey = o.externalorderkey2
        and zt.sourcekey = pd.orderkey
        and zt.new_pkg_id = it.toid)
    </select>

    <insert id="batchInsertTransferBox" parameterType="list">
        INSERT INTO PLUGIN.ZMS_TRANSFER_BOX (
        SERIALKEY, EXTERNALKEY, WHSEID, SOURCEKEY,
        OLD_PKG_ID, NEW_PKG_ID, LOC, LOT, QTY, MPN, SKU,
        ITEM_BARCODE, STATUS, RECEIPT_DATE, CREATED_BY,
        CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE,
        ENABLED_FLAG, MESSAGE_ID, MESSAGE_TYPE, SEND_STATUS, SEND_TIMES
        )
        SELECT PLUGIN.ZMS_TRANSFER_BOX_S.nextval, temp.* FROM (
        <foreach collection="list" item="item" index="index" separator=" UNION ALL ">
            SELECT
            #{item.externalKey,jdbcType=NVARCHAR} EXTERNALKEY,
            #{item.whseId,jdbcType=NVARCHAR} WHSEID,
            #{item.sourceKey,jdbcType=NVARCHAR} SOURCEKEY,
            #{item.oldPkgId,jdbcType=NVARCHAR} OLD_PKG_ID,
            #{item.newPkgId,jdbcType=NVARCHAR} NEW_PKG_ID,
            #{item.loc,jdbcType=NVARCHAR} LOC,
            #{item.lot,jdbcType=NVARCHAR} LOT,
            #{item.qty,jdbcType=DECIMAL} QTY,
            #{item.mpn,jdbcType=NVARCHAR} MPN,
            #{item.sku,jdbcType=NVARCHAR} SKU,
            #{item.itemBarcode,jdbcType=NVARCHAR} ITEM_BARCODE,
            #{item.status,jdbcType=NVARCHAR} STATUS,
            #{item.receiptDate,jdbcType=DATE} RECEIPT_DATE,
            #{item.createdBy,jdbcType=NVARCHAR} CREATED_BY,
            sysdate CREATION_DATE,
            #{item.lastUpdatedBy,jdbcType=NVARCHAR} LAST_UPDATED_BY,
            sysdate LAST_UPDATE_DATE,
            'Y' ENABLED_FLAG,
            #{item.messageId,jdbcType=NVARCHAR} MESSAGE_ID,
            #{item.messageType,jdbcType=NVARCHAR} MESSAGE_TYPE,
            #{item.sendStatus,jdbcType=VARCHAR} SEND_STATUS,
            #{item.sendTimes,jdbcType=NUMERIC} SEND_TIMES
            FROM DUAL
        </foreach>
        ) temp
    </insert>

    <select id="selectPendingTransfer" resultMap="BaseResultMap">
        SELECT SERIALKEY, EXTERNALKEY, WHSEID, SOURCEKEY,
        OLD_PKG_ID, NEW_PKG_ID, LOC, LOT,QTY, MPN, SKU,
        ITEM_BARCODE,
        status, RECEIPT_DATE, CREATED_BY,
        CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE,
        ENABLED_FLAG, MESSAGE_ID, MESSAGE_TYPE, SEND_STATUS, SEND_TIMES
        FROM PLUGIN.ZMS_TRANSFER_BOX
        where ENABLED_FLAG = 'Y' AND STATUS = 1 AND SEND_STATUS = -1 AND SEND_TIMES <![CDATA[<]]> 4
    </select>

    <select id="selectSnByExternalKeyAndPkgs" resultType="com.zte.interfaces.infor.dto.SnBoundDetail">
        select zbd.pkg_id,zbd.sn
        from plugin.zms_sn_bound_detail zbd
        where zbd.externalkey = #{externalKey,jdbcType=NVARCHAR}
        and zbd.pkg_id in
        <foreach item='pkg' collection='pkgIds' open='(' separator=',' close=')'>
            #{pkg,jdbcType=NVARCHAR}
        </foreach>
        and TRAN_TYPE = 'WD'
    </select>

    <update id="batchUpdateSendStatus">
        UPDATE PLUGIN.ZMS_TRANSFER_BOX SET
        SEND_STATUS = #{status,jdbcType=NVARCHAR},
        SEND_TIMES = SEND_TIMES + 1,
        LAST_UPDATE_DATE = SYSDATE
        WHERE SERIALKEY IN
        <foreach item='item' collection='ids' open='(' separator=',' close=')'>
            #{item,jdbcType=NUMERIC}
        </foreach>
    </update>

    <update id="updateSendStatus">
        UPDATE PLUGIN.ZMS_TRANSFER_BOX SET
        SEND_STATUS = #{status,jdbcType=NVARCHAR},
        LAST_UPDATE_DATE = SYSDATE
        WHERE MESSAGE_ID = #{messageId,jdbcType=NVARCHAR}
    </update>

    <select id="getPoSoBySourceKey" parameterType="com.zte.action.iscpedi.model.IscpEdiLog" resultType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        select t.externreceiptkey billNo, t.externlineno detailId, t.receiptkey sourcekey, sum(t.qtyreceived) qty
        from ${whseid}.receiptdetail t
        where t.externreceiptkey = #{externkey, jdbcType=NVARCHAR}
        and t.receiptkey = #{sourcekey, jdbcType=NVARCHAR}
        and t.status = '15'
        and t.qtyreceived > 0
        group by t.externreceiptkey, t.externlineno, t.receiptkey
        union all
        select o.externalorderkey2 billNo, od.externlineno detailId, p.pickdetailkey sourcekey, p.qty
        from ${whseid}.pickdetail p
        join ${whseid}.orderdetail od
        on od.orderkey = p.orderkey
        and od.orderlinenumber = p.orderlinenumber
        join ${whseid}.orders o
        on o.orderkey = od.orderkey
        where p.pickdetailkey = #{sourcekey, jdbcType=NVARCHAR}
        and o.externalorderkey2 = #{externkey, jdbcType=NVARCHAR}
        and p.status = '9'
        and p.qty > 0
    </select>
</mapper>