<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DelayBillRepository">

	<update id="updateSendCheckParam">
		update plugin.delay_recheck_bill_head h
		set h.send_params_result = #{param}
		where h.recheck_no = #{recheckNo,jdbcType=VARCHAR}
	</update>

	<select id="getRecheckNoNum"  parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(1) from
		plugin.delay_recheck_bill_head
		where recheck_no = #{recheckNo,jdbcType=VARCHAR}
		and status = 'NEW'
	</select>

	<select id="getQaExInspectionDetail" resultType="com.zte.interfaces.infor.dto.QaExInspectionDetail"  parameterType="java.lang.String">
		select h.recheck_no receiveNo,
		d.serialkey lineId,
		h.recheck_no receiptKey,
		case when d.location='LTC001A' then 'NJLTC' when d.location='LTC002A' then 'NJLTC' else to_char(d.location) end location,
		'In_Inspection' testMode,
		d.item_no itemNo,
		d.item_name itemName,
		d.qty deliveryQty,
		0 isNeedSupReport,
		d.pkg_id packNo,
		h.whseid stock
		from plugin.delay_recheck_bill_head h
		join plugin.delay_recheck_bill_detail d
		on h.recheck_no = d.recheck_no
		where h.recheck_no = #{recheckNo,jdbcType=VARCHAR}
	</select>

	<select id="getQaExInspectionHead" resultType="com.zte.interfaces.infor.dto.QaExInspectionHead" parameterType="java.lang.String" >
		select 'iWMS' dataType,
		'04' billType,
		h.item_barcode itemBarcode,
		h.recheck_no receiveNo,
		h.recheck_no receiptKey,
		'4977' purchaseOrg,
		'4977' receiptOrg,
		'SC' deliveryType,
		'Normal' shipmentMode,
		h.whseid stock,
		case h.recheck_type when 3 then 1 else 0 end isOverTwice
		from plugin.delay_recheck_bill_head h
		where h.recheck_no = #{recheckNo,jdbcType=VARCHAR}
	</select>

	<select id="getQaExInspectionPackWithPackNo" resultType="com.zte.interfaces.infor.dto.QaExInspectionPack" parameterType="com.zte.interfaces.infor.dto.QaExInspectionDetail" >
		select d.recheck_no receiveNo,
		d.recheck_no receiptKey,
		d.serialkey lineId,
		d.pkg_id packNo,
		to_char(h.product_date, 'yyyy-mm-dd') productionDate,
		d.item_barcode code22,
		d.qty qty,
		d.location packLocation
		from plugin.delay_recheck_bill_detail d
		left join plugin.delay_recheck_bill_head h
		on d.recheck_no=h.recheck_no
		where d.recheck_no = #{receiveNo,jdbcType=VARCHAR}
		and d.serialkey = #{lineId,jdbcType=VARCHAR}
		and d.pkg_id = #{packNo,jdbcType=VARCHAR}
	</select>

	<select id="getQaExInspectionPack" resultType="com.zte.interfaces.infor.dto.QaExInspectionPack" parameterType="com.zte.interfaces.infor.dto.QaExInspectionDetail" >
		select d.recheck_no receiveNo,
		d.recheck_no receiptKey,
		d.serialkey lineId,
		d.item_barcode packNo,
		to_char(h.product_date, 'yyyy-mm-dd') productionDate,
		d.item_barcode code22,
		d.qty qty
		from plugin.delay_recheck_bill_detail d
		left join plugin.delay_recheck_bill_head h
		on d.recheck_no=h.recheck_no
		where d.recheck_no = #{receiveNo,jdbcType=VARCHAR}
		and d.serialkey = #{lineId,jdbcType=VARCHAR}
	</select>

	<resultMap id="queryInventoryMap" type="com.zte.interfaces.material.dto.OverTimeOutDTO">
		<result column="item_barcode" jdbcType="VARCHAR" property="itemBarcode" />
		<result column="is_ok" jdbcType="VARCHAR" property="isOk" />
	</resultMap>
	<select id="queryInventory" parameterType="com.zte.interfaces.material.dto.OverTimeInDTO" resultMap="queryInventoryMap">
		select distinct wh.item_barcode, 'Y' is_ok
		from (
			select a.item_barcode from (
			select t.item_barcode,
			trunc(sysdate)-trunc(max(t.check_end_date)) as diff
			from plugin.delay_recheck_bill_detail t
			where  t.whseid = #{whseid,jdbcType=VARCHAR}
			and t.item_barcode IN
			<foreach collection="itemBarcode" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
			and t.check_result =1
			group by t.item_barcode
			) a where a.diff <![CDATA[<]]> #{warningDays,jdbcType=INTEGER}
			union all
			select d.item_code item_barcode
			from plugin.delay_whitelists d
			where d.white_status = 1
			and d.whseid = #{whseid,jdbcType=VARCHAR}
			and d.orderkey = #{orderKey,jdbcType=VARCHAR}
			and d.item_code IN
			<foreach collection="itemBarcode" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		) wh
	</select>
</mapper>