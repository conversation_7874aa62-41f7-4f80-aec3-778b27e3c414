<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.UpdateInforStageRepository">

	<select id="getAllWhse" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT DISTINCT UPPER(P.DB_LOGID) WHSEID
		FROM WMSADMIN.PL_DB P
		WHERE P.ISACTIVE = 1
		AND UPPER(P.DB_LOGID)<![CDATA[<>]]>  'ENTERPRISE'
		AND EXISTS (SELECT 1
		FROM ENTERPRISE.CODELKUP L
		WHERE L.LISTNAME LIKE '%ZWHPICKTO%'
		AND L.ACTIVE = '1'
		AND UPPER(L.CODE) = UPPER(P.DB_LOGID))
	</select>
	<select id="getCodelKup" resultType="com.zte.interfaces.infor.dto.CodelKup">
		select l.whseid, l.active,l.udf1, l.udf2, l.udf3, l.udf4, l.udf5
		from enterprise.codelkup l
		where l.listname like '%ZWHPICKTO%'
		and l.active = '1'
		and upper(l.code)= upper(#{whseId,jdbcType=VARCHAR})
	</select>

	<select id="getOrdersList" resultType="com.zte.interfaces.infor.dto.Orders">
		SELECT t.orderkey, t.whseid whseId, trim(t.stage) stage
		FROM ${whseId}.orders t
		where t.status = '02'
		and t.ref11 in ('100', '101')
		and trim(t.stage) is null
		order by t.orderkey asc
	</select>

	<update id="updateStageByOrders" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
			update ${item.whseId}.orders t
			<set>
				t.stage = #{item.stage,jdbcType=VARCHAR}
			</set>
			<where>
				1=1 and  t.orderkey = #{item.orderKey,jdbcType=VARCHAR}
				and t.status = '02'
				and t.ref11 in ('100', '101')
			</where>
		</foreach>
	</update>

	<update id="updateCodeListByWhse">
		update enterprise.codelkup l
		set l.udf2 = decode(#{pickTo,jdbcType=VARCHAR},'600','1',#{pickTo,jdbcType=VARCHAR}),
		l.udf1 = #{tim,jdbcType=VARCHAR},
		l.editdate = sysdate
		where l.listname = 'ZWHPICKTO'
		and l.active = '1'
		and upper(l.code)= upper(#{whseId,jdbcType=VARCHAR})
	</update>
</mapper>