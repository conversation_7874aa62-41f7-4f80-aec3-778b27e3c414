<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DeliveryTaskProgressRepository">
	<resultMap id="BaseResultMap" type="com.zte.interfaces.infor.dto.DeliverTaskProgressDTO">
		<result column="externorderkey" jdbcType="VARCHAR" property="prodplanNo" />
		<result column="whseid" jdbcType="VARCHAR" property="whseId" />
		<result column="adddate" jdbcType="TIMESTAMP" property="demandDate" />
		<result column="actualshipdate" jdbcType="TIMESTAMP" property="actualShipDate" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="href20" jdbcType="VARCHAR" property="outSupplier" />
		<result column="href53" jdbcType="VARCHAR" property="inforLoc" />
		<result column="href55" jdbcType="VARCHAR" property="lineSideName" />
		<result column="iskititems" jdbcType="VARCHAR" property="isComplete" />
		<result column="company" jdbcType="VARCHAR" property="company" />
	</resultMap>

	<select id="queryOrderList" parameterType="java.util.List" resultMap="BaseResultMap">
		select q.whseid,
		q.externorderkey,
		min(q.adddate) adddate,
		max(q.actualshipdate) actualshipdate,
		to_char(wm_concat(distinct to_char(q.status))) status,
		max(q.ref20) href20,
		max(q.ref53) href53,
		max(q.ref55) href55,
		max(q.iskititems) iskititems,
		to_char(wm_concat(distinct to_char(q.company))) company
		from plugin.v_bj_order_query q
		where q.externorderkey in
		<foreach collection = "list" item = "tmp" index = "index" open = "(" close = ")" separator = "," >
			#{tmp}
		</foreach >
		group by q.externorderkey, q.whseid
	</select>
	<select id="queryShortCount" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(*)
		from ${whseId}.orders oo
		join ${whseId}.orderdetail od on oo.orderkey = od.orderkey
		where oo.externorderkey = #{externo,jdbcType=VARCHAR}
		and od.status = '02'
	</select>
</mapper>