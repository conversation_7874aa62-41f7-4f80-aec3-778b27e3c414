<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforIapsRepository">

    <update id="saveXcItemInfo" parameterType="java.util.List">
        merge into plugin.xc_item_info a
        using (
        <foreach collection="list" index="index" item="item" separator="union all">
            SELECT
            #{item.sku,jdbcType=VARCHAR} sku,
            #{item.priority,jdbcType=VARCHAR} priority,
            #{item.domesticFirst,jdbcType=VARCHAR} domesticFirst,
            #{item.internalFirst,jdbcType=VARCHAR} internalFirst,
            #{item.enableFlag,jdbcType=VARCHAR} enableFlag,
            #{item.updateBy,jdbcType=VARCHAR} updateBy
            from dual
        </foreach>
        ) b
        ON (a.sku = b.sku)
        when matched then
            update set a.priority = b.priority,
            a.domestic_first = b.domesticFirst,
            a.internal_first = b.internalFirst,
            a.enable_flag = b.enableFlag,
            a.update_by = b.updateBy,
            a.update_time = sysdate
            where a.sku = B.sku
        when not matched then
            insert (a.serialkey, a.sku, a.priority, a.domestic_first, a.internal_first,
            a.enable_flag, a.create_by,a.create_time,a.update_by, a.update_time)
            values (plugin.seq_xc_item_info.nextval, b.sku, b.priority, b.domesticFirst,
            b.internalFirst, b.enableFlag, b.updateBy, sysdate, b.updateBy, sysdate)
    </update>
    <select id="getCodelkup" resultType="java.lang.String">
        select description from enterprise.codelkup where listname = 'ZSTORERORG'
    </select>
    <select id="selectInforStockCount" parameterType="com.zte.interfaces.infor.dto.IapsStockDto"
            resultType="java.lang.Integer">
        select count(1) from (
        <foreach collection="infoStockItemList" item="item" index="index" separator="union all">
            select #{item.stepStock,jdbcType=VARCHAR} stockNo,lt.lottable02
            from ${item.inforStock}.lotxlocxid lx
            join ${item.inforStock}.lotattribute lt
            on lt.sku = lx.sku and lt.lot = lx.lot and lx.storerkey = lt.storerkey
            where lx.storerkey = 'ZTE' and lx.qty > 0
            <if test="item.itemBarcodeList != null and item.itemBarcodeList.size > 0">
                and lt.lottable02 in (
                <foreach collection="item.itemBarcodeList" item="itemBarcode" index="index" separator=",">
                    #{itemBarcode,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
            <if test=" codelKupVOList.contains(item.goodsType)">
                and lt.lottable03 = #{item.organizationId,jdbcType=VARCHAR}
                and lt.lottable06 = #{item.goodsType,jdbcType=VARCHAR}
                group by lt.lottable02,lt.lottable03,lt.lottable06
            </if>
            <if test=" !codelKupVOList.contains(item.goodsType) ">
                and lt.lottable03 = #{item.organizationId,jdbcType=VARCHAR}
                and lt.lottable06 not in (
                <foreach collection="codelKupVOList" item="itemGoodsType" index="index" separator=",">
                    #{itemGoodsType,jdbcType=VARCHAR}
                </foreach>
                )
                group by lt.lottable02,lt.lottable03
            </if>
        </foreach>
        ) 
    </select>
    <select id="selectInforStockPage" parameterType="com.zte.interfaces.infor.dto.IapsStockDto"
            resultType="com.zte.interfaces.infor.dto.IapsInforStockDto">
        SELECT * FROM (select t1.*,ROWNUM RN from (select * from (
        <foreach collection="infoStockItemList" item="item" index="index" separator="union all">
            select #{item.stepStock,jdbcType=VARCHAR} stockNo,#{item.inforStock,jdbcType=VARCHAR} whseId,
            lt.lottable02 itemBarcode,sum(lx.qty) qty,
            sum(case when lx.status = 'OK' then lx.qty else 0 end) - sum(lx.qtyallocated + lx.qtypicked) qtyAvailable,
            sum(case when lx.status = 'HOLD' then lx.qty else 0 end) qtyOnhold,
            sum(lx.qtyallocated + lx.qtypicked) qtyRemain
            from ${item.inforStock}.lotxlocxid lx
            join ${item.inforStock}.lotattribute lt
            on lt.sku = lx.sku and lt.lot = lx.lot and lx.storerkey = lt.storerkey
            where lx.storerkey = 'ZTE' and lx.qty > 0
            <if test="item.itemBarcodeList != null and item.itemBarcodeList.size > 0">
                and lt.lottable02 in (
                <foreach collection="item.itemBarcodeList" item="itemBarcode" index="index" separator=",">
                    #{itemBarcode,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
            <if test=" codelKupVOList.contains(item.goodsType)">
                and lt.lottable03 = #{item.organizationId,jdbcType=VARCHAR}
                and lt.lottable06 = #{item.goodsType,jdbcType=VARCHAR}
                group by lt.lottable02,lt.lottable03,lt.lottable06
            </if>
            <if test=" !codelKupVOList.contains(item.goodsType) ">
                and lt.lottable03 = #{item.organizationId,jdbcType=VARCHAR}
                and lt.lottable06 not in (
                <foreach collection="codelKupVOList" item="itemGoodsType" index="index" separator=",">
                    #{itemGoodsType,jdbcType=VARCHAR}
                </foreach>
                )
                group by lt.lottable02,lt.lottable03
            </if>
        </foreach>
        ) t
        order by stockNo,whseId,itemBarcode
        ) t1)
        <if test="pageIndex != null and pageSize != null">
            WHERE RN BETWEEN #{pageIndex} AND #{pageSize}
        </if>
    </select>
    <select id="getCodeByCodelkup" resultType="java.lang.String">
        select cc.code from enterprise.codelkup cc where cc.listname = 'ZSTORERORG'
    </select>
</mapper>