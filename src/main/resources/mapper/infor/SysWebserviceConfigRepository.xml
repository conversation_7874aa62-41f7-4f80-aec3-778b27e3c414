<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.SysWebserviceConfigRepository">
    <resultMap id="baseResultMap" type="com.zte.interfaces.infor.dto.SysLookupValuesDTO">
        <result column="LOOKUP_TYPE" jdbcType="VARCHAR" property="lookupType" />
        <result column="LOOKUP_CODE" jdbcType="VARCHAR" property="lookupCode" />
        <result column="LOOKUP_MEANING" jdbcType="VARCHAR" property="lookupMeaning" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    </resultMap>
    <select id="getWebserviceInterface" parameterType="java.util.List" resultMap="baseResultMap">
        select LOOKUP_TYPE,LOOKUP_CODE,LOOKUP_MEANING,DESCRIPTION,ATTRIBUTE1
        from plugin.sys_lookup_values
        where enabled_flag = 'Y'
        and LOOKUP_TYPE in (
        <foreach collection="list" separator="," item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
    </select>
</mapper>
