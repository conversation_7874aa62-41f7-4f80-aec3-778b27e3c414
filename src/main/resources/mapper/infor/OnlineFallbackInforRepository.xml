<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.OnlineFallbackInforRepository">
 
     <resultMap id="FallbackNoInfoMap" type="com.zte.interfaces.onlinefallback.dto.FallbackNoInfo">
        <result column="fallbackNo" jdbcType="VARCHAR" property="fallbackNo" />
        <result column="fallStatus" jdbcType="VARCHAR" property="fallStatus" />
        <result column="lastUpdatedBy" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="lastUpdatedDate" jdbcType="VARCHAR" property="lastUpdatedDate" />
    </resultMap> 
    <resultMap id="DetailInforReceivedMap" type="com.zte.interfaces.onlinefallback.dto.ReceiptDetailDTO">
        <result column="externreceiptkey" jdbcType="VARCHAR" property="externreceiptkey" />
        <result column="toid" jdbcType="VARCHAR" property="toid" />
        <result column="receiptlinenumber" jdbcType="VARCHAR" property="receiptLineNumber" />
        <result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
        <result column="qtyreceived" jdbcType="DECIMAL" property="qtyreceived" />
        <result column="lottable06" jdbcType="VARCHAR" property="lottable06" />
        <result column="lottable03" jdbcType="VARCHAR" property="lottable03" />
    </resultMap>      

    <select id="queryDealWarehouseReceived" resultType="com.zte.interfaces.onlinefallback.dto.FallbackNoInfo" resultMap="FallbackNoInfoMap">
        select distinct temp.* from (
        <foreach collection="list" item="tmp" index="index" separator="union all">
            SELECT externreceiptkey fallbackNo,'WAREHOUSED' fallStatus,'SO' lastUpdatedBy,
            to_char(sysdate,'yyyy-MM-dd HH24:MI:SS') lastUpdatedDate FROM ${tmp.targetLocation}.RECEIPT
            WHERE externreceiptkey = #{tmp.fallbackNo} AND STATUS=15
        </foreach>
        ) temp
    </select>
    <select id="queryDetailInforReceived" parameterType="com.zte.interfaces.onlinefallback.dto.ReceiptDetailDTO" resultMap="DetailInforReceivedMap" >
        select rd.externreceiptkey,rd.toid, rd.qtyreceived,rd.receiptlinenumber,rd.lottable02,
        case when rd.lottable06 in
            (select cc.description
            from enterprise.codelkup cc
            where cc.listname = 'ZSTORERORG')
        then 'ZTE' else 'VENDOR' end lottable06,
        rd.lottable03
        from ${targetLocation}.receiptdetail rd
        where rd.externreceiptkey = #{externreceiptkey}
        and rd.lottable02= #{lottable02}
        and rd.qtyreceived > 0
    </select>
    <select id="queryDetailInforQtyStore" parameterType="com.zte.interfaces.onlinefallback.dto.ReceiptDetailDTO" resultType="Integer" >
        select nvl(eps.qtystore,0) qty
		  from plugin.edi_po_s eps
		 where eps.externreceiptkey = #{externreceiptkey}
		   and eps.lottable02 = #{lottable02}
           and eps.receiptlinenumber = #{receiptLineNumber}
    </select>

    <select id="queryDealWarehouseSo" parameterType="java.util.List" resultType="java.lang.String">
        select distinct temp.externalorderkey2 from (
        <foreach collection="list" item="tmp" index="index" separator="union all">
            select a.externalorderkey2
            from (select o.externalorderkey2,
            sum(od.shippedqty) pickqty,
            (select nvl(sum(ess.shippedqty),0)
            from plugin.edi_so_s ess
            where ess.externalorderkey2 = o.externalorderkey2
            and ess.invsymbol = 1) shippedqty
            from ${tmp.targetLocation}.orderdetail od
            join ${tmp.targetLocation}.orders o
            on od.orderkey = o.orderkey
            and o.externalorderkey2 = #{tmp.billNo}
            and o.status = '95'
            and od.status = '95'
            group by o.externalorderkey2) a
            where a.pickqty = a.shippedqty
        </foreach>
        ) temp
    </select>  
    
    <select id="queryRealOutNumber" resultType="Double">
    	select nvl(sum(ess.shippedqty),0) from plugin.edi_so_s ess where ess.externalorderkey2 = #{applybillNo,jdbcType=VARCHAR} and ess.externlineno = #{rowNo,jdbcType=VARCHAR}
    </select>
        
    <select id="queryInforSoStatus" resultMap="FallbackNoInfoMap">  
        SELECT DISTINCT EXTERNALORDERKEY2 fallbackNo,'STOCK_OUT' fallStatus,'IWMS' lastUpdatedBy,
        to_char(sysdate,'yyyy-MM-dd HH24:MI:SS') lastUpdatedDate
        FROM ${whseid}.ORDERS
        WHERE EXTERNALORDERKEY2 in
        <foreach collection="params" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp}
        </foreach> 
        AND STATUS=95
    </select>
</mapper>
