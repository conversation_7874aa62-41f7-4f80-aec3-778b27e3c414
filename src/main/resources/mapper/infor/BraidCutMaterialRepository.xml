<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.BraidCutMaterialRepository">
    <resultMap id="outPickingTaskMap" type="com.zte.interfaces.infor.dto.OutPickingTaskDto">
        <result column="consigneekey" jdbcType="VARCHAR" property="consigneeKey"/>
        <result column="PICKDETAILKEY" jdbcType="VARCHAR" property="demandKey"/>
        <result column="CASEID" jdbcType="VARCHAR" property="caseid"/>
        <result column="createDate" jdbcType="VARCHAR" property="createDate"/>
        <result column="editdate" jdbcType="VARCHAR" property="editdate"/>
        <result column="externorderkey" jdbcType="VARCHAR" property="externOrderKey"/>
        <result column="loc" jdbcType="VARCHAR" property="loc"/>
        <result column="lot" jdbcType="VARCHAR" property="lot"/>
        <result column="lot04" jdbcType="VARCHAR" property="lot04"/>
        <result column="lottable02" jdbcType="VARCHAR" property="lot02"/>
        <result column="id" jdbcType="VARCHAR" property="lpn"/>
        <result column="ordergroup" jdbcType="VARCHAR" property="orderGroup"/>
        <result column="orderkey" jdbcType="VARCHAR" property="orderKey"/>
        <result column="orderLineNumber" jdbcType="VARCHAR" property="orderLineNumber"/>
        <result column="pickType" jdbcType="VARCHAR" property="pickType"/>
        <result column="qty" jdbcType="VARCHAR" property="qty"/>
        <result column="ref01" jdbcType="VARCHAR" property="ref01"/>
        <result column="ref11" jdbcType="VARCHAR" property="ref11"/>
        <result column="ref20" jdbcType="VARCHAR" property="ref20"/>
        <result column="ref33" jdbcType="VARCHAR" property="ref33"/>
        <result column="ref34" jdbcType="VARCHAR" property="ref34"/>
        <result column="serialkey" jdbcType="VARCHAR" property="serialkey"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="storerkey" jdbcType="VARCHAR" property="storerKey"/>
        <result column="TASKDETAILKEY" jdbcType="VARCHAR" property="taskdetailkey"/>
        <result column="uom" jdbcType="VARCHAR" property="uom"/>
        <result column="wavekey" jdbcType="VARCHAR" property="waveKey"/>
        <result column="whseid" jdbcType="VARCHAR" property="whseId"/>
        <result column="CUSTOM1" jdbcType="VARCHAR" property="braidSpacing"/>
        <result column="REELIDPROCESS" jdbcType="VARCHAR" property="isReelid"/>
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierCode"/>
    </resultMap>
    <!--INFOR_编带拆零拣货任务接口-->
    <select id="queryPickingTask" parameterType="java.util.List" resultMap="outPickingTaskMap">
        select temp.*,zb.supplier_no,qc.CUSTOM1,'CP' pickType from (
        <foreach collection="list" item="tmp" index="index" separator="union all">
            SELECT  d.orderlinenumber,
            d.wavekey,
            d.PICKDETAILKEY,
            d.orderkey,
            d.whseid,
            d.storerkey,
            d.sku,
            d.qty,
            d.id,
            d.uom,
            o.externorderkey,
            o.ordergroup,
            o.consigneekey,
            TO_CHAR(o.adddate,'YYYY-MM-DD HH24:MI:SS') createDate,
            o.ref11,
            o.ref20,
            la.lottable02,
            TO_CHAR(la.lottable04,'YYYY-MM-DD HH24:MI:SS') lot04,
            orz.ref01,
            d.lot,
            d.loc,
            TK.TASKDETAILKEY,
            TK.CASEID,
            d.serialkey,
            orz.ref33,
            orz.ref34,
            TO_CHAR(TK.editdate,'YYYY-MM-DD HH24:MI:SS') editdate,
            sk.REELIDPROCESS
            FROM ${tmp.whseid}.pickdetail d
            inner join ${tmp.whseid}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
            inner join ${tmp.whseid}.lotxlocxid f
            on d.id = f.id and d.loc = f.loc and d.lot = f.lot and d.qty > 0 and f.qty > 0 and f.qty > d.qty
            inner join ${tmp.whseid}.orders o on d.orderkey = o.orderkey
            inner join ${tmp.whseid}.lotattribute la on d.lot = la.lot
            inner join ${tmp.whseid}.TASKDETAIL TK on d.ORDERKEY = TK.ORDERKEY AND d.PICKDETAILKEY = TK.PICKDETAILKEY
            left join ${tmp.whseid}.orders_ref_zte orz on o.ORDERKEY = ORZ.ORDERKEY
            WHERE 1=1
            <if test="tmp.orderkey!=null and tmp.orderkey!=''">
                AND d.orderkey = #{tmp.orderkey}
            </if>
            <![CDATA[AND d.status < '5']]>
        </foreach>
        ) temp
        inner join enterprise.ztebarcode zb on temp.lottable02 = zb.item_barcode
        inner join plugin.QC_DATABASE_HEDA qc on temp.sku =qc.ITEM_NO and zb.supplier_no=qc.SUPPLIER_NO and qc.PACKAGE_TYPE='编带'
    </select>
    <resultMap id="pickingInfoMap" type="com.zte.interfaces.infor.vo.BraidPickingInfoVo" >
        <result column="WAVEKEY" jdbcType="VARCHAR" property="waveKey"/>
        <result column="EXTERNORDERKEY" jdbcType="VARCHAR" property="externorderKey"/>
        <result column="ORDERKEY" jdbcType="VARCHAR" property="orderKey"/>
        <result column="WHINFO" jdbcType="VARCHAR" property="whInfo"/>
        <result column="DESCR" jdbcType="VARCHAR" property="descr"/>
        <result column="REQUESTEDSHIPDATE" jdbcType="VARCHAR" property="requestedShipDate"/>
        <result column="C_COMPANY" jdbcType="VARCHAR" property="company"/>
        <result column="REF20" jdbcType="VARCHAR" property="outSupplier"/>
        <result column="CLASSGROUP" jdbcType="VARCHAR" property="classGroup"/>
        <result column="PUTAWAYZONE" jdbcType="VARCHAR" property="putawayzone"/>
        <result column="STAGE" jdbcType="VARCHAR" property="stage"/>
        <result column="SUSR1" jdbcType="VARCHAR" property="prodoctNo"/>
        <result column="SUSR5" jdbcType="VARCHAR" property="susr5"/>
        <result column="ID" jdbcType="VARCHAR" property="lpn"/>
        <result column="LOC" jdbcType="VARCHAR" property="loc"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="lottable02" jdbcType="VARCHAR" property="itemBarcode"/>
        <result column="QTY" jdbcType="VARCHAR" property="qty"/>
        <result column="uom" jdbcType="VARCHAR" property="uom"/>
        <result column="isAvl" jdbcType="VARCHAR" property="isAvl"/>
        <result column="isRd" jdbcType="VARCHAR" property="isRd"/>
        <result column="issMall" jdbcType="VARCHAR" property="issMall"/>
        <result column="WETLEVEL" jdbcType="VARCHAR" property="wetLevel"/>
        <result column="skuName" jdbcType="VARCHAR" property="skuName"/>
        <result column="brand_style" jdbcType="VARCHAR" property="brandStyle"/>
        <result column="CUSTOM1" jdbcType="VARCHAR" property="braidSpacing"/>
        <result column="isReelid" jdbcType="VARCHAR" property="isReelid"/>
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierCode"/>
    </resultMap>
    <!--INFOR_拆零拣货清单-->
    <select id="queryPickingInfo" parameterType="com.zte.interfaces.infor.dto.PickingTaskDto" resultMap="pickingInfoMap">
        select t.* from (SELECT wd.WAVEKEY,
        o.EXTERNORDERKEY,
        o.ORDERKEY,
        SUBSTR(pb.DB_ALIAS, 1, 2) || '0' || SUBSTR(pb.DB_ALIAS, 3, 1) || SUBSTR(o.ORDERKEY, 0, 15) WHINFO,
        W.DESCR,
        o.REQUESTEDSHIPDATE,
        o.C_COMPANY,
        o.REF20,
        pc.CLASSGROUP,
        sk.PUTAWAYZONE,
        o.STAGE,
        o.SUSR1,
        o.SUSR5,
        d.ID,
        d.LOC,
        od.sku,
        la.lottable02,
        d.QTY,
        sk.BUSR2 uom,
        od.SUSR1 isAvl,
        DECODE(od.PICKINGINSTRUCTIONS, '否', NULL, od.PICKINGINSTRUCTIONS) isRd,
        DECODE(zb.IS_XP, 30, '是', NULL) issMall,
        zb.WETLEVEL,
        sk.DESCR skuName,
        uu.brand_style,
        zb.supplier_no,
        qc.CUSTOM1,
        case when sk.REELIDPROCESS=1 then 'Y' else 'N' end isReelid,
        rownum rn
        FROM ${whseid}.pickdetail d
        inner join ${whseid}.lotattribute la on d.lot = la.lot
        inner join enterprise.ztebarcode zb on la.lottable02 = zb.item_barcode
        inner join plugin.QC_DATABASE_HEDA qc on d.sku =qc.ITEM_NO and zb.supplier_no=qc.SUPPLIER_NO and qc.PACKAGE_TYPE='编带'
        inner join ${whseid}.lotxlocxid f
        on d.id = f.id and d.loc = f.loc and d.lot = f.lot and d.qty > 0 and f.qty > 0 and f.qty > d.qty
        inner join ${whseid}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        inner join ${whseid}.orders o on d.orderkey = o.orderkey
        inner join ${whseid}.ORDERDETAIL od on d.orderkey = od.orderkey and d.ORDERLINENUMBER = od.ORDERLINENUMBER
        inner join WMSADMIN.PL_DB pb ON UPPER(o.WHSEID) = UPPER(pb.DB_LOGID)
        inner join enterprise.uuid uu on zb.item_uuid = uu.item_uuid
        left join ${whseid}.WAVEDETAIL wd on o.orderkey = wd.orderkey
        left join ${whseid}.WAVE w on wd.WAVEKEY = w.WAVEKEY
        left join ${whseid}.V_PUTAWAYZONE_CLASSGROUP pc ON sk.PUTAWAYZONE = pc.PUTAWAYZONE
        WHERE 1=1
        <if test="orderkey!=null and orderkey!=''">
            AND d.orderkey = #{orderkey}
        </if>
        <if test="waveKey!=null and waveKey!=''">
            AND wd.WAVEKEY = #{waveKey}
        </if>
        <if test="externorderKey!=null and externorderKey!=''">
            AND o.EXTERNORDERKEY = #{externorderKey}
        </if>
        <if test="putawayzone!=null and putawayzone!=''">
            AND sk.PUTAWAYZONE = #{putawayzone}
        </if>
        <if test="classGroup!=null and classGroup!=''">
            AND pc.CLASSGROUP = #{classGroup}
        </if>
        <if test="pickingDateBegin != null and pickingDateBegin !='' ">
            and d.ADDDATE <![CDATA[>=]]> to_date(#{pickingDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="pickingDateEnd != null and pickingDateEnd !='' ">
            and d.ADDDATE <![CDATA[<=]]> to_date(#{pickingDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <![CDATA[AND d.status < '5']]>
        ORDER BY d.LOC,od.SKU,la.LOTTABLE02,d.ID
        ) t where 1=1
        <if test="startRow != null and endRow != null ">
            and  t.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or endRow == null ">
            and  t.rn &lt;= 1000000
        </if>
    </select>
    <!--INFOR_拆零拣货清单总数-->
    <select id="queryPickingInfoTotal" parameterType="com.zte.interfaces.infor.dto.PickingTaskDto" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ${whseid}.pickdetail d
        inner join ${whseid}.lotattribute la on d.lot = la.lot
        inner join enterprise.ztebarcode zb on la.lottable02 = zb.item_barcode
        inner join plugin.QC_DATABASE_HEDA qc on d.sku =qc.ITEM_NO and zb.supplier_no=qc.SUPPLIER_NO and qc.PACKAGE_TYPE='编带'
        inner join ${whseid}.lotxlocxid f
        on d.id = f.id and d.loc = f.loc and d.lot = f.lot and d.qty > 0 and f.qty > 0 and f.qty > d.qty
        inner join ${whseid}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        inner join ${whseid}.orders o on d.orderkey = o.orderkey
        inner join ${whseid}.ORDERDETAIL od on d.orderkey = od.orderkey and d.ORDERLINENUMBER = od.ORDERLINENUMBER
        inner join WMSADMIN.PL_DB pb ON UPPER(o.WHSEID) = UPPER(pb.DB_LOGID)
        inner join enterprise.uuid uu on zb.item_uuid = uu.item_uuid
        left join ${whseid}.WAVEDETAIL wd on o.orderkey = wd.orderkey
        left join ${whseid}.WAVE w on wd.WAVEKEY = w.WAVEKEY
        left join ${whseid}.V_PUTAWAYZONE_CLASSGROUP pc ON sk.PUTAWAYZONE = pc.PUTAWAYZONE
        WHERE 1=1
        <if test="orderkey!=null and orderkey!=''">
            AND d.orderkey = #{orderkey}
        </if>
        <if test="waveKey!=null and waveKey!=''">
            AND wd.WAVEKEY = #{waveKey}
        </if>
        <if test="externorderKey!=null and externorderKey!=''">
            AND o.EXTERNORDERKEY = #{externorderKey}
        </if>
        <if test="putawayzone!=null and putawayzone!=''">
            AND sk.PUTAWAYZONE = #{putawayzone}
        </if>
        <if test="pickingDateBegin != null and pickingDateBegin !='' ">
            and d.ADDDATE <![CDATA[>=]]> to_date(#{pickingDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="pickingDateEnd != null and pickingDateEnd !='' ">
            and d.ADDDATE <![CDATA[<=]]> to_date(#{pickingDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <![CDATA[AND d.status < '5']]>
    </select>
</mapper>