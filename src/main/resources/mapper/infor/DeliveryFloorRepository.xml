<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.DeliveryFloorRepository">

    <insert id="insertFloorInfo">
        insert into plugin.DELIVERY_ROSTER
        (serialkey,PRODUCT_BASE,FLOOR,WAREHOUSE_NAME,LIABILITY,DIRECTOR,STATE,CREATED_BY,LAST_UPDATED_BY)
        select PLUGIN.SEQ_DELIVERY_ROSTER.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.productBase,jdbcType=VARCHAR},#{item.floor,jdbcType=VARCHAR},#{item.warehouseName,jdbcType=VARCHAR},
            #{item.liability,jdbcType=VARCHAR}, #{item.director,jdbcType=VARCHAR},'1',
            #{xEmpNo,jdbcType=VARCHAR},#{xEmpNo,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) temp
    </insert>
    <select id="getDeliveryFloorDetailTotal" resultType="java.lang.Integer">
        select count(1) from plugin.DELIVERY_ROSTER
        where enabled_flag = 'Y'
        <if test="productBase != null and productBase !='' ">
            and PRODUCT_BASE  = #{productBase,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state !='' ">
            and state  = #{state,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
    <select id="getDeliveryFloorDetailVo" resultType="com.zte.domain.model.infor.DeliveryFloorDetailDTO">
        select B.* from (
        select A.*,rownum rn from(
        select t.serialKey,
        t.PRODUCT_BASE productBase,
        t.floor,
        t.WAREHOUSE_NAME warehouseName,
        t.liability,
        t.director,
        t.state,
        t.CREATED_BY createdBy,
        t.CREATION_DATE creationDate,
        t.LAST_UPDATED_BY lastUpdatedBy,
        t.LAST_UPDATE_DATE lastUpdateDate
        from plugin.DELIVERY_ROSTER t
        where enabled_flag = 'Y'
        <if test="productBase != null and productBase !='' ">
            and t.PRODUCT_BASE  = #{productBase,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state !='' ">
            and t.state  = #{state,jdbcType=VARCHAR}
        </if>
        <if test="lastUpdateDateBegin != null and lastUpdateDateBegin !='' ">
            and t.LAST_UPDATE_DATE <![CDATA[>=]]> to_date(#{lastUpdateDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="lastUpdateDateEnd != null and lastUpdateDateEnd !='' ">
            and t.LAST_UPDATE_DATE <![CDATA[<=]]> to_date(#{lastUpdateDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by t.LAST_UPDATE_DATE desc
        )A )B
        where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  b.rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  b.rn &lt;= 500000
        </if>
    </select>
    <update id="loseFloorInfo">
        update plugin.DELIVERY_ROSTER
        set state = '2',
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{xEmpNo,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and serialkey in
        <foreach collection="serialKeys" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="effectFloorInfo">
        update plugin.DELIVERY_ROSTER
        set state = '1',
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{xEmpNo,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and serialkey in
        <foreach collection="serialKeys" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getDeliveryPrintInfo" resultType="com.zte.interfaces.infor.dto.DeliveryPrintInfoDTO">
        select * from
        (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            SELECT R.EXTERNRECEIPTKEY,
            S.COMPANY,
            RD.WHSEID,
            F.DESCRIPTION WHNAME,
            R.RECEIPTDATE,
            RD.SKU,
            SK.DESCR,
            BAR.SUPPLIER_MODEL NOTES,
            RD.LOTTABLE02,
            SUM(RD.QTYEXPECTED) QTYEXPECTED,
            SUM(RD.QTYRECEIVED) QTYRECEIVED,
            RD.RECEIPTKEY,
            R.editWho
            FROM ${item}.RECEIPT R
            JOIN ${item}.RECEIPTDETAIL RD ON R.RECEIPTKEY = RD.RECEIPTKEY
            AND R.STORERKEY = RD.STORERKEY
            JOIN ${item}.SKU SK ON SK.SKU = RD.SKU
            AND SK.STORERKEY = RD.STORERKEY
            JOIN WMSADMIN.PL_DB DB ON UPPER(DB.DB_LOGID) = RD.WHSEID
            JOIN ENTERPRISE.FACILITYNEST F ON DB.DB_NAME = F.NAME
            JOIN ENTERPRISE.ZTEBARCODE BAR ON BAR.ITEM_BARCODE = RD.LOTTABLE02
            LEFT JOIN ${item}.STORER S ON S.STORERKEY = BAR.SUPPLIER_NO
            AND S.TYPE = 5
            WHERE R.STORERKEY = 'ZTE'
            AND R.REF11 IN ('100', '110', '120', '130', '140')
            AND R.EXTERNRECEIPTKEY = #{deliveryNumber, jdbcType = VARCHAR}
            GROUP BY R.EXTERNRECEIPTKEY,  S.COMPANY, RD.WHSEID, F.DESCRIPTION,
            R.RECEIPTDATE, RD.SKU, SK.DESCR, BAR.SUPPLIER_MODEL,
            RD.LOTTABLE02, RD.RECEIPTKEY, R.editWho
            HAVING SUM(RD.QTYRECEIVED) > 0
        </foreach>
        ) ORDER BY RECEIPTDATE desc
    </select>
    <update id="deleteFloorInfo">
        update plugin.DELIVERY_ROSTER
        set
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{xEmpNo,jdbcType=VARCHAR},
        enabled_flag = 'N'
        where serialkey in
        <foreach collection="serialKeys" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="queryFloorInfoExist" resultType="java.lang.Integer">
        select count(1) from plugin.DELIVERY_ROSTER
        where enabled_flag = 'Y'
        and PRODUCT_BASE = #{productBase,jdbcType=VARCHAR}
        and floor = #{floor,jdbcType=VARCHAR}
    </select>
    <select id="queryDeliveryInfo" resultType="com.zte.domain.model.infor.DeliverySignDetailDTO">
        select * from
        (
        <foreach collection="whseidList" item="item" index="index" separator="union all">
            select distinct rd.whseid whseId,
            rd.externreceiptkey deliveryNumber,
            r.receiptkey receiptKey,
            rd.toid pkgId,
            zb.supplier_no supplierNo,
            st.company supplierName,
            rz.ref44 deliveryAddress,
            rd.sku itemNo,
            rd.lottable02 itemBarcode,
            r.status asnStatus,
            rd.status detailStatus,
            rd.qtyexpected qtyReceived
            from ${item}.receipt r
            join ${item}.receiptdetail rd on r.receiptkey = rd.receiptkey
            join ${item}.receiptdetail_ref_zte rz on rz.receiptkey = r.receiptkey
            join enterprise.ztebarcode zb on zb.item_barcode = rd.lottable02
            left join enterprise.storer st on zb.supplier_no = st.storerkey
            and st.type = 5
            where rd.qtyexpected > 0
            and rd.externreceiptkey = #{deliveryNumber, jdbcType = VARCHAR}
        </foreach>
        )
    </select>
    <update id="updateFloorInfo">
        update plugin.DELIVERY_ROSTER
        set WAREHOUSE_NAME = #{dto.warehouseName,jdbcType=VARCHAR},
        LIABILITY = #{dto.liability,jdbcType=VARCHAR},
        DIRECTOR = #{dto.director,jdbcType=VARCHAR},
        LAST_UPDATE_DATE = sysdate,
        LAST_UPDATED_BY = #{xEmpNo,jdbcType=VARCHAR}
        where enabled_flag = 'Y'
        and PRODUCT_BASE = #{dto.productBase,jdbcType=VARCHAR}
        and floor = #{dto.floor,jdbcType=VARCHAR}
    </update>
</mapper>