<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ZteAlibabaInventoryRepository">
    <insert id="insertAlibabaInventoryData" parameterType="com.zte.interfaces.step.dto.B2BCallBackInventoryResultDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO PLUGIN.ZMS_INVENTORY_ALIBABA_SNAP
            (SERIALKEY,
            MESSAGE_ID,
            MESSAGE_TYPE,
            INVENTORY_CATEGORY_NAME,
            BRAND_NAME,
            CATEGORY_NAME,
            ITEM_NAME,
            MPN,
            BATCH,
            INVENTORY_QUANTITY,
            AVA<PERSON><PERSON>LE_QUANTITY,
            FREEZE_QUANTITY,
            HOLD_DURATION,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            ENABLED_FLAG,
            SNAP_DATE)
            VALUES
            (plugin.zms_inventory_alibaba_snap_s.NEXTVAL,
            #{item.messageId},
            #{item.messageType},
            #{item.inventoryCategoryName},
            #{item.brandName},
            #{item.categoryName},
            #{item.itemName},
            #{item.mpn},
            #{item.batch},
            #{item.inventoryQuantity},
            #{item.availableQuantity},
            #{item.freezeQuantity},
            #{item.holdDuration},
             'system',
             sysdate,
             'system',
             sysdate,
             'Y',
            to_char(SYSDATE,'yyyy-MM-dd')
            )
        </foreach>
    </insert>
    <update id="delInventoryDiffData">
        update PLUGIN.ZMS_INVENTORY_DIFF set ENABLED_FLAG = 'N' where ENABLED_FLAG = 'Y' and SNAP_DATE <![CDATA[>=]]> TRUNC(SYSDATE) and SNAP_DATE <![CDATA[<]]> TRUNC(SYSDATE) + 1
    </update>
    <insert id="insertInventoryDiffData" parameterType="com.zte.interfaces.step.dto.ZmsInventoryDiffDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO PLUGIN.ZMS_INVENTORY_DIFF
            (SERIALKEY,
            INVENTORY_TYPE,
            MPN,
            ZTE_STOCK_QTY,
            ZTE_INSTOCK_QTY,
            ZTE_ONLINE_QTY,
            ALIBABA_STOCK_QTY,
            DIFF_STOCK_QTY,
            SNAP_DATE,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            ENABLED_FLAG,
            AVAILABLE_QUANTITY,
            FREEZE_QUANTITY)
            VALUES
            (plugin.zms_inventory_diff_s.NEXTVAL,
            #{item.inventoryType},
            #{item.mpn},
            #{item.zteStockQty},
            #{item.zteInstockQty},
            #{item.zteOnlineQty},
            #{item.alibabaStockQty},
            #{item.diffStockQty},
            TRUNC(SYSDATE),
            'system',
            sysdate,
            'system',
            sysdate,
            'Y',
            #{item.availableQuantity},
            #{item.freezeQuantity}
            )
        </foreach>
    </insert>
</mapper>