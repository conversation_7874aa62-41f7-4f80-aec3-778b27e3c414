<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.RedDotExecuteRepository">
    <resultMap id="OrdersMap" type="com.zte.interfaces.infor.dto.OrdersDto">
        <result column="WHSEID" jdbcType="VARCHAR" property="whseId"/>
        <result column="ADDDATE" jdbcType="VARCHAR" property="addDate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="ORDERKEY" jdbcType="VARCHAR" property="orderKey"/>
        <result column="ORDERLINENUMBER" jdbcType="VARCHAR" property="orderLineNumber"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        <result column="isTimeout" jdbcType="INTEGER" property="isTimeout"/>
        <result column="lottable02" jdbcType="VARCHAR" property="lottable02"/>
        <result column="classgroup" jdbcType="VARCHAR" property="classgroup"/>
        <result column="isKitting" jdbcType="INTEGER" property="isKitting"/>
        <result column="availableQty" jdbcType="INTEGER" property="availableQty"/>
        <result column="qty" jdbcType="INTEGER" property="qty"/>
        <result column="ref11" jdbcType="VARCHAR" property="ref11"/>
        <result column="lot" jdbcType="VARCHAR" property="lot"/>
        <result column="isSupplierDelivery" jdbcType="INTEGER" property="isSupplierDelivery"/>
        <result column="holdCode" jdbcType="VARCHAR" property="holdCode"/>
        <result column="holdDesc" jdbcType="VARCHAR" property="holdDesc"/>
        <result column="isHoldCheck" jdbcType="INTEGER" property="isHoldCheck"/>
        <result column="EXTERNALORDERKEY2" jdbcType="VARCHAR" property="externalorderkey2"/>
    </resultMap>
    <resultMap id="BaseMap" type="com.zte.domain.model.infor.RedDotExecuteInfo">
        <result column="SERIALKEY" jdbcType="INTEGER" property="serialkey"/>
        <result column="RED_DOT_TYPE" jdbcType="INTEGER" property="redDotType"/>
        <result column="RED_DOT_SUBCLASS" jdbcType="INTEGER" property="redDotSubclass"/>
        <result column="WHSEID" jdbcType="VARCHAR" property="whseId"/>
        <result column="EXTERNRECEIPTKEY" jdbcType="VARCHAR" property="externreceiptkey"/>
        <result column="EXTERNRECEIPTNUMBER" jdbcType="VARCHAR" property="externreceiptnumber"/>
        <result column="EXECUTE_COUNT" jdbcType="INTEGER" property="executeCount"/>
        <result column="RED_DOT_STATUS" jdbcType="INTEGER" property="redDotStatus"/>
        <result column="CREATED_DATE" jdbcType="VARCHAR" property="createdDate"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="LAST_UPDATED_DATE" jdbcType="VARCHAR" property="lastUpdatedDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy"/>
        <result column="OVER_TIME_CHECK_RED" jdbcType="INTEGER" property="overTimeCheckRed"/>
        <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType"/>
        <result column="ERROR_REASON" jdbcType="VARCHAR" property="errorReason"/>
        <result column="EXTERNALORDERKEY2" jdbcType="VARCHAR" property="externalorderkey2"/>
        <result column="OVER_TIME_CHECK_DATE" jdbcType="VARCHAR" property="overTimeCheckDate"/>
        <result column="executeMin" jdbcType="INTEGER" property="executeMin"/>
        <result column="executeOverMin" jdbcType="INTEGER" property="executeOverMin"/>
    </resultMap>
    <select id="getInforWarehouseList" parameterType="java.util.List" resultType="com.zte.interfaces.infor.dto.PlDbDTO">
        SELECT UPPER(PB.DB_LOGID) warehouseId,PB.DB_TYPE dbType FROM WMSADMIN.PL_DB PB
        WHERE PB.ISACTIVE = 1 AND PB.DB_ENTERPRISE = 0 AND PB.DB_TYPE in (1,2)
        <if test=" list != null and list.size() > 0">
            and UPPER(PB.DB_LOGID) not in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <delete id="deleteRedDot" parameterType="java.lang.String">
        delete from PLUGIN.red_dot_execute_info where SERIALKEY in (select r.SERIALKEY from PLUGIN.red_dot_execute_info r
        inner join ${whseid}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY and to_number(o.STATUS) <![CDATA[>=]]> 95
        where r.red_dot_type = 1 and r.WHSEID = #{whseid})
    </delete>
    <select id="getOrderData" parameterType="com.zte.interfaces.infor.dto.OrdersDto" resultMap="OrdersMap">
        select o.WHSEID,o.ADDDATE,o.STATUS,o.ORDERKEY,d.ORDERLINENUMBER,d.sku,vpc.classgroup,o.ref11,o.EXTERNALORDERKEY2,
        CASE WHEN to_number(o.STATUS) <![CDATA[<]]> 10 THEN (CASE WHEN (SYSDATE - (o.ADDDATE + 8/24)) <![CDATA[>]]> (#{isTimeout}/24) THEN 1 ELSE 2 END) ELSE 0 end isTimeout,
        CASE WHEN to_number(o.STATUS) <![CDATA[>=]]> 10 THEN
             CASE WHEN d.QTYALLOCATED+d.QTYPICKED+d.ADJUSTEDQTY <![CDATA[>=]]> d.ORIGINALQTY THEN 1 ELSE
                  CASE WHEN QTYALLOCATED=0 THEN 0 ELSE 2 END
             END
        ELSE 2 END isKitting
        from ${whseId}.Orders o
        inner join ${whseId}.Orderdetail d on o.ORDERKEY = d.ORDERKEY
        inner join ${whseId}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        left join ${whseId}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
        where to_number(o.STATUS) <![CDATA[<]]> 95 and to_number(d.STATUS) <![CDATA[<]]> 95
        and o.EXTERNORDERKEY not like 'IMES%'
    </select>
    <select id="getOrderAllocationTimeout" parameterType="com.zte.interfaces.infor.dto.RedDotOverTimeDTO" resultMap="OrdersMap">
        select temp.*,#{whseId} as whseId from (
        select t.orderKey,t.orderLineNumber,t.sku,t.classgroup,t.ref11,t.externalorderkey2,min(h.ADDDATE) allocatedDate from (
        <foreach collection="ordersList" item="item" index="index" separator="union all">
            select
            #{item.orderKey,jdbcType=VARCHAR} orderKey, #{item.orderLineNumber,jdbcType=VARCHAR} orderLineNumber,
            #{item.sku,jdbcType=VARCHAR} sku,#{item.classgroup,jdbcType=VARCHAR} classgroup,
            #{item.ref11,jdbcType=VARCHAR} ref11,#{item.externalorderkey2,jdbcType=VARCHAR} externalorderkey2
            from dual
        </foreach>
        ) t
        inner join ${whseId}.orderstatushistory h on t.orderKey = h.ORDERKEY and t.orderLineNumber =h.ORDERLINENUMBER
        where to_number(h.STATUS) <![CDATA[>=]]> 17 group by t.orderKey,t.orderLineNumber,t.sku,t.classgroup,t.ref11,
        t.externalorderkey2
        ) temp where SYSDATE - (temp.allocatedDate + 8/24) <![CDATA[>]]> #{allocationTimeout}/24
    </select>
    <select id="getOrderLot02" parameterType="com.zte.interfaces.infor.dto.RedDotOverTimeDTO" resultMap="OrdersMap">
        select temp.*,lt.lottable02,#{whseId} as whseId from (
        select distinct t.orderKey,t.orderLineNumber,t.sku,t.classgroup,t.ref11,t.externalorderkey2,NVL(td.lot,d.lot) lot from (
        <foreach collection="ordersList" item="item" index="index" separator="union all">
            select
            #{item.orderKey,jdbcType=VARCHAR} orderKey, #{item.orderLineNumber,jdbcType=VARCHAR} orderLineNumber,
            #{item.sku,jdbcType=VARCHAR} sku,#{item.classgroup,jdbcType=VARCHAR} classgroup,
            #{item.ref11,jdbcType=VARCHAR} ref11,#{item.externalorderkey2,jdbcType=VARCHAR} externalorderkey2
            from dual
        </foreach>
        ) t
        left join ${whseId}.taskdetail td on t.orderKey = td.ORDERKEY and t.orderLineNumber =td.ORDERLINENUMBER and td.STORERKEY='ZTE'
        left join ${whseId}.demandallocation d on t.orderKey = d.ORDERKEY and t.orderLineNumber =d.ORDERLINENUMBER and d.STORERKEY='ZTE'
        ) temp
        inner join ${whseId}.LOTATTRIBUTE lt on temp.lot = lt.LOT AND temp.sku = lt.sku AND lt.STORERKEY ='ZTE'
    </select>
    <select id="getOrderQty" parameterType="com.zte.interfaces.infor.dto.RedDotOverTimeDTO" resultMap="OrdersMap">
        select t.orderKey,t.orderLineNumber,t.sku,t.ref11,t.classgroup,t.externalorderkey2,sum(lx.qty) qty,
        sum(CASE WHEN lx.STATUS='OK' THEN lx.qty - (lx.qtyallocated) - (lx.qtypicked) ELSE 0
        END) availableQty,#{whseId} as whseId,
        0 isHoldCheck from (
        <foreach collection="ordersList" item="item" index="index" separator="union all">
            select
            #{item.orderKey,jdbcType=VARCHAR} orderKey, #{item.orderLineNumber,jdbcType=VARCHAR} orderLineNumber,
            #{item.sku,jdbcType=VARCHAR} sku,#{item.ref11,jdbcType=VARCHAR} ref11,#{item.classgroup,jdbcType=VARCHAR} classgroup,
            #{item.externalorderkey2,jdbcType=VARCHAR} externalorderkey2
            from dual
        </foreach>
        ) t
        inner join ${whseId}.LOTXLOCXID lx on t.sku = lx.sku AND lx.STORERKEY ='ZTE'
        group by t.orderKey,t.orderLineNumber,t.sku,t.ref11,t.classgroup,t.externalorderkey2
    </select>
    <select id="getOrderHoldInfo" parameterType="com.zte.interfaces.infor.dto.RedDotOverTimeDTO" resultMap="OrdersMap">
        select temp.*,c.DESCRIPTION holdDesc from (
        select distinct lx.sku,h.status holdCode from
        ${whseId}.LOTXLOCXID lx
        inner join ${whseId}.INVENTORYHOLD h on lx.lot = h.lot or lx.id=h.id or lx.loc=h.loc
        where lx.sku in
        <foreach collection="skuList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
         AND lx.STORERKEY ='ZTE' and lx.qty>0 and h.HOLD=1
        ) temp
        left join ${whseId}.inventoryholdcode c on temp.holdCode = c.code
    </select>
    <select id="getReceiptBarcode" parameterType="com.zte.interfaces.infor.dto.RedDotOverTimeDTO" resultMap="OrdersMap">
        select distinct rd.sku,
        CASE WHEN rr.ref11 in ('100', '110', '120', '130', '134','140') and rr.status = '0' THEN 1 ELSE 0 END isSupplierDelivery
        from ${whseId}.receipt rr
        inner join ${whseId}.receiptdetail rd on rr.receiptkey = rd.receiptkey
        where rd.sku in
        <foreach collection="skuList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and (
              (rr.ref11 in ('100', '110', '120', '130', '134','140','461') and rr.status = '0')
             or
              (rr.externreceiptkey like 'T%'  and rr.status = '2')
        )
    </select>
    <select id="getDelayCheckInfo" parameterType="com.zte.interfaces.material.dto.OverTimeInDTO" resultType="java.lang.String">
        select distinct a.item_barcode from (
        select t.item_barcode,
        trunc(sysdate)-trunc(max(t.check_end_date)) as diff
        from plugin.delay_recheck_bill_detail t
        where  t.whseid = #{whseid,jdbcType=VARCHAR}
        and t.item_barcode IN
        <foreach collection="itemBarcode" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t.check_result =1
        group by t.item_barcode
        ) a where a.diff <![CDATA[<]]> #{warningDays,jdbcType=INTEGER}
    </select>
    <select id="getDelayWhiteLists" parameterType="com.zte.interfaces.infor.dto.OrdersDto" resultMap="OrdersMap">
        select distinct d.whseid whseId,d.orderkey,d.item_code lottable02
        from plugin.delay_whitelists d
        where d.white_status = 1
        <if test="orderKey != null and orderKey != ''">
            and d.orderkey = #{orderKey, jdbcType=VARCHAR}
        </if>
        <if test="whseId != null and whseId != ''">
            and d.whseid = #{whseId, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getRedDotExecuteInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultMap="BaseMap">
        select SERIALKEY,RED_DOT_TYPE,RED_DOT_SUBCLASS,WHSEID,EXTERNRECEIPTKEY,EXTERNRECEIPTNUMBER,EXECUTE_COUNT,
        RED_DOT_STATUS,CREATED_DATE,CREATED_BY,LAST_UPDATED_DATE,LAST_UPDATED_BY,OVER_TIME_CHECK_RED,BILL_TYPE,
        ERROR_REASON,EXTERNALORDERKEY2,OVER_TIME_CHECK_DATE,FLOOR((sysdate - LAST_UPDATED_DATE) * 1440) executeMin,
        FLOOR((sysdate - OVER_TIME_CHECK_DATE) * 1440) executeOverMin
        from PLUGIN.red_dot_execute_info
        where red_dot_type = #{redDotType}
        <if test="redDotStatus != null ">
            and RED_DOT_STATUS = #{redDotType, jdbcType=INTEGER}
        </if>
        <if test="whseId != null and whseId != ''">
            and WHSEID = #{whseId, jdbcType=VARCHAR}
        </if>
        <if test="externreceiptnumber != null and externreceiptnumber != ''">
            and EXTERNRECEIPTNUMBER = #{externreceiptnumber, jdbcType=VARCHAR}
        </if>
        <if test="externreceiptkey != null and externreceiptkey != ''">
            and EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        </if>
    </select>
    <insert id="batchInsertRedDotInfo" parameterType="java.util.List">
        insert into PLUGIN.red_dot_execute_info (
        RED_DOT_TYPE,
        RED_DOT_SUBCLASS,
        WHSEID,
        EXTERNRECEIPTKEY,
        EXTERNRECEIPTNUMBER,
        EXECUTE_COUNT,
        RED_DOT_STATUS,
        CREATED_BY,
        LAST_UPDATED_BY,
        OVER_TIME_CHECK_RED,
        BILL_TYPE,
        ERROR_REASON,
        EXTERNALORDERKEY2)
        select temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.redDotType,jdbcType=INTEGER},
            #{item.redDotSubclass,jdbcType=INTEGER},
            #{item.whseId,jdbcType=VARCHAR},
            #{item.externreceiptkey,jdbcType=VARCHAR},
            #{item.externreceiptnumber,jdbcType=VARCHAR},
            1,
            #{item.redDotStatus,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.overTimeCheckRed,jdbcType=INTEGER},
            #{item.billType,jdbcType=VARCHAR},
            #{item.errorReason,jdbcType=VARCHAR},
            #{item.externalorderkey2,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) temp
    </insert>
    <update id="batchUpdateRedDotInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator =";" >
            update plugin.red_dot_execute_info
            set EXECUTE_COUNT = EXECUTE_COUNT+1,
            red_dot_STATUS = #{item.redDotStatus,jdbcType=INTEGER},
            <if test="item.errorReason != null and item.errorReason != ''">
                ERROR_REASON = #{item.errorReason,jdbcType=VARCHAR},
            </if>
            <if test="item.redDotSubclass != null and item.redDotSubclass > 0 ">
                red_dot_Subclass = #{item.redDotSubclass,jdbcType=INTEGER},
            </if>
            <if test="item.overTimeCheckRed != null ">
                OVER_TIME_CHECK_RED = #{item.overTimeCheckRed,jdbcType=INTEGER},
                OVER_TIME_CHECK_DATE = sysdate,
            </if>
            LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
            LAST_UPDATED_DATE = sysdate
            where red_dot_type = #{item.redDotType,jdbcType=INTEGER}
            <if test="item.whseId != null and item.whseId != ''">
                and WHSEID = #{item.whseId, jdbcType=VARCHAR}
            </if>
            <if test="item.externreceiptkey != null and item.externreceiptkey != ''">
                and EXTERNRECEIPTKEY = #{item.externreceiptkey, jdbcType=VARCHAR}
            </if>
            <if test="item.externreceiptnumber != null and item.externreceiptnumber != ''">
                and EXTERNRECEIPTNUMBER = #{item.externreceiptnumber, jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>
    <update id="updateRedDotInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo">
        update plugin.red_dot_execute_info
        set LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
        <if test="executeCount != null ">
            EXECUTE_COUNT = EXECUTE_COUNT+1,
        </if>
        <if test="redDotStatus != null ">
            red_dot_STATUS = #{redDotStatus,jdbcType=INTEGER},
        </if>
        <if test="errorReason != null and errorReason != ''">
            ERROR_REASON = #{errorReason,jdbcType=VARCHAR},
        </if>
        <if test="redDotSubclass != null ">
            red_dot_Subclass = #{redDotSubclass,jdbcType=INTEGER},
        </if>
        <if test="overTimeCheckRed != null ">
            OVER_TIME_CHECK_RED = #{overTimeCheckRed,jdbcType=INTEGER},
            OVER_TIME_CHECK_DATE = sysdate,
        </if>
        <if test="overTimeCheckRed == null ">
            LAST_UPDATED_DATE = sysdate,
        </if>
        red_dot_type = #{redDotType,jdbcType=INTEGER}
        where red_dot_type = #{redDotType,jdbcType=INTEGER}
        <if test="whseId != null and whseId != ''">
            and WHSEID = #{whseId, jdbcType=VARCHAR}
        </if>
        <if test="externreceiptkey != null and externreceiptkey != ''">
            and EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        </if>
        <if test="externreceiptnumber != null and externreceiptnumber != ''">
            and EXTERNRECEIPTNUMBER = #{externreceiptnumber, jdbcType=VARCHAR}
        </if>
    </update>
    <select id="queryRedDotInfoCount" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="java.lang.Integer">
        select sum(rowCount) from (
        <foreach collection="whseIdList" item="itemWhId" index="index" separator="union all">
            select count(1) rowCount
            from
            (select r.EXTERNRECEIPTKEY,r.RED_DOT_STATUS redDotStatus from PLUGIN.red_dot_execute_info r
            where r.red_dot_type=1 and r.WHSEID = #{itemWhId, jdbcType=VARCHAR}
            <if test="externalorderkey2 != null and externalorderkey2 != ''">
                and r.EXTERNALORDERKEY2 = #{externalorderkey2, jdbcType=VARCHAR}
            </if>
            <if test="billTypeList !=null and billTypeList.size()>0">
                and r.BILL_TYPE in
                <foreach collection="billTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="qtBillTypeList !=null and qtBillTypeList.size()>0">
                and r.BILL_TYPE not in
                <foreach collection="qtBillTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="externreceiptkey != null and externreceiptkey != ''">
                and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
            </if>
            <if test="isError != null and isError != '' ">
                <choose>
                    <when test="isError == 1 ">
                        and (r.OVER_TIME_CHECK_RED = 1 or r.RED_DOT_SUBCLASS > 0)
                    </when>
                    <otherwise>
                        and r.OVER_TIME_CHECK_RED = 0 and r.RED_DOT_SUBCLASS = 0
                    </otherwise>
                </choose>
            </if>
            group by r.EXTERNRECEIPTKEY,r.RED_DOT_STATUS) t
            inner join ${itemWhId}.Orders o on t.EXTERNRECEIPTKEY = o.ORDERKEY
        </foreach>
        ) temp
    </select>
    <select id="queryRedDotInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.vo.RedDotExecuteInfoVo">
      select s.* from (
        select temp.*,rownum rn from (
        <foreach collection="whseIdList" item="itemWhId" index="index" separator="union all">
            select #{itemWhId, jdbcType=VARCHAR} as whseId,t.redDotStatus,o.EXTERNORDERKEY,o.SUSR1 productId,o.SUSR2||o.SUSR4 itemCode,o.SUSR2 itemName,
            o.SUSR5 qty,cc.DESCRIPTION address,orz.REF43 lineBody,o.ref11 billType,
            o.ORDERKEY orderKey,o.c_company company,o.REF20 supplier,case when t.timeOutCount>0 then 'Y' else 'N' end isTimeOut,
            case when t.overTimeCheckCount>0 then 'Y' else 'N' end overTimeCheck,case when t.allocErrorCount>0 then 'Y' else 'N' end allocError,
            o.ADDDATE addDate
            from
            (select r.EXTERNRECEIPTKEY,r.RED_DOT_STATUS redDotStatus,sum(case when r.RED_DOT_SUBCLASS = 2 THEN 1 ELSE 0 END) timeOutCount,
            sum(r.OVER_TIME_CHECK_RED) overTimeCheckCount,sum(case when r.RED_DOT_SUBCLASS != 2 and r.RED_DOT_SUBCLASS !=0 THEN 1 ELSE 0 END) allocErrorCount
            from PLUGIN.red_dot_execute_info r
            where r.red_dot_type=1 and r.WHSEID = #{itemWhId, jdbcType=VARCHAR}
            <if test="externalorderkey2 != null and externalorderkey2 != ''">
                and r.EXTERNALORDERKEY2 = #{externalorderkey2, jdbcType=VARCHAR}
            </if>
            <if test="billTypeList !=null and billTypeList.size()>0">
                and r.BILL_TYPE in
                <foreach collection="billTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="qtBillTypeList !=null and qtBillTypeList.size()>0">
                and r.BILL_TYPE not in
                <foreach collection="qtBillTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="externreceiptkey != null and externreceiptkey != ''">
                and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
            </if>
            <if test="isError != null and isError != ''">
                <choose>
                    <when test="isError == 1 ">
                        and (r.OVER_TIME_CHECK_RED = 1 or r.RED_DOT_SUBCLASS > 0)
                    </when>
                    <otherwise>
                        and r.OVER_TIME_CHECK_RED = 0 and r.RED_DOT_SUBCLASS = 0
                    </otherwise>
                </choose>
            </if>
            group by r.EXTERNRECEIPTKEY,r.RED_DOT_STATUS) t
            inner join ${itemWhId}.Orders o on t.EXTERNRECEIPTKEY = o.ORDERKEY
            left join enterprise.codelkup cc on o.REF18 = cc.code and cc.listname = 'ZPRODADRES'
            left join ${itemWhId}.ORDERS_REF_ZTE orz on o.ORDERKEY = orz.ORDERKEY
        </foreach>
        ) temp ) s where 1=1
        <if test="startRow != null and endRow != null ">
            and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or endRow == null ">
            and  s.rn <![CDATA[<=]]> 1000000
        </if>
    </select>
    <select id="queryRedDotDetilInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.dto.OrdersDto">
        select r.WHSEID whseId,r.EXTERNRECEIPTKEY orderKey,r.EXTERNRECEIPTNUMBER orderLineNumber,r.red_dot_Subclass redDotSubclass,r.error_reason errorReason,
        o.EXTERNORDERKEY,null sku,null classgroup,r.RED_DOT_STATUS redDotStatus,'订单超时分配' errorType
        from PLUGIN.red_dot_execute_info r
        inner join ${whseId}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY
        where r.red_dot_type=1 and r.WHSEID = #{whseId, jdbcType=VARCHAR}
        and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and r.red_dot_Subclass = 1
        and to_number(o.STATUS) <![CDATA[<]]> 10
        union all
        select r.WHSEID whseId,r.EXTERNRECEIPTKEY orderKey,r.EXTERNRECEIPTNUMBER orderLineNumber,r.red_dot_Subclass redDotSubclass,r.error_reason errorReason,
        o.EXTERNORDERKEY,d.sku,vpc.classgroup,r.RED_DOT_STATUS redDotStatus,
        case r.red_dot_Subclass when 3 then '缺料' when 4 then '待补货' else '库存冻结' end errorType
        from PLUGIN.red_dot_execute_info r
        inner join ${whseId}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY
        inner join ${whseId}.Orderdetail d on r.EXTERNRECEIPTKEY = d.ORDERKEY and r.EXTERNRECEIPTNUMBER = d.ORDERLINENUMBER
        inner join ${whseId}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        left join ${whseId}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
        where r.red_dot_type=1 and r.WHSEID = #{whseId, jdbcType=VARCHAR}
        and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and r.red_dot_Subclass <![CDATA[>]]> 2 and d.QTYALLOCATED=0
        and d.QTYALLOCATED+d.QTYPICKED+d.ADJUSTEDQTY <![CDATA[<]]> d.ORIGINALQTY
        and to_number(o.STATUS) <![CDATA[<]]> 95 and to_number(d.STATUS) <![CDATA[<]]> 95
    </select>
    <select id="queryRedDotTimeOutDetilInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.dto.OrdersDto">
        select r.WHSEID whseId,r.EXTERNRECEIPTKEY orderKey,r.EXTERNRECEIPTNUMBER orderLineNumber,r.red_dot_Subclass redDotSubclass,
        o.EXTERNORDERKEY,d.sku,vpc.classgroup,r.RED_DOT_STATUS redDotStatus
        from PLUGIN.red_dot_execute_info r
        inner join ${whseId}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY
        inner join ${whseId}.Orderdetail d on r.EXTERNRECEIPTKEY = d.ORDERKEY and r.EXTERNRECEIPTNUMBER = d.ORDERLINENUMBER
        inner join ${whseId}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        left join ${whseId}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
        where r.red_dot_type=1 and r.WHSEID = #{whseId, jdbcType=VARCHAR}
        and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and r.red_dot_Subclass = 2 and d.QTYALLOCATED+d.QTYPICKED+d.ADJUSTEDQTY <![CDATA[>=]]> d.ORIGINALQTY
        and to_number(o.STATUS) <![CDATA[<]]> 95 and to_number(d.STATUS) <![CDATA[<]]> 95
        <if test="externreceiptnumber != null and externreceiptnumber != ''">
            and r.EXTERNRECEIPTNUMBER = #{externreceiptnumber, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryRedDotDetilLot02" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.dto.OrdersDto">
        select #{whseId} as whseId,temp.orderKey,temp.redDotStatus,od.sku,lt.lottable02,vpc.classgroup,temp.orderKey,temp.orderLineNumber
        from (
        select distinct t.EXTERNRECEIPTKEY orderKey,t.RED_DOT_STATUS redDotStatus,t.EXTERNRECEIPTNUMBER orderLineNumber,NVL(td.lot,d.lot) lot from
        PLUGIN.red_dot_execute_info t
        left join ${whseId}.taskdetail td on t.EXTERNRECEIPTKEY = td.ORDERKEY and t.EXTERNRECEIPTNUMBER =td.ORDERLINENUMBER and td.STORERKEY='ZTE'
        left join ${whseId}.demandallocation d on t.EXTERNRECEIPTKEY = d.ORDERKEY and t.EXTERNRECEIPTNUMBER =d.ORDERLINENUMBER and d.STORERKEY='ZTE'
        where t.red_dot_type=1 and t.WHSEID = #{whseId, jdbcType=VARCHAR}
        and t.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and t.Over_Time_Check_Red = 1
        ) temp
        inner join ${whseId}.Orders o on temp.orderKey = o.ORDERKEY
        inner join ${whseId}.Orderdetail od on temp.orderKey = od.ORDERKEY and temp.orderLineNumber = od.ORDERLINENUMBER
        inner join ${whseId}.LOTATTRIBUTE lt on temp.lot = lt.LOT AND od.sku = lt.sku AND lt.STORERKEY ='ZTE'
        left join ${whseId}.SKU sk ON od.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        left join ${whseId}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
        where to_number(o.STATUS) <![CDATA[<]]> 95 and to_number(od.STATUS) <![CDATA[<]]> 95
    </select>
    <select id="queryRedDotAllocDetilInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.dto.OrdersDto">
        select r.WHSEID whseId,r.EXTERNRECEIPTKEY orderKey,r.red_dot_Subclass redDotSubclass
        from PLUGIN.red_dot_execute_info r
        inner join ${whseId}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY
        where r.red_dot_type=1 and r.WHSEID = #{whseId, jdbcType=VARCHAR}
        and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and r.red_dot_Subclass = 1
        and to_number(o.STATUS) <![CDATA[<]]> 10
    </select>
    <select id="queryRedDotLackMateDetilInfo" parameterType="com.zte.domain.model.infor.RedDotExecuteInfo" resultType="com.zte.interfaces.infor.dto.OrdersDto">
        select r.WHSEID whseId,r.EXTERNRECEIPTKEY orderKey,r.EXTERNRECEIPTNUMBER orderLineNumber,r.red_dot_Subclass redDotSubclass,
        o.EXTERNORDERKEY,d.sku,vpc.classgroup,
        <if test="redDotSubclass != null and redDotSubclass == 3">
            1 redDotThreeClass,
        </if>
        o.ref11
        from PLUGIN.red_dot_execute_info r
        inner join ${whseId}.Orders o on r.EXTERNRECEIPTKEY = o.ORDERKEY
        inner join ${whseId}.Orderdetail d on r.EXTERNRECEIPTKEY = d.ORDERKEY and r.EXTERNRECEIPTNUMBER = d.ORDERLINENUMBER
        inner join ${whseId}.SKU sk ON d.SKU = sk.SKU AND sk.STORERKEY = 'ZTE'
        left join ${whseId}.V_PUTAWAYZONE_CLASSGROUP VPC ON sk.PUTAWAYZONE = VPC.PUTAWAYZONE
        where r.red_dot_type=1 and r.WHSEID = #{whseId, jdbcType=VARCHAR}
        and r.EXTERNRECEIPTKEY = #{externreceiptkey, jdbcType=VARCHAR}
        and d.QTYALLOCATED=0 and d.QTYALLOCATED+d.QTYPICKED+d.ADJUSTEDQTY <![CDATA[<]]> d.ORIGINALQTY
        and to_number(o.STATUS) <![CDATA[<]]> 95 and to_number(d.STATUS) <![CDATA[<]]> 95
        <if test="externreceiptnumber != null and externreceiptnumber != ''">
            and r.EXTERNRECEIPTNUMBER = #{externreceiptnumber, jdbcType=VARCHAR}
        </if>
        <if test="redDotSubclass != null ">
            and r.red_dot_Subclass = #{redDotSubclass, jdbcType=INTEGER}
        </if>
    </select>
</mapper>