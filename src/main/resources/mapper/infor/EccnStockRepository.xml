<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.EccnStockRepository">
	<resultMap id="BaseResultMap" type="com.zte.interfaces.infor.dto.EccnStock">
		<id column="RECORD_ID" jdbcType="DECIMAL" property="recordId" />
		<id column="VERSION" jdbcType="DECIMAL" property="version" />
		<result column="LICENSE" jdbcType="VARCHAR" property="license" />
		<result column="CCATS" jdbcType="VARCHAR" property="ccats" />
		<result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
		<result column="CALCULATE_TIME" jdbcType="VARCHAR" property="calculateTime" />
		<result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
		<result column="PURCHASE_PART_FLG" jdbcType="VARCHAR" property="purchasePartFlg" />
		<result column="DEMINIMIS_DM25" jdbcType="VARCHAR" property="deminimisDM25" />
		<result column="DEMINIMIS_DM10" jdbcType="VARCHAR" property="deminimisDM10" />
		<result column="EAR_FLG_DM25" jdbcType="VARCHAR" property="earFlgDM25" />
		<result column="EAR_FLG_DM10" jdbcType="VARCHAR" property="earFlgDM10" />
		<result column="US_ECCN" jdbcType="VARCHAR" property="usECCN" />
		<result column="LICENSE_EXCEPTION" jdbcType="VARCHAR" property="licenseException" />
		<result column="APPLICABLE_EXC_CLAUSE" jdbcType="VARCHAR"
			property="applicableExcClause" />
		<result column="COMP_TIME" jdbcType="VARCHAR" property="comptime" />
		<result column="ERR_MSG" jdbcType="DECIMAL" property="errMsg" />
		<result column="COMPLIANCE_FLG" jdbcType="VARCHAR" property="complianceFlg" />
		<result column="REMARKS_ZH" jdbcType="VARCHAR" property="remarksZH" />
		<result column="REMARKS_EN" jdbcType="VARCHAR" property="remarksEN" />
		<result column="LAST_UPDATED_TIME" jdbcType="TIMESTAMP"
			property="lastUpdatedTime" />
		<result column="LAST_UPDATED_BY" jdbcType="VARCHAR" property="lastUpdatedBy" />
		<result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
		<result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
		<result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
	</resultMap>

	<sql id="Base_Column_List">
		RECORD_ID, LICENSE, CCATS, ORG_ID, CALCULATE_TIME, ITEM_NO,
		PURCHASE_PART_FLG,
		DEMINIMIS_DM25,
		DEMINIMIS_DM10, EAR_FLG_DM25,
		EAR_FLG_DM10, US_ECCN, LICENSE_EXCEPTION,
		APPLICABLE_EXC_CLAUSE,
		COMP_TIME, ERR_MSG, COMPLIANCE_FLG, REMARKS_ZH, REMARKS_EN,
		LAST_UPDATED_TIME,
		LAST_UPDATED_BY,
		ENABLED_FLAG, CREATED_BY,
		CREATION_DATE
	</sql>

	<select id="selectEccnStockAll" resultMap="BaseResultMap">
		select RECORD_ID, LICENSE, CCATS, ORG_ID, CALCULATE_TIME, ITEM_NO,
		PURCHASE_PART_FLG,
		DEMINIMIS_DM25, DEMINIMIS_DM10, EAR_FLG_DM25,
		EAR_FLG_DM10, US_ECCN,
		LICENSE_EXCEPTION,
		APPLICABLE_EXC_CLAUSE,
		COMP_TIME, ERR_MSG, COMPLIANCE_FLG, REMARKS_ZH, REMARKS_EN,
		LAST_UPDATED_TIME, LAST_UPDATED_BY, ENABLED_FLAG, CREATED_BY,
		CREATION_DATE
		from PLUGIN.ECCN_STOCK where ENABLED_FLAG='Y'
		<if test="itemNo != null">
			and ITEM_NO = #{itemNo,jdbcType=VARCHAR}
		</if>
		<if test="orgId != null">
			and ORG_ID = #{orgId,jdbcType=VARCHAR}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=VARCHAR}
		</if>
		
	</select>

	<select id="selectEccnStockById" parameterType="com.zte.interfaces.infor.dto.EccnStock"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PLUGIN.ECCN_STOCK
		where RECORD_ID = #{recordId,jdbcType=DECIMAL}
	</select>

	<delete id="deleteEccnStockById" parameterType="com.zte.interfaces.infor.dto.EccnStock">
		update PLUGIN.ECCN_STOCK set ENABLED_FLAG = 'Y'
		where RECORD_ID = #{recordId,jdbcType=DECIMAL}
	</delete>

	<insert id="insertEccnStock" parameterType="com.zte.interfaces.infor.dto.EccnStock">
		insert into
		PLUGIN.ECCN_STOCK (RECORD_ID, LICENSE, CCATS,
		ORG_ID, CALCULATE_TIME, ITEM_NO,
		PURCHASE_PART_FLG, DEMINIMIS_DM25, DEMINIMIS_DM10,
		EAR_FLG_DM25,
		EAR_FLG_DM10, US_ECCN,
		LICENSE_EXCEPTION, APPLICABLE_EXC_CLAUSE,
		COMP_TIME,
		ERR_MSG, COMPLIANCE_FLG, REMARKS_ZH,
		REMARKS_EN,
		LAST_UPDATED_TIME, LAST_UPDATED_BY,
		ENABLED_FLAG, CREATED_BY,
		CREATION_DATE
		)
		values (plugin.SEQUENCE_ECCN_STOCK.nextval,
		#{license,jdbcType=VARCHAR},
		#{ccats,jdbcType=VARCHAR},
		#{orgId,jdbcType=VARCHAR}, #{calculateTime,jdbcType=VARCHAR},
		#{itemNo,jdbcType=VARCHAR},
		#{purchasePartFlg,jdbcType=VARCHAR},
		#{deminimisDM25,jdbcType=VARCHAR},
		#{deminimisDM10,jdbcType=VARCHAR},
		#{earFlgDM25,jdbcType=VARCHAR}, #{earFlgDM10,jdbcType=VARCHAR},
		#{usECCN,jdbcType=VARCHAR},
		#{licenseException,jdbcType=VARCHAR},
		#{applicableExcClause,jdbcType=VARCHAR}, #{comptime,jdbcType=VARCHAR},
		#{errMsg,jdbcType=DECIMAL}, #{complianceFlg,jdbcType=VARCHAR},
		#{remarksZH,jdbcType=VARCHAR},
		#{remarksEN,jdbcType=VARCHAR},
		#{lastUpdatedTime,jdbcType=TIMESTAMP},
		#{lastUpdatedBy,jdbcType=VARCHAR},
		'Y', #{createdBy,jdbcType=VARCHAR},
		sysdate
		)
	</insert>

	<insert id="insertEccnStocks" parameterType="java.util.List">
		insert into plugin.ECCN_STOCK (RECORD_ID, LICENSE, CCATS,
		ORG_ID,
		CALCULATE_TIME, ITEM_NO,
		PURCHASE_PART_FLG, DEMINIMIS_DM25,
		DEMINIMIS_DM10,
		EAR_FLG_DM25, EAR_FLG_DM10, US_ECCN,
		LICENSE_EXCEPTION,
		APPLICABLE_EXC_CLAUSE, COMP_TIME,
		ERR_MSG, COMPLIANCE_FLG, REMARKS_ZH,
		REMARKS_EN, LAST_UPDATED_TIME, LAST_UPDATED_BY,
		ENABLED_FLAG,
		CREATED_BY, CREATION_DATE,VERSION,PART_LINK
		)
		select plugin.SEQUENCE_ECCN_STOCK.nextval RECORD_ID, LICENSE, CCATS,
		ORG_ID,
		CALCULATE_TIME, ITEM_NO,
		PURCHASE_PART_FLG, DEMINIMIS_DM25,
		DEMINIMIS_DM10,
		EAR_FLG_DM25, EAR_FLG_DM10, US_ECCN,
		LICENSE_EXCEPTION,
		APPLICABLE_EXC_CLAUSE, COMP_TIME,
		ERR_MSG, COMPLIANCE_FLG, REMARKS_ZH,
		REMARKS_EN, LAST_UPDATED_TIME, LAST_UPDATED_BY,
		ENABLED_FLAG,
		CREATED_BY, CREATION_DATE,VERSION,PART_LINK
		from (
		<foreach collection="list" item="item" index="index"
			separator="UNION ALL">
			SELECT
			#{item.license,jdbcType=VARCHAR} LICENSE,
			#{item.ccats,jdbcType=VARCHAR} CCATS,
			#{item.orgId,jdbcType=VARCHAR} ORG_ID,
			#{item.calculateTime,jdbcType=VARCHAR} CALCULATE_TIME,
			#{item.itemNo,jdbcType=VARCHAR} ITEM_NO,
			#{item.purchasePartFlg,jdbcType=VARCHAR} PURCHASE_PART_FLG,
			#{item.deminimisDM25,jdbcType=VARCHAR} DEMINIMIS_DM25,
			#{item.deminimisDM10,jdbcType=VARCHAR} DEMINIMIS_DM10,
			#{item.earFlgDM25,jdbcType=VARCHAR} EAR_FLG_DM25,
			#{item.earFlgDM10,jdbcType=VARCHAR} EAR_FLG_DM10,
			#{item.usECCN,jdbcType=VARCHAR} US_ECCN,
			#{item.licenseException,jdbcType=VARCHAR} LICENSE_EXCEPTION,
			#{item.applicableExcClause,jdbcType=VARCHAR} APPLICABLE_EXC_CLAUSE,
			#{item.comptime,jdbcType=VARCHAR} COMP_TIME,
			#{item.errMsg,jdbcType=DECIMAL} ERR_MSG,
			#{item.complianceFlg,jdbcType=VARCHAR} COMPLIANCE_FLG,
			#{item.remarksZH,jdbcType=VARCHAR} REMARKS_ZH,
			#{item.remarksEN,jdbcType=VARCHAR} REMARKS_EN,
			#{item.lastUpdatedTime,jdbcType=TIMESTAMP} LAST_UPDATED_TIME,
			'syncEccn' LAST_UPDATED_BY,
			'Y' ENABLED_FLAG,
			'syncEccn' CREATED_BY,
			sysdate CREATION_DATE,
			#{item.version,jdbcType=VARCHAR} VERSION,
			#{item.partLink,jdbcType=VARCHAR} PART_LINK
			from dual
		</foreach>
		) a where a.ORG_ID = '0000'
	</insert>


	<insert id="insertEccnStockSelective" parameterType="com.zte.interfaces.infor.dto.EccnStock">
		insert into PLUGIN.ECCN_STOCK
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="recordId != null">
				RECORD_ID,
			</if>

			<if test="license != null">
				LICENSE,
			</if>

			<if test="ccats != null">
				CCATS,
			</if>

			<if test="orgId != null">
				ORG_ID,
			</if>

			<if test="calculateTime != null">
				CALCULATE_TIME,
			</if>

			<if test="itemNo != null">
				ITEM_NO,
			</if>

			<if test="purchasePartFlg != null">
				PURCHASE_PART_FLG,
			</if>

			<if test="deminimisDM25 != null">
				DEMINIMIS_DM25,
			</if>

			<if test="deminimisDM10 != null">
				DEMINIMIS_DM10,
			</if>

			<if test="earFlgDM25 != null">
				EAR_FLG_DM25,
			</if>

			<if test="earFlgDM10 != null">
				EAR_FLG_DM10,
			</if>

			<if test="usECCN != null">
				US_ECCN,
			</if>

			<if test="licenseException != null">
				LICENSE_EXCEPTION,
			</if>

			<if test="applicableExcClause != null">
				APPLICABLE_EXC_CLAUSE,
			</if>

			<if test="comptime != null">
				COMP_TIME,
			</if>

			<if test="errMsg != null">
				ERR_MSG,
			</if>

			<if test="complianceFlg != null">
				COMPLIANCE_FLG,
			</if>

			<if test="remarksZH != null">
				REMARKS_ZH,
			</if>

			<if test="remarksEN != null">
				REMARKS_EN,
			</if>

			<if test="lastUpdatedTime != null">
				LAST_UPDATED_TIME,
			</if>

			<if test="lastUpdatedBy != null">
				LAST_UPDATED_BY,
			</if>

			<if test="enabledFlag != null">
				ENABLED_FLAG,
			</if>

			<if test="createdBy != null">
				CREATED_BY,
			</if>

			<if test="creationDate != null">
				CREATION_DATE,
			</if>

		</trim>

		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="recordId != null">
				plugin.SEQUENCE_ECCN_STOCK.nextval,
			</if>

			<if test="license != null">
				#{license,jdbcType=VARCHAR},
			</if>

			<if test="ccats != null">
				#{ccats,jdbcType=VARCHAR},
			</if>

			<if test="orgId != null">
				#{orgId,jdbcType=VARCHAR},
			</if>

			<if test="calculateTime != null">
				#{calculateTime,jdbcType=VARCHAR},
			</if>

			<if test="itemNo != null">
				#{itemNo,jdbcType=VARCHAR},
			</if>

			<if test="purchasePartFlg != null">
				#{purchasePartFlg,jdbcType=VARCHAR},
			</if>

			<if test="deminimisDM25 != null">
				#{deminimisDM25,jdbcType=VARCHAR},
			</if>

			<if test="deminimisDM10 != null">
				#{deminimisDM10,jdbcType=VARCHAR},
			</if>

			<if test="earFlgDM25 != null">
				#{earFlgDM25,jdbcType=VARCHAR},
			</if>

			<if test="earFlgDM10 != null">
				#{earFlgDM10,jdbcType=VARCHAR},
			</if>

			<if test="usECCN != null">
				#{usECCN,jdbcType=VARCHAR},
			</if>

			<if test="licenseException != null">
				#{licenseException,jdbcType=VARCHAR},
			</if>

			<if test="applicableExcClause != null">
				#{applicableExcClause,jdbcType=VARCHAR},
			</if>

			<if test="comptime != null">
				#{comptime,jdbcType=VARCHAR},
			</if>

			<if test="errMsg != null">
				#{errMsg,jdbcType=DECIMAL},
			</if>

			<if test="complianceFlg != null">
				#{complianceFlg,jdbcType=VARCHAR},
			</if>

			<if test="remarksZH != null">
				#{remarksZH,jdbcType=VARCHAR},
			</if>

			<if test="remarksEN != null">
				#{remarksEN,jdbcType=VARCHAR},
			</if>

			<if test="lastUpdatedTime != null">
				#{lastUpdatedTime,jdbcType=TIMESTAMP},
			</if>

			<if test="lastUpdatedBy != null">
				#{lastUpdatedBy,jdbcType=VARCHAR},
			</if>

			<if test="enabledFlag != null">
				#{enabledFlag,jdbcType=VARCHAR},
			</if>

			<if test="createdBy != null">
				#{createdBy,jdbcType=VARCHAR},
			</if>

			<if test="creationDate != null">
				sysdate,
			</if>

		</trim>

	</insert>

	<update id="updateEccnStocks" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index"
			open="begin" separator=";" close="; end;">
			update PLUGIN.ECCN_STOCK
			<set>
				<trim suffixOverrides=",">
					<if test="item.license != null and item.license != ''">
						LICENSE = #{item.license,jdbcType=VARCHAR},
					</if>

					<if test="item.ccats != null and item.ccats != ''">
						CCATS = #{item.ccats,jdbcType=VARCHAR},
					</if>

					<if test="item.orgId != null and item.orgId != ''">
						ORG_ID = #{item.orgId,jdbcType=VARCHAR},
					</if>

					<if test="item.calculateTime != null">
						CALCULATE_TIME = #{item.calculateTime,jdbcType=VARCHAR},
					</if>

					<if test="item.itemNo != null and item.itemNo != ''">
						ITEM_NO = #{item.itemNo,jdbcType=VARCHAR},
					</if>

					<if test="item.purchasePartFlg != null and item.purchasePartFlg != ''">
						PURCHASE_PART_FLG = #{item.purchasePartFlg,jdbcType=VARCHAR},
					</if>

					<if test="item.deminimisDM25 != null and item.deminimisDM25 != ''">
						DEMINIMIS_DM25 = #{item.deminimisDM25,jdbcType=VARCHAR},
					</if>

					<if test="item.deminimisDM10 != null and item.deminimisDM10 != ''">
						DEMINIMIS_DM10 = #{item.deminimisDM10,jdbcType=VARCHAR},
					</if>

					<if test="item.earFlgDM25 != null and item.earFlgDM25 != ''">
						EAR_FLG_DM25 = #{item.earFlgDM25,jdbcType=VARCHAR},
					</if>

					<if test="item.earFlgDM10 != null and item.earFlgDM10 != ''">
						EAR_FLG_DM10 = #{item.earFlgDM10,jdbcType=VARCHAR},
					</if>

					<if test="item.usECCN != null and item.usECCN != ''">
						us_ECCN = #{item.usECCN,jdbcType=VARCHAR},
					</if>

					<if test="item.licenseException != null and item.licenseException != ''">
						LICENSE_EXCEPTION = #{item.licenseException,jdbcType=VARCHAR},
					</if>

					<if test="item.applicableExcClause != null and item.applicableExcClause != ''">
						APPLICABLE_EXC_CLAUSE = #{item.applicableExcClause,jdbcType=VARCHAR},
					</if>

					<if test="item.comptime != null">
						COMP_TIME = #{item.comptime,jdbcType=VARCHAR},
					</if>

					<if test="item.errMsg != null">
						ERR_MSG = #{item.errMsg,jdbcType=DECIMAL},
					</if>

					<if test="item.complianceFlg != null">
						COMPLIANCE_FLG = #{item.complianceFlg,jdbcType=VARCHAR},
					</if>

					<if test="item.remarksZH != null and item.remarksZH != ''">
						REMARKS_ZH = #{item.remarksZH,jdbcType=VARCHAR},
					</if>

					<if test="item.remarksEN != null and item.remarksEN != ''">
						REMARKS_EN = #{item.remarksEN,jdbcType=VARCHAR},
					</if>

					<if test="item.lastUpdatedTime != null">
						LAST_UPDATED_TIME = #{item.lastUpdatedTime,jdbcType=TIMESTAMP},
					</if>

					<if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">
						LAST_UPDATED_BY = #{item.lastUpdatedBy,jdbcType=VARCHAR},
					</if>

					<if test="item.version != null and item.version != ''">
						VERSION = #{item.version,jdbcType=VARCHAR},
					</if>

					<if test="item.partLink != null and item.partLink != ''">
						PART_LINK = #{item.partLink,jdbcType=VARCHAR},
					</if>

				</trim>
			</set>

			where RECORD_ID = #{item.recordId,jdbcType=DECIMAL}
		</foreach>
	</update>

	<update id="updateEccnStockByIdSelective" parameterType="com.zte.interfaces.infor.dto.EccnStock">
		update PLUGIN.ECCN_STOCK
		<set>
			<trim suffixOverrides=",">
				<if test="license != null">
					LICENSE = #{license,jdbcType=VARCHAR},
				</if>

				<if test="ccats != null">
					CCATS = #{ccats,jdbcType=VARCHAR},
				</if>

				<if test="orgId != null">
					ORG_ID = #{orgId,jdbcType=VARCHAR},
				</if>

				<if test="calculateTime != null">
					CALCULATE_TIME = #{calculateTime,jdbcType=VARCHAR},
				</if>

				<if test="itemNo != null">
					ITEM_NO = #{itemNo,jdbcType=VARCHAR},
				</if>

				<if test="purchasePartFlg != null">
					PURCHASE_PART_FLG = #{purchasePartFlg,jdbcType=VARCHAR},
				</if>

				<if test="deminimisDM25 != null">
					DEMINIMIS_DM25 = #{deminimisDM25,jdbcType=VARCHAR},
				</if>

				<if test="deminimisDM10 != null">
					DEMINIMIS_DM10 = #{deminimisDM10,jdbcType=VARCHAR},
				</if>

				<if test="earFlgDM25 != null">
					EAR_FLG_DM25 = #{earFlgDM25,jdbcType=VARCHAR},
				</if>

				<if test="earFlgDM10 != null">
					EAR_FLG_DM10 = #{earFlgDM10,jdbcType=VARCHAR},
				</if>

				<if test="usECCN != null">
					us_ECCN = #{usECCN,jdbcType=VARCHAR},
				</if>

				<if test="licenseException != null">
					LICENSE_EXCEPTION =
					#{licenseException,jdbcType=VARCHAR},
				</if>

				<if test="applicableExcClause != null">
					APPLICABLE_EXC_CLAUSE =
					#{applicableExcClause,jdbcType=VARCHAR},
				</if>

				<if test="comptime != null">
					COMP_TIME = #{comptime,jdbcType=VARCHAR},
				</if>

				<if test="errMsg != null">
					ERR_MSG = #{errMsg,jdbcType=DECIMAL},
				</if>

				<if test="complianceFlg != null">
					COMPLIANCE_FLG = #{complianceFlg,jdbcType=VARCHAR},
				</if>

				<if test="remarksZH != null">
					REMARKS_ZH = #{remarksZH,jdbcType=VARCHAR},
				</if>

				<if test="remarksEN != null">
					REMARKS_EN = #{remarksEN,jdbcType=VARCHAR},
				</if>

				<if test="lastUpdatedTime != null">
					LAST_UPDATED_TIME =
					#{lastUpdatedTime,jdbcType=TIMESTAMP},
				</if>

				<if test="lastUpdatedBy != null">
					LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR},
				</if>

			</trim>
		</set>

		where RECORD_ID = #{recordId,jdbcType=DECIMAL}
	</update>

	<update id="updateEccnStockById" parameterType="com.zte.interfaces.infor.dto.EccnStock">
		update PLUGIN.ECCN_STOCK
		set LICENSE = #{license,jdbcType=VARCHAR},
		CCATS =
		#{ccats,jdbcType=VARCHAR},
		ORG_ID = #{orgId,jdbcType=VARCHAR},
		CALCULATE_TIME = #{calculateTime,jdbcType=VARCHAR},
		ITEM_NO =
		#{itemNo,jdbcType=VARCHAR},
		PURCHASE_PART_FLG =
		#{purchasePartFlg,jdbcType=VARCHAR},
		DEMINIMIS_DM25 =
		#{deminimisDM25,jdbcType=VARCHAR},
		DEMINIMIS_DM10 =
		#{deminimisDM10,jdbcType=VARCHAR},
		EAR_FLG_DM25 =
		#{earFlgDM25,jdbcType=VARCHAR},
		EAR_FLG_DM10 =
		#{earFlgDM10,jdbcType=VARCHAR},
		US_ECCN = #{usECCN,jdbcType=VARCHAR},
		LICENSE_EXCEPTION = #{licenseException,jdbcType=VARCHAR},
		APPLICABLE_EXC_CLAUSE = #{applicableExcClause,jdbcType=VARCHAR},
		COMP_TIME = #{comptime,jdbcType=VARCHAR},
		ERR_MSG =
		#{errMsg,jdbcType=DECIMAL},
		COMPLIANCE_FLG =
		#{complianceFlg,jdbcType=VARCHAR},
		REMARKS_ZH =
		#{remarksZH,jdbcType=VARCHAR},
		REMARKS_EN =
		#{remarksEN,jdbcType=VARCHAR},
		LAST_UPDATED_TIME =
		#{lastUpdatedTime,jdbcType=TIMESTAMP},
		LAST_UPDATED_BY =
		#{lastUpdatedBy,jdbcType=VARCHAR}
		where RECORD_ID =
		#{recordId,jdbcType=DECIMAL}
	</update>
	
	
	<insert id="insertEccnStockLog" parameterType="com.zte.interfaces.infor.dto.EccnStockLog">
		insert into PLUGIN.ECCN_STOCK_LOG
		<trim prefix="(" suffix=")" suffixOverrides=",">			
		   RECORD_ID,		
			<if test="license != null">
				LICENSE,
			</if>

			<if test="ccats != null">
				CCATS,
			</if>

			<if test="orgId != null">
				ORG_ID,
			</if>

			<if test="calculateTime != null">
				CALCULATE_TIME,
			</if>

			<if test="itemNo != null">
				ITEM_NO,
			</if>

			<if test="purchasePartFlg != null">
				PURCHASE_PART_FLG,
			</if>

			<if test="deminimisDM25 != null">
				DEMINIMIS_DM25,
			</if>

			<if test="deminimisDM10 != null">
				DEMINIMIS_DM10,
			</if>

			<if test="earFlgDM25 != null">
				EAR_FLG_DM25,
			</if>

			<if test="earFlgDM10 != null">
				EAR_FLG_DM10,
			</if>

			<if test="usECCN != null">
				US_ECCN,
			</if>

			<if test="licenseException != null">
				LICENSE_EXCEPTION,
			</if>

			<if test="applicableExcClause != null">
				APPLICABLE_EXC_CLAUSE,
			</if>

			<if test="comptime != null">
				COMP_TIME,
			</if>

			<if test="errMsg != null">
				ERR_MSG,
			</if>

			<if test="complianceFlg != null">
				COMPLIANCE_FLG,
			</if>

			<if test="remarksZH != null">
				REMARKS_ZH,
			</if>

			<if test="remarksEN != null">
				REMARKS_EN,
			</if>

			<if test="lastUpdatedTime != null">
				LAST_UPDATED_TIME,
			</if>

			<if test="lastUpdatedBy != null">
				LAST_UPDATED_BY,
			</if>			
		     ENABLED_FLAG,	
			<if test="createdBy != null">
				CREATED_BY,
			</if>			
		    CREATION_DATE,
		    sync_error,sysnc_date,
		</trim>

		<trim prefix="values (" suffix=")" suffixOverrides=",">			
		   plugin.SEQUENCE_ECCN_STOCK.nextval,	
			<if test="license != null">
				#{license,jdbcType=VARCHAR},
			</if>

			<if test="ccats != null">
				#{ccats,jdbcType=VARCHAR},
			</if>

			<if test="orgId != null">
				#{orgId,jdbcType=VARCHAR},
			</if>

			<if test="calculateTime != null">
				#{calculateTime,jdbcType=VARCHAR},
			</if>

			<if test="itemNo != null">
				#{itemNo,jdbcType=VARCHAR},
			</if>

			<if test="purchasePartFlg != null">
				#{purchasePartFlg,jdbcType=VARCHAR},
			</if>

			<if test="deminimisDM25 != null">
				#{deminimisDM25,jdbcType=VARCHAR},
			</if>

			<if test="deminimisDM10 != null">
				#{deminimisDM10,jdbcType=VARCHAR},
			</if>

			<if test="earFlgDM25 != null">
				#{earFlgDM25,jdbcType=VARCHAR},
			</if>

			<if test="earFlgDM10 != null">
				#{earFlgDM10,jdbcType=VARCHAR},
			</if>

			<if test="usECCN != null">
				#{usECCN,jdbcType=VARCHAR},
			</if>

			<if test="licenseException != null">
				#{licenseException,jdbcType=VARCHAR},
			</if>

			<if test="applicableExcClause != null">
				#{applicableExcClause,jdbcType=VARCHAR},
			</if>

			<if test="comptime != null">
				#{comptime,jdbcType=VARCHAR},
			</if>

			<if test="errMsg != null">
				#{errMsg,jdbcType=DECIMAL},
			</if>

			<if test="complianceFlg != null">
				#{complianceFlg,jdbcType=VARCHAR},
			</if>

			<if test="remarksZH != null">
				#{remarksZH,jdbcType=VARCHAR},
			</if>

			<if test="remarksEN != null">
				#{remarksEN,jdbcType=VARCHAR},
			</if>

			<if test="lastUpdatedTime != null">
				#{lastUpdatedTime,jdbcType=TIMESTAMP},
			</if>

			<if test="lastUpdatedBy != null">
				#{lastUpdatedBy,jdbcType=VARCHAR},
			</if>
			'Y',
			<if test="createdBy != null">
				#{createdBy,jdbcType=VARCHAR},
			</if>			
		   sysdate,#{syncError,jdbcType=VARCHAR},SYSDATE,    
		</trim>		
			
	</insert>


</mapper>
