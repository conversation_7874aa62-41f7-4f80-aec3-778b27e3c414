<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.OverdueMaterialsRepository">
    <select id="selectOverdueMaterialsList" parameterType="com.zte.interfaces.infor.dto.OverdueMaterialsDTO" resultType="com.zte.interfaces.infor.dto.OverdueMaterialsDTO">
        select s.* from (
        select r.factory,r.item_no,r.qty,r.last_updated_by lastUpdatedBy,r.last_update_date lastUpdatedDate,rownum rn
        from plugin.delay_recheck_require r
        WHERE 1=1
        <if test="factory != null and factory !='' ">
            and r.factory  = #{factory,jdbcType=VARCHAR}
        </if>
        <if test="itemNo != null and itemNo != ''">
            AND r.item_no = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="creationBegin != null and creationBegin !='' ">
            and r.last_update_date <![CDATA[>=]]> to_date(#{creationBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="creationEnd != null and creationEnd !='' ">
            and r.last_update_date <![CDATA[<=]]> to_date(#{creationEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        ) s where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  s.rn &lt;= 500000
        </if>
    </select>
    <select id="selectOverdueMaterialsListVOTotal" parameterType="com.zte.interfaces.infor.dto.OverdueMaterialsDTO" resultType="java.lang.Integer">
        select count(1)
        from plugin.delay_recheck_require r
        where 1 = 1
        <if test="factory != null and factory !='' ">
            and r.factory  = #{factory,jdbcType=VARCHAR}
        </if>
        <if test="itemNo != null and itemNo != ''">
            AND r.item_no = #{itemNo,jdbcType=VARCHAR}
        </if>
        <if test="creationBegin != null and creationBegin !='' ">
            and r.last_update_date <![CDATA[>=]]> to_date(#{creationBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="creationEnd != null and creationEnd !='' ">
            and r.last_update_date <![CDATA[<=]]> to_date(#{creationEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>
    <delete id="deleteRecheckRequire">
        TRUNCATE TABLE plugin.delay_recheck_require
    </delete>

    <insert id="insertOverdueMaterialsRequire" parameterType="com.zte.interfaces.infor.dto.OverdueMaterialsDTO">
        insert into plugin.delay_recheck_require (serail_id, factory, item_no, qty, created_by, creation_date, last_updated_by, last_update_date)
        select plugin.seq_delay_recheck_require.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.factory,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR},  #{item.qty,jdbcType=DECIMAL},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},sysdate as creationDate, #{item.lastUpdatedBy,jdbcType=VARCHAR},sysdate as lastUpdateDate
            from dual
        </foreach>
        ) temp
    </insert>
    <select id="getBatchOverdueItemNo" parameterType="java.lang.String" resultType="java.lang.String">
        select distinct r.item_no itemNo
        from plugin.delay_recheck_require r
        where r.item_no in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectOverdueRequireList" resultType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO">
        select r.FACTORY planOrgName,(case when r.FACTORY='南京' then 'WMWHSE26' else nvl(cast(s.DEFAULTWH1 as varchar2(60)),'') end) as whseId,r.ITEM_NO itemNo,r.qty from plugin.delay_recheck_require r
        left join ENTERPRISE.SKU s on r.ITEM_NO=s.SKU
        where r.enabled_flag='Y' and s.STORERKEY = 'ZTE'
    </select>
    <select id="selectOverdueMaterialsEmailList" parameterType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO"
            resultType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO">
        select ktemp.* from (select rtemp.*,rownum rn from
        (select temp.*,ft.description stockName from (
        select s.whseid,
        s.itemNo,
        s.itemName,
        s.itemBarcode,
        s.classgroup,
        s.bomNo,
        s.prodPlanNo,
        s.productClassName,
        s.planOrgName,
        s.planner,
        s.remark,
        s.reqQty,
        case when p.whseid is null then s.qty else CASE WHEN s.qty<![CDATA[<=]]>p.req_qty THEN 0 else s.qty-p.req_qty end end qty
        from (
        select
        t.whseid,
        t.item_no itemNo,
        t.item_name itemName,
        t.item_barcode itemBarcode,
        t.classgroup,
        t.bom_no bomNo,
        t.prod_plan_no prodPlanNo,
        t.product_class_name productClassName,
        t.plan_org_name planOrgName,
        t.planner,
        t.remark,
        min(t.req_qty) reqQty,
        sum(t.qty) qty
        from plugin.delay_recheck_material_email t
        where t.enabled_flag = 'Y' and t.plan_flag = 'Y' and t.email_type = 2
        group by t.whseid,t.item_no,t.item_name,t.item_barcode,t.classgroup,t.plan_org_name,t.bom_no,
        t.prod_plan_no,t.product_class_name,t.planner,t.remark
        ) s
        left join (select whseid,item_no,item_barcode,sum(req_qty) req_qty
                   from plugin.DELAY_RECHECK_MATERIAL_PLAN group by whseid,item_no,item_barcode
        ) p on s.whseid=p.whseid and s.itemNo=p.item_no and s.itemBarcode=p.item_barcode
        union all
        select ps.whseid,ps.item_no itemNo,ts.item_name itemName,ps.item_barcode itemBarcode,ts.classgroup,ps.bom_no bomNo,
        ps.prod_plan_no prodPlanNo,ps.product_class_name productClassName,ps.plan_org_name planOrgName,ps.planner,ts.remark,
        min(ps.req_qty) reqQty,min(ps.req_qty) qty
        from plugin.DELAY_RECHECK_MATERIAL_PLAN ps
        inner join plugin.delay_recheck_material_email ts on ps.whseid=ts.whseid and ps.item_no=ts.item_no and ps.item_barcode=ts.item_barcode
        where ts.enabled_flag = 'Y' and ts.plan_flag = 'Y' and ts.email_type = 2
        group by ps.whseid,ps.item_no,ts.item_name,ps.item_barcode,ts.classgroup,ps.plan_org_name,ps.bom_no,
        ps.prod_plan_no,ps.product_class_name,ps.planner,ts.remark
        ) temp
        LEFT JOIN ENTERPRISE.FACILITYNEST FT ON temp.whseid = UPPER(SUBSTR(FT.NAME, 7)) AND FT.PARENTNESTID = 1
        where temp.qty<![CDATA[>]]>0
        order by temp.whseid,temp.itemNo,temp.itemBarcode
        ) rtemp) ktemp
        where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  ktemp.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  ktemp.rn &lt;= 500000
        </if>
    </select>
    <select id="selectOverdueEmailListCount" resultType="java.lang.Integer">
        select count(1)
        from plugin.delay_recheck_material_email t
        where t.enabled_flag = 'Y'
        and t.plan_flag = 'Y'
        and t.email_type = 2
    </select>
    <select id="selectOverdueMaterialItemList" parameterType="java.lang.Integer" resultType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO">
        select t2.WHSEID whseId,t1.itemNo,t2.ITEM_BARCODE itemBarcode,
        max(t2.lottable08) lottable08,
        max(t2.lottable12) lottable12,nvl(t2.fefo,'0') fefo,sum(t2.qty) qty,'1' emailType
        from (select distinct t.item_no itemNo
               from plugin.delay_recheck_material_email t
              where t.enabled_flag = 'Y'
              and t.plan_flag = 'N'
              and rownum <![CDATA[<]]> #{execCount ,jdbcType=INTEGER}
        ) t1
        inner join delay_recheck_material_email t2 on t1.itemNo=t2.item_no
        group by t2.whseId,t1.itemNo,t2.ITEM_BARCODE,t2.FEFO
    </select>
    <select id="selectRecheckMaterialItemList" parameterType="java.lang.String" resultType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO">
        SELECT
        t.WHSEID whseId,
        t.ITEM_NO itemNo,
        t.ITEM_BARCODE itemBarcode,
        zb.IS_LEAD lottable08,
        sum(t.qty) qty,
        '1' emailType,
        CASE WHEN t.QC_STATUS = 'CLOSED' THEN '已复检' ELSE '复检中' END qcStatus
        FROM PLUGIN.DELAY_RECHECK_BILL_DETAIL t
        INNER JOIN ENTERPRISE.ZTEBARCODE zb ON zb.ITEM_BARCODE = t.ITEM_BARCODE
        WHERE
        t.ITEM_NO IN
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item, jdbcType=VARCHAR}
        </foreach>
        AND (
        (t.QC_STATUS != 'CLOSED')
        OR
        (t.CHECK_RESULT = 1 AND t.QC_STATUS = 'CLOSED' AND (
        (zb.PRODUCT_DATE + zb.QUALITY_TIME * 30 + zb.QUALITY_NEW_TIME * 60) <![CDATA[>=]]> TRUNC(SYSDATE)
        OR EXISTS (
        SELECT 1
        FROM PLUGIN.DELAY_RECHECK_BILL_DETAIL t2
        WHERE
        t2.WHSEID = t.WHSEID
        AND t2.ITEM_BARCODE = t.ITEM_BARCODE
        AND t2.CHECK_RESULT = 1
        GROUP BY t2.ITEM_BARCODE
        HAVING TRUNC(SYSDATE) - TRUNC(MAX(t2.CHECK_END_DATE)) <![CDATA[<=]]> (
        SELECT TO_NUMBER(LOOKUP_MEANING)
        FROM plugin.sys_lookup_values
        WHERE LOOKUP_CODE = '100005900003' AND ENABLED_FLAG = 'Y'
        )
        )
        ))
        )
        GROUP BY t.WHSEID, t.ITEM_NO, t.ITEM_BARCODE,zb.IS_LEAD,
        CASE WHEN t.QC_STATUS = 'CLOSED' THEN '已复检' ELSE '复检中' END
        HAVING MAX(CASE WHEN t.QC_STATUS = 'CLOSED' THEN 1 ELSE 0 END) <![CDATA[>]]> 0
        OR MIN(CASE WHEN t.QC_STATUS != 'CLOSED' THEN 1 ELSE 0 END) <![CDATA[>]]> 0
    </select>
    <update id="batchUpdatePlanFlag" parameterType="com.zte.interfaces.infor.vo.OverDueMaterialsEmailListVO">
        <foreach collection="list" item="info" index="index" open="begin" close=";end;" separator =";" >
            update plugin.delay_recheck_material_email e
            set e.email_type = #{info.emailType, jdbcType=VARCHAR},
            e.remark = #{info.remark, jdbcType=VARCHAR},
            e.plan_org_name = #{info.planOrgName, jdbcType=VARCHAR},
            e.req_qty = #{info.reqQty, jdbcType=INTEGER},
            e.bom_no = #{info.bomNo, jdbcType=VARCHAR},
            e.prod_plan_no = #{info.prodPlanNo, jdbcType=VARCHAR},
            e.product_class_name = #{info.productClassName, jdbcType=VARCHAR},
            e.planner = #{info.planner, jdbcType=VARCHAR},
            e.last_update_date = sysdate
            where e.WHSEID = #{info.whseId, jdbcType=VARCHAR}
            and e.item_no = #{info.itemNo, jdbcType=VARCHAR}
            and e.ITEM_BARCODE = #{info.itemBarcode, jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updatePlanFlag" parameterType="java.lang.String">
        update plugin.delay_recheck_material_email
        set plan_flag = 'Y',last_update_date = sysdate
        where item_no in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="selectOverdueMaterialWhseId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT DEFAULTWH1 FROM ENTERPRISE.SKU WHERE SKU = #{itemNo, jdbcType=VARCHAR} AND STORERKEY = 'ZTE'
    </select>
    <select id="selectOverdueMaterialIsLead" parameterType="com.zte.interfaces.infor.vo.PlanKitItemListVo" resultType="com.zte.interfaces.infor.vo.OverDueMaterialsLeadVO">
        SELECT code,description FROM ENTERPRISE.CODELKUP WHERE LISTNAME = 'ZLEADTYPE'
        AND DESCRIPTION in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item.isLead,jdbcType=VARCHAR}
        </foreach>
    </select>
    <insert id="insertOverdueMaterialsPlan" parameterType="com.zte.interfaces.infor.vo.PlanKitItemListVo">
        insert into plugin.DELAY_RECHECK_MATERIAL_PLAN (WHSEID, ITEM_NO, ITEM_BARCODE, PLAN_ORG_NAME, REQ_QTY, BOM_NO, PROD_PLAN_NO, PRODUCT_CLASS_NAME,PLANNER)
        select temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.itemWhseId,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR},  #{item.upperItemBarcode,jdbcType=VARCHAR},
            #{item.planOrgName,jdbcType=VARCHAR}, #{item.reqQty,jdbcType=INTEGER}, #{item.bomNo,jdbcType=VARCHAR},
            #{item.prodPlanNo,jdbcType=VARCHAR}, #{item.productClassName,jdbcType=VARCHAR}, #{item.planner,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) temp
    </insert>
    <delete id="deleteDelayRecheckMaterialEmailBack">
        delete from plugin.DELAY_RECHECK_EMAIL_BACK
        where CREATION_DATE <![CDATA[<]]> TRUNC(SYSDATE - (select LOOKUP_MEANING from plugin.sys_lookup_values where LOOKUP_CODE='100005900005' and enabled_flag = 'Y'))
    </delete>
    <insert id="insertDelayRecheckMaterialEmailBack">
        insert into plugin.DELAY_RECHECK_EMAIL_BACK (SERAIL_ID,WHSEID,CLASSGROUP,PUTAWAYZONE,LOC,ITEM_NO,ITEM_NAME,ITEM_BARCODE,
        ID,QTY,EMAIL_TYPE,CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATE_DATE,ENABLED_FLAG,PLAN_FLAG,EMAIL_FLAG,LOTTABLE08,
        LOTTABLE12,FEFO,REMARK,PLAN_ORG_NAME,REQ_QTY,BOM_NO,PROD_PLAN_NO,PRODUCT_CLASS_NAME,PLANNER)
        select SERAIL_ID,WHSEID,CLASSGROUP,PUTAWAYZONE,LOC,ITEM_NO,ITEM_NAME,ITEM_BARCODE,
        ID,QTY,EMAIL_TYPE,CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATE_DATE,ENABLED_FLAG,PLAN_FLAG,EMAIL_FLAG,LOTTABLE08,
        LOTTABLE12,FEFO,REMARK,PLAN_ORG_NAME,REQ_QTY,BOM_NO,PROD_PLAN_NO,PRODUCT_CLASS_NAME,PLANNER from plugin.delay_recheck_material_email
    </insert>
</mapper>