<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforWmsRepublishRepository">

	<select id="getInforWmsRepublishList" parameterType="com.zte.interfaces.infor.dto.WarehouseReplenishmentPathDTO" resultType="com.zte.interfaces.infor.dto.RepublishRouteDTO">
        select ero.serialkey,
        ero.whseid,
        f.DESCRIPTION as whName,
        ero.loc,
        ero.warehouse,
        slv1.description warehouseDesc,
        ero.warehouse_area as warehouseArea,
        slv2.description warehouseAreaDesc,
        t.loc_mark as locMark,
        CASE
        WHEN t.coordinate_type = N'库房拣选起点' THEN
        N'位置码'
        WHEN t.coordinate_type = N'库房拣选终点' THEN
        N'位置码'
        ELSE
        t.coordinate_type
        END AS locType,
        t.loc_coordinate_x as xcoord,
        t.loc_coordinate_y as ycoord,
        t.inf_point_coordinate_x1 as turningPoint1X,
        t.inf_point_coordinate_y1 as turningPoint1Y,
        t.inf_point_coordinate_x2 as turningPoint2X,
        t.inf_point_coordinate_y2 as turningPoint2Y,
        ero.fromloc as fromLoc,
        ero.toloc as toLoc,
        ero.sku,
        ero.lottable02 AS lotTable02,
        ero.id,
        ero.qty,
        decode(ero.PUBLISH_TYPE,
        '0',
        '拣货库位补货',
        '1',
        '整捡零捡补货',
        '2',
        '缺料补货') rep_type,
        0 AS "start",
        0 AS "end"
        from (select ic.*, wr.serialkey, wr.loc, wr.warehouse, wr.warehouse_area
        from plugin.warehouse_road_work wr
        join plugin.INFOR_WMS_REPUBLISH ic on ic.whseid = wr.whseid
        and wr.loc = ic.toloc
        where wr.whseid = #{whseid, jdbcType = VARCHAR}
        and wr.warehouse = #{warehouse,jdbcType = VARCHAR}
        and wr.warehouse_area = #{warehouseArea,jdbcType = VARCHAR}) ero
        left JOIN plugin.warehouse_road_work t ON t.whseid = ero.whseid
        AND t.LOC = ero.FROMLOC
        and t.whseid = #{whseid,jdbcType=VARCHAR}
        and t.warehouse = #{warehouse,jdbcType=VARCHAR}
        and t.warehouse_area = #{inventoryStorageArea,jdbcType=VARCHAR}
        left join ENTERPRISE.FACILITYNEST f on ero.whseid =
        upper(substr(f.name, 7))
        left join plugin.sys_lookup_values slv1 on slv1.lookup_type = '1000064'
        and slv1.lookup_meaning =
        ero.warehouse
        and slv1.enabled_flag = 'Y'
        left join plugin.sys_lookup_values slv2 on slv2.lookup_type = '1000065'
        and slv2.lookup_meaning =
        ero.warehouse_area
        and slv2.enabled_flag = 'Y'
        where 1 = 1
        <if test="repType != null and repType !='' ">
            and ero.publish_type = #{repType,jdbcType=VARCHAR}
        </if>
		union All
		(select t.serialkey,
		t.whseid,
		f.DESCRIPTION as whName,
		t.loc,
		t.warehouse,
		slv1.description warehouseDesc,
		t.warehouse_area as warehouseArea,
		slv2.description warehouseAreaDesc,
		t.loc_mark as locMark,
		t.coordinate_type as locType,
		t.loc_coordinate_x as xcoord,
		t.loc_coordinate_y as ycoord,
		t.inf_point_coordinate_x1 as turningPoint1X,
		t.inf_point_coordinate_y1 as turningPoint1Y,
		t.inf_point_coordinate_x2 as turningPoint2X,
		t.inf_point_coordinate_y2 as turningPoint2Y,
		t.loc fromLoc,
		null  toLoc,
		null sku,
		null  lotTable02,
		null id,
		null qty,
		null rep_type,
        CASE WHEN t.coordinate_type = '库房拣选起点' THEN 1 ELSE 0 END AS "start",
        CASE WHEN t.coordinate_type = '库房拣选终点' THEN 1 ELSE 0 END AS "end"
		from plugin.warehouse_road_work t
		left join ENTERPRISE.FACILITYNEST f
		on t.whseid = upper(substr(f.name, 7))
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000064'
		and slv1.lookup_meaning = t.warehouse
		and slv1.enabled_flag = 'Y'
		left join plugin.sys_lookup_values slv2
		on slv2.lookup_type = '1000065'
		and slv2.lookup_meaning = t.warehouse_area
		and slv2.enabled_flag = 'Y'
		where
		t.coordinate_type in ('库房拣选起点','库房拣选终点')
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="inventoryStorageArea != null and inventoryStorageArea !='' ">
			and t.warehouse_area  = #{inventoryStorageArea,jdbcType=VARCHAR}
		</if>
		)

    </select>

	<select id="getInforWmsRepublishListTotal" parameterType="com.zte.interfaces.infor.dto.WarehouseReplenishmentPathDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.warehouse_road_work t
		join  plugin.INFOR_WMS_REPUBLISH i on i.whseid = t.whseid and t.loc = i.fromloc
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and t.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="repType != null and repType !='' ">
			and i.publish_type = #{repType,jdbcType=VARCHAR}
		</if>
	</select>

</mapper>
