<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.SkuRepository">
  <resultMap id="SkuDefaultResultMap" type="com.zte.interfaces.infor.dto.SkuDTO"> 
    <result column="SKU" jdbcType="OTHER" property="sku" />
    <result column="LOC" jdbcType="OTHER" property="loc" />
    <result column="PUTAWAYZONE" jdbcType="OTHER" property="putAwayZone" />
    <result column="DESCR" jdbcType="OTHER" property="descr" />
    <result column="BUSR4" jdbcType="OTHER" property="bsur" /> 
  </resultMap> 
  <resultMap id="ResponseResultMap" type="com.zte.interfaces.tally.dto.BoxDetailDTO"> 
    <result column="externalorderkey2" jdbcType="VARCHAR" property="containerNo" />
    <result column="itemcode" jdbcType="VARCHAR" property="materialCode" />
    <result column="item_name" jdbcType="VARCHAR" property="materialName" />
    <result column="qty" jdbcType="INTEGER" property="piece" />    
    <result column="ID" jdbcType="VARCHAR" property="toId" />  
  </resultMap> 
  <resultMap id="SoResultMap" type="com.zte.interfaces.tally.dto.InforBillDTO"> 
    <result column="EXTERNALORDERKEY2" jdbcType="OTHER" property="externalorderkey2" /> 
    <result column="HREF14" jdbcType="OTHER" property="whseid" />
    <result column="HREF15" jdbcType="OTHER" property="destStock" /> 
  </resultMap>
  
  <resultMap id="ReelIdResultMap" type="com.zte.interfaces.infor.dto.SkuReelIdOutputDTO"> 
    <result column="SKU" jdbcType="OTHER" property="sku" /> 
    <result column="REELIDPROCESS" jdbcType="OTHER" property="reelidProcess" /> 
  </resultMap>
  
  <select id="selectSkuById" parameterType="com.zte.interfaces.infor.dto.SkuQueryDTO" resultMap="SkuDefaultResultMap">
    SELECT S.SKU, SL.LOC, S.PUTAWAYZONE, P.DESCR, S.BUSR4      
	FROM ${whseid}.SKU S
  	LEFT JOIN ${whseid}.SKUXLOC SL
	ON S.SKU = SL.SKU
   	AND SL.LOCATIONTYPE = 'PICK'
  	LEFT JOIN ${whseid}.LOC L
	ON SL.LOC = L.LOC
  	LEFT JOIN ${whseid}.PUTAWAYZONE P
	ON P.PUTAWAYZONE = S.PUTAWAYZONE
 	WHERE S.STORERKEY = 'ZTE'
    AND UPPER(S.WHSEID) = #{whseid}
    and s.sku = #{sku}
  </select>
  <select id="selectLfid" parameterType="com.zte.interfaces.infor.dto.SkuQueryDTO" resultMap="ResponseResultMap">
      select ID, QTY, SKU itemcode,DESCR item_name,externalorderkey2
      from (
      <foreach collection="list" separator="union" item="item">
          select PD.ID, PD.QTY, PD.SKU, S.DESCR,oo.externalorderkey2
	          FROM ${item.whseid}.ORDERS OO
			  JOIN ${item.whseid}.PICKDETAIL PD
			    ON PD.ORDERKEY = OO.ORDERKEY
			  JOIN ${item.whseid}.SKU S
			    ON S.SKU = PD.SKU
			   AND S.STORERKEY = 'ZTE'
          where PD.Dropid = #{item.sku,jdbcType=VARCHAR}
          group by PD.ID, PD.QTY, PD.SKU, S.DESCR, PD.SKU,oo.externalorderkey2
      </foreach>
      ) t
  </select>
  <select id="selectExtNo" parameterType="com.zte.interfaces.tally.dto.InforBillDTO" resultMap="ResponseResultMap">
      select ID, QTY, SKU itemcode,DESCR item_name,externalorderkey2
      from (
      <foreach collection="list" separator="union" item="item">
          select PD.ID, PD.QTY, PD.SKU, S.DESCR,oo.externalorderkey2
	          FROM ${item.whseid}.ORDERS OO
			  JOIN ${item.whseid}.PICKDETAIL PD
			    ON PD.ORDERKEY = OO.ORDERKEY
			  JOIN ${item.whseid}.SKU S
			    ON S.SKU = PD.SKU
			   AND S.STORERKEY = 'ZTE'
          where OO.Externalorderkey2 = #{item.externalorderkey2,jdbcType=VARCHAR}
          group by PD.ID, PD.QTY, PD.SKU, S.DESCR, PD.SKU,oo.externalorderkey2
      </foreach>
      ) t
  </select>
  <select id="selectEdiSoAll" parameterType="java.lang.String" resultMap="SoResultMap">
    select  distinct
    EXTERNALORDERKEY2, HREF14, HREF15
    from plugin.EDI_SO ES
    where 
    es.whseid = es.href14
    and es.symbol = 1
    and EXTERNALORDERKEY2 in  
    <foreach collection="list" open="(" separator="," close=")" item="item">
      #{item,jdbcType=VARCHAR}
     </foreach>
  </select> 
  <select id="selectReelId" parameterType="com.zte.interfaces.infor.dto.SkuReelIdInputDTO" resultMap="ReelIdResultMap">
      SELECT S.SKU, S.REELIDPROCESS
      FROM ENTERPRISE.SKU S
      WHERE S.STORERKEY = 'ZTE'
      AND SKU  in
      <foreach collection="skuList" open="(" separator="," close=")" item="item">
          #{item,jdbcType=VARCHAR}
      </foreach>
      AND EXISTS (
      select 1
      from ${whseid}.NSQLCONFIG
      where CONFIGKEY = 'EnablingReelid'
      AND NSQLVALUE = '1'
      )
  </select>
</mapper>