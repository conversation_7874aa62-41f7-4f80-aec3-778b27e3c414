<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.StoreIssueSituationRepository">
	<update id="updateProdRecDate" >
		UPDATE PLUGIN.STORE_ISSUE_SITUATION SIS
		SET SIS.PRODUCTRECIVEDATE =#{updateToDate},
		SIS.LAST_UPDATE_DATE  = SYSDATE
		WHERE SIS.EXTERNORDERKEY = #{taskNo}
	</update>
	<select id="getStoreIssueList" resultType="java.lang.String">
		select t.EXTERNORDERKEY  from (
		SELECT SIS.EXTERNORDERKEY,rownum rn
		FROM PLUGIN.STORE_ISSUE_SITUATION SIS
		WHERE SIS.PRODUCTRECIVEDATE IS NULL
		<if test="externKey!=null and externKey!=''">
			and SIS.EXTERNORDERKEY = #{externKey}
		</if>

		) t
		where  t.rn <![CDATA[>=]]> #{startRow,jdbcType=INTEGER}
		and t.rn <![CDATA[<=]]> #{endRow,jdbcType=INTEGER}
	</select>
	<select id="getStoreIssueCount" resultType="java.lang.Long">
		SELECT count(1)
		FROM PLUGIN.STORE_ISSUE_SITUATION SIS
		WHERE SIS.PRODUCTRECIVEDATE IS NULL
	</select>
</mapper>